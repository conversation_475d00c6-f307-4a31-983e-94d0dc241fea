<div class="sidebar-container" [class.collapsed]="isCollapsed">
  <!-- Logo Section -->
  <div class="sidebar-header">
    <div class="logo-section">
      <mat-icon class="logo-icon">store</mat-icon>
      <div class="logo-text">
        <h3>Terra Retail</h3>
        <span>نظام إدارة المتاجر</span>
      </div>
    </div>
    <button mat-icon-button class="sidebar-toggle" (click)="toggleSidebar()">
      <mat-icon>{{ isCollapsed ? 'menu' : 'menu_open' }}</mat-icon>
    </button>
  </div>

  <!-- User Info -->
  <div class="user-info" *ngIf="!isCollapsed">
    <div class="user-avatar">
      <mat-icon>account_circle</mat-icon>
    </div>
    <div class="user-details">
      <span class="user-name">{{ currentUser?.username || 'مدير النظام' }}</span>
      <span class="user-branch">{{ currentUser?.branch?.nameAr || 'الفرع الرئيسي' }}</span>
    </div>
  </div>

  <!-- Navigation Menu -->
  <nav class="sidebar-nav">
    <!-- لوحة التحكم الرئيسية -->
    <div class="nav-section">
      <a mat-button class="nav-item" routerLink="/dashboard" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon>
        <span *ngIf="!isCollapsed">لوحة التحكم الرئيسية</span>
      </a>
    </div>

    <!-- المبيعات -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">المبيعات</h4>

      <a mat-button class="nav-item" routerLink="/pos" routerLinkActive="active">
        <mat-icon>storefront</mat-icon>
        <span *ngIf="!isCollapsed">نقطة البيع</span>
      </a>

      <a mat-button class="nav-item" routerLink="/sales" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة المبيعات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/new-sale" routerLinkActive="active">
        <mat-icon>add_shopping_cart</mat-icon>
        <span *ngIf="!isCollapsed">بيع جديد</span>
      </a>

      <a mat-button class="nav-item" routerLink="/sales-returns" routerLinkActive="active">
        <mat-icon>keyboard_return</mat-icon>
        <span *ngIf="!isCollapsed">المرتجعات</span>
      </a>
    </div>

    <!-- المشتريات -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">المشتريات</h4>

      <a mat-button class="nav-item" routerLink="/purchase-orders" routerLinkActive="active">
        <mat-icon>assignment</mat-icon>
        <span *ngIf="!isCollapsed">أوامر الشراء</span>
      </a>

      <a mat-button class="nav-item" routerLink="/new-purchase" routerLinkActive="active">
        <mat-icon>add_shopping_cart</mat-icon>
        <span *ngIf="!isCollapsed">شراء جديد</span>
      </a>

      <a mat-button class="nav-item" routerLink="/purchase-returns" routerLinkActive="active">
        <mat-icon>keyboard_return</mat-icon>
        <span *ngIf="!isCollapsed">مرتجعات الشراء</span>
      </a>

      <a mat-button class="nav-item" routerLink="/goods-receipt" routerLinkActive="active">
        <mat-icon>local_shipping</mat-icon>
        <span *ngIf="!isCollapsed">استلام البضائع</span>
      </a>
    </div>

    <!-- المنتجات -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">المنتجات</h4>

      <a mat-button class="nav-item" routerLink="/products" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة المنتجات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/add-product" routerLinkActive="active">
        <mat-icon>add</mat-icon>
        <span *ngIf="!isCollapsed">إضافة منتج</span>
      </a>

      <a mat-button class="nav-item" routerLink="/categories" routerLinkActive="active">
        <mat-icon>category</mat-icon>
        <span *ngIf="!isCollapsed">الفئات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/units" routerLinkActive="active">
        <mat-icon>straighten</mat-icon>
        <span *ngIf="!isCollapsed">الوحدات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/barcode-print" routerLinkActive="active">
        <mat-icon>qr_code</mat-icon>
        <span *ngIf="!isCollapsed">طباعة الباركود</span>
      </a>
    </div>

    <!-- الموردين -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">الموردين</h4>

      <a mat-button class="nav-item" routerLink="/suppliers" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة الموردين</span>
      </a>

      <a mat-button class="nav-item" routerLink="/supplier-accounts" routerLinkActive="active">
        <mat-icon>account_balance</mat-icon>
        <span *ngIf="!isCollapsed">حسابات الموردين</span>
      </a>

      <a mat-button class="nav-item" routerLink="/supplier-payments" routerLinkActive="active">
        <mat-icon>payment</mat-icon>
        <span *ngIf="!isCollapsed">مدفوعات الموردين</span>
      </a>

      <a mat-button class="nav-item" routerLink="/suppliers/stats" routerLinkActive="active">
        <mat-icon>analytics</mat-icon>
        <span *ngIf="!isCollapsed">احصائيات</span>
      </a>

      <!-- Sub-pages -->
      <div class="sub-section" *ngIf="!isCollapsed">
        <h5 class="sub-title">الإعدادات</h5>

        <a mat-button class="nav-item sub-item" routerLink="/suppliers/types" routerLinkActive="active">
          <mat-icon>category</mat-icon>
          <span>أنواع الموردين</span>
        </a>

        <a mat-button class="nav-item sub-item" routerLink="/suppliers/areas" routerLinkActive="active">
          <mat-icon>location_city</mat-icon>
          <span>المحافظات</span>
        </a>

        <a mat-button class="nav-item sub-item" routerLink="/suppliers/countries" routerLinkActive="active">
          <mat-icon>public</mat-icon>
          <span>البلدان</span>
        </a>
      </div>
    </div>

    <!-- العملاء -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">العملاء</h4>

      <a mat-button class="nav-item" routerLink="/customers" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة العملاء</span>
      </a>

      <a mat-button class="nav-item" routerLink="/add-customer" routerLinkActive="active">
        <mat-icon>person_add</mat-icon>
        <span *ngIf="!isCollapsed">إضافة عميل</span>
      </a>

      <a mat-button class="nav-item" routerLink="/customer-accounts" routerLinkActive="active">
        <mat-icon>account_balance</mat-icon>
        <span *ngIf="!isCollapsed">حسابات العملاء</span>
      </a>

      <a mat-button class="nav-item" routerLink="/customer-payments" routerLinkActive="active">
        <mat-icon>payment</mat-icon>
        <span *ngIf="!isCollapsed">مدفوعات العملاء</span>
      </a>
    </div>

    <!-- الموظفين -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">الموظفين</h4>

      <a mat-button class="nav-item" routerLink="/employees" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة الموظفين</span>
      </a>

      <a mat-button class="nav-item" routerLink="/add-employee" routerLinkActive="active">
        <mat-icon>person_add</mat-icon>
        <span *ngIf="!isCollapsed">إضافة موظف</span>
      </a>

      <a mat-button class="nav-item" routerLink="/attendance" routerLinkActive="active">
        <mat-icon>access_time</mat-icon>
        <span *ngIf="!isCollapsed">الحضور والانصراف</span>
      </a>

      <a mat-button class="nav-item" routerLink="/payroll" routerLinkActive="active">
        <mat-icon>receipt</mat-icon>
        <span *ngIf="!isCollapsed">كشوف المرتبات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/fingerprint" routerLinkActive="active">
        <mat-icon>fingerprint</mat-icon>
        <span *ngIf="!isCollapsed">البصمة</span>
      </a>

      <a mat-button class="nav-item" routerLink="/employee-reports" routerLinkActive="active">
        <mat-icon>assessment</mat-icon>
        <span *ngIf="!isCollapsed">تقارير الموظفين</span>
      </a>
    </div>

    <!-- المخزون -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">المخزون</h4>

      <a mat-button class="nav-item" routerLink="/inventory-levels" routerLinkActive="active">
        <mat-icon>bar_chart</mat-icon>
        <span *ngIf="!isCollapsed">مستويات المخزون</span>
      </a>

      <a mat-button class="nav-item" routerLink="/inventory-movements" routerLinkActive="active">
        <mat-icon>swap_horiz</mat-icon>
        <span *ngIf="!isCollapsed">حركات المخزون</span>
      </a>

      <a mat-button class="nav-item" routerLink="/low-stock" routerLinkActive="active">
        <mat-icon>warning</mat-icon>
        <span *ngIf="!isCollapsed">مخزون منخفض</span>
      </a>

      <a mat-button class="nav-item" routerLink="/inventory-adjustment" routerLinkActive="active">
        <mat-icon>tune</mat-icon>
        <span *ngIf="!isCollapsed">تسوية المخزون</span>
      </a>

      <a mat-button class="nav-item" routerLink="/inventory-transfer" routerLinkActive="active">
        <mat-icon>compare_arrows</mat-icon>
        <span *ngIf="!isCollapsed">نقل المخزون</span>
      </a>
    </div>

    <!-- المالية -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">المالية</h4>

      <a mat-button class="nav-item" routerLink="/chart-of-accounts" routerLinkActive="active">
        <mat-icon>account_tree</mat-icon>
        <span *ngIf="!isCollapsed">دليل الحسابات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/journal-entries" routerLinkActive="active">
        <mat-icon>receipt_long</mat-icon>
        <span *ngIf="!isCollapsed">القيود اليومية</span>
      </a>

      <a mat-button class="nav-item" routerLink="/trial-balance" routerLinkActive="active">
        <mat-icon>balance</mat-icon>
        <span *ngIf="!isCollapsed">ميزان المراجعة</span>
      </a>

      <a mat-button class="nav-item" routerLink="/income-statement" routerLinkActive="active">
        <mat-icon>trending_up</mat-icon>
        <span *ngIf="!isCollapsed">قائمة الدخل</span>
      </a>

      <a mat-button class="nav-item" routerLink="/balance-sheet" routerLinkActive="active">
        <mat-icon>account_balance_wallet</mat-icon>
        <span *ngIf="!isCollapsed">الميزانية العمومية</span>
      </a>

      <a mat-button class="nav-item" routerLink="/cash-flow" routerLinkActive="active">
        <mat-icon>waterfall_chart</mat-icon>
        <span *ngIf="!isCollapsed">التدفق النقدي</span>
      </a>
    </div>

    <!-- الخزينة -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">الخزينة</h4>

      <a mat-button class="nav-item" routerLink="/cash-accounts" routerLinkActive="active">
        <mat-icon>money</mat-icon>
        <span *ngIf="!isCollapsed">حسابات النقدية</span>
      </a>

      <a mat-button class="nav-item" routerLink="/bank-accounts" routerLinkActive="active">
        <mat-icon>account_balance</mat-icon>
        <span *ngIf="!isCollapsed">حسابات البنوك</span>
      </a>

      <a mat-button class="nav-item" routerLink="/receipts" routerLinkActive="active">
        <mat-icon>receipt</mat-icon>
        <span *ngIf="!isCollapsed">إيصالات القبض</span>
      </a>

      <a mat-button class="nav-item" routerLink="/payments" routerLinkActive="active">
        <mat-icon>payment</mat-icon>
        <span *ngIf="!isCollapsed">إيصالات الدفع</span>
      </a>

      <a mat-button class="nav-item" routerLink="/transfers" routerLinkActive="active">
        <mat-icon>swap_horiz</mat-icon>
        <span *ngIf="!isCollapsed">التحويلات</span>
      </a>
    </div>

    <!-- التقارير -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">التقارير</h4>

      <a mat-button class="nav-item" routerLink="/sales-reports" routerLinkActive="active">
        <mat-icon>point_of_sale</mat-icon>
        <span *ngIf="!isCollapsed">تقارير المبيعات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/purchase-reports" routerLinkActive="active">
        <mat-icon>shopping_cart</mat-icon>
        <span *ngIf="!isCollapsed">تقارير المشتريات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/inventory-reports" routerLinkActive="active">
        <mat-icon>inventory</mat-icon>
        <span *ngIf="!isCollapsed">تقارير المخزون</span>
      </a>

      <a mat-button class="nav-item" routerLink="/financial-reports" routerLinkActive="active">
        <mat-icon>account_balance</mat-icon>
        <span *ngIf="!isCollapsed">التقارير المالية</span>
      </a>

      <a mat-button class="nav-item" routerLink="/customer-reports" routerLinkActive="active">
        <mat-icon>people</mat-icon>
        <span *ngIf="!isCollapsed">تقارير العملاء</span>
      </a>

      <a mat-button class="nav-item" routerLink="/supplier-reports" routerLinkActive="active">
        <mat-icon>business</mat-icon>
        <span *ngIf="!isCollapsed">تقارير الموردين</span>
      </a>
    </div>

    <!-- الفروع -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">الفروع</h4>

      <a mat-button class="nav-item" routerLink="/branches" routerLinkActive="active">
        <mat-icon>list</mat-icon>
        <span *ngIf="!isCollapsed">قائمة الفروع</span>
      </a>

      <a mat-button class="nav-item" routerLink="/add-branch" routerLinkActive="active">
        <mat-icon>add_location</mat-icon>
        <span *ngIf="!isCollapsed">إضافة فرع</span>
      </a>

      <a mat-button class="nav-item" routerLink="/branch-transfers" routerLinkActive="active">
        <mat-icon>compare_arrows</mat-icon>
        <span *ngIf="!isCollapsed">تحويلات الفروع</span>
      </a>
    </div>

    <!-- الإعدادات -->
    <div class="nav-section">
      <h4 class="nav-section-title" *ngIf="!isCollapsed">الإعدادات</h4>

      <a mat-button class="nav-item" routerLink="/general-settings" routerLinkActive="active">
        <mat-icon>tune</mat-icon>
        <span *ngIf="!isCollapsed">الإعدادات العامة</span>
      </a>

      <a mat-button class="nav-item" routerLink="/users" routerLinkActive="active">
        <mat-icon>people</mat-icon>
        <span *ngIf="!isCollapsed">المستخدمين</span>
      </a>

      <a mat-button class="nav-item" routerLink="/permissions" routerLinkActive="active">
        <mat-icon>security</mat-icon>
        <span *ngIf="!isCollapsed">الصلاحيات</span>
      </a>

      <a mat-button class="nav-item" routerLink="/company-info" routerLinkActive="active">
        <mat-icon>business</mat-icon>
        <span *ngIf="!isCollapsed">بيانات الشركة</span>
      </a>

      <a mat-button class="nav-item" routerLink="/tax-settings" routerLinkActive="active">
        <mat-icon>calculate</mat-icon>
        <span *ngIf="!isCollapsed">إعدادات الضرائب</span>
      </a>

      <a mat-button class="nav-item" routerLink="/backup" routerLinkActive="active">
        <mat-icon>backup</mat-icon>
        <span *ngIf="!isCollapsed">النسخ الاحتياطي</span>
      </a>
    </div>
  </nav>

  <!-- Footer -->
  <div class="sidebar-footer" *ngIf="!isCollapsed">
    <button mat-button class="logout-btn" (click)="logout()">
      <mat-icon>logout</mat-icon>
      <span>تسجيل الخروج</span>
    </button>
  </div>
</div>
