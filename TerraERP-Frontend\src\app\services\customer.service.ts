import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Customer {
  id?: number;
  customerCode?: string;
  nameAr: string;
  nameEn: string;
  phone1: string;
  phone2?: string;
  email?: string;
  address?: string;
  customerTypeId: number;
  priceCategoryId: number;
  creditLimit?: number;
  openingBalance?: number;
  currentBalance?: number;
  branchId?: number;
  referralSource: string;
  notes?: string;
  isActive: boolean;
  createdAt?: Date;
  createdBy?: number;
  updatedAt?: Date;
  updatedBy?: number;
}

export interface CustomerResponse {
  success: boolean;
  message: string;
  data: Customer | Customer[];
  count?: number;
}

export interface JournalEntry {
  description: string;
  transactionType: string;
  customerId?: number;
  amount: number;
  details: JournalEntryDetail[];
}

export interface JournalEntryDetail {
  accountCode: string;
  debit: number;
  credit: number;
  description: string;
}

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private apiUrl = 'http://localhost:5233/api';

  constructor(private http: HttpClient) {}

  // الحصول على جميع العملاء
  getCustomers(): Observable<CustomerResponse> {
    return this.http.get<CustomerResponse>(`${this.apiUrl}/customers`);
  }

  // الحصول على عميل واحد
  getCustomer(id: number): Observable<CustomerResponse> {
    return this.http.get<CustomerResponse>(`${this.apiUrl}/customers/${id}`);
  }

  // إنشاء عميل جديد
  createCustomer(customer: Customer): Observable<CustomerResponse> {
    return this.http.post<CustomerResponse>(`${this.apiUrl}/customers`, customer);
  }

  // تحديث عميل
  updateCustomer(id: number, customer: Customer): Observable<CustomerResponse> {
    return this.http.put<CustomerResponse>(`${this.apiUrl}/customers/${id}`, customer);
  }

  // حذف عميل (soft delete)
  deleteCustomer(id: number): Observable<CustomerResponse> {
    return this.http.delete<CustomerResponse>(`${this.apiUrl}/customers/${id}`);
  }

  // البحث في العملاء
  searchCustomers(searchTerm: string): Observable<CustomerResponse> {
    return this.http.get<CustomerResponse>(`${this.apiUrl}/customers/search?term=${searchTerm}`);
  }

  // الحصول على كود العميل التالي
  getNextCustomerCode(): Observable<{success: boolean, data: {nextCode: string}}> {
    return this.http.get<{success: boolean, data: {nextCode: string}}>(`${this.apiUrl}/customers/next-code`);
  }

  // إنشاء حساب العميل في شجرة الحسابات
  createCustomerAccount(customer: Customer): Observable<any> {
    const accountData = {
      accountCode: customer.customerCode,
      nameAr: customer.nameAr,
      nameEn: customer.nameEn,
      accountType: 'customer',
      parentAccountCode: '1201', // حساب العملاء الرئيسي
      isActive: true,
      customerId: customer.id
    };

    return this.http.post<any>(`${this.apiUrl}/chart-of-accounts`, accountData);
  }

  // إنشاء قيد يومية للرصيد الافتتاحي
  createOpeningBalanceEntry(customer: Customer, amount: number): Observable<any> {
    const journalEntry: JournalEntry = {
      description: `رصيد افتتاحي للعميل ${customer.customerCode} - ${customer.nameAr}`,
      transactionType: 'opening_balance',
      customerId: customer.id,
      amount: Math.abs(amount),
      details: [
        {
          accountCode: customer.customerCode!, // حساب العميل
          debit: amount > 0 ? amount : 0,
          credit: amount < 0 ? Math.abs(amount) : 0,
          description: `رصيد افتتاحي للعميل ${customer.nameAr}`
        },
        {
          accountCode: '1001', // حساب الصندوق
          debit: amount < 0 ? Math.abs(amount) : 0,
          credit: amount > 0 ? amount : 0,
          description: `رصيد افتتاحي للعميل ${customer.nameAr}`
        }
      ]
    };

    return this.http.post<any>(`${this.apiUrl}/journal-entries`, journalEntry);
  }

  // تحديث رصيد العميل
  updateCustomerBalance(customerId: number, amount: number, operation: 'add' | 'subtract'): Observable<any> {
    return this.http.patch<any>(`${this.apiUrl}/customers/${customerId}/balance`, {
      amount: amount,
      operation: operation
    });
  }

  // الحصول على كشف حساب العميل
  getCustomerStatement(customerId: number, fromDate?: Date, toDate?: Date): Observable<any> {
    let url = `${this.apiUrl}/customers/${customerId}/statement`;
    const params = [];
    
    if (fromDate) {
      params.push(`fromDate=${fromDate.toISOString()}`);
    }
    if (toDate) {
      params.push(`toDate=${toDate.toISOString()}`);
    }
    
    if (params.length > 0) {
      url += '?' + params.join('&');
    }

    return this.http.get<any>(url);
  }

  // تحويل الاسم العربي إلى إنجليزي
  translateArabicToEnglish(arabicText: string): string {
    const arabicToEnglishMap: { [key: string]: string } = {
      'أحمد': 'Ahmed', 'محمد': 'Mohamed', 'علي': 'Ali', 'فاطمة': 'Fatma',
      'عائشة': 'Aisha', 'خديجة': 'Khadija', 'حسن': 'Hassan', 'حسين': 'Hussein',
      'عبد': 'Abd', 'الله': 'Allah', 'الرحمن': 'Rahman', 'عبدالله': 'Abdullah',
      'عبدالرحمن': 'Abdulrahman', 'إبراهيم': 'Ibrahim', 'إسماعيل': 'Ismail',
      'يوسف': 'Youssef', 'موسى': 'Musa', 'عيسى': 'Issa', 'مريم': 'Mariam',
      'زينب': 'Zeinab', 'رقية': 'Ruqaya', 'سارة': 'Sara', 'ليلى': 'Layla',
      'نور': 'Nour', 'أمل': 'Amal', 'هدى': 'Hoda', 'منى': 'Mona',
      'سلمى': 'Salma', 'دينا': 'Dina', 'ريم': 'Reem', 'نادية': 'Nadia',
      'سميرة': 'Samira', 'كريم': 'Karim', 'طارق': 'Tarek', 'عمر': 'Omar',
      'خالد': 'Khaled', 'وليد': 'Walid', 'سامي': 'Sami', 'رامي': 'Rami',
      'هاني': 'Hani', 'مصطفى': 'Mostafa', 'محمود': 'Mahmoud'
    };

    let englishName = arabicText;
    
    // تحويل الأسماء المعروفة
    Object.keys(arabicToEnglishMap).forEach(arabic => {
      const regex = new RegExp(arabic, 'g');
      englishName = englishName.replace(regex, arabicToEnglishMap[arabic]);
    });

    // إزالة المسافات الزائدة وتنسيق النص
    englishName = englishName.trim().replace(/\s+/g, ' ');
    
    // تحويل أول حرف من كل كلمة إلى كبير
    englishName = englishName.replace(/\b\w/g, l => l.toUpperCase());

    return englishName;
  }

  // التحقق من صحة رقم الهاتف المصري
  validateEgyptianPhone(phone: string): boolean {
    const phoneRegex = /^01[0-9]{9}$/;
    return phoneRegex.test(phone);
  }

  // التحقق من صحة البريد الإلكتروني
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // تصدير العملاء إلى Excel
  exportCustomersToExcel(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/customers/export/excel`, {
      responseType: 'blob'
    });
  }

  // استيراد العملاء من Excel
  importCustomersFromExcel(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.http.post<any>(`${this.apiUrl}/customers/import/excel`, formData);
  }

  // الحصول على إحصائيات العملاء
  getCustomersStatistics(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/customers/statistics`);
  }

  // الحصول على أفضل العملاء
  getTopCustomers(limit: number = 10): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/customers/top?limit=${limit}`);
  }

  // الحصول على العملاء المتأخرين في السداد
  getOverdueCustomers(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/customers/overdue`);
  }
}
