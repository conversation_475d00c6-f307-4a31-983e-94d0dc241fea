using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// جلسات المستخدمين
    /// </summary>
    public class UserSession : BaseEntity
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// رمز الجلسة
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string SessionToken { get; set; } = string.Empty;

        /// <summary>
        /// رمز التحديث
        /// </summary>
        [MaxLength(255)]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// تاريخ بداية الجلسة
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// تاريخ انتهاء الجلسة
        /// </summary>
        public DateTime ExpiryTime { get; set; }

        /// <summary>
        /// تاريخ آخر نشاط
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// عنوان IP
        /// </summary>
        [MaxLength(45)]
        public string? IPAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح
        /// </summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// نوع الجهاز
        /// </summary>
        [MaxLength(50)]
        public string? DeviceType { get; set; }

        /// <summary>
        /// نظام التشغيل
        /// </summary>
        [MaxLength(100)]
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// المتصفح
        /// </summary>
        [MaxLength(100)]
        public string? Browser { get; set; }

        /// <summary>
        /// الموقع الجغرافي
        /// </summary>
        [MaxLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// هل الجلسة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// سبب انتهاء الجلسة
        /// </summary>
        public SessionEndReason? EndReason { get; set; }

        /// <summary>
        /// تاريخ انتهاء الجلسة
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// معرف الفرع المستخدم في الجلسة
        /// </summary>
        public int? BranchId { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Branch? Branch { get; set; }
    }

    /// <summary>
    /// أسباب انتهاء الجلسة
    /// </summary>
    public enum SessionEndReason
    {
        /// <summary>
        /// تسجيل خروج عادي
        /// </summary>
        Logout = 1,

        /// <summary>
        /// انتهاء الوقت
        /// </summary>
        Timeout = 2,

        /// <summary>
        /// إنهاء إداري
        /// </summary>
        AdminTermination = 3,

        /// <summary>
        /// تسجيل دخول من جهاز آخر
        /// </summary>
        NewLogin = 4,

        /// <summary>
        /// خطأ في النظام
        /// </summary>
        SystemError = 5
    }
}
