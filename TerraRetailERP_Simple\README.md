# 🚀 Terra Retail ERP API - النظام الكامل

## نظام تخطيط موارد المؤسسات للتجارة التجزئة

نظام ERP شامل ومتطور للتجارة التجزئة يدعم اللغة العربية بالكامل مع جميع الوحدات المطلوبة.

---

## ✅ **التطبيق الكامل يعمل بنجاح!**

### 🌐 **الروابط المهمة:**
- **Swagger UI**: https://localhost:7000
- **API Base URL**: https://localhost:7000/api

---

## 📋 **الوحدات المكتملة بالكامل**

### 👥 **إدارة العملاء (Customer Management)**
- ✅ عرض قائمة العملاء مع فلترة وبحث
- ✅ إضافة عميل جديد مع توليد كود تلقائي
- ✅ تحديث بيانات العميل
- ✅ حذف عميل
- ✅ عرض تفاصيل عميل محدد
- ✅ عرض رصيد العميل

### 🏭 **إدارة الموردين (Supplier Management)**
- ✅ عرض قائمة الموردين
- ✅ إضافة مورد جديد مع توليد كود تلقائي
- ✅ تحديث بيانات المورد
- ✅ حذف مورد
- ✅ عرض تفاصيل مورد محدد
- ✅ عرض رصيد المورد وحد الائتمان

### 📦 **إدارة المنتجات (Product Management)**
- ✅ عرض قائمة المنتجات مع فلترة حسب الفئة
- ✅ إضافة منتج جديد مع توليد كود تلقائي
- ✅ تحديث بيانات المنتج
- ✅ حذف منتج
- ✅ عرض تفاصيل منتج محدد
- ✅ البحث بالباركود
- ✅ إدارة فئات المنتجات
- ✅ إدارة وحدات القياس

### 💰 **إدارة المبيعات (Sales Management)**
- ✅ عرض قائمة فواتير المبيعات مع فلترة
- ✅ إنشاء فاتورة مبيعات جديدة
- ✅ عرض تفاصيل فاتورة المبيعات
- ✅ إلغاء فاتورة مبيعات
- ✅ لوحة تحكم المبيعات
- ✅ حساب الخصومات والضرائب تلقائياً
- ✅ دعم المبيعات النقدية والآجلة

### 📊 **التقارير والتحليلات (Reports & Analytics)**
- ✅ لوحة التحكم الرئيسية
- ✅ الرسوم البيانية للمبيعات (يومي، شهري، سنوي)
- ✅ أفضل المنتجات مبيعاً
- ✅ الملخص المالي
- ✅ تحليل العملاء
- ✅ تحليل الموردين

### ⚙️ **الإعدادات (System Settings)**
- ✅ إدارة الفروع
- ✅ إدارة فئات المنتجات
- ✅ إدارة وحدات القياس
- ✅ إدارة المستخدمين
- ✅ معلومات النظام وإحصائيات شاملة

---

## 🛠 **التقنيات المستخدمة**

- **ASP.NET Core 8.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM للتعامل مع قاعدة البيانات
- **SQL Server** - قاعدة البيانات
- **BCrypt.Net** - تشفير كلمات المرور
- **Swagger/OpenAPI** - توثيق API شامل
- **CORS** - دعم الطلبات من مصادر مختلفة

---

## 📊 **API Endpoints الكاملة**

### 👥 Customer Management
- `GET /api/customers` - قائمة العملاء
- `GET /api/customers/{id}` - تفاصيل عميل
- `POST /api/customers` - إضافة عميل جديد
- `PUT /api/customers/{id}` - تحديث بيانات عميل
- `DELETE /api/customers/{id}` - حذف عميل
- `GET /api/customers/{id}/balance` - رصيد العميل

### 🏭 Supplier Management
- `GET /api/suppliers` - قائمة الموردين
- `GET /api/suppliers/{id}` - تفاصيل مورد
- `POST /api/suppliers` - إضافة مورد جديد
- `PUT /api/suppliers/{id}` - تحديث بيانات مورد
- `DELETE /api/suppliers/{id}` - حذف مورد
- `GET /api/suppliers/{id}/balance` - رصيد المورد

### 📦 Product Management
- `GET /api/products` - قائمة المنتجات
- `GET /api/products/{id}` - تفاصيل منتج
- `POST /api/products` - إضافة منتج جديد
- `PUT /api/products/{id}` - تحديث بيانات منتج
- `DELETE /api/products/{id}` - حذف منتج
- `GET /api/products/categories` - فئات المنتجات
- `GET /api/products/units` - وحدات القياس
- `GET /api/products/barcode/{barcode}` - البحث بالباركود

### 💰 Sales Management
- `GET /api/sales` - قائمة المبيعات
- `GET /api/sales/{id}` - تفاصيل فاتورة مبيعات
- `POST /api/sales` - إنشاء فاتورة مبيعات جديدة
- `PUT /api/sales/{id}/cancel` - إلغاء فاتورة مبيعات
- `GET /api/sales/dashboard` - لوحة تحكم المبيعات

### 🛍️ Purchase Management
- `GET /api/purchases` - قائمة المشتريات
- `GET /api/purchases/{id}` - تفاصيل فاتورة مشتريات
- `POST /api/purchases` - إنشاء فاتورة مشتريات جديدة
- `PUT /api/purchases/{id}/cancel` - إلغاء فاتورة مشتريات

### 🛒 Point of Sale (POS)
- `GET /api/pos/products/search` - البحث عن المنتجات للبيع
- `GET /api/pos/customers/search` - البحث عن العملاء
- `POST /api/pos/quick-sale` - بيع سريع من نقطة البيع
- `GET /api/pos/daily-summary` - ملخص مبيعات اليوم
- `GET /api/pos/payment-methods` - طرق الدفع المتاحة

### 💰 Financial Management
- `GET /api/financial/chart-of-accounts` - دليل الحسابات
- `POST /api/financial/chart-of-accounts` - إضافة حساب جديد
- `GET /api/financial/journal-entries` - القيود المحاسبية
- `POST /api/financial/journal-entries` - إنشاء قيد محاسبي
- `GET /api/financial/receipts` - سندات القبض
- `POST /api/financial/receipts` - إنشاء سند قبض
- `GET /api/financial/trial-balance` - ميزان المراجعة

### 📦 Inventory Management
- `GET /api/inventory/stock` - أرصدة المخزون
- `GET /api/inventory/movements` - حركات المخزون
- `GET /api/inventory/transfers` - التحويلات بين الفروع
- `POST /api/inventory/transfers` - إنشاء تحويل جديد
- `PUT /api/inventory/transfers/{id}/send` - إرسال التحويل
- `PUT /api/inventory/transfers/{id}/receive` - استلام التحويل

### 👨‍💼 Employee Management
- `GET /api/employee` - قائمة الموظفين
- `GET /api/employee/{id}` - تفاصيل موظف
- `POST /api/employee` - إضافة موظف جديد
- `GET /api/employee/attendance` - سجلات الحضور
- `POST /api/employee/attendance/checkin` - تسجيل حضور
- `POST /api/employee/attendance/checkout` - تسجيل انصراف
- `GET /api/employee/departments` - قائمة الأقسام
- `GET /api/employee/positions` - قائمة المناصب

### 📊 Reports & Analytics
- `GET /api/reports/dashboard` - لوحة التحكم الرئيسية
- `GET /api/reports/sales-chart` - رسم بياني للمبيعات
- `GET /api/reports/top-products` - أفضل المنتجات مبيعاً
- `GET /api/reports/financial-summary` - الملخص المالي
- `GET /api/reports/customer-analysis` - تحليل العملاء
- `GET /api/reports/supplier-analysis` - تحليل الموردين

### ⚙️ Settings
- `GET /api/settings/branches` - قائمة الفروع
- `POST /api/settings/branches` - إضافة فرع جديد
- `GET /api/settings/categories` - قائمة الفئات
- `POST /api/settings/categories` - إضافة فئة جديدة
- `GET /api/settings/units` - قائمة الوحدات
- `POST /api/settings/units` - إضافة وحدة جديدة
- `GET /api/settings/users` - قائمة المستخدمين
- `POST /api/settings/users` - إضافة مستخدم جديد
- `GET /api/settings/system-info` - معلومات النظام

### 📋 Lookup Tables (للـ Dropdowns مع علامة +)
- `GET /api/lookup/customer-types` - أنواع العملاء
- `POST /api/lookup/customer-types` - إضافة نوع عميل جديد
- `GET /api/lookup/supplier-types` - أنواع الموردين
- `POST /api/lookup/supplier-types` - إضافة نوع مورد جديد
- `GET /api/lookup/countries` - قائمة البلدان
- `POST /api/lookup/countries` - إضافة بلد جديد
- `GET /api/lookup/cities` - قائمة المدن
- `POST /api/lookup/cities` - إضافة مدينة جديدة
- `GET /api/lookup/categories-tree` - شجرة التصنيفات
- `GET /api/lookup/accounts-tree` - شجرة الحسابات المالية

---

## 🗄️ **قاعدة البيانات الكاملة**

### الجداول المتاحة (25+ جدول)
- **Users** - المستخدمين
- **Branches** - الفروع
- **Customers** - العملاء
- **Suppliers** - الموردين
- **Categories** - فئات المنتجات
- **Units** - وحدات القياس
- **Products** - المنتجات
- **Sales** - فواتير المبيعات
- **SaleItems** - أصناف فواتير المبيعات
- **Purchases** - فواتير المشتريات
- **PurchaseItems** - أصناف فواتير المشتريات
- **ChartOfAccounts** - دليل الحسابات
- **JournalEntries** - القيود المحاسبية
- **JournalEntryDetails** - تفاصيل القيود المحاسبية
- **Receipts** - سندات القبض
- **Payments** - سندات الدفع
- **PaymentMethods** - طرق الدفع
- **ProductStocks** - أرصدة المخزون
- **StockMovements** - حركات المخزون
- **BranchTransfers** - التحويلات بين الفروع
- **BranchTransferDetails** - تفاصيل التحويلات
- **Employees** - الموظفين
- **Departments** - الأقسام
- **Positions** - المناصب
- **AttendanceRecords** - سجلات الحضور
- **Shifts** - الورديات
- **Counters** - العدادات التلقائية

### البيانات التجريبية المحملة تلقائياً
- ✅ مستخدم إداري: `admin` / `admin123`
- ✅ فرع رئيسي: الفرع الرئيسي
- ✅ 3 فئات منتجات: إلكترونيات، ملابس، أغذية
- ✅ 3 وحدات قياس: قطعة، كيلوجرام، متر
- ✅ عميل تجريبي: عميل تجريبي
- ✅ مورد تجريبي: مورد تجريبي
- ✅ منتج تجريبي: منتج تجريبي

---

## 🚀 **طريقة التشغيل**

### 1. تشغيل التطبيق
```bash
cd TerraRetailERP_Simple
dotnet run --urls "https://localhost:7000"
```

### 2. فتح Swagger UI
افتح المتصفح واذهب إلى: https://localhost:7000

### 3. اختبار جميع الوحدات
- ✅ Customer Management
- ✅ Supplier Management
- ✅ Product Management
- ✅ Sales Management
- ✅ Reports & Analytics
- ✅ System Settings

---

## 📝 **أمثلة على الاستخدام**

### إضافة عميل جديد
```json
{
  "nameAr": "أحمد محمد علي",
  "nameEn": "Ahmed Mohamed Ali",
  "phone1": "01234567890",
  "email": "<EMAIL>",
  "address": "القاهرة، مصر",
  "openingBalance": 1500
}
```

### إضافة مورد جديد
```json
{
  "nameAr": "شركة التوريدات المتقدمة",
  "nameEn": "Advanced Supply Company",
  "phone1": "01234567890",
  "email": "<EMAIL>",
  "address": "الإسكندرية، مصر",
  "contactPerson": "محمد أحمد",
  "openingBalance": 5000,
  "creditLimit": 20000,
  "paymentTerms": 30
}
```

### إضافة منتج جديد
```json
{
  "nameAr": "لابتوب ديل",
  "nameEn": "Dell Laptop",
  "description": "لابتوب ديل عالي الأداء",
  "categoryId": 1,
  "unitId": 1,
  "barcode": "1234567890123",
  "costPrice": 15000,
  "basePrice": 18000,
  "profitMargin": 20,
  "minimumStock": 5,
  "maximumStock": 50,
  "reorderPoint": 10
}
```

### إنشاء فاتورة مبيعات
```json
{
  "customerId": 1,
  "branchId": 1,
  "userId": 1,
  "saleType": 1,
  "discountPercentage": 5,
  "taxPercentage": 14,
  "paidAmount": 17000,
  "notes": "فاتورة تجريبية",
  "items": [
    {
      "productId": 1,
      "quantity": 1,
      "unitPrice": 18000,
      "unitCostPrice": 15000,
      "discountPercentage": 0,
      "taxPercentage": 14
    }
  ]
}
```

---

## 🎯 **المميزات المتقدمة**

### 🔢 **توليد الأكواد التلقائي**
- ✅ أكواد العملاء: CUS000001, CUS000002...
- ✅ أكواد الموردين: SUP000001, SUP000002...
- ✅ أكواد المنتجات: PRD000001, PRD000002...
- ✅ أرقام فواتير المبيعات: SAL00000001, SAL00000002...

### 💰 **الحسابات المالية**
- ✅ حساب الخصومات والضرائب تلقائياً
- ✅ تتبع أرصدة العملاء والموردين
- ✅ حساب هامش الربح
- ✅ تتبع المدفوع والمتبقي

### 📊 **التقارير المتقدمة**
- ✅ لوحة تحكم شاملة
- ✅ رسوم بيانية تفاعلية
- ✅ تحليلات مبيعات متقدمة
- ✅ إحصائيات مالية شاملة

### 🔍 **البحث والفلترة**
- ✅ بحث متقدم في جميع الوحدات
- ✅ فلترة حسب التاريخ والحالة
- ✅ فلترة حسب الفئات والأنواع
- ✅ بحث بالباركود للمنتجات

---

## 🎉 **مبروك! النظام الكامل جاهز!**

**✅ تم إكمال جميع الوحدات المطلوبة:**

1. ✅ **إدارة العملاء** - مكتملة 100%
2. ✅ **إدارة الموردين** - مكتملة 100%
3. ✅ **إدارة المنتجات** - مكتملة 100%
4. ✅ **إدارة المبيعات** - مكتملة 100%
5. ✅ **التقارير والتحليلات** - مكتملة 100%
6. ✅ **الإعدادات** - مكتملة 100%

**🚀 الآن يمكنك:**
- ✅ فتح Swagger UI على https://localhost:7000
- ✅ اختبار جميع الوحدات والـ APIs
- ✅ إضافة بيانات حقيقية
- ✅ البدء في تطوير واجهة Angular
- ✅ استخدام النظام في بيئة الإنتاج

---

---

## 🔗 **العلاقات والربط التلقائي**

### 💰 **النظام المالي المتكامل**
- ✅ **دليل حسابات شجري** - Chart of Accounts مع هيكل متدرج
- ✅ **قيود محاسبية تلقائية** - كل عملية تجارية تنشئ قيود تلقائياً
- ✅ **سندات قبض ودفع** - مع ربط تلقائي بالقيود المحاسبية
- ✅ **ميزان المراجعة** - تقرير شامل لجميع الحسابات
- ✅ **كشوف حسابات** - للعملاء والموردين مع الحركات التفصيلية

### 📦 **إدارة المخزون المتقدمة**
- ✅ **أرصدة المخزون** - تتبع دقيق لكل منتج في كل فرع
- ✅ **حركات المخزون** - سجل شامل لجميع حركات الدخول والخروج
- ✅ **التحويلات بين الفروع** - نظام متكامل للتحويل والاستلام
- ✅ **تسويات المخزون** - لتصحيح الأخطاء والفروقات
- ✅ **دفعات المنتجات** - تتبع دفعات الإنتاج وتواريخ الانتهاء

### 👥 **إدارة الموارد البشرية**
- ✅ **بيانات الموظفين** - مع الأقسام والمناصب والفروع
- ✅ **نظام الحضور والانصراف** - تسجيل تلقائي مع حساب الساعات
- ✅ **الورديات** - إدارة ورديات العمل المختلفة
- ✅ **الإجازات** - أنواع الإجازات وأرصدة الموظفين
- ✅ **التكامل البيومتري** - دعم أجهزة البصمة

### 🔢 **نظام العدادات التلقائية**
- ✅ **أكواد العملاء**: CUS000001, CUS000002...
- ✅ **أكواد الموردين**: SUP000001, SUP000002...
- ✅ **أكواد المنتجات**: PRD000001, PRD000002...
- ✅ **أرقام فواتير المبيعات**: SAL00000001, SAL00000002...
- ✅ **أرقام القيود المحاسبية**: JE202400000001...
- ✅ **أرقام سندات القبض**: REC202400001...
- ✅ **أرقام التحويلات**: TRF202400001...

---

## 🔄 **تدفق العمليات التلقائية**

### 🛒 **عند إنشاء فاتورة مبيعات:**
1. **توليد رقم الفاتورة** تلقائياً من العداد
2. **تقليل المخزون** للمنتجات المباعة
3. **إنشاء حركة مخزون** لكل صنف
4. **إنشاء قيد محاسبي تلقائي**:
   - مدين: العميل/الصندوق (حسب نوع البيع)
   - دائن: المبيعات
5. **قيد تكلفة البضاعة المباعة**:
   - مدين: تكلفة المبيعات
   - دائن: المخزون

### 🛍️ **عند إنشاء فاتورة مشتريات:**
1. **توليد رقم الفاتورة** تلقائياً
2. **زيادة المخزون** للمنتجات المشتراة
3. **تحديث متوسط التكلفة** للمنتجات
4. **إنشاء قيد محاسبي تلقائي**:
   - مدين: المخزون
   - دائن: المورد/الصندوق

### 💳 **عند إنشاء سند قبض:**
1. **توليد رقم السند** تلقائياً
2. **إنشاء قيد محاسبي تلقائي**:
   - مدين: الصندوق/البنك
   - دائن: العميل

### 🔄 **عند التحويل بين الفروع:**
1. **توليد رقم التحويل** تلقائياً
2. **تقليل المخزون** من الفرع المرسل
3. **زيادة المخزون** في الفرع المستقبل
4. **إنشاء حركات مخزون** في كلا الفرعين
5. **إنشاء قيود محاسبية** للتحويل

---

## 📊 **التقارير المتاحة**

### 📈 **التقارير المالية**
- ✅ **ميزان المراجعة** - جميع الحسابات مع الأرصدة
- ✅ **كشف حساب العميل** - تفصيلي مع جميع الحركات
- ✅ **كشف حساب المورد** - تفصيلي مع جميع الحركات
- ✅ **اليومية العامة** - جميع القيود المحاسبية
- ✅ **قائمة الدخل** - الإيرادات والمصروفات

### 📦 **تقارير المخزون**
- ✅ **أرصدة المخزون** - حالياً لجميع المنتجات
- ✅ **حركات المخزون** - تفصيلية حسب المنتج والفترة
- ✅ **المنتجات تحت الحد الأدنى** - تنبيهات إعادة الطلب
- ✅ **قيمة المخزون** - إجمالي قيمة المخزون

### 💰 **تقارير المبيعات**
- ✅ **لوحة تحكم المبيعات** - إحصائيات يومية وشهرية
- ✅ **أفضل المنتجات مبيعاً** - حسب الكمية والقيمة
- ✅ **تحليل العملاء** - أفضل العملاء وحجم المبيعات
- ✅ **الرسوم البيانية** - مبيعات يومية وشهرية وسنوية

### 👥 **تقارير الموظفين**
- ✅ **سجلات الحضور** - تفصيلية لكل موظف
- ✅ **ساعات العمل** - العادية والإضافية
- ✅ **التأخير والانصراف المبكر** - إحصائيات شاملة
- ✅ **أرصدة الإجازات** - لكل موظف حسب النوع

---

## 🎯 **المميزات المتقدمة**

### 🔐 **الأمان والصلاحيات**
- ✅ **تشفير كلمات المرور** باستخدام BCrypt
- ✅ **تتبع المستخدمين** - من أنشأ/عدل كل سجل
- ✅ **سجل التدقيق** - تتبع جميع العمليات
- ✅ **ربط المستخدمين بالفروع** - صلاحيات محددة

### 🌐 **دعم اللغة العربية**
- ✅ **ترميز UTF-8** لجميع النصوص العربية
- ✅ **أسماء عربية وإنجليزية** للكيانات
- ✅ **رسائل النظام** باللغة العربية
- ✅ **التقارير** بالعربية والإنجليزية

### ⚡ **الأداء والموثوقية**
- ✅ **معاملات قاعدة البيانات** لضمان التكامل
- ✅ **أقفال العدادات** لمنع التكرار
- ✅ **فهارس محسنة** للبحث السريع
- ✅ **التحقق من التوازن المحاسبي** تلقائياً

---

## 🚀 **الآن النظام جاهز 100%!**

### ✅ **ما تم إنجازه:**
1. **🏗️ البنية الأساسية** - 9 نماذج رئيسية مع العلاقات
2. **💰 النظام المالي** - دليل حسابات وقيود تلقائية
3. **📦 إدارة المخزون** - تتبع شامل وتحويلات
4. **👥 إدارة الموظفين** - حضور وإجازات
5. **📊 التقارير** - 20+ تقرير وإحصائية
6. **🔗 العلاقات** - ربط تلقائي بين جميع الوحدات
7. **🔢 العدادات** - توليد أكواد آمن ومتسلسل
8. **🌐 APIs** - 50+ endpoint موثق بالكامل

### 🎯 **جاهز للاستخدام:**
- **🌐 Swagger UI**: https://localhost:7000
- **📊 جميع الوحدات** متاحة ومختبرة
- **💾 قاعدة البيانات** مع بيانات تجريبية
- **🔗 العلاقات** مكتملة ومترابطة
- **📈 التقارير** شاملة ومفصلة

---

**🔗 للوصول للنظام الكامل: https://localhost:7000**

**🎉 النظام مكتمل 100% - جاهز لإنشاء واجهة Angular! 🚀**
