<!-- Terra Retail ERP - Supplier Statistics -->
<div class="supplier-stats-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">إحصائيات الموردين</h1>
          <p class="page-subtitle">تقرير شامل عن حالة الموردين وأكواد النظام</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-stroked-button (click)="goToSuppliers()">
          <mat-icon>list</mat-icon>
          <span>عرض الموردين</span>
        </button>
        <button mat-raised-button color="primary" (click)="addSupplier()">
          <mat-icon>add</mat-icon>
          <span>إضافة مورد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content" *ngIf="!isLoading && statistics && codeInfo">
    
    <!-- General Statistics -->
    <div class="stats-section">
      <h2 class="section-title">الإحصائيات العامة</h2>
      
      <div class="stats-grid">
        
        <!-- Total Suppliers -->
        <mat-card class="stat-card total-card">
          <mat-card-content>
            <div class="stat-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-info">
              <h3>إجمالي الموردين</h3>
              <p class="stat-number">{{ formatNumber(statistics.totalSuppliers) }}</p>
              <span class="stat-label">مورد مسجل</span>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Active Suppliers -->
        <mat-card class="stat-card active-card">
          <mat-card-content>
            <div class="stat-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <div class="stat-info">
              <h3>الموردين النشطين</h3>
              <p class="stat-number">{{ formatNumber(statistics.activeSuppliers) }}</p>
              <span class="stat-label">{{ getActivePercentage().toFixed(1) }}% من الإجمالي</span>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Inactive Suppliers -->
        <mat-card class="stat-card inactive-card">
          <mat-card-content>
            <div class="stat-icon">
              <mat-icon>cancel</mat-icon>
            </div>
            <div class="stat-info">
              <h3>الموردين غير النشطين</h3>
              <p class="stat-number">{{ formatNumber(statistics.inactiveSuppliers) }}</p>
              <span class="stat-label">{{ (100 - getActivePercentage()).toFixed(1) }}% من الإجمالي</span>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Total Balance -->
        <mat-card class="stat-card balance-card">
          <mat-card-content>
            <div class="stat-icon">
              <mat-icon>account_balance_wallet</mat-icon>
            </div>
            <div class="stat-info">
              <h3>إجمالي الأرصدة</h3>
              <p class="stat-number" [ngClass]="getBalanceClass(statistics.totalBalance)">
                {{ formatCurrency(statistics.totalBalance) }}
              </p>
              <span class="stat-label">رصيد إجمالي</span>
            </div>
          </mat-card-content>
        </mat-card>

      </div>
    </div>

    <!-- Financial Details -->
    <div class="financial-section">
      <h2 class="section-title">التفاصيل المالية</h2>
      
      <div class="financial-grid">
        
        <!-- Positive Balance -->
        <mat-card class="financial-card positive-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>trending_up</mat-icon>
              <span>الأرصدة الموجبة</span>
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="amount positive">{{ formatCurrency(statistics.positiveBalance) }}</div>
            <p class="description">المبالغ المستحقة للموردين</p>
          </mat-card-content>
        </mat-card>

        <!-- Negative Balance -->
        <mat-card class="financial-card negative-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>trending_down</mat-icon>
              <span>الأرصدة السالبة</span>
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="amount negative">{{ formatCurrency(statistics.negativeBalance) }}</div>
            <p class="description">المبالغ المستحقة من الموردين</p>
          </mat-card-content>
        </mat-card>

      </div>
    </div>

    <!-- Code Information -->
    <div class="code-section">
      <h2 class="section-title">معلومات أكواد الموردين</h2>
      
      <div class="code-grid">
        
        <!-- Current Status -->
        <mat-card class="code-card status-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>info</mat-icon>
              <span>الحالة الحالية</span>
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="code-info">
              <div class="info-row">
                <span class="label">آخر كود مستخدم:</span>
                <span class="value">{{ codeInfo.lastSupplierNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">الكود التالي:</span>
                <span class="value highlight">{{ codeInfo.nextSupplierCode }}</span>
              </div>
              <div class="info-row">
                <span class="label">تنسيق الأكواد:</span>
                <span class="value">{{ codeInfo.codeFormat }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Usage Statistics -->
        <mat-card class="code-card usage-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>analytics</mat-icon>
              <span>إحصائيات الاستخدام</span>
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="usage-info">
              <div class="usage-percentage">
                <span class="percentage-value">{{ getUsagePercentage().toFixed(2) }}%</span>
                <span class="percentage-label">من الأكواد المستخدمة</span>
              </div>
              <mat-progress-bar 
                [value]="getUsagePercentage()" 
                [ngClass]="getUsageStatus()">
              </mat-progress-bar>
              <div class="usage-status">
                <mat-chip [ngClass]="getUsageStatus()">
                  {{ getUsageStatusText() }}
                </mat-chip>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Capacity Information -->
        <mat-card class="code-card capacity-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>storage</mat-icon>
              <span>السعة والحدود</span>
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="capacity-info">
              <div class="capacity-row">
                <span class="label">الحد الأقصى للأكواد:</span>
                <span class="value">{{ formatNumber(codeInfo.maxPossibleCodes) }}</span>
              </div>
              <div class="capacity-row">
                <span class="label">الأكواد المتبقية:</span>
                <span class="value remaining">{{ formatNumber(codeInfo.remainingCodes) }}</span>
              </div>
              <div class="capacity-row">
                <span class="label">يمكن إضافة موردين:</span>
                <mat-chip [color]="codeInfo.canAddMore ? 'primary' : 'warn'">
                  {{ codeInfo.canAddMore ? 'نعم' : 'لا' }}
                </mat-chip>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

      </div>
    </div>

    <!-- Recommendations -->
    <div class="recommendations-section" *ngIf="getUsagePercentage() > 80">
      <mat-card class="recommendations-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>warning</mat-icon>
            <span>تنبيهات وتوصيات</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="recommendations-list">
            <div class="recommendation-item" *ngIf="getUsagePercentage() > 90">
              <mat-icon class="warning-icon">error</mat-icon>
              <div class="recommendation-text">
                <h4>تحذير: استخدام عالي للأكواد</h4>
                <p>تم استخدام أكثر من 90% من أكواد الموردين. يُنصح بمراجعة النظام قريباً.</p>
              </div>
            </div>
            <div class="recommendation-item" *ngIf="getUsagePercentage() > 80 && getUsagePercentage() <= 90">
              <mat-icon class="info-icon">info</mat-icon>
              <div class="recommendation-text">
                <h4>تنبيه: استخدام متقدم للأكواد</h4>
                <p>تم استخدام أكثر من 80% من أكواد الموردين. يُنصح بمتابعة الاستخدام.</p>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل إحصائيات الموردين...</p>
  </div>

</div>
