/* Terra Retail ERP - Supplier Details Styles */

.supplier-details-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--warning-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .back-btn {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      width: 48px !important;
      height: 48px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }
    }

    .header-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0 0 var(--spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .page-subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    .edit-btn {
      background: var(--warning-500) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-xl) !important;
      font-weight: 600 !important;
      box-shadow: var(--shadow-lg) !important;

      &:hover {
        background: var(--warning-600) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
      }
    }
  }
}

/* ===== CONTENT ===== */
.content {
  padding: var(--spacing-2xl) 0;
}

/* ===== OVERVIEW CARDS ===== */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.overview-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-xl) !important;
  }

  .mat-mdc-card-content {
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-lg) !important;
    padding: var(--spacing-xl) !important;
  }

  .card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: white;
    }
  }

  .card-info {
    flex: 1;

    h3 {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-600);
      margin: 0 0 var(--spacing-xs) 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    p {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
    }
  }

  &.balance-card {
    .card-icon {
      background: linear-gradient(135deg, var(--success-500), var(--success-600));
    }

    .balance-amount {
      &.positive {
        color: var(--success-600);
      }

      &.negative {
        color: var(--error-600);
      }

      &.zero {
        color: var(--gray-600);
      }
    }
  }

  &.credit-card {
    .card-icon {
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    }

    .credit-amount {
      color: var(--primary-600);
    }
  }

  &.rating-card {
    .card-icon {
      background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    }

    .rating-stars {
      color: var(--warning-600);
      font-size: 1.25rem;
    }
  }

  &.payment-card {
    .card-icon {
      background: linear-gradient(135deg, var(--info-500), var(--info-600));
    }

    .payment-terms {
      color: var(--info-600);
    }
  }
}

/* ===== TABS ===== */
.details-tabs {
  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 2px solid var(--gray-200) !important;

      .mat-mdc-tab {
        font-weight: 600 !important;
        font-size: 1rem !important;
        padding: var(--spacing-lg) var(--spacing-xl) !important;

        &.mdc-tab--active {
          color: var(--primary-600) !important;
        }
      }

      .mat-mdc-tab-header-pagination {
        display: none !important;
      }
    }
  }
}

.tab-content {
  padding: var(--spacing-2xl) 0;
}

/* ===== INFO GRID ===== */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);

  .full-width {
    grid-column: 1 / -1;
  }
}

.info-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--gray-200) !important;

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;
    border-bottom: 1px solid var(--gray-200) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.125rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;

      mat-icon {
        color: var(--primary-600) !important;
        font-size: 1.25rem !important;
        width: 1.25rem !important;
        height: 1.25rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--gray-100);

  &:last-child {
    border-bottom: none;
  }

  .label {
    font-weight: 600;
    color: var(--gray-700);
    flex-shrink: 0;
    margin-left: var(--spacing-md);
  }

  .value {
    color: var(--gray-900);
    text-align: left;

    &.link {
      color: var(--primary-600);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    &.positive {
      color: var(--success-600);
      font-weight: 600;
    }

    &.negative {
      color: var(--error-600);
      font-weight: 600;
    }

    &.zero {
      color: var(--gray-600);
    }
  }
}

.notes-text {
  color: var(--gray-700);
  line-height: 1.6;
  margin: 0;
}

/* ===== PRODUCTS LIST ===== */
.products-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.product-card {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
  }

  .mat-mdc-card-content {
    padding: var(--spacing-lg) !important;
  }

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);

    h3 {
      font-size: 1.125rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
      flex: 1;
    }

    .category-chip {
      background: var(--primary-100) !important;
      color: var(--primary-700) !important;
      font-size: 0.75rem !important;
      font-weight: 600 !important;
    }
  }

  .product-details {
    .detail-item {
      display: flex;
      justify-content: space-between;
      padding: var(--spacing-xs) 0;

      .label {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
      }

      .value {
        font-size: 0.875rem;
        color: var(--gray-900);
        font-weight: 600;
      }
    }
  }
}

/* ===== TRANSACTIONS LIST ===== */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.transaction-card {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--gray-200) !important;

  .mat-mdc-card-content {
    padding: var(--spacing-lg) !important;
  }

  .transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);

    .transaction-info {
      flex: 1;

      h3 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--gray-900);
        margin: 0 0 var(--spacing-xs) 0;
      }

      .reference {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin: 0;
      }
    }

    .transaction-amount {
      font-size: 1.25rem;
      font-weight: 700;

      &.positive {
        color: var(--success-600);
      }

      &.negative {
        color: var(--error-600);
      }
    }
  }

  .transaction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .date {
      font-size: 0.875rem;
      color: var(--gray-600);
    }

    .mat-mdc-chip {
      font-size: 0.75rem !important;
      font-weight: 600 !important;

      &.purchase {
        background: var(--error-100) !important;
        color: var(--error-700) !important;
      }

      &.payment {
        background: var(--success-100) !important;
        color: var(--success-700) !important;
      }
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: var(--primary-500);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }

  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: var(--spacing-md);

      .header-text {
        .page-title {
          font-size: 2rem;
        }
      }
    }
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .products-list {
    grid-template-columns: 1fr;
  }

  .content {
    padding: var(--spacing-xl) 0;
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-left {
      .header-text {
        .page-title {
          font-size: 1.75rem;
        }

        .page-subtitle {
          font-size: 1rem;
        }
      }
    }
  }

  .overview-card {
    .mat-mdc-card-content {
      flex-direction: column !important;
      text-align: center !important;
      gap: var(--spacing-md) !important;
    }

    .card-info {
      p {
        font-size: 1.25rem;
      }
    }
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);

    .label {
      margin-left: 0;
    }
  }

  .transaction-header {
    flex-direction: column !important;
    gap: var(--spacing-sm) !important;
  }
}
