using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الخزائن
    /// </summary>
    public class CashBox : BaseEntity
    {
        /// <summary>
        /// اسم الخزينة
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// كود الخزينة
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// الفرع التابعة له الخزينة
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// المسؤول عن الخزينة
        /// </summary>
        public int? ResponsibleUserId { get; set; }

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الحد الأدنى للرصيد
        /// </summary>
        public decimal? MinBalance { get; set; }

        /// <summary>
        /// الحد الأقصى للرصيد
        /// </summary>
        public decimal? MaxBalance { get; set; }

        /// <summary>
        /// هل الخزينة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الخزينة رئيسية
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// نوع الخزينة
        /// </summary>
        public CashBoxType CashBoxType { get; set; } = CashBoxType.Cash;

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// موقع الخزينة
        /// </summary>
        [MaxLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// وصف الخزينة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// تاريخ آخر حركة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        /// <summary>
        /// معرف الحساب المالي المرتبط
        /// </summary>
        public int? AccountId { get; set; }

        // Navigation Properties
        public virtual Branch Branch { get; set; } = null!;
        public virtual User? ResponsibleUser { get; set; }
        public virtual Account? Account { get; set; }
        public virtual ICollection<CashTransaction> Transactions { get; set; } = new List<CashTransaction>();
        public virtual ICollection<SalePayment> SalePayments { get; set; } = new List<SalePayment>();
    }

    /// <summary>
    /// أنواع الخزائن
    /// </summary>
    public enum CashBoxType
    {
        /// <summary>
        /// خزينة نقدية
        /// </summary>
        Cash = 1,

        /// <summary>
        /// خزينة بنكية
        /// </summary>
        Bank = 2,

        /// <summary>
        /// خزينة شيكات
        /// </summary>
        Checks = 3,

        /// <summary>
        /// خزينة بطاقات
        /// </summary>
        Cards = 4,

        /// <summary>
        /// خزينة إلكترونية
        /// </summary>
        Electronic = 5
    }
}
