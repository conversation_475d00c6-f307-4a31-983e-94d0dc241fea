using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("ProductStocks")]
    public class ProductStock
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        public int BranchId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal AvailableQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReservedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OnOrderQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalInQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalOutQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchMinStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchMaxStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchReorderPoint { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageCostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LastCostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal StockValue { get; set; } = 0;

        [StringLength(100)]
        public string? StorageLocation { get; set; }

        public bool IsAvailableForSale { get; set; } = true;

        public DateTime LastMovementDate { get; set; } = DateTime.Now;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
    }

    [Table("StockMovements")]
    public class StockMovement
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        public int BranchId { get; set; }

        [Required]
        [StringLength(40)]
        public string MovementNumber { get; set; } = string.Empty;

        public int MovementType { get; set; } // 1=In, 2=Out, 3=Transfer, 4=Adjustment

        [Required]
        [StringLength(100)]
        public string MovementReason { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceBefore { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public DateTime MovementDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? SourceType { get; set; } // Sale, Purchase, Transfer, Adjustment

        public int? SourceId { get; set; }

        public int UserId { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    [Table("BranchTransfers")]
    public class BranchTransfer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string TransferNumber { get; set; } = string.Empty;

        public DateTime TransferDate { get; set; } = DateTime.Now;

        public DateTime? ReceivedDate { get; set; }

        public int FromBranchId { get; set; }

        public int ToBranchId { get; set; }

        public int Status { get; set; } = 1; // 1=Pending, 2=Sent, 3=Received, 4=Cancelled

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; } = 0;

        public int TotalItems { get; set; } = 0;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int RequestedBy { get; set; }

        public int? SentBy { get; set; }

        public int? ReceivedBy { get; set; }

        public DateTime? SentAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Branch FromBranch { get; set; } = null!;
        public virtual Branch ToBranch { get; set; } = null!;
        public virtual User RequestedByUser { get; set; } = null!;
        public virtual User? SentByUser { get; set; }
        public virtual User? ReceivedByUser { get; set; }
        public virtual ICollection<BranchTransferDetail> BranchTransferDetails { get; set; } = new List<BranchTransferDetail>();
    }

    [Table("BranchTransferDetails")]
    public class BranchTransferDetail
    {
        [Key]
        public int Id { get; set; }

        public int BranchTransferId { get; set; }

        public int ProductId { get; set; }

        public int LineNumber { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal RequestedQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal SentQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReceivedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } = 0;

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(1000)]
        public string? ItemNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual BranchTransfer BranchTransfer { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    [Table("ProductBatches")]
    public class ProductBatch
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        public int BranchId { get; set; }

        [Required]
        [StringLength(50)]
        public string BatchNumber { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal RemainingQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        public DateTime? ExpiryDate { get; set; }

        public DateTime? ManufacturingDate { get; set; }

        [StringLength(100)]
        public string? SupplierBatchNumber { get; set; }

        public int? SupplierId { get; set; }

        public int? PurchaseId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual Supplier? Supplier { get; set; }
    }

    [Table("StockAdjustments")]
    public class StockAdjustment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string AdjustmentNumber { get; set; } = string.Empty;

        public DateTime AdjustmentDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(100)]
        public string AdjustmentReason { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public int BranchId { get; set; }

        public int Status { get; set; } = 1; // 1=Draft, 2=Approved, 3=Posted

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAdjustmentValue { get; set; } = 0;

        public int TotalItems { get; set; } = 0;

        public int UserId { get; set; }

        public int? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? ApprovedByUser { get; set; }
        public virtual ICollection<StockAdjustmentDetail> StockAdjustmentDetails { get; set; } = new List<StockAdjustmentDetail>();
    }

    [Table("StockAdjustmentDetails")]
    public class StockAdjustmentDetail
    {
        [Key]
        public int Id { get; set; }

        public int StockAdjustmentId { get; set; }

        public int ProductId { get; set; }

        public int LineNumber { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal SystemQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal PhysicalQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal AdjustmentQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AdjustmentValue { get; set; } = 0;

        [StringLength(1000)]
        public string? Reason { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual StockAdjustment StockAdjustment { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }




}
