import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Interfaces
interface Supplier {
  id: number;
  supplierCode: string;
  nameAr: string;
  nameEn?: string;
  supplierTypeId?: number;
  phone1: string;
  phone2?: string;
  email?: string;
  website?: string;
  address?: string;
  areaId?: number;
  contactPersonName?: string;
  contactPersonPhone?: string;
  contactPersonEmail?: string;
  paymentTerms: number;
  deliveryDays: number;
  creditLimit: number;
  openingBalance: number;
  currentBalance: number;
  taxNumber?: string;
  commercialRegister?: string;
  bankName?: string;
  bankAccountNumber?: string;
  rating?: number;
  notes?: string;
  isActive: boolean;
}

interface SupplierType {
  Id: number;
  NameAr: string;
  NameEn: string;
}

interface Area {
  Id: number;
  NameAr: string;
  NameEn: string;
  Code: string;
}

@Component({
  selector: 'app-edit-supplier',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './edit-supplier.component.html',
  styleUrls: ['./edit-supplier.component.scss']
})
export class EditSupplierComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  supplierForm: FormGroup;
  supplierId: number = 0;
  
  // Data
  supplier: Supplier | null = null;
  supplierTypes: SupplierType[] = [];
  areas: Area[] = [];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {
    this.supplierForm = this.createForm();
  }

  ngOnInit(): void {
    this.supplierId = Number(this.route.snapshot.paramMap.get('id'));
    if (this.supplierId) {
      this.loadSupplier();
      this.loadInitialData();
    } else {
      this.router.navigate(['/suppliers']);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      supplierCode: ['', [Validators.required]],
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: [''],
      supplierTypeId: [''],
      phone1: ['', [Validators.required]],
      phone2: [''],
      email: ['', [Validators.email]],
      website: [''],
      address: [''],
      areaId: [''],
      contactPersonName: [''],
      contactPersonPhone: [''],
      contactPersonEmail: ['', [Validators.email]],
      paymentTerms: [30, [Validators.required, Validators.min(0)]],
      deliveryDays: [7, [Validators.required, Validators.min(1)]],
      creditLimit: [0, [Validators.required, Validators.min(0)]],
      taxNumber: [''],
      commercialRegister: [''],
      bankName: [''],
      bankAccountNumber: [''],
      rating: [''],
      notes: [''],
      isActive: [true]
    });
  }

  /**
   * Load supplier data
   */
  private loadSupplier(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({
      next: (response) => {
        console.log('API Response:', response);
        console.log('Supplier Data:', response.supplier);
        this.supplier = response.supplier;
        this.populateForm();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading supplier:', error);
        this.showError('خطأ في تحميل بيانات المورد');
        this.isLoading = false;
        this.router.navigate(['/suppliers']);
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Populate form with supplier data
   */
  private populateForm(): void {
    if (this.supplier) {
      const data = this.supplier as any; // Use any to avoid TypeScript issues
      this.supplierForm.patchValue({
        supplierCode: data.SupplierCode || data.supplierCode || '',
        nameAr: data.NameAr || data.nameAr || '',
        nameEn: data.NameEn || data.nameEn || '',
        supplierTypeId: data.SupplierTypeId || data.supplierTypeId || '',
        phone1: data.Phone1 || data.phone1 || '',
        phone2: data.Phone2 || data.phone2 || '',
        email: data.Email || data.email || '',
        website: data.Website || data.website || '',
        address: data.Address || data.address || '',
        areaId: data.AreaId || data.areaId || '',
        contactPersonName: data.ContactPersonName || data.contactPersonName || '',
        contactPersonPhone: data.ContactPersonPhone || data.contactPersonPhone || '',
        contactPersonEmail: data.ContactPersonEmail || data.contactPersonEmail || '',
        paymentTerms: data.PaymentTerms || data.paymentTerms || 30,
        deliveryDays: data.DeliveryDays || data.deliveryDays || 7,
        creditLimit: data.CreditLimit || data.creditLimit || 0,
        taxNumber: data.TaxNumber || data.taxNumber || '',
        commercialRegister: data.CommercialRegister || data.commercialRegister || '',
        bankName: data.BankName || data.bankName || '',
        bankAccountNumber: data.BankAccountNumber || data.bankAccountNumber || '',
        rating: data.Rating || data.rating || '',
        notes: data.Notes || data.notes || '',
        isActive: data.IsActive !== undefined ? data.IsActive : (data.isActive !== false)
      });
    }
  }

  /**
   * Load initial data
   */
  private loadInitialData(): void {
    Promise.all([
      this.loadSupplierTypes(),
      this.loadAreas()
    ]).catch(error => {
      console.error('Error loading initial data:', error);
    });
  }

  /**
   * Load supplier types
   */
  private loadSupplierTypes(): Promise<void> {
    return new Promise((resolve) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({
        next: (response) => {
          this.supplierTypes = response.supplierTypes || [];
          resolve();
        },
        error: () => {
          this.supplierTypes = this.getMockSupplierTypes();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load areas
   */
  private loadAreas(): Promise<void> {
    return new Promise((resolve) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/areas-db').subscribe({
        next: (response) => {
          this.areas = response.areas || [];
          resolve();
        },
        error: () => {
          this.areas = this.getMockAreas();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Update supplier
   */
  updateSupplier(): void {
    if (this.supplierForm.valid) {
      this.isLoading = true;
      
      const formValue = this.supplierForm.value;
      const request = {
        nameAr: formValue.nameAr,
        nameEn: formValue.nameEn || null,
        supplierTypeId: formValue.supplierTypeId || null,
        phone1: formValue.phone1,
        phone2: formValue.phone2 || null,
        email: formValue.email || null,
        website: formValue.website || null,
        address: formValue.address || null,
        areaId: formValue.areaId || null,
        contactPersonName: formValue.contactPersonName || null,
        contactPersonPhone: formValue.contactPersonPhone || null,
        contactPersonEmail: formValue.contactPersonEmail || null,
        paymentTerms: formValue.paymentTerms,
        deliveryDays: formValue.deliveryDays,
        creditLimit: formValue.creditLimit,
        taxNumber: formValue.taxNumber || null,
        commercialRegister: formValue.commercialRegister || null,
        bankName: formValue.bankName || null,
        bankAccountNumber: formValue.bankAccountNumber || null,
        rating: formValue.rating || null,
        notes: formValue.notes || null,
        isActive: formValue.isActive
      };

      const sub = this.http.put<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`, request).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccess('تم تحديث بيانات المورد بنجاح');
          this.router.navigate(['/suppliers']);
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error updating supplier:', error);
          this.showError('خطأ في تحديث بيانات المورد');
        }
      });
      this.subscriptions.push(sub);
    } else {
      this.markFormGroupTouched();
      this.showError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.supplierForm.controls).forEach(key => {
      const control = this.supplierForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Go back to suppliers list
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Cancel and go back
   */
  cancel(): void {
    this.goBack();
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Get mock supplier types
   */
  private getMockSupplierTypes(): SupplierType[] {
    return [
      { Id: 1, NameAr: 'مورد محلي', NameEn: 'Local Supplier' },
      { Id: 2, NameAr: 'مورد دولي', NameEn: 'International Supplier' },
      { Id: 3, NameAr: 'مورد حكومي', NameEn: 'Government Supplier' }
    ];
  }

  /**
   * Get mock areas
   */
  private getMockAreas(): Area[] {
    return [
      { Id: 1, NameAr: 'القاهرة', NameEn: 'Cairo', Code: 'CAI' },
      { Id: 2, NameAr: 'الجيزة', NameEn: 'Giza', Code: 'GIZ' },
      { Id: 3, NameAr: 'الإسكندرية', NameEn: 'Alexandria', Code: 'ALX' },
      { Id: 4, NameAr: 'الدقهلية', NameEn: 'Dakahlia', Code: 'DKH' },
      { Id: 5, NameAr: 'الشرقية', NameEn: 'Sharqia', Code: 'SHR' },
      { Id: 6, NameAr: 'القليوبية', NameEn: 'Qalyubia', Code: 'QLY' }
    ];
  }
}
