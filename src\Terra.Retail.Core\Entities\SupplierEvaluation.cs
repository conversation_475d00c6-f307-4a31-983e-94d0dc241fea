using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// تقييمات الموردين
    /// </summary>
    public class SupplierEvaluation : BaseEntity
    {
        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// فترة التقييم (من)
        /// </summary>
        public DateTime EvaluationPeriodFrom { get; set; }

        /// <summary>
        /// فترة التقييم (إلى)
        /// </summary>
        public DateTime EvaluationPeriodTo { get; set; }

        /// <summary>
        /// تقييم جودة المنتجات (1-5)
        /// </summary>
        public decimal ProductQualityRating { get; set; } = 0;

        /// <summary>
        /// تقييم الالتزام بالمواعيد (1-5)
        /// </summary>
        public decimal DeliveryTimeRating { get; set; } = 0;

        /// <summary>
        /// تقييم الأسعار (1-5)
        /// </summary>
        public decimal PricingRating { get; set; } = 0;

        /// <summary>
        /// تقييم خدمة العملاء (1-5)
        /// </summary>
        public decimal CustomerServiceRating { get; set; } = 0;

        /// <summary>
        /// تقييم المرونة في التعامل (1-5)
        /// </summary>
        public decimal FlexibilityRating { get; set; } = 0;

        /// <summary>
        /// التقييم الإجمالي (1-5)
        /// </summary>
        public decimal OverallRating { get; set; } = 0;

        /// <summary>
        /// ملاحظات التقييم
        /// </summary>
        [MaxLength(1000)]
        public string? EvaluationNotes { get; set; }

        /// <summary>
        /// نقاط القوة
        /// </summary>
        [MaxLength(1000)]
        public string? Strengths { get; set; }

        /// <summary>
        /// نقاط الضعف
        /// </summary>
        [MaxLength(1000)]
        public string? Weaknesses { get; set; }

        /// <summary>
        /// التوصيات
        /// </summary>
        [MaxLength(1000)]
        public string? Recommendations { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بالتقييم
        /// </summary>
        public int EvaluatedById { get; set; }

        /// <summary>
        /// تاريخ التقييم
        /// </summary>
        public DateTime EvaluationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// هل التقييم معتمد
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// معرف المستخدم الذي اعتمد التقييم
        /// </summary>
        public int? ApprovedById { get; set; }

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        // Navigation Properties
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual User EvaluatedBy { get; set; } = null!;
        public virtual User? ApprovedBy { get; set; }
    }
}
