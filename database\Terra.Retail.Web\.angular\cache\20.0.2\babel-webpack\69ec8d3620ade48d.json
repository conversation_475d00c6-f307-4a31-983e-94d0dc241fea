{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nlet ShowOnDirtyErrorStateMatcher = /*#__PURE__*/(() => {\n  class ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.dirty || form && form.submitted));\n    }\n    static ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ShowOnDirtyErrorStateMatcher,\n      factory: ShowOnDirtyErrorStateMatcher.ɵfac\n    });\n  }\n  return ShowOnDirtyErrorStateMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nlet ErrorStateMatcher = /*#__PURE__*/(() => {\n  class ErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.touched || form && form.submitted));\n    }\n    static ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ErrorStateMatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ErrorStateMatcher,\n      factory: ErrorStateMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ErrorStateMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };", "map": {"version": 3, "names": ["i0", "Injectable", "ShowOnDirtyErrorStateMatcher", "isErrorState", "control", "form", "invalid", "dirty", "submitted", "ɵfac", "ShowOnDirtyErrorStateMatcher_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ErrorStateMatcher", "touched", "ErrorStateMatcher_Factory", "providedIn", "E", "S"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/error-options-DCNQlTOA.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AAAA,IACMC,4BAA4B;EAAlC,MAAMA,4BAA4B,CAAC;IAC/BC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;MACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACG,KAAK,IAAKF,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;IACxF;IACA,OAAOC,IAAI,YAAAC,qCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFT,4BAA4B;IAAA;IAC/H,OAAOU,KAAK,kBAD6EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EACYZ,4BAA4B;MAAAa,OAAA,EAA5Bb,4BAA4B,CAAAO;IAAA;EACvI;EAAC,OANKP,4BAA4B;AAAA;AAOlC;EAAA,QAAAc,SAAA,oBAAAA,SAAA;AAAA;AAGA;AAAA,IACMC,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpBd,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;MACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACc,OAAO,IAAKb,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;IAC1F;IACA,OAAOC,IAAI,YAAAU,0BAAAR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFM,iBAAiB;IAAA;IACpH,OAAOL,KAAK,kBAZ6EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EAYYG,iBAAiB;MAAAF,OAAA,EAAjBE,iBAAiB,CAAAR,IAAA;MAAAW,UAAA,EAAc;IAAM;EAChJ;EAAC,OANKH,iBAAiB;AAAA;AAOvB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAKA,SAASC,iBAAiB,IAAII,CAAC,EAAEnB,4BAA4B,IAAIoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}