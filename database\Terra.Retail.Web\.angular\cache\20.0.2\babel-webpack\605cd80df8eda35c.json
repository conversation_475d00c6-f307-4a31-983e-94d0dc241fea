{"ast": null, "code": "export { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport * as i0 from '@angular/core';\nimport { Version, inject, Injectable, NgModule } from '@angular/core';\nexport { A as AnimationCurves, a as AnimationDurations, M as MATERIAL_ANIMATIONS, _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nexport { a as MATERIAL_SANITY_CHECKS, M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nexport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { D as DateAdapter, M as MAT_DATE_LOCALE, a as MAT_DATE_FORMATS } from './date-formats-K6TQue-Y.mjs';\nexport { b as MAT_DATE_LOCALE_FACTORY } from './date-formats-K6TQue-Y.mjs';\nexport { E as ErrorStateMatcher, S as ShowOnDirtyErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nexport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nexport { M as MatLine, a as MatLineModule, s as setLines } from './line-Bz5f9Cyx.mjs';\nexport { d as MAT_OPTGROUP, c as MAT_OPTION_PARENT_COMPONENT, a as MatOptgroup, M as MatOption, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-BzhYL_xC.mjs';\nexport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nexport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nexport { a as MAT_RIPPLE_GLOBAL_OPTIONS, M as MatRipple, c as RippleRef, R as RippleRenderer, b as RippleState, d as defaultRippleAnimationConfig } from './ripple-BYgV4oZC.mjs';\nexport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nexport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nexport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/keycodes';\nimport '@angular/cdk/private';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\n\n/** Current version of Angular Material. */\nconst VERSION = /*#__PURE__*/new Version('20.0.3');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings with an out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/**\n * Matches a time string. Supported formats:\n * - {{hours}}:{{minutes}}\n * - {{hours}}:{{minutes}}:{{seconds}}\n * - {{hours}}:{{minutes}} AM/PM\n * - {{hours}}:{{minutes}}:{{seconds}} AM/PM\n * - {{hours}}.{{minutes}}\n * - {{hours}}.{{minutes}}.{{seconds}}\n * - {{hours}}.{{minutes}} AM/PM\n * - {{hours}}.{{minutes}}.{{seconds}} AM/PM\n */\nconst TIME_REGEX = /^(\\d?\\d)[:.](\\d?\\d)(?:[:.](\\d?\\d))?\\s*(AM|PM)?$/i;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nlet NativeDateAdapter = /*#__PURE__*/(() => {\n  class NativeDateAdapter extends DateAdapter {\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    useUtcForDisplay = false;\n    /** The injected locale. */\n    _matDateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    constructor() {\n      super();\n      const matDateLocale = inject(MAT_DATE_LOCALE, {\n        optional: true\n      });\n      if (matDateLocale !== undefined) {\n        this._matDateLocale = matDateLocale;\n      }\n      super.setLocale(this._matDateLocale);\n    }\n    getYear(date) {\n      return date.getFullYear();\n    }\n    getMonth(date) {\n      return date.getMonth();\n    }\n    getDate(date) {\n      return date.getDate();\n    }\n    getDayOfWeek(date) {\n      return date.getDay();\n    }\n    getMonthNames(style) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        month: style,\n        timeZone: 'utc'\n      });\n      return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        day: 'numeric',\n        timeZone: 'utc'\n      });\n      return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        weekday: style,\n        timeZone: 'utc'\n      });\n      return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        year: 'numeric',\n        timeZone: 'utc'\n      });\n      return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n      // At the time of writing `Intl.Locale` isn't available\n      // in the internal types so we need to cast to `any`.\n      if (typeof Intl !== 'undefined' && Intl.Locale) {\n        const locale = new Intl.Locale(this.locale);\n        // Some browsers implement a `getWeekInfo` method while others have a `weekInfo` getter.\n        // Note that this isn't supported in all browsers so we need to null check it.\n        const firstDay = (locale.getWeekInfo?.() || locale.weekInfo)?.firstDay ?? 0;\n        // `weekInfo.firstDay` is a number between 1 and 7 where, starting from Monday,\n        // whereas our representation is 0 to 6 where 0 is Sunday so we need to normalize it.\n        return firstDay === 7 ? 0 : firstDay;\n      }\n      // Default to Sunday if the browser doesn't provide the week information.\n      return 0;\n    }\n    getNumDaysInMonth(date) {\n      return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n      return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Check for invalid month and date (except upper bound on date which we have to check after\n        // creating the Date).\n        if (month < 0 || month > 11) {\n          throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n        }\n        if (date < 1) {\n          throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n        }\n      }\n      let result = this._createDateWithOverflow(year, month, date);\n      // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n      if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n      }\n      return result;\n    }\n    today() {\n      return new Date();\n    }\n    parse(value, parseFormat) {\n      // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n      // parameters.\n      if (typeof value == 'number') {\n        return new Date(value);\n      }\n      return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n      if (!this.isValid(date)) {\n        throw Error('NativeDateAdapter: Cannot format invalid date.');\n      }\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        ...displayFormat,\n        timeZone: 'utc'\n      });\n      return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n      return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n      let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n      // It's possible to wind up in the wrong month if the original month has more days than the new\n      // month. In this case we want to go to the last day of the desired month.\n      // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n      // guarantee this.\n      if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n        newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n      }\n      return newDate;\n    }\n    addCalendarDays(date, days) {\n      return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n      return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n      if (typeof value === 'string') {\n        if (!value) {\n          return null;\n        }\n        // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n        // string is the right format first.\n        if (ISO_8601_REGEX.test(value)) {\n          let date = new Date(value);\n          if (this.isValid(date)) {\n            return date;\n          }\n        }\n      }\n      return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n      return obj instanceof Date;\n    }\n    isValid(date) {\n      return !isNaN(date.getTime());\n    }\n    invalid() {\n      return new Date(NaN);\n    }\n    setTime(target, hours, minutes, seconds) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!inRange(hours, 0, 23)) {\n          throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n        }\n        if (!inRange(minutes, 0, 59)) {\n          throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n        }\n        if (!inRange(seconds, 0, 59)) {\n          throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n        }\n      }\n      const clone = this.clone(target);\n      clone.setHours(hours, minutes, seconds, 0);\n      return clone;\n    }\n    getHours(date) {\n      return date.getHours();\n    }\n    getMinutes(date) {\n      return date.getMinutes();\n    }\n    getSeconds(date) {\n      return date.getSeconds();\n    }\n    parseTime(userValue, parseFormat) {\n      if (typeof userValue !== 'string') {\n        return userValue instanceof Date ? new Date(userValue.getTime()) : null;\n      }\n      const value = userValue.trim();\n      if (value.length === 0) {\n        return null;\n      }\n      // Attempt to parse the value directly.\n      let result = this._parseTimeString(value);\n      // Some locales add extra characters around the time, but are otherwise parseable\n      // (e.g. `00:05 ч.` in bg-BG). Try replacing all non-number and non-colon characters.\n      if (result === null) {\n        const withoutExtras = value.replace(/[^0-9:(AM|PM)]/gi, '').trim();\n        if (withoutExtras.length > 0) {\n          result = this._parseTimeString(withoutExtras);\n        }\n      }\n      return result || this.invalid();\n    }\n    addSeconds(date, amount) {\n      return new Date(date.getTime() + amount * 1000);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n      // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n      // To work around this we use `setFullYear` and `setHours` instead.\n      const d = new Date();\n      d.setFullYear(year, month, date);\n      d.setHours(0, 0, 0, 0);\n      return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n      return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n      // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n      // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n      const d = new Date();\n      d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n      d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n      return dtf.format(d);\n    }\n    /**\n     * Attempts to parse a time string into a date object. Returns null if it cannot be parsed.\n     * @param value Time string to parse.\n     */\n    _parseTimeString(value) {\n      // Note: we can technically rely on the browser for the time parsing by generating\n      // an ISO string and appending the string to the end of it. We don't do it, because\n      // browsers aren't consistent in what they support. Some examples:\n      // - Safari doesn't support AM/PM.\n      // - Firefox produces a valid date object if the time string has overflows (e.g. 12:75) while\n      //   other browsers produce an invalid date.\n      // - Safari doesn't allow padded numbers.\n      const parsed = value.toUpperCase().match(TIME_REGEX);\n      if (parsed) {\n        let hours = parseInt(parsed[1]);\n        const minutes = parseInt(parsed[2]);\n        let seconds = parsed[3] == null ? undefined : parseInt(parsed[3]);\n        const amPm = parsed[4];\n        if (hours === 12) {\n          hours = amPm === 'AM' ? 0 : hours;\n        } else if (amPm === 'PM') {\n          hours += 12;\n        }\n        if (inRange(hours, 0, 23) && inRange(minutes, 0, 59) && (seconds == null || inRange(seconds, 0, 59))) {\n          return this.setTime(this.today(), hours, minutes, seconds || 0);\n        }\n      }\n      return null;\n    }\n    static ɵfac = function NativeDateAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NativeDateAdapter)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NativeDateAdapter,\n      factory: NativeDateAdapter.ɵfac\n    });\n  }\n  return NativeDateAdapter;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Checks whether a number is within a certain range. */\nfunction inRange(value, min, max) {\n  return !isNaN(value) && value >= min && value <= max;\n}\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null,\n    timeInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    timeInput: {\n      hour: 'numeric',\n      minute: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    },\n    timeOptionLabel: {\n      hour: 'numeric',\n      minute: 'numeric'\n    }\n  }\n};\nlet NativeDateModule = /*#__PURE__*/(() => {\n  class NativeDateModule {\n    static ɵfac = function NativeDateModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NativeDateModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NativeDateModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    });\n  }\n  return NativeDateModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatNativeDateModule = /*#__PURE__*/(() => {\n  class MatNativeDateModule {\n    static ɵfac = function MatNativeDateModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatNativeDateModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatNativeDateModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideNativeDateAdapter()]\n    });\n  }\n  return MatNativeDateModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\nexport { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_NATIVE_DATE_FORMATS, MatNativeDateModule, NativeDateAdapter, NativeDateModule, VERSION, provideNativeDateAdapter };", "map": {"version": 3, "names": ["_", "_MatInternalFormField", "i0", "Version", "inject", "Injectable", "NgModule", "A", "AnimationCurves", "a", "AnimationDurations", "M", "MATERIAL_ANIMATIONS", "_animationsDisabled", "MATERIAL_SANITY_CHECKS", "MatCommonModule", "_ErrorStateTracker", "D", "DateAdapter", "MAT_DATE_LOCALE", "MAT_DATE_FORMATS", "b", "MAT_DATE_LOCALE_FACTORY", "E", "ErrorStateMatcher", "S", "ShowOnDirtyErrorStateMatcher", "_StructuralStylesLoader", "MatLine", "MatLineModule", "s", "setLines", "d", "MAT_OPTGROUP", "c", "MAT_OPTION_PARENT_COMPONENT", "MatOptgroup", "MatOption", "e", "MatOptionSelectionChange", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MatOptionModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "RippleRef", "R", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RippleState", "defaultRippleAnimationConfig", "MatRippleModule", "MatPseudoCheckbox", "MatPseudoCheckboxModule", "VERSION", "ISO_8601_REGEX", "TIME_REGEX", "range", "length", "valueFunction", "valuesArray", "Array", "i", "NativeDateAdapter", "useUtcForDisplay", "_matDateLocale", "optional", "constructor", "matDateLocale", "undefined", "setLocale", "getYear", "date", "getFullYear", "getMonth", "getDate", "getDayOfWeek", "getDay", "getMonthNames", "style", "dtf", "Intl", "DateTimeFormat", "locale", "month", "timeZone", "_format", "Date", "getDateNames", "day", "getDayOfWeekNames", "weekday", "getYearName", "year", "getFirstDayOfWeek", "Locale", "firstDay", "getWeekInfo", "weekInfo", "getNumDaysInMonth", "_createDateWithOverflow", "clone", "getTime", "createDate", "ngDevMode", "Error", "result", "today", "parse", "value", "parseFormat", "format", "displayFormat", "<PERSON><PERSON><PERSON><PERSON>", "addCalendarYears", "years", "addCalendarMonths", "months", "newDate", "addCalendarDays", "days", "toIso8601", "getUTCFullYear", "_2digit", "getUTCMonth", "getUTCDate", "join", "deserialize", "test", "isDateInstance", "obj", "isNaN", "invalid", "NaN", "setTime", "target", "hours", "minutes", "seconds", "inRange", "setHours", "getHours", "getMinutes", "getSeconds", "parseTime", "userValue", "trim", "_parseTimeString", "withoutExtras", "replace", "addSeconds", "amount", "setFullYear", "n", "slice", "setUTCFullYear", "setUTCHours", "getMilliseconds", "parsed", "toUpperCase", "match", "parseInt", "amPm", "ɵfac", "NativeDateAdapter_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "min", "max", "MAT_NATIVE_DATE_FORMATS", "dateInput", "timeInput", "display", "hour", "minute", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "timeOptionLabel", "NativeDateModule", "NativeDateModule_Factory", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useClass", "MatNativeDateModule", "MatNativeDateModule_Factory", "provideNativeDateAdapter", "formats", "useValue"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/core.mjs"], "sourcesContent": ["export { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport * as i0 from '@angular/core';\nimport { Version, inject, Injectable, NgModule } from '@angular/core';\nexport { A as AnimationCurves, a as AnimationDurations, M as MATERIAL_ANIMATIONS, _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nexport { a as MATERIAL_SANITY_CHECKS, M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nexport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { D as DateAdapter, M as MAT_DATE_LOCALE, a as MAT_DATE_FORMATS } from './date-formats-K6TQue-Y.mjs';\nexport { b as MAT_DATE_LOCALE_FACTORY } from './date-formats-K6TQue-Y.mjs';\nexport { E as ErrorStateMatcher, S as ShowOnDirtyErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nexport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nexport { M as MatLine, a as MatLineModule, s as setLines } from './line-Bz5f9Cyx.mjs';\nexport { d as MAT_OPTGROUP, c as MAT_OPTION_PARENT_COMPONENT, a as MatOptgroup, M as MatOption, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-BzhYL_xC.mjs';\nexport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nexport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nexport { a as MAT_RIPPLE_GLOBAL_OPTIONS, M as MatRipple, c as RippleRef, R as RippleRenderer, b as RippleState, d as defaultRippleAnimationConfig } from './ripple-BYgV4oZC.mjs';\nexport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nexport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nexport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/keycodes';\nimport '@angular/cdk/private';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\n\n/** Current version of Angular Material. */\nconst VERSION = new Version('20.0.3');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings with an out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/**\n * Matches a time string. Supported formats:\n * - {{hours}}:{{minutes}}\n * - {{hours}}:{{minutes}}:{{seconds}}\n * - {{hours}}:{{minutes}} AM/PM\n * - {{hours}}:{{minutes}}:{{seconds}} AM/PM\n * - {{hours}}.{{minutes}}\n * - {{hours}}.{{minutes}}.{{seconds}}\n * - {{hours}}.{{minutes}} AM/PM\n * - {{hours}}.{{minutes}}.{{seconds}} AM/PM\n */\nconst TIME_REGEX = /^(\\d?\\d)[:.](\\d?\\d)(?:[:.](\\d?\\d))?\\s*(AM|PM)?$/i;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n    const valuesArray = Array(length);\n    for (let i = 0; i < length; i++) {\n        valuesArray[i] = valueFunction(i);\n    }\n    return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    useUtcForDisplay = false;\n    /** The injected locale. */\n    _matDateLocale = inject(MAT_DATE_LOCALE, { optional: true });\n    constructor() {\n        super();\n        const matDateLocale = inject(MAT_DATE_LOCALE, { optional: true });\n        if (matDateLocale !== undefined) {\n            this._matDateLocale = matDateLocale;\n        }\n        super.setLocale(this._matDateLocale);\n    }\n    getYear(date) {\n        return date.getFullYear();\n    }\n    getMonth(date) {\n        return date.getMonth();\n    }\n    getDate(date) {\n        return date.getDate();\n    }\n    getDayOfWeek(date) {\n        return date.getDay();\n    }\n    getMonthNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { month: style, timeZone: 'utc' });\n        return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n        const dtf = new Intl.DateTimeFormat(this.locale, { day: 'numeric', timeZone: 'utc' });\n        return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { weekday: style, timeZone: 'utc' });\n        return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { year: 'numeric', timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n        // At the time of writing `Intl.Locale` isn't available\n        // in the internal types so we need to cast to `any`.\n        if (typeof Intl !== 'undefined' && Intl.Locale) {\n            const locale = new Intl.Locale(this.locale);\n            // Some browsers implement a `getWeekInfo` method while others have a `weekInfo` getter.\n            // Note that this isn't supported in all browsers so we need to null check it.\n            const firstDay = (locale.getWeekInfo?.() || locale.weekInfo)?.firstDay ?? 0;\n            // `weekInfo.firstDay` is a number between 1 and 7 where, starting from Monday,\n            // whereas our representation is 0 to 6 where 0 is Sunday so we need to normalize it.\n            return firstDay === 7 ? 0 : firstDay;\n        }\n        // Default to Sunday if the browser doesn't provide the week information.\n        return 0;\n    }\n    getNumDaysInMonth(date) {\n        return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n        return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Check for invalid month and date (except upper bound on date which we have to check after\n            // creating the Date).\n            if (month < 0 || month > 11) {\n                throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n            }\n            if (date < 1) {\n                throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n            }\n        }\n        let result = this._createDateWithOverflow(year, month, date);\n        // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n        if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n        }\n        return result;\n    }\n    today() {\n        return new Date();\n    }\n    parse(value, parseFormat) {\n        // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n        // parameters.\n        if (typeof value == 'number') {\n            return new Date(value);\n        }\n        return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n        if (!this.isValid(date)) {\n            throw Error('NativeDateAdapter: Cannot format invalid date.');\n        }\n        const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat, timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n        return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n        let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n        // It's possible to wind up in the wrong month if the original month has more days than the new\n        // month. In this case we want to go to the last day of the desired month.\n        // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n        // guarantee this.\n        if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n            newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n        }\n        return newDate;\n    }\n    addCalendarDays(date, days) {\n        return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n        return [\n            date.getUTCFullYear(),\n            this._2digit(date.getUTCMonth() + 1),\n            this._2digit(date.getUTCDate()),\n        ].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n        if (typeof value === 'string') {\n            if (!value) {\n                return null;\n            }\n            // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n            // string is the right format first.\n            if (ISO_8601_REGEX.test(value)) {\n                let date = new Date(value);\n                if (this.isValid(date)) {\n                    return date;\n                }\n            }\n        }\n        return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n        return obj instanceof Date;\n    }\n    isValid(date) {\n        return !isNaN(date.getTime());\n    }\n    invalid() {\n        return new Date(NaN);\n    }\n    setTime(target, hours, minutes, seconds) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!inRange(hours, 0, 23)) {\n                throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n            }\n            if (!inRange(minutes, 0, 59)) {\n                throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n            }\n            if (!inRange(seconds, 0, 59)) {\n                throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n            }\n        }\n        const clone = this.clone(target);\n        clone.setHours(hours, minutes, seconds, 0);\n        return clone;\n    }\n    getHours(date) {\n        return date.getHours();\n    }\n    getMinutes(date) {\n        return date.getMinutes();\n    }\n    getSeconds(date) {\n        return date.getSeconds();\n    }\n    parseTime(userValue, parseFormat) {\n        if (typeof userValue !== 'string') {\n            return userValue instanceof Date ? new Date(userValue.getTime()) : null;\n        }\n        const value = userValue.trim();\n        if (value.length === 0) {\n            return null;\n        }\n        // Attempt to parse the value directly.\n        let result = this._parseTimeString(value);\n        // Some locales add extra characters around the time, but are otherwise parseable\n        // (e.g. `00:05 ч.` in bg-BG). Try replacing all non-number and non-colon characters.\n        if (result === null) {\n            const withoutExtras = value.replace(/[^0-9:(AM|PM)]/gi, '').trim();\n            if (withoutExtras.length > 0) {\n                result = this._parseTimeString(withoutExtras);\n            }\n        }\n        return result || this.invalid();\n    }\n    addSeconds(date, amount) {\n        return new Date(date.getTime() + amount * 1000);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setFullYear` and `setHours` instead.\n        const d = new Date();\n        d.setFullYear(year, month, date);\n        d.setHours(0, 0, 0, 0);\n        return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n        return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n        const d = new Date();\n        d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n        d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n        return dtf.format(d);\n    }\n    /**\n     * Attempts to parse a time string into a date object. Returns null if it cannot be parsed.\n     * @param value Time string to parse.\n     */\n    _parseTimeString(value) {\n        // Note: we can technically rely on the browser for the time parsing by generating\n        // an ISO string and appending the string to the end of it. We don't do it, because\n        // browsers aren't consistent in what they support. Some examples:\n        // - Safari doesn't support AM/PM.\n        // - Firefox produces a valid date object if the time string has overflows (e.g. 12:75) while\n        //   other browsers produce an invalid date.\n        // - Safari doesn't allow padded numbers.\n        const parsed = value.toUpperCase().match(TIME_REGEX);\n        if (parsed) {\n            let hours = parseInt(parsed[1]);\n            const minutes = parseInt(parsed[2]);\n            let seconds = parsed[3] == null ? undefined : parseInt(parsed[3]);\n            const amPm = parsed[4];\n            if (hours === 12) {\n                hours = amPm === 'AM' ? 0 : hours;\n            }\n            else if (amPm === 'PM') {\n                hours += 12;\n            }\n            if (inRange(hours, 0, 23) &&\n                inRange(minutes, 0, 59) &&\n                (seconds == null || inRange(seconds, 0, 59))) {\n                return this.setTime(this.today(), hours, minutes, seconds || 0);\n            }\n        }\n        return null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateAdapter, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateAdapter });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateAdapter, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n/** Checks whether a number is within a certain range. */\nfunction inRange(value, min, max) {\n    return !isNaN(value) && value >= min && value <= max;\n}\n\nconst MAT_NATIVE_DATE_FORMATS = {\n    parse: {\n        dateInput: null,\n        timeInput: null,\n    },\n    display: {\n        dateInput: { year: 'numeric', month: 'numeric', day: 'numeric' },\n        timeInput: { hour: 'numeric', minute: 'numeric' },\n        monthYearLabel: { year: 'numeric', month: 'short' },\n        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },\n        monthYearA11yLabel: { year: 'numeric', month: 'long' },\n        timeOptionLabel: { hour: 'numeric', minute: 'numeric' },\n    },\n};\n\nclass NativeDateModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateModule, providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }],\n                }]\n        }] });\nclass MatNativeDateModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNativeDateModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNativeDateModule, providers: [provideNativeDateAdapter()] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [provideNativeDateAdapter()],\n                }]\n        }] });\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n    return [\n        { provide: DateAdapter, useClass: NativeDateAdapter },\n        { provide: MAT_DATE_FORMATS, useValue: formats },\n    ];\n}\n\nexport { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_NATIVE_DATE_FORMATS, MatNativeDateModule, NativeDateAdapter, NativeDateModule, VERSION, provideNativeDateAdapter };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,qBAAqB,QAAQ,oCAAoC;AAC/E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACrE,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEZ,CAAC,IAAIa,mBAAmB,QAAQ,0BAA0B;AAC5I,SAASJ,CAAC,IAAIK,sBAAsB,EAAEH,CAAC,IAAII,eAAe,QAAQ,8BAA8B;AAChG,SAASf,CAAC,IAAIgB,kBAAkB,QAAQ,4BAA4B;AACpE,SAASC,CAAC,IAAIC,WAAW,EAAEP,CAAC,IAAIQ,eAAe,EAAEV,CAAC,IAAIW,gBAAgB,QAAQ,6BAA6B;AAC3G,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,6BAA6B;AAC1E,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,4BAA4B,QAAQ,8BAA8B;AACxG,SAAS1B,CAAC,IAAI2B,uBAAuB,QAAQ,kCAAkC;AAC/E,SAAShB,CAAC,IAAIiB,OAAO,EAAEnB,CAAC,IAAIoB,aAAa,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,qBAAqB;AACrF,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,2BAA2B,EAAE1B,CAAC,IAAI2B,WAAW,EAAEzB,CAAC,IAAI0B,SAAS,EAAEC,CAAC,IAAIC,wBAAwB,EAAEvC,CAAC,IAAIwC,6BAA6B,EAAEnB,CAAC,IAAIoB,wBAAwB,QAAQ,uBAAuB;AAC/N,SAAS9B,CAAC,IAAI+B,eAAe,QAAQ,sBAAsB;AAC3D,SAAS/B,CAAC,IAAIgC,eAAe,QAAQ,8BAA8B;AACnE,SAASlC,CAAC,IAAImC,yBAAyB,EAAEjC,CAAC,IAAIkC,SAAS,EAAEX,CAAC,IAAIY,SAAS,EAAEC,CAAC,IAAIC,cAAc,EAAE3B,CAAC,IAAI4B,WAAW,EAAEjB,CAAC,IAAIkB,4BAA4B,QAAQ,uBAAuB;AAChL,SAASvC,CAAC,IAAIwC,eAAe,QAAQ,sBAAsB;AAC3D,SAASxC,CAAC,IAAIyC,iBAAiB,QAAQ,gCAAgC;AACvE,SAASzC,CAAC,IAAI0C,uBAAuB,QAAQ,uCAAuC;AACpF,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAC1B,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;;AAE9B;AACA,MAAMC,OAAO,gBAAG,IAAInD,OAAO,CAAC,QAAQ,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMoD,cAAc,GAAG,oFAAoF;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,kDAAkD;AACrE;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAClC,MAAMC,WAAW,GAAGC,KAAK,CAACH,MAAM,CAAC;EACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC7BF,WAAW,CAACE,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,CAAC;EACrC;EACA,OAAOF,WAAW;AACtB;AACA;AAAA,IACMG,iBAAiB;EAAvB,MAAMA,iBAAiB,SAAS7C,WAAW,CAAC;IACxC;AACJ;AACA;AACA;IACI8C,gBAAgB,GAAG,KAAK;IACxB;IACAC,cAAc,GAAG7D,MAAM,CAACe,eAAe,EAAE;MAAE+C,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC5DC,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAMC,aAAa,GAAGhE,MAAM,CAACe,eAAe,EAAE;QAAE+C,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjE,IAAIE,aAAa,KAAKC,SAAS,EAAE;QAC7B,IAAI,CAACJ,cAAc,GAAGG,aAAa;MACvC;MACA,KAAK,CAACE,SAAS,CAAC,IAAI,CAACL,cAAc,CAAC;IACxC;IACAM,OAAOA,CAACC,IAAI,EAAE;MACV,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC;IAC7B;IACAC,QAAQA,CAACF,IAAI,EAAE;MACX,OAAOA,IAAI,CAACE,QAAQ,CAAC,CAAC;IAC1B;IACAC,OAAOA,CAACH,IAAI,EAAE;MACV,OAAOA,IAAI,CAACG,OAAO,CAAC,CAAC;IACzB;IACAC,YAAYA,CAACJ,IAAI,EAAE;MACf,OAAOA,IAAI,CAACK,MAAM,CAAC,CAAC;IACxB;IACAC,aAAaA,CAACC,KAAK,EAAE;MACjB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,EAAE;QAAEC,KAAK,EAAEL,KAAK;QAAEM,QAAQ,EAAE;MAAM,CAAC,CAAC;MACnF,OAAO5B,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACwB,OAAO,CAACN,GAAG,EAAE,IAAIO,IAAI,CAAC,IAAI,EAAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE;IACA0B,YAAYA,CAAA,EAAG;MACX,MAAMR,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,EAAE;QAAEM,GAAG,EAAE,SAAS;QAAEJ,QAAQ,EAAE;MAAM,CAAC,CAAC;MACrF,OAAO5B,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACwB,OAAO,CAACN,GAAG,EAAE,IAAIO,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEzB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtE;IACA4B,iBAAiBA,CAACX,KAAK,EAAE;MACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,EAAE;QAAEQ,OAAO,EAAEZ,KAAK;QAAEM,QAAQ,EAAE;MAAM,CAAC,CAAC;MACrF,OAAO5B,KAAK,CAAC,CAAC,EAAEK,CAAC,IAAI,IAAI,CAACwB,OAAO,CAACN,GAAG,EAAE,IAAIO,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEzB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrE;IACA8B,WAAWA,CAACpB,IAAI,EAAE;MACd,MAAMQ,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,EAAE;QAAEU,IAAI,EAAE,SAAS;QAAER,QAAQ,EAAE;MAAM,CAAC,CAAC;MACtF,OAAO,IAAI,CAACC,OAAO,CAACN,GAAG,EAAER,IAAI,CAAC;IAClC;IACAsB,iBAAiBA,CAAA,EAAG;MAChB;MACA;MACA,IAAI,OAAOb,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACc,MAAM,EAAE;QAC5C,MAAMZ,MAAM,GAAG,IAAIF,IAAI,CAACc,MAAM,CAAC,IAAI,CAACZ,MAAM,CAAC;QAC3C;QACA;QACA,MAAMa,QAAQ,GAAG,CAACb,MAAM,CAACc,WAAW,GAAG,CAAC,IAAId,MAAM,CAACe,QAAQ,GAAGF,QAAQ,IAAI,CAAC;QAC3E;QACA;QACA,OAAOA,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGA,QAAQ;MACxC;MACA;MACA,OAAO,CAAC;IACZ;IACAG,iBAAiBA,CAAC3B,IAAI,EAAE;MACpB,OAAO,IAAI,CAACG,OAAO,CAAC,IAAI,CAACyB,uBAAuB,CAAC,IAAI,CAAC7B,OAAO,CAACC,IAAI,CAAC,EAAE,IAAI,CAACE,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACrG;IACA6B,KAAKA,CAAC7B,IAAI,EAAE;MACR,OAAO,IAAIe,IAAI,CAACf,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC;IACnC;IACAC,UAAUA,CAACV,IAAI,EAAET,KAAK,EAAEZ,IAAI,EAAE;MAC1B,IAAI,OAAOgC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA;QACA,IAAIpB,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;UACzB,MAAMqB,KAAK,CAAC,wBAAwBrB,KAAK,4CAA4C,CAAC;QAC1F;QACA,IAAIZ,IAAI,GAAG,CAAC,EAAE;UACV,MAAMiC,KAAK,CAAC,iBAAiBjC,IAAI,mCAAmC,CAAC;QACzE;MACJ;MACA,IAAIkC,MAAM,GAAG,IAAI,CAACN,uBAAuB,CAACP,IAAI,EAAET,KAAK,EAAEZ,IAAI,CAAC;MAC5D;MACA,IAAIkC,MAAM,CAAChC,QAAQ,CAAC,CAAC,IAAIU,KAAK,KAAK,OAAOoB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/E,MAAMC,KAAK,CAAC,iBAAiBjC,IAAI,2BAA2BY,KAAK,IAAI,CAAC;MAC1E;MACA,OAAOsB,MAAM;IACjB;IACAC,KAAKA,CAAA,EAAG;MACJ,OAAO,IAAIpB,IAAI,CAAC,CAAC;IACrB;IACAqB,KAAKA,CAACC,KAAK,EAAEC,WAAW,EAAE;MACtB;MACA;MACA,IAAI,OAAOD,KAAK,IAAI,QAAQ,EAAE;QAC1B,OAAO,IAAItB,IAAI,CAACsB,KAAK,CAAC;MAC1B;MACA,OAAOA,KAAK,GAAG,IAAItB,IAAI,CAACA,IAAI,CAACqB,KAAK,CAACC,KAAK,CAAC,CAAC,GAAG,IAAI;IACrD;IACAE,MAAMA,CAACvC,IAAI,EAAEwC,aAAa,EAAE;MACxB,IAAI,CAAC,IAAI,CAACC,OAAO,CAACzC,IAAI,CAAC,EAAE;QACrB,MAAMiC,KAAK,CAAC,gDAAgD,CAAC;MACjE;MACA,MAAMzB,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,EAAE;QAAE,GAAG6B,aAAa;QAAE3B,QAAQ,EAAE;MAAM,CAAC,CAAC;MACvF,OAAO,IAAI,CAACC,OAAO,CAACN,GAAG,EAAER,IAAI,CAAC;IAClC;IACA0C,gBAAgBA,CAAC1C,IAAI,EAAE2C,KAAK,EAAE;MAC1B,OAAO,IAAI,CAACC,iBAAiB,CAAC5C,IAAI,EAAE2C,KAAK,GAAG,EAAE,CAAC;IACnD;IACAC,iBAAiBA,CAAC5C,IAAI,EAAE6C,MAAM,EAAE;MAC5B,IAAIC,OAAO,GAAG,IAAI,CAAClB,uBAAuB,CAAC,IAAI,CAAC7B,OAAO,CAACC,IAAI,CAAC,EAAE,IAAI,CAACE,QAAQ,CAACF,IAAI,CAAC,GAAG6C,MAAM,EAAE,IAAI,CAAC1C,OAAO,CAACH,IAAI,CAAC,CAAC;MAChH;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACE,QAAQ,CAAC4C,OAAO,CAAC,IAAI,CAAE,CAAC,IAAI,CAAC5C,QAAQ,CAACF,IAAI,CAAC,GAAG6C,MAAM,IAAI,EAAE,GAAI,EAAE,IAAI,EAAE,EAAE;QAC7EC,OAAO,GAAG,IAAI,CAAClB,uBAAuB,CAAC,IAAI,CAAC7B,OAAO,CAAC+C,OAAO,CAAC,EAAE,IAAI,CAAC5C,QAAQ,CAAC4C,OAAO,CAAC,EAAE,CAAC,CAAC;MAC5F;MACA,OAAOA,OAAO;IAClB;IACAC,eAAeA,CAAC/C,IAAI,EAAEgD,IAAI,EAAE;MACxB,OAAO,IAAI,CAACpB,uBAAuB,CAAC,IAAI,CAAC7B,OAAO,CAACC,IAAI,CAAC,EAAE,IAAI,CAACE,QAAQ,CAACF,IAAI,CAAC,EAAE,IAAI,CAACG,OAAO,CAACH,IAAI,CAAC,GAAGgD,IAAI,CAAC;IAC3G;IACAC,SAASA,CAACjD,IAAI,EAAE;MACZ,OAAO,CACHA,IAAI,CAACkD,cAAc,CAAC,CAAC,EACrB,IAAI,CAACC,OAAO,CAACnD,IAAI,CAACoD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EACpC,IAAI,CAACD,OAAO,CAACnD,IAAI,CAACqD,UAAU,CAAC,CAAC,CAAC,CAClC,CAACC,IAAI,CAAC,GAAG,CAAC;IACf;IACA;AACJ;AACA;AACA;AACA;IACIC,WAAWA,CAAClB,KAAK,EAAE;MACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAACA,KAAK,EAAE;UACR,OAAO,IAAI;QACf;QACA;QACA;QACA,IAAItD,cAAc,CAACyE,IAAI,CAACnB,KAAK,CAAC,EAAE;UAC5B,IAAIrC,IAAI,GAAG,IAAIe,IAAI,CAACsB,KAAK,CAAC;UAC1B,IAAI,IAAI,CAACI,OAAO,CAACzC,IAAI,CAAC,EAAE;YACpB,OAAOA,IAAI;UACf;QACJ;MACJ;MACA,OAAO,KAAK,CAACuD,WAAW,CAAClB,KAAK,CAAC;IACnC;IACAoB,cAAcA,CAACC,GAAG,EAAE;MAChB,OAAOA,GAAG,YAAY3C,IAAI;IAC9B;IACA0B,OAAOA,CAACzC,IAAI,EAAE;MACV,OAAO,CAAC2D,KAAK,CAAC3D,IAAI,CAAC8B,OAAO,CAAC,CAAC,CAAC;IACjC;IACA8B,OAAOA,CAAA,EAAG;MACN,OAAO,IAAI7C,IAAI,CAAC8C,GAAG,CAAC;IACxB;IACAC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACrC,IAAI,OAAOlC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACmC,OAAO,CAACH,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;UACxB,MAAM/B,KAAK,CAAC,kBAAkB+B,KAAK,0CAA0C,CAAC;QAClF;QACA,IAAI,CAACG,OAAO,CAACF,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;UAC1B,MAAMhC,KAAK,CAAC,oBAAoBgC,OAAO,4CAA4C,CAAC;QACxF;QACA,IAAI,CAACE,OAAO,CAACD,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;UAC1B,MAAMjC,KAAK,CAAC,oBAAoBiC,OAAO,4CAA4C,CAAC;QACxF;MACJ;MACA,MAAMrC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkC,MAAM,CAAC;MAChClC,KAAK,CAACuC,QAAQ,CAACJ,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE,CAAC,CAAC;MAC1C,OAAOrC,KAAK;IAChB;IACAwC,QAAQA,CAACrE,IAAI,EAAE;MACX,OAAOA,IAAI,CAACqE,QAAQ,CAAC,CAAC;IAC1B;IACAC,UAAUA,CAACtE,IAAI,EAAE;MACb,OAAOA,IAAI,CAACsE,UAAU,CAAC,CAAC;IAC5B;IACAC,UAAUA,CAACvE,IAAI,EAAE;MACb,OAAOA,IAAI,CAACuE,UAAU,CAAC,CAAC;IAC5B;IACAC,SAASA,CAACC,SAAS,EAAEnC,WAAW,EAAE;MAC9B,IAAI,OAAOmC,SAAS,KAAK,QAAQ,EAAE;QAC/B,OAAOA,SAAS,YAAY1D,IAAI,GAAG,IAAIA,IAAI,CAAC0D,SAAS,CAAC3C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;MAC3E;MACA,MAAMO,KAAK,GAAGoC,SAAS,CAACC,IAAI,CAAC,CAAC;MAC9B,IAAIrC,KAAK,CAACnD,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;MACA;MACA,IAAIgD,MAAM,GAAG,IAAI,CAACyC,gBAAgB,CAACtC,KAAK,CAAC;MACzC;MACA;MACA,IAAIH,MAAM,KAAK,IAAI,EAAE;QACjB,MAAM0C,aAAa,GAAGvC,KAAK,CAACwC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAACH,IAAI,CAAC,CAAC;QAClE,IAAIE,aAAa,CAAC1F,MAAM,GAAG,CAAC,EAAE;UAC1BgD,MAAM,GAAG,IAAI,CAACyC,gBAAgB,CAACC,aAAa,CAAC;QACjD;MACJ;MACA,OAAO1C,MAAM,IAAI,IAAI,CAAC0B,OAAO,CAAC,CAAC;IACnC;IACAkB,UAAUA,CAAC9E,IAAI,EAAE+E,MAAM,EAAE;MACrB,OAAO,IAAIhE,IAAI,CAACf,IAAI,CAAC8B,OAAO,CAAC,CAAC,GAAGiD,MAAM,GAAG,IAAI,CAAC;IACnD;IACA;IACAnD,uBAAuBA,CAACP,IAAI,EAAET,KAAK,EAAEZ,IAAI,EAAE;MACvC;MACA;MACA,MAAMxC,CAAC,GAAG,IAAIuD,IAAI,CAAC,CAAC;MACpBvD,CAAC,CAACwH,WAAW,CAAC3D,IAAI,EAAET,KAAK,EAAEZ,IAAI,CAAC;MAChCxC,CAAC,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB,OAAO5G,CAAC;IACZ;IACA;AACJ;AACA;AACA;AACA;IACI2F,OAAOA,CAAC8B,CAAC,EAAE;MACP,OAAO,CAAC,IAAI,GAAGA,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIpE,OAAOA,CAACN,GAAG,EAAER,IAAI,EAAE;MACf;MACA;MACA,MAAMxC,CAAC,GAAG,IAAIuD,IAAI,CAAC,CAAC;MACpBvD,CAAC,CAAC2H,cAAc,CAACnF,IAAI,CAACC,WAAW,CAAC,CAAC,EAAED,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAEF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC;MACrE3C,CAAC,CAAC4H,WAAW,CAACpF,IAAI,CAACqE,QAAQ,CAAC,CAAC,EAAErE,IAAI,CAACsE,UAAU,CAAC,CAAC,EAAEtE,IAAI,CAACuE,UAAU,CAAC,CAAC,EAAEvE,IAAI,CAACqF,eAAe,CAAC,CAAC,CAAC;MAC5F,OAAO7E,GAAG,CAAC+B,MAAM,CAAC/E,CAAC,CAAC;IACxB;IACA;AACJ;AACA;AACA;IACImH,gBAAgBA,CAACtC,KAAK,EAAE;MACpB;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMiD,MAAM,GAAGjD,KAAK,CAACkD,WAAW,CAAC,CAAC,CAACC,KAAK,CAACxG,UAAU,CAAC;MACpD,IAAIsG,MAAM,EAAE;QACR,IAAItB,KAAK,GAAGyB,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAMrB,OAAO,GAAGwB,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC;QACnC,IAAIpB,OAAO,GAAGoB,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGzF,SAAS,GAAG4F,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC;QACjE,MAAMI,IAAI,GAAGJ,MAAM,CAAC,CAAC,CAAC;QACtB,IAAItB,KAAK,KAAK,EAAE,EAAE;UACdA,KAAK,GAAG0B,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG1B,KAAK;QACrC,CAAC,MACI,IAAI0B,IAAI,KAAK,IAAI,EAAE;UACpB1B,KAAK,IAAI,EAAE;QACf;QACA,IAAIG,OAAO,CAACH,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,IACrBG,OAAO,CAACF,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,KACtBC,OAAO,IAAI,IAAI,IAAIC,OAAO,CAACD,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;UAC9C,OAAO,IAAI,CAACJ,OAAO,CAAC,IAAI,CAAC3B,KAAK,CAAC,CAAC,EAAE6B,KAAK,EAAEC,OAAO,EAAEC,OAAO,IAAI,CAAC,CAAC;QACnE;MACJ;MACA,OAAO,IAAI;IACf;IACA,OAAOyB,IAAI,YAAAC,0BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFtG,iBAAiB;IAAA;IACpH,OAAOuG,KAAK,kBAD6EpK,EAAE,CAAAqK,kBAAA;MAAAC,KAAA,EACYzG,iBAAiB;MAAA0G,OAAA,EAAjB1G,iBAAiB,CAAAoG;IAAA;EAC5H;EAAC,OAjRKpG,iBAAiB;AAAA;AAkRvB;EAAA,QAAAyC,SAAA,oBAAAA,SAAA;AAAA;AAGA;AACA,SAASmC,OAAOA,CAAC9B,KAAK,EAAE6D,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAO,CAACxC,KAAK,CAACtB,KAAK,CAAC,IAAIA,KAAK,IAAI6D,GAAG,IAAI7D,KAAK,IAAI8D,GAAG;AACxD;AAEA,MAAMC,uBAAuB,GAAG;EAC5BhE,KAAK,EAAE;IACHiE,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACLF,SAAS,EAAE;MAAEhF,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,SAAS;MAAEK,GAAG,EAAE;IAAU,CAAC;IAChEqF,SAAS,EAAE;MAAEE,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC;IACjDC,cAAc,EAAE;MAAErF,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAQ,CAAC;IACnD+F,aAAa,EAAE;MAAEtF,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,MAAM;MAAEK,GAAG,EAAE;IAAU,CAAC;IACjE2F,kBAAkB,EAAE;MAAEvF,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAO,CAAC;IACtDiG,eAAe,EAAE;MAAEL,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU;EAC1D;AACJ,CAAC;AAAC,IAEIK,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB,OAAOnB,IAAI,YAAAoB,yBAAAlB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiB,gBAAgB;IAAA;IACnH,OAAOE,IAAI,kBA5B8EtL,EAAE,CAAAuL,gBAAA;MAAAC,IAAA,EA4BSJ;IAAgB;IACpH,OAAOK,IAAI,kBA7B8EzL,EAAE,CAAA0L,gBAAA;MAAAC,SAAA,EA6BsC,CAAC;QAAEC,OAAO,EAAE5K,WAAW;QAAE6K,QAAQ,EAAEhI;MAAkB,CAAC;IAAC;EAC5L;EAAC,OAJKuH,gBAAgB;AAAA;AAKtB;EAAA,QAAA9E,SAAA,oBAAAA,SAAA;AAAA;AAKc,IACRwF,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtB,OAAO7B,IAAI,YAAA8B,4BAAA5B,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2B,mBAAmB;IAAA;IACtH,OAAOR,IAAI,kBAvC8EtL,EAAE,CAAAuL,gBAAA;MAAAC,IAAA,EAuCSM;IAAmB;IACvH,OAAOL,IAAI,kBAxC8EzL,EAAE,CAAA0L,gBAAA;MAAAC,SAAA,EAwCyC,CAACK,wBAAwB,CAAC,CAAC;IAAC;EACpK;EAAC,OAJKF,mBAAmB;AAAA;AAKzB;EAAA,QAAAxF,SAAA,oBAAAA,SAAA;AAAA;AAMA,SAAS0F,wBAAwBA,CAACC,OAAO,GAAGvB,uBAAuB,EAAE;EACjE,OAAO,CACH;IAAEkB,OAAO,EAAE5K,WAAW;IAAE6K,QAAQ,EAAEhI;EAAkB,CAAC,EACrD;IAAE+H,OAAO,EAAE1K,gBAAgB;IAAEgL,QAAQ,EAAED;EAAQ,CAAC,CACnD;AACL;AAEA,SAASjL,WAAW,EAAEE,gBAAgB,EAAED,eAAe,EAAEyJ,uBAAuB,EAAEoB,mBAAmB,EAAEjI,iBAAiB,EAAEuH,gBAAgB,EAAEhI,OAAO,EAAE4I,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}