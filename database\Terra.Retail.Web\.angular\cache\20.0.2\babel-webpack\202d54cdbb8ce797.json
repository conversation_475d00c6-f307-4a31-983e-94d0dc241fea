{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/table\";\nimport * as i13 from \"@angular/material/chips\";\nimport * as i14 from \"@angular/material/badge\";\nfunction Products_mat_option_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r1.nameAr, \" \");\n  }\n}\nfunction Products_mat_option_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const unit_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", unit_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", unit_r2.nameAr, \" \");\n  }\n}\nfunction Products_div_99_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"span\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r3.productCode);\n  }\n}\nfunction Products_div_99_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"div\", 51)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.nameEn);\n  }\n}\nfunction Products_div_99_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0641\\u0626\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"mat-chip-set\")(2, \"mat-chip\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r5.categoryName);\n  }\n}\nfunction Products_div_99_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r6.unitName);\n  }\n}\nfunction Products_div_99_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0633\\u0639\\u0631\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"span\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(3, 1, product_r7.price, \"EGP\", \"symbol\", \"1.2-2\"));\n  }\n}\nfunction Products_div_99_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"small\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getStockClass(product_r8.stock));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", product_r8.stock, \" \", product_r8.unitName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.getStockStatus(product_r8.stock));\n  }\n}\nfunction Products_div_99_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"mat-chip-set\")(2, \"mat-chip\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", product_r10.isActive ? \"primary\" : \"warn\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r10.isActive ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n  }\n}\nfunction Products_div_99_th_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 48);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Products_div_99_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 49)(1, \"div\", 56)(2, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function Products_div_99_td_25_Template_button_click_2_listener() {\n      const product_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.viewProduct(product_r12));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function Products_div_99_td_25_Template_button_click_5_listener() {\n      const product_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.editProduct(product_r12));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function Products_div_99_td_25_Template_button_click_8_listener() {\n      const product_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.deleteProduct(product_r12));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction Products_div_99_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction Products_div_99_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 61);\n  }\n}\nfunction Products_div_99_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"inventory_2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u062A\\u0637\\u0627\\u0628\\u0642 \\u0645\\u0639\\u0627\\u064A\\u064A\\u0631 \\u0627\\u0644\\u0628\\u062D\\u062B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function Products_div_99_div_28_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.clearFilters());\n    });\n    i0.ɵɵtext(8, \" \\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0644\\u0627\\u062A\\u0631 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Products_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"table\", 34);\n    i0.ɵɵelementContainerStart(2, 35);\n    i0.ɵɵtemplate(3, Products_div_99_th_3_Template, 2, 0, \"th\", 36)(4, Products_div_99_td_4_Template, 3, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 38);\n    i0.ɵɵtemplate(6, Products_div_99_th_6_Template, 2, 0, \"th\", 36)(7, Products_div_99_td_7_Template, 6, 2, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 39);\n    i0.ɵɵtemplate(9, Products_div_99_th_9_Template, 2, 0, \"th\", 36)(10, Products_div_99_td_10_Template, 4, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 40);\n    i0.ɵɵtemplate(12, Products_div_99_th_12_Template, 2, 0, \"th\", 36)(13, Products_div_99_td_13_Template, 2, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 41);\n    i0.ɵɵtemplate(15, Products_div_99_th_15_Template, 2, 0, \"th\", 36)(16, Products_div_99_td_16_Template, 4, 6, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 42);\n    i0.ɵɵtemplate(18, Products_div_99_th_18_Template, 2, 0, \"th\", 36)(19, Products_div_99_td_19_Template, 5, 4, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 43);\n    i0.ɵɵtemplate(21, Products_div_99_th_21_Template, 2, 0, \"th\", 36)(22, Products_div_99_td_22_Template, 4, 2, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(23, 44);\n    i0.ɵɵtemplate(24, Products_div_99_th_24_Template, 2, 0, \"th\", 36)(25, Products_div_99_td_25_Template, 11, 0, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(26, Products_div_99_tr_26_Template, 1, 0, \"tr\", 45)(27, Products_div_99_tr_27_Template, 1, 0, \"tr\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, Products_div_99_div_28_Template, 9, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r8.filteredProducts);\n    i0.ɵɵadvance(25);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r8.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r8.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.filteredProducts.length === 0);\n  }\n}\nfunction Products_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"mat-icon\", 65);\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let Products = /*#__PURE__*/(() => {\n  class Products {\n    http;\n    snackBar;\n    products = [];\n    filteredProducts = [];\n    categories = [];\n    units = [];\n    searchTerm = '';\n    selectedCategory = '';\n    selectedUnit = '';\n    isLoading = false;\n    totalProducts = 0;\n    activeProducts = 0;\n    totalValue = 0;\n    lowStockProducts = 0;\n    displayedColumns = ['productCode', 'nameAr', 'category', 'unit', 'price', 'stock', 'status', 'actions'];\n    constructor(http, snackBar) {\n      this.http = http;\n      this.snackBar = snackBar;\n    }\n    ngOnInit() {\n      this.loadProducts();\n      this.loadCategories();\n      this.loadUnits();\n    }\n    loadProducts() {\n      this.isLoading = true;\n      this.http.get('http://localhost:5127/api/simple/products').subscribe({\n        next: response => {\n          this.products = response.products || [];\n          this.filteredProducts = [...this.products];\n          this.calculateStatistics();\n          this.isLoading = false;\n          if (this.products.length === 0) {\n            this.showMessage('لا توجد منتجات في قاعدة البيانات');\n          }\n        },\n        error: error => {\n          console.error('Error loading products:', error);\n          this.showMessage('خطأ في تحميل بيانات المنتجات من قاعدة البيانات');\n          this.products = [];\n          this.filteredProducts = [];\n          this.isLoading = false;\n        }\n      });\n    }\n    loadCategories() {\n      this.http.get('http://localhost:5127/api/simple/categories').subscribe({\n        next: response => {\n          this.categories = response.categories || [];\n        },\n        error: error => {\n          console.error('Error loading categories:', error);\n          this.categories = [{\n            id: 1,\n            nameAr: 'عام',\n            nameEn: 'General',\n            code: 'GEN'\n          }, {\n            id: 2,\n            nameAr: 'مواد غذائية ومشروبات',\n            nameEn: 'Food & Beverages',\n            code: 'FOOD'\n          }];\n        }\n      });\n    }\n    loadUnits() {\n      this.http.get('http://localhost:5127/api/simple/units').subscribe({\n        next: response => {\n          this.units = response.units || [];\n        },\n        error: error => {\n          console.error('Error loading units:', error);\n          this.units = [{\n            id: 13,\n            nameAr: 'حبة',\n            nameEn: 'Piece',\n            symbol: 'حبة'\n          }, {\n            id: 14,\n            nameAr: 'كيلو',\n            nameEn: 'Kilogram',\n            symbol: 'كجم'\n          }];\n        }\n      });\n    }\n    calculateStatistics() {\n      this.totalProducts = this.products.length;\n      this.activeProducts = this.products.filter(p => p.isActive).length;\n      this.totalValue = this.products.reduce((sum, p) => sum + p.price * p.stock, 0);\n      this.lowStockProducts = this.products.filter(p => p.stock < 100).length;\n    }\n    onSearch() {\n      this.applyFilters();\n    }\n    onFilterChange() {\n      this.applyFilters();\n    }\n    applyFilters() {\n      this.filteredProducts = this.products.filter(product => {\n        const matchesSearch = !this.searchTerm || product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) || product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase()) || product.nameEn.toLowerCase().includes(this.searchTerm.toLowerCase());\n        const matchesCategory = !this.selectedCategory || product.categoryId.toString() === this.selectedCategory;\n        const matchesUnit = !this.selectedUnit || product.unitId.toString() === this.selectedUnit;\n        return matchesSearch && matchesCategory && matchesUnit;\n      });\n    }\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedCategory = '';\n      this.selectedUnit = '';\n      this.filteredProducts = [...this.products];\n    }\n    getStockStatus(stock) {\n      if (stock === 0) return 'نفد المخزون';\n      if (stock < 50) return 'مخزون منخفض';\n      if (stock < 100) return 'مخزون متوسط';\n      return 'مخزون جيد';\n    }\n    getStockClass(stock) {\n      if (stock === 0) return 'out-of-stock';\n      if (stock < 50) return 'low-stock';\n      if (stock < 100) return 'medium-stock';\n      return 'good-stock';\n    }\n    addProduct() {\n      this.showMessage('إضافة منتج جديد - قيد التطوير');\n    }\n    viewProduct(product) {\n      this.showMessage(`عرض تفاصيل المنتج: ${product.nameAr}`);\n    }\n    editProduct(product) {\n      this.showMessage(`تعديل المنتج: ${product.nameAr} - قيد التطوير`);\n    }\n    deleteProduct(product) {\n      this.showMessage(`حذف المنتج: ${product.nameAr} - قيد التطوير`);\n    }\n    exportProducts() {\n      this.showMessage('تصدير بيانات المنتجات - قيد التطوير');\n    }\n    showMessage(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    static ɵfac = function Products_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Products)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Products,\n      selectors: [[\"app-products\"]],\n      decls: 101,\n      vars: 18,\n      consts: [[1, \"products-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"title-section\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/add-product\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-details\"], [1, \"stat-card\", \"active\"], [1, \"stat-card\", \"value\"], [1, \"stat-card\", \"warning\"], [\"matBadgeColor\", \"warn\", 3, \"matBadge\"], [1, \"filters-card\"], [1, \"filters-container\"], [1, \"search-section\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u060C \\u0627\\u0644\\u0643\\u0648\\u062F\\u060C \\u0623\\u0648 \\u0627\\u0644\\u0648\\u0635\\u0641\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [1, \"filter-section\"], [\"appearance\", \"outline\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"table-card\"], [1, \"table-header\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"value\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"products-table\", 3, \"dataSource\"], [\"matColumnDef\", \"productCode\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"nameAr\"], [\"matColumnDef\", \"category\"], [\"matColumnDef\", \"unit\"], [\"matColumnDef\", \"price\"], [\"matColumnDef\", \"stock\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"product-code\"], [1, \"product-name\"], [1, \"price\"], [1, \"stock-badge\", 3, \"ngClass\"], [1, \"stock-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-container\"], [1, \"loading-icon\"]],\n      template: function Products_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\")(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"inventory\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u062A\\u062A\\u0628\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0641\\u064A \\u0627\\u0644\\u0645\\u062A\\u062C\\u0631\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 4)(11, \"button\", 5)(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C \\u062C\\u062F\\u064A\\u062F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function Products_Template_button_click_15_listener() {\n            return ctx.exportProducts();\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" \\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 7)(20, \"mat-card\", 8)(21, \"mat-card-content\")(22, \"div\", 9)(23, \"div\", 10)(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"inventory_2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 11)(27, \"h3\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(31, \"mat-card\", 12)(32, \"mat-card-content\")(33, \"div\", 9)(34, \"div\", 10)(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"check_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 11)(38, \"h3\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\");\n          i0.ɵɵtext(41, \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"mat-card\", 13)(43, \"mat-card-content\")(44, \"div\", 9)(45, \"div\", 10)(46, \"mat-icon\");\n          i0.ɵɵtext(47, \"attach_money\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 11)(49, \"h3\");\n          i0.ɵɵtext(50);\n          i0.ɵɵpipe(51, \"currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(54, \"mat-card\", 14)(55, \"mat-card-content\")(56, \"div\", 9)(57, \"div\", 10)(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"warning\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 11)(61, \"h3\", 15);\n          i0.ɵɵtext(62);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\");\n          i0.ɵɵtext(64, \"\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0645\\u0646\\u062E\\u0641\\u0636\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(65, \"mat-card\", 16)(66, \"mat-card-content\")(67, \"div\", 17)(68, \"div\", 18)(69, \"mat-form-field\", 19)(70, \"mat-label\");\n          i0.ɵɵtext(71, \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"input\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function Products_Template_input_ngModelChange_72_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function Products_Template_input_input_72_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-icon\", 21);\n          i0.ɵɵtext(74, \"search\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 22)(76, \"mat-form-field\", 23)(77, \"mat-label\");\n          i0.ɵɵtext(78, \"\\u0627\\u0644\\u0641\\u0626\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"mat-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function Products_Template_mat_select_ngModelChange_79_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function Products_Template_mat_select_selectionChange_79_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(80, \"mat-option\", 25);\n          i0.ɵɵtext(81, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0641\\u0626\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(82, Products_mat_option_82_Template, 2, 2, \"mat-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"mat-form-field\", 23)(84, \"mat-label\");\n          i0.ɵɵtext(85, \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"mat-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function Products_Template_mat_select_ngModelChange_86_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedUnit, $event) || (ctx.selectedUnit = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function Products_Template_mat_select_selectionChange_86_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(87, \"mat-option\", 25);\n          i0.ɵɵtext(88, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(89, Products_mat_option_89_Template, 2, 2, \"mat-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function Products_Template_button_click_90_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(91, \"mat-icon\");\n          i0.ɵɵtext(92, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(93, \" \\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0644\\u0627\\u062A\\u0631 \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(94, \"mat-card\", 28)(95, \"mat-card-content\")(96, \"div\", 29)(97, \"h2\");\n          i0.ɵɵtext(98);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(99, Products_div_99_Template, 29, 4, \"div\", 30)(100, Products_div_100_Template, 5, 0, \"div\", 31);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(28);\n          i0.ɵɵtextInterpolate(ctx.totalProducts);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.activeProducts);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(51, 13, ctx.totalValue, \"EGP\", \"symbol\", \"1.2-2\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matBadge\", ctx.lowStockProducts);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.lowStockProducts);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedUnit);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.units);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A (\", ctx.filteredProducts.length, \")\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.CurrencyPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, HttpClientModule, RouterModule, i5.RouterLink, MatCardModule, i6.MatCard, i6.MatCardContent, MatButtonModule, i7.MatButton, i7.MatIconButton, MatIconModule, i8.MatIcon, MatInputModule, i9.MatInput, i10.MatFormField, i10.MatLabel, i10.MatSuffix, MatSelectModule, i11.MatSelect, i11.MatOption, MatTableModule, i12.MatTable, i12.MatHeaderCellDef, i12.MatHeaderRowDef, i12.MatColumnDef, i12.MatCellDef, i12.MatRowDef, i12.MatHeaderCell, i12.MatCell, i12.MatHeaderRow, i12.MatRow, MatPaginatorModule, MatSnackBarModule, MatChipsModule, i13.MatChip, i13.MatChipSet, MatBadgeModule, i14.MatBadge, MatFormFieldModule],\n      styles: [\".products-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background-color: #f5f5f5;\\n  min-height: 100vh;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  flex-wrap: wrap;\\n}\\n.products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 24px;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card.total[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card.active[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card.value[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);\\n}\\n.products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card.warning[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  flex-wrap: wrap;\\n  align-items: center;\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n.products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: white;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  font-weight: 600;\\n  color: #333;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .product-code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background-color: #e3f2fd;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  color: #1976d2;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4CAF50;\\n  font-size: 1.1rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-badge.good-stock[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-badge.medium-stock[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #f57c00;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-badge.low-stock[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #d32f2f;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-badge.out-of-stock[_ngcontent-%COMP%] {\\n  background-color: #fce4ec;\\n  color: #c2185b;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-top: 4px;\\n  font-size: 0.75rem;\\n  color: #666;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #666;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  color: #333;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 1rem;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #666;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #1976d2;\\n  margin-bottom: 16px;\\n  animation: _ngcontent-%COMP%_spin 2s linear infinite;\\n}\\n.products-container[_ngcontent-%COMP%]   .table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .products-container[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .products-container[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .products-container[_ngcontent-%COMP%]   .filters-card[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return Products;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "HttpClientModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatSelectModule", "MatTableModule", "MatPaginatorModule", "MatSnackBarModule", "MatFormFieldModule", "MatChipsModule", "MatBadgeModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "nameAr", "unit_r2", "ɵɵtextInterpolate", "product_r3", "productCode", "product_r4", "nameEn", "product_r5", "categoryName", "product_r6", "unitName", "ɵɵpipeBind4", "product_r7", "price", "ctx_r8", "getStockClass", "product_r8", "stock", "ɵɵtextInterpolate2", "getStockStatus", "product_r10", "isActive", "ɵɵlistener", "Products_div_99_td_25_Template_button_click_2_listener", "product_r12", "ɵɵrestoreView", "_r11", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewProduct", "Products_div_99_td_25_Template_button_click_5_listener", "editProduct", "Products_div_99_td_25_Template_button_click_8_listener", "deleteProduct", "ɵɵelement", "Products_div_99_div_28_Template_button_click_7_listener", "_r13", "clearFilters", "ɵɵelementContainerStart", "ɵɵtemplate", "Products_div_99_th_3_Template", "Products_div_99_td_4_Template", "Products_div_99_th_6_Template", "Products_div_99_td_7_Template", "Products_div_99_th_9_Template", "Products_div_99_td_10_Template", "Products_div_99_th_12_Template", "Products_div_99_td_13_Template", "Products_div_99_th_15_Template", "Products_div_99_td_16_Template", "Products_div_99_th_18_Template", "Products_div_99_td_19_Template", "Products_div_99_th_21_Template", "Products_div_99_td_22_Template", "Products_div_99_th_24_Template", "Products_div_99_td_25_Template", "Products_div_99_tr_26_Template", "Products_div_99_tr_27_Template", "Products_div_99_div_28_Template", "filteredProducts", "displayedColumns", "length", "Products", "http", "snackBar", "products", "categories", "units", "searchTerm", "selectedCate<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "totalProducts", "activeProducts", "totalValue", "lowStockProducts", "constructor", "ngOnInit", "loadProducts", "loadCategories", "loadUnits", "get", "subscribe", "next", "response", "calculateStatistics", "showMessage", "error", "console", "code", "symbol", "filter", "p", "reduce", "sum", "onSearch", "applyFilters", "onFilterChange", "product", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "categoryId", "toString", "matchesUnit", "unitId", "addProduct", "exportProducts", "message", "open", "duration", "horizontalPosition", "verticalPosition", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "Products_Template", "rf", "ctx", "Products_Template_button_click_15_listener", "ɵɵtwoWayListener", "Products_Template_input_ngModelChange_72_listener", "$event", "ɵɵtwoWayBindingSet", "Products_Template_input_input_72_listener", "Products_Template_mat_select_ngModelChange_79_listener", "Products_Template_mat_select_selectionC<PERSON>e_79_listener", "Products_mat_option_82_Template", "Products_Template_mat_select_ngModelChange_86_listener", "Products_Template_mat_select_selectionChange_86_listener", "Products_mat_option_89_Template", "Products_Template_button_click_90_listener", "Products_div_99_Template", "Products_div_100_Template", "ɵɵtwoWayProperty", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "RouterLink", "i6", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "i7", "MatButton", "MatIconButton", "i8", "MatIcon", "i9", "MatInput", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatSelect", "MatOption", "i12", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i13", "MatChip", "MatChipSet", "i14", "MatBadge", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\products\\products.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\products\\products.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HttpClient, HttpClientModule } from '@angular/common/http';\r\nimport { RouterModule } from '@angular/router';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatBadgeModule } from '@angular/material/badge';\r\n\r\ninterface Product {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n  productCode: string;\r\n  categoryId: number;\r\n  categoryName: string;\r\n  unitId: number;\r\n  unitName: string;\r\n  price: number;\r\n  stock: number;\r\n  isActive: boolean;\r\n}\r\n\r\ninterface Category {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n  code: string;\r\n}\r\n\r\ninterface Unit {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n  symbol: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-products',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    HttpClientModule,\r\n    RouterModule,\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSnackBarModule,\r\n    MatChipsModule,\r\n    MatBadgeModule,\r\n    MatFormFieldModule\r\n  ],\r\n  templateUrl: './products.html',\r\n  styleUrls: ['./products.scss']\r\n})\r\nexport class Products implements OnInit {\r\n  products: Product[] = [];\r\n  filteredProducts: Product[] = [];\r\n  categories: Category[] = [];\r\n  units: Unit[] = [];\r\n\r\n  searchTerm: string = '';\r\n  selectedCategory: string = '';\r\n  selectedUnit: string = '';\r\n  isLoading: boolean = false;\r\n\r\n  totalProducts: number = 0;\r\n  activeProducts: number = 0;\r\n  totalValue: number = 0;\r\n  lowStockProducts: number = 0;\r\n\r\n  displayedColumns: string[] = ['productCode', 'nameAr', 'category', 'unit', 'price', 'stock', 'status', 'actions'];\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadProducts();\r\n    this.loadCategories();\r\n    this.loadUnits();\r\n  }\r\n\r\n  loadProducts() {\r\n    this.isLoading = true;\r\n    this.http.get<any>('http://localhost:5127/api/simple/products').subscribe({\r\n      next: (response) => {\r\n        this.products = response.products || [];\r\n        this.filteredProducts = [...this.products];\r\n        this.calculateStatistics();\r\n        this.isLoading = false;\r\n\r\n        if (this.products.length === 0) {\r\n          this.showMessage('لا توجد منتجات في قاعدة البيانات');\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading products:', error);\r\n        this.showMessage('خطأ في تحميل بيانات المنتجات من قاعدة البيانات');\r\n        this.products = [];\r\n        this.filteredProducts = [];\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadCategories() {\r\n    this.http.get<any>('http://localhost:5127/api/simple/categories').subscribe({\r\n      next: (response) => {\r\n        this.categories = response.categories || [];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading categories:', error);\r\n        this.categories = [\r\n          { id: 1, nameAr: 'عام', nameEn: 'General', code: 'GEN' },\r\n          { id: 2, nameAr: 'مواد غذائية ومشروبات', nameEn: 'Food & Beverages', code: 'FOOD' }\r\n        ];\r\n      }\r\n    });\r\n  }\r\n\r\n  loadUnits() {\r\n    this.http.get<any>('http://localhost:5127/api/simple/units').subscribe({\r\n      next: (response) => {\r\n        this.units = response.units || [];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading units:', error);\r\n        this.units = [\r\n          { id: 13, nameAr: 'حبة', nameEn: 'Piece', symbol: 'حبة' },\r\n          { id: 14, nameAr: 'كيلو', nameEn: 'Kilogram', symbol: 'كجم' }\r\n        ];\r\n      }\r\n    });\r\n  }\r\n\r\n  calculateStatistics() {\r\n    this.totalProducts = this.products.length;\r\n    this.activeProducts = this.products.filter(p => p.isActive).length;\r\n    this.totalValue = this.products.reduce((sum, p) => sum + (p.price * p.stock), 0);\r\n    this.lowStockProducts = this.products.filter(p => p.stock < 100).length;\r\n  }\r\n\r\n  onSearch() {\r\n    this.applyFilters();\r\n  }\r\n\r\n  onFilterChange() {\r\n    this.applyFilters();\r\n  }\r\n\r\n  applyFilters() {\r\n    this.filteredProducts = this.products.filter(product => {\r\n      const matchesSearch = !this.searchTerm ||\r\n        product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n        product.nameEn.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n\r\n      const matchesCategory = !this.selectedCategory ||\r\n        product.categoryId.toString() === this.selectedCategory;\r\n\r\n      const matchesUnit = !this.selectedUnit ||\r\n        product.unitId.toString() === this.selectedUnit;\r\n\r\n      return matchesSearch && matchesCategory && matchesUnit;\r\n    });\r\n  }\r\n\r\n  clearFilters() {\r\n    this.searchTerm = '';\r\n    this.selectedCategory = '';\r\n    this.selectedUnit = '';\r\n    this.filteredProducts = [...this.products];\r\n  }\r\n\r\n  getStockStatus(stock: number): string {\r\n    if (stock === 0) return 'نفد المخزون';\r\n    if (stock < 50) return 'مخزون منخفض';\r\n    if (stock < 100) return 'مخزون متوسط';\r\n    return 'مخزون جيد';\r\n  }\r\n\r\n  getStockClass(stock: number): string {\r\n    if (stock === 0) return 'out-of-stock';\r\n    if (stock < 50) return 'low-stock';\r\n    if (stock < 100) return 'medium-stock';\r\n    return 'good-stock';\r\n  }\r\n\r\n  addProduct() {\r\n    this.showMessage('إضافة منتج جديد - قيد التطوير');\r\n  }\r\n\r\n  viewProduct(product: Product) {\r\n    this.showMessage(`عرض تفاصيل المنتج: ${product.nameAr}`);\r\n  }\r\n\r\n  editProduct(product: Product) {\r\n    this.showMessage(`تعديل المنتج: ${product.nameAr} - قيد التطوير`);\r\n  }\r\n\r\n  deleteProduct(product: Product) {\r\n    this.showMessage(`حذف المنتج: ${product.nameAr} - قيد التطوير`);\r\n  }\r\n\r\n  exportProducts() {\r\n    this.showMessage('تصدير بيانات المنتجات - قيد التطوير');\r\n  }\r\n\r\n  private showMessage(message: string) {\r\n    this.snackBar.open(message, 'إغلاق', {\r\n      duration: 3000,\r\n      horizontalPosition: 'center',\r\n      verticalPosition: 'top'\r\n    });\r\n  }\r\n}\r\n", "<!-- صفحة إدارة المنتجات -->\r\n<div class=\"products-container\">\r\n  <!-- Header -->\r\n  <div class=\"page-header\">\r\n    <div class=\"header-content\">\r\n      <div class=\"title-section\">\r\n        <h1>\r\n          <mat-icon>inventory</mat-icon>\r\n          إدارة المنتجات\r\n        </h1>\r\n        <p>إدارة وتتبع جميع المنتجات في المتجر</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button mat-raised-button color=\"primary\" routerLink=\"/add-product\">\r\n          <mat-icon>add</mat-icon>\r\n          إضافة منتج جديد\r\n        </button>\r\n        <button mat-stroked-button (click)=\"exportProducts()\">\r\n          <mat-icon>download</mat-icon>\r\n          تصدير البيانات\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Statistics Cards -->\r\n  <div class=\"stats-grid\">\r\n    <mat-card class=\"stat-card total\">\r\n      <mat-card-content>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>inventory_2</mat-icon>\r\n          </div>\r\n          <div class=\"stat-details\">\r\n            <h3>{{ totalProducts }}</h3>\r\n            <p>إجمالي المنتجات</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <mat-card class=\"stat-card active\">\r\n      <mat-card-content>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>check_circle</mat-icon>\r\n          </div>\r\n          <div class=\"stat-details\">\r\n            <h3>{{ activeProducts }}</h3>\r\n            <p>المنتجات النشطة</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <mat-card class=\"stat-card value\">\r\n      <mat-card-content>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>attach_money</mat-icon>\r\n          </div>\r\n          <div class=\"stat-details\">\r\n            <h3>{{ totalValue | currency:'EGP':'symbol':'1.2-2' }}</h3>\r\n            <p>قيمة المخزون</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n\r\n    <mat-card class=\"stat-card warning\">\r\n      <mat-card-content>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-icon\">\r\n            <mat-icon>warning</mat-icon>\r\n          </div>\r\n          <div class=\"stat-details\">\r\n            <h3 [matBadge]=\"lowStockProducts\" matBadgeColor=\"warn\">{{ lowStockProducts }}</h3>\r\n            <p>مخزون منخفض</p>\r\n          </div>\r\n        </div>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  </div>\r\n\r\n  <!-- Filters Section -->\r\n  <mat-card class=\"filters-card\">\r\n    <mat-card-content>\r\n      <div class=\"filters-container\">\r\n        <div class=\"search-section\">\r\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n            <mat-label>البحث في المنتجات</mat-label>\r\n            <input matInput\r\n                   [(ngModel)]=\"searchTerm\"\r\n                   (input)=\"onSearch()\"\r\n                   placeholder=\"اسم المنتج، الكود، أو الوصف\">\r\n            <mat-icon matSuffix>search</mat-icon>\r\n          </mat-form-field>\r\n        </div>\r\n\r\n        <div class=\"filter-section\">\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>الفئة</mat-label>\r\n            <mat-select [(ngModel)]=\"selectedCategory\" (selectionChange)=\"onFilterChange()\">\r\n              <mat-option value=\"\">جميع الفئات</mat-option>\r\n              <mat-option *ngFor=\"let category of categories\" [value]=\"category.id\">\r\n                {{ category.nameAr }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>الوحدة</mat-label>\r\n            <mat-select [(ngModel)]=\"selectedUnit\" (selectionChange)=\"onFilterChange()\">\r\n              <mat-option value=\"\">جميع الوحدات</mat-option>\r\n              <mat-option *ngFor=\"let unit of units\" [value]=\"unit.id\">\r\n                {{ unit.nameAr }}\r\n              </mat-option>\r\n            </mat-select>\r\n          </mat-form-field>\r\n\r\n          <button mat-stroked-button (click)=\"clearFilters()\" class=\"clear-filters-btn\">\r\n            <mat-icon>clear</mat-icon>\r\n            مسح الفلاتر\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <!-- Products Table -->\r\n  <mat-card class=\"table-card\">\r\n    <mat-card-content>\r\n      <div class=\"table-header\">\r\n        <h2>قائمة المنتجات ({{ filteredProducts.length }})</h2>\r\n      </div>\r\n\r\n      <div class=\"table-container\" *ngIf=\"!isLoading\">\r\n        <table mat-table [dataSource]=\"filteredProducts\" class=\"products-table\">\r\n          <!-- Product Code Column -->\r\n          <ng-container matColumnDef=\"productCode\">\r\n            <th mat-header-cell *matHeaderCellDef>كود المنتج</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <span class=\"product-code\">{{ product.productCode }}</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Product Name Column -->\r\n          <ng-container matColumnDef=\"nameAr\">\r\n            <th mat-header-cell *matHeaderCellDef>اسم المنتج</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <div class=\"product-name\">\r\n                <strong>{{ product.nameAr }}</strong>\r\n                <small>{{ product.nameEn }}</small>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Category Column -->\r\n          <ng-container matColumnDef=\"category\">\r\n            <th mat-header-cell *matHeaderCellDef>الفئة</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <mat-chip-set>\r\n                <mat-chip>{{ product.categoryName }}</mat-chip>\r\n              </mat-chip-set>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Unit Column -->\r\n          <ng-container matColumnDef=\"unit\">\r\n            <th mat-header-cell *matHeaderCellDef>الوحدة</th>\r\n            <td mat-cell *matCellDef=\"let product\">{{ product.unitName }}</td>\r\n          </ng-container>\r\n\r\n          <!-- Price Column -->\r\n          <ng-container matColumnDef=\"price\">\r\n            <th mat-header-cell *matHeaderCellDef>السعر</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <span class=\"price\">{{ product.price | currency:'EGP':'symbol':'1.2-2' }}</span>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Stock Column -->\r\n          <ng-container matColumnDef=\"stock\">\r\n            <th mat-header-cell *matHeaderCellDef>المخزون</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <span class=\"stock-badge\" [ngClass]=\"getStockClass(product.stock)\">\r\n                {{ product.stock }} {{ product.unitName }}\r\n              </span>\r\n              <small class=\"stock-status\">{{ getStockStatus(product.stock) }}</small>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Status Column -->\r\n          <ng-container matColumnDef=\"status\">\r\n            <th mat-header-cell *matHeaderCellDef>الحالة</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <mat-chip-set>\r\n                <mat-chip [color]=\"product.isActive ? 'primary' : 'warn'\" selected>\r\n                  {{ product.isActive ? 'نشط' : 'غير نشط' }}\r\n                </mat-chip>\r\n              </mat-chip-set>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <!-- Actions Column -->\r\n          <ng-container matColumnDef=\"actions\">\r\n            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\r\n            <td mat-cell *matCellDef=\"let product\">\r\n              <div class=\"action-buttons\">\r\n                <button mat-icon-button color=\"primary\" (click)=\"viewProduct(product)\" matTooltip=\"عرض التفاصيل\">\r\n                  <mat-icon>visibility</mat-icon>\r\n                </button>\r\n                <button mat-icon-button color=\"accent\" (click)=\"editProduct(product)\" matTooltip=\"تعديل\">\r\n                  <mat-icon>edit</mat-icon>\r\n                </button>\r\n                <button mat-icon-button color=\"warn\" (click)=\"deleteProduct(product)\" matTooltip=\"حذف\">\r\n                  <mat-icon>delete</mat-icon>\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </ng-container>\r\n\r\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\r\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\r\n        </table>\r\n\r\n        <!-- No Data Message -->\r\n        <div *ngIf=\"filteredProducts.length === 0\" class=\"no-data\">\r\n          <mat-icon>inventory_2</mat-icon>\r\n          <h3>لا توجد منتجات</h3>\r\n          <p>لم يتم العثور على منتجات تطابق معايير البحث</p>\r\n          <button mat-raised-button color=\"primary\" (click)=\"clearFilters()\">\r\n            مسح الفلاتر\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Loading -->\r\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n        <mat-icon class=\"loading-icon\">hourglass_empty</mat-icon>\r\n        <p>جاري تحميل المنتجات...</p>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAqBC,gBAAgB,QAAQ,sBAAsB;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;ICyF1CC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,EAAA,CAAqB;IACnEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,MAAA,MACF;;;;;IAQAT,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,EAAA,CAAiB;IACtDN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAD,MAAA,MACF;;;;;IAwBFT,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnDH,EADF,CAAAC,cAAA,aAAuC,eACV;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACxD;;;;IADwBH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAW,iBAAA,CAAAC,UAAA,CAAAC,WAAA,CAAyB;;;;;IAMtDb,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAGjDH,EAFJ,CAAAC,cAAA,aAAuC,cACX,aAChB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAE/BF,EAF+B,CAAAG,YAAA,EAAQ,EAC/B,EACH;;;;IAHOH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAG,UAAA,CAAAL,MAAA,CAAoB;IACrBT,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAG,UAAA,CAAAC,MAAA,CAAoB;;;;;IAO/Bf,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG5CH,EAFJ,CAAAC,cAAA,aAAuC,mBACvB,eACF;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAExCF,EAFwC,CAAAG,YAAA,EAAW,EAClC,EACZ;;;;IAFSH,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAW,iBAAA,CAAAK,UAAA,CAAAC,YAAA,CAA0B;;;;;IAOxCjB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACjDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA3BH,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAO,UAAA,CAAAC,QAAA,CAAsB;;;;;IAK7DnB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE9CH,EADF,CAAAC,cAAA,aAAuC,eACjB;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC7E;;;;IADiBH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAoB,WAAA,OAAAC,UAAA,CAAAC,KAAA,4BAAqD;;;;;IAM3EtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,eAC8B;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAQ,EACpE;;;;;IAJuBH,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAC,KAAA,EAAwC;IAChE1B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA2B,kBAAA,MAAAF,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAN,QAAA,MACF;IAC4BnB,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAW,iBAAA,CAAAY,MAAA,CAAAK,cAAA,CAAAH,UAAA,CAAAC,KAAA,EAAmC;;;;;IAMjE1B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG7CH,EAFJ,CAAAC,cAAA,aAAuC,mBACvB,mBACuD;IACjED,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAW,EACE,EACZ;;;;IAJSH,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAI,UAAA,UAAAyB,WAAA,CAAAC,QAAA,sBAA+C;IACvD9B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAqB,WAAA,CAAAC,QAAA,uEACF;;;;;IAOJ9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAuC,cACT,iBACuE;IAAzDD,EAAA,CAAA+B,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAvB,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAASf,MAAA,CAAAgB,WAAA,CAAAN,WAAA,CAAoB;IAAA,EAAC;IACpEjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAAyF;IAAlDD,EAAA,CAAA+B,UAAA,mBAAAS,uDAAA;MAAA,MAAAP,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAvB,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAASf,MAAA,CAAAkB,WAAA,CAAAR,WAAA,CAAoB;IAAA,EAAC;IACnEjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAAuF;IAAlDD,EAAA,CAAA+B,UAAA,mBAAAW,uDAAA;MAAA,MAAAT,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAvB,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAASf,MAAA,CAAAoB,aAAA,CAAAV,WAAA,CAAsB;IAAA,EAAC;IACnEjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGtBF,EAHsB,CAAAG,YAAA,EAAW,EACpB,EACL,EACH;;;;;IAGPH,EAAA,CAAA4C,SAAA,aAA4D;;;;;IAC5D5C,EAAA,CAAA4C,SAAA,aAAkE;;;;;;IAKlE5C,EADF,CAAAC,cAAA,cAA2D,eAC/C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sOAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClDH,EAAA,CAAAC,cAAA,iBAAmE;IAAzBD,EAAA,CAAA+B,UAAA,mBAAAc,wDAAA;MAAA7C,EAAA,CAAAkC,aAAA,CAAAY,IAAA;MAAA,MAAAvB,MAAA,GAAAvB,EAAA,CAAAqC,aAAA;MAAA,OAAArC,EAAA,CAAAsC,WAAA,CAASf,MAAA,CAAAwB,YAAA,EAAc;IAAA,EAAC;IAChE/C,EAAA,CAAAE,MAAA,sEACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAjGNH,EADF,CAAAC,cAAA,cAAgD,gBAC0B;IAEtED,EAAA,CAAAgD,uBAAA,OAAyC;IAEvChD,EADA,CAAAiD,UAAA,IAAAC,6BAAA,iBAAsC,IAAAC,6BAAA,iBACC;;IAMzCnD,EAAA,CAAAgD,uBAAA,OAAoC;IAElChD,EADA,CAAAiD,UAAA,IAAAG,6BAAA,iBAAsC,IAAAC,6BAAA,iBACC;;IASzCrD,EAAA,CAAAgD,uBAAA,OAAsC;IAEpChD,EADA,CAAAiD,UAAA,IAAAK,6BAAA,iBAAsC,KAAAC,8BAAA,iBACC;;IAQzCvD,EAAA,CAAAgD,uBAAA,QAAkC;IAEhChD,EADA,CAAAiD,UAAA,KAAAO,8BAAA,iBAAsC,KAAAC,8BAAA,iBACC;;IAIzCzD,EAAA,CAAAgD,uBAAA,QAAmC;IAEjChD,EADA,CAAAiD,UAAA,KAAAS,8BAAA,iBAAsC,KAAAC,8BAAA,iBACC;;IAMzC3D,EAAA,CAAAgD,uBAAA,QAAmC;IAEjChD,EADA,CAAAiD,UAAA,KAAAW,8BAAA,iBAAsC,KAAAC,8BAAA,iBACC;;IASzC7D,EAAA,CAAAgD,uBAAA,QAAoC;IAElChD,EADA,CAAAiD,UAAA,KAAAa,8BAAA,iBAAsC,KAAAC,8BAAA,iBACC;;IAUzC/D,EAAA,CAAAgD,uBAAA,QAAqC;IAEnChD,EADA,CAAAiD,UAAA,KAAAe,8BAAA,iBAAsC,KAAAC,8BAAA,kBACC;;IAgBzCjE,EADA,CAAAiD,UAAA,KAAAiB,8BAAA,iBAAuD,KAAAC,8BAAA,iBACM;IAC/DnE,EAAA,CAAAG,YAAA,EAAQ;IAGRH,EAAA,CAAAiD,UAAA,KAAAmB,+BAAA,kBAA2D;IAQ7DpE,EAAA,CAAAG,YAAA,EAAM;;;;IAlGaH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAI,UAAA,eAAAmB,MAAA,CAAA8C,gBAAA,CAA+B;IAqF1BrE,EAAA,CAAAO,SAAA,IAAiC;IAAjCP,EAAA,CAAAI,UAAA,oBAAAmB,MAAA,CAAA+C,gBAAA,CAAiC;IACpBtE,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,UAAA,qBAAAmB,MAAA,CAAA+C,gBAAA,CAA0B;IAIvDtE,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAI,UAAA,SAAAmB,MAAA,CAAA8C,gBAAA,CAAAE,MAAA,OAAmC;;;;;IAYzCvE,EADF,CAAAC,cAAA,cAAiD,mBAChB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;AD7KZ,WAAaqE,QAAQ;EAAf,MAAOA,QAAQ;IAmBTC,IAAA;IACAC,QAAA;IAnBVC,QAAQ,GAAc,EAAE;IACxBN,gBAAgB,GAAc,EAAE;IAChCO,UAAU,GAAe,EAAE;IAC3BC,KAAK,GAAW,EAAE;IAElBC,UAAU,GAAW,EAAE;IACvBC,gBAAgB,GAAW,EAAE;IAC7BC,YAAY,GAAW,EAAE;IACzBC,SAAS,GAAY,KAAK;IAE1BC,aAAa,GAAW,CAAC;IACzBC,cAAc,GAAW,CAAC;IAC1BC,UAAU,GAAW,CAAC;IACtBC,gBAAgB,GAAW,CAAC;IAE5Bf,gBAAgB,GAAa,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IAEjHgB,YACUb,IAAgB,EAChBC,QAAqB;MADrB,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,QAAQ,GAARA,QAAQ;IACf;IAEHa,QAAQA,CAAA;MACN,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,SAAS,EAAE;IAClB;IAEAF,YAAYA,CAAA;MACV,IAAI,CAACP,SAAS,GAAG,IAAI;MACrB,IAAI,CAACR,IAAI,CAACkB,GAAG,CAAM,2CAA2C,CAAC,CAACC,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACnB,QAAQ,GAAGmB,QAAQ,CAACnB,QAAQ,IAAI,EAAE;UACvC,IAAI,CAACN,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACM,QAAQ,CAAC;UAC1C,IAAI,CAACoB,mBAAmB,EAAE;UAC1B,IAAI,CAACd,SAAS,GAAG,KAAK;UAEtB,IAAI,IAAI,CAACN,QAAQ,CAACJ,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAACyB,WAAW,CAAC,kCAAkC,CAAC;UACtD;QACF,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACD,WAAW,CAAC,gDAAgD,CAAC;UAClE,IAAI,CAACrB,QAAQ,GAAG,EAAE;UAClB,IAAI,CAACN,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACY,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;IAEAQ,cAAcA,CAAA;MACZ,IAAI,CAAChB,IAAI,CAACkB,GAAG,CAAM,6CAA6C,CAAC,CAACC,SAAS,CAAC;QAC1EC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,UAAU,GAAGkB,QAAQ,CAAClB,UAAU,IAAI,EAAE;QAC7C,CAAC;QACDqB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAACrB,UAAU,GAAG,CAChB;YAAEtE,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,KAAK;YAAEM,MAAM,EAAE,SAAS;YAAEoF,IAAI,EAAE;UAAK,CAAE,EACxD;YAAE7F,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,sBAAsB;YAAEM,MAAM,EAAE,kBAAkB;YAAEoF,IAAI,EAAE;UAAM,CAAE,CACpF;QACH;OACD,CAAC;IACJ;IAEAT,SAASA,CAAA;MACP,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAM,wCAAwC,CAAC,CAACC,SAAS,CAAC;QACrEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACjB,KAAK,GAAGiB,QAAQ,CAACjB,KAAK,IAAI,EAAE;QACnC,CAAC;QACDoB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACpB,KAAK,GAAG,CACX;YAAEvE,EAAE,EAAE,EAAE;YAAEG,MAAM,EAAE,KAAK;YAAEM,MAAM,EAAE,OAAO;YAAEqF,MAAM,EAAE;UAAK,CAAE,EACzD;YAAE9F,EAAE,EAAE,EAAE;YAAEG,MAAM,EAAE,MAAM;YAAEM,MAAM,EAAE,UAAU;YAAEqF,MAAM,EAAE;UAAK,CAAE,CAC9D;QACH;OACD,CAAC;IACJ;IAEAL,mBAAmBA,CAAA;MACjB,IAAI,CAACb,aAAa,GAAG,IAAI,CAACP,QAAQ,CAACJ,MAAM;MACzC,IAAI,CAACY,cAAc,GAAG,IAAI,CAACR,QAAQ,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxE,QAAQ,CAAC,CAACyC,MAAM;MAClE,IAAI,CAACa,UAAU,GAAG,IAAI,CAACT,QAAQ,CAAC4B,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAIF,CAAC,CAAChF,KAAK,GAAGgF,CAAC,CAAC5E,KAAM,EAAE,CAAC,CAAC;MAChF,IAAI,CAAC2D,gBAAgB,GAAG,IAAI,CAACV,QAAQ,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5E,KAAK,GAAG,GAAG,CAAC,CAAC6C,MAAM;IACzE;IAEAkC,QAAQA,CAAA;MACN,IAAI,CAACC,YAAY,EAAE;IACrB;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAACD,YAAY,EAAE;IACrB;IAEAA,YAAYA,CAAA;MACV,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACM,QAAQ,CAAC0B,MAAM,CAACO,OAAO,IAAG;QACrD,MAAMC,aAAa,GAAG,CAAC,IAAI,CAAC/B,UAAU,IACpC8B,OAAO,CAACnG,MAAM,CAACqG,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC,IACpEF,OAAO,CAAC/F,WAAW,CAACiG,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC,IACzEF,OAAO,CAAC7F,MAAM,CAAC+F,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC;QAEtE,MAAME,eAAe,GAAG,CAAC,IAAI,CAACjC,gBAAgB,IAC5C6B,OAAO,CAACK,UAAU,CAACC,QAAQ,EAAE,KAAK,IAAI,CAACnC,gBAAgB;QAEzD,MAAMoC,WAAW,GAAG,CAAC,IAAI,CAACnC,YAAY,IACpC4B,OAAO,CAACQ,MAAM,CAACF,QAAQ,EAAE,KAAK,IAAI,CAAClC,YAAY;QAEjD,OAAO6B,aAAa,IAAIG,eAAe,IAAIG,WAAW;MACxD,CAAC,CAAC;IACJ;IAEApE,YAAYA,CAAA;MACV,IAAI,CAAC+B,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,CAACX,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACM,QAAQ,CAAC;IAC5C;IAEA/C,cAAcA,CAACF,KAAa;MAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,aAAa;MACrC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,aAAa;MACpC,IAAIA,KAAK,GAAG,GAAG,EAAE,OAAO,aAAa;MACrC,OAAO,WAAW;IACpB;IAEAF,aAAaA,CAACE,KAAa;MACzB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,cAAc;MACtC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,WAAW;MAClC,IAAIA,KAAK,GAAG,GAAG,EAAE,OAAO,cAAc;MACtC,OAAO,YAAY;IACrB;IAEA2F,UAAUA,CAAA;MACR,IAAI,CAACrB,WAAW,CAAC,+BAA+B,CAAC;IACnD;IAEAzD,WAAWA,CAACqE,OAAgB;MAC1B,IAAI,CAACZ,WAAW,CAAC,sBAAsBY,OAAO,CAACnG,MAAM,EAAE,CAAC;IAC1D;IAEAgC,WAAWA,CAACmE,OAAgB;MAC1B,IAAI,CAACZ,WAAW,CAAC,iBAAiBY,OAAO,CAACnG,MAAM,gBAAgB,CAAC;IACnE;IAEAkC,aAAaA,CAACiE,OAAgB;MAC5B,IAAI,CAACZ,WAAW,CAAC,eAAeY,OAAO,CAACnG,MAAM,gBAAgB,CAAC;IACjE;IAEA6G,cAAcA,CAAA;MACZ,IAAI,CAACtB,WAAW,CAAC,qCAAqC,CAAC;IACzD;IAEQA,WAAWA,CAACuB,OAAe;MACjC,IAAI,CAAC7C,QAAQ,CAAC8C,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;;uCAjKWnD,QAAQ,EAAAxE,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;;YAARxD,QAAQ;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7DXvI,EANV,CAAAC,cAAA,aAAgC,aAEL,aACK,aACC,SACrB,eACQ;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,wFACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,gMAAmC;UACxCF,EADwC,CAAAG,YAAA,EAAI,EACtC;UAGFH,EAFJ,CAAAC,cAAA,cAA4B,iBAC0C,gBACxD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,0FACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAsD;UAA3BD,EAAA,CAAA+B,UAAA,mBAAA0G,2CAAA;YAAA,OAASD,GAAA,CAAAlB,cAAA,EAAgB;UAAA,EAAC;UACnDtH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,yFACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAQIH,EALV,CAAAC,cAAA,cAAwB,mBACY,wBACd,cACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAI1BF,EAJ0B,CAAAG,YAAA,EAAI,EAClB,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAmC,wBACf,cACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;UAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAI1BF,EAJ0B,CAAAG,YAAA,EAAI,EAClB,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAkC,wBACd,cACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;UAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAAkD;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAIvBF,EAJuB,CAAAG,YAAA,EAAI,EACf,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAoC,wBAChB,cACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACnBF,EADmB,CAAAG,YAAA,EAAW,EACxB;UAEJH,EADF,CAAAC,cAAA,eAA0B,cAC+B;UAAAD,EAAA,CAAAE,MAAA,IAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClFH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAKxBF,EALwB,CAAAG,YAAA,EAAI,EACd,EACF,EACW,EACV,EACP;UAQIH,EALV,CAAAC,cAAA,oBAA+B,wBACX,eACe,eACD,0BACgC,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,oGAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,iBAGiD;UAF1CD,EAAA,CAAA0I,gBAAA,2BAAAC,kDAAAC,MAAA;YAAA5I,EAAA,CAAA6I,kBAAA,CAAAL,GAAA,CAAA1D,UAAA,EAAA8D,MAAA,MAAAJ,GAAA,CAAA1D,UAAA,GAAA8D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxB5I,EAAA,CAAA+B,UAAA,mBAAA+G,0CAAA;YAAA,OAASN,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UAF3BzG,EAAA,CAAAG,YAAA,EAGiD;UACjDH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAE9BF,EAF8B,CAAAG,YAAA,EAAW,EACtB,EACb;UAIFH,EAFJ,CAAAC,cAAA,eAA4B,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,sBAAgF;UAApED,EAAA,CAAA0I,gBAAA,2BAAAK,uDAAAH,MAAA;YAAA5I,EAAA,CAAA6I,kBAAA,CAAAL,GAAA,CAAAzD,gBAAA,EAAA6D,MAAA,MAAAJ,GAAA,CAAAzD,gBAAA,GAAA6D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAAC5I,EAAA,CAAA+B,UAAA,6BAAAiH,yDAAA;YAAA,OAAmBR,GAAA,CAAA7B,cAAA,EAAgB;UAAA,EAAC;UAC7E3G,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC7CH,EAAA,CAAAiD,UAAA,KAAAgG,+BAAA,yBAAsE;UAI1EjJ,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,sBAA4E;UAAhED,EAAA,CAAA0I,gBAAA,2BAAAQ,uDAAAN,MAAA;YAAA5I,EAAA,CAAA6I,kBAAA,CAAAL,GAAA,CAAAxD,YAAA,EAAA4D,MAAA,MAAAJ,GAAA,CAAAxD,YAAA,GAAA4D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAAC5I,EAAA,CAAA+B,UAAA,6BAAAoH,yDAAA;YAAA,OAAmBX,GAAA,CAAA7B,cAAA,EAAgB;UAAA,EAAC;UACzE3G,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAiD,UAAA,KAAAmG,+BAAA,yBAAyD;UAI7DpJ,EADE,CAAAG,YAAA,EAAa,EACE;UAEjBH,EAAA,CAAAC,cAAA,kBAA8E;UAAnDD,EAAA,CAAA+B,UAAA,mBAAAsH,2CAAA;YAAA,OAASb,GAAA,CAAAzF,YAAA,EAAc;UAAA,EAAC;UACjD/C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,uEACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,oBAA6B,wBACT,eACU,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAA8C;UACpDF,EADoD,CAAAG,YAAA,EAAK,EACnD;UAwGNH,EAtGA,CAAAiD,UAAA,KAAAqG,wBAAA,mBAAgD,MAAAC,yBAAA,kBAsGC;UAMvDvJ,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;;;UAlNUH,EAAA,CAAAO,SAAA,IAAmB;UAAnBP,EAAA,CAAAW,iBAAA,CAAA6H,GAAA,CAAAtD,aAAA,CAAmB;UAcnBlF,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAW,iBAAA,CAAA6H,GAAA,CAAArD,cAAA,CAAoB;UAcpBnF,EAAA,CAAAO,SAAA,IAAkD;UAAlDP,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAoB,WAAA,SAAAoH,GAAA,CAAApD,UAAA,4BAAkD;UAclDpF,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,aAAAoI,GAAA,CAAAnD,gBAAA,CAA6B;UAAsBrF,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAW,iBAAA,CAAA6H,GAAA,CAAAnD,gBAAA,CAAsB;UAgBtErF,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAAwJ,gBAAA,YAAAhB,GAAA,CAAA1D,UAAA,CAAwB;UAUnB9E,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAwJ,gBAAA,YAAAhB,GAAA,CAAAzD,gBAAA,CAA8B;UAEP/E,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,YAAAoI,GAAA,CAAA5D,UAAA,CAAa;UAQpC5E,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAwJ,gBAAA,YAAAhB,GAAA,CAAAxD,YAAA,CAA0B;UAEPhF,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAI,UAAA,YAAAoI,GAAA,CAAA3D,KAAA,CAAQ;UAmBvC7E,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,kBAAA,sFAAAgI,GAAA,CAAAnE,gBAAA,CAAAE,MAAA,MAA8C;UAGtBvE,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAoI,GAAA,CAAAvD,SAAA,CAAgB;UAsGxCjF,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAoI,GAAA,CAAAvD,SAAA,CAAe;;;qBD7LvBhG,YAAY,EAAAwK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,YAAA,EACZ3K,WAAW,EAAA4K,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX9K,gBAAgB,EAChBC,YAAY,EAAA8K,EAAA,CAAAC,UAAA,EACZ9K,aAAa,EAAA+K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EACbhL,eAAe,EAAAiL,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACflL,aAAa,EAAAmL,EAAA,CAAAC,OAAA,EACbnL,cAAc,EAAAoL,EAAA,CAAAC,QAAA,EAAAC,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EACdxL,eAAe,EAAAyL,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACf1L,cAAc,EAAA2L,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdpM,kBAAkB,EAClBC,iBAAiB,EACjBE,cAAc,EAAAkM,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,UAAA,EACdnM,cAAc,EAAAoM,GAAA,CAAAC,QAAA,EACdvM,kBAAkB;MAAAwM,MAAA;IAAA;;SAKT7H,QAAQ;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}