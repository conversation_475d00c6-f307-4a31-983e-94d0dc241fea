import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'customers/add',
    loadComponent: () => import('./pages/customers/customer-form/customer-form.component').then(m => m.CustomerFormComponent)
  },
  {
    path: 'customers/edit/:id',
    loadComponent: () => import('./pages/customers/customer-form/customer-form.component').then(m => m.CustomerFormComponent)
  },
  {
    path: 'settings/countries',
    loadComponent: () => import('./pages/settings/countries/countries.component').then(m => m.CountriesComponent)
  },
  {
    path: '',
    redirectTo: '/customers/add',
    pathMatch: 'full'
  }
];
