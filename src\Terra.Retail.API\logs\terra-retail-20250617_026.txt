2025-06-17 06:52:47.367 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.502 +03:00 [WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.516 +03:00 [WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.521 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.531 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.536 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.545 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.550 +03:00 [WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.555 +03:00 [WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.561 +03:00 [WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.566 +03:00 [WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.571 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.578 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.584 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.591 +03:00 [WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.599 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.610 +03:00 [WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.615 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.620 +03:00 [WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.626 +03:00 [WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.631 +03:00 [WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.636 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.642 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.647 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.652 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.663 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.668 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.677 +03:00 [WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.683 +03:00 [WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.694 +03:00 [WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.698 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.703 +03:00 [WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.710 +03:00 [WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.714 +03:00 [WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.719 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.725 +03:00 [WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.730 +03:00 [WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.734 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.742 +03:00 [WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.747 +03:00 [WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.791 +03:00 [WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.800 +03:00 [WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.812 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.827 +03:00 [WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.834 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.842 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.847 +03:00 [WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.852 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.859 +03:00 [WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.864 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.869 +03:00 [WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.875 +03:00 [WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.879 +03:00 [WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.884 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.890 +03:00 [WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.895 +03:00 [WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.900 +03:00 [WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.905 +03:00 [WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.911 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.915 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.920 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.926 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.930 +03:00 [WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.934 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.940 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.945 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.949 +03:00 [WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.954 +03:00 [WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.961 +03:00 [WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.967 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.978 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.983 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.990 +03:00 [WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:47.997 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.002 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.009 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.013 +03:00 [WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.018 +03:00 [WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.025 +03:00 [WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.030 +03:00 [WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.035 +03:00 [WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.042 +03:00 [WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.046 +03:00 [WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.051 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.058 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.063 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.067 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.073 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.078 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.083 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.088 +03:00 [WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.094 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.099 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.103 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.110 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.117 +03:00 [WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.123 +03:00 [WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.130 +03:00 [WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.135 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.141 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.146 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.150 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.155 +03:00 [WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.161 +03:00 [WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.166 +03:00 [WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.170 +03:00 [WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.177 +03:00 [WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:48.182 +03:00 [WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 06:52:49.042 +03:00 [INF] تم إنشاء قاعدة البيانات بنجاح
2025-06-17 06:52:49.141 +03:00 [INF] تم بدء تشغيل Terra Retail ERP API
2025-06-17 06:54:16.156 +03:00 [WRN] Failed to determine the https port for redirect.
2025-06-17 06:54:16.582 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:54:35.259 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:54:48.954 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:54:55.954 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:54:55.973 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 06:54:56.624 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:54:56.628 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 06:55:12.763 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:55:51.164 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:55:58.614 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 06:55:58.609 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:56:00.117 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:56:00.124 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 06:59:19.545 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 06:59:19.547 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:59:27.258 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 06:59:27.258 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:02:33.623 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:02:33.633 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:03:00.273 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:03:00.274 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:03:46.820 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:03:46.824 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:04:10.332 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:04:10.371 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:04:20.302 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:04:20.301 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:04:26.849 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:04:26.850 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:04:28.912 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:04:28.969 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:05:04.235 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:05:04.234 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:05:07.145 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:05:07.146 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:05:37.533 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:05:37.530 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:05:39.938 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:05:39.948 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:05:48.444 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:05:48.453 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:06:14.549 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:06:14.657 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:06:18.579 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:06:18.582 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:06:34.889 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:06:34.890 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:06:37.947 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:06:37.961 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:07:01.971 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:07:01.977 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:07:05.485 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:07:05.535 +03:00 [INF] تم استرجاع 26 محافظة
[2025-06-17 07:08:57.734 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.813 +03:00 WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.859 +03:00 WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.865 +03:00 WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.870 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.875 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.884 +03:00 WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.892 +03:00 WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.900 +03:00 WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.906 +03:00 WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.914 +03:00 WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.919 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.925 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.938 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.946 +03:00 WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.953 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.962 +03:00 WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.967 +03:00 WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:57.973 +03:00 WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.004 +03:00 WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.031 +03:00 WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.063 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.125 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.142 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.155 +03:00 WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.166 +03:00 WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.174 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.195 +03:00 WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.225 +03:00 WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.241 +03:00 WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.272 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.288 +03:00 WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.370 +03:00 WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.424 +03:00 WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.437 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.450 +03:00 WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.458 +03:00 WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.468 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.475 +03:00 WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.489 +03:00 WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.497 +03:00 WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.505 +03:00 WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.523 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.536 +03:00 WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.540 +03:00 WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.547 +03:00 WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.553 +03:00 WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.558 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.565 +03:00 WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.570 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.584 +03:00 WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.589 +03:00 WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.593 +03:00 WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.600 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.605 +03:00 WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.610 +03:00 WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.616 +03:00 WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.621 +03:00 WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.629 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.635 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.642 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.650 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.656 +03:00 WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.667 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.690 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.698 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.704 +03:00 WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.709 +03:00 WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.719 +03:00 WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.725 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.732 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.738 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.748 +03:00 WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.756 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.772 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.785 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.800 +03:00 WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.809 +03:00 WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.822 +03:00 WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.831 +03:00 WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.837 +03:00 WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.842 +03:00 WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.857 +03:00 WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.867 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.876 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.905 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.914 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.920 +03:00 WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.934 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.941 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.951 +03:00 WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.958 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.967 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.973 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.982 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.987 +03:00 WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:58.997 +03:00 WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.002 +03:00 WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.051 +03:00 WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.064 +03:00 WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.081 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.091 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.106 +03:00 WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.122 +03:00 WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.136 +03:00 WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.214 +03:00 WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.221 +03:00 WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:08:59.232 +03:00 WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 07:09:00.569 +03:00 INF] تم إنشاء قاعدة البيانات بنجاح {}
[2025-06-17 07:09:01.552 +03:00 INF] تم بدء تشغيل Terra Retail ERP API {}
[2025-06-17 07:09:49.256 +03:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNDDAJ6M1R6J:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6J"}
[2025-06-17 07:09:50.378 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6I:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6I"}
[2025-06-17 07:09:50.387 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6J:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6J"}
[2025-06-17 07:09:50.378 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6H:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6H"}
[2025-06-17 07:10:01.119 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6K:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6K"}
[2025-06-17 07:10:01.119 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6L:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6L"}
[2025-06-17 07:10:01.128 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6M:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6M"}
[2025-06-17 07:10:10.799 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6O:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6O"}
[2025-06-17 07:10:10.799 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6P:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6P"}
[2025-06-17 07:10:10.806 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6N:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6N"}
[2025-06-17 07:10:13.563 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6L:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6L"}
[2025-06-17 07:10:13.563 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6K:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6K"}
[2025-06-17 07:10:13.566 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6M:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6M"}
[2025-06-17 07:10:27.995 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6O:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6O"}
[2025-06-17 07:10:27.996 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6P:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6P"}
[2025-06-17 07:10:27.999 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6N:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6N"}
[2025-06-17 07:10:31.551 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6M:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6M"}
[2025-06-17 07:10:31.565 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6M:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6M"}
[2025-06-17 07:10:31.591 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6K:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6K"}
[2025-06-17 07:10:39.893 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6Q:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6Q"}
[2025-06-17 07:10:49.409 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6Q:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6Q"}
[2025-06-17 07:11:13.058 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6N:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6N"}
[2025-06-17 07:11:13.061 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6P:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6P"}
[2025-06-17 07:11:13.082 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6O:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6O"}
[2025-06-17 07:13:37.673 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6R:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6R"}
[2025-06-17 07:13:37.705 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6R:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6R"}
[2025-06-17 07:13:37.705 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6S:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6S"}
[2025-06-17 07:14:03.740 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6R:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6R"}
[2025-06-17 07:14:03.741 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6T:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6T"}
[2025-06-17 07:14:03.741 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6S:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6S"}
[2025-06-17 07:14:27.473 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R70:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R70"}
[2025-06-17 07:14:27.473 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6V:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6V"}
[2025-06-17 07:14:27.476 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6U:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6U"}
[2025-06-17 07:14:47.174 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6U:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R6U"}
[2025-06-17 07:14:47.190 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6U:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6U"}
[2025-06-17 07:14:47.196 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R70:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R70"}
[2025-06-17 07:14:51.450 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R71:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R71"}
[2025-06-17 07:14:51.511 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R72:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R72"}
[2025-06-17 07:14:51.511 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R73:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R73"}
[2025-06-17 07:15:15.503 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R72:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R72"}
[2025-06-17 07:15:15.505 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R73:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R73"}
[2025-06-17 07:15:15.509 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R71:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R71"}
[2025-06-17 07:15:19.071 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6U:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R6U"}
[2025-06-17 07:15:19.074 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R70:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R70"}
[2025-06-17 07:15:19.093 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R6V:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R6V"}
[2025-06-17 07:15:34.276 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R71:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R71"}
[2025-06-17 07:15:34.277 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R73:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R73"}
[2025-06-17 07:15:34.278 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R72:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R72"}
[2025-06-17 07:15:39.288 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R76:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R76"}
[2025-06-17 07:15:39.289 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R74:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R74"}
[2025-06-17 07:15:39.291 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R75:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R75"}
[2025-06-17 07:17:42.971 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R78:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R78"}
[2025-06-17 07:17:42.973 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R77:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R77"}
[2025-06-17 07:17:42.973 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R79:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R79"}
[2025-06-17 07:17:45.283 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R79:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R79"}
[2025-06-17 07:17:45.286 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R78:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R78"}
[2025-06-17 07:17:45.286 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R77:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R77"}
[2025-06-17 07:17:46.688 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R78:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R78"}
[2025-06-17 07:17:46.688 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R79:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R79"}
[2025-06-17 07:17:46.690 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R77:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R77"}
[2025-06-17 07:17:47.539 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R75:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R75"}
[2025-06-17 07:17:47.541 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R74:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R74"}
[2025-06-17 07:17:47.545 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R76:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R76"}
[2025-06-17 07:17:48.393 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7A:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7A"}
[2025-06-17 07:17:48.395 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7B:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7B"}
[2025-06-17 07:17:48.398 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7C:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7C"}
[2025-06-17 07:17:55.549 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7C:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7C"}
[2025-06-17 07:17:55.550 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7A:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7A"}
[2025-06-17 07:17:55.550 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7B:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7B"}
[2025-06-17 07:18:04.087 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7E:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7E"}
[2025-06-17 07:18:04.089 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7D:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7D"}
[2025-06-17 07:18:04.090 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7F:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7F"}
[2025-06-17 07:18:07.440 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7H:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7H"}
[2025-06-17 07:18:07.443 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7G:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7G"}
[2025-06-17 07:18:07.444 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7I:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7I"}
[2025-06-17 07:18:30.385 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7J:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7J"}
[2025-06-17 07:18:30.391 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7K:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7K"}
[2025-06-17 07:18:30.391 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7L:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7L"}
[2025-06-17 07:18:32.877 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7N:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7N"}
[2025-06-17 07:18:32.880 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7O:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7O"}
[2025-06-17 07:18:32.883 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7M:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7M"}
[2025-06-17 07:19:01.161 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7Q:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7Q"}
[2025-06-17 07:19:01.161 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7R:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7R"}
[2025-06-17 07:19:01.162 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7P:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7P"}
[2025-06-17 07:19:03.217 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7T:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7T"}
[2025-06-17 07:19:03.218 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7S:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7S"}
[2025-06-17 07:19:03.218 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7U:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7U"}
[2025-06-17 07:19:09.309 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7P:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7P"}
[2025-06-17 07:19:09.329 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7R:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7R"}
[2025-06-17 07:19:09.331 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7Q:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7Q"}
[2025-06-17 07:19:10.683 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"20c2f862-aff2-46a7-807c-b874b106bf38","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7P:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDAJ6M1R7P"}
[2025-06-17 07:19:10.683 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"98593c3d-1353-4ee4-a4ea-fa43a8ccba1c","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7Q:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDAJ6M1R7Q"}
[2025-06-17 07:19:10.690 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"0804018e-ff5e-45ea-9085-a0e1b3da48d5","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDAJ6M1R7R:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDAJ6M1R7R"}
