using Microsoft.AspNetCore.Mvc;
using System.Data.SqlClient;
using Dapper;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api/simple")]
    public class SupplierStatsController : ControllerBase
    {
        private readonly string _connectionString;

        public SupplierStatsController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? "";
        }

        /// <summary>
        /// Get supplier statistics
        /// </summary>
        [HttpGet("supplier-stats")]
        public async Task<ActionResult> GetSupplierStatistics()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Get supplier counts
                var supplierStats = await connection.QuerySingleAsync(@"
                    SELECT 
                        COUNT(*) as TotalSuppliers,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveSuppliers,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveSuppliers,
                        ISNULL(SUM(OpeningBalance), 0) as TotalBalance,
                        ISNULL(SUM(CASE WHEN OpeningBalance > 0 THEN OpeningBalance ELSE 0 END), 0) as PositiveBalance,
                        ISNULL(SUM(CASE WHEN OpeningBalance < 0 THEN OpeningBalance ELSE 0 END), 0) as NegativeBalance
                    FROM Suppliers");

                // Get code information
                var lastSupplierCode = await connection.QuerySingleOrDefaultAsync<string>(@"
                    SELECT TOP 1 SupplierCode 
                    FROM Suppliers 
                    ORDER BY Id DESC");

                var lastNumber = 0;
                if (!string.IsNullOrEmpty(lastSupplierCode))
                {
                    var numberPart = lastSupplierCode.Replace("SUP", "");
                    int.TryParse(numberPart, out lastNumber);
                }

                var codeInfo = new {
                    lastSupplierNumber = lastNumber,
                    nextSupplierCode = $"SUP{(lastNumber + 1):D3}",
                    maxPossibleCodes = 999999,
                    remainingCodes = 999999 - lastNumber,
                    codeFormat = "SUP001 إلى SUP999999",
                    canAddMore = lastNumber < 999999
                };

                return Ok(new {
                    statistics = supplierStats,
                    codeInfo = codeInfo,
                    message = "تم تحميل إحصائيات الموردين بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل إحصائيات الموردين",
                    error = ex.Message
                });
            }
        }
    }
}
