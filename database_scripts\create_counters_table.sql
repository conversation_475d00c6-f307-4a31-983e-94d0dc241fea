-- Terra Retail ERP - Counters Table for Auto-Increment IDs
-- جدول العدادات للحصول على أرقام تلقائية آمنة

USE [TerraRetailERP]
GO

-- التحقق من وجود جدول العدادات (موجود بالفعل)
IF EXISTS (SELECT * FROM sysobjects WHERE name='Counters' AND xtype='U')
BEGIN
    PRINT N'جدول Counters موجود بالفعل'
END
ELSE
BEGIN
    PRINT N'جدول Counters غير موجود!'
END

-- إدراج العدادات الأساسية
IF NOT EXISTS (SELECT 1 FROM Counters WHERE CounterName = 'SupplierCode')
BEGIN
    -- الحصول على آخر رقم مورد موجود
    DECLARE @LastSupplierNumber INT = 0
    
    SELECT @LastSupplierNumber = ISNULL(MAX(CAST(SUBSTRING(SupplierCode, 4, 10) AS INT)), 0)
    FROM Suppliers 
    WHERE SupplierCode LIKE 'SUP%' 
    AND ISNUMERIC(SUBSTRING(SupplierCode, 4, 10)) = 1
    
    INSERT INTO Counters (CounterName, CurrentValue, Prefix, NumberLength, Description, IsActive, CodeFormat)
    VALUES ('SupplierCode', @LastSupplierNumber, 'SUP', 3, N'عداد أكواد الموردين', 1, 'SUP{000}')
    
    PRINT N'تم إضافة عداد أكواد الموردين - آخر رقم: ' + CAST(@LastSupplierNumber AS NVARCHAR(10))
END

-- إضافة عدادات أخرى للمستقبل
IF NOT EXISTS (SELECT 1 FROM Counters WHERE CounterName = 'CustomerCode')
BEGIN
    INSERT INTO Counters (CounterName, CurrentValue, Prefix, NumberLength, Description, IsActive, CodeFormat)
    VALUES ('CustomerCode', 0, 'CUS', 3, N'عداد أكواد العملاء', 1, 'CUS{000}')
END

IF NOT EXISTS (SELECT 1 FROM Counters WHERE CounterName = 'ProductCode')
BEGIN
    INSERT INTO Counters (CounterName, CurrentValue, Prefix, NumberLength, Description, IsActive, CodeFormat)
    VALUES ('ProductCode', 2000000000000, '', 13, N'عداد أكواد المنتجات المحلية', 1, '{0000000000000}')
END

-- إنشاء Stored Procedure للحصول على الرقم التالي
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetNextCounterValue')
    DROP PROCEDURE GetNextCounterValue
GO

CREATE PROCEDURE GetNextCounterValue
    @CounterName NVARCHAR(50),
    @NextCode NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON
    
    DECLARE @CurrentValue BIGINT
    DECLARE @Prefix NVARCHAR(10)
    DECLARE @NumberLength INT

    -- استخدام TRANSACTION للحماية من التداخل
    BEGIN TRANSACTION

    BEGIN TRY
        -- الحصول على القيم الحالية مع قفل الصف
        SELECT
            @CurrentValue = CurrentValue,
            @Prefix = ISNULL(Prefix, ''),
            @NumberLength = NumberLength
        FROM Counters WITH (UPDLOCK, ROWLOCK)
        WHERE CounterName = @CounterName AND IsActive = 1
        
        IF @CurrentValue IS NULL
        BEGIN
            ROLLBACK TRANSACTION
            RAISERROR(N'العداد غير موجود أو غير نشط: %s', 16, 1, @CounterName)
            RETURN
        END
        
        -- زيادة القيمة
        SET @CurrentValue = @CurrentValue + 1
        
        -- تحديث العداد
        UPDATE Counters 
        SET 
            CurrentValue = @CurrentValue,
            UpdatedAt = GETDATE()
        WHERE CounterName = @CounterName
        
        -- تكوين الكود الجديد
        SET @NextCode = @Prefix + RIGHT('000000000000' + CAST(@CurrentValue AS NVARCHAR(12)), @NumberLength)
        
        COMMIT TRANSACTION
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        THROW
    END CATCH
END
GO

-- إنشاء Stored Procedure لإعادة تعيين العداد
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'ResetCounter')
    DROP PROCEDURE ResetCounter
GO

CREATE PROCEDURE ResetCounter
    @CounterName NVARCHAR(50),
    @NewValue INT
AS
BEGIN
    SET NOCOUNT ON
    
    UPDATE Counters 
    SET 
        CurrentValue = @NewValue,
        UpdatedAt = GETDATE()
    WHERE CounterName = @CounterName AND IsActive = 1
    
    IF @@ROWCOUNT = 0
    BEGIN
        RAISERROR(N'العداد غير موجود أو غير نشط: %s', 16, 1, @CounterName)
        RETURN
    END
    
    PRINT N'تم إعادة تعيين العداد ' + @CounterName + N' إلى القيمة: ' + CAST(@NewValue AS NVARCHAR(10))
END
GO

-- اختبار الـ Stored Procedure
DECLARE @TestCode NVARCHAR(50)
EXEC GetNextCounterValue 'SupplierCode', @TestCode OUTPUT
PRINT N'الكود التالي للمورد: ' + @TestCode

-- عرض حالة العدادات
SELECT
    CounterName as [اسم العداد],
    CurrentValue as [القيمة الحالية],
    Prefix as [البادئة],
    NumberLength as [طول الرقم],
    Description as [الوصف],
    IsActive as [نشط],
    CreatedAt as [تاريخ الإنشاء],
    UpdatedAt as [تاريخ التحديث]
FROM Counters
ORDER BY CounterName

PRINT N'تم إنشاء نظام العدادات بنجاح!'
