using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// القيود المحاسبية
    /// </summary>
    public class JournalEntry : BaseEntity
    {
        /// <summary>
        /// رقم القيد
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string EntryNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ القيد
        /// </summary>
        public DateTime EntryDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// وصف القيد
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// نوع القيد
        /// </summary>
        public JournalEntryType EntryType { get; set; } = JournalEntryType.Manual;

        /// <summary>
        /// إجمالي المدين
        /// </summary>
        public decimal TotalDebit { get; set; } = 0;

        /// <summary>
        /// إجمالي الدائن
        /// </summary>
        public decimal TotalCredit { get; set; } = 0;

        /// <summary>
        /// حالة القيد
        /// </summary>
        public JournalEntryStatus Status { get; set; } = JournalEntryStatus.Draft;

        /// <summary>
        /// مرجع القيد (رقم الفاتورة مثلاً)
        /// </summary>
        [MaxLength(50)]
        public string? Reference { get; set; }

        /// <summary>
        /// نوع المرجع
        /// </summary>
        public JournalReferenceType? ReferenceType { get; set; }

        /// <summary>
        /// معرف المرجع في الجدول المرتبط
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ القيد
        /// </summary>
        public int PreparedById { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime PreparedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي راجع القيد
        /// </summary>
        public int? ReviewedById { get; set; }

        /// <summary>
        /// تاريخ المراجعة
        /// </summary>
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي اعتمد القيد
        /// </summary>
        public int? ApprovedById { get; set; }

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// هل القيد مرحل
        /// </summary>
        public bool IsPosted { get; set; } = false;

        /// <summary>
        /// تاريخ الترحيل
        /// </summary>
        public DateTime? PostedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي رحل القيد
        /// </summary>
        public int? PostedById { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// الفترة المحاسبية
        /// </summary>
        [MaxLength(10)]
        public string? AccountingPeriod { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(1000)]
        public string? AdditionalNotes { get; set; }

        /// <summary>
        /// هل القيد قابل للتعديل
        /// </summary>
        public bool IsEditable { get; set; } = true;

        /// <summary>
        /// هل القيد تلقائي
        /// </summary>
        public bool IsAutoGenerated { get; set; } = false;

        // Navigation Properties
        public virtual Branch Branch { get; set; } = null!;
        public virtual User PreparedBy { get; set; } = null!;
        public virtual User? ReviewedBy { get; set; }
        public virtual User? ApprovedBy { get; set; }
        public virtual User? PostedBy { get; set; }
        public virtual ICollection<JournalEntryItem> Items { get; set; } = new List<JournalEntryItem>();
    }

    /// <summary>
    /// بنود القيد المحاسبي
    /// </summary>
    public class JournalEntryItem : BaseEntity
    {
        /// <summary>
        /// معرف القيد
        /// </summary>
        public int JournalEntryId { get; set; }

        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رقم السطر
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// وصف البند
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        public decimal DebitAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        public decimal CreditAmount { get; set; } = 0;

        /// <summary>
        /// معرف مركز التكلفة
        /// </summary>
        public int? CostCenterId { get; set; }

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// سعر الصرف
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// المبلغ بالعملة الأساسية
        /// </summary>
        public decimal BaseAmount { get; set; }

        /// <summary>
        /// ملاحظات البند
        /// </summary>
        [MaxLength(500)]
        public string? ItemNotes { get; set; }

        // Navigation Properties
        public virtual JournalEntry JournalEntry { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
        public virtual CostCenter? CostCenter { get; set; }
    }

    /// <summary>
    /// أنواع القيود المحاسبية
    /// </summary>
    public enum JournalEntryType
    {
        /// <summary>
        /// قيد يدوي
        /// </summary>
        Manual = 1,

        /// <summary>
        /// قيد تلقائي من المبيعات
        /// </summary>
        AutoSales = 2,

        /// <summary>
        /// قيد تلقائي من المشتريات
        /// </summary>
        AutoPurchases = 3,

        /// <summary>
        /// قيد تلقائي من المدفوعات
        /// </summary>
        AutoPayments = 4,

        /// <summary>
        /// قيد تلقائي من المقبوضات
        /// </summary>
        AutoReceipts = 5,

        /// <summary>
        /// قيد افتتاحي
        /// </summary>
        Opening = 6,

        /// <summary>
        /// قيد ختامي
        /// </summary>
        Closing = 7,

        /// <summary>
        /// قيد تسوية
        /// </summary>
        Adjustment = 8
    }

    /// <summary>
    /// حالة القيد المحاسبي
    /// </summary>
    public enum JournalEntryStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,

        /// <summary>
        /// قيد المراجعة
        /// </summary>
        UnderReview = 2,

        /// <summary>
        /// معتمد
        /// </summary>
        Approved = 3,

        /// <summary>
        /// مرحل
        /// </summary>
        Posted = 4,

        /// <summary>
        /// مرفوض
        /// </summary>
        Rejected = 5,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 6
    }

    /// <summary>
    /// أنواع مراجع القيود
    /// </summary>
    public enum JournalReferenceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        SalesInvoice = 1,

        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        PurchaseInvoice = 2,

        /// <summary>
        /// سند قبض
        /// </summary>
        Receipt = 3,

        /// <summary>
        /// سند دفع
        /// </summary>
        Payment = 4,

        /// <summary>
        /// حركة خزينة
        /// </summary>
        CashTransaction = 5,

        /// <summary>
        /// كشف راتب
        /// </summary>
        Payroll = 6,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 7
    }
}
