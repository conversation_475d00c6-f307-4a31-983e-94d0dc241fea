using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomerTypesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<CustomerTypesController> _logger;

        public CustomerTypesController(TerraRetailDbContext context, ILogger<CustomerTypesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع أنواع العملاء
        /// </summary>
        /// <returns>قائمة أنواع العملاء</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetCustomerTypes()
        {
            try
            {
                var customerTypes = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.Id)
                    .Select(ct => new
                    {
                        id = ct.Id,
                        nameAr = ct.NameAr,
                        nameEn = ct.NameEn,
                        defaultDiscountPercentage = ct.DefaultDiscountPercentage,
                        isActive = ct.IsActive
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع {Count} نوع عميل", customerTypes.Count);
                return Ok(customerTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع أنواع العملاء");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على نوع عميل محدد
        /// </summary>
        /// <param name="id">معرف نوع العميل</param>
        /// <returns>نوع العميل</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetCustomerType(int id)
        {
            try
            {
                var customerType = await _context.CustomerTypes
                    .Where(ct => ct.Id == id && ct.IsActive)
                    .Select(ct => new
                    {
                        id = ct.Id,
                        nameAr = ct.NameAr,
                        nameEn = ct.NameEn,
                        defaultDiscountPercentage = ct.DefaultDiscountPercentage,
                        isActive = ct.IsActive
                    })
                    .FirstOrDefaultAsync();

                if (customerType == null)
                {
                    return NotFound(new { message = "نوع العميل غير موجود" });
                }

                return Ok(customerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع نوع العميل {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }
    }
}
