-- 🏪 بيانات تجريبية كاملة لمشروع Terra Retail ERP
-- مع دورة محاسبية متكاملة وفئات أسعار متعددة وموردين متعددين

-- 🗑️ مسح البيانات وإعادة تعيين العدادات (DELETE RESET 0)
DELETE FROM JournalEntryDetails;
DELETE FROM JournalEntries;
DELETE FROM CashReceipts;
DELETE FROM CashPayments;
DELETE FROM FinancialTransactions;
DELETE FROM ProductPrices;
DELETE FROM ProductStocks;
DELETE FROM Products WHERE Id > 1;
DELETE FROM Suppliers WHERE Id > 1;
DELETE FROM Customers WHERE Id > 1;
DELETE FROM Branches WHERE Id > 2;
DELETE FROM Users WHERE Id > 2;
DELETE FROM Categories WHERE Id > 4;
DELETE FROM BarcodeTypes;
DELETE FROM ChartOfAccounts WHERE Id > 33;
DELETE FROM CustomerTypes;
DELETE FROM SupplierTypes;

-- إعادة تعيين العدادات إلى الصفر
UPDATE Counters SET CurrentValue = 0;
DBCC CHECKIDENT ('BarcodeTypes', RESEED, 0);
DBCC CHECKIDENT ('Products', RESEED, 1);
DBCC CHECKIDENT ('Suppliers', RESEED, 1);
DBCC CHECKIDENT ('Customers', RESEED, 1);
DBCC CHECKIDENT ('Branches', RESEED, 2);
DBCC CHECKIDENT ('Users', RESEED, 2);
DBCC CHECKIDENT ('Categories', RESEED, 4);
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 33);
DBCC CHECKIDENT ('CustomerTypes', RESEED, 0);
DBCC CHECKIDENT ('SupplierTypes', RESEED, 0);

-- 📊 إنشاء أنواع الباركود
INSERT INTO BarcodeTypes (TypeCode, NameAr, NameEn, Format, IsActive, CreatedAt) VALUES
('EAN13', N'باركود أوروبي 13 رقم', 'European Article Number 13', 'EAN-13', 1, GETDATE()),
('CODE128', N'كود 128', 'Code 128', 'Code128', 1, GETDATE()),
('QR', N'رمز الاستجابة السريعة', 'QR Code', 'QR', 1, GETDATE()),
('UPC', N'رمز المنتج العالمي', 'Universal Product Code', 'UPC-A', 1, GETDATE());

-- 🏢 إنشاء الفروع
INSERT INTO Branches (Code, NameAr, NameEn, Address, Phone, Email, IsActive, IsMainBranch, CreatedAt) VALUES
('BR001', N'الفرع الرئيسي - القاهرة', 'Main Branch - Cairo', N'شارع التحرير، وسط البلد، القاهرة', '02-********', '<EMAIL>', 1, 1, GETDATE()),
('BR002', N'فرع الإسكندرية', 'Alexandria Branch', N'شارع الكورنيش، الإسكندرية', '03-4888888', '<EMAIL>', 1, 0, GETDATE()),
('BR003', N'فرع الجيزة', 'Giza Branch', N'شارع الهرم، الجيزة', '02-33777777', '<EMAIL>', 1, 0, GETDATE());

-- 👥 إنشاء المستخدمين
INSERT INTO Users (Username, Email, PasswordHash, FullName, IsActive, IsSystemAdmin, CreatedAt) VALUES
('admin', '<EMAIL>', 'hashed_password_123', N'أحمد محمد الإدارة', 1, 1, GETDATE()),
('sales1', '<EMAIL>', 'hashed_password_456', N'فاطمة علي المبيعات', 1, 0, GETDATE()),
('cashier1', '<EMAIL>', 'hashed_password_789', N'محمد سعد الكاشير', 1, 0, GETDATE()),
('manager1', '<EMAIL>', 'hashed_password_101', N'سارة أحمد المدير', 1, 0, GETDATE());

-- 🏷️ إنشاء فئات المنتجات
INSERT INTO Categories (NameAr, NameEn, ParentId, DisplayOrder, IsActive, CreatedAt) VALUES
(N'غرف النوم', 'Bedrooms', NULL, 1, 1, GETDATE()),
(N'غرف المعيشة', 'Living Rooms', NULL, 2, 1, GETDATE()),
(N'غرف الطعام', 'Dining Rooms', NULL, 3, 1, GETDATE()),
(N'المطابخ', 'Kitchens', NULL, 4, 1, GETDATE()),
(N'الأدوات الكهربائية', 'Electrical Appliances', NULL, 5, 1, GETDATE()),
(N'الأدوات المنزلية', 'Home Accessories', NULL, 6, 1, GETDATE()),
(N'الإضاءة', 'Lighting', NULL, 7, 1, GETDATE());

-- 🏪 إنشاء أنواع العملاء
INSERT INTO CustomerTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'عميل تجزئة', 'Retail Customer', 1, 1, GETDATE()),
(N'عميل جملة', 'Wholesale Customer', 2, 1, GETDATE()),
(N'عميل مميز', 'VIP Customer', 3, 1, GETDATE()),
(N'عميل مؤسسي', 'Corporate Customer', 4, 1, GETDATE());

-- 🏭 إنشاء أنواع الموردين
INSERT INTO SupplierTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'مورد محلي', 'Local Supplier', 1, 1, GETDATE()),
(N'مورد مستورد', 'Import Supplier', 2, 1, GETDATE()),
(N'مصنع', 'Factory', 3, 1, GETDATE()),
(N'وكيل تجاري', 'Commercial Agent', 4, 1, GETDATE());

-- 🏭 إنشاء الموردين
INSERT INTO Suppliers (SupplierCode, NameAr, NameEn, SupplierTypeId, Phone1, Email, Address, PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('SUP001', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 1, '02-24567890', '<EMAIL>', N'المنطقة الصناعية، القاهرة', 30, 100000.00, 0.00, 0.00, 1, GETDATE()),
('SUP002', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 3, '03-5678901', '<EMAIL>', N'برج العرب، الإسكندرية', 45, 150000.00, 0.00, 0.00, 1, GETDATE()),
('SUP003', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, '02-33456789', '<EMAIL>', N'مدينة نصر، القاهرة', 30, 80000.00, 0.00, 0.00, 1, GETDATE()),
('SUP004', N'مستوردو الإضاءة الأوروبية', 'European Lighting Importers', 2, '02-25678901', '<EMAIL>', N'مصر الجديدة، القاهرة', 60, 200000.00, 0.00, 0.00, 1, GETDATE()),
('SUP005', N'مصنع السجاد الشرقي', 'Oriental Carpet Factory', 3, '02-44567890', '<EMAIL>', N'حلوان، القاهرة', 30, 120000.00, 0.00, 0.00, 1, GETDATE());

-- 👥 إنشاء العملاء
INSERT INTO Customers (CustomerCode, NameAr, NameEn, CustomerTypeId, Phone1, Email, Address, PriceCategoryId, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('CUS001', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, '01012345678', '<EMAIL>', N'شارع الجمهورية، وسط البلد، القاهرة', 1, 0.00, 0.00, 1, GETDATE()),
('CUS002', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, '01098765432', '<EMAIL>', N'شارع الكورنيش، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE()),
('CUS003', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 4, '02-33456789', '<EMAIL>', N'شارع النيل، الجيزة', 2, 0.00, 0.00, 1, GETDATE()),
('CUS004', N'محمد سعد الدين', 'Mohamed Saad El Din', 3, '01076543210', '<EMAIL>', N'شارع التحرير، القاهرة', 3, 0.00, 0.00, 1, GETDATE()),
('CUS005', N'سارة علي محمود', 'Sara Ali Mahmoud', 1, '01055555555', '<EMAIL>', N'شارع سعد زغلول، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE());

-- 📦 إنشاء المنتجات مع فئات أسعار وموردين متعددين
INSERT INTO Products (ProductCode, NameAr, NameEn, Description, CategoryId, UnitId, BarcodeTypeId, Barcode, CostPrice, BasePrice, ProfitMargin, MinimumStock, MaximumStock, IsActive, CreatedAt) VALUES
-- غرف النوم
('PRD001', N'سرير خشب زان مقاس 180×200', 'Beech Wood Bed 180x200', N'سرير من خشب الزان الطبيعي مع كومودينو', 5, 1, 1, '1234567890123', 2500.00, 3500.00, 40.00, 5, 50, 1, GETDATE()),
('PRD002', N'دولاب ملابس 6 أبواب', '6-Door Wardrobe', N'دولاب ملابس من الخشب المضغوط مع مرآة', 5, 1, 1, '1234567890124', 3000.00, 4200.00, 40.00, 3, 30, 1, GETDATE()),
('PRD003', N'تسريحة مع مرآة ومقعد', 'Dressing Table with Mirror', N'تسريحة خشبية مع مرآة كبيرة ومقعد مبطن', 5, 1, 1, '1234567890125', 1200.00, 1680.00, 40.00, 5, 40, 1, GETDATE()),

-- غرف المعيشة
('PRD004', N'طقم صالون جلد طبيعي 3+2+1', 'Leather Sofa Set 3+2+1', N'طقم صالون من الجلد الطبيعي مع إطار خشبي', 6, 2, 1, '1234567890126', 8000.00, 12000.00, 50.00, 2, 20, 1, GETDATE()),
('PRD005', N'طاولة وسط زجاجية', 'Glass Coffee Table', N'طاولة وسط من الزجاج المقسى مع قاعدة معدنية', 6, 1, 1, '1234567890127', 800.00, 1200.00, 50.00, 5, 30, 1, GETDATE()),

-- الأدوات الكهربائية
('PRD006', N'ثلاجة سامسونج 18 قدم', 'Samsung Refrigerator 18ft', N'ثلاجة سامسونج نوفروست 18 قدم مع فريزر', 9, 1, 1, '1234567890132', 8000.00, 11200.00, 40.00, 5, 25, 1, GETDATE()),
('PRD007', N'غسالة أتوماتيك LG 7 كيلو', 'LG Automatic Washer 7kg', N'غسالة أتوماتيك من LG سعة 7 كيلو', 9, 1, 1, '1234567890133', 4500.00, 6300.00, 40.00, 3, 20, 1, GETDATE()),

-- الأدوات المنزلية
('PRD008', N'طقم أواني طبخ تيفال 12 قطعة', 'Tefal Cookware Set 12pcs', N'طقم أواني طبخ تيفال مع طلاء تيفلون', 10, 2, 1, '1234567890135', 800.00, 1120.00, 40.00, 10, 50, 1, GETDATE()),
('PRD009', N'مجموعة أطباق بورسلين 24 قطعة', 'Porcelain Dinnerware Set 24pcs', N'مجموعة أطباق من البورسلين الفاخر', 10, 2, 1, '1234567890136', 600.00, 840.00, 40.00, 8, 40, 1, GETDATE()),

-- الإضاءة
('PRD010', N'نجفة كريستال كلاسيكية', 'Classic Crystal Chandelier', N'نجفة من الكريستال الطبيعي تصميم كلاسيكي', 11, 1, 1, '1234567890138', 2000.00, 2800.00, 40.00, 3, 15, 1, GETDATE());

-- 🔗 ربط المنتجات بالموردين (كل منتج له موردين أو أكثر)
INSERT INTO ProductSuppliers (ProductId, SupplierId, SupplierProductCode, CostPrice, MinOrderQuantity, LeadTimeDays, IsMainSupplier, IsActive, CreatedAt) VALUES
-- سرير خشب زان - موردين
(2, 2, 'EGY-BED-001', 2500.00, 1, 7, 1, 1, GETDATE()),
(2, 6, 'ORI-BED-001', 2400.00, 2, 10, 0, 1, GETDATE()),

-- دولاب ملابس - موردين
(3, 2, 'EGY-WAR-001', 3000.00, 1, 7, 1, 1, GETDATE()),
(3, 4, 'UHT-WAR-001', 2900.00, 1, 5, 0, 1, GETDATE()),

-- تسريحة - مورد واحد
(4, 2, 'EGY-DRS-001', 1200.00, 1, 7, 1, 1, GETDATE()),

-- طقم صالون - موردين
(5, 2, 'EGY-SOF-001', 8000.00, 1, 14, 1, 1, GETDATE()),
(5, 6, 'ORI-SOF-001', 7800.00, 1, 21, 0, 1, GETDATE()),

-- طاولة وسط - مورد واحد
(6, 4, 'UHT-TAB-001', 800.00, 1, 5, 1, 1, GETDATE()),

-- ثلاجة سامسونج - موردين
(7, 3, 'MOD-REF-001', 8000.00, 1, 3, 1, 1, GETDATE()),
(7, 5, 'EUR-REF-001', 7900.00, 1, 7, 0, 1, GETDATE()),

-- غسالة LG - مورد واحد
(8, 3, 'MOD-WAS-001', 4500.00, 1, 3, 1, 1, GETDATE()),

-- طقم أواني تيفال - موردين
(9, 4, 'UHT-COO-001', 800.00, 5, 5, 1, 1, GETDATE()),
(9, 3, 'MOD-COO-001', 820.00, 3, 7, 0, 1, GETDATE()),

-- أطباق بورسلين - مورد واحد
(10, 4, 'UHT-DIN-001', 600.00, 2, 5, 1, 1, GETDATE()),

-- نجفة كريستال - موردين
(11, 5, 'EUR-CHA-001', 2000.00, 1, 14, 1, 1, GETDATE()),
(11, 6, 'ORI-CHA-001', 1950.00, 1, 21, 0, 1, GETDATE());

-- 🏷️ إنشاء أسعار المنتجات حسب الفئات والفروع
INSERT INTO ProductBranchPrices (ProductId, BranchId, PriceCategoryId, Price, DiscountPercentage, IsActive, EffectiveDate, CreatedAt) VALUES
-- سرير خشب زان - جميع الفروع وجميع فئات الأسعار
(2, 3, 1, 3500.00, 0.00, 1, GETDATE(), GETDATE()),    -- تجزئة
(2, 3, 2, 3150.00, 10.00, 1, GETDATE(), GETDATE()),   -- جملة
(2, 3, 3, 2800.00, 20.00, 1, GETDATE(), GETDATE()),   -- مميز
(2, 3, 4, 2625.00, 25.00, 1, GETDATE(), GETDATE()),   -- موظفين

(2, 4, 1, 3500.00, 0.00, 1, GETDATE(), GETDATE()),    -- الإسكندرية
(2, 4, 2, 3150.00, 10.00, 1, GETDATE(), GETDATE()),
(2, 4, 3, 2800.00, 20.00, 1, GETDATE(), GETDATE()),
(2, 4, 4, 2625.00, 25.00, 1, GETDATE(), GETDATE()),

-- ثلاجة سامسونج - جميع الفروع
(7, 3, 1, 11200.00, 0.00, 1, GETDATE(), GETDATE()),   -- تجزئة
(7, 3, 2, 10080.00, 10.00, 1, GETDATE(), GETDATE()),  -- جملة
(7, 3, 3, 8960.00, 20.00, 1, GETDATE(), GETDATE()),   -- مميز
(7, 3, 4, 8400.00, 25.00, 1, GETDATE(), GETDATE()),   -- موظفين

(7, 4, 1, 11200.00, 0.00, 1, GETDATE(), GETDATE()),   -- الإسكندرية
(7, 4, 2, 10080.00, 10.00, 1, GETDATE(), GETDATE()),
(7, 4, 3, 8960.00, 20.00, 1, GETDATE(), GETDATE()),
(7, 4, 4, 8400.00, 25.00, 1, GETDATE(), GETDATE()),

-- طقم أواني تيفال - جميع الفروع
(9, 3, 1, 1120.00, 0.00, 1, GETDATE(), GETDATE()),    -- تجزئة
(9, 3, 2, 1008.00, 10.00, 1, GETDATE(), GETDATE()),   -- جملة
(9, 3, 3, 896.00, 20.00, 1, GETDATE(), GETDATE()),    -- مميز
(9, 3, 4, 840.00, 25.00, 1, GETDATE(), GETDATE()),    -- موظفين

(9, 4, 1, 1120.00, 0.00, 1, GETDATE(), GETDATE()),    -- الإسكندرية
(9, 4, 2, 1008.00, 10.00, 1, GETDATE(), GETDATE()),
(9, 4, 3, 896.00, 20.00, 1, GETDATE(), GETDATE()),
(9, 4, 4, 840.00, 25.00, 1, GETDATE(), GETDATE());

-- 📊 إنشاء مخزون المنتجات بالفروع
INSERT INTO ProductBranchStock (ProductId, BranchId, CurrentStock, ReservedStock, MinimumStock, MaximumStock, ReorderPoint, LastStockDate, CreatedAt) VALUES
-- الفرع الرئيسي - القاهرة
(2, 3, 0, 0, 5, 50, 10, GETDATE(), GETDATE()),   -- سرير خشب زان
(3, 3, 0, 0, 3, 30, 8, GETDATE(), GETDATE()),    -- دولاب ملابس
(4, 3, 0, 0, 5, 40, 10, GETDATE(), GETDATE()),   -- تسريحة
(5, 3, 0, 0, 2, 20, 5, GETDATE(), GETDATE()),    -- طقم صالون
(6, 3, 0, 0, 5, 30, 10, GETDATE(), GETDATE()),   -- طاولة وسط
(7, 3, 0, 0, 5, 25, 8, GETDATE(), GETDATE()),    -- ثلاجة سامسونج
(8, 3, 0, 0, 3, 20, 6, GETDATE(), GETDATE()),    -- غسالة LG
(9, 3, 0, 0, 10, 50, 15, GETDATE(), GETDATE()),  -- طقم أواني
(10, 3, 0, 0, 8, 40, 12, GETDATE(), GETDATE()),  -- أطباق بورسلين
(11, 3, 0, 0, 3, 15, 5, GETDATE(), GETDATE()),   -- نجفة كريستال

-- فرع الإسكندرية
(2, 4, 0, 0, 3, 30, 8, GETDATE(), GETDATE()),    -- سرير خشب زان
(7, 4, 0, 0, 3, 15, 5, GETDATE(), GETDATE()),    -- ثلاجة سامسونج
(9, 4, 0, 0, 5, 30, 10, GETDATE(), GETDATE());   -- طقم أواني

-- 🔄 دورة محاسبية كاملة: مشتريات → مبيعات → مقبوضات → مدفوعات

-- 1️⃣ مشتريات: شراء 10 أسرة من شركة الأثاث المصري بـ 25000 جنيه
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, TotalDebit, TotalCredit, Status, UserId, CreatedAt) VALUES
('JE001', GETDATE(), N'قيد مشتريات من شركة الأثاث المصري - 10 أسرة', 1, 25000.00, 25000.00, 2, 3, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, CreatedAt) VALUES
(1, 16, 25000.00, 0.00, N'مخزون البضاعة', GETDATE()),
(1, 28, 0.00, 25000.00, N'شركة الأثاث المصري', GETDATE());

-- تحديث المخزون والمورد
UPDATE ProductBranchStock SET CurrentStock = 10, LastStockDate = GETDATE() WHERE ProductId = 2 AND BranchId = 3;
UPDATE Suppliers SET CurrentBalance = CurrentBalance + 25000.00 WHERE Id = 2;

-- 2️⃣ مبيعات: بيع 3 أسرة لأحمد محمد علي بـ 10500 جنيه (سعر تجزئة)
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, TotalDebit, TotalCredit, Status, UserId, CreatedAt) VALUES
('JE002', GETDATE(), N'قيد مبيعات لأحمد محمد علي - 3 أسرة', 2, 10500.00, 10500.00, 2, 3, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, CreatedAt) VALUES
(2, 15, 10500.00, 0.00, N'أحمد محمد علي', GETDATE()),
(2, 38, 0.00, 10500.00, N'مبيعات', GETDATE());

-- قيد تكلفة البضاعة المباعة
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, TotalDebit, TotalCredit, Status, UserId, CreatedAt) VALUES
('JE003', GETDATE(), N'قيد تكلفة البضاعة المباعة - 3 أسرة', 3, 7500.00, 7500.00, 2, 3, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, CreatedAt) VALUES
(3, 54, 7500.00, 0.00, N'تكلفة البضاعة المباعة', GETDATE()),
(3, 16, 0.00, 7500.00, N'مخزون البضاعة', GETDATE());

-- تحديث المخزون والعميل
UPDATE ProductBranchStock SET CurrentStock = CurrentStock - 3, LastStockDate = GETDATE() WHERE ProductId = 2 AND BranchId = 3;
UPDATE Customers SET CurrentBalance = CurrentBalance + 10500.00 WHERE Id = 2;

-- 3️⃣ مقبوضات: تحصيل 5000 جنيه من أحمد محمد علي
INSERT INTO CashReceipts (ReceiptNumber, CustomerId, Amount, ReceiptDate, PaymentMethod, Notes, CreatedAt) VALUES
('REC001', 2, 5000.00, GETDATE(), N'نقدي', N'دفعة على حساب فاتورة المبيعات', GETDATE());

INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, TotalDebit, TotalCredit, Status, UserId, CreatedAt) VALUES
('JE004', GETDATE(), N'قيد تحصيل من أحمد محمد علي', 4, 5000.00, 5000.00, 2, 3, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, CreatedAt) VALUES
(4, 11, 5000.00, 0.00, N'الصندوق الرئيسي', GETDATE()),
(4, 15, 0.00, 5000.00, N'أحمد محمد علي', GETDATE());

-- تحديث رصيد العميل
UPDATE Customers SET CurrentBalance = CurrentBalance - 5000.00 WHERE Id = 2;

-- 4️⃣ مدفوعات: دفع 10000 جنيه لشركة الأثاث المصري
INSERT INTO CashPayments (PaymentNumber, SupplierId, Amount, PaymentDate, PaymentMethod, Notes, CreatedAt) VALUES
('PAY001', 2, 10000.00, GETDATE(), N'نقدي', N'دفعة على حساب فاتورة المشتريات', GETDATE());

INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, TotalDebit, TotalCredit, Status, UserId, CreatedAt) VALUES
('JE005', GETDATE(), N'قيد دفع لشركة الأثاث المصري', 5, 10000.00, 10000.00, 2, 3, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, CreatedAt) VALUES
(5, 28, 10000.00, 0.00, N'شركة الأثاث المصري', GETDATE()),
(5, 11, 0.00, 10000.00, N'الصندوق الرئيسي', GETDATE());

-- تحديث رصيد المورد
UPDATE Suppliers SET CurrentBalance = CurrentBalance - 10000.00 WHERE Id = 2;

PRINT N'تم إنشاء دورة محاسبية كاملة مع فئات أسعار متعددة وموردين متعددين ✅';
PRINT N'🎉 البرنامج جاهز للاستخدام مع جميع المتطلبات!';
