using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("Products")]
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string ProductCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int CategoryId { get; set; }

        public int UnitId { get; set; }

        [StringLength(50)]
        public string? Barcode { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal ProfitMargin { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MinimumStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaximumStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ReorderPoint { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? Weight { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Category Category { get; set; } = null!;
        public virtual Unit Unit { get; set; } = null!;
    }

    [Table("Categories")]
    public class Category
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int? ParentId { get; set; }

        public int DisplayOrder { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Category? Parent { get; set; }
        public virtual ICollection<Category> Children { get; set; } = new List<Category>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    [Table("Units")]
    public class Unit
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(10)]
        public string Symbol { get; set; } = string.Empty;

        public int DisplayOrder { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
