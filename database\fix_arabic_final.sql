-- إصلاح نهائي للترميز العربي
-- Final Arabic Encoding Fix

USE TerraRetailERP;
GO

-- تغيير ترتيب قاعدة البيانات لدعم العربية
ALTER DATABASE TerraRetailERP COLLATE Arabic_CI_AS;
GO

PRINT N'إصلاح نهائي للبيانات العربية...';

-- حذف وإعادة إدراج أنواع العملاء بالترميز الصحيح
TRUNCATE TABLE CustomerTypes;

INSERT INTO CustomerTypes (NameAr, NameEn, DefaultDiscountPercentage, IsActive, CreatedAt) VALUES
(N'عميل عادي', N'Regular Customer', 0, 1, GETUTCDATE()),
(N'عميل جملة', N'Wholesale Customer', 5, 1, GETUTCDATE()),
(N'عميل VIP', N'VIP Customer', 10, 1, GETUTCDATE()),
(N'عميل مؤسسي', N'Corporate Customer', 7, 1, GETUTCDATE()),
(N'عميل حكومي', N'Government Customer', 3, 1, GETUTCDATE());

-- حذف وإعادة إدراج وحدات القياس
TRUNCATE TABLE Units;

INSERT INTO Units (NameAr, NameEn, Symbol, UnitType, IsDefault, IsActive, CreatedAt) VALUES
(N'قطعة', N'Piece', 'PC', 1, 1, 1, GETUTCDATE()),
(N'كيلوجرام', N'Kilogram', 'KG', 2, 0, 1, GETUTCDATE()),
(N'جرام', N'Gram', 'G', 2, 0, 1, GETUTCDATE()),
(N'متر', N'Meter', 'M', 3, 0, 1, GETUTCDATE()),
(N'سنتيمتر', N'Centimeter', 'CM', 3, 0, 1, GETUTCDATE()),
(N'لتر', N'Liter', 'L', 5, 0, 1, GETUTCDATE()),
(N'مليلتر', N'Milliliter', 'ML', 5, 0, 1, GETUTCDATE()),
(N'علبة', N'Box', 'BOX', 1, 0, 1, GETUTCDATE()),
(N'كرتون', N'Carton', 'CTN', 1, 0, 1, GETUTCDATE()),
(N'دزينة', N'Dozen', 'DOZ', 1, 0, 1, GETUTCDATE());

-- حذف وإعادة إدراج فئات الأسعار
TRUNCATE TABLE PriceCategories;

INSERT INTO PriceCategories (NameAr, NameEn, Code, IsDefault, IsActive, PriceAdjustmentPercentage, CreatedAt) VALUES
(N'سعر التجزئة', N'Retail Price', 'RETAIL', 1, 1, 0, GETUTCDATE()),
(N'سعر الجملة', N'Wholesale Price', 'WHOLESALE', 0, 1, -10, GETUTCDATE()),
(N'سعر VIP', N'VIP Price', 'VIP', 0, 1, -15, GETUTCDATE()),
(N'سعر المؤسسات', N'Corporate Price', 'CORP', 0, 1, -8, GETUTCDATE()),
(N'سعر الموظفين', N'Employee Price', 'EMP', 0, 1, -20, GETUTCDATE());

-- حذف وإعادة إدراج فئات المنتجات
TRUNCATE TABLE Categories;

INSERT INTO Categories (NameAr, NameEn, Code, Level, IsActive, CreatedAt) VALUES
(N'عام', N'General', 'GEN', 1, 1, GETUTCDATE()),
(N'أغذية ومشروبات', N'Food & Beverages', 'FOOD', 1, 1, GETUTCDATE()),
(N'إلكترونيات', N'Electronics', 'ELEC', 1, 1, GETUTCDATE()),
(N'ملابس وأزياء', N'Clothing & Fashion', 'CLOTH', 1, 1, GETUTCDATE()),
(N'منزل وحديقة', N'Home & Garden', 'HOME', 1, 1, GETUTCDATE()),
(N'صحة وجمال', N'Health & Beauty', 'HEALTH', 1, 1, GETUTCDATE()),
(N'رياضة وترفيه', N'Sports & Recreation', 'SPORT', 1, 1, GETUTCDATE()),
(N'كتب وقرطاسية', N'Books & Stationery', 'BOOKS', 1, 1, GETUTCDATE()),
(N'ألعاب وهدايا', N'Toys & Gifts', 'TOYS', 1, 1, GETUTCDATE()),
(N'سيارات وقطع غيار', N'Automotive & Parts', 'AUTO', 1, 1, GETUTCDATE());

-- تحديث الفروع
UPDATE Branches SET 
    NameAr = N'الفرع الرئيسي', 
    NameEn = N'Main Branch'
WHERE Code = 'MAIN';

-- حذف وإعادة إدراج طرق الدفع
DELETE FROM PaymentMethods;

INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsDefault, IsActive, DisplayOrder, CreatedAt) VALUES
(N'نقدي', N'Cash', 'CASH', 1, 1, 1, 1, GETUTCDATE()),
(N'بطاقة ائتمان', N'Credit Card', 'CREDIT', 2, 0, 1, 2, GETUTCDATE()),
(N'بطاقة مدى', N'Debit Card', 'DEBIT', 2, 0, 1, 3, GETUTCDATE()),
(N'تحويل بنكي', N'Bank Transfer', 'TRANSFER', 3, 0, 1, 4, GETUTCDATE()),
(N'آجل', N'Credit Term', 'TERM', 4, 0, 1, 5, GETUTCDATE()),
(N'شيك', N'Check', 'CHECK', 5, 0, 1, 6, GETUTCDATE()),
(N'محفظة إلكترونية', N'E-Wallet', 'EWALLET', 2, 0, 1, 7, GETUTCDATE());

-- إضافة أنواع الموردين
IF NOT EXISTS (SELECT * FROM SupplierTypes WHERE Id = 1)
BEGIN
    INSERT INTO SupplierTypes (NameAr, NameEn, DefaultPaymentTerms, IsActive, CreatedAt) VALUES
    (N'مورد محلي', N'Local Supplier', 30, 1, GETUTCDATE()),
    (N'مورد دولي', N'International Supplier', 60, 1, GETUTCDATE()),
    (N'مورد حكومي', N'Government Supplier', 45, 1, GETUTCDATE()),
    (N'مورد خاص', N'Private Supplier', 30, 1, GETUTCDATE()),
    (N'مورد معتمد', N'Certified Supplier', 15, 1, GETUTCDATE());
END

-- تحديث العدادات
UPDATE Counters SET Description = N'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = N'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = N'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = N'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = N'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = N'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

PRINT N'تم الانتهاء من الإصلاح النهائي للترميز العربي!';

-- عرض النتائج النهائية
SELECT N'أنواع العملاء' as TableName, COUNT(*) as RecordCount FROM CustomerTypes
UNION ALL
SELECT N'وحدات القياس', COUNT(*) FROM Units
UNION ALL
SELECT N'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT N'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT N'طرق الدفع', COUNT(*) FROM PaymentMethods
UNION ALL
SELECT N'أنواع الموردين', COUNT(*) FROM SupplierTypes;

-- عرض عينة من البيانات
PRINT N'';
PRINT N'عينة من أنواع العملاء:';
SELECT TOP 3 Id, NameAr, NameEn FROM CustomerTypes ORDER BY Id;

PRINT N'';
PRINT N'عينة من وحدات القياس:';
SELECT TOP 3 Id, NameAr, NameEn, Symbol FROM Units ORDER BY Id;
