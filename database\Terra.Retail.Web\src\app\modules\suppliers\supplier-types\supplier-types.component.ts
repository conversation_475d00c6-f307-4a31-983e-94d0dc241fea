import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';

// Interfaces
interface SupplierType {
  Id: number;
  NameAr: string;
  NameEn: string;
  Description?: string;
  IsActive: boolean;
  CreatedAt?: string;
  UpdatedAt?: string;
}

@Component({
  selector: 'app-supplier-types',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCheckboxModule
  ],
  templateUrl: './supplier-types.component.html',
  styleUrls: ['./supplier-types.component.scss']
})
export class SupplierTypesComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  showAddForm = false;
  editingType: SupplierType | null = null;
  
  // Data
  supplierTypes: SupplierType[] = [];
  
  // Form
  supplierTypeForm: FormGroup;
  
  // Table Configuration
  displayedColumns: string[] = ['nameAr', 'nameEn', 'description', 'isActive', 'actions'];
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private router: Router,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.supplierTypeForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadSupplierTypes();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required, Validators.minLength(2)]],
      description: [''],
      isActive: [true]
    });
  }

  /**
   * Load supplier types
   */
  loadSupplierTypes(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({
      next: (response) => {
        console.log('Supplier types response:', response);
        this.supplierTypes = response.supplierTypes || [];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading supplier types:', error);
        this.showError('خطأ في تحميل أنواع الموردين');
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Show add form
   */
  showAddSupplierType(): void {
    this.showAddForm = true;
    this.editingType = null;
    this.supplierTypeForm.reset();
    this.supplierTypeForm.patchValue({ isActive: true });
  }

  /**
   * Edit supplier type
   */
  editSupplierType(supplierType: SupplierType): void {
    this.showAddForm = true;
    this.editingType = supplierType;
    this.supplierTypeForm.patchValue({
      nameAr: supplierType.NameAr,
      nameEn: supplierType.NameEn,
      description: supplierType.Description || '',
      isActive: supplierType.IsActive
    });
  }

  /**
   * Save supplier type
   */
  saveSupplierType(): void {
    if (this.supplierTypeForm.valid) {
      this.isLoading = true;
      
      const formValue = this.supplierTypeForm.value;
      const request = {
        nameAr: formValue.nameAr,
        nameEn: formValue.nameEn,
        description: formValue.description || null,
        isActive: formValue.isActive
      };

      const apiCall = this.editingType 
        ? this.http.put<any>(`http://localhost:5127/api/simple/supplier-types/${this.editingType.Id}`, request)
        : this.http.post<any>('http://localhost:5127/api/simple/supplier-types', request);

      const sub = apiCall.subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccess(this.editingType ? 'تم تحديث نوع المورد بنجاح' : 'تم إضافة نوع المورد بنجاح');
          this.showAddForm = false;
          this.loadSupplierTypes();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error saving supplier type:', error);
          this.showError('خطأ في حفظ نوع المورد');
        }
      });
      this.subscriptions.push(sub);
    } else {
      this.markFormGroupTouched();
      this.showError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  /**
   * Delete supplier type
   */
  deleteSupplierType(supplierType: SupplierType): void {
    if (confirm(`هل أنت متأكد من حذف نوع المورد "${supplierType.NameAr}"؟`)) {
      this.isLoading = true;

      const sub = this.http.delete(`http://localhost:5127/api/simple/supplier-types/${supplierType.Id}`).subscribe({
        next: (response: any) => {
          this.isLoading = false;
          this.showSuccess('تم حذف نوع المورد بنجاح');
          this.loadSupplierTypes();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error deleting supplier type:', error);
          this.showError('خطأ في حذف نوع المورد');
        }
      });
      this.subscriptions.push(sub);
    }
  }

  /**
   * Cancel form
   */
  cancelForm(): void {
    this.showAddForm = false;
    this.editingType = null;
    this.supplierTypeForm.reset();
  }

  /**
   * Go back to suppliers
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.supplierTypeForm.controls).forEach(key => {
      const control = this.supplierTypeForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
