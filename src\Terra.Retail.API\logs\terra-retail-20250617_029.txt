2025-06-17 07:25:12.738 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.810 +03:00 [WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.878 +03:00 [WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.895 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.900 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.912 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.917 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.926 +03:00 [WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.931 +03:00 [WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.940 +03:00 [WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.945 +03:00 [WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.950 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.959 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.965 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.978 +03:00 [WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.984 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.994 +03:00 [WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:12.999 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.013 +03:00 [WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.025 +03:00 [WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.060 +03:00 [WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.097 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.109 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.115 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.126 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.145 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.160 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.168 +03:00 [WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.178 +03:00 [WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.184 +03:00 [WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.197 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.212 +03:00 [WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.224 +03:00 [WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.261 +03:00 [WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.348 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.358 +03:00 [WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.364 +03:00 [WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.376 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.381 +03:00 [WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.393 +03:00 [WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.399 +03:00 [WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.409 +03:00 [WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.414 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.423 +03:00 [WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.430 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.443 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.460 +03:00 [WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.468 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.480 +03:00 [WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.492 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.511 +03:00 [WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.519 +03:00 [WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.567 +03:00 [WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.583 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.679 +03:00 [WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.707 +03:00 [WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.759 +03:00 [WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.794 +03:00 [WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.810 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.829 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.834 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.843 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.847 +03:00 [WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.857 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.866 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.913 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:13.931 +03:00 [WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.695 +03:00 [WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.780 +03:00 [WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.787 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.800 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.810 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.815 +03:00 [WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.831 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.845 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.859 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.866 +03:00 [WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.893 +03:00 [WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.900 +03:00 [WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:14.919 +03:00 [WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.012 +03:00 [WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.095 +03:00 [WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.130 +03:00 [WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.146 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.153 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.201 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.215 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.227 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.248 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.268 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.282 +03:00 [WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.316 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.328 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.334 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.344 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.349 +03:00 [WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.360 +03:00 [WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.366 +03:00 [WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.370 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.384 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.395 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.400 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.412 +03:00 [WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.418 +03:00 [WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.433 +03:00 [WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.452 +03:00 [WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.467 +03:00 [WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:15.480 +03:00 [WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 07:25:16.453 +03:00 [INF] تم إنشاء قاعدة البيانات بنجاح
2025-06-17 07:25:16.598 +03:00 [INF] تم بدء تشغيل Terra Retail ERP API
2025-06-17 07:26:56.383 +03:00 [WRN] Failed to determine the https port for redirect.
2025-06-17 07:26:56.844 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:26:56.844 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:26:56.844 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:27:15.676 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:27:15.805 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:27:15.805 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:27:37.808 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:27:37.812 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:27:37.823 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:27:54.832 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:27:54.845 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:27:54.859 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:28:40.403 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:28:40.409 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:28:40.426 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:28:59.393 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:28:59.398 +03:00 [INF] تم استرجاع 26 محافظة
2025-06-17 07:28:59.398 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:29:14.797 +03:00 [INF] تم استرجاع 24 عميل
2025-06-17 07:29:14.804 +03:00 [INF] تم استرجاع 5 نوع عميل
2025-06-17 07:29:14.809 +03:00 [INF] تم استرجاع 26 محافظة
[2025-06-17 16:05:32.561 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.743 +03:00 WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.753 +03:00 WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.767 +03:00 WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.781 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.795 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.801 +03:00 WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.812 +03:00 WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.818 +03:00 WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.828 +03:00 WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.833 +03:00 WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:32.839 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.499 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.517 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.527 +03:00 WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.532 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.536 +03:00 WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.547 +03:00 WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.564 +03:00 WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.582 +03:00 WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.596 +03:00 WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.603 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.613 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.618 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.626 +03:00 WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.631 +03:00 WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.635 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.643 +03:00 WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.648 +03:00 WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.652 +03:00 WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.661 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.665 +03:00 WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.671 +03:00 WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.683 +03:00 WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.695 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.717 +03:00 WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:33.799 +03:00 WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.004 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.030 +03:00 WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.038 +03:00 WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.050 +03:00 WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.061 +03:00 WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.071 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.085 +03:00 WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.094 +03:00 WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.100 +03:00 WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.109 +03:00 WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.115 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.127 +03:00 WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.134 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.145 +03:00 WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.152 +03:00 WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.166 +03:00 WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.176 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.183 +03:00 WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.194 +03:00 WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.200 +03:00 WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.209 +03:00 WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.221 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.232 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.238 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.247 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.252 +03:00 WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.260 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.266 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.271 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.279 +03:00 WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.284 +03:00 WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.292 +03:00 WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.298 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.303 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.312 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.317 +03:00 WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.321 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.350 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.443 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.450 +03:00 WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.459 +03:00 WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.465 +03:00 WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.474 +03:00 WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.482 +03:00 WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.495 +03:00 WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.500 +03:00 WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.505 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.513 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.518 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.525 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.530 +03:00 WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.535 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.545 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.552 +03:00 WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.564 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.569 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.579 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.584 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.597 +03:00 WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.602 +03:00 WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.611 +03:00 WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.618 +03:00 WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.627 +03:00 WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.633 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.638 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.647 +03:00 WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.781 +03:00 WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.797 +03:00 WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.803 +03:00 WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.815 +03:00 WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:34.834 +03:00 WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 16:05:36.720 +03:00 INF] تم إنشاء قاعدة البيانات بنجاح {}
[2025-06-17 16:05:36.822 +03:00 INF] تم بدء تشغيل Terra Retail ERP API {}
[2025-06-17 16:05:39.433 +03:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNDDJUK5K1AO:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1AO"}
[2025-06-17 16:05:40.070 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AP:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1AP"}
[2025-06-17 16:05:40.070 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AO:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1AO"}
[2025-06-17 16:05:40.070 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AQ:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1AQ"}
[2025-06-17 16:07:10.917 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AO:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1AO"}
[2025-06-17 16:07:10.919 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AQ:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1AQ"}
[2025-06-17 16:07:10.951 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AP:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1AP"}
[2025-06-17 16:11:52.203 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AT:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1AT"}
[2025-06-17 16:11:52.206 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AS:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1AS"}
[2025-06-17 16:11:52.225 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AU:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1AU"}
[2025-06-17 16:12:09.340 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AS:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1AS"}
[2025-06-17 16:12:09.346 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AU:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1AU"}
[2025-06-17 16:12:09.351 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1AT:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1AT"}
[2025-06-17 16:12:32.543 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:12:32.587 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:12:32.595 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B4:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B4"}
[2025-06-17 16:12:35.428 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B5:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B5"}
[2025-06-17 16:12:35.430 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B1:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B1"}
[2025-06-17 16:12:35.430 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B2:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B2"}
[2025-06-17 16:12:49.291 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:12:49.292 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B6:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B6"}
[2025-06-17 16:12:49.294 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B4:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B4"}
[2025-06-17 16:13:08.673 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B4:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B4"}
[2025-06-17 16:13:08.681 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B6:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B6"}
[2025-06-17 16:13:08.692 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:13:26.312 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B6:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B6"}
[2025-06-17 16:13:26.316 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000005","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:13:26.318 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B4:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B4"}
[2025-06-17 16:13:44.746 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B3:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B3"}
[2025-06-17 16:13:44.746 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B4:00000005","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B4"}
[2025-06-17 16:13:44.765 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B6:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B6"}
[2025-06-17 16:14:39.157 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B8:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1B8"}
[2025-06-17 16:14:39.157 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B9:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1B9"}
[2025-06-17 16:14:39.157 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1B7:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1B7"}
[2025-06-17 16:14:58.786 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BB:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BB"}
[2025-06-17 16:14:58.789 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BA:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BA"}
[2025-06-17 16:14:58.791 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BC:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BC"}
[2025-06-17 16:15:14.892 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BE:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BE"}
[2025-06-17 16:15:14.894 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BF:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BF"}
[2025-06-17 16:15:14.895 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BD:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BD"}
[2025-06-17 16:15:35.977 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BH:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BH"}
[2025-06-17 16:15:35.977 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BG:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BG"}
[2025-06-17 16:15:35.978 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BI:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BI"}
[2025-06-17 16:15:37.758 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BF:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BF"}
[2025-06-17 16:15:37.759 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BD:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BD"}
[2025-06-17 16:15:37.776 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BE:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BE"}
[2025-06-17 16:17:10.458 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BL:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BL"}
[2025-06-17 16:17:10.471 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BK:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BK"}
[2025-06-17 16:17:10.471 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:17:13.640 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:17:13.656 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:17:13.661 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:17:32.525 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:17:32.525 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:17:32.526 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:17:52.696 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:17:52.698 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:17:52.704 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:18:10.468 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:18:10.469 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:18:10.472 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:18:30.227 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:18:30.227 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:18:30.232 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:18:47.069 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000007","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:18:47.070 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:18:47.072 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000005","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:18:52.133 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BK:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BK"}
[2025-06-17 16:18:52.134 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:18:52.138 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BL:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BL"}
[2025-06-17 16:19:07.238 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000008","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:19:07.238 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000007","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:19:07.241 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000006","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:19:25.714 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000008","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:19:25.715 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000009","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:19:25.716 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:19:47.020 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000009","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:19:47.021 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000A","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:19:47.022 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000008","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:20:04.751 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:00000009","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:20:04.755 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000B","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:20:04.771 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000A","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:20:23.606 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000B","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:20:23.618 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:0000000A","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:20:23.625 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:20:38.703 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:0000000B","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:20:38.705 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000D","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:20:38.707 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:21:11.247 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000D","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:21:11.269 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000E","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:21:11.281 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000E","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:21:37.264 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:0000000F","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:21:37.264 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:21:37.266 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:0000000F","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:21:55.051 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:0000000D","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:21:55.051 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000010","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:21:55.051 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000010","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:22:15.090 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:0000000A","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:22:15.090 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BK:0000000A","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BK"}
[2025-06-17 16:22:15.091 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BP:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BP"}
[2025-06-17 16:22:22.894 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BO:0000000E","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BO"}
[2025-06-17 16:22:22.895 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BM:00000011","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BM"}
[2025-06-17 16:22:22.897 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BN:00000011","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BN"}
[2025-06-17 16:24:12.897 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:0000000D","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:24:12.900 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BK:0000000B","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BK"}
[2025-06-17 16:24:12.903 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BP:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BP"}
[2025-06-17 16:25:09.364 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:25:09.365 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:25:09.367 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:25:09.376 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:0000000F","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:25:09.383 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BP:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BP"}
[2025-06-17 16:25:09.383 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BK:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BK"}
[2025-06-17 16:25:29.282 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:25:29.282 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:25:29.282 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:25:52.053 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:25:52.055 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:25:52.058 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:26:13.400 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:26:13.400 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:26:13.405 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:26:34.107 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000005","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:26:34.108 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000005","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:26:34.108 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:26:58.218 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:26:58.218 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000006","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:26:58.223 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:27:19.298 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000007","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:27:19.298 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000007","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:27:19.321 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:27:42.206 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BQ:00000008","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BQ"}
[2025-06-17 16:27:42.206 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BS:00000008","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BS"}
[2025-06-17 16:27:42.212 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BR:00000008","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BR"}
[2025-06-17 16:27:42.349 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:00000016","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:27:42.354 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BP:0000000C","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BP"}
[2025-06-17 16:27:42.356 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BT:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BT"}
[2025-06-17 16:28:00.333 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BP:0000000D","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BP"}
[2025-06-17 16:28:00.339 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BT:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BT"}
[2025-06-17 16:28:00.341 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BJ:00000017","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BJ"}
[2025-06-17 16:30:23.094 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:30:23.095 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:30:23.096 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:30:23.176 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:30:23.183 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:30:23.184 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:30:23.417 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:30:23.417 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:30:23.418 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:30:46.401 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:30:46.408 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:30:46.408 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:30:46.419 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:30:46.444 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:30:46.444 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:30:46.516 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:30:46.522 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000002","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:30:46.522 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:30:58.517 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000005","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:30:58.517 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:30:58.517 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:31:06.347 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:06.347 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000006","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:31:06.348 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:31:07.372 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:07.372 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C1:00000007","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C1"}
[2025-06-17 16:31:07.376 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C3:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C3"}
[2025-06-17 16:31:07.396 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:31:07.396 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:07.397 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:07.442 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:31:07.444 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:31:07.444 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:31:32.196 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:32.224 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000003","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:32.225 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:32.368 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:32.368 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:32.414 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000004","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:31:32.414 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:31:32.430 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000004","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:31:32.448 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000008","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:32.462 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:31:32.462 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000005","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:32.506 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:53.571 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:31:53.571 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:00000009","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:53.571 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:53.636 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:0000000A","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:53.636 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000008","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:53.640 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000007","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:31:53.694 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:00000009","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:31:53.695 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000008","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:31:53.696 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:0000000B","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:31:53.912 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000005","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:31:53.919 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000005","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:31:53.920 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:32:14.013 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:0000000A","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:32:14.013 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:00000009","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:32:14.025 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:0000000C","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:32:14.100 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BV:00000006","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1BV"}
[2025-06-17 16:32:14.100 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C0:00000006","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C0"}
[2025-06-17 16:32:14.100 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1BU:00000006","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1BU"}
[2025-06-17 16:32:14.163 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:0000000B","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:32:14.163 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:0000000D","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:32:14.163 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:0000000A","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:32:14.247 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C4:0000000E","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C4"}
[2025-06-17 16:32:14.255 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C2:0000000B","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C2"}
[2025-06-17 16:32:14.261 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C5:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C5"}
[2025-06-17 16:42:43.219 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"4e87729a-0d3b-4fc8-abd4-0c6c67456f76","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C8:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDDJUK5K1C8"}
[2025-06-17 16:42:43.229 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"0ab85c4c-8e3d-4a4a-81f7-a53ca7d445df","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C6:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDDJUK5K1C6"}
[2025-06-17 16:42:43.236 +03:00 INF] تم استرجاع 5 نوع عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomerTypesController","ActionId":"56615218-baac-4b42-a9e4-edeffe655ebc","ActionName":"Terra.Retail.API.Controllers.CustomerTypesController.GetCustomerTypes (Terra.Retail.API)","RequestId":"0HNDDJUK5K1C7:00000001","RequestPath":"/api/customertypes","ConnectionId":"0HNDDJUK5K1C7"}
