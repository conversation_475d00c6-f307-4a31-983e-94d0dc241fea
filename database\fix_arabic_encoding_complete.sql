-- إصلاح الترميز العربي الشامل لجميع الجداول
-- Complete Arabic Encoding Fix for All Tables

USE TerraRetailERP;
GO

-- تعيين الترميز الصحيح
SET NOCOUNT ON;
GO

PRINT 'بدء إصلاح الترميز العربي الشامل...';

-- حذف وإعادة إنشاء المحافظات المصرية بترميز صحيح
DELETE FROM Areas;
DBCC CHECKIDENT ('Areas', RESEED, 0);

INSERT INTO Areas (NameAr, NameEn, Code, IsActive, DisplayOrder, CreatedAt) VALUES
('القاهرة', 'Cairo', 'CAI', 1, 1, GETUTCDATE()),
('الجيزة', 'Giza', 'GIZ', 1, 2, GETUTCDATE()),
('الإسكندرية', 'Alexandria', 'ALX', 1, 3, GETUTCDATE()),
('الدقهلية', 'Dakahlia', 'DAK', 1, 4, GETUTCDATE()),
('الشرقية', 'Sharqia', 'SHR', 1, 5, GETUTCDATE()),
('القليوبية', 'Qalyubia', 'QLY', 1, 6, GETUTCDATE()),
('كفر الشيخ', 'Kafr El Sheikh', 'KFS', 1, 7, GETUTCDATE()),
('الغربية', 'Gharbia', 'GHR', 1, 8, GETUTCDATE()),
('المنوفية', 'Monufia', 'MNF', 1, 9, GETUTCDATE()),
('البحيرة', 'Beheira', 'BHR', 1, 10, GETUTCDATE()),
('الإسماعيلية', 'Ismailia', 'ISM', 1, 11, GETUTCDATE()),
('بورسعيد', 'Port Said', 'PTS', 1, 12, GETUTCDATE()),
('السويس', 'Suez', 'SUZ', 1, 13, GETUTCDATE()),
('شمال سيناء', 'North Sinai', 'NSI', 1, 14, GETUTCDATE()),
('جنوب سيناء', 'South Sinai', 'SSI', 1, 15, GETUTCDATE()),
('الفيوم', 'Fayoum', 'FYM', 1, 16, GETUTCDATE()),
('بني سويف', 'Beni Suef', 'BSF', 1, 17, GETUTCDATE()),
('المنيا', 'Minya', 'MNY', 1, 18, GETUTCDATE()),
('أسيوط', 'Assiut', 'AST', 1, 19, GETUTCDATE()),
('سوهاج', 'Sohag', 'SOH', 1, 20, GETUTCDATE()),
('قنا', 'Qena', 'QNA', 1, 21, GETUTCDATE()),
('الأقصر', 'Luxor', 'LXR', 1, 22, GETUTCDATE()),
('أسوان', 'Aswan', 'ASN', 1, 23, GETUTCDATE()),
('البحر الأحمر', 'Red Sea', 'RDS', 1, 24, GETUTCDATE()),
('الوادي الجديد', 'New Valley', 'NVL', 1, 25, GETUTCDATE()),
('مطروح', 'Matrouh', 'MTR', 1, 26, GETUTCDATE());

PRINT 'تم إصلاح المحافظات المصرية';

-- إصلاح الفروع المصرية
UPDATE Branches SET NameAr = 'الفرع الرئيسي - القاهرة' WHERE Id = 1;
UPDATE Branches SET NameAr = 'فرع الإسكندرية' WHERE Id = 13;
UPDATE Branches SET NameAr = 'فرع الجيزة' WHERE Id = 14;
UPDATE Branches SET NameAr = 'فرع المنصورة' WHERE Id = 15;
UPDATE Branches SET NameAr = 'فرع أسيوط' WHERE Id = 16;
UPDATE Branches SET NameAr = 'فرع طنطا' WHERE Id = 17;
UPDATE Branches SET NameAr = 'فرع الزقازيق' WHERE Id = 18;
UPDATE Branches SET NameAr = 'فرع أسوان' WHERE Id = 19;

PRINT 'تم إصلاح أسماء الفروع';

-- إصلاح أنواع العملاء
UPDATE CustomerTypes SET NameAr = 'عميل عادي' WHERE Id = 1;
UPDATE CustomerTypes SET NameAr = 'عميل جملة' WHERE Id = 2;
UPDATE CustomerTypes SET NameAr = 'عميل VIP' WHERE Id = 3;
UPDATE CustomerTypes SET NameAr = 'عميل تاجر' WHERE Id = 4;
UPDATE CustomerTypes SET NameAr = 'عميل مؤسسة' WHERE Id = 5;

PRINT 'تم إصلاح أنواع العملاء';

-- إصلاح وحدات القياس
UPDATE Units SET NameAr = 'حبة' WHERE Id = 1;
UPDATE Units SET NameAr = 'كيلو' WHERE Id = 2;
UPDATE Units SET NameAr = 'جرام' WHERE Id = 3;
UPDATE Units SET NameAr = 'متر' WHERE Id = 4;
UPDATE Units SET NameAr = 'سم' WHERE Id = 5;
UPDATE Units SET NameAr = 'لتر' WHERE Id = 6;
UPDATE Units SET NameAr = 'مل' WHERE Id = 7;
UPDATE Units SET NameAr = 'علبة' WHERE Id = 8;
UPDATE Units SET NameAr = 'كرتونة' WHERE Id = 9;
UPDATE Units SET NameAr = 'دستة' WHERE Id = 10;

PRINT 'تم إصلاح وحدات القياس';

-- إصلاح فئات الأسعار
UPDATE PriceCategories SET NameAr = 'سعر التجزئة' WHERE Id = 1;
UPDATE PriceCategories SET NameAr = 'سعر الجملة' WHERE Id = 2;
UPDATE PriceCategories SET NameAr = 'سعر VIP' WHERE Id = 3;
UPDATE PriceCategories SET NameAr = 'سعر التجار' WHERE Id = 4;
UPDATE PriceCategories SET NameAr = 'سعر الموظفين' WHERE Id = 5;

PRINT 'تم إصلاح فئات الأسعار';

-- إصلاح فئات المنتجات
UPDATE Categories SET NameAr = 'عام' WHERE Id = 1;
UPDATE Categories SET NameAr = 'مواد غذائية ومشروبات' WHERE Id = 2;
UPDATE Categories SET NameAr = 'أجهزة كهربائية' WHERE Id = 3;
UPDATE Categories SET NameAr = 'ملابس وأحذية' WHERE Id = 4;
UPDATE Categories SET NameAr = 'أدوات منزلية' WHERE Id = 5;
UPDATE Categories SET NameAr = 'مستحضرات تجميل وعناية' WHERE Id = 6;
UPDATE Categories SET NameAr = 'رياضة ولياقة' WHERE Id = 7;
UPDATE Categories SET NameAr = 'مكتبة وقرطاسية' WHERE Id = 8;
UPDATE Categories SET NameAr = 'ألعاب وهدايا' WHERE Id = 9;
UPDATE Categories SET NameAr = 'قطع غيار وإكسسوارات' WHERE Id = 10;

PRINT 'تم إصلاح فئات المنتجات';

-- إصلاح طرق الدفع المصرية
UPDATE PaymentMethods SET NameAr = 'كاش' WHERE Code = 'CASH';
UPDATE PaymentMethods SET NameAr = 'فيزا' WHERE Code = 'CREDIT';
UPDATE PaymentMethods SET NameAr = 'آجل' WHERE Code = 'TERM';

-- إضافة طرق دفع مصرية جديدة إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM PaymentMethods WHERE Code = 'MEEZA')
BEGIN
    INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsActive, DisplayOrder, CreatedAt) VALUES
    ('ميزة', 'Meeza Card', 'MEEZA', 2, 1, 4, GETUTCDATE()),
    ('فودافون كاش', 'Vodafone Cash', 'VFCASH', 2, 1, 5, GETUTCDATE()),
    ('أورانج موني', 'Orange Money', 'ORANGE', 2, 1, 6, GETUTCDATE()),
    ('إتصالات فلوس', 'Etisalat Flous', 'ETISALAT', 2, 1, 7, GETUTCDATE()),
    ('فوري', 'Fawry', 'FAWRY', 2, 1, 8, GETUTCDATE()),
    ('تحويل بنكي', 'Bank Transfer', 'TRANSFER', 3, 1, 9, GETUTCDATE()),
    ('شيك', 'Check', 'CHECK', 5, 1, 10, GETUTCDATE());
END

PRINT 'تم إصلاح طرق الدفع المصرية';

-- إصلاح أنواع الموردين
UPDATE SupplierTypes SET NameAr = 'مورد محلي' WHERE Id = 1;
UPDATE SupplierTypes SET NameAr = 'مورد مستورد' WHERE Id = 2;
UPDATE SupplierTypes SET NameAr = 'مورد حكومي' WHERE Id = 3;

PRINT 'تم إصلاح أنواع الموردين';

-- إصلاح العدادات
UPDATE Counters SET Description = 'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = 'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = 'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = 'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = 'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = 'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

PRINT 'تم إصلاح العدادات';

-- إصلاح الأدوار
UPDATE Roles SET NameAr = 'مدير النظام' WHERE Code = 'SUPER_ADMIN';
UPDATE Roles SET NameAr = 'مدير عام' WHERE Code = 'GENERAL_MANAGER';
UPDATE Roles SET NameAr = 'مدير فرع' WHERE Code = 'BRANCH_MANAGER';
UPDATE Roles SET NameAr = 'محاسب' WHERE Code = 'ACCOUNTANT';
UPDATE Roles SET NameAr = 'كاشير' WHERE Code = 'CASHIER';
UPDATE Roles SET NameAr = 'مخزني' WHERE Code = 'INVENTORY_CLERK';
UPDATE Roles SET NameAr = 'مندوب مبيعات' WHERE Code = 'SALES_REP';
UPDATE Roles SET NameAr = 'مستخدم عادي' WHERE Code = 'USER';

PRINT 'تم إصلاح الأدوار';

PRINT 'تم الانتهاء من إصلاح الترميز العربي الشامل!';

-- عرض النتائج النهائية
SELECT 'المحافظات المصرية' as TableName, COUNT(*) as RecordCount FROM Areas
UNION ALL
SELECT 'الفروع المصرية', COUNT(*) FROM Branches
UNION ALL
SELECT 'أنواع العملاء', COUNT(*) FROM CustomerTypes
UNION ALL
SELECT 'وحدات القياس', COUNT(*) FROM Units
UNION ALL
SELECT 'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT 'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT 'طرق الدفع', COUNT(*) FROM PaymentMethods
UNION ALL
SELECT 'أنواع الموردين', COUNT(*) FROM SupplierTypes
UNION ALL
SELECT 'الأدوار', COUNT(*) FROM Roles;
