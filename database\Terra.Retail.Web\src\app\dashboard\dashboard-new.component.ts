import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { interval, Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';

@Component({
  selector: 'app-dashboard-new',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatBadgeModule
  ],
  templateUrl: './dashboard-new.component.html',
  styleUrls: ['./dashboard-new.component.scss']
})
export class DashboardNewComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('salesChart', { static: false }) salesChartRef!: ElementRef<HTMLCanvasElement>;

  // Component State
  isLoading = true;
  currentDate = new Date();
  currentTime = new Date();

  // Subscriptions
  private timeSubscription?: Subscription;
  private dataSubscription?: Subscription;

  // Dashboard Data
  stats = {
    todaySales: 0,
    todayOrders: 0,
    customers: 0,
    products: 0
  };

  systemStatus = {
    overall: 'healthy',
    api: 'connected',
    database: 'connected',
    cache: 'connected'
  };

  recentOrders: any[] = [];
  topProducts: any[] = [];
  notifications: any[] = [];

  // Chart instance
  private salesChart: any;

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.loadDashboardData();
    this.startTimeUpdater();
    this.setupDataRefresh();
  }

  ngAfterViewInit(): void {
    // Initialize charts after view is ready
    setTimeout(() => {
      this.initializeSalesChart();
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.timeSubscription?.unsubscribe();
    this.dataSubscription?.unsubscribe();
    
    // Destroy chart
    if (this.salesChart) {
      this.salesChart.destroy();
    }
  }

  /**
   * Initialize component with default data
   */
  private initializeComponent(): void {
    // Set initial mock data
    this.stats = {
      todaySales: 25750,
      todayOrders: 156,
      customers: 24,
      products: 5
    };

    this.recentOrders = [
      {
        id: '2024001',
        customerName: 'أحمد محمد علي',
        total: 1250,
        status: 'completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago
      },
      {
        id: '2024002',
        customerName: 'فاطمة أحمد',
        total: 850,
        status: 'pending',
        createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        id: '2024003',
        customerName: 'محمد حسن',
        total: 2100,
        status: 'completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 45) // 45 minutes ago
      }
    ];

    this.topProducts = [
      {
        name: 'لابتوب Dell Inspiron',
        category: 'أجهزة كمبيوتر',
        salesCount: 45,
        salesAmount: 67500,
        image: 'assets/images/product-placeholder.svg'
      },
      {
        name: 'هاتف Samsung Galaxy',
        category: 'هواتف ذكية',
        salesCount: 38,
        salesAmount: 45600,
        image: 'assets/images/product-placeholder.svg'
      },
      {
        name: 'سماعات Sony',
        category: 'إكسسوارات',
        salesCount: 62,
        salesAmount: 18600,
        image: 'assets/images/product-placeholder.svg'
      }
    ];

    this.notifications = [
      {
        type: 'warning',
        title: 'نفاد مخزون',
        message: 'المنتج "لابتوب Dell" أوشك على النفاد',
        createdAt: new Date(Date.now() - 1000 * 60 * 10)
      },
      {
        type: 'info',
        title: 'طلب جديد',
        message: 'تم استلام طلب جديد من العميل أحمد محمد',
        createdAt: new Date(Date.now() - 1000 * 60 * 20)
      },
      {
        type: 'success',
        title: 'تم الدفع',
        message: 'تم استلام دفعة بقيمة 5000 جنيه',
        createdAt: new Date(Date.now() - 1000 * 60 * 35)
      }
    ];
  }

  /**
   * Load dashboard data from API
   */
  private loadDashboardData(): void {
    this.isLoading = true;

    // Load statistics
    this.http.get<any>('http://localhost:5127/api/simple/statistics').subscribe({
      next: (response) => {
        this.stats.customers = response.customersCount || this.stats.customers;
        this.stats.products = response.productsCount || this.stats.products;
        console.log('Statistics loaded:', response);
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
      }
    });

    // Check system status
    this.http.get('http://localhost:5127/health').subscribe({
      next: (response: any) => {
        this.systemStatus.api = 'connected';
        this.systemStatus.database = response.Database?.Connected ? 'connected' : 'disconnected';
        this.systemStatus.overall = this.calculateOverallStatus();
      },
      error: () => {
        this.systemStatus.api = 'disconnected';
        this.systemStatus.database = 'disconnected';
        this.systemStatus.overall = 'error';
      }
    });

    // Simulate loading delay
    setTimeout(() => {
      this.isLoading = false;
    }, 1500);
  }

  /**
   * Start time updater
   */
  private startTimeUpdater(): void {
    this.timeSubscription = interval(1000).subscribe(() => {
      this.currentTime = new Date();
      
      // Update date at midnight
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0 && now.getSeconds() === 0) {
        this.currentDate = now;
      }
    });
  }

  /**
   * Setup automatic data refresh
   */
  private setupDataRefresh(): void {
    // Refresh data every 5 minutes
    this.dataSubscription = interval(5 * 60 * 1000).subscribe(() => {
      this.loadDashboardData();
    });
  }

  /**
   * Initialize sales chart
   */
  private initializeSalesChart(): void {
    if (!this.salesChartRef?.nativeElement) {
      return;
    }

    const ctx = this.salesChartRef.nativeElement.getContext('2d');
    if (!ctx) {
      return;
    }

    // Mock chart data
    const chartData = {
      labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
      datasets: [{
        label: 'المبيعات',
        data: [12000, 19000, 15000, 25000, 22000, 30000],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      }]
    };

    // Note: You would need to install Chart.js for this to work
    // For now, we'll just log that the chart would be initialized
    console.log('Sales chart would be initialized with data:', chartData);
  }

  /**
   * Calculate overall system status
   */
  private calculateOverallStatus(): string {
    const statuses = [this.systemStatus.api, this.systemStatus.database, this.systemStatus.cache];
    
    if (statuses.every(status => status === 'connected')) {
      return 'healthy';
    } else if (statuses.some(status => status === 'disconnected')) {
      return 'error';
    } else {
      return 'warning';
    }
  }

  /**
   * Get order status text
   */
  getOrderStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'pending': 'قيد الانتظار',
      'completed': 'مكتمل',
      'cancelled': 'ملغي',
      'processing': 'قيد المعالجة'
    };
    
    return statusMap[status] || status;
  }

  /**
   * Get system status text
   */
  getSystemStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'healthy': 'سليم',
      'warning': 'تحذير',
      'error': 'خطأ'
    };
    
    return statusMap[status] || status;
  }

  /**
   * Get status text
   */
  getStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'connected': 'متصل',
      'disconnected': 'غير متصل',
      'warning': 'تحذير'
    };
    
    return statusMap[status] || status;
  }

  /**
   * Get notification icon
   */
  getNotificationIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'info': 'info',
      'warning': 'warning',
      'error': 'error',
      'success': 'check_circle'
    };
    
    return iconMap[type] || 'notifications';
  }

  /**
   * Open POS system
   */
  openPOS(): void {
    window.open('http://localhost:5127/swagger', '_blank');
    this.showMessage('فتح نقطة البيع في نافذة جديدة');
  }

  /**
   * Refresh dashboard data
   */
  refreshData(): void {
    this.loadDashboardData();
    this.showMessage('تم تحديث البيانات');
  }

  /**
   * Show message (you can implement a toast service)
   */
  private showMessage(message: string): void {
    console.log(message);
    // Implement toast notification here
  }

  /**
   * Handle notification click
   */
  onNotificationClick(notification: any): void {
    console.log('Notification clicked:', notification);
    // Implement notification handling
  }

  /**
   * View all notifications
   */
  viewAllNotifications(): void {
    console.log('View all notifications');
    // Navigate to notifications page
  }

  /**
   * View order details
   */
  viewOrderDetails(order: any): void {
    console.log('View order details:', order);
    // Navigate to order details page
  }

  /**
   * View all products
   */
  viewAllProducts(): void {
    console.log('View all products');
    // Navigate to products page
  }

  /**
   * View detailed reports
   */
  viewDetailedReports(): void {
    console.log('View detailed reports');
    // Navigate to reports page
  }
}
