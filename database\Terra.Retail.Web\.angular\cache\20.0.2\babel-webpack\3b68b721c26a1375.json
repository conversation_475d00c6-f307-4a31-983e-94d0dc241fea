{"ast": null, "code": "import { _IdGenerator } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, viewChild, computed, contentChild, signal, afterRenderEffect, ContentChild, ContentChildren } from '@angular/core';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** The floating label for a `mat-form-field`. */\nconst _c0 = [\"notch\"];\nconst _c1 = [\"matFormFieldNotchedOutline\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = [\"iconPrefixContainer\"];\nconst _c4 = [\"textPrefixContainer\"];\nconst _c5 = [\"iconSuffixContainer\"];\nconst _c6 = [\"textSuffixContainer\"];\nconst _c7 = [\"textField\"];\nconst _c8 = [\"*\", [[\"mat-label\"]], [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"]], [[\"\", \"matTextPrefix\", \"\"]], [[\"\", \"matTextSuffix\", \"\"]], [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"]], [[\"mat-error\"], [\"\", \"matError\", \"\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c9 = [\"*\", \"mat-label\", \"[matPrefix], [matIconPrefix]\", \"[matTextPrefix]\", \"[matTextSuffix]\", \"[matSuffix], [matIconSuffix]\", \"mat-error, [matError]\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nfunction MatFormField_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n}\nfunction MatFormField_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 19);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵconditionalCreate(2, MatFormField_ng_template_0_Conditional_0_Conditional_2_Template, 1, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"floating\", ctx_r1._shouldLabelFloat())(\"monitorResize\", ctx_r1._hasOutline())(\"id\", ctx_r1._labelId);\n    i0.ɵɵattribute(\"for\", ctx_r1._control.disableAutomaticLabeling ? null : ctx_r1._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(!ctx_r1.hideRequiredMarker && ctx_r1._control.required ? 2 : -1);\n  }\n}\nfunction MatFormField_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_ng_template_0_Conditional_0_Template, 3, 5, \"label\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1._hasFloatingLabel() ? 0 : -1);\n  }\n}\nfunction MatFormField_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nfunction MatFormField_Conditional_6_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_6_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_6_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵconditionalCreate(1, MatFormField_Conditional_6_Conditional_1_Template, 1, 1, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matFormFieldNotchedOutlineOpen\", ctx_r1._shouldLabelFloat());\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1._forceDisplayInfixLabel() ? 1 : -1);\n  }\n}\nfunction MatFormField_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10, 2);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵprojection(2, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_10_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_10_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 4);\n    i0.ɵɵprojection(2, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 5);\n    i0.ɵɵprojection(2, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n}\nfunction MatFormField_Case_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 6);\n  }\n}\nfunction MatFormField_Case_18_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r1._hintLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.hintLabel);\n  }\n}\nfunction MatFormField_Case_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_Case_18_Conditional_0_Template, 2, 2, \"mat-hint\", 21);\n    i0.ɵɵprojection(1, 7);\n    i0.ɵɵelement(2, \"div\", 22);\n    i0.ɵɵprojection(3, 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.hintLabel ? 0 : -1);\n  }\n}\nlet MatLabel = /*#__PURE__*/(() => {\n  class MatLabel {\n    static ɵfac = function MatLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLabel,\n      selectors: [[\"mat-label\"]]\n    });\n  }\n  return MatLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = /*#__PURE__*/new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nlet MatError = /*#__PURE__*/(() => {\n  class MatError {\n    id = inject(_IdGenerator).getId('mat-mdc-error-');\n    constructor() {}\n    static ɵfac = function MatError_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatError)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatError,\n      selectors: [[\"mat-error\"], [\"\", \"matError\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-form-field-error\", \"mat-mdc-form-field-bottom-align\"],\n      hostVars: 1,\n      hostBindings: function MatError_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ERROR,\n        useExisting: MatError\n      }])]\n    });\n  }\n  return MatError;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Hint text to be shown underneath the form field control. */\nlet MatHint = /*#__PURE__*/(() => {\n  class MatHint {\n    /** Whether to align the hint label at the start or end of the line. */\n    align = 'start';\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n    id = inject(_IdGenerator).getId('mat-mdc-hint-');\n    static ɵfac = function MatHint_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatHint)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHint,\n      selectors: [[\"mat-hint\"]],\n      hostAttrs: [1, \"mat-mdc-form-field-hint\", \"mat-mdc-form-field-bottom-align\"],\n      hostVars: 4,\n      hostBindings: function MatHint_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"align\", null);\n          i0.ɵɵclassProp(\"mat-mdc-form-field-hint-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\",\n        id: \"id\"\n      }\n    });\n  }\n  return MatHint;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = /*#__PURE__*/new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nlet MatPrefix = /*#__PURE__*/(() => {\n  class MatPrefix {\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = function MatPrefix_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPrefix)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatPrefix,\n      selectors: [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"], [\"\", \"matTextPrefix\", \"\"]],\n      inputs: {\n        _isTextSelector: [0, \"matTextPrefix\", \"_isTextSelector\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_PREFIX,\n        useExisting: MatPrefix\n      }])]\n    });\n  }\n  return MatPrefix;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = /*#__PURE__*/new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nlet MatSuffix = /*#__PURE__*/(() => {\n  class MatSuffix {\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = function MatSuffix_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSuffix)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSuffix,\n      selectors: [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"], [\"\", \"matTextSuffix\", \"\"]],\n      inputs: {\n        _isTextSelector: [0, \"matTextSuffix\", \"_isTextSelector\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SUFFIX,\n        useExisting: MatSuffix\n      }])]\n    });\n  }\n  return MatSuffix;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = /*#__PURE__*/new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nlet MatFormFieldFloatingLabel = /*#__PURE__*/(() => {\n  class MatFormFieldFloatingLabel {\n    _elementRef = inject(ElementRef);\n    /** Whether the label is floating. */\n    get floating() {\n      return this._floating;\n    }\n    set floating(value) {\n      this._floating = value;\n      if (this.monitorResize) {\n        this._handleResize();\n      }\n    }\n    _floating = false;\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n      return this._monitorResize;\n    }\n    set monitorResize(value) {\n      this._monitorResize = value;\n      if (this._monitorResize) {\n        this._subscribeToResize();\n      } else {\n        this._resizeSubscription.unsubscribe();\n      }\n    }\n    _monitorResize = false;\n    /** The shared ResizeObserver. */\n    _resizeObserver = inject(SharedResizeObserver);\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    /** The parent form-field. */\n    _parent = inject(FLOATING_LABEL_PARENT);\n    /** The current resize event subscription. */\n    _resizeSubscription = new Subscription();\n    constructor() {}\n    ngOnDestroy() {\n      this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n      return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n      return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n      // In the case where the label grows in size, the following sequence of events occurs:\n      // 1. The label grows by 1px triggering the ResizeObserver\n      // 2. The notch is expanded to accommodate the entire label\n      // 3. The label expands to its full width, triggering the ResizeObserver again\n      //\n      // This is expected, but If we allow this to all happen within the same macro task it causes an\n      // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n      // the next macro task.\n      setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n      this._resizeSubscription.unsubscribe();\n      this._ngZone.runOutsideAngular(() => {\n        this._resizeSubscription = this._resizeObserver.observe(this._elementRef.nativeElement, {\n          box: 'border-box'\n        }).subscribe(() => this._handleResize());\n      });\n    }\n    static ɵfac = function MatFormFieldFloatingLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldFloatingLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldFloatingLabel,\n      selectors: [[\"label\", \"matFormFieldFloatingLabel\", \"\"]],\n      hostAttrs: [1, \"mdc-floating-label\", \"mat-mdc-floating-label\"],\n      hostVars: 2,\n      hostBindings: function MatFormFieldFloatingLabel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-floating-label--float-above\", ctx.floating);\n        }\n      },\n      inputs: {\n        floating: \"floating\",\n        monitorResize: \"monitorResize\"\n      }\n    });\n  }\n  return MatFormFieldFloatingLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  const clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nlet MatFormFieldLineRipple = /*#__PURE__*/(() => {\n  class MatFormFieldLineRipple {\n    _elementRef = inject(ElementRef);\n    _cleanupTransitionEnd;\n    constructor() {\n      const ngZone = inject(NgZone);\n      const renderer = inject(Renderer2);\n      ngZone.runOutsideAngular(() => {\n        this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n      });\n    }\n    activate() {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(DEACTIVATING_CLASS);\n      classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n      this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    _handleTransitionEnd = event => {\n      const classList = this._elementRef.nativeElement.classList;\n      const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n      if (event.propertyName === 'opacity' && isDeactivating) {\n        classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n      }\n    };\n    ngOnDestroy() {\n      this._cleanupTransitionEnd();\n    }\n    static ɵfac = function MatFormFieldLineRipple_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldLineRipple)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldLineRipple,\n      selectors: [[\"div\", \"matFormFieldLineRipple\", \"\"]],\n      hostAttrs: [1, \"mdc-line-ripple\"]\n    });\n  }\n  return MatFormFieldLineRipple;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nlet MatFormFieldNotchedOutline = /*#__PURE__*/(() => {\n  class MatFormFieldNotchedOutline {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    /** Whether the notch should be opened. */\n    open = false;\n    _notch;\n    ngAfterViewInit() {\n      const element = this._elementRef.nativeElement;\n      const label = element.querySelector('.mdc-floating-label');\n      if (label) {\n        element.classList.add('mdc-notched-outline--upgraded');\n        if (typeof requestAnimationFrame === 'function') {\n          label.style.transitionDuration = '0s';\n          this._ngZone.runOutsideAngular(() => {\n            requestAnimationFrame(() => label.style.transitionDuration = '');\n          });\n        }\n      } else {\n        element.classList.add('mdc-notched-outline--no-label');\n      }\n    }\n    _setNotchWidth(labelWidth) {\n      const notch = this._notch.nativeElement;\n      if (!this.open || !labelWidth) {\n        notch.style.width = '';\n      } else {\n        const NOTCH_ELEMENT_PADDING = 8;\n        const NOTCH_ELEMENT_BORDER = 1;\n        notch.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n      }\n    }\n    _setMaxWidth(prefixAndSuffixWidth) {\n      // Set this only on the notch to avoid style recalculations in other parts of the form field.\n      this._notch.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n    }\n    static ɵfac = function MatFormFieldNotchedOutline_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldNotchedOutline)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFormFieldNotchedOutline,\n      selectors: [[\"div\", \"matFormFieldNotchedOutline\", \"\"]],\n      viewQuery: function MatFormFieldNotchedOutline_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notch = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-notched-outline\"],\n      hostVars: 2,\n      hostBindings: function MatFormFieldNotchedOutline_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-notched-outline--notched\", ctx.open);\n        }\n      },\n      inputs: {\n        open: [0, \"matFormFieldNotchedOutlineOpen\", \"open\"]\n      },\n      attrs: _c1,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 0,\n      consts: [[\"notch\", \"\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__leading\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__notch\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__trailing\"]],\n      template: function MatFormFieldNotchedOutline_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelement(0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2, 0);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"div\", 3);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatFormFieldNotchedOutline;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nlet MatFormFieldControl = /*#__PURE__*/(() => {\n  class MatFormFieldControl {\n    /** The value of the control. */\n    value;\n    /**\n     * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n     * needs to run change detection.\n     */\n    stateChanges;\n    /** The element ID for this control. */\n    id;\n    /** The placeholder for this control. */\n    placeholder;\n    /** Gets the AbstractControlDirective for this control. */\n    ngControl;\n    /** Whether the control is focused. */\n    focused;\n    /** Whether the control is empty. */\n    empty;\n    /** Whether the `MatFormField` label should try to float. */\n    shouldLabelFloat;\n    /** Whether the control is required. */\n    required;\n    /** Whether the control is disabled. */\n    disabled;\n    /** Whether the control is in an error state. */\n    errorState;\n    /**\n     * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n     * based on their control type. The form field will add a class,\n     * `mat-form-field-type-{{controlType}}` to its root element.\n     */\n    controlType;\n    /**\n     * Whether the input is currently in an autofilled state. If property is not present on the\n     * control it is assumed to be false.\n     */\n    autofilled;\n    /**\n     * Value of `aria-describedby` that should be merged with the described-by ids\n     * which are set by the form-field.\n     */\n    userAriaDescribedBy;\n    /**\n     * Whether to automatically assign the ID of the form field as the `for` attribute\n     * on the `<label>` inside the form field. Set this to true to prevent the form\n     * field from associating the label with non-native elements.\n     */\n    disableAutomaticLabeling;\n    /** Gets the list of element IDs that currently describe this control. */\n    describedByIds;\n    static ɵfac = function MatFormFieldControl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldControl)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldControl\n    });\n  }\n  return MatFormFieldControl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = /*#__PURE__*/new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nlet MatFormField = /*#__PURE__*/(() => {\n  class MatFormField {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality);\n    _platform = inject(Platform);\n    _idGenerator = inject(_IdGenerator);\n    _ngZone = inject(NgZone);\n    _defaults = inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _textField;\n    _iconPrefixContainer;\n    _textPrefixContainer;\n    _iconSuffixContainer;\n    _textSuffixContainer;\n    _floatingLabel;\n    _notchedOutline;\n    _lineRipple;\n    _iconPrefixContainerSignal = viewChild('iconPrefixContainer');\n    _textPrefixContainerSignal = viewChild('textPrefixContainer');\n    _iconSuffixContainerSignal = viewChild('iconSuffixContainer');\n    _textSuffixContainerSignal = viewChild('textSuffixContainer');\n    _prefixSuffixContainers = computed(() => {\n      return [this._iconPrefixContainerSignal(), this._textPrefixContainerSignal(), this._iconSuffixContainerSignal(), this._textSuffixContainerSignal()].map(container => container?.nativeElement).filter(e => e !== undefined);\n    });\n    _formFieldControl;\n    _prefixChildren;\n    _suffixChildren;\n    _errorChildren;\n    _hintChildren;\n    _labelChild = contentChild(MatLabel);\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n      return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n      this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    _hideRequiredMarker = false;\n    /**\n     * Theme color of the form field. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n      return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n      if (value !== this._floatLabel) {\n        this._floatLabel = value;\n        // For backwards compatibility. Custom form field controls or directives might set\n        // the \"floatLabel\" input and expect the form field view to be updated automatically.\n        // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n        // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    _floatLabel;\n    /** The form field appearance style. */\n    get appearance() {\n      return this._appearanceSignal();\n    }\n    set appearance(value) {\n      const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n          throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n        }\n      }\n      this._appearanceSignal.set(newAppearance);\n    }\n    _appearanceSignal = signal(DEFAULT_APPEARANCE);\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n      return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n      this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    _subscriptSizing = null;\n    /** Text for the form field hint. */\n    get hintLabel() {\n      return this._hintLabel;\n    }\n    set hintLabel(value) {\n      this._hintLabel = value;\n      this._processHints();\n    }\n    _hintLabel = '';\n    _hasIconPrefix = false;\n    _hasTextPrefix = false;\n    _hasIconSuffix = false;\n    _hasTextSuffix = false;\n    // Unique id for the internal form field label.\n    _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n    // Unique id for the hint label.\n    _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n    // Ids obtained from the error and hint fields\n    _describedByIds;\n    /** Gets the current form field control */\n    get _control() {\n      return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n      this._explicitFormFieldControl = value;\n    }\n    _destroyed = new Subject();\n    _isFocused = null;\n    _explicitFormFieldControl;\n    _previousControl = null;\n    _previousControlValidatorFn = null;\n    _stateChanges;\n    _valueChanges;\n    _describedByChanges;\n    _animationsDisabled = _animationsDisabled();\n    constructor() {\n      const defaults = this._defaults;\n      if (defaults) {\n        if (defaults.appearance) {\n          this.appearance = defaults.appearance;\n        }\n        this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n        if (defaults.color) {\n          this.color = defaults.color;\n        }\n      }\n      this._syncOutlineLabelOffset();\n    }\n    ngAfterViewInit() {\n      // Initial focus state sync. This happens rarely, but we want to account for\n      // it in case the form field control has \"focused\" set to true on init.\n      this._updateFocusState();\n      if (!this._animationsDisabled) {\n        this._ngZone.runOutsideAngular(() => {\n          // Enable animations after a certain amount of time so that they don't run on init.\n          setTimeout(() => {\n            this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n          }, 300);\n        });\n      }\n      // Because the above changes a value used in the template after it was checked, we need\n      // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n      this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n      this._assertFormFieldControl();\n      this._initializeSubscript();\n      this._initializePrefixAndSuffix();\n    }\n    ngAfterContentChecked() {\n      this._assertFormFieldControl();\n      // if form field was being used with an input in first place and then replaced by other\n      // component such as select.\n      if (this._control !== this._previousControl) {\n        this._initializeControl(this._previousControl);\n        // keep a reference for last validator we had.\n        if (this._control.ngControl && this._control.ngControl.control) {\n          this._previousControlValidatorFn = this._control.ngControl.control.validator;\n        }\n        this._previousControl = this._control;\n      }\n      // make sure the the control has been initialized.\n      if (this._control.ngControl && this._control.ngControl.control) {\n        // get the validators for current control.\n        const validatorFn = this._control.ngControl.control.validator;\n        // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n        // component will allow us to catch up.\n        if (validatorFn !== this._previousControlValidatorFn) {\n          this._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._outlineLabelOffsetResizeObserver?.disconnect();\n      this._stateChanges?.unsubscribe();\n      this._valueChanges?.unsubscribe();\n      this._describedByChanges?.unsubscribe();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId = computed(() => this._hasFloatingLabel() ? this._labelId : null);\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n      return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n      // This is for backwards compatibility only. Consumers of the form field might use\n      // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n      // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n      // animation. This is different in MDC where the label always animates, so this method\n      // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n      // the floating label state without animations. The non-MDC implementation was inconsistent\n      // because it always animates if \"floatLabel\" is set away from \"always\".\n      // TODO(devversion): consider removing this method when releasing the MDC form field.\n      if (this._hasFloatingLabel()) {\n        this.floatLabel = 'always';\n      }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl(previousControl) {\n      const control = this._control;\n      const classPrefix = 'mat-mdc-form-field-type-';\n      if (previousControl) {\n        this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n      }\n      if (control.controlType) {\n        this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n      }\n      // Subscribe to changes in the child control state in order to update the form field UI.\n      this._stateChanges?.unsubscribe();\n      this._stateChanges = control.stateChanges.subscribe(() => {\n        this._updateFocusState();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n      this._describedByChanges?.unsubscribe();\n      this._describedByChanges = control.stateChanges.pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n        return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n      })).subscribe(() => this._syncDescribedByIds());\n      this._valueChanges?.unsubscribe();\n      // Run change detection if the value changes.\n      if (control.ngControl && control.ngControl.valueChanges) {\n        this._valueChanges = control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n      }\n    }\n    _checkPrefixAndSuffixTypes() {\n      this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n      this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n      this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n      this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n      this._checkPrefixAndSuffixTypes();\n      // Mark the form field as dirty whenever the prefix or suffix children change. This\n      // is necessary because we conditionally display the prefix/suffix containers based\n      // on whether there is projected content.\n      merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n        this._checkPrefixAndSuffixTypes();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n      // Re-validate when the number of hints changes.\n      this._hintChildren.changes.subscribe(() => {\n        this._processHints();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Update the aria-described by when the number of errors changes.\n      this._errorChildren.changes.subscribe(() => {\n        this._syncDescribedByIds();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Initial mat-hint validation and subscript describedByIds sync.\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n      if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatFormFieldMissingControlError();\n      }\n    }\n    _updateFocusState() {\n      // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n      // certain DOM events are emitted. This is not possible in our implementation of the\n      // form field because we support abstract form field controls which are not necessarily\n      // of type input, nor do we have a reference to a native form field control element. Instead\n      // we handle the focus by checking if the abstract form field control focused state changes.\n      if (this._control.focused && !this._isFocused) {\n        this._isFocused = true;\n        this._lineRipple?.activate();\n      } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n        this._isFocused = false;\n        this._lineRipple?.deactivate();\n      }\n      this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    _outlineLabelOffsetResizeObserver = null;\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update.\n     */\n    _syncOutlineLabelOffset() {\n      afterRenderEffect({\n        earlyRead: () => {\n          if (this._appearanceSignal() !== 'outline') {\n            this._outlineLabelOffsetResizeObserver?.disconnect();\n            return null;\n          }\n          // Setup a resize observer to monitor changes to the size of the prefix / suffix and\n          // readjust the label offset.\n          if (globalThis.ResizeObserver) {\n            this._outlineLabelOffsetResizeObserver ||= new globalThis.ResizeObserver(() => {\n              this._writeOutlinedLabelStyles(this._getOutlinedLabelOffset());\n            });\n            for (const el of this._prefixSuffixContainers()) {\n              this._outlineLabelOffsetResizeObserver.observe(el, {\n                box: 'border-box'\n              });\n            }\n          }\n          return this._getOutlinedLabelOffset();\n        },\n        write: labelStyles => this._writeOutlinedLabelStyles(labelStyles())\n      });\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n      return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n      return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n      return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel = computed(() => !!this._labelChild());\n    _shouldLabelFloat() {\n      if (!this._hasFloatingLabel()) {\n        return false;\n      }\n      return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n      const control = this._control ? this._control.ngControl : null;\n      return control && control[prop];\n    }\n    /** Gets the type of subscript message to render (error or hint). */\n    _getSubscriptMessageType() {\n      return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n      this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n      if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n        this._notchedOutline?._setNotchWidth(0);\n      } else {\n        this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n      }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n      if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        let startHint;\n        let endHint;\n        this._hintChildren.forEach(hint => {\n          if (hint.align === 'start') {\n            if (startHint || this.hintLabel) {\n              throw getMatFormFieldDuplicatedHintError('start');\n            }\n            startHint = hint;\n          } else if (hint.align === 'end') {\n            if (endHint) {\n              throw getMatFormFieldDuplicatedHintError('end');\n            }\n            endHint = hint;\n          }\n        });\n      }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n      if (this._control) {\n        let ids = [];\n        // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n        if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n          ids.push(...this._control.userAriaDescribedBy.split(' '));\n        }\n        if (this._getSubscriptMessageType() === 'hint') {\n          const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n          const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n          if (startHint) {\n            ids.push(startHint.id);\n          } else if (this._hintLabel) {\n            ids.push(this._hintLabelId);\n          }\n          if (endHint) {\n            ids.push(endHint.id);\n          }\n        } else if (this._errorChildren) {\n          ids.push(...this._errorChildren.map(error => error.id));\n        }\n        const existingDescribedBy = this._control.describedByIds;\n        let toAssign;\n        // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n        // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n        // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n        // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n        if (existingDescribedBy) {\n          const exclude = this._describedByIds || ids;\n          toAssign = ids.concat(existingDescribedBy.filter(id => id && !exclude.includes(id)));\n        } else {\n          toAssign = ids;\n        }\n        this._control.setDescribedByIds(toAssign);\n        this._describedByIds = ids;\n      }\n    }\n    /**\n     * Calculates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _getOutlinedLabelOffset() {\n      const dir = this._dir.valueSignal();\n      if (!this._hasOutline() || !this._floatingLabel) {\n        return null;\n      }\n      // If no prefix is displayed, reset the outline label offset from potential\n      // previous label offset updates.\n      if (!this._iconPrefixContainer && !this._textPrefixContainer) {\n        return ['', null];\n      }\n      // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n      // the label offset update until the zone stabilizes.\n      if (!this._isAttachedToDom()) {\n        return null;\n      }\n      const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n      const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n      const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n      const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n      const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n      const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n      const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n      const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n      // If the directionality is RTL, the x-axis transform needs to be inverted. This\n      // is because `transformX` does not change based on the page directionality.\n      const negate = dir === 'rtl' ? '-1' : '1';\n      const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n      const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n      const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n      // Update the translateX of the floating label to account for the prefix container,\n      // but allow the CSS to override this setting via a CSS variable when the label is\n      // floating.\n      const floatingLabelTransform = 'var(--mat-mdc-form-field-label-transform, ' + `${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset}))`;\n      // Prevent the label from overlapping the suffix when in resting position.\n      const notchedOutlineWidth = iconPrefixContainerWidth + textPrefixContainerWidth + iconSuffixContainerWidth + textSuffixContainerWidth;\n      return [floatingLabelTransform, notchedOutlineWidth];\n    }\n    /** Writes the styles produced by `_getOutlineLabelOffset` synchronously to the DOM. */\n    _writeOutlinedLabelStyles(styles) {\n      if (styles !== null) {\n        const [floatingLabelTransform, notchedOutlineWidth] = styles;\n        if (this._floatingLabel) {\n          this._floatingLabel.element.style.transform = floatingLabelTransform;\n        }\n        if (notchedOutlineWidth !== null) {\n          this._notchedOutline?._setMaxWidth(notchedOutlineWidth);\n        }\n      }\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n      const element = this._elementRef.nativeElement;\n      if (element.getRootNode) {\n        const rootNode = element.getRootNode();\n        // If the element is inside the DOM the root node will be either the document\n        // or the closest shadow root, otherwise it'll be the element itself.\n        return rootNode && rootNode !== element;\n      }\n      // Otherwise fall back to checking if it's in the document. This doesn't account for\n      // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n      return document.documentElement.contains(element);\n    }\n    static ɵfac = function MatFormField_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormField)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFormField,\n      selectors: [[\"mat-form-field\"]],\n      contentQueries: function MatFormField_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx._labelChild, MatLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatHint, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance();\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formFieldControl = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n        }\n      },\n      viewQuery: function MatFormField_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuerySignal(ctx._iconPrefixContainerSignal, _c3, 5);\n          i0.ɵɵviewQuerySignal(ctx._textPrefixContainerSignal, _c4, 5);\n          i0.ɵɵviewQuerySignal(ctx._iconSuffixContainerSignal, _c5, 5);\n          i0.ɵɵviewQuerySignal(ctx._textSuffixContainerSignal, _c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(MatFormFieldFloatingLabel, 5);\n          i0.ɵɵviewQuery(MatFormFieldNotchedOutline, 5);\n          i0.ɵɵviewQuery(MatFormFieldLineRipple, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance(4);\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textField = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconPrefixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textPrefixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconSuffixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textSuffixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._floatingLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notchedOutline = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lineRipple = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-form-field\"],\n      hostVars: 40,\n      hostBindings: function MatFormField_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-form-field-label-always-float\", ctx._shouldAlwaysFloat())(\"mat-mdc-form-field-has-icon-prefix\", ctx._hasIconPrefix)(\"mat-mdc-form-field-has-icon-suffix\", ctx._hasIconSuffix)(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-hide-placeholder\", ctx._hasFloatingLabel() && !ctx._shouldLabelFloat())(\"mat-focused\", ctx._control.focused)(\"mat-primary\", ctx.color !== \"accent\" && ctx.color !== \"warn\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"));\n        }\n      },\n      inputs: {\n        hideRequiredMarker: \"hideRequiredMarker\",\n        color: \"color\",\n        floatLabel: \"floatLabel\",\n        appearance: \"appearance\",\n        subscriptSizing: \"subscriptSizing\",\n        hintLabel: \"hintLabel\"\n      },\n      exportAs: [\"matFormField\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_FORM_FIELD,\n        useExisting: MatFormField\n      }, {\n        provide: FLOATING_LABEL_PARENT,\n        useExisting: MatFormField\n      }])],\n      ngContentSelectors: _c9,\n      decls: 19,\n      vars: 25,\n      consts: [[\"labelTemplate\", \"\"], [\"textField\", \"\"], [\"iconPrefixContainer\", \"\"], [\"textPrefixContainer\", \"\"], [\"textSuffixContainer\", \"\"], [\"iconSuffixContainer\", \"\"], [1, \"mat-mdc-text-field-wrapper\", \"mdc-text-field\", 3, \"click\"], [1, \"mat-mdc-form-field-focus-overlay\"], [1, \"mat-mdc-form-field-flex\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\"], [1, \"mat-mdc-form-field-icon-prefix\"], [1, \"mat-mdc-form-field-text-prefix\"], [1, \"mat-mdc-form-field-infix\"], [3, \"ngTemplateOutlet\"], [1, \"mat-mdc-form-field-text-suffix\"], [1, \"mat-mdc-form-field-icon-suffix\"], [\"matFormFieldLineRipple\", \"\"], [1, \"mat-mdc-form-field-subscript-wrapper\", \"mat-mdc-form-field-bottom-align\"], [\"aria-atomic\", \"true\", \"aria-live\", \"polite\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-form-field-required-marker\", \"mdc-floating-label--required\"], [3, \"id\"], [1, \"mat-mdc-form-field-hint-spacer\"]],\n      template: function MatFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c8);\n          i0.ɵɵtemplate(0, MatFormField_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\", 6, 1);\n          i0.ɵɵlistener(\"click\", function MatFormField_Template_div_click_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._control.onContainerClick($event));\n          });\n          i0.ɵɵconditionalCreate(4, MatFormField_Conditional_4_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementStart(5, \"div\", 8);\n          i0.ɵɵconditionalCreate(6, MatFormField_Conditional_6_Template, 2, 2, \"div\", 9);\n          i0.ɵɵconditionalCreate(7, MatFormField_Conditional_7_Template, 3, 0, \"div\", 10);\n          i0.ɵɵconditionalCreate(8, MatFormField_Conditional_8_Template, 3, 0, \"div\", 11);\n          i0.ɵɵelementStart(9, \"div\", 12);\n          i0.ɵɵconditionalCreate(10, MatFormField_Conditional_10_Template, 1, 1, null, 13);\n          i0.ɵɵprojection(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(12, MatFormField_Conditional_12_Template, 3, 0, \"div\", 14);\n          i0.ɵɵconditionalCreate(13, MatFormField_Conditional_13_Template, 3, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(14, MatFormField_Conditional_14_Template, 1, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 17)(16, \"div\", 18);\n          i0.ɵɵconditionalCreate(17, MatFormField_Case_17_Template, 1, 0)(18, MatFormField_Case_18_Template, 4, 1);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_19_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-text-field--filled\", !ctx._hasOutline())(\"mdc-text-field--outlined\", ctx._hasOutline())(\"mdc-text-field--no-label\", !ctx._hasFloatingLabel())(\"mdc-text-field--disabled\", ctx._control.disabled)(\"mdc-text-field--invalid\", ctx._control.errorState);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx._hasOutline() && !ctx._control.disabled ? 4 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasOutline() ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasIconPrefix ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasTextPrefix ? 8 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx._hasOutline() || ctx._forceDisplayInfixLabel() ? 10 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasTextSuffix ? 12 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasIconSuffix ? 13 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx._hasOutline() ? 14 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mat-mdc-form-field-subscript-dynamic-size\", ctx.subscriptSizing === \"dynamic\");\n          const subscriptMessageType_r4 = ctx._getSubscriptMessageType();\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mat-mdc-form-field-error-wrapper\", subscriptMessageType_r4 === \"error\")(\"mat-mdc-form-field-hint-wrapper\", subscriptMessageType_r4 === \"hint\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((tmp_19_0 = subscriptMessageType_r4) === \"error\" ? 17 : tmp_19_0 === \"hint\" ? 18 : -1);\n        }\n      },\n      dependencies: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n      styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatFormField;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };", "map": {"version": 3, "names": ["_IdGenerator", "Directionality", "coerceBooleanProperty", "Platform", "NgTemplateOutlet", "i0", "Directive", "InjectionToken", "inject", "Input", "ElementRef", "NgZone", "Renderer2", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "ViewChild", "ChangeDetectorRef", "viewChild", "computed", "contentChild", "signal", "afterRenderEffect", "ContentChild", "ContentChildren", "Subscription", "Subject", "merge", "startWith", "map", "pairwise", "filter", "takeUntil", "SharedResizeObserver", "_", "_animationsDisabled", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9", "MatFormField_ng_template_0_Conditional_0_Conditional_2_Template", "rf", "ctx", "ɵɵelement", "MatFormField_ng_template_0_Conditional_0_Template", "ɵɵelementStart", "ɵɵprojection", "ɵɵconditionalCreate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "_shouldLabelFloat", "_hasOutline", "_labelId", "ɵɵattribute", "_control", "disableAutomaticLabeling", "id", "ɵɵadvance", "ɵɵconditional", "hideRequiredMarker", "required", "MatFormField_ng_template_0_Template", "_hasFloatingLabel", "MatFormField_Conditional_4_Template", "MatFormField_Conditional_6_Conditional_1_ng_template_0_Template", "MatFormField_Conditional_6_Conditional_1_Template", "ɵɵtemplate", "labelTemplate_r3", "ɵɵreference", "MatFormField_Conditional_6_Template", "_forceDisplayInfixLabel", "MatFormField_Conditional_7_Template", "MatFormField_Conditional_8_Template", "MatFormField_Conditional_10_ng_template_0_Template", "MatFormField_Conditional_10_Template", "MatFormField_Conditional_12_Template", "MatFormField_Conditional_13_Template", "MatFormField_Conditional_14_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Case_17_Template", "Mat<PERSON>orm<PERSON>ield_Case_18_Conditional_0_Template", "ɵɵtext", "_hintLabelId", "ɵɵtextInterpolate", "hintLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Case_18_Template", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "MatLabel_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "MAT_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "getId", "constructor", "MatError_Factory", "hostAttrs", "hostVars", "hostBindings", "MatE<PERSON>r_HostBindings", "ɵɵdomProperty", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "MatHint", "align", "MatHint_Factory", "MatHint_HostBindings", "ɵɵclassProp", "MAT_PREFIX", "MatPrefix", "_isTextSelector", "value", "_isText", "MatPrefix_Factory", "MAT_SUFFIX", "MatSuffix", "MatSuffix_Factory", "FLOATING_LABEL_PARENT", "MatFormFieldFloatingLabel", "_elementRef", "floating", "_floating", "monitorResize", "_handleResize", "_monitorResize", "_subscribeToResize", "_resizeSubscription", "unsubscribe", "_resizeObserver", "_ngZone", "_parent", "ngOnDestroy", "getWidth", "estimateScrollWidth", "nativeElement", "element", "setTimeout", "_handleLabelResized", "runOutsideAngular", "observe", "box", "subscribe", "MatFormFieldFloatingLabel_Factory", "MatFormFieldFloatingLabel_HostBindings", "htmlEl", "offsetParent", "scrollWidth", "clone", "cloneNode", "style", "setProperty", "document", "documentElement", "append<PERSON><PERSON><PERSON>", "remove", "ACTIVATE_CLASS", "DEACTIVATING_CLASS", "MatFormFieldLineRipple", "_cleanupTransitionEnd", "ngZone", "renderer", "listen", "_handleTransitionEnd", "activate", "classList", "add", "deactivate", "event", "isDeactivating", "contains", "propertyName", "MatFormFieldLineRipple_Factory", "MatFormFieldNotchedOutline", "open", "_notch", "ngAfterViewInit", "label", "querySelector", "requestAnimationFrame", "transitionDuration", "_setNotchWidth", "labelWidth", "notch", "width", "NOTCH_ELEMENT_PADDING", "NOTCH_ELEMENT_BORDER", "_setMaxWidth", "prefixAndSuffixWidth", "MatFormFieldNotchedOutline_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatFormFieldNotchedOutline_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "MatFormFieldNotchedOutline_HostBindings", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatFormFieldNotchedOutline_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "MatFormFieldControl", "stateChanges", "placeholder", "ngControl", "focused", "empty", "shouldLabelFloat", "disabled", "errorState", "controlType", "autofilled", "userAriaDescribedBy", "describedByIds", "MatFormFieldControl_Factory", "getMatFormFieldPlaceholderConflictError", "Error", "getMatFormFieldDuplicatedHintError", "getMatFormFieldMissingControlError", "MAT_FORM_FIELD", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "DEFAULT_APPEARANCE", "DEFAULT_FLOAT_LABEL", "DEFAULT_SUBSCRIPT_SIZING", "FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM", "MatFormField", "_changeDetectorRef", "_dir", "_platform", "_idGenerator", "_defaults", "optional", "_textField", "_iconPrefixContainer", "_textPrefixContainer", "_iconSuffixContainer", "_textSuffixContainer", "_floating<PERSON>abel", "_notchedOutline", "_lineRipple", "_iconPrefixContainerSignal", "_textPrefixContainerSignal", "_iconSuffixContainerSignal", "_textSuffixContainerSignal", "_prefixSuffixContainers", "container", "e", "undefined", "_formFieldControl", "_prefixChildren", "_suffixC<PERSON><PERSON>n", "_errorC<PERSON><PERSON>n", "_hint<PERSON><PERSON><PERSON>n", "_labelChild", "_hideRequiredMarker", "color", "floatLabel", "_floatLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appearance", "_appearanceSignal", "newAppearance", "set", "subscriptSizing", "_subscriptSizing", "_<PERSON><PERSON><PERSON>l", "_processHints", "_hasIconPrefix", "_hasTextPrefix", "_hasIconSuffix", "_hasTextSuffix", "_describedByIds", "_explicitFormFieldControl", "_destroyed", "_isFocused", "_previousControl", "_previousControlValidatorFn", "_stateChanges", "_valueChanges", "_describedByChanges", "defaults", "Boolean", "_syncOutlineLabelOffset", "_updateFocusState", "detectChanges", "ngAfterContentInit", "_assertFormFieldControl", "_initializeSubscript", "_initializePrefixAndSuffix", "ngAfterContentChecked", "_initializeControl", "control", "validator", "validatorFn", "_outlineLabelOffsetResizeObserver", "disconnect", "next", "complete", "getLabelId", "getConnectedOverlayOrigin", "_animateAndLockLabel", "previousControl", "classPrefix", "pipe", "prevErrorState", "prevDescribedBy", "currentErrorState", "currentDescribedBy", "_syncDescribedByIds", "valueChanges", "_checkPrefixAndSuffixTypes", "find", "p", "s", "changes", "_validateHints", "toggle", "earlyRead", "globalThis", "ResizeObserver", "_writeOutlinedLabelStyles", "_getOutlinedLabelOffset", "el", "write", "labelStyles", "_shouldAlwaysFloat", "<PERSON><PERSON><PERSON><PERSON>", "length", "_shouldForward", "prop", "_getSubscriptMessageType", "_refreshOutlineNotchWidth", "startHint", "endHint", "for<PERSON>ach", "hint", "ids", "push", "split", "error", "existingDescribedBy", "toAs<PERSON>", "exclude", "concat", "includes", "setDescribedByIds", "dir", "valueSignal", "_isAttachedToDom", "iconPrefixContainer", "textPrefixContainer", "iconSuffixContainer", "textSuffixContainer", "iconPrefixContainer<PERSON>idth", "getBoundingClientRect", "textPrefixContainer<PERSON><PERSON><PERSON>", "iconSuffixContainerWidth", "textSuffixContainer<PERSON>idth", "negate", "prefixWidth", "labelOffset", "labelHorizontalOffset", "floatingLabelTransform", "notched<PERSON><PERSON><PERSON><PERSON>idth", "styles", "transform", "getRootNode", "rootNode", "MatFormField_Factory", "contentQueries", "MatFormField_ContentQueries", "dirIndex", "ɵɵcontentQuerySignal", "ɵɵcontentQuery", "ɵɵqueryAdvance", "MatFormField_Query", "ɵɵviewQuerySignal", "MatFormField_HostBindings", "exportAs", "MatFormField_Template", "_r1", "ɵɵgetCurrentView", "ɵɵtemplateRefExtractor", "ɵɵlistener", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_click_2_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onContainerClick", "tmp_19_0", "subscriptMessageType_r4", "dependencies", "M", "a", "b", "c", "d", "f", "g", "h", "i", "j", "k", "l", "m", "n"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/form-field-C9DZXojn.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, viewChild, computed, contentChild, signal, afterRenderEffect, ContentChild, ContentChildren } from '@angular/core';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** The floating label for a `mat-form-field`. */\nclass MatLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatLabel, isStandalone: true, selector: \"mat-label\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-label',\n                }]\n        }] });\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nclass MatError {\n    id = inject(_IdGenerator).getId('mat-mdc-error-');\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatError, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatError, isStandalone: true, selector: \"mat-error, [matError]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-form-field-error mat-mdc-form-field-bottom-align\" }, providers: [{ provide: MAT_ERROR, useExisting: MatError }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatError, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-error, [matError]',\n                    host: {\n                        'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n                        '[id]': 'id',\n                    },\n                    providers: [{ provide: MAT_ERROR, useExisting: MatError }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { id: [{\n                type: Input\n            }] } });\n\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n    /** Whether to align the hint label at the start or end of the line. */\n    align = 'start';\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n    id = inject(_IdGenerator).getId('mat-mdc-hint-');\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatHint, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatHint, isStandalone: true, selector: \"mat-hint\", inputs: { align: \"align\", id: \"id\" }, host: { properties: { \"class.mat-mdc-form-field-hint-end\": \"align === \\\"end\\\"\", \"id\": \"id\", \"attr.align\": \"null\" }, classAttribute: \"mat-mdc-form-field-hint mat-mdc-form-field-bottom-align\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatHint, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-hint',\n                    host: {\n                        'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n                        '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n                        '[id]': 'id',\n                        // Remove align attribute to prevent it from interfering with layout.\n                        '[attr.align]': 'null',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPrefix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatPrefix, isStandalone: true, selector: \"[matPrefix], [matIconPrefix], [matTextPrefix]\", inputs: { _isTextSelector: [\"matTextPrefix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPrefix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n                    providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }],\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextPrefix']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSuffix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSuffix, isStandalone: true, selector: \"[matSuffix], [matIconSuffix], [matTextSuffix]\", inputs: { _isTextSelector: [\"matTextSuffix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSuffix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n                    providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }],\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextSuffix']\n            }] } });\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nclass MatFormFieldFloatingLabel {\n    _elementRef = inject(ElementRef);\n    /** Whether the label is floating. */\n    get floating() {\n        return this._floating;\n    }\n    set floating(value) {\n        this._floating = value;\n        if (this.monitorResize) {\n            this._handleResize();\n        }\n    }\n    _floating = false;\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n        return this._monitorResize;\n    }\n    set monitorResize(value) {\n        this._monitorResize = value;\n        if (this._monitorResize) {\n            this._subscribeToResize();\n        }\n        else {\n            this._resizeSubscription.unsubscribe();\n        }\n    }\n    _monitorResize = false;\n    /** The shared ResizeObserver. */\n    _resizeObserver = inject(SharedResizeObserver);\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    /** The parent form-field. */\n    _parent = inject(FLOATING_LABEL_PARENT);\n    /** The current resize event subscription. */\n    _resizeSubscription = new Subscription();\n    constructor() { }\n    ngOnDestroy() {\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n        return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n        return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n        // In the case where the label grows in size, the following sequence of events occurs:\n        // 1. The label grows by 1px triggering the ResizeObserver\n        // 2. The notch is expanded to accommodate the entire label\n        // 3. The label expands to its full width, triggering the ResizeObserver again\n        //\n        // This is expected, but If we allow this to all happen within the same macro task it causes an\n        // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n        // the next macro task.\n        setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n        this._resizeSubscription.unsubscribe();\n        this._ngZone.runOutsideAngular(() => {\n            this._resizeSubscription = this._resizeObserver\n                .observe(this._elementRef.nativeElement, { box: 'border-box' })\n                .subscribe(() => this._handleResize());\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldFloatingLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatFormFieldFloatingLabel, isStandalone: true, selector: \"label[matFormFieldFloatingLabel]\", inputs: { floating: \"floating\", monitorResize: \"monitorResize\" }, host: { properties: { \"class.mdc-floating-label--float-above\": \"floating\" }, classAttribute: \"mdc-floating-label mat-mdc-floating-label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldFloatingLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'label[matFormFieldFloatingLabel]',\n                    host: {\n                        'class': 'mdc-floating-label mat-mdc-floating-label',\n                        '[class.mdc-floating-label--float-above]': 'floating',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { floating: [{\n                type: Input\n            }], monitorResize: [{\n                type: Input\n            }] } });\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n    // Check the offsetParent. If the element inherits display: none from any\n    // parent, the offsetParent property will be null (see\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n    // This check ensures we only clone the node when necessary.\n    const htmlEl = element;\n    if (htmlEl.offsetParent !== null) {\n        return htmlEl.scrollWidth;\n    }\n    const clone = htmlEl.cloneNode(true);\n    clone.style.setProperty('position', 'absolute');\n    clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n    document.documentElement.appendChild(clone);\n    const scrollWidth = clone.scrollWidth;\n    clone.remove();\n    return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nclass MatFormFieldLineRipple {\n    _elementRef = inject(ElementRef);\n    _cleanupTransitionEnd;\n    constructor() {\n        const ngZone = inject(NgZone);\n        const renderer = inject(Renderer2);\n        ngZone.runOutsideAngular(() => {\n            this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n        });\n    }\n    activate() {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(DEACTIVATING_CLASS);\n        classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n        this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    _handleTransitionEnd = (event) => {\n        const classList = this._elementRef.nativeElement.classList;\n        const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n        if (event.propertyName === 'opacity' && isDeactivating) {\n            classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n        }\n    };\n    ngOnDestroy() {\n        this._cleanupTransitionEnd();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldLineRipple, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatFormFieldLineRipple, isStandalone: true, selector: \"div[matFormFieldLineRipple]\", host: { classAttribute: \"mdc-line-ripple\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldLineRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'div[matFormFieldLineRipple]',\n                    host: {\n                        'class': 'mdc-line-ripple',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nclass MatFormFieldNotchedOutline {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    /** Whether the notch should be opened. */\n    open = false;\n    _notch;\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        const label = element.querySelector('.mdc-floating-label');\n        if (label) {\n            element.classList.add('mdc-notched-outline--upgraded');\n            if (typeof requestAnimationFrame === 'function') {\n                label.style.transitionDuration = '0s';\n                this._ngZone.runOutsideAngular(() => {\n                    requestAnimationFrame(() => (label.style.transitionDuration = ''));\n                });\n            }\n        }\n        else {\n            element.classList.add('mdc-notched-outline--no-label');\n        }\n    }\n    _setNotchWidth(labelWidth) {\n        const notch = this._notch.nativeElement;\n        if (!this.open || !labelWidth) {\n            notch.style.width = '';\n        }\n        else {\n            const NOTCH_ELEMENT_PADDING = 8;\n            const NOTCH_ELEMENT_BORDER = 1;\n            notch.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n        }\n    }\n    _setMaxWidth(prefixAndSuffixWidth) {\n        // Set this only on the notch to avoid style recalculations in other parts of the form field.\n        this._notch.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldNotchedOutline, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatFormFieldNotchedOutline, isStandalone: true, selector: \"div[matFormFieldNotchedOutline]\", inputs: { open: [\"matFormFieldNotchedOutlineOpen\", \"open\"] }, host: { properties: { \"class.mdc-notched-outline--notched\": \"open\" }, classAttribute: \"mdc-notched-outline\" }, viewQueries: [{ propertyName: \"_notch\", first: true, predicate: [\"notch\"], descendants: true }], ngImport: i0, template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldNotchedOutline, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[matFormFieldNotchedOutline]', host: {\n                        'class': 'mdc-notched-outline',\n                        // Besides updating the notch state through the MDC component, we toggle this class through\n                        // a host binding in order to ensure that the notched-outline renders correctly on the server.\n                        '[class.mdc-notched-outline--notched]': 'open',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\" }]\n        }], propDecorators: { open: [{\n                type: Input,\n                args: ['matFormFieldNotchedOutlineOpen']\n            }], _notch: [{\n                type: ViewChild,\n                args: ['notch']\n            }] } });\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n    /** The value of the control. */\n    value;\n    /**\n     * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n     * needs to run change detection.\n     */\n    stateChanges;\n    /** The element ID for this control. */\n    id;\n    /** The placeholder for this control. */\n    placeholder;\n    /** Gets the AbstractControlDirective for this control. */\n    ngControl;\n    /** Whether the control is focused. */\n    focused;\n    /** Whether the control is empty. */\n    empty;\n    /** Whether the `MatFormField` label should try to float. */\n    shouldLabelFloat;\n    /** Whether the control is required. */\n    required;\n    /** Whether the control is disabled. */\n    disabled;\n    /** Whether the control is in an error state. */\n    errorState;\n    /**\n     * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n     * based on their control type. The form field will add a class,\n     * `mat-form-field-type-{{controlType}}` to its root element.\n     */\n    controlType;\n    /**\n     * Whether the input is currently in an autofilled state. If property is not present on the\n     * control it is assumed to be false.\n     */\n    autofilled;\n    /**\n     * Value of `aria-describedby` that should be merged with the described-by ids\n     * which are set by the form-field.\n     */\n    userAriaDescribedBy;\n    /**\n     * Whether to automatically assign the ID of the form field as the `for` attribute\n     * on the `<label>` inside the form field. Set this to true to prevent the form\n     * field from associating the label with non-native elements.\n     */\n    disableAutomaticLabeling;\n    /** Gets the list of element IDs that currently describe this control. */\n    describedByIds;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldControl, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatFormFieldControl, isStandalone: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldControl, decorators: [{\n            type: Directive\n        }] });\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n    return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n    return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n    return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality);\n    _platform = inject(Platform);\n    _idGenerator = inject(_IdGenerator);\n    _ngZone = inject(NgZone);\n    _defaults = inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _textField;\n    _iconPrefixContainer;\n    _textPrefixContainer;\n    _iconSuffixContainer;\n    _textSuffixContainer;\n    _floatingLabel;\n    _notchedOutline;\n    _lineRipple;\n    _iconPrefixContainerSignal = viewChild('iconPrefixContainer');\n    _textPrefixContainerSignal = viewChild('textPrefixContainer');\n    _iconSuffixContainerSignal = viewChild('iconSuffixContainer');\n    _textSuffixContainerSignal = viewChild('textSuffixContainer');\n    _prefixSuffixContainers = computed(() => {\n        return [\n            this._iconPrefixContainerSignal(),\n            this._textPrefixContainerSignal(),\n            this._iconSuffixContainerSignal(),\n            this._textSuffixContainerSignal(),\n        ]\n            .map(container => container?.nativeElement)\n            .filter(e => e !== undefined);\n    });\n    _formFieldControl;\n    _prefixChildren;\n    _suffixChildren;\n    _errorChildren;\n    _hintChildren;\n    _labelChild = contentChild(MatLabel);\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n        return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n        this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    _hideRequiredMarker = false;\n    /**\n     * Theme color of the form field. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n        return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n        if (value !== this._floatLabel) {\n            this._floatLabel = value;\n            // For backwards compatibility. Custom form field controls or directives might set\n            // the \"floatLabel\" input and expect the form field view to be updated automatically.\n            // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n            // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _floatLabel;\n    /** The form field appearance style. */\n    get appearance() {\n        return this._appearanceSignal();\n    }\n    set appearance(value) {\n        const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n                throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n            }\n        }\n        this._appearanceSignal.set(newAppearance);\n    }\n    _appearanceSignal = signal(DEFAULT_APPEARANCE);\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n        return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n        this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    _subscriptSizing = null;\n    /** Text for the form field hint. */\n    get hintLabel() {\n        return this._hintLabel;\n    }\n    set hintLabel(value) {\n        this._hintLabel = value;\n        this._processHints();\n    }\n    _hintLabel = '';\n    _hasIconPrefix = false;\n    _hasTextPrefix = false;\n    _hasIconSuffix = false;\n    _hasTextSuffix = false;\n    // Unique id for the internal form field label.\n    _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n    // Unique id for the hint label.\n    _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n    // Ids obtained from the error and hint fields\n    _describedByIds;\n    /** Gets the current form field control */\n    get _control() {\n        return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n        this._explicitFormFieldControl = value;\n    }\n    _destroyed = new Subject();\n    _isFocused = null;\n    _explicitFormFieldControl;\n    _previousControl = null;\n    _previousControlValidatorFn = null;\n    _stateChanges;\n    _valueChanges;\n    _describedByChanges;\n    _animationsDisabled = _animationsDisabled();\n    constructor() {\n        const defaults = this._defaults;\n        if (defaults) {\n            if (defaults.appearance) {\n                this.appearance = defaults.appearance;\n            }\n            this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n            if (defaults.color) {\n                this.color = defaults.color;\n            }\n        }\n        this._syncOutlineLabelOffset();\n    }\n    ngAfterViewInit() {\n        // Initial focus state sync. This happens rarely, but we want to account for\n        // it in case the form field control has \"focused\" set to true on init.\n        this._updateFocusState();\n        if (!this._animationsDisabled) {\n            this._ngZone.runOutsideAngular(() => {\n                // Enable animations after a certain amount of time so that they don't run on init.\n                setTimeout(() => {\n                    this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n                }, 300);\n            });\n        }\n        // Because the above changes a value used in the template after it was checked, we need\n        // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n        this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n        this._assertFormFieldControl();\n        this._initializeSubscript();\n        this._initializePrefixAndSuffix();\n    }\n    ngAfterContentChecked() {\n        this._assertFormFieldControl();\n        // if form field was being used with an input in first place and then replaced by other\n        // component such as select.\n        if (this._control !== this._previousControl) {\n            this._initializeControl(this._previousControl);\n            // keep a reference for last validator we had.\n            if (this._control.ngControl && this._control.ngControl.control) {\n                this._previousControlValidatorFn = this._control.ngControl.control.validator;\n            }\n            this._previousControl = this._control;\n        }\n        // make sure the the control has been initialized.\n        if (this._control.ngControl && this._control.ngControl.control) {\n            // get the validators for current control.\n            const validatorFn = this._control.ngControl.control.validator;\n            // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n            // component will allow us to catch up.\n            if (validatorFn !== this._previousControlValidatorFn) {\n                this._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._outlineLabelOffsetResizeObserver?.disconnect();\n        this._stateChanges?.unsubscribe();\n        this._valueChanges?.unsubscribe();\n        this._describedByChanges?.unsubscribe();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId = computed(() => (this._hasFloatingLabel() ? this._labelId : null));\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n        return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n        // This is for backwards compatibility only. Consumers of the form field might use\n        // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n        // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n        // animation. This is different in MDC where the label always animates, so this method\n        // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n        // the floating label state without animations. The non-MDC implementation was inconsistent\n        // because it always animates if \"floatLabel\" is set away from \"always\".\n        // TODO(devversion): consider removing this method when releasing the MDC form field.\n        if (this._hasFloatingLabel()) {\n            this.floatLabel = 'always';\n        }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl(previousControl) {\n        const control = this._control;\n        const classPrefix = 'mat-mdc-form-field-type-';\n        if (previousControl) {\n            this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n        }\n        if (control.controlType) {\n            this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n        }\n        // Subscribe to changes in the child control state in order to update the form field UI.\n        this._stateChanges?.unsubscribe();\n        this._stateChanges = control.stateChanges.subscribe(() => {\n            this._updateFocusState();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n        this._describedByChanges?.unsubscribe();\n        this._describedByChanges = control.stateChanges\n            .pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n            return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n        }))\n            .subscribe(() => this._syncDescribedByIds());\n        this._valueChanges?.unsubscribe();\n        // Run change detection if the value changes.\n        if (control.ngControl && control.ngControl.valueChanges) {\n            this._valueChanges = control.ngControl.valueChanges\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => this._changeDetectorRef.markForCheck());\n        }\n    }\n    _checkPrefixAndSuffixTypes() {\n        this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n        this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n        this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n        this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n        this._checkPrefixAndSuffixTypes();\n        // Mark the form field as dirty whenever the prefix or suffix children change. This\n        // is necessary because we conditionally display the prefix/suffix containers based\n        // on whether there is projected content.\n        merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n            this._checkPrefixAndSuffixTypes();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n        // Re-validate when the number of hints changes.\n        this._hintChildren.changes.subscribe(() => {\n            this._processHints();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Update the aria-described by when the number of errors changes.\n        this._errorChildren.changes.subscribe(() => {\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Initial mat-hint validation and subscript describedByIds sync.\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n        if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatFormFieldMissingControlError();\n        }\n    }\n    _updateFocusState() {\n        // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n        // certain DOM events are emitted. This is not possible in our implementation of the\n        // form field because we support abstract form field controls which are not necessarily\n        // of type input, nor do we have a reference to a native form field control element. Instead\n        // we handle the focus by checking if the abstract form field control focused state changes.\n        if (this._control.focused && !this._isFocused) {\n            this._isFocused = true;\n            this._lineRipple?.activate();\n        }\n        else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n            this._isFocused = false;\n            this._lineRipple?.deactivate();\n        }\n        this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    _outlineLabelOffsetResizeObserver = null;\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update.\n     */\n    _syncOutlineLabelOffset() {\n        afterRenderEffect({\n            earlyRead: () => {\n                if (this._appearanceSignal() !== 'outline') {\n                    this._outlineLabelOffsetResizeObserver?.disconnect();\n                    return null;\n                }\n                // Setup a resize observer to monitor changes to the size of the prefix / suffix and\n                // readjust the label offset.\n                if (globalThis.ResizeObserver) {\n                    this._outlineLabelOffsetResizeObserver ||= new globalThis.ResizeObserver(() => {\n                        this._writeOutlinedLabelStyles(this._getOutlinedLabelOffset());\n                    });\n                    for (const el of this._prefixSuffixContainers()) {\n                        this._outlineLabelOffsetResizeObserver.observe(el, { box: 'border-box' });\n                    }\n                }\n                return this._getOutlinedLabelOffset();\n            },\n            write: labelStyles => this._writeOutlinedLabelStyles(labelStyles()),\n        });\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n        return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n        return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n        return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel = computed(() => !!this._labelChild());\n    _shouldLabelFloat() {\n        if (!this._hasFloatingLabel()) {\n            return false;\n        }\n        return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n        const control = this._control ? this._control.ngControl : null;\n        return control && control[prop];\n    }\n    /** Gets the type of subscript message to render (error or hint). */\n    _getSubscriptMessageType() {\n        return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n            ? 'error'\n            : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n        this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n        if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n            this._notchedOutline?._setNotchWidth(0);\n        }\n        else {\n            this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n        }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n        if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            let startHint;\n            let endHint;\n            this._hintChildren.forEach((hint) => {\n                if (hint.align === 'start') {\n                    if (startHint || this.hintLabel) {\n                        throw getMatFormFieldDuplicatedHintError('start');\n                    }\n                    startHint = hint;\n                }\n                else if (hint.align === 'end') {\n                    if (endHint) {\n                        throw getMatFormFieldDuplicatedHintError('end');\n                    }\n                    endHint = hint;\n                }\n            });\n        }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n        if (this._control) {\n            let ids = [];\n            // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n            if (this._control.userAriaDescribedBy &&\n                typeof this._control.userAriaDescribedBy === 'string') {\n                ids.push(...this._control.userAriaDescribedBy.split(' '));\n            }\n            if (this._getSubscriptMessageType() === 'hint') {\n                const startHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'start')\n                    : null;\n                const endHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'end')\n                    : null;\n                if (startHint) {\n                    ids.push(startHint.id);\n                }\n                else if (this._hintLabel) {\n                    ids.push(this._hintLabelId);\n                }\n                if (endHint) {\n                    ids.push(endHint.id);\n                }\n            }\n            else if (this._errorChildren) {\n                ids.push(...this._errorChildren.map(error => error.id));\n            }\n            const existingDescribedBy = this._control.describedByIds;\n            let toAssign;\n            // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n            // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n            // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n            // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n            if (existingDescribedBy) {\n                const exclude = this._describedByIds || ids;\n                toAssign = ids.concat(existingDescribedBy.filter(id => id && !exclude.includes(id)));\n            }\n            else {\n                toAssign = ids;\n            }\n            this._control.setDescribedByIds(toAssign);\n            this._describedByIds = ids;\n        }\n    }\n    /**\n     * Calculates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _getOutlinedLabelOffset() {\n        const dir = this._dir.valueSignal();\n        if (!this._hasOutline() || !this._floatingLabel) {\n            return null;\n        }\n        // If no prefix is displayed, reset the outline label offset from potential\n        // previous label offset updates.\n        if (!this._iconPrefixContainer && !this._textPrefixContainer) {\n            return ['', null];\n        }\n        // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n        // the label offset update until the zone stabilizes.\n        if (!this._isAttachedToDom()) {\n            return null;\n        }\n        const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n        const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n        const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n        const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n        const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n        const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n        const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n        const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n        // If the directionality is RTL, the x-axis transform needs to be inverted. This\n        // is because `transformX` does not change based on the page directionality.\n        const negate = dir === 'rtl' ? '-1' : '1';\n        const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n        const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n        const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n        // Update the translateX of the floating label to account for the prefix container,\n        // but allow the CSS to override this setting via a CSS variable when the label is\n        // floating.\n        const floatingLabelTransform = 'var(--mat-mdc-form-field-label-transform, ' +\n            `${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset}))`;\n        // Prevent the label from overlapping the suffix when in resting position.\n        const notchedOutlineWidth = iconPrefixContainerWidth +\n            textPrefixContainerWidth +\n            iconSuffixContainerWidth +\n            textSuffixContainerWidth;\n        return [floatingLabelTransform, notchedOutlineWidth];\n    }\n    /** Writes the styles produced by `_getOutlineLabelOffset` synchronously to the DOM. */\n    _writeOutlinedLabelStyles(styles) {\n        if (styles !== null) {\n            const [floatingLabelTransform, notchedOutlineWidth] = styles;\n            if (this._floatingLabel) {\n                this._floatingLabel.element.style.transform = floatingLabelTransform;\n            }\n            if (notchedOutlineWidth !== null) {\n                this._notchedOutline?._setMaxWidth(notchedOutlineWidth);\n            }\n        }\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n        const element = this._elementRef.nativeElement;\n        if (element.getRootNode) {\n            const rootNode = element.getRootNode();\n            // If the element is inside the DOM the root node will be either the document\n            // or the closest shadow root, otherwise it'll be the element itself.\n            return rootNode && rootNode !== element;\n        }\n        // Otherwise fall back to checking if it's in the document. This doesn't account for\n        // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n        return document.documentElement.contains(element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormField, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatFormField, isStandalone: true, selector: \"mat-form-field\", inputs: { hideRequiredMarker: \"hideRequiredMarker\", color: \"color\", floatLabel: \"floatLabel\", appearance: \"appearance\", subscriptSizing: \"subscriptSizing\", hintLabel: \"hintLabel\" }, host: { properties: { \"class.mat-mdc-form-field-label-always-float\": \"_shouldAlwaysFloat()\", \"class.mat-mdc-form-field-has-icon-prefix\": \"_hasIconPrefix\", \"class.mat-mdc-form-field-has-icon-suffix\": \"_hasIconSuffix\", \"class.mat-form-field-invalid\": \"_control.errorState\", \"class.mat-form-field-disabled\": \"_control.disabled\", \"class.mat-form-field-autofilled\": \"_control.autofilled\", \"class.mat-form-field-appearance-fill\": \"appearance == \\\"fill\\\"\", \"class.mat-form-field-appearance-outline\": \"appearance == \\\"outline\\\"\", \"class.mat-form-field-hide-placeholder\": \"_hasFloatingLabel() && !_shouldLabelFloat()\", \"class.mat-focused\": \"_control.focused\", \"class.mat-primary\": \"color !== \\\"accent\\\" && color !== \\\"warn\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.ng-untouched\": \"_shouldForward(\\\"untouched\\\")\", \"class.ng-touched\": \"_shouldForward(\\\"touched\\\")\", \"class.ng-pristine\": \"_shouldForward(\\\"pristine\\\")\", \"class.ng-dirty\": \"_shouldForward(\\\"dirty\\\")\", \"class.ng-valid\": \"_shouldForward(\\\"valid\\\")\", \"class.ng-invalid\": \"_shouldForward(\\\"invalid\\\")\", \"class.ng-pending\": \"_shouldForward(\\\"pending\\\")\" }, classAttribute: \"mat-mdc-form-field\" }, providers: [\n            { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n            { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n        ], queries: [{ propertyName: \"_labelChild\", first: true, predicate: MatLabel, descendants: true, isSignal: true }, { propertyName: \"_formFieldControl\", first: true, predicate: MatFormFieldControl, descendants: true }, { propertyName: \"_prefixChildren\", predicate: MAT_PREFIX, descendants: true }, { propertyName: \"_suffixChildren\", predicate: MAT_SUFFIX, descendants: true }, { propertyName: \"_errorChildren\", predicate: MAT_ERROR, descendants: true }, { propertyName: \"_hintChildren\", predicate: MatHint, descendants: true }], viewQueries: [{ propertyName: \"_iconPrefixContainerSignal\", first: true, predicate: [\"iconPrefixContainer\"], descendants: true, isSignal: true }, { propertyName: \"_textPrefixContainerSignal\", first: true, predicate: [\"textPrefixContainer\"], descendants: true, isSignal: true }, { propertyName: \"_iconSuffixContainerSignal\", first: true, predicate: [\"iconSuffixContainer\"], descendants: true, isSignal: true }, { propertyName: \"_textSuffixContainerSignal\", first: true, predicate: [\"textSuffixContainer\"], descendants: true, isSignal: true }, { propertyName: \"_textField\", first: true, predicate: [\"textField\"], descendants: true }, { propertyName: \"_iconPrefixContainer\", first: true, predicate: [\"iconPrefixContainer\"], descendants: true }, { propertyName: \"_textPrefixContainer\", first: true, predicate: [\"textPrefixContainer\"], descendants: true }, { propertyName: \"_iconSuffixContainer\", first: true, predicate: [\"iconSuffixContainer\"], descendants: true }, { propertyName: \"_textSuffixContainer\", first: true, predicate: [\"textSuffixContainer\"], descendants: true }, { propertyName: \"_floatingLabel\", first: true, predicate: MatFormFieldFloatingLabel, descendants: true }, { propertyName: \"_notchedOutline\", first: true, predicate: MatFormFieldNotchedOutline, descendants: true }, { propertyName: \"_lineRipple\", first: true, predicate: MatFormFieldLineRipple, descendants: true }], exportAs: [\"matFormField\"], ngImport: i0, template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\", styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"], dependencies: [{ kind: \"directive\", type: MatFormFieldFloatingLabel, selector: \"label[matFormFieldFloatingLabel]\", inputs: [\"floating\", \"monitorResize\"] }, { kind: \"component\", type: MatFormFieldNotchedOutline, selector: \"div[matFormFieldNotchedOutline]\", inputs: [\"matFormFieldNotchedOutlineOpen\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: MatFormFieldLineRipple, selector: \"div[matFormFieldLineRipple]\" }, { kind: \"directive\", type: MatHint, selector: \"mat-hint\", inputs: [\"align\", \"id\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-form-field', exportAs: 'matFormField', host: {\n                        'class': 'mat-mdc-form-field',\n                        '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n                        '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n                        '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n                        // Note that these classes reuse the same names as the non-MDC version, because they can be\n                        // considered a public API since custom form controls may use them to style themselves.\n                        // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n                        '[class.mat-form-field-invalid]': '_control.errorState',\n                        '[class.mat-form-field-disabled]': '_control.disabled',\n                        '[class.mat-form-field-autofilled]': '_control.autofilled',\n                        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n                        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n                        '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n                        '[class.mat-focused]': '_control.focused',\n                        '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n                        '[class.ng-touched]': '_shouldForward(\"touched\")',\n                        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n                        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n                        '[class.ng-valid]': '_shouldForward(\"valid\")',\n                        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n                        '[class.ng-pending]': '_shouldForward(\"pending\")',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n                        { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n                    ], imports: [\n                        MatFormFieldFloatingLabel,\n                        MatFormFieldNotchedOutline,\n                        NgTemplateOutlet,\n                        MatFormFieldLineRipple,\n                        MatHint,\n                    ], template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\", styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _textField: [{\n                type: ViewChild,\n                args: ['textField']\n            }], _iconPrefixContainer: [{\n                type: ViewChild,\n                args: ['iconPrefixContainer']\n            }], _textPrefixContainer: [{\n                type: ViewChild,\n                args: ['textPrefixContainer']\n            }], _iconSuffixContainer: [{\n                type: ViewChild,\n                args: ['iconSuffixContainer']\n            }], _textSuffixContainer: [{\n                type: ViewChild,\n                args: ['textSuffixContainer']\n            }], _floatingLabel: [{\n                type: ViewChild,\n                args: [MatFormFieldFloatingLabel]\n            }], _notchedOutline: [{\n                type: ViewChild,\n                args: [MatFormFieldNotchedOutline]\n            }], _lineRipple: [{\n                type: ViewChild,\n                args: [MatFormFieldLineRipple]\n            }], _formFieldControl: [{\n                type: ContentChild,\n                args: [MatFormFieldControl]\n            }], _prefixChildren: [{\n                type: ContentChildren,\n                args: [MAT_PREFIX, { descendants: true }]\n            }], _suffixChildren: [{\n                type: ContentChildren,\n                args: [MAT_SUFFIX, { descendants: true }]\n            }], _errorChildren: [{\n                type: ContentChildren,\n                args: [MAT_ERROR, { descendants: true }]\n            }], _hintChildren: [{\n                type: ContentChildren,\n                args: [MatHint, { descendants: true }]\n            }], hideRequiredMarker: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], floatLabel: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }], subscriptSizing: [{\n                type: Input\n            }], hintLabel: [{\n                type: Input\n            }] } });\n\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe,QAAQ,eAAe;AACzR,SAASC,YAAY,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,SAAS,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAC5E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;;AAEnE;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gEAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAE6F1C,EAAE,CAAA4C,SAAA,cAy+BmkH,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+BtkH1C,EAAE,CAAA8C,cAAA,eAy+B4kG,CAAC;IAz+B/kG9C,EAAE,CAAA+C,YAAA,KAy+BkoG,CAAC;IAz+BroG/C,EAAE,CAAAgD,mBAAA,IAAAP,+DAAA,kBAy+B66G,CAAC;IAz+Bh7GzC,EAAE,CAAAiD,YAAA,CAy+B0lH,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAz+B7lHlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAoD,UAAA,aAAAF,MAAA,CAAAG,iBAAA,EAy+Bs7F,CAAC,kBAAAH,MAAA,CAAAI,WAAA,EAAwC,CAAC,OAAAJ,MAAA,CAAAK,QAAwB,CAAC;IAz+B3/FvD,EAAE,CAAAwD,WAAA,QAAAN,MAAA,CAAAO,QAAA,CAAAC,wBAAA,UAAAR,MAAA,CAAAO,QAAA,CAAAE,EAAA;IAAF3D,EAAE,CAAA4D,SAAA,EAy+B4kH,CAAC;IAz+B/kH5D,EAAE,CAAA6D,aAAA,EAAAX,MAAA,CAAAY,kBAAA,IAAAZ,MAAA,CAAAO,QAAA,CAAAM,QAAA,SAy+B4kH,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B/kH1C,EAAE,CAAAgD,mBAAA,IAAAH,iDAAA,mBAy+B+1F,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAQ,MAAA,GAz+Bl2FlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA6D,aAAA,CAAAX,MAAA,CAAAe,iBAAA,WAy+B+lH,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+BlmH1C,EAAE,CAAA4C,SAAA,YAy+BynI,CAAC;EAAA;AAAA;AAAA,SAAAuB,gEAAAzB,EAAA,EAAAC,GAAA;AAAA,SAAAyB,kDAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B5nI1C,EAAE,CAAAqE,UAAA,IAAAF,+DAAA,yBAy+B+4I,CAAC;EAAA;EAAA,IAAAzB,EAAA;IAz+Bl5I1C,EAAE,CAAAmD,aAAA;IAAA,MAAAmB,gBAAA,GAAFtE,EAAE,CAAAuE,WAAA;IAAFvE,EAAE,CAAAoD,UAAA,qBAAAkB,gBAy+B84I,CAAC;EAAA;AAAA;AAAA,SAAAE,oCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+Bj5I1C,EAAE,CAAA8C,cAAA,YAy+BqyI,CAAC;IAz+BxyI9C,EAAE,CAAAgD,mBAAA,IAAAoB,iDAAA,gBAy+Bi1I,CAAC;IAz+Bp1IpE,EAAE,CAAAiD,YAAA,CAy+Bs7I,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAz+Bz7IlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAoD,UAAA,mCAAAF,MAAA,CAAAG,iBAAA,EAy+BoyI,CAAC;IAz+BvyIrD,EAAE,CAAA4D,SAAA,CAy+Bw6I,CAAC;IAz+B36I5D,EAAE,CAAA6D,aAAA,EAAAX,MAAA,CAAAuB,uBAAA,WAy+Bw6I,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B36I1C,EAAE,CAAA8C,cAAA,gBAy+BsiJ,CAAC;IAz+BziJ9C,EAAE,CAAA+C,YAAA,KAy+BinJ,CAAC;IAz+BpnJ/C,EAAE,CAAAiD,YAAA,CAy+B+nJ,CAAC;EAAA;AAAA;AAAA,SAAA0B,oCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+BloJ1C,EAAE,CAAA8C,cAAA,gBAy+B+uJ,CAAC;IAz+BlvJ9C,EAAE,CAAA+C,YAAA,KAy+B6yJ,CAAC;IAz+BhzJ/C,EAAE,CAAAiD,YAAA,CAy+B2zJ,CAAC;EAAA;AAAA;AAAA,SAAA2B,mDAAAlC,EAAA,EAAAC,GAAA;AAAA,SAAAkC,qCAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B9zJ1C,EAAE,CAAAqE,UAAA,IAAAO,kDAAA,yBAy+By+J,CAAC;EAAA;EAAA,IAAAlC,EAAA;IAz+B5+J1C,EAAE,CAAAmD,aAAA;IAAA,MAAAmB,gBAAA,GAAFtE,EAAE,CAAAuE,WAAA;IAAFvE,EAAE,CAAAoD,UAAA,qBAAAkB,gBAy+Bw+J,CAAC;EAAA;AAAA;AAAA,SAAAQ,qCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B3+J1C,EAAE,CAAA8C,cAAA,gBAy+BwpK,CAAC;IAz+B3pK9C,EAAE,CAAA+C,YAAA,KAy+BstK,CAAC;IAz+BztK/C,EAAE,CAAAiD,YAAA,CAy+BouK,CAAC;EAAA;AAAA;AAAA,SAAA8B,qCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+BvuK1C,EAAE,CAAA8C,cAAA,gBAy+Bo1K,CAAC;IAz+Bv1K9C,EAAE,CAAA+C,YAAA,KAy+B+5K,CAAC;IAz+Bl6K/C,EAAE,CAAAiD,YAAA,CAy+B66K,CAAC;EAAA;AAAA;AAAA,SAAA+B,qCAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+Bh7K1C,EAAE,CAAA4C,SAAA,aAy+BkgL,CAAC;EAAA;AAAA;AAAA,SAAAqC,8BAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+BrgL1C,EAAE,CAAA+C,YAAA,KAy+B02M,CAAC;EAAA;AAAA;AAAA,SAAAmC,4CAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+B72M1C,EAAE,CAAA8C,cAAA,kBAy+Bo9M,CAAC;IAz+Bv9M9C,EAAE,CAAAmF,MAAA,EAy+Bi+M,CAAC;IAz+Bp+MnF,EAAE,CAAAiD,YAAA,CAy+B4+M,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAz+B/+MlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAoD,UAAA,OAAAF,MAAA,CAAAkC,YAy+Bm9M,CAAC;IAz+Bt9MpF,EAAE,CAAA4D,SAAA,CAy+Bi+M,CAAC;IAz+Bp+M5D,EAAE,CAAAqF,iBAAA,CAAAnC,MAAA,CAAAoC,SAy+Bi+M,CAAC;EAAA;AAAA;AAAA,SAAAC,8BAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz+Bp+M1C,EAAE,CAAAgD,mBAAA,IAAAkC,2CAAA,sBAy+Bw6M,CAAC;IAz+B36MlF,EAAE,CAAA+C,YAAA,KAy+BikN,CAAC;IAz+BpkN/C,EAAE,CAAA4C,SAAA,aAy+B+nN,CAAC;IAz+BloN5C,EAAE,CAAA+C,YAAA,KAy+BmsN,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAQ,MAAA,GAz+BtsNlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA6D,aAAA,CAAAX,MAAA,CAAAoC,SAAA,SAy+Bu/M,CAAC;EAAA;AAAA;AAAA,IA1+BjlNE,QAAQ;EAAd,MAAMA,QAAQ,CAAC;IACX,OAAOC,IAAI,YAAAC,iBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,QAAQ;IAAA;IAC3G,OAAOI,IAAI,kBAD8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EACJN,QAAQ;MAAAO,SAAA;IAAA;EACnG;EAAC,OAHKP,QAAQ;AAAA;AAId;EAAA,QAAAQ,SAAA,oBAAAA,SAAA;AAAA;;AAOA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,gBAAG,IAAI/F,cAAc,CAAC,UAAU,CAAC;AAChD;AAAA,IACMgG,QAAQ;EAAd,MAAMA,QAAQ,CAAC;IACXvC,EAAE,GAAGxD,MAAM,CAACR,YAAY,CAAC,CAACwG,KAAK,CAAC,gBAAgB,CAAC;IACjDC,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOX,IAAI,YAAAY,iBAAAV,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,QAAQ;IAAA;IAC3G,OAAON,IAAI,kBArB8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAqBJI,QAAQ;MAAAH,SAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAA/D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArBN1C,EAAE,CAAA0G,aAAA,OAAA/D,GAAA,CAAAgB,EAqBG,CAAC;QAAA;MAAA;MAAAgD,MAAA;QAAAhD,EAAA;MAAA;MAAAiD,QAAA,GArBN5G,EAAE,CAAA6G,kBAAA,CAqBgN,CAAC;QAAEC,OAAO,EAAEb,SAAS;QAAEc,WAAW,EAAEb;MAAS,CAAC,CAAC;IAAA;EAC9V;EAAC,OALKA,QAAQ;AAAA;AAMd;EAAA,QAAAF,SAAA,oBAAAA,SAAA;AAAA;;AAcA;AAAA,IACMgB,OAAO;EAAb,MAAMA,OAAO,CAAC;IACV;IACAC,KAAK,GAAG,OAAO;IACf;IACAtD,EAAE,GAAGxD,MAAM,CAACR,YAAY,CAAC,CAACwG,KAAK,CAAC,eAAe,CAAC;IAChD,OAAOV,IAAI,YAAAyB,gBAAAvB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqB,OAAO;IAAA;IAC1G,OAAOpB,IAAI,kBA5C8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EA4CJkB,OAAO;MAAAjB,SAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAW,qBAAAzE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5CL1C,EAAE,CAAA0G,aAAA,OAAA/D,GAAA,CAAAgB,EA4CE,CAAC;UA5CL3D,EAAE,CAAAwD,WAAA,UA4CJ,IAAI;UA5CFxD,EAAE,CAAAoH,WAAA,gCAAAzE,GAAA,CAAAsE,KAAA,KA4CM,KAAJ,CAAC;QAAA;MAAA;MAAAN,MAAA;QAAAM,KAAA;QAAAtD,EAAA;MAAA;IAAA;EAClG;EAAC,OAPKqD,OAAO;AAAA;AAQb;EAAA,QAAAhB,SAAA,oBAAAA,SAAA;AAAA;;AAkBA;AACA;AACA;AACA;AACA;AACA,MAAMqB,UAAU,gBAAG,IAAInH,cAAc,CAAC,WAAW,CAAC;AAClD;AAAA,IACMoH,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZ,IAAIC,eAAeA,CAACC,KAAK,EAAE;MACvB,IAAI,CAACC,OAAO,GAAG,IAAI;IACvB;IACAA,OAAO,GAAG,KAAK;IACf,OAAOhC,IAAI,YAAAiC,kBAAA/B,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2B,SAAS;IAAA;IAC5G,OAAO1B,IAAI,kBA7E8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EA6EJwB,SAAS;MAAAvB,SAAA;MAAAY,MAAA;QAAAY,eAAA;MAAA;MAAAX,QAAA,GA7EP5G,EAAE,CAAA6G,kBAAA,CA6EoK,CAAC;QAAEC,OAAO,EAAEO,UAAU;QAAEN,WAAW,EAAEO;MAAU,CAAC,CAAC;IAAA;EACpT;EAAC,OAPKA,SAAS;AAAA;AAQf;EAAA,QAAAtB,SAAA,oBAAAA,SAAA;AAAA;;AAWA;AACA;AACA;AACA;AACA;AACA,MAAM2B,UAAU,gBAAG,IAAIzH,cAAc,CAAC,WAAW,CAAC;AAClD;AAAA,IACM0H,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZ,IAAIL,eAAeA,CAACC,KAAK,EAAE;MACvB,IAAI,CAACC,OAAO,GAAG,IAAI;IACvB;IACAA,OAAO,GAAG,KAAK;IACf,OAAOhC,IAAI,YAAAoC,kBAAAlC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiC,SAAS;IAAA;IAC5G,OAAOhC,IAAI,kBAvG8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAuGJ8B,SAAS;MAAA7B,SAAA;MAAAY,MAAA;QAAAY,eAAA;MAAA;MAAAX,QAAA,GAvGP5G,EAAE,CAAA6G,kBAAA,CAuGoK,CAAC;QAAEC,OAAO,EAAEa,UAAU;QAAEZ,WAAW,EAAEa;MAAU,CAAC,CAAC;IAAA;EACpT;EAAC,OAPKA,SAAS;AAAA;AAQf;EAAA,QAAA5B,SAAA,oBAAAA,SAAA;AAAA;;AAWA;AACA,MAAM8B,qBAAqB,gBAAG,IAAI5H,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,IAaM6H,yBAAyB;EAA/B,MAAMA,yBAAyB,CAAC;IAC5BC,WAAW,GAAG7H,MAAM,CAACE,UAAU,CAAC;IAChC;IACA,IAAI4H,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACT,KAAK,EAAE;MAChB,IAAI,CAACU,SAAS,GAAGV,KAAK;MACtB,IAAI,IAAI,CAACW,aAAa,EAAE;QACpB,IAAI,CAACC,aAAa,CAAC,CAAC;MACxB;IACJ;IACAF,SAAS,GAAG,KAAK;IACjB;IACA,IAAIC,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACE,cAAc;IAC9B;IACA,IAAIF,aAAaA,CAACX,KAAK,EAAE;MACrB,IAAI,CAACa,cAAc,GAAGb,KAAK;MAC3B,IAAI,IAAI,CAACa,cAAc,EAAE;QACrB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACC,mBAAmB,CAACC,WAAW,CAAC,CAAC;MAC1C;IACJ;IACAH,cAAc,GAAG,KAAK;IACtB;IACAI,eAAe,GAAGtI,MAAM,CAACyB,oBAAoB,CAAC;IAC9C;IACA8G,OAAO,GAAGvI,MAAM,CAACG,MAAM,CAAC;IACxB;IACAqI,OAAO,GAAGxI,MAAM,CAAC2H,qBAAqB,CAAC;IACvC;IACAS,mBAAmB,GAAG,IAAInH,YAAY,CAAC,CAAC;IACxCgF,WAAWA,CAAA,EAAG,CAAE;IAChBwC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACL,mBAAmB,CAACC,WAAW,CAAC,CAAC;IAC1C;IACA;IACAK,QAAQA,CAAA,EAAG;MACP,OAAOC,mBAAmB,CAAC,IAAI,CAACd,WAAW,CAACe,aAAa,CAAC;IAC9D;IACA;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAAChB,WAAW,CAACe,aAAa;IACzC;IACA;IACAX,aAAaA,CAAA,EAAG;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAa,UAAU,CAAC,MAAM,IAAI,CAACN,OAAO,CAACO,mBAAmB,CAAC,CAAC,CAAC;IACxD;IACA;IACAZ,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACC,mBAAmB,CAACC,WAAW,CAAC,CAAC;MACtC,IAAI,CAACE,OAAO,CAACS,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACZ,mBAAmB,GAAG,IAAI,CAACE,eAAe,CAC1CW,OAAO,CAAC,IAAI,CAACpB,WAAW,CAACe,aAAa,EAAE;UAAEM,GAAG,EAAE;QAAa,CAAC,CAAC,CAC9DC,SAAS,CAAC,MAAM,IAAI,CAAClB,aAAa,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC;IACN;IACA,OAAO3C,IAAI,YAAA8D,kCAAA5D,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoC,yBAAyB;IAAA;IAC5H,OAAOnC,IAAI,kBAxM8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAwMJiC,yBAAyB;MAAAhC,SAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgD,uCAAA9G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxMvB1C,EAAE,CAAAoH,WAAA,oCAAAzE,GAAA,CAAAsF,QAwMoB,CAAC;QAAA;MAAA;MAAAtB,MAAA;QAAAsB,QAAA;QAAAE,aAAA;MAAA;IAAA;EACpH;EAAC,OAtEKJ,yBAAyB;AAAA;AAuE/B;EAAA,QAAA/B,SAAA,oBAAAA,SAAA;AAAA;AAcA;AACA;AACA;AACA;AACA,SAAS8C,mBAAmBA,CAACE,OAAO,EAAE;EAClC;EACA;EACA;EACA;EACA,MAAMS,MAAM,GAAGT,OAAO;EACtB,IAAIS,MAAM,CAACC,YAAY,KAAK,IAAI,EAAE;IAC9B,OAAOD,MAAM,CAACE,WAAW;EAC7B;EACA,MAAMC,KAAK,GAAGH,MAAM,CAACI,SAAS,CAAC,IAAI,CAAC;EACpCD,KAAK,CAACE,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;EAC/CH,KAAK,CAACE,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,6BAA6B,CAAC;EACnEC,QAAQ,CAACC,eAAe,CAACC,WAAW,CAACN,KAAK,CAAC;EAC3C,MAAMD,WAAW,GAAGC,KAAK,CAACD,WAAW;EACrCC,KAAK,CAACO,MAAM,CAAC,CAAC;EACd,OAAOR,WAAW;AACtB;;AAEA;AACA,MAAMS,cAAc,GAAG,yBAAyB;AAChD;AACA,MAAMC,kBAAkB,GAAG,+BAA+B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,IAQMC,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzBtC,WAAW,GAAG7H,MAAM,CAACE,UAAU,CAAC;IAChCkK,qBAAqB;IACrBnE,WAAWA,CAAA,EAAG;MACV,MAAMoE,MAAM,GAAGrK,MAAM,CAACG,MAAM,CAAC;MAC7B,MAAMmK,QAAQ,GAAGtK,MAAM,CAACI,SAAS,CAAC;MAClCiK,MAAM,CAACrB,iBAAiB,CAAC,MAAM;QAC3B,IAAI,CAACoB,qBAAqB,GAAGE,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC1C,WAAW,CAACe,aAAa,EAAE,eAAe,EAAE,IAAI,CAAC4B,oBAAoB,CAAC;MAC5H,CAAC,CAAC;IACN;IACAC,QAAQA,CAAA,EAAG;MACP,MAAMC,SAAS,GAAG,IAAI,CAAC7C,WAAW,CAACe,aAAa,CAAC8B,SAAS;MAC1DA,SAAS,CAACV,MAAM,CAACE,kBAAkB,CAAC;MACpCQ,SAAS,CAACC,GAAG,CAACV,cAAc,CAAC;IACjC;IACAW,UAAUA,CAAA,EAAG;MACT,IAAI,CAAC/C,WAAW,CAACe,aAAa,CAAC8B,SAAS,CAACC,GAAG,CAACT,kBAAkB,CAAC;IACpE;IACAM,oBAAoB,GAAIK,KAAK,IAAK;MAC9B,MAAMH,SAAS,GAAG,IAAI,CAAC7C,WAAW,CAACe,aAAa,CAAC8B,SAAS;MAC1D,MAAMI,cAAc,GAAGJ,SAAS,CAACK,QAAQ,CAACb,kBAAkB,CAAC;MAC7D,IAAIW,KAAK,CAACG,YAAY,KAAK,SAAS,IAAIF,cAAc,EAAE;QACpDJ,SAAS,CAACV,MAAM,CAACC,cAAc,EAAEC,kBAAkB,CAAC;MACxD;IACJ,CAAC;IACDzB,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2B,qBAAqB,CAAC,CAAC;IAChC;IACA,OAAO9E,IAAI,YAAA2F,+BAAAzF,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2E,sBAAsB;IAAA;IACzH,OAAO1E,IAAI,kBAvR8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAuRJwE,sBAAsB;MAAAvE,SAAA;MAAAO,SAAA;IAAA;EACjH;EAAC,OA9BKgE,sBAAsB;AAAA;AA+B5B;EAAA,QAAAtE,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMqF,0BAA0B;EAAhC,MAAMA,0BAA0B,CAAC;IAC7BrD,WAAW,GAAG7H,MAAM,CAACE,UAAU,CAAC;IAChCqI,OAAO,GAAGvI,MAAM,CAACG,MAAM,CAAC;IACxB;IACAgL,IAAI,GAAG,KAAK;IACZC,MAAM;IACNC,eAAeA,CAAA,EAAG;MACd,MAAMxC,OAAO,GAAG,IAAI,CAAChB,WAAW,CAACe,aAAa;MAC9C,MAAM0C,KAAK,GAAGzC,OAAO,CAAC0C,aAAa,CAAC,qBAAqB,CAAC;MAC1D,IAAID,KAAK,EAAE;QACPzC,OAAO,CAAC6B,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;QACtD,IAAI,OAAOa,qBAAqB,KAAK,UAAU,EAAE;UAC7CF,KAAK,CAAC3B,KAAK,CAAC8B,kBAAkB,GAAG,IAAI;UACrC,IAAI,CAAClD,OAAO,CAACS,iBAAiB,CAAC,MAAM;YACjCwC,qBAAqB,CAAC,MAAOF,KAAK,CAAC3B,KAAK,CAAC8B,kBAAkB,GAAG,EAAG,CAAC;UACtE,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD5C,OAAO,CAAC6B,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC1D;IACJ;IACAe,cAAcA,CAACC,UAAU,EAAE;MACvB,MAAMC,KAAK,GAAG,IAAI,CAACR,MAAM,CAACxC,aAAa;MACvC,IAAI,CAAC,IAAI,CAACuC,IAAI,IAAI,CAACQ,UAAU,EAAE;QAC3BC,KAAK,CAACjC,KAAK,CAACkC,KAAK,GAAG,EAAE;MAC1B,CAAC,MACI;QACD,MAAMC,qBAAqB,GAAG,CAAC;QAC/B,MAAMC,oBAAoB,GAAG,CAAC;QAC9BH,KAAK,CAACjC,KAAK,CAACkC,KAAK,GAAG,QAAQF,UAAU,+DAA+DG,qBAAqB,GAAGC,oBAAoB,KAAK;MAC1J;IACJ;IACAC,YAAYA,CAACC,oBAAoB,EAAE;MAC/B;MACA,IAAI,CAACb,MAAM,CAACxC,aAAa,CAACe,KAAK,CAACC,WAAW,CAAC,kCAAkC,EAAE,eAAeqC,oBAAoB,KAAK,CAAC;IAC7H;IACA,OAAO3G,IAAI,YAAA4G,mCAAA1G,iBAAA;MAAA,YAAAA,iBAAA,IAAwF0F,0BAA0B;IAAA;IAC7H,OAAOiB,IAAI,kBA/U8EtM,EAAE,CAAAuM,iBAAA;MAAAzG,IAAA,EA+UJuF,0BAA0B;MAAAtF,SAAA;MAAAyG,SAAA,WAAAC,iCAAA/J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/UxB1C,EAAE,CAAA0M,WAAA,CAAA3K,GAAA;QAAA;QAAA,IAAAW,EAAA;UAAA,IAAAiK,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAA4I,MAAA,GAAAoB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAxG,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAuG,wCAAArK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAoH,WAAA,iCAAAzE,GAAA,CAAA2I,IA+UqB,CAAC;QAAA;MAAA;MAAA3E,MAAA;QAAA2E,IAAA;MAAA;MAAA0B,KAAA,EAAAhL,GAAA;MAAAiL,kBAAA,EAAAhL,GAAA;MAAAiL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAA5K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/UxB1C,EAAE,CAAAuN,eAAA;UAAFvN,EAAE,CAAA4C,SAAA,YA+Uqc,CAAC;UA/Uxc5C,EAAE,CAAA8C,cAAA,eA+U4gB,CAAC;UA/U/gB9C,EAAE,CAAA+C,YAAA,EA+UyiB,CAAC;UA/U5iB/C,EAAE,CAAAiD,YAAA,CA+UijB,CAAC;UA/UpjBjD,EAAE,CAAA4C,SAAA,YA+U0nB,CAAC;QAAA;MAAA;MAAA4K,aAAA;MAAAC,eAAA;IAAA;EAC1tB;EAAC,OAvCKpC,0BAA0B;AAAA;AAwChC;EAAA,QAAArF,SAAA,oBAAAA,SAAA;AAAA;;AAgBA;AAAA,IACM0H,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtB;IACAlG,KAAK;IACL;AACJ;AACA;AACA;IACImG,YAAY;IACZ;IACAhK,EAAE;IACF;IACAiK,WAAW;IACX;IACAC,SAAS;IACT;IACAC,OAAO;IACP;IACAC,KAAK;IACL;IACAC,gBAAgB;IAChB;IACAjK,QAAQ;IACR;IACAkK,QAAQ;IACR;IACAC,UAAU;IACV;AACJ;AACA;AACA;AACA;IACIC,WAAW;IACX;AACJ;AACA;AACA;IACIC,UAAU;IACV;AACJ;AACA;AACA;IACIC,mBAAmB;IACnB;AACJ;AACA;AACA;AACA;IACI3K,wBAAwB;IACxB;IACA4K,cAAc;IACd,OAAO7I,IAAI,YAAA8I,4BAAA5I,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+H,mBAAmB;IAAA;IACtH,OAAO9H,IAAI,kBArZ8E5F,EAAE,CAAA6F,iBAAA;MAAAC,IAAA,EAqZJ4H;IAAmB;EAC9G;EAAC,OApDKA,mBAAmB;AAAA;AAqDzB;EAAA,QAAA1H,SAAA,oBAAAA,SAAA;AAAA;;AAIA;AACA,SAASwI,uCAAuCA,CAAA,EAAG;EAC/C,OAAOC,KAAK,CAAC,8DAA8D,CAAC;AAChF;AACA;AACA,SAASC,kCAAkCA,CAACzH,KAAK,EAAE;EAC/C,OAAOwH,KAAK,CAAC,2CAA2CxH,KAAK,KAAK,CAAC;AACvE;AACA;AACA,SAAS0H,kCAAkCA,CAAA,EAAG;EAC1C,OAAOF,KAAK,CAAC,oDAAoD,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,gBAAG,IAAI1O,cAAc,CAAC,cAAc,CAAC;AACzD;AACA;AACA;AACA;AACA,MAAM2O,8BAA8B,gBAAG,IAAI3O,cAAc,CAAC,gCAAgC,CAAC;AAC3F;AACA,MAAM4O,kBAAkB,GAAG,MAAM;AACjC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,MAAM;AAClC;AACA,MAAMC,wBAAwB,GAAG,OAAO;AACxC;AACA;AACA;AACA;AACA;AACA,MAAMC,uCAAuC,GAAG,kBAAkB;AAClE;AAAA,IACMC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACflH,WAAW,GAAG7H,MAAM,CAACE,UAAU,CAAC;IAChC8O,kBAAkB,GAAGhP,MAAM,CAACS,iBAAiB,CAAC;IAC9CwO,IAAI,GAAGjP,MAAM,CAACP,cAAc,CAAC;IAC7ByP,SAAS,GAAGlP,MAAM,CAACL,QAAQ,CAAC;IAC5BwP,YAAY,GAAGnP,MAAM,CAACR,YAAY,CAAC;IACnC+I,OAAO,GAAGvI,MAAM,CAACG,MAAM,CAAC;IACxBiP,SAAS,GAAGpP,MAAM,CAAC0O,8BAA8B,EAAE;MAC/CW,QAAQ,EAAE;IACd,CAAC,CAAC;IACFC,UAAU;IACVC,oBAAoB;IACpBC,oBAAoB;IACpBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC,eAAe;IACfC,WAAW;IACXC,0BAA0B,GAAGpP,SAAS,CAAC,qBAAqB,CAAC;IAC7DqP,0BAA0B,GAAGrP,SAAS,CAAC,qBAAqB,CAAC;IAC7DsP,0BAA0B,GAAGtP,SAAS,CAAC,qBAAqB,CAAC;IAC7DuP,0BAA0B,GAAGvP,SAAS,CAAC,qBAAqB,CAAC;IAC7DwP,uBAAuB,GAAGvP,QAAQ,CAAC,MAAM;MACrC,OAAO,CACH,IAAI,CAACmP,0BAA0B,CAAC,CAAC,EACjC,IAAI,CAACC,0BAA0B,CAAC,CAAC,EACjC,IAAI,CAACC,0BAA0B,CAAC,CAAC,EACjC,IAAI,CAACC,0BAA0B,CAAC,CAAC,CACpC,CACI5O,GAAG,CAAC8O,SAAS,IAAIA,SAAS,EAAEvH,aAAa,CAAC,CAC1CrH,MAAM,CAAC6O,CAAC,IAAIA,CAAC,KAAKC,SAAS,CAAC;IACrC,CAAC,CAAC;IACFC,iBAAiB;IACjBC,eAAe;IACfC,eAAe;IACfC,cAAc;IACdC,aAAa;IACbC,WAAW,GAAG/P,YAAY,CAACyE,QAAQ,CAAC;IACpC;IACA,IAAI1B,kBAAkBA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACiN,mBAAmB;IACnC;IACA,IAAIjN,kBAAkBA,CAAC0D,KAAK,EAAE;MAC1B,IAAI,CAACuJ,mBAAmB,GAAGlR,qBAAqB,CAAC2H,KAAK,CAAC;IAC3D;IACAuJ,mBAAmB,GAAG,KAAK;IAC3B;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,KAAK,GAAG,SAAS;IACjB;IACA,IAAIC,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAAC3B,SAAS,EAAE0B,UAAU,IAAIlC,mBAAmB;IAChF;IACA,IAAIkC,UAAUA,CAACzJ,KAAK,EAAE;MAClB,IAAIA,KAAK,KAAK,IAAI,CAAC0J,WAAW,EAAE;QAC5B,IAAI,CAACA,WAAW,GAAG1J,KAAK;QACxB;QACA;QACA;QACA;QACA,IAAI,CAAC2H,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1C;IACJ;IACAD,WAAW;IACX;IACA,IAAIE,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACnC;IACA,IAAID,UAAUA,CAAC5J,KAAK,EAAE;MAClB,MAAM8J,aAAa,GAAG9J,KAAK,IAAI,IAAI,CAAC+H,SAAS,EAAE6B,UAAU,IAAItC,kBAAkB;MAC/E,IAAI,OAAO9I,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAIsL,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,SAAS,EAAE;UACzD,MAAM,IAAI7C,KAAK,CAAC,qCAAqC6C,aAAa,0CAA0C,CAAC;QACjH;MACJ;MACA,IAAI,CAACD,iBAAiB,CAACE,GAAG,CAACD,aAAa,CAAC;IAC7C;IACAD,iBAAiB,GAAGrQ,MAAM,CAAC8N,kBAAkB,CAAC;IAC9C;AACJ;AACA;AACA;AACA;IACI,IAAI0C,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAAClC,SAAS,EAAEiC,eAAe,IAAIxC,wBAAwB;IAC/F;IACA,IAAIwC,eAAeA,CAAChK,KAAK,EAAE;MACvB,IAAI,CAACiK,gBAAgB,GAAGjK,KAAK,IAAI,IAAI,CAAC+H,SAAS,EAAEiC,eAAe,IAAIxC,wBAAwB;IAChG;IACAyC,gBAAgB,GAAG,IAAI;IACvB;IACA,IAAInM,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACoM,UAAU;IAC1B;IACA,IAAIpM,SAASA,CAACkC,KAAK,EAAE;MACjB,IAAI,CAACkK,UAAU,GAAGlK,KAAK;MACvB,IAAI,CAACmK,aAAa,CAAC,CAAC;IACxB;IACAD,UAAU,GAAG,EAAE;IACfE,cAAc,GAAG,KAAK;IACtBC,cAAc,GAAG,KAAK;IACtBC,cAAc,GAAG,KAAK;IACtBC,cAAc,GAAG,KAAK;IACtB;IACAxO,QAAQ,GAAG,IAAI,CAAC+L,YAAY,CAACnJ,KAAK,CAAC,2BAA2B,CAAC;IAC/D;IACAf,YAAY,GAAG,IAAI,CAACkK,YAAY,CAACnJ,KAAK,CAAC,eAAe,CAAC;IACvD;IACA6L,eAAe;IACf;IACA,IAAIvO,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACwO,yBAAyB,IAAI,IAAI,CAACxB,iBAAiB;IACnE;IACA,IAAIhN,QAAQA,CAAC+D,KAAK,EAAE;MAChB,IAAI,CAACyK,yBAAyB,GAAGzK,KAAK;IAC1C;IACA0K,UAAU,GAAG,IAAI7Q,OAAO,CAAC,CAAC;IAC1B8Q,UAAU,GAAG,IAAI;IACjBF,yBAAyB;IACzBG,gBAAgB,GAAG,IAAI;IACvBC,2BAA2B,GAAG,IAAI;IAClCC,aAAa;IACbC,aAAa;IACbC,mBAAmB;IACnB1Q,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3CsE,WAAWA,CAAA,EAAG;MACV,MAAMqM,QAAQ,GAAG,IAAI,CAAClD,SAAS;MAC/B,IAAIkD,QAAQ,EAAE;QACV,IAAIA,QAAQ,CAACrB,UAAU,EAAE;UACrB,IAAI,CAACA,UAAU,GAAGqB,QAAQ,CAACrB,UAAU;QACzC;QACA,IAAI,CAACL,mBAAmB,GAAG2B,OAAO,CAACD,QAAQ,EAAE3O,kBAAkB,CAAC;QAChE,IAAI2O,QAAQ,CAACzB,KAAK,EAAE;UAChB,IAAI,CAACA,KAAK,GAAGyB,QAAQ,CAACzB,KAAK;QAC/B;MACJ;MACA,IAAI,CAAC2B,uBAAuB,CAAC,CAAC;IAClC;IACAnH,eAAeA,CAAA,EAAG;MACd;MACA;MACA,IAAI,CAACoH,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAAC,IAAI,CAAC9Q,mBAAmB,EAAE;QAC3B,IAAI,CAAC4G,OAAO,CAACS,iBAAiB,CAAC,MAAM;UACjC;UACAF,UAAU,CAAC,MAAM;YACb,IAAI,CAACjB,WAAW,CAACe,aAAa,CAAC8B,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;UACrF,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,CAAC;MACN;MACA;MACA;MACA,IAAI,CAACqE,kBAAkB,CAAC0D,aAAa,CAAC,CAAC;IAC3C;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACrC;IACAC,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAACH,uBAAuB,CAAC,CAAC;MAC9B;MACA;MACA,IAAI,IAAI,CAACtP,QAAQ,KAAK,IAAI,CAAC2O,gBAAgB,EAAE;QACzC,IAAI,CAACe,kBAAkB,CAAC,IAAI,CAACf,gBAAgB,CAAC;QAC9C;QACA,IAAI,IAAI,CAAC3O,QAAQ,CAACoK,SAAS,IAAI,IAAI,CAACpK,QAAQ,CAACoK,SAAS,CAACuF,OAAO,EAAE;UAC5D,IAAI,CAACf,2BAA2B,GAAG,IAAI,CAAC5O,QAAQ,CAACoK,SAAS,CAACuF,OAAO,CAACC,SAAS;QAChF;QACA,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAAC3O,QAAQ;MACzC;MACA;MACA,IAAI,IAAI,CAACA,QAAQ,CAACoK,SAAS,IAAI,IAAI,CAACpK,QAAQ,CAACoK,SAAS,CAACuF,OAAO,EAAE;QAC5D;QACA,MAAME,WAAW,GAAG,IAAI,CAAC7P,QAAQ,CAACoK,SAAS,CAACuF,OAAO,CAACC,SAAS;QAC7D;QACA;QACA,IAAIC,WAAW,KAAK,IAAI,CAACjB,2BAA2B,EAAE;UAClD,IAAI,CAAClD,kBAAkB,CAACgC,YAAY,CAAC,CAAC;QAC1C;MACJ;IACJ;IACAvI,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2K,iCAAiC,EAAEC,UAAU,CAAC,CAAC;MACpD,IAAI,CAAClB,aAAa,EAAE9J,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC+J,aAAa,EAAE/J,WAAW,CAAC,CAAC;MACjC,IAAI,CAACgK,mBAAmB,EAAEhK,WAAW,CAAC,CAAC;MACvC,IAAI,CAAC0J,UAAU,CAACuB,IAAI,CAAC,CAAC;MACtB,IAAI,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;IACIC,UAAU,GAAG7S,QAAQ,CAAC,MAAO,IAAI,CAACmD,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACV,QAAQ,GAAG,IAAK,CAAC;IAC9E;AACJ;AACA;AACA;IACIqQ,yBAAyBA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACnE,UAAU,IAAI,IAAI,CAACzH,WAAW;IAC9C;IACA;IACA6L,oBAAoBA,CAAA,EAAG;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC5P,iBAAiB,CAAC,CAAC,EAAE;QAC1B,IAAI,CAACgN,UAAU,GAAG,QAAQ;MAC9B;IACJ;IACA;IACAkC,kBAAkBA,CAACW,eAAe,EAAE;MAChC,MAAMV,OAAO,GAAG,IAAI,CAAC3P,QAAQ;MAC7B,MAAMsQ,WAAW,GAAG,0BAA0B;MAC9C,IAAID,eAAe,EAAE;QACjB,IAAI,CAAC9L,WAAW,CAACe,aAAa,CAAC8B,SAAS,CAACV,MAAM,CAAC4J,WAAW,GAAGD,eAAe,CAAC3F,WAAW,CAAC;MAC9F;MACA,IAAIiF,OAAO,CAACjF,WAAW,EAAE;QACrB,IAAI,CAACnG,WAAW,CAACe,aAAa,CAAC8B,SAAS,CAACC,GAAG,CAACiJ,WAAW,GAAGX,OAAO,CAACjF,WAAW,CAAC;MACnF;MACA;MACA,IAAI,CAACmE,aAAa,EAAE9J,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC8J,aAAa,GAAGc,OAAO,CAACzF,YAAY,CAACrE,SAAS,CAAC,MAAM;QACtD,IAAI,CAACsJ,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACzD,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF;MACA,IAAI,CAACqB,mBAAmB,EAAEhK,WAAW,CAAC,CAAC;MACvC,IAAI,CAACgK,mBAAmB,GAAGY,OAAO,CAACzF,YAAY,CAC1CqG,IAAI,CAACzS,SAAS,CAAC,CAACiP,SAAS,EAAEA,SAAS,CAAC,CAAC,EAAEhP,GAAG,CAAC,MAAM,CAAC4R,OAAO,CAAClF,UAAU,EAAEkF,OAAO,CAAC/E,mBAAmB,CAAC,CAAC,EAAE5M,QAAQ,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACuS,cAAc,EAAEC,eAAe,CAAC,EAAE,CAACC,iBAAiB,EAAEC,kBAAkB,CAAC,CAAC,KAAK;QAC5M,OAAOH,cAAc,KAAKE,iBAAiB,IAAID,eAAe,KAAKE,kBAAkB;MACzF,CAAC,CAAC,CAAC,CACE9K,SAAS,CAAC,MAAM,IAAI,CAAC+K,mBAAmB,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC9B,aAAa,EAAE/J,WAAW,CAAC,CAAC;MACjC;MACA,IAAI4K,OAAO,CAACvF,SAAS,IAAIuF,OAAO,CAACvF,SAAS,CAACyG,YAAY,EAAE;QACrD,IAAI,CAAC/B,aAAa,GAAGa,OAAO,CAACvF,SAAS,CAACyG,YAAY,CAC9CN,IAAI,CAACrS,SAAS,CAAC,IAAI,CAACuQ,UAAU,CAAC,CAAC,CAChC5I,SAAS,CAAC,MAAM,IAAI,CAAC6F,kBAAkB,CAACgC,YAAY,CAAC,CAAC,CAAC;MAChE;IACJ;IACAoD,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC3C,cAAc,GAAG,CAAC,CAAC,IAAI,CAAClB,eAAe,CAAC8D,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,CAAChN,OAAO,CAAC;MAClE,IAAI,CAACoK,cAAc,GAAG,CAAC,CAAC,IAAI,CAACnB,eAAe,CAAC8D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChN,OAAO,CAAC;MACjE,IAAI,CAACqK,cAAc,GAAG,CAAC,CAAC,IAAI,CAACnB,eAAe,CAAC6D,IAAI,CAACE,CAAC,IAAI,CAACA,CAAC,CAACjN,OAAO,CAAC;MAClE,IAAI,CAACsK,cAAc,GAAG,CAAC,CAAC,IAAI,CAACpB,eAAe,CAAC6D,IAAI,CAACE,CAAC,IAAIA,CAAC,CAACjN,OAAO,CAAC;IACrE;IACA;IACAwL,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAACsB,0BAA0B,CAAC,CAAC;MACjC;MACA;MACA;MACAjT,KAAK,CAAC,IAAI,CAACoP,eAAe,CAACiE,OAAO,EAAE,IAAI,CAAChE,eAAe,CAACgE,OAAO,CAAC,CAACrL,SAAS,CAAC,MAAM;QAC9E,IAAI,CAACiL,0BAA0B,CAAC,CAAC;QACjC,IAAI,CAACpF,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;AACA;IACI6B,oBAAoBA,CAAA,EAAG;MACnB;MACA,IAAI,CAACnC,aAAa,CAAC8D,OAAO,CAACrL,SAAS,CAAC,MAAM;QACvC,IAAI,CAACqI,aAAa,CAAC,CAAC;QACpB,IAAI,CAACxC,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF;MACA,IAAI,CAACP,cAAc,CAAC+D,OAAO,CAACrL,SAAS,CAAC,MAAM;QACxC,IAAI,CAAC+K,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAAClF,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF;MACA,IAAI,CAACyD,cAAc,CAAC,CAAC;MACrB,IAAI,CAACP,mBAAmB,CAAC,CAAC;IAC9B;IACA;IACAtB,uBAAuBA,CAAA,EAAG;MACtB,IAAI,CAAC,IAAI,CAACtP,QAAQ,KAAK,OAAOuC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACnE,MAAM2I,kCAAkC,CAAC,CAAC;MAC9C;IACJ;IACAiE,iBAAiBA,CAAA,EAAG;MAChB;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACnP,QAAQ,CAACqK,OAAO,IAAI,CAAC,IAAI,CAACqE,UAAU,EAAE;QAC3C,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAI,CAACnC,WAAW,EAAEpF,QAAQ,CAAC,CAAC;MAChC,CAAC,MACI,IAAI,CAAC,IAAI,CAACnH,QAAQ,CAACqK,OAAO,KAAK,IAAI,CAACqE,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,IAAI,CAAC,EAAE;QAC9E,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB,IAAI,CAACnC,WAAW,EAAEjF,UAAU,CAAC,CAAC;MAClC;MACA,IAAI,CAAC0E,UAAU,EAAE1G,aAAa,CAAC8B,SAAS,CAACgK,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAACpR,QAAQ,CAACqK,OAAO,CAAC;IACrG;IACAyF,iCAAiC,GAAG,IAAI;IACxC;AACJ;AACA;AACA;AACA;AACA;IACIZ,uBAAuBA,CAAA,EAAG;MACtB1R,iBAAiB,CAAC;QACd6T,SAAS,EAAEA,CAAA,KAAM;UACb,IAAI,IAAI,CAACzD,iBAAiB,CAAC,CAAC,KAAK,SAAS,EAAE;YACxC,IAAI,CAACkC,iCAAiC,EAAEC,UAAU,CAAC,CAAC;YACpD,OAAO,IAAI;UACf;UACA;UACA;UACA,IAAIuB,UAAU,CAACC,cAAc,EAAE;YAC3B,IAAI,CAACzB,iCAAiC,KAAK,IAAIwB,UAAU,CAACC,cAAc,CAAC,MAAM;cAC3E,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAACC,uBAAuB,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC;YACF,KAAK,MAAMC,EAAE,IAAI,IAAI,CAAC9E,uBAAuB,CAAC,CAAC,EAAE;cAC7C,IAAI,CAACkD,iCAAiC,CAACnK,OAAO,CAAC+L,EAAE,EAAE;gBAAE9L,GAAG,EAAE;cAAa,CAAC,CAAC;YAC7E;UACJ;UACA,OAAO,IAAI,CAAC6L,uBAAuB,CAAC,CAAC;QACzC,CAAC;QACDE,KAAK,EAAEC,WAAW,IAAI,IAAI,CAACJ,yBAAyB,CAACI,WAAW,CAAC,CAAC;MACtE,CAAC,CAAC;IACN;IACA;IACAC,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACrE,UAAU,KAAK,QAAQ;IACvC;IACA3N,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAAC8N,UAAU,KAAK,SAAS;IACxC;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI3M,uBAAuBA,CAAA,EAAG;MACtB,OAAO,CAAC,IAAI,CAAC4K,SAAS,CAACkG,SAAS,IAAI,IAAI,CAAC7E,eAAe,CAAC8E,MAAM,IAAI,CAAC,IAAI,CAACnS,iBAAiB,CAAC,CAAC;IAChG;IACAY,iBAAiB,GAAGnD,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAACgQ,WAAW,CAAC,CAAC,CAAC;IACxDzN,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAACY,iBAAiB,CAAC,CAAC,EAAE;QAC3B,OAAO,KAAK;MAChB;MACA,OAAO,IAAI,CAACR,QAAQ,CAACuK,gBAAgB,IAAI,IAAI,CAACsH,kBAAkB,CAAC,CAAC;IACtE;IACA;AACJ;AACA;AACA;IACIG,cAAcA,CAACC,IAAI,EAAE;MACjB,MAAMtC,OAAO,GAAG,IAAI,CAAC3P,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoK,SAAS,GAAG,IAAI;MAC9D,OAAOuF,OAAO,IAAIA,OAAO,CAACsC,IAAI,CAAC;IACnC;IACA;IACAC,wBAAwBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAAC/E,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC4E,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC/R,QAAQ,CAACyK,UAAU,GAClF,OAAO,GACP,MAAM;IAChB;IACA;IACAhF,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC0M,yBAAyB,CAAC,CAAC;IACpC;IACA;IACAA,yBAAyBA,CAAA,EAAG;MACxB,IAAI,CAAC,IAAI,CAACtS,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAACwM,cAAc,IAAI,CAAC,IAAI,CAACzM,iBAAiB,CAAC,CAAC,EAAE;QAC1E,IAAI,CAAC0M,eAAe,EAAElE,cAAc,CAAC,CAAC,CAAC;MAC3C,CAAC,MACI;QACD,IAAI,CAACkE,eAAe,EAAElE,cAAc,CAAC,IAAI,CAACiE,cAAc,CAACjH,QAAQ,CAAC,CAAC,CAAC;MACxE;IACJ;IACA;IACA8I,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACiD,cAAc,CAAC,CAAC;MACrB,IAAI,CAACP,mBAAmB,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIO,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAAC/D,aAAa,KAAK,OAAO7K,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACvE,IAAI6P,SAAS;QACb,IAAIC,OAAO;QACX,IAAI,CAACjF,aAAa,CAACkF,OAAO,CAAEC,IAAI,IAAK;UACjC,IAAIA,IAAI,CAAC/O,KAAK,KAAK,OAAO,EAAE;YACxB,IAAI4O,SAAS,IAAI,IAAI,CAACvQ,SAAS,EAAE;cAC7B,MAAMoJ,kCAAkC,CAAC,OAAO,CAAC;YACrD;YACAmH,SAAS,GAAGG,IAAI;UACpB,CAAC,MACI,IAAIA,IAAI,CAAC/O,KAAK,KAAK,KAAK,EAAE;YAC3B,IAAI6O,OAAO,EAAE;cACT,MAAMpH,kCAAkC,CAAC,KAAK,CAAC;YACnD;YACAoH,OAAO,GAAGE,IAAI;UAClB;QACJ,CAAC,CAAC;MACN;IACJ;IACA;AACJ;AACA;AACA;IACI3B,mBAAmBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC5Q,QAAQ,EAAE;QACf,IAAIwS,GAAG,GAAG,EAAE;QACZ;QACA,IAAI,IAAI,CAACxS,QAAQ,CAAC4K,mBAAmB,IACjC,OAAO,IAAI,CAAC5K,QAAQ,CAAC4K,mBAAmB,KAAK,QAAQ,EAAE;UACvD4H,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,CAACzS,QAAQ,CAAC4K,mBAAmB,CAAC8H,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7D;QACA,IAAI,IAAI,CAACR,wBAAwB,CAAC,CAAC,KAAK,MAAM,EAAE;UAC5C,MAAME,SAAS,GAAG,IAAI,CAAChF,aAAa,GAC9B,IAAI,CAACA,aAAa,CAAC2D,IAAI,CAACwB,IAAI,IAAIA,IAAI,CAAC/O,KAAK,KAAK,OAAO,CAAC,GACvD,IAAI;UACV,MAAM6O,OAAO,GAAG,IAAI,CAACjF,aAAa,GAC5B,IAAI,CAACA,aAAa,CAAC2D,IAAI,CAACwB,IAAI,IAAIA,IAAI,CAAC/O,KAAK,KAAK,KAAK,CAAC,GACrD,IAAI;UACV,IAAI4O,SAAS,EAAE;YACXI,GAAG,CAACC,IAAI,CAACL,SAAS,CAAClS,EAAE,CAAC;UAC1B,CAAC,MACI,IAAI,IAAI,CAAC+N,UAAU,EAAE;YACtBuE,GAAG,CAACC,IAAI,CAAC,IAAI,CAAC9Q,YAAY,CAAC;UAC/B;UACA,IAAI0Q,OAAO,EAAE;YACTG,GAAG,CAACC,IAAI,CAACJ,OAAO,CAACnS,EAAE,CAAC;UACxB;QACJ,CAAC,MACI,IAAI,IAAI,CAACiN,cAAc,EAAE;UAC1BqF,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,CAACtF,cAAc,CAACpP,GAAG,CAAC4U,KAAK,IAAIA,KAAK,CAACzS,EAAE,CAAC,CAAC;QAC3D;QACA,MAAM0S,mBAAmB,GAAG,IAAI,CAAC5S,QAAQ,CAAC6K,cAAc;QACxD,IAAIgI,QAAQ;QACZ;QACA;QACA;QACA;QACA,IAAID,mBAAmB,EAAE;UACrB,MAAME,OAAO,GAAG,IAAI,CAACvE,eAAe,IAAIiE,GAAG;UAC3CK,QAAQ,GAAGL,GAAG,CAACO,MAAM,CAACH,mBAAmB,CAAC3U,MAAM,CAACiC,EAAE,IAAIA,EAAE,IAAI,CAAC4S,OAAO,CAACE,QAAQ,CAAC9S,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MACI;UACD2S,QAAQ,GAAGL,GAAG;QAClB;QACA,IAAI,CAACxS,QAAQ,CAACiT,iBAAiB,CAACJ,QAAQ,CAAC;QACzC,IAAI,CAACtE,eAAe,GAAGiE,GAAG;MAC9B;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIf,uBAAuBA,CAAA,EAAG;MACtB,MAAMyB,GAAG,GAAG,IAAI,CAACvH,IAAI,CAACwH,WAAW,CAAC,CAAC;MACnC,IAAI,CAAC,IAAI,CAACtT,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAACwM,cAAc,EAAE;QAC7C,OAAO,IAAI;MACf;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,oBAAoB,IAAI,CAAC,IAAI,CAACC,oBAAoB,EAAE;QAC1D,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC;MACrB;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACkH,gBAAgB,CAAC,CAAC,EAAE;QAC1B,OAAO,IAAI;MACf;MACA,MAAMC,mBAAmB,GAAG,IAAI,CAACpH,oBAAoB,EAAE3G,aAAa;MACpE,MAAMgO,mBAAmB,GAAG,IAAI,CAACpH,oBAAoB,EAAE5G,aAAa;MACpE,MAAMiO,mBAAmB,GAAG,IAAI,CAACpH,oBAAoB,EAAE7G,aAAa;MACpE,MAAMkO,mBAAmB,GAAG,IAAI,CAACpH,oBAAoB,EAAE9G,aAAa;MACpE,MAAMmO,wBAAwB,GAAGJ,mBAAmB,EAAEK,qBAAqB,CAAC,CAAC,CAACnL,KAAK,IAAI,CAAC;MACxF,MAAMoL,wBAAwB,GAAGL,mBAAmB,EAAEI,qBAAqB,CAAC,CAAC,CAACnL,KAAK,IAAI,CAAC;MACxF,MAAMqL,wBAAwB,GAAGL,mBAAmB,EAAEG,qBAAqB,CAAC,CAAC,CAACnL,KAAK,IAAI,CAAC;MACxF,MAAMsL,wBAAwB,GAAGL,mBAAmB,EAAEE,qBAAqB,CAAC,CAAC,CAACnL,KAAK,IAAI,CAAC;MACxF;MACA;MACA,MAAMuL,MAAM,GAAGZ,GAAG,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG;MACzC,MAAMa,WAAW,GAAG,GAAGN,wBAAwB,GAAGE,wBAAwB,IAAI;MAC9E,MAAMK,WAAW,GAAG,+CAA+C;MACnE,MAAMC,qBAAqB,GAAG,QAAQH,MAAM,OAAOC,WAAW,MAAMC,WAAW,IAAI;MACnF;MACA;MACA;MACA,MAAME,sBAAsB,GAAG,4CAA4C,GACvE,GAAG1I,uCAAuC,eAAeyI,qBAAqB,IAAI;MACtF;MACA,MAAME,mBAAmB,GAAGV,wBAAwB,GAChDE,wBAAwB,GACxBC,wBAAwB,GACxBC,wBAAwB;MAC5B,OAAO,CAACK,sBAAsB,EAAEC,mBAAmB,CAAC;IACxD;IACA;IACA3C,yBAAyBA,CAAC4C,MAAM,EAAE;MAC9B,IAAIA,MAAM,KAAK,IAAI,EAAE;QACjB,MAAM,CAACF,sBAAsB,EAAEC,mBAAmB,CAAC,GAAGC,MAAM;QAC5D,IAAI,IAAI,CAAC/H,cAAc,EAAE;UACrB,IAAI,CAACA,cAAc,CAAC9G,OAAO,CAACc,KAAK,CAACgO,SAAS,GAAGH,sBAAsB;QACxE;QACA,IAAIC,mBAAmB,KAAK,IAAI,EAAE;UAC9B,IAAI,CAAC7H,eAAe,EAAE5D,YAAY,CAACyL,mBAAmB,CAAC;QAC3D;MACJ;IACJ;IACA;IACAf,gBAAgBA,CAAA,EAAG;MACf,MAAM7N,OAAO,GAAG,IAAI,CAAChB,WAAW,CAACe,aAAa;MAC9C,IAAIC,OAAO,CAAC+O,WAAW,EAAE;QACrB,MAAMC,QAAQ,GAAGhP,OAAO,CAAC+O,WAAW,CAAC,CAAC;QACtC;QACA;QACA,OAAOC,QAAQ,IAAIA,QAAQ,KAAKhP,OAAO;MAC3C;MACA;MACA;MACA,OAAOgB,QAAQ,CAACC,eAAe,CAACiB,QAAQ,CAAClC,OAAO,CAAC;IACrD;IACA,OAAOvD,IAAI,YAAAwS,qBAAAtS,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuJ,YAAY;IAAA;IAC/G,OAAO5C,IAAI,kBAt+B8EtM,EAAE,CAAAuM,iBAAA;MAAAzG,IAAA,EAs+BJoJ,YAAY;MAAAnJ,SAAA;MAAAmS,cAAA,WAAAC,4BAAAzV,EAAA,EAAAC,GAAA,EAAAyV,QAAA;QAAA,IAAA1V,EAAA;UAt+BV1C,EAAE,CAAAqY,oBAAA,CAAAD,QAAA,EAAAzV,GAAA,CAAAmO,WAAA,EAy+BnBtL,QAAQ;UAz+BSxF,EAAE,CAAAsY,cAAA,CAAAF,QAAA,EAy+ByF1K,mBAAmB;UAz+B9G1N,EAAE,CAAAsY,cAAA,CAAAF,QAAA,EAy+BiL/Q,UAAU;UAz+B7LrH,EAAE,CAAAsY,cAAA,CAAAF,QAAA,EAy+BgQzQ,UAAU;UAz+B5Q3H,EAAE,CAAAsY,cAAA,CAAAF,QAAA,EAy+B8UnS,SAAS;UAz+BzVjG,EAAE,CAAAsY,cAAA,CAAAF,QAAA,EAy+B0ZpR,OAAO;QAAA;QAAA,IAAAtE,EAAA;UAz+Bna1C,EAAE,CAAAuY,cAAA;UAAA,IAAA5L,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAA8N,iBAAA,GAAA9D,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAA+N,eAAA,GAAA/D,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAgO,eAAA,GAAAhE,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAiO,cAAA,GAAAjE,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAkO,aAAA,GAAAlE,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAgM,mBAAA9V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAyY,iBAAA,CAAA9V,GAAA,CAAAsN,0BAAA,EAAA/N,GAAA;UAAFlC,EAAE,CAAAyY,iBAAA,CAAA9V,GAAA,CAAAuN,0BAAA,EAAA/N,GAAA;UAAFnC,EAAE,CAAAyY,iBAAA,CAAA9V,GAAA,CAAAwN,0BAAA,EAAA/N,GAAA;UAAFpC,EAAE,CAAAyY,iBAAA,CAAA9V,GAAA,CAAAyN,0BAAA,EAAA/N,GAAA;UAAFrC,EAAE,CAAA0M,WAAA,CAAApK,GAAA;UAAFtC,EAAE,CAAA0M,WAAA,CAAAxK,GAAA;UAAFlC,EAAE,CAAA0M,WAAA,CAAAvK,GAAA;UAAFnC,EAAE,CAAA0M,WAAA,CAAAtK,GAAA;UAAFpC,EAAE,CAAA0M,WAAA,CAAArK,GAAA;UAAFrC,EAAE,CAAA0M,WAAA,CAy+BmiD3E,yBAAyB;UAz+B9jD/H,EAAE,CAAA0M,WAAA,CAy+B8oDrB,0BAA0B;UAz+B1qDrL,EAAE,CAAA0M,WAAA,CAy+BsvDpC,sBAAsB;QAAA;QAAA,IAAA5H,EAAA;UAz+B9wD1C,EAAE,CAAAuY,cAAA;UAAA,IAAA5L,EAAA;UAAF3M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAA8M,UAAA,GAAA9C,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAA+M,oBAAA,GAAA/C,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAgN,oBAAA,GAAAhD,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAiN,oBAAA,GAAAjD,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAkN,oBAAA,GAAAlD,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAmN,cAAA,GAAAnD,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAoN,eAAA,GAAApD,EAAA,CAAAG,KAAA;UAAF9M,EAAE,CAAA4M,cAAA,CAAAD,EAAA,GAAF3M,EAAE,CAAA6M,WAAA,QAAAlK,GAAA,CAAAqN,WAAA,GAAArD,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAxG,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAkS,0BAAAhW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAoH,WAAA,0CAs+BJzE,GAAA,CAAA2S,kBAAA,CAAmB,CAAR,CAAC,uCAAA3S,GAAA,CAAAiP,cAAD,CAAC,uCAAAjP,GAAA,CAAAmP,cAAD,CAAC,2BAAAnP,GAAA,CAAAc,QAAA,CAAAyK,UAAD,CAAC,4BAAAvL,GAAA,CAAAc,QAAA,CAAAwK,QAAD,CAAC,8BAAAtL,GAAA,CAAAc,QAAA,CAAA2K,UAAD,CAAC,mCAAAzL,GAAA,CAAAyO,UAAA,IAAE,MAAH,CAAC,sCAAAzO,GAAA,CAAAyO,UAAA,IAAE,SAAH,CAAC,oCAAZzO,GAAA,CAAAsB,iBAAA,CAAkB,CAAC,KAAKtB,GAAA,CAAAU,iBAAA,CAAkB,CAA/B,CAAC,gBAAAV,GAAA,CAAAc,QAAA,CAAAqK,OAAD,CAAC,gBAAAnL,GAAA,CAAAqO,KAAA,KAAF,QAAQ,IAAArO,GAAA,CAAAqO,KAAA,KAAc,MAArB,CAAC,eAAArO,GAAA,CAAAqO,KAAA,KAAF,QAAC,CAAC,aAAArO,GAAA,CAAAqO,KAAA,KAAF,MAAC,CAAC,iBAAZrO,GAAA,CAAA8S,cAAA,CAAe,WAAW,CAAf,CAAC,eAAZ9S,GAAA,CAAA8S,cAAA,CAAe,SAAS,CAAb,CAAC,gBAAZ9S,GAAA,CAAA8S,cAAA,CAAe,UAAU,CAAd,CAAC,aAAZ9S,GAAA,CAAA8S,cAAA,CAAe,OAAO,CAAX,CAAC,aAAZ9S,GAAA,CAAA8S,cAAA,CAAe,OAAO,CAAX,CAAC,eAAZ9S,GAAA,CAAA8S,cAAA,CAAe,SAAS,CAAb,CAAC,eAAZ9S,GAAA,CAAA8S,cAAA,CAAe,SAAS,CAAb,CAAC;QAAA;MAAA;MAAA9O,MAAA;QAAA7C,kBAAA;QAAAkN,KAAA;QAAAC,UAAA;QAAAG,UAAA;QAAAI,eAAA;QAAAlM,SAAA;MAAA;MAAAqT,QAAA;MAAA/R,QAAA,GAt+BV5G,EAAE,CAAA6G,kBAAA,CAs+B06C,CAC7/C;QAAEC,OAAO,EAAE8H,cAAc;QAAE7H,WAAW,EAAEmI;MAAa,CAAC,EACtD;QAAEpI,OAAO,EAAEgB,qBAAqB;QAAEf,WAAW,EAAEmI;MAAa,CAAC,CAChE;MAAAjC,kBAAA,EAAAzK,GAAA;MAAA0K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuL,sBAAAlW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAmW,GAAA,GAz+BoF7Y,EAAE,CAAA8Y,gBAAA;UAAF9Y,EAAE,CAAAuN,eAAA,CAAAhL,GAAA;UAAFvC,EAAE,CAAAqE,UAAA,IAAAL,mCAAA,gCAAFhE,EAAE,CAAA+Y,sBAy+Bo3D,CAAC;UAz+Bv3D/Y,EAAE,CAAA8C,cAAA,eAy+B6gI,CAAC;UAz+BhhI9C,EAAE,CAAAgZ,UAAA,mBAAAC,2CAAAC,MAAA;YAAFlZ,EAAE,CAAAmZ,aAAA,CAAAN,GAAA;YAAA,OAAF7Y,EAAE,CAAAoZ,WAAA,CAy+Bw+HzW,GAAA,CAAAc,QAAA,CAAA4V,gBAAA,CAAAH,MAAgC,CAAC;UAAA,CAAC,CAAC;UAz+B7gIlZ,EAAE,CAAAgD,mBAAA,IAAAkB,mCAAA,gBAy+B6jI,CAAC;UAz+BhkIlE,EAAE,CAAA8C,cAAA,YAy+ByqI,CAAC;UAz+B5qI9C,EAAE,CAAAgD,mBAAA,IAAAwB,mCAAA,gBAy+BosI,CAAC;UAz+BvsIxE,EAAE,CAAAgD,mBAAA,IAAA0B,mCAAA,iBAy+B29I,CAAC;UAz+B99I1E,EAAE,CAAAgD,mBAAA,IAAA2B,mCAAA,iBAy+BoqJ,CAAC;UAz+BvqJ3E,EAAE,CAAA8C,cAAA,aAy+Bk3J,CAAC;UAz+Br3J9C,EAAE,CAAAgD,mBAAA,KAAA6B,oCAAA,gBAy+B66J,CAAC;UAz+Bh7J7E,EAAE,CAAA+C,YAAA,GAy+BmiK,CAAC;UAz+BtiK/C,EAAE,CAAAiD,YAAA,CAy+B+iK,CAAC;UAz+BljKjD,EAAE,CAAAgD,mBAAA,KAAA8B,oCAAA,iBAy+B6kK,CAAC;UAz+BhlK9E,EAAE,CAAAgD,mBAAA,KAAA+B,oCAAA,iBAy+BywK,CAAC;UAz+B5wK/E,EAAE,CAAAiD,YAAA,CAy+B87K,CAAC;UAz+Bj8KjD,EAAE,CAAAgD,mBAAA,KAAAgC,oCAAA,iBAy+B09K,CAAC;UAz+B79KhF,EAAE,CAAAiD,YAAA,CAy+B+gL,CAAC;UAz+BlhLjD,EAAE,CAAA8C,cAAA,cAy+BusL,CAAC,cAA+hB,CAAC;UAz+B1uM9C,EAAE,CAAAgD,mBAAA,KAAAiC,6BAAA,MAy+BsyM,CAAC,KAAAM,6BAAA,MAAsG,CAAC;UAz+Bh5MvF,EAAE,CAAAiD,YAAA,CAy+B6tN,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAP,EAAA;UAAA,IAAA4W,QAAA;UAz+BxuNtZ,EAAE,CAAA4D,SAAA,EAy+BivH,CAAC;UAz+BpvH5D,EAAE,CAAAoH,WAAA,4BAAAzE,GAAA,CAAAW,WAAA,EAy+BivH,CAAC,6BAAAX,GAAA,CAAAW,WAAA,EAAqD,CAAC,8BAAAX,GAAA,CAAAsB,iBAAA,EAA4D,CAAC,6BAAAtB,GAAA,CAAAc,QAAA,CAAAwK,QAAyD,CAAC,4BAAAtL,GAAA,CAAAc,QAAA,CAAAyK,UAA0D,CAAC;UAz+B59HlO,EAAE,CAAA4D,SAAA,EAy+B8nI,CAAC;UAz+BjoI5D,EAAE,CAAA6D,aAAA,EAAAlB,GAAA,CAAAW,WAAA,OAAAX,GAAA,CAAAc,QAAA,CAAAwK,QAAA,SAy+B8nI,CAAC;UAz+BjoIjO,EAAE,CAAA4D,SAAA,EAy+B67I,CAAC;UAz+Bh8I5D,EAAE,CAAA6D,aAAA,CAAAlB,GAAA,CAAAW,WAAA,WAy+B67I,CAAC;UAz+Bh8ItD,EAAE,CAAA4D,SAAA,CAy+BsoJ,CAAC;UAz+BzoJ5D,EAAE,CAAA6D,aAAA,CAAAlB,GAAA,CAAAiP,cAAA,SAy+BsoJ,CAAC;UAz+BzoJ5R,EAAE,CAAA4D,SAAA,CAy+Bk0J,CAAC;UAz+Br0J5D,EAAE,CAAA6D,aAAA,CAAAlB,GAAA,CAAAkP,cAAA,SAy+Bk0J,CAAC;UAz+Br0J7R,EAAE,CAAA4D,SAAA,EAy+BggK,CAAC;UAz+BngK5D,EAAE,CAAA6D,aAAA,EAAAlB,GAAA,CAAAW,WAAA,MAAAX,GAAA,CAAA8B,uBAAA,YAy+BggK,CAAC;UAz+BngKzE,EAAE,CAAA4D,SAAA,EAy+B2uK,CAAC;UAz+B9uK5D,EAAE,CAAA6D,aAAA,CAAAlB,GAAA,CAAAoP,cAAA,UAy+B2uK,CAAC;UAz+B9uK/R,EAAE,CAAA4D,SAAA,CAy+Bo7K,CAAC;UAz+Bv7K5D,EAAE,CAAA6D,aAAA,CAAAlB,GAAA,CAAAmP,cAAA,UAy+Bo7K,CAAC;UAz+Bv7K9R,EAAE,CAAA4D,SAAA,CAy+BugL,CAAC;UAz+B1gL5D,EAAE,CAAA6D,aAAA,EAAAlB,GAAA,CAAAW,WAAA,YAy+BugL,CAAC;UAz+B1gLtD,EAAE,CAAA4D,SAAA,CAy+BosL,CAAC;UAz+BvsL5D,EAAE,CAAAoH,WAAA,8CAAAzE,GAAA,CAAA6O,eAAA,cAy+BosL,CAAC;UAAA,MAAA+H,uBAAA,GAAmC5W,GAAA,CAAAgT,wBAAA,CAAyB,CAAC;UAz+BpwL3V,EAAE,CAAA4D,SAAA,CAy+B6oM,CAAC;UAz+BhpM5D,EAAE,CAAAoH,WAAA,qCAAAmS,uBAAA,YAy+B6oM,CAAC,oCAAAA,uBAAA,WAAkF,CAAC;UAz+BnuMvZ,EAAE,CAAA4D,SAAA,CAy+BmtN,CAAC;UAz+BttN5D,EAAE,CAAA6D,aAAA,EAAAyV,QAAA,GAAAC,uBAAA,MAy+B8uM,OAAO,QAAAD,QAAA,KAAP,MAAM,UAA+d,CAAC;QAAA;MAAA;MAAAE,YAAA,GAA4xiCzR,yBAAyB,EAAoHsD,0BAA0B,EAAwHtL,gBAAgB,EAAoJuK,sBAAsB,EAAwEtD,OAAO;MAAA6Q,MAAA;MAAArK,aAAA;MAAAC,eAAA;IAAA;EACvnxC;EAAC,OAviBKyB,YAAY;AAAA;AAwiBlB;EAAA,QAAAlJ,SAAA,oBAAAA,SAAA;AAAA;AA0FA,SAASR,QAAQ,IAAIiU,CAAC,EAAExT,SAAS,IAAIyT,CAAC,EAAExT,QAAQ,IAAIyT,CAAC,EAAE3S,OAAO,IAAI4S,CAAC,EAAEvS,UAAU,IAAIwS,CAAC,EAAEvS,SAAS,IAAIiJ,CAAC,EAAE5I,UAAU,IAAImS,CAAC,EAAElS,SAAS,IAAImS,CAAC,EAAEnL,cAAc,IAAIoL,CAAC,EAAEnL,8BAA8B,IAAIoL,CAAC,EAAE/K,YAAY,IAAIgL,CAAC,EAAExM,mBAAmB,IAAIyM,CAAC,EAAE3L,uCAAuC,IAAI4L,CAAC,EAAE1L,kCAAkC,IAAI2L,CAAC,EAAE1L,kCAAkC,IAAI2L,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}