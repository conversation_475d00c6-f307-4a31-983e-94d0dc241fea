using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class ProductStockConfiguration : IEntityTypeConfiguration<ProductStock>
    {
        public void Configure(EntityTypeBuilder<ProductStock> builder)
        {
            builder.ToTable("ProductStocks");

            builder.<PERSON><PERSON>ey(ps => ps.Id);

            builder.Property(ps => ps.AvailableQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.ReservedQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.OnOrderQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.OpeningQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.TotalInQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.TotalOutQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.AverageCostPrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.LastCostPrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.StockValue)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(ps => ps.BranchMinStock)
                .HasColumnType("decimal(18,3)");

            builder.Property(ps => ps.BranchMaxStock)
                .HasColumnType("decimal(18,3)");

            builder.Property(ps => ps.BranchReorderPoint)
                .HasColumnType("decimal(18,3)");

            builder.Property(ps => ps.StorageLocation)
                .HasMaxLength(100);

            builder.Property(ps => ps.ShelfNumber)
                .HasMaxLength(20);

            builder.Property(ps => ps.StockNotes)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(ps => ps.Product)
                .WithMany(p => p.Stock)
                .HasForeignKey(ps => ps.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ps => ps.Branch)
                .WithMany(b => b.ProductStocks)
                .HasForeignKey(ps => ps.BranchId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(ps => new { ps.ProductId, ps.BranchId })
                .IsUnique()
                .HasDatabaseName("IX_ProductStocks_ProductId_BranchId");

            builder.HasIndex(ps => new { ps.BranchId, ps.AvailableQuantity })
                .HasDatabaseName("IX_ProductStocks_BranchId_AvailableQuantity");

            builder.HasIndex(ps => ps.IsAvailableForSale)
                .HasDatabaseName("IX_ProductStocks_IsAvailableForSale");
        }
    }

    public class StockMovementConfiguration : IEntityTypeConfiguration<StockMovement>
    {
        public void Configure(EntityTypeBuilder<StockMovement> builder)
        {
            builder.ToTable("StockMovements");

            builder.HasKey(sm => sm.Id);

            builder.Property(sm => sm.MovementNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(sm => sm.Quantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(sm => sm.UnitPrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(sm => sm.TotalValue)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(sm => sm.BalanceBefore)
                .HasColumnType("decimal(18,3)");

            builder.Property(sm => sm.BalanceAfter)
                .HasColumnType("decimal(18,3)");

            builder.Property(sm => sm.Reference)
                .HasMaxLength(50);

            builder.Property(sm => sm.Description)
                .HasMaxLength(500);

            builder.Property(sm => sm.BatchNumber)
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(sm => sm.Product)
                .WithMany(p => p.StockMovements)
                .HasForeignKey(sm => sm.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sm => sm.Branch)
                .WithMany()
                .HasForeignKey(sm => sm.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sm => sm.FromBranch)
                .WithMany()
                .HasForeignKey(sm => sm.FromBranchId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(sm => sm.ToBranch)
                .WithMany()
                .HasForeignKey(sm => sm.ToBranchId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(sm => sm.User)
                .WithMany(u => u.StockMovements)
                .HasForeignKey(sm => sm.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sm => sm.ConfirmedBy)
                .WithMany()
                .HasForeignKey(sm => sm.ConfirmedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(sm => sm.MovementNumber)
                .IsUnique()
                .HasDatabaseName("IX_StockMovements_MovementNumber");

            builder.HasIndex(sm => new { sm.ProductId, sm.MovementDate })
                .HasDatabaseName("IX_StockMovements_ProductId_MovementDate");

            builder.HasIndex(sm => new { sm.BranchId, sm.MovementDate })
                .HasDatabaseName("IX_StockMovements_BranchId_MovementDate");

            builder.HasIndex(sm => sm.MovementType)
                .HasDatabaseName("IX_StockMovements_MovementType");

            builder.HasIndex(sm => new { sm.ReferenceType, sm.ReferenceId })
                .HasDatabaseName("IX_StockMovements_ReferenceType_ReferenceId");
        }
    }

    public class ProductBatchConfiguration : IEntityTypeConfiguration<ProductBatch>
    {
        public void Configure(EntityTypeBuilder<ProductBatch> builder)
        {
            builder.ToTable("ProductBatches");

            builder.HasKey(pb => pb.Id);

            builder.Property(pb => pb.BatchNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(pb => pb.OriginalQuantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(pb => pb.AvailableQuantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(pb => pb.ReservedQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(pb => pb.CostPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(pb => pb.StorageLocation)
                .HasMaxLength(100);

            builder.Property(pb => pb.Notes)
                .HasMaxLength(500);

            builder.Property(pb => pb.QualityCertificateNumber)
                .HasMaxLength(50);

            builder.Property(pb => pb.StorageTemperature)
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(pb => pb.Product)
                .WithMany()
                .HasForeignKey(pb => pb.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(pb => pb.Branch)
                .WithMany()
                .HasForeignKey(pb => pb.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(pb => pb.Purchase)
                .WithMany()
                .HasForeignKey(pb => pb.PurchaseId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(pb => pb.Supplier)
                .WithMany()
                .HasForeignKey(pb => pb.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(pb => pb.ProductStock)
                .WithMany(ps => ps.Batches)
                .HasForeignKey(pb => new { pb.ProductId, pb.BranchId })
                .HasPrincipalKey(ps => new { ps.ProductId, ps.BranchId })
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(pb => new { pb.ProductId, pb.BranchId, pb.BatchNumber })
                .IsUnique()
                .HasDatabaseName("IX_ProductBatches_ProductId_BranchId_BatchNumber");

            builder.HasIndex(pb => pb.ExpiryDate)
                .HasDatabaseName("IX_ProductBatches_ExpiryDate");

            builder.HasIndex(pb => pb.Status)
                .HasDatabaseName("IX_ProductBatches_Status");

            builder.HasIndex(pb => new { pb.IsExpired, pb.ExpiryDate })
                .HasDatabaseName("IX_ProductBatches_IsExpired_ExpiryDate");
        }
    }
}
