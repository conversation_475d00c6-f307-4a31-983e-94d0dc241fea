import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from '../../../../environments/environment';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

// Interfaces
interface SupplierType {
  Id: number;
  NameAr: string;
  NameEn: string;
}

interface Area {
  Id: number;
  NameAr: string;
  NameEn: string;
  Code: string;
}

interface Country {
  Id: number;
  NameAr: string;
  NameEn: string;
  Code: string;
  PhoneCode: string;
}

interface CreateSupplierRequest {
  supplierCode: string;
  nameAr: string;
  nameEn?: string;
  supplierTypeId: number;
  phone1: string;
  phone2?: string;
  email?: string;
  website?: string;
  address?: string;
  areaId?: number;
  countryId?: number;
  contactPersonName?: string;
  contactPersonPhone?: string;
  contactPersonEmail?: string;
  paymentTerms: number;
  deliveryDays: number;
  creditLimit: number;
  openingBalance: number;
  taxNumber?: string;
  commercialRegister?: string;
  bankName?: string;
  bankAccountNumber?: string;
  iban?: string;
  rating?: number;
  notes?: string;
  isActive: boolean;
}

@Component({
  selector: 'app-add-supplier',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule
  ],
  templateUrl: './add-supplier.component.html',
  styleUrls: ['./add-supplier.component.scss']
})
export class AddSupplierComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  supplierForm: FormGroup;

  // API URL
  private apiUrl = `${environment.apiUrl}/simple`;

  // Data
  supplierTypes: SupplierType[] = [];
  areas: Area[] = [];
  countries: Country[] = [];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {
    this.supplierForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
    this.generateSupplierCode(); // توليد الكود عند فتح الصفحة
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Arabic names to English mapping for common names
  private arabicNamesMap: { [key: string]: string } = {
    'محمد': 'Mohamed',
    'أحمد': 'Ahmed',
    'علي': 'Ali',
    'حسن': 'Hassan',
    'حسين': 'Hussein',
    'عبد الله': 'Abdullah',
    'عبدالله': 'Abdullah',
    'عبد الرحمن': 'Abdulrahman',
    'عبدالرحمن': 'Abdulrahman',
    'عبد العزيز': 'Abdulaziz',
    'عبدالعزيز': 'Abdulaziz',
    'خالد': 'Khaled',
    'سعد': 'Saad',
    'فهد': 'Fahd',
    'عمر': 'Omar',
    'يوسف': 'Youssef',
    'إبراهيم': 'Ibrahim',
    'عثمان': 'Othman',
    'صالح': 'Saleh',
    'ناصر': 'Nasser',
    'الصعيدي': 'Alsaydy',
    'للادوات': 'Lladwat',
    'المنزلية': 'Almnzlyh',
    'للتجارة': 'Lltgara',
    'والتوزيع': 'Waltwze',
    'شركة': 'Company',
    'مؤسسة': 'Foundation',
    'متجر': 'Store',
    'محل': 'Shop'
  };

  // Arabic to English character mapping for fallback
  private arabicToEnglishMap: { [key: string]: string } = {
    'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',
    'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'g',
    'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'th',
    'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',
    'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',
    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
    'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',
    'ة': 'h', 'ء': 'a', 'ئ': 'e', 'ؤ': 'o',
    ' ': ' ', '-': '-', '_': '_'
  };

  onArabicNameChange(event: any): void {
    const arabicText = event.target.value;
    const englishText = this.translateArabicToEnglish(arabicText);
    this.supplierForm.patchValue({ nameEn: englishText });
  }

  private translateArabicToEnglish(arabicText: string): string {
    if (!arabicText) return '';

    let englishText = arabicText.trim();

    // First, try to translate using the names mapping
    const words = englishText.split(/\s+/);
    const translatedWords: string[] = [];

    for (let word of words) {
      const cleanWord = word.trim();
      if (this.arabicNamesMap[cleanWord]) {
        translatedWords.push(this.arabicNamesMap[cleanWord]);
      } else {
        // Fallback to character-by-character translation
        let translatedWord = '';
        for (let char of cleanWord) {
          translatedWord += this.arabicToEnglishMap[char] || char;
        }
        translatedWords.push(translatedWord);
      }
    }

    englishText = translatedWords.join(' ');

    // Clean up and capitalize
    englishText = englishText.replace(/\s+/g, ' ').trim();
    return englishText.replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      supplierCode: ['', [Validators.required]], // سيتم توليده تلقائياً عند فتح الصفحة
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required]],
      supplierTypeId: [null, [Validators.required]],
      phone1: ['', [Validators.required, Validators.pattern(/^[+]?[0-9\s\-\(\)]+$/)]],
      phone2: [''],
      email: ['', [Validators.email]],
      website: [''],
      address: [''],
      areaId: [''],
      countryId: [''],
      contactPersonName: [''],
      contactPersonPhone: [''],
      contactPersonEmail: ['', [Validators.email]],
      paymentTerms: [30, [Validators.min(0)]],
      deliveryDays: [7, [Validators.required, Validators.min(1)]], // مدة التوريد بالأيام
      creditLimit: [0, [Validators.min(0)]],
      openingBalance: [0],
      taxNumber: [''],
      commercialRegister: [''],
      bankName: [''],
      bankAccountNumber: [''],
      iban: [''],
      rating: [''],
      notes: [''],
      isActive: [true]
    });
  }

  /**
   * Load initial data
   */
  private loadInitialData(): void {
    Promise.all([
      this.loadSupplierTypes(),
      this.loadAreas(),
      this.loadCountries()
    ]).catch(error => {
      console.error('Error loading initial data:', error);
      this.showError('خطأ في تحميل البيانات الأولية');
    });
  }

  /**
   * Load supplier types
   */
  private loadSupplierTypes(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${this.apiUrl}/supplier-types`).subscribe({
        next: (response) => {
          console.log('Supplier types response:', response);
          this.supplierTypes = response.supplierTypes || [];
          console.log('Loaded supplier types:', this.supplierTypes);
          resolve();
        },
        error: (error) => {
          console.error('Error loading supplier types:', error);
          this.showError('خطأ في تحميل أنواع الموردين');
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load areas
   */
  private loadAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${this.apiUrl}/areas-db`).subscribe({
        next: (response) => {
          console.log('Areas response:', response);
          this.areas = response.areas || [];
          console.log('Loaded areas:', this.areas);
          resolve();
        },
        error: (error) => {
          console.error('Error loading areas:', error);
          this.showError('خطأ في تحميل المحافظات');
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load countries
   */
  private loadCountries(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${this.apiUrl}/countries`).subscribe({
        next: (response) => {
          console.log('Countries response:', response);
          this.countries = response.countries || [];
          console.log('Loaded countries:', this.countries);
          resolve();
        },
        error: (error) => {
          console.error('Error loading countries:', error);
          this.showError('خطأ في تحميل البلدان');
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Generate supplier code
   */
  generateSupplierCode(): void {
    this.isLoading = true;
    const sub = this.http.get<any>(`${this.apiUrl}/next-supplier-code`).subscribe({
      next: (response) => {
        console.log('Next supplier code response:', response);
        this.supplierForm.patchValue({
          supplierCode: response.nextCode
        });
        console.log('Set supplier code:', response.nextCode);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error generating supplier code:', error);
        this.showError('خطأ في توليد كود المورد');
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Save supplier
   */
  saveSupplier(): void {
    if (this.supplierForm.valid) {
      this.isLoading = true;

      const formValue = this.supplierForm.value;
      const request: CreateSupplierRequest = {
        supplierCode: formValue.supplierCode,
        nameAr: formValue.nameAr,
        nameEn: formValue.nameEn || null,
        supplierTypeId: formValue.supplierTypeId,
        phone1: formValue.phone1,
        phone2: formValue.phone2 || null,
        email: formValue.email || null,
        website: formValue.website || null,
        address: formValue.address || null,
        areaId: formValue.areaId || null,
        countryId: formValue.countryId || null,
        contactPersonName: formValue.contactPersonName || null,
        contactPersonPhone: formValue.contactPersonPhone || null,
        contactPersonEmail: formValue.contactPersonEmail || null,
        paymentTerms: formValue.paymentTerms,
        deliveryDays: formValue.deliveryDays,
        creditLimit: formValue.creditLimit,
        openingBalance: formValue.openingBalance,
        taxNumber: formValue.taxNumber || null,
        commercialRegister: formValue.commercialRegister || null,
        bankName: formValue.bankName || null,
        bankAccountNumber: formValue.bankAccountNumber || null,
        iban: formValue.iban || null,
        rating: formValue.rating || null,
        notes: formValue.notes || null,
        isActive: formValue.isActive
      };

      const sub = this.http.post<any>(`${this.apiUrl}/suppliers`, request).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccess('تم إضافة المورد بنجاح');
          this.router.navigate(['/suppliers']);
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error saving supplier:', error);
          this.showError('خطأ في حفظ بيانات المورد');
        }
      });
      this.subscriptions.push(sub);
    } else {
      this.markFormGroupTouched();
      this.showError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.supplierForm.controls).forEach(key => {
      const control = this.supplierForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Go back to suppliers list
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Cancel and go back
   */
  cancel(): void {
    this.goBack();
  }

  /**
   * Open supplier types page
   */
  openAddSupplierTypeDialog(): void {
    this.router.navigate(['/suppliers/types']);
  }

  /**
   * Open areas page
   */
  openAddAreaDialog(): void {
    this.router.navigate(['/suppliers/areas']);
  }

  /**
   * Open countries page
   */
  openAddCountryDialog(): void {
    this.router.navigate(['/suppliers/countries']);
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }


}
