/* Terra Retail ERP - Professional Sidebar */

.sidebar-container {
  height: 100vh;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  width: 280px;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1000;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  direction: rtl;

  &.collapsed {
    width: 70px;
  }
}

/* Header */
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  font-size: 2rem !important;
  color: #3498db;
  background: rgba(52, 152, 219, 0.2);
  padding: 0.5rem;
  border-radius: 10px;
}

.logo-text h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
}

.logo-text span {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.sidebar-toggle {
  color: rgba(255, 255, 255, 0.8);

  &:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
  }
}

/* User Info */
.user-info {
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem;
  border-radius: 10px;
}

.user-avatar mat-icon {
  font-size: 2.5rem !important;
  color: #3498db;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: white;
}

.user-branch {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
}

.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 0.5rem 1rem;
  padding: 0.5rem 0;
}

.sub-section {
  margin-top: 1rem;
  padding-right: 1rem;
  border-right: 2px solid rgba(255, 255, 255, 0.1);

  .sub-title {
    font-size: 0.7rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.4);
    margin: 0 0 0.5rem 1rem;
    padding: 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .sub-item {
    padding-right: 2rem;
    font-size: 0.875rem;

    mat-icon {
      font-size: 1.125rem;
      width: 1.125rem;
      height: 1.125rem;
    }
  }
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  margin: 0.2rem 0.5rem;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  justify-content: flex-start;
  font-size: 0.9rem;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-2px);
  }

  &.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);

    mat-icon {
      color: white;
    }
  }

  mat-icon {
    font-size: 1.3rem !important;
    width: 1.3rem;
    height: 1.3rem;
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
  }

  span {
    font-weight: 500;
  }
}

/* Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: translateY(-1px);
  }

  mat-icon {
    font-size: 1.2rem !important;
  }
}

/* Collapsed State */
.sidebar-container.collapsed {
  .logo-text,
  .user-details,
  .nav-section-title,
  .nav-item span,
  .logout-btn span {
    display: none;
  }

  .user-info {
    justify-content: center;
    margin: 1rem 0.5rem;
  }

  .nav-item {
    justify-content: center;
    padding: 0.75rem;
    margin: 0.2rem 0.5rem;
  }

  .logout-btn {
    justify-content: center;
    padding: 0.75rem;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar-container {
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.mobile-open {
      transform: translateX(0);
    }
  }
}

/* Animation for nav items */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.nav-item {
  animation: slideInRight 0.3s ease forwards;
}

.nav-section:nth-child(1) .nav-item { animation-delay: 0.1s; }
.nav-section:nth-child(2) .nav-item { animation-delay: 0.2s; }
.nav-section:nth-child(3) .nav-item { animation-delay: 0.3s; }
.nav-section:nth-child(4) .nav-item { animation-delay: 0.4s; }
.nav-section:nth-child(5) .nav-item { animation-delay: 0.5s; }
.nav-section:nth-child(6) .nav-item { animation-delay: 0.6s; }