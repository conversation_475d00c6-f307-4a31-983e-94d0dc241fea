<!-- Terra Retail ERP - Customers Page -->
<div class="customers-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" routerLink="/dashboard">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">إدارة العملاء</h1>
          <p class="page-subtitle">إدارة بيانات العملاء والحسابات المالية</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-raised-button class="add-btn" (click)="openAddCustomerDialog()">
          <mat-icon>add</mat-icon>
          إضافة عميل جديد
        </button>
        <button mat-stroked-button class="export-btn" (click)="exportCustomers()">
          <mat-icon>download</mat-icon>
          تصدير
        </button>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="content-area">
    <!-- Statistics Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card customers-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalCustomers }}</h3>
              <p>إجمالي العملاء</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card active-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>person_check</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ activeCustomers }}</h3>
              <p>العملاء النشطين</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card balance-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>account_balance_wallet</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalBalance | currency:'EGP':'symbol':'1.2-2' }}</h3>
              <p>إجمالي الأرصدة</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card credit-card">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon">
              <mat-icon>credit_card</mat-icon>
            </div>
            <div class="stat-info">
              <h3>{{ totalCreditLimit | currency:'EGP':'symbol':'1.2-2' }}</h3>
              <p>إجمالي حدود الائتمان</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Filters and Search -->
    <mat-card class="filters-card">
      <mat-card-content>
        <div class="filters-row">
          <mat-form-field class="search-field">
            <mat-label>البحث في العملاء</mat-label>
            <input matInput [(ngModel)]="searchTerm" (input)="onSearch()" placeholder="اسم العميل، الكود، أو رقم الهاتف">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field class="filter-field">
            <mat-label>نوع العميل</mat-label>
            <mat-select [(ngModel)]="selectedCustomerType" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الأنواع</mat-option>
              <mat-option *ngFor="let type of customerTypes" [value]="type.id">
                {{ type.nameAr }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="filter-field">
            <mat-label>المنطقة</mat-label>
            <mat-select [(ngModel)]="selectedArea" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع المناطق</mat-option>
              <mat-option *ngFor="let area of areas" [value]="area.id">
                {{ area.nameAr }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="filter-field">
            <mat-label>الفرع</mat-label>
            <mat-select [(ngModel)]="selectedBranch" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الفروع</mat-option>
              <mat-option *ngFor="let branch of branches" [value]="branch.id">
                {{ branch.nameAr }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <button mat-stroked-button class="clear-filters-btn" (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            مسح الفلاتر
          </button>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Customers Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>people</mat-icon>
          قائمة العملاء
        </mat-card-title>
        <div class="table-actions">
          <button mat-icon-button (click)="refreshCustomers()" matTooltip="تحديث البيانات">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div class="table-container">
          <table mat-table [dataSource]="dataSource" class="customers-table" matSort>
            <!-- Customer Code Column -->
            <ng-container matColumnDef="customerCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>كود العميل</th>
              <td mat-cell *matCellDef="let customer">
                <span class="customer-code">{{ customer.customerCode }}</span>
              </td>
            </ng-container>

            <!-- Customer Name Column -->
            <ng-container matColumnDef="fullName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم العميل</th>
              <td mat-cell *matCellDef="let customer">
                <div class="customer-info">
                  <span class="customer-name-ar">{{ customer.fullName }}</span>
                  <span class="customer-name-en" *ngIf="customer.nameEn">{{ customer.nameEn }}</span>
                </div>
              </td>
            </ng-container>

            <!-- Customer Type Column -->
            <ng-container matColumnDef="customerTypeName">
              <th mat-header-cell *matHeaderCellDef>نوع العميل</th>
              <td mat-cell *matCellDef="let customer">
                <span class="customer-type-badge" [class]="'type-' + customer.customerTypeId">
                  {{ customer.customerTypeName }}
                </span>
              </td>
            </ng-container>

            <!-- Phone Column -->
            <ng-container matColumnDef="phoneNumber">
              <th mat-header-cell *matHeaderCellDef>رقم الهاتف</th>
              <td mat-cell *matCellDef="let customer">
                <span class="phone-number">{{ customer.phoneNumber }}</span>
              </td>
            </ng-container>

            <!-- Area Column -->
            <ng-container matColumnDef="areaName">
              <th mat-header-cell *matHeaderCellDef>المنطقة</th>
              <td mat-cell *matCellDef="let customer">
                <span class="area-name">{{ customer.areaName }}</span>
              </td>
            </ng-container>

            <!-- Balance Column -->
            <ng-container matColumnDef="currentBalance">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد الحالي</th>
              <td mat-cell *matCellDef="let customer">
                <span class="balance" [class.negative]="customer.currentBalance < 0">
                  {{ customer.currentBalance | currency:'EGP':'symbol':'1.2-2' }}
                </span>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="isActive">
              <th mat-header-cell *matHeaderCellDef>الحالة</th>
              <td mat-cell *matCellDef="let customer">
                <span class="status-badge" [class.active]="customer.isActive" [class.inactive]="!customer.isActive">
                  {{ customer.isActive ? 'نشط' : 'غير نشط' }}
                </span>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
              <td mat-cell *matCellDef="let customer">
                <div class="action-buttons">
                  <button mat-icon-button (click)="viewCustomer(customer)" matTooltip="عرض التفاصيل">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button (click)="editCustomer(customer)" matTooltip="تعديل">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button (click)="viewFinancials(customer)" matTooltip="الحساب المالي">
                    <mat-icon>account_balance</mat-icon>
                  </button>
                  <button mat-icon-button [matMenuTriggerFor]="actionMenu" matTooltip="المزيد">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu">
                    <button mat-menu-item (click)="toggleCustomerStatus(customer)">
                      <mat-icon>{{ customer.isActive ? 'block' : 'check_circle' }}</mat-icon>
                      {{ customer.isActive ? 'إلغاء التفعيل' : 'تفعيل' }}
                    </button>
                    <button mat-menu-item (click)="deleteCustomer(customer)">
                      <mat-icon>delete</mat-icon>
                      حذف
                    </button>
                  </mat-menu>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="viewCustomer(row)" class="clickable-row"></tr>
          </table>

          <!-- No Data Message -->
          <div *ngIf="dataSource.data.length === 0" class="no-data">
            <mat-icon>people_outline</mat-icon>
            <h3>لا توجد عملاء</h3>
            <p>لم يتم العثور على أي عملاء. قم بإضافة عميل جديد للبدء.</p>
            <button mat-raised-button color="primary" (click)="openAddCustomerDialog()">
              إضافة عميل جديد
            </button>
          </div>
        </div>

        <!-- Pagination -->
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل بيانات العملاء...</p>
  </div>
</div>
