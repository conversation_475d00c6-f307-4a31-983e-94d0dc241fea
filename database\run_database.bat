@echo off
echo ========================================
echo Terra Retail ERP - Database Setup
echo ========================================
echo.

echo تشغيل سكريبت إنشاء قاعدة البيانات...
echo Running database creation script...
echo.

sqlcmd -S localhost -U sa -P @a123admin4 -i create_database.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo تم إنشاء قاعدة البيانات بنجاح!
    echo Database created successfully!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل التطبيق
    echo You can now run the application
    echo.
) else (
    echo.
    echo ========================================
    echo حدث خطأ في إنشاء قاعدة البيانات
    echo Error occurred while creating database
    echo ========================================
    echo.
    echo تأكد من:
    echo Please check:
    echo 1. SQL Server is running
    echo 2. Username and password are correct
    echo 3. You have sufficient permissions
    echo.
)

pause
