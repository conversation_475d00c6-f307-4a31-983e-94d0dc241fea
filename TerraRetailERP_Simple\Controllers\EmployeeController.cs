using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("👥 Employee Management")]
    public class EmployeeController : ControllerBase
    {
        private readonly AppDbContext _context;

        public EmployeeController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult> GetEmployees(
            [FromQuery] int? branchId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Branch)
                    .AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(e => e.BranchId == branchId);

                if (departmentId.HasValue)
                    query = query.Where(e => e.DepartmentId == departmentId);

                if (isActive.HasValue)
                    query = query.Where(e => e.IsActive == isActive);

                var employees = await query
                    .Select(e => new
                    {
                        e.Id,
                        e.EmployeeCode,
                        e.NameAr,
                        e.NameEn,
                        e.Phone1,
                        e.Email,
                        DepartmentName = e.Department != null ? e.Department.NameAr : null,
                        PositionName = e.Position != null ? e.Position.NameAr : null,
                        BranchName = e.Branch.NameAr,
                        e.BasicSalary,
                        e.HireDate,
                        e.IsActive,
                        e.CreatedAt
                    })
                    .OrderBy(e => e.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الموظفين بنجاح",
                    data = employees,
                    count = employees.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult> GetEmployee(int id)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Branch)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (employee == null)
                    return NotFound(new { success = false, message = "الموظف غير موجود" });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات الموظف بنجاح",
                    data = employee
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult> CreateEmployee(CreateEmployeeRequest request)
        {
            try
            {
                // Generate employee code
                var employeeCode = await GetNextEmployeeCode(request.BranchId);

                var employee = new Employee
                {
                    EmployeeCode = employeeCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    NationalId = request.NationalId,
                    Phone1 = request.Phone1,
                    Phone2 = request.Phone2,
                    Email = request.Email,
                    Address = request.Address,
                    BirthDate = request.BirthDate,
                    HireDate = request.HireDate ?? DateTime.Now,
                    DepartmentId = request.DepartmentId,
                    PositionId = request.PositionId,
                    BranchId = request.BranchId,
                    BasicSalary = request.BasicSalary,
                    BiometricId = request.BiometricId,
                    EmergencyContact = request.EmergencyContact,
                    EmergencyPhone = request.EmergencyPhone,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetEmployee), new { id = employee.Id }, new
                {
                    success = true,
                    message = "تم إضافة الموظف بنجاح",
                    data = employee
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("attendance")]
        public async Task<ActionResult> GetAttendance(
            [FromQuery] int? employeeId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddDays(-30);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.AttendanceRecords
                    .Include(ar => ar.Employee)
                    .Include(ar => ar.Shift)
                    .Where(ar => ar.ShiftDate >= startDate && ar.ShiftDate <= endDate);

                if (employeeId.HasValue)
                    query = query.Where(ar => ar.EmployeeId == employeeId);

                var attendance = await query
                    .Select(ar => new
                    {
                        ar.Id,
                        ar.EmployeeId,
                        EmployeeName = ar.Employee.NameAr,
                        ar.ShiftDate,
                        ShiftName = ar.Shift != null ? ar.Shift.ShiftName : null,
                        ar.PlannedCheckInTime,
                        ar.PlannedCheckOutTime,
                        ar.ActualCheckInTime,
                        ar.ActualCheckOutTime,
                        ar.WorkingMinutes,
                        ar.LateMinutes,
                        ar.EarlyLeaveMinutes,
                        ar.OvertimeMinutes,
                        ar.AttendanceStatus,
                        ar.IsComplete,
                        ar.Notes
                    })
                    .OrderByDescending(ar => ar.ShiftDate)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب سجلات الحضور بنجاح",
                    data = attendance,
                    count = attendance.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("attendance/checkin")]
        public async Task<ActionResult> CheckIn(CheckInRequest request)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(request.EmployeeId);
                if (employee == null)
                    return NotFound(new { success = false, message = "الموظف غير موجود" });

                var today = DateTime.Today;
                var existingRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.EmployeeId == request.EmployeeId && ar.ShiftDate.Date == today);

                if (existingRecord != null && existingRecord.ActualCheckInTime.HasValue)
                    return BadRequest(new { success = false, message = "تم تسجيل الحضور مسبقاً لهذا اليوم" });

                var checkInTime = DateTime.Now;
                
                if (existingRecord == null)
                {
                    // Create new attendance record
                    var attendanceRecord = new AttendanceRecord
                    {
                        EmployeeId = request.EmployeeId,
                        ShiftDate = today,
                        PlannedCheckInTime = new DateTime(today.Year, today.Month, today.Day, 8, 0, 0), // Default 8 AM
                        PlannedCheckOutTime = new DateTime(today.Year, today.Month, today.Day, 16, 0, 0), // Default 4 PM
                        ActualCheckInTime = checkInTime,
                        AttendanceStatus = "Present",
                        CreatedAt = DateTime.Now
                    };

                    // Calculate late minutes
                    if (checkInTime.TimeOfDay > attendanceRecord.PlannedCheckInTime.TimeOfDay)
                    {
                        attendanceRecord.LateMinutes = (int)(checkInTime.TimeOfDay - attendanceRecord.PlannedCheckInTime.TimeOfDay).TotalMinutes;
                        attendanceRecord.AttendanceStatus = "Late";
                    }

                    _context.AttendanceRecords.Add(attendanceRecord);
                }
                else
                {
                    existingRecord.ActualCheckInTime = checkInTime;
                    existingRecord.AttendanceStatus = "Present";
                    
                    if (checkInTime.TimeOfDay > existingRecord.PlannedCheckInTime.TimeOfDay)
                    {
                        existingRecord.LateMinutes = (int)(checkInTime.TimeOfDay - existingRecord.PlannedCheckInTime.TimeOfDay).TotalMinutes;
                        existingRecord.AttendanceStatus = "Late";
                    }
                }

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تسجيل الحضور بنجاح",
                    checkInTime = checkInTime
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("attendance/checkout")]
        public async Task<ActionResult> CheckOut(CheckOutRequest request)
        {
            try
            {
                var today = DateTime.Today;
                var attendanceRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.EmployeeId == request.EmployeeId && ar.ShiftDate.Date == today);

                if (attendanceRecord == null || !attendanceRecord.ActualCheckInTime.HasValue)
                    return BadRequest(new { success = false, message = "لم يتم تسجيل الحضور لهذا اليوم" });

                if (attendanceRecord.ActualCheckOutTime.HasValue)
                    return BadRequest(new { success = false, message = "تم تسجيل الانصراف مسبقاً" });

                var checkOutTime = DateTime.Now;
                attendanceRecord.ActualCheckOutTime = checkOutTime;

                // Calculate working minutes
                var workingTime = checkOutTime - attendanceRecord.ActualCheckInTime.Value;
                attendanceRecord.WorkingMinutes = (int)workingTime.TotalMinutes;

                // Calculate early leave
                if (checkOutTime.TimeOfDay < attendanceRecord.PlannedCheckOutTime.TimeOfDay)
                {
                    attendanceRecord.EarlyLeaveMinutes = (int)(attendanceRecord.PlannedCheckOutTime.TimeOfDay - checkOutTime.TimeOfDay).TotalMinutes;
                    attendanceRecord.AttendanceStatus = "EarlyLeave";
                }

                // Calculate overtime
                if (checkOutTime.TimeOfDay > attendanceRecord.PlannedCheckOutTime.TimeOfDay)
                {
                    attendanceRecord.OvertimeMinutes = (int)(checkOutTime.TimeOfDay - attendanceRecord.PlannedCheckOutTime.TimeOfDay).TotalMinutes;
                }

                attendanceRecord.IsComplete = true;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تسجيل الانصراف بنجاح",
                    checkOutTime = checkOutTime,
                    workingMinutes = attendanceRecord.WorkingMinutes,
                    overtimeMinutes = attendanceRecord.OvertimeMinutes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("departments")]
        public async Task<ActionResult> GetDepartments()
        {
            try
            {
                var departments = await _context.Departments
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الأقسام بنجاح",
                    data = departments
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("positions")]
        public async Task<ActionResult> GetPositions()
        {
            try
            {
                var positions = await _context.Positions
                    .Where(p => p.IsActive)
                    .OrderBy(p => p.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المناصب بنجاح",
                    data = positions
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("shifts")]
        public async Task<ActionResult> GetShifts()
        {
            try
            {
                var shifts = await _context.Shifts
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.StartTime)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الورديات بنجاح",
                    data = shifts
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost("shifts")]
        public async Task<ActionResult> CreateShift(CreateShiftRequest request)
        {
            try
            {
                var shift = new Shift
                {
                    ShiftName = request.ShiftName,
                    StartTime = request.StartTime,
                    EndTime = request.EndTime,
                    BreakDuration = request.BreakDuration,
                    GracePeriodMinutes = request.GracePeriodMinutes,
                    MaxOvertimeHours = request.MaxOvertimeHours,
                    IsFlexible = request.IsFlexible,
                    Description = request.Description,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Shifts.Add(shift);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetShifts), new { id = shift.Id }, new
                {
                    success = true,
                    message = "تم إضافة الوردية بنجاح",
                    data = shift
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpGet("leave-types")]
        public async Task<ActionResult> GetLeaveTypes()
        {
            try
            {
                var leaveTypes = await _context.LeaveTypes
                    .Where(lt => lt.IsActive)
                    .OrderBy(lt => lt.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أنواع الإجازات بنجاح",
                    data = leaveTypes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost("leave-types")]
        public async Task<ActionResult> CreateLeaveType(CreateLeaveTypeRequest request)
        {
            try
            {
                var leaveType = new LeaveType
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    MaxDaysPerYear = request.MaxDaysPerYear,
                    IsPaid = request.IsPaid,
                    RequireApproval = request.RequireApproval,
                    RequireDocument = request.RequireDocument,
                    Description = request.Description,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.LeaveTypes.Add(leaveType);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetLeaveTypes), new { id = leaveType.Id }, new
                {
                    success = true,
                    message = "تم إضافة نوع الإجازة بنجاح",
                    data = leaveType
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        private async Task<string> GetNextEmployeeCode(int branchId)
        {
            var lastEmployee = await _context.Employees
                .Where(e => e.BranchId == branchId)
                .OrderByDescending(e => e.Id)
                .FirstOrDefaultAsync();

            var nextId = (lastEmployee?.Id ?? 0) + 1;
            return $"EMP{branchId:D2}{nextId:D4}";
        }
    }

    // DTOs
    public class CreateEmployeeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? NationalId { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public DateTime? BirthDate { get; set; }
        public DateTime? HireDate { get; set; }
        public int? DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public int BranchId { get; set; } = 1;
        public decimal BasicSalary { get; set; } = 0;
        public string? BiometricId { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
    }

    public class CheckInRequest
    {
        public int EmployeeId { get; set; }
    }

    public class CheckOutRequest
    {
        public int EmployeeId { get; set; }
    }

    public class CreateShiftRequest
    {
        public string ShiftName { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public int BreakDuration { get; set; } = 0;
        public int GracePeriodMinutes { get; set; } = 0;
        public decimal MaxOvertimeHours { get; set; } = 0;
        public bool IsFlexible { get; set; } = false;
        public string? Description { get; set; }
    }

    public class CreateLeaveTypeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int MaxDaysPerYear { get; set; } = 0;
        public bool IsPaid { get; set; } = true;
        public bool RequireApproval { get; set; } = true;
        public bool RequireDocument { get; set; } = false;
        public string? Description { get; set; }
    }
}
