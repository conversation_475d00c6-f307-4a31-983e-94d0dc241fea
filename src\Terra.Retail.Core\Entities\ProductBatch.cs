using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// دفعات/أرقام تشغيل المنتجات
    /// </summary>
    public class ProductBatch : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// رقم الدفعة/التشغيل
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الإنتاج
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الكمية الأصلية
        /// </summary>
        public decimal OriginalQuantity { get; set; }

        /// <summary>
        /// الكمية المتاحة
        /// </summary>
        public decimal AvailableQuantity { get; set; }

        /// <summary>
        /// الكمية المحجوزة
        /// </summary>
        public decimal ReservedQuantity { get; set; } = 0;

        /// <summary>
        /// سعر التكلفة للدفعة
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// حالة الدفعة
        /// </summary>
        public BatchStatus Status { get; set; } = BatchStatus.Active;

        /// <summary>
        /// معرف فاتورة الشراء
        /// </summary>
        public int? PurchaseId { get; set; }

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int? SupplierId { get; set; }

        /// <summary>
        /// موقع التخزين
        /// </summary>
        [MaxLength(100)]
        public string? StorageLocation { get; set; }

        /// <summary>
        /// ملاحظات الدفعة
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل الدفعة منتهية الصلاحية
        /// </summary>
        public bool IsExpired { get; set; } = false;

        /// <summary>
        /// تاريخ التنبيه لانتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryAlertDate { get; set; }

        /// <summary>
        /// رقم شهادة الجودة
        /// </summary>
        [MaxLength(50)]
        public string? QualityCertificateNumber { get; set; }

        /// <summary>
        /// درجة حرارة التخزين
        /// </summary>
        [MaxLength(50)]
        public string? StorageTemperature { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual Purchase? Purchase { get; set; }
        public virtual Supplier? Supplier { get; set; }
        public virtual ProductStock ProductStock { get; set; } = null!;
    }

    /// <summary>
    /// حالة الدفعة
    /// </summary>
    public enum BatchStatus
    {
        /// <summary>
        /// نشطة
        /// </summary>
        Active = 1,

        /// <summary>
        /// محجوزة
        /// </summary>
        Reserved = 2,

        /// <summary>
        /// منتهية الصلاحية
        /// </summary>
        Expired = 3,

        /// <summary>
        /// تالفة
        /// </summary>
        Damaged = 4,

        /// <summary>
        /// مستهلكة
        /// </summary>
        Consumed = 5,

        /// <summary>
        /// مرتجعة
        /// </summary>
        Returned = 6
    }
}
