{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { interval } from 'rxjs';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nconst _c0 = [\"salesChart\"];\nfunction DashboardNewComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 66);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 67)(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"#\", order_r2.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r2.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 7, order_r2.createdAt, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(11, 10, order_r2.total, \"1.0-0\"), \" \\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(order_r2.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getOrderStatusText(order_r2.status));\n  }\n}\nfunction DashboardNewComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵelement(4, \"img\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 72)(6, \"div\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 74);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 75)(11, \"div\", 76);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 77);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.image || \"assets/images/product-placeholder.svg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", product_r4.salesCount, \" \\u0645\\u0628\\u064A\\u0639\\u0629\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(15, 7, product_r4.salesAmount, \"1.0-0\"), \" \\u062C\\u0646\\u064A\\u0647\");\n  }\n}\nfunction DashboardNewComponent_div_197_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 80)(5, \"div\", 81);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 82);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 83);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 84)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"more_vert\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(notification_r6.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getNotificationIcon(notification_r6.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r6.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r6.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 6, notification_r6.createdAt, \"short\"));\n  }\n}\nfunction DashboardNewComponent_div_198_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"mat-spinner\", 86);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let DashboardNewComponent = /*#__PURE__*/(() => {\n  class DashboardNewComponent {\n    http;\n    salesChartRef;\n    // Component State\n    isLoading = true;\n    currentDate = new Date();\n    currentTime = new Date();\n    // Subscriptions\n    timeSubscription;\n    dataSubscription;\n    // Dashboard Data\n    stats = {\n      todaySales: 0,\n      todayOrders: 0,\n      customers: 0,\n      products: 0\n    };\n    systemStatus = {\n      overall: 'healthy',\n      api: 'connected',\n      database: 'connected',\n      cache: 'connected'\n    };\n    recentOrders = [];\n    topProducts = [];\n    notifications = [];\n    // Chart instance\n    salesChart;\n    constructor(http) {\n      this.http = http;\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      this.loadDashboardData();\n      this.startTimeUpdater();\n      this.setupDataRefresh();\n    }\n    ngAfterViewInit() {\n      // Initialize charts after view is ready\n      setTimeout(() => {\n        this.initializeSalesChart();\n      }, 100);\n    }\n    ngOnDestroy() {\n      // Clean up subscriptions\n      this.timeSubscription?.unsubscribe();\n      this.dataSubscription?.unsubscribe();\n      // Destroy chart\n      if (this.salesChart) {\n        this.salesChart.destroy();\n      }\n    }\n    /**\n     * Initialize component with default data\n     */\n    initializeComponent() {\n      // Set initial mock data\n      this.stats = {\n        todaySales: 25750,\n        todayOrders: 156,\n        customers: 24,\n        products: 5\n      };\n      this.recentOrders = [{\n        id: '2024001',\n        customerName: 'أحمد محمد علي',\n        total: 1250,\n        status: 'completed',\n        createdAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago\n      }, {\n        id: '2024002',\n        customerName: 'فاطمة أحمد',\n        total: 850,\n        status: 'pending',\n        createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago\n      }, {\n        id: '2024003',\n        customerName: 'محمد حسن',\n        total: 2100,\n        status: 'completed',\n        createdAt: new Date(Date.now() - 1000 * 60 * 45) // 45 minutes ago\n      }];\n      this.topProducts = [{\n        name: 'لابتوب Dell Inspiron',\n        category: 'أجهزة كمبيوتر',\n        salesCount: 45,\n        salesAmount: 67500,\n        image: 'assets/images/product-placeholder.svg'\n      }, {\n        name: 'هاتف Samsung Galaxy',\n        category: 'هواتف ذكية',\n        salesCount: 38,\n        salesAmount: 45600,\n        image: 'assets/images/product-placeholder.svg'\n      }, {\n        name: 'سماعات Sony',\n        category: 'إكسسوارات',\n        salesCount: 62,\n        salesAmount: 18600,\n        image: 'assets/images/product-placeholder.svg'\n      }];\n      this.notifications = [{\n        type: 'warning',\n        title: 'نفاد مخزون',\n        message: 'المنتج \"لابتوب Dell\" أوشك على النفاد',\n        createdAt: new Date(Date.now() - 1000 * 60 * 10)\n      }, {\n        type: 'info',\n        title: 'طلب جديد',\n        message: 'تم استلام طلب جديد من العميل أحمد محمد',\n        createdAt: new Date(Date.now() - 1000 * 60 * 20)\n      }, {\n        type: 'success',\n        title: 'تم الدفع',\n        message: 'تم استلام دفعة بقيمة 5000 جنيه',\n        createdAt: new Date(Date.now() - 1000 * 60 * 35)\n      }];\n    }\n    /**\n     * Load dashboard data from API\n     */\n    loadDashboardData() {\n      this.isLoading = true;\n      // Load statistics\n      this.http.get('http://localhost:5127/api/simple/statistics').subscribe({\n        next: response => {\n          this.stats.customers = response.customersCount || this.stats.customers;\n          this.stats.products = response.productsCount || this.stats.products;\n          console.log('Statistics loaded:', response);\n        },\n        error: error => {\n          console.error('Error loading statistics:', error);\n        }\n      });\n      // Check system status\n      this.http.get('http://localhost:5127/health').subscribe({\n        next: response => {\n          this.systemStatus.api = 'connected';\n          this.systemStatus.database = response.Database?.Connected ? 'connected' : 'disconnected';\n          this.systemStatus.overall = this.calculateOverallStatus();\n        },\n        error: () => {\n          this.systemStatus.api = 'disconnected';\n          this.systemStatus.database = 'disconnected';\n          this.systemStatus.overall = 'error';\n        }\n      });\n      // Simulate loading delay\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 1500);\n    }\n    /**\n     * Start time updater\n     */\n    startTimeUpdater() {\n      this.timeSubscription = interval(1000).subscribe(() => {\n        this.currentTime = new Date();\n        // Update date at midnight\n        const now = new Date();\n        if (now.getHours() === 0 && now.getMinutes() === 0 && now.getSeconds() === 0) {\n          this.currentDate = now;\n        }\n      });\n    }\n    /**\n     * Setup automatic data refresh\n     */\n    setupDataRefresh() {\n      // Refresh data every 5 minutes\n      this.dataSubscription = interval(5 * 60 * 1000).subscribe(() => {\n        this.loadDashboardData();\n      });\n    }\n    /**\n     * Initialize sales chart\n     */\n    initializeSalesChart() {\n      if (!this.salesChartRef?.nativeElement) {\n        return;\n      }\n      const ctx = this.salesChartRef.nativeElement.getContext('2d');\n      if (!ctx) {\n        return;\n      }\n      // Mock chart data\n      const chartData = {\n        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],\n        datasets: [{\n          label: 'المبيعات',\n          data: [12000, 19000, 15000, 25000, 22000, 30000],\n          borderColor: 'rgb(59, 130, 246)',\n          backgroundColor: 'rgba(59, 130, 246, 0.1)',\n          tension: 0.4,\n          fill: true\n        }]\n      };\n      // Note: You would need to install Chart.js for this to work\n      // For now, we'll just log that the chart would be initialized\n      console.log('Sales chart would be initialized with data:', chartData);\n    }\n    /**\n     * Calculate overall system status\n     */\n    calculateOverallStatus() {\n      const statuses = [this.systemStatus.api, this.systemStatus.database, this.systemStatus.cache];\n      if (statuses.every(status => status === 'connected')) {\n        return 'healthy';\n      } else if (statuses.some(status => status === 'disconnected')) {\n        return 'error';\n      } else {\n        return 'warning';\n      }\n    }\n    /**\n     * Get order status text\n     */\n    getOrderStatusText(status) {\n      const statusMap = {\n        'pending': 'قيد الانتظار',\n        'completed': 'مكتمل',\n        'cancelled': 'ملغي',\n        'processing': 'قيد المعالجة'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get system status text\n     */\n    getSystemStatusText(status) {\n      const statusMap = {\n        'healthy': 'سليم',\n        'warning': 'تحذير',\n        'error': 'خطأ'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get status text\n     */\n    getStatusText(status) {\n      const statusMap = {\n        'connected': 'متصل',\n        'disconnected': 'غير متصل',\n        'warning': 'تحذير'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get notification icon\n     */\n    getNotificationIcon(type) {\n      const iconMap = {\n        'info': 'info',\n        'warning': 'warning',\n        'error': 'error',\n        'success': 'check_circle'\n      };\n      return iconMap[type] || 'notifications';\n    }\n    /**\n     * Open POS system\n     */\n    openPOS() {\n      window.open('http://localhost:5127/swagger', '_blank');\n      this.showMessage('فتح نقطة البيع في نافذة جديدة');\n    }\n    /**\n     * Refresh dashboard data\n     */\n    refreshData() {\n      this.loadDashboardData();\n      this.showMessage('تم تحديث البيانات');\n    }\n    /**\n     * Show message (you can implement a toast service)\n     */\n    showMessage(message) {\n      console.log(message);\n      // Implement toast notification here\n    }\n    /**\n     * Handle notification click\n     */\n    onNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n      // Implement notification handling\n    }\n    /**\n     * View all notifications\n     */\n    viewAllNotifications() {\n      console.log('View all notifications');\n      // Navigate to notifications page\n    }\n    /**\n     * View order details\n     */\n    viewOrderDetails(order) {\n      console.log('View order details:', order);\n      // Navigate to order details page\n    }\n    /**\n     * View all products\n     */\n    viewAllProducts() {\n      console.log('View all products');\n      // Navigate to products page\n    }\n    /**\n     * View detailed reports\n     */\n    viewDetailedReports() {\n      console.log('View detailed reports');\n      // Navigate to reports page\n    }\n    static ɵfac = function DashboardNewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardNewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardNewComponent,\n      selectors: [[\"app-dashboard-new\"]],\n      viewQuery: function DashboardNewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.salesChartRef = _t.first);\n        }\n      },\n      decls: 199,\n      vars: 39,\n      consts: [[\"salesChart\", \"\"], [1, \"dashboard-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-right\"], [1, \"date-time\"], [1, \"current-date\"], [1, \"current-time\"], [1, \"stats-grid\"], [1, \"stat-card\", \"sales-card\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-change\", \"positive\"], [1, \"stat-chart\"], [1, \"mini-chart\", \"sales-chart\"], [1, \"stat-card\", \"orders-card\"], [1, \"mini-chart\", \"orders-chart\"], [1, \"stat-card\", \"customers-card\"], [1, \"mini-chart\", \"customers-chart\"], [1, \"stat-card\", \"products-card\"], [1, \"stat-change\", \"neutral\"], [1, \"mini-chart\", \"products-chart\"], [1, \"content-grid\"], [1, \"chart-card\", \"sales-overview\"], [1, \"card-header\"], [1, \"card-actions\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"card-content\"], [1, \"chart-container\"], [1, \"data-card\", \"recent-orders\"], [\"mat-icon-button\", \"\"], [1, \"orders-list\"], [\"class\", \"order-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"data-card\", \"top-products\"], [1, \"products-list\"], [\"class\", \"product-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"status-card\", \"system-status\"], [1, \"status-indicator\"], [1, \"status-items\"], [1, \"status-item\"], [1, \"status-icon\"], [1, \"status-info\"], [1, \"status-name\"], [1, \"status-value\"], [1, \"actions-card\", \"quick-actions\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/customers/add\", 1, \"action-btn\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/products/add\", 1, \"action-btn\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/reports\", 1, \"action-btn\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/inventory\", 1, \"action-btn\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/settings\", 1, \"action-btn\"], [1, \"notifications-card\"], [1, \"notifications-list\"], [\"class\", \"notification-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"order-item\"], [1, \"order-info\"], [1, \"order-number\"], [1, \"order-customer\"], [1, \"order-time\"], [1, \"order-amount\"], [1, \"order-status\"], [1, \"product-item\"], [1, \"product-rank\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-category\"], [1, \"product-sales\"], [1, \"sales-count\"], [1, \"sales-amount\"], [1, \"notification-item\"], [1, \"notification-icon\"], [1, \"notification-content\"], [1, \"notification-title\"], [1, \"notification-message\"], [1, \"notification-time\"], [\"mat-icon-button\", \"\", 1, \"notification-action\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function DashboardNewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\", 5);\n          i0.ɵɵtext(5, \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 6);\n          i0.ɵɵtext(7, \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0646\\u0638\\u0627\\u0645 Terra Retail ERP\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"date\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"trending_up\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵtext(26, \"\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 17)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"arrow_upward\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"+12.5%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 18);\n          i0.ɵɵelement(33, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 20)(35, \"div\", 13)(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"shopping_cart\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 14)(39, \"div\", 15);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 16);\n          i0.ɵɵtext(42, \"\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u064A\\u0648\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 17)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"arrow_upward\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"span\");\n          i0.ɵɵtext(47, \"+8.2%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 18);\n          i0.ɵɵelement(49, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 22)(51, \"div\", 13)(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"people\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 14)(55, \"div\", 15);\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 16);\n          i0.ɵɵtext(58, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 17)(60, \"mat-icon\");\n          i0.ɵɵtext(61, \"arrow_upward\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"span\");\n          i0.ɵɵtext(63, \"+5.1%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"div\", 18);\n          i0.ɵɵelement(65, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 24)(67, \"div\", 13)(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"inventory_2\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 14)(71, \"div\", 15);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 16);\n          i0.ɵɵtext(74, \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 25)(76, \"mat-icon\");\n          i0.ɵɵtext(77, \"remove\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\");\n          i0.ɵɵtext(79, \"0%\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"div\", 18);\n          i0.ɵɵelement(81, \"div\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"div\", 27)(83, \"div\", 28)(84, \"div\", 29)(85, \"h3\");\n          i0.ɵɵtext(86, \"\\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 30)(88, \"button\", 31);\n          i0.ɵɵtext(89, \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 32)(91, \"div\", 33);\n          i0.ɵɵelement(92, \"canvas\", null, 0);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 34)(95, \"div\", 29)(96, \"h3\");\n          i0.ɵɵtext(97, \"\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"div\", 30)(99, \"button\", 35)(100, \"mat-icon\");\n          i0.ɵɵtext(101, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(102, \"div\", 32)(103, \"div\", 36);\n          i0.ɵɵtemplate(104, DashboardNewComponent_div_104_Template, 15, 13, \"div\", 37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(105, \"div\", 38)(106, \"div\", 29)(107, \"h3\");\n          i0.ɵɵtext(108, \"\\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0628\\u064A\\u0639\\u0627\\u064B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 30)(110, \"button\", 31);\n          i0.ɵɵtext(111, \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"div\", 32)(113, \"div\", 39);\n          i0.ɵɵtemplate(114, DashboardNewComponent_div_114_Template, 16, 10, \"div\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(115, \"div\", 41)(116, \"div\", 29)(117, \"h3\");\n          i0.ɵɵtext(118, \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"div\", 30)(120, \"div\", 42)(121, \"span\");\n          i0.ɵɵtext(122);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(123, \"div\", 32)(124, \"div\", 43)(125, \"div\", 44)(126, \"div\", 45)(127, \"mat-icon\");\n          i0.ɵɵtext(128, \"cloud\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"div\", 46)(130, \"div\", 47);\n          i0.ɵɵtext(131, \"\\u062E\\u062F\\u0645\\u0629 API\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"div\", 48);\n          i0.ɵɵtext(133);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(134, \"div\", 44)(135, \"div\", 45)(136, \"mat-icon\");\n          i0.ɵɵtext(137, \"storage\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(138, \"div\", 46)(139, \"div\", 47);\n          i0.ɵɵtext(140, \"\\u0642\\u0627\\u0639\\u062F\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"div\", 48);\n          i0.ɵɵtext(142);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(143, \"div\", 44)(144, \"div\", 45)(145, \"mat-icon\");\n          i0.ɵɵtext(146, \"memory\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(147, \"div\", 46)(148, \"div\", 47);\n          i0.ɵɵtext(149, \"\\u0630\\u0627\\u0643\\u0631\\u0629 \\u0627\\u0644\\u062A\\u062E\\u0632\\u064A\\u0646 \\u0627\\u0644\\u0645\\u0624\\u0642\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"div\", 48);\n          i0.ɵɵtext(151);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(152, \"div\", 49)(153, \"div\", 29)(154, \"h3\");\n          i0.ɵɵtext(155, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(156, \"div\", 32)(157, \"div\", 50)(158, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function DashboardNewComponent_Template_button_click_158_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openPOS());\n          });\n          i0.ɵɵelementStart(159, \"mat-icon\");\n          i0.ɵɵtext(160, \"point_of_sale\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"span\");\n          i0.ɵɵtext(162, \"\\u0641\\u062A\\u062D \\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(163, \"button\", 52)(164, \"mat-icon\");\n          i0.ɵɵtext(165, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(166, \"span\");\n          i0.ɵɵtext(167, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(168, \"button\", 53)(169, \"mat-icon\");\n          i0.ɵɵtext(170, \"add_box\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"span\");\n          i0.ɵɵtext(172, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(173, \"button\", 54)(174, \"mat-icon\");\n          i0.ɵɵtext(175, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"span\");\n          i0.ɵɵtext(177, \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(178, \"button\", 55)(179, \"mat-icon\");\n          i0.ɵɵtext(180, \"inventory\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(181, \"span\");\n          i0.ɵɵtext(182, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(183, \"button\", 56)(184, \"mat-icon\");\n          i0.ɵɵtext(185, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(186, \"span\");\n          i0.ɵɵtext(187, \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(188, \"div\", 57)(189, \"div\", 29)(190, \"h3\");\n          i0.ɵɵtext(191, \"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"div\", 30)(193, \"button\", 31);\n          i0.ɵɵtext(194, \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0643\\u0644\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(195, \"div\", 32)(196, \"div\", 58);\n          i0.ɵɵtemplate(197, DashboardNewComponent_div_197_Template, 15, 9, \"div\", 59);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(198, DashboardNewComponent_div_198_Template, 4, 0, \"div\", 60);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 30, ctx.currentDate, \"EEEE, dd MMMM yyyy\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 33, ctx.currentTime, \"HH:mm\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(24, 36, ctx.stats.todaySales, \"1.0-0\"), \" \\u062C\\u0646\\u064A\\u0647\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.stats.todayOrders);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.stats.customers);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.stats.products);\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngForOf\", ctx.recentOrders);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topProducts);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassMap(ctx.systemStatus.overall);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getSystemStatusText(ctx.systemStatus.overall));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassMap(ctx.systemStatus.api);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getStatusText(ctx.systemStatus.api));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.systemStatus.database);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getStatusText(ctx.systemStatus.database));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.systemStatus.cache);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getStatusText(ctx.systemStatus.cache));\n          i0.ɵɵadvance(46);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, RouterModule, i3.RouterLink, MatCardModule, MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, MatProgressSpinnerModule, i6.MatProgressSpinner, MatMenuModule, MatBadgeModule],\n      styles: [\"\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.page-header[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   .current-date[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin-bottom: var(--spacing-xs);\\n}\\n.page-header[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   .current-time[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n\\n\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-2xl);\\n  margin-bottom: var(--spacing-3xl);\\n  padding: 0 var(--spacing-sm);\\n  box-sizing: border-box;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  padding: var(--spacing-2xl);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--gray-200);\\n  position: relative;\\n  overflow: hidden;\\n  transition: all var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xl);\\n  min-height: 120px;\\n  box-sizing: border-box;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--shadow-2xl);\\n}\\n.stat-card.loading[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  pointer-events: none;\\n}\\n.stat-card.loading[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: var(--radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 800;\\n  color: var(--gray-900);\\n  line-height: 1;\\n  margin-bottom: var(--spacing-xs);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  margin-bottom: var(--spacing-sm);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-change[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-change.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-change.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-change.neutral[_ngcontent-%COMP%] {\\n  color: var(--gray-500);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-change[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-chart[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 40px;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mini-chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: var(--radius-md);\\n  opacity: 0.8;\\n}\\n.stat-card.sales-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.stat-card.sales-card[_ngcontent-%COMP%]   .sales-chart[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-100), var(--success-200));\\n}\\n.stat-card.orders-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.stat-card.orders-card[_ngcontent-%COMP%]   .orders-chart[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));\\n}\\n.stat-card.customers-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));\\n}\\n.stat-card.customers-card[_ngcontent-%COMP%]   .customers-chart[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-100), var(--secondary-200));\\n}\\n.stat-card.products-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.stat-card.products-card[_ngcontent-%COMP%]   .products-chart[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-100), var(--warning-200));\\n}\\n\\n\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: var(--spacing-2xl);\\n  grid-template-areas: \\\"sales sales sales sales orders orders orders products products products products products\\\" \\\"status status status actions actions actions notifications notifications notifications notifications notifications notifications\\\";\\n  padding: 0 var(--spacing-sm);\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.chart-card[_ngcontent-%COMP%], \\n.data-card[_ngcontent-%COMP%], \\n.status-card[_ngcontent-%COMP%], \\n.actions-card[_ngcontent-%COMP%], \\n.notifications-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--gray-200);\\n  overflow: hidden;\\n  transition: all var(--transition-normal);\\n}\\n.chart-card[_ngcontent-%COMP%]:hover, \\n.data-card[_ngcontent-%COMP%]:hover, \\n.status-card[_ngcontent-%COMP%]:hover, \\n.actions-card[_ngcontent-%COMP%]:hover, \\n.notifications-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-xl);\\n  transform: translateY(-2px);\\n}\\n.chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], \\n.data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], \\n.status-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], \\n.actions-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], \\n.notifications-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-xl) var(--spacing-2xl);\\n  border-bottom: 1px solid var(--gray-100);\\n  background: var(--gray-50);\\n  box-sizing: border-box;\\n}\\n.chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.status-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.actions-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.notifications-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0;\\n}\\n.chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%], \\n.data-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%], \\n.status-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%], \\n.actions-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%], \\n.notifications-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-sm);\\n}\\n.chart-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], \\n.data-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], \\n.status-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], \\n.actions-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], \\n.notifications-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl);\\n  box-sizing: border-box;\\n}\\n\\n.sales-overview[_ngcontent-%COMP%] {\\n  grid-area: sales;\\n}\\n.sales-overview[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 300px;\\n  position: relative;\\n}\\n\\n.recent-orders[_ngcontent-%COMP%] {\\n  grid-area: orders;\\n}\\n.recent-orders[_ngcontent-%COMP%]   .orders-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: var(--spacing-md) 0;\\n  border-bottom: 1px solid var(--gray-100);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-info[_ngcontent-%COMP%]   .order-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-600);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-info[_ngcontent-%COMP%]   .order-customer[_ngcontent-%COMP%] {\\n  color: var(--gray-700);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-info[_ngcontent-%COMP%]   .order-time[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-amount[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0 var(--spacing-lg);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-status[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xs) var(--spacing-md);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-status.pending[_ngcontent-%COMP%] {\\n  background: var(--warning-100);\\n  color: var(--warning-700);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-status.completed[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n}\\n.recent-orders[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .order-status.cancelled[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n}\\n\\n.top-products[_ngcontent-%COMP%] {\\n  grid-area: products;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md) 0;\\n  border-bottom: 1px solid var(--gray-100);\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-rank[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background: var(--primary-100);\\n  color: var(--primary-700);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.875rem;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--radius-md);\\n  overflow: hidden;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-category[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-sales[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-sales[_ngcontent-%COMP%]   .sales-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-700);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.top-products[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-sales[_ngcontent-%COMP%]   .sales-amount[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--success-600);\\n  font-weight: 600;\\n}\\n\\n.system-status[_ngcontent-%COMP%] {\\n  grid-area: status;\\n}\\n.system-status[_ngcontent-%COMP%]   .status-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-lg);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]   mat-icon.connected[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]   mat-icon.disconnected[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]   mat-icon.warning[_ngcontent-%COMP%] {\\n  color: var(--warning-600);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xs) var(--spacing-md);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.system-status[_ngcontent-%COMP%]   .status-indicator.healthy[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-indicator.warning[_ngcontent-%COMP%] {\\n  background: var(--warning-100);\\n  color: var(--warning-700);\\n}\\n.system-status[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n}\\n\\n.quick-actions[_ngcontent-%COMP%] {\\n  grid-area: actions;\\n}\\n.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: var(--spacing-md);\\n}\\n.quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  flex-direction: column !important;\\n  align-items: center !important;\\n  gap: var(--spacing-sm) !important;\\n  padding: var(--spacing-lg) !important;\\n  height: auto !important;\\n  min-height: 80px !important;\\n  border-radius: var(--radius-lg) !important;\\n}\\n.quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.quick-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  text-align: center !important;\\n}\\n\\n.notifications-card[_ngcontent-%COMP%] {\\n  grid-area: notifications;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md) 0;\\n  border-bottom: 1px solid var(--gray-100);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: white;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon.info[_ngcontent-%COMP%] {\\n  background: var(--primary-500);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon.warning[_ngcontent-%COMP%] {\\n  background: var(--warning-500);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon.error[_ngcontent-%COMP%] {\\n  background: var(--error-500);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon.success[_ngcontent-%COMP%] {\\n  background: var(--success-500);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%] {\\n  color: var(--gray-700);\\n  margin-bottom: var(--spacing-xs);\\n  font-size: 0.875rem;\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n}\\n.notifications-card[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-action[_ngcontent-%COMP%] {\\n  color: var(--gray-400) !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.stat-card[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n  animation-fill-mode: both;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0.1s;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.3s;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(4) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n@media (max-width: 1400px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-areas: \\\"sales sales sales sales sales sales orders orders orders orders orders orders\\\" \\\"products products products products products products status status status status status status\\\" \\\"actions actions actions actions actions actions notifications notifications notifications notifications notifications notifications\\\";\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: var(--spacing-xl);\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-areas: \\\"sales sales sales sales sales sales sales sales sales sales sales sales\\\" \\\"orders orders orders orders orders orders products products products products products products\\\" \\\"status status status status actions actions actions actions notifications notifications notifications notifications\\\";\\n  }\\n}\\n@media (max-width: 992px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    grid-template-areas: \\\"sales\\\" \\\"orders\\\" \\\"products\\\" \\\"status\\\" \\\"actions\\\" \\\"notifications\\\";\\n    gap: var(--spacing-xl);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .page-header[_ngcontent-%COMP%] {\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n    padding: var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: var(--spacing-lg);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n    padding: 0;\\n  }\\n  .stat-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    min-height: 100px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    gap: var(--spacing-lg);\\n    padding: 0;\\n  }\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  }\\n  .card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) !important;\\n  }\\n  .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-md);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-lg);\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    gap: var(--spacing-md);\\n  }\\n  .stat-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n    gap: var(--spacing-md);\\n    min-height: 80px;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    gap: var(--spacing-md);\\n  }\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) var(--spacing-lg) !important;\\n  }\\n  .card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DashboardNewComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "interval", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatMenuModule", "MatBadgeModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "order_r2", "id", "ɵɵtextInterpolate", "customerName", "ɵɵpipeBind2", "createdAt", "total", "ɵɵclassMap", "status", "ctx_r2", "getOrderStatusText", "ɵɵelement", "i_r5", "ɵɵproperty", "product_r4", "image", "ɵɵsanitizeUrl", "name", "category", "salesCount", "salesAmount", "notification_r6", "type", "getNotificationIcon", "title", "message", "DashboardNewComponent", "http", "salesChartRef", "isLoading", "currentDate", "Date", "currentTime", "timeSubscription", "dataSubscription", "stats", "todaySales", "todayOrders", "customers", "products", "systemStatus", "overall", "api", "database", "cache", "recentOrders", "topProducts", "notifications", "salesChart", "constructor", "ngOnInit", "initializeComponent", "loadDashboardData", "startTimeUpdater", "setupDataRefresh", "ngAfterViewInit", "setTimeout", "initializeSales<PERSON>hart", "ngOnDestroy", "unsubscribe", "destroy", "now", "get", "subscribe", "next", "response", "customersCount", "productsCount", "console", "log", "error", "Database", "Connected", "calculateOverallStatus", "getHours", "getMinutes", "getSeconds", "nativeElement", "ctx", "getContext", "chartData", "labels", "datasets", "label", "data", "borderColor", "backgroundColor", "tension", "fill", "statuses", "every", "some", "statusMap", "getSystemStatusText", "getStatusText", "iconMap", "openPOS", "window", "open", "showMessage", "refreshData", "onNotificationClick", "notification", "viewAllNotifications", "viewOrderDetails", "order", "viewAllProducts", "viewDetailedReports", "ɵɵdirectiveInject", "i1", "HttpClient", "selectors", "viewQuery", "DashboardNewComponent_Query", "rf", "ɵɵtemplate", "DashboardNewComponent_div_104_Template", "DashboardNewComponent_div_114_Template", "ɵɵlistener", "DashboardNewComponent_Template_button_click_158_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "DashboardNewComponent_div_197_Template", "DashboardNewComponent_div_198_Template", "ɵɵclassProp", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i3", "RouterLink", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "i6", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\dashboard\\dashboard-new.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\dashboard\\dashboard-new.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { interval, Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\n\n@Component({\n  selector: 'app-dashboard-new',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatMenuModule,\n    MatBadgeModule\n  ],\n  templateUrl: './dashboard-new.component.html',\n  styleUrls: ['./dashboard-new.component.scss']\n})\nexport class DashboardNewComponent implements OnInit, OnDestroy, AfterViewInit {\n\n  @ViewChild('salesChart', { static: false }) salesChartRef!: ElementRef<HTMLCanvasElement>;\n\n  // Component State\n  isLoading = true;\n  currentDate = new Date();\n  currentTime = new Date();\n\n  // Subscriptions\n  private timeSubscription?: Subscription;\n  private dataSubscription?: Subscription;\n\n  // Dashboard Data\n  stats = {\n    todaySales: 0,\n    todayOrders: 0,\n    customers: 0,\n    products: 0\n  };\n\n  systemStatus = {\n    overall: 'healthy',\n    api: 'connected',\n    database: 'connected',\n    cache: 'connected'\n  };\n\n  recentOrders: any[] = [];\n  topProducts: any[] = [];\n  notifications: any[] = [];\n\n  // Chart instance\n  private salesChart: any;\n\n  constructor(private http: HttpClient) {}\n\n  ngOnInit(): void {\n    this.initializeComponent();\n    this.loadDashboardData();\n    this.startTimeUpdater();\n    this.setupDataRefresh();\n  }\n\n  ngAfterViewInit(): void {\n    // Initialize charts after view is ready\n    setTimeout(() => {\n      this.initializeSalesChart();\n    }, 100);\n  }\n\n  ngOnDestroy(): void {\n    // Clean up subscriptions\n    this.timeSubscription?.unsubscribe();\n    this.dataSubscription?.unsubscribe();\n    \n    // Destroy chart\n    if (this.salesChart) {\n      this.salesChart.destroy();\n    }\n  }\n\n  /**\n   * Initialize component with default data\n   */\n  private initializeComponent(): void {\n    // Set initial mock data\n    this.stats = {\n      todaySales: 25750,\n      todayOrders: 156,\n      customers: 24,\n      products: 5\n    };\n\n    this.recentOrders = [\n      {\n        id: '2024001',\n        customerName: 'أحمد محمد علي',\n        total: 1250,\n        status: 'completed',\n        createdAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago\n      },\n      {\n        id: '2024002',\n        customerName: 'فاطمة أحمد',\n        total: 850,\n        status: 'pending',\n        createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago\n      },\n      {\n        id: '2024003',\n        customerName: 'محمد حسن',\n        total: 2100,\n        status: 'completed',\n        createdAt: new Date(Date.now() - 1000 * 60 * 45) // 45 minutes ago\n      }\n    ];\n\n    this.topProducts = [\n      {\n        name: 'لابتوب Dell Inspiron',\n        category: 'أجهزة كمبيوتر',\n        salesCount: 45,\n        salesAmount: 67500,\n        image: 'assets/images/product-placeholder.svg'\n      },\n      {\n        name: 'هاتف Samsung Galaxy',\n        category: 'هواتف ذكية',\n        salesCount: 38,\n        salesAmount: 45600,\n        image: 'assets/images/product-placeholder.svg'\n      },\n      {\n        name: 'سماعات Sony',\n        category: 'إكسسوارات',\n        salesCount: 62,\n        salesAmount: 18600,\n        image: 'assets/images/product-placeholder.svg'\n      }\n    ];\n\n    this.notifications = [\n      {\n        type: 'warning',\n        title: 'نفاد مخزون',\n        message: 'المنتج \"لابتوب Dell\" أوشك على النفاد',\n        createdAt: new Date(Date.now() - 1000 * 60 * 10)\n      },\n      {\n        type: 'info',\n        title: 'طلب جديد',\n        message: 'تم استلام طلب جديد من العميل أحمد محمد',\n        createdAt: new Date(Date.now() - 1000 * 60 * 20)\n      },\n      {\n        type: 'success',\n        title: 'تم الدفع',\n        message: 'تم استلام دفعة بقيمة 5000 جنيه',\n        createdAt: new Date(Date.now() - 1000 * 60 * 35)\n      }\n    ];\n  }\n\n  /**\n   * Load dashboard data from API\n   */\n  private loadDashboardData(): void {\n    this.isLoading = true;\n\n    // Load statistics\n    this.http.get<any>('http://localhost:5127/api/simple/statistics').subscribe({\n      next: (response) => {\n        this.stats.customers = response.customersCount || this.stats.customers;\n        this.stats.products = response.productsCount || this.stats.products;\n        console.log('Statistics loaded:', response);\n      },\n      error: (error) => {\n        console.error('Error loading statistics:', error);\n      }\n    });\n\n    // Check system status\n    this.http.get('http://localhost:5127/health').subscribe({\n      next: (response: any) => {\n        this.systemStatus.api = 'connected';\n        this.systemStatus.database = response.Database?.Connected ? 'connected' : 'disconnected';\n        this.systemStatus.overall = this.calculateOverallStatus();\n      },\n      error: () => {\n        this.systemStatus.api = 'disconnected';\n        this.systemStatus.database = 'disconnected';\n        this.systemStatus.overall = 'error';\n      }\n    });\n\n    // Simulate loading delay\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1500);\n  }\n\n  /**\n   * Start time updater\n   */\n  private startTimeUpdater(): void {\n    this.timeSubscription = interval(1000).subscribe(() => {\n      this.currentTime = new Date();\n      \n      // Update date at midnight\n      const now = new Date();\n      if (now.getHours() === 0 && now.getMinutes() === 0 && now.getSeconds() === 0) {\n        this.currentDate = now;\n      }\n    });\n  }\n\n  /**\n   * Setup automatic data refresh\n   */\n  private setupDataRefresh(): void {\n    // Refresh data every 5 minutes\n    this.dataSubscription = interval(5 * 60 * 1000).subscribe(() => {\n      this.loadDashboardData();\n    });\n  }\n\n  /**\n   * Initialize sales chart\n   */\n  private initializeSalesChart(): void {\n    if (!this.salesChartRef?.nativeElement) {\n      return;\n    }\n\n    const ctx = this.salesChartRef.nativeElement.getContext('2d');\n    if (!ctx) {\n      return;\n    }\n\n    // Mock chart data\n    const chartData = {\n      labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],\n      datasets: [{\n        label: 'المبيعات',\n        data: [12000, 19000, 15000, 25000, 22000, 30000],\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        tension: 0.4,\n        fill: true\n      }]\n    };\n\n    // Note: You would need to install Chart.js for this to work\n    // For now, we'll just log that the chart would be initialized\n    console.log('Sales chart would be initialized with data:', chartData);\n  }\n\n  /**\n   * Calculate overall system status\n   */\n  private calculateOverallStatus(): string {\n    const statuses = [this.systemStatus.api, this.systemStatus.database, this.systemStatus.cache];\n    \n    if (statuses.every(status => status === 'connected')) {\n      return 'healthy';\n    } else if (statuses.some(status => status === 'disconnected')) {\n      return 'error';\n    } else {\n      return 'warning';\n    }\n  }\n\n  /**\n   * Get order status text\n   */\n  getOrderStatusText(status: string): string {\n    const statusMap: { [key: string]: string } = {\n      'pending': 'قيد الانتظار',\n      'completed': 'مكتمل',\n      'cancelled': 'ملغي',\n      'processing': 'قيد المعالجة'\n    };\n    \n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get system status text\n   */\n  getSystemStatusText(status: string): string {\n    const statusMap: { [key: string]: string } = {\n      'healthy': 'سليم',\n      'warning': 'تحذير',\n      'error': 'خطأ'\n    };\n    \n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get status text\n   */\n  getStatusText(status: string): string {\n    const statusMap: { [key: string]: string } = {\n      'connected': 'متصل',\n      'disconnected': 'غير متصل',\n      'warning': 'تحذير'\n    };\n    \n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get notification icon\n   */\n  getNotificationIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'info': 'info',\n      'warning': 'warning',\n      'error': 'error',\n      'success': 'check_circle'\n    };\n    \n    return iconMap[type] || 'notifications';\n  }\n\n  /**\n   * Open POS system\n   */\n  openPOS(): void {\n    window.open('http://localhost:5127/swagger', '_blank');\n    this.showMessage('فتح نقطة البيع في نافذة جديدة');\n  }\n\n  /**\n   * Refresh dashboard data\n   */\n  refreshData(): void {\n    this.loadDashboardData();\n    this.showMessage('تم تحديث البيانات');\n  }\n\n  /**\n   * Show message (you can implement a toast service)\n   */\n  private showMessage(message: string): void {\n    console.log(message);\n    // Implement toast notification here\n  }\n\n  /**\n   * Handle notification click\n   */\n  onNotificationClick(notification: any): void {\n    console.log('Notification clicked:', notification);\n    // Implement notification handling\n  }\n\n  /**\n   * View all notifications\n   */\n  viewAllNotifications(): void {\n    console.log('View all notifications');\n    // Navigate to notifications page\n  }\n\n  /**\n   * View order details\n   */\n  viewOrderDetails(order: any): void {\n    console.log('View order details:', order);\n    // Navigate to order details page\n  }\n\n  /**\n   * View all products\n   */\n  viewAllProducts(): void {\n    console.log('View all products');\n    // Navigate to products page\n  }\n\n  /**\n   * View detailed reports\n   */\n  viewDetailedReports(): void {\n    console.log('View detailed reports');\n    // Navigate to reports page\n  }\n}\n", "<!-- Terra Retail ERP - Professional Dashboard -->\n<div class=\"dashboard-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <h1 class=\"page-title\">لوحة التحكم</h1>\n        <p class=\"page-subtitle\">مرحباً بك في نظام Terra Retail ERP</p>\n      </div>\n      <div class=\"header-right\">\n        <div class=\"date-time\">\n          <div class=\"current-date\">{{ currentDate | date:'EEEE, dd MMMM yyyy' }}</div>\n          <div class=\"current-time\">{{ currentTime | date:'HH:mm' }}</div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Quick Stats Cards -->\n  <div class=\"stats-grid\">\n    <div class=\"stat-card sales-card\" [class.loading]=\"isLoading\">\n      <div class=\"stat-icon\">\n        <mat-icon>trending_up</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ stats.todaySales | number:'1.0-0' }} جنيه</div>\n        <div class=\"stat-label\">مبيعات اليوم</div>\n        <div class=\"stat-change positive\">\n          <mat-icon>arrow_upward</mat-icon>\n          <span>+12.5%</span>\n        </div>\n      </div>\n      <div class=\"stat-chart\">\n        <!-- Mini chart placeholder -->\n        <div class=\"mini-chart sales-chart\"></div>\n      </div>\n    </div>\n\n    <div class=\"stat-card orders-card\" [class.loading]=\"isLoading\">\n      <div class=\"stat-icon\">\n        <mat-icon>shopping_cart</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ stats.todayOrders }}</div>\n        <div class=\"stat-label\">طلبات اليوم</div>\n        <div class=\"stat-change positive\">\n          <mat-icon>arrow_upward</mat-icon>\n          <span>+8.2%</span>\n        </div>\n      </div>\n      <div class=\"stat-chart\">\n        <div class=\"mini-chart orders-chart\"></div>\n      </div>\n    </div>\n\n    <div class=\"stat-card customers-card\" [class.loading]=\"isLoading\">\n      <div class=\"stat-icon\">\n        <mat-icon>people</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ stats.customers }}</div>\n        <div class=\"stat-label\">إجمالي العملاء</div>\n        <div class=\"stat-change positive\">\n          <mat-icon>arrow_upward</mat-icon>\n          <span>+5.1%</span>\n        </div>\n      </div>\n      <div class=\"stat-chart\">\n        <div class=\"mini-chart customers-chart\"></div>\n      </div>\n    </div>\n\n    <div class=\"stat-card products-card\" [class.loading]=\"isLoading\">\n      <div class=\"stat-icon\">\n        <mat-icon>inventory_2</mat-icon>\n      </div>\n      <div class=\"stat-content\">\n        <div class=\"stat-value\">{{ stats.products }}</div>\n        <div class=\"stat-label\">المنتجات</div>\n        <div class=\"stat-change neutral\">\n          <mat-icon>remove</mat-icon>\n          <span>0%</span>\n        </div>\n      </div>\n      <div class=\"stat-chart\">\n        <div class=\"mini-chart products-chart\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content Grid -->\n  <div class=\"content-grid\">\n    \n    <!-- Sales Chart -->\n    <div class=\"chart-card sales-overview\">\n      <div class=\"card-header\">\n        <h3>نظرة عامة على المبيعات</h3>\n        <div class=\"card-actions\">\n          <button mat-button color=\"primary\">عرض التفاصيل</button>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"chart-container\">\n          <canvas #salesChart></canvas>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Orders -->\n    <div class=\"data-card recent-orders\">\n      <div class=\"card-header\">\n        <h3>الطلبات الأخيرة</h3>\n        <div class=\"card-actions\">\n          <button mat-icon-button>\n            <mat-icon>refresh</mat-icon>\n          </button>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"orders-list\">\n          <div class=\"order-item\" *ngFor=\"let order of recentOrders\">\n            <div class=\"order-info\">\n              <div class=\"order-number\">#{{ order.id }}</div>\n              <div class=\"order-customer\">{{ order.customerName }}</div>\n              <div class=\"order-time\">{{ order.createdAt | date:'short' }}</div>\n            </div>\n            <div class=\"order-amount\">{{ order.total | number:'1.0-0' }} جنيه</div>\n            <div class=\"order-status\" [class]=\"order.status\">\n              <span>{{ getOrderStatusText(order.status) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Top Products -->\n    <div class=\"data-card top-products\">\n      <div class=\"card-header\">\n        <h3>أفضل المنتجات مبيعاً</h3>\n        <div class=\"card-actions\">\n          <button mat-button color=\"primary\">عرض الكل</button>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"products-list\">\n          <div class=\"product-item\" *ngFor=\"let product of topProducts; let i = index\">\n            <div class=\"product-rank\">{{ i + 1 }}</div>\n            <div class=\"product-image\">\n              <img [src]=\"product.image || 'assets/images/product-placeholder.svg'\" [alt]=\"product.name\">\n            </div>\n            <div class=\"product-info\">\n              <div class=\"product-name\">{{ product.name }}</div>\n              <div class=\"product-category\">{{ product.category }}</div>\n            </div>\n            <div class=\"product-sales\">\n              <div class=\"sales-count\">{{ product.salesCount }} مبيعة</div>\n              <div class=\"sales-amount\">{{ product.salesAmount | number:'1.0-0' }} جنيه</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- System Status -->\n    <div class=\"status-card system-status\">\n      <div class=\"card-header\">\n        <h3>حالة النظام</h3>\n        <div class=\"card-actions\">\n          <div class=\"status-indicator\" [class]=\"systemStatus.overall\">\n            <span>{{ getSystemStatusText(systemStatus.overall) }}</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"status-items\">\n          <div class=\"status-item\">\n            <div class=\"status-icon\">\n              <mat-icon [class]=\"systemStatus.api\">cloud</mat-icon>\n            </div>\n            <div class=\"status-info\">\n              <div class=\"status-name\">خدمة API</div>\n              <div class=\"status-value\">{{ getStatusText(systemStatus.api) }}</div>\n            </div>\n          </div>\n\n          <div class=\"status-item\">\n            <div class=\"status-icon\">\n              <mat-icon [class]=\"systemStatus.database\">storage</mat-icon>\n            </div>\n            <div class=\"status-info\">\n              <div class=\"status-name\">قاعدة البيانات</div>\n              <div class=\"status-value\">{{ getStatusText(systemStatus.database) }}</div>\n            </div>\n          </div>\n\n          <div class=\"status-item\">\n            <div class=\"status-icon\">\n              <mat-icon [class]=\"systemStatus.cache\">memory</mat-icon>\n            </div>\n            <div class=\"status-info\">\n              <div class=\"status-name\">ذاكرة التخزين المؤقت</div>\n              <div class=\"status-value\">{{ getStatusText(systemStatus.cache) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"actions-card quick-actions\">\n      <div class=\"card-header\">\n        <h3>إجراءات سريعة</h3>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"actions-grid\">\n          <button mat-raised-button color=\"primary\" class=\"action-btn\" (click)=\"openPOS()\">\n            <mat-icon>point_of_sale</mat-icon>\n            <span>فتح نقطة البيع</span>\n          </button>\n\n          <button mat-raised-button color=\"accent\" class=\"action-btn\" routerLink=\"/customers/add\">\n            <mat-icon>person_add</mat-icon>\n            <span>إضافة عميل</span>\n          </button>\n\n          <button mat-raised-button class=\"action-btn\" routerLink=\"/products/add\">\n            <mat-icon>add_box</mat-icon>\n            <span>إضافة منتج</span>\n          </button>\n\n          <button mat-raised-button class=\"action-btn\" routerLink=\"/reports\">\n            <mat-icon>assessment</mat-icon>\n            <span>عرض التقارير</span>\n          </button>\n\n          <button mat-raised-button class=\"action-btn\" routerLink=\"/inventory\">\n            <mat-icon>inventory</mat-icon>\n            <span>إدارة المخزون</span>\n          </button>\n\n          <button mat-raised-button class=\"action-btn\" routerLink=\"/settings\">\n            <mat-icon>settings</mat-icon>\n            <span>الإعدادات</span>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Notifications -->\n    <div class=\"notifications-card\">\n      <div class=\"card-header\">\n        <h3>الإشعارات</h3>\n        <div class=\"card-actions\">\n          <button mat-button color=\"primary\">عرض الكل</button>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"notifications-list\">\n          <div class=\"notification-item\" *ngFor=\"let notification of notifications\">\n            <div class=\"notification-icon\" [class]=\"notification.type\">\n              <mat-icon>{{ getNotificationIcon(notification.type) }}</mat-icon>\n            </div>\n            <div class=\"notification-content\">\n              <div class=\"notification-title\">{{ notification.title }}</div>\n              <div class=\"notification-message\">{{ notification.message }}</div>\n              <div class=\"notification-time\">{{ notification.createdAt | date:'short' }}</div>\n            </div>\n            <button mat-icon-button class=\"notification-action\">\n              <mat-icon>more_vert</mat-icon>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل البيانات...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,QAAQ,QAAsB,MAAM;AAE7C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;IC+G1CC,EAFJ,CAAAC,cAAA,cAA2D,cACjC,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;;IAC9DF,EAD8D,CAAAG,YAAA,EAAM,EAC9D;IACNH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAErEH,EADF,CAAAC,cAAA,eAAiD,YACzC;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAEhDF,EAFgD,CAAAG,YAAA,EAAO,EAC/C,EACF;;;;;IARwBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,kBAAA,MAAAC,QAAA,CAAAC,EAAA,CAAe;IACbP,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAF,QAAA,CAAAG,YAAA,CAAwB;IAC5BT,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAU,WAAA,OAAAJ,QAAA,CAAAK,SAAA,WAAoC;IAEpCX,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAU,WAAA,SAAAJ,QAAA,CAAAM,KAAA,wCAAuC;IACvCZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAa,UAAA,CAAAP,QAAA,CAAAQ,MAAA,CAAsB;IACxCd,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAQ,iBAAA,CAAAO,MAAA,CAAAC,kBAAA,CAAAV,QAAA,CAAAQ,MAAA,EAAsC;;;;;IAkB9Cd,EADF,CAAAC,cAAA,cAA6E,cACjD;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3CH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAiB,SAAA,cAA2F;IAC7FjB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACtDF,EADsD,CAAAG,YAAA,EAAM,EACtD;IAEJH,EADF,CAAAC,cAAA,eAA2B,eACA;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA+C;;IAE7EF,EAF6E,CAAAG,YAAA,EAAM,EAC3E,EACF;;;;;IAZsBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAQ,iBAAA,CAAAU,IAAA,KAAW;IAE9BlB,EAAA,CAAAI,SAAA,GAAgE;IAACJ,EAAjE,CAAAmB,UAAA,QAAAC,UAAA,CAAAC,KAAA,6CAAArB,EAAA,CAAAsB,aAAA,CAAgE,QAAAF,UAAA,CAAAG,IAAA,CAAqB;IAGhEvB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAQ,iBAAA,CAAAY,UAAA,CAAAG,IAAA,CAAkB;IACdvB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAQ,iBAAA,CAAAY,UAAA,CAAAI,QAAA,CAAsB;IAG3BxB,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,KAAAe,UAAA,CAAAK,UAAA,oCAA8B;IAC7BzB,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAU,WAAA,QAAAU,UAAA,CAAAM,WAAA,wCAA+C;;;;;IAwGzE1B,EAFJ,CAAAC,cAAA,cAA0E,cACb,eAC/C;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IACxDF,EADwD,CAAAG,YAAA,EAAW,EAC7D;IAEJH,EADF,CAAAC,cAAA,cAAkC,cACA;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAC5EF,EAD4E,CAAAG,YAAA,EAAM,EAC5E;IAEJH,EADF,CAAAC,cAAA,kBAAoD,gBACxC;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAEvBF,EAFuB,CAAAG,YAAA,EAAW,EACvB,EACL;;;;;IAX2BH,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAa,UAAA,CAAAc,eAAA,CAAAC,IAAA,CAA2B;IAC9C5B,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAQ,iBAAA,CAAAO,MAAA,CAAAc,mBAAA,CAAAF,eAAA,CAAAC,IAAA,EAA4C;IAGtB5B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,iBAAA,CAAAmB,eAAA,CAAAG,KAAA,CAAwB;IACtB9B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAmB,eAAA,CAAAI,OAAA,CAA0B;IAC7B/B,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAU,WAAA,QAAAiB,eAAA,CAAAhB,SAAA,WAA2C;;;;;IAatFX,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAiB,SAAA,sBAAyC;IACzCjB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;AD5PR,WAAa6B,qBAAqB;EAA5B,MAAOA,qBAAqB;IAmCZC,IAAA;IAjCwBC,aAAa;IAEzD;IACAC,SAAS,GAAG,IAAI;IAChBC,WAAW,GAAG,IAAIC,IAAI,EAAE;IACxBC,WAAW,GAAG,IAAID,IAAI,EAAE;IAExB;IACQE,gBAAgB;IAChBC,gBAAgB;IAExB;IACAC,KAAK,GAAG;MACNC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAEDC,YAAY,GAAG;MACbC,OAAO,EAAE,SAAS;MAClBC,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE;KACR;IAEDC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,aAAa,GAAU,EAAE;IAEzB;IACQC,UAAU;IAElBC,YAAoBtB,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAe;IAEvCuB,QAAQA,CAAA;MACN,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,gBAAgB,EAAE;IACzB;IAEAC,eAAeA,CAAA;MACb;MACAC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,oBAAoB,EAAE;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT;IAEAC,WAAWA,CAAA;MACT;MACA,IAAI,CAACzB,gBAAgB,EAAE0B,WAAW,EAAE;MACpC,IAAI,CAACzB,gBAAgB,EAAEyB,WAAW,EAAE;MAEpC;MACA,IAAI,IAAI,CAACX,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACY,OAAO,EAAE;MAC3B;IACF;IAEA;;;IAGQT,mBAAmBA,CAAA;MACzB;MACA,IAAI,CAAChB,KAAK,GAAG;QACXC,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,GAAG;QAChBC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;OACX;MAED,IAAI,CAACM,YAAY,GAAG,CAClB;QACE5C,EAAE,EAAE,SAAS;QACbE,YAAY,EAAE,eAAe;QAC7BG,KAAK,EAAE,IAAI;QACXE,MAAM,EAAE,WAAW;QACnBH,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;OAClD,EACD;QACE5D,EAAE,EAAE,SAAS;QACbE,YAAY,EAAE,YAAY;QAC1BG,KAAK,EAAE,GAAG;QACVE,MAAM,EAAE,SAAS;QACjBH,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;OAClD,EACD;QACE5D,EAAE,EAAE,SAAS;QACbE,YAAY,EAAE,UAAU;QACxBG,KAAK,EAAE,IAAI;QACXE,MAAM,EAAE,WAAW;QACnBH,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;OAClD,CACF;MAED,IAAI,CAACf,WAAW,GAAG,CACjB;QACE7B,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,eAAe;QACzBC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBL,KAAK,EAAE;OACR,EACD;QACEE,IAAI,EAAE,qBAAqB;QAC3BC,QAAQ,EAAE,YAAY;QACtBC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBL,KAAK,EAAE;OACR,EACD;QACEE,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,WAAW;QACrBC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBL,KAAK,EAAE;OACR,CACF;MAED,IAAI,CAACgC,aAAa,GAAG,CACnB;QACEzB,IAAI,EAAE,SAAS;QACfE,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE,sCAAsC;QAC/CpB,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;OAChD,EACD;QACEvC,IAAI,EAAE,MAAM;QACZE,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE,wCAAwC;QACjDpB,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;OAChD,EACD;QACEvC,IAAI,EAAE,SAAS;QACfE,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE,gCAAgC;QACzCpB,SAAS,EAAE,IAAI0B,IAAI,CAACA,IAAI,CAAC8B,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;OAChD,CACF;IACH;IAEA;;;IAGQT,iBAAiBA,CAAA;MACvB,IAAI,CAACvB,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,CAACF,IAAI,CAACmC,GAAG,CAAM,6CAA6C,CAAC,CAACC,SAAS,CAAC;QAC1EC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC9B,KAAK,CAACG,SAAS,GAAG2B,QAAQ,CAACC,cAAc,IAAI,IAAI,CAAC/B,KAAK,CAACG,SAAS;UACtE,IAAI,CAACH,KAAK,CAACI,QAAQ,GAAG0B,QAAQ,CAACE,aAAa,IAAI,IAAI,CAAChC,KAAK,CAACI,QAAQ;UACnE6B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAAC;QAC7C,CAAC;QACDK,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;OACD,CAAC;MAEF;MACA,IAAI,CAAC3C,IAAI,CAACmC,GAAG,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAAC;QACtDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACzB,YAAY,CAACE,GAAG,GAAG,WAAW;UACnC,IAAI,CAACF,YAAY,CAACG,QAAQ,GAAGsB,QAAQ,CAACM,QAAQ,EAAEC,SAAS,GAAG,WAAW,GAAG,cAAc;UACxF,IAAI,CAAChC,YAAY,CAACC,OAAO,GAAG,IAAI,CAACgC,sBAAsB,EAAE;QAC3D,CAAC;QACDH,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAC9B,YAAY,CAACE,GAAG,GAAG,cAAc;UACtC,IAAI,CAACF,YAAY,CAACG,QAAQ,GAAG,cAAc;UAC3C,IAAI,CAACH,YAAY,CAACC,OAAO,GAAG,OAAO;QACrC;OACD,CAAC;MAEF;MACAe,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3B,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,IAAI,CAAC;IACV;IAEA;;;IAGQwB,gBAAgBA,CAAA;MACtB,IAAI,CAACpB,gBAAgB,GAAG9C,QAAQ,CAAC,IAAI,CAAC,CAAC4E,SAAS,CAAC,MAAK;QACpD,IAAI,CAAC/B,WAAW,GAAG,IAAID,IAAI,EAAE;QAE7B;QACA,MAAM8B,GAAG,GAAG,IAAI9B,IAAI,EAAE;QACtB,IAAI8B,GAAG,CAACa,QAAQ,EAAE,KAAK,CAAC,IAAIb,GAAG,CAACc,UAAU,EAAE,KAAK,CAAC,IAAId,GAAG,CAACe,UAAU,EAAE,KAAK,CAAC,EAAE;UAC5E,IAAI,CAAC9C,WAAW,GAAG+B,GAAG;QACxB;MACF,CAAC,CAAC;IACJ;IAEA;;;IAGQP,gBAAgBA,CAAA;MACtB;MACA,IAAI,CAACpB,gBAAgB,GAAG/C,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC4E,SAAS,CAAC,MAAK;QAC7D,IAAI,CAACX,iBAAiB,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA;;;IAGQK,oBAAoBA,CAAA;MAC1B,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAEiD,aAAa,EAAE;QACtC;MACF;MAEA,MAAMC,GAAG,GAAG,IAAI,CAAClD,aAAa,CAACiD,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACD,GAAG,EAAE;QACR;MACF;MAEA;MACA,MAAME,SAAS,GAAG;QAChBC,MAAM,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;QAC7DC,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;UAChDC,WAAW,EAAE,mBAAmB;UAChCC,eAAe,EAAE,yBAAyB;UAC1CC,OAAO,EAAE,GAAG;UACZC,IAAI,EAAE;SACP;OACF;MAED;MACA;MACApB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEW,SAAS,CAAC;IACvE;IAEA;;;IAGQP,sBAAsBA,CAAA;MAC5B,MAAMgB,QAAQ,GAAG,CAAC,IAAI,CAACjD,YAAY,CAACE,GAAG,EAAE,IAAI,CAACF,YAAY,CAACG,QAAQ,EAAE,IAAI,CAACH,YAAY,CAACI,KAAK,CAAC;MAE7F,IAAI6C,QAAQ,CAACC,KAAK,CAAClF,MAAM,IAAIA,MAAM,KAAK,WAAW,CAAC,EAAE;QACpD,OAAO,SAAS;MAClB,CAAC,MAAM,IAAIiF,QAAQ,CAACE,IAAI,CAACnF,MAAM,IAAIA,MAAM,KAAK,cAAc,CAAC,EAAE;QAC7D,OAAO,OAAO;MAChB,CAAC,MAAM;QACL,OAAO,SAAS;MAClB;IACF;IAEA;;;IAGAE,kBAAkBA,CAACF,MAAc;MAC/B,MAAMoF,SAAS,GAA8B;QAC3C,SAAS,EAAE,cAAc;QACzB,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,MAAM;QACnB,YAAY,EAAE;OACf;MAED,OAAOA,SAAS,CAACpF,MAAM,CAAC,IAAIA,MAAM;IACpC;IAEA;;;IAGAqF,mBAAmBA,CAACrF,MAAc;MAChC,MAAMoF,SAAS,GAA8B;QAC3C,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,OAAO;QAClB,OAAO,EAAE;OACV;MAED,OAAOA,SAAS,CAACpF,MAAM,CAAC,IAAIA,MAAM;IACpC;IAEA;;;IAGAsF,aAAaA,CAACtF,MAAc;MAC1B,MAAMoF,SAAS,GAA8B;QAC3C,WAAW,EAAE,MAAM;QACnB,cAAc,EAAE,UAAU;QAC1B,SAAS,EAAE;OACZ;MAED,OAAOA,SAAS,CAACpF,MAAM,CAAC,IAAIA,MAAM;IACpC;IAEA;;;IAGAe,mBAAmBA,CAACD,IAAY;MAC9B,MAAMyE,OAAO,GAA8B;QACzC,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,SAAS;QACpB,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE;OACZ;MAED,OAAOA,OAAO,CAACzE,IAAI,CAAC,IAAI,eAAe;IACzC;IAEA;;;IAGA0E,OAAOA,CAAA;MACLC,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAE,QAAQ,CAAC;MACtD,IAAI,CAACC,WAAW,CAAC,+BAA+B,CAAC;IACnD;IAEA;;;IAGAC,WAAWA,CAAA;MACT,IAAI,CAAChD,iBAAiB,EAAE;MACxB,IAAI,CAAC+C,WAAW,CAAC,mBAAmB,CAAC;IACvC;IAEA;;;IAGQA,WAAWA,CAAC1E,OAAe;MACjC2C,OAAO,CAACC,GAAG,CAAC5C,OAAO,CAAC;MACpB;IACF;IAEA;;;IAGA4E,mBAAmBA,CAACC,YAAiB;MACnClC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiC,YAAY,CAAC;MAClD;IACF;IAEA;;;IAGAC,oBAAoBA,CAAA;MAClBnC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA;;;IAGAmC,gBAAgBA,CAACC,KAAU;MACzBrC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoC,KAAK,CAAC;MACzC;IACF;IAEA;;;IAGAC,eAAeA,CAAA;MACbtC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC;IACF;IAEA;;;IAGAsC,mBAAmBA,CAAA;MACjBvC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC;IACF;;uCAjXW3C,qBAAqB,EAAAhC,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;YAArBpF,qBAAqB;MAAAqF,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAApC,GAAA;QAAA,IAAAoC,EAAA;;;;;;;;;;;;;;UCvB1BxH,EANR,CAAAC,cAAA,aAAiC,aAGN,aACK,aACD,YACA;UAAAD,EAAA,CAAAE,MAAA,oEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,+GAAkC;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EAC3D;UAGFH,EAFJ,CAAAC,cAAA,aAA0B,aACD,cACK;UAAAD,EAAA,CAAAE,MAAA,IAA6C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7EH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,IAAgC;;UAIlEF,EAJkE,CAAAG,YAAA,EAAM,EAC5D,EACF,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAAwB,eACwC,eACrC,gBACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAA4C;;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1EH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAExCH,EADF,CAAAC,cAAA,eAAkC,gBACtB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAEhBF,EAFgB,CAAAG,YAAA,EAAO,EACf,EACF;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAEtBD,EAAA,CAAAiB,SAAA,eAA0C;UAE9CjB,EADE,CAAAG,YAAA,EAAM,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA+D,eACtC,gBACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACzBF,EADyB,CAAAG,YAAA,EAAW,EAC9B;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEvCH,EADF,CAAAC,cAAA,eAAkC,gBACtB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAEfF,EAFe,CAAAG,YAAA,EAAO,EACd,EACF;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAiB,SAAA,eAA2C;UAE/CjB,EADE,CAAAG,YAAA,EAAM,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAkE,eACzC,gBACX;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAE1CH,EADF,CAAAC,cAAA,eAAkC,gBACtB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAEfF,EAFe,CAAAG,YAAA,EAAO,EACd,EACF;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAiB,SAAA,eAA8C;UAElDjB,EADE,CAAAG,YAAA,EAAM,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAiE,eACxC,gBACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEpCH,EADF,CAAAC,cAAA,eAAiC,gBACrB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAEZF,EAFY,CAAAG,YAAA,EAAO,EACX,EACF;UACNH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAiB,SAAA,eAA6C;UAGnDjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAQAH,EALN,CAAAC,cAAA,eAA0B,eAGe,eACZ,UACnB;UAAAD,EAAA,CAAAE,MAAA,6HAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7BH,EADF,CAAAC,cAAA,eAA0B,kBACW;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAEnDF,EAFmD,CAAAG,YAAA,EAAS,EACpD,EACF;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACK;UAC3BD,EAAA,CAAAiB,SAAA,uBAA6B;UAGnCjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,eAAqC,eACV,UACnB;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAFJ,CAAAC,cAAA,eAA0B,kBACA,iBACZ;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAGvBF,EAHuB,CAAAG,YAAA,EAAW,EACrB,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACC;UACvBD,EAAA,CAAAyH,UAAA,MAAAC,sCAAA,oBAA2D;UAajE1H,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,gBAAoC,gBACT,WACnB;UAAAD,EAAA,CAAAE,MAAA,uHAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE3BH,EADF,CAAAC,cAAA,gBAA0B,mBACW;UAAAD,EAAA,CAAAE,MAAA,oDAAQ;UAE/CF,EAF+C,CAAAG,YAAA,EAAS,EAChD,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACG;UACzBD,EAAA,CAAAyH,UAAA,MAAAE,sCAAA,oBAA6E;UAgBnF3H,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,gBAAuC,gBACZ,WACnB;UAAAD,EAAA,CAAAE,MAAA,sEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGhBH,EAFJ,CAAAC,cAAA,gBAA0B,gBACqC,aACrD;UAAAD,EAAA,CAAAE,MAAA,KAA+C;UAG3DF,EAH2D,CAAAG,YAAA,EAAO,EACxD,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,gBAA0B,gBACE,gBACC,gBACE,iBACc;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAC5CF,EAD4C,CAAAG,YAAA,EAAW,EACjD;UAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACE;UAAAD,EAAA,CAAAE,MAAA,qCAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACvCH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,KAAqC;UAEnEF,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAAyB,gBACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UACnDF,EADmD,CAAAG,YAAA,EAAW,EACxD;UAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACE;UAAAD,EAAA,CAAAE,MAAA,wFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,KAA0C;UAExEF,EAFwE,CAAAG,YAAA,EAAM,EACtE,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAAyB,gBACE,iBACgB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAC/CF,EAD+C,CAAAG,YAAA,EAAW,EACpD;UAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACE;UAAAD,EAAA,CAAAE,MAAA,uHAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,KAAuC;UAK3EF,EAL2E,CAAAG,YAAA,EAAM,EACnE,EACF,EACF,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACb,WACnB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UACnBF,EADmB,CAAAG,YAAA,EAAK,EAClB;UAGFH,EAFJ,CAAAC,cAAA,gBAA0B,gBACE,mBACyD;UAApBD,EAAA,CAAA4H,UAAA,mBAAAC,yDAAA;YAAA7H,EAAA,CAAA8H,aAAA,CAAAC,GAAA;YAAA,OAAA/H,EAAA,CAAAgI,WAAA,CAAS5C,GAAA,CAAAkB,OAAA,EAAS;UAAA,EAAC;UAC9EtG,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,mFAAc;UACtBF,EADsB,CAAAG,YAAA,EAAO,EACpB;UAGPH,EADF,CAAAC,cAAA,mBAAwF,iBAC5E;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;UAGPH,EADF,CAAAC,cAAA,mBAAwE,iBAC5D;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;UAGPH,EADF,CAAAC,cAAA,mBAAmE,iBACvD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,4EAAY;UACpBF,EADoB,CAAAG,YAAA,EAAO,EAClB;UAGPH,EADF,CAAAC,cAAA,mBAAqE,iBACzD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UACrBF,EADqB,CAAAG,YAAA,EAAO,EACnB;UAGPH,EADF,CAAAC,cAAA,mBAAoE,iBACxD;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,+DAAS;UAIvBF,EAJuB,CAAAG,YAAA,EAAO,EACf,EACL,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,gBAAgC,gBACL,WACnB;UAAAD,EAAA,CAAAE,MAAA,+DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEhBH,EADF,CAAAC,cAAA,gBAA0B,mBACW;UAAAD,EAAA,CAAAE,MAAA,oDAAQ;UAE/CF,EAF+C,CAAAG,YAAA,EAAS,EAChD,EACF;UAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACQ;UAC9BD,EAAA,CAAAyH,UAAA,MAAAQ,sCAAA,mBAA0E;UAiBlFjI,EAJM,CAAAG,YAAA,EAAM,EACF,EACF,EAEF;UAGNH,EAAA,CAAAyH,UAAA,MAAAS,sCAAA,kBAA+C;UAKjDlI,EAAA,CAAAG,YAAA,EAAM;;;UAhR8BH,EAAA,CAAAI,SAAA,IAA6C;UAA7CJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAU,WAAA,SAAA0E,GAAA,CAAAhD,WAAA,wBAA6C;UAC7CpC,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAU,WAAA,SAAA0E,GAAA,CAAA9C,WAAA,WAAgC;UAQ9BtC,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAmI,WAAA,YAAA/C,GAAA,CAAAjD,SAAA,CAA2B;UAKjCnC,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAU,WAAA,SAAA0E,GAAA,CAAA3C,KAAA,CAAAC,UAAA,wCAA4C;UAarC1C,EAAA,CAAAI,SAAA,IAA2B;UAA3BJ,EAAA,CAAAmI,WAAA,YAAA/C,GAAA,CAAAjD,SAAA,CAA2B;UAKlCnC,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAA3C,KAAA,CAAAE,WAAA,CAAuB;UAYb3C,EAAA,CAAAI,SAAA,IAA2B;UAA3BJ,EAAA,CAAAmI,WAAA,YAAA/C,GAAA,CAAAjD,SAAA,CAA2B;UAKrCnC,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAA3C,KAAA,CAAAG,SAAA,CAAqB;UAYZ5C,EAAA,CAAAI,SAAA,IAA2B;UAA3BJ,EAAA,CAAAmI,WAAA,YAAA/C,GAAA,CAAAjD,SAAA,CAA2B;UAKpCnC,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAA3C,KAAA,CAAAI,QAAA,CAAoB;UA2CA7C,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAmB,UAAA,YAAAiE,GAAA,CAAAjC,YAAA,CAAe;UAyBXnD,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAmB,UAAA,YAAAiE,GAAA,CAAAhC,WAAA,CAAgB;UAuBhCpD,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAa,UAAA,CAAAuE,GAAA,CAAAtC,YAAA,CAAAC,OAAA,CAA8B;UACpD/C,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAAe,mBAAA,CAAAf,GAAA,CAAAtC,YAAA,CAAAC,OAAA,EAA+C;UAQzC/C,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAa,UAAA,CAAAuE,GAAA,CAAAtC,YAAA,CAAAE,GAAA,CAA0B;UAIVhD,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAAgB,aAAA,CAAAhB,GAAA,CAAAtC,YAAA,CAAAE,GAAA,EAAqC;UAMrDhD,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAa,UAAA,CAAAuE,GAAA,CAAAtC,YAAA,CAAAG,QAAA,CAA+B;UAIfjD,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAAgB,aAAA,CAAAhB,GAAA,CAAAtC,YAAA,CAAAG,QAAA,EAA0C;UAM1DjD,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAa,UAAA,CAAAuE,GAAA,CAAAtC,YAAA,CAAAI,KAAA,CAA4B;UAIZlD,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAQ,iBAAA,CAAA4E,GAAA,CAAAgB,aAAA,CAAAhB,GAAA,CAAAtC,YAAA,CAAAI,KAAA,EAAuC;UAyDblD,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAmB,UAAA,YAAAiE,GAAA,CAAA/B,aAAA,CAAgB;UAoBlDrD,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAmB,UAAA,SAAAiE,GAAA,CAAAjD,SAAA,CAAe;;;qBDrQ3C5C,YAAY,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,QAAA,EACZhJ,YAAY,EAAAiJ,EAAA,CAAAC,UAAA,EACZhJ,aAAa,EACbC,eAAe,EAAAgJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjJ,aAAa,EAAAkJ,EAAA,CAAAC,OAAA,EACblJ,wBAAwB,EAAAmJ,EAAA,CAAAC,kBAAA,EACxBnJ,aAAa,EACbC,cAAc;MAAAmJ,MAAA;IAAA;;SAKLlH,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}