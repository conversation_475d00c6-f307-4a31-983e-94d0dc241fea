import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';

// Interfaces
interface SupplierDetails {
  id: number;
  supplierCode: string;
  nameAr: string;
  nameEn?: string;
  supplierTypeId?: number;
  phone1: string;
  phone2?: string;
  email?: string;
  website?: string;
  address?: string;
  contactPersonName?: string;
  contactPersonPhone?: string;
  contactPersonEmail?: string;
  paymentTerms: number;
  creditLimit: number;
  currentBalance: number;
  taxNumber?: string;
  commercialRegister?: string;
  bankName?: string;
  bankAccountNumber?: string;
  rating?: number;
  notes?: string;
  isActive: boolean;
  createdAt: string;
}

interface SupplierProduct {
  id: number;
  productCode: string;
  nameAr: string;
  nameEn: string;
  categoryName: string;
  unitPrice: number;
  lastOrderDate: string;
  totalOrdered: number;
}

interface SupplierTransaction {
  id: number;
  transactionType: string;
  amount: number;
  date: string;
  description: string;
  referenceNumber: string;
}

@Component({
  selector: 'app-supplier-details',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatListModule
  ],
  templateUrl: './supplier-details.component.html',
  styleUrls: ['./supplier-details.component.scss']
})
export class SupplierDetailsComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  supplierId: number = 0;
  
  // Data
  supplier: SupplierDetails | null = null;
  supplierProducts: SupplierProduct[] = [];
  supplierTransactions: SupplierTransaction[] = [];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.supplierId = Number(this.route.snapshot.paramMap.get('id'));
    if (this.supplierId) {
      this.loadSupplierDetails();
      this.loadSupplierProducts();
      this.loadSupplierTransactions();
    } else {
      this.router.navigate(['/suppliers']);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load supplier details
   */
  private loadSupplierDetails(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({
      next: (response) => {
        console.log('Supplier details API response:', response);
        // Map API response to match our interface
        if (response.supplier) {
          this.supplier = {
            id: response.supplier.Id || response.supplier.id,
            supplierCode: response.supplier.SupplierCode || response.supplier.supplierCode,
            nameAr: response.supplier.NameAr || response.supplier.nameAr,
            nameEn: response.supplier.NameEn || response.supplier.nameEn,
            supplierTypeId: response.supplier.SupplierTypeId || response.supplier.supplierTypeId,
            phone1: response.supplier.Phone || response.supplier.phone1 || response.supplier.Phone1,
            phone2: response.supplier.Phone2 || response.supplier.phone2,
            email: response.supplier.Email || response.supplier.email,
            website: response.supplier.Website || response.supplier.website,
            address: response.supplier.Address || response.supplier.address,
            contactPersonName: response.supplier.ContactPersonName || response.supplier.contactPersonName,
            contactPersonPhone: response.supplier.ContactPersonPhone || response.supplier.contactPersonPhone,
            contactPersonEmail: response.supplier.ContactPersonEmail || response.supplier.contactPersonEmail,
            paymentTerms: response.supplier.PaymentTerms || response.supplier.paymentTerms || 30,
            creditLimit: response.supplier.CreditLimit || response.supplier.creditLimit || 0,
            currentBalance: response.supplier.CurrentBalance || response.supplier.currentBalance || 0,
            taxNumber: response.supplier.TaxNumber || response.supplier.taxNumber,
            commercialRegister: response.supplier.CommercialRegister || response.supplier.commercialRegister,
            bankName: response.supplier.BankName || response.supplier.bankName,
            bankAccountNumber: response.supplier.BankAccountNumber || response.supplier.bankAccountNumber,
            rating: response.supplier.Rating || response.supplier.rating || 0,
            notes: response.supplier.Notes || response.supplier.notes,
            isActive: response.supplier.IsActive !== false,
            createdAt: response.supplier.CreatedAt || response.supplier.createdAt || new Date().toISOString()
          };
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading supplier details:', error);
        this.supplier = this.getMockSupplierDetails();
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Load supplier products
   */
  private loadSupplierProducts(): void {
    // Mock data for now
    this.supplierProducts = this.getMockSupplierProducts();
  }

  /**
   * Load supplier transactions
   */
  private loadSupplierTransactions(): void {
    // Mock data for now
    this.supplierTransactions = this.getMockSupplierTransactions();
  }

  /**
   * Edit supplier
   */
  editSupplier(): void {
    this.router.navigate(['/suppliers/edit', this.supplierId]);
  }

  /**
   * Go back to suppliers list
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Get supplier type name
   */
  getSupplierTypeName(typeId?: number): string {
    const types: { [key: number]: string } = {
      1: 'مورد محلي',
      2: 'مورد دولي',
      3: 'مورد حكومي'
    };
    return types[typeId || 1] || 'مورد محلي';
  }

  /**
   * Get balance class for styling
   */
  getBalanceClass(balance: number): string {
    if (balance > 0) return 'positive';
    if (balance < 0) return 'negative';
    return 'zero';
  }

  /**
   * Format currency
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP'
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('ar-EG');
  }

  /**
   * Get rating stars
   */
  getRatingStars(rating?: number): string {
    const stars = rating || 0;
    return '⭐'.repeat(Math.floor(stars)) + '☆'.repeat(5 - Math.floor(stars));
  }

  /**
   * Get mock supplier details
   */
  private getMockSupplierDetails(): SupplierDetails {
    return {
      id: this.supplierId,
      supplierCode: 'SUP001',
      nameAr: 'شركة الأهرام للتجارة',
      nameEn: 'Al-Ahram Trading Company',
      supplierTypeId: 1,
      phone1: '+************',
      phone2: '+201*********',
      email: '<EMAIL>',
      website: 'www.ahram-trading.com',
      address: 'شارع الهرم، الجيزة، مصر',
      contactPersonName: 'أحمد محمد علي',
      contactPersonPhone: '+************',
      contactPersonEmail: '<EMAIL>',
      paymentTerms: 30,
      creditLimit: 100000,
      currentBalance: -15000,
      taxNumber: '*********',
      commercialRegister: 'CR123456',
      bankName: 'البنك الأهلي المصري',
      bankAccountNumber: '*********012',
      rating: 4,
      notes: 'مورد موثوق للمواد الغذائية والمشروبات',
      isActive: true,
      createdAt: new Date().toISOString()
    };
  }

  /**
   * Get mock supplier products
   */
  private getMockSupplierProducts(): SupplierProduct[] {
    return [
      {
        id: 1,
        productCode: 'PRD001',
        nameAr: 'أرز مصري فاخر',
        nameEn: 'Premium Egyptian Rice',
        categoryName: 'حبوب',
        unitPrice: 25.50,
        lastOrderDate: '2024-01-15',
        totalOrdered: 500
      },
      {
        id: 2,
        productCode: 'PRD002',
        nameAr: 'زيت عباد الشمس',
        nameEn: 'Sunflower Oil',
        categoryName: 'زيوت',
        unitPrice: 45.00,
        lastOrderDate: '2024-01-10',
        totalOrdered: 200
      },
      {
        id: 3,
        productCode: 'PRD003',
        nameAr: 'سكر أبيض',
        nameEn: 'White Sugar',
        categoryName: 'سكريات',
        unitPrice: 18.75,
        lastOrderDate: '2024-01-08',
        totalOrdered: 300
      }
    ];
  }

  /**
   * Get mock supplier transactions
   */
  private getMockSupplierTransactions(): SupplierTransaction[] {
    return [
      {
        id: 1,
        transactionType: 'purchase',
        amount: -25000,
        date: '2024-01-15',
        description: 'فاتورة شراء رقم PUR001',
        referenceNumber: 'PUR001'
      },
      {
        id: 2,
        transactionType: 'payment',
        amount: 10000,
        date: '2024-01-10',
        description: 'دفعة نقدية',
        referenceNumber: 'PAY001'
      },
      {
        id: 3,
        transactionType: 'purchase',
        amount: -15000,
        date: '2024-01-08',
        description: 'فاتورة شراء رقم PUR002',
        referenceNumber: 'PUR002'
      }
    ];
  }
}
