#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة encoding اللغة العربية في قاعدة البيانات
Fix Arabic Encoding Issues in Database
"""

import pyodbc
import sys
from datetime import datetime

# إعدادات الاتصال بقاعدة البيانات مع دعم Unicode
SERVER = 'localhost'
DATABASE = 'TerraRetailERP'
USERNAME = 'sa'
PASSWORD = '@a123admin4'

# سلسلة الاتصال مع دعم Unicode
CONNECTION_STRING = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};charset=utf8;'

def connect_to_database():
    """الاتصال بقاعدة البيانات مع دعم Unicode"""
    try:
        conn = pyodbc.connect(CONNECTION_STRING)
        # تعيين encoding للاتصال
        conn.setdecoding(pyodbc.SQL_CHAR, encoding='utf-8')
        conn.setdecoding(pyodbc.SQL_WCHAR, encoding='utf-8')
        conn.setencoding(encoding='utf-8')
        print("✅ تم الاتصال بقاعدة البيانات بنجاح مع دعم Unicode")
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def clear_suppliers(cursor):
    """حذف الموردين الموجودين"""
    try:
        cursor.execute("DELETE FROM Suppliers")
        cursor.execute("DBCC CHECKIDENT ('Suppliers', RESEED, 0)")
        print("🗑️ تم حذف الموردين الموجودين")
    except Exception as e:
        print(f"❌ خطأ في حذف الموردين: {e}")

def add_suppliers_with_correct_encoding(cursor):
    """إضافة الموردين المصريين مع encoding صحيح"""
    
    suppliers_data = [
        # موردين محليين - القاهرة
        {
            'code': 'SUP001',
            'name_ar': 'شركة النيل للتجارة والتوريدات',
            'name_en': 'Nile Trading & Supply Co.',
            'type_id': 1,
            'phone1': '+************',
            'phone2': '+201*********',
            'email': '<EMAIL>',
            'website': 'www.nile-trading.com',
            'address': 'شارع التحرير، وسط البلد، القاهرة',
            'area_id': 1,
            'contact_name': 'أحمد محمد علي',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 100000.00,
            'opening_balance': -25000.00,
            'current_balance': -25000.00,
            'tax_number': '*********',
            'commercial_register': 'CR123456',
            'bank_name': 'البنك الأهلي المصري',
            'bank_account': '*********012',
            'rating': 4,
            'notes': 'مورد موثوق للمواد الغذائية والمشروبات'
        },
        {
            'code': 'SUP002',
            'name_ar': 'مجموعة العربي للاستيراد والتصدير',
            'name_en': 'Al Arabi Import Export Group',
            'type_id': 2,
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.alarabi-group.com',
            'address': 'مدينة نصر، القاهرة',
            'area_id': 1,
            'contact_name': 'فاطمة حسن إبراهيم',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 200000.00,
            'opening_balance': 50000.00,
            'current_balance': 50000.00,
            'tax_number': '*********',
            'commercial_register': 'CR987654',
            'bank_name': 'بنك مصر',
            'bank_account': '*********098',
            'rating': 5,
            'notes': 'مورد دولي للإلكترونيات والأجهزة المنزلية'
        },
        {
            'code': 'SUP003',
            'name_ar': 'مصنع الأهرام للمنسوجات',
            'name_en': 'Pyramids Textile Factory',
            'type_id': 1,
            'phone1': '+20*********0',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.pyramids-textile.com',
            'address': 'المنطقة الصناعية، 6 أكتوبر، الجيزة',
            'area_id': 2,
            'contact_name': 'محمود عبدالله أحمد',
            'contact_phone': '+20*********0',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 150000.00,
            'opening_balance': 0.00,
            'current_balance': 0.00,
            'tax_number': '*********',
            'commercial_register': 'CR456789',
            'bank_name': 'بنك القاهرة',
            'bank_account': '*********456',
            'rating': 3,
            'notes': 'مصنع محلي للملابس والمنسوجات القطنية'
        },
        {
            'code': 'SUP004',
            'name_ar': 'شركة دلتا للكيماويات',
            'name_en': 'Delta Chemicals Company',
            'type_id': 1,
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.delta-chemicals.com',
            'address': 'الدقي، الجيزة',
            'area_id': 2,
            'contact_name': 'نورا سامي محمد',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 60,
            'credit_limit': 300000.00,
            'opening_balance': -75000.00,
            'current_balance': -75000.00,
            'tax_number': '*********',
            'commercial_register': 'CR789123',
            'bank_name': 'البنك التجاري الدولي',
            'bank_account': '789*********',
            'rating': 4,
            'notes': 'مورد للمواد الكيماوية ومنتجات التنظيف'
        },
        {
            'code': 'SUP005',
            'name_ar': 'مجموعة البحر المتوسط للأغذية',
            'name_en': 'Mediterranean Food Group',
            'type_id': 1,
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.med-food.com',
            'address': 'منطقة الميناء، الإسكندرية',
            'area_id': 3,
            'contact_name': 'عمر خالد حسن',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 120000.00,
            'opening_balance': 30000.00,
            'current_balance': 30000.00,
            'tax_number': '*********',
            'commercial_register': 'CR321654',
            'bank_name': 'بنك الإسكندرية',
            'bank_account': '*********321',
            'rating': 4,
            'notes': 'مورد للأسماك والمأكولات البحرية المجمدة'
        },
        {
            'code': 'SUP006',
            'name_ar': 'شركة الإسكندرية للأدوية',
            'name_en': 'Alexandria Pharmaceuticals',
            'type_id': 1,
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.alex-pharma.com',
            'address': 'المنطقة الصناعية، برج العرب، الإسكندرية',
            'area_id': 3,
            'contact_name': 'سارة أحمد محمود',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 500000.00,
            'opening_balance': -100000.00,
            'current_balance': -100000.00,
            'tax_number': '*********',
            'commercial_register': 'CR654987',
            'bank_name': 'بنك الاتحاد الوطني',
            'bank_account': '*********654',
            'rating': 5,
            'notes': 'مورد للأدوية والمستلزمات الطبية'
        },
        {
            'code': 'SUP007',
            'name_ar': 'الشركة الصينية للإلكترونيات',
            'name_en': 'China Electronics International',
            'type_id': 2,
            'phone1': '+*************',
            'phone2': '+*************',
            'email': '<EMAIL>',
            'website': 'www.china-electronics.com',
            'address': 'شنزن، الصين - مكتب القاهرة: مدينة نصر',
            'area_id': 1,
            'contact_name': 'يوسف إبراهيم علي',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 60,
            'credit_limit': 400000.00,
            'opening_balance': 80000.00,
            'current_balance': 80000.00,
            'tax_number': 'INT001',
            'commercial_register': 'CRINT001',
            'bank_name': 'بنك HSBC',
            'bank_account': 'INT*********',
            'rating': 5,
            'notes': 'مورد دولي للإلكترونيات والهواتف الذكية'
        },
        {
            'code': 'SUP008',
            'name_ar': 'الشركة التركية للمنسوجات',
            'name_en': 'Turkish Textile Corporation',
            'type_id': 2,
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.turkish-textile.com',
            'address': 'إسطنبول، تركيا - مكتب الإسكندرية',
            'area_id': 3,
            'contact_name': 'مريم عبدالرحمن',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 250000.00,
            'opening_balance': -40000.00,
            'current_balance': -40000.00,
            'tax_number': 'INT002',
            'commercial_register': 'TRINT002',
            'bank_name': 'بنك QNB',
            'bank_account': 'INT*********',
            'rating': 4,
            'notes': 'مورد دولي للأقمشة والملابس الجاهزة'
        },
        {
            'code': 'SUP009',
            'name_ar': 'الشركة المصرية للأدوية',
            'name_en': 'Egyptian Pharmaceutical Company',
            'type_id': 3,
            'phone1': '+***********',
            'phone2': '+***********',
            'email': '<EMAIL>',
            'website': 'www.epc.gov.eg',
            'address': 'مدينة العبور، القليوبية',
            'area_id': 6,
            'contact_name': 'هالة محمد أحمد',
            'contact_phone': '+***********',
            'contact_email': '<EMAIL>',
            'payment_terms': 90,
            'credit_limit': 1000000.00,
            'opening_balance': 0.00,
            'current_balance': 0.00,
            'tax_number': 'GOV001',
            'commercial_register': 'CRGOV001',
            'bank_name': 'البنك المركزي المصري',
            'bank_account': 'GOV*********',
            'rating': 5,
            'notes': 'شركة حكومية لتصنيع الأدوية والمستلزمات الطبية'
        },
        {
            'code': 'SUP010',
            'name_ar': 'الهيئة العامة للسلع التموينية',
            'name_en': 'General Authority for Supply Commodities',
            'type_id': 3,
            'phone1': '+***********',
            'phone2': '+***********',
            'email': '<EMAIL>',
            'website': 'www.gasc.gov.eg',
            'address': 'مدينة نصر، القاهرة',
            'area_id': 1,
            'contact_name': 'خالد عبدالعزيز',
            'contact_phone': '+***********',
            'contact_email': '<EMAIL>',
            'payment_terms': 120,
            'credit_limit': 2000000.00,
            'opening_balance': 150000.00,
            'current_balance': 150000.00,
            'tax_number': 'GOV002',
            'commercial_register': 'CRGOV002',
            'bank_name': 'البنك المركزي المصري',
            'bank_account': 'GOV*********',
            'rating': 4,
            'notes': 'هيئة حكومية لتوريد السلع الاستراتيجية'
        }
    ]
    
    # SQL لإدراج المورد مع دعم Unicode
    insert_sql = """
    INSERT INTO Suppliers (
        SupplierCode, NameAr, NameEn, SupplierTypeId,
        Phone1, Phone2, Email, Website, Address, AreaId,
        ContactPersonName, ContactPersonPhone, ContactPersonEmail,
        PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance,
        TaxNumber, CommercialRegister, BankName, BankAccountNumber,
        IsActive, Rating, Notes, CreatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    try:
        for supplier in suppliers_data:
            # تحويل النصوص العربية إلى Unicode صريح
            params = (
                supplier['code'],
                supplier['name_ar'],  # النص العربي
                supplier['name_en'],
                supplier['type_id'],
                supplier['phone1'],
                supplier['phone2'],
                supplier['email'],
                supplier['website'],
                supplier['address'],  # العنوان العربي
                supplier['area_id'],
                supplier['contact_name'],  # الاسم العربي
                supplier['contact_phone'],
                supplier['contact_email'],
                supplier['payment_terms'],
                supplier['credit_limit'],
                supplier['opening_balance'],
                supplier['current_balance'],
                supplier['tax_number'],
                supplier['commercial_register'],
                supplier['bank_name'],  # اسم البنك العربي
                supplier['bank_account'],
                1,  # IsActive
                supplier['rating'],
                supplier['notes'],  # الملاحظات العربية
                datetime.now()
            )
            
            cursor.execute(insert_sql, params)
            print(f"✅ تم إضافة المورد: {supplier['name_ar']}")
        
        # تحديث العداد
        cursor.execute("UPDATE Counters SET CurrentValue = ? WHERE CounterName = 'SUPPLIER'", len(suppliers_data))
        
        print(f"🎉 تم إضافة {len(suppliers_data)} مورد مصري بنجاح مع encoding صحيح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الموردين: {e}")
        raise

def verify_arabic_text(cursor):
    """التحقق من صحة النصوص العربية"""
    try:
        cursor.execute("SELECT TOP 5 SupplierCode, NameAr, Address, ContactPersonName FROM Suppliers ORDER BY Id")
        
        print("\n🔍 التحقق من النصوص العربية:")
        print("الكود\t\tالاسم\t\t\t\tالعنوان\t\t\tالشخص المسؤول")
        print("-" * 100)
        
        for row in cursor.fetchall():
            code, name, address, contact = row
            print(f"{code}\t{name[:20]:<20}\t{(address or 'غير محدد')[:20]:<20}\t{(contact or 'غير محدد')[:15]:<15}")
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من النصوص: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح encoding اللغة العربية...")
    
    # الاتصال بقاعدة البيانات
    conn = connect_to_database()
    if not conn:
        sys.exit(1)
    
    try:
        cursor = conn.cursor()
        
        # حذف الموردين الموجودين
        clear_suppliers(cursor)
        
        # إضافة الموردين الجدد مع encoding صحيح
        add_suppliers_with_correct_encoding(cursor)
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النصوص العربية
        verify_arabic_text(cursor)
        
        print("\n✅ تم إصلاح encoding اللغة العربية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        conn.rollback()
        sys.exit(1)
    
    finally:
        conn.close()
        print("🔒 تم إغلاق الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    main()
