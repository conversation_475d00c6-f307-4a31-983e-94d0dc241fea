{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5127/api',\n  appName: 'Terra Retail ERP',\n  version: '1.0.0'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5127/api',\n  appName: 'Terra Retail ERP',\n  version: '1.0.0'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,2BAA2B;EACnCC,OAAO,EAAE,kBAAkB;EAC3BC,OAAO,EAAE;CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}