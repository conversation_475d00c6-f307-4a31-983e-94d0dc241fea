using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;
using System.ComponentModel.DataAnnotations;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("📋 Journal Entries - القيود المحاسبية")]
    public class JournalEntryController : ControllerBase
    {
        private readonly AppDbContext _context;

        public JournalEntryController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult> GetJournalEntries([FromQuery] int page = 1, [FromQuery] int pageSize = 50, [FromQuery] JournalEntryStatus? status = null)
        {
            try
            {
                var query = _context.JournalEntries
                    .Include(je => je.User)
                    .Include(je => je.PostedByUser)
                    .Include(je => je.PreliminaryPostedByUser)
                    .Include(je => je.FinalPostedByUser)
                    .Include(je => je.JournalEntryDetails)
                        .ThenInclude(jed => jed.Account)
                    .AsQueryable();

                if (status.HasValue)
                {
                    query = query.Where(je => je.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var entries = await query
                    .OrderByDescending(je => je.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب القيود المحاسبية بنجاح",
                    data = entries,
                    pagination = new
                    {
                        currentPage = page,
                        pageSize = pageSize,
                        totalCount = totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult> GetJournalEntry(int id)
        {
            try
            {
                var entry = await _context.JournalEntries
                    .Include(je => je.User)
                    .Include(je => je.PostedByUser)
                    .Include(je => je.PreliminaryPostedByUser)
                    .Include(je => je.FinalPostedByUser)
                    .Include(je => je.ReversedByUser)
                    .Include(je => je.JournalEntryDetails)
                        .ThenInclude(jed => jed.Account)
                    .FirstOrDefaultAsync(je => je.Id == id);

                if (entry == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "القيد المحاسبي غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب تفاصيل القيد المحاسبي بنجاح",
                    data = entry
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult> CreateJournalEntry([FromBody] CreateJournalEntryDto request)
        {
            try
            {
                // التحقق من توازن القيد
                var totalDebit = request.Details.Sum(d => d.DebitAmount);
                var totalCredit = request.Details.Sum(d => d.CreditAmount);

                if (totalDebit != totalCredit)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن",
                        totalDebit = totalDebit,
                        totalCredit = totalCredit
                    });
                }

                // إنشاء رقم القيد
                var entryNumber = await GenerateEntryNumber();

                var journalEntry = new JournalEntry
                {
                    EntryNumber = entryNumber,
                    EntryDate = request.EntryDate ?? DateTime.Now,
                    Description = request.Description,
                    Reference = request.Reference,
                    TransactionType = request.TransactionType,
                    Status = JournalEntryStatus.Draft,
                    TotalDebit = totalDebit,
                    TotalCredit = totalCredit,
                    UserId = request.UserId,
                    CreatedAt = DateTime.Now
                };

                _context.JournalEntries.Add(journalEntry);
                await _context.SaveChangesAsync();

                // إضافة تفاصيل القيد
                var details = request.Details.Select((detail, index) => new JournalEntryDetail
                {
                    JournalEntryId = journalEntry.Id,
                    AccountId = detail.AccountId,
                    LineNumber = index + 1,
                    Description = detail.Description,
                    DebitAmount = detail.DebitAmount,
                    CreditAmount = detail.CreditAmount,
                    CostCenterId = detail.CostCenterId,
                    Reference = detail.Reference,
                    CreatedAt = DateTime.Now
                }).ToList();

                _context.JournalEntryDetails.AddRange(details);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم إنشاء القيد المحاسبي بنجاح",
                    data = new { Id = journalEntry.Id, EntryNumber = journalEntry.EntryNumber }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost("{id}/preliminary-post")]
        public async Task<ActionResult> PreliminaryPost(int id, [FromBody] PostingDto request)
        {
            try
            {
                var entry = await _context.JournalEntries.FindAsync(id);
                if (entry == null)
                {
                    return NotFound(new { success = false, message = "القيد المحاسبي غير موجود" });
                }

                if (entry.Status != JournalEntryStatus.Draft)
                {
                    return BadRequest(new { success = false, message = "لا يمكن ترحيل هذا القيد - الحالة الحالية: " + entry.Status });
                }

                entry.Status = JournalEntryStatus.PreliminaryPosted;
                entry.PreliminaryPostedBy = request.UserId;
                entry.PreliminaryPostedAt = DateTime.Now;
                entry.PreliminaryPostingNotes = request.Notes;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم الترحيل المبدئي للقيد بنجاح",
                    data = new { Id = entry.Id, Status = entry.Status }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("{id}/final-post")]
        public async Task<ActionResult> FinalPost(int id, [FromBody] PostingDto request)
        {
            try
            {
                var entry = await _context.JournalEntries.FindAsync(id);
                if (entry == null)
                {
                    return NotFound(new { success = false, message = "القيد المحاسبي غير موجود" });
                }

                if (entry.Status != JournalEntryStatus.PreliminaryPosted)
                {
                    return BadRequest(new { success = false, message = "يجب أن يكون القيد مرحل مبدئياً أولاً" });
                }

                entry.Status = JournalEntryStatus.FinalPosted;
                entry.FinalPostedBy = request.UserId;
                entry.FinalPostedAt = DateTime.Now;
                entry.FinalPostingNotes = request.Notes;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم الترحيل النهائي للقيد بنجاح",
                    data = new { Id = entry.Id, Status = entry.Status }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("{id}/reverse")]
        public async Task<ActionResult> ReverseEntry(int id, [FromBody] ReversalDto request)
        {
            try
            {
                var entry = await _context.JournalEntries
                    .Include(je => je.JournalEntryDetails)
                    .FirstOrDefaultAsync(je => je.Id == id);

                if (entry == null)
                {
                    return NotFound(new { success = false, message = "القيد المحاسبي غير موجود" });
                }

                if (entry.Status == JournalEntryStatus.FinalPosted)
                {
                    return BadRequest(new { success = false, message = "لا يمكن عكس قيد مرحل نهائياً" });
                }

                if (entry.Status != JournalEntryStatus.PreliminaryPosted)
                {
                    return BadRequest(new { success = false, message = "يمكن عكس القيود المرحلة مبدئياً فقط" });
                }

                // إنشاء قيد عكسي
                var reversalEntryNumber = await GenerateEntryNumber();
                var reversalEntry = new JournalEntry
                {
                    EntryNumber = reversalEntryNumber,
                    EntryDate = DateTime.Now,
                    Description = $"عكس القيد رقم {entry.EntryNumber} - {request.Reason}",
                    Reference = entry.Reference,
                    TransactionType = entry.TransactionType,
                    Status = JournalEntryStatus.PreliminaryPosted,
                    TotalDebit = entry.TotalCredit, // عكس المبالغ
                    TotalCredit = entry.TotalDebit,
                    UserId = request.UserId,
                    PreliminaryPostedBy = request.UserId,
                    PreliminaryPostedAt = DateTime.Now,
                    CreatedAt = DateTime.Now
                };

                _context.JournalEntries.Add(reversalEntry);
                await _context.SaveChangesAsync();

                // إنشاء تفاصيل القيد العكسي
                var reversalDetails = entry.JournalEntryDetails.Select((detail, index) => new JournalEntryDetail
                {
                    JournalEntryId = reversalEntry.Id,
                    AccountId = detail.AccountId,
                    LineNumber = index + 1,
                    Description = detail.Description,
                    DebitAmount = detail.CreditAmount, // عكس المبالغ
                    CreditAmount = detail.DebitAmount,
                    CostCenterId = detail.CostCenterId,
                    Reference = detail.Reference,
                    CreatedAt = DateTime.Now
                }).ToList();

                _context.JournalEntryDetails.AddRange(reversalDetails);

                // تحديث القيد الأصلي
                entry.Status = JournalEntryStatus.Reversed;
                entry.ReversedBy = request.UserId;
                entry.ReversedAt = DateTime.Now;
                entry.ReversalReason = request.Reason;
                entry.ReversalJournalEntryId = reversalEntry.Id;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم عكس القيد بنجاح",
                    data = new
                    {
                        OriginalEntryId = entry.Id,
                        ReversalEntryId = reversalEntry.Id,
                        ReversalEntryNumber = reversalEntry.EntryNumber
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateEntryNumber()
        {
            var year = DateTime.Now.Year;
            var lastEntry = await _context.JournalEntries
                .Where(je => je.EntryNumber.StartsWith(year.ToString()))
                .OrderByDescending(je => je.EntryNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastEntry != null && lastEntry.EntryNumber.Length > 4)
            {
                var numberPart = lastEntry.EntryNumber.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{year}{nextNumber:D6}";
        }
    }

    // DTOs
    public class CreateJournalEntryDto
    {
        public DateTime? EntryDate { get; set; }
        [Required] public string Description { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public int TransactionType { get; set; } = 1;
        public int UserId { get; set; } = 1;
        [Required] public List<JournalEntryDetailDto> Details { get; set; } = new();
    }

    public class JournalEntryDetailDto
    {
        public int AccountId { get; set; }
        public string? Description { get; set; }
        public decimal DebitAmount { get; set; } = 0;
        public decimal CreditAmount { get; set; } = 0;
        public int? CostCenterId { get; set; }
        public string? Reference { get; set; }
    }

    public class PostingDto
    {
        public int UserId { get; set; } = 1;
        public string? Notes { get; set; }
    }

    public class ReversalDto
    {
        public int UserId { get; set; } = 1;
        [Required] public string Reason { get; set; } = string.Empty;
    }
}
