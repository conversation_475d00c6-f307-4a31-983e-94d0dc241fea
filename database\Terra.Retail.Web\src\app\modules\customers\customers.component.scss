/* Terra Retail ERP - Customers Page Styles */

.customers-container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
  color: white;
  padding: 40px 40px 60px;
  margin: -40px -40px 40px;
  border-radius: 0 0 30px 30px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateX(-5px);
    }
  }

  .header-text {
    .page-title {
      font-size: 3rem;
      font-weight: 700;
      margin: 0 0 8px 0;
      text-shadow: 0 4px 8px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ffffff, #e3f2fd);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-subtitle {
      font-size: 1.2rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 300;
    }
  }

  .header-actions {
    display: flex;
    gap: 15px;
    align-items: center;

    .add-btn {
      background: linear-gradient(45deg, #4CAF50, #45a049);
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 1rem;
      font-weight: 600;
      border-radius: 25px;
      box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
      }

      mat-icon {
        margin-left: 8px;
      }
    }

    .export-btn {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 12px 20px;
      border-radius: 25px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      mat-icon {
        margin-left: 8px;
      }
    }
  }
}

/* ===== CONTENT AREA ===== */
.content-area {
  padding: 0 40px 40px;
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== STATISTICS CARDS ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  &.customers-card::before { background: linear-gradient(90deg, #2196F3, #21CBF3); }
  &.active-card::before { background: linear-gradient(90deg, #4CAF50, #45a049); }
  &.balance-card::before { background: linear-gradient(90deg, #FF9800, #F57C00); }
  &.credit-card::before { background: linear-gradient(90deg, #9C27B0, #673AB7); }

  mat-card-content {
    padding: 25px !important;
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: 20px;

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: #667eea;
      }
    }

    .stat-info {
      flex: 1;

      h3 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 5px 0;
        color: #2c3e50;
      }

      p {
        font-size: 0.95rem;
        color: #7f8c8d;
        margin: 0;
        font-weight: 500;
      }
    }
  }
}

/* ===== FILTERS CARD ===== */
.filters-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  mat-card-content {
    padding: 25px !important;
  }

  .filters-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 20px;
    align-items: end;

    .search-field {
      min-width: 300px;
    }

    .filter-field {
      min-width: 180px;
    }

    .clear-filters-btn {
      height: 48px;
      border-radius: 12px;
      border: 1px solid #e0e0e0;
      color: #666;
      transition: all 0.3s ease;

      &:hover {
        background: #f5f5f5;
        border-color: #ccc;
      }

      mat-icon {
        margin-left: 8px;
      }
    }
  }
}

/* ===== TABLE CARD ===== */
.table-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  mat-card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;

      mat-icon {
        color: #667eea;
      }
    }

    .table-actions {
      display: flex;
      gap: 10px;
    }
  }

  mat-card-content {
    padding: 0 !important;
  }
}

/* ===== TABLE STYLES ===== */
.table-container {
  overflow-x: auto;
  max-height: 600px;
}

.customers-table {
  width: 100%;
  background: white;

  th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 16px 12px;
    border-bottom: 2px solid #e0e0e0;
  }

  td {
    padding: 16px 12px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.9rem;
  }

  .clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }
  }

  .customer-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
  }

  .customer-info {
    .customer-name-ar {
      display: block;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 2px;
    }

    .customer-name-en {
      display: block;
      font-size: 0.8rem;
      color: #7f8c8d;
      font-style: italic;
    }
  }

  .customer-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;

    &.type-1 { background: #e3f2fd; color: #1976d2; }
    &.type-2 { background: #f3e5f5; color: #7b1fa2; }
    &.type-3 { background: #e8f5e8; color: #388e3c; }
    &.type-4 { background: #fff3e0; color: #f57c00; }
    &.type-5 { background: #fce4ec; color: #c2185b; }
  }

  .phone-number {
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    direction: ltr;
    text-align: left;
  }

  .area-name {
    color: #2c3e50;
    font-weight: 500;
  }

  .balance {
    font-weight: 600;
    color: #4caf50;
    font-family: 'Courier New', monospace;

    &.negative {
      color: #f44336;
    }
  }

  .status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 60px;

    &.active {
      background: #e8f5e8;
      color: #388e3c;
    }

    &.inactive {
      background: #ffebee;
      color: #d32f2f;
    }
  }

  .action-buttons {
    display: flex;
    gap: 5px;
    align-items: center;

    button {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        transform: scale(1.1);
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

/* ===== NO DATA MESSAGE ===== */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;

  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #bdc3c7;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 1.5rem;
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  p {
    font-size: 1rem;
    margin: 0 0 30px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  button {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;

  p {
    margin-top: 20px;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .content-area {
    padding: 0 20px 20px;
  }

  .page-header {
    padding: 30px 20px 40px;
    margin: -30px -20px 30px;

    .header-content {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: 15px;
    }

    .page-title {
      font-size: 2.5rem !important;
    }
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 15px;

    .search-field,
    .filter-field {
      min-width: auto;
    }
  }
}

@media (max-width: 768px) {
  .customers-container {
    padding: 0 10px;
  }

  .page-header {
    padding: 20px 15px 30px;
    margin: -20px -15px 20px;

    .page-title {
      font-size: 2rem !important;
    }

    .page-subtitle {
      font-size: 1rem !important;
    }

    .header-actions {
      flex-direction: column;
      width: 100%;

      .add-btn,
      .export-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .customers-table {
    font-size: 0.8rem;

    th, td {
      padding: 12px 8px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;

    button {
      width: 32px;
      height: 32px;
    }
  }
}
