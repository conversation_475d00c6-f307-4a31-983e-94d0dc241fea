<!-- Terra Retail ERP - Supplier Details -->
<div class="supplier-details-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text" *ngIf="supplier">
          <h1 class="page-title">{{ supplier.nameAr }}</h1>
          <p class="page-subtitle">{{ supplier.supplierCode }} - {{ getSupplierTypeName(supplier.supplierTypeId) }}</p>
        </div>
      </div>
      <div class="header-actions" *ngIf="supplier">
        <button mat-raised-button color="primary" class="edit-btn" (click)="editSupplier()">
          <mat-icon>edit</mat-icon>
          <span>تعديل البيانات</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content" *ngIf="!isLoading && supplier">
    
    <!-- Supplier Overview Cards -->
    <div class="overview-cards">
      
      <!-- Balance Card -->
      <mat-card class="overview-card balance-card">
        <mat-card-content>
          <div class="card-icon">
            <mat-icon>account_balance_wallet</mat-icon>
          </div>
          <div class="card-info">
            <h3>الرصيد الحالي</h3>
            <p class="balance-amount" [ngClass]="getBalanceClass(supplier.currentBalance)">
              {{ formatCurrency(supplier.currentBalance) }}
            </p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Credit Limit Card -->
      <mat-card class="overview-card credit-card">
        <mat-card-content>
          <div class="card-icon">
            <mat-icon>credit_card</mat-icon>
          </div>
          <div class="card-info">
            <h3>الحد الائتماني</h3>
            <p class="credit-amount">{{ formatCurrency(supplier.creditLimit) }}</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Rating Card -->
      <mat-card class="overview-card rating-card">
        <mat-card-content>
          <div class="card-icon">
            <mat-icon>star</mat-icon>
          </div>
          <div class="card-info">
            <h3>التقييم</h3>
            <p class="rating-stars">{{ getRatingStars(supplier.rating) }}</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Payment Terms Card -->
      <mat-card class="overview-card payment-card">
        <mat-card-content>
          <div class="card-icon">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="card-info">
            <h3>مدة السداد</h3>
            <p class="payment-terms">{{ supplier.paymentTerms }} يوم</p>
          </div>
        </mat-card-content>
      </mat-card>

    </div>

    <!-- Tabs Content -->
    <mat-tab-group class="details-tabs" animationDuration="300ms">
      
      <!-- Basic Information Tab -->
      <mat-tab label="المعلومات الأساسية">
        <div class="tab-content">
          
          <div class="info-grid">
            
            <!-- Company Information -->
            <mat-card class="info-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>business</mat-icon>
                  <span>معلومات الشركة</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-row">
                  <span class="label">كود المورد:</span>
                  <span class="value">{{ supplier.supplierCode }}</span>
                </div>
                <div class="info-row">
                  <span class="label">الاسم بالعربية:</span>
                  <span class="value">{{ supplier.nameAr }}</span>
                </div>
                <div class="info-row" *ngIf="supplier.nameEn">
                  <span class="label">الاسم بالإنجليزية:</span>
                  <span class="value">{{ supplier.nameEn }}</span>
                </div>
                <div class="info-row">
                  <span class="label">نوع المورد:</span>
                  <span class="value">{{ getSupplierTypeName(supplier.supplierTypeId) }}</span>
                </div>
                <div class="info-row" *ngIf="supplier.website">
                  <span class="label">الموقع الإلكتروني:</span>
                  <a [href]="supplier.website" target="_blank" class="value link">{{ supplier.website }}</a>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Contact Information -->
            <mat-card class="info-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>contact_phone</mat-icon>
                  <span>معلومات الاتصال</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-row">
                  <span class="label">الهاتف الأول:</span>
                  <a [href]="'tel:' + supplier.phone1" class="value link">{{ supplier.phone1 }}</a>
                </div>
                <div class="info-row" *ngIf="supplier.phone2">
                  <span class="label">الهاتف الثاني:</span>
                  <a [href]="'tel:' + supplier.phone2" class="value link">{{ supplier.phone2 }}</a>
                </div>
                <div class="info-row" *ngIf="supplier.email">
                  <span class="label">البريد الإلكتروني:</span>
                  <a [href]="'mailto:' + supplier.email" class="value link">{{ supplier.email }}</a>
                </div>
                <div class="info-row" *ngIf="supplier.address">
                  <span class="label">العنوان:</span>
                  <span class="value">{{ supplier.address }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Contact Person -->
            <mat-card class="info-card" *ngIf="supplier.contactPersonName">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>person</mat-icon>
                  <span>الشخص المسؤول</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-row">
                  <span class="label">الاسم:</span>
                  <span class="value">{{ supplier.contactPersonName }}</span>
                </div>
                <div class="info-row" *ngIf="supplier.contactPersonPhone">
                  <span class="label">الهاتف:</span>
                  <a [href]="'tel:' + supplier.contactPersonPhone" class="value link">{{ supplier.contactPersonPhone }}</a>
                </div>
                <div class="info-row" *ngIf="supplier.contactPersonEmail">
                  <span class="label">البريد الإلكتروني:</span>
                  <a [href]="'mailto:' + supplier.contactPersonEmail" class="value link">{{ supplier.contactPersonEmail }}</a>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Financial Information -->
            <mat-card class="info-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>account_balance</mat-icon>
                  <span>المعلومات المالية</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-row">
                  <span class="label">الرصيد الحالي:</span>
                  <span class="value" [ngClass]="getBalanceClass(supplier.currentBalance)">
                    {{ formatCurrency(supplier.currentBalance) }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="label">الحد الائتماني:</span>
                  <span class="value">{{ formatCurrency(supplier.creditLimit) }}</span>
                </div>
                <div class="info-row">
                  <span class="label">مدة السداد:</span>
                  <span class="value">{{ supplier.paymentTerms }} يوم</span>
                </div>
                <div class="info-row" *ngIf="supplier.taxNumber">
                  <span class="label">الرقم الضريبي:</span>
                  <span class="value">{{ supplier.taxNumber }}</span>
                </div>
                <div class="info-row" *ngIf="supplier.commercialRegister">
                  <span class="label">السجل التجاري:</span>
                  <span class="value">{{ supplier.commercialRegister }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Bank Information -->
            <mat-card class="info-card" *ngIf="supplier.bankName">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>account_balance</mat-icon>
                  <span>المعلومات البنكية</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-row">
                  <span class="label">اسم البنك:</span>
                  <span class="value">{{ supplier.bankName }}</span>
                </div>
                <div class="info-row" *ngIf="supplier.bankAccountNumber">
                  <span class="label">رقم الحساب:</span>
                  <span class="value">{{ supplier.bankAccountNumber }}</span>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Additional Information -->
            <mat-card class="info-card full-width" *ngIf="supplier.notes">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>note</mat-icon>
                  <span>ملاحظات</span>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p class="notes-text">{{ supplier.notes }}</p>
              </mat-card-content>
            </mat-card>

          </div>
        </div>
      </mat-tab>

      <!-- Products Tab -->
      <mat-tab label="المنتجات">
        <div class="tab-content">
          <div class="products-list">
            <mat-card *ngFor="let product of supplierProducts" class="product-card">
              <mat-card-content>
                <div class="product-header">
                  <h3>{{ product.nameAr }}</h3>
                  <mat-chip class="category-chip">{{ product.categoryName }}</mat-chip>
                </div>
                <div class="product-details">
                  <div class="detail-item">
                    <span class="label">كود المنتج:</span>
                    <span class="value">{{ product.productCode }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">السعر:</span>
                    <span class="value">{{ formatCurrency(product.unitPrice) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">آخر طلب:</span>
                    <span class="value">{{ formatDate(product.lastOrderDate) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">إجمالي المطلوب:</span>
                    <span class="value">{{ product.totalOrdered }}</span>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

      <!-- Transactions Tab -->
      <mat-tab label="المعاملات المالية">
        <div class="tab-content">
          <div class="transactions-list">
            <mat-card *ngFor="let transaction of supplierTransactions" class="transaction-card">
              <mat-card-content>
                <div class="transaction-header">
                  <div class="transaction-info">
                    <h3>{{ transaction.description }}</h3>
                    <p class="reference">{{ transaction.referenceNumber }}</p>
                  </div>
                  <div class="transaction-amount" [ngClass]="getBalanceClass(transaction.amount)">
                    {{ formatCurrency(transaction.amount) }}
                  </div>
                </div>
                <div class="transaction-footer">
                  <span class="date">{{ formatDate(transaction.date) }}</span>
                  <mat-chip [ngClass]="transaction.transactionType">
                    {{ transaction.transactionType === 'purchase' ? 'مشتريات' : 'دفعة' }}
                  </mat-chip>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </mat-tab>

    </mat-tab-group>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل بيانات المورد...</p>
  </div>

</div>
