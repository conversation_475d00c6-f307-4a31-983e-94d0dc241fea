using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BranchesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public BranchesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetBranches()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.IsMainBranch ? 0 : 1)
                    .ThenBy(b => b.NameAr)
                    .Select(b => new
                    {
                        id = b.Id,
                        nameAr = b.NameAr,
                        nameEn = b.NameEn,
                        address = b.Address,
                        phone = b.Phone,
                        email = b.Email,
                        isMainBranch = b.IsMainBranch,
                        defaultPriceCategoryId = b.DefaultPriceCategoryId,
                        isActive = b.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الفروع بنجاح",
                    data = branches,
                    count = branches.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة الفروع",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetBranch(int id)
        {
            try
            {
                var branch = await _context.Branches
                    .Where(b => b.Id == id && b.IsActive)
                    .Select(b => new
                    {
                        id = b.Id,
                        nameAr = b.NameAr,
                        nameEn = b.NameEn,
                        address = b.Address,
                        phone = b.Phone,
                        email = b.Email,
                        isMainBranch = b.IsMainBranch,
                        defaultPriceCategoryId = b.DefaultPriceCategoryId,
                        isActive = b.IsActive,
                        createdAt = b.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (branch == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفرع غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات الفرع بنجاح",
                    data = branch
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات الفرع",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreateBranch(CreateBranchRequest request)
        {
            try
            {
                var branch = new Branch
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Address = request.Address,
                    Phone = request.Phone,
                    Email = request.Email,
                    IsMainBranch = request.IsMainBranch,
                    DefaultPriceCategoryId = request.DefaultPriceCategoryId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Branches.Add(branch);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetBranch), new { id = branch.Id }, new
                {
                    success = true,
                    message = "تم إضافة الفرع بنجاح",
                    data = new
                    {
                        id = branch.Id,
                        nameAr = branch.NameAr,
                        nameEn = branch.NameEn,
                        address = branch.Address,
                        phone = branch.Phone,
                        email = branch.Email,
                        isMainBranch = branch.IsMainBranch,
                        defaultPriceCategoryId = branch.DefaultPriceCategoryId,
                        isActive = branch.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة الفرع",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdateBranch(int id, UpdateBranchRequest request)
        {
            try
            {
                var branch = await _context.Branches.FindAsync(id);
                if (branch == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفرع غير موجود"
                    });
                }

                branch.NameAr = request.NameAr;
                branch.NameEn = request.NameEn;
                branch.Address = request.Address;
                branch.Phone = request.Phone;
                branch.Email = request.Email;
                branch.IsMainBranch = request.IsMainBranch;
                branch.DefaultPriceCategoryId = request.DefaultPriceCategoryId;
                branch.IsActive = request.IsActive;
                branch.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات الفرع بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث الفرع",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteBranch(int id)
        {
            try
            {
                var branch = await _context.Branches.FindAsync(id);
                if (branch == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفرع غير موجود"
                    });
                }

                branch.IsActive = false;
                branch.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف الفرع بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف الفرع",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreateBranchRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public bool IsMainBranch { get; set; } = false;
        public int? DefaultPriceCategoryId { get; set; }
    }

    public class UpdateBranchRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public bool IsMainBranch { get; set; } = false;
        public int? DefaultPriceCategoryId { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
