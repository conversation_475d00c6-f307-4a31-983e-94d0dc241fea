{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZone, RendererFactory2, Injectable, ElementRef, Renderer2, DOCUMENT, ChangeDetectorRef, signal, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport '@angular/common';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  _scrolledIndexChange = /*#__PURE__*/new Subject();\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  scrolledIndexChange = /*#__PURE__*/this._scrolledIndexChange.pipe(/*#__PURE__*/distinctUntilChanged());\n  /** The attached viewport. */\n  _viewport = null;\n  /** The size of the items in the virtually scrolling list. */\n  _itemSize;\n  /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n  _minBufferPx;\n  /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n  _maxBufferPx;\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nlet CdkFixedSizeVirtualScroll = /*#__PURE__*/(() => {\n  class CdkFixedSizeVirtualScroll {\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(value) {\n      this._itemSize = coerceNumberProperty(value);\n    }\n    _itemSize = 20;\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n      return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n      this._minBufferPx = coerceNumberProperty(value);\n    }\n    _minBufferPx = 100;\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n      return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n      this._maxBufferPx = coerceNumberProperty(value);\n    }\n    _maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    _scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    ngOnChanges() {\n      this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static ɵfac = function CdkFixedSizeVirtualScroll_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkFixedSizeVirtualScroll)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFixedSizeVirtualScroll,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n      inputs: {\n        itemSize: \"itemSize\",\n        minBufferPx: \"minBufferPx\",\n        maxBufferPx: \"maxBufferPx\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return CdkFixedSizeVirtualScroll;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nlet ScrollDispatcher = /*#__PURE__*/(() => {\n  class ScrollDispatcher {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupGlobalListener;\n    constructor() {}\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    _scrolled = new Subject();\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    _scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    scrollContainers = new Map();\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n      if (!this.scrollContainers.has(scrollable)) {\n        this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n      }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n      const scrollableReference = this.scrollContainers.get(scrollable);\n      if (scrollableReference) {\n        scrollableReference.unsubscribe();\n        this.scrollContainers.delete(scrollable);\n      }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n      if (!this._platform.isBrowser) {\n        return of();\n      }\n      return new Observable(observer => {\n        if (!this._cleanupGlobalListener) {\n          this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n        }\n        // In the case of a 0ms delay, use an observable without auditTime\n        // since it does add a perceptible delay in processing overhead.\n        const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n        this._scrolledCount++;\n        return () => {\n          subscription.unsubscribe();\n          this._scrolledCount--;\n          if (!this._scrolledCount) {\n            this._cleanupGlobalListener?.();\n            this._cleanupGlobalListener = undefined;\n          }\n        };\n      });\n    }\n    ngOnDestroy() {\n      this._cleanupGlobalListener?.();\n      this._cleanupGlobalListener = undefined;\n      this.scrollContainers.forEach((_, container) => this.deregister(container));\n      this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n      const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n      return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n      const scrollingContainers = [];\n      this.scrollContainers.forEach((_subscription, scrollable) => {\n        if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n          scrollingContainers.push(scrollable);\n        }\n      });\n      return scrollingContainers;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n      let element = coerceElement(elementOrElementRef);\n      let scrollableElement = scrollable.getElementRef().nativeElement;\n      // Traverse through the element parents until we reach null, checking if any of the elements\n      // are the scrollable's element.\n      do {\n        if (element == scrollableElement) {\n          return true;\n        }\n      } while (element = element.parentElement);\n      return false;\n    }\n    static ɵfac = function ScrollDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollDispatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollDispatcher,\n      factory: ScrollDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ScrollDispatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nlet CdkScrollable = /*#__PURE__*/(() => {\n  class CdkScrollable {\n    elementRef = inject(ElementRef);\n    scrollDispatcher = inject(ScrollDispatcher);\n    ngZone = inject(NgZone);\n    dir = inject(Directionality, {\n      optional: true\n    });\n    _scrollElement = this.elementRef.nativeElement;\n    _destroyed = new Subject();\n    _renderer = inject(Renderer2);\n    _cleanupScroll;\n    _elementScrolled = new Subject();\n    constructor() {}\n    ngOnInit() {\n      this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n      this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n      this._cleanupScroll?.();\n      this._elementScrolled.complete();\n      this.scrollDispatcher.deregister(this);\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n      return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n      return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n      const el = this.elementRef.nativeElement;\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      // Rewrite start & end offsets as right or left offsets.\n      if (options.left == null) {\n        options.left = isRtl ? options.end : options.start;\n      }\n      if (options.right == null) {\n        options.right = isRtl ? options.start : options.end;\n      }\n      // Rewrite the bottom offset as a top offset.\n      if (options.bottom != null) {\n        options.top = el.scrollHeight - el.clientHeight - options.bottom;\n      }\n      // Rewrite the right offset as a left offset.\n      if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n        if (options.left != null) {\n          options.right = el.scrollWidth - el.clientWidth - options.left;\n        }\n        if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n          options.left = options.right;\n        } else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n          options.left = options.right ? -options.right : options.right;\n        }\n      } else {\n        if (options.right != null) {\n          options.left = el.scrollWidth - el.clientWidth - options.right;\n        }\n      }\n      this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n      const el = this.elementRef.nativeElement;\n      if (supportsScrollBehavior()) {\n        el.scrollTo(options);\n      } else {\n        if (options.top != null) {\n          el.scrollTop = options.top;\n        }\n        if (options.left != null) {\n          el.scrollLeft = options.left;\n        }\n      }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n      const LEFT = 'left';\n      const RIGHT = 'right';\n      const el = this.elementRef.nativeElement;\n      if (from == 'top') {\n        return el.scrollTop;\n      }\n      if (from == 'bottom') {\n        return el.scrollHeight - el.clientHeight - el.scrollTop;\n      }\n      // Rewrite start & end as left or right offsets.\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      if (from == 'start') {\n        from = isRtl ? RIGHT : LEFT;\n      } else if (from == 'end') {\n        from = isRtl ? LEFT : RIGHT;\n      }\n      if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n        // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        } else {\n          return el.scrollLeft;\n        }\n      } else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n        // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft + el.scrollWidth - el.clientWidth;\n        } else {\n          return -el.scrollLeft;\n        }\n      } else {\n        // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n        // (scrollWidth - clientWidth) when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft;\n        } else {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        }\n      }\n    }\n    static ɵfac = function CdkScrollable_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkScrollable)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkScrollable,\n      selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n    });\n  }\n  return CdkScrollable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nlet ViewportRuler = /*#__PURE__*/(() => {\n  class ViewportRuler {\n    _platform = inject(Platform);\n    _listeners;\n    /** Cached viewport dimensions. */\n    _viewportSize;\n    /** Stream of viewport change events. */\n    _change = new Subject();\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, {\n      optional: true\n    });\n    constructor() {\n      const ngZone = inject(NgZone);\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      ngZone.runOutsideAngular(() => {\n        if (this._platform.isBrowser) {\n          const changeListener = event => this._change.next(event);\n          this._listeners = [renderer.listen('window', 'resize', changeListener), renderer.listen('window', 'orientationchange', changeListener)];\n        }\n        // Clear the cached position so that the viewport is re-measured next time it is required.\n        // We don't need to keep track of the subscription, because it is completed on destroy.\n        this.change().subscribe(() => this._viewportSize = null);\n      });\n    }\n    ngOnDestroy() {\n      this._listeners?.forEach(cleanup => cleanup());\n      this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n      if (!this._viewportSize) {\n        this._updateViewportSize();\n      }\n      const output = {\n        width: this._viewportSize.width,\n        height: this._viewportSize.height\n      };\n      // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n      if (!this._platform.isBrowser) {\n        this._viewportSize = null;\n      }\n      return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n      // Use the document element's bounding rect rather than the window scroll properties\n      // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n      // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n      // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n      // can disagree when the page is pinch-zoomed (on devices that support touch).\n      // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n      // We use the documentElement instead of the body because, by default (without a css reset)\n      // browsers typically give the document body an 8px margin, which is not included in\n      // getBoundingClientRect().\n      const scrollPosition = this.getViewportScrollPosition();\n      const {\n        width,\n        height\n      } = this.getViewportSize();\n      return {\n        top: scrollPosition.top,\n        left: scrollPosition.left,\n        bottom: scrollPosition.top + height,\n        right: scrollPosition.left + width,\n        height,\n        width\n      };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n      // While we can get a reference to the fake document\n      // during SSR, it doesn't have getBoundingClientRect.\n      if (!this._platform.isBrowser) {\n        return {\n          top: 0,\n          left: 0\n        };\n      }\n      // The top-left-corner of the viewport is determined by the scroll position of the document\n      // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n      // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n      // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n      // `document.documentElement` works consistently, where the `top` and `left` values will\n      // equal negative the scroll position.\n      const document = this._document;\n      const window = this._getWindow();\n      const documentElement = document.documentElement;\n      const documentRect = documentElement.getBoundingClientRect();\n      const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n      const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n      return {\n        top,\n        left\n      };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n      return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n      const window = this._getWindow();\n      this._viewportSize = this._platform.isBrowser ? {\n        width: window.innerWidth,\n        height: window.innerHeight\n      } : {\n        width: 0,\n        height: 0\n      };\n    }\n    static ɵfac = function ViewportRuler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewportRuler)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ViewportRuler,\n      factory: ViewportRuler.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ViewportRuler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst VIRTUAL_SCROLLABLE = /*#__PURE__*/new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the `CdkScrollable` to be used as scrolling container for virtual scrolling.\n */\nlet CdkVirtualScrollable = /*#__PURE__*/(() => {\n  class CdkVirtualScrollable extends CdkScrollable {\n    constructor() {\n      super();\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n      const viewportEl = this.elementRef.nativeElement;\n      return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static ɵfac = function CdkVirtualScrollable_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkVirtualScrollable)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollable,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nlet CdkVirtualScrollViewport = /*#__PURE__*/(() => {\n  class CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _scrollStrategy = inject(VIRTUAL_SCROLL_STRATEGY, {\n      optional: true\n    });\n    scrollable = inject(VIRTUAL_SCROLLABLE, {\n      optional: true\n    });\n    _platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    _detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    _renderedRangeSubject = new Subject();\n    /** The direction the viewport scrolls. */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(orientation) {\n      if (this._orientation !== orientation) {\n        this._orientation = orientation;\n        this._calculateSpacerSize();\n      }\n    }\n    _orientation = 'vertical';\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** The element that wraps the rendered content. */\n    _contentWrapper;\n    /** A stream that emits whenever the rendered range changes. */\n    renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    _totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    _totalContentWidth = signal('');\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    _totalContentHeight = signal('');\n    /**\n     * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n     * of the visible viewport.\n     */\n    _renderedContentTransform;\n    /** The currently rendered range of indices. */\n    _renderedRange = {\n      start: 0,\n      end: 0\n    };\n    /** The length of the data bound to this viewport (in number of items). */\n    _dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    _viewportSize = 0;\n    /** the currently attached CdkVirtualScrollRepeater. */\n    _forOf;\n    /** The last rendered content offset that was set. */\n    _renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    _renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    _isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    _runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    _viewportChanges = Subscription.EMPTY;\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n      super();\n      const viewportRuler = inject(ViewportRuler);\n      if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n      }\n      this._viewportChanges = viewportRuler.change().subscribe(() => {\n        this.checkViewportSize();\n      });\n      if (!this.scrollable) {\n        // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n        this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n        this.scrollable = this;\n      }\n    }\n    ngOnInit() {\n      // Scrolling depends on the element dimensions which we can't get during SSR.\n      if (!this._platform.isBrowser) {\n        return;\n      }\n      if (this.scrollable === this) {\n        super.ngOnInit();\n      }\n      // It's still too early to measure the viewport at this point. Deferring with a promise allows\n      // the Viewport to be rendered with the correct size before we measure. We run this outside the\n      // zone to avoid causing more change detection cycles. We handle the change detection loop\n      // ourselves instead.\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._measureViewportSize();\n        this._scrollStrategy.attach(this);\n        this.scrollable.elementScrolled().pipe(\n        // Start off with a fake scroll event so we properly detect our initial position.\n        startWith(null),\n        // Collect multiple events into one until the next animation frame. This way if\n        // there are multiple scroll events in the same frame we only need to recheck\n        // our layout once.\n        auditTime(0, SCROLL_SCHEDULER),\n        // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n        // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n        // to unsubscribe here just in case.\n        takeUntil(this._destroyed)).subscribe(() => this._scrollStrategy.onContentScrolled());\n        this._markChangeDetectionNeeded();\n      }));\n    }\n    ngOnDestroy() {\n      this.detach();\n      this._scrollStrategy.detach();\n      // Complete all subjects\n      this._renderedRangeSubject.complete();\n      this._detachedSubject.complete();\n      this._viewportChanges.unsubscribe();\n      this._isDestroyed = true;\n      super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n      if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('CdkVirtualScrollViewport is already attached.');\n      }\n      // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n      // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n      // change detection loop ourselves.\n      this.ngZone.runOutsideAngular(() => {\n        this._forOf = forOf;\n        this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n          const newLength = data.length;\n          if (newLength !== this._dataLength) {\n            this._dataLength = newLength;\n            this._scrollStrategy.onDataLengthChanged();\n          }\n          this._doChangeDetection();\n        });\n      });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n      this._forOf = null;\n      this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n      return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n      return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n      return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n      if (this._totalContentSize !== size) {\n        this._totalContentSize = size;\n        this._calculateSpacerSize();\n        this._markChangeDetectionNeeded();\n      }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n      if (!rangesEqual(this._renderedRange, range)) {\n        if (this.appendOnly) {\n          range = {\n            start: 0,\n            end: Math.max(this._renderedRange.end, range.end)\n          };\n        }\n        this._renderedRangeSubject.next(this._renderedRange = range);\n        this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n      }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n      return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n      // In appendOnly, we always start from the top\n      offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n      // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n      // in the negative direction.\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      const isHorizontal = this.orientation == 'horizontal';\n      const axis = isHorizontal ? 'X' : 'Y';\n      const axisDirection = isHorizontal && isRtl ? -1 : 1;\n      let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n      this._renderedContentOffset = offset;\n      if (to === 'to-end') {\n        transform += ` translate${axis}(-100%)`;\n        // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n        // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n        // expand upward).\n        this._renderedContentOffsetNeedsRewrite = true;\n      }\n      if (this._renderedContentTransform != transform) {\n        // We know this value is safe because we parse `offset` with `Number()` before passing it\n        // into the string.\n        this._renderedContentTransform = transform;\n        this._markChangeDetectionNeeded(() => {\n          if (this._renderedContentOffsetNeedsRewrite) {\n            this._renderedContentOffset -= this.measureRenderedContentSize();\n            this._renderedContentOffsetNeedsRewrite = false;\n            this.setRenderedContentOffset(this._renderedContentOffset);\n          } else {\n            this._scrollStrategy.onRenderedOffsetChanged();\n          }\n        });\n      }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n      const options = {\n        behavior\n      };\n      if (this.orientation === 'horizontal') {\n        options.start = offset;\n      } else {\n        options.top = offset;\n      }\n      this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n      this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n      // This is to break the call cycle\n      let measureScrollOffset;\n      if (this.scrollable == this) {\n        measureScrollOffset = _from => super.measureScrollOffset(_from);\n      } else {\n        measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n      }\n      return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) - this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n      let fromRect;\n      const LEFT = 'left';\n      const RIGHT = 'right';\n      const isRtl = this.dir?.value == 'rtl';\n      if (from == 'start') {\n        fromRect = isRtl ? RIGHT : LEFT;\n      } else if (from == 'end') {\n        fromRect = isRtl ? LEFT : RIGHT;\n      } else if (from) {\n        fromRect = from;\n      } else {\n        fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n      }\n      const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n      const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n      return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n      const contentEl = this._contentWrapper.nativeElement;\n      return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n      if (!this._forOf) {\n        return 0;\n      }\n      return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n      // TODO: Cleanup later when add logic for handling content resize\n      this._measureViewportSize();\n      this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n      this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n      if (runAfter) {\n        this._runAfterChangeDetection.push(runAfter);\n      }\n      // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n      // properties sequentially we only have to run `_doChangeDetection` once at the end.\n      if (!this._isChangeDetectionPending) {\n        this._isChangeDetectionPending = true;\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n          this._doChangeDetection();\n        }));\n      }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n      if (this._isDestroyed) {\n        return;\n      }\n      this.ngZone.run(() => {\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this._changeDetectorRef.markForCheck();\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        afterNextRender(() => {\n          this._isChangeDetectionPending = false;\n          const runAfterChangeDetection = this._runAfterChangeDetection;\n          this._runAfterChangeDetection = [];\n          for (const fn of runAfterChangeDetection) {\n            fn();\n          }\n        }, {\n          injector: this._injector\n        });\n      });\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n      this._totalContentHeight.set(this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`);\n      this._totalContentWidth.set(this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '');\n    }\n    static ɵfac = function CdkVirtualScrollViewport_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkVirtualScrollViewport)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkVirtualScrollViewport,\n      selectors: [[\"cdk-virtual-scroll-viewport\"]],\n      viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n        }\n      },\n      hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n      hostVars: 4,\n      hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n        }\n      },\n      inputs: {\n        orientation: \"orientation\",\n        appendOnly: [2, \"appendOnly\", \"appendOnly\", booleanAttribute]\n      },\n      outputs: {\n        scrolledIndexChange: \"scrolledIndexChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 4,\n      consts: [[\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-content-wrapper\"], [1, \"cdk-virtual-scroll-spacer\"]],\n      template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth())(\"height\", ctx._totalContentHeight());\n        }\n      },\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return CdkVirtualScrollViewport;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nlet CdkVirtualForOf = /*#__PURE__*/(() => {\n  class CdkVirtualForOf {\n    _viewContainerRef = inject(ViewContainerRef);\n    _template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _viewport = inject(CdkVirtualScrollViewport, {\n      skipSelf: true\n    });\n    /** Emits when the rendered view of the data changes. */\n    viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    _dataSourceChanges = new Subject();\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n      return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n      this._cdkVirtualForOf = value;\n      if (isDataSource(value)) {\n        this._dataSourceChanges.next(value);\n      } else {\n        // If value is an an NgIterable, convert it to an array.\n        this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n      }\n    }\n    _cdkVirtualForOf;\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n      return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n      this._needsUpdate = true;\n      this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n    }\n    _cdkVirtualForTrackBy;\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n      if (value) {\n        this._needsUpdate = true;\n        this._template = value;\n      }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n      return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n      this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /** Emits whenever the data in the current DataSource changes. */\n    dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null),\n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(),\n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    _differ = null;\n    /** The most recent data emitted from the DataSource. */\n    _data;\n    /** The currently rendered items. */\n    _renderedItems;\n    /** The currently rendered range of indices. */\n    _renderedRange;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    _needsUpdate = false;\n    _destroyed = new Subject();\n    constructor() {\n      const ngZone = inject(NgZone);\n      this.dataStream.subscribe(data => {\n        this._data = data;\n        this._onRenderedDataChange();\n      });\n      this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n        this._renderedRange = range;\n        if (this.viewChange.observers.length) {\n          ngZone.run(() => this.viewChange.next(this._renderedRange));\n        }\n        this._onRenderedDataChange();\n      });\n      this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n      if (range.start >= range.end) {\n        return 0;\n      }\n      if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Error: attempted to measure an item that isn't rendered.`);\n      }\n      // The index into the list of rendered views for the first item in the range.\n      const renderedStartIndex = range.start - this._renderedRange.start;\n      // The length of the range we're measuring.\n      const rangeLen = range.end - range.start;\n      // Loop over all the views, find the first and land node and compute the size by subtracting\n      // the top of the first node from the bottom of the last one.\n      let firstNode;\n      let lastNode;\n      // Find the first node by starting from the beginning and going forwards.\n      for (let i = 0; i < rangeLen; i++) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n        if (view && view.rootNodes.length) {\n          firstNode = lastNode = view.rootNodes[0];\n          break;\n        }\n      }\n      // Find the last node by starting from the end and going backwards.\n      for (let i = rangeLen - 1; i > -1; i--) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n        if (view && view.rootNodes.length) {\n          lastNode = view.rootNodes[view.rootNodes.length - 1];\n          break;\n        }\n      }\n      return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n    }\n    ngDoCheck() {\n      if (this._differ && this._needsUpdate) {\n        // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n        // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n        // changing (need to do this diff).\n        const changes = this._differ.diff(this._renderedItems);\n        if (!changes) {\n          this._updateContext();\n        } else {\n          this._applyChanges(changes);\n        }\n        this._needsUpdate = false;\n      }\n    }\n    ngOnDestroy() {\n      this._viewport.detach();\n      this._dataSourceChanges.next(undefined);\n      this._dataSourceChanges.complete();\n      this.viewChange.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n      if (!this._renderedRange) {\n        return;\n      }\n      this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n      if (!this._differ) {\n        // Use a wrapper function for the `trackBy` so any new values are\n        // picked up automatically without having to recreate the differ.\n        this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n          return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n        });\n      }\n      this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n      if (oldDs) {\n        oldDs.disconnect(this);\n      }\n      this._needsUpdate = true;\n      return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n        this._updateComputedContextProperties(view.context);\n        view.detectChanges();\n      }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n      this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n      // Update $implicit for any items that had an identity change.\n      changes.forEachIdentityChange(record => {\n        const view = this._viewContainerRef.get(record.currentIndex);\n        view.context.$implicit = record.item;\n      });\n      // Update the context variables on all items.\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n        this._updateComputedContextProperties(view.context);\n      }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n      context.first = context.index === 0;\n      context.last = context.index === context.count - 1;\n      context.even = context.index % 2 === 0;\n      context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n      // Note that it's important that we insert the item directly at the proper index,\n      // rather than inserting it and the moving it in place, because if there's a directive\n      // on the same node that injects the `ViewContainerRef`, Angular will insert another\n      // comment node which can throw off the move when it's being repeated for all items.\n      return {\n        templateRef: this._template,\n        context: {\n          $implicit: record.item,\n          // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n          // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n          cdkVirtualForOf: this._cdkVirtualForOf,\n          index: -1,\n          count: -1,\n          first: false,\n          last: false,\n          odd: false,\n          even: false\n        },\n        index\n      };\n    }\n    static ngTemplateContextGuard(directive, context) {\n      return true;\n    }\n    static ɵfac = function CdkVirtualForOf_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkVirtualForOf)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualForOf,\n      selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n      inputs: {\n        cdkVirtualForOf: \"cdkVirtualForOf\",\n        cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n        cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n        cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n  return CdkVirtualForOf;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nlet CdkVirtualScrollableElement = /*#__PURE__*/(() => {\n  class CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor() {\n      super();\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n    }\n    static ɵfac = function CdkVirtualScrollableElement_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkVirtualScrollableElement)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableElement,\n      selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n      hostAttrs: [1, \"cdk-virtual-scrollable\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollableElement;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nlet CdkVirtualScrollableWindow = /*#__PURE__*/(() => {\n  class CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor() {\n      super();\n      const document = inject(DOCUMENT);\n      this.elementRef = new ElementRef(document.documentElement);\n      this._scrollElement = document;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static ɵfac = function CdkVirtualScrollableWindow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkVirtualScrollableWindow)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableWindow,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollableWindow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkScrollableModule = /*#__PURE__*/(() => {\n  class CdkScrollableModule {\n    static ɵfac = function CdkScrollableModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkScrollableModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkScrollableModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return CdkScrollableModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-primary-export\n */\nlet ScrollingModule = /*#__PURE__*/(() => {\n  class ScrollingModule {\n    static ɵfac = function ScrollingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollingModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ScrollingModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n    });\n  }\n  return ScrollingModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "Directive", "Input", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "Renderer2", "DOCUMENT", "ChangeDetectorRef", "signal", "Injector", "afterNextRender", "booleanAttribute", "Optional", "Inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "NgModule", "Subject", "of", "Observable", "Subscription", "animationFrameScheduler", "asapScheduler", "isObservable", "distinctUntilChanged", "auditTime", "filter", "startWith", "takeUntil", "pairwise", "switchMap", "shareReplay", "c", "coerceNumberProperty", "a", "coerceElement", "P", "Platform", "D", "Directionality", "g", "getRtlScrollAxisType", "R", "RtlScrollAxisType", "s", "supportsScrollBehavior", "BidiModule", "_c0", "_c1", "<PERSON><PERSON>", "ɵɵDir", "b", "_VIEW_REPEATER_STRATEGY", "A", "ArrayDataSource", "_", "_RecycleViewRepeaterStrategy", "i", "isDataSource", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "CdkFixedSizeVirtualScroll_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "ɵɵNgOnChangesFeature", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_cleanupGlobalListener", "_scrolled", "_scrolledCount", "scrollContainers", "Map", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "runOutsideAngular", "listen", "subscription", "undefined", "ngOnDestroy", "for<PERSON>ach", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "ScrollDispatcher_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "optional", "_scrollElement", "_destroyed", "_cleanupScroll", "_elementScrolled", "ngOnInit", "event", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "NORMAL", "scrollWidth", "clientWidth", "INVERTED", "NEGATED", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "CdkScrollable_Factory", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_listeners", "_viewportSize", "_change", "_document", "renderer", "changeListener", "change", "cleanup", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "document", "window", "_getWindow", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "defaultView", "innerWidth", "innerHeight", "ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_changeDetectorRef", "_detachedSubject", "_renderedRangeSubject", "_orientation", "_calculateSpacerSize", "appendOnly", "Promise", "resolve", "then", "run", "_contentWrapper", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_renderedContentTransform", "_rendered<PERSON><PERSON>e", "_dataLength", "_forOf", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "_injector", "_isDestroyed", "viewportRuler", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "runAfterChangeDetection", "fn", "injector", "CdkVirtualScrollViewport_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "CdkVirtualScrollViewport_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "outputs", "virtualScrollable", "ngContentSelectors", "decls", "vars", "consts", "template", "CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "_viewContainerRef", "_template", "_differs", "_view<PERSON><PERSON><PERSON>er", "skipSelf", "viewChange", "_dataSourceChanges", "cdkVirtualForOf", "_cdkVirtualForOf", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "cdkVirtualForTemplate", "cdkVirtualForTemplateCacheSize", "viewCacheSize", "prev", "cur", "_changeDataSource", "_differ", "_data", "_renderedItems", "_onRenderedDataChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "last", "even", "odd", "templateRef", "ngTemplateContextGuard", "directive", "CdkVirtualForOf_Factory", "useClass", "CdkVirtualScrollableElement", "CdkVirtualScrollableElement_Factory", "useExisting", "CdkVirtualScrollableWindow", "CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "CdkScrollableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "ScrollingModule", "ScrollingModule_Factory", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZ<PERSON>, RendererFactory2, Injectable, ElementRef, Renderer2, DOCUMENT, ChangeDetectorRef, signal, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport '@angular/common';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    _scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    _viewport = null;\n    /** The size of the items in the virtually scrolling list. */\n    _itemSize;\n    /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n    _minBufferPx;\n    /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n    _maxBufferPx;\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    _itemSize = 20;\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    _minBufferPx = 100;\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    _maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    _scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupGlobalListener;\n    constructor() { }\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    _scrolled = new Subject();\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    _scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    scrollContainers = new Map();\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._cleanupGlobalListener) {\n                this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._cleanupGlobalListener?.();\n                    this._cleanupGlobalListener = undefined;\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._cleanupGlobalListener?.();\n        this._cleanupGlobalListener = undefined;\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    elementRef = inject(ElementRef);\n    scrollDispatcher = inject(ScrollDispatcher);\n    ngZone = inject(NgZone);\n    dir = inject(Directionality, { optional: true });\n    _scrollElement = this.elementRef.nativeElement;\n    _destroyed = new Subject();\n    _renderer = inject(Renderer2);\n    _cleanupScroll;\n    _elementScrolled = new Subject();\n    constructor() { }\n    ngOnInit() {\n        this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this._cleanupScroll?.();\n        this._elementScrolled.complete();\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                }]\n        }], ctorParameters: () => [] });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    _platform = inject(Platform);\n    _listeners;\n    /** Cached viewport dimensions. */\n    _viewportSize;\n    /** Stream of viewport change events. */\n    _change = new Subject();\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    constructor() {\n        const ngZone = inject(NgZone);\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        ngZone.runOutsideAngular(() => {\n            if (this._platform.isBrowser) {\n                const changeListener = (event) => this._change.next(event);\n                this._listeners = [\n                    renderer.listen('window', 'resize', changeListener),\n                    renderer.listen('window', 'orientationchange', changeListener),\n                ];\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        this._listeners?.forEach(cleanup => cleanup());\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the `CdkScrollable` to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor() {\n        super();\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollable, isStandalone: true, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _scrollStrategy = inject(VIRTUAL_SCROLL_STRATEGY, {\n        optional: true,\n    });\n    scrollable = inject(VIRTUAL_SCROLLABLE, { optional: true });\n    _platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    _detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    _renderedRangeSubject = new Subject();\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    _orientation = 'vertical';\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** The element that wraps the rendered content. */\n    _contentWrapper;\n    /** A stream that emits whenever the rendered range changes. */\n    renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    _totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    _totalContentWidth = signal('');\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    _totalContentHeight = signal('');\n    /**\n     * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n     * of the visible viewport.\n     */\n    _renderedContentTransform;\n    /** The currently rendered range of indices. */\n    _renderedRange = { start: 0, end: 0 };\n    /** The length of the data bound to this viewport (in number of items). */\n    _dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    _viewportSize = 0;\n    /** the currently attached CdkVirtualScrollRepeater. */\n    _forOf;\n    /** The last rendered content offset that was set. */\n    _renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    _renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    _isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    _runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    _viewportChanges = Subscription.EMPTY;\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        const viewportRuler = inject(ViewportRuler);\n        if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        this._isDestroyed = true;\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        if (this._isDestroyed) {\n            return;\n        }\n        this.ngZone.run(() => {\n            // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n            // from the root, since the repeated items are content projected in. Calling `detectChanges`\n            // instead does not properly check the projected content.\n            this._changeDetectorRef.markForCheck();\n            // Apply the content transform. The transform can't be set via an Angular binding because\n            // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n            // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n            // the `Number` function first to coerce it to a numeric value.\n            this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n            afterNextRender(() => {\n                this._isChangeDetectionPending = false;\n                const runAfterChangeDetection = this._runAfterChangeDetection;\n                this._runAfterChangeDetection = [];\n                for (const fn of runAfterChangeDetection) {\n                    fn();\n                }\n            }, { injector: this._injector });\n        });\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight.set(this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`);\n        this._totalContentWidth.set(this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: [\"appendOnly\", \"appendOnly\", booleanAttribute] }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth()\\\" [style.height]=\\\"_totalContentHeight()\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth()\\\" [style.height]=\\\"_totalContentHeight()\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    _viewContainerRef = inject(ViewContainerRef);\n    _template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _viewport = inject(CdkVirtualScrollViewport, { skipSelf: true });\n    /** Emits when the rendered view of the data changes. */\n    viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    _dataSourceChanges = new Subject();\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    _cdkVirtualForOf;\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    _cdkVirtualForTrackBy;\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /** Emits whenever the data in the current DataSource changes. */\n    dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null), \n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(), \n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    _differ = null;\n    /** The most recent data emitted from the DataSource. */\n    _data;\n    /** The currently rendered items. */\n    _renderedItems;\n    /** The currently rendered range of indices. */\n    _renderedRange;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    _needsUpdate = false;\n    _destroyed = new Subject();\n    constructor() {\n        const ngZone = inject(NgZone);\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static ngTemplateContextGuard(directive, context) {\n        return true;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualForOf, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor() {\n        super();\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor() {\n        super();\n        const document = inject(DOCUMENT);\n        this.elementRef = new ElementRef(document.documentElement);\n        this._scrollElement = document;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                }]\n        }], ctorParameters: () => [] });\n\nclass CdkScrollableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzX,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,MAAM;AAClH,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;AACtF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACzH,SAASC,UAAU,QAAQ,YAAY;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACxC,SAASC,GAAG,IAAIC,KAAK,QAAQ,YAAY;AACzC,SAASC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,4BAA4B,QAAQ,+CAA+C;AACrJ,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC9D,OAAO,iBAAiB;;AAExB;AACA,MAAMC,uBAAuB,gBAAG,IAAIrE,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAMsE,8BAA8B,CAAC;EACjCC,oBAAoB,gBAAG,IAAI5C,OAAO,CAAC,CAAC;EACpC;EACA6C,mBAAmB,gBAAG,IAAI,CAACD,oBAAoB,CAACE,IAAI,cAACvC,oBAAoB,CAAC,CAAC,CAAC;EAC5E;EACAwC,SAAS,GAAG,IAAI;EAChB;EACAC,SAAS;EACT;EACAC,YAAY;EACZ;EACAC,YAAY;EACZ;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACN,SAAS,GAAGI,QAAQ;IACzB,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACH,YAAY,GAAGI,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACT,SAAS,GAAGS,QAAQ;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACf,oBAAoB,CAACgB,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACb,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,uBAAuBA,CAACT,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACf,SAAS,GAAGI,QAAQ;IACzB,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACH,YAAY,GAAGI,WAAW;IAC/B,IAAI,CAACG,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACvB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACwB,cAAc,CAACF,KAAK,GAAG,IAAI,CAACrB,SAAS,EAAEsB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACV,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACyB,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC0B,aAAa,CAAC,CAAC,GAAG,IAAI,CAACzB,SAAS,CAAC;EACvF;EACA;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACX,SAAS,EAAE;MACjB;IACJ;IACA,MAAM2B,aAAa,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAChC,SAAS,CAACiC,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAAClC,SAAS,CAAC0B,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAACnC,SAAS,CAACoC,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAACpC,SAAS,GAAG,CAAC,GAAGkC,YAAY,GAAG,IAAI,CAAClC,SAAS,GAAG,CAAC;IAC9E;IACA,IAAI4B,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC/B,SAAS,CAAC;MAChE,MAAMwC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACxC,SAAS;QAC/C4B,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAAC7B,SAAS;IAClE,IAAI4C,WAAW,GAAG,IAAI,CAAC3C,YAAY,IAAI2B,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACrC,YAAY,GAAG0C,WAAW,IAAI,IAAI,CAAC5C,SAAS,CAAC;MACjF4B,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC9B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM8C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC9B,SAAS,IAAIkC,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAAC7C,YAAY,IAAI2B,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACrC,YAAY,GAAG4C,SAAS,IAAI,IAAI,CAAC9C,SAAS,CAAC;QAC7E,IAAI+C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAACiD,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAAC7B,SAAS,CAACkD,wBAAwB,CAAC,IAAI,CAACjD,SAAS,GAAG4B,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAACjC,oBAAoB,CAACsD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AAAA,IACMC,yBAAyB;EAA/B,MAAMA,yBAAyB,CAAC;IAC5B;IACA,IAAIlD,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACJ,SAAS;IACzB;IACA,IAAII,QAAQA,CAACmD,KAAK,EAAE;MAChB,IAAI,CAACvD,SAAS,GAAGhC,oBAAoB,CAACuF,KAAK,CAAC;IAChD;IACAvD,SAAS,GAAG,EAAE;IACd;AACJ;AACA;AACA;IACI,IAAIK,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACJ,YAAY;IAC5B;IACA,IAAII,WAAWA,CAACkD,KAAK,EAAE;MACnB,IAAI,CAACtD,YAAY,GAAGjC,oBAAoB,CAACuF,KAAK,CAAC;IACnD;IACAtD,YAAY,GAAG,GAAG;IAClB;AACJ;AACA;IACI,IAAIK,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACJ,YAAY;IAC5B;IACA,IAAII,WAAWA,CAACiD,KAAK,EAAE;MACnB,IAAI,CAACrD,YAAY,GAAGlC,oBAAoB,CAACuF,KAAK,CAAC;IACnD;IACArD,YAAY,GAAG,GAAG;IAClB;IACAmD,eAAe,GAAG,IAAI1D,8BAA8B,CAAC,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;IACvGkD,WAAWA,CAAA,EAAG;MACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAACT,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;IACnG;IACA,OAAOmD,IAAI,YAAAC,kCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFL,yBAAyB;IAAA;IAC5H,OAAOM,IAAI,kBAD8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EACJR,yBAAyB;MAAAS,SAAA;MAAAC,MAAA;QAAA5D,QAAA;QAAAC,WAAA;QAAAC,WAAA;MAAA;MAAA2D,QAAA,GADvB7I,EAAE,CAAA8I,kBAAA,CACmM,CACtR;QACIC,OAAO,EAAEzE,uBAAuB;QAChC0E,UAAU,EAAEjB,sCAAsC;QAClDkB,IAAI,EAAE,CAAC/I,UAAU,CAAC,MAAMgI,yBAAyB,CAAC;MACtD,CAAC,CACJ,GAPoFlI,EAAE,CAAAkJ,oBAAA;IAAA;EAQ/F;EAAC,OA3CKhB,yBAAyB;AAAA;AA4C/B;EAAA,QAAAxC,SAAA,oBAAAA,SAAA;AAAA;;AAoBA;AACA,MAAMyD,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AAHA,IAIMC,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnBC,OAAO,GAAGhJ,MAAM,CAACC,MAAM,CAAC;IACxBgJ,SAAS,GAAGjJ,MAAM,CAAC2C,QAAQ,CAAC;IAC5BuG,SAAS,GAAGlJ,MAAM,CAACE,gBAAgB,CAAC,CAACiJ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/DC,sBAAsB;IACtB1E,WAAWA,CAAA,EAAG,CAAE;IAChB;IACA2E,SAAS,GAAG,IAAI9H,OAAO,CAAC,CAAC;IACzB;IACA+H,cAAc,GAAG,CAAC;IAClB;AACJ;AACA;AACA;IACIC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B;AACJ;AACA;AACA;AACA;IACIC,QAAQA,CAACC,UAAU,EAAE;MACjB,IAAI,CAAC,IAAI,CAACH,gBAAgB,CAACI,GAAG,CAACD,UAAU,CAAC,EAAE;QACxC,IAAI,CAACH,gBAAgB,CAACK,GAAG,CAACF,UAAU,EAAEA,UAAU,CAACG,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACT,SAAS,CAAC5B,IAAI,CAACiC,UAAU,CAAC,CAAC,CAAC;MACxH;IACJ;IACA;AACJ;AACA;AACA;IACIK,UAAUA,CAACL,UAAU,EAAE;MACnB,MAAMM,mBAAmB,GAAG,IAAI,CAACT,gBAAgB,CAACU,GAAG,CAACP,UAAU,CAAC;MACjE,IAAIM,mBAAmB,EAAE;QACrBA,mBAAmB,CAACE,WAAW,CAAC,CAAC;QACjC,IAAI,CAACX,gBAAgB,CAACY,MAAM,CAACT,UAAU,CAAC;MAC5C;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIU,QAAQA,CAACC,aAAa,GAAGvB,mBAAmB,EAAE;MAC1C,IAAI,CAAC,IAAI,CAACG,SAAS,CAACqB,SAAS,EAAE;QAC3B,OAAO9I,EAAE,CAAC,CAAC;MACf;MACA,OAAO,IAAIC,UAAU,CAAE8I,QAAQ,IAAK;QAChC,IAAI,CAAC,IAAI,CAACnB,sBAAsB,EAAE;UAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACJ,OAAO,CAACwB,iBAAiB,CAAC,MAAM,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACpB,SAAS,CAAC5B,IAAI,CAAC,CAAC,CAAC,CAAC;QAChJ;QACA;QACA;QACA,MAAMiD,YAAY,GAAGL,aAAa,GAAG,CAAC,GAChC,IAAI,CAAChB,SAAS,CAAChF,IAAI,CAACtC,SAAS,CAACsI,aAAa,CAAC,CAAC,CAACP,SAAS,CAACS,QAAQ,CAAC,GACjE,IAAI,CAAClB,SAAS,CAACS,SAAS,CAACS,QAAQ,CAAC;QACxC,IAAI,CAACjB,cAAc,EAAE;QACrB,OAAO,MAAM;UACToB,YAAY,CAACR,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACZ,cAAc,EAAE;UACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;YACtB,IAAI,CAACF,sBAAsB,GAAG,CAAC;YAC/B,IAAI,CAACA,sBAAsB,GAAGuB,SAAS;UAC3C;QACJ,CAAC;MACL,CAAC,CAAC;IACN;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACxB,sBAAsB,GAAG,CAAC;MAC/B,IAAI,CAACA,sBAAsB,GAAGuB,SAAS;MACvC,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,CAAC,CAAChH,CAAC,EAAEiH,SAAS,KAAK,IAAI,CAACf,UAAU,CAACe,SAAS,CAAC,CAAC;MAC3E,IAAI,CAACzB,SAAS,CAAClE,QAAQ,CAAC,CAAC;IAC7B;IACA;AACJ;AACA;AACA;AACA;AACA;IACI4F,gBAAgBA,CAACC,mBAAmB,EAAEX,aAAa,EAAE;MACjD,MAAMY,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;MACvE,OAAO,IAAI,CAACZ,QAAQ,CAACC,aAAa,CAAC,CAAChG,IAAI,CAACrC,MAAM,CAACmJ,MAAM,IAAI,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzG;IACA;IACAD,2BAA2BA,CAACF,mBAAmB,EAAE;MAC7C,MAAMK,mBAAmB,GAAG,EAAE;MAC9B,IAAI,CAAC9B,gBAAgB,CAACsB,OAAO,CAAC,CAACS,aAAa,EAAE5B,UAAU,KAAK;QACzD,IAAI,IAAI,CAAC6B,0BAA0B,CAAC7B,UAAU,EAAEsB,mBAAmB,CAAC,EAAE;UAClEK,mBAAmB,CAACG,IAAI,CAAC9B,UAAU,CAAC;QACxC;MACJ,CAAC,CAAC;MACF,OAAO2B,mBAAmB;IAC9B;IACA;IACAE,0BAA0BA,CAAC7B,UAAU,EAAEsB,mBAAmB,EAAE;MACxD,IAAIS,OAAO,GAAGhJ,aAAa,CAACuI,mBAAmB,CAAC;MAChD,IAAIU,iBAAiB,GAAGhC,UAAU,CAACiC,aAAa,CAAC,CAAC,CAACC,aAAa;MAChE;MACA;MACA,GAAG;QACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;UAC9B,OAAO,IAAI;QACf;MACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;MACzC,OAAO,KAAK;IAChB;IACA,OAAO7D,IAAI,YAAA8D,yBAAA5D,iBAAA;MAAA,YAAAA,iBAAA,IAAwFa,gBAAgB;IAAA;IACnH,OAAOgD,KAAK,kBAjJ6EpM,EAAE,CAAAqM,kBAAA;MAAAC,KAAA,EAiJYlD,gBAAgB;MAAAmD,OAAA,EAAhBnD,gBAAgB,CAAAf,IAAA;MAAAmE,UAAA,EAAc;IAAM;EAC/I;EAAC,OA/GKpD,gBAAgB;AAAA;AAgHtB;EAAA,QAAA1D,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA;AACA;AACA;AACA;AAJA,IAKM+G,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBC,UAAU,GAAGrM,MAAM,CAACI,UAAU,CAAC;IAC/BkM,gBAAgB,GAAGtM,MAAM,CAAC+I,gBAAgB,CAAC;IAC3CwD,MAAM,GAAGvM,MAAM,CAACC,MAAM,CAAC;IACvBuM,GAAG,GAAGxM,MAAM,CAAC6C,cAAc,EAAE;MAAE4J,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChDC,cAAc,GAAG,IAAI,CAACL,UAAU,CAACT,aAAa;IAC9Ce,UAAU,GAAG,IAAIpL,OAAO,CAAC,CAAC;IAC1B2H,SAAS,GAAGlJ,MAAM,CAACK,SAAS,CAAC;IAC7BuM,cAAc;IACdC,gBAAgB,GAAG,IAAItL,OAAO,CAAC,CAAC;IAChCmD,WAAWA,CAAA,EAAG,CAAE;IAChBoI,QAAQA,CAAA,EAAG;MACP,IAAI,CAACF,cAAc,GAAG,IAAI,CAACL,MAAM,CAAC/B,iBAAiB,CAAC,MAAM,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,IAAI,CAACiC,cAAc,EAAE,QAAQ,EAAEK,KAAK,IAAI,IAAI,CAACF,gBAAgB,CAACpF,IAAI,CAACsF,KAAK,CAAC,CAAC,CAAC;MAC3J,IAAI,CAACT,gBAAgB,CAAC7C,QAAQ,CAAC,IAAI,CAAC;IACxC;IACAmB,WAAWA,CAAA,EAAG;MACV,IAAI,CAACgC,cAAc,GAAG,CAAC;MACvB,IAAI,CAACC,gBAAgB,CAAC1H,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACmH,gBAAgB,CAACvC,UAAU,CAAC,IAAI,CAAC;MACtC,IAAI,CAAC4C,UAAU,CAAClF,IAAI,CAAC,CAAC;MACtB,IAAI,CAACkF,UAAU,CAACxH,QAAQ,CAAC,CAAC;IAC9B;IACA;IACA0E,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACgD,gBAAgB;IAChC;IACA;IACAlB,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACU,UAAU;IAC1B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIW,QAAQA,CAACC,OAAO,EAAE;MACd,MAAMC,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;MACxC,MAAMuB,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC1E,KAAK,IAAI,KAAK;MACjD;MACA,IAAImF,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBH,OAAO,CAACG,IAAI,GAAGD,KAAK,GAAGF,OAAO,CAAC5G,GAAG,GAAG4G,OAAO,CAAC7G,KAAK;MACtD;MACA,IAAI6G,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;QACvBJ,OAAO,CAACI,KAAK,GAAGF,KAAK,GAAGF,OAAO,CAAC7G,KAAK,GAAG6G,OAAO,CAAC5G,GAAG;MACvD;MACA;MACA,IAAI4G,OAAO,CAACK,MAAM,IAAI,IAAI,EAAE;QACxBL,OAAO,CAACM,GAAG,GACPL,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGR,OAAO,CAACK,MAAM;MAC1D;MACA;MACA,IAAIH,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACyK,MAAM,EAAE;QAC7D,IAAIT,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;UACtBH,OAAO,CAACI,KAAK,GACTH,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACG,IAAI;QACtD;QACA,IAAIrK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAAC4K,QAAQ,EAAE;UACtDZ,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK;QAChC,CAAC,MACI,IAAItK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAAC6K,OAAO,EAAE;UAC1Db,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK,GAAG,CAACJ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;QACjE;MACJ,CAAC,MACI;QACD,IAAIJ,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;UACvBJ,OAAO,CAACG,IAAI,GACRF,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACI,KAAK;QACvD;MACJ;MACA,IAAI,CAACU,qBAAqB,CAACd,OAAO,CAAC;IACvC;IACAc,qBAAqBA,CAACd,OAAO,EAAE;MAC3B,MAAMC,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;MACxC,IAAIzI,sBAAsB,CAAC,CAAC,EAAE;QAC1B+J,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;MACxB,CAAC,MACI;QACD,IAAIA,OAAO,CAACM,GAAG,IAAI,IAAI,EAAE;UACrBL,EAAE,CAACc,SAAS,GAAGf,OAAO,CAACM,GAAG;QAC9B;QACA,IAAIN,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;UACtBF,EAAE,CAACe,UAAU,GAAGhB,OAAO,CAACG,IAAI;QAChC;MACJ;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI1G,mBAAmBA,CAACwH,IAAI,EAAE;MACtB,MAAMC,IAAI,GAAG,MAAM;MACnB,MAAMC,KAAK,GAAG,OAAO;MACrB,MAAMlB,EAAE,GAAG,IAAI,CAACb,UAAU,CAACT,aAAa;MACxC,IAAIsC,IAAI,IAAI,KAAK,EAAE;QACf,OAAOhB,EAAE,CAACc,SAAS;MACvB;MACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;QAClB,OAAOhB,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACc,SAAS;MAC3D;MACA;MACA,MAAMb,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC1E,KAAK,IAAI,KAAK;MACjD,IAAIoG,IAAI,IAAI,OAAO,EAAE;QACjBA,IAAI,GAAGf,KAAK,GAAGiB,KAAK,GAAGD,IAAI;MAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;QACpBA,IAAI,GAAGf,KAAK,GAAGgB,IAAI,GAAGC,KAAK;MAC/B;MACA,IAAIjB,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAAC4K,QAAQ,EAAE;QAC/D;QACA;QACA,IAAIK,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;QAC1D,CAAC,MACI;UACD,OAAOf,EAAE,CAACe,UAAU;QACxB;MACJ,CAAC,MACI,IAAId,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAAC6K,OAAO,EAAE;QACnE;QACA;QACA,IAAII,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACe,UAAU,GAAGf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW;QAC1D,CAAC,MACI;UACD,OAAO,CAACV,EAAE,CAACe,UAAU;QACzB;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACe,UAAU;QACxB,CAAC,MACI;UACD,OAAOf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;QAC1D;MACJ;IACJ;IACA,OAAOjG,IAAI,YAAAqG,sBAAAnG,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkE,aAAa;IAAA;IAChH,OAAOjE,IAAI,kBAhT8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EAgTJ+D,aAAa;MAAA9D,SAAA;IAAA;EACxG;EAAC,OApJK8D,aAAa;AAAA;AAqJnB;EAAA,QAAA/G,SAAA,oBAAAA,SAAA;AAAA;;AAOA;AACA,MAAMiJ,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AAHA,IAIMC,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBtF,SAAS,GAAGjJ,MAAM,CAAC2C,QAAQ,CAAC;IAC5B6L,UAAU;IACV;IACAC,aAAa;IACb;IACAC,OAAO,GAAG,IAAInN,OAAO,CAAC,CAAC;IACvB;IACAoN,SAAS,GAAG3O,MAAM,CAACM,QAAQ,EAAE;MAAEmM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChD/H,WAAWA,CAAA,EAAG;MACV,MAAM6H,MAAM,GAAGvM,MAAM,CAACC,MAAM,CAAC;MAC7B,MAAM2O,QAAQ,GAAG5O,MAAM,CAACE,gBAAgB,CAAC,CAACiJ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;MACpEoD,MAAM,CAAC/B,iBAAiB,CAAC,MAAM;QAC3B,IAAI,IAAI,CAACvB,SAAS,CAACqB,SAAS,EAAE;UAC1B,MAAMuE,cAAc,GAAI9B,KAAK,IAAK,IAAI,CAAC2B,OAAO,CAACjH,IAAI,CAACsF,KAAK,CAAC;UAC1D,IAAI,CAACyB,UAAU,GAAG,CACdI,QAAQ,CAACnE,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAEoE,cAAc,CAAC,EACnDD,QAAQ,CAACnE,MAAM,CAAC,QAAQ,EAAE,mBAAmB,EAAEoE,cAAc,CAAC,CACjE;QACL;QACA;QACA;QACA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAChF,SAAS,CAAC,MAAO,IAAI,CAAC2E,aAAa,GAAG,IAAK,CAAC;MAC9D,CAAC,CAAC;IACN;IACA7D,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC4D,UAAU,EAAE3D,OAAO,CAACkE,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC9C,IAAI,CAACL,OAAO,CAACvJ,QAAQ,CAAC,CAAC;IAC3B;IACA;IACAoB,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAACkI,aAAa,EAAE;QACrB,IAAI,CAACO,mBAAmB,CAAC,CAAC;MAC9B;MACA,MAAMC,MAAM,GAAG;QAAEC,KAAK,EAAE,IAAI,CAACT,aAAa,CAACS,KAAK;QAAEC,MAAM,EAAE,IAAI,CAACV,aAAa,CAACU;MAAO,CAAC;MACrF;MACA,IAAI,CAAC,IAAI,CAAClG,SAAS,CAACqB,SAAS,EAAE;QAC3B,IAAI,CAACmE,aAAa,GAAG,IAAI;MAC7B;MACA,OAAOQ,MAAM;IACjB;IACA;IACAG,eAAeA,CAAA,EAAG;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;MACvD,MAAM;QAAEJ,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAAC5I,eAAe,CAAC,CAAC;MAChD,OAAO;QACHgH,GAAG,EAAE8B,cAAc,CAAC9B,GAAG;QACvBH,IAAI,EAAEiC,cAAc,CAACjC,IAAI;QACzBE,MAAM,EAAE+B,cAAc,CAAC9B,GAAG,GAAG4B,MAAM;QACnC9B,KAAK,EAAEgC,cAAc,CAACjC,IAAI,GAAG8B,KAAK;QAClCC,MAAM;QACND;MACJ,CAAC;IACL;IACA;IACAI,yBAAyBA,CAAA,EAAG;MACxB;MACA;MACA,IAAI,CAAC,IAAI,CAACrG,SAAS,CAACqB,SAAS,EAAE;QAC3B,OAAO;UAAEiD,GAAG,EAAE,CAAC;UAAEH,IAAI,EAAE;QAAE,CAAC;MAC9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMmC,QAAQ,GAAG,IAAI,CAACZ,SAAS;MAC/B,MAAMa,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MAChC,MAAMC,eAAe,GAAGH,QAAQ,CAACG,eAAe;MAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,CAAC,CAAC;MAC5D,MAAMrC,GAAG,GAAG,CAACoC,YAAY,CAACpC,GAAG,IACzBgC,QAAQ,CAACM,IAAI,CAAC7B,SAAS,IACvBwB,MAAM,CAACM,OAAO,IACdJ,eAAe,CAAC1B,SAAS,IACzB,CAAC;MACL,MAAMZ,IAAI,GAAG,CAACuC,YAAY,CAACvC,IAAI,IAC3BmC,QAAQ,CAACM,IAAI,CAAC5B,UAAU,IACxBuB,MAAM,CAACO,OAAO,IACdL,eAAe,CAACzB,UAAU,IAC1B,CAAC;MACL,OAAO;QAAEV,GAAG;QAAEH;MAAK,CAAC;IACxB;IACA;AACJ;AACA;AACA;AACA;IACI0B,MAAMA,CAACkB,YAAY,GAAG1B,mBAAmB,EAAE;MACvC,OAAO0B,YAAY,GAAG,CAAC,GAAG,IAAI,CAACtB,OAAO,CAACrK,IAAI,CAACtC,SAAS,CAACiO,YAAY,CAAC,CAAC,GAAG,IAAI,CAACtB,OAAO;IACvF;IACA;IACAe,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAACd,SAAS,CAACsB,WAAW,IAAIT,MAAM;IAC/C;IACA;IACAR,mBAAmBA,CAAA,EAAG;MAClB,MAAMQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MAChC,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACxF,SAAS,CAACqB,SAAS,GACvC;QAAE4E,KAAK,EAAEM,MAAM,CAACU,UAAU;QAAEf,MAAM,EAAEK,MAAM,CAACW;MAAY,CAAC,GACxD;QAAEjB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;IACjC;IACA,OAAOnH,IAAI,YAAAoI,sBAAAlI,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqG,aAAa;IAAA;IAChH,OAAOxC,KAAK,kBA/a6EpM,EAAE,CAAAqM,kBAAA;MAAAC,KAAA,EA+aYsC,aAAa;MAAArC,OAAA,EAAbqC,aAAa,CAAAvG,IAAA;MAAAmE,UAAA,EAAc;IAAM;EAC5I;EAAC,OAjHKoC,aAAa;AAAA;AAkHnB;EAAA,QAAAlJ,SAAA,oBAAAA,SAAA;AAAA;AAKA,MAAMgL,kBAAkB,gBAAG,IAAIzQ,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AAFA,IAGM0Q,oBAAoB;EAA1B,MAAMA,oBAAoB,SAASlE,aAAa,CAAC;IAC7C1H,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;IACX;IACA;AACJ;AACA;AACA;AACA;IACI6L,mBAAmBA,CAACC,WAAW,EAAE;MAC7B,MAAMC,UAAU,GAAG,IAAI,CAACpE,UAAU,CAACT,aAAa;MAChD,OAAO4E,WAAW,KAAK,YAAY,GAAGC,UAAU,CAAC7C,WAAW,GAAG6C,UAAU,CAAChD,YAAY;IAC1F;IACA,OAAOzF,IAAI,YAAA0I,6BAAAxI,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoI,oBAAoB;IAAA;IACvH,OAAOnI,IAAI,kBAxc8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EAwcJiI,oBAAoB;MAAA9H,QAAA,GAxclB7I,EAAE,CAAAgR,0BAAA;IAAA;EAyc/F;EAAC,OAfKL,oBAAoB;AAAA;AAgB1B;EAAA,QAAAjL,SAAA,oBAAAA,SAAA;AAAA;;AAIA;AACA,SAASuL,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAACzK,KAAK,IAAI0K,EAAE,CAAC1K,KAAK,IAAIyK,EAAE,CAACxK,GAAG,IAAIyK,EAAE,CAACzK,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0K,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAGrP,uBAAuB,GAAGC,aAAa;AAC/G;AAAA,IACMqP,wBAAwB;EAA9B,MAAMA,wBAAwB,SAASX,oBAAoB,CAAC;IACxDjE,UAAU,GAAGrM,MAAM,CAACI,UAAU,CAAC;IAC/B8Q,kBAAkB,GAAGlR,MAAM,CAACO,iBAAiB,CAAC;IAC9CqH,eAAe,GAAG5H,MAAM,CAACiE,uBAAuB,EAAE;MAC9CwI,QAAQ,EAAE;IACd,CAAC,CAAC;IACF/C,UAAU,GAAG1J,MAAM,CAACqQ,kBAAkB,EAAE;MAAE5D,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3DxD,SAAS,GAAGjJ,MAAM,CAAC2C,QAAQ,CAAC;IAC5B;IACAwO,gBAAgB,GAAG,IAAI5P,OAAO,CAAC,CAAC;IAChC;IACA6P,qBAAqB,GAAG,IAAI7P,OAAO,CAAC,CAAC;IACrC;IACA,IAAIiP,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACa,YAAY;IAC5B;IACA,IAAIb,WAAWA,CAACA,WAAW,EAAE;MACzB,IAAI,IAAI,CAACa,YAAY,KAAKb,WAAW,EAAE;QACnC,IAAI,CAACa,YAAY,GAAGb,WAAW;QAC/B,IAAI,CAACc,oBAAoB,CAAC,CAAC;MAC/B;IACJ;IACAD,YAAY,GAAG,UAAU;IACzB;AACJ;AACA;AACA;IACIE,UAAU,GAAG,KAAK;IAClB;IACA;IACA;IACA;IACA;IACAnN,mBAAmB,GAAG,IAAI3C,UAAU,CAAE8I,QAAQ,IAAK,IAAI,CAAC3C,eAAe,CAACxD,mBAAmB,CAAC0F,SAAS,CAAClE,KAAK,IAAI4L,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACnF,MAAM,CAACoF,GAAG,CAAC,MAAMpH,QAAQ,CAAC9C,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1L;IACAgM,eAAe;IACf;IACAC,mBAAmB,GAAG,IAAI,CAACT,qBAAqB;IAChD;AACJ;AACA;IACIU,iBAAiB,GAAG,CAAC;IACrB;IACAC,kBAAkB,GAAGvR,MAAM,CAAC,EAAE,CAAC;IAC/B;IACAwR,mBAAmB,GAAGxR,MAAM,CAAC,EAAE,CAAC;IAChC;AACJ;AACA;AACA;IACIyR,yBAAyB;IACzB;IACAC,cAAc,GAAG;MAAE9L,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IACrC;IACA8L,WAAW,GAAG,CAAC;IACf;IACA1D,aAAa,GAAG,CAAC;IACjB;IACA2D,MAAM;IACN;IACAC,sBAAsB,GAAG,CAAC;IAC1B;AACJ;AACA;AACA;IACIC,kCAAkC,GAAG,KAAK;IAC1C;IACAC,yBAAyB,GAAG,KAAK;IACjC;IACAC,wBAAwB,GAAG,EAAE;IAC7B;IACAC,gBAAgB,GAAG/Q,YAAY,CAACgR,KAAK;IACrCC,SAAS,GAAG3S,MAAM,CAACS,QAAQ,CAAC;IAC5BmS,YAAY,GAAG,KAAK;IACpBlO,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAMmO,aAAa,GAAG7S,MAAM,CAACuO,aAAa,CAAC;MAC3C,IAAI,CAAC,IAAI,CAAC3G,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMC,KAAK,CAAC,gFAAgF,CAAC;MACjG;MACA,IAAI,CAACmN,gBAAgB,GAAGI,aAAa,CAAC/D,MAAM,CAAC,CAAC,CAAChF,SAAS,CAAC,MAAM;QAC3D,IAAI,CAACgJ,iBAAiB,CAAC,CAAC;MAC5B,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAACpJ,UAAU,EAAE;QAClB;QACA,IAAI,CAAC2C,UAAU,CAACT,aAAa,CAACmH,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrE,IAAI,CAACtJ,UAAU,GAAG,IAAI;MAC1B;IACJ;IACAoD,QAAQA,CAAA,EAAG;MACP;MACA,IAAI,CAAC,IAAI,CAAC7D,SAAS,CAACqB,SAAS,EAAE;QAC3B;MACJ;MACA,IAAI,IAAI,CAACZ,UAAU,KAAK,IAAI,EAAE;QAC1B,KAAK,CAACoD,QAAQ,CAAC,CAAC;MACpB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACP,MAAM,CAAC/B,iBAAiB,CAAC,MAAMgH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACuB,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACrL,eAAe,CAAC9C,MAAM,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC4E,UAAU,CACVG,eAAe,CAAC,CAAC,CACjBxF,IAAI;QACT;QACApC,SAAS,CAAC,IAAI,CAAC;QACf;QACA;QACA;QACAF,SAAS,CAAC,CAAC,EAAEgP,gBAAgB,CAAC;QAC9B;QACA;QACA;QACA7O,SAAS,CAAC,IAAI,CAACyK,UAAU,CAAC,CAAC,CACtB7C,SAAS,CAAC,MAAM,IAAI,CAAClC,eAAe,CAACrC,iBAAiB,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC2N,0BAA0B,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC;IACP;IACAtI,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC1F,MAAM,CAAC,CAAC;MACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,CAAC,CAAC;MAC7B;MACA,IAAI,CAACkM,qBAAqB,CAACjM,QAAQ,CAAC,CAAC;MACrC,IAAI,CAACgM,gBAAgB,CAAChM,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACsN,gBAAgB,CAACvI,WAAW,CAAC,CAAC;MACnC,IAAI,CAAC0I,YAAY,GAAG,IAAI;MACxB,KAAK,CAAChI,WAAW,CAAC,CAAC;IACvB;IACA;IACA9F,MAAMA,CAACqO,KAAK,EAAE;MACV,IAAI,IAAI,CAACf,MAAM,KAAK,OAAO/M,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;MAChE;MACA;MACA;MACA;MACA,IAAI,CAACiH,MAAM,CAAC/B,iBAAiB,CAAC,MAAM;QAChC,IAAI,CAAC4H,MAAM,GAAGe,KAAK;QACnB,IAAI,CAACf,MAAM,CAACgB,UAAU,CAAC/O,IAAI,CAACnC,SAAS,CAAC,IAAI,CAACiP,gBAAgB,CAAC,CAAC,CAACrH,SAAS,CAACuJ,IAAI,IAAI;UAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;UAC7B,IAAID,SAAS,KAAK,IAAI,CAACnB,WAAW,EAAE;YAChC,IAAI,CAACA,WAAW,GAAGmB,SAAS;YAC5B,IAAI,CAAC1L,eAAe,CAACpC,mBAAmB,CAAC,CAAC;UAC9C;UACA,IAAI,CAACgO,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA;IACAtO,MAAMA,CAAA,EAAG;MACL,IAAI,CAACkN,MAAM,GAAG,IAAI;MAClB,IAAI,CAACjB,gBAAgB,CAAC1J,IAAI,CAAC,CAAC;IAChC;IACA;IACAzB,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACmM,WAAW;IAC3B;IACA;IACA5L,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACkI,aAAa;IAC7B;IACA;IACA;IACA;IACA;IACA;IACAvI,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAACgM,cAAc;IAC9B;IACAuB,yCAAyCA,CAACvF,IAAI,EAAE;MAC5C,OAAO,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC;IAC3E;IACA;AACJ;AACA;AACA;IACInI,mBAAmBA,CAAC2N,IAAI,EAAE;MACtB,IAAI,IAAI,CAAC5B,iBAAiB,KAAK4B,IAAI,EAAE;QACjC,IAAI,CAAC5B,iBAAiB,GAAG4B,IAAI;QAC7B,IAAI,CAACpC,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAAC4B,0BAA0B,CAAC,CAAC;MACrC;IACJ;IACA;IACA3L,gBAAgBA,CAACoM,KAAK,EAAE;MACpB,IAAI,CAAC/C,WAAW,CAAC,IAAI,CAACsB,cAAc,EAAEyB,KAAK,CAAC,EAAE;QAC1C,IAAI,IAAI,CAACpC,UAAU,EAAE;UACjBoC,KAAK,GAAG;YAAEvN,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACkL,cAAc,CAAC7L,GAAG,EAAEsN,KAAK,CAACtN,GAAG;UAAE,CAAC;QAC3E;QACA,IAAI,CAAC+K,qBAAqB,CAAC3J,IAAI,CAAE,IAAI,CAACyK,cAAc,GAAGyB,KAAM,CAAC;QAC9D,IAAI,CAACT,0BAA0B,CAAC,MAAM,IAAI,CAACtL,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;MACnF;IACJ;IACA;AACJ;AACA;IACImO,+BAA+BA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACtB,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;IACvF;IACA;AACJ;AACA;AACA;IACI7K,wBAAwBA,CAACqM,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;MAC9C;MACAD,MAAM,GAAG,IAAI,CAACtC,UAAU,IAAIuC,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;MAC1D;MACA;MACA,MAAM1G,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC1E,KAAK,IAAI,KAAK;MACjD,MAAMiM,YAAY,GAAG,IAAI,CAACvD,WAAW,IAAI,YAAY;MACrD,MAAMwD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;MACrC,MAAME,aAAa,GAAGF,YAAY,IAAI5G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACpD,IAAI+G,SAAS,GAAG,YAAYF,IAAI,IAAIG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAC,KAAK;MACvE,IAAI,CAACxB,sBAAsB,GAAGwB,MAAM;MACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;QACjBI,SAAS,IAAI,aAAaF,IAAI,SAAS;QACvC;QACA;QACA;QACA,IAAI,CAAC1B,kCAAkC,GAAG,IAAI;MAClD;MACA,IAAI,IAAI,CAACL,yBAAyB,IAAIiC,SAAS,EAAE;QAC7C;QACA;QACA,IAAI,CAACjC,yBAAyB,GAAGiC,SAAS;QAC1C,IAAI,CAAChB,0BAA0B,CAAC,MAAM;UAClC,IAAI,IAAI,CAACZ,kCAAkC,EAAE;YACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAAC+B,0BAA0B,CAAC,CAAC;YAChE,IAAI,CAAC9B,kCAAkC,GAAG,KAAK;YAC/C,IAAI,CAAC9K,wBAAwB,CAAC,IAAI,CAAC6K,sBAAsB,CAAC;UAC9D,CAAC,MACI;YACD,IAAI,CAACzK,eAAe,CAAClC,uBAAuB,CAAC,CAAC;UAClD;QACJ,CAAC,CAAC;MACN;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACII,cAAcA,CAAC+N,MAAM,EAAEhO,QAAQ,GAAG,MAAM,EAAE;MACtC,MAAMoH,OAAO,GAAG;QAAEpH;MAAS,CAAC;MAC5B,IAAI,IAAI,CAAC2K,WAAW,KAAK,YAAY,EAAE;QACnCvD,OAAO,CAAC7G,KAAK,GAAGyN,MAAM;MAC1B,CAAC,MACI;QACD5G,OAAO,CAACM,GAAG,GAAGsG,MAAM;MACxB;MACA,IAAI,CAACnK,UAAU,CAACsD,QAAQ,CAACC,OAAO,CAAC;IACrC;IACA;AACJ;AACA;AACA;AACA;IACItH,aAAaA,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;MACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;IACvD;IACA;AACJ;AACA;AACA;AACA;IACIa,mBAAmBA,CAACwH,IAAI,EAAE;MACtB;MACA,IAAIxH,mBAAmB;MACvB,IAAI,IAAI,CAACgD,UAAU,IAAI,IAAI,EAAE;QACzBhD,mBAAmB,GAAI2N,KAAK,IAAK,KAAK,CAAC3N,mBAAmB,CAAC2N,KAAK,CAAC;MACrE,CAAC,MACI;QACD3N,mBAAmB,GAAI2N,KAAK,IAAK,IAAI,CAAC3K,UAAU,CAAChD,mBAAmB,CAAC2N,KAAK,CAAC;MAC/E;MACA,OAAOxN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAACwH,IAAI,KAAK,IAAI,CAACsC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAAC8D,qBAAqB,CAAC,CAAC,CAAC;IACrC;IACA;AACJ;AACA;AACA;IACIA,qBAAqBA,CAACpG,IAAI,EAAE;MACxB,IAAIqG,QAAQ;MACZ,MAAMpG,IAAI,GAAG,MAAM;MACnB,MAAMC,KAAK,GAAG,OAAO;MACrB,MAAMjB,KAAK,GAAG,IAAI,CAACX,GAAG,EAAE1E,KAAK,IAAI,KAAK;MACtC,IAAIoG,IAAI,IAAI,OAAO,EAAE;QACjBqG,QAAQ,GAAGpH,KAAK,GAAGiB,KAAK,GAAGD,IAAI;MACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;QACpBqG,QAAQ,GAAGpH,KAAK,GAAGgB,IAAI,GAAGC,KAAK;MACnC,CAAC,MACI,IAAIF,IAAI,EAAE;QACXqG,QAAQ,GAAGrG,IAAI;MACnB,CAAC,MACI;QACDqG,QAAQ,GAAG,IAAI,CAAC/D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;MACjE;MACA,MAAMgE,kBAAkB,GAAG,IAAI,CAAC9K,UAAU,CAAC+J,yCAAyC,CAACc,QAAQ,CAAC;MAC9F,MAAME,kBAAkB,GAAG,IAAI,CAACpI,UAAU,CAACT,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC2E,QAAQ,CAAC;MAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;IAClD;IACA;IACAJ,0BAA0BA,CAAA,EAAG;MACzB,MAAMM,SAAS,GAAG,IAAI,CAAC9C,eAAe,CAAChG,aAAa;MACpD,OAAO,IAAI,CAAC4E,WAAW,KAAK,YAAY,GAAGkE,SAAS,CAACC,WAAW,GAAGD,SAAS,CAACE,YAAY;IAC7F;IACA;AACJ;AACA;AACA;IACIC,gBAAgBA,CAAClB,KAAK,EAAE;MACpB,IAAI,CAAC,IAAI,CAACvB,MAAM,EAAE;QACd,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACA,MAAM,CAACyC,gBAAgB,CAAClB,KAAK,EAAE,IAAI,CAACnD,WAAW,CAAC;IAChE;IACA;IACAsC,iBAAiBA,CAAA,EAAG;MAChB;MACA,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACrL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;IAC9C;IACA;IACAyN,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACxE,aAAa,GAAG,IAAI,CAAC/E,UAAU,CAAC6G,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9E;IACA;IACA0C,0BAA0BA,CAAC4B,QAAQ,EAAE;MACjC,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACtC,wBAAwB,CAAChH,IAAI,CAACsJ,QAAQ,CAAC;MAChD;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACvC,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrC,IAAI,CAAChG,MAAM,CAAC/B,iBAAiB,CAAC,MAAMgH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UAC7D,IAAI,CAAC8B,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;MACP;IACJ;IACA;IACAA,kBAAkBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACZ,YAAY,EAAE;QACnB;MACJ;MACA,IAAI,CAACrG,MAAM,CAACoF,GAAG,CAAC,MAAM;QAClB;QACA;QACA;QACA,IAAI,CAACT,kBAAkB,CAAC6D,YAAY,CAAC,CAAC;QACtC;QACA;QACA;QACA;QACA,IAAI,CAACnD,eAAe,CAAChG,aAAa,CAACoJ,KAAK,CAACd,SAAS,GAAG,IAAI,CAACjC,yBAAyB;QACnFvR,eAAe,CAAC,MAAM;UAClB,IAAI,CAAC6R,yBAAyB,GAAG,KAAK;UACtC,MAAM0C,uBAAuB,GAAG,IAAI,CAACzC,wBAAwB;UAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;UAClC,KAAK,MAAM0C,EAAE,IAAID,uBAAuB,EAAE;YACtCC,EAAE,CAAC,CAAC;UACR;QACJ,CAAC,EAAE;UAAEC,QAAQ,EAAE,IAAI,CAACxC;QAAU,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;IACA;IACArB,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACU,mBAAmB,CAACpI,GAAG,CAAC,IAAI,CAAC4G,WAAW,KAAK,YAAY,GAAG,EAAE,GAAG,GAAG,IAAI,CAACsB,iBAAiB,IAAI,CAAC;MACpG,IAAI,CAACC,kBAAkB,CAACnI,GAAG,CAAC,IAAI,CAAC4G,WAAW,KAAK,YAAY,GAAG,GAAG,IAAI,CAACsB,iBAAiB,IAAI,GAAG,EAAE,CAAC;IACvG;IACA,OAAO9J,IAAI,YAAAoN,iCAAAlN,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+I,wBAAwB;IAAA;IAC3H,OAAOoE,IAAI,kBAl1B8E1V,EAAE,CAAA2V,iBAAA;MAAAjN,IAAA,EAk1BJ4I,wBAAwB;MAAA3I,SAAA;MAAAiN,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAl1BtB9V,EAAE,CAAAgW,WAAA,CAAAtS,GAAA;QAAA;QAAA,IAAAoS,EAAA;UAAA,IAAAG,EAAA;UAAFjW,EAAE,CAAAkW,cAAA,CAAAD,EAAA,GAAFjW,EAAE,CAAAmW,WAAA,QAAAJ,GAAA,CAAA9D,eAAA,GAAAgE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9V,EAAE,CAAAyW,WAAA,8CAAAV,GAAA,CAAAlF,WAAA,KAk1BY,YAAO,CAAC,4CAAAkF,GAAA,CAAAlF,WAAA,KAAR,YAAO,CAAC;QAAA;MAAA;MAAAjI,MAAA;QAAAiI,WAAA;QAAAe,UAAA,kCAA8I5Q,gBAAgB;MAAA;MAAA0V,OAAA;QAAAjS,mBAAA;MAAA;MAAAoE,QAAA,GAl1BpL7I,EAAE,CAAA8I,kBAAA,CAk1B4e,CAC/jB;QACIC,OAAO,EAAE0D,aAAa;QACtBzD,UAAU,EAAEA,CAAC2N,iBAAiB,EAAEvR,QAAQ,KAAKuR,iBAAiB,IAAIvR,QAAQ;QAC1E6D,IAAI,EAAE,CAAC,CAAC,IAAIhI,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAACwP,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;MACrF,CAAC,CACJ,GAx1BoFtR,EAAE,CAAAgR,0BAAA;MAAA4F,kBAAA,EAAAjT,GAAA;MAAAkT,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAnB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9V,EAAE,CAAAkX,eAAA;UAAFlX,EAAE,CAAAmX,cAAA,eAw1BoR,CAAC;UAx1BvRnX,EAAE,CAAAoX,YAAA,EAw1BiT,CAAC;UAx1BpTpX,EAAE,CAAAqX,YAAA,CAw1ByT,CAAC;UAx1B5TrX,EAAE,CAAAsX,SAAA,YAw1BknB,CAAC;QAAA;QAAA,IAAAxB,EAAA;UAx1BrnB9V,EAAE,CAAAuX,SAAA,EAw1BkkB,CAAC;UAx1BrkBvX,EAAE,CAAAwX,WAAA,UAAAzB,GAAA,CAAA3D,kBAAA,EAw1BkkB,CAAC,WAAA2D,GAAA,CAAA1D,mBAAA,EAAwC,CAAC;QAAA;MAAA;MAAAoF,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC3sB;EAAC,OAhYKrG,wBAAwB;AAAA;AAiY9B;EAAA,QAAA5L,SAAA,oBAAAA,SAAA;AAAA;;AAyBA;AACA,SAASkS,SAASA,CAAC/G,WAAW,EAAEgH,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAMvK,EAAE,GAAGuK,IAAI;EACf,IAAI,CAACvK,EAAE,CAAC0C,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAM8H,IAAI,GAAGxK,EAAE,CAAC0C,qBAAqB,CAAC,CAAC;EACvC,IAAIY,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOgH,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACtK,IAAI,GAAGsK,IAAI,CAACrK,KAAK;EACzD;EACA,OAAOmK,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACnK,GAAG,GAAGmK,IAAI,CAACpK,MAAM;AACzD;AACA;AACA;AACA;AACA;AAHA,IAIMqK,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBC,iBAAiB,GAAG5X,MAAM,CAACmB,gBAAgB,CAAC;IAC5C0W,SAAS,GAAG7X,MAAM,CAACoB,WAAW,CAAC;IAC/B0W,QAAQ,GAAG9X,MAAM,CAACqB,eAAe,CAAC;IAClC0W,aAAa,GAAG/X,MAAM,CAAC0D,uBAAuB,CAAC;IAC/CY,SAAS,GAAGtE,MAAM,CAACiR,wBAAwB,EAAE;MAAE+G,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE;IACAC,UAAU,GAAG,IAAI1W,OAAO,CAAC,CAAC;IAC1B;IACA2W,kBAAkB,GAAG,IAAI3W,OAAO,CAAC,CAAC;IAClC;IACA,IAAI4W,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACC,gBAAgB;IAChC;IACA,IAAID,eAAeA,CAACrQ,KAAK,EAAE;MACvB,IAAI,CAACsQ,gBAAgB,GAAGtQ,KAAK;MAC7B,IAAI9D,YAAY,CAAC8D,KAAK,CAAC,EAAE;QACrB,IAAI,CAACoQ,kBAAkB,CAACzQ,IAAI,CAACK,KAAK,CAAC;MACvC,CAAC,MACI;QACD;QACA,IAAI,CAACoQ,kBAAkB,CAACzQ,IAAI,CAAC,IAAI7D,eAAe,CAAC/B,YAAY,CAACiG,KAAK,CAAC,GAAGA,KAAK,GAAGuQ,KAAK,CAACnK,IAAI,CAACpG,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5G;IACJ;IACAsQ,gBAAgB;IAChB;AACJ;AACA;AACA;IACI,IAAIE,oBAAoBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,qBAAqB;IACrC;IACA,IAAID,oBAAoBA,CAACpD,EAAE,EAAE;MACzB,IAAI,CAACsD,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,qBAAqB,GAAGrD,EAAE,GACzB,CAACtP,KAAK,EAAE6S,IAAI,KAAKvD,EAAE,CAACtP,KAAK,IAAI,IAAI,CAACsM,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC9L,KAAK,GAAG,CAAC,CAAC,EAAEqS,IAAI,CAAC,GACxF9N,SAAS;IACnB;IACA4N,qBAAqB;IACrB;IACA,IAAIG,qBAAqBA,CAAC5Q,KAAK,EAAE;MAC7B,IAAIA,KAAK,EAAE;QACP,IAAI,CAAC0Q,YAAY,GAAG,IAAI;QACxB,IAAI,CAACX,SAAS,GAAG/P,KAAK;MAC1B;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAI6Q,8BAA8BA,CAAA,EAAG;MACjC,OAAO,IAAI,CAACZ,aAAa,CAACa,aAAa;IAC3C;IACA,IAAID,8BAA8BA,CAACjF,IAAI,EAAE;MACrC,IAAI,CAACqE,aAAa,CAACa,aAAa,GAAGrW,oBAAoB,CAACmR,IAAI,CAAC;IACjE;IACA;IACAN,UAAU,GAAG,IAAI,CAAC8E,kBAAkB,CAAC7T,IAAI;IACzC;IACApC,SAAS,CAAC,IAAI,CAAC;IACf;IACAE,QAAQ,CAAC,CAAC;IACV;IACA;IACA;IACAC,SAAS,CAAC,CAAC,CAACyW,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;IAC7D;IACAzW,WAAW,CAAC,CAAC,CAAC,CAAC;IACf;IACA2W,OAAO,GAAG,IAAI;IACd;IACAC,KAAK;IACL;IACAC,cAAc;IACd;IACAhH,cAAc;IACd;IACAsG,YAAY,GAAG,KAAK;IACpB7L,UAAU,GAAG,IAAIpL,OAAO,CAAC,CAAC;IAC1BmD,WAAWA,CAAA,EAAG;MACV,MAAM6H,MAAM,GAAGvM,MAAM,CAACC,MAAM,CAAC;MAC7B,IAAI,CAACmT,UAAU,CAACtJ,SAAS,CAACuJ,IAAI,IAAI;QAC9B,IAAI,CAAC4F,KAAK,GAAG5F,IAAI;QACjB,IAAI,CAAC8F,qBAAqB,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,IAAI,CAAC7U,SAAS,CAACuN,mBAAmB,CAACxN,IAAI,CAACnC,SAAS,CAAC,IAAI,CAACyK,UAAU,CAAC,CAAC,CAAC7C,SAAS,CAAC6J,KAAK,IAAI;QACnF,IAAI,CAACzB,cAAc,GAAGyB,KAAK;QAC3B,IAAI,IAAI,CAACsE,UAAU,CAACmB,SAAS,CAAC7F,MAAM,EAAE;UAClChH,MAAM,CAACoF,GAAG,CAAC,MAAM,IAAI,CAACsG,UAAU,CAACxQ,IAAI,CAAC,IAAI,CAACyK,cAAc,CAAC,CAAC;QAC/D;QACA,IAAI,CAACiH,qBAAqB,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,IAAI,CAAC7U,SAAS,CAACQ,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA;AACJ;AACA;AACA;AACA;IACI+P,gBAAgBA,CAAClB,KAAK,EAAEnD,WAAW,EAAE;MACjC,IAAImD,KAAK,CAACvN,KAAK,IAAIuN,KAAK,CAACtN,GAAG,EAAE;QAC1B,OAAO,CAAC;MACZ;MACA,IAAI,CAACsN,KAAK,CAACvN,KAAK,GAAG,IAAI,CAAC8L,cAAc,CAAC9L,KAAK,IAAIuN,KAAK,CAACtN,GAAG,GAAG,IAAI,CAAC6L,cAAc,CAAC7L,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMC,KAAK,CAAC,0DAA0D,CAAC;MAC3E;MACA;MACA,MAAM+T,kBAAkB,GAAG1F,KAAK,CAACvN,KAAK,GAAG,IAAI,CAAC8L,cAAc,CAAC9L,KAAK;MAClE;MACA,MAAMkT,QAAQ,GAAG3F,KAAK,CAACtN,GAAG,GAAGsN,KAAK,CAACvN,KAAK;MACxC;MACA;MACA,IAAImT,SAAS;MACb,IAAIC,QAAQ;MACZ;MACA,KAAK,IAAIzV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuV,QAAQ,EAAEvV,CAAC,EAAE,EAAE;QAC/B,MAAM0V,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC3N,GAAG,CAAClG,CAAC,GAAGsV,kBAAkB,CAAC;QAC/D,IAAII,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACnG,MAAM,EAAE;UAC/BgG,SAAS,GAAGC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;UACxC;QACJ;MACJ;MACA;MACA,KAAK,IAAI3V,CAAC,GAAGuV,QAAQ,GAAG,CAAC,EAAEvV,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpC,MAAM0V,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC3N,GAAG,CAAClG,CAAC,GAAGsV,kBAAkB,CAAC;QAC/D,IAAII,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACnG,MAAM,EAAE;UAC/BiG,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACD,IAAI,CAACC,SAAS,CAACnG,MAAM,GAAG,CAAC,CAAC;UACpD;QACJ;MACJ;MACA,OAAOgG,SAAS,IAAIC,QAAQ,GACtBjC,SAAS,CAAC/G,WAAW,EAAE,KAAK,EAAEgJ,QAAQ,CAAC,GAAGjC,SAAS,CAAC/G,WAAW,EAAE,OAAO,EAAE+I,SAAS,CAAC,GACpF,CAAC;IACX;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACR,YAAY,EAAE;QACnC;QACA;QACA;QACA,MAAMoB,OAAO,GAAG,IAAI,CAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,CAACX,cAAc,CAAC;QACtD,IAAI,CAACU,OAAO,EAAE;UACV,IAAI,CAACE,cAAc,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACC,aAAa,CAACH,OAAO,CAAC;QAC/B;QACA,IAAI,CAACpB,YAAY,GAAG,KAAK;MAC7B;IACJ;IACA5N,WAAWA,CAAA,EAAG;MACV,IAAI,CAACtG,SAAS,CAACY,MAAM,CAAC,CAAC;MACvB,IAAI,CAACgT,kBAAkB,CAACzQ,IAAI,CAACkD,SAAS,CAAC;MACvC,IAAI,CAACuN,kBAAkB,CAAC/S,QAAQ,CAAC,CAAC;MAClC,IAAI,CAAC8S,UAAU,CAAC9S,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAACwH,UAAU,CAAClF,IAAI,CAAC,CAAC;MACtB,IAAI,CAACkF,UAAU,CAACxH,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAAC4S,aAAa,CAAC7S,MAAM,CAAC,CAAC;IAC/B;IACA;IACAiU,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACjH,cAAc,EAAE;QACtB;MACJ;MACA,IAAI,CAACgH,cAAc,GAAG,IAAI,CAACD,KAAK,CAACe,KAAK,CAAC,IAAI,CAAC9H,cAAc,CAAC9L,KAAK,EAAE,IAAI,CAAC8L,cAAc,CAAC7L,GAAG,CAAC;MAC1F,IAAI,CAAC,IAAI,CAAC2S,OAAO,EAAE;QACf;QACA;QACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAAClB,QAAQ,CAACmC,IAAI,CAAC,IAAI,CAACf,cAAc,CAAC,CAACgB,MAAM,CAAC,CAACtU,KAAK,EAAE6S,IAAI,KAAK;UAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC1S,KAAK,EAAE6S,IAAI,CAAC,GAAGA,IAAI;QACpF,CAAC,CAAC;MACN;MACA,IAAI,CAACD,YAAY,GAAG,IAAI;IAC5B;IACA;IACAO,iBAAiBA,CAACoB,KAAK,EAAEC,KAAK,EAAE;MAC5B,IAAID,KAAK,EAAE;QACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAAC7B,YAAY,GAAG,IAAI;MACxB,OAAO4B,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAG9Y,EAAE,CAAC,CAAC;IAC7C;IACA;IACAsY,cAAcA,CAAA,EAAG;MACb,MAAMS,KAAK,GAAG,IAAI,CAACtB,KAAK,CAAC1F,MAAM;MAC/B,IAAIxP,CAAC,GAAG,IAAI,CAAC6T,iBAAiB,CAACrE,MAAM;MACrC,OAAOxP,CAAC,EAAE,EAAE;QACR,MAAM0V,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC3N,GAAG,CAAClG,CAAC,CAAC;QAC1C0V,IAAI,CAACe,OAAO,CAAC5U,KAAK,GAAG,IAAI,CAACsM,cAAc,CAAC9L,KAAK,GAAGrC,CAAC;QAClD0V,IAAI,CAACe,OAAO,CAACD,KAAK,GAAGA,KAAK;QAC1B,IAAI,CAACE,gCAAgC,CAAChB,IAAI,CAACe,OAAO,CAAC;QACnDf,IAAI,CAACiB,aAAa,CAAC,CAAC;MACxB;IACJ;IACA;IACAX,aAAaA,CAACH,OAAO,EAAE;MACnB,IAAI,CAAC7B,aAAa,CAAC4C,YAAY,CAACf,OAAO,EAAE,IAAI,CAAChC,iBAAiB,EAAE,CAACgD,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACnC,IAAI,CAAC;MAC1L;MACAmB,OAAO,CAACoB,qBAAqB,CAAEJ,MAAM,IAAK;QACtC,MAAMnB,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC3N,GAAG,CAAC2Q,MAAM,CAACE,YAAY,CAAC;QAC5DrB,IAAI,CAACe,OAAO,CAACS,SAAS,GAAGL,MAAM,CAACnC,IAAI;MACxC,CAAC,CAAC;MACF;MACA,MAAM8B,KAAK,GAAG,IAAI,CAACtB,KAAK,CAAC1F,MAAM;MAC/B,IAAIxP,CAAC,GAAG,IAAI,CAAC6T,iBAAiB,CAACrE,MAAM;MACrC,OAAOxP,CAAC,EAAE,EAAE;QACR,MAAM0V,IAAI,GAAG,IAAI,CAAC7B,iBAAiB,CAAC3N,GAAG,CAAClG,CAAC,CAAC;QAC1C0V,IAAI,CAACe,OAAO,CAAC5U,KAAK,GAAG,IAAI,CAACsM,cAAc,CAAC9L,KAAK,GAAGrC,CAAC;QAClD0V,IAAI,CAACe,OAAO,CAACD,KAAK,GAAGA,KAAK;QAC1B,IAAI,CAACE,gCAAgC,CAAChB,IAAI,CAACe,OAAO,CAAC;MACvD;IACJ;IACA;IACAC,gCAAgCA,CAACD,OAAO,EAAE;MACtCA,OAAO,CAACzE,KAAK,GAAGyE,OAAO,CAAC5U,KAAK,KAAK,CAAC;MACnC4U,OAAO,CAACU,IAAI,GAAGV,OAAO,CAAC5U,KAAK,KAAK4U,OAAO,CAACD,KAAK,GAAG,CAAC;MAClDC,OAAO,CAACW,IAAI,GAAGX,OAAO,CAAC5U,KAAK,GAAG,CAAC,KAAK,CAAC;MACtC4U,OAAO,CAACY,GAAG,GAAG,CAACZ,OAAO,CAACW,IAAI;IAC/B;IACAJ,oBAAoBA,CAACH,MAAM,EAAEhV,KAAK,EAAE;MAChC;MACA;MACA;MACA;MACA,OAAO;QACHyV,WAAW,EAAE,IAAI,CAACxD,SAAS;QAC3B2C,OAAO,EAAE;UACLS,SAAS,EAAEL,MAAM,CAACnC,IAAI;UACtB;UACA;UACAN,eAAe,EAAE,IAAI,CAACC,gBAAgB;UACtCxS,KAAK,EAAE,CAAC,CAAC;UACT2U,KAAK,EAAE,CAAC,CAAC;UACTxE,KAAK,EAAE,KAAK;UACZmF,IAAI,EAAE,KAAK;UACXE,GAAG,EAAE,KAAK;UACVD,IAAI,EAAE;QACV,CAAC;QACDvV;MACJ,CAAC;IACL;IACA,OAAO0V,sBAAsBA,CAACC,SAAS,EAAEf,OAAO,EAAE;MAC9C,OAAO,IAAI;IACf;IACA,OAAOxS,IAAI,YAAAwT,wBAAAtT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyP,eAAe;IAAA;IAClH,OAAOxP,IAAI,kBAxnC8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EAwnCJsP,eAAe;MAAArP,SAAA;MAAAC,MAAA;QAAA4P,eAAA;QAAAG,oBAAA;QAAAI,qBAAA;QAAAC,8BAAA;MAAA;MAAAnQ,QAAA,GAxnCb7I,EAAE,CAAA8I,kBAAA,CAwnC0S,CAAC;QAAEC,OAAO,EAAEhF,uBAAuB;QAAE+X,QAAQ,EAAE3X;MAA6B,CAAC,CAAC;IAAA;EACvd;EAAC,OAtPK6T,eAAe;AAAA;AAuPrB;EAAA,QAAAtS,SAAA,oBAAAA,SAAA;AAAA;;AAgBA;AACA;AACA;AAFA,IAGMqW,2BAA2B;EAAjC,MAAMA,2BAA2B,SAASpL,oBAAoB,CAAC;IAC3D5L,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;IACX;IACA+O,yCAAyCA,CAACvF,IAAI,EAAE;MAC5C,OAAQ,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC,GACpE,IAAI,CAACxH,mBAAmB,CAACwH,IAAI,CAAC;IACtC;IACA,OAAOlG,IAAI,YAAA2T,oCAAAzT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwT,2BAA2B;IAAA;IAC9H,OAAOvT,IAAI,kBAtpC8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EAspCJqT,2BAA2B;MAAApT,SAAA;MAAA0N,SAAA;MAAAxN,QAAA,GAtpCzB7I,EAAE,CAAA8I,kBAAA,CAspCsJ,CAAC;QAAEC,OAAO,EAAE2H,kBAAkB;QAAEuL,WAAW,EAAEF;MAA4B,CAAC,CAAC,GAtpCnO/b,EAAE,CAAAgR,0BAAA;IAAA;EAupC/F;EAAC,OAVK+K,2BAA2B;AAAA;AAWjC;EAAA,QAAArW,SAAA,oBAAAA,SAAA;AAAA;;AAWA;AACA;AACA;AAFA,IAGMwW,0BAA0B;EAAhC,MAAMA,0BAA0B,SAASvL,oBAAoB,CAAC;IAC1D5L,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAM6K,QAAQ,GAAGvP,MAAM,CAACM,QAAQ,CAAC;MACjC,IAAI,CAAC+L,UAAU,GAAG,IAAIjM,UAAU,CAACmP,QAAQ,CAACG,eAAe,CAAC;MAC1D,IAAI,CAAChD,cAAc,GAAG6C,QAAQ;IAClC;IACAkE,yCAAyCA,CAACvF,IAAI,EAAE;MAC5C,OAAO,IAAI,CAACvC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC1B,IAAI,CAAC;IAC3E;IACA,OAAOlG,IAAI,YAAA8T,mCAAA5T,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2T,0BAA0B;IAAA;IAC7H,OAAO1T,IAAI,kBAjrC8ExI,EAAE,CAAAyI,iBAAA;MAAAC,IAAA,EAirCJwT,0BAA0B;MAAAvT,SAAA;MAAAE,QAAA,GAjrCxB7I,EAAE,CAAA8I,kBAAA,CAirC8G,CAAC;QAAEC,OAAO,EAAE2H,kBAAkB;QAAEuL,WAAW,EAAEC;MAA2B,CAAC,CAAC,GAjrC1Llc,EAAE,CAAAgR,0BAAA;IAAA;EAkrC/F;EAAC,OAZKkL,0BAA0B;AAAA;AAahC;EAAA,QAAAxW,SAAA,oBAAAA,SAAA;AAAA;AAMwC,IAElC0W,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtB,OAAO/T,IAAI,YAAAgU,4BAAA9T,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6T,mBAAmB;IAAA;IACtH,OAAOE,IAAI,kBA7rC8Etc,EAAE,CAAAuc,gBAAA;MAAA7T,IAAA,EA6rCS0T;IAAmB;IACvH,OAAOI,IAAI,kBA9rC8Exc,EAAE,CAAAyc,gBAAA;EA+rC/F;EAAC,OAJKL,mBAAmB;AAAA;AAKzB;EAAA,QAAA1W,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AAFA,IAGMgX,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOrU,IAAI,YAAAsU,wBAAApU,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmU,eAAe;IAAA;IAClH,OAAOJ,IAAI,kBA5sC8Etc,EAAE,CAAAuc,gBAAA;MAAA7T,IAAA,EA4sCSgU;IAAe;IASnH,OAAOF,IAAI,kBArtC8Exc,EAAE,CAAAyc,gBAAA;MAAAG,OAAA,GAqtCoCnZ,UAAU,EACjI2Y,mBAAmB,EAAE3Y,UAAU,EAAE2Y,mBAAmB;IAAA;EAChE;EAAC,OAbKM,eAAe;AAAA;AAcrB;EAAA,QAAAhX,SAAA,oBAAAA,SAAA;AAAA;AAwBA,SAASwC,yBAAyB,EAAEuE,aAAa,EAAE2P,mBAAmB,EAAEpE,eAAe,EAAE1G,wBAAwB,EAAEX,oBAAoB,EAAEoL,2BAA2B,EAAEG,0BAA0B,EAAEvN,mBAAmB,EAAExF,mBAAmB,EAAE5E,8BAA8B,EAAE6E,gBAAgB,EAAEsT,eAAe,EAAEhM,kBAAkB,EAAEpM,uBAAuB,EAAEsK,aAAa,EAAE7G,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}