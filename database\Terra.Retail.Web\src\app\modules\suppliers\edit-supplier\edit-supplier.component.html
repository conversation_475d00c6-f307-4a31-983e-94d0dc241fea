<!-- Terra Retail ERP - Edit Supplier Form -->
<div class="edit-supplier-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">تعديل بيانات المورد</h1>
          <p class="page-subtitle" *ngIf="supplier">{{ supplier.nameAr }} - {{ supplier.supplierCode }}</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-stroked-button class="cancel-btn" (click)="cancel()">
          <mat-icon>close</mat-icon>
          <span>إلغاء</span>
        </button>
        <button mat-raised-button color="primary" class="save-btn" 
                [disabled]="!supplierForm.valid || isLoading"
                (click)="updateSupplier()">
          <mat-icon>save</mat-icon>
          <span>حفظ التعديلات</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Form Content -->
  <div class="form-content" *ngIf="!isLoading">
    <form [formGroup]="supplierForm" class="supplier-form">
      
      <!-- Basic Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>business</mat-icon>
            <span>المعلومات الأساسية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Supplier Code -->
            <mat-form-field appearance="outline">
              <mat-label>كود المورد</mat-label>
              <input matInput formControlName="supplierCode" readonly>
              <mat-icon matSuffix>tag</mat-icon>
            </mat-form-field>

            <!-- Supplier Type -->
            <mat-form-field appearance="outline">
              <mat-label>نوع المورد</mat-label>
              <mat-select formControlName="supplierTypeId">
                <mat-option value="">اختر نوع المورد</mat-option>
                <mat-option *ngFor="let type of supplierTypes" [value]="type.Id">
                  {{ type.NameAr }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>category</mat-icon>
            </mat-form-field>

            <!-- Arabic Name -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>اسم المورد بالعربية *</mat-label>
              <input matInput formControlName="nameAr" placeholder="أدخل اسم المورد بالعربية" required>
              <mat-icon matSuffix>business</mat-icon>
              <mat-error *ngIf="supplierForm.get('nameAr')?.hasError('required')">
                اسم المورد مطلوب
              </mat-error>
            </mat-form-field>

            <!-- English Name -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>اسم المورد بالإنجليزية</mat-label>
              <input matInput formControlName="nameEn" placeholder="Enter supplier name in English">
              <mat-icon matSuffix>business</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Contact Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>contact_phone</mat-icon>
            <span>معلومات الاتصال</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Phone 1 -->
            <mat-form-field appearance="outline">
              <mat-label>الهاتف الأول *</mat-label>
              <input matInput formControlName="phone1" placeholder="+201234567890" required>
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="supplierForm.get('phone1')?.hasError('required')">
                رقم الهاتف مطلوب
              </mat-error>
            </mat-form-field>

            <!-- Phone 2 -->
            <mat-form-field appearance="outline">
              <mat-label>الهاتف الثاني</mat-label>
              <input matInput formControlName="phone2" placeholder="+201234567890">
              <mat-icon matSuffix>phone</mat-icon>
            </mat-form-field>

            <!-- Email -->
            <mat-form-field appearance="outline">
              <mat-label>البريد الإلكتروني</mat-label>
              <input matInput formControlName="email" type="email" placeholder="<EMAIL>">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="supplierForm.get('email')?.hasError('email')">
                البريد الإلكتروني غير صحيح
              </mat-error>
            </mat-form-field>

            <!-- Website -->
            <mat-form-field appearance="outline">
              <mat-label>الموقع الإلكتروني</mat-label>
              <input matInput formControlName="website" placeholder="www.supplier.com">
              <mat-icon matSuffix>language</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Address Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>location_on</mat-icon>
            <span>معلومات العنوان</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Area -->
            <mat-form-field appearance="outline">
              <mat-label>المحافظة</mat-label>
              <mat-select formControlName="areaId">
                <mat-option value="">اختر المحافظة</mat-option>
                <mat-option *ngFor="let area of areas" [value]="area.Id">
                  {{ area.NameAr }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>location_city</mat-icon>
            </mat-form-field>

            <!-- Address -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>العنوان التفصيلي</mat-label>
              <textarea matInput formControlName="address" rows="3" 
                        placeholder="أدخل العنوان التفصيلي للمورد"></textarea>
              <mat-icon matSuffix>home</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Contact Person Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            <span>الشخص المسؤول</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Contact Person Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم الشخص المسؤول</mat-label>
              <input matInput formControlName="contactPersonName" placeholder="أدخل اسم الشخص المسؤول">
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>

            <!-- Contact Person Phone -->
            <mat-form-field appearance="outline">
              <mat-label>هاتف الشخص المسؤول</mat-label>
              <input matInput formControlName="contactPersonPhone" placeholder="+201234567890">
              <mat-icon matSuffix>phone</mat-icon>
            </mat-form-field>

            <!-- Contact Person Email -->
            <mat-form-field appearance="outline">
              <mat-label>بريد الشخص المسؤول</mat-label>
              <input matInput formControlName="contactPersonEmail" type="email" placeholder="<EMAIL>">
              <mat-icon matSuffix>email</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Financial Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_balance</mat-icon>
            <span>المعلومات المالية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Payment Terms -->
            <mat-form-field appearance="outline">
              <mat-label>مدة السداد (بالأيام)</mat-label>
              <input matInput formControlName="paymentTerms" type="number" placeholder="30">
              <mat-icon matSuffix>schedule</mat-icon>
            </mat-form-field>

            <!-- Delivery Days -->
            <mat-form-field appearance="outline">
              <mat-label>مدة التوريد (بالأيام) *</mat-label>
              <input matInput formControlName="deliveryDays" type="number" placeholder="7" required>
              <mat-icon matSuffix>local_shipping</mat-icon>
              <mat-error *ngIf="supplierForm.get('deliveryDays')?.hasError('required')">
                مدة التوريد مطلوبة
              </mat-error>
              <mat-error *ngIf="supplierForm.get('deliveryDays')?.hasError('min')">
                مدة التوريد يجب أن تكون يوم واحد على الأقل
              </mat-error>
            </mat-form-field>

            <!-- Credit Limit -->
            <mat-form-field appearance="outline">
              <mat-label>الحد الائتماني</mat-label>
              <input matInput formControlName="creditLimit" type="number" placeholder="0.00">
              <mat-icon matSuffix>credit_card</mat-icon>
            </mat-form-field>

            <!-- Tax Number -->
            <mat-form-field appearance="outline">
              <mat-label>الرقم الضريبي</mat-label>
              <input matInput formControlName="taxNumber" placeholder="أدخل الرقم الضريبي">
              <mat-icon matSuffix>receipt</mat-icon>
            </mat-form-field>

            <!-- Commercial Register -->
            <mat-form-field appearance="outline">
              <mat-label>السجل التجاري</mat-label>
              <input matInput formControlName="commercialRegister" placeholder="أدخل رقم السجل التجاري">
              <mat-icon matSuffix>business_center</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Bank Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_balance</mat-icon>
            <span>المعلومات البنكية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Bank Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم البنك</mat-label>
              <input matInput formControlName="bankName" placeholder="أدخل اسم البنك">
              <mat-icon matSuffix>account_balance</mat-icon>
            </mat-form-field>

            <!-- Bank Account Number -->
            <mat-form-field appearance="outline">
              <mat-label>رقم الحساب البنكي</mat-label>
              <input matInput formControlName="bankAccountNumber" placeholder="أدخل رقم الحساب">
              <mat-icon matSuffix>credit_card</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Additional Information Card -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>info</mat-icon>
            <span>معلومات إضافية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">
            
            <!-- Rating -->
            <mat-form-field appearance="outline">
              <mat-label>التقييم</mat-label>
              <mat-select formControlName="rating">
                <mat-option value="">بدون تقييم</mat-option>
                <mat-option value="1">⭐ (1/5)</mat-option>
                <mat-option value="2">⭐⭐ (2/5)</mat-option>
                <mat-option value="3">⭐⭐⭐ (3/5)</mat-option>
                <mat-option value="4">⭐⭐⭐⭐ (4/5)</mat-option>
                <mat-option value="5">⭐⭐⭐⭐⭐ (5/5)</mat-option>
              </mat-select>
              <mat-icon matSuffix>star</mat-icon>
            </mat-form-field>

            <!-- Is Active -->
            <div class="checkbox-field">
              <mat-checkbox formControlName="isActive">
                مورد نشط
              </mat-checkbox>
            </div>

            <!-- Notes -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>ملاحظات</mat-label>
              <textarea matInput formControlName="notes" rows="4" 
                        placeholder="أدخل أي ملاحظات إضافية عن المورد"></textarea>
              <mat-icon matSuffix>note</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

    </form>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل بيانات المورد...</p>
  </div>

</div>
