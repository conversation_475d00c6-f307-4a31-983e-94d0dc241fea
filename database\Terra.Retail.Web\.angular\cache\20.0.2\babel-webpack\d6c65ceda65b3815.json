{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n  const buffer = [];\n  let active = 0;\n  let index = 0;\n  let isComplete = false;\n  const checkComplete = () => {\n    if (isComplete && !buffer.length && !active) {\n      subscriber.complete();\n    }\n  };\n  const outerNext = value => active < concurrent ? doInnerSub(value) : buffer.push(value);\n  const doInnerSub = value => {\n    expand && subscriber.next(value);\n    active++;\n    let innerComplete = false;\n    innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, innerValue => {\n      onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n      if (expand) {\n        outerNext(innerValue);\n      } else {\n        subscriber.next(innerValue);\n      }\n    }, () => {\n      innerComplete = true;\n    }, undefined, () => {\n      if (innerComplete) {\n        try {\n          active--;\n          while (buffer.length && active < concurrent) {\n            const bufferedValue = buffer.shift();\n            if (innerSubScheduler) {\n              executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n            } else {\n              doInnerSub(bufferedValue);\n            }\n          }\n          checkComplete();\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }\n    }));\n  };\n  source.subscribe(createOperatorSubscriber(subscriber, outerNext, () => {\n    isComplete = true;\n    checkComplete();\n  }));\n  return () => {\n    additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n  };\n}", "map": {"version": 3, "names": ["innerFrom", "executeSchedule", "createOperatorSubscriber", "mergeInternals", "source", "subscriber", "project", "concurrent", "onBeforeNext", "expand", "innerSubScheduler", "additionalFinalizer", "buffer", "active", "index", "isComplete", "checkComplete", "length", "complete", "outerNext", "value", "doInnerSub", "push", "next", "innerComplete", "subscribe", "innerValue", "undefined", "bufferedValue", "shift", "err", "error"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/rxjs/dist/esm/internal/operators/mergeInternals.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n    const buffer = [];\n    let active = 0;\n    let index = 0;\n    let isComplete = false;\n    const checkComplete = () => {\n        if (isComplete && !buffer.length && !active) {\n            subscriber.complete();\n        }\n    };\n    const outerNext = (value) => (active < concurrent ? doInnerSub(value) : buffer.push(value));\n    const doInnerSub = (value) => {\n        expand && subscriber.next(value);\n        active++;\n        let innerComplete = false;\n        innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, (innerValue) => {\n            onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n            if (expand) {\n                outerNext(innerValue);\n            }\n            else {\n                subscriber.next(innerValue);\n            }\n        }, () => {\n            innerComplete = true;\n        }, undefined, () => {\n            if (innerComplete) {\n                try {\n                    active--;\n                    while (buffer.length && active < concurrent) {\n                        const bufferedValue = buffer.shift();\n                        if (innerSubScheduler) {\n                            executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n                        }\n                        else {\n                            doInnerSub(bufferedValue);\n                        }\n                    }\n                    checkComplete();\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }\n        }));\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, outerNext, () => {\n        isComplete = true;\n        checkComplete();\n    }));\n    return () => {\n        additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;EAClI,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,UAAU,GAAG,KAAK;EACtB,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAID,UAAU,IAAI,CAACH,MAAM,CAACK,MAAM,IAAI,CAACJ,MAAM,EAAE;MACzCR,UAAU,CAACa,QAAQ,CAAC,CAAC;IACzB;EACJ,CAAC;EACD,MAAMC,SAAS,GAAIC,KAAK,IAAMP,MAAM,GAAGN,UAAU,GAAGc,UAAU,CAACD,KAAK,CAAC,GAAGR,MAAM,CAACU,IAAI,CAACF,KAAK,CAAE;EAC3F,MAAMC,UAAU,GAAID,KAAK,IAAK;IAC1BX,MAAM,IAAIJ,UAAU,CAACkB,IAAI,CAACH,KAAK,CAAC;IAChCP,MAAM,EAAE;IACR,IAAIW,aAAa,GAAG,KAAK;IACzBxB,SAAS,CAACM,OAAO,CAACc,KAAK,EAAEN,KAAK,EAAE,CAAC,CAAC,CAACW,SAAS,CAACvB,wBAAwB,CAACG,UAAU,EAAGqB,UAAU,IAAK;MAC9FlB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACkB,UAAU,CAAC;MACpF,IAAIjB,MAAM,EAAE;QACRU,SAAS,CAACO,UAAU,CAAC;MACzB,CAAC,MACI;QACDrB,UAAU,CAACkB,IAAI,CAACG,UAAU,CAAC;MAC/B;IACJ,CAAC,EAAE,MAAM;MACLF,aAAa,GAAG,IAAI;IACxB,CAAC,EAAEG,SAAS,EAAE,MAAM;MAChB,IAAIH,aAAa,EAAE;QACf,IAAI;UACAX,MAAM,EAAE;UACR,OAAOD,MAAM,CAACK,MAAM,IAAIJ,MAAM,GAAGN,UAAU,EAAE;YACzC,MAAMqB,aAAa,GAAGhB,MAAM,CAACiB,KAAK,CAAC,CAAC;YACpC,IAAInB,iBAAiB,EAAE;cACnBT,eAAe,CAACI,UAAU,EAAEK,iBAAiB,EAAE,MAAMW,UAAU,CAACO,aAAa,CAAC,CAAC;YACnF,CAAC,MACI;cACDP,UAAU,CAACO,aAAa,CAAC;YAC7B;UACJ;UACAZ,aAAa,CAAC,CAAC;QACnB,CAAC,CACD,OAAOc,GAAG,EAAE;UACRzB,UAAU,CAAC0B,KAAK,CAACD,GAAG,CAAC;QACzB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP,CAAC;EACD1B,MAAM,CAACqB,SAAS,CAACvB,wBAAwB,CAACG,UAAU,EAAEc,SAAS,EAAE,MAAM;IACnEJ,UAAU,GAAG,IAAI;IACjBC,aAAa,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC;EACH,OAAO,MAAM;IACTL,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC,CAAC;EACnG,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}