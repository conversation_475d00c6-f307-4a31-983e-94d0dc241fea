/* Terra Retail ERP - Simple Clean Layout */

.customers-page {
  padding: 1rem;
  direction: rtl;
  background: #f8fafc;
  min-height: 100vh;
  font-family: '<PERSON><PERSON><PERSON>', 'IBM Plex Sans Arabic', sans-serif;
}

/* إعادة تعيين كامل لجميع Material Components */
::ng-deep {
  /* إصلاح Form Fields بشكل جذري */
  .mat-mdc-form-field {
    width: 100% !important;
    display: block !important;
    margin-bottom: 0 !important;
    
    .mat-mdc-form-field-wrapper {
      width: 100% !important;
      padding-bottom: 0 !important;
      margin-bottom: 0 !important;
    }
    
    .mat-mdc-form-field-flex {
      width: 100% !important;
      height: 56px !important;
      align-items: center !important;
    }
    
    .mat-mdc-form-field-infix {
      width: 100% !important;
      padding: 16px !important;
      border: none !important;
      min-height: auto !important;
    }
    
    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
    }
    
    .mat-mdc-text-field-wrapper {
      width: 100% !important;
      height: 56px !important;
      border: 2px solid #e2e8f0 !important;
      border-radius: 8px !important;
      background: white !important;
      
      &:hover {
        border-color: #cbd5e1 !important;
      }
      
      &.mdc-text-field--focused {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
      }
    }
    
    .mat-mdc-select-trigger {
      width: 100% !important;
      height: 56px !important;
      display: flex !important;
      align-items: center !important;
      padding: 0 16px !important;
    }
    
    .mat-mdc-select-value {
      width: 100% !important;
      line-height: 1.5 !important;
    }
    
    .mat-mdc-select-arrow-wrapper {
      transform: none !important;
    }
    
    input {
      width: 100% !important;
      height: auto !important;
      padding: 0 !important;
      border: none !important;
      outline: none !important;
      background: transparent !important;
      font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif !important;
      font-size: 16px !important;
    }
  }
  
  /* إصلاح الأزرار */
  .mat-mdc-raised-button,
  .mat-mdc-outlined-button {
    height: 56px !important;
    padding: 0 24px !important;
    border-radius: 8px !important;
    font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    
    .mat-icon {
      margin-left: 8px !important;
      margin-right: 0 !important;
    }
  }
  
  /* إصلاح Cards */
  .mat-mdc-card {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    
    .mat-mdc-card-content {
      padding: 24px !important;
    }
  }
}

/* رأس الصفحة */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-info h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.header-info p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* الإحصائيات */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-content {
  padding: 1.5rem;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* البحث والفلاتر */
.search-filters-wrapper {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section,
.filters-section {
  width: 100%;
}

.search-card,
.filters-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 24px;
}

.search-input {
  width: 100%;
  max-width: 600px;
}

.filters-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;
  padding: 24px;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.clear-filters-btn {
  background: #3b82f6 !important;
  color: white !important;
  
  &:hover {
    background: #2563eb !important;
  }
}

/* الجدول */
.customers-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .customers-page {
    padding: 0.5rem;
  }
  
  .page-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-item {
    min-width: auto;
    width: 100%;
  }
  
  .clear-filters-btn {
    width: 100% !important;
  }
}
