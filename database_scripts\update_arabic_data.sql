-- Terra Retail ERP - Update Arabic Data with Correct Encoding
-- تحديث البيانات العربية بـ encoding صحيح

USE [TerraRetailERP]
GO

-- تحديث أنواع الموردين
UPDATE SupplierTypes SET NameAr = N'مورد محلي' WHERE Id = 1
UPDATE SupplierTypes SET NameAr = N'مورد دولي' WHERE Id = 2  
UPDATE SupplierTypes SET NameAr = N'مورد حكومي' WHERE Id = 3

-- إضافة أنواع موردين جديدة
IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 4)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد مواد خام', 'Raw Materials Supplier', N'مورد المواد الخام والمكونات', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 5)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد تجزئة', 'Retail Supplier', N'مورد منتجات جاهزة للبيع', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 6)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد جملة', 'Wholesale Supplier', N'مورد بكميات كبيرة', 1, GETDATE(), GETDATE())

-- تحديث المحافظات المصرية
UPDATE Areas SET NameAr = N'القاهرة' WHERE Id = 1
UPDATE Areas SET NameAr = N'الجيزة' WHERE Id = 2
UPDATE Areas SET NameAr = N'الإسكندرية' WHERE Id = 3
UPDATE Areas SET NameAr = N'الدقهلية' WHERE Id = 4
UPDATE Areas SET NameAr = N'الشرقية' WHERE Id = 5
UPDATE Areas SET NameAr = N'القليوبية' WHERE Id = 6
UPDATE Areas SET NameAr = N'كفر الشيخ' WHERE Id = 7
UPDATE Areas SET NameAr = N'الغربية' WHERE Id = 8
UPDATE Areas SET NameAr = N'المنوفية' WHERE Id = 9
UPDATE Areas SET NameAr = N'البحيرة' WHERE Id = 10
UPDATE Areas SET NameAr = N'الإسماعيلية' WHERE Id = 11
UPDATE Areas SET NameAr = N'بورسعيد' WHERE Id = 12
UPDATE Areas SET NameAr = N'السويس' WHERE Id = 13
UPDATE Areas SET NameAr = N'شمال سيناء' WHERE Id = 14
UPDATE Areas SET NameAr = N'جنوب سيناء' WHERE Id = 15
UPDATE Areas SET NameAr = N'الفيوم' WHERE Id = 16
UPDATE Areas SET NameAr = N'بني سويف' WHERE Id = 17
UPDATE Areas SET NameAr = N'المنيا' WHERE Id = 18
UPDATE Areas SET NameAr = N'أسيوط' WHERE Id = 19
UPDATE Areas SET NameAr = N'سوهاج' WHERE Id = 20
UPDATE Areas SET NameAr = N'قنا' WHERE Id = 21
UPDATE Areas SET NameAr = N'الأقصر' WHERE Id = 22
UPDATE Areas SET NameAr = N'أسوان' WHERE Id = 23
UPDATE Areas SET NameAr = N'البحر الأحمر' WHERE Id = 24
UPDATE Areas SET NameAr = N'الوادي الجديد' WHERE Id = 25
UPDATE Areas SET NameAr = N'مطروح' WHERE Id = 26

-- إضافة محافظة دمياط إذا لم تكن موجودة
IF NOT EXISTS (SELECT 1 FROM Areas WHERE Id = 27)
    INSERT INTO Areas (NameAr, NameEn, Code, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'دمياط', 'Damietta', 'DMT', 1, GETDATE(), GETDATE())

-- التحقق من البيانات المحدثة
PRINT N'=== أنواع الموردين ==='
SELECT Id, NameAr, NameEn FROM SupplierTypes WHERE IsActive = 1 ORDER BY Id

PRINT N'=== المحافظات ==='
SELECT Id, NameAr, NameEn, Code FROM Areas WHERE IsActive = 1 ORDER BY Id

PRINT N'تم تحديث البيانات العربية بنجاح!'
