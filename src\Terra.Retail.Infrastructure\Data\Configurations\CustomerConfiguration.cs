using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
    {
        public void Configure(EntityTypeBuilder<Customer> builder)
        {
            builder.ToTable("Customers");

            // Primary Key
            builder.HasKey(c => c.Id);

            // Properties
            builder.Property(c => c.CustomerCode)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(c => c.NameAr)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.NameEn)
                .HasMaxLength(100);

            builder.Property(c => c.Phone1)
                .HasMaxLength(20);

            builder.Property(c => c.Phone2)
                .HasMaxLength(20);

            builder.Property(c => c.Email)
                .HasMaxLength(100);

            builder.Property(c => c.Address)
                .HasMaxLength(500);

            builder.Property(c => c.DiscountPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(c => c.OpeningBalance)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(c => c.CurrentBalance)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(c => c.CreditLimit)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(c => c.IdentityNumber)
                .HasMaxLength(50);

            builder.Property(c => c.TaxNumber)
                .HasMaxLength(50);

            builder.Property(c => c.IsActive)
                .HasDefaultValue(true);

            // Relationships
            builder.HasOne(c => c.CustomerType)
                .WithMany(ct => ct.Customers)
                .HasForeignKey(c => c.CustomerTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(c => c.Area)
                .WithMany(a => a.Customers)
                .HasForeignKey(c => c.AreaId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(c => c.Branch)
                .WithMany(b => b.Customers)
                .HasForeignKey(c => c.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(c => c.PriceCategory)
                .WithMany(pc => pc.Customers)
                .HasForeignKey(c => c.PriceCategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(c => c.CustomerCode)
                .IsUnique()
                .HasDatabaseName("IX_Customers_CustomerCode");

            builder.HasIndex(c => new { c.NameAr, c.BranchId })
                .HasDatabaseName("IX_Customers_NameAr_BranchId");

            builder.HasIndex(c => c.Phone1)
                .HasDatabaseName("IX_Customers_Phone1");

            builder.HasIndex(c => c.Email)
                .HasDatabaseName("IX_Customers_Email");

            builder.HasIndex(c => c.IdentityNumber)
                .HasDatabaseName("IX_Customers_IdentityNumber");
        }
    }

    public class CustomerTypeConfiguration : IEntityTypeConfiguration<CustomerType>
    {
        public void Configure(EntityTypeBuilder<CustomerType> builder)
        {
            builder.ToTable("CustomerTypes");

            builder.HasKey(ct => ct.Id);

            builder.Property(ct => ct.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(ct => ct.NameEn)
                .HasMaxLength(50);

            builder.Property(ct => ct.Description)
                .HasMaxLength(200);

            builder.Property(ct => ct.DefaultDiscountPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(ct => ct.DefaultCreditLimit)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(ct => ct.IsActive)
                .HasDefaultValue(true);

            builder.Property(ct => ct.Color)
                .HasMaxLength(7);

            builder.HasIndex(ct => ct.NameAr)
                .HasDatabaseName("IX_CustomerTypes_NameAr");
        }
    }

    public class CustomerTransactionConfiguration : IEntityTypeConfiguration<CustomerTransaction>
    {
        public void Configure(EntityTypeBuilder<CustomerTransaction> builder)
        {
            builder.ToTable("CustomerTransactions");

            builder.HasKey(ct => ct.Id);

            builder.Property(ct => ct.TransactionNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(ct => ct.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.BalanceBefore)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.BalanceAfter)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.Description)
                .HasMaxLength(500);

            builder.Property(ct => ct.Reference)
                .HasMaxLength(50);

            // Relationships
            builder.HasOne(ct => ct.Customer)
                .WithMany(c => c.Transactions)
                .HasForeignKey(ct => ct.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ct => ct.PaymentMethod)
                .WithMany(pm => pm.CustomerTransactions)
                .HasForeignKey(ct => ct.PaymentMethodId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(ct => ct.Branch)
                .WithMany()
                .HasForeignKey(ct => ct.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ct => ct.User)
                .WithMany(u => u.CustomerTransactions)
                .HasForeignKey(ct => ct.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ct => ct.ConfirmedBy)
                .WithMany()
                .HasForeignKey(ct => ct.ConfirmedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ct => ct.TransactionNumber)
                .IsUnique()
                .HasDatabaseName("IX_CustomerTransactions_TransactionNumber");

            builder.HasIndex(ct => new { ct.CustomerId, ct.TransactionDate })
                .HasDatabaseName("IX_CustomerTransactions_CustomerId_TransactionDate");

            builder.HasIndex(ct => ct.TransactionDate)
                .HasDatabaseName("IX_CustomerTransactions_TransactionDate");
        }
    }
}
