using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الموظفين
    /// </summary>
    public class Employee : BaseEntity
    {
        /// <summary>
        /// كود الموظف (فريد)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string EmployeeCode { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FullNameAr { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? FullNameEn { get; set; }

        /// <summary>
        /// رقم الهوية/الإقامة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string IdentityNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// الجنسية
        /// </summary>
        [MaxLength(50)]
        public string? Nationality { get; set; }

        /// <summary>
        /// الجنس
        /// </summary>
        public Gender? Gender { get; set; }

        /// <summary>
        /// الحالة الاجتماعية
        /// </summary>
        public MaritalStatus? MaritalStatus { get; set; }

        /// <summary>
        /// العنوان التفصيلي
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// رقم الهاتف الرئيسي
        /// </summary>
        [MaxLength(20)]
        public string? Phone1 { get; set; }

        /// <summary>
        /// رقم الهاتف الإضافي
        /// </summary>
        [MaxLength(20)]
        public string? Phone2 { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// صورة الموظف
        /// </summary>
        [MaxLength(500)]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// المنصب الوظيفي
        /// </summary>
        public int PositionId { get; set; }

        /// <summary>
        /// القسم
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// المنطقة
        /// </summary>
        public int? AreaId { get; set; }

        /// <summary>
        /// تاريخ التعيين
        /// </summary>
        public DateTime HireDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// نوع العقد
        /// </summary>
        public ContractType ContractType { get; set; } = ContractType.Permanent;

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        public decimal BasicSalary { get; set; } = 0;

        /// <summary>
        /// بدل السكن
        /// </summary>
        public decimal HousingAllowance { get; set; } = 0;

        /// <summary>
        /// بدل المواصلات
        /// </summary>
        public decimal TransportationAllowance { get; set; } = 0;

        /// <summary>
        /// بدلات أخرى
        /// </summary>
        public decimal OtherAllowances { get; set; } = 0;

        /// <summary>
        /// المدير المباشر
        /// </summary>
        public int? ManagerId { get; set; }

        /// <summary>
        /// حالة الموظف
        /// </summary>
        public EmployeeStatus Status { get; set; } = EmployeeStatus.Active;

        /// <summary>
        /// تاريخ انتهاء الخدمة
        /// </summary>
        public DateTime? TerminationDate { get; set; }

        /// <summary>
        /// سبب انتهاء الخدمة
        /// </summary>
        [MaxLength(500)]
        public string? TerminationReason { get; set; }

        /// <summary>
        /// رقم الحساب البنكي
        /// </summary>
        [MaxLength(50)]
        public string? BankAccountNumber { get; set; }

        /// <summary>
        /// اسم البنك
        /// </summary>
        [MaxLength(100)]
        public string? BankName { get; set; }

        /// <summary>
        /// رقم IBAN
        /// </summary>
        [MaxLength(50)]
        public string? IBAN { get; set; }

        /// <summary>
        /// رصيد الإجازات السنوية
        /// </summary>
        public decimal AnnualLeaveBalance { get; set; } = 0;

        /// <summary>
        /// رصيد الإجازات المرضية
        /// </summary>
        public decimal SickLeaveBalance { get; set; } = 0;

        /// <summary>
        /// هل يستخدم البصمة
        /// </summary>
        public bool UsesBiometric { get; set; } = false;

        /// <summary>
        /// بيانات البصمة (مشفرة)
        /// </summary>
        [MaxLength(1000)]
        public string? BiometricData { get; set; }

        /// <summary>
        /// الموقع الجغرافي للموظف
        /// </summary>
        [MaxLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(1000)]
        public string? AdditionalNotes { get; set; }

        // Navigation Properties
        // public virtual Position Position { get; set; } = null!;
        // public virtual Department Department { get; set; } = null!;
        // public virtual Branch Branch { get; set; } = null!;
        // public virtual Area? Area { get; set; }
        // public virtual Employee? Manager { get; set; }
        // public virtual ICollection<Employee> Subordinates { get; set; } = new List<Employee>();
        // public virtual User? User { get; set; }
        // public virtual ICollection<EmployeeDocument> Documents { get; set; } = new List<EmployeeDocument>();
        // public virtual ICollection<Attendance> AttendanceRecords { get; set; } = new List<Attendance>();
        // public virtual ICollection<EmployeeLeave> Leaves { get; set; } = new List<EmployeeLeave>();
        // public virtual ICollection<PayrollItem> PayrollItems { get; set; } = new List<PayrollItem>();
    }

    /// <summary>
    /// الجنس
    /// </summary>
    public enum Gender
    {
        Male = 1,
        Female = 2
    }

    /// <summary>
    /// الحالة الاجتماعية
    /// </summary>
    public enum MaritalStatus
    {
        Single = 1,
        Married = 2,
        Divorced = 3,
        Widowed = 4
    }

    /// <summary>
    /// نوع العقد
    /// </summary>
    public enum ContractType
    {
        Permanent = 1,
        Temporary = 2,
        PartTime = 3,
        Contract = 4,
        Internship = 5
    }

    /// <summary>
    /// حالة الموظف
    /// </summary>
    public enum EmployeeStatus
    {
        Active = 1,
        OnLeave = 2,
        Suspended = 3,
        Terminated = 4,
        Resigned = 5
    }
}
