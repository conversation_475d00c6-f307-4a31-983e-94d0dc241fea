{"ast": null, "code": "/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Je<PERSON>.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)\n  return (\n    // @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ ||\n    // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine ||\n    // @ts-ignore\n    typeof jest !== 'undefined' && !!jest ||\n    // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\nexport { _isTestEnvironment as _ };", "map": {"version": 3, "names": ["_isTestEnvironment", "__karma__", "jasmine", "jest", "<PERSON><PERSON>", "_"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/test-environment-CT0XxPyp.mjs"], "sourcesContent": ["/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n    // We can't use `declare const` because it causes conflicts inside Google with the real typings\n    // for these symbols and we can't read them off the global object, because they don't appear to\n    // be attached there for some runners like Je<PERSON>.\n    // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)\n    return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n        // @ts-ignore\n        (typeof jasmine !== 'undefined' && !!jasmine) ||\n        // @ts-ignore\n        (typeof jest !== 'undefined' && !!jest) ||\n        // @ts-ignore\n        (typeof Mocha !== 'undefined' && !!Mocha));\n}\n\nexport { _isTestEnvironment as _ };\n"], "mappings": "AAAA;AACA,SAASA,kBAAkBA,CAAA,EAAG;EAC1B;EACA;EACA;EACA;EACA;IACA;IACC,OAAOC,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;IAC5C;IACC,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAQ;IAC7C;IACC,OAAOC,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAK;IACvC;IACC,OAAOC,KAAK,KAAK,WAAW,IAAI,CAAC,CAACA;EAAM;AACjD;AAEA,SAASJ,kBAAkB,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}