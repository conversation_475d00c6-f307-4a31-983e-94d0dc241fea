{"format": 1, "restore": {"X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Core\\Terra.Retail.Core.csproj": {}}, "projects": {"X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Core\\Terra.Retail.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Core\\Terra.Retail.Core.csproj", "projectName": "Terra.Retail.Core", "projectPath": "X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Core\\Terra.Retail.Core.csproj", "packagesPath": "X:\\NugetCache", "outputPath": "X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 22.2\\Components\\Offline Packages", "x:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 22.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}