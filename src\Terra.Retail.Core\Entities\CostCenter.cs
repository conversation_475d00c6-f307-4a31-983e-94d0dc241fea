using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مراكز التكلفة
    /// </summary>
    public class CostCenter : BaseEntity
    {
        /// <summary>
        /// كود مركز التكلفة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// اسم مركز التكلفة بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم مركز التكلفة بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? NameEn { get; set; }

        /// <summary>
        /// وصف مركز التكلفة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// مركز التكلفة الأب
        /// </summary>
        public int? ParentCostCenterId { get; set; }

        /// <summary>
        /// مستوى مركز التكلفة
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// المسؤول عن مركز التكلفة
        /// </summary>
        public int? ManagerId { get; set; }

        /// <summary>
        /// الميزانية المخصصة
        /// </summary>
        public decimal? AllocatedBudget { get; set; }

        /// <summary>
        /// الميزانية المستخدمة
        /// </summary>
        public decimal UsedBudget { get; set; } = 0;

        /// <summary>
        /// الميزانية المتبقية
        /// </summary>
        public decimal RemainingBudget { get; set; } = 0;

        /// <summary>
        /// هل مركز التكلفة نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ بداية الفترة المالية
        /// </summary>
        public DateTime? PeriodStartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الفترة المالية
        /// </summary>
        public DateTime? PeriodEndDate { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        // Navigation Properties
        public virtual CostCenter? ParentCostCenter { get; set; }
        public virtual ICollection<CostCenter> SubCostCenters { get; set; } = new List<CostCenter>();
        public virtual Employee? Manager { get; set; }
        public virtual ICollection<Account> Accounts { get; set; } = new List<Account>();
    }
}
