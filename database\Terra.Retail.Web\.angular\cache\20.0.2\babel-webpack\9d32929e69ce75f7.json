{"ast": null, "code": "import { LayoutNewComponent } from './layout/layout-new.component';\nimport { CustomersModule } from './modules/customers/customers.module';\nimport * as i0 from \"@angular/core\";\nexport let App = /*#__PURE__*/(() => {\n  class App {\n    title = 'Terra Retail ERP';\n    static ɵfac = function App_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || App)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: App,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function App_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-layout-new\");\n        }\n      },\n      dependencies: [LayoutNewComponent, CustomersModule],\n      styles: [\"\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n  direction: rtl;\\n  text-align: right;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n}\\n\\n.app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.app-header[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 2rem;\\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.logo-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.logo-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 1rem;\\n}\\n\\n.status-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 0.5rem;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  background: #27ae60;\\n  color: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.api-status[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 3rem;\\n  color: white;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 1rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  opacity: 0.9;\\n}\\n\\n\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 3rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  opacity: 0.8;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.125rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  color: #3498db;\\n  font-size: 2rem;\\n  font-weight: bold;\\n  margin: 0;\\n}\\n\\n\\n\\n.actions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n\\n.actions-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  text-align: center;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border: none;\\n  border-radius: 15px;\\n  padding: 2rem 1rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n  background: white;\\n}\\n\\n.action-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n}\\n\\n.action-text[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n}\\n\\n\\n\\n.system-info[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.system-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  text-align: center;\\n}\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 10px;\\n  color: #2c3e50;\\n}\\n\\n\\n\\n.app-footer[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  text-align: center;\\n  padding: 1.5rem;\\n  margin-top: auto;\\n}\\n\\n.app-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .welcome-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n    gap: 1rem;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 1.5rem 0.75rem;\\n  }\\n  .action-icon[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .action-text[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return App;\n})();", "map": {"version": 3, "names": ["LayoutNewComponent", "CustomersModule", "App", "title", "selectors", "decls", "vars", "template", "App_Template", "rf", "ctx", "i0", "ɵɵelement", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\app.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\app.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { LayoutNewComponent } from './layout/layout-new.component';\r\nimport { CustomersModule } from './modules/customers/customers.module';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [LayoutNewComponent, CustomersModule],\r\n  templateUrl: './app.html',\r\n  styleUrls: ['./app.scss']\r\n})\r\nexport class App {\r\n  title = 'Terra Retail ERP';\r\n}\r\n", "<!-- Terra Retail ERP - Professional Layout -->\n<app-layout-new></app-layout-new>\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,eAAe,QAAQ,sCAAsC;;AAStE,WAAaC,GAAG;EAAV,MAAOA,GAAG;IACdC,KAAK,GAAG,kBAAkB;;uCADfD,GAAG;IAAA;;YAAHA,GAAG;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,aAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhBE,EAAA,CAAAC,SAAA,qBAAiC;;;qBDMrBZ,kBAAkB,EAAEC,eAAe;MAAAY,MAAA;IAAA;;SAIlCX,GAAG;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}