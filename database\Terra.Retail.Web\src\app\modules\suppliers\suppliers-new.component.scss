/* Terra Retail ERP - Professional Suppliers Module Styles */

.suppliers-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--secondary-600) 0%, var(--primary-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 var(--spacing-sm) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;

    .add-btn {
      background: var(--success-500) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-xl) !important;
      font-weight: 600 !important;
      box-shadow: var(--shadow-lg) !important;

      &:hover {
        background: var(--success-600) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
      }
    }

    .export-btn, .import-btn {
      border-color: rgba(255, 255, 255, 0.5) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-lg) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: white !important;
      }
    }
  }
}

/* ===== FILTERS SECTION ===== */
.filters-section {
  margin-bottom: var(--spacing-2xl);
}

.filters-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;

  .mat-mdc-card-content {
    padding: var(--spacing-2xl) !important;
  }
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: var(--spacing-xl);
  align-items: end;

  .search-field {
    .mat-mdc-text-field-wrapper {
      background: var(--gray-50) !important;
    }
  }

  .clear-filters-btn {
    height: 56px;
    padding: 0 var(--spacing-lg) !important;
    color: var(--gray-600) !important;
    border-color: var(--gray-300) !important;

    &:hover {
      background: var(--gray-50) !important;
      color: var(--gray-800) !important;
    }
  }
}

/* ===== STATISTICS SECTION ===== */
.stats-section {
  margin-bottom: var(--spacing-2xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.stat-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  min-height: 100px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: white;
    }
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: 2rem;
      font-weight: 800;
      color: var(--gray-900);
      line-height: 1;
      margin-bottom: var(--spacing-xs);
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--gray-600);
      font-weight: 500;
    }
  }

  &.total-suppliers .stat-icon {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  }

  &.active-suppliers .stat-icon {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
  }

  &.local-suppliers .stat-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  }

  &.international-suppliers .stat-icon {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  }
}

/* ===== TABLE SECTION ===== */
.table-section {
  margin-bottom: var(--spacing-2xl);
}

.table-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  overflow: hidden !important;

  .mat-mdc-card-content {
    padding: 0 !important;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);

  .table-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--gray-900);
    }

    .results-count {
      font-size: 0.875rem;
      color: var(--gray-500);
      background: var(--gray-200);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-md);
    }
  }

  .table-actions {
    .page-size-field {
      width: 120px;
    }
  }
}

.table-container {
  overflow-x: auto;
  max-height: 600px;
}

.suppliers-table {
  width: 100% !important;
  background: white !important;

  .mat-mdc-header-row {
    background: var(--gray-50) !important;

    .mat-mdc-header-cell {
      font-weight: 700 !important;
      color: var(--gray-800) !important;
      border-bottom: 2px solid var(--gray-200) !important;
      padding: var(--spacing-lg) !important;
      font-family: var(--font-family-primary) !important;
      font-size: 0.875rem !important;
    }
  }

  .mat-mdc-row {
    transition: all var(--transition-fast) !important;
    cursor: pointer !important;

    &:hover {
      background: var(--secondary-50) !important;
    }

    .mat-mdc-cell {
      padding: var(--spacing-lg) !important;
      border-bottom: 1px solid var(--gray-100) !important;
      font-family: var(--font-family-primary) !important;
      vertical-align: middle !important;
    }
  }
}

/* ===== TABLE CELL STYLES ===== */
.supplier-code {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--secondary-600);
  background: var(--secondary-50);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
}

.supplier-info {
  .supplier-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
  }

  .supplier-type {
    font-size: 0.75rem;
    color: var(--gray-500);
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    display: inline-block;
  }
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  .phone, .email, .website {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--gray-500);
    }
  }
}

.location-info {
  .country {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--warning-500);
    }
  }

  .city {
    font-size: 0.875rem;
    color: var(--gray-600);
  }
}

.products-count {
  text-align: center;

  .count {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-600);
    display: block;
  }

  .label {
    font-size: 0.75rem;
    color: var(--gray-500);
  }
}

.balance {
  font-weight: 700;
  font-size: 0.875rem;

  &.positive {
    color: var(--success-600);
  }

  &.negative {
    color: var(--error-600);
  }

  &.zero {
    color: var(--gray-500);
  }
}

.rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);

  .stars {
    display: flex;
    gap: 2px;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;

      &.filled {
        color: var(--warning-500);
      }

      &.empty {
        color: var(--gray-300);
      }
    }
  }

  .rating-value {
    font-size: 0.75rem;
    color: var(--gray-500);
  }
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;

  &.active {
    background: var(--success-100);
    color: var(--success-700);
  }

  &.inactive {
    background: var(--gray-100);
    color: var(--gray-700);
  }

  &.blocked {
    background: var(--error-100);
    color: var(--error-700);
  }
}

.actions-buttons {
  display: flex;
  gap: var(--spacing-xs);

  button {
    width: 36px !important;
    height: 36px !important;
    min-width: 36px !important;

    mat-icon {
      font-size: 1.125rem !important;
      width: 1.125rem !important;
      height: 1.125rem !important;
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr 1fr;
    
    .clear-filters-btn {
      grid-column: span 3;
      justify-self: center;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .page-title {
      font-size: 2rem;
    }

    .header-actions {
      width: 100%;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    .clear-filters-btn {
      grid-column: span 1;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .table-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;

    .table-actions {
      align-self: center;
    }
  }

  .suppliers-table {
    .mat-mdc-header-cell,
    .mat-mdc-cell {
      padding: var(--spacing-md) !important;
      font-size: 0.875rem !important;
    }
  }

  .actions-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);

    button {
      width: 32px !important;
      height: 32px !important;
      min-width: 32px !important;
    }
  }
}
