@echo off
title Terra Retail ERP - Full System Launcher
color 0A

echo ========================================
echo    Terra Retail ERP - Full System
echo ========================================
echo.
echo Starting both API and Angular...
echo.

echo 🚀 Starting API Server...
start "Terra Retail API" cmd /k "cd src\Terra.Retail.API && dotnet run --urls http://localhost:5000"

echo ⏳ Waiting for API to start...
timeout /t 10 /nobreak >nul

echo 🌐 Starting Angular Application...
start "Terra Retail Web" cmd /k "cd src\Terra.Retail.Web && ng serve --port 4200 --open"

echo.
echo ========================================
echo ✅ System Started Successfully!
echo ========================================
echo.
echo 📍 Access URLs:
echo 🔗 API Server: http://localhost:5000
echo 🔗 Angular App: http://localhost:4200
echo 📖 API Docs: http://localhost:5000/swagger
echo.
echo 💡 Both applications are running in separate windows
echo 💡 Close the command windows to stop the applications
echo.

pause
