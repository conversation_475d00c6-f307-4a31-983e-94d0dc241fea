/* Customers List Styles */
.customers-container {
  padding: 20px;
  direction: rtl;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 12px;

      i {
        color: #3498db;
      }
    }

    .page-subtitle {
      color: #7f8c8d;
      margin: 0;
      font-size: 16px;
    }
  }

  .header-actions {
    .btn {
      padding: 12px 24px;
      font-weight: 500;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

/* Search and Filters */
.search-filters {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .search-boxes {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;

    .search-box {
      flex: 1;
      min-width: 300px;
      position: relative;

      i {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #7f8c8d;
        z-index: 1;
      }

      .form-control {
        padding-right: 40px;
        border-radius: 8px;
        border: 1px solid #ddd;
        font-size: 14px;
        height: 45px;

        &:focus {
          border-color: #3498db;
          box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        &::placeholder {
          color: #7f8c8d;
          font-size: 13px;
        }
      }
    }
  }

  .filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;

    .form-control {
      min-width: 150px;
      border-radius: 8px;
      border: 1px solid #ddd;
      font-size: 14px;
      height: 45px;

      &:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
      }
    }
  }
}

/* Statistics Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stat-content {
    flex: 1;

    h3 {
      font-size: 24px;
      font-weight: 700;
      margin: 0 0 4px 0;
      color: #2c3e50;
    }

    p {
      font-size: 14px;
      color: #7f8c8d;
      margin: 0;
    }
  }

  &.total .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.active .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  &.balance .stat-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
}

/* Table Container */
.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;

  .table-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }

    .table-actions {
      .btn {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }

  .table-responsive {
    overflow-x: auto;
  }

  .table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;

    th, td {
      padding: 12px 16px;
      text-align: right;
      border-bottom: 1px solid #e9ecef;
      vertical-align: middle;
    }

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
    }

    td {
      color: #495057;
      font-size: 14px;
    }

    .customer-code {
      font-weight: 600;
      color: #3498db;
    }

    .customer-name strong {
      color: #2c3e50;
    }

    .phone-numbers {
      .phone-primary {
        display: block;
        font-weight: 500;
      }

      .phone-secondary {
        display: block;
        font-size: 12px;
        color: #7f8c8d;
      }
    }

    .email-link {
      color: #3498db;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    .customer-type, .price-category {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      background-color: #e9ecef;
      color: #495057;
    }

    .balance {
      font-weight: 600;

      &.negative {
        color: #e74c3c;
      }
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.active {
        background-color: #d4edda;
        color: #155724;
      }

      &.inactive {
        background-color: #f8d7da;
        color: #721c24;
      }
    }

    .action-buttons {
      display: flex;
      gap: 4px;

      .btn {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: all 0.2s;

        &.btn-info {
          background-color: #17a2b8;
          color: white;

          &:hover {
            background-color: #138496;
          }
        }

        &.btn-warning {
          background-color: #ffc107;
          color: #212529;

          &:hover {
            background-color: #e0a800;
          }
        }

        &.btn-danger {
          background-color: #dc3545;
          color: white;

          &:hover {
            background-color: #c82333;
          }
        }

        &.btn-success {
          background-color: #28a745;
          color: white;

          &:hover {
            background-color: #218838;
          }
        }
      }
    }
  }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 4px;

    .page-item {
      .page-link {
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        color: #495057;
        text-decoration: none;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background-color: #e9ecef;
          border-color: #adb5bd;
        }
      }

      &.active .page-link {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
      }

      &.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        cursor: not-allowed;
      }
    }
  }

  .pagination-info {
    font-size: 14px;
    color: #6c757d;
  }
}

/* Loading and Empty States */
.loading-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .spinner {
    margin: 0 auto 20px;
  }

  p {
    color: #6c757d;
    font-size: 16px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  i {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
  }

  h3 {
    color: #6c757d;
    margin-bottom: 10px;
  }

  p {
    color: #adb5bd;
    margin-bottom: 30px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .search-filters {
    flex-direction: column;
  }

  .stats-row {
    grid-template-columns: 1fr;
  }

  .table-container .table-responsive {
    font-size: 12px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }
}
