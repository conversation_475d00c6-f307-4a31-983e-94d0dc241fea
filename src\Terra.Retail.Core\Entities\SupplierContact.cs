using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// جهات اتصال الموردين
    /// </summary>
    public class SupplierContact : BaseEntity
    {
        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// اسم جهة الاتصال
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string ContactName { get; set; } = string.Empty;

        /// <summary>
        /// المنصب/الوظيفة
        /// </summary>
        [MaxLength(100)]
        public string? Position { get; set; }

        /// <summary>
        /// القسم
        /// </summary>
        [MaxLength(100)]
        public string? Department { get; set; }

        /// <summary>
        /// رقم الهاتف المكتبي
        /// </summary>
        [MaxLength(20)]
        public string? OfficePhone { get; set; }

        /// <summary>
        /// رقم الهاتف المحمول
        /// </summary>
        [MaxLength(20)]
        public string? MobilePhone { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// رقم الفاكس
        /// </summary>
        [MaxLength(20)]
        public string? Fax { get; set; }

        /// <summary>
        /// هل جهة الاتصال رئيسية
        /// </summary>
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// هل جهة الاتصال نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ملاحظات
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Supplier Supplier { get; set; } = null!;
    }
}
