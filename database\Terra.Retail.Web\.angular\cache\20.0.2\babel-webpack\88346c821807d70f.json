{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, NgModule } from '@angular/core';\nimport { startWith } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nlet MatLine = /*#__PURE__*/(() => {\n  class MatLine {\n    static ɵfac = function MatLine_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLine)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLine,\n      selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n      hostAttrs: [1, \"mat-line\"]\n    });\n  }\n  return MatLine;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nlet MatLineModule = /*#__PURE__*/(() => {\n  class MatLineModule {\n    static ɵfac = function MatLineModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLineModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatLineModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatLineModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatLine as M, MatLineModule as a, setLines as s };", "map": {"version": 3, "names": ["i0", "Directive", "NgModule", "startWith", "M", "MatCommonModule", "MatLine", "ɵfac", "MatLine_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "setLines", "lines", "element", "prefix", "changes", "pipe", "subscribe", "length", "setClass", "className", "isAdd", "nativeElement", "classList", "toggle", "MatLineModule", "MatLineModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "a", "s"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/line-Bz5f9Cyx.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, NgModule } from '@angular/core';\nimport { startWith } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLine, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatLine, isStandalone: true, selector: \"[mat-line], [matLine]\", host: { classAttribute: \"mat-line\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-line], [matLine]',\n                    host: { 'class': 'mat-line' },\n                }]\n        }] });\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n    // Note: doesn't need to unsubscribe, because `changes`\n    // gets completed by Angular when the view is destroyed.\n    lines.changes.pipe(startWith(lines)).subscribe(({ length }) => {\n        setClass(element, `${prefix}-2-line`, false);\n        setClass(element, `${prefix}-3-line`, false);\n        setClass(element, `${prefix}-multi-line`, false);\n        if (length === 2 || length === 3) {\n            setClass(element, `${prefix}-${length}-line`, true);\n        }\n        else if (length > 3) {\n            setClass(element, `${prefix}-multi-line`, true);\n        }\n    });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n    element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLineModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatLine], exports: [MatLine, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatLineModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatLine],\n                    exports: [MatLine, MatCommonModule],\n                }]\n        }] });\n\nexport { MatLine as M, MatLineModule as a, setLines as s };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;;AAEnE;AACA;AACA;AACA;AACA;AAJA,IAKMC,OAAO;EAAb,MAAMA,OAAO,CAAC;IACV,OAAOC,IAAI,YAAAC,gBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,OAAO;IAAA;IAC1G,OAAOI,IAAI,kBAD8EV,EAAE,CAAAW,iBAAA;MAAAC,IAAA,EACJN,OAAO;MAAAO,SAAA;MAAAC,SAAA;IAAA;EAClG;EAAC,OAHKR,OAAO;AAAA;AAIb;EAAA,QAAAS,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC9C;EACA;EACAF,KAAK,CAACG,OAAO,CAACC,IAAI,CAAClB,SAAS,CAACc,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IAAEC;EAAO,CAAC,KAAK;IAC3DC,QAAQ,CAACN,OAAO,EAAE,GAAGC,MAAM,SAAS,EAAE,KAAK,CAAC;IAC5CK,QAAQ,CAACN,OAAO,EAAE,GAAGC,MAAM,SAAS,EAAE,KAAK,CAAC;IAC5CK,QAAQ,CAACN,OAAO,EAAE,GAAGC,MAAM,aAAa,EAAE,KAAK,CAAC;IAChD,IAAII,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC9BC,QAAQ,CAACN,OAAO,EAAE,GAAGC,MAAM,IAAII,MAAM,OAAO,EAAE,IAAI,CAAC;IACvD,CAAC,MACI,IAAIA,MAAM,GAAG,CAAC,EAAE;MACjBC,QAAQ,CAACN,OAAO,EAAE,GAAGC,MAAM,aAAa,EAAE,IAAI,CAAC;IACnD;EACJ,CAAC,CAAC;AACN;AACA;AACA,SAASK,QAAQA,CAACN,OAAO,EAAEO,SAAS,EAAEC,KAAK,EAAE;EACzCR,OAAO,CAACS,aAAa,CAACC,SAAS,CAACC,MAAM,CAACJ,SAAS,EAAEC,KAAK,CAAC;AAC5D;AAAC,IACKI,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB,OAAOvB,IAAI,YAAAwB,sBAAAtB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqB,aAAa;IAAA;IAChH,OAAOE,IAAI,kBAnC8EhC,EAAE,CAAAiC,gBAAA;MAAArB,IAAA,EAmCSkB;IAAa;IACjH,OAAOI,IAAI,kBApC8ElC,EAAE,CAAAmC,gBAAA;MAAAC,OAAA,GAoCkC/B,eAAe,EAAEA,eAAe;IAAA;EACjK;EAAC,OAJKyB,aAAa;AAAA;AAKnB;EAAA,QAAAf,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAAST,OAAO,IAAIF,CAAC,EAAE0B,aAAa,IAAIO,CAAC,EAAErB,QAAQ,IAAIsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}