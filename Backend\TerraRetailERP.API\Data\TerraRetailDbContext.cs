using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Data
{
    public class TerraRetailDbContext : DbContext
    {
        public TerraRetailDbContext(DbContextOptions<TerraRetailDbContext> options) : base(options)
        {
        }

        // User Management
        public DbSet<User> Users { get; set; }
        public DbSet<UserBranch> UserBranches { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }

        // Company Structure
        public DbSet<Branch> Branches { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }

        // HR Management
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Shift> Shifts { get; set; }
        public DbSet<ShiftBreak> ShiftBreaks { get; set; }
        public DbSet<EmployeeShift> EmployeeShifts { get; set; }
        public DbSet<BiometricDevice> BiometricDevices { get; set; }
        public DbSet<AttendanceRecord> AttendanceRecords { get; set; }

        // Leave Management
        public DbSet<LeaveType> LeaveTypes { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; }

        // Payroll Management
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<PayrollItem> PayrollItems { get; set; }
        public DbSet<Payslip> Payslips { get; set; }

        // Product Management
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<ProductStock> ProductStocks { get; set; }
        public DbSet<ProductBatch> ProductBatches { get; set; }

        // Customer & Supplier Management
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }

        // Sales & Purchase Management
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }

        // Common/Reference Data
        public DbSet<Country> Countries { get; set; }
        public DbSet<Area> Areas { get; set; }
        public DbSet<EmployeeDocument> EmployeeDocuments { get; set; }
        public DbSet<WorkRegulation> WorkRegulations { get; set; }
        public DbSet<AuditTrail> AuditTrails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User relationships
            modelBuilder.Entity<User>()
                .HasOne(u => u.Creator)
                .WithMany()
                .HasForeignKey(u => u.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasOne(u => u.LastModifier)
                .WithMany()
                .HasForeignKey(u => u.LastModifiedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure UserBranch unique constraint
            modelBuilder.Entity<UserBranch>()
                .HasIndex(ub => new { ub.UserId, ub.BranchId })
                .IsUnique();

            // Configure UserBranch default branch constraint
            modelBuilder.Entity<UserBranch>()
                .HasIndex(ub => new { ub.UserId, ub.IsDefault })
                .HasFilter("[IsDefault] = 1")
                .IsUnique();

            // Configure Employee relationships
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.BiometricId)
                .IsUnique()
                .HasFilter("[BiometricId] IS NOT NULL");

            // Configure Product relationships
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.ProductCode)
                .IsUnique();

            // Configure Sale relationships
            modelBuilder.Entity<Sale>()
                .HasIndex(s => s.InvoiceNumber)
                .IsUnique();

            // Configure PurchaseInvoice relationships
            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            // Configure Payroll unique constraint
            modelBuilder.Entity<Payroll>()
                .HasIndex(p => new { p.EmployeeId, p.Year, p.Month })
                .IsUnique();

            // Configure EmployeeLeaveBalance unique constraint
            modelBuilder.Entity<EmployeeLeaveBalance>()
                .HasIndex(elb => new { elb.EmployeeId, elb.LeaveTypeId, elb.Year })
                .IsUnique();

            // Configure EmployeeShift unique constraint
            modelBuilder.Entity<EmployeeShift>()
                .HasIndex(es => new { es.EmployeeId, es.EffectiveDate, es.IsActive })
                .IsUnique()
                .HasFilter("[IsActive] = 1");

            // Configure AttendanceRecord relationships
            modelBuilder.Entity<AttendanceRecord>()
                .HasOne(ar => ar.CheckInDevice)
                .WithMany(bd => bd.CheckInRecords)
                .HasForeignKey(ar => ar.CheckInDeviceId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AttendanceRecord>()
                .HasOne(ar => ar.CheckOutDevice)
                .WithMany(bd => bd.CheckOutRecords)
                .HasForeignKey(ar => ar.CheckOutDeviceId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Department Manager relationship
            modelBuilder.Entity<Department>()
                .HasOne(d => d.Manager)
                .WithMany()
                .HasForeignKey(d => d.ManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Payslip unique constraint
            modelBuilder.Entity<Payslip>()
                .HasIndex(ps => ps.PayslipNumber)
                .IsUnique();

            // Configure decimal precision
            ConfigureDecimalPrecision(modelBuilder);

            // Seed initial data
            SeedInitialData(modelBuilder);
        }

        private void ConfigureDecimalPrecision(ModelBuilder modelBuilder)
        {
            // Configure all decimal properties to have consistent precision
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                    {
                        if (property.Name.Contains("Price") || property.Name.Contains("Amount") || 
                            property.Name.Contains("Cost") || property.Name.Contains("Salary") ||
                            property.Name.Contains("Total") || property.Name.Contains("Tax") ||
                            property.Name.Contains("Discount"))
                        {
                            property.SetColumnType("decimal(18,2)");
                        }
                        else if (property.Name.Contains("Quantity") || property.Name.Contains("Stock"))
                        {
                            property.SetColumnType("decimal(18,3)");
                        }
                        else if (property.Name.Contains("Hours"))
                        {
                            property.SetColumnType("decimal(5,2)");
                        }
                    }
                }
            }
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // Seed Countries
            modelBuilder.Entity<Country>().HasData(
                new Country { Id = 1, NameAr = "مصر", NameEn = "Egypt", Code = "EG", IsActive = true },
                new Country { Id = 2, NameAr = "السعودية", NameEn = "Saudi Arabia", Code = "SA", IsActive = true }
            );

            // Seed Areas
            modelBuilder.Entity<Area>().HasData(
                new Area { Id = 1, NameAr = "القاهرة", NameEn = "Cairo", CountryId = 1, IsActive = true },
                new Area { Id = 2, NameAr = "الجيزة", NameEn = "Giza", CountryId = 1, IsActive = true },
                new Area { Id = 3, NameAr = "الإسكندرية", NameEn = "Alexandria", CountryId = 1, IsActive = true }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, NameAr = "إلكترونيات", NameEn = "Electronics", Code = "ELEC", IsActive = true },
                new Category { Id = 2, NameAr = "ملابس", NameEn = "Clothing", Code = "CLOTH", IsActive = true },
                new Category { Id = 3, NameAr = "أجهزة كمبيوتر", NameEn = "Computers", Code = "COMP", IsActive = true }
            );

            // Seed Units
            modelBuilder.Entity<Unit>().HasData(
                new Unit { Id = 1, NameAr = "قطعة", NameEn = "Piece", Symbol = "PC", IsActive = true },
                new Unit { Id = 2, NameAr = "كيلو", NameEn = "Kilogram", Symbol = "KG", IsActive = true },
                new Unit { Id = 3, NameAr = "متر", NameEn = "Meter", Symbol = "M", IsActive = true }
            );

            // Seed Leave Types
            modelBuilder.Entity<LeaveType>().HasData(
                new LeaveType { Id = 1, NameAr = "إجازة سنوية", NameEn = "Annual Leave", MaxDaysPerYear = 21, IsPaid = true, RequireApproval = true, CanCarryForward = true, IsActive = true, CreatedBy = 1 },
                new LeaveType { Id = 2, NameAr = "إجازة مرضية", NameEn = "Sick Leave", MaxDaysPerYear = 15, IsPaid = true, RequireApproval = false, CanCarryForward = false, IsActive = true, CreatedBy = 1 },
                new LeaveType { Id = 3, NameAr = "إجازة طارئة", NameEn = "Emergency Leave", MaxDaysPerYear = 7, IsPaid = false, RequireApproval = true, CanCarryForward = false, IsActive = true, CreatedBy = 1 }
            );
        }
    }
}
