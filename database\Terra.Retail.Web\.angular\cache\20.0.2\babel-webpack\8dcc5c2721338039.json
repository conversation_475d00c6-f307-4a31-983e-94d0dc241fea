{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatRadioModule } from '@angular/material/radio';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction AddProduct_mat_icon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_mat_icon_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"2\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_mat_icon_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProduct_div_49_mat_option_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r3.nameAr, \" \");\n  }\n}\nfunction AddProduct_div_49_mat_option_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const unit_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", unit_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", unit_r4.nameAr, \" (\", unit_r4.symbol, \") \");\n  }\n}\nfunction AddProduct_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h2\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629 \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 30)(14, \"div\", 31)(15, \"mat-form-field\", 32)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-select\", 33)(19, \"mat-option\", 34)(20, \"div\", 35)(21, \"strong\");\n    i0.ɵɵtext(22, \"\\u0645\\u0646\\u062A\\u062C \\u0645\\u062D\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"small\");\n    i0.ɵɵtext(24, \"\\u064A\\u0628\\u062F\\u0623 \\u0645\\u0646 2000000000001\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"mat-option\", 36)(26, \"div\", 35)(27, \"strong\");\n    i0.ɵɵtext(28, \"\\u0645\\u0646\\u062A\\u062C \\u062F\\u0648\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"small\");\n    i0.ɵɵtext(30, \"\\u0643\\u0648\\u062F \\u064A\\u062F\\u0648\\u064A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"mat-option\", 37)(32, \"div\", 35)(33, \"strong\");\n    i0.ɵɵtext(34, \"\\u0645\\u0646\\u062A\\u062C \\u0645\\u0648\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"small\");\n    i0.ɵɵtext(36, \"\\u0644\\u0644\\u0645\\u064A\\u0632\\u0627\\u0646 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"mat-error\");\n    i0.ɵɵtext(38, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 31)(40, \"mat-form-field\", 32)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 38);\n    i0.ɵɵelementStart(44, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AddProduct_div_49_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generateCode());\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"auto_awesome\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"mat-error\");\n    i0.ɵɵtext(48, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 30)(50, \"div\", 31)(51, \"mat-form-field\", 32)(52, \"mat-label\");\n    i0.ɵɵtext(53, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"input\", 40);\n    i0.ɵɵelementStart(55, \"mat-error\");\n    i0.ɵɵtext(56, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 31)(58, \"mat-form-field\", 32)(59, \"mat-label\");\n    i0.ɵɵtext(60, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"input\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 42)(63, \"mat-form-field\", 32)(64, \"mat-label\");\n    i0.ɵɵtext(65, \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"textarea\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 30)(68, \"div\", 31)(69, \"mat-form-field\", 32)(70, \"mat-label\");\n    i0.ɵɵtext(71, \"\\u0627\\u0644\\u0641\\u0626\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"mat-select\", 44);\n    i0.ɵɵtemplate(73, AddProduct_div_49_mat_option_73_Template, 2, 2, \"mat-option\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"mat-error\");\n    i0.ɵɵtext(75, \"\\u0627\\u0644\\u0641\\u0626\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 31)(77, \"mat-form-field\", 32)(78, \"mat-label\");\n    i0.ɵɵtext(79, \"\\u0648\\u062D\\u062F\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0627\\u0633\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"mat-select\", 46);\n    i0.ɵɵtemplate(81, AddProduct_div_49_mat_option_81_Template, 2, 3, \"mat-option\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"mat-error\");\n    i0.ɵɵtext(83, \"\\u0648\\u062D\\u062F\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0627\\u0633 \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(44);\n    i0.ɵɵproperty(\"disabled\", ((tmp_1_0 = ctx_r1.productForm.get(\"productType\")) == null ? null : tmp_1_0.value) === \"\\u062F\\u0648\\u0644\\u064A\");\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.units);\n  }\n}\nfunction AddProduct_div_50_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u0647\\u0627\\u0645\\u0634 \\u0627\\u0644\\u0631\\u0628\\u062D: \", ctx_r1.profitMargin, \"%\");\n  }\n}\nfunction AddProduct_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h2\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0627\\u0644\\u062A\\u0633\\u0639\\u064A\\u0631 \\u0648\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"\\u062D\\u062F\\u062F \\u0623\\u0633\\u0639\\u0627\\u0631 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621 \\u0648\\u0627\\u0644\\u0628\\u064A\\u0639\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 30)(14, \"div\", 31)(15, \"mat-form-field\", 32)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 48);\n    i0.ɵɵelementStart(19, \"span\", 49);\n    i0.ɵɵtext(20, \"\\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"mat-error\");\n    i0.ɵɵtext(22, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621 \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 31)(24, \"mat-form-field\", 32)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 50);\n    i0.ɵɵelementStart(28, \"span\", 49);\n    i0.ɵɵtext(29, \"\\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-error\");\n    i0.ɵɵtext(31, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639 \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(32, AddProduct_div_50_div_32_Template, 5, 1, \"div\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 27)(34, \"div\", 28)(35, \"h3\");\n    i0.ɵɵtext(36, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\");\n    i0.ɵɵtext(38, \"\\u062D\\u062F\\u062F \\u0645\\u0633\\u062A\\u0648\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 29)(40, \"div\", 30)(41, \"div\", 31)(42, \"mat-form-field\", 32)(43, \"mat-label\");\n    i0.ɵɵtext(44, \"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"input\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 31)(47, \"mat-form-field\", 32)(48, \"mat-label\");\n    i0.ɵɵtext(49, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0623\\u062F\\u0646\\u0649\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"input\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 31)(52, \"mat-form-field\", 32)(53, \"mat-label\");\n    i0.ɵɵtext(54, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0623\\u0642\\u0635\\u0649\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(55, \"input\", 54);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(32);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.profitMargin);\n  }\n}\nfunction AddProduct_div_51_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const supplier_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", supplier_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", supplier_r5.nameAr, \" - \", supplier_r5.supplierCode, \" \");\n  }\n}\nfunction AddProduct_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h2\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0648\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A \\u0648\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0627\\u0644\\u0625\\u0636\\u0627\\u0641\\u064A\\u064A\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 31)(14, \"mat-form-field\", 32)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 56);\n    i0.ɵɵtemplate(18, AddProduct_div_51_mat_option_18_Template, 2, 3, \"mat-option\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-error\");\n    i0.ɵɵtext(20, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A \\u0645\\u0637\\u0644\\u0648\\u0628\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(21, \"div\", 27)(22, \"div\", 28)(23, \"h3\");\n    i0.ɵɵtext(24, \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\");\n    i0.ɵɵtext(26, \"\\u062E\\u0635\\u0627\\u0626\\u0635 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629 \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 29)(28, \"div\", 57)(29, \"mat-checkbox\", 58)(30, \"span\", 59);\n    i0.ɵɵtext(31, \"\\u0645\\u0646\\u062A\\u062C \\u0646\\u0634\\u0637\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"small\");\n    i0.ɵɵtext(33, \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0645\\u062A\\u0627\\u062D \\u0644\\u0644\\u0628\\u064A\\u0639\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"mat-checkbox\", 60)(35, \"span\", 59);\n    i0.ɵɵtext(36, \"\\u0645\\u0646\\u062A\\u062C \\u0645\\u0648\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"small\");\n    i0.ɵɵtext(38, \"\\u064A\\u0628\\u0627\\u0639 \\u0628\\u0627\\u0644\\u0648\\u0632\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"mat-checkbox\", 61)(40, \"span\", 59);\n    i0.ɵɵtext(41, \"\\u0644\\u0647 \\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0646\\u062A\\u0647\\u0627\\u0621\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"small\");\n    i0.ɵɵtext(43, \"\\u064A\\u062A\\u0637\\u0644\\u0628 \\u062A\\u062A\\u0628\\u0639 \\u0627\\u0644\\u0635\\u0644\\u0627\\u062D\\u064A\\u0629\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"mat-checkbox\", 62)(45, \"span\", 59);\n    i0.ɵɵtext(46, \"\\u062A\\u062A\\u0628\\u0639 \\u0627\\u0644\\u0623\\u0631\\u0642\\u0627\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0644\\u0633\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"small\");\n    i0.ɵɵtext(48, \"\\u0644\\u0643\\u0644 \\u0642\\u0637\\u0639\\u0629 \\u0631\\u0642\\u0645 \\u0645\\u0645\\u064A\\u0632\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suppliers);\n  }\n}\nfunction AddProduct_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h2\", 26)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"preview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u0645\\u0644\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"\\u0631\\u0627\\u062C\\u0639 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0642\\u0628\\u0644 \\u0627\\u0644\\u062D\\u0641\\u0638\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 63)(14, \"div\", 64)(15, \"label\");\n    i0.ɵɵtext(16, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 64)(20, \"label\");\n    i0.ɵɵtext(21, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 64)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 64)(30, \"label\");\n    i0.ɵɵtext(31, \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r1.productForm.get(\"nameAr\")) == null ? null : tmp_1_0.value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r1.productForm.get(\"productCode\")) == null ? null : tmp_2_0.value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r1.productForm.get(\"productType\")) == null ? null : tmp_3_0.value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", (tmp_4_0 = ctx_r1.productForm.get(\"salePrice\")) == null ? null : tmp_4_0.value, \" \\u062C\\u0646\\u064A\\u0647\");\n  }\n}\nfunction AddProduct_button_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AddProduct_button_58_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u062A\\u0627\\u0644\\u064A \");\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"navigate_next\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProduct_button_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AddProduct_button_59_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveProduct());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction AddProduct_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67);\n    i0.ɵɵelement(2, \"mat-progress-spinner\", 68);\n    i0.ɵɵelementStart(3, \"p\", 69);\n    i0.ɵɵtext(4, \"\\u062C\\u0627\\u0631\\u064A \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let AddProduct = /*#__PURE__*/(() => {\n  class AddProduct {\n    fb;\n    http;\n    router;\n    snackBar;\n    productForm;\n    categories = [];\n    units = [];\n    suppliers = [];\n    selectedSuppliers = [];\n    isLoading = false;\n    isGeneratingCode = false;\n    currentStep = 1;\n    profitMargin = null;\n    productTypes = [{\n      value: 'محلي',\n      label: 'محلي',\n      description: 'منتج محلي - كود تلقائي يبدأ من 2000000000001'\n    }, {\n      value: 'دولي',\n      label: 'دولي',\n      description: 'منتج دولي - إدخال الكود يدوياً'\n    }, {\n      value: 'موزون',\n      label: 'موزون',\n      description: 'منتج موزون - كود تلقائي للميزان'\n    }];\n    constructor(fb, http, router, snackBar) {\n      this.fb = fb;\n      this.http = http;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.productForm = this.fb.group({\n        // معلومات أساسية\n        productType: ['محلي', Validators.required],\n        productCode: ['', Validators.required],\n        nameAr: ['', Validators.required],\n        nameEn: [''],\n        description: [''],\n        // التصنيف والوحدة\n        categoryId: ['', Validators.required],\n        unitId: ['', Validators.required],\n        // الأسعار والمخزون\n        purchasePrice: [0, [Validators.required, Validators.min(0)]],\n        salePrice: [0, [Validators.required, Validators.min(0)]],\n        minStock: [0, [Validators.min(0)]],\n        maxStock: [0, [Validators.min(0)]],\n        currentStock: [0, [Validators.min(0)]],\n        // المورد الرئيسي\n        mainSupplierId: ['', Validators.required],\n        // إعدادات\n        isActive: [true],\n        isWeighted: [false],\n        hasExpiry: [false],\n        trackSerial: [false]\n      });\n    }\n    ngOnInit() {\n      this.loadCategories();\n      this.loadUnits();\n      this.loadSuppliers();\n      // مراقبة تغيير نوع المنتج\n      this.productForm.get('productType')?.valueChanges.subscribe(type => {\n        this.onProductTypeChange(type);\n      });\n    }\n    loadCategories() {\n      this.http.get('http://localhost:5000/api/simple/categories').subscribe({\n        next: response => {\n          this.categories = response.categories || [];\n        },\n        error: error => {\n          console.error('Error loading categories:', error);\n          this.showMessage('خطأ في تحميل الفئات');\n        }\n      });\n    }\n    loadUnits() {\n      this.http.get('http://localhost:5000/api/simple/units').subscribe({\n        next: response => {\n          this.units = response.units || [];\n        },\n        error: error => {\n          console.error('Error loading units:', error);\n          this.showMessage('خطأ في تحميل الوحدات');\n        }\n      });\n    }\n    loadSuppliers() {\n      this.http.get('http://localhost:5000/api/simple/suppliers').subscribe({\n        next: response => {\n          this.suppliers = response.suppliers || [];\n        },\n        error: error => {\n          console.error('Error loading suppliers:', error);\n          this.showMessage('خطأ في تحميل الموردين');\n        }\n      });\n    }\n    onProductTypeChange(type) {\n      if (type === 'موزون') {\n        this.productForm.get('isWeighted')?.setValue(true);\n        this.productForm.get('isWeighted')?.disable();\n      } else {\n        this.productForm.get('isWeighted')?.setValue(false);\n        this.productForm.get('isWeighted')?.enable();\n      }\n      // تحديد إمكانية تعديل الكود\n      if (type === 'دولي') {\n        this.productForm.get('productCode')?.enable();\n      } else {\n        this.productForm.get('productCode')?.disable();\n      }\n      // مسح الكود الحالي عند تغيير النوع\n      this.productForm.get('productCode')?.setValue('');\n    }\n    generateProductCode() {\n      const productType = this.productForm.get('productType')?.value;\n      if (!productType) {\n        this.showMessage('يرجى اختيار نوع المنتج أولاً');\n        return;\n      }\n      if (productType === 'دولي') {\n        this.showMessage('للمنتجات الدولية، يرجى إدخال الكود يدوياً');\n        return;\n      }\n      this.isGeneratingCode = true;\n      this.http.post('http://localhost:5000/api/simple/generate-product-code', {\n        productType: productType\n      }).subscribe({\n        next: response => {\n          if (response.requiresManualInput) {\n            this.showMessage(response.message);\n          } else {\n            this.productForm.patchValue({\n              productCode: response.productCode\n            });\n            this.showMessage(`تم توليد الكود: ${response.productCode}`);\n          }\n          this.isGeneratingCode = false;\n        },\n        error: error => {\n          console.error('Error generating code:', error);\n          this.showMessage('خطأ في توليد الكود');\n          this.isGeneratingCode = false;\n        }\n      });\n    }\n    checkProductCode() {\n      const productCode = this.productForm.get('productCode')?.value;\n      if (!productCode) {\n        return;\n      }\n      this.http.post('http://localhost:5000/api/simple/check-product-code', {\n        productCode: productCode\n      }).subscribe({\n        next: response => {\n          if (!response.isAvailable) {\n            this.showMessage('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر', true);\n            this.productForm.get('productCode')?.setErrors({\n              'duplicate': true\n            });\n          } else {\n            this.productForm.get('productCode')?.setErrors(null);\n          }\n        },\n        error: error => {\n          console.error('Error checking code:', error);\n        }\n      });\n    }\n    addSupplier(supplier) {\n      if (!this.selectedSuppliers.find(s => s.id === supplier.id)) {\n        this.selectedSuppliers.push(supplier);\n      }\n    }\n    removeSupplier(supplier) {\n      this.selectedSuppliers = this.selectedSuppliers.filter(s => s.id !== supplier.id);\n    }\n    onSubmit() {\n      if (this.productForm.valid) {\n        this.isLoading = true;\n        const formData = {\n          ...this.productForm.value,\n          suppliers: this.selectedSuppliers.map(s => s.id)\n        };\n        // هنا سيتم إرسال البيانات لـ API\n        console.log('Product Data:', formData);\n        // محاكاة حفظ البيانات\n        setTimeout(() => {\n          this.isLoading = false;\n          this.showMessage('تم إضافة المنتج بنجاح');\n          this.router.navigate(['/products']);\n        }, 2000);\n      } else {\n        this.showMessage('يرجى ملء جميع الحقول المطلوبة', true);\n      }\n    }\n    cancel() {\n      this.router.navigate(['/products']);\n    }\n    showMessage(message, isError = false) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        horizontalPosition: 'center',\n        verticalPosition: 'top',\n        panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\n      });\n    }\n    // Navigation Methods for Professional UI\n    nextStep() {\n      if (this.currentStep < 4) {\n        this.currentStep++;\n      }\n    }\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    goBack() {\n      // Navigate back to products page\n      this.router.navigate(['/products']);\n    }\n    // Calculate profit margin\n    calculateProfitMargin() {\n      const purchasePrice = this.productForm.get('purchasePrice')?.value;\n      const salePrice = this.productForm.get('salePrice')?.value;\n      if (purchasePrice && salePrice && purchasePrice > 0) {\n        this.profitMargin = Math.round((salePrice - purchasePrice) / purchasePrice * 100);\n      } else {\n        this.profitMargin = null;\n      }\n    }\n    // Generate product code\n    generateCode() {\n      this.generateProductCode();\n    }\n    // Save product\n    saveProduct() {\n      if (this.productForm.valid) {\n        this.onSubmit();\n      } else {\n        this.snackBar.open('يرجى ملء جميع الحقول المطلوبة', 'إغلاق', {\n          duration: 3000,\n          horizontalPosition: 'center',\n          verticalPosition: 'top'\n        });\n      }\n    }\n    static ɵfac = function AddProduct_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddProduct)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProduct,\n      selectors: [[\"app-add-product\"]],\n      decls: 61,\n      vars: 36,\n      consts: [[1, \"professional-add-product\"], [1, \"professional-header\", \"animate-slide-up\"], [1, \"header-content\"], [1, \"header-info\"], [1, \"header-title\"], [1, \"header-icon\"], [1, \"header-subtitle\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 1, \"professional-button\", \"secondary\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"professional-button\", \"primary\", 3, \"click\", \"disabled\"], [1, \"progress-steps\"], [1, \"step\"], [1, \"step-circle\"], [4, \"ngIf\"], [1, \"step-label\"], [1, \"step-line\"], [1, \"professional-content\"], [1, \"professional-form\", 3, \"formGroup\"], [\"class\", \"form-step\", 4, \"ngIf\"], [1, \"navigation-buttons\"], [\"mat-stroked-button\", \"\", 1, \"professional-button\", \"secondary\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"class\", \"professional-button primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"class\", \"professional-button primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"form-step\"], [1, \"step-content\"], [1, \"step-title\"], [1, \"professional-card\"], [1, \"card-header\"], [1, \"card-content\"], [1, \"form-grid\"], [1, \"form-group\"], [\"appearance\", \"outline\", 1, \"professional-input\"], [\"formControlName\", \"productType\", \"required\", \"\"], [\"value\", \"\\u0645\\u062D\\u0644\\u064A\"], [1, \"option-content\"], [\"value\", \"\\u062F\\u0648\\u0644\\u064A\"], [\"value\", \"\\u0645\\u0648\\u0632\\u0648\\u0646\"], [\"matInput\", \"\", \"formControlName\", \"productCode\", \"placeholder\", \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"nameAr\", \"placeholder\", \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"nameEn\", \"placeholder\", \"Product Name in English\"], [1, \"form-group\", \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u0648\\u0635\\u0641 \\u062A\\u0641\\u0635\\u064A\\u0644\\u064A \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\"], [\"formControlName\", \"categoryId\", \"required\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"unitId\", \"required\", \"\"], [3, \"value\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"purchasePrice\", \"placeholder\", \"0.00\", \"min\", \"0\", \"step\", \"0.01\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"salePrice\", \"placeholder\", \"0.00\", \"min\", \"0\", \"step\", \"0.01\"], [\"class\", \"profit-margin\", 4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"currentStock\", \"placeholder\", \"0\", \"min\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"minStock\", \"placeholder\", \"0\", \"min\", \"0\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"maxStock\", \"placeholder\", \"0\", \"min\", \"0\"], [1, \"profit-margin\"], [\"formControlName\", \"mainSupplierId\", \"required\", \"\"], [1, \"settings-grid\"], [\"formControlName\", \"isActive\", 1, \"professional-checkbox\"], [1, \"checkbox-label\"], [\"formControlName\", \"isWeighted\", 1, \"professional-checkbox\"], [\"formControlName\", \"hasExpiry\", 1, \"professional-checkbox\"], [\"formControlName\", \"trackSerial\", 1, \"professional-checkbox\"], [1, \"review-grid\"], [1, \"review-item\"], [\"mat-raised-button\", \"\", 1, \"professional-button\", \"primary\", 3, \"click\"], [1, \"loading-overlay\"], [1, \"loading-content\"], [\"diameter\", \"50\"], [1, \"loading-text\"]],\n      template: function AddProduct_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"add_business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C \\u062C\\u062F\\u064A\\u062F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0645\\u0646\\u062A\\u062C \\u062C\\u062F\\u064A\\u062F \\u0648\\u0625\\u0636\\u0627\\u0641\\u062A\\u0647 \\u0625\\u0644\\u0649 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0628\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AddProduct_Template_button_click_11_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" \\u0627\\u0644\\u0639\\u0648\\u062F\\u0629 \\u0644\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AddProduct_Template_button_click_15_listener() {\n            return ctx.saveProduct();\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12);\n          i0.ɵɵtemplate(22, AddProduct_mat_icon_22_Template, 2, 0, \"mat-icon\", 13)(23, AddProduct_span_23_Template, 2, 0, \"span\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 14);\n          i0.ɵɵtext(25, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(26, \"div\", 15);\n          i0.ɵɵelementStart(27, \"div\", 11)(28, \"div\", 12);\n          i0.ɵɵtemplate(29, AddProduct_mat_icon_29_Template, 2, 0, \"mat-icon\", 13)(30, AddProduct_span_30_Template, 2, 0, \"span\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\", 14);\n          i0.ɵɵtext(32, \"\\u0627\\u0644\\u062A\\u0633\\u0639\\u064A\\u0631 \\u0648\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(33, \"div\", 15);\n          i0.ɵɵelementStart(34, \"div\", 11)(35, \"div\", 12);\n          i0.ɵɵtemplate(36, AddProduct_mat_icon_36_Template, 2, 0, \"mat-icon\", 13)(37, AddProduct_span_37_Template, 2, 0, \"span\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"span\", 14);\n          i0.ɵɵtext(39, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0648\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(40, \"div\", 15);\n          i0.ɵɵelementStart(41, \"div\", 11)(42, \"div\", 12)(43, \"span\");\n          i0.ɵɵtext(44, \"4\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"span\", 14);\n          i0.ɵɵtext(46, \"\\u0627\\u0644\\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0648\\u0627\\u0644\\u062D\\u0641\\u0638\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 16)(48, \"form\", 17);\n          i0.ɵɵtemplate(49, AddProduct_div_49_Template, 84, 3, \"div\", 18)(50, AddProduct_div_50_Template, 56, 1, \"div\", 18)(51, AddProduct_div_51_Template, 49, 1, \"div\", 18)(52, AddProduct_div_52_Template, 34, 4, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 19)(54, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function AddProduct_Template_button_click_54_listener() {\n            return ctx.previousStep();\n          });\n          i0.ɵɵelementStart(55, \"mat-icon\");\n          i0.ɵɵtext(56, \"navigate_before\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" \\u0627\\u0644\\u0633\\u0627\\u0628\\u0642 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(58, AddProduct_button_58_Template, 4, 0, \"button\", 21)(59, AddProduct_button_59_Template, 4, 1, \"button\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(60, AddProduct_div_60_Template, 5, 0, \"div\", 23);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 1)(\"completed\", ctx.currentStep > 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 2)(\"completed\", ctx.currentStep > 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 3)(\"completed\", ctx.currentStep > 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 3);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.currentStep > 3);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 4);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.productForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentStep === 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, HttpClientModule, MatCardModule, MatButtonModule, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatInputModule, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, MatSelectModule, i10.MatSelect, i10.MatOption, MatCheckboxModule, i11.MatCheckbox, MatStepperModule, MatSnackBarModule, MatChipsModule, MatAutocompleteModule, MatRadioModule, MatFormFieldModule, MatProgressSpinnerModule, i12.MatProgressSpinner],\n      styles: [\"\\n\\n.professional-add-product[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e2e8f0;\\n  padding: 24px 32px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 32px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: #1e293b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  width: 32px;\\n  height: 32px;\\n  color: #3fa6f0;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   .header-subtitle[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #64748b;\\n  font-size: 16px;\\n  font-weight: 400;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 16px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  color: #64748b;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-circle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n  font-weight: 500;\\n  text-align: center;\\n  max-width: 100px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-circle[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3fa6f0 0%, #1b7be6 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(63, 166, 240, 0.3);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  color: #3fa6f0;\\n  font-weight: 600;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-circle[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n  color: white;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  color: #10b981;\\n  font-weight: 600;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-line[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 2px;\\n  background: #e2e8f0;\\n  transition: all 0.3s ease;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-line.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #10b981 0%, #059669 100%);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 32px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 24px 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n  color: #3fa6f0;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);\\n  border: 1px solid #e2e8f0;\\n  margin-bottom: 24px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);\\n  transform: translateY(-2px);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\\n  padding: 20px 24px;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #64748b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .professional-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .professional-input[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .professional-input[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  background-color: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  transition: all 0.3s ease;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .professional-input[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: #cbd5e1;\\n  background-color: #f1f5f9;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .professional-input[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%] {\\n  border-color: #3fa6f0;\\n  background-color: white;\\n  box-shadow: 0 0 0 3px rgba(63, 166, 240, 0.1);\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 12px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .profit-margin[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 16px;\\n  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);\\n  border: 1px solid #a7f3d0;\\n  border-radius: 8px;\\n  color: #065f46;\\n  font-weight: 500;\\n  margin-top: 16px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .profit-margin[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #10b981;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .professional-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 16px;\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .professional-checkbox[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  border-color: #cbd5e1;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .professional-checkbox[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1e293b;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .professional-checkbox[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 12px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .review-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .review-grid[_ngcontent-%COMP%]   .review-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 16px;\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .review-grid[_ngcontent-%COMP%]   .review-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #64748b;\\n  font-size: 12px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .review-grid[_ngcontent-%COMP%]   .review-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n  font-size: 16px;\\n}\\n.professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 32px;\\n  padding: 24px;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_slideAnimation {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n    margin-bottom: 24px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .professional-button[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 12px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    max-width: none;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-header[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-line[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .professional-form[_ngcontent-%COMP%]   .form-step[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .professional-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .review-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .professional-add-product[_ngcontent-%COMP%]   .professional-content[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .professional-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYWRkLXByb2R1Y3QvYWRkLXByb2R1Y3QtcHJvZmVzc2lvbmFsLnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi8uLi9FcnAlMjAyL2RhdGFiYXNlL1RlcnJhLlJldGFpbC5XZWIvc3JjL2FwcC9wYWdlcy9hZGQtcHJvZHVjdC9hZGQtcHJvZHVjdC1wcm9mZXNzaW9uYWwuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxvQ0FBQTtBQUNBO0VBQ0UsaUJBQUE7RUFDQSw2REFBQTtBQ0NGO0FEQ0U7RUFDRSxpQkFBQTtFQUNBLGdDQUFBO0VBQ0Esa0JBQUE7RUFDQSx3Q0FBQTtBQ0NKO0FEQ0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FDQ047QURFUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQ0FWO0FERVU7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FDQVo7QURJUTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQ0ZWO0FETU07RUFDRSxhQUFBO0VBQ0EsU0FBQTtBQ0pSO0FEUUk7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7QUNOTjtBRFFNO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FDTlI7QURRUTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7QUNOVjtBRFFVO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FDTlo7QURVUTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FDUlY7QURZVTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLDhDQUFBO0FDVlo7QURhVTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtBQ1haO0FEZ0JVO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0FDZFo7QURpQlU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUNmWjtBRG9CTTtFQUNFLFdBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQ2xCUjtBRG9CUTtFQUNFLDREQUFBO0FDbEJWO0FEd0JFO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsYUFBQTtBQ3RCSjtBRDJCVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUN6Qlo7QUQyQlk7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FDekJkO0FENkJVO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLHVFQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7QUMzQlo7QUQ2Qlk7RUFDRSx1RUFBQTtFQUNBLDJCQUFBO0FDM0JkO0FEOEJZO0VBQ0UsNkRBQUE7RUFDQSxrQkFBQTtFQUNBLGdDQUFBO0FDNUJkO0FEOEJjO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FDNUJoQjtBRCtCYztFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQzdCaEI7QURpQ1k7RUFDRSxhQUFBO0FDL0JkO0FEaUNjO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FDL0JoQjtBRGlDZ0I7RUFDRSxnQkFBQTtBQy9CbEI7QURvQ2dCO0VBQ0UsaUJBQUE7QUNsQ2xCO0FEcUNnQjtFQUNFLFdBQUE7QUNuQ2xCO0FEcUNrQjtFQUNFLFdBQUE7QUNuQ3BCO0FEcUNvQjtFQUNFLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0FDbkN0QjtBRHFDc0I7RUFDRSxxQkFBQTtFQUNBLHlCQUFBO0FDbkN4QjtBRHNDc0I7RUFDRSxxQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkNBQUE7QUNwQ3hCO0FEMENnQjtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFFBQUE7QUN4Q2xCO0FEMENrQjtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQ3hDcEI7QUQyQ2tCO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUN6Q3BCO0FEOENjO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGtCQUFBO0VBQ0EsNkRBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUM1Q2hCO0FEOENnQjtFQUNFLGNBQUE7QUM1Q2xCO0FEZ0RjO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtBQzlDaEI7QURnRGdCO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQzlDbEI7QURnRGtCO0VBQ0UsbUJBQUE7RUFDQSxxQkFBQTtBQzlDcEI7QURpRGtCO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0FDL0NwQjtBRGtEa0I7RUFDRSxjQUFBO0VBQ0EsZUFBQTtBQ2hEcEI7QURxRGM7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0FDbkRoQjtBRHFEZ0I7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxRQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQ25EbEI7QURxRGtCO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUNuRHBCO0FEc0RrQjtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUNwRHBCO0FEOERJO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0Esd0NBQUE7RUFDQSx5QkFBQTtBQzVETjs7QURpRUEsZUFBQTtBQUNBO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsMkJBQUE7RUM5REY7RURnRUE7SUFDRSxVQUFBO0lBQ0Esd0JBQUE7RUM5REY7QUFDRjtBRGlFQSxzQkFBQTtBQUNBO0VBRUk7SUFDRSxrQkFBQTtFQ2hFSjtFRGtFSTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7SUFDQSxTQUFBO0lBQ0EsbUJBQUE7RUNoRU47RURrRU07SUFDRSxXQUFBO0VDaEVSO0VEa0VRO0lBQ0UsT0FBQTtFQ2hFVjtFRHFFSTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtFQ25FTjtFRHFFTTtJQUNFLG1CQUFBO0lBQ0EsU0FBQTtFQ25FUjtFRHFFUTtJQUNFLGVBQUE7RUNuRVY7RUR1RU07SUFDRSxhQUFBO0VDckVSO0VEMEVFO0lBQ0Usa0JBQUE7RUN4RUo7RUQrRWM7SUFDRSwwQkFBQTtJQUNBLFNBQUE7RUM3RWhCO0VEZ0ZjO0lBQ0UsMEJBQUE7RUM5RWhCO0VEaUZjO0lBQ0UsMEJBQUE7RUMvRWhCO0VEdUZJO0lBQ0Usc0JBQUE7SUFDQSxTQUFBO0VDckZOO0VEdUZNO0lBQ0UsV0FBQTtFQ3JGUjtBQUNGO0FEQ0EsdzNvQ0FBdzNvQyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFByb2Zlc3Npb25hbCBBZGQgUHJvZHVjdCBTdHlsZXMgKi9cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3Qge1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZmFmYyAwJSwgI2UyZThmMCAxMDAlKTtcbiAgXG4gIC5wcm9mZXNzaW9uYWwtaGVhZGVyIHtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UyZThmMDtcbiAgICBwYWRkaW5nOiAyNHB4IDMycHg7XG4gICAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICBcbiAgICAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gICAgICBtYXJnaW46IDAgYXV0bztcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgICAgIFxuICAgICAgLmhlYWRlci1pbmZvIHtcbiAgICAgICAgLmhlYWRlci10aXRsZSB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgZm9udC1zaXplOiAyOHB4O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgICAgICAgY29sb3I6ICMxZTI5M2I7XG4gICAgICAgICAgXG4gICAgICAgICAgLmhlYWRlci1pY29uIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMzJweDtcbiAgICAgICAgICAgIHdpZHRoOiAzMnB4O1xuICAgICAgICAgICAgaGVpZ2h0OiAzMnB4O1xuICAgICAgICAgICAgY29sb3I6ICMzZmE2ZjA7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAuaGVhZGVyLXN1YnRpdGxlIHtcbiAgICAgICAgICBtYXJnaW46IDhweCAwIDAgMDtcbiAgICAgICAgICBjb2xvcjogIzY0NzQ4YjtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDEycHg7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC5wcm9ncmVzcy1zdGVwcyB7XG4gICAgICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGdhcDogMTZweDtcbiAgICAgIFxuICAgICAgLnN0ZXAge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBnYXA6IDhweDtcbiAgICAgICAgXG4gICAgICAgIC5zdGVwLWNpcmNsZSB7XG4gICAgICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xuICAgICAgICAgIGNvbG9yOiAjNjQ3NDhiO1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICAgICAgXG4gICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgICAgICAgd2lkdGg6IDIwcHg7XG4gICAgICAgICAgICBoZWlnaHQ6IDIwcHg7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAuc3RlcC1sYWJlbCB7XG4gICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgIGNvbG9yOiAjNjQ3NDhiO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgIG1heC13aWR0aDogMTAwcHg7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgICYuYWN0aXZlIHtcbiAgICAgICAgICAuc3RlcC1jaXJjbGUge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNmYTZmMCAwJSwgIzFiN2JlNiAxMDAlKTtcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg2MywgMTY2LCAyNDAsIDAuMyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIC5zdGVwLWxhYmVsIHtcbiAgICAgICAgICAgIGNvbG9yOiAjM2ZhNmYwO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgICYuY29tcGxldGVkIHtcbiAgICAgICAgICAuc3RlcC1jaXJjbGUge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzEwYjk4MSAwJSwgIzA1OTY2OSAxMDAlKTtcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgLnN0ZXAtbGFiZWwge1xuICAgICAgICAgICAgY29sb3I6ICMxMGI5ODE7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAuc3RlcC1saW5lIHtcbiAgICAgICAgd2lkdGg6IDYwcHg7XG4gICAgICAgIGhlaWdodDogMnB4O1xuICAgICAgICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgICBcbiAgICAgICAgJi5jb21wbGV0ZWQge1xuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzEwYjk4MSAwJSwgIzA1OTY2OSAxMDAlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICBcbiAgLnByb2Zlc3Npb25hbC1jb250ZW50IHtcbiAgICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgICBtYXJnaW46IDAgYXV0bztcbiAgICBwYWRkaW5nOiAzMnB4O1xuICAgIFxuICAgIC5wcm9mZXNzaW9uYWwtZm9ybSB7XG4gICAgICAuZm9ybS1zdGVwIHtcbiAgICAgICAgLnN0ZXAtY29udGVudCB7XG4gICAgICAgICAgLnN0ZXAtdGl0bGUge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBnYXA6IDEycHg7XG4gICAgICAgICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XG4gICAgICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgY29sb3I6ICMxZTI5M2I7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIG1hdC1pY29uIHtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAyOHB4O1xuICAgICAgICAgICAgICB3aWR0aDogMjhweDtcbiAgICAgICAgICAgICAgaGVpZ2h0OiAyOHB4O1xuICAgICAgICAgICAgICBjb2xvcjogIzNmYTZmMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgLnByb2Zlc3Npb25hbC1jYXJkIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UyZThmMDtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC5jYXJkLWhlYWRlciB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGZhZmMgMCUsICNmMWY1ZjkgMTAwJSk7XG4gICAgICAgICAgICAgIHBhZGRpbmc6IDIwcHggMjRweDtcbiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMmU4ZjA7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICBoMyB7XG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICAgICAgY29sb3I6ICMxZTI5M2I7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHAge1xuICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICAgICAgY29sb3I6ICM2NDc0OGI7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLmNhcmQtY29udGVudCB7XG4gICAgICAgICAgICAgIHBhZGRpbmc6IDI0cHg7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAuZm9ybS1ncmlkIHtcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzAwcHgsIDFmcikpO1xuICAgICAgICAgICAgICAgIGdhcDogMjBweDtcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgLmZvcm0tZ3JvdXAge1xuICAgICAgICAgICAgICAgICYuZnVsbC13aWR0aCB7XG4gICAgICAgICAgICAgICAgICBncmlkLWNvbHVtbjogMSAvIC0xO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAucHJvZmVzc2lvbmFsLWlucHV0IHtcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAubWF0LW1kYy1mb3JtLWZpZWxkIHtcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmYWZjO1xuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZjA7XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2NiZDVlMTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMWY1Zjk7XG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICYubWRjLXRleHQtZmllbGQtLWZvY3VzZWQge1xuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjM2ZhNmYwO1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gICAgICAgICAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSg2MywgMTY2LCAyNDAsIDAuMSk7XG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC5vcHRpb24tY29udGVudCB7XG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgICAgICAgICAgIGdhcDogMnB4O1xuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICBzdHJvbmcge1xuICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzFlMjkzYjtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgc21hbGwge1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzY0NzQ4YjtcbiAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgLnByb2ZpdC1tYXJnaW4ge1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICBnYXA6IDhweDtcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2VjZmRmNSAwJSwgI2QxZmFlNSAxMDAlKTtcbiAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjYTdmM2QwO1xuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgICAgICAgICAgICBjb2xvcjogIzA2NWY0NjtcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDE2cHg7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgbWF0LWljb24ge1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICMxMGI5ODE7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAuc2V0dGluZ3MtZ3JpZCB7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgICAgICAgICAgICAgICBnYXA6IDE2cHg7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLnByb2Zlc3Npb25hbC1jaGVja2JveCB7XG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgICAgICAgICAgIGdhcDogNHB4O1xuICAgICAgICAgICAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmOGZhZmM7XG4gICAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmMWY1Zjk7XG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2NiZDVlMTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgLmNoZWNrYm94LWxhYmVsIHtcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxZTI5M2I7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHNtYWxsIHtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NDc0OGI7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIC5yZXZpZXctZ3JpZCB7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgICAgICAgICAgICAgICBnYXA6IDE2cHg7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLnJldmlldy1pdGVtIHtcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICAgICAgICAgICAgZ2FwOiA0cHg7XG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZmFmYztcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZjA7XG4gICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIGxhYmVsIHtcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NDc0OGI7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgICAgICAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICBzcGFuIHtcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxZTI5M2I7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgLm5hdmlnYXRpb24tYnV0dG9ucyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi10b3A6IDMycHg7XG4gICAgICBwYWRkaW5nOiAyNHB4O1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZjA7XG4gICAgfVxuICB9XG59XG5cbi8qIEFuaW1hdGlvbnMgKi9cbkBrZXlmcmFtZXMgc2xpZGVBbmltYXRpb24ge1xuICBmcm9tIHtcbiAgICBvcGFjaXR5OiAwO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgyMHB4KTtcbiAgfVxuICB0byB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XG4gIH1cbn1cblxuLyogUmVzcG9uc2l2ZSBEZXNpZ24gKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IHtcbiAgICAucHJvZmVzc2lvbmFsLWhlYWRlciB7XG4gICAgICBwYWRkaW5nOiAxNnB4IDIwcHg7XG4gICAgICBcbiAgICAgIC5oZWFkZXItY29udGVudCB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgICBnYXA6IDE2cHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgICAgIFxuICAgICAgICAuaGVhZGVyLWFjdGlvbnMge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIFxuICAgICAgICAgIC5wcm9mZXNzaW9uYWwtYnV0dG9uIHtcbiAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIC5wcm9ncmVzcy1zdGVwcyB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgXG4gICAgICAgIC5zdGVwIHtcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgICBcbiAgICAgICAgICAuc3RlcC1sYWJlbCB7XG4gICAgICAgICAgICBtYXgtd2lkdGg6IG5vbmU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAuc3RlcC1saW5lIHtcbiAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC5wcm9mZXNzaW9uYWwtY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAxNnB4IDIwcHg7XG4gICAgICBcbiAgICAgIC5wcm9mZXNzaW9uYWwtZm9ybSB7XG4gICAgICAgIC5mb3JtLXN0ZXAge1xuICAgICAgICAgIC5zdGVwLWNvbnRlbnQge1xuICAgICAgICAgICAgLnByb2Zlc3Npb25hbC1jYXJkIHtcbiAgICAgICAgICAgICAgLmNhcmQtY29udGVudCB7XG4gICAgICAgICAgICAgICAgLmZvcm0tZ3JpZCB7XG4gICAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgICAgICAgICAgIGdhcDogMTZweDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLnNldHRpbmdzLWdyaWQge1xuICAgICAgICAgICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC5yZXZpZXctZ3JpZCB7XG4gICAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgLm5hdmlnYXRpb24tYnV0dG9ucyB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgXG4gICAgICAgIC5wcm9mZXNzaW9uYWwtYnV0dG9uIHtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIiwiLyogUHJvZmVzc2lvbmFsIEFkZCBQcm9kdWN0IFN0eWxlcyAqL1xuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCB7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmYWZjIDAlLCAjZTJlOGYwIDEwMCUpO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UyZThmMDtcbiAgcGFkZGluZzogMjRweCAzMnB4O1xuICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAuaGVhZGVyLWNvbnRlbnQge1xuICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLmhlYWRlci1jb250ZW50IC5oZWFkZXItaW5mbyAuaGVhZGVyLXRpdGxlIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxMnB4O1xuICBtYXJnaW46IDA7XG4gIGZvbnQtc2l6ZTogMjhweDtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6ICMxZTI5M2I7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5oZWFkZXItY29udGVudCAuaGVhZGVyLWluZm8gLmhlYWRlci10aXRsZSAuaGVhZGVyLWljb24ge1xuICBmb250LXNpemU6IDMycHg7XG4gIHdpZHRoOiAzMnB4O1xuICBoZWlnaHQ6IDMycHg7XG4gIGNvbG9yOiAjM2ZhNmYwO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAuaGVhZGVyLWNvbnRlbnQgLmhlYWRlci1pbmZvIC5oZWFkZXItc3VidGl0bGUge1xuICBtYXJnaW46IDhweCAwIDAgMDtcbiAgY29sb3I6ICM2NDc0OGI7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgZm9udC13ZWlnaHQ6IDQwMDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLmhlYWRlci1jb250ZW50IC5oZWFkZXItYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMTJweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLnByb2dyZXNzLXN0ZXBzIHtcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAxNnB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLnByb2dyZXNzLXN0ZXBzIC5zdGVwIC5zdGVwLWNpcmNsZSB7XG4gIHdpZHRoOiA0MHB4O1xuICBoZWlnaHQ6IDQwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYmFja2dyb3VuZDogI2UyZThmMDtcbiAgY29sb3I6ICM2NDc0OGI7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAgLnN0ZXAtY2lyY2xlIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAyMHB4O1xuICB3aWR0aDogMjBweDtcbiAgaGVpZ2h0OiAyMHB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAgLnN0ZXAtbGFiZWwge1xuICBmb250LXNpemU6IDEycHg7XG4gIGNvbG9yOiAjNjQ3NDhiO1xuICBmb250LXdlaWdodDogNTAwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1heC13aWR0aDogMTAwcHg7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5wcm9ncmVzcy1zdGVwcyAuc3RlcC5hY3RpdmUgLnN0ZXAtY2lyY2xlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNmYTZmMCAwJSwgIzFiN2JlNiAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNjMsIDE2NiwgMjQwLCAwLjMpO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAuYWN0aXZlIC5zdGVwLWxhYmVsIHtcbiAgY29sb3I6ICMzZmE2ZjA7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5wcm9ncmVzcy1zdGVwcyAuc3RlcC5jb21wbGV0ZWQgLnN0ZXAtY2lyY2xlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzEwYjk4MSAwJSwgIzA1OTY2OSAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAuY29tcGxldGVkIC5zdGVwLWxhYmVsIHtcbiAgY29sb3I6ICMxMGI5ODE7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5wcm9ncmVzcy1zdGVwcyAuc3RlcC1saW5lIHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogMnB4O1xuICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAtbGluZS5jb21wbGV0ZWQge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMxMGI5ODEgMCUsICMwNTk2NjkgMTAwJSk7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCB7XG4gIG1heC13aWR0aDogMTIwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgcGFkZGluZzogMzJweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnN0ZXAtdGl0bGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIG1hcmdpbjogMCAwIDI0cHggMDtcbiAgZm9udC1zaXplOiAyNHB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzFlMjkzYjtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnN0ZXAtdGl0bGUgbWF0LWljb24ge1xuICBmb250LXNpemU6IDI4cHg7XG4gIHdpZHRoOiAyOHB4O1xuICBoZWlnaHQ6IDI4cHg7XG4gIGNvbG9yOiAjM2ZhNmYwO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4wNik7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZjA7XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZDpob3ZlciB7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1oZWFkZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmYWZjIDAlLCAjZjFmNWY5IDEwMCUpO1xuICBwYWRkaW5nOiAyMHB4IDI0cHg7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtaGVhZGVyIGgzIHtcbiAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6ICMxZTI5M2I7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1oZWFkZXIgcCB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBjb2xvcjogIzY0NzQ4Yjtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQge1xuICBwYWRkaW5nOiAyNHB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gIGdhcDogMjBweDtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLmZvcm0tZ3JpZDpsYXN0LWNoaWxkIHtcbiAgbWFyZ2luLWJvdHRvbTogMDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLmZvcm0tZ3JvdXAuZnVsbC13aWR0aCB7XG4gIGdyaWQtY29sdW1uOiAxLy0xO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAucHJvZmVzc2lvbmFsLWlucHV0IHtcbiAgd2lkdGg6IDEwMCU7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5mb3JtLWdyb3VwIC5wcm9mZXNzaW9uYWwtaW5wdXQgLm1hdC1tZGMtZm9ybS1maWVsZCB7XG4gIHdpZHRoOiAxMDAlO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAucHJvZmVzc2lvbmFsLWlucHV0IC5tYXQtbWRjLWZvcm0tZmllbGQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmYWZjO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAucHJvZmVzc2lvbmFsLWlucHV0IC5tYXQtbWRjLWZvcm0tZmllbGQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyOmhvdmVyIHtcbiAgYm9yZGVyLWNvbG9yOiAjY2JkNWUxO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmNWY5O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAucHJvZmVzc2lvbmFsLWlucHV0IC5tYXQtbWRjLWZvcm0tZmllbGQgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyLm1kYy10ZXh0LWZpZWxkLS1mb2N1c2VkIHtcbiAgYm9yZGVyLWNvbG9yOiAjM2ZhNmYwO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcbiAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoNjMsIDE2NiwgMjQwLCAwLjEpO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAub3B0aW9uLWNvbnRlbnQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDJweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLmZvcm0tZ3JvdXAgLm9wdGlvbi1jb250ZW50IHN0cm9uZyB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMWUyOTNiO1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAuZm9ybS1ncm91cCAub3B0aW9uLWNvbnRlbnQgc21hbGwge1xuICBjb2xvcjogIzY0NzQ4YjtcbiAgZm9udC1zaXplOiAxMnB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAucHJvZml0LW1hcmdpbiB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogOHB4O1xuICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlY2ZkZjUgMCUsICNkMWZhZTUgMTAwJSk7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNhN2YzZDA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgY29sb3I6ICMwNjVmNDY7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1hcmdpbi10b3A6IDE2cHg7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5wcm9maXQtbWFyZ2luIG1hdC1pY29uIHtcbiAgY29sb3I6ICMxMGI5ODE7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5zZXR0aW5ncy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XG4gIGdhcDogMTZweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLnNldHRpbmdzLWdyaWQgLnByb2Zlc3Npb25hbC1jaGVja2JveCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogNHB4O1xuICBwYWRkaW5nOiAxNnB4O1xuICBiYWNrZ3JvdW5kOiAjZjhmYWZjO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5zZXR0aW5ncy1ncmlkIC5wcm9mZXNzaW9uYWwtY2hlY2tib3g6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjZjFmNWY5O1xuICBib3JkZXItY29sb3I6ICNjYmQ1ZTE7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5zZXR0aW5ncy1ncmlkIC5wcm9mZXNzaW9uYWwtY2hlY2tib3ggLmNoZWNrYm94LWxhYmVsIHtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY29sb3I6ICMxZTI5M2I7XG59XG4ucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAucHJvZmVzc2lvbmFsLWZvcm0gLmZvcm0tc3RlcCAuc3RlcC1jb250ZW50IC5wcm9mZXNzaW9uYWwtY2FyZCAuY2FyZC1jb250ZW50IC5zZXR0aW5ncy1ncmlkIC5wcm9mZXNzaW9uYWwtY2hlY2tib3ggc21hbGwge1xuICBjb2xvcjogIzY0NzQ4YjtcbiAgZm9udC1zaXplOiAxMnB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAucmV2aWV3LWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgZ2FwOiAxNnB4O1xufVxuLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1mb3JtIC5mb3JtLXN0ZXAgLnN0ZXAtY29udGVudCAucHJvZmVzc2lvbmFsLWNhcmQgLmNhcmQtY29udGVudCAucmV2aWV3LWdyaWQgLnJldmlldy1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiA0cHg7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJhY2tncm91bmQ6ICNmOGZhZmM7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZjA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLnJldmlldy1ncmlkIC5yZXZpZXctaXRlbSBsYWJlbCB7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGNvbG9yOiAjNjQ3NDhiO1xuICBmb250LXNpemU6IDEycHg7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIGxldHRlci1zcGFjaW5nOiAwLjVweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLnJldmlldy1ncmlkIC5yZXZpZXctaXRlbSBzcGFuIHtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6ICMxZTI5M2I7XG4gIGZvbnQtc2l6ZTogMTZweDtcbn1cbi5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5uYXZpZ2F0aW9uLWJ1dHRvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi10b3A6IDMycHg7XG4gIHBhZGRpbmc6IDI0cHg7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4vKiBBbmltYXRpb25zICovXG5Aa2V5ZnJhbWVzIHNsaWRlQW5pbWF0aW9uIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMjBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDApO1xuICB9XG59XG4vKiBSZXNwb25zaXZlIERlc2lnbiAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIge1xuICAgIHBhZGRpbmc6IDE2cHggMjBweDtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5oZWFkZXItY29udGVudCB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICBnYXA6IDE2cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5oZWFkZXItY29udGVudCAuaGVhZGVyLWFjdGlvbnMge1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLmhlYWRlci1jb250ZW50IC5oZWFkZXItYWN0aW9ucyAucHJvZmVzc2lvbmFsLWJ1dHRvbiB7XG4gICAgZmxleDogMTtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtaGVhZGVyIC5wcm9ncmVzcy1zdGVwcyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDEycHg7XG4gIH1cbiAgLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAge1xuICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XG4gICAgZ2FwOiAxMnB4O1xuICB9XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1oZWFkZXIgLnByb2dyZXNzLXN0ZXBzIC5zdGVwIC5zdGVwLWxhYmVsIHtcbiAgICBtYXgtd2lkdGg6IG5vbmU7XG4gIH1cbiAgLnByb2Zlc3Npb25hbC1hZGQtcHJvZHVjdCAucHJvZmVzc2lvbmFsLWhlYWRlciAucHJvZ3Jlc3Mtc3RlcHMgLnN0ZXAtbGluZSB7XG4gICAgZGlzcGxheTogbm9uZTtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCB7XG4gICAgcGFkZGluZzogMTZweCAyMHB4O1xuICB9XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLmZvcm0tZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgZ2FwOiAxNnB4O1xuICB9XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLnNldHRpbmdzLWdyaWQge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICB9XG4gIC5wcm9mZXNzaW9uYWwtYWRkLXByb2R1Y3QgLnByb2Zlc3Npb25hbC1jb250ZW50IC5wcm9mZXNzaW9uYWwtZm9ybSAuZm9ybS1zdGVwIC5zdGVwLWNvbnRlbnQgLnByb2Zlc3Npb25hbC1jYXJkIC5jYXJkLWNvbnRlbnQgLnJldmlldy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAubmF2aWdhdGlvbi1idXR0b25zIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMTJweDtcbiAgfVxuICAucHJvZmVzc2lvbmFsLWFkZC1wcm9kdWN0IC5wcm9mZXNzaW9uYWwtY29udGVudCAubmF2aWdhdGlvbi1idXR0b25zIC5wcm9mZXNzaW9uYWwtYnV0dG9uIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgfVxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return AddProduct;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "Validators", "HttpClientModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatStepperModule", "MatSnackBarModule", "MatFormFieldModule", "MatProgressSpinnerModule", "MatChipsModule", "MatAutocompleteModule", "MatRadioModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "category_r3", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "nameAr", "unit_r4", "ɵɵtextInterpolate2", "symbol", "ɵɵelement", "ɵɵlistener", "AddProduct_div_49_Template_button_click_44_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "generateCode", "ɵɵtemplate", "AddProduct_div_49_mat_option_73_Template", "AddProduct_div_49_mat_option_81_Template", "tmp_1_0", "productForm", "get", "value", "categories", "units", "profitMargin", "AddProduct_div_50_div_32_Template", "supplier_r5", "supplierCode", "AddProduct_div_51_mat_option_18_Template", "suppliers", "ɵɵtextInterpolate", "tmp_2_0", "tmp_3_0", "tmp_4_0", "AddProduct_button_58_Template_button_click_0_listener", "_r6", "nextStep", "AddProduct_button_59_Template_button_click_0_listener", "_r7", "saveProduct", "isLoading", "AddProduct", "fb", "http", "router", "snackBar", "selectedSuppliers", "isGeneratingCode", "currentStep", "productTypes", "label", "description", "constructor", "group", "productType", "required", "productCode", "nameEn", "categoryId", "unitId", "purchasePrice", "min", "salePrice", "minStock", "maxStock", "currentStock", "mainSupplierId", "isActive", "isWeighted", "hasEx<PERSON>ry", "trackSerial", "ngOnInit", "loadCategories", "loadUnits", "loadSuppliers", "valueChanges", "subscribe", "type", "onProductTypeChange", "next", "response", "error", "console", "showMessage", "setValue", "disable", "enable", "generateProductCode", "post", "requiresManualInput", "message", "patchValue", "checkProductCode", "isAvailable", "setErrors", "addSupplier", "supplier", "find", "s", "push", "removeSupplier", "filter", "onSubmit", "valid", "formData", "map", "log", "setTimeout", "navigate", "cancel", "isError", "open", "duration", "horizontalPosition", "verticalPosition", "panelClass", "previousStep", "goBack", "calculateProfitMargin", "Math", "round", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "HttpClient", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "AddProduct_Template", "rf", "ctx", "AddProduct_Template_button_click_11_listener", "AddProduct_Template_button_click_15_listener", "AddProduct_mat_icon_22_Template", "AddProduct_span_23_Template", "AddProduct_mat_icon_29_Template", "AddProduct_span_30_Template", "AddProduct_mat_icon_36_Template", "AddProduct_span_37_Template", "AddProduct_div_49_Template", "AddProduct_div_50_Template", "AddProduct_div_51_Template", "AddProduct_div_52_Template", "AddProduct_Template_button_click_54_listener", "AddProduct_button_58_Template", "AddProduct_button_59_Template", "AddProduct_div_60_Template", "ɵɵclassProp", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "FormGroupDirective", "FormControlName", "i6", "MatButton", "MatIconButton", "i7", "MatIcon", "i8", "MatInput", "i9", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i10", "MatSelect", "MatOption", "i11", "MatCheckbox", "i12", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\add-product\\add-product.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\add-product\\add-product-new.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { HttpClient, HttpClientModule } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatRadioModule } from '@angular/material/radio';\n\ninterface Category {\n  id: number;\n  nameAr: string;\n  nameEn: string;\n  code: string;\n}\n\ninterface Unit {\n  id: number;\n  nameAr: string;\n  nameEn: string;\n  symbol: string;\n}\n\ninterface Supplier {\n  id: number;\n  nameAr: string;\n  nameEn: string;\n  supplierCode: string;\n  phone: string;\n  email: string;\n}\n\n@Component({\n  selector: 'app-add-product',\n  standalone: true,\n  // animations: [\n  //   trigger('slideAnimation', [\n  //     transition(':enter', [\n  //       style({ opacity: 0, transform: 'translateX(20px)' }),\n  //       animate('300ms ease-in', style({ opacity: 1, transform: 'translateX(0)' }))\n  //     ])\n  //   ])\n  // ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatInputModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatStepperModule,\n    MatSnackBarModule,\n    MatChipsModule,\n    MatAutocompleteModule,\n    MatRadioModule,\n    MatFormFieldModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './add-product-new.html',\n  styleUrls: ['./add-product-professional.scss']\n})\nexport class AddProduct implements OnInit {\n  productForm: FormGroup;\n  categories: Category[] = [];\n  units: Unit[] = [];\n  suppliers: Supplier[] = [];\n  selectedSuppliers: Supplier[] = [];\n  \n  isLoading: boolean = false;\n  isGeneratingCode: boolean = false;\n  currentStep: number = 1;\n  profitMargin: number | null = null;\n  \n  productTypes = [\n    { value: 'محلي', label: 'محلي', description: 'منتج محلي - كود تلقائي يبدأ من 2000000000001' },\n    { value: 'دولي', label: 'دولي', description: 'منتج دولي - إدخال الكود يدوياً' },\n    { value: 'موزون', label: 'موزون', description: 'منتج موزون - كود تلقائي للميزان' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private http: HttpClient,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.productForm = this.fb.group({\n      // معلومات أساسية\n      productType: ['محلي', Validators.required],\n      productCode: ['', Validators.required],\n      nameAr: ['', Validators.required],\n      nameEn: [''],\n      description: [''],\n      \n      // التصنيف والوحدة\n      categoryId: ['', Validators.required],\n      unitId: ['', Validators.required],\n      \n      // الأسعار والمخزون\n      purchasePrice: [0, [Validators.required, Validators.min(0)]],\n      salePrice: [0, [Validators.required, Validators.min(0)]],\n      minStock: [0, [Validators.min(0)]],\n      maxStock: [0, [Validators.min(0)]],\n      currentStock: [0, [Validators.min(0)]],\n      \n      // المورد الرئيسي\n      mainSupplierId: ['', Validators.required],\n      \n      // إعدادات\n      isActive: [true],\n      isWeighted: [false],\n      hasExpiry: [false],\n      trackSerial: [false]\n    });\n  }\n\n  ngOnInit() {\n    this.loadCategories();\n    this.loadUnits();\n    this.loadSuppliers();\n    \n    // مراقبة تغيير نوع المنتج\n    this.productForm.get('productType')?.valueChanges.subscribe(type => {\n      this.onProductTypeChange(type);\n    });\n  }\n\n  loadCategories() {\n    this.http.get<any>('http://localhost:5000/api/simple/categories').subscribe({\n      next: (response) => {\n        this.categories = response.categories || [];\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n        this.showMessage('خطأ في تحميل الفئات');\n      }\n    });\n  }\n\n  loadUnits() {\n    this.http.get<any>('http://localhost:5000/api/simple/units').subscribe({\n      next: (response) => {\n        this.units = response.units || [];\n      },\n      error: (error) => {\n        console.error('Error loading units:', error);\n        this.showMessage('خطأ في تحميل الوحدات');\n      }\n    });\n  }\n\n  loadSuppliers() {\n    this.http.get<any>('http://localhost:5000/api/simple/suppliers').subscribe({\n      next: (response) => {\n        this.suppliers = response.suppliers || [];\n      },\n      error: (error) => {\n        console.error('Error loading suppliers:', error);\n        this.showMessage('خطأ في تحميل الموردين');\n      }\n    });\n  }\n\n  onProductTypeChange(type: string) {\n    if (type === 'موزون') {\n      this.productForm.get('isWeighted')?.setValue(true);\n      this.productForm.get('isWeighted')?.disable();\n    } else {\n      this.productForm.get('isWeighted')?.setValue(false);\n      this.productForm.get('isWeighted')?.enable();\n    }\n\n    // تحديد إمكانية تعديل الكود\n    if (type === 'دولي') {\n      this.productForm.get('productCode')?.enable();\n    } else {\n      this.productForm.get('productCode')?.disable();\n    }\n\n    // مسح الكود الحالي عند تغيير النوع\n    this.productForm.get('productCode')?.setValue('');\n  }\n\n  generateProductCode() {\n    const productType = this.productForm.get('productType')?.value;\n    \n    if (!productType) {\n      this.showMessage('يرجى اختيار نوع المنتج أولاً');\n      return;\n    }\n\n    if (productType === 'دولي') {\n      this.showMessage('للمنتجات الدولية، يرجى إدخال الكود يدوياً');\n      return;\n    }\n\n    this.isGeneratingCode = true;\n    \n    this.http.post<any>('http://localhost:5000/api/simple/generate-product-code', {\n      productType: productType\n    }).subscribe({\n      next: (response) => {\n        if (response.requiresManualInput) {\n          this.showMessage(response.message);\n        } else {\n          this.productForm.patchValue({ productCode: response.productCode });\n          this.showMessage(`تم توليد الكود: ${response.productCode}`);\n        }\n        this.isGeneratingCode = false;\n      },\n      error: (error) => {\n        console.error('Error generating code:', error);\n        this.showMessage('خطأ في توليد الكود');\n        this.isGeneratingCode = false;\n      }\n    });\n  }\n\n  checkProductCode() {\n    const productCode = this.productForm.get('productCode')?.value;\n    \n    if (!productCode) {\n      return;\n    }\n\n    this.http.post<any>('http://localhost:5000/api/simple/check-product-code', {\n      productCode: productCode\n    }).subscribe({\n      next: (response) => {\n        if (!response.isAvailable) {\n          this.showMessage('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر', true);\n          this.productForm.get('productCode')?.setErrors({ 'duplicate': true });\n        } else {\n          this.productForm.get('productCode')?.setErrors(null);\n        }\n      },\n      error: (error) => {\n        console.error('Error checking code:', error);\n      }\n    });\n  }\n\n  addSupplier(supplier: Supplier) {\n    if (!this.selectedSuppliers.find(s => s.id === supplier.id)) {\n      this.selectedSuppliers.push(supplier);\n    }\n  }\n\n  removeSupplier(supplier: Supplier) {\n    this.selectedSuppliers = this.selectedSuppliers.filter(s => s.id !== supplier.id);\n  }\n\n  onSubmit() {\n    if (this.productForm.valid) {\n      this.isLoading = true;\n      \n      const formData = {\n        ...this.productForm.value,\n        suppliers: this.selectedSuppliers.map(s => s.id)\n      };\n\n      // هنا سيتم إرسال البيانات لـ API\n      console.log('Product Data:', formData);\n      \n      // محاكاة حفظ البيانات\n      setTimeout(() => {\n        this.isLoading = false;\n        this.showMessage('تم إضافة المنتج بنجاح');\n        this.router.navigate(['/products']);\n      }, 2000);\n    } else {\n      this.showMessage('يرجى ملء جميع الحقول المطلوبة', true);\n    }\n  }\n\n  cancel() {\n    this.router.navigate(['/products']);\n  }\n\n  private showMessage(message: string, isError = false) {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      horizontalPosition: 'center',\n      verticalPosition: 'top',\n      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\n    });\n  }\n\n  // Navigation Methods for Professional UI\n  nextStep(): void {\n    if (this.currentStep < 4) {\n      this.currentStep++;\n    }\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  goBack(): void {\n    // Navigate back to products page\n    this.router.navigate(['/products']);\n  }\n\n  // Calculate profit margin\n  calculateProfitMargin(): void {\n    const purchasePrice = this.productForm.get('purchasePrice')?.value;\n    const salePrice = this.productForm.get('salePrice')?.value;\n\n    if (purchasePrice && salePrice && purchasePrice > 0) {\n      this.profitMargin = Math.round(((salePrice - purchasePrice) / purchasePrice) * 100);\n    } else {\n      this.profitMargin = null;\n    }\n  }\n\n  // Generate product code\n  generateCode(): void {\n    this.generateProductCode();\n  }\n\n  // Save product\n  saveProduct(): void {\n    if (this.productForm.valid) {\n      this.onSubmit();\n    } else {\n      this.snackBar.open('يرجى ملء جميع الحقول المطلوبة', 'إغلاق', {\n        duration: 3000,\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n  }\n}\n", "<!-- Professional Add Product Page -->\n<div class=\"professional-add-product\">\n  <!-- Professional Header -->\n  <div class=\"professional-header animate-slide-up\">\n    <div class=\"header-content\">\n      <div class=\"header-info\">\n        <h1 class=\"header-title\">\n          <mat-icon class=\"header-icon\">add_business</mat-icon>\n          إضافة منتج جديد\n        </h1>\n        <p class=\"header-subtitle\">إنشاء منتج جديد وإضافته إلى نظام المخزون بطريقة احترافية</p>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-stroked-button class=\"professional-button secondary\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n          العودة للمنتجات\n        </button>\n        <button mat-raised-button class=\"professional-button primary\" (click)=\"saveProduct()\" [disabled]=\"isLoading\">\n          <mat-icon>save</mat-icon>\n          حفظ المنتج\n        </button>\n      </div>\n    </div>\n    \n    <!-- Progress Steps -->\n    <div class=\"progress-steps\">\n      <div class=\"step\" [class.active]=\"currentStep >= 1\" [class.completed]=\"currentStep > 1\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"currentStep > 1\">check</mat-icon>\n          <span *ngIf=\"currentStep <= 1\">1</span>\n        </div>\n        <span class=\"step-label\">المعلومات الأساسية</span>\n      </div>\n      <div class=\"step-line\" [class.completed]=\"currentStep > 1\"></div>\n      <div class=\"step\" [class.active]=\"currentStep >= 2\" [class.completed]=\"currentStep > 2\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"currentStep > 2\">check</mat-icon>\n          <span *ngIf=\"currentStep <= 2\">2</span>\n        </div>\n        <span class=\"step-label\">التسعير والمخزون</span>\n      </div>\n      <div class=\"step-line\" [class.completed]=\"currentStep > 2\"></div>\n      <div class=\"step\" [class.active]=\"currentStep >= 3\" [class.completed]=\"currentStep > 3\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"currentStep > 3\">check</mat-icon>\n          <span *ngIf=\"currentStep <= 3\">3</span>\n        </div>\n        <span class=\"step-label\">الموردين والإعدادات</span>\n      </div>\n      <div class=\"step-line\" [class.completed]=\"currentStep > 3\"></div>\n      <div class=\"step\" [class.active]=\"currentStep >= 4\">\n        <div class=\"step-circle\">\n          <span>4</span>\n        </div>\n        <span class=\"step-label\">المراجعة والحفظ</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <div class=\"professional-content\">\n    <form [formGroup]=\"productForm\" class=\"professional-form\">\n      \n      <!-- Step 1: Basic Information -->\n      <div class=\"form-step\" *ngIf=\"currentStep === 1\">\n        <div class=\"step-content\">\n          <h2 class=\"step-title\">\n            <mat-icon>info</mat-icon>\n            المعلومات الأساسية\n          </h2>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>تفاصيل المنتج</h3>\n              <p>أدخل المعلومات الأساسية للمنتج</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <!-- Product Type & Code -->\n              <div class=\"form-grid\">\n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>نوع المنتج</mat-label>\n                    <mat-select formControlName=\"productType\" required>\n                      <mat-option value=\"محلي\">\n                        <div class=\"option-content\">\n                          <strong>منتج محلي</strong>\n                          <small>يبدأ من 2000000000001</small>\n                        </div>\n                      </mat-option>\n                      <mat-option value=\"دولي\">\n                        <div class=\"option-content\">\n                          <strong>منتج دولي</strong>\n                          <small>كود يدوي</small>\n                        </div>\n                      </mat-option>\n                      <mat-option value=\"موزون\">\n                        <div class=\"option-content\">\n                          <strong>منتج موزون</strong>\n                          <small>للميزان الإلكتروني</small>\n                        </div>\n                      </mat-option>\n                    </mat-select>\n                    <mat-error>نوع المنتج مطلوب</mat-error>\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>كود المنتج</mat-label>\n                    <input matInput formControlName=\"productCode\" placeholder=\"كود المنتج\">\n                    <button mat-icon-button matSuffix type=\"button\" (click)=\"generateCode()\" \n                            [disabled]=\"productForm.get('productType')?.value === 'دولي'\">\n                      <mat-icon>auto_awesome</mat-icon>\n                    </button>\n                    <mat-error>كود المنتج مطلوب</mat-error>\n                  </mat-form-field>\n                </div>\n              </div>\n              \n              <!-- Product Names -->\n              <div class=\"form-grid\">\n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>اسم المنتج بالعربية</mat-label>\n                    <input matInput formControlName=\"nameAr\" placeholder=\"اسم المنتج بالعربية\" required>\n                    <mat-error>اسم المنتج بالعربية مطلوب</mat-error>\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>اسم المنتج بالإنجليزية</mat-label>\n                    <input matInput formControlName=\"nameEn\" placeholder=\"Product Name in English\">\n                  </mat-form-field>\n                </div>\n              </div>\n              \n              <!-- Description -->\n              <div class=\"form-group full-width\">\n                <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                  <mat-label>وصف المنتج</mat-label>\n                  <textarea matInput formControlName=\"description\" rows=\"3\" \n                           placeholder=\"وصف تفصيلي للمنتج\"></textarea>\n                </mat-form-field>\n              </div>\n              \n              <!-- Category & Unit -->\n              <div class=\"form-grid\">\n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>الفئة</mat-label>\n                    <mat-select formControlName=\"categoryId\" required>\n                      <mat-option *ngFor=\"let category of categories\" [value]=\"category.id\">\n                        {{ category.nameAr }}\n                      </mat-option>\n                    </mat-select>\n                    <mat-error>الفئة مطلوبة</mat-error>\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>وحدة القياس</mat-label>\n                    <mat-select formControlName=\"unitId\" required>\n                      <mat-option *ngFor=\"let unit of units\" [value]=\"unit.id\">\n                        {{ unit.nameAr }} ({{ unit.symbol }})\n                      </mat-option>\n                    </mat-select>\n                    <mat-error>وحدة القياس مطلوبة</mat-error>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Step 2: Pricing & Inventory -->\n      <div class=\"form-step\" *ngIf=\"currentStep === 2\">\n        <div class=\"step-content\">\n          <h2 class=\"step-title\">\n            <mat-icon>attach_money</mat-icon>\n            التسعير والمخزون\n          </h2>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>الأسعار</h3>\n              <p>حدد أسعار الشراء والبيع</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <div class=\"form-grid\">\n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>سعر الشراء</mat-label>\n                    <input matInput type=\"number\" formControlName=\"purchasePrice\" \n                           placeholder=\"0.00\" min=\"0\" step=\"0.01\">\n                    <span matSuffix>جنيه</span>\n                    <mat-error>سعر الشراء مطلوب</mat-error>\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>سعر البيع</mat-label>\n                    <input matInput type=\"number\" formControlName=\"salePrice\" \n                           placeholder=\"0.00\" min=\"0\" step=\"0.01\">\n                    <span matSuffix>جنيه</span>\n                    <mat-error>سعر البيع مطلوب</mat-error>\n                  </mat-form-field>\n                </div>\n              </div>\n              \n              <!-- Profit Margin Display -->\n              <div class=\"profit-margin\" *ngIf=\"profitMargin\">\n                <mat-icon>trending_up</mat-icon>\n                <span>هامش الربح: {{ profitMargin }}%</span>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>إدارة المخزون</h3>\n              <p>حدد مستويات المخزون</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <div class=\"form-grid\">\n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>المخزون الحالي</mat-label>\n                    <input matInput type=\"number\" formControlName=\"currentStock\" \n                           placeholder=\"0\" min=\"0\">\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>الحد الأدنى</mat-label>\n                    <input matInput type=\"number\" formControlName=\"minStock\" \n                           placeholder=\"0\" min=\"0\">\n                  </mat-form-field>\n                </div>\n                \n                <div class=\"form-group\">\n                  <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                    <mat-label>الحد الأقصى</mat-label>\n                    <input matInput type=\"number\" formControlName=\"maxStock\" \n                           placeholder=\"0\" min=\"0\">\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Step 3: Suppliers & Settings -->\n      <div class=\"form-step\" *ngIf=\"currentStep === 3\">\n        <div class=\"step-content\">\n          <h2 class=\"step-title\">\n            <mat-icon>business</mat-icon>\n            الموردين والإعدادات\n          </h2>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>الموردين</h3>\n              <p>اختر المورد الرئيسي والموردين الإضافيين</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <div class=\"form-group\">\n                <mat-form-field appearance=\"outline\" class=\"professional-input\">\n                  <mat-label>المورد الرئيسي</mat-label>\n                  <mat-select formControlName=\"mainSupplierId\" required>\n                    <mat-option *ngFor=\"let supplier of suppliers\" [value]=\"supplier.id\">\n                      {{ supplier.nameAr }} - {{ supplier.supplierCode }}\n                    </mat-option>\n                  </mat-select>\n                  <mat-error>المورد الرئيسي مطلوب</mat-error>\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>إعدادات المنتج</h3>\n              <p>خصائص إضافية للمنتج</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <div class=\"settings-grid\">\n                <mat-checkbox formControlName=\"isActive\" class=\"professional-checkbox\">\n                  <span class=\"checkbox-label\">منتج نشط</span>\n                  <small>المنتج متاح للبيع</small>\n                </mat-checkbox>\n                \n                <mat-checkbox formControlName=\"isWeighted\" class=\"professional-checkbox\">\n                  <span class=\"checkbox-label\">منتج موزون</span>\n                  <small>يباع بالوزن</small>\n                </mat-checkbox>\n                \n                <mat-checkbox formControlName=\"hasExpiry\" class=\"professional-checkbox\">\n                  <span class=\"checkbox-label\">له تاريخ انتهاء</span>\n                  <small>يتطلب تتبع الصلاحية</small>\n                </mat-checkbox>\n                \n                <mat-checkbox formControlName=\"trackSerial\" class=\"professional-checkbox\">\n                  <span class=\"checkbox-label\">تتبع الأرقام التسلسلية</span>\n                  <small>لكل قطعة رقم مميز</small>\n                </mat-checkbox>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Step 4: Review -->\n      <div class=\"form-step\" *ngIf=\"currentStep === 4\">\n        <div class=\"step-content\">\n          <h2 class=\"step-title\">\n            <mat-icon>preview</mat-icon>\n            مراجعة البيانات\n          </h2>\n          \n          <div class=\"professional-card\">\n            <div class=\"card-header\">\n              <h3>ملخص المنتج</h3>\n              <p>راجع جميع البيانات قبل الحفظ</p>\n            </div>\n            \n            <div class=\"card-content\">\n              <div class=\"review-grid\">\n                <div class=\"review-item\">\n                  <label>اسم المنتج:</label>\n                  <span>{{ productForm.get('nameAr')?.value }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>كود المنتج:</label>\n                  <span>{{ productForm.get('productCode')?.value }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>نوع المنتج:</label>\n                  <span>{{ productForm.get('productType')?.value }}</span>\n                </div>\n                <div class=\"review-item\">\n                  <label>سعر البيع:</label>\n                  <span>{{ productForm.get('salePrice')?.value }} جنيه</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </form>\n    \n    <!-- Navigation Buttons -->\n    <div class=\"navigation-buttons\">\n      <button mat-stroked-button class=\"professional-button secondary\" \n              (click)=\"previousStep()\" [disabled]=\"currentStep === 1\">\n        <mat-icon>navigate_before</mat-icon>\n        السابق\n      </button>\n      \n      <button mat-raised-button class=\"professional-button primary\" \n              (click)=\"nextStep()\" *ngIf=\"currentStep < 4\">\n        التالي\n        <mat-icon>navigate_next</mat-icon>\n      </button>\n      \n      <button mat-raised-button class=\"professional-button primary\" \n              (click)=\"saveProduct()\" *ngIf=\"currentStep === 4\" [disabled]=\"isLoading\">\n        <mat-icon>save</mat-icon>\n        حفظ المنتج\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"loading-overlay\">\n  <div class=\"loading-content\">\n    <mat-progress-spinner diameter=\"50\"></mat-progress-spinner>\n    <p class=\"loading-text\">جاري حفظ المنتج...</p>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACrG,SAAqBC,gBAAgB,QAAQ,sBAAsB;AAEnE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;;;;;ICU9CC,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOvCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOvCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA4G3BH,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAAC,EAAA,CAAqB;IACnEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,WAAA,CAAAI,MAAA,MACF;;;;;IAUAT,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,EAAA,CAAiB;IACtDN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAW,kBAAA,MAAAD,OAAA,CAAAD,MAAA,QAAAC,OAAA,CAAAE,MAAA,OACF;;;;;;IApGVZ,EAHN,CAAAC,cAAA,cAAiD,cACrB,aACD,eACX;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIDH,EAFJ,CAAAC,cAAA,cAA+B,cACJ,SACnB;IAAAD,EAAA,CAAAE,MAAA,gFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6KAA8B;IACnCF,EADmC,CAAAG,YAAA,EAAI,EACjC;IAOEH,EALR,CAAAC,cAAA,eAA0B,eAED,eACG,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAI3BH,EAHN,CAAAC,cAAA,sBAAmD,sBACxB,eACK,cAClB;IAAAD,EAAA,CAAAE,MAAA,yDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1BH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,2DAAqB;IAEhCF,EAFgC,CAAAG,YAAA,EAAQ,EAChC,EACK;IAGTH,EAFJ,CAAAC,cAAA,sBAAyB,eACK,cAClB;IAAAD,EAAA,CAAAE,MAAA,yDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1BH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,mDAAQ;IAEnBF,EAFmB,CAAAG,YAAA,EAAQ,EACnB,EACK;IAGTH,EAFJ,CAAAC,cAAA,sBAA0B,eACI,cAClB;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3BH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,+GAAkB;IAG/BF,EAH+B,CAAAG,YAAA,EAAQ,EAC7B,EACK,EACF;IACbH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,8FAAgB;IAE/BF,EAF+B,CAAAG,YAAA,EAAY,EACxB,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAa,SAAA,iBAAuE;IACvEb,EAAA,CAAAC,cAAA,kBACsE;IADtBD,EAAA,CAAAc,UAAA,mBAAAC,oDAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAEtErB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IACxBF,EADwB,CAAAG,YAAA,EAAW,EAC1B;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,8FAAgB;IAGjCF,EAHiC,CAAAG,YAAA,EAAY,EACxB,EACb,EACF;IAMAH,EAHN,CAAAC,cAAA,eAAuB,eACG,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,gHAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAa,SAAA,iBAAoF;IACpFb,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,+IAAyB;IAExCF,EAFwC,CAAAG,YAAA,EAAY,EACjC,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,kIAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7CH,EAAA,CAAAa,SAAA,iBAA+E;IAGrFb,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAmC,0BAC+B,iBACnD;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAa,SAAA,oBACoD;IAExDb,EADE,CAAAG,YAAA,EAAiB,EACb;IAMAH,EAHN,CAAAC,cAAA,eAAuB,eACG,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAC,cAAA,sBAAkD;IAChDD,EAAA,CAAAsB,UAAA,KAAAC,wCAAA,yBAAsE;IAGxEvB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,2EAAY;IAE3BF,EAF2B,CAAAG,YAAA,EAAY,EACpB,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAC,cAAA,sBAA8C;IAC5CD,EAAA,CAAAsB,UAAA,KAAAE,wCAAA,yBAAyD;IAG3DxB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,0GAAkB;IAO3CF,EAP2C,CAAAG,YAAA,EAAY,EAC1B,EACb,EACF,EACF,EACF,EACF,EACF;;;;;IAhEgBH,EAAA,CAAAO,SAAA,IAA6D;IAA7DP,EAAA,CAAAI,UAAA,eAAAqB,OAAA,GAAAP,MAAA,CAAAQ,WAAA,CAAAC,GAAA,kCAAAF,OAAA,CAAAG,KAAA,iCAA6D;IAyClC5B,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAW,UAAA,CAAa;IAYjB7B,EAAA,CAAAO,SAAA,GAAQ;IAARP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAY,KAAA,CAAQ;;;;;IAoD3C9B,EADF,CAAAC,cAAA,cAAgD,eACpC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACxC;;;;IADEH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAQ,kBAAA,8DAAAU,MAAA,CAAAa,YAAA,MAA+B;;;;;IApCzC/B,EAHN,CAAAC,cAAA,cAAiD,cACrB,aACD,eACX;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,oGACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIDH,EAFJ,CAAAC,cAAA,cAA+B,cACJ,SACnB;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,mIAAuB;IAC5BF,EAD4B,CAAAG,YAAA,EAAI,EAC1B;IAMEH,EAJR,CAAAC,cAAA,eAA0B,eACD,eACG,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAa,SAAA,iBAC8C;IAC9Cb,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,8FAAgB;IAE/BF,EAF+B,CAAAG,YAAA,EAAY,EACxB,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,yDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAa,SAAA,iBAC8C;IAC9Cb,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,wFAAe;IAGhCF,EAHgC,CAAAG,YAAA,EAAY,EACvB,EACb,EACF;IAGNH,EAAA,CAAAsB,UAAA,KAAAU,iCAAA,kBAAgD;IAKpDhC,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA+B,eACJ,UACnB;IAAAD,EAAA,CAAAE,MAAA,iFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,gHAAmB;IACxBF,EADwB,CAAAG,YAAA,EAAI,EACtB;IAMEH,EAJR,CAAAC,cAAA,eAA0B,eACD,eACG,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAa,SAAA,iBAC+B;IAEnCb,EADE,CAAAG,YAAA,EAAiB,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAa,SAAA,iBAC+B;IAEnCb,EADE,CAAAG,YAAA,EAAiB,EACb;IAIFH,EAFJ,CAAAC,cAAA,eAAwB,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAa,SAAA,iBAC+B;IAO7Cb,EANY,CAAAG,YAAA,EAAiB,EACb,EACF,EACF,EACF,EACF,EACF;;;;IA1C8BH,EAAA,CAAAO,SAAA,IAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAc,MAAA,CAAAa,YAAA,CAAkB;;;;;IA+DxC/B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAA6B,WAAA,CAAA3B,EAAA,CAAqB;IAClEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAW,kBAAA,MAAAsB,WAAA,CAAAxB,MAAA,SAAAwB,WAAA,CAAAC,YAAA,MACF;;;;;IAjBRlC,EAHN,CAAAC,cAAA,cAAiD,cACrB,aACD,eACX;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,sHACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIDH,EAFJ,CAAAC,cAAA,cAA+B,cACJ,SACnB;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,8NAAuC;IAC5CF,EAD4C,CAAAG,YAAA,EAAI,EAC1C;IAKAH,EAHN,CAAAC,cAAA,eAA0B,eACA,0BAC0C,iBACnD;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAC,cAAA,sBAAsD;IACpDD,EAAA,CAAAsB,UAAA,KAAAa,wCAAA,yBAAqE;IAGvEnC,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,sHAAoB;IAIvCF,EAJuC,CAAAG,YAAA,EAAY,EAC5B,EACb,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA+B,eACJ,UACnB;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,gHAAmB;IACxBF,EADwB,CAAAG,YAAA,EAAI,EACtB;IAKAH,EAHN,CAAAC,cAAA,eAA0B,eACG,wBAC8C,gBACxC;IAAAD,EAAA,CAAAE,MAAA,mDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oGAAiB;IAC1BF,EAD0B,CAAAG,YAAA,EAAQ,EACnB;IAGbH,EADF,CAAAC,cAAA,wBAAyE,gBAC1C;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IACpBF,EADoB,CAAAG,YAAA,EAAQ,EACb;IAGbH,EADF,CAAAC,cAAA,wBAAwE,gBACzC;IAAAD,EAAA,CAAAE,MAAA,wFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,gHAAmB;IAC5BF,EAD4B,CAAAG,YAAA,EAAQ,EACrB;IAGbH,EADF,CAAAC,cAAA,wBAA0E,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,kIAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,+FAAiB;IAMpCF,EANoC,CAAAG,YAAA,EAAQ,EACnB,EACX,EACF,EACF,EACF,EACF;;;;IAzCyCH,EAAA,CAAAO,SAAA,IAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAkB,SAAA,CAAY;;;;;IA+CrDpC,EAHN,CAAAC,cAAA,cAAiD,cACrB,aACD,eACX;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,8FACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIDH,EAFJ,CAAAC,cAAA,cAA+B,cACJ,SACnB;IAAAD,EAAA,CAAAE,MAAA,oEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,4JAA4B;IACjCF,EADiC,CAAAG,YAAA,EAAI,EAC/B;IAKAH,EAHN,CAAAC,cAAA,eAA0B,eACC,eACE,aAChB;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,0DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAMhEF,EANgE,CAAAG,YAAA,EAAO,EACvD,EACF,EACF,EACF,EACF,EACF;;;;;;;;IAlBYH,EAAA,CAAAO,SAAA,IAAsC;IAAtCP,EAAA,CAAAqC,iBAAA,EAAAZ,OAAA,GAAAP,MAAA,CAAAQ,WAAA,CAAAC,GAAA,6BAAAF,OAAA,CAAAG,KAAA,CAAsC;IAItC5B,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAqC,iBAAA,EAAAC,OAAA,GAAApB,MAAA,CAAAQ,WAAA,CAAAC,GAAA,kCAAAW,OAAA,CAAAV,KAAA,CAA2C;IAI3C5B,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAqC,iBAAA,EAAAE,OAAA,GAAArB,MAAA,CAAAQ,WAAA,CAAAC,GAAA,kCAAAY,OAAA,CAAAX,KAAA,CAA2C;IAI3C5B,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAQ,kBAAA,MAAAgC,OAAA,GAAAtB,MAAA,CAAAQ,WAAA,CAAAC,GAAA,gCAAAa,OAAA,CAAAZ,KAAA,8BAA8C;;;;;;IAiBhE5B,EAAA,CAAAC,cAAA,iBACqD;IAA7CD,EAAA,CAAAc,UAAA,mBAAA2B,sDAAA;MAAAzC,EAAA,CAAAgB,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAyB,QAAA,EAAU;IAAA,EAAC;IAC1B3C,EAAA,CAAAE,MAAA,6CACA;IAAAF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IACzBF,EADyB,CAAAG,YAAA,EAAW,EAC3B;;;;;;IAETH,EAAA,CAAAC,cAAA,gBACiF;IAAzED,EAAA,CAAAc,UAAA,mBAAA8B,sDAAA;MAAA5C,EAAA,CAAAgB,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA4B,WAAA,EAAa;IAAA,EAAC;IAC7B9C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,gEACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHiDH,EAAA,CAAAI,UAAA,aAAAc,MAAA,CAAA6B,SAAA,CAAsB;;;;;IAUpF/C,EADF,CAAAC,cAAA,cAA+C,cAChB;IAC3BD,EAAA,CAAAa,SAAA,+BAA2D;IAC3Db,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,0FAAkB;IAE9CF,EAF8C,CAAAG,YAAA,EAAI,EAC1C,EACF;;;AD1TN,WAAa6C,UAAU;EAAjB,MAAOA,UAAU;IAmBXC,EAAA;IACAC,IAAA;IACAC,MAAA;IACAC,QAAA;IArBV1B,WAAW;IACXG,UAAU,GAAe,EAAE;IAC3BC,KAAK,GAAW,EAAE;IAClBM,SAAS,GAAe,EAAE;IAC1BiB,iBAAiB,GAAe,EAAE;IAElCN,SAAS,GAAY,KAAK;IAC1BO,gBAAgB,GAAY,KAAK;IACjCC,WAAW,GAAW,CAAC;IACvBxB,YAAY,GAAkB,IAAI;IAElCyB,YAAY,GAAG,CACb;MAAE5B,KAAK,EAAE,MAAM;MAAE6B,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAA8C,CAAE,EAC7F;MAAE9B,KAAK,EAAE,MAAM;MAAE6B,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAgC,CAAE,EAC/E;MAAE9B,KAAK,EAAE,OAAO;MAAE6B,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAiC,CAAE,CACnF;IAEDC,YACUV,EAAe,EACfC,IAAgB,EAChBC,MAAc,EACdC,QAAqB;MAHrB,KAAAH,EAAE,GAAFA,EAAE;MACF,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,QAAQ,GAARA,QAAQ;MAEhB,IAAI,CAAC1B,WAAW,GAAG,IAAI,CAACuB,EAAE,CAACW,KAAK,CAAC;QAC/B;QACAC,WAAW,EAAE,CAAC,MAAM,EAAE5E,UAAU,CAAC6E,QAAQ,CAAC;QAC1CC,WAAW,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAAC6E,QAAQ,CAAC;QACtCrD,MAAM,EAAE,CAAC,EAAE,EAAExB,UAAU,CAAC6E,QAAQ,CAAC;QACjCE,MAAM,EAAE,CAAC,EAAE,CAAC;QACZN,WAAW,EAAE,CAAC,EAAE,CAAC;QAEjB;QACAO,UAAU,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAAC6E,QAAQ,CAAC;QACrCI,MAAM,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAAC6E,QAAQ,CAAC;QAEjC;QACAK,aAAa,EAAE,CAAC,CAAC,EAAE,CAAClF,UAAU,CAAC6E,QAAQ,EAAE7E,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACpF,UAAU,CAAC6E,QAAQ,EAAE7E,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACrF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClCG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACtF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClCI,YAAY,EAAE,CAAC,CAAC,EAAE,CAACvF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC;QACAK,cAAc,EAAE,CAAC,EAAE,EAAExF,UAAU,CAAC6E,QAAQ,CAAC;QAEzC;QACAY,QAAQ,EAAE,CAAC,IAAI,CAAC;QAChBC,UAAU,EAAE,CAAC,KAAK,CAAC;QACnBC,SAAS,EAAE,CAAC,KAAK,CAAC;QAClBC,WAAW,EAAE,CAAC,KAAK;OACpB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,SAAS,EAAE;MAChB,IAAI,CAACC,aAAa,EAAE;MAEpB;MACA,IAAI,CAACvD,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEuD,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;QACjE,IAAI,CAACC,mBAAmB,CAACD,IAAI,CAAC;MAChC,CAAC,CAAC;IACJ;IAEAL,cAAcA,CAAA;MACZ,IAAI,CAAC7B,IAAI,CAACvB,GAAG,CAAM,6CAA6C,CAAC,CAACwD,SAAS,CAAC;QAC1EG,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC1D,UAAU,GAAG0D,QAAQ,CAAC1D,UAAU,IAAI,EAAE;QAC7C,CAAC;QACD2D,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAACE,WAAW,CAAC,qBAAqB,CAAC;QACzC;OACD,CAAC;IACJ;IAEAV,SAASA,CAAA;MACP,IAAI,CAAC9B,IAAI,CAACvB,GAAG,CAAM,wCAAwC,CAAC,CAACwD,SAAS,CAAC;QACrEG,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACzD,KAAK,GAAGyD,QAAQ,CAACzD,KAAK,IAAI,EAAE;QACnC,CAAC;QACD0D,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACE,WAAW,CAAC,sBAAsB,CAAC;QAC1C;OACD,CAAC;IACJ;IAEAT,aAAaA,CAAA;MACX,IAAI,CAAC/B,IAAI,CAACvB,GAAG,CAAM,4CAA4C,CAAC,CAACwD,SAAS,CAAC;QACzEG,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACnD,SAAS,GAAGmD,QAAQ,CAACnD,SAAS,IAAI,EAAE;QAC3C,CAAC;QACDoD,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,IAAI,CAACE,WAAW,CAAC,uBAAuB,CAAC;QAC3C;OACD,CAAC;IACJ;IAEAL,mBAAmBA,CAACD,IAAY;MAC9B,IAAIA,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAAC1D,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEgE,QAAQ,CAAC,IAAI,CAAC;QAClD,IAAI,CAACjE,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEiE,OAAO,EAAE;MAC/C,CAAC,MAAM;QACL,IAAI,CAAClE,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEgE,QAAQ,CAAC,KAAK,CAAC;QACnD,IAAI,CAACjE,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEkE,MAAM,EAAE;MAC9C;MAEA;MACA,IAAIT,IAAI,KAAK,MAAM,EAAE;QACnB,IAAI,CAAC1D,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEkE,MAAM,EAAE;MAC/C,CAAC,MAAM;QACL,IAAI,CAACnE,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEiE,OAAO,EAAE;MAChD;MAEA;MACA,IAAI,CAAClE,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEgE,QAAQ,CAAC,EAAE,CAAC;IACnD;IAEAG,mBAAmBA,CAAA;MACjB,MAAMjC,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK;MAE9D,IAAI,CAACiC,WAAW,EAAE;QAChB,IAAI,CAAC6B,WAAW,CAAC,8BAA8B,CAAC;QAChD;MACF;MAEA,IAAI7B,WAAW,KAAK,MAAM,EAAE;QAC1B,IAAI,CAAC6B,WAAW,CAAC,2CAA2C,CAAC;QAC7D;MACF;MAEA,IAAI,CAACpC,gBAAgB,GAAG,IAAI;MAE5B,IAAI,CAACJ,IAAI,CAAC6C,IAAI,CAAM,wDAAwD,EAAE;QAC5ElC,WAAW,EAAEA;OACd,CAAC,CAACsB,SAAS,CAAC;QACXG,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACS,mBAAmB,EAAE;YAChC,IAAI,CAACN,WAAW,CAACH,QAAQ,CAACU,OAAO,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACvE,WAAW,CAACwE,UAAU,CAAC;cAAEnC,WAAW,EAAEwB,QAAQ,CAACxB;YAAW,CAAE,CAAC;YAClE,IAAI,CAAC2B,WAAW,CAAC,mBAAmBH,QAAQ,CAACxB,WAAW,EAAE,CAAC;UAC7D;UACA,IAAI,CAACT,gBAAgB,GAAG,KAAK;QAC/B,CAAC;QACDkC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACE,WAAW,CAAC,oBAAoB,CAAC;UACtC,IAAI,CAACpC,gBAAgB,GAAG,KAAK;QAC/B;OACD,CAAC;IACJ;IAEA6C,gBAAgBA,CAAA;MACd,MAAMpC,WAAW,GAAG,IAAI,CAACrC,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK;MAE9D,IAAI,CAACmC,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAACb,IAAI,CAAC6C,IAAI,CAAM,qDAAqD,EAAE;QACzEhC,WAAW,EAAEA;OACd,CAAC,CAACoB,SAAS,CAAC;QACXG,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACA,QAAQ,CAACa,WAAW,EAAE;YACzB,IAAI,CAACV,WAAW,CAAC,8CAA8C,EAAE,IAAI,CAAC;YACtE,IAAI,CAAChE,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAE0E,SAAS,CAAC;cAAE,WAAW,EAAE;YAAI,CAAE,CAAC;UACvE,CAAC,MAAM;YACL,IAAI,CAAC3E,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAE0E,SAAS,CAAC,IAAI,CAAC;UACtD;QACF,CAAC;QACDb,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;IACJ;IAEAc,WAAWA,CAACC,QAAkB;MAC5B,IAAI,CAAC,IAAI,CAAClD,iBAAiB,CAACmD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnG,EAAE,KAAKiG,QAAQ,CAACjG,EAAE,CAAC,EAAE;QAC3D,IAAI,CAAC+C,iBAAiB,CAACqD,IAAI,CAACH,QAAQ,CAAC;MACvC;IACF;IAEAI,cAAcA,CAACJ,QAAkB;MAC/B,IAAI,CAAClD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACuD,MAAM,CAACH,CAAC,IAAIA,CAAC,CAACnG,EAAE,KAAKiG,QAAQ,CAACjG,EAAE,CAAC;IACnF;IAEAuG,QAAQA,CAAA;MACN,IAAI,IAAI,CAACnF,WAAW,CAACoF,KAAK,EAAE;QAC1B,IAAI,CAAC/D,SAAS,GAAG,IAAI;QAErB,MAAMgE,QAAQ,GAAG;UACf,GAAG,IAAI,CAACrF,WAAW,CAACE,KAAK;UACzBQ,SAAS,EAAE,IAAI,CAACiB,iBAAiB,CAAC2D,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACnG,EAAE;SAChD;QAED;QACAmF,OAAO,CAACwB,GAAG,CAAC,eAAe,EAAEF,QAAQ,CAAC;QAEtC;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAACnE,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC2C,WAAW,CAAC,uBAAuB,CAAC;UACzC,IAAI,CAACvC,MAAM,CAACgE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,IAAI,CAACzB,WAAW,CAAC,+BAA+B,EAAE,IAAI,CAAC;MACzD;IACF;IAEA0B,MAAMA,CAAA;MACJ,IAAI,CAACjE,MAAM,CAACgE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IACrC;IAEQzB,WAAWA,CAACO,OAAe,EAAEoB,OAAO,GAAG,KAAK;MAClD,IAAI,CAACjE,QAAQ,CAACkE,IAAI,CAACrB,OAAO,EAAE,OAAO,EAAE;QACnCsB,QAAQ,EAAE,IAAI;QACdC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE,KAAK;QACvBC,UAAU,EAAEL,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB;OAC/D,CAAC;IACJ;IAEA;IACA1E,QAAQA,CAAA;MACN,IAAI,IAAI,CAACY,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB;IACF;IAEAoE,YAAYA,CAAA;MACV,IAAI,IAAI,CAACpE,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB;IACF;IAEAqE,MAAMA,CAAA;MACJ;MACA,IAAI,CAACzE,MAAM,CAACgE,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IACrC;IAEA;IACAU,qBAAqBA,CAAA;MACnB,MAAM1D,aAAa,GAAG,IAAI,CAACzC,WAAW,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK;MAClE,MAAMyC,SAAS,GAAG,IAAI,CAAC3C,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEC,KAAK;MAE1D,IAAIuC,aAAa,IAAIE,SAAS,IAAIF,aAAa,GAAG,CAAC,EAAE;QACnD,IAAI,CAACpC,YAAY,GAAG+F,IAAI,CAACC,KAAK,CAAE,CAAC1D,SAAS,GAAGF,aAAa,IAAIA,aAAa,GAAI,GAAG,CAAC;MACrF,CAAC,MAAM;QACL,IAAI,CAACpC,YAAY,GAAG,IAAI;MAC1B;IACF;IAEA;IACAV,YAAYA,CAAA;MACV,IAAI,CAACyE,mBAAmB,EAAE;IAC5B;IAEA;IACAhD,WAAWA,CAAA;MACT,IAAI,IAAI,CAACpB,WAAW,CAACoF,KAAK,EAAE;QAC1B,IAAI,CAACD,QAAQ,EAAE;MACjB,CAAC,MAAM;QACL,IAAI,CAACzD,QAAQ,CAACkE,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAC3DC,QAAQ,EAAE,IAAI;UACdC,kBAAkB,EAAE,QAAQ;UAC5BC,gBAAgB,EAAE;SACnB,CAAC;MACJ;IACF;;uCAhRWzE,UAAU,EAAAhD,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;;YAAVxF,UAAU;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrEb/I,EANV,CAAAC,cAAA,aAAsC,aAEc,aACpB,aACD,YACE,kBACO;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAE,MAAA,yFACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAE,MAAA,+SAAwD;UACrFF,EADqF,CAAAG,YAAA,EAAI,EACnF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBAC0D;UAAnBD,EAAA,CAAAc,UAAA,mBAAAmI,6CAAA;YAAA,OAASD,GAAA,CAAApB,MAAA,EAAQ;UAAA,EAAC;UACjF5H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,+FACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAA6G;UAA/CD,EAAA,CAAAc,UAAA,mBAAAoI,6CAAA;YAAA,OAASF,GAAA,CAAAlG,WAAA,EAAa;UAAA,EAAC;UACnF9C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,iEACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,eAC8D,eAC7D;UAEvBD,EADA,CAAAsB,UAAA,KAAA6H,+BAAA,uBAAkC,KAAAC,2BAAA,mBACH;UACjCpJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,+GAAkB;UAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;UACNH,EAAA,CAAAa,SAAA,eAAiE;UAE/Db,EADF,CAAAC,cAAA,eAAwF,eAC7D;UAEvBD,EADA,CAAAsB,UAAA,KAAA+H,+BAAA,uBAAkC,KAAAC,2BAAA,mBACH;UACjCtJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mGAAgB;UAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;UACNH,EAAA,CAAAa,SAAA,eAAiE;UAE/Db,EADF,CAAAC,cAAA,eAAwF,eAC7D;UAEvBD,EADA,CAAAsB,UAAA,KAAAiI,+BAAA,uBAAkC,KAAAC,2BAAA,mBACH;UACjCxJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qHAAmB;UAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;UACNH,EAAA,CAAAa,SAAA,eAAiE;UAG7Db,EAFJ,CAAAC,cAAA,eAAoD,eACzB,YACjB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UACTF,EADS,CAAAG,YAAA,EAAO,EACV;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EAC3C,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAAkC,gBAC0B;UAsQxDD,EAnQA,CAAAsB,UAAA,KAAAmI,0BAAA,mBAAiD,KAAAC,0BAAA,mBAmHA,KAAAC,0BAAA,mBAkFA,KAAAC,0BAAA,mBA8DA;UAoCnD5J,EAAA,CAAAG,YAAA,EAAO;UAILH,EADF,CAAAC,cAAA,eAAgC,kBAEkC;UAAxDD,EAAA,CAAAc,UAAA,mBAAA+I,6CAAA;YAAA,OAASb,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC;UAC9B3H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAQTH,EANA,CAAAsB,UAAA,KAAAwI,6BAAA,qBACqD,KAAAC,6BAAA,qBAM4B;UAMvF/J,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAGNH,EAAA,CAAAsB,UAAA,KAAA0I,0BAAA,kBAA+C;;;UAhX+ChK,EAAA,CAAAO,SAAA,IAAsB;UAAtBP,EAAA,CAAAI,UAAA,aAAA4I,GAAA,CAAAjG,SAAA,CAAsB;UAS5F/C,EAAA,CAAAO,SAAA,GAAiC;UAACP,EAAlC,CAAAiK,WAAA,WAAAjB,GAAA,CAAAzF,WAAA,MAAiC,cAAAyF,GAAA,CAAAzF,WAAA,KAAoC;UAExEvD,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,KAAqB;UACzBvD,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,MAAsB;UAIVvD,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAiK,WAAA,cAAAjB,GAAA,CAAAzF,WAAA,KAAmC;UACxCvD,EAAA,CAAAO,SAAA,EAAiC;UAACP,EAAlC,CAAAiK,WAAA,WAAAjB,GAAA,CAAAzF,WAAA,MAAiC,cAAAyF,GAAA,CAAAzF,WAAA,KAAoC;UAExEvD,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,KAAqB;UACzBvD,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,MAAsB;UAIVvD,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAiK,WAAA,cAAAjB,GAAA,CAAAzF,WAAA,KAAmC;UACxCvD,EAAA,CAAAO,SAAA,EAAiC;UAACP,EAAlC,CAAAiK,WAAA,WAAAjB,GAAA,CAAAzF,WAAA,MAAiC,cAAAyF,GAAA,CAAAzF,WAAA,KAAoC;UAExEvD,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,KAAqB;UACzBvD,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,MAAsB;UAIVvD,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAiK,WAAA,cAAAjB,GAAA,CAAAzF,WAAA,KAAmC;UACxCvD,EAAA,CAAAO,SAAA,EAAiC;UAAjCP,EAAA,CAAAiK,WAAA,WAAAjB,GAAA,CAAAzF,WAAA,MAAiC;UAW/CvD,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,cAAA4I,GAAA,CAAAtH,WAAA,CAAyB;UAGL1B,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,OAAuB;UAmHvBvD,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,OAAuB;UAkFvBvD,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,OAAuB;UA8DvBvD,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,OAAuB;UAyCdvD,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,aAAA4I,GAAA,CAAAzF,WAAA,OAA8B;UAMjCvD,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,KAAqB;UAMlBvD,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAzF,WAAA,OAAuB;UASxDvD,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAjG,SAAA,CAAe;;;qBD1UjBjE,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZrL,WAAW,EAAAkJ,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,mBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EAAAvC,EAAA,CAAAwC,oBAAA,EAAAxC,EAAA,CAAAyC,iBAAA,EAAAzC,EAAA,CAAA0C,YAAA,EACX3L,mBAAmB,EAAAiJ,EAAA,CAAA2C,kBAAA,EAAA3C,EAAA,CAAA4C,eAAA,EACnB3L,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EAAA0L,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3L,aAAa,EAAA4L,EAAA,CAAAC,OAAA,EACb5L,cAAc,EAAA6L,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EACdlM,eAAe,EAAAmM,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACfpM,iBAAiB,EAAAqM,GAAA,CAAAC,WAAA,EACjBrM,gBAAgB,EAChBC,iBAAiB,EACjBG,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdJ,kBAAkB,EAClBC,wBAAwB,EAAAmM,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;;SAKfjJ,UAAU;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}