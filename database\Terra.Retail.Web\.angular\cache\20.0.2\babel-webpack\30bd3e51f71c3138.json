{"ast": null, "code": "import { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, signal, Input, NgZone, Injector, ContentChildren, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ChangeDetectorRef, EventEmitter, Output, forwardRef, Renderer2, NgModule } from '@angular/core';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { Subscription, merge, Subject } from 'rxjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer } from './ripple-BYgV4oZC.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { CdkObserveContent, ObserversModule } from '@angular/cdk/observers';\nimport { MatDividerModule } from './divider.mjs';\nconst _c0 = [\"*\"];\nconst _c1 = \".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\";\nconst _c2 = [\"unscopedContent\"];\nconst _c3 = [\"text\"];\nconst _c4 = [[[\"\", \"matListItemAvatar\", \"\"], [\"\", \"matListItemIcon\", \"\"]], [[\"\", \"matListItemTitle\", \"\"]], [[\"\", \"matListItemLine\", \"\"]], \"*\", [[\"\", \"matListItemMeta\", \"\"]], [[\"mat-divider\"]]];\nconst _c5 = [\"[matListItemAvatar],[matListItemIcon]\", \"[matListItemTitle]\", \"[matListItemLine]\", \"*\", \"[matListItemMeta]\", \"mat-divider\"];\nconst _c6 = [[[\"\", \"matListItemTitle\", \"\"]], [[\"\", \"matListItemLine\", \"\"]], \"*\", [[\"mat-divider\"]], [[\"\", \"matListItemAvatar\", \"\"], [\"\", \"matListItemIcon\", \"\"]]];\nconst _c7 = [\"[matListItemTitle]\", \"[matListItemLine]\", \"*\", \"mat-divider\", \"[matListItemAvatar],[matListItemIcon]\"];\nfunction MatListOption_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 4);\n  }\n}\nfunction MatListOption_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"input\", 12);\n    i0.ɵɵelementStart(2, \"div\", 13);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 14);\n    i0.ɵɵelement(4, \"path\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-checkbox--disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selected)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatListOption_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"input\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19);\n    i0.ɵɵelement(3, \"div\", 20)(4, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selected)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatListOption_Conditional_6_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_6_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const checkbox_r3 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", checkbox_r3);\n  }\n}\nfunction MatListOption_Conditional_7_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_7_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const radio_r4 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", radio_r4);\n  }\n}\nfunction MatListOption_Conditional_8_ng_template_0_Template(rf, ctx) {}\nfunction MatListOption_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatListOption_Conditional_8_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const icons_r5 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icons_r5);\n  }\n}\nfunction MatListOption_Conditional_15_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_15_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const checkbox_r3 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", checkbox_r3);\n  }\n}\nfunction MatListOption_Conditional_16_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_16_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const radio_r4 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", radio_r4);\n  }\n}\nfunction MatListOption_Conditional_17_ng_template_0_Template(rf, ctx) {}\nfunction MatListOption_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatListOption_Conditional_17_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const icons_r5 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icons_r5);\n  }\n}\nexport { MatDivider } from './divider.mjs';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ENTER, SPACE, A, hasModifierKey } from '@angular/cdk/keycodes';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\n\n/**\n * Injection token that can be used to reference instances of an `ListOption`. It serves\n * as alternative token to an actual implementation which could result in undesired\n * retention of the class or circular references breaking runtime execution.\n * @docs-private\n */\nconst LIST_OPTION = /*#__PURE__*/new InjectionToken('ListOption');\n\n/**\n * Directive capturing the title of a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content for the title never wraps. There can only be a single title per list item.\n */\nlet MatListItemTitle = /*#__PURE__*/(() => {\n  class MatListItemTitle {\n    _elementRef = inject(ElementRef);\n    constructor() {}\n    static ɵfac = function MatListItemTitle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListItemTitle)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemTitle,\n      selectors: [[\"\", \"matListItemTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-list-item-title\", \"mdc-list-item__primary-text\"]\n    });\n  }\n  return MatListItemTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive capturing a line in a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content inside a line never wraps. There can be at maximum two lines per list item.\n */\nlet MatListItemLine = /*#__PURE__*/(() => {\n  class MatListItemLine {\n    _elementRef = inject(ElementRef);\n    constructor() {}\n    static ɵfac = function MatListItemLine_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListItemLine)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemLine,\n      selectors: [[\"\", \"matListItemLine\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-list-item-line\", \"mdc-list-item__secondary-text\"]\n    });\n  }\n  return MatListItemLine;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive matching an optional meta section for list items.\n *\n * List items can reserve space at the end of an item to display a control,\n * button or additional text content.\n */\nlet MatListItemMeta = /*#__PURE__*/(() => {\n  class MatListItemMeta {\n    static ɵfac = function MatListItemMeta_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListItemMeta)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemMeta,\n      selectors: [[\"\", \"matListItemMeta\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-list-item-meta\", \"mdc-list-item__end\"]\n    });\n  }\n  return MatListItemMeta;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n *\n * MDC uses the very intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n * position content such as icons or checkboxes/radios that comes either before or after the text\n * content respectively. This directive detects the placement of the checkbox/radio and applies the\n * correct MDC class to position the icon/avatar on the opposite side.\n */\nlet _MatListItemGraphicBase = /*#__PURE__*/(() => {\n  class _MatListItemGraphicBase {\n    _listOption = inject(LIST_OPTION, {\n      optional: true\n    });\n    constructor() {}\n    _isAlignedAtStart() {\n      // By default, in all list items the graphic is aligned at start. In list options,\n      // the graphic is only aligned at start if the checkbox/radio is at the end.\n      return !this._listOption || this._listOption?._getTogglePosition() === 'after';\n    }\n    static ɵfac = function _MatListItemGraphicBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatListItemGraphicBase)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatListItemGraphicBase,\n      hostVars: 4,\n      hostBindings: function _MatListItemGraphicBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-list-item__start\", ctx._isAlignedAtStart())(\"mdc-list-item__end\", !ctx._isAlignedAtStart());\n        }\n      }\n    });\n  }\n  return _MatListItemGraphicBase;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive matching an optional avatar within a list item.\n *\n * List items can reserve space at the beginning of an item to display an avatar.\n */\nlet MatListItemAvatar = /*#__PURE__*/(() => {\n  class MatListItemAvatar extends _MatListItemGraphicBase {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatListItemAvatar_BaseFactory;\n      return function MatListItemAvatar_Factory(__ngFactoryType__) {\n        return (ɵMatListItemAvatar_BaseFactory || (ɵMatListItemAvatar_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItemAvatar)))(__ngFactoryType__ || MatListItemAvatar);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemAvatar,\n      selectors: [[\"\", \"matListItemAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-list-item-avatar\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatListItemAvatar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive matching an optional icon within a list item.\n *\n * List items can reserve space at the beginning of an item to display an icon.\n */\nlet MatListItemIcon = /*#__PURE__*/(() => {\n  class MatListItemIcon extends _MatListItemGraphicBase {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatListItemIcon_BaseFactory;\n      return function MatListItemIcon_Factory(__ngFactoryType__) {\n        return (ɵMatListItemIcon_BaseFactory || (ɵMatListItemIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItemIcon)))(__ngFactoryType__ || MatListItemIcon);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemIcon,\n      selectors: [[\"\", \"matListItemIcon\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-list-item-icon\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatListItemIcon;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that can be used to provide the default options for the list module. */\nconst MAT_LIST_CONFIG = /*#__PURE__*/new InjectionToken('MAT_LIST_CONFIG');\n\n/** @docs-private */\nlet MatListBase = /*#__PURE__*/(() => {\n  class MatListBase {\n    _isNonInteractive = true;\n    /** Whether ripples for all list items is disabled. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    _disableRipple = false;\n    /**\n     * Whether the entire list is disabled. When disabled, the list itself and each of its list items\n     * are disabled.\n     */\n    get disabled() {\n      return this._disabled();\n    }\n    set disabled(value) {\n      this._disabled.set(coerceBooleanProperty(value));\n    }\n    _disabled = signal(false);\n    _defaultOptions = inject(MAT_LIST_CONFIG, {\n      optional: true\n    });\n    static ɵfac = function MatListBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListBase)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListBase,\n      hostVars: 1,\n      hostBindings: function MatListBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        disableRipple: \"disableRipple\",\n        disabled: \"disabled\"\n      }\n    });\n  }\n  return MatListBase;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** @docs-private */\nlet MatListItemBase = /*#__PURE__*/(() => {\n  class MatListItemBase {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _listBase = inject(MatListBase, {\n      optional: true\n    });\n    _platform = inject(Platform);\n    /** Host element for the list item. */\n    _hostElement;\n    /** indicate whether the host element is a button or not */\n    _isButtonElement;\n    /** Whether animations are disabled. */\n    _noopAnimations = _animationsDisabled();\n    _avatars;\n    _icons;\n    /**\n     * The number of lines this list item should reserve space for. If not specified,\n     * lines are inferred based on the projected content.\n     *\n     * Explicitly specifying the number of lines is useful if you want to acquire additional\n     * space and enable the wrapping of text. The unscoped text content of a list item will\n     * always be able to take up the remaining space of the item, unless it represents the title.\n     *\n     * A maximum of three lines is supported as per the Material Design specification.\n     */\n    set lines(lines) {\n      this._explicitLines = coerceNumberProperty(lines, null);\n      this._updateItemLines(false);\n    }\n    _explicitLines = null;\n    /** Whether ripples for list items are disabled. */\n    get disableRipple() {\n      return this.disabled || this._disableRipple || this._noopAnimations || !!this._listBase?.disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    _disableRipple = false;\n    /** Whether the list-item is disabled. */\n    get disabled() {\n      return this._disabled() || !!this._listBase?.disabled;\n    }\n    set disabled(value) {\n      this._disabled.set(coerceBooleanProperty(value));\n    }\n    _disabled = signal(false);\n    _subscriptions = new Subscription();\n    _rippleRenderer = null;\n    /** Whether the list item has unscoped text content. */\n    _hasUnscopedTextContent = false;\n    /**\n     * Implemented as part of `RippleTarget`.\n     * @docs-private\n     */\n    rippleConfig;\n    /**\n     * Implemented as part of `RippleTarget`.\n     * @docs-private\n     */\n    get rippleDisabled() {\n      return this.disableRipple || !!this.rippleConfig.disabled;\n    }\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n        optional: true\n      });\n      this.rippleConfig = globalRippleOptions || {};\n      this._hostElement = this._elementRef.nativeElement;\n      this._isButtonElement = this._hostElement.nodeName.toLowerCase() === 'button';\n      if (this._listBase && !this._listBase._isNonInteractive) {\n        this._initInteractiveListItem();\n      }\n      // If no type attribute is specified for a host `<button>` element, set it to `button`. If a\n      // type attribute is already specified, we do nothing. We do this for backwards compatibility.\n      // TODO: Determine if we intend to continue doing this for the MDC-based list.\n      if (this._isButtonElement && !this._hostElement.hasAttribute('type')) {\n        this._hostElement.setAttribute('type', 'button');\n      }\n    }\n    ngAfterViewInit() {\n      this._monitorProjectedLinesAndTitle();\n      this._updateItemLines(true);\n    }\n    ngOnDestroy() {\n      this._subscriptions.unsubscribe();\n      if (this._rippleRenderer !== null) {\n        this._rippleRenderer._removeTriggerEvents();\n      }\n    }\n    /** Whether the list item has icons or avatars. */\n    _hasIconOrAvatar() {\n      return !!(this._avatars.length || this._icons.length);\n    }\n    _initInteractiveListItem() {\n      this._hostElement.classList.add('mat-mdc-list-item-interactive');\n      this._rippleRenderer = new RippleRenderer(this, this._ngZone, this._hostElement, this._platform, inject(Injector));\n      this._rippleRenderer.setupTriggerEvents(this._hostElement);\n    }\n    /**\n     * Subscribes to changes in the projected title and lines. Triggers a\n     * item lines update whenever a change occurs.\n     */\n    _monitorProjectedLinesAndTitle() {\n      this._ngZone.runOutsideAngular(() => {\n        this._subscriptions.add(merge(this._lines.changes, this._titles.changes).subscribe(() => this._updateItemLines(false)));\n      });\n    }\n    /**\n     * Updates the lines of the list item. Based on the projected user content and optional\n     * explicit lines setting, the visual appearance of the list item is determined.\n     *\n     * This method should be invoked whenever the projected user content changes, or\n     * when the explicit lines have been updated.\n     *\n     * @param recheckUnscopedContent Whether the projected unscoped content should be re-checked.\n     *   The unscoped content is not re-checked for every update as it is a rather expensive check\n     *   for content that is expected to not change very often.\n     */\n    _updateItemLines(recheckUnscopedContent) {\n      // If the updated is triggered too early before the view and content is initialized,\n      // we just skip the update. After view initialization the update is triggered again.\n      if (!this._lines || !this._titles || !this._unscopedContent) {\n        return;\n      }\n      // Re-check the DOM for unscoped text content if requested. This needs to\n      // happen before any computation or sanity checks run as these rely on the\n      // result of whether there is unscoped text content or not.\n      if (recheckUnscopedContent) {\n        this._checkDomForUnscopedTextContent();\n      }\n      // Sanity check the list item lines and title in the content. This is a dev-mode only\n      // check that can be dead-code eliminated by Terser in production.\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        sanityCheckListItemContent(this);\n      }\n      const numberOfLines = this._explicitLines ?? this._inferLinesFromContent();\n      const unscopedContentEl = this._unscopedContent.nativeElement;\n      // Update the list item element to reflect the number of lines.\n      this._hostElement.classList.toggle('mat-mdc-list-item-single-line', numberOfLines <= 1);\n      this._hostElement.classList.toggle('mdc-list-item--with-one-line', numberOfLines <= 1);\n      this._hostElement.classList.toggle('mdc-list-item--with-two-lines', numberOfLines === 2);\n      this._hostElement.classList.toggle('mdc-list-item--with-three-lines', numberOfLines === 3);\n      // If there is no title and the unscoped content is the is the only line, the\n      // unscoped text content will be treated as the title of the list-item.\n      if (this._hasUnscopedTextContent) {\n        const treatAsTitle = this._titles.length === 0 && numberOfLines === 1;\n        unscopedContentEl.classList.toggle('mdc-list-item__primary-text', treatAsTitle);\n        unscopedContentEl.classList.toggle('mdc-list-item__secondary-text', !treatAsTitle);\n      } else {\n        unscopedContentEl.classList.remove('mdc-list-item__primary-text');\n        unscopedContentEl.classList.remove('mdc-list-item__secondary-text');\n      }\n    }\n    /**\n     * Infers the number of lines based on the projected user content. This is useful\n     * if no explicit number of lines has been specified on the list item.\n     *\n     * The number of lines is inferred based on whether there is a title, the number of\n     * additional lines (secondary/tertiary). An additional line is acquired if there is\n     * unscoped text content.\n     */\n    _inferLinesFromContent() {\n      let numOfLines = this._titles.length + this._lines.length;\n      if (this._hasUnscopedTextContent) {\n        numOfLines += 1;\n      }\n      return numOfLines;\n    }\n    /** Checks whether the list item has unscoped text content. */\n    _checkDomForUnscopedTextContent() {\n      this._hasUnscopedTextContent = Array.from(this._unscopedContent.nativeElement.childNodes).filter(node => node.nodeType !== node.COMMENT_NODE).some(node => !!(node.textContent && node.textContent.trim()));\n    }\n    static ɵfac = function MatListItemBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListItemBase)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListItemBase,\n      contentQueries: function MatListItemBase_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatListItemAvatar, 4);\n          i0.ɵɵcontentQuery(dirIndex, MatListItemIcon, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._avatars = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icons = _t);\n        }\n      },\n      hostVars: 4,\n      hostBindings: function MatListItemBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx._isButtonElement && ctx.disabled || null);\n          i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        lines: \"lines\",\n        disableRipple: \"disableRipple\",\n        disabled: \"disabled\"\n      }\n    });\n  }\n  return MatListItemBase;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Sanity checks the configuration of the list item with respect to the amount\n * of lines, whether there is a title, or if there is unscoped text content.\n *\n * The checks are extracted into a top-level function that can be dead-code\n * eliminated by Terser or other optimizers in production mode.\n */\nfunction sanityCheckListItemContent(item) {\n  const numTitles = item._titles.length;\n  const numLines = item._lines.length;\n  if (numTitles > 1) {\n    console.warn('A list item cannot have multiple titles.');\n  }\n  if (numTitles === 0 && numLines > 0) {\n    console.warn('A list item line can only be used if there is a list item title.');\n  }\n  if (numTitles === 0 && item._hasUnscopedTextContent && item._explicitLines !== null && item._explicitLines > 1) {\n    console.warn('A list item cannot have wrapping content without a title.');\n  }\n  if (numLines > 2 || numLines === 2 && item._hasUnscopedTextContent) {\n    console.warn('A list item can have at maximum three lines.');\n  }\n}\nlet MatActionList = /*#__PURE__*/(() => {\n  class MatActionList extends MatListBase {\n    // An navigation list is considered interactive, but does not extend the interactive list\n    // base class. We do this because as per MDC, items of interactive lists are only reachable\n    // through keyboard shortcuts. We want all items for the navigation list to be reachable\n    // through tab key as we do not intend to provide any special accessibility treatment. The\n    // accessibility treatment depends on how the end-user will interact with it.\n    _isNonInteractive = false;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatActionList_BaseFactory;\n      return function MatActionList_Factory(__ngFactoryType__) {\n        return (ɵMatActionList_BaseFactory || (ɵMatActionList_BaseFactory = i0.ɵɵgetInheritedFactory(MatActionList)))(__ngFactoryType__ || MatActionList);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatActionList,\n      selectors: [[\"mat-action-list\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-action-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n      exportAs: [\"matActionList\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatListBase,\n        useExisting: MatActionList\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatActionList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatActionList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_LIST = /*#__PURE__*/new InjectionToken('MatList');\nlet MatList = /*#__PURE__*/(() => {\n  class MatList extends MatListBase {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatList_BaseFactory;\n      return function MatList_Factory(__ngFactoryType__) {\n        return (ɵMatList_BaseFactory || (ɵMatList_BaseFactory = i0.ɵɵgetInheritedFactory(MatList)))(__ngFactoryType__ || MatList);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatList,\n      selectors: [[\"mat-list\"]],\n      hostAttrs: [1, \"mat-mdc-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n      exportAs: [\"matList\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatListBase,\n        useExisting: MatList\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [_c1],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatListItem = /*#__PURE__*/(() => {\n  class MatListItem extends MatListItemBase {\n    _lines;\n    _titles;\n    _meta;\n    _unscopedContent;\n    _itemText;\n    /** Indicates whether an item in a `<mat-nav-list>` is the currently active page. */\n    get activated() {\n      return this._activated;\n    }\n    set activated(activated) {\n      this._activated = coerceBooleanProperty(activated);\n    }\n    _activated = false;\n    /**\n     * Determine the value of `aria-current`. Return 'page' if this item is an activated anchor tag.\n     * Otherwise, return `null`. This method is safe to use with server-side rendering.\n     */\n    _getAriaCurrent() {\n      return this._hostElement.nodeName === 'A' && this._activated ? 'page' : null;\n    }\n    _hasBothLeadingAndTrailing() {\n      return this._meta.length !== 0 && (this._avatars.length !== 0 || this._icons.length !== 0);\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatListItem_BaseFactory;\n      return function MatListItem_Factory(__ngFactoryType__) {\n        return (ɵMatListItem_BaseFactory || (ɵMatListItem_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItem)))(__ngFactoryType__ || MatListItem);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatListItem,\n      selectors: [[\"mat-list-item\"], [\"a\", \"mat-list-item\", \"\"], [\"button\", \"mat-list-item\", \"\"]],\n      contentQueries: function MatListItem_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatListItemLine, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatListItemTitle, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatListItemMeta, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._titles = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._meta = _t);\n        }\n      },\n      viewQuery: function MatListItem_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._unscopedContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._itemText = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-list-item\", \"mdc-list-item\"],\n      hostVars: 13,\n      hostBindings: function MatListItem_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-current\", ctx._getAriaCurrent());\n          i0.ɵɵclassProp(\"mdc-list-item--activated\", ctx.activated)(\"mdc-list-item--with-leading-avatar\", ctx._avatars.length !== 0)(\"mdc-list-item--with-leading-icon\", ctx._icons.length !== 0)(\"mdc-list-item--with-trailing-meta\", ctx._meta.length !== 0)(\"mat-mdc-list-item-both-leading-and-trailing\", ctx._hasBothLeadingAndTrailing())(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        activated: \"activated\"\n      },\n      exportAs: [\"matListItem\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 10,\n      vars: 0,\n      consts: [[\"unscopedContent\", \"\"], [1, \"mdc-list-item__content\"], [1, \"mat-mdc-list-item-unscoped-content\", 3, \"cdkObserveContent\"], [1, \"mat-focus-indicator\"]],\n      template: function MatListItem_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c4);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementStart(4, \"span\", 2, 0);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatListItem_Template_span_cdkObserveContent_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._updateItemLines(true));\n          });\n          i0.ɵɵprojection(6, 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(7, 4);\n          i0.ɵɵprojection(8, 5);\n          i0.ɵɵelement(9, \"div\", 3);\n        }\n      },\n      dependencies: [CdkObserveContent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatListItem;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of an `SelectionList`. It serves\n * as alternative token to an actual implementation which would result in circular references.\n * @docs-private\n */\nconst SELECTION_LIST = /*#__PURE__*/new InjectionToken('SelectionList');\nlet MatListOption = /*#__PURE__*/(() => {\n  class MatListOption extends MatListItemBase {\n    _selectionList = inject(SELECTION_LIST);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _lines;\n    _titles;\n    _unscopedContent;\n    /**\n     * Emits when the selected state of the option has changed.\n     * Use to facilitate two-data binding to the `selected` property.\n     * @docs-private\n     */\n    selectedChange = new EventEmitter();\n    /** Whether the label should appear before or after the checkbox/radio. Defaults to 'after' */\n    togglePosition = 'after';\n    /**\n     * Whether the label should appear before or after the checkbox/radio. Defaults to 'after'\n     *\n     * @deprecated Use `togglePosition` instead.\n     * @breaking-change 17.0.0\n     */\n    get checkboxPosition() {\n      return this.togglePosition;\n    }\n    set checkboxPosition(value) {\n      this.togglePosition = value;\n    }\n    /**\n     * Theme color of the list option. This sets the color of the checkbox/radio.\n     * This API is supported in M2 themes only, it has no effect in M3 themes. For color customization\n     * in M3, see https://material.angular.dev/components/list/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      return this._color || this._selectionList.color;\n    }\n    set color(newValue) {\n      this._color = newValue;\n    }\n    _color;\n    /** Value of the option */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      if (this.selected && newValue !== this.value && this._inputsInitialized) {\n        this.selected = false;\n      }\n      this._value = newValue;\n    }\n    _value;\n    /** Whether the option is selected. */\n    get selected() {\n      return this._selectionList.selectedOptions.isSelected(this);\n    }\n    set selected(value) {\n      const isSelected = coerceBooleanProperty(value);\n      if (isSelected !== this._selected) {\n        this._setSelected(isSelected);\n        if (isSelected || this._selectionList.multiple) {\n          this._selectionList._reportValueChange();\n        }\n      }\n    }\n    _selected = false;\n    /**\n     * This is set to true after the first OnChanges cycle so we don't\n     * clear the value of `selected` in the first cycle.\n     */\n    _inputsInitialized = false;\n    ngOnInit() {\n      const list = this._selectionList;\n      if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n        this._setSelected(true);\n      }\n      const wasSelected = this._selected;\n      // List options that are selected at initialization can't be reported properly to the form\n      // control. This is because it takes some time until the selection-list knows about all\n      // available options. Also it can happen that the ControlValueAccessor has an initial value\n      // that should be used instead. Deferring the value change report to the next tick ensures\n      // that the form control value is not being overwritten.\n      Promise.resolve().then(() => {\n        if (this._selected || wasSelected) {\n          this.selected = true;\n          this._changeDetectorRef.markForCheck();\n        }\n      });\n      this._inputsInitialized = true;\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      if (this.selected) {\n        // We have to delay this until the next tick in order\n        // to avoid changed after checked errors.\n        Promise.resolve().then(() => {\n          this.selected = false;\n        });\n      }\n    }\n    /** Toggles the selection state of the option. */\n    toggle() {\n      this.selected = !this.selected;\n    }\n    /** Allows for programmatic focusing of the option. */\n    focus() {\n      this._hostElement.focus();\n    }\n    /** Gets the text label of the list option. Used for the typeahead functionality in the list. */\n    getLabel() {\n      const titleElement = this._titles?.get(0)?._elementRef.nativeElement;\n      // If there is no explicit title element, the unscoped text content\n      // is treated as the list item title.\n      const labelEl = titleElement || this._unscopedContent?.nativeElement;\n      return labelEl?.textContent || '';\n    }\n    /** Whether a checkbox is shown at the given position. */\n    _hasCheckboxAt(position) {\n      return this._selectionList.multiple && this._getTogglePosition() === position;\n    }\n    /** Where a radio indicator is shown at the given position. */\n    _hasRadioAt(position) {\n      return !this._selectionList.multiple && this._getTogglePosition() === position && !this._selectionList.hideSingleSelectionIndicator;\n    }\n    /** Whether icons or avatars are shown at the given position. */\n    _hasIconsOrAvatarsAt(position) {\n      return this._hasProjected('icons', position) || this._hasProjected('avatars', position);\n    }\n    /** Gets whether the given type of element is projected at the specified position. */\n    _hasProjected(type, position) {\n      // If the checkbox/radio is shown at the specified position, neither icons or\n      // avatars can be shown at the position.\n      return this._getTogglePosition() !== position && (type === 'avatars' ? this._avatars.length !== 0 : this._icons.length !== 0);\n    }\n    _handleBlur() {\n      this._selectionList._onTouched();\n    }\n    /** Gets the current position of the checkbox/radio. */\n    _getTogglePosition() {\n      return this.togglePosition || 'after';\n    }\n    /**\n     * Sets the selected state of the option.\n     * @returns Whether the value has changed.\n     */\n    _setSelected(selected) {\n      if (selected === this._selected) {\n        return false;\n      }\n      this._selected = selected;\n      if (selected) {\n        this._selectionList.selectedOptions.select(this);\n      } else {\n        this._selectionList.selectedOptions.deselect(this);\n      }\n      this.selectedChange.emit(selected);\n      this._changeDetectorRef.markForCheck();\n      return true;\n    }\n    /**\n     * Notifies Angular that the option needs to be checked in the next change detection run.\n     * Mainly used to trigger an update of the list option if the disabled state of the selection\n     * list changed.\n     */\n    _markForCheck() {\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Toggles the option's value based on a user interaction. */\n    _toggleOnInteraction() {\n      if (!this.disabled) {\n        if (this._selectionList.multiple) {\n          this.selected = !this.selected;\n          this._selectionList._emitChangeEvent([this]);\n        } else if (!this.selected) {\n          this.selected = true;\n          this._selectionList._emitChangeEvent([this]);\n        }\n      }\n    }\n    /** Sets the tabindex of the list option. */\n    _setTabindex(value) {\n      this._hostElement.setAttribute('tabindex', value + '');\n    }\n    _hasBothLeadingAndTrailing() {\n      const hasLeading = this._hasProjected('avatars', 'before') || this._hasProjected('icons', 'before') || this._hasCheckboxAt('before') || this._hasRadioAt('before');\n      const hasTrailing = this._hasProjected('icons', 'after') || this._hasProjected('avatars', 'after') || this._hasCheckboxAt('after') || this._hasRadioAt('after');\n      return hasLeading && hasTrailing;\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatListOption_BaseFactory;\n      return function MatListOption_Factory(__ngFactoryType__) {\n        return (ɵMatListOption_BaseFactory || (ɵMatListOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatListOption)))(__ngFactoryType__ || MatListOption);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatListOption,\n      selectors: [[\"mat-list-option\"]],\n      contentQueries: function MatListOption_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatListItemLine, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatListItemTitle, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._titles = _t);\n        }\n      },\n      viewQuery: function MatListOption_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._unscopedContent = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-list-item\", \"mat-mdc-list-option\", \"mdc-list-item\"],\n      hostVars: 27,\n      hostBindings: function MatListOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function MatListOption_blur_HostBindingHandler() {\n            return ctx._handleBlur();\n          })(\"click\", function MatListOption_click_HostBindingHandler() {\n            return ctx._toggleOnInteraction();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected);\n          i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected && !ctx._selectionList.multiple && ctx._selectionList.hideSingleSelectionIndicator)(\"mdc-list-item--with-leading-avatar\", ctx._hasProjected(\"avatars\", \"before\"))(\"mdc-list-item--with-leading-icon\", ctx._hasProjected(\"icons\", \"before\"))(\"mdc-list-item--with-trailing-icon\", ctx._hasProjected(\"icons\", \"after\"))(\"mat-mdc-list-option-with-trailing-avatar\", ctx._hasProjected(\"avatars\", \"after\"))(\"mdc-list-item--with-leading-checkbox\", ctx._hasCheckboxAt(\"before\"))(\"mdc-list-item--with-trailing-checkbox\", ctx._hasCheckboxAt(\"after\"))(\"mdc-list-item--with-leading-radio\", ctx._hasRadioAt(\"before\"))(\"mdc-list-item--with-trailing-radio\", ctx._hasRadioAt(\"after\"))(\"mat-mdc-list-item-both-leading-and-trailing\", ctx._hasBothLeadingAndTrailing())(\"mat-accent\", ctx.color !== \"primary\" && ctx.color !== \"warn\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        togglePosition: \"togglePosition\",\n        checkboxPosition: \"checkboxPosition\",\n        color: \"color\",\n        value: \"value\",\n        selected: \"selected\"\n      },\n      outputs: {\n        selectedChange: \"selectedChange\"\n      },\n      exportAs: [\"matListOption\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatListItemBase,\n        useExisting: MatListOption\n      }, {\n        provide: LIST_OPTION,\n        useExisting: MatListOption\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c7,\n      decls: 20,\n      vars: 4,\n      consts: [[\"icons\", \"\"], [\"checkbox\", \"\"], [\"radio\", \"\"], [\"unscopedContent\", \"\"], [1, \"mdc-list-item__start\", \"mat-mdc-list-option-checkbox-before\"], [1, \"mdc-list-item__start\", \"mat-mdc-list-option-radio-before\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-list-item__content\"], [1, \"mat-mdc-list-item-unscoped-content\", 3, \"cdkObserveContent\"], [1, \"mdc-list-item__end\"], [1, \"mat-focus-indicator\"], [1, \"mdc-checkbox\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"checked\", \"disabled\"], [1, \"mdc-checkbox__background\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [1, \"mdc-radio\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"checked\", \"disabled\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"]],\n      template: function MatListOption_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c6);\n          i0.ɵɵtemplate(0, MatListOption_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, MatListOption_ng_template_2_Template, 6, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, MatListOption_ng_template_4_Template, 5, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵconditionalCreate(6, MatListOption_Conditional_6_Template, 2, 1, \"span\", 4)(7, MatListOption_Conditional_7_Template, 2, 1, \"span\", 5);\n          i0.ɵɵconditionalCreate(8, MatListOption_Conditional_8_Template, 1, 1, null, 6);\n          i0.ɵɵelementStart(9, \"span\", 7);\n          i0.ɵɵprojection(10);\n          i0.ɵɵprojection(11, 1);\n          i0.ɵɵelementStart(12, \"span\", 8, 3);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatListOption_Template_span_cdkObserveContent_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._updateItemLines(true));\n          });\n          i0.ɵɵprojection(14, 2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵconditionalCreate(15, MatListOption_Conditional_15_Template, 2, 1, \"span\", 9)(16, MatListOption_Conditional_16_Template, 2, 1, \"span\", 9);\n          i0.ɵɵconditionalCreate(17, MatListOption_Conditional_17_Template, 1, 1, null, 6);\n          i0.ɵɵprojection(18, 3);\n          i0.ɵɵelement(19, \"div\", 10);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵconditional(ctx._hasCheckboxAt(\"before\") ? 6 : ctx._hasRadioAt(\"before\") ? 7 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasIconsOrAvatarsAt(\"before\") ? 8 : -1);\n          i0.ɵɵadvance(7);\n          i0.ɵɵconditional(ctx._hasCheckboxAt(\"after\") ? 15 : ctx._hasRadioAt(\"after\") ? 16 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasIconsOrAvatarsAt(\"after\") ? 17 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet, CdkObserveContent],\n      styles: [\".mat-mdc-list-option-with-trailing-avatar.mdc-list-item,[dir=rtl] .mat-mdc-list-option-with-trailing-avatar.mdc-list-item{padding-left:0;padding-right:0}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{margin-left:16px;margin-right:16px;width:40px;height:40px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{border-radius:50%}.mat-mdc-list-option .mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled{opacity:.5}}.mat-mdc-list-option .mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mat-mdc-list-option .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mat-mdc-list-option .mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__mixedmark{margin:0 1px}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-list-option .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-list-option .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-list-option .mdc-checkbox__native-control,.mat-mdc-list-option .mdc-radio__native-control{display:none}@media(forced-colors: active){.mat-mdc-list-option.mdc-list-item--selected::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.mat-mdc-list-option.mdc-list-item--selected [dir=rtl]::after{right:auto;left:16px}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatListOption;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatListSubheaderCssMatStyler = /*#__PURE__*/(() => {\n  class MatListSubheaderCssMatStyler {\n    static ɵfac = function MatListSubheaderCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListSubheaderCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatListSubheaderCssMatStyler,\n      selectors: [[\"\", \"mat-subheader\", \"\"], [\"\", \"matSubheader\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-subheader\", \"mdc-list-group__subheader\"]\n    });\n  }\n  return MatListSubheaderCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_NAV_LIST = /*#__PURE__*/new InjectionToken('MatNavList');\nlet MatNavList = /*#__PURE__*/(() => {\n  class MatNavList extends MatListBase {\n    // An navigation list is considered interactive, but does not extend the interactive list\n    // base class. We do this because as per MDC, items of interactive lists are only reachable\n    // through keyboard shortcuts. We want all items for the navigation list to be reachable\n    // through tab key as we do not intend to provide any special accessibility treatment. The\n    // accessibility treatment depends on how the end-user will interact with it.\n    _isNonInteractive = false;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatNavList_BaseFactory;\n      return function MatNavList_Factory(__ngFactoryType__) {\n        return (ɵMatNavList_BaseFactory || (ɵMatNavList_BaseFactory = i0.ɵɵgetInheritedFactory(MatNavList)))(__ngFactoryType__ || MatNavList);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatNavList,\n      selectors: [[\"mat-nav-list\"]],\n      hostAttrs: [\"role\", \"navigation\", 1, \"mat-mdc-nav-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n      exportAs: [\"matNavList\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatListBase,\n        useExisting: MatNavList\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatNavList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [_c1],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatNavList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MAT_SELECTION_LIST_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatSelectionList),\n  multi: true\n};\n/** Change event that is being fired whenever the selected state of an option changes. */\nclass MatSelectionListChange {\n  source;\n  options;\n  constructor(/** Reference to the selection list that emitted the event. */\n  source, /** Reference to the options that have been changed. */\n  options) {\n    this.source = source;\n    this.options = options;\n  }\n}\nlet MatSelectionList = /*#__PURE__*/(() => {\n  class MatSelectionList extends MatListBase {\n    _element = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _initialized = false;\n    _keyManager;\n    _listenerCleanups;\n    /** Emits when the list has been destroyed. */\n    _destroyed = new Subject();\n    /** Whether the list has been destroyed. */\n    _isDestroyed;\n    /** View to model callback that should be called whenever the selected options change. */\n    _onChange = _ => {};\n    _items;\n    /** Emits a change event whenever the selected state of an option changes. */\n    selectionChange = new EventEmitter();\n    /**\n     * Theme color of the selection list. This sets the checkbox color for all\n     * list options. This API is supported in M2 themes only, it has no effect in\n     * M3 themes. For color customization in M3, see https://material.angular.dev/components/list/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'accent';\n    /**\n     * Function used for comparing an option against the selected value when determining which\n     * options should appear as selected. The first argument is the value of an options. The second\n     * one is a value from the selected value. A boolean must be returned.\n     */\n    compareWith = (a1, a2) => a1 === a2;\n    /** Whether selection is limited to one or multiple items (default multiple). */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      const newValue = coerceBooleanProperty(value);\n      if (newValue !== this._multiple) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._initialized) {\n          throw new Error('Cannot change `multiple` mode of mat-selection-list after initialization.');\n        }\n        this._multiple = newValue;\n        this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n      }\n    }\n    _multiple = true;\n    /** Whether radio indicator for all list items is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** The currently selected options. */\n    selectedOptions = new SelectionModel(this._multiple);\n    /** Keeps track of the currently-selected value. */\n    _value;\n    /** View to model callback that should be called if the list or its options lost focus. */\n    _onTouched = () => {};\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    constructor() {\n      super();\n      this._isNonInteractive = false;\n    }\n    ngAfterViewInit() {\n      // Mark the selection list as initialized so that the `multiple`\n      // binding can no longer be changed.\n      this._initialized = true;\n      this._setupRovingTabindex();\n      // These events are bound outside the zone, because they don't change\n      // any change-detected properties and they can trigger timeouts.\n      this._ngZone.runOutsideAngular(() => {\n        this._listenerCleanups = [this._renderer.listen(this._element.nativeElement, 'focusin', this._handleFocusin), this._renderer.listen(this._element.nativeElement, 'focusout', this._handleFocusout)];\n      });\n      if (this._value) {\n        this._setOptionsFromValues(this._value);\n      }\n      this._watchForSelectionChange();\n    }\n    ngOnChanges(changes) {\n      const disabledChanges = changes['disabled'];\n      const disableRippleChanges = changes['disableRipple'];\n      const hideSingleSelectionIndicatorChanges = changes['hideSingleSelectionIndicator'];\n      if (disableRippleChanges && !disableRippleChanges.firstChange || disabledChanges && !disabledChanges.firstChange || hideSingleSelectionIndicatorChanges && !hideSingleSelectionIndicatorChanges.firstChange) {\n        this._markOptionsForCheck();\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._listenerCleanups?.forEach(current => current());\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._isDestroyed = true;\n    }\n    /** Focuses the selection list. */\n    focus(options) {\n      this._element.nativeElement.focus(options);\n    }\n    /** Selects all of the options. Returns the options that changed as a result. */\n    selectAll() {\n      return this._setAllOptionsSelected(true);\n    }\n    /** Deselects all of the options. Returns the options that changed as a result. */\n    deselectAll() {\n      return this._setAllOptionsSelected(false);\n    }\n    /** Reports a value change to the ControlValueAccessor */\n    _reportValueChange() {\n      // Stop reporting value changes after the list has been destroyed. This avoids\n      // cases where the list might wrongly reset its value once it is removed, but\n      // the form control is still live.\n      if (this.options && !this._isDestroyed) {\n        const value = this._getSelectedOptionValues();\n        this._onChange(value);\n        this._value = value;\n      }\n    }\n    /** Emits a change event if the selected state of an option changed. */\n    _emitChangeEvent(options) {\n      this.selectionChange.emit(new MatSelectionListChange(this, options));\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(values) {\n      this._value = values;\n      if (this.options) {\n        this._setOptionsFromValues(values || []);\n      }\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetectorRef.markForCheck();\n      this._markOptionsForCheck();\n    }\n    /**\n     * Whether the *entire* selection list is disabled. When true, each list item is also disabled\n     * and each list item is removed from the tab order (has tabindex=\"-1\").\n     */\n    get disabled() {\n      return this._selectionListDisabled();\n    }\n    set disabled(value) {\n      // Update the disabled state of this list. Write to `this._selectionListDisabled` instead of\n      // `super.disabled`. That is to avoid closure compiler compatibility issues with assigning to\n      // a super property.\n      this._selectionListDisabled.set(coerceBooleanProperty(value));\n      if (this._selectionListDisabled()) {\n        this._keyManager?.setActiveItem(-1);\n      }\n    }\n    _selectionListDisabled = signal(false);\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /** Watches for changes in the selected state of the options and updates the list accordingly. */\n    _watchForSelectionChange() {\n      this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n        // Sync external changes to the model back to the options.\n        for (let item of event.added) {\n          item.selected = true;\n        }\n        for (let item of event.removed) {\n          item.selected = false;\n        }\n        if (!this._containsFocus()) {\n          this._resetActiveOption();\n        }\n      });\n    }\n    /** Sets the selected options based on the specified values. */\n    _setOptionsFromValues(values) {\n      this.options.forEach(option => option._setSelected(false));\n      values.forEach(value => {\n        const correspondingOption = this.options.find(option => {\n          // Skip options that are already in the model. This allows us to handle cases\n          // where the same primitive value is selected multiple times.\n          return option.selected ? false : this.compareWith(option.value, value);\n        });\n        if (correspondingOption) {\n          correspondingOption._setSelected(true);\n        }\n      });\n    }\n    /** Returns the values of the selected options. */\n    _getSelectedOptionValues() {\n      return this.options.filter(option => option.selected).map(option => option.value);\n    }\n    /** Marks all the options to be checked in the next change detection run. */\n    _markOptionsForCheck() {\n      if (this.options) {\n        this.options.forEach(option => option._markForCheck());\n      }\n    }\n    /**\n     * Sets the selected state on all of the options\n     * and emits an event if anything changed.\n     */\n    _setAllOptionsSelected(isSelected, skipDisabled) {\n      // Keep track of whether anything changed, because we only want to\n      // emit the changed event when something actually changed.\n      const changedOptions = [];\n      this.options.forEach(option => {\n        if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n          changedOptions.push(option);\n        }\n      });\n      if (changedOptions.length) {\n        this._reportValueChange();\n      }\n      return changedOptions;\n    }\n    // Note: This getter exists for backwards compatibility. The `_items` query list\n    // cannot be named `options` as it will be picked up by the interactive list base.\n    /** The option components contained within this selection-list. */\n    get options() {\n      return this._items;\n    }\n    /** Handles keydown events within the list. */\n    _handleKeydown(event) {\n      const activeItem = this._keyManager.activeItem;\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this._keyManager.isTyping() && activeItem && !activeItem.disabled) {\n        event.preventDefault();\n        activeItem._toggleOnInteraction();\n      } else if (event.keyCode === A && this.multiple && !this._keyManager.isTyping() && hasModifierKey(event, 'ctrlKey', 'metaKey')) {\n        const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n        event.preventDefault();\n        this._emitChangeEvent(this._setAllOptionsSelected(shouldSelect, true));\n      } else {\n        this._keyManager.onKeydown(event);\n      }\n    }\n    /** Handles focusout events within the list. */\n    _handleFocusout = () => {\n      // Focus takes a while to update so we have to wrap our call in a timeout.\n      setTimeout(() => {\n        if (!this._containsFocus()) {\n          this._resetActiveOption();\n        }\n      });\n    };\n    /** Handles focusin events within the list. */\n    _handleFocusin = event => {\n      if (this.disabled) {\n        return;\n      }\n      const activeIndex = this._items.toArray().findIndex(item => item._elementRef.nativeElement.contains(event.target));\n      if (activeIndex > -1) {\n        this._setActiveOption(activeIndex);\n      } else {\n        this._resetActiveOption();\n      }\n    };\n    /**\n     * Sets up the logic for maintaining the roving tabindex.\n     *\n     * `skipPredicate` determines if key manager should avoid putting a given list item in the tab\n     * index. Allow disabled list items to receive focus to align with WAI ARIA recommendation.\n     * Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n     * makes a few exceptions for compound widgets.\n     *\n     * From [Developing a Keyboard Interface](\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n     *   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n     *   Listbox...\"\n     */\n    _setupRovingTabindex() {\n      this._keyManager = new FocusKeyManager(this._items).withHomeAndEnd().withTypeAhead().withWrap().skipPredicate(() => this.disabled);\n      // Set the initial focus.\n      this._resetActiveOption();\n      // Move the tabindex to the currently-focused list item.\n      this._keyManager.change.subscribe(activeItemIndex => this._setActiveOption(activeItemIndex));\n      // If the active item is removed from the list, reset back to the first one.\n      this._items.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        const activeItem = this._keyManager.activeItem;\n        if (!activeItem || this._items.toArray().indexOf(activeItem) === -1) {\n          this._resetActiveOption();\n        }\n      });\n    }\n    /**\n     * Sets an option as active.\n     * @param index Index of the active option. If set to -1, no option will be active.\n     */\n    _setActiveOption(index) {\n      this._items.forEach((item, itemIndex) => item._setTabindex(itemIndex === index ? 0 : -1));\n      this._keyManager.updateActiveItem(index);\n    }\n    /**\n     * Resets the active option. When the list is disabled, remove all options from to the tab order.\n     * Otherwise, focus the first selected option.\n     */\n    _resetActiveOption() {\n      if (this.disabled) {\n        this._setActiveOption(-1);\n        return;\n      }\n      const activeItem = this._items.find(item => item.selected && !item.disabled) || this._items.first;\n      this._setActiveOption(activeItem ? this._items.toArray().indexOf(activeItem) : -1);\n    }\n    /** Returns whether the focus is currently within the list. */\n    _containsFocus() {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      return activeElement && this._element.nativeElement.contains(activeElement);\n    }\n    static ɵfac = function MatSelectionList_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectionList)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelectionList,\n      selectors: [[\"mat-selection-list\"]],\n      contentQueries: function MatSelectionList_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatListOption, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"listbox\", 1, \"mat-mdc-selection-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n      hostVars: 1,\n      hostBindings: function MatSelectionList_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatSelectionList_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-multiselectable\", ctx.multiple);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        compareWith: \"compareWith\",\n        multiple: \"multiple\",\n        hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      exportAs: [\"matSelectionList\"],\n      features: [i0.ɵɵProvidersFeature([MAT_SELECTION_LIST_VALUE_ACCESSOR, {\n        provide: MatListBase,\n        useExisting: MatSelectionList\n      }, {\n        provide: SELECTION_LIST,\n        useExisting: MatSelectionList\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatSelectionList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [_c1],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSelectionList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatListModule = /*#__PURE__*/(() => {\n  class MatListModule {\n    static ɵfac = function MatListModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatListModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatListModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ObserversModule, MatCommonModule, MatRippleModule, MatPseudoCheckboxModule, MatDividerModule]\n    });\n  }\n  return MatListModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_LIST, MAT_LIST_CONFIG, MAT_NAV_LIST, MAT_SELECTION_LIST_VALUE_ACCESSOR, MatActionList, MatList, MatListItem, MatListItemAvatar, MatListItemIcon, MatListItemLine, MatListItemMeta, MatListItemTitle, MatListModule, MatListOption, MatListSubheaderCssMatStyler, MatNavList, MatSelectionList, MatSelectionListChange, SELECTION_LIST, _MatListItemGraphicBase };", "map": {"version": 3, "names": ["coerceBooleanProperty", "coerceNumberProperty", "i0", "InjectionToken", "inject", "ElementRef", "Directive", "signal", "Input", "NgZone", "Injector", "ContentChildren", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "ChangeDetectorRef", "EventEmitter", "Output", "forwardRef", "Renderer2", "NgModule", "Platform", "_getFocusedElementPierceShadowDom", "_CdkPrivateStyleLoader", "Subscription", "merge", "Subject", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "R", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "_animationsDisabled", "_StructuralStylesLoader", "NgTemplateOutlet", "CdkObserveContent", "ObserversModule", "MatDividerModule", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "MatListOption_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "MatListOption_ng_template_2_Template", "ɵɵelementStart", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ctx_r1", "ɵɵnextContext", "ɵɵclassProp", "disabled", "ɵɵadvance", "ɵɵproperty", "selected", "MatListOption_ng_template_4_Template", "MatListOption_Conditional_6_ng_template_1_Template", "MatListOption_Conditional_6_Template", "ɵɵtemplate", "checkbox_r3", "ɵɵreference", "MatListOption_Conditional_7_ng_template_1_Template", "MatListOption_Conditional_7_Template", "radio_r4", "MatListOption_Conditional_8_ng_template_0_Template", "MatListOption_Conditional_8_Template", "icons_r5", "MatListOption_Conditional_15_ng_template_1_Template", "MatListOption_Conditional_15_Template", "MatListOption_Conditional_16_ng_template_1_Template", "MatListOption_Conditional_16_Template", "MatListOption_Conditional_17_ng_template_0_Template", "MatListOption_Conditional_17_Template", "<PERSON><PERSON><PERSON><PERSON>", "FocusKeyManager", "SelectionModel", "ENTER", "SPACE", "A", "hasModifierKey", "NG_VALUE_ACCESSOR", "takeUntil", "M", "MatCommonModule", "MatRippleModule", "MatPseudoCheckboxModule", "LIST_OPTION", "MatListItemTitle", "_elementRef", "constructor", "ɵfac", "MatListItemTitle_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "MatListItemLine", "MatListItemLine_Factory", "MatListItemMeta", "MatListItemMeta_Factory", "_MatListItemGraphicBase", "_listOption", "optional", "_isAlignedAtStart", "_getTogglePosition", "_MatListItemGraphicBase_Factory", "hostVars", "hostBindings", "_MatListItemGraphicBase_HostBindings", "MatListItemAvatar", "ɵMatListItemAvatar_BaseFactory", "MatListItemAvatar_Factory", "ɵɵgetInheritedFactory", "features", "ɵɵInheritDefinitionFeature", "MatListItemIcon", "ɵMatListItemIcon_BaseFactory", "MatListItemIcon_Factory", "MAT_LIST_CONFIG", "MatListBase", "_isNonInteractive", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "value", "_disabled", "set", "_defaultOptions", "MatListBase_Factory", "MatListBase_HostBindings", "ɵɵattribute", "inputs", "MatListItemBase", "_ngZone", "_listBase", "_platform", "_hostElement", "_isButtonElement", "_noopAnimations", "_avatars", "_icons", "lines", "_explicitLines", "_updateItemLines", "_subscriptions", "_ripple<PERSON><PERSON>er", "_hasUnscopedTextContent", "rippleConfig", "rippleDisabled", "load", "globalRippleOptions", "nativeElement", "nodeName", "toLowerCase", "_initInteractiveListItem", "hasAttribute", "setAttribute", "ngAfterViewInit", "_monitorProjectedLinesAndTitle", "ngOnDestroy", "unsubscribe", "_removeTriggerEvents", "_hasIconOrAvatar", "length", "classList", "add", "setupTriggerEvents", "runOutsideAngular", "_lines", "changes", "_titles", "subscribe", "recheckUnscopedContent", "_unscopedContent", "_checkDomForUnscopedTextContent", "sanityCheckListItemContent", "numberOfLines", "_inferLinesFromContent", "unscopedContentEl", "toggle", "treatAsTitle", "remove", "numOfLines", "Array", "from", "childNodes", "filter", "node", "nodeType", "COMMENT_NODE", "some", "textContent", "trim", "MatListItemBase_Factory", "contentQueries", "MatListItemBase_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "MatListItemBase_HostBindings", "item", "numTitles", "numLines", "console", "warn", "MatActionList", "ɵMatActionList_BaseFactory", "MatActionList_Factory", "ɵcmp", "ɵɵdefineComponent", "exportAs", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "decls", "vars", "template", "MatActionList_Template", "ɵɵprojectionDef", "styles", "encapsulation", "changeDetection", "MAT_LIST", "MatList", "ɵMatList_BaseFactory", "MatList_Factory", "MatList_Template", "MatListItem", "_meta", "_itemText", "activated", "_activated", "_getAriaCurrent", "_hasBothLeadingAndTrailing", "ɵMatListItem_BaseFactory", "MatListItem_Factory", "MatListItem_ContentQueries", "viewQuery", "MatListItem_Query", "ɵɵviewQuery", "first", "MatListItem_HostBindings", "consts", "MatListItem_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatListItem_Template_span_cdkObserveContent_4_listener", "ɵɵrestoreView", "ɵɵresetView", "dependencies", "SELECTION_LIST", "MatListOption", "_selectionList", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON>", "togglePosition", "checkboxPosition", "color", "_color", "newValue", "_value", "_inputsInitialized", "selectedOptions", "isSelected", "_selected", "_setSelected", "multiple", "_reportValueChange", "ngOnInit", "list", "compareWith", "wasSelected", "Promise", "resolve", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focus", "get<PERSON><PERSON><PERSON>", "titleElement", "get", "labelEl", "_hasCheckboxAt", "position", "_hasRadioAt", "hideSingleSelectionIndicator", "_hasIconsOrAvatarsAt", "_hasProjected", "_handleBlur", "_onTouched", "select", "deselect", "emit", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toggleOnInteraction", "_emitChangeEvent", "_setTabindex", "hasLeading", "hasTrailing", "ɵMatListOption_BaseFactory", "MatListOption_Factory", "MatListOption_ContentQueries", "MatListOption_Query", "MatListOption_HostBindings", "MatListOption_blur_HostBindingHandler", "MatListOption_click_HostBindingHandler", "outputs", "MatListOption_Template", "ɵɵtemplateRefExtractor", "ɵɵconditionalCreate", "MatListOption_Template_span_cdkObserveContent_12_listener", "ɵɵconditional", "MatListSubheaderCssMatStyler", "MatListSubheaderCssMatStyler_Factory", "MAT_NAV_LIST", "MatNavList", "ɵMatNavList_BaseFactory", "MatNavList_Factory", "MatNavList_Template", "MAT_SELECTION_LIST_VALUE_ACCESSOR", "MatSelectionList", "multi", "MatSelectionListChange", "source", "options", "_element", "_renderer", "_initialized", "_keyManager", "_listenerCleanups", "_destroyed", "_isDestroyed", "_onChange", "_items", "selectionChange", "a1", "a2", "_multiple", "Error", "_hideSingleSelectionIndicator", "_setupRovingTabindex", "listen", "_handleFocusin", "_handleFocusout", "_setOptionsFromValues", "_watchForSelectionChange", "ngOnChanges", "disabled<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON>ple<PERSON>hang<PERSON>", "hideSingleSelectionIndicatorChanges", "firstChange", "_markOptionsForCheck", "destroy", "for<PERSON>ach", "current", "next", "complete", "selectAll", "_setAllOptionsSelected", "deselectAll", "_getSelectedOptionValues", "writeValue", "values", "setDisabledState", "isDisabled", "_selectionListDisabled", "setActiveItem", "registerOnChange", "fn", "registerOnTouched", "changed", "pipe", "event", "added", "removed", "_containsFocus", "_resetActiveOption", "option", "correspondingOption", "find", "map", "skipDisabled", "changedOptions", "push", "_handleKeydown", "activeItem", "keyCode", "isTyping", "preventDefault", "shouldSelect", "onKeydown", "setTimeout", "activeIndex", "toArray", "findIndex", "contains", "target", "_setActiveOption", "withHomeAndEnd", "withTypeAhead", "withWrap", "skipPredicate", "change", "activeItemIndex", "indexOf", "index", "itemIndex", "updateActiveItem", "activeElement", "MatSelectionList_Factory", "MatSelectionList_ContentQueries", "MatSelectionList_HostBindings", "MatSelectionList_keydown_HostBindingHandler", "$event", "ɵɵNgOnChangesFeature", "MatSelectionList_Template", "MatListModule", "MatListModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/list.mjs"], "sourcesContent": ["import { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, signal, Input, NgZone, Injector, ContentChildren, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ChangeDetectorRef, EventEmitter, Output, forwardRef, Renderer2, NgModule } from '@angular/core';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { Subscription, merge, Subject } from 'rxjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer } from './ripple-BYgV4oZC.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { CdkObserveContent, ObserversModule } from '@angular/cdk/observers';\nimport { MatDividerModule } from './divider.mjs';\nexport { MatDivider } from './divider.mjs';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ENTER, SPACE, A, hasModifierKey } from '@angular/cdk/keycodes';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\n\n/**\n * Injection token that can be used to reference instances of an `ListOption`. It serves\n * as alternative token to an actual implementation which could result in undesired\n * retention of the class or circular references breaking runtime execution.\n * @docs-private\n */\nconst LIST_OPTION = new InjectionToken('ListOption');\n\n/**\n * Directive capturing the title of a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content for the title never wraps. There can only be a single title per list item.\n */\nclass MatListItemTitle {\n    _elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemTitle, isStandalone: true, selector: \"[matListItemTitle]\", host: { classAttribute: \"mat-mdc-list-item-title mdc-list-item__primary-text\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matListItemTitle]',\n                    host: { 'class': 'mat-mdc-list-item-title mdc-list-item__primary-text' },\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive capturing a line in a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content inside a line never wraps. There can be at maximum two lines per list item.\n */\nclass MatListItemLine {\n    _elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemLine, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemLine, isStandalone: true, selector: \"[matListItemLine]\", host: { classAttribute: \"mat-mdc-list-item-line mdc-list-item__secondary-text\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matListItemLine]',\n                    host: { 'class': 'mat-mdc-list-item-line mdc-list-item__secondary-text' },\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive matching an optional meta section for list items.\n *\n * List items can reserve space at the end of an item to display a control,\n * button or additional text content.\n */\nclass MatListItemMeta {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemMeta, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemMeta, isStandalone: true, selector: \"[matListItemMeta]\", host: { classAttribute: \"mat-mdc-list-item-meta mdc-list-item__end\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemMeta, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matListItemMeta]',\n                    host: { 'class': 'mat-mdc-list-item-meta mdc-list-item__end' },\n                }]\n        }] });\n/**\n * @docs-private\n *\n * MDC uses the very intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n * position content such as icons or checkboxes/radios that comes either before or after the text\n * content respectively. This directive detects the placement of the checkbox/radio and applies the\n * correct MDC class to position the icon/avatar on the opposite side.\n */\nclass _MatListItemGraphicBase {\n    _listOption = inject(LIST_OPTION, { optional: true });\n    constructor() { }\n    _isAlignedAtStart() {\n        // By default, in all list items the graphic is aligned at start. In list options,\n        // the graphic is only aligned at start if the checkbox/radio is at the end.\n        return !this._listOption || this._listOption?._getTogglePosition() === 'after';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatListItemGraphicBase, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _MatListItemGraphicBase, isStandalone: true, host: { properties: { \"class.mdc-list-item__start\": \"_isAlignedAtStart()\", \"class.mdc-list-item__end\": \"!_isAlignedAtStart()\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatListItemGraphicBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        // MDC uses intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n                        // position content such as icons or checkboxes/radios that comes either before or after the\n                        // text content respectively. This directive detects the placement of the checkbox/radio and\n                        // applies the correct MDC class to position the icon/avatar on the opposite side.\n                        '[class.mdc-list-item__start]': '_isAlignedAtStart()',\n                        '[class.mdc-list-item__end]': '!_isAlignedAtStart()',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive matching an optional avatar within a list item.\n *\n * List items can reserve space at the beginning of an item to display an avatar.\n */\nclass MatListItemAvatar extends _MatListItemGraphicBase {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemAvatar, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemAvatar, isStandalone: true, selector: \"[matListItemAvatar]\", host: { classAttribute: \"mat-mdc-list-item-avatar\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matListItemAvatar]',\n                    host: { 'class': 'mat-mdc-list-item-avatar' },\n                }]\n        }] });\n/**\n * Directive matching an optional icon within a list item.\n *\n * List items can reserve space at the beginning of an item to display an icon.\n */\nclass MatListItemIcon extends _MatListItemGraphicBase {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemIcon, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemIcon, isStandalone: true, selector: \"[matListItemIcon]\", host: { classAttribute: \"mat-mdc-list-item-icon\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matListItemIcon]',\n                    host: { 'class': 'mat-mdc-list-item-icon' },\n                }]\n        }] });\n\n/** Injection token that can be used to provide the default options for the list module. */\nconst MAT_LIST_CONFIG = new InjectionToken('MAT_LIST_CONFIG');\n\n/** @docs-private */\nclass MatListBase {\n    _isNonInteractive = true;\n    /** Whether ripples for all list items is disabled. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    _disableRipple = false;\n    /**\n     * Whether the entire list is disabled. When disabled, the list itself and each of its list items\n     * are disabled.\n     */\n    get disabled() {\n        return this._disabled();\n    }\n    set disabled(value) {\n        this._disabled.set(coerceBooleanProperty(value));\n    }\n    _disabled = signal(false);\n    _defaultOptions = inject(MAT_LIST_CONFIG, { optional: true });\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListBase, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListBase, isStandalone: true, inputs: { disableRipple: \"disableRipple\", disabled: \"disabled\" }, host: { properties: { \"attr.aria-disabled\": \"disabled\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[attr.aria-disabled]': 'disabled',\n                    },\n                }]\n        }], propDecorators: { disableRipple: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n/** @docs-private */\nclass MatListItemBase {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _listBase = inject(MatListBase, { optional: true });\n    _platform = inject(Platform);\n    /** Host element for the list item. */\n    _hostElement;\n    /** indicate whether the host element is a button or not */\n    _isButtonElement;\n    /** Whether animations are disabled. */\n    _noopAnimations = _animationsDisabled();\n    _avatars;\n    _icons;\n    /**\n     * The number of lines this list item should reserve space for. If not specified,\n     * lines are inferred based on the projected content.\n     *\n     * Explicitly specifying the number of lines is useful if you want to acquire additional\n     * space and enable the wrapping of text. The unscoped text content of a list item will\n     * always be able to take up the remaining space of the item, unless it represents the title.\n     *\n     * A maximum of three lines is supported as per the Material Design specification.\n     */\n    set lines(lines) {\n        this._explicitLines = coerceNumberProperty(lines, null);\n        this._updateItemLines(false);\n    }\n    _explicitLines = null;\n    /** Whether ripples for list items are disabled. */\n    get disableRipple() {\n        return (this.disabled ||\n            this._disableRipple ||\n            this._noopAnimations ||\n            !!this._listBase?.disableRipple);\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    _disableRipple = false;\n    /** Whether the list-item is disabled. */\n    get disabled() {\n        return this._disabled() || !!this._listBase?.disabled;\n    }\n    set disabled(value) {\n        this._disabled.set(coerceBooleanProperty(value));\n    }\n    _disabled = signal(false);\n    _subscriptions = new Subscription();\n    _rippleRenderer = null;\n    /** Whether the list item has unscoped text content. */\n    _hasUnscopedTextContent = false;\n    /**\n     * Implemented as part of `RippleTarget`.\n     * @docs-private\n     */\n    rippleConfig;\n    /**\n     * Implemented as part of `RippleTarget`.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return this.disableRipple || !!this.rippleConfig.disabled;\n    }\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n            optional: true,\n        });\n        this.rippleConfig = globalRippleOptions || {};\n        this._hostElement = this._elementRef.nativeElement;\n        this._isButtonElement = this._hostElement.nodeName.toLowerCase() === 'button';\n        if (this._listBase && !this._listBase._isNonInteractive) {\n            this._initInteractiveListItem();\n        }\n        // If no type attribute is specified for a host `<button>` element, set it to `button`. If a\n        // type attribute is already specified, we do nothing. We do this for backwards compatibility.\n        // TODO: Determine if we intend to continue doing this for the MDC-based list.\n        if (this._isButtonElement && !this._hostElement.hasAttribute('type')) {\n            this._hostElement.setAttribute('type', 'button');\n        }\n    }\n    ngAfterViewInit() {\n        this._monitorProjectedLinesAndTitle();\n        this._updateItemLines(true);\n    }\n    ngOnDestroy() {\n        this._subscriptions.unsubscribe();\n        if (this._rippleRenderer !== null) {\n            this._rippleRenderer._removeTriggerEvents();\n        }\n    }\n    /** Whether the list item has icons or avatars. */\n    _hasIconOrAvatar() {\n        return !!(this._avatars.length || this._icons.length);\n    }\n    _initInteractiveListItem() {\n        this._hostElement.classList.add('mat-mdc-list-item-interactive');\n        this._rippleRenderer = new RippleRenderer(this, this._ngZone, this._hostElement, this._platform, inject(Injector));\n        this._rippleRenderer.setupTriggerEvents(this._hostElement);\n    }\n    /**\n     * Subscribes to changes in the projected title and lines. Triggers a\n     * item lines update whenever a change occurs.\n     */\n    _monitorProjectedLinesAndTitle() {\n        this._ngZone.runOutsideAngular(() => {\n            this._subscriptions.add(merge(this._lines.changes, this._titles.changes).subscribe(() => this._updateItemLines(false)));\n        });\n    }\n    /**\n     * Updates the lines of the list item. Based on the projected user content and optional\n     * explicit lines setting, the visual appearance of the list item is determined.\n     *\n     * This method should be invoked whenever the projected user content changes, or\n     * when the explicit lines have been updated.\n     *\n     * @param recheckUnscopedContent Whether the projected unscoped content should be re-checked.\n     *   The unscoped content is not re-checked for every update as it is a rather expensive check\n     *   for content that is expected to not change very often.\n     */\n    _updateItemLines(recheckUnscopedContent) {\n        // If the updated is triggered too early before the view and content is initialized,\n        // we just skip the update. After view initialization the update is triggered again.\n        if (!this._lines || !this._titles || !this._unscopedContent) {\n            return;\n        }\n        // Re-check the DOM for unscoped text content if requested. This needs to\n        // happen before any computation or sanity checks run as these rely on the\n        // result of whether there is unscoped text content or not.\n        if (recheckUnscopedContent) {\n            this._checkDomForUnscopedTextContent();\n        }\n        // Sanity check the list item lines and title in the content. This is a dev-mode only\n        // check that can be dead-code eliminated by Terser in production.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            sanityCheckListItemContent(this);\n        }\n        const numberOfLines = this._explicitLines ?? this._inferLinesFromContent();\n        const unscopedContentEl = this._unscopedContent.nativeElement;\n        // Update the list item element to reflect the number of lines.\n        this._hostElement.classList.toggle('mat-mdc-list-item-single-line', numberOfLines <= 1);\n        this._hostElement.classList.toggle('mdc-list-item--with-one-line', numberOfLines <= 1);\n        this._hostElement.classList.toggle('mdc-list-item--with-two-lines', numberOfLines === 2);\n        this._hostElement.classList.toggle('mdc-list-item--with-three-lines', numberOfLines === 3);\n        // If there is no title and the unscoped content is the is the only line, the\n        // unscoped text content will be treated as the title of the list-item.\n        if (this._hasUnscopedTextContent) {\n            const treatAsTitle = this._titles.length === 0 && numberOfLines === 1;\n            unscopedContentEl.classList.toggle('mdc-list-item__primary-text', treatAsTitle);\n            unscopedContentEl.classList.toggle('mdc-list-item__secondary-text', !treatAsTitle);\n        }\n        else {\n            unscopedContentEl.classList.remove('mdc-list-item__primary-text');\n            unscopedContentEl.classList.remove('mdc-list-item__secondary-text');\n        }\n    }\n    /**\n     * Infers the number of lines based on the projected user content. This is useful\n     * if no explicit number of lines has been specified on the list item.\n     *\n     * The number of lines is inferred based on whether there is a title, the number of\n     * additional lines (secondary/tertiary). An additional line is acquired if there is\n     * unscoped text content.\n     */\n    _inferLinesFromContent() {\n        let numOfLines = this._titles.length + this._lines.length;\n        if (this._hasUnscopedTextContent) {\n            numOfLines += 1;\n        }\n        return numOfLines;\n    }\n    /** Checks whether the list item has unscoped text content. */\n    _checkDomForUnscopedTextContent() {\n        this._hasUnscopedTextContent = Array.from(this._unscopedContent.nativeElement.childNodes)\n            .filter(node => node.nodeType !== node.COMMENT_NODE)\n            .some(node => !!(node.textContent && node.textContent.trim()));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemBase, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItemBase, isStandalone: true, inputs: { lines: \"lines\", disableRipple: \"disableRipple\", disabled: \"disabled\" }, host: { properties: { \"class.mdc-list-item--disabled\": \"disabled\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"(_isButtonElement && disabled) || null\" } }, queries: [{ propertyName: \"_avatars\", predicate: MatListItemAvatar }, { propertyName: \"_icons\", predicate: MatListItemIcon }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItemBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': '(_isButtonElement && disabled) || null',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { _avatars: [{\n                type: ContentChildren,\n                args: [MatListItemAvatar, { descendants: false }]\n            }], _icons: [{\n                type: ContentChildren,\n                args: [MatListItemIcon, { descendants: false }]\n            }], lines: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n/**\n * Sanity checks the configuration of the list item with respect to the amount\n * of lines, whether there is a title, or if there is unscoped text content.\n *\n * The checks are extracted into a top-level function that can be dead-code\n * eliminated by Terser or other optimizers in production mode.\n */\nfunction sanityCheckListItemContent(item) {\n    const numTitles = item._titles.length;\n    const numLines = item._lines.length;\n    if (numTitles > 1) {\n        console.warn('A list item cannot have multiple titles.');\n    }\n    if (numTitles === 0 && numLines > 0) {\n        console.warn('A list item line can only be used if there is a list item title.');\n    }\n    if (numTitles === 0 &&\n        item._hasUnscopedTextContent &&\n        item._explicitLines !== null &&\n        item._explicitLines > 1) {\n        console.warn('A list item cannot have wrapping content without a title.');\n    }\n    if (numLines > 2 || (numLines === 2 && item._hasUnscopedTextContent)) {\n        console.warn('A list item can have at maximum three lines.');\n    }\n}\n\nclass MatActionList extends MatListBase {\n    // An navigation list is considered interactive, but does not extend the interactive list\n    // base class. We do this because as per MDC, items of interactive lists are only reachable\n    // through keyboard shortcuts. We want all items for the navigation list to be reachable\n    // through tab key as we do not intend to provide any special accessibility treatment. The\n    // accessibility treatment depends on how the end-user will interact with it.\n    _isNonInteractive = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatActionList, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatActionList, isStandalone: true, selector: \"mat-action-list\", host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-action-list mat-mdc-list-base mdc-list\" }, providers: [{ provide: MatListBase, useExisting: MatActionList }], exportAs: [\"matActionList\"], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatActionList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-action-list', exportAs: 'matActionList', template: '<ng-content></ng-content>', host: {\n                        'class': 'mat-mdc-action-list mat-mdc-list-base mdc-list',\n                        'role': 'group',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MatListBase, useExisting: MatActionList }], styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"] }]\n        }] });\n\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_LIST = new InjectionToken('MatList');\nclass MatList extends MatListBase {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatList, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatList, isStandalone: true, selector: \"mat-list\", host: { classAttribute: \"mat-mdc-list mat-mdc-list-base mdc-list\" }, providers: [{ provide: MatListBase, useExisting: MatList }], exportAs: [\"matList\"], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list', exportAs: 'matList', template: '<ng-content></ng-content>', host: {\n                        'class': 'mat-mdc-list mat-mdc-list-base mdc-list',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MatListBase, useExisting: MatList }], styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"] }]\n        }] });\nclass MatListItem extends MatListItemBase {\n    _lines;\n    _titles;\n    _meta;\n    _unscopedContent;\n    _itemText;\n    /** Indicates whether an item in a `<mat-nav-list>` is the currently active page. */\n    get activated() {\n        return this._activated;\n    }\n    set activated(activated) {\n        this._activated = coerceBooleanProperty(activated);\n    }\n    _activated = false;\n    /**\n     * Determine the value of `aria-current`. Return 'page' if this item is an activated anchor tag.\n     * Otherwise, return `null`. This method is safe to use with server-side rendering.\n     */\n    _getAriaCurrent() {\n        return this._hostElement.nodeName === 'A' && this._activated ? 'page' : null;\n    }\n    _hasBothLeadingAndTrailing() {\n        return this._meta.length !== 0 && (this._avatars.length !== 0 || this._icons.length !== 0);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItem, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListItem, isStandalone: true, selector: \"mat-list-item, a[mat-list-item], button[mat-list-item]\", inputs: { activated: \"activated\" }, host: { properties: { \"class.mdc-list-item--activated\": \"activated\", \"class.mdc-list-item--with-leading-avatar\": \"_avatars.length !== 0\", \"class.mdc-list-item--with-leading-icon\": \"_icons.length !== 0\", \"class.mdc-list-item--with-trailing-meta\": \"_meta.length !== 0\", \"class.mat-mdc-list-item-both-leading-and-trailing\": \"_hasBothLeadingAndTrailing()\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"attr.aria-current\": \"_getAriaCurrent()\" }, classAttribute: \"mat-mdc-list-item mdc-list-item\" }, queries: [{ propertyName: \"_lines\", predicate: MatListItemLine, descendants: true }, { propertyName: \"_titles\", predicate: MatListItemTitle, descendants: true }, { propertyName: \"_meta\", predicate: MatListItemMeta, descendants: true }], viewQueries: [{ propertyName: \"_unscopedContent\", first: true, predicate: [\"unscopedContent\"], descendants: true }, { propertyName: \"_itemText\", first: true, predicate: [\"text\"], descendants: true }], exportAs: [\"matListItem\"], usesInheritance: true, ngImport: i0, template: \"<ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n<ng-content select=\\\"[matListItemMeta]\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\", dependencies: [{ kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListItem, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list-item, a[mat-list-item], button[mat-list-item]', exportAs: 'matListItem', host: {\n                        'class': 'mat-mdc-list-item mdc-list-item',\n                        '[class.mdc-list-item--activated]': 'activated',\n                        '[class.mdc-list-item--with-leading-avatar]': '_avatars.length !== 0',\n                        '[class.mdc-list-item--with-leading-icon]': '_icons.length !== 0',\n                        '[class.mdc-list-item--with-trailing-meta]': '_meta.length !== 0',\n                        // Utility class that makes it easier to target the case where there's both a leading\n                        // and a trailing icon. Avoids having to write out all the combinations.\n                        '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [CdkObserveContent], template: \"<ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n<ng-content select=\\\"[matListItemMeta]\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\" }]\n        }], propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatListItemLine, { descendants: true }]\n            }], _titles: [{\n                type: ContentChildren,\n                args: [MatListItemTitle, { descendants: true }]\n            }], _meta: [{\n                type: ContentChildren,\n                args: [MatListItemMeta, { descendants: true }]\n            }], _unscopedContent: [{\n                type: ViewChild,\n                args: ['unscopedContent']\n            }], _itemText: [{\n                type: ViewChild,\n                args: ['text']\n            }], activated: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of an `SelectionList`. It serves\n * as alternative token to an actual implementation which would result in circular references.\n * @docs-private\n */\nconst SELECTION_LIST = new InjectionToken('SelectionList');\nclass MatListOption extends MatListItemBase {\n    _selectionList = inject(SELECTION_LIST);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _lines;\n    _titles;\n    _unscopedContent;\n    /**\n     * Emits when the selected state of the option has changed.\n     * Use to facilitate two-data binding to the `selected` property.\n     * @docs-private\n     */\n    selectedChange = new EventEmitter();\n    /** Whether the label should appear before or after the checkbox/radio. Defaults to 'after' */\n    togglePosition = 'after';\n    /**\n     * Whether the label should appear before or after the checkbox/radio. Defaults to 'after'\n     *\n     * @deprecated Use `togglePosition` instead.\n     * @breaking-change 17.0.0\n     */\n    get checkboxPosition() {\n        return this.togglePosition;\n    }\n    set checkboxPosition(value) {\n        this.togglePosition = value;\n    }\n    /**\n     * Theme color of the list option. This sets the color of the checkbox/radio.\n     * This API is supported in M2 themes only, it has no effect in M3 themes. For color customization\n     * in M3, see https://material.angular.dev/components/list/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color || this._selectionList.color;\n    }\n    set color(newValue) {\n        this._color = newValue;\n    }\n    _color;\n    /** Value of the option */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        if (this.selected && newValue !== this.value && this._inputsInitialized) {\n            this.selected = false;\n        }\n        this._value = newValue;\n    }\n    _value;\n    /** Whether the option is selected. */\n    get selected() {\n        return this._selectionList.selectedOptions.isSelected(this);\n    }\n    set selected(value) {\n        const isSelected = coerceBooleanProperty(value);\n        if (isSelected !== this._selected) {\n            this._setSelected(isSelected);\n            if (isSelected || this._selectionList.multiple) {\n                this._selectionList._reportValueChange();\n            }\n        }\n    }\n    _selected = false;\n    /**\n     * This is set to true after the first OnChanges cycle so we don't\n     * clear the value of `selected` in the first cycle.\n     */\n    _inputsInitialized = false;\n    ngOnInit() {\n        const list = this._selectionList;\n        if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n            this._setSelected(true);\n        }\n        const wasSelected = this._selected;\n        // List options that are selected at initialization can't be reported properly to the form\n        // control. This is because it takes some time until the selection-list knows about all\n        // available options. Also it can happen that the ControlValueAccessor has an initial value\n        // that should be used instead. Deferring the value change report to the next tick ensures\n        // that the form control value is not being overwritten.\n        Promise.resolve().then(() => {\n            if (this._selected || wasSelected) {\n                this.selected = true;\n                this._changeDetectorRef.markForCheck();\n            }\n        });\n        this._inputsInitialized = true;\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this.selected) {\n            // We have to delay this until the next tick in order\n            // to avoid changed after checked errors.\n            Promise.resolve().then(() => {\n                this.selected = false;\n            });\n        }\n    }\n    /** Toggles the selection state of the option. */\n    toggle() {\n        this.selected = !this.selected;\n    }\n    /** Allows for programmatic focusing of the option. */\n    focus() {\n        this._hostElement.focus();\n    }\n    /** Gets the text label of the list option. Used for the typeahead functionality in the list. */\n    getLabel() {\n        const titleElement = this._titles?.get(0)?._elementRef.nativeElement;\n        // If there is no explicit title element, the unscoped text content\n        // is treated as the list item title.\n        const labelEl = titleElement || this._unscopedContent?.nativeElement;\n        return labelEl?.textContent || '';\n    }\n    /** Whether a checkbox is shown at the given position. */\n    _hasCheckboxAt(position) {\n        return this._selectionList.multiple && this._getTogglePosition() === position;\n    }\n    /** Where a radio indicator is shown at the given position. */\n    _hasRadioAt(position) {\n        return (!this._selectionList.multiple &&\n            this._getTogglePosition() === position &&\n            !this._selectionList.hideSingleSelectionIndicator);\n    }\n    /** Whether icons or avatars are shown at the given position. */\n    _hasIconsOrAvatarsAt(position) {\n        return this._hasProjected('icons', position) || this._hasProjected('avatars', position);\n    }\n    /** Gets whether the given type of element is projected at the specified position. */\n    _hasProjected(type, position) {\n        // If the checkbox/radio is shown at the specified position, neither icons or\n        // avatars can be shown at the position.\n        return (this._getTogglePosition() !== position &&\n            (type === 'avatars' ? this._avatars.length !== 0 : this._icons.length !== 0));\n    }\n    _handleBlur() {\n        this._selectionList._onTouched();\n    }\n    /** Gets the current position of the checkbox/radio. */\n    _getTogglePosition() {\n        return this.togglePosition || 'after';\n    }\n    /**\n     * Sets the selected state of the option.\n     * @returns Whether the value has changed.\n     */\n    _setSelected(selected) {\n        if (selected === this._selected) {\n            return false;\n        }\n        this._selected = selected;\n        if (selected) {\n            this._selectionList.selectedOptions.select(this);\n        }\n        else {\n            this._selectionList.selectedOptions.deselect(this);\n        }\n        this.selectedChange.emit(selected);\n        this._changeDetectorRef.markForCheck();\n        return true;\n    }\n    /**\n     * Notifies Angular that the option needs to be checked in the next change detection run.\n     * Mainly used to trigger an update of the list option if the disabled state of the selection\n     * list changed.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Toggles the option's value based on a user interaction. */\n    _toggleOnInteraction() {\n        if (!this.disabled) {\n            if (this._selectionList.multiple) {\n                this.selected = !this.selected;\n                this._selectionList._emitChangeEvent([this]);\n            }\n            else if (!this.selected) {\n                this.selected = true;\n                this._selectionList._emitChangeEvent([this]);\n            }\n        }\n    }\n    /** Sets the tabindex of the list option. */\n    _setTabindex(value) {\n        this._hostElement.setAttribute('tabindex', value + '');\n    }\n    _hasBothLeadingAndTrailing() {\n        const hasLeading = this._hasProjected('avatars', 'before') ||\n            this._hasProjected('icons', 'before') ||\n            this._hasCheckboxAt('before') ||\n            this._hasRadioAt('before');\n        const hasTrailing = this._hasProjected('icons', 'after') ||\n            this._hasProjected('avatars', 'after') ||\n            this._hasCheckboxAt('after') ||\n            this._hasRadioAt('after');\n        return hasLeading && hasTrailing;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListOption, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatListOption, isStandalone: true, selector: \"mat-list-option\", inputs: { togglePosition: \"togglePosition\", checkboxPosition: \"checkboxPosition\", color: \"color\", value: \"value\", selected: \"selected\" }, outputs: { selectedChange: \"selectedChange\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"blur\": \"_handleBlur()\", \"click\": \"_toggleOnInteraction()\" }, properties: { \"class.mdc-list-item--selected\": \"selected && !_selectionList.multiple && _selectionList.hideSingleSelectionIndicator\", \"class.mdc-list-item--with-leading-avatar\": \"_hasProjected(\\\"avatars\\\", \\\"before\\\")\", \"class.mdc-list-item--with-leading-icon\": \"_hasProjected(\\\"icons\\\", \\\"before\\\")\", \"class.mdc-list-item--with-trailing-icon\": \"_hasProjected(\\\"icons\\\", \\\"after\\\")\", \"class.mat-mdc-list-option-with-trailing-avatar\": \"_hasProjected(\\\"avatars\\\", \\\"after\\\")\", \"class.mdc-list-item--with-leading-checkbox\": \"_hasCheckboxAt(\\\"before\\\")\", \"class.mdc-list-item--with-trailing-checkbox\": \"_hasCheckboxAt(\\\"after\\\")\", \"class.mdc-list-item--with-leading-radio\": \"_hasRadioAt(\\\"before\\\")\", \"class.mdc-list-item--with-trailing-radio\": \"_hasRadioAt(\\\"after\\\")\", \"class.mat-mdc-list-item-both-leading-and-trailing\": \"_hasBothLeadingAndTrailing()\", \"class.mat-accent\": \"color !== \\\"primary\\\" && color !== \\\"warn\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"attr.aria-selected\": \"selected\" }, classAttribute: \"mat-mdc-list-item mat-mdc-list-option mdc-list-item\" }, providers: [\n            { provide: MatListItemBase, useExisting: MatListOption },\n            { provide: LIST_OPTION, useExisting: MatListOption },\n        ], queries: [{ propertyName: \"_lines\", predicate: MatListItemLine, descendants: true }, { propertyName: \"_titles\", predicate: MatListItemTitle, descendants: true }], viewQueries: [{ propertyName: \"_unscopedContent\", first: true, predicate: [\"unscopedContent\"], descendants: true }], exportAs: [\"matListOption\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Save icons and the pseudo checkbox/radio so that they can be re-used in the template without\\n  duplication. Also content can only be injected once so we need to extract icons/avatars\\n  into a template since we use it in multiple places.\\n-->\\n<ng-template #icons>\\n  <ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\">\\n  </ng-content>\\n</ng-template>\\n\\n<ng-template #checkbox>\\n  <div class=\\\"mdc-checkbox\\\" [class.mdc-checkbox--disabled]=\\\"disabled\\\">\\n    <input type=\\\"checkbox\\\" class=\\\"mdc-checkbox__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n<ng-template #radio>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <input type=\\\"radio\\\" class=\\\"mdc-radio__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n@if (_hasCheckboxAt('before')) {\\n  <!-- Container for the checkbox at start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-checkbox-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('before')) {\\n  <!-- Container for the radio at the start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-radio-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n<!-- Conditionally renders icons/avatars before the list item text. -->\\n@if (_hasIconsOrAvatarsAt('before')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Text -->\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n@if (_hasCheckboxAt('after')) {\\n  <!-- Container for the checkbox at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('after')) {\\n  <!-- Container for the radio at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n\\n<!-- Conditionally renders icons/avatars after the list item text. -->\\n@if (_hasIconsOrAvatarsAt('after')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Divider -->\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\", styles: [\".mat-mdc-list-option-with-trailing-avatar.mdc-list-item,[dir=rtl] .mat-mdc-list-option-with-trailing-avatar.mdc-list-item{padding-left:0;padding-right:0}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{margin-left:16px;margin-right:16px;width:40px;height:40px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{border-radius:50%}.mat-mdc-list-option .mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled{opacity:.5}}.mat-mdc-list-option .mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mat-mdc-list-option .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mat-mdc-list-option .mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__mixedmark{margin:0 1px}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-list-option .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-list-option .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-list-option .mdc-checkbox__native-control,.mat-mdc-list-option .mdc-radio__native-control{display:none}@media(forced-colors: active){.mat-mdc-list-option.mdc-list-item--selected::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.mat-mdc-list-option.mdc-list-item--selected [dir=rtl]::after{right:auto;left:16px}}\\n\"], dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list-option', exportAs: 'matListOption', host: {\n                        'class': 'mat-mdc-list-item mat-mdc-list-option mdc-list-item',\n                        'role': 'option',\n                        // As per MDC, only list items without checkbox or radio indicator should receive the\n                        // `--selected` class.\n                        '[class.mdc-list-item--selected]': 'selected && !_selectionList.multiple && _selectionList.hideSingleSelectionIndicator',\n                        // Based on the checkbox/radio position and whether there are icons or avatars, we apply MDC's\n                        // list-item `--leading` and `--trailing` classes.\n                        '[class.mdc-list-item--with-leading-avatar]': '_hasProjected(\"avatars\", \"before\")',\n                        '[class.mdc-list-item--with-leading-icon]': '_hasProjected(\"icons\", \"before\")',\n                        '[class.mdc-list-item--with-trailing-icon]': '_hasProjected(\"icons\", \"after\")',\n                        '[class.mat-mdc-list-option-with-trailing-avatar]': '_hasProjected(\"avatars\", \"after\")',\n                        // Based on the checkbox/radio position, we apply the `--leading` or `--trailing` MDC classes\n                        // which ensure that the checkbox/radio is positioned correctly within the list item.\n                        '[class.mdc-list-item--with-leading-checkbox]': '_hasCheckboxAt(\"before\")',\n                        '[class.mdc-list-item--with-trailing-checkbox]': '_hasCheckboxAt(\"after\")',\n                        '[class.mdc-list-item--with-leading-radio]': '_hasRadioAt(\"before\")',\n                        '[class.mdc-list-item--with-trailing-radio]': '_hasRadioAt(\"after\")',\n                        // Utility class that makes it easier to target the case where there's both a leading\n                        // and a trailing icon. Avoids having to write out all the combinations.\n                        '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n                        '[class.mat-accent]': 'color !== \"primary\" && color !== \"warn\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        '[attr.aria-selected]': 'selected',\n                        '(blur)': '_handleBlur()',\n                        '(click)': '_toggleOnInteraction()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        { provide: MatListItemBase, useExisting: MatListOption },\n                        { provide: LIST_OPTION, useExisting: MatListOption },\n                    ], imports: [NgTemplateOutlet, CdkObserveContent], template: \"<!--\\n  Save icons and the pseudo checkbox/radio so that they can be re-used in the template without\\n  duplication. Also content can only be injected once so we need to extract icons/avatars\\n  into a template since we use it in multiple places.\\n-->\\n<ng-template #icons>\\n  <ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\">\\n  </ng-content>\\n</ng-template>\\n\\n<ng-template #checkbox>\\n  <div class=\\\"mdc-checkbox\\\" [class.mdc-checkbox--disabled]=\\\"disabled\\\">\\n    <input type=\\\"checkbox\\\" class=\\\"mdc-checkbox__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n<ng-template #radio>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <input type=\\\"radio\\\" class=\\\"mdc-radio__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n@if (_hasCheckboxAt('before')) {\\n  <!-- Container for the checkbox at start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-checkbox-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('before')) {\\n  <!-- Container for the radio at the start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-radio-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n<!-- Conditionally renders icons/avatars before the list item text. -->\\n@if (_hasIconsOrAvatarsAt('before')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Text -->\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n@if (_hasCheckboxAt('after')) {\\n  <!-- Container for the checkbox at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('after')) {\\n  <!-- Container for the radio at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n\\n<!-- Conditionally renders icons/avatars after the list item text. -->\\n@if (_hasIconsOrAvatarsAt('after')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Divider -->\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\", styles: [\".mat-mdc-list-option-with-trailing-avatar.mdc-list-item,[dir=rtl] .mat-mdc-list-option-with-trailing-avatar.mdc-list-item{padding-left:0;padding-right:0}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{margin-left:16px;margin-right:16px;width:40px;height:40px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{border-radius:50%}.mat-mdc-list-option .mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled{opacity:.5}}.mat-mdc-list-option .mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mat-mdc-list-option .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mat-mdc-list-option .mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__mixedmark{margin:0 1px}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-list-option .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-list-option .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-list-option .mdc-checkbox__native-control,.mat-mdc-list-option .mdc-radio__native-control{display:none}@media(forced-colors: active){.mat-mdc-list-option.mdc-list-item--selected::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.mat-mdc-list-option.mdc-list-item--selected [dir=rtl]::after{right:auto;left:16px}}\\n\"] }]\n        }], propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatListItemLine, { descendants: true }]\n            }], _titles: [{\n                type: ContentChildren,\n                args: [MatListItemTitle, { descendants: true }]\n            }], _unscopedContent: [{\n                type: ViewChild,\n                args: ['unscopedContent']\n            }], selectedChange: [{\n                type: Output\n            }], togglePosition: [{\n                type: Input\n            }], checkboxPosition: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }] } });\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatListSubheaderCssMatStyler {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListSubheaderCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatListSubheaderCssMatStyler, isStandalone: true, selector: \"[mat-subheader], [matSubheader]\", host: { classAttribute: \"mat-mdc-subheader mdc-list-group__subheader\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListSubheaderCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-subheader], [matSubheader]',\n                    // TODO(mmalerba): MDC's subheader font looks identical to the list item font, figure out why and\n                    //  make a change in one of the repos to visually distinguish.\n                    host: { 'class': 'mat-mdc-subheader mdc-list-group__subheader' },\n                }]\n        }] });\n\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_NAV_LIST = new InjectionToken('MatNavList');\nclass MatNavList extends MatListBase {\n    // An navigation list is considered interactive, but does not extend the interactive list\n    // base class. We do this because as per MDC, items of interactive lists are only reachable\n    // through keyboard shortcuts. We want all items for the navigation list to be reachable\n    // through tab key as we do not intend to provide any special accessibility treatment. The\n    // accessibility treatment depends on how the end-user will interact with it.\n    _isNonInteractive = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNavList, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatNavList, isStandalone: true, selector: \"mat-nav-list\", host: { attributes: { \"role\": \"navigation\" }, classAttribute: \"mat-mdc-nav-list mat-mdc-list-base mdc-list\" }, providers: [{ provide: MatListBase, useExisting: MatNavList }], exportAs: [\"matNavList\"], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatNavList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-nav-list', exportAs: 'matNavList', template: '<ng-content></ng-content>', host: {\n                        'class': 'mat-mdc-nav-list mat-mdc-list-base mdc-list',\n                        'role': 'navigation',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MatListBase, useExisting: MatNavList }], styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"] }]\n        }] });\n\nconst MAT_SELECTION_LIST_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSelectionList),\n    multi: true,\n};\n/** Change event that is being fired whenever the selected state of an option changes. */\nclass MatSelectionListChange {\n    source;\n    options;\n    constructor(\n    /** Reference to the selection list that emitted the event. */\n    source, \n    /** Reference to the options that have been changed. */\n    options) {\n        this.source = source;\n        this.options = options;\n    }\n}\nclass MatSelectionList extends MatListBase {\n    _element = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _initialized = false;\n    _keyManager;\n    _listenerCleanups;\n    /** Emits when the list has been destroyed. */\n    _destroyed = new Subject();\n    /** Whether the list has been destroyed. */\n    _isDestroyed;\n    /** View to model callback that should be called whenever the selected options change. */\n    _onChange = (_) => { };\n    _items;\n    /** Emits a change event whenever the selected state of an option changes. */\n    selectionChange = new EventEmitter();\n    /**\n     * Theme color of the selection list. This sets the checkbox color for all\n     * list options. This API is supported in M2 themes only, it has no effect in\n     * M3 themes. For color customization in M3, see https://material.angular.dev/components/list/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'accent';\n    /**\n     * Function used for comparing an option against the selected value when determining which\n     * options should appear as selected. The first argument is the value of an options. The second\n     * one is a value from the selected value. A boolean must be returned.\n     */\n    compareWith = (a1, a2) => a1 === a2;\n    /** Whether selection is limited to one or multiple items (default multiple). */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._multiple) {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._initialized) {\n                throw new Error('Cannot change `multiple` mode of mat-selection-list after initialization.');\n            }\n            this._multiple = newValue;\n            this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n        }\n    }\n    _multiple = true;\n    /** Whether radio indicator for all list items is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** The currently selected options. */\n    selectedOptions = new SelectionModel(this._multiple);\n    /** Keeps track of the currently-selected value. */\n    _value;\n    /** View to model callback that should be called if the list or its options lost focus. */\n    _onTouched = () => { };\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    constructor() {\n        super();\n        this._isNonInteractive = false;\n    }\n    ngAfterViewInit() {\n        // Mark the selection list as initialized so that the `multiple`\n        // binding can no longer be changed.\n        this._initialized = true;\n        this._setupRovingTabindex();\n        // These events are bound outside the zone, because they don't change\n        // any change-detected properties and they can trigger timeouts.\n        this._ngZone.runOutsideAngular(() => {\n            this._listenerCleanups = [\n                this._renderer.listen(this._element.nativeElement, 'focusin', this._handleFocusin),\n                this._renderer.listen(this._element.nativeElement, 'focusout', this._handleFocusout),\n            ];\n        });\n        if (this._value) {\n            this._setOptionsFromValues(this._value);\n        }\n        this._watchForSelectionChange();\n    }\n    ngOnChanges(changes) {\n        const disabledChanges = changes['disabled'];\n        const disableRippleChanges = changes['disableRipple'];\n        const hideSingleSelectionIndicatorChanges = changes['hideSingleSelectionIndicator'];\n        if ((disableRippleChanges && !disableRippleChanges.firstChange) ||\n            (disabledChanges && !disabledChanges.firstChange) ||\n            (hideSingleSelectionIndicatorChanges && !hideSingleSelectionIndicatorChanges.firstChange)) {\n            this._markOptionsForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._listenerCleanups?.forEach(current => current());\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._isDestroyed = true;\n    }\n    /** Focuses the selection list. */\n    focus(options) {\n        this._element.nativeElement.focus(options);\n    }\n    /** Selects all of the options. Returns the options that changed as a result. */\n    selectAll() {\n        return this._setAllOptionsSelected(true);\n    }\n    /** Deselects all of the options. Returns the options that changed as a result. */\n    deselectAll() {\n        return this._setAllOptionsSelected(false);\n    }\n    /** Reports a value change to the ControlValueAccessor */\n    _reportValueChange() {\n        // Stop reporting value changes after the list has been destroyed. This avoids\n        // cases where the list might wrongly reset its value once it is removed, but\n        // the form control is still live.\n        if (this.options && !this._isDestroyed) {\n            const value = this._getSelectedOptionValues();\n            this._onChange(value);\n            this._value = value;\n        }\n    }\n    /** Emits a change event if the selected state of an option changed. */\n    _emitChangeEvent(options) {\n        this.selectionChange.emit(new MatSelectionListChange(this, options));\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(values) {\n        this._value = values;\n        if (this.options) {\n            this._setOptionsFromValues(values || []);\n        }\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this._markOptionsForCheck();\n    }\n    /**\n     * Whether the *entire* selection list is disabled. When true, each list item is also disabled\n     * and each list item is removed from the tab order (has tabindex=\"-1\").\n     */\n    get disabled() {\n        return this._selectionListDisabled();\n    }\n    set disabled(value) {\n        // Update the disabled state of this list. Write to `this._selectionListDisabled` instead of\n        // `super.disabled`. That is to avoid closure compiler compatibility issues with assigning to\n        // a super property.\n        this._selectionListDisabled.set(coerceBooleanProperty(value));\n        if (this._selectionListDisabled()) {\n            this._keyManager?.setActiveItem(-1);\n        }\n    }\n    _selectionListDisabled = signal(false);\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /** Watches for changes in the selected state of the options and updates the list accordingly. */\n    _watchForSelectionChange() {\n        this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n            // Sync external changes to the model back to the options.\n            for (let item of event.added) {\n                item.selected = true;\n            }\n            for (let item of event.removed) {\n                item.selected = false;\n            }\n            if (!this._containsFocus()) {\n                this._resetActiveOption();\n            }\n        });\n    }\n    /** Sets the selected options based on the specified values. */\n    _setOptionsFromValues(values) {\n        this.options.forEach(option => option._setSelected(false));\n        values.forEach(value => {\n            const correspondingOption = this.options.find(option => {\n                // Skip options that are already in the model. This allows us to handle cases\n                // where the same primitive value is selected multiple times.\n                return option.selected ? false : this.compareWith(option.value, value);\n            });\n            if (correspondingOption) {\n                correspondingOption._setSelected(true);\n            }\n        });\n    }\n    /** Returns the values of the selected options. */\n    _getSelectedOptionValues() {\n        return this.options.filter(option => option.selected).map(option => option.value);\n    }\n    /** Marks all the options to be checked in the next change detection run. */\n    _markOptionsForCheck() {\n        if (this.options) {\n            this.options.forEach(option => option._markForCheck());\n        }\n    }\n    /**\n     * Sets the selected state on all of the options\n     * and emits an event if anything changed.\n     */\n    _setAllOptionsSelected(isSelected, skipDisabled) {\n        // Keep track of whether anything changed, because we only want to\n        // emit the changed event when something actually changed.\n        const changedOptions = [];\n        this.options.forEach(option => {\n            if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n                changedOptions.push(option);\n            }\n        });\n        if (changedOptions.length) {\n            this._reportValueChange();\n        }\n        return changedOptions;\n    }\n    // Note: This getter exists for backwards compatibility. The `_items` query list\n    // cannot be named `options` as it will be picked up by the interactive list base.\n    /** The option components contained within this selection-list. */\n    get options() {\n        return this._items;\n    }\n    /** Handles keydown events within the list. */\n    _handleKeydown(event) {\n        const activeItem = this._keyManager.activeItem;\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) &&\n            !this._keyManager.isTyping() &&\n            activeItem &&\n            !activeItem.disabled) {\n            event.preventDefault();\n            activeItem._toggleOnInteraction();\n        }\n        else if (event.keyCode === A &&\n            this.multiple &&\n            !this._keyManager.isTyping() &&\n            hasModifierKey(event, 'ctrlKey', 'metaKey')) {\n            const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n            event.preventDefault();\n            this._emitChangeEvent(this._setAllOptionsSelected(shouldSelect, true));\n        }\n        else {\n            this._keyManager.onKeydown(event);\n        }\n    }\n    /** Handles focusout events within the list. */\n    _handleFocusout = () => {\n        // Focus takes a while to update so we have to wrap our call in a timeout.\n        setTimeout(() => {\n            if (!this._containsFocus()) {\n                this._resetActiveOption();\n            }\n        });\n    };\n    /** Handles focusin events within the list. */\n    _handleFocusin = (event) => {\n        if (this.disabled) {\n            return;\n        }\n        const activeIndex = this._items\n            .toArray()\n            .findIndex(item => item._elementRef.nativeElement.contains(event.target));\n        if (activeIndex > -1) {\n            this._setActiveOption(activeIndex);\n        }\n        else {\n            this._resetActiveOption();\n        }\n    };\n    /**\n     * Sets up the logic for maintaining the roving tabindex.\n     *\n     * `skipPredicate` determines if key manager should avoid putting a given list item in the tab\n     * index. Allow disabled list items to receive focus to align with WAI ARIA recommendation.\n     * Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n     * makes a few exceptions for compound widgets.\n     *\n     * From [Developing a Keyboard Interface](\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n     *   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n     *   Listbox...\"\n     */\n    _setupRovingTabindex() {\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHomeAndEnd()\n            .withTypeAhead()\n            .withWrap()\n            .skipPredicate(() => this.disabled);\n        // Set the initial focus.\n        this._resetActiveOption();\n        // Move the tabindex to the currently-focused list item.\n        this._keyManager.change.subscribe(activeItemIndex => this._setActiveOption(activeItemIndex));\n        // If the active item is removed from the list, reset back to the first one.\n        this._items.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            const activeItem = this._keyManager.activeItem;\n            if (!activeItem || this._items.toArray().indexOf(activeItem) === -1) {\n                this._resetActiveOption();\n            }\n        });\n    }\n    /**\n     * Sets an option as active.\n     * @param index Index of the active option. If set to -1, no option will be active.\n     */\n    _setActiveOption(index) {\n        this._items.forEach((item, itemIndex) => item._setTabindex(itemIndex === index ? 0 : -1));\n        this._keyManager.updateActiveItem(index);\n    }\n    /**\n     * Resets the active option. When the list is disabled, remove all options from to the tab order.\n     * Otherwise, focus the first selected option.\n     */\n    _resetActiveOption() {\n        if (this.disabled) {\n            this._setActiveOption(-1);\n            return;\n        }\n        const activeItem = this._items.find(item => item.selected && !item.disabled) || this._items.first;\n        this._setActiveOption(activeItem ? this._items.toArray().indexOf(activeItem) : -1);\n    }\n    /** Returns whether the focus is currently within the list. */\n    _containsFocus() {\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return activeElement && this._element.nativeElement.contains(activeElement);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectionList, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSelectionList, isStandalone: true, selector: \"mat-selection-list\", inputs: { color: \"color\", compareWith: \"compareWith\", multiple: \"multiple\", hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\", disabled: \"disabled\" }, outputs: { selectionChange: \"selectionChange\" }, host: { attributes: { \"role\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-multiselectable\": \"multiple\" }, classAttribute: \"mat-mdc-selection-list mat-mdc-list-base mdc-list\" }, providers: [\n            MAT_SELECTION_LIST_VALUE_ACCESSOR,\n            { provide: MatListBase, useExisting: MatSelectionList },\n            { provide: SELECTION_LIST, useExisting: MatSelectionList },\n        ], queries: [{ propertyName: \"_items\", predicate: MatListOption, descendants: true }], exportAs: [\"matSelectionList\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSelectionList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-selection-list', exportAs: 'matSelectionList', host: {\n                        'class': 'mat-mdc-selection-list mat-mdc-list-base mdc-list',\n                        'role': 'listbox',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, template: '<ng-content></ng-content>', encapsulation: ViewEncapsulation.None, providers: [\n                        MAT_SELECTION_LIST_VALUE_ACCESSOR,\n                        { provide: MatListBase, useExisting: MatSelectionList },\n                        { provide: SELECTION_LIST, useExisting: MatSelectionList },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatListOption, { descendants: true }]\n            }], selectionChange: [{\n                type: Output\n            }], color: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], hideSingleSelectionIndicator: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\nclass MatListModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListModule, imports: [ObserversModule,\n            MatCommonModule,\n            MatRippleModule,\n            MatPseudoCheckboxModule,\n            MatList,\n            MatActionList,\n            MatNavList,\n            MatSelectionList,\n            MatListItem,\n            MatListOption,\n            MatListSubheaderCssMatStyler,\n            MatListItemAvatar,\n            MatListItemIcon,\n            MatListItemLine,\n            MatListItemTitle,\n            MatListItemMeta], exports: [MatList,\n            MatActionList,\n            MatNavList,\n            MatSelectionList,\n            MatListItem,\n            MatListOption,\n            MatListItemAvatar,\n            MatListItemIcon,\n            MatListSubheaderCssMatStyler,\n            MatDividerModule,\n            MatListItemLine,\n            MatListItemTitle,\n            MatListItemMeta] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListModule, imports: [ObserversModule,\n            MatCommonModule,\n            MatRippleModule,\n            MatPseudoCheckboxModule, MatDividerModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatListModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        ObserversModule,\n                        MatCommonModule,\n                        MatRippleModule,\n                        MatPseudoCheckboxModule,\n                        MatList,\n                        MatActionList,\n                        MatNavList,\n                        MatSelectionList,\n                        MatListItem,\n                        MatListOption,\n                        MatListSubheaderCssMatStyler,\n                        MatListItemAvatar,\n                        MatListItemIcon,\n                        MatListItemLine,\n                        MatListItemTitle,\n                        MatListItemMeta,\n                    ],\n                    exports: [\n                        MatList,\n                        MatActionList,\n                        MatNavList,\n                        MatSelectionList,\n                        MatListItem,\n                        MatListOption,\n                        MatListItemAvatar,\n                        MatListItemIcon,\n                        MatListSubheaderCssMatStyler,\n                        MatDividerModule,\n                        MatListItemLine,\n                        MatListItemTitle,\n                        MatListItemMeta,\n                    ],\n                }]\n        }] });\n\nexport { MAT_LIST, MAT_LIST_CONFIG, MAT_NAV_LIST, MAT_SELECTION_LIST_VALUE_ACCESSOR, MatActionList, MatList, MatListItem, MatListItemAvatar, MatListItemIcon, MatListItemLine, MatListItemMeta, MatListItemTitle, MatListModule, MatListOption, MatListSubheaderCssMatStyler, MatNavList, MatSelectionList, MatSelectionListChange, SELECTION_LIST, _MatListItemGraphicBase };\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC3Q,SAASC,QAAQ,EAAEC,iCAAiC,QAAQ,uBAAuB;AACnF,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,YAAY,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,uBAAuB;AAC3F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASD,CAAC,IAAIE,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAC3E,SAASC,gBAAgB,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+B4C9C,EAAE,CAAAgD,YAAA,KA6qBmnB,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qBtnB9C,EAAE,CAAAkD,cAAA,aA6qB0uB,CAAC;IA7qB7uBlD,EAAE,CAAAmD,SAAA,eA6qB42B,CAAC;IA7qB/2BnD,EAAE,CAAAkD,cAAA,aA6qB05B,CAAC;IA7qB75BlD,EAAE,CAAAoD,cAAA;IAAFpD,EAAE,CAAAkD,cAAA,aA6qB4gC,CAAC;IA7qB/gClD,EAAE,CAAAmD,SAAA,cA6qBupC,CAAC;IA7qB1pCnD,EAAE,CAAAqD,YAAA,CA6qBqqC,CAAC;IA7qBxqCrD,EAAE,CAAAsD,eAAA;IAAFtD,EAAE,CAAAmD,SAAA,aA6qB0tC,CAAC;IA7qB7tCnD,EAAE,CAAAqD,YAAA,CA6qBsuC,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAS,MAAA,GA7qBnvCvD,EAAE,CAAAwD,aAAA;IAAFxD,EAAE,CAAAyD,WAAA,2BAAAF,MAAA,CAAAG,QA6qByuB,CAAC;IA7qB5uB1D,EAAE,CAAA2D,SAAA,CA6qBk1B,CAAC;IA7qBr1B3D,EAAE,CAAA4D,UAAA,YAAAL,MAAA,CAAAM,QA6qBk1B,CAAC,aAAAN,MAAA,CAAAG,QAAuB,CAAC;EAAA;AAAA;AAAA,SAAAI,qCAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qB72B9C,EAAE,CAAAkD,cAAA,aA6qB81C,CAAC;IA7qBj2ClD,EAAE,CAAAmD,SAAA,eA6qB09C,CAAC;IA7qB79CnD,EAAE,CAAAkD,cAAA,aA6qBqgD,CAAC;IA7qBxgDlD,EAAE,CAAAmD,SAAA,aA6qB0jD,CAAC,aAAoD,CAAC;IA7qBlnDnD,EAAE,CAAAqD,YAAA,CA6qB2nD,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAS,MAAA,GA7qBxoDvD,EAAE,CAAAwD,aAAA;IAAFxD,EAAE,CAAAyD,WAAA,wBAAAF,MAAA,CAAAG,QA6qB61C,CAAC;IA7qBh2C1D,EAAE,CAAA2D,SAAA,CA6qBg8C,CAAC;IA7qBn8C3D,EAAE,CAAA4D,UAAA,YAAAL,MAAA,CAAAM,QA6qBg8C,CAAC,aAAAN,MAAA,CAAAG,QAAuB,CAAC;EAAA;AAAA;AAAA,SAAAK,mDAAAjB,EAAA,EAAAC,GAAA;AAAA,SAAAiB,qCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qB39C9C,EAAE,CAAAkD,cAAA,aA6qBuzD,CAAC;IA7qB1zDlD,EAAE,CAAAiE,UAAA,IAAAF,kDAAA,wBA6qB02D,CAAC;IA7qB72D/D,EAAE,CAAAqD,YAAA,CA6qBm4D,CAAC;EAAA;EAAA,IAAAP,EAAA;IA7qBt4D9C,EAAE,CAAAwD,aAAA;IAAA,MAAAU,WAAA,GAAFlE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA2D,SAAA,CA6qBy2D,CAAC;IA7qB52D3D,EAAE,CAAA4D,UAAA,qBAAAM,WA6qBy2D,CAAC;EAAA;AAAA;AAAA,SAAAE,mDAAAtB,EAAA,EAAAC,GAAA;AAAA,SAAAsB,qCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qB52D9C,EAAE,CAAAkD,cAAA,aA6qBqiE,CAAC;IA7qBxiElD,EAAE,CAAAiE,UAAA,IAAAG,kDAAA,wBA6qBqlE,CAAC;IA7qBxlEpE,EAAE,CAAAqD,YAAA,CA6qB8mE,CAAC;EAAA;EAAA,IAAAP,EAAA;IA7qBjnE9C,EAAE,CAAAwD,aAAA;IAAA,MAAAc,QAAA,GAAFtE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA2D,SAAA,CA6qBolE,CAAC;IA7qBvlE3D,EAAE,CAAA4D,UAAA,qBAAAU,QA6qBolE,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAzB,EAAA,EAAAC,GAAA;AAAA,SAAAyB,qCAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qBvlE9C,EAAE,CAAAiE,UAAA,IAAAM,kDAAA,wBA6qBgxE,CAAC;EAAA;EAAA,IAAAzB,EAAA;IA7qBnxE9C,EAAE,CAAAwD,aAAA;IAAA,MAAAiB,QAAA,GAAFzE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA4D,UAAA,qBAAAa,QA6qB+wE,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAA5B,EAAA,EAAAC,GAAA;AAAA,SAAA4B,sCAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qBlxE9C,EAAE,CAAAkD,cAAA,aA6qBgwF,CAAC;IA7qBnwFlD,EAAE,CAAAiE,UAAA,IAAAS,mDAAA,wBA6qBmzF,CAAC;IA7qBtzF1E,EAAE,CAAAqD,YAAA,CA6qB40F,CAAC;EAAA;EAAA,IAAAP,EAAA;IA7qB/0F9C,EAAE,CAAAwD,aAAA;IAAA,MAAAU,WAAA,GAAFlE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA2D,SAAA,CA6qBkzF,CAAC;IA7qBrzF3D,EAAE,CAAA4D,UAAA,qBAAAM,WA6qBkzF,CAAC;EAAA;AAAA;AAAA,SAAAU,oDAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,sCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qBrzF9C,EAAE,CAAAkD,cAAA,aA6qBw8F,CAAC;IA7qB38FlD,EAAE,CAAAiE,UAAA,IAAAW,mDAAA,wBA6qBw/F,CAAC;IA7qB3/F5E,EAAE,CAAAqD,YAAA,CA6qBihG,CAAC;EAAA;EAAA,IAAAP,EAAA;IA7qBphG9C,EAAE,CAAAwD,aAAA;IAAA,MAAAc,QAAA,GAAFtE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA2D,SAAA,CA6qBu/F,CAAC;IA7qB1/F3D,EAAE,CAAA4D,UAAA,qBAAAU,QA6qBu/F,CAAC;EAAA;AAAA;AAAA,SAAAQ,oDAAAhC,EAAA,EAAAC,GAAA;AAAA,SAAAgC,sCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7qB1/F9C,EAAE,CAAAiE,UAAA,IAAAa,mDAAA,wBA6qBmrG,CAAC;EAAA;EAAA,IAAAhC,EAAA;IA7qBtrG9C,EAAE,CAAAwD,aAAA;IAAA,MAAAiB,QAAA,GAAFzE,EAAE,CAAAmE,WAAA;IAAFnE,EAAE,CAAA4D,UAAA,qBAAAa,QA6qBkrG,CAAC;EAAA;AAAA;AA3sBlxG,SAASO,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,cAAc,QAAQ,uBAAuB;AACvE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASD,CAAC,IAAIE,eAAe,QAAQ,sBAAsB;AAC3D,SAASF,CAAC,IAAIG,uBAAuB,QAAQ,uCAAuC;AACpF,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;AAC1B,OAAO,gCAAgC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,gBAAG,IAAI5F,cAAc,CAAC,YAAY,CAAC;;AAEpD;AACA;AACA;AACA;AACA;AACA;AALA,IAMM6F,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnBC,WAAW,GAAG7F,MAAM,CAACC,UAAU,CAAC;IAChC6F,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFL,gBAAgB;IAAA;IACnH,OAAOM,IAAI,kBAD8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EACJR,gBAAgB;MAAAS,SAAA;MAAAC,SAAA;IAAA;EAC3G;EAAC,OALKV,gBAAgB;AAAA;AAMtB;EAAA,QAAAW,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBX,WAAW,GAAG7F,MAAM,CAACC,UAAU,CAAC;IAChC6F,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOC,IAAI,YAAAU,wBAAAR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,eAAe;IAAA;IAClH,OAAON,IAAI,kBApB8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAoBJI,eAAe;MAAAH,SAAA;MAAAC,SAAA;IAAA;EAC1G;EAAC,OALKE,eAAe;AAAA;AAMrB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMG,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOX,IAAI,YAAAY,wBAAAV,iBAAA;MAAA,YAAAA,iBAAA,IAAwFS,eAAe;IAAA;IAClH,OAAOR,IAAI,kBArC8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAqCJM,eAAe;MAAAL,SAAA;MAAAC,SAAA;IAAA;EAC1G;EAAC,OAHKI,eAAe;AAAA;AAIrB;EAAA,QAAAH,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,IAQMK,uBAAuB;EAA7B,MAAMA,uBAAuB,CAAC;IAC1BC,WAAW,GAAG7G,MAAM,CAAC2F,WAAW,EAAE;MAAEmB,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrDhB,WAAWA,CAAA,EAAG,CAAE;IAChBiB,iBAAiBA,CAAA,EAAG;MAChB;MACA;MACA,OAAO,CAAC,IAAI,CAACF,WAAW,IAAI,IAAI,CAACA,WAAW,EAAEG,kBAAkB,CAAC,CAAC,KAAK,OAAO;IAClF;IACA,OAAOjB,IAAI,YAAAkB,gCAAAhB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFW,uBAAuB;IAAA;IAC1H,OAAOV,IAAI,kBA/D8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA+DJQ,uBAAuB;MAAAM,QAAA;MAAAC,YAAA,WAAAC,qCAAAxE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/DrB9C,EAAE,CAAAyD,WAAA,yBA+DJV,GAAA,CAAAkE,iBAAA,CAAkB,CAAI,CAAC,wBAAtBlE,GAAA,CAAAkE,iBAAA,CAAkB,CAAG,CAAC;QAAA;MAAA;IAAA;EAClH;EAAC,OAVKH,uBAAuB;AAAA;AAW7B;EAAA,QAAAL,SAAA,oBAAAA,SAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AAJA,IAKMc,iBAAiB;EAAvB,MAAMA,iBAAiB,SAAST,uBAAuB,CAAC;IACpD,OAAOb,IAAI;MAAA,IAAAuB,8BAAA;MAAA,gBAAAC,0BAAAtB,iBAAA;QAAA,QAAAqB,8BAAA,KAAAA,8BAAA,GApF8ExH,EAAE,CAAA0H,qBAAA,CAoFQH,iBAAiB,IAAApB,iBAAA,IAAjBoB,iBAAiB;MAAA;IAAA;IACpH,OAAOnB,IAAI,kBArF8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAqFJiB,iBAAiB;MAAAhB,SAAA;MAAAC,SAAA;MAAAmB,QAAA,GArFf3H,EAAE,CAAA4H,0BAAA;IAAA;EAsF/F;EAAC,OAHKL,iBAAiB;AAAA;AAIvB;EAAA,QAAAd,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AAJA,IAKMoB,eAAe;EAArB,MAAMA,eAAe,SAASf,uBAAuB,CAAC;IAClD,OAAOb,IAAI;MAAA,IAAA6B,4BAAA;MAAA,gBAAAC,wBAAA5B,iBAAA;QAAA,QAAA2B,4BAAA,KAAAA,4BAAA,GApG8E9H,EAAE,CAAA0H,qBAAA,CAoGQG,eAAe,IAAA1B,iBAAA,IAAf0B,eAAe;MAAA;IAAA;IAClH,OAAOzB,IAAI,kBArG8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EAqGJuB,eAAe;MAAAtB,SAAA;MAAAC,SAAA;MAAAmB,QAAA,GArGb3H,EAAE,CAAA4H,0BAAA;IAAA;EAsG/F;EAAC,OAHKC,eAAe;AAAA;AAIrB;EAAA,QAAApB,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA,MAAMuB,eAAe,gBAAG,IAAI/H,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AAAA,IACMgI,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdC,iBAAiB,GAAG,IAAI;IACxB;IACA,IAAIC,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACC,cAAc;IAC9B;IACA,IAAID,aAAaA,CAACE,KAAK,EAAE;MACrB,IAAI,CAACD,cAAc,GAAGtI,qBAAqB,CAACuI,KAAK,CAAC;IACtD;IACAD,cAAc,GAAG,KAAK;IACtB;AACJ;AACA;AACA;IACI,IAAI1E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC4E,SAAS,CAAC,CAAC;IAC3B;IACA,IAAI5E,QAAQA,CAAC2E,KAAK,EAAE;MAChB,IAAI,CAACC,SAAS,CAACC,GAAG,CAACzI,qBAAqB,CAACuI,KAAK,CAAC,CAAC;IACpD;IACAC,SAAS,GAAGjI,MAAM,CAAC,KAAK,CAAC;IACzBmI,eAAe,GAAGtI,MAAM,CAAC8H,eAAe,EAAE;MAAEhB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7D,OAAOf,IAAI,YAAAwC,oBAAAtC,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8B,WAAW;IAAA;IAC9G,OAAO7B,IAAI,kBA1I8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA0IJ2B,WAAW;MAAAb,QAAA;MAAAC,YAAA,WAAAqB,yBAAA5F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1IT9C,EAAE,CAAA2I,WAAA,kBAAA5F,GAAA,CAAAW,QAAA;QAAA;MAAA;MAAAkF,MAAA;QAAAT,aAAA;QAAAzE,QAAA;MAAA;IAAA;EA2I/F;EAAC,OAxBKuE,WAAW;AAAA;AAyBjB;EAAA,QAAAxB,SAAA,oBAAAA,SAAA;AAAA;AAYA;AAAA,IACMoC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB9C,WAAW,GAAG7F,MAAM,CAACC,UAAU,CAAC;IAChC2I,OAAO,GAAG5I,MAAM,CAACK,MAAM,CAAC;IACxBwI,SAAS,GAAG7I,MAAM,CAAC+H,WAAW,EAAE;MAAEjB,QAAQ,EAAE;IAAK,CAAC,CAAC;IACnDgC,SAAS,GAAG9I,MAAM,CAACkB,QAAQ,CAAC;IAC5B;IACA6H,YAAY;IACZ;IACAC,gBAAgB;IAChB;IACAC,eAAe,GAAGpH,mBAAmB,CAAC,CAAC;IACvCqH,QAAQ;IACRC,MAAM;IACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIC,KAAKA,CAACA,KAAK,EAAE;MACb,IAAI,CAACC,cAAc,GAAGxJ,oBAAoB,CAACuJ,KAAK,EAAE,IAAI,CAAC;MACvD,IAAI,CAACE,gBAAgB,CAAC,KAAK,CAAC;IAChC;IACAD,cAAc,GAAG,IAAI;IACrB;IACA,IAAIpB,aAAaA,CAAA,EAAG;MAChB,OAAQ,IAAI,CAACzE,QAAQ,IACjB,IAAI,CAAC0E,cAAc,IACnB,IAAI,CAACe,eAAe,IACpB,CAAC,CAAC,IAAI,CAACJ,SAAS,EAAEZ,aAAa;IACvC;IACA,IAAIA,aAAaA,CAACE,KAAK,EAAE;MACrB,IAAI,CAACD,cAAc,GAAGtI,qBAAqB,CAACuI,KAAK,CAAC;IACtD;IACAD,cAAc,GAAG,KAAK;IACtB;IACA,IAAI1E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC4E,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAACS,SAAS,EAAErF,QAAQ;IACzD;IACA,IAAIA,QAAQA,CAAC2E,KAAK,EAAE;MAChB,IAAI,CAACC,SAAS,CAACC,GAAG,CAACzI,qBAAqB,CAACuI,KAAK,CAAC,CAAC;IACpD;IACAC,SAAS,GAAGjI,MAAM,CAAC,KAAK,CAAC;IACzBoJ,cAAc,GAAG,IAAIlI,YAAY,CAAC,CAAC;IACnCmI,eAAe,GAAG,IAAI;IACtB;IACAC,uBAAuB,GAAG,KAAK;IAC/B;AACJ;AACA;AACA;IACIC,YAAY;IACZ;AACJ;AACA;AACA;IACI,IAAIC,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC1B,aAAa,IAAI,CAAC,CAAC,IAAI,CAACyB,YAAY,CAAClG,QAAQ;IAC7D;IACAsC,WAAWA,CAAA,EAAG;MACV9F,MAAM,CAACoB,sBAAsB,CAAC,CAACwI,IAAI,CAAC9H,uBAAuB,CAAC;MAC5D,MAAM+H,mBAAmB,GAAG7J,MAAM,CAACyB,yBAAyB,EAAE;QAC1DqF,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAAC4C,YAAY,GAAGG,mBAAmB,IAAI,CAAC,CAAC;MAC7C,IAAI,CAACd,YAAY,GAAG,IAAI,CAAClD,WAAW,CAACiE,aAAa;MAClD,IAAI,CAACd,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAACgB,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ;MAC7E,IAAI,IAAI,CAACnB,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACb,iBAAiB,EAAE;QACrD,IAAI,CAACiC,wBAAwB,CAAC,CAAC;MACnC;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACjB,gBAAgB,IAAI,CAAC,IAAI,CAACD,YAAY,CAACmB,YAAY,CAAC,MAAM,CAAC,EAAE;QAClE,IAAI,CAACnB,YAAY,CAACoB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACpD;IACJ;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,CAACC,8BAA8B,CAAC,CAAC;MACrC,IAAI,CAACf,gBAAgB,CAAC,IAAI,CAAC;IAC/B;IACAgB,WAAWA,CAAA,EAAG;MACV,IAAI,CAACf,cAAc,CAACgB,WAAW,CAAC,CAAC;MACjC,IAAI,IAAI,CAACf,eAAe,KAAK,IAAI,EAAE;QAC/B,IAAI,CAACA,eAAe,CAACgB,oBAAoB,CAAC,CAAC;MAC/C;IACJ;IACA;IACAC,gBAAgBA,CAAA,EAAG;MACf,OAAO,CAAC,EAAE,IAAI,CAACvB,QAAQ,CAACwB,MAAM,IAAI,IAAI,CAACvB,MAAM,CAACuB,MAAM,CAAC;IACzD;IACAT,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAClB,YAAY,CAAC4B,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAChE,IAAI,CAACpB,eAAe,GAAG,IAAI7H,cAAc,CAAC,IAAI,EAAE,IAAI,CAACiH,OAAO,EAAE,IAAI,CAACG,YAAY,EAAE,IAAI,CAACD,SAAS,EAAE9I,MAAM,CAACM,QAAQ,CAAC,CAAC;MAClH,IAAI,CAACkJ,eAAe,CAACqB,kBAAkB,CAAC,IAAI,CAAC9B,YAAY,CAAC;IAC9D;IACA;AACJ;AACA;AACA;IACIsB,8BAA8BA,CAAA,EAAG;MAC7B,IAAI,CAACzB,OAAO,CAACkC,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACvB,cAAc,CAACqB,GAAG,CAACtJ,KAAK,CAAC,IAAI,CAACyJ,MAAM,CAACC,OAAO,EAAE,IAAI,CAACC,OAAO,CAACD,OAAO,CAAC,CAACE,SAAS,CAAC,MAAM,IAAI,CAAC5B,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC3H,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIA,gBAAgBA,CAAC6B,sBAAsB,EAAE;MACrC;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,MAAM,IAAI,CAAC,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACG,gBAAgB,EAAE;QACzD;MACJ;MACA;MACA;MACA;MACA,IAAID,sBAAsB,EAAE;QACxB,IAAI,CAACE,+BAA+B,CAAC,CAAC;MAC1C;MACA;MACA;MACA,IAAI,OAAO9E,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C+E,0BAA0B,CAAC,IAAI,CAAC;MACpC;MACA,MAAMC,aAAa,GAAG,IAAI,CAAClC,cAAc,IAAI,IAAI,CAACmC,sBAAsB,CAAC,CAAC;MAC1E,MAAMC,iBAAiB,GAAG,IAAI,CAACL,gBAAgB,CAACtB,aAAa;MAC7D;MACA,IAAI,CAACf,YAAY,CAAC4B,SAAS,CAACe,MAAM,CAAC,+BAA+B,EAAEH,aAAa,IAAI,CAAC,CAAC;MACvF,IAAI,CAACxC,YAAY,CAAC4B,SAAS,CAACe,MAAM,CAAC,8BAA8B,EAAEH,aAAa,IAAI,CAAC,CAAC;MACtF,IAAI,CAACxC,YAAY,CAAC4B,SAAS,CAACe,MAAM,CAAC,+BAA+B,EAAEH,aAAa,KAAK,CAAC,CAAC;MACxF,IAAI,CAACxC,YAAY,CAAC4B,SAAS,CAACe,MAAM,CAAC,iCAAiC,EAAEH,aAAa,KAAK,CAAC,CAAC;MAC1F;MACA;MACA,IAAI,IAAI,CAAC9B,uBAAuB,EAAE;QAC9B,MAAMkC,YAAY,GAAG,IAAI,CAACV,OAAO,CAACP,MAAM,KAAK,CAAC,IAAIa,aAAa,KAAK,CAAC;QACrEE,iBAAiB,CAACd,SAAS,CAACe,MAAM,CAAC,6BAA6B,EAAEC,YAAY,CAAC;QAC/EF,iBAAiB,CAACd,SAAS,CAACe,MAAM,CAAC,+BAA+B,EAAE,CAACC,YAAY,CAAC;MACtF,CAAC,MACI;QACDF,iBAAiB,CAACd,SAAS,CAACiB,MAAM,CAAC,6BAA6B,CAAC;QACjEH,iBAAiB,CAACd,SAAS,CAACiB,MAAM,CAAC,+BAA+B,CAAC;MACvE;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIJ,sBAAsBA,CAAA,EAAG;MACrB,IAAIK,UAAU,GAAG,IAAI,CAACZ,OAAO,CAACP,MAAM,GAAG,IAAI,CAACK,MAAM,CAACL,MAAM;MACzD,IAAI,IAAI,CAACjB,uBAAuB,EAAE;QAC9BoC,UAAU,IAAI,CAAC;MACnB;MACA,OAAOA,UAAU;IACrB;IACA;IACAR,+BAA+BA,CAAA,EAAG;MAC9B,IAAI,CAAC5B,uBAAuB,GAAGqC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACX,gBAAgB,CAACtB,aAAa,CAACkC,UAAU,CAAC,CACpFC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKD,IAAI,CAACE,YAAY,CAAC,CACnDC,IAAI,CAACH,IAAI,IAAI,CAAC,EAAEA,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACI,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtE;IACA,OAAOxG,IAAI,YAAAyG,wBAAAvG,iBAAA;MAAA,YAAAA,iBAAA,IAAwF0C,eAAe;IAAA;IAClH,OAAOzC,IAAI,kBA3U8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA2UJuC,eAAe;MAAA8D,cAAA,WAAAC,+BAAA9J,EAAA,EAAAC,GAAA,EAAA8J,QAAA;QAAA,IAAA/J,EAAA;UA3Ub9C,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EA2UwUtF,iBAAiB;UA3U3VvH,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EA2UkYhF,eAAe;QAAA;QAAA,IAAA/E,EAAA;UAAA,IAAAiK,EAAA;UA3UnZ/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAqG,QAAA,GAAA2D,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAsG,MAAA,GAAA0D,EAAA;QAAA;MAAA;MAAA3F,QAAA;MAAAC,YAAA,WAAA6F,6BAAApK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA2I,WAAA,kBAAA5F,GAAA,CAAAW,QAAA,cAAAX,GAAA,CAAAmG,gBAAA,IAAAnG,GAAA,CAAAW,QAAA,IA2U8B,IAAI;UA3UpC1D,EAAE,CAAAyD,WAAA,4BAAAV,GAAA,CAAAW,QA2UU,CAAC;QAAA;MAAA;MAAAkF,MAAA;QAAAU,KAAA;QAAAnB,aAAA;QAAAzE,QAAA;MAAA;IAAA;EAC1G;EAAC,OAnLKmF,eAAe;AAAA;AAoLrB;EAAA,QAAApC,SAAA,oBAAAA,SAAA;AAAA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+E,0BAA0BA,CAAC2B,IAAI,EAAE;EACtC,MAAMC,SAAS,GAAGD,IAAI,CAAChC,OAAO,CAACP,MAAM;EACrC,MAAMyC,QAAQ,GAAGF,IAAI,CAAClC,MAAM,CAACL,MAAM;EACnC,IAAIwC,SAAS,GAAG,CAAC,EAAE;IACfE,OAAO,CAACC,IAAI,CAAC,0CAA0C,CAAC;EAC5D;EACA,IAAIH,SAAS,KAAK,CAAC,IAAIC,QAAQ,GAAG,CAAC,EAAE;IACjCC,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;EACpF;EACA,IAAIH,SAAS,KAAK,CAAC,IACfD,IAAI,CAACxD,uBAAuB,IAC5BwD,IAAI,CAAC5D,cAAc,KAAK,IAAI,IAC5B4D,IAAI,CAAC5D,cAAc,GAAG,CAAC,EAAE;IACzB+D,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;EAC7E;EACA,IAAIF,QAAQ,GAAG,CAAC,IAAKA,QAAQ,KAAK,CAAC,IAAIF,IAAI,CAACxD,uBAAwB,EAAE;IAClE2D,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;EAChE;AACJ;AAAC,IAEKC,aAAa;EAAnB,MAAMA,aAAa,SAASvF,WAAW,CAAC;IACpC;IACA;IACA;IACA;IACA;IACAC,iBAAiB,GAAG,KAAK;IACzB,OAAOjC,IAAI;MAAA,IAAAwH,0BAAA;MAAA,gBAAAC,sBAAAvH,iBAAA;QAAA,QAAAsH,0BAAA,KAAAA,0BAAA,GArY8EzN,EAAE,CAAA0H,qBAAA,CAqYQ8F,aAAa,IAAArH,iBAAA,IAAbqH,aAAa;MAAA;IAAA;IAChH,OAAOG,IAAI,kBAtY8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EAsYJkH,aAAa;MAAAjH,SAAA;MAAAC,SAAA,WAAiF,OAAO;MAAAqH,QAAA;MAAAlG,QAAA,GAtYnG3H,EAAE,CAAA8N,kBAAA,CAsYoL,CAAC;QAAEC,OAAO,EAAE9F,WAAW;QAAE+F,WAAW,EAAER;MAAc,CAAC,CAAC,GAtY5OxN,EAAE,CAAA4H,0BAAA;MAAAqG,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAvL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAgD,YAAA,EAsYiV,CAAC;QAAA;MAAA;MAAAuL,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACjb;EAAC,OATKjB,aAAa;AAAA;AAUnB;EAAA,QAAA/G,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;AACA;AACA;AACA,MAAMiI,QAAQ,gBAAG,IAAIzO,cAAc,CAAC,SAAS,CAAC;AAAC,IACzC0O,OAAO;EAAb,MAAMA,OAAO,SAAS1G,WAAW,CAAC;IAC9B,OAAOhC,IAAI;MAAA,IAAA2I,oBAAA;MAAA,gBAAAC,gBAAA1I,iBAAA;QAAA,QAAAyI,oBAAA,KAAAA,oBAAA,GAvZ8E5O,EAAE,CAAA0H,qBAAA,CAuZQiH,OAAO,IAAAxI,iBAAA,IAAPwI,OAAO;MAAA;IAAA;IAC1G,OAAOhB,IAAI,kBAxZ8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EAwZJqI,OAAO;MAAApI,SAAA;MAAAC,SAAA;MAAAqH,QAAA;MAAAlG,QAAA,GAxZL3H,EAAE,CAAA8N,kBAAA,CAwZ+H,CAAC;QAAEC,OAAO,EAAE9F,WAAW;QAAE+F,WAAW,EAAEW;MAAQ,CAAC,CAAC,GAxZjL3O,EAAE,CAAA4H,0BAAA;MAAAqG,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAU,iBAAAhM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAgD,YAAA,EAwZgR,CAAC;QAAA;MAAA;MAAAuL,MAAA,GAAAjM,GAAA;MAAAkM,aAAA;MAAAC,eAAA;IAAA;EAChX;EAAC,OAHKE,OAAO;AAAA;AAIb;EAAA,QAAAlI,SAAA,oBAAAA,SAAA;AAAA;AAKc,IACRsI,WAAW;EAAjB,MAAMA,WAAW,SAASlG,eAAe,CAAC;IACtCoC,MAAM;IACNE,OAAO;IACP6D,KAAK;IACL1D,gBAAgB;IAChB2D,SAAS;IACT;IACA,IAAIC,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACC,UAAU;IAC1B;IACA,IAAID,SAASA,CAACA,SAAS,EAAE;MACrB,IAAI,CAACC,UAAU,GAAGrP,qBAAqB,CAACoP,SAAS,CAAC;IACtD;IACAC,UAAU,GAAG,KAAK;IAClB;AACJ;AACA;AACA;IACIC,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACnG,YAAY,CAACgB,QAAQ,KAAK,GAAG,IAAI,IAAI,CAACkF,UAAU,GAAG,MAAM,GAAG,IAAI;IAChF;IACAE,0BAA0BA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACL,KAAK,CAACpE,MAAM,KAAK,CAAC,KAAK,IAAI,CAACxB,QAAQ,CAACwB,MAAM,KAAK,CAAC,IAAI,IAAI,CAACvB,MAAM,CAACuB,MAAM,KAAK,CAAC,CAAC;IAC9F;IACA,OAAO3E,IAAI;MAAA,IAAAqJ,wBAAA;MAAA,gBAAAC,oBAAApJ,iBAAA;QAAA,QAAAmJ,wBAAA,KAAAA,wBAAA,GAxb8EtP,EAAE,CAAA0H,qBAAA,CAwbQqH,WAAW,IAAA5I,iBAAA,IAAX4I,WAAW;MAAA;IAAA;IAC9G,OAAOpB,IAAI,kBAzb8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EAybJyI,WAAW;MAAAxI,SAAA;MAAAoG,cAAA,WAAA6C,2BAAA1M,EAAA,EAAAC,GAAA,EAAA8J,QAAA;QAAA,IAAA/J,EAAA;UAzbT9C,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EAyb0qBnG,eAAe;UAzb3rB1G,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EAybsvB/G,gBAAgB;UAzbxwB9F,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EAybi0BjG,eAAe;QAAA;QAAA,IAAA9D,EAAA;UAAA,IAAAiK,EAAA;UAzbl1B/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAkI,MAAA,GAAA8B,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAoI,OAAA,GAAA4B,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAiM,KAAA,GAAAjC,EAAA;QAAA;MAAA;MAAA0C,SAAA,WAAAC,kBAAA5M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA2P,WAAA,CAAApN,GAAA;UAAFvC,EAAE,CAAA2P,WAAA,CAAAnN,GAAA;QAAA;QAAA,IAAAM,EAAA;UAAA,IAAAiK,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAuI,gBAAA,GAAAyB,EAAA,CAAA6C,KAAA;UAAF5P,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAkM,SAAA,GAAAlC,EAAA,CAAA6C,KAAA;QAAA;MAAA;MAAApJ,SAAA;MAAAY,QAAA;MAAAC,YAAA,WAAAwI,yBAAA/M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA2I,WAAA,iBAybJ5F,GAAA,CAAAqM,eAAA,CAAgB,CAAC;UAzbfpP,EAAE,CAAAyD,WAAA,6BAAAV,GAAA,CAAAmM,SAybM,CAAC,uCAAAnM,GAAA,CAAAqG,QAAA,CAAAwB,MAAA,KAAS,CAAV,CAAC,qCAAA7H,GAAA,CAAAsG,MAAA,CAAAuB,MAAA,KAAO,CAAR,CAAC,sCAAA7H,GAAA,CAAAiM,KAAA,CAAApE,MAAA,KAAM,CAAP,CAAC,gDAAX7H,GAAA,CAAAsM,0BAAA,CAA2B,CAAjB,CAAC,4BAAAtM,GAAA,CAAAoG,eAAD,CAAC;QAAA;MAAA;MAAAP,MAAA;QAAAsG,SAAA;MAAA;MAAArB,QAAA;MAAAlG,QAAA,GAzbT3H,EAAE,CAAA4H,0BAAA;MAAAqG,kBAAA,EAAAvL,GAAA;MAAAwL,KAAA;MAAAC,IAAA;MAAA2B,MAAA;MAAA1B,QAAA,WAAA2B,qBAAAjN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAkN,GAAA,GAAFhQ,EAAE,CAAAiQ,gBAAA;UAAFjQ,EAAE,CAAAsO,eAAA,CAAA7L,GAAA;UAAFzC,EAAE,CAAAgD,YAAA,EAybqsC,CAAC;UAzbxsChD,EAAE,CAAAkD,cAAA,aAybgvC,CAAC;UAzbnvClD,EAAE,CAAAgD,YAAA,KAyb2yC,CAAC;UAzb9yChD,EAAE,CAAAgD,YAAA,KAybq2C,CAAC;UAzbx2ChD,EAAE,CAAAkD,cAAA,gBAybq+C,CAAC;UAzbx+ClD,EAAE,CAAAkQ,UAAA,+BAAAC,uDAAA;YAAFnQ,EAAE,CAAAoQ,aAAA,CAAAJ,GAAA;YAAA,OAAFhQ,EAAE,CAAAqQ,WAAA,CAyb68CtN,GAAA,CAAAyG,gBAAA,CAAiB,IAAI,CAAC;UAAA,CAAC,CAAC;UAzbv+CxJ,EAAE,CAAAgD,YAAA,KAybogD,CAAC;UAzbvgDhD,EAAE,CAAAqD,YAAA,CAyb+gD,CAAC,CAAQ,CAAC;UAzb3hDrD,EAAE,CAAAgD,YAAA,KAybklD,CAAC;UAzbrlDhD,EAAE,CAAAgD,YAAA,KAybsoD,CAAC;UAzbzoDhD,EAAE,CAAAmD,SAAA,YAybs1D,CAAC;QAAA;MAAA;MAAAmN,YAAA,GAA+CpO,iBAAiB;MAAAsM,aAAA;MAAAC,eAAA;IAAA;EACt/D;EAAC,OA1BKM,WAAW;AAAA;AA2BjB;EAAA,QAAAtI,SAAA,oBAAAA,SAAA;AAAA;;AAiCA;AACA;AACA;AACA;AACA;AACA,MAAM8J,cAAc,gBAAG,IAAItQ,cAAc,CAAC,eAAe,CAAC;AAAC,IACrDuQ,aAAa;EAAnB,MAAMA,aAAa,SAAS3H,eAAe,CAAC;IACxC4H,cAAc,GAAGvQ,MAAM,CAACqQ,cAAc,CAAC;IACvCG,kBAAkB,GAAGxQ,MAAM,CAACY,iBAAiB,CAAC;IAC9CmK,MAAM;IACNE,OAAO;IACPG,gBAAgB;IAChB;AACJ;AACA;AACA;AACA;IACIqF,cAAc,GAAG,IAAI5P,YAAY,CAAC,CAAC;IACnC;IACA6P,cAAc,GAAG,OAAO;IACxB;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIC,gBAAgBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACD,cAAc;IAC9B;IACA,IAAIC,gBAAgBA,CAACxI,KAAK,EAAE;MACxB,IAAI,CAACuI,cAAc,GAAGvI,KAAK;IAC/B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIyI,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACN,cAAc,CAACK,KAAK;IACnD;IACA,IAAIA,KAAKA,CAACE,QAAQ,EAAE;MAChB,IAAI,CAACD,MAAM,GAAGC,QAAQ;IAC1B;IACAD,MAAM;IACN;IACA,IAAI1I,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC4I,MAAM;IACtB;IACA,IAAI5I,KAAKA,CAAC2I,QAAQ,EAAE;MAChB,IAAI,IAAI,CAACnN,QAAQ,IAAImN,QAAQ,KAAK,IAAI,CAAC3I,KAAK,IAAI,IAAI,CAAC6I,kBAAkB,EAAE;QACrE,IAAI,CAACrN,QAAQ,GAAG,KAAK;MACzB;MACA,IAAI,CAACoN,MAAM,GAAGD,QAAQ;IAC1B;IACAC,MAAM;IACN;IACA,IAAIpN,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC4M,cAAc,CAACU,eAAe,CAACC,UAAU,CAAC,IAAI,CAAC;IAC/D;IACA,IAAIvN,QAAQA,CAACwE,KAAK,EAAE;MAChB,MAAM+I,UAAU,GAAGtR,qBAAqB,CAACuI,KAAK,CAAC;MAC/C,IAAI+I,UAAU,KAAK,IAAI,CAACC,SAAS,EAAE;QAC/B,IAAI,CAACC,YAAY,CAACF,UAAU,CAAC;QAC7B,IAAIA,UAAU,IAAI,IAAI,CAACX,cAAc,CAACc,QAAQ,EAAE;UAC5C,IAAI,CAACd,cAAc,CAACe,kBAAkB,CAAC,CAAC;QAC5C;MACJ;IACJ;IACAH,SAAS,GAAG,KAAK;IACjB;AACJ;AACA;AACA;IACIH,kBAAkB,GAAG,KAAK;IAC1BO,QAAQA,CAAA,EAAG;MACP,MAAMC,IAAI,GAAG,IAAI,CAACjB,cAAc;MAChC,IAAIiB,IAAI,CAACT,MAAM,IAAIS,IAAI,CAACT,MAAM,CAAC1E,IAAI,CAAClE,KAAK,IAAIqJ,IAAI,CAACC,WAAW,CAAC,IAAI,CAACV,MAAM,EAAE5I,KAAK,CAAC,CAAC,EAAE;QAChF,IAAI,CAACiJ,YAAY,CAAC,IAAI,CAAC;MAC3B;MACA,MAAMM,WAAW,GAAG,IAAI,CAACP,SAAS;MAClC;MACA;MACA;MACA;MACA;MACAQ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,IAAI,CAACV,SAAS,IAAIO,WAAW,EAAE;UAC/B,IAAI,CAAC/N,QAAQ,GAAG,IAAI;UACpB,IAAI,CAAC6M,kBAAkB,CAACsB,YAAY,CAAC,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,IAAI,CAACd,kBAAkB,GAAG,IAAI;IAClC;IACA1G,WAAWA,CAAA,EAAG;MACV,KAAK,CAACA,WAAW,CAAC,CAAC;MACnB,IAAI,IAAI,CAAC3G,QAAQ,EAAE;QACf;QACA;QACAgO,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAAClO,QAAQ,GAAG,KAAK;QACzB,CAAC,CAAC;MACN;IACJ;IACA;IACA+H,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC/H,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAClC;IACA;IACAoO,KAAKA,CAAA,EAAG;MACJ,IAAI,CAAChJ,YAAY,CAACgJ,KAAK,CAAC,CAAC;IAC7B;IACA;IACAC,QAAQA,CAAA,EAAG;MACP,MAAMC,YAAY,GAAG,IAAI,CAAChH,OAAO,EAAEiH,GAAG,CAAC,CAAC,CAAC,EAAErM,WAAW,CAACiE,aAAa;MACpE;MACA;MACA,MAAMqI,OAAO,GAAGF,YAAY,IAAI,IAAI,CAAC7G,gBAAgB,EAAEtB,aAAa;MACpE,OAAOqI,OAAO,EAAE7F,WAAW,IAAI,EAAE;IACrC;IACA;IACA8F,cAAcA,CAACC,QAAQ,EAAE;MACrB,OAAO,IAAI,CAAC9B,cAAc,CAACc,QAAQ,IAAI,IAAI,CAACrK,kBAAkB,CAAC,CAAC,KAAKqL,QAAQ;IACjF;IACA;IACAC,WAAWA,CAACD,QAAQ,EAAE;MAClB,OAAQ,CAAC,IAAI,CAAC9B,cAAc,CAACc,QAAQ,IACjC,IAAI,CAACrK,kBAAkB,CAAC,CAAC,KAAKqL,QAAQ,IACtC,CAAC,IAAI,CAAC9B,cAAc,CAACgC,4BAA4B;IACzD;IACA;IACAC,oBAAoBA,CAACH,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAACI,aAAa,CAAC,OAAO,EAAEJ,QAAQ,CAAC,IAAI,IAAI,CAACI,aAAa,CAAC,SAAS,EAAEJ,QAAQ,CAAC;IAC3F;IACA;IACAI,aAAaA,CAACrM,IAAI,EAAEiM,QAAQ,EAAE;MAC1B;MACA;MACA,OAAQ,IAAI,CAACrL,kBAAkB,CAAC,CAAC,KAAKqL,QAAQ,KACzCjM,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC8C,QAAQ,CAACwB,MAAM,KAAK,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACuB,MAAM,KAAK,CAAC,CAAC;IACpF;IACAgI,WAAWA,CAAA,EAAG;MACV,IAAI,CAACnC,cAAc,CAACoC,UAAU,CAAC,CAAC;IACpC;IACA;IACA3L,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC0J,cAAc,IAAI,OAAO;IACzC;IACA;AACJ;AACA;AACA;IACIU,YAAYA,CAACzN,QAAQ,EAAE;MACnB,IAAIA,QAAQ,KAAK,IAAI,CAACwN,SAAS,EAAE;QAC7B,OAAO,KAAK;MAChB;MACA,IAAI,CAACA,SAAS,GAAGxN,QAAQ;MACzB,IAAIA,QAAQ,EAAE;QACV,IAAI,CAAC4M,cAAc,CAACU,eAAe,CAAC2B,MAAM,CAAC,IAAI,CAAC;MACpD,CAAC,MACI;QACD,IAAI,CAACrC,cAAc,CAACU,eAAe,CAAC4B,QAAQ,CAAC,IAAI,CAAC;MACtD;MACA,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAACnP,QAAQ,CAAC;MAClC,IAAI,CAAC6M,kBAAkB,CAACsB,YAAY,CAAC,CAAC;MACtC,OAAO,IAAI;IACf;IACA;AACJ;AACA;AACA;AACA;IACIiB,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACvC,kBAAkB,CAACsB,YAAY,CAAC,CAAC;IAC1C;IACA;IACAkB,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAACxP,QAAQ,EAAE;QAChB,IAAI,IAAI,CAAC+M,cAAc,CAACc,QAAQ,EAAE;UAC9B,IAAI,CAAC1N,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;UAC9B,IAAI,CAAC4M,cAAc,CAAC0C,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,MACI,IAAI,CAAC,IAAI,CAACtP,QAAQ,EAAE;UACrB,IAAI,CAACA,QAAQ,GAAG,IAAI;UACpB,IAAI,CAAC4M,cAAc,CAAC0C,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;QAChD;MACJ;IACJ;IACA;IACAC,YAAYA,CAAC/K,KAAK,EAAE;MAChB,IAAI,CAACY,YAAY,CAACoB,YAAY,CAAC,UAAU,EAAEhC,KAAK,GAAG,EAAE,CAAC;IAC1D;IACAgH,0BAA0BA,CAAA,EAAG;MACzB,MAAMgE,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,IACtD,IAAI,CAACA,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,IACrC,IAAI,CAACL,cAAc,CAAC,QAAQ,CAAC,IAC7B,IAAI,CAACE,WAAW,CAAC,QAAQ,CAAC;MAC9B,MAAMc,WAAW,GAAG,IAAI,CAACX,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,IACpD,IAAI,CAACA,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,IACtC,IAAI,CAACL,cAAc,CAAC,OAAO,CAAC,IAC5B,IAAI,CAACE,WAAW,CAAC,OAAO,CAAC;MAC7B,OAAOa,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOrN,IAAI;MAAA,IAAAsN,0BAAA;MAAA,gBAAAC,sBAAArN,iBAAA;QAAA,QAAAoN,0BAAA,KAAAA,0BAAA,GAzqB8EvT,EAAE,CAAA0H,qBAAA,CAyqBQ8I,aAAa,IAAArK,iBAAA,IAAbqK,aAAa;MAAA;IAAA;IAChH,OAAO7C,IAAI,kBA1qB8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EA0qBJkK,aAAa;MAAAjK,SAAA;MAAAoG,cAAA,WAAA8G,6BAAA3Q,EAAA,EAAAC,GAAA,EAAA8J,QAAA;QAAA,IAAA/J,EAAA;UA1qBX9C,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EA6qBrCnG,eAAe;UA7qBoB1G,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EA6qBuC/G,gBAAgB;QAAA;QAAA,IAAAhD,EAAA;UAAA,IAAAiK,EAAA;UA7qBzD/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAkI,MAAA,GAAA8B,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAoI,OAAA,GAAA4B,EAAA;QAAA;MAAA;MAAA0C,SAAA,WAAAiE,oBAAA5Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA2P,WAAA,CAAApN,GAAA;QAAA;QAAA,IAAAO,EAAA;UAAA,IAAAiK,EAAA;UAAF/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAAuI,gBAAA,GAAAyB,EAAA,CAAA6C,KAAA;QAAA;MAAA;MAAApJ,SAAA,WA0qBmR,QAAQ;MAAAY,QAAA;MAAAC,YAAA,WAAAsM,2BAAA7Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1qB7R9C,EAAE,CAAAkQ,UAAA,kBAAA0D,sCAAA;YAAA,OA0qBJ7Q,GAAA,CAAA6P,WAAA,CAAY,CAAC;UAAA,CAAD,CAAC,mBAAAiB,uCAAA;YAAA,OAAb9Q,GAAA,CAAAmQ,oBAAA,CAAqB,CAAC;UAAA,CAAV,CAAC;QAAA;QAAA,IAAApQ,EAAA;UA1qBX9C,EAAE,CAAA2I,WAAA,kBAAA5F,GAAA,CAAAc,QAAA;UAAF7D,EAAE,CAAAyD,WAAA,4BAAAV,GAAA,CAAAc,QAAA,KAAAd,GAAA,CAAA0N,cAAA,CAAAc,QAAA,IAAAxO,GAAA,CAAA0N,cAAA,CAAAgC,4BA0qBQ,CAAC,uCAAb1P,GAAA,CAAA4P,aAAA,CAAc,SAAS,EAAE,QAAQ,CAArB,CAAC,qCAAb5P,GAAA,CAAA4P,aAAA,CAAc,OAAO,EAAE,QAAQ,CAAnB,CAAC,sCAAb5P,GAAA,CAAA4P,aAAA,CAAc,OAAO,EAAE,OAAO,CAAlB,CAAC,6CAAb5P,GAAA,CAAA4P,aAAA,CAAc,SAAS,EAAE,OAAO,CAApB,CAAC,yCAAb5P,GAAA,CAAAuP,cAAA,CAAe,QAAQ,CAAX,CAAC,0CAAbvP,GAAA,CAAAuP,cAAA,CAAe,OAAO,CAAV,CAAC,sCAAbvP,GAAA,CAAAyP,WAAA,CAAY,QAAQ,CAAR,CAAC,uCAAbzP,GAAA,CAAAyP,WAAA,CAAY,OAAO,CAAP,CAAC,gDAAbzP,GAAA,CAAAsM,0BAAA,CAA2B,CAAf,CAAC,eAAAtM,GAAA,CAAA+N,KAAA,KAAH,SAAS,IAAA/N,GAAA,CAAA+N,KAAA,KAAc,MAArB,CAAC,aAAA/N,GAAA,CAAA+N,KAAA,KAAH,MAAE,CAAC,4BAAA/N,GAAA,CAAAoG,eAAD,CAAC;QAAA;MAAA;MAAAP,MAAA;QAAAgI,cAAA;QAAAC,gBAAA;QAAAC,KAAA;QAAAzI,KAAA;QAAAxE,QAAA;MAAA;MAAAiQ,OAAA;QAAAnD,cAAA;MAAA;MAAA9C,QAAA;MAAAlG,QAAA,GA1qBX3H,EAAE,CAAA8N,kBAAA,CA0qBq9C,CACxiD;QAAEC,OAAO,EAAElF,eAAe;QAAEmF,WAAW,EAAEwC;MAAc,CAAC,EACxD;QAAEzC,OAAO,EAAElI,WAAW;QAAEmI,WAAW,EAAEwC;MAAc,CAAC,CACvD,GA7qBoFxQ,EAAE,CAAA4H,0BAAA;MAAAqG,kBAAA,EAAArL,GAAA;MAAAsL,KAAA;MAAAC,IAAA;MAAA2B,MAAA;MAAA1B,QAAA,WAAA2F,uBAAAjR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAkN,GAAA,GAAFhQ,EAAE,CAAAiQ,gBAAA;UAAFjQ,EAAE,CAAAsO,eAAA,CAAA3L,GAAA;UAAF3C,EAAE,CAAAiE,UAAA,IAAApB,oCAAA,gCAAF7C,EAAE,CAAAgU,sBA6qBiiB,CAAC,IAAA/Q,oCAAA,gCA7qBpiBjD,EAAE,CAAAgU,sBA6qB8pB,CAAC,IAAAlQ,oCAAA,gCA7qBjqB9D,EAAE,CAAAgU,sBA6qBwxC,CAAC;UA7qB3xChU,EAAE,CAAAiU,mBAAA,IAAAjQ,oCAAA,iBA6qByrD,CAAC,IAAAK,oCAAA,iBAA+O,CAAC;UA7qB56DrE,EAAE,CAAAiU,mBAAA,IAAAzP,oCAAA,eA6qBkuE,CAAC;UA7qBruExE,EAAE,CAAAkD,cAAA,aA6qB21E,CAAC;UA7qB91ElD,EAAE,CAAAgD,YAAA,GA6qBs5E,CAAC;UA7qBz5EhD,EAAE,CAAAgD,YAAA,MA6qBg9E,CAAC;UA7qBn9EhD,EAAE,CAAAkD,cAAA,iBA6qBglF,CAAC;UA7qBnlFlD,EAAE,CAAAkQ,UAAA,+BAAAgE,0DAAA;YAAFlU,EAAE,CAAAoQ,aAAA,CAAAJ,GAAA;YAAA,OAAFhQ,EAAE,CAAAqQ,WAAA,CA6qBwjFtN,GAAA,CAAAyG,gBAAA,CAAiB,IAAI,CAAC;UAAA,CAAC,CAAC;UA7qBllFxJ,EAAE,CAAAgD,YAAA,MA6qB+mF,CAAC;UA7qBlnFhD,EAAE,CAAAqD,YAAA,CA6qB0nF,CAAC,CAAQ,CAAC;UA7qBtoFrD,EAAE,CAAAiU,mBAAA,KAAAtP,qCAAA,iBA6qBsqF,CAAC,KAAAE,qCAAA,iBAA0M,CAAC;UA7qBp3F7E,EAAE,CAAAiU,mBAAA,KAAAlP,qCAAA,eA6qBqoG,CAAC;UA7qBxoG/E,EAAE,CAAAgD,YAAA,MA6qB0wG,CAAC;UA7qB7wGhD,EAAE,CAAAmD,SAAA,cA6qB09G,CAAC;QAAA;QAAA,IAAAL,EAAA;UA7qB79G9C,EAAE,CAAA2D,SAAA,EA6qBinE,CAAC;UA7qBpnE3D,EAAE,CAAAmU,aAAA,CAAApR,GAAA,CAAAuP,cAAA,iBAAAvP,GAAA,CAAAyP,WAAA,mBA6qBinE,CAAC;UA7qBpnExS,EAAE,CAAA2D,SAAA,EA6qBiyE,CAAC;UA7qBpyE3D,EAAE,CAAAmU,aAAA,CAAApR,GAAA,CAAA2P,oBAAA,mBA6qBiyE,CAAC;UA7qBpyE1S,EAAE,CAAA2D,SAAA,EA6qBohG,CAAC;UA7qBvhG3D,EAAE,CAAAmU,aAAA,CAAApR,GAAA,CAAAuP,cAAA,iBAAAvP,GAAA,CAAAyP,WAAA,mBA6qBohG,CAAC;UA7qBvhGxS,EAAE,CAAA2D,SAAA,EA6qBosG,CAAC;UA7qBvsG3D,EAAE,CAAAmU,aAAA,CAAApR,GAAA,CAAA2P,oBAAA,mBA6qBosG,CAAC;QAAA;MAAA;MAAApC,YAAA,GAAsvlBrO,gBAAgB,EAAoJC,iBAAiB;MAAAqM,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC/ssB;EAAC,OA5MK+B,aAAa;AAAA;AA6MnB;EAAA,QAAA/J,SAAA,oBAAAA,SAAA;AAAA;;AAwDA;AACA;AACA;AACA;AAHA,IAIM2N,4BAA4B;EAAlC,MAAMA,4BAA4B,CAAC;IAC/B,OAAOnO,IAAI,YAAAoO,qCAAAlO,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiO,4BAA4B;IAAA;IAC/H,OAAOhO,IAAI,kBA7uB8EpG,EAAE,CAAAqG,iBAAA;MAAAC,IAAA,EA6uBJ8N,4BAA4B;MAAA7N,SAAA;MAAAC,SAAA;IAAA;EACvH;EAAC,OAHK4N,4BAA4B;AAAA;AAIlC;EAAA,QAAA3N,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA;AACA;AACA;AACA;AACA,MAAM6N,YAAY,gBAAG,IAAIrU,cAAc,CAAC,YAAY,CAAC;AAAC,IAChDsU,UAAU;EAAhB,MAAMA,UAAU,SAAStM,WAAW,CAAC;IACjC;IACA;IACA;IACA;IACA;IACAC,iBAAiB,GAAG,KAAK;IACzB,OAAOjC,IAAI;MAAA,IAAAuO,uBAAA;MAAA,gBAAAC,mBAAAtO,iBAAA;QAAA,QAAAqO,uBAAA,KAAAA,uBAAA,GAtwB8ExU,EAAE,CAAA0H,qBAAA,CAswBQ6M,UAAU,IAAApO,iBAAA,IAAVoO,UAAU;MAAA;IAAA;IAC7G,OAAO5G,IAAI,kBAvwB8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EAuwBJiO,UAAU;MAAAhO,SAAA;MAAAC,SAAA,WAA8E,YAAY;MAAAqH,QAAA;MAAAlG,QAAA,GAvwBlG3H,EAAE,CAAA8N,kBAAA,CAuwBgL,CAAC;QAAEC,OAAO,EAAE9F,WAAW;QAAE+F,WAAW,EAAEuG;MAAW,CAAC,CAAC,GAvwBrOvU,EAAE,CAAA4H,0BAAA;MAAAqG,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAsG,oBAAA5R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAgD,YAAA,EAuwBuU,CAAC;QAAA;MAAA;MAAAuL,MAAA,GAAAjM,GAAA;MAAAkM,aAAA;MAAAC,eAAA;IAAA;EACva;EAAC,OATK8F,UAAU;AAAA;AAUhB;EAAA,QAAA9N,SAAA,oBAAAA,SAAA;AAAA;AAQA,MAAMkO,iCAAiC,GAAG;EACtC5G,OAAO,EAAExI,iBAAiB;EAC1ByI,WAAW,eAAE/M,UAAU,CAAC,MAAM2T,gBAAgB,CAAC;EAC/CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,sBAAsB,CAAC;EACzBC,MAAM;EACNC,OAAO;EACPhP,WAAWA,CACX;EACA+O,MAAM,EACN;EACAC,OAAO,EAAE;IACL,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AAAC,IACKJ,gBAAgB;EAAtB,MAAMA,gBAAgB,SAAS3M,WAAW,CAAC;IACvCgN,QAAQ,GAAG/U,MAAM,CAACC,UAAU,CAAC;IAC7B2I,OAAO,GAAG5I,MAAM,CAACK,MAAM,CAAC;IACxB2U,SAAS,GAAGhV,MAAM,CAACgB,SAAS,CAAC;IAC7BiU,YAAY,GAAG,KAAK;IACpBC,WAAW;IACXC,iBAAiB;IACjB;IACAC,UAAU,GAAG,IAAI7T,OAAO,CAAC,CAAC;IAC1B;IACA8T,YAAY;IACZ;IACAC,SAAS,GAAI1T,CAAC,IAAK,CAAE,CAAC;IACtB2T,MAAM;IACN;IACAC,eAAe,GAAG,IAAI3U,YAAY,CAAC,CAAC;IACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI+P,KAAK,GAAG,QAAQ;IAChB;AACJ;AACA;AACA;AACA;IACIa,WAAW,GAAGA,CAACgE,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACnC;IACA,IAAIrE,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACsE,SAAS;IACzB;IACA,IAAItE,QAAQA,CAAClJ,KAAK,EAAE;MAChB,MAAM2I,QAAQ,GAAGlR,qBAAqB,CAACuI,KAAK,CAAC;MAC7C,IAAI2I,QAAQ,KAAK,IAAI,CAAC6E,SAAS,EAAE;QAC7B,IAAI,CAAC,OAAOpP,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,IAAI,CAAC0O,YAAY,EAAE;UACtE,MAAM,IAAIW,KAAK,CAAC,2EAA2E,CAAC;QAChG;QACA,IAAI,CAACD,SAAS,GAAG7E,QAAQ;QACzB,IAAI,CAACG,eAAe,GAAG,IAAIjM,cAAc,CAAC,IAAI,CAAC2Q,SAAS,EAAE,IAAI,CAAC1E,eAAe,CAACtN,QAAQ,CAAC;MAC5F;IACJ;IACAgS,SAAS,GAAG,IAAI;IAChB;IACA,IAAIpD,4BAA4BA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAACsD,6BAA6B;IAC7C;IACA,IAAItD,4BAA4BA,CAACpK,KAAK,EAAE;MACpC,IAAI,CAAC0N,6BAA6B,GAAGjW,qBAAqB,CAACuI,KAAK,CAAC;IACrE;IACA0N,6BAA6B,GAAG,IAAI,CAACvN,eAAe,EAAEiK,4BAA4B,IAAI,KAAK;IAC3F;IACAtB,eAAe,GAAG,IAAIjM,cAAc,CAAC,IAAI,CAAC2Q,SAAS,CAAC;IACpD;IACA5E,MAAM;IACN;IACA4B,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtBnC,kBAAkB,GAAGxQ,MAAM,CAACY,iBAAiB,CAAC;IAC9CkF,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,IAAI,CAACkC,iBAAiB,GAAG,KAAK;IAClC;IACAoC,eAAeA,CAAA,EAAG;MACd;MACA;MACA,IAAI,CAAC6K,YAAY,GAAG,IAAI;MACxB,IAAI,CAACa,oBAAoB,CAAC,CAAC;MAC3B;MACA;MACA,IAAI,CAAClN,OAAO,CAACkC,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACqK,iBAAiB,GAAG,CACrB,IAAI,CAACH,SAAS,CAACe,MAAM,CAAC,IAAI,CAAChB,QAAQ,CAACjL,aAAa,EAAE,SAAS,EAAE,IAAI,CAACkM,cAAc,CAAC,EAClF,IAAI,CAAChB,SAAS,CAACe,MAAM,CAAC,IAAI,CAAChB,QAAQ,CAACjL,aAAa,EAAE,UAAU,EAAE,IAAI,CAACmM,eAAe,CAAC,CACvF;MACL,CAAC,CAAC;MACF,IAAI,IAAI,CAAClF,MAAM,EAAE;QACb,IAAI,CAACmF,qBAAqB,CAAC,IAAI,CAACnF,MAAM,CAAC;MAC3C;MACA,IAAI,CAACoF,wBAAwB,CAAC,CAAC;IACnC;IACAC,WAAWA,CAACpL,OAAO,EAAE;MACjB,MAAMqL,eAAe,GAAGrL,OAAO,CAAC,UAAU,CAAC;MAC3C,MAAMsL,oBAAoB,GAAGtL,OAAO,CAAC,eAAe,CAAC;MACrD,MAAMuL,mCAAmC,GAAGvL,OAAO,CAAC,8BAA8B,CAAC;MACnF,IAAKsL,oBAAoB,IAAI,CAACA,oBAAoB,CAACE,WAAW,IACzDH,eAAe,IAAI,CAACA,eAAe,CAACG,WAAY,IAChDD,mCAAmC,IAAI,CAACA,mCAAmC,CAACC,WAAY,EAAE;QAC3F,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ;IACAnM,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC4K,WAAW,EAAEwB,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACvB,iBAAiB,EAAEwB,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MACrD,IAAI,CAACxB,UAAU,CAACyB,IAAI,CAAC,CAAC;MACtB,IAAI,CAACzB,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAACzB,YAAY,GAAG,IAAI;IAC5B;IACA;IACAtD,KAAKA,CAAC+C,OAAO,EAAE;MACX,IAAI,CAACC,QAAQ,CAACjL,aAAa,CAACiI,KAAK,CAAC+C,OAAO,CAAC;IAC9C;IACA;IACAiC,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC;IAC5C;IACA;IACAC,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAACD,sBAAsB,CAAC,KAAK,CAAC;IAC7C;IACA;IACA1F,kBAAkBA,CAAA,EAAG;MACjB;MACA;MACA;MACA,IAAI,IAAI,CAACwD,OAAO,IAAI,CAAC,IAAI,CAACO,YAAY,EAAE;QACpC,MAAMlN,KAAK,GAAG,IAAI,CAAC+O,wBAAwB,CAAC,CAAC;QAC7C,IAAI,CAAC5B,SAAS,CAACnN,KAAK,CAAC;QACrB,IAAI,CAAC4I,MAAM,GAAG5I,KAAK;MACvB;IACJ;IACA;IACA8K,gBAAgBA,CAAC6B,OAAO,EAAE;MACtB,IAAI,CAACU,eAAe,CAAC1C,IAAI,CAAC,IAAI8B,sBAAsB,CAAC,IAAI,EAAEE,OAAO,CAAC,CAAC;IACxE;IACA;IACAqC,UAAUA,CAACC,MAAM,EAAE;MACf,IAAI,CAACrG,MAAM,GAAGqG,MAAM;MACpB,IAAI,IAAI,CAACtC,OAAO,EAAE;QACd,IAAI,CAACoB,qBAAqB,CAACkB,MAAM,IAAI,EAAE,CAAC;MAC5C;IACJ;IACA;IACAC,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAAC9T,QAAQ,GAAG8T,UAAU;MAC1B,IAAI,CAAC9G,kBAAkB,CAACsB,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC2E,oBAAoB,CAAC,CAAC;IAC/B;IACA;AACJ;AACA;AACA;IACI,IAAIjT,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC+T,sBAAsB,CAAC,CAAC;IACxC;IACA,IAAI/T,QAAQA,CAAC2E,KAAK,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAACoP,sBAAsB,CAAClP,GAAG,CAACzI,qBAAqB,CAACuI,KAAK,CAAC,CAAC;MAC7D,IAAI,IAAI,CAACoP,sBAAsB,CAAC,CAAC,EAAE;QAC/B,IAAI,CAACrC,WAAW,EAAEsC,aAAa,CAAC,CAAC,CAAC,CAAC;MACvC;IACJ;IACAD,sBAAsB,GAAGpX,MAAM,CAAC,KAAK,CAAC;IACtC;IACAsX,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACpC,SAAS,GAAGoC,EAAE;IACvB;IACA;IACAC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAAC/E,UAAU,GAAG+E,EAAE;IACxB;IACA;IACAvB,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAClF,eAAe,CAAC2G,OAAO,CAACC,IAAI,CAACvS,SAAS,CAAC,IAAI,CAAC8P,UAAU,CAAC,CAAC,CAAClK,SAAS,CAAC4M,KAAK,IAAI;QAC7E;QACA,KAAK,IAAI7K,IAAI,IAAI6K,KAAK,CAACC,KAAK,EAAE;UAC1B9K,IAAI,CAACtJ,QAAQ,GAAG,IAAI;QACxB;QACA,KAAK,IAAIsJ,IAAI,IAAI6K,KAAK,CAACE,OAAO,EAAE;UAC5B/K,IAAI,CAACtJ,QAAQ,GAAG,KAAK;QACzB;QACA,IAAI,CAAC,IAAI,CAACsU,cAAc,CAAC,CAAC,EAAE;UACxB,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;IACA;IACAhC,qBAAqBA,CAACkB,MAAM,EAAE;MAC1B,IAAI,CAACtC,OAAO,CAAC6B,OAAO,CAACwB,MAAM,IAAIA,MAAM,CAAC/G,YAAY,CAAC,KAAK,CAAC,CAAC;MAC1DgG,MAAM,CAACT,OAAO,CAACxO,KAAK,IAAI;QACpB,MAAMiQ,mBAAmB,GAAG,IAAI,CAACtD,OAAO,CAACuD,IAAI,CAACF,MAAM,IAAI;UACpD;UACA;UACA,OAAOA,MAAM,CAACxU,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC8N,WAAW,CAAC0G,MAAM,CAAChQ,KAAK,EAAEA,KAAK,CAAC;QAC1E,CAAC,CAAC;QACF,IAAIiQ,mBAAmB,EAAE;UACrBA,mBAAmB,CAAChH,YAAY,CAAC,IAAI,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN;IACA;IACA8F,wBAAwBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACpC,OAAO,CAAC7I,MAAM,CAACkM,MAAM,IAAIA,MAAM,CAACxU,QAAQ,CAAC,CAAC2U,GAAG,CAACH,MAAM,IAAIA,MAAM,CAAChQ,KAAK,CAAC;IACrF;IACA;IACAsO,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC3B,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC6B,OAAO,CAACwB,MAAM,IAAIA,MAAM,CAACpF,aAAa,CAAC,CAAC,CAAC;MAC1D;IACJ;IACA;AACJ;AACA;AACA;IACIiE,sBAAsBA,CAAC9F,UAAU,EAAEqH,YAAY,EAAE;MAC7C;MACA;MACA,MAAMC,cAAc,GAAG,EAAE;MACzB,IAAI,CAAC1D,OAAO,CAAC6B,OAAO,CAACwB,MAAM,IAAI;QAC3B,IAAI,CAAC,CAACI,YAAY,IAAI,CAACJ,MAAM,CAAC3U,QAAQ,KAAK2U,MAAM,CAAC/G,YAAY,CAACF,UAAU,CAAC,EAAE;UACxEsH,cAAc,CAACC,IAAI,CAACN,MAAM,CAAC;QAC/B;MACJ,CAAC,CAAC;MACF,IAAIK,cAAc,CAAC9N,MAAM,EAAE;QACvB,IAAI,CAAC4G,kBAAkB,CAAC,CAAC;MAC7B;MACA,OAAOkH,cAAc;IACzB;IACA;IACA;IACA;IACA,IAAI1D,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACS,MAAM;IACtB;IACA;IACAmD,cAAcA,CAACZ,KAAK,EAAE;MAClB,MAAMa,UAAU,GAAG,IAAI,CAACzD,WAAW,CAACyD,UAAU;MAC9C,IAAI,CAACb,KAAK,CAACc,OAAO,KAAK3T,KAAK,IAAI6S,KAAK,CAACc,OAAO,KAAK1T,KAAK,KACnD,CAAC,IAAI,CAACgQ,WAAW,CAAC2D,QAAQ,CAAC,CAAC,IAC5BF,UAAU,IACV,CAACA,UAAU,CAACnV,QAAQ,EAAE;QACtBsU,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtBH,UAAU,CAAC3F,oBAAoB,CAAC,CAAC;MACrC,CAAC,MACI,IAAI8E,KAAK,CAACc,OAAO,KAAKzT,CAAC,IACxB,IAAI,CAACkM,QAAQ,IACb,CAAC,IAAI,CAAC6D,WAAW,CAAC2D,QAAQ,CAAC,CAAC,IAC5BzT,cAAc,CAAC0S,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE;QAC7C,MAAMiB,YAAY,GAAG,IAAI,CAACjE,OAAO,CAACzI,IAAI,CAAC8L,MAAM,IAAI,CAACA,MAAM,CAAC3U,QAAQ,IAAI,CAAC2U,MAAM,CAACxU,QAAQ,CAAC;QACtFmU,KAAK,CAACgB,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC7F,gBAAgB,CAAC,IAAI,CAAC+D,sBAAsB,CAAC+B,YAAY,EAAE,IAAI,CAAC,CAAC;MAC1E,CAAC,MACI;QACD,IAAI,CAAC7D,WAAW,CAAC8D,SAAS,CAAClB,KAAK,CAAC;MACrC;IACJ;IACA;IACA7B,eAAe,GAAGA,CAAA,KAAM;MACpB;MACAgD,UAAU,CAAC,MAAM;QACb,IAAI,CAAC,IAAI,CAAChB,cAAc,CAAC,CAAC,EAAE;UACxB,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN,CAAC;IACD;IACAlC,cAAc,GAAI8B,KAAK,IAAK;MACxB,IAAI,IAAI,CAACtU,QAAQ,EAAE;QACf;MACJ;MACA,MAAM0V,WAAW,GAAG,IAAI,CAAC3D,MAAM,CAC1B4D,OAAO,CAAC,CAAC,CACTC,SAAS,CAACnM,IAAI,IAAIA,IAAI,CAACpH,WAAW,CAACiE,aAAa,CAACuP,QAAQ,CAACvB,KAAK,CAACwB,MAAM,CAAC,CAAC;MAC7E,IAAIJ,WAAW,GAAG,CAAC,CAAC,EAAE;QAClB,IAAI,CAACK,gBAAgB,CAACL,WAAW,CAAC;MACtC,CAAC,MACI;QACD,IAAI,CAAChB,kBAAkB,CAAC,CAAC;MAC7B;IACJ,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIpC,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACZ,WAAW,GAAG,IAAInQ,eAAe,CAAC,IAAI,CAACwQ,MAAM,CAAC,CAC9CiE,cAAc,CAAC,CAAC,CAChBC,aAAa,CAAC,CAAC,CACfC,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,MAAM,IAAI,CAACnW,QAAQ,CAAC;MACvC;MACA,IAAI,CAAC0U,kBAAkB,CAAC,CAAC;MACzB;MACA,IAAI,CAAChD,WAAW,CAAC0E,MAAM,CAAC1O,SAAS,CAAC2O,eAAe,IAAI,IAAI,CAACN,gBAAgB,CAACM,eAAe,CAAC,CAAC;MAC5F;MACA,IAAI,CAACtE,MAAM,CAACvK,OAAO,CAAC6M,IAAI,CAACvS,SAAS,CAAC,IAAI,CAAC8P,UAAU,CAAC,CAAC,CAAClK,SAAS,CAAC,MAAM;QACjE,MAAMyN,UAAU,GAAG,IAAI,CAACzD,WAAW,CAACyD,UAAU;QAC9C,IAAI,CAACA,UAAU,IAAI,IAAI,CAACpD,MAAM,CAAC4D,OAAO,CAAC,CAAC,CAACW,OAAO,CAACnB,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;UACjE,IAAI,CAACT,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIqB,gBAAgBA,CAACQ,KAAK,EAAE;MACpB,IAAI,CAACxE,MAAM,CAACoB,OAAO,CAAC,CAAC1J,IAAI,EAAE+M,SAAS,KAAK/M,IAAI,CAACiG,YAAY,CAAC8G,SAAS,KAAKD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzF,IAAI,CAAC7E,WAAW,CAAC+E,gBAAgB,CAACF,KAAK,CAAC;IAC5C;IACA;AACJ;AACA;AACA;IACI7B,kBAAkBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAAC1U,QAAQ,EAAE;QACf,IAAI,CAAC+V,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACzB;MACJ;MACA,MAAMZ,UAAU,GAAG,IAAI,CAACpD,MAAM,CAAC8C,IAAI,CAACpL,IAAI,IAAIA,IAAI,CAACtJ,QAAQ,IAAI,CAACsJ,IAAI,CAACzJ,QAAQ,CAAC,IAAI,IAAI,CAAC+R,MAAM,CAAC7F,KAAK;MACjG,IAAI,CAAC6J,gBAAgB,CAACZ,UAAU,GAAG,IAAI,CAACpD,MAAM,CAAC4D,OAAO,CAAC,CAAC,CAACW,OAAO,CAACnB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IACtF;IACA;IACAV,cAAcA,CAAA,EAAG;MACb,MAAMiC,aAAa,GAAG/Y,iCAAiC,CAAC,CAAC;MACzD,OAAO+Y,aAAa,IAAI,IAAI,CAACnF,QAAQ,CAACjL,aAAa,CAACuP,QAAQ,CAACa,aAAa,CAAC;IAC/E;IACA,OAAOnU,IAAI,YAAAoU,yBAAAlU,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyO,gBAAgB;IAAA;IACnH,OAAOjH,IAAI,kBA9mC8E3N,EAAE,CAAA4N,iBAAA;MAAAtH,IAAA,EA8mCJsO,gBAAgB;MAAArO,SAAA;MAAAoG,cAAA,WAAA2N,gCAAAxX,EAAA,EAAAC,GAAA,EAAA8J,QAAA;QAAA,IAAA/J,EAAA;UA9mCd9C,EAAE,CAAA8M,cAAA,CAAAD,QAAA,EAknCrC2D,aAAa;QAAA;QAAA,IAAA1N,EAAA;UAAA,IAAAiK,EAAA;UAlnCsB/M,EAAE,CAAAgN,cAAA,CAAAD,EAAA,GAAF/M,EAAE,CAAAiN,WAAA,QAAAlK,GAAA,CAAA0S,MAAA,GAAA1I,EAAA;QAAA;MAAA;MAAAvG,SAAA,WA8mCmT,SAAS;MAAAY,QAAA;MAAAC,YAAA,WAAAkT,8BAAAzX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9mC9T9C,EAAE,CAAAkQ,UAAA,qBAAAsK,4CAAAC,MAAA;YAAA,OA8mCJ1X,GAAA,CAAA6V,cAAA,CAAA6B,MAAqB,CAAC;UAAA,CAAP,CAAC;QAAA;QAAA,IAAA3X,EAAA;UA9mCd9C,EAAE,CAAA2I,WAAA,yBAAA5F,GAAA,CAAAwO,QAAA;QAAA;MAAA;MAAA3I,MAAA;QAAAkI,KAAA;QAAAa,WAAA;QAAAJ,QAAA;QAAAkB,4BAAA;QAAA/O,QAAA;MAAA;MAAAoQ,OAAA;QAAA4B,eAAA;MAAA;MAAA7H,QAAA;MAAAlG,QAAA,GAAF3H,EAAE,CAAA8N,kBAAA,CA8mC+f,CACllB6G,iCAAiC,EACjC;QAAE5G,OAAO,EAAE9F,WAAW;QAAE+F,WAAW,EAAE4G;MAAiB,CAAC,EACvD;QAAE7G,OAAO,EAAEwC,cAAc;QAAEvC,WAAW,EAAE4G;MAAiB,CAAC,CAC7D,GAlnCoF5U,EAAE,CAAA4H,0BAAA,EAAF5H,EAAE,CAAA0a,oBAAA;MAAAzM,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAuM,0BAAA7X,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAsO,eAAA;UAAFtO,EAAE,CAAAgD,YAAA,EAknC6H,CAAC;QAAA;MAAA;MAAAuL,MAAA,GAAAjM,GAAA;MAAAkM,aAAA;MAAAC,eAAA;IAAA;EAC7N;EAAC,OAhVKmG,gBAAgB;AAAA;AAiVtB;EAAA,QAAAnO,SAAA,oBAAAA,SAAA;AAAA;AA2BoB,IAEdmU,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB,OAAO3U,IAAI,YAAA4U,sBAAA1U,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyU,aAAa;IAAA;IAChH,OAAOE,IAAI,kBAnpC8E9a,EAAE,CAAA+a,gBAAA;MAAAzU,IAAA,EAmpCSsU;IAAa;IA4BjH,OAAOI,IAAI,kBA/qC8Ehb,EAAE,CAAAib,gBAAA;MAAAC,OAAA,GA+qCkC/Y,eAAe,EACpIuD,eAAe,EACfC,eAAe,EACfC,uBAAuB,EAAExD,gBAAgB;IAAA;EACrD;EAAC,OAlCKwY,aAAa;AAAA;AAmCnB;EAAA,QAAAnU,SAAA,oBAAAA,SAAA;AAAA;AAuCA,SAASiI,QAAQ,EAAE1G,eAAe,EAAEsM,YAAY,EAAEK,iCAAiC,EAAEnH,aAAa,EAAEmB,OAAO,EAAEI,WAAW,EAAExH,iBAAiB,EAAEM,eAAe,EAAEnB,eAAe,EAAEE,eAAe,EAAEd,gBAAgB,EAAE8U,aAAa,EAAEpK,aAAa,EAAE4D,4BAA4B,EAAEG,UAAU,EAAEK,gBAAgB,EAAEE,sBAAsB,EAAEvE,cAAc,EAAEzJ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}