import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';

interface Country {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
  phoneCode: string;
  currency: string;
  isActive: boolean;
  createdAt: string;
}

@Component({
  selector: 'app-countries',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './countries.component.html',
  styleUrls: ['./countries.component.scss']
})
export class CountriesComponent implements OnInit {
  countries: Country[] = [];
  countryForm: FormGroup;
  isEditing = false;
  editingId: number | null = null;
  isLoading = false;
  
  private apiUrl = 'http://localhost:5233/api';

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {
    this.countryForm = this.createForm();
  }

  ngOnInit() {
    this.loadCountries();
  }

  createForm(): FormGroup {
    return this.fb.group({
      nameAr: ['', [Validators.required, Validators.maxLength(100)]],
      nameEn: ['', [Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.maxLength(10)]],
      phoneCode: ['', [Validators.required, Validators.maxLength(10)]],
      currency: ['', [Validators.required, Validators.maxLength(10)]]
    });
  }

  loadCountries() {
    this.isLoading = true;
    this.http.get<any>(`${this.apiUrl}/lookup/countries`).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = response.data;
        } else {
          alert('حدث خطأ في تحميل البلدان: ' + response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        alert('حدث خطأ في تحميل البلدان');
        this.isLoading = false;
      }
    });
  }

  onSubmit() {
    if (this.countryForm.valid) {
      const formData = this.countryForm.value;
      
      if (this.isEditing && this.editingId) {
        this.updateCountry(this.editingId, formData);
      } else {
        this.createCountry(formData);
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  createCountry(countryData: any) {
    this.http.post<any>(`${this.apiUrl}/lookup/countries`, countryData).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم إضافة البلد بنجاح!');
          this.loadCountries();
          this.resetForm();
        } else {
          alert('حدث خطأ في إضافة البلد: ' + response.message);
        }
      },
      error: (error) => {
        console.error('Error creating country:', error);
        alert('حدث خطأ في إضافة البلد');
      }
    });
  }

  updateCountry(id: number, countryData: any) {
    this.http.put<any>(`${this.apiUrl}/lookup/countries/${id}`, countryData).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم تحديث البلد بنجاح!');
          this.loadCountries();
          this.resetForm();
        } else {
          alert('حدث خطأ في تحديث البلد: ' + response.message);
        }
      },
      error: (error) => {
        console.error('Error updating country:', error);
        alert('حدث خطأ في تحديث البلد');
      }
    });
  }

  editCountry(country: Country) {
    this.isEditing = true;
    this.editingId = country.id;
    this.countryForm.patchValue({
      nameAr: country.nameAr,
      nameEn: country.nameEn,
      code: country.code,
      phoneCode: country.phoneCode,
      currency: country.currency
    });
  }

  deleteCountry(id: number) {
    if (confirm('هل أنت متأكد من حذف هذا البلد؟')) {
      this.http.delete<any>(`${this.apiUrl}/lookup/countries/${id}`).subscribe({
        next: (response) => {
          if (response.success) {
            alert('تم حذف البلد بنجاح!');
            this.loadCountries();
          } else {
            alert('حدث خطأ في حذف البلد: ' + response.message);
          }
        },
        error: (error) => {
          console.error('Error deleting country:', error);
          alert('حدث خطأ في حذف البلد');
        }
      });
    }
  }

  resetForm() {
    this.countryForm.reset();
    this.isEditing = false;
    this.editingId = null;
  }

  markFormGroupTouched() {
    Object.keys(this.countryForm.controls).forEach(key => {
      this.countryForm.get(key)?.markAsTouched();
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.countryForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldClasses(fieldName: string): string {
    const classes = ['form-control'];
    if (this.isFieldInvalid(fieldName)) {
      classes.push('is-invalid');
    }
    return classes.join(' ');
  }
}
