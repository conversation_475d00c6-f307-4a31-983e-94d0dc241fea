-- 🔄 دورة البرنامج الكاملة - مشتريات ومبيعات ومحاسبة
-- مع تتبع الحركات والقيود المحاسبية

-- 🏗️ إنشاء دليل الحسابات الأساسي
DELETE FROM ChartOfAccounts WHERE Id > 33;

INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
-- الحسابات الرئيسية
('1000', N'الأصول', 'Assets', 1, NULL, 1, 1, 0, 1, GETDATE()),
('2000', N'الخصوم', 'Liabilities', 2, NULL, 1, 1, 0, 1, GETDATE()),
('3000', N'حقوق الملكية', 'Equity', 3, NULL, 1, 1, 0, 1, GETDATE()),
('4000', N'الإيرادات', 'Revenue', 4, NULL, 1, 1, 0, 1, GETDATE()),
('5000', N'المصروفات', 'Expenses', 5, NULL, 1, 1, 0, 1, GETDATE()),

-- الحسابات الفرعية - الأصول
('1100', N'النقدية والبنوك', 'Cash and Banks', 1, 34, 2, 1, 0, 1, GETDATE()),
('1110', N'الصندوق الرئيسي', 'Main Cash', 1, 39, 3, 0, 1, 1, GETDATE()),
('1120', N'البنك الأهلي المصري', 'National Bank of Egypt', 1, 39, 3, 0, 1, 1, GETDATE()),

('1200', N'العملاء', 'Customers', 1, 34, 2, 1, 0, 1, GETDATE()),
('1210', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, 42, 3, 0, 1, 1, GETDATE()),
('1220', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, 42, 3, 0, 1, 1, GETDATE()),
('1230', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 1, 42, 3, 0, 1, 1, GETDATE()),

('1300', N'المخزون', 'Inventory', 1, 34, 2, 1, 0, 1, GETDATE()),
('1310', N'مخزون البضاعة', 'Merchandise Inventory', 1, 46, 3, 0, 1, 1, GETDATE()),

-- الحسابات الفرعية - الخصوم
('2100', N'الموردين', 'Suppliers', 2, 35, 2, 1, 0, 1, GETDATE()),
('2110', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 2, 48, 3, 0, 1, 1, GETDATE()),
('2120', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 2, 48, 3, 0, 1, 1, GETDATE()),
('2130', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, 48, 3, 0, 1, 1, GETDATE()),

-- حقوق الملكية
('3100', N'رأس المال', 'Capital', 3, 36, 2, 0, 1, 1, GETDATE()),

-- الإيرادات
('4100', N'مبيعات', 'Sales', 4, 37, 2, 0, 1, 1, GETDATE()),

-- المصروفات
('5100', N'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 5, 38, 2, 0, 1, 1, GETDATE()),
('5200', N'مصروفات عمومية', 'General Expenses', 5, 38, 2, 0, 1, 1, GETDATE());

-- 📦 إنشاء منتجات للدورة
INSERT INTO Products (ProductCode, NameAr, NameEn, Description, CategoryId, UnitId, BarcodeTypeId, Barcode, CostPrice, BasePrice, ProfitMargin, MinimumStock, MaximumStock, CurrentStock, IsActive, CreatedAt) VALUES
('PRD001', N'سرير خشب زان مقاس 180×200', 'Beech Wood Bed 180x200', N'سرير من خشب الزان الطبيعي مع كومودينو', 5, 1, 1, '1234567890123', 2500.00, 3500.00, 40.00, 5, 50, 0, 1, GETDATE()),
('PRD002', N'دولاب ملابس 6 أبواب', '6-Door Wardrobe', N'دولاب ملابس من الخشب المضغوط مع مرآة', 5, 1, 1, '1234567890124', 3000.00, 4200.00, 40.00, 3, 30, 0, 1, GETDATE()),
('PRD003', N'ثلاجة سامسونج 18 قدم', 'Samsung Refrigerator 18ft', N'ثلاجة سامسونج نوفروست 18 قدم مع فريزر', 9, 1, 1, '*************', 8000.00, 11200.00, 40.00, 5, 25, 0, 1, GETDATE()),
('PRD004', N'طقم أواني طبخ تيفال 12 قطعة', 'Tefal Cookware Set 12pcs', N'طقم أواني طبخ تيفال مع طلاء تيفلون', 10, 2, 1, '*************', 800.00, 1120.00, 40.00, 10, 50, 0, 1, GETDATE());

-- 👥 إنشاء عملاء للدورة
INSERT INTO Customers (CustomerCode, NameAr, NameEn, CustomerTypeId, Phone1, Email, Address, PriceCategoryId, AccountCode, ChartOfAccountId, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('CUS001', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, '***********', '<EMAIL>', N'شارع الجمهورية، وسط البلد، القاهرة', 1, '1210', 43, 0.00, 0.00, 1, GETDATE()),
('CUS002', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, '***********', '<EMAIL>', N'شارع الكورنيش، الإسكندرية', 1, '1220', 44, 0.00, 0.00, 1, GETDATE()),
('CUS003', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 4, '02-********', '<EMAIL>', N'شارع النيل، الجيزة', 2, '1230', 45, 0.00, 0.00, 1, GETDATE());

-- 🏭 إنشاء موردين للدورة
INSERT INTO Suppliers (SupplierCode, NameAr, NameEn, SupplierTypeId, Phone1, Email, Address, PaymentTerms, CreditLimit, AccountCode, ChartOfAccountId, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('SUP001', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 1, '02-********', '<EMAIL>', N'المنطقة الصناعية، القاهرة', 30, 100000.00, '2110', 49, 0.00, 0.00, 1, GETDATE()),
('SUP002', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 3, '03-5678901', '<EMAIL>', N'برج العرب، الإسكندرية', 45, 150000.00, '2120', 50, 0.00, 0.00, 1, GETDATE()),
('SUP003', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, '02-********', '<EMAIL>', N'مدينة نصر، القاهرة', 30, 80000.00, '2130', 51, 0.00, 0.00, 1, GETDATE());

-- 🛒 الدورة الأولى: مشتريات من الموردين
-- فاتورة شراء رقم 1 من شركة الأثاث المصري
INSERT INTO PurchaseInvoices (InvoiceNumber, SupplierId, InvoiceDate, TotalAmount, PaidAmount, RemainingAmount, Status, Notes, CreatedAt) VALUES
('PUR001', 2, GETDATE(), 15000.00, 5000.00, 10000.00, 2, N'فاتورة شراء أسرة وخزائن', GETDATE());

-- تفاصيل فاتورة الشراء
INSERT INTO PurchaseInvoiceDetails (PurchaseInvoiceId, ProductId, Quantity, UnitCost, TotalCost, Notes) VALUES
(1, 2, 3, 2500.00, 7500.00, N'3 أسرة خشب زان'),
(1, 3, 2, 3000.00, 6000.00, N'2 دولاب ملابس'),
(1, 4, 2, 750.00, 1500.00, N'2 ثلاجة سامسونج');

-- القيد المحاسبي للمشتريات
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE001', GETDATE(), N'قيد مشتريات من شركة الأثاث المصري - فاتورة PUR001', 15000.00, 15000.00, 2, 1, GETDATE());

-- تفاصيل القيد المحاسبي للمشتريات
INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(1, 47, 15000.00, 0.00, N'مخزون البضاعة'),
(1, 49, 0.00, 15000.00, N'شركة الأثاث المصري');

-- تحديث المخزون
UPDATE Products SET CurrentStock = CurrentStock + 3, CostPrice = 2500.00 WHERE Id = 2;
UPDATE Products SET CurrentStock = CurrentStock + 2, CostPrice = 3000.00 WHERE Id = 3;
UPDATE Products SET CurrentStock = CurrentStock + 2, CostPrice = 8000.00 WHERE Id = 4;

-- تحديث رصيد المورد
UPDATE Suppliers SET CurrentBalance = CurrentBalance + 15000.00 WHERE Id = 2;

-- 🛒 الدورة الثانية: مشتريات من مصنع الكهربائيات
INSERT INTO PurchaseInvoices (InvoiceNumber, SupplierId, InvoiceDate, TotalAmount, PaidAmount, RemainingAmount, Status, Notes, CreatedAt) VALUES
('PUR002', 3, GETDATE(), 20000.00, 20000.00, 0.00, 3, N'فاتورة شراء أجهزة كهربائية - مدفوعة نقداً', GETDATE());

INSERT INTO PurchaseInvoiceDetails (PurchaseInvoiceId, ProductId, Quantity, UnitCost, TotalCost, Notes) VALUES
(2, 4, 3, 8000.00, 24000.00, N'3 ثلاجة سامسونج إضافية'),
(2, 5, 10, 800.00, 8000.00, N'10 طقم أواني تيفال');

-- القيد المحاسبي للمشتريات النقدية
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE002', GETDATE(), N'قيد مشتريات نقدية من مصنع الكهربائيات - فاتورة PUR002', 32000.00, 32000.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(2, 47, 32000.00, 0.00, N'مخزون البضاعة'),
(2, 40, 0.00, 32000.00, N'الصندوق الرئيسي');

-- تحديث المخزون
UPDATE Products SET CurrentStock = CurrentStock + 3 WHERE Id = 4;
UPDATE Products SET CurrentStock = CurrentStock + 10, CostPrice = 800.00 WHERE Id = 5;

-- 💰 الدورة الثالثة: مبيعات للعملاء
-- فاتورة بيع رقم 1 لأحمد محمد علي
INSERT INTO SalesInvoices (InvoiceNumber, CustomerId, InvoiceDate, TotalAmount, PaidAmount, RemainingAmount, Status, Notes, CreatedAt) VALUES
('SAL001', 2, GETDATE(), 8400.00, 3000.00, 5400.00, 2, N'فاتورة بيع سرير ودولاب', GETDATE());

-- تفاصيل فاتورة البيع
INSERT INTO SalesInvoiceDetails (SalesInvoiceId, ProductId, Quantity, UnitPrice, TotalPrice, Notes) VALUES
(1, 2, 1, 3500.00, 3500.00, N'سرير خشب زان'),
(1, 3, 1, 4200.00, 4200.00, N'دولاب ملابس'),
(1, 5, 1, 1120.00, 1120.00, N'طقم أواني تيفال');

-- القيد المحاسبي للمبيعات الآجلة
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE003', GETDATE(), N'قيد مبيعات آجلة لأحمد محمد علي - فاتورة SAL001', 8820.00, 8820.00, 2, 1, GETDATE());

-- تفاصيل قيد المبيعات
INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(3, 43, 8820.00, 0.00, N'أحمد محمد علي'),
(3, 53, 0.00, 8820.00, N'مبيعات');

-- قيد تكلفة البضاعة المباعة
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE004', GETDATE(), N'قيد تكلفة البضاعة المباعة - فاتورة SAL001', 6300.00, 6300.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(4, 54, 6300.00, 0.00, N'تكلفة البضاعة المباعة'),
(4, 47, 0.00, 6300.00, N'مخزون البضاعة');

-- تحديث المخزون (خصم الكميات المباعة)
UPDATE Products SET CurrentStock = CurrentStock - 1 WHERE Id = 2;
UPDATE Products SET CurrentStock = CurrentStock - 1 WHERE Id = 3;
UPDATE Products SET CurrentStock = CurrentStock - 1 WHERE Id = 5;

-- تحديث رصيد العميل
UPDATE Customers SET CurrentBalance = CurrentBalance + 8820.00 WHERE Id = 2;

-- 💰 الدورة الرابعة: مبيعات نقدية لفاطمة أحمد حسن
INSERT INTO SalesInvoices (InvoiceNumber, CustomerId, InvoiceDate, TotalAmount, PaidAmount, RemainingAmount, Status, Notes, CreatedAt) VALUES
('SAL002', 3, GETDATE(), 11200.00, 11200.00, 0.00, 3, N'فاتورة بيع ثلاجة - نقدي', GETDATE());

INSERT INTO SalesInvoiceDetails (SalesInvoiceId, ProductId, Quantity, UnitPrice, TotalPrice, Notes) VALUES
(2, 4, 1, 11200.00, 11200.00, N'ثلاجة سامسونج 18 قدم');

-- القيد المحاسبي للمبيعات النقدية
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE005', GETDATE(), N'قيد مبيعات نقدية لفاطمة أحمد حسن - فاتورة SAL002', 11200.00, 11200.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(5, 40, 11200.00, 0.00, N'الصندوق الرئيسي'),
(5, 53, 0.00, 11200.00, N'مبيعات');

-- قيد تكلفة البضاعة المباعة
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE006', GETDATE(), N'قيد تكلفة البضاعة المباعة - فاتورة SAL002', 8000.00, 8000.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(6, 54, 8000.00, 0.00, N'تكلفة البضاعة المباعة'),
(6, 47, 0.00, 8000.00, N'مخزون البضاعة');

-- تحديث المخزون
UPDATE Products SET CurrentStock = CurrentStock - 1 WHERE Id = 4;

-- 💵 الدورة الخامسة: مقبوضات من العملاء
-- سند قبض من أحمد محمد علي
INSERT INTO CashReceipts (ReceiptNumber, CustomerId, Amount, ReceiptDate, PaymentMethodId, Notes, CreatedAt) VALUES
('REC001', 2, 3000.00, GETDATE(), 1, N'دفعة على حساب فاتورة SAL001', GETDATE());

-- القيد المحاسبي لسند القبض
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE007', GETDATE(), N'قيد سند قبض من أحمد محمد علي - سند REC001', 3000.00, 3000.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(7, 40, 3000.00, 0.00, N'الصندوق الرئيسي'),
(7, 43, 0.00, 3000.00, N'أحمد محمد علي');

-- تحديث رصيد العميل
UPDATE Customers SET CurrentBalance = CurrentBalance - 3000.00 WHERE Id = 2;

-- 💸 الدورة السادسة: مدفوعات للموردين
-- سند دفع لشركة الأثاث المصري
INSERT INTO CashPayments (PaymentNumber, SupplierId, Amount, PaymentDate, PaymentMethodId, Notes, CreatedAt) VALUES
('PAY001', 2, 5000.00, GETDATE(), 1, N'دفعة على حساب فاتورة PUR001', GETDATE());

-- القيد المحاسبي لسند الدفع
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalDebit, TotalCredit, Status, CreatedBy, CreatedAt) VALUES
('JE008', GETDATE(), N'قيد سند دفع لشركة الأثاث المصري - سند PAY001', 5000.00, 5000.00, 2, 1, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, Description) VALUES
(8, 49, 5000.00, 0.00, N'شركة الأثاث المصري'),
(8, 40, 0.00, 5000.00, N'الصندوق الرئيسي');

-- تحديث رصيد المورد
UPDATE Suppliers SET CurrentBalance = CurrentBalance - 5000.00 WHERE Id = 2;

-- 📊 تحديث العدادات
UPDATE Counters SET CurrentValue = 3 WHERE CounterName = 'Customer';
UPDATE Counters SET CurrentValue = 3 WHERE CounterName = 'Supplier';

PRINT N'تم إنشاء الدورة الكاملة للبرنامج ✅';
PRINT N'🔄 الدورة تشمل: مشتريات، مبيعات، مقبوضات، مدفوعات، قيود محاسبية، تتبع المخزون';
PRINT N'📈 جميع الحركات مترابطة ومتوازنة محاسبياً';
