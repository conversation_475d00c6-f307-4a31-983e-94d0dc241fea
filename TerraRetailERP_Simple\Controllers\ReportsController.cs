using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("📊 Reports & Analytics")]
    public class ReportsController : ControllerBase
    {
        private readonly AppDbContext _context;

        public ReportsController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetDashboard()
        {
            try
            {
                var today = DateTime.Today;
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var thisYear = new DateTime(today.Year, 1, 1);

                // Sales Summary
                var salesSummary = new
                {
                    Today = await _context.Sales
                        .Where(s => s.InvoiceDate.Date == today && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m },

                    ThisMonth = await _context.Sales
                        .Where(s => s.InvoiceDate >= thisMonth && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m },

                    ThisYear = await _context.Sales
                        .Where(s => s.InvoiceDate >= thisYear && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m }
                };

                // Customer & Supplier Summary
                var customerSummary = new
                {
                    TotalCustomers = await _context.Customers.Where(c => c.IsActive).CountAsync(),
                    TotalReceivables = await _context.Customers.SumAsync(c => c.CurrentBalance)
                };

                var supplierSummary = new
                {
                    TotalSuppliers = await _context.Suppliers.Where(s => s.IsActive).CountAsync(),
                    TotalPayables = await _context.Suppliers.SumAsync(s => s.CurrentBalance)
                };

                // Product Summary
                var productSummary = new
                {
                    TotalProducts = await _context.Products.Where(p => p.IsActive).CountAsync(),
                    TotalCategories = await _context.Categories.Where(c => c.IsActive).CountAsync()
                };

                return Ok(new
                {
                    success = true,
                    message = "تم جلب لوحة التحكم بنجاح",
                    data = new
                    {
                        sales = salesSummary,
                        customers = customerSummary,
                        suppliers = supplierSummary,
                        products = productSummary,
                        generatedAt = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("sales-chart")]
        public async Task<ActionResult> GetSalesChart(
            [FromQuery] string period = "month", // day, week, month, year
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var endDate = toDate ?? DateTime.Today;
                var startDate = fromDate ?? period switch
                {
                    "day" => endDate.AddDays(-30),
                    "week" => endDate.AddDays(-7 * 12), // 12 weeks
                    "month" => endDate.AddMonths(-12),
                    "year" => endDate.AddYears(-5),
                    _ => endDate.AddMonths(-12)
                };

                var query = _context.Sales
                    .Where(s => s.InvoiceDate >= startDate && s.InvoiceDate <= endDate && s.Status == 1);

                var data = period switch
                {
                    "day" => await query
                        .GroupBy(s => s.InvoiceDate.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .OrderBy(x => x.Date)
                        .ToListAsync(),

                    "month" => await query
                        .GroupBy(s => new { s.InvoiceDate.Year, s.InvoiceDate.Month })
                        .Select(g => new { 
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Count = g.Count(), 
                            Total = g.Sum(s => s.TotalAmount) 
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync(),

                    "year" => await query
                        .GroupBy(s => s.InvoiceDate.Year)
                        .Select(g => new { 
                            Date = new DateTime(g.Key, 1, 1),
                            Count = g.Count(), 
                            Total = g.Sum(s => s.TotalAmount) 
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync(),

                    _ => await query
                        .GroupBy(s => new { s.InvoiceDate.Year, s.InvoiceDate.Month })
                        .Select(g => new { 
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Count = g.Count(), 
                            Total = g.Sum(s => s.TotalAmount) 
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync()
                };

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات الرسم البياني بنجاح",
                    data = data
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("top-products")]
        public async Task<ActionResult> GetTopProducts(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int limit = 10,
            [FromQuery] string sortBy = "quantity") // quantity, amount
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate >= startDate && 
                               si.Sale.InvoiceDate <= endDate && 
                               si.Sale.Status == 1);

                var products = await query
                    .GroupBy(si => new { si.ProductId, si.Product.NameAr, si.Product.ProductCode })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.NameAr,
                        ProductCode = g.Key.ProductCode,
                        TotalQuantity = g.Sum(si => si.Quantity),
                        TotalAmount = g.Sum(si => si.FinalTotal),
                        TransactionCount = g.Count(),
                        AveragePrice = g.Average(si => si.UnitPrice)
                    })
                    .ToListAsync();

                var sortedProducts = sortBy.ToLower() switch
                {
                    "amount" => products.OrderByDescending(p => p.TotalAmount),
                    "transactions" => products.OrderByDescending(p => p.TransactionCount),
                    _ => products.OrderByDescending(p => p.TotalQuantity)
                };

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أفضل المنتجات مبيعاً بنجاح",
                    data = sortedProducts.Take(limit)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("financial-summary")]
        public async Task<ActionResult> GetFinancialSummary(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today;

                // Sales Revenue
                var salesRevenue = await _context.Sales
                    .Where(s => s.InvoiceDate >= startDate && s.InvoiceDate <= endDate && s.Status == 1)
                    .SumAsync(s => s.TotalAmount);

                var salesCost = await _context.SaleItems
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate >= startDate && 
                               si.Sale.InvoiceDate <= endDate && 
                               si.Sale.Status == 1)
                    .SumAsync(si => si.Quantity * si.UnitCostPrice);

                // Receivables & Payables
                var receivables = await _context.Customers.SumAsync(c => c.CurrentBalance);
                var payables = await _context.Suppliers.SumAsync(s => s.CurrentBalance);

                return Ok(new
                {
                    success = true,
                    message = "تم جلب الملخص المالي بنجاح",
                    data = new
                    {
                        revenue = new
                        {
                            sales = salesRevenue,
                            cost = salesCost,
                            grossProfit = salesRevenue - salesCost,
                            grossProfitMargin = salesRevenue > 0 ? ((salesRevenue - salesCost) / salesRevenue) * 100 : 0
                        },
                        balances = new
                        {
                            receivables,
                            payables,
                            netPosition = receivables - payables
                        },
                        period = new
                        {
                            fromDate = startDate,
                            toDate = endDate
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("customer-analysis")]
        public async Task<ActionResult> GetCustomerAnalysis()
        {
            try
            {
                var customerData = await _context.Customers
                    .Where(c => c.IsActive)
                    .Select(c => new
                    {
                        c.Id,
                        c.CustomerCode,
                        c.NameAr,
                        c.CurrentBalance,
                        SalesCount = _context.Sales.Count(s => s.CustomerId == c.Id && s.Status == 1),
                        TotalSales = _context.Sales
                            .Where(s => s.CustomerId == c.Id && s.Status == 1)
                            .Sum(s => s.TotalAmount),
                        LastSaleDate = _context.Sales
                            .Where(s => s.CustomerId == c.Id && s.Status == 1)
                            .Max(s => (DateTime?)s.InvoiceDate)
                    })
                    .OrderByDescending(c => c.TotalSales)
                    .Take(20)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب تحليل العملاء بنجاح",
                    data = customerData
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("supplier-analysis")]
        public async Task<ActionResult> GetSupplierAnalysis()
        {
            try
            {
                var supplierData = await _context.Suppliers
                    .Where(s => s.IsActive)
                    .Select(s => new
                    {
                        s.Id,
                        s.SupplierCode,
                        s.NameAr,
                        s.CurrentBalance,
                        s.CreditLimit,
                        AvailableCredit = s.CreditLimit - s.CurrentBalance,
                        s.PaymentTerms,
                        s.ContactPerson,
                        s.Phone1,
                        s.Email
                    })
                    .OrderByDescending(s => s.CurrentBalance)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب تحليل الموردين بنجاح",
                    data = supplierData
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }
}
