-- إدخال أنواع المعاملات الكاملة
-- Complete Transaction Types

-- حذف البيانات الموجودة
DELETE FROM TransactionTypes;
DBCC CHECKIDENT ('TransactionTypes', RESEED, 0);

-- إدخال أنواع المعاملات
INSERT INTO TransactionTypes (Id, NameAr, NameEn, Code, Category, Description, IsActive, SortOrder, Icon, Color, RequiresApproval, AllowReversal, CreatedAt) VALUES

-- مبيعات (Sales) - 1-10
(1, 'مبيعات', 'Sale', 'SALE', 'مبيعات', 'فاتورة مبيعات عادية', 1, 1, 'shopping-cart', '#28a745', 0, 1, GETDATE()),
(2, 'مرتجع مبيعات', 'Sale Return', 'SALE_RETURN', 'مبيعات', 'مرتجع فاتورة مبيعات', 1, 2, 'undo', '#dc3545', 1, 0, GETDATE()),
(3, 'خصم مبيعات', 'Sale Discount', 'SALE_DISCOUNT', 'مبيعات', 'خصم على فاتورة مبيعات', 1, 3, 'percent', '#ffc107', 1, 1, GETDATE()),
(4, 'دفعة من عميل', 'Sale Payment', 'SALE_PAYMENT', 'مبيعات', 'تحصيل من عميل', 1, 4, 'money-bill', '#17a2b8', 0, 1, GETDATE()),

-- مشتريات (Purchases) - 11-20
(11, 'مشتريات', 'Purchase', 'PURCHASE', 'مشتريات', 'فاتورة مشتريات عادية', 1, 11, 'shopping-bag', '#6f42c1', 0, 1, GETDATE()),
(12, 'مرتجع مشتريات', 'Purchase Return', 'PURCHASE_RETURN', 'مشتريات', 'مرتجع فاتورة مشتريات', 1, 12, 'undo', '#e83e8c', 1, 0, GETDATE()),
(13, 'خصم مشتريات', 'Purchase Discount', 'PURCHASE_DISCOUNT', 'مشتريات', 'خصم على فاتورة مشتريات', 1, 13, 'percent', '#fd7e14', 1, 1, GETDATE()),
(14, 'دفعة لمورد', 'Purchase Payment', 'PURCHASE_PAYMENT', 'مشتريات', 'دفع لمورد', 1, 14, 'credit-card', '#20c997', 0, 1, GETDATE()),

-- نقدية وبنوك (Cash & Banks) - 21-30
(21, 'سند قبض نقدي', 'Cash Receipt', 'CASH_RECEIPT', 'نقدية وبنوك', 'سند قبض نقدي', 1, 21, 'hand-holding-usd', '#28a745', 0, 1, GETDATE()),
(22, 'سند دفع نقدي', 'Cash Payment', 'CASH_PAYMENT', 'نقدية وبنوك', 'سند دفع نقدي', 1, 22, 'money-bill-wave', '#dc3545', 0, 1, GETDATE()),
(23, 'إيداع بنكي', 'Bank Deposit', 'BANK_DEPOSIT', 'نقدية وبنوك', 'إيداع في البنك', 1, 23, 'university', '#007bff', 0, 1, GETDATE()),
(24, 'سحب بنكي', 'Bank Withdrawal', 'BANK_WITHDRAWAL', 'نقدية وبنوك', 'سحب من البنك', 1, 24, 'university', '#6c757d', 0, 1, GETDATE()),
(25, 'تحويل بنكي', 'Bank Transfer', 'BANK_TRANSFER', 'نقدية وبنوك', 'تحويل بين البنوك', 1, 25, 'exchange-alt', '#17a2b8', 0, 1, GETDATE()),
(26, 'تحويل نقدي', 'Cash Transfer', 'CASH_TRANSFER', 'نقدية وبنوك', 'تحويل بين الصناديق', 1, 26, 'arrows-alt-h', '#ffc107', 0, 1, GETDATE()),

-- مخزون (Inventory) - 31-40
(31, 'تسوية مخزون', 'Stock Adjustment', 'STOCK_ADJUSTMENT', 'مخزون', 'تسوية كميات المخزون', 1, 31, 'boxes', '#6f42c1', 1, 1, GETDATE()),
(32, 'تحويل مخزون', 'Stock Transfer', 'STOCK_TRANSFER', 'مخزون', 'تحويل بين المخازن', 1, 32, 'truck', '#fd7e14', 0, 1, GETDATE()),
(33, 'تلف مخزون', 'Stock Damage', 'STOCK_DAMAGE', 'مخزون', 'إعدام مخزون تالف', 1, 33, 'exclamation-triangle', '#dc3545', 1, 0, GETDATE()),
(34, 'جرد مخزون', 'Stock Count', 'STOCK_COUNT', 'مخزون', 'عملية جرد المخزون', 1, 34, 'clipboard-list', '#28a745', 1, 0, GETDATE()),
(35, 'إنتاج داخل', 'Production In', 'PRODUCTION_IN', 'مخزون', 'إضافة إنتاج للمخزون', 1, 35, 'cogs', '#20c997', 0, 1, GETDATE()),
(36, 'إنتاج خارج', 'Production Out', 'PRODUCTION_OUT', 'مخزون', 'استهلاك مواد للإنتاج', 1, 36, 'cogs', '#e83e8c', 0, 1, GETDATE()),

-- موظفين (Employees) - 41-50
(41, 'دفع راتب', 'Salary Payment', 'SALARY_PAYMENT', 'موظفين', 'دفع راتب موظف', 1, 41, 'user-tie', '#007bff', 0, 1, GETDATE()),
(42, 'سلفة راتب', 'Salary Advance', 'SALARY_ADVANCE', 'موظفين', 'سلفة على الراتب', 1, 42, 'hand-holding-usd', '#ffc107', 1, 1, GETDATE()),
(43, 'مكافأة', 'Bonus', 'BONUS', 'موظفين', 'مكافأة للموظف', 1, 43, 'gift', '#28a745', 1, 1, GETDATE()),
(44, 'خصم', 'Deduction', 'DEDUCTION', 'موظفين', 'خصم من راتب الموظف', 1, 44, 'minus-circle', '#dc3545', 1, 1, GETDATE()),
(45, 'عمولة', 'Commission', 'COMMISSION', 'موظفين', 'عمولة للموظف', 1, 45, 'percentage', '#17a2b8', 0, 1, GETDATE()),
(46, 'إضافي', 'Overtime', 'OVERTIME', 'موظفين', 'بدل عمل إضافي', 1, 46, 'clock', '#6f42c1', 0, 1, GETDATE()),

-- مصروفات (Expenses) - 51-60
(51, 'مصروف تشغيلي', 'Operating Expense', 'OPERATING_EXPENSE', 'مصروفات', 'مصروف تشغيل عام', 1, 51, 'receipt', '#6c757d', 0, 1, GETDATE()),
(52, 'مصروف خدمات', 'Utility Expense', 'UTILITY_EXPENSE', 'مصروفات', 'فواتير الخدمات', 1, 52, 'bolt', '#ffc107', 0, 1, GETDATE()),
(53, 'مصروف إيجار', 'Rent Expense', 'RENT_EXPENSE', 'مصروفات', 'دفع إيجار', 1, 53, 'home', '#fd7e14', 0, 1, GETDATE()),
(54, 'مصروف صيانة', 'Maintenance Expense', 'MAINTENANCE_EXPENSE', 'مصروفات', 'مصروفات الصيانة', 1, 54, 'tools', '#20c997', 0, 1, GETDATE()),
(55, 'مصروف تسويق', 'Marketing Expense', 'MARKETING_EXPENSE', 'مصروفات', 'مصروفات التسويق', 1, 55, 'bullhorn', '#e83e8c', 0, 1, GETDATE()),
(56, 'مصروف سفر', 'Travel Expense', 'TRAVEL_EXPENSE', 'مصروفات', 'مصروفات السفر', 1, 56, 'plane', '#6f42c1', 0, 1, GETDATE()),

-- ضرائب (Taxes) - 61-70
(61, 'دفع ضريبة', 'Tax Payment', 'TAX_PAYMENT', 'ضرائب', 'دفع ضريبة للحكومة', 1, 61, 'file-invoice-dollar', '#dc3545', 1, 1, GETDATE()),
(62, 'استرداد ضريبة', 'Tax Refund', 'TAX_REFUND', 'ضرائب', 'استرداد ضريبة من الحكومة', 1, 62, 'undo-alt', '#28a745', 1, 1, GETDATE()),
(63, 'تحصيل ضريبة قيمة مضافة', 'VAT Collection', 'VAT_COLLECTION', 'ضرائب', 'تحصيل ض.ق.م من العملاء', 1, 63, 'percent', '#007bff', 0, 1, GETDATE()),
(64, 'دفع ضريبة قيمة مضافة', 'VAT Payment', 'VAT_PAYMENT', 'ضرائب', 'دفع ض.ق.م للحكومة', 1, 64, 'percent', '#ffc107', 1, 1, GETDATE()),

-- أصول (Assets) - 71-80
(71, 'شراء أصل', 'Asset Purchase', 'ASSET_PURCHASE', 'أصول', 'شراء أصل ثابت', 1, 71, 'building', '#6f42c1', 1, 1, GETDATE()),
(72, 'بيع أصل', 'Asset Sale', 'ASSET_SALE', 'أصول', 'بيع أصل ثابت', 1, 72, 'handshake', '#28a745', 1, 1, GETDATE()),
(73, 'استهلاك أصل', 'Asset Depreciation', 'ASSET_DEPRECIATION', 'أصول', 'إهلاك الأصول الثابتة', 1, 73, 'chart-line', '#6c757d', 0, 0, GETDATE()),
(74, 'صيانة أصل', 'Asset Maintenance', 'ASSET_MAINTENANCE', 'أصول', 'صيانة الأصول الثابتة', 1, 74, 'wrench', '#fd7e14', 0, 1, GETDATE()),

-- قروض (Loans) - 81-90
(81, 'قرض مستلم', 'Loan Received', 'LOAN_RECEIVED', 'قروض', 'استلام قرض', 1, 81, 'hand-holding-usd', '#17a2b8', 1, 1, GETDATE()),
(82, 'دفعة قرض', 'Loan Payment', 'LOAN_PAYMENT', 'قروض', 'سداد قسط قرض', 1, 82, 'credit-card', '#dc3545', 0, 1, GETDATE()),
(83, 'فوائد قرض', 'Loan Interest', 'LOAN_INTEREST', 'قروض', 'فوائد على القروض', 1, 83, 'percentage', '#ffc107', 0, 1, GETDATE()),
(84, 'قرض معطى', 'Loan Given', 'LOAN_GIVEN', 'قروض', 'إعطاء قرض للغير', 1, 84, 'handshake', '#20c997', 1, 1, GETDATE()),

-- استثمارات (Investments) - 91-100
(91, 'استثمار', 'Investment', 'INVESTMENT', 'استثمارات', 'استثمار في أوراق مالية', 1, 91, 'chart-pie', '#6f42c1', 1, 1, GETDATE()),
(92, 'عائد استثمار', 'Investment Return', 'INVESTMENT_RETURN', 'استثمارات', 'عائد من الاستثمارات', 1, 92, 'coins', '#28a745', 0, 1, GETDATE()),
(93, 'بيع استثمار', 'Investment Sale', 'INVESTMENT_SALE', 'استثمارات', 'بيع استثمارات', 1, 93, 'chart-line', '#fd7e14', 1, 1, GETDATE()),

-- تسويات (Adjustments) - 101-110
(101, 'تسوية عامة', 'General Adjustment', 'GENERAL_ADJUSTMENT', 'تسويات', 'تسوية محاسبية عامة', 1, 101, 'balance-scale', '#6c757d', 1, 1, GETDATE()),
(102, 'تسوية عملة', 'Currency Adjustment', 'CURRENCY_ADJUSTMENT', 'تسويات', 'تسوية فروق العملة', 1, 102, 'dollar-sign', '#17a2b8', 1, 1, GETDATE()),
(103, 'تسوية نهاية السنة', 'Year End Adjustment', 'YEAR_END_ADJUSTMENT', 'تسويات', 'تسويات نهاية السنة المالية', 1, 103, 'calendar-alt', '#e83e8c', 1, 0, GETDATE()),
(104, 'تصحيح خطأ', 'Error Correction', 'ERROR_CORRECTION', 'تسويات', 'تصحيح خطأ محاسبي', 1, 104, 'exclamation-circle', '#dc3545', 1, 1, GETDATE()),
(105, 'إعدام', 'Write Off', 'WRITE_OFF', 'تسويات', 'إعدام رصيد', 1, 105, 'times-circle', '#6c757d', 1, 0, GETDATE()),

-- أخرى (Others) - 111-120
(111, 'رصيد افتتاحي', 'Opening', 'OPENING', 'أخرى', 'أرصدة افتتاحية', 1, 111, 'play-circle', '#28a745', 1, 0, GETDATE()),
(112, 'رصيد ختامي', 'Closing', 'CLOSING', 'أخرى', 'أرصدة ختامية', 1, 112, 'stop-circle', '#dc3545', 1, 0, GETDATE()),
(113, 'تحويل', 'Transfer', 'TRANSFER', 'أخرى', 'تحويل عام', 1, 113, 'exchange-alt', '#ffc107', 0, 1, GETDATE()),
(114, 'عكس قيد', 'Reversal', 'REVERSAL', 'أخرى', 'عكس قيد محاسبي', 1, 114, 'undo', '#fd7e14', 1, 0, GETDATE()),
(115, 'إلغاء', 'Cancellation', 'CANCELLATION', 'أخرى', 'إلغاء معاملة', 1, 115, 'ban', '#6c757d', 1, 0, GETDATE());

-- مبيعات (Sales) - 1-10
(1, 'مبيعات', 'Sale', 'SALE', 'مبيعات', 'فاتورة مبيعات عادية', 1, 1, 'shopping-cart', '#28a745', 0, 1, GETDATE()),
(2, 'مرتجع مبيعات', 'Sale Return', 'SALE_RETURN', 'مبيعات', 'مرتجع فاتورة مبيعات', 1, 2, 'undo', '#dc3545', 1, 0, GETDATE()),
(3, 'خصم مبيعات', 'Sale Discount', 'SALE_DISCOUNT', 'مبيعات', 'خصم على فاتورة مبيعات', 1, 3, 'percent', '#ffc107', 1, 1, GETDATE()),
(4, 'دفعة من عميل', 'Sale Payment', 'SALE_PAYMENT', 'مبيعات', 'تحصيل من عميل', 1, 4, 'money-bill', '#17a2b8', 0, 1, GETDATE()),

-- مشتريات (Purchases) - 11-20
(11, 'مشتريات', 'Purchase', 'PURCHASE', 'مشتريات', 'فاتورة مشتريات عادية', 1, 11, 'shopping-bag', '#6f42c1', 0, 1, GETDATE()),
(12, 'مرتجع مشتريات', 'Purchase Return', 'PURCHASE_RETURN', 'مشتريات', 'مرتجع فاتورة مشتريات', 1, 12, 'undo', '#e83e8c', 1, 0, GETDATE()),
(13, 'خصم مشتريات', 'Purchase Discount', 'PURCHASE_DISCOUNT', 'مشتريات', 'خصم على فاتورة مشتريات', 1, 13, 'percent', '#fd7e14', 1, 1, GETDATE()),
(14, 'دفعة لمورد', 'Purchase Payment', 'PURCHASE_PAYMENT', 'مشتريات', 'دفع لمورد', 1, 14, 'credit-card', '#20c997', 0, 1, GETDATE()),

-- نقدية وبنوك (Cash & Banks) - 21-30
(21, 'سند قبض نقدي', 'Cash Receipt', 'CASH_RECEIPT', 'نقدية وبنوك', 'سند قبض نقدي', 1, 21, 'hand-holding-usd', '#28a745', 0, 1, GETDATE()),
(22, 'سند دفع نقدي', 'Cash Payment', 'CASH_PAYMENT', 'نقدية وبنوك', 'سند دفع نقدي', 1, 22, 'money-bill-wave', '#dc3545', 0, 1, GETDATE()),
(23, 'إيداع بنكي', 'Bank Deposit', 'BANK_DEPOSIT', 'نقدية وبنوك', 'إيداع في البنك', 1, 23, 'university', '#007bff', 0, 1, GETDATE()),
(24, 'سحب بنكي', 'Bank Withdrawal', 'BANK_WITHDRAWAL', 'نقدية وبنوك', 'سحب من البنك', 1, 24, 'university', '#6c757d', 0, 1, GETDATE()),
(25, 'تحويل بنكي', 'Bank Transfer', 'BANK_TRANSFER', 'نقدية وبنوك', 'تحويل بين البنوك', 1, 25, 'exchange-alt', '#17a2b8', 0, 1, GETDATE()),
(26, 'تحويل نقدي', 'Cash Transfer', 'CASH_TRANSFER', 'نقدية وبنوك', 'تحويل بين الصناديق', 1, 26, 'arrows-alt-h', '#ffc107', 0, 1, GETDATE()),

-- مخزون (Inventory) - 31-40
(31, 'تسوية مخزون', 'Stock Adjustment', 'STOCK_ADJUSTMENT', 'مخزون', 'تسوية كميات المخزون', 1, 31, 'boxes', '#6f42c1', 1, 1, GETDATE()),
(32, 'تحويل مخزون', 'Stock Transfer', 'STOCK_TRANSFER', 'مخزون', 'تحويل بين المخازن', 1, 32, 'truck', '#fd7e14', 0, 1, GETDATE()),
(33, 'تلف مخزون', 'Stock Damage', 'STOCK_DAMAGE', 'مخزون', 'إعدام مخزون تالف', 1, 33, 'exclamation-triangle', '#dc3545', 1, 0, GETDATE()),
(34, 'جرد مخزون', 'Stock Count', 'STOCK_COUNT', 'مخزون', 'عملية جرد المخزون', 1, 34, 'clipboard-list', '#28a745', 1, 0, GETDATE()),
(35, 'إنتاج داخل', 'Production In', 'PRODUCTION_IN', 'مخزون', 'إضافة إنتاج للمخزون', 1, 35, 'cogs', '#20c997', 0, 1, GETDATE()),
(36, 'إنتاج خارج', 'Production Out', 'PRODUCTION_OUT', 'مخزون', 'استهلاك مواد للإنتاج', 1, 36, 'cogs', '#e83e8c', 0, 1, GETDATE()),

-- موظفين (Employees) - 41-50
(41, 'دفع راتب', 'Salary Payment', 'SALARY_PAYMENT', 'موظفين', 'دفع راتب موظف', 1, 41, 'user-tie', '#007bff', 0, 1, GETDATE()),
(42, 'سلفة راتب', 'Salary Advance', 'SALARY_ADVANCE', 'موظفين', 'سلفة على الراتب', 1, 42, 'hand-holding-usd', '#ffc107', 1, 1, GETDATE()),
(43, 'مكافأة', 'Bonus', 'BONUS', 'موظفين', 'مكافأة للموظف', 1, 43, 'gift', '#28a745', 1, 1, GETDATE()),
(44, 'خصم', 'Deduction', 'DEDUCTION', 'موظفين', 'خصم من راتب الموظف', 1, 44, 'minus-circle', '#dc3545', 1, 1, GETDATE()),
(45, 'عمولة', 'Commission', 'COMMISSION', 'موظفين', 'عمولة للموظف', 1, 45, 'percentage', '#17a2b8', 0, 1, GETDATE()),
(46, 'إضافي', 'Overtime', 'OVERTIME', 'موظفين', 'بدل عمل إضافي', 1, 46, 'clock', '#6f42c1', 0, 1, GETDATE()),

-- مصروفات (Expenses) - 51-60
(51, 'مصروف تشغيلي', 'Operating Expense', 'OPERATING_EXPENSE', 'مصروفات', 'مصروف تشغيل عام', 1, 51, 'receipt', '#6c757d', 0, 1, GETDATE()),
(52, 'مصروف خدمات', 'Utility Expense', 'UTILITY_EXPENSE', 'مصروفات', 'فواتير الخدمات', 1, 52, 'bolt', '#ffc107', 0, 1, GETDATE()),
(53, 'مصروف إيجار', 'Rent Expense', 'RENT_EXPENSE', 'مصروفات', 'دفع إيجار', 1, 53, 'home', '#fd7e14', 0, 1, GETDATE()),
(54, 'مصروف صيانة', 'Maintenance Expense', 'MAINTENANCE_EXPENSE', 'مصروفات', 'مصروفات الصيانة', 1, 54, 'tools', '#20c997', 0, 1, GETDATE()),
(55, 'مصروف تسويق', 'Marketing Expense', 'MARKETING_EXPENSE', 'مصروفات', 'مصروفات التسويق', 1, 55, 'bullhorn', '#e83e8c', 0, 1, GETDATE()),
(56, 'مصروف سفر', 'Travel Expense', 'TRAVEL_EXPENSE', 'مصروفات', 'مصروفات السفر', 1, 56, 'plane', '#6f42c1', 0, 1, GETDATE()),

-- ضرائب (Taxes) - 61-70
(61, 'دفع ضريبة', 'Tax Payment', 'TAX_PAYMENT', 'ضرائب', 'دفع ضريبة للحكومة', 1, 61, 'file-invoice-dollar', '#dc3545', 1, 1, GETDATE()),
(62, 'استرداد ضريبة', 'Tax Refund', 'TAX_REFUND', 'ضرائب', 'استرداد ضريبة من الحكومة', 1, 62, 'undo-alt', '#28a745', 1, 1, GETDATE()),
(63, 'تحصيل ضريبة قيمة مضافة', 'VAT Collection', 'VAT_COLLECTION', 'ضرائب', 'تحصيل ض.ق.م من العملاء', 1, 63, 'percent', '#007bff', 0, 1, GETDATE()),
(64, 'دفع ضريبة قيمة مضافة', 'VAT Payment', 'VAT_PAYMENT', 'ضرائب', 'دفع ض.ق.م للحكومة', 1, 64, 'percent', '#ffc107', 1, 1, GETDATE()),

-- أصول (Assets) - 71-80
(71, 'شراء أصل', 'Asset Purchase', 'ASSET_PURCHASE', 'أصول', 'شراء أصل ثابت', 1, 71, 'building', '#6f42c1', 1, 1, GETDATE()),
(72, 'بيع أصل', 'Asset Sale', 'ASSET_SALE', 'أصول', 'بيع أصل ثابت', 1, 72, 'handshake', '#28a745', 1, 1, GETDATE()),
(73, 'استهلاك أصل', 'Asset Depreciation', 'ASSET_DEPRECIATION', 'أصول', 'إهلاك الأصول الثابتة', 1, 73, 'chart-line', '#6c757d', 0, 0, GETDATE()),
(74, 'صيانة أصل', 'Asset Maintenance', 'ASSET_MAINTENANCE', 'أصول', 'صيانة الأصول الثابتة', 1, 74, 'wrench', '#fd7e14', 0, 1, GETDATE()),

-- قروض (Loans) - 81-90
(81, 'قرض مستلم', 'Loan Received', 'LOAN_RECEIVED', 'قروض', 'استلام قرض', 1, 81, 'hand-holding-usd', '#17a2b8', 1, 1, GETDATE()),
(82, 'دفعة قرض', 'Loan Payment', 'LOAN_PAYMENT', 'قروض', 'سداد قسط قرض', 1, 82, 'credit-card', '#dc3545', 0, 1, GETDATE()),
(83, 'فوائد قرض', 'Loan Interest', 'LOAN_INTEREST', 'قروض', 'فوائد على القروض', 1, 83, 'percentage', '#ffc107', 0, 1, GETDATE()),
(84, 'قرض معطى', 'Loan Given', 'LOAN_GIVEN', 'قروض', 'إعطاء قرض للغير', 1, 84, 'handshake', '#20c997', 1, 1, GETDATE()),

-- استثمارات (Investments) - 91-100
(91, 'استثمار', 'Investment', 'INVESTMENT', 'استثمارات', 'استثمار في أوراق مالية', 1, 91, 'chart-pie', '#6f42c1', 1, 1, GETDATE()),
(92, 'عائد استثمار', 'Investment Return', 'INVESTMENT_RETURN', 'استثمارات', 'عائد من الاستثمارات', 1, 92, 'coins', '#28a745', 0, 1, GETDATE()),
(93, 'بيع استثمار', 'Investment Sale', 'INVESTMENT_SALE', 'استثمارات', 'بيع استثمارات', 1, 93, 'chart-line', '#fd7e14', 1, 1, GETDATE()),

-- تسويات (Adjustments) - 101-110
(101, 'تسوية عامة', 'General Adjustment', 'GENERAL_ADJUSTMENT', 'تسويات', 'تسوية محاسبية عامة', 1, 101, 'balance-scale', '#6c757d', 1, 1, GETDATE()),
(102, 'تسوية عملة', 'Currency Adjustment', 'CURRENCY_ADJUSTMENT', 'تسويات', 'تسوية فروق العملة', 1, 102, 'dollar-sign', '#17a2b8', 1, 1, GETDATE()),
(103, 'تسوية نهاية السنة', 'Year End Adjustment', 'YEAR_END_ADJUSTMENT', 'تسويات', 'تسويات نهاية السنة المالية', 1, 103, 'calendar-alt', '#e83e8c', 1, 0, GETDATE()),
(104, 'تصحيح خطأ', 'Error Correction', 'ERROR_CORRECTION', 'تسويات', 'تصحيح خطأ محاسبي', 1, 104, 'exclamation-circle', '#dc3545', 1, 1, GETDATE()),
(105, 'إعدام', 'Write Off', 'WRITE_OFF', 'تسويات', 'إعدام رصيد', 1, 105, 'times-circle', '#6c757d', 1, 0, GETDATE()),

-- أخرى (Others) - 111-120
(111, 'رصيد افتتاحي', 'Opening', 'OPENING', 'أخرى', 'أرصدة افتتاحية', 1, 111, 'play-circle', '#28a745', 1, 0, GETDATE()),
(112, 'رصيد ختامي', 'Closing', 'CLOSING', 'أخرى', 'أرصدة ختامية', 1, 112, 'stop-circle', '#dc3545', 1, 0, GETDATE()),
(113, 'تحويل', 'Transfer', 'TRANSFER', 'أخرى', 'تحويل عام', 1, 113, 'exchange-alt', '#ffc107', 0, 1, GETDATE()),
(114, 'عكس قيد', 'Reversal', 'REVERSAL', 'أخرى', 'عكس قيد محاسبي', 1, 114, 'undo', '#fd7e14', 1, 0, GETDATE()),
(115, 'إلغاء', 'Cancellation', 'CANCELLATION', 'أخرى', 'إلغاء معاملة', 1, 115, 'ban', '#6c757d', 1, 0, GETDATE());
