@echo off
title Terra Retail ERP - Professional System Launcher
color 0A
chcp 65001 >nul

echo.
echo ========================================
echo    🏪 Terra Retail ERP Professional
echo    نظام إدارة احترافي متكامل للمتاجر
echo ========================================
echo.

echo 🔧 Preparing professional system...
echo تحضير النظام الاحترافي...
echo.

echo 📊 Checking database and authentication...
echo فحص قاعدة البيانات والمصادقة...
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "USE TerraRetailERP; SELECT COUNT(*) as AuthTables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME IN ('Users', 'Roles', 'Permissions')" -h -1 >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ Database and authentication ready
    echo ✅ قاعدة البيانات والمصادقة جاهزة
) else (
    echo ❌ Database setup required
    echo ❌ يتطلب إعداد قاعدة البيانات
    echo.
    echo Running database setup...
    echo تشغيل إعداد قاعدة البيانات...
    cd database
    sqlcmd -S localhost -U sa -P "@a123admin4" -i create_auth_tables.sql
    cd ..
)

echo.
echo 🚀 Starting Professional API Server...
echo تشغيل خادم API الاحترافي...
start "Terra Retail API" /min cmd /c "cd src\Terra.Retail.API && echo Starting Professional API Server... && dotnet run --urls http://localhost:5000"

echo ⏳ Waiting for API to initialize...
echo انتظار تهيئة API...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 Starting Professional Angular Application...
echo تشغيل تطبيق Angular الاحترافي...
start "Terra Retail Web" cmd /c "cd src\Terra.Retail.Web && echo Starting Professional Angular App... && ng serve --port 4200 --open"

echo.
echo ⏳ Waiting for Angular to compile...
echo انتظار تجميع Angular...
timeout /t 20 /nobreak >nul

echo.
echo ========================================
echo ✅ Professional System Started!
echo ✅ تم تشغيل النظام الاحترافي!
echo ========================================
echo.
echo 📍 Access URLs | روابط الوصول:
echo.
echo 🔗 Login Page:    http://localhost:4200/login
echo 🔗 Dashboard:     http://localhost:4200/dashboard
echo 🔗 API Server:    http://localhost:5000  
echo 📖 API Docs:      http://localhost:5000/swagger
echo 🏥 Health Check:  http://localhost:5000/health
echo.
echo 👤 Demo Login Credentials | بيانات الدخول التجريبية:
echo Username: admin
echo Password: admin123
echo.
echo 🏢 Available Branches | الفروع المتاحة:
echo - الفرع الرئيسي (Main Branch)
echo - فرع الرياض (Riyadh Branch)
echo - فرع جدة (Jeddah Branch)
echo - فرع الدمام (Dammam Branch)
echo.
echo 💡 Professional Features | الميزات الاحترافية:
echo ✅ Professional Login Page with Branch Selection
echo ✅ صفحة دخول احترافية مع اختيار الفرع
echo ✅ Modern Dashboard with Real-time Stats
echo ✅ لوحة تحكم حديثة مع إحصائيات فورية
echo ✅ Material Design UI Components
echo ✅ مكونات واجهة المستخدم بتصميم Material
echo ✅ Multi-language Support (Arabic/English)
echo ✅ دعم متعدد اللغات (عربي/إنجليزي)
echo ✅ Role-based Authentication System
echo ✅ نظام مصادقة قائم على الأدوار
echo.

echo 🌐 Opening Professional Login Page...
echo فتح صفحة الدخول الاحترافية...
timeout /t 5 /nobreak >nul
start http://localhost:4200/login

echo.
echo ========================================
echo 🎉 Terra Retail ERP Professional Ready!
echo 🎉 Terra Retail ERP الاحترافي جاهز!
echo ========================================
echo.
echo Press any key to view system status...
echo اضغط أي مفتاح لعرض حالة النظام...
pause >nul

echo.
echo 📊 Professional System Status | حالة النظام الاحترافي:
echo.

echo Testing API Health...
echo اختبار صحة API...
curl -s http://localhost:5000/health >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ API Server is running professionally
    echo ✅ خادم API يعمل بشكل احترافي
) else (
    echo ❌ API not responding
    echo ❌ API لا يستجيب
)

echo.
echo Testing Professional Angular App...
echo اختبار تطبيق Angular الاحترافي...
curl -s http://localhost:4200 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Professional Angular App is running
    echo ✅ تطبيق Angular الاحترافي يعمل
) else (
    echo ⏳ Professional Angular App still loading...
    echo ⏳ تطبيق Angular الاحترافي لا يزال يحمل...
)

echo.
echo Testing Login Page...
echo اختبار صفحة الدخول...
curl -s http://localhost:4200/login >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Professional Login Page is accessible
    echo ✅ صفحة الدخول الاحترافية متاحة
) else (
    echo ⏳ Login Page still loading...
    echo ⏳ صفحة الدخول لا تزال تحمل...
)

echo.
echo 🎯 Professional System is ready for business!
echo 🎯 النظام الاحترافي جاهز للعمل!
echo.
echo 📋 Next Steps | الخطوات التالية:
echo 1. Login with admin/admin123
echo 2. Select your branch
echo 3. Explore the professional dashboard
echo 4. Start managing your business!
echo.

pause
