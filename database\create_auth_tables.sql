-- إنشاء جداول المصادقة والصلاحيات
-- Create Authentication and Authorization Tables

USE TerraRetailERP;
GO

PRINT N'إنشاء جداول المصادقة والصلاحيات...';

-- جدول الأدوار
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Code nvarchar(50) NOT NULL UNIQUE,
        Description nvarchar(500) NULL,
        Level int NOT NULL DEFAULT 1,
        IsActive bit NOT NULL DEFAULT 1,
        IsSystemRole bit NOT NULL DEFAULT 0,
        Color nvarchar(7) NULL,
        Icon nvarchar(50) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT N'تم إنشاء جدول Roles';
END

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Permissions' AND xtype='U')
BEGIN
    CREATE TABLE Permissions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(100) NOT NULL,
        NameEn nvarchar(100) NULL,
        Code nvarchar(100) NOT NULL UNIQUE,
        Description nvarchar(500) NULL,
        [Group] nvarchar(50) NOT NULL,
        ParentPermissionId int NULL,
        PermissionType int NOT NULL DEFAULT 1, -- 1=Menu, 2=Action, 3=Data
        IsActive bit NOT NULL DEFAULT 1,
        DisplayOrder int NOT NULL DEFAULT 0,
        Icon nvarchar(50) NULL,
        Color nvarchar(7) NULL,
        Path nvarchar(200) NULL,
        HttpMethod nvarchar(10) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (ParentPermissionId) REFERENCES Permissions(Id)
    );
    PRINT N'تم إنشاء جدول Permissions';
END

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        Email nvarchar(100) NOT NULL UNIQUE,
        PasswordHash nvarchar(255) NOT NULL,
        FullName nvarchar(100) NOT NULL,
        PhoneNumber nvarchar(20) NULL,
        ProfileImage nvarchar(500) NULL,
        DefaultBranchId int NULL,
        EmployeeId int NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsEmailConfirmed bit NOT NULL DEFAULT 0,
        EmailConfirmationToken nvarchar(255) NULL,
        PasswordResetToken nvarchar(255) NULL,
        PasswordResetExpiry datetime2 NULL,
        LastLoginAt datetime2 NULL,
        LastLoginIP nvarchar(45) NULL,
        LoginAttempts int NOT NULL DEFAULT 0,
        LockedUntil datetime2 NULL,
        PreferredLanguage nvarchar(5) NOT NULL DEFAULT 'ar',
        TimeZone nvarchar(50) NOT NULL DEFAULT 'Asia/Riyadh',
        Settings nvarchar(2000) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (DefaultBranchId) REFERENCES Branches(Id)
    );
    PRINT N'تم إنشاء جدول Users';
END

-- جدول أدوار المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        UserId int NOT NULL,
        RoleId int NOT NULL,
        IsActive bit NOT NULL DEFAULT 1,
        GrantedById int NULL,
        GrantedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        ExpiryDate datetime2 NULL,
        Notes nvarchar(500) NULL,
        FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
        FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
        FOREIGN KEY (GrantedById) REFERENCES Users(Id)
    );
    
    CREATE UNIQUE INDEX IX_UserRoles_UserId_RoleId ON UserRoles(UserId, RoleId);
    PRINT N'تم إنشاء جدول UserRoles';
END

-- جدول صلاحيات الأدوار
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RolePermissions' AND xtype='U')
BEGIN
    CREATE TABLE RolePermissions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        RoleId int NOT NULL,
        PermissionId int NOT NULL,
        IsGranted bit NOT NULL DEFAULT 1,
        GrantedById int NULL,
        GrantedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        Constraints nvarchar(1000) NULL,
        Notes nvarchar(500) NULL,
        FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
        FOREIGN KEY (PermissionId) REFERENCES Permissions(Id) ON DELETE CASCADE,
        FOREIGN KEY (GrantedById) REFERENCES Users(Id)
    );
    
    CREATE UNIQUE INDEX IX_RolePermissions_RoleId_PermissionId ON RolePermissions(RoleId, PermissionId);
    PRINT N'تم إنشاء جدول RolePermissions';
END

-- جدول فروع المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserBranches' AND xtype='U')
BEGIN
    CREATE TABLE UserBranches (
        Id int IDENTITY(1,1) PRIMARY KEY,
        UserId int NOT NULL,
        BranchId int NOT NULL,
        HasAccess bit NOT NULL DEFAULT 1,
        GrantedById int NULL,
        GrantedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        Notes nvarchar(500) NULL,
        FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
        FOREIGN KEY (BranchId) REFERENCES Branches(Id) ON DELETE CASCADE,
        FOREIGN KEY (GrantedById) REFERENCES Users(Id)
    );
    
    CREATE UNIQUE INDEX IX_UserBranches_UserId_BranchId ON UserBranches(UserId, BranchId);
    PRINT N'تم إنشاء جدول UserBranches';
END

-- إدراج الأدوار الأساسية
IF NOT EXISTS (SELECT * FROM Roles WHERE Code = 'SUPER_ADMIN')
BEGIN
    INSERT INTO Roles (NameAr, NameEn, Code, Description, Level, IsSystemRole, Color, Icon) VALUES
    (N'مدير النظام', N'Super Admin', 'SUPER_ADMIN', N'مدير النظام الرئيسي - صلاحيات كاملة', 1, 1, '#e74c3c', 'fas fa-crown'),
    (N'مدير عام', N'General Manager', 'GENERAL_MANAGER', N'مدير عام - صلاحيات إدارية واسعة', 2, 0, '#3498db', 'fas fa-user-tie'),
    (N'مدير فرع', N'Branch Manager', 'BRANCH_MANAGER', N'مدير فرع - إدارة فرع واحد', 3, 0, '#2ecc71', 'fas fa-building'),
    (N'محاسب', N'Accountant', 'ACCOUNTANT', N'محاسب - إدارة الحسابات والمالية', 4, 0, '#f39c12', 'fas fa-calculator'),
    (N'كاشير', N'Cashier', 'CASHIER', N'كاشير - نقطة البيع والمبيعات', 5, 0, '#9b59b6', 'fas fa-cash-register'),
    (N'مخزني', N'Inventory Clerk', 'INVENTORY_CLERK', N'مخزني - إدارة المخزون', 6, 0, '#1abc9c', 'fas fa-boxes'),
    (N'مندوب مبيعات', N'Sales Rep', 'SALES_REP', N'مندوب مبيعات - المبيعات الخارجية', 7, 0, '#e67e22', 'fas fa-handshake'),
    (N'مستخدم عادي', N'Regular User', 'USER', N'مستخدم عادي - صلاحيات محدودة', 8, 0, '#95a5a6', 'fas fa-user');
    PRINT N'تم إدراج الأدوار الأساسية';
END

-- إدراج الصلاحيات الأساسية
IF NOT EXISTS (SELECT * FROM Permissions WHERE Code = 'DASHBOARD_VIEW')
BEGIN
    INSERT INTO Permissions (NameAr, NameEn, Code, [Group], PermissionType, DisplayOrder, Icon, Path) VALUES
    -- لوحة التحكم
    (N'عرض لوحة التحكم', N'View Dashboard', 'DASHBOARD_VIEW', 'Dashboard', 1, 1, 'fas fa-tachometer-alt', '/dashboard'),
    
    -- إدارة المبيعات
    (N'إدارة المبيعات', N'Sales Management', 'SALES_MODULE', 'Sales', 1, 10, 'fas fa-shopping-cart', '/sales'),
    (N'عرض المبيعات', N'View Sales', 'SALES_VIEW', 'Sales', 2, 11, 'fas fa-eye', '/sales/list'),
    (N'إضافة مبيعة', N'Add Sale', 'SALES_CREATE', 'Sales', 2, 12, 'fas fa-plus', '/sales/create'),
    (N'تعديل مبيعة', N'Edit Sale', 'SALES_EDIT', 'Sales', 2, 13, 'fas fa-edit', '/sales/edit'),
    (N'حذف مبيعة', N'Delete Sale', 'SALES_DELETE', 'Sales', 2, 14, 'fas fa-trash', NULL),
    (N'نقطة البيع', N'POS', 'POS_ACCESS', 'Sales', 2, 15, 'fas fa-cash-register', '/pos'),
    
    -- إدارة العملاء
    (N'إدارة العملاء', N'Customer Management', 'CUSTOMERS_MODULE', 'Customers', 1, 20, 'fas fa-users', '/customers'),
    (N'عرض العملاء', N'View Customers', 'CUSTOMERS_VIEW', 'Customers', 2, 21, 'fas fa-eye', '/customers/list'),
    (N'إضافة عميل', N'Add Customer', 'CUSTOMERS_CREATE', 'Customers', 2, 22, 'fas fa-user-plus', '/customers/create'),
    (N'تعديل عميل', N'Edit Customer', 'CUSTOMERS_EDIT', 'Customers', 2, 23, 'fas fa-user-edit', '/customers/edit'),
    (N'حذف عميل', N'Delete Customer', 'CUSTOMERS_DELETE', 'Customers', 2, 24, 'fas fa-user-times', NULL),
    
    -- إدارة المنتجات
    (N'إدارة المنتجات', N'Product Management', 'PRODUCTS_MODULE', 'Products', 1, 30, 'fas fa-box', '/products'),
    (N'عرض المنتجات', N'View Products', 'PRODUCTS_VIEW', 'Products', 2, 31, 'fas fa-eye', '/products/list'),
    (N'إضافة منتج', N'Add Product', 'PRODUCTS_CREATE', 'Products', 2, 32, 'fas fa-plus', '/products/create'),
    (N'تعديل منتج', N'Edit Product', 'PRODUCTS_EDIT', 'Products', 2, 33, 'fas fa-edit', '/products/edit'),
    (N'حذف منتج', N'Delete Product', 'PRODUCTS_DELETE', 'Products', 2, 34, 'fas fa-trash', NULL),
    
    -- إدارة المخزون
    (N'إدارة المخزون', N'Inventory Management', 'INVENTORY_MODULE', 'Inventory', 1, 40, 'fas fa-warehouse', '/inventory'),
    (N'عرض المخزون', N'View Inventory', 'INVENTORY_VIEW', 'Inventory', 2, 41, 'fas fa-eye', '/inventory/list'),
    (N'تحديث المخزون', N'Update Inventory', 'INVENTORY_UPDATE', 'Inventory', 2, 42, 'fas fa-sync', '/inventory/update'),
    
    -- التقارير
    (N'التقارير', N'Reports', 'REPORTS_MODULE', 'Reports', 1, 50, 'fas fa-chart-bar', '/reports'),
    (N'تقارير المبيعات', N'Sales Reports', 'REPORTS_SALES', 'Reports', 2, 51, 'fas fa-chart-line', '/reports/sales'),
    (N'تقارير المخزون', N'Inventory Reports', 'REPORTS_INVENTORY', 'Reports', 2, 52, 'fas fa-boxes', '/reports/inventory'),
    
    -- الإعدادات
    (N'الإعدادات', N'Settings', 'SETTINGS_MODULE', 'Settings', 1, 60, 'fas fa-cog', '/settings'),
    (N'إدارة المستخدمين', N'User Management', 'USERS_MANAGE', 'Settings', 2, 61, 'fas fa-users-cog', '/settings/users'),
    (N'إدارة الأدوار', N'Role Management', 'ROLES_MANAGE', 'Settings', 2, 62, 'fas fa-user-shield', '/settings/roles'),
    (N'إدارة الفروع', N'Branch Management', 'BRANCHES_MANAGE', 'Settings', 2, 63, 'fas fa-building', '/settings/branches');
    
    PRINT N'تم إدراج الصلاحيات الأساسية';
END

-- إنشاء مستخدم مدير النظام الافتراضي
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, Email, PasswordHash, FullName, IsActive, IsEmailConfirmed, DefaultBranchId) VALUES
    ('admin', '<EMAIL>', '$2a$11$rQiU9k7Z8X9vZ8X9vZ8X9uK7Z8X9vZ8X9vZ8X9vZ8X9vZ8X9vZ8X9u', N'مدير النظام', 1, 1, 1);
    
    DECLARE @AdminUserId int = SCOPE_IDENTITY();
    
    -- إضافة دور مدير النظام للمستخدم
    INSERT INTO UserRoles (UserId, RoleId) VALUES (@AdminUserId, 1);
    
    -- إضافة جميع الفروع للمستخدم
    INSERT INTO UserBranches (UserId, BranchId)
    SELECT @AdminUserId, Id FROM Branches WHERE IsActive = 1;
    
    PRINT N'تم إنشاء مستخدم مدير النظام الافتراضي';
    PRINT N'Username: admin';
    PRINT N'Password: admin123';
END

PRINT N'تم الانتهاء من إنشاء جداول المصادقة والصلاحيات!';
