/* Terra Retail ERP - Professional Supplier Form */

.add-supplier-container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
  color: white;
  padding: 40px 40px 60px;
  margin: -40px -40px 40px;
  border-radius: 0 0 30px 30px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .back-btn {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      width: 48px !important;
      height: 48px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }
    }

    .header-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0 0 var(--spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .page-subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;

    .save-btn {
      background: var(--success-500) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-xl) !important;
      font-weight: 600 !important;
      box-shadow: var(--shadow-lg) !important;

      &:hover:not(:disabled) {
        background: var(--success-600) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
      }

      &:disabled {
        background: var(--gray-400) !important;
        color: var(--gray-600) !important;
      }
    }

    .cancel-btn {
      border-color: rgba(255, 255, 255, 0.5) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-lg) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: white !important;
      }
    }
  }
}

/* ===== FORM CONTENT ===== */
.form-content {
  padding: var(--spacing-2xl) 0;
}

.supplier-form {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

/* ===== FORM CARDS ===== */
.form-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  overflow: hidden !important;

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-xl) var(--spacing-2xl) !important;
    border-bottom: 1px solid var(--gray-200) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;

      mat-icon {
        color: var(--primary-600) !important;
        font-size: 1.5rem !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-2xl) !important;
  }
}

/* ===== FORM GRID ===== */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-2xl);
  align-items: start;
  padding: var(--spacing-md);

  .full-width {
    grid-column: 1 / -1;
  }
}

/* ===== FORM FIELDS ===== */
.mat-mdc-form-field {
  width: 100% !important;

  .mat-mdc-text-field-wrapper {
    background: white !important;
    border-radius: var(--radius-lg) !important;
    transition: all var(--transition-normal) !important;

    &:hover {
      box-shadow: var(--shadow-sm) !important;
    }

    &.mdc-text-field--focused {
      box-shadow: 0 0 0 3px var(--primary-100) !important;
    }
  }

  .mat-mdc-form-field-label {
    font-family: var(--font-family-primary) !important;
    font-weight: 500 !important;
  }

  .mat-mdc-input-element {
    font-family: var(--font-family-primary) !important;
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--gray-500) !important;
  }

  // تحسين شكل الـ Select dropdown
  &.mat-mdc-form-field-type-mat-select {
    .mat-mdc-select-trigger {
      background: white !important;
    }

    .mat-mdc-select-arrow {
      color: var(--primary-600) !important;
    }

    .mat-mdc-select-value {
      font-weight: 500 !important;
    }
  }
}

/* ===== CHECKBOX FIELD ===== */
.checkbox-field {
  display: flex;
  align-items: center;
  height: 56px;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background: white;
  transition: all var(--transition-normal);

  &:hover {
    border-color: var(--primary-300);
    box-shadow: var(--shadow-sm);
  }

  .mat-mdc-checkbox {
    .mdc-checkbox {
      .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
        background-color: var(--primary-500) !important;
        border-color: var(--primary-500) !important;
      }
    }

    .mdc-form-field {
      font-family: var(--font-family-primary) !important;
      font-weight: 500 !important;
      color: var(--gray-800) !important;
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: var(--primary-500);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: var(--spacing-md);

      .header-text {
        .page-title {
          font-size: 2rem;
        }
      }
    }

    .header-actions {
      width: 100%;
      justify-content: center;
    }
  }

  .form-content {
    padding: var(--spacing-xl) 0;
  }

  .supplier-form {
    gap: var(--spacing-xl);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .form-card {
    .mat-mdc-card-header {
      padding: var(--spacing-lg) var(--spacing-xl) !important;

      .mat-mdc-card-title {
        font-size: 1.125rem !important;
      }
    }

    .mat-mdc-card-content {
      padding: var(--spacing-xl) !important;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-left {
      .header-text {
        .page-title {
          font-size: 1.75rem;
        }

        .page-subtitle {
          font-size: 1rem;
        }
      }
    }

    .header-actions {
      flex-direction: column;
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  .form-card {
    .mat-mdc-card-header {
      padding: var(--spacing-md) var(--spacing-lg) !important;
    }

    .mat-mdc-card-content {
      padding: var(--spacing-lg) !important;
    }
  }

  .form-grid {
    gap: var(--spacing-md);
  }
}

/* ===== FIELD WITH BUTTON ===== */
.field-with-button {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;

  .flex-field {
    flex: 1;
  }

  .add-button {
    margin-top: 8px;
    width: 48px !important;
    height: 48px !important;
    color: white !important;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
    border: 2px solid #2196f3 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important;

    &:hover {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
      border-color: #1976d2 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }

    mat-icon {
      font-size: 24px !important;
      width: 24px !important;
      height: 24px !important;
      font-weight: bold !important;
    }
  }
}

/* ===== REQUIRED FIELDS STYLING ===== */
.required-section {
  border: 3px solid #f44336 !important;
  border-radius: 12px !important;
  background: rgba(244, 67, 54, 0.02) !important;
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.1) !important;

  .required-title {
    color: #d32f2f !important;
    font-weight: 700 !important;
    font-size: 1.3rem !important;

    mat-icon {
      color: #d32f2f !important;
      font-size: 1.6rem !important;
      width: 1.6rem !important;
      height: 1.6rem !important;
    }
  }

  .mat-mdc-card-header {
    background: rgba(244, 67, 54, 0.05) !important;
    border-bottom: 2px solid rgba(244, 67, 54, 0.2) !important;
  }
}

.required-field {
  .mat-mdc-form-field {
    .mat-mdc-form-field-label {
      color: #d32f2f !important;
      font-weight: 600 !important;
    }

    .mat-mdc-text-field-wrapper {
      .mdc-notched-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336 !important;
          border-width: 2px !important;
        }
      }

      &:hover .mdc-notched-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #d32f2f !important;
        }
      }

      &.mdc-text-field--focused .mdc-notched-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #d32f2f !important;
          border-width: 2px !important;
        }
      }
    }

    .mat-mdc-form-field-required-marker {
      color: #d32f2f !important;
      font-weight: bold !important;
    }
  }

  // للـ Select fields
  .mat-mdc-form-field-type-mat-select {
    .mat-mdc-text-field-wrapper {
      .mdc-notched-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336 !important;
          border-width: 2px !important;
        }
      }
    }
  }
}

/* ===== VALIDATION SUMMARY ===== */
.validation-summary {
  margin-bottom: 1.5rem;

  .error-card {
    background-color: #ffebee !important;
    border: 2px solid #f44336 !important;
    border-radius: 8px;

    .error-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
      color: #d32f2f !important;
      font-weight: 600;

      mat-icon {
        color: #d32f2f !important;
      }
    }

    .error-list {
      margin: 0;
      padding-right: 1.5rem;
      color: #d32f2f !important;

      li {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  padding: var(--spacing-2xl) 0;
  margin-top: var(--spacing-xl);
  border-top: 2px solid var(--gray-200);

  .save-btn {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%) !important;
    color: white !important;
    padding: var(--spacing-lg) var(--spacing-2xl) !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
    border: 2px solid #4caf50 !important;
    min-width: 160px !important;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
    }

    &:disabled {
      background: var(--gray-400) !important;
      color: var(--gray-600) !important;
      border-color: var(--gray-400) !important;
      box-shadow: none !important;
    }

    mat-icon {
      margin-left: 8px !important;
    }
  }

  .cancel-btn {
    background: white !important;
    color: #f44336 !important;
    border: 2px solid #f44336 !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    border-radius: 8px !important;
    min-width: 120px !important;

    &:hover {
      background: rgba(244, 67, 54, 0.05) !important;
      border-color: #d32f2f !important;
      color: #d32f2f !important;
    }

    mat-icon {
      margin-left: 8px !important;
    }
  }
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);

    .save-btn,
    .cancel-btn {
      width: 100%;
      max-width: 300px;
    }
  }
}

/* ===== GLOBAL DROPDOWN STYLING ===== */
::ng-deep .mat-mdc-select-panel {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid var(--gray-200) !important;
  max-height: 300px !important;

  .mat-mdc-option {
    font-family: var(--font-family-primary) !important;
    font-weight: 500 !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid var(--gray-100) !important;

    &:last-child {
      border-bottom: none !important;
    }

    &:hover {
      background: var(--primary-50) !important;
      color: var(--primary-700) !important;
    }

    &.mat-mdc-option-active {
      background: var(--primary-100) !important;
      color: var(--primary-800) !important;
    }

    &.mdc-list-item--selected {
      background: var(--primary-500) !important;
      color: white !important;
      font-weight: 600 !important;
    }
  }
}

/* ===== SNACKBAR STYLES ===== */
::ng-deep .success-snackbar {
  background: #4caf50 !important;
  color: white !important;
  font-weight: 600 !important;

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}

::ng-deep .error-snackbar {
  background: #f44336 !important;
  color: white !important;
  font-weight: 600 !important;

  .mat-mdc-snack-bar-action {
    color: white !important;
  }
}
