import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  count?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = 'http://localhost:5233/api';

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });
  }

  // Generic CRUD operations
  get<T>(endpoint: string): Observable<ApiResponse<T[]>> {
    return this.http.get<ApiResponse<T[]>>(`${this.baseUrl}/${endpoint}`, {
      headers: this.getHeaders()
    });
  }

  getById<T>(endpoint: string, id: number): Observable<ApiResponse<T>> {
    return this.http.get<ApiResponse<T>>(`${this.baseUrl}/${endpoint}/${id}`, {
      headers: this.getHeaders()
    });
  }

  post<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, {
      headers: this.getHeaders()
    });
  }

  put<T>(endpoint: string, id: number, data: any): Observable<ApiResponse<T>> {
    return this.http.put<ApiResponse<T>>(`${this.baseUrl}/${endpoint}/${id}`, data, {
      headers: this.getHeaders()
    });
  }

  delete<T>(endpoint: string, id: number): Observable<ApiResponse<T>> {
    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}/${endpoint}/${id}`, {
      headers: this.getHeaders()
    });
  }

  // Specific API endpoints
  getCustomers(): Observable<ApiResponse<any[]>> {
    return this.get('customers');
  }

  getSuppliers(): Observable<ApiResponse<any[]>> {
    return this.get('suppliers');
  }

  getProducts(): Observable<ApiResponse<any[]>> {
    return this.get('products');
  }

  getBranches(): Observable<ApiResponse<any[]>> {
    return this.get('settings/branches');
  }

  getUsers(): Observable<ApiResponse<any[]>> {
    return this.get('settings/users');
  }

  // Lookup data
  getLookupData(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.baseUrl}/lookup/all`, {
      headers: this.getHeaders()
    });
  }

  getCustomerTypes(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/customer-types');
  }

  getSupplierTypes(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/supplier-types');
  }

  getCategories(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/categories');
  }

  getUnits(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/units');
  }

  getPriceCategories(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/price-categories');
  }

  getPaymentMethods(): Observable<ApiResponse<any[]>> {
    return this.get('lookup/payment-methods');
  }

  // Reports
  getReports(): Observable<ApiResponse<any[]>> {
    return this.get('reports');
  }

  // POS
  getPOSData(): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(`${this.baseUrl}/pos/data`, {
      headers: this.getHeaders()
    });
  }

  // Sales
  getSales(): Observable<ApiResponse<any[]>> {
    return this.get('sales');
  }

  // Purchases
  getPurchases(): Observable<ApiResponse<any[]>> {
    return this.get('purchases');
  }

  // Inventory
  getInventory(): Observable<ApiResponse<any[]>> {
    return this.get('inventory');
  }

  // Financial
  getJournalEntries(): Observable<ApiResponse<any[]>> {
    return this.get('journal-entry');
  }

  getChartOfAccounts(): Observable<ApiResponse<any[]>> {
    return this.get('journal-entry/chart-of-accounts');
  }
}
