/* Terra Retail ERP - Areas Styles */

.areas-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--warning-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateX(-2px);
    }
  }

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .page-subtitle {
    font-size: 1rem;
    margin: var(--spacing-xs) 0 0;
    opacity: 0.9;
  }

  .add-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

/* ===== CONTENT ===== */
.content {
  padding: 0;
}

/* ===== FORM CARD ===== */
.form-card {
  margin-bottom: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;

  mat-card-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);

    mat-card-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary-700);
      margin: 0;

      mat-icon {
        color: var(--primary-600);
      }
    }
  }

  mat-card-content {
    padding: var(--spacing-xl);
  }

  mat-card-actions {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--surface-50);
    border-top: 1px solid var(--border-color);
  }
}

/* ===== FORM STYLES ===== */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  align-items: start;

  .full-width {
    grid-column: 1 / -1;
  }

  .checkbox-field {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) 0;
  }
}

/* ===== TABLE CARD ===== */
.table-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;

  mat-card-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);

    mat-card-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary-700);
      margin: 0;

      mat-icon {
        color: var(--primary-600);
      }
    }
  }

  mat-card-content {
    padding: 0;
  }
}

/* ===== TABLE STYLES ===== */
.table-container {
  overflow-x: auto;
}

.areas-table {
  width: 100%;
  background: white;

  th {
    background: var(--surface-100);
    color: var(--text-primary);
    font-weight: 600;
    padding: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
  }

  td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
  }

  tr:hover {
    background: var(--surface-50);
  }
}

/* ===== CODE BADGE ===== */
.code-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ===== STATUS BADGE ===== */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.active {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-200);
  }

  &.inactive {
    background: var(--error-100);
    color: var(--error-700);
    border: 1px solid var(--error-200);
  }
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

/* ===== NO DATA ===== */
.no-data {
  text-align: center;
  padding: var(--spacing-4xl);
  color: var(--text-secondary);

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    color: var(--text-disabled);
    margin-bottom: var(--spacing-lg);
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 var(--spacing-sm);
    color: var(--text-primary);
  }

  p {
    font-size: 1rem;
    margin: 0 0 var(--spacing-xl);
  }
}

/* ===== LOADING ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  color: var(--text-secondary);

  mat-spinner {
    margin-bottom: var(--spacing-lg);
  }

  p {
    font-size: 1rem;
    margin: 0;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);

  mat-spinner {
    margin-bottom: var(--spacing-lg);
  }

  p {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin: 0;
  }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: var(--spacing-md);
    }
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .areas-table {
    th, td {
      padding: var(--spacing-md);
      font-size: 0.875rem;
    }
  }
}
