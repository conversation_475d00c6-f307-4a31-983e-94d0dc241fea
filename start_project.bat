@echo off
chcp 65001 >nul
echo ========================================
echo 🏪 Terra Retail ERP System
echo ========================================
echo.

echo 📋 بدء تشغيل نظام Terra Retail ERP...
echo Starting Terra Retail ERP System...
echo.

echo 🔧 التحقق من المتطلبات...
echo Checking requirements...

:: التحقق من .NET
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET SDK غير مثبت
    echo ❌ .NET SDK is not installed
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

:: التحقق من Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo ❌ Node.js is not installed
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo ✅ All requirements are met
echo.

echo 🗄️ إعداد قاعدة البيانات...
echo Setting up database...
cd database
call run_database.bat
cd ..
echo.

echo 🔧 استعادة حزم .NET...
echo Restoring .NET packages...
cd src\Terra.Retail.API
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة حزم .NET
    echo ❌ Failed to restore .NET packages
    pause
    exit /b 1
)
cd ..\..
echo.

echo 📦 تثبيت حزم Angular...
echo Installing Angular packages...
cd src\Terra.Retail.Web
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تثبيت حزم Angular
    echo ❌ Failed to install Angular packages
    pause
    exit /b 1
)
cd ..\..
echo.

echo 🚀 تشغيل التطبيق...
echo Starting application...
echo.

echo ========================================
echo 🎉 تم إعداد النظام بنجاح!
echo 🎉 System setup completed successfully!
echo ========================================
echo.

echo 📍 روابط الوصول:
echo Access URLs:
echo.
echo 🌐 API Documentation: http://localhost:5000
echo 🖥️ Angular Application: http://localhost:4200
echo.

echo 💡 لتشغيل النظام:
echo To run the system:
echo.
echo 1. Backend API:
echo    cd src\Terra.Retail.API
echo    dotnet run
echo.
echo 2. Frontend (في terminal جديد):
echo    cd src\Terra.Retail.Web
echo    ng serve
echo.

echo 📚 للمزيد من المعلومات، راجع README.md
echo For more information, check README.md
echo.

pause
