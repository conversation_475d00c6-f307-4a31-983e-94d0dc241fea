{"version": 2, "dgSpecHash": "xXqtJGpcP0Y=", "success": true, "projectFilePath": "X:\\barmaga\\Angler\\Erp 2\\TerraRetailERP_Simple\\TerraRetailERP_Simple.csproj", "expectedPackageFiles": ["X:\\NugetCache\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "X:\\NugetCache\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "X:\\NugetCache\\bcrypt.net-next\\4.0.3\\bcrypt.net-next.4.0.3.nupkg.sha512", "X:\\NugetCache\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "X:\\NugetCache\\microsoft.aspnetcore.openapi\\8.0.16\\microsoft.aspnetcore.openapi.8.0.16.nupkg.sha512", "X:\\NugetCache\\microsoft.bcl.asyncinterfaces\\7.0.0\\microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "X:\\NugetCache\\microsoft.build.locator\\1.7.8\\microsoft.build.locator.1.7.8.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.workspaces.msbuild\\4.8.0\\microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "X:\\NugetCache\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "X:\\NugetCache\\microsoft.data.sqlclient\\5.1.6\\microsoft.data.sqlclient.5.1.6.nupkg.sha512", "X:\\NugetCache\\microsoft.data.sqlclient.sni.runtime\\5.1.1\\microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore\\9.0.6\\microsoft.entityframeworkcore.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.abstractions\\9.0.6\\microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.analyzers\\9.0.6\\microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.design\\9.0.6\\microsoft.entityframeworkcore.design.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.relational\\9.0.6\\microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.sqlserver\\9.0.6\\microsoft.entityframeworkcore.sqlserver.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.caching.abstractions\\9.0.6\\microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.caching.memory\\9.0.6\\microsoft.extensions.caching.memory.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencymodel\\9.0.6\\microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "X:\\NugetCache\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "X:\\NugetCache\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.protocols\\6.35.0\\microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.protocols.openidconnect\\6.35.0\\microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "X:\\NugetCache\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "X:\\NugetCache\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "X:\\NugetCache\\microsoft.openapi\\1.6.14\\microsoft.openapi.1.6.14.nupkg.sha512", "X:\\NugetCache\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "X:\\NugetCache\\mono.texttemplating\\3.0.0\\mono.texttemplating.3.0.0.nupkg.sha512", "X:\\NugetCache\\swashbuckle.aspnetcore\\6.6.2\\swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "X:\\NugetCache\\swashbuckle.aspnetcore.swagger\\6.6.2\\swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "X:\\NugetCache\\swashbuckle.aspnetcore.swaggergen\\6.6.2\\swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "X:\\NugetCache\\swashbuckle.aspnetcore.swaggerui\\6.6.2\\swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512", "X:\\NugetCache\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "X:\\NugetCache\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "X:\\NugetCache\\system.diagnostics.diagnosticsource\\9.0.6\\system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "X:\\NugetCache\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.formats.asn1\\9.0.6\\system.formats.asn1.9.0.6.nupkg.sha512", "X:\\NugetCache\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "X:\\NugetCache\\system.io.pipelines\\9.0.6\\system.io.pipelines.9.0.6.nupkg.sha512", "X:\\NugetCache\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "X:\\NugetCache\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "X:\\NugetCache\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "X:\\NugetCache\\system.reflection.metadata\\7.0.0\\system.reflection.metadata.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "X:\\NugetCache\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "X:\\NugetCache\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "X:\\NugetCache\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.text.encodings.web\\9.0.6\\system.text.encodings.web.9.0.6.nupkg.sha512", "X:\\NugetCache\\system.text.json\\9.0.6\\system.text.json.9.0.6.nupkg.sha512", "X:\\NugetCache\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "X:\\NugetCache\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "X:\\NugetCache\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": []}