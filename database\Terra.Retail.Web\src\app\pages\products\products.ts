import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';

interface Product {
  id: number;
  nameAr: string;
  nameEn: string;
  productCode: string;
  categoryId: number;
  categoryName: string;
  unitId: number;
  unitName: string;
  price: number;
  stock: number;
  isActive: boolean;
}

interface Category {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
}

interface Unit {
  id: number;
  nameAr: string;
  nameEn: string;
  symbol: string;
}

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatSnackBarModule,
    MatChipsModule,
    MatBadgeModule,
    MatFormFieldModule
  ],
  templateUrl: './products.html',
  styleUrls: ['./products.scss']
})
export class Products implements OnInit {
  products: Product[] = [];
  filteredProducts: Product[] = [];
  categories: Category[] = [];
  units: Unit[] = [];

  searchTerm: string = '';
  selectedCategory: string = '';
  selectedUnit: string = '';
  isLoading: boolean = false;

  totalProducts: number = 0;
  activeProducts: number = 0;
  totalValue: number = 0;
  lowStockProducts: number = 0;

  displayedColumns: string[] = ['productCode', 'nameAr', 'category', 'unit', 'price', 'stock', 'status', 'actions'];

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadProducts();
    this.loadCategories();
    this.loadUnits();
  }

  loadProducts() {
    this.isLoading = true;
    this.http.get<any>('http://localhost:5127/api/simple/products').subscribe({
      next: (response) => {
        this.products = response.products || [];
        this.filteredProducts = [...this.products];
        this.calculateStatistics();
        this.isLoading = false;

        if (this.products.length === 0) {
          this.showMessage('لا توجد منتجات في قاعدة البيانات');
        }
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.showMessage('خطأ في تحميل بيانات المنتجات من قاعدة البيانات');
        this.products = [];
        this.filteredProducts = [];
        this.isLoading = false;
      }
    });
  }

  loadCategories() {
    this.http.get<any>('http://localhost:5127/api/simple/categories').subscribe({
      next: (response) => {
        this.categories = response.categories || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.categories = [
          { id: 1, nameAr: 'عام', nameEn: 'General', code: 'GEN' },
          { id: 2, nameAr: 'مواد غذائية ومشروبات', nameEn: 'Food & Beverages', code: 'FOOD' }
        ];
      }
    });
  }

  loadUnits() {
    this.http.get<any>('http://localhost:5127/api/simple/units').subscribe({
      next: (response) => {
        this.units = response.units || [];
      },
      error: (error) => {
        console.error('Error loading units:', error);
        this.units = [
          { id: 13, nameAr: 'حبة', nameEn: 'Piece', symbol: 'حبة' },
          { id: 14, nameAr: 'كيلو', nameEn: 'Kilogram', symbol: 'كجم' }
        ];
      }
    });
  }

  calculateStatistics() {
    this.totalProducts = this.products.length;
    this.activeProducts = this.products.filter(p => p.isActive).length;
    this.totalValue = this.products.reduce((sum, p) => sum + (p.price * p.stock), 0);
    this.lowStockProducts = this.products.filter(p => p.stock < 100).length;
  }

  onSearch() {
    this.applyFilters();
  }

  onFilterChange() {
    this.applyFilters();
  }

  applyFilters() {
    this.filteredProducts = this.products.filter(product => {
      const matchesSearch = !this.searchTerm ||
        product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.nameEn.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = !this.selectedCategory ||
        product.categoryId.toString() === this.selectedCategory;

      const matchesUnit = !this.selectedUnit ||
        product.unitId.toString() === this.selectedUnit;

      return matchesSearch && matchesCategory && matchesUnit;
    });
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.selectedUnit = '';
    this.filteredProducts = [...this.products];
  }

  getStockStatus(stock: number): string {
    if (stock === 0) return 'نفد المخزون';
    if (stock < 50) return 'مخزون منخفض';
    if (stock < 100) return 'مخزون متوسط';
    return 'مخزون جيد';
  }

  getStockClass(stock: number): string {
    if (stock === 0) return 'out-of-stock';
    if (stock < 50) return 'low-stock';
    if (stock < 100) return 'medium-stock';
    return 'good-stock';
  }

  addProduct() {
    this.showMessage('إضافة منتج جديد - قيد التطوير');
  }

  viewProduct(product: Product) {
    this.showMessage(`عرض تفاصيل المنتج: ${product.nameAr}`);
  }

  editProduct(product: Product) {
    this.showMessage(`تعديل المنتج: ${product.nameAr} - قيد التطوير`);
  }

  deleteProduct(product: Product) {
    this.showMessage(`حذف المنتج: ${product.nameAr} - قيد التطوير`);
  }

  exportProducts() {
    this.showMessage('تصدير بيانات المنتجات - قيد التطوير');
  }

  private showMessage(message: string) {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
