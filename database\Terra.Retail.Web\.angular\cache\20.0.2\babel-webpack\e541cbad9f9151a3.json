{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\n// Components\nimport { CustomersComponent } from './customers.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomersComponent,\n  data: {\n    title: 'إدارة العملاء'\n  }\n}];\nexport let CustomersModule = /*#__PURE__*/(() => {\n  class CustomersModule {\n    static ɵfac = function CustomersModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomersModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomersModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), FormsModule, ReactiveFormsModule, HttpClientModule,\n      // Angular Material Modules\n      MatCardModule, MatButtonModule, MatIconModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatMenuModule, MatTooltipModule, MatChipsModule, MatBadgeModule, MatTabsModule, MatExpansionModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule]\n    });\n  }\n  return CustomersModule;\n})();\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomersModule, {\n    declarations: [CustomersComponent],\n    imports: [CommonModule, i1.RouterModule, FormsModule, ReactiveFormsModule, HttpClientModule,\n    // Angular Material Modules\n    MatCardModule, MatButtonModule, MatIconModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatMenuModule, MatTooltipModule, MatChipsModule, MatBadgeModule, MatTabsModule, MatExpansionModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatMenuModule", "MatTooltipModule", "MatChipsModule", "MatBadgeModule", "MatTabsModule", "MatExpansionModule", "MatCheckboxModule", "MatRadioModule", "MatDatepickerModule", "MatNativeDateModule", "CustomersComponent", "routes", "path", "component", "data", "title", "CustomersModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\customers\\customers.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n\n// Angular Material Modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\n\n// Components\nimport { CustomersComponent } from './customers.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: CustomersComponent,\n    data: { title: 'إدارة العملاء' }\n  }\n];\n\n@NgModule({\n  declarations: [\n    CustomersComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    \n    // Angular Material Modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatMenuModule,\n    MatTooltipModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTabsModule,\n    MatExpansionModule,\n    MatCheckboxModule,\n    MatRadioModule,\n    MatDatepickerModule,\n    MatNativeDateModule\n  ],\n  providers: []\n})\nexport class CustomersModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAE5D;AACA,SAASC,kBAAkB,QAAQ,uBAAuB;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,kBAAkB;EAC7BI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAe;CAC/B,CACF;AAuCD,WAAaC,eAAe;EAAtB,MAAOA,eAAe;;uCAAfA,eAAe;IAAA;;YAAfA;IAAe;;gBAhCxBjC,YAAY,EACZC,YAAY,CAACiC,QAAQ,CAACN,MAAM,CAAC,EAC7B1B,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB;MAEhB;MACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB;IAAA;;SAIVO,eAAe;AAAA;;2EAAfA,eAAe;IAAAE,YAAA,GAnCxBR,kBAAkB;IAAAS,OAAA,GAGlBpC,YAAY,EAAAqC,EAAA,CAAApC,YAAA,EAEZC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB;IAEhB;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}