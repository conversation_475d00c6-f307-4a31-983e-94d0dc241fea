{"ast": null, "code": "import { MediaMatcher } from '@angular/cdk/layout';\nimport { InjectionToken, inject, ANIMATION_MODULE_TYPE } from '@angular/core';\n\n/** Injection token used to configure the animations in Angular Material. */\nconst MATERIAL_ANIMATIONS = /*#__PURE__*/new InjectionToken('MATERIAL_ANIMATIONS');\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nlet AnimationCurves = /*#__PURE__*/(() => {\n  class AnimationCurves {\n    static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n    static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n    static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n    static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  }\n  return AnimationCurves;\n})();\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nlet AnimationDurations = /*#__PURE__*/(() => {\n  class AnimationDurations {\n    static COMPLEX = '375ms';\n    static ENTERING = '225ms';\n    static EXITING = '195ms';\n  }\n  return AnimationDurations;\n})();\n/**\n * Returns whether animations have been disabled by DI. Must be called in a DI context.\n * @docs-private\n */\nfunction _animationsDisabled() {\n  if (inject(MATERIAL_ANIMATIONS, {\n    optional: true\n  })?.animationsDisabled || inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations') {\n    return true;\n  }\n  const mediaMatcher = inject(MediaMatcher);\n  return mediaMatcher.matchMedia('(prefers-reduced-motion)').matches;\n}\nexport { AnimationCurves as A, MATERIAL_ANIMATIONS as M, _animationsDisabled as _, AnimationDurations as a };", "map": {"version": 3, "names": ["MediaMatcher", "InjectionToken", "inject", "ANIMATION_MODULE_TYPE", "MATERIAL_ANIMATIONS", "AnimationCurves", "STANDARD_CURVE", "DECELERATION_CURVE", "ACCELERATION_CURVE", "SHARP_CURVE", "AnimationDurations", "COMPLEX", "ENTERING", "EXITING", "_animationsDisabled", "optional", "animationsDisabled", "mediaMatcher", "matchMedia", "matches", "A", "M", "_", "a"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/animation-DfMFjxHu.mjs"], "sourcesContent": ["import { MediaMatcher } from '@angular/cdk/layout';\nimport { InjectionToken, inject, ANIMATION_MODULE_TYPE } from '@angular/core';\n\n/** Injection token used to configure the animations in Angular Material. */\nconst MATERIAL_ANIMATIONS = new InjectionToken('MATERIAL_ANIMATIONS');\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationCurves {\n    static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n    static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n    static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n    static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n}\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationDurations {\n    static COMPLEX = '375ms';\n    static ENTERING = '225ms';\n    static EXITING = '195ms';\n}\n/**\n * Returns whether animations have been disabled by DI. Must be called in a DI context.\n * @docs-private\n */\nfunction _animationsDisabled() {\n    if (inject(MATERIAL_ANIMATIONS, { optional: true })?.animationsDisabled ||\n        inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations') {\n        return true;\n    }\n    const mediaMatcher = inject(MediaMatcher);\n    return mediaMatcher.matchMedia('(prefers-reduced-motion)').matches;\n}\n\nexport { AnimationCurves as A, MATERIAL_ANIMATIONS as M, _animationsDisabled as _, AnimationDurations as a };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,SAASC,cAAc,EAAEC,MAAM,EAAEC,qBAAqB,QAAQ,eAAe;;AAE7E;AACA,MAAMC,mBAAmB,gBAAG,IAAIH,cAAc,CAAC,qBAAqB,CAAC;AACrE;AACA;AACA;AACA;AACA;AAJA,IAKMI,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOC,cAAc,GAAG,6BAA6B;IACrD,OAAOC,kBAAkB,GAAG,6BAA6B;IACzD,OAAOC,kBAAkB,GAAG,2BAA2B;IACvD,OAAOC,WAAW,GAAG,6BAA6B;EACtD;EAAC,OALKJ,eAAe;AAAA;AAMrB;AACA;AACA;AACA;AACA;AAJA,IAKMK,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAOC,OAAO,GAAG,OAAO;IACxB,OAAOC,QAAQ,GAAG,OAAO;IACzB,OAAOC,OAAO,GAAG,OAAO;EAC5B;EAAC,OAJKH,kBAAkB;AAAA;AAKxB;AACA;AACA;AACA;AACA,SAASI,mBAAmBA,CAAA,EAAG;EAC3B,IAAIZ,MAAM,CAACE,mBAAmB,EAAE;IAAEW,QAAQ,EAAE;EAAK,CAAC,CAAC,EAAEC,kBAAkB,IACnEd,MAAM,CAACC,qBAAqB,EAAE;IAAEY,QAAQ,EAAE;EAAK,CAAC,CAAC,KAAK,gBAAgB,EAAE;IACxE,OAAO,IAAI;EACf;EACA,MAAME,YAAY,GAAGf,MAAM,CAACF,YAAY,CAAC;EACzC,OAAOiB,YAAY,CAACC,UAAU,CAAC,0BAA0B,CAAC,CAACC,OAAO;AACtE;AAEA,SAASd,eAAe,IAAIe,CAAC,EAAEhB,mBAAmB,IAAIiB,CAAC,EAAEP,mBAAmB,IAAIQ,CAAC,EAAEZ,kBAAkB,IAAIa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}