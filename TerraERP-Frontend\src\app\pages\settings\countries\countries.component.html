<div class="container-fluid p-4">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">
            <i class="fas fa-globe me-2"></i>
            إدارة البلدان
          </h4>
        </div>
        <div class="card-body">
          
          <!-- نموذج إضافة/تعديل البلد -->
          <div class="row mb-4">
            <div class="col-12">
              <form [formGroup]="countryForm" (ngSubmit)="onSubmit()">
                <div class="row">
                  <div class="col-md-3">
                    <label class="form-label">الاسم بالعربية *</label>
                    <input 
                      type="text" 
                      formControlName="nameAr"
                      [class]="getFieldClasses('nameAr')"
                      placeholder="أدخل اسم البلد بالعربية">
                    <div *ngIf="isFieldInvalid('nameAr')" class="invalid-feedback">
                      الاسم بالعربية مطلوب
                    </div>
                  </div>
                  
                  <div class="col-md-3">
                    <label class="form-label">الاسم بالإنجليزية</label>
                    <input 
                      type="text" 
                      formControlName="nameEn"
                      [class]="getFieldClasses('nameEn')"
                      placeholder="أدخل اسم البلد بالإنجليزية">
                  </div>
                  
                  <div class="col-md-2">
                    <label class="form-label">الكود *</label>
                    <input 
                      type="text" 
                      formControlName="code"
                      [class]="getFieldClasses('code')"
                      placeholder="مثل: EG">
                    <div *ngIf="isFieldInvalid('code')" class="invalid-feedback">
                      الكود مطلوب
                    </div>
                  </div>
                  
                  <div class="col-md-2">
                    <label class="form-label">كود الهاتف *</label>
                    <input 
                      type="text" 
                      formControlName="phoneCode"
                      [class]="getFieldClasses('phoneCode')"
                      placeholder="مثل: +20">
                    <div *ngIf="isFieldInvalid('phoneCode')" class="invalid-feedback">
                      كود الهاتف مطلوب
                    </div>
                  </div>
                  
                  <div class="col-md-2">
                    <label class="form-label">العملة *</label>
                    <input 
                      type="text" 
                      formControlName="currency"
                      [class]="getFieldClasses('currency')"
                      placeholder="مثل: EGP">
                    <div *ngIf="isFieldInvalid('currency')" class="invalid-feedback">
                      العملة مطلوبة
                    </div>
                  </div>
                </div>
                
                <div class="row mt-3">
                  <div class="col-12">
                    <button 
                      type="submit" 
                      class="btn btn-primary me-2"
                      [disabled]="countryForm.invalid">
                      <i class="fas fa-save me-1"></i>
                      {{ isEditing ? 'تحديث' : 'إضافة' }}
                    </button>
                    
                    <button 
                      type="button" 
                      class="btn btn-secondary"
                      (click)="resetForm()"
                      *ngIf="isEditing">
                      <i class="fas fa-times me-1"></i>
                      إلغاء
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
          
          <!-- جدول البلدان -->
          <div class="row">
            <div class="col-12">
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>الرقم</th>
                      <th>الاسم بالعربية</th>
                      <th>الاسم بالإنجليزية</th>
                      <th>الكود</th>
                      <th>كود الهاتف</th>
                      <th>العملة</th>
                      <th>الحالة</th>
                      <th>تاريخ الإنشاء</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngIf="isLoading">
                      <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                          <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                      </td>
                    </tr>
                    
                    <tr *ngIf="!isLoading && countries.length === 0">
                      <td colspan="9" class="text-center text-muted">
                        لا توجد بلدان مسجلة
                      </td>
                    </tr>
                    
                    <tr *ngFor="let country of countries; let i = index">
                      <td>{{ i + 1 }}</td>
                      <td>{{ country.nameAr }}</td>
                      <td>{{ country.nameEn || '-' }}</td>
                      <td>
                        <span class="badge bg-info">{{ country.code }}</span>
                      </td>
                      <td>
                        <span class="badge bg-success">{{ country.phoneCode }}</span>
                      </td>
                      <td>
                        <span class="badge bg-warning text-dark">{{ country.currency }}</span>
                      </td>
                      <td>
                        <span 
                          class="badge"
                          [class.bg-success]="country.isActive"
                          [class.bg-danger]="!country.isActive">
                          {{ country.isActive ? 'نشط' : 'غير نشط' }}
                        </span>
                      </td>
                      <td>{{ country.createdAt | date:'dd/MM/yyyy' }}</td>
                      <td>
                        <div class="btn-group" role="group">
                          <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary"
                            (click)="editCountry(country)"
                            title="تعديل">
                            <i class="fas fa-edit"></i>
                          </button>
                          
                          <button 
                            type="button" 
                            class="btn btn-sm btn-outline-danger"
                            (click)="deleteCountry(country.id)"
                            title="حذف">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</div>
