using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// صور المنتجات
    /// </summary>
    public class ProductImage : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// مسار الصورة
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// اسم الملف الأصلي
        /// </summary>
        [MaxLength(255)]
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// حجم الملف (بالبايت)
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// نوع الملف (MIME Type)
        /// </summary>
        [MaxLength(50)]
        public string? ContentType { get; set; }

        /// <summary>
        /// عرض الصورة (بكسل)
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// ارتفاع الصورة (بكسل)
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// هل الصورة رئيسية
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// نص بديل للصورة
        /// </summary>
        [MaxLength(200)]
        public string? AltText { get; set; }

        /// <summary>
        /// عنوان الصورة
        /// </summary>
        [MaxLength(200)]
        public string? Title { get; set; }

        /// <summary>
        /// وصف الصورة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// هل الصورة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
    }
}
