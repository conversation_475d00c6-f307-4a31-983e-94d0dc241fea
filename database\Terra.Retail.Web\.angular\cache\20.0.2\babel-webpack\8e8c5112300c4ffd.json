{"ast": null, "code": "import { FocusTrapFactory, FocusMonitor, InteractivityChecker } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Platform } from '@angular/cdk/platform';\nimport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, NgZone, Component, ChangeDetectionStrategy, ViewEncapsulation, Renderer2, DOCUMENT, signal, EventEmitter, Injector, afterNextRender, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, take, startWith, debounceTime } from 'rxjs/operators';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\nfunction MatDrawerContainer_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_Conditional_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r1._isShowingBackdrop());\n  }\n}\nfunction MatDrawerContainer_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nfunction MatSidenavContainer_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_Conditional_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r1._isShowingBackdrop());\n  }\n}\nfunction MatSidenavContainer_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\";\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = /*#__PURE__*/new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = /*#__PURE__*/new InjectionToken('MAT_DRAWER_CONTAINER');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\nlet MatDrawerContent = /*#__PURE__*/(() => {\n  class MatDrawerContent extends CdkScrollable {\n    _platform = inject(Platform);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _container = inject(MatDrawerContainer);\n    constructor() {\n      const elementRef = inject(ElementRef);\n      const scrollDispatcher = inject(ScrollDispatcher);\n      const ngZone = inject(NgZone);\n      super(elementRef, scrollDispatcher, ngZone);\n    }\n    ngAfterContentInit() {\n      this._container._contentMarginChanges.subscribe(() => {\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Determines whether the content element should be hidden from the user. */\n    _shouldBeHidden() {\n      // In some modes the content is pushed based on the width of the opened sidenavs, however on\n      // the server we can't measure the sidenav so the margin is always zero. This can cause the\n      // content to jump around when it's rendered on the server and hydrated on the client. We\n      // avoid it by hiding the content on the initial render and then showing it once the sidenav\n      // has been measured on the client.\n      if (this._platform.isBrowser) {\n        return false;\n      }\n      const {\n        start,\n        end\n      } = this._container;\n      return start != null && start.mode !== 'over' && start.opened || end != null && end.mode !== 'over' && end.opened;\n    }\n    static ɵfac = function MatDrawerContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDrawerContent)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawerContent,\n      selectors: [[\"mat-drawer-content\"]],\n      hostAttrs: [1, \"mat-drawer-content\"],\n      hostVars: 6,\n      hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n          i0.ɵɵclassProp(\"mat-drawer-content-hidden\", ctx._shouldBeHidden());\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatDrawerContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatDrawerContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nlet MatDrawer = /*#__PURE__*/(() => {\n  class MatDrawer {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _focusMonitor = inject(FocusMonitor);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _interactivityChecker = inject(InteractivityChecker);\n    _doc = inject(DOCUMENT, {\n      optional: true\n    });\n    _container = inject(MAT_DRAWER_CONTAINER, {\n      optional: true\n    });\n    _focusTrap = null;\n    _elementFocusedBeforeDrawerWasOpened = null;\n    _eventCleanups;\n    /** Whether the view of the component has been attached. */\n    _isAttached;\n    /** Anchor node used to restore the drawer to its initial position. */\n    _anchor;\n    /** The side that the drawer is attached to. */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      // Make sure we have a valid value.\n      value = value === 'end' ? 'end' : 'start';\n      if (value !== this._position) {\n        // Static inputs in Ivy are set before the element is in the DOM.\n        if (this._isAttached) {\n          this._updatePositionInParent(value);\n        }\n        this._position = value;\n        this.onPositionChanged.emit();\n      }\n    }\n    _position = 'start';\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n    get mode() {\n      return this._mode;\n    }\n    set mode(value) {\n      this._mode = value;\n      this._updateFocusTrapState();\n      this._modeChanged.next();\n    }\n    _mode = 'over';\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n    get disableClose() {\n      return this._disableClose;\n    }\n    set disableClose(value) {\n      this._disableClose = coerceBooleanProperty(value);\n    }\n    _disableClose = false;\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n    get autoFocus() {\n      const value = this._autoFocus;\n      // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n      // because we don't know how the sidenav is being used, but in some cases it still makes\n      // sense to do it. The consumer can explicitly set `autoFocus`.\n      if (value == null) {\n        if (this.mode === 'side') {\n          return 'dialog';\n        } else {\n          return 'first-tabbable';\n        }\n      }\n      return value;\n    }\n    set autoFocus(value) {\n      if (value === 'true' || value === 'false' || value == null) {\n        value = coerceBooleanProperty(value);\n      }\n      this._autoFocus = value;\n    }\n    _autoFocus;\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n    get opened() {\n      return this._opened();\n    }\n    set opened(value) {\n      this.toggle(coerceBooleanProperty(value));\n    }\n    _opened = signal(false);\n    /** How the sidenav was opened (keypress, mouse click etc.) */\n    _openedVia;\n    /** Emits whenever the drawer has started animating. */\n    _animationStarted = new Subject();\n    /** Emits whenever the drawer is done animating. */\n    _animationEnd = new Subject();\n    /** Event emitted when the drawer open state is changed. */\n    openedChange =\n    // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter(/* isAsync */true);\n    /** Event emitted when the drawer has been opened. */\n    _openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the drawer has started opening. */\n    openedStart = this._animationStarted.pipe(filter(() => this.opened), mapTo(undefined));\n    /** Event emitted when the drawer has been closed. */\n    _closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the drawer has started closing. */\n    closedStart = this._animationStarted.pipe(filter(() => !this.opened), mapTo(undefined));\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Event emitted when the drawer's position changes. */\n    // tslint:disable-next-line:no-output-on-prefix\n    onPositionChanged = new EventEmitter();\n    /** Reference to the inner element that contains all the content. */\n    _content;\n    /**\n     * An observable that emits when the drawer mode changes. This is used by the drawer container to\n     * to know when to when the mode changes so it can adapt the margins on the content.\n     */\n    _modeChanged = new Subject();\n    _injector = inject(Injector);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    constructor() {\n      this.openedChange.pipe(takeUntil(this._destroyed)).subscribe(opened => {\n        if (opened) {\n          if (this._doc) {\n            this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n          }\n          this._takeFocus();\n        } else if (this._isFocusWithinDrawer()) {\n          this._restoreFocus(this._openedVia || 'program');\n        }\n      });\n      /**\n       * Listen to `keydown` events outside the zone so that change detection is not run every\n       * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n       * and we don't have close disabled.\n       */\n      this._ngZone.runOutsideAngular(() => {\n        const element = this._elementRef.nativeElement;\n        fromEvent(element, 'keydown').pipe(filter(event => {\n          return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n        }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n          this.close();\n          event.stopPropagation();\n          event.preventDefault();\n        }));\n        this._eventCleanups = [this._renderer.listen(element, 'transitionrun', this._handleTransitionEvent), this._renderer.listen(element, 'transitionend', this._handleTransitionEvent), this._renderer.listen(element, 'transitioncancel', this._handleTransitionEvent)];\n      });\n      this._animationEnd.subscribe(() => {\n        this.openedChange.emit(this.opened);\n      });\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1;\n        // The tabindex attribute should be removed to avoid navigating to that element again\n        this._ngZone.runOutsideAngular(() => {\n          const callback = () => {\n            cleanupBlur();\n            cleanupMousedown();\n            element.removeAttribute('tabindex');\n          };\n          const cleanupBlur = this._renderer.listen(element, 'blur', callback);\n          const cleanupMousedown = this._renderer.listen(element, 'mousedown', callback);\n        });\n      }\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n    _takeFocus() {\n      if (!this._focusTrap) {\n        return;\n      }\n      const element = this._elementRef.nativeElement;\n      // When autoFocus is not on the sidenav, if the element cannot be focused or does\n      // not exist, focus the sidenav itself so the keyboard navigation still works.\n      // We need to check that `focus` is a function due to Universal.\n      switch (this.autoFocus) {\n        case false:\n        case 'dialog':\n          return;\n        case true:\n        case 'first-tabbable':\n          afterNextRender(() => {\n            const hasMovedFocus = this._focusTrap.focusInitialElement();\n            if (!hasMovedFocus && typeof element.focus === 'function') {\n              element.focus();\n            }\n          }, {\n            injector: this._injector\n          });\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n          break;\n        default:\n          this._focusByCssSelector(this.autoFocus);\n          break;\n      }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n    _restoreFocus(focusOrigin) {\n      if (this.autoFocus === 'dialog') {\n        return;\n      }\n      if (this._elementFocusedBeforeDrawerWasOpened) {\n        this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n      } else {\n        this._elementRef.nativeElement.blur();\n      }\n      this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n    _isFocusWithinDrawer() {\n      const activeEl = this._doc.activeElement;\n      return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n    ngAfterViewInit() {\n      this._isAttached = true;\n      // Only update the DOM position when the sidenav is positioned at\n      // the end since we project the sidenav before the content by default.\n      if (this._position === 'end') {\n        this._updatePositionInParent('end');\n      }\n      // Needs to happen after the position is updated\n      // so the focus trap anchors are in the right place.\n      if (this._platform.isBrowser) {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        this._updateFocusTrapState();\n      }\n    }\n    ngOnDestroy() {\n      this._eventCleanups.forEach(cleanup => cleanup());\n      this._focusTrap?.destroy();\n      this._anchor?.remove();\n      this._anchor = null;\n      this._animationStarted.complete();\n      this._animationEnd.complete();\n      this._modeChanged.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    open(openedVia) {\n      return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n    close() {\n      return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n    _closeViaBackdropClick() {\n      // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n      // don't need to check whether focus is currently in the drawer, as clicking on the\n      // backdrop causes blurs the active element.\n      return this._setOpen(/* isOpen */false, /* restoreFocus */true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    toggle(isOpen = !this.opened, openedVia) {\n      // If the focus is currently inside the drawer content and we are closing the drawer,\n      // restore the focus to the initially focused element (when the drawer opened).\n      if (isOpen && openedVia) {\n        this._openedVia = openedVia;\n      }\n      const result = this._setOpen(isOpen, /* restoreFocus */!isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n      if (!isOpen) {\n        this._openedVia = null;\n      }\n      return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n      if (isOpen === this.opened) {\n        return Promise.resolve(isOpen ? 'open' : 'close');\n      }\n      this._opened.set(isOpen);\n      if (this._container?._transitionsEnabled) {\n        // Note: it's importatnt to set this as early as possible,\n        // otherwise the animation can look glitchy in some cases.\n        this._setIsAnimating(true);\n      } else {\n        // Simulate the animation events if animations are disabled.\n        setTimeout(() => {\n          this._animationStarted.next();\n          this._animationEnd.next();\n        });\n      }\n      this._elementRef.nativeElement.classList.toggle('mat-drawer-opened', isOpen);\n      if (!isOpen && restoreFocus) {\n        this._restoreFocus(focusOrigin);\n      }\n      // Needed to ensure that the closing sequence fires off correctly.\n      this._changeDetectorRef.markForCheck();\n      this._updateFocusTrapState();\n      return new Promise(resolve => {\n        this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n      });\n    }\n    /** Toggles whether the drawer is currently animating. */\n    _setIsAnimating(isAnimating) {\n      this._elementRef.nativeElement.classList.toggle('mat-drawer-animating', isAnimating);\n    }\n    _getWidth() {\n      return this._elementRef.nativeElement.offsetWidth || 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n    _updateFocusTrapState() {\n      if (this._focusTrap) {\n        // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n        // sidenav content.\n        this._focusTrap.enabled = !!this._container?.hasBackdrop && this.opened;\n      }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n    _updatePositionInParent(newPosition) {\n      // Don't move the DOM node around on the server, because it can throw off hydration.\n      if (!this._platform.isBrowser) {\n        return;\n      }\n      const element = this._elementRef.nativeElement;\n      const parent = element.parentNode;\n      if (newPosition === 'end') {\n        if (!this._anchor) {\n          this._anchor = this._doc.createComment('mat-drawer-anchor');\n          parent.insertBefore(this._anchor, element);\n        }\n        parent.appendChild(element);\n      } else if (this._anchor) {\n        this._anchor.parentNode.insertBefore(element, this._anchor);\n      }\n    }\n    /** Event handler for animation events. */\n    _handleTransitionEvent = event => {\n      const element = this._elementRef.nativeElement;\n      if (event.target === element) {\n        this._ngZone.run(() => {\n          if (event.type === 'transitionrun') {\n            this._animationStarted.next(event);\n          } else {\n            // Don't toggle the animating state on `transitioncancel` since another animation should\n            // start afterwards. This prevents the drawer from blinking if an animation is interrupted.\n            if (event.type === 'transitionend') {\n              this._setIsAnimating(false);\n            }\n            this._animationEnd.next(event);\n          }\n        });\n      }\n    };\n    static ɵfac = function MatDrawer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDrawer)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawer,\n      selectors: [[\"mat-drawer\"]],\n      viewQuery: function MatDrawer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-drawer\"],\n      hostVars: 12,\n      hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"align\", null)(\"tabIndex\", ctx.mode !== \"side\" ? \"-1\" : null);\n          i0.ɵɵstyleProp(\"visibility\", !ctx._container && !ctx.opened ? \"hidden\" : null);\n          i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\");\n        }\n      },\n      inputs: {\n        position: \"position\",\n        mode: \"mode\",\n        disableClose: \"disableClose\",\n        autoFocus: \"autoFocus\",\n        opened: \"opened\"\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        openedStart: \"openedStart\",\n        _closedStream: \"closed\",\n        closedStart: \"closedStart\",\n        onPositionChanged: \"positionChanged\"\n      },\n      exportAs: [\"matDrawer\"],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"]],\n      template: function MatDrawer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CdkScrollable],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatDrawer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nlet MatDrawerContainer = /*#__PURE__*/(() => {\n  class MatDrawerContainer {\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _element = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationDisabled = _animationsDisabled();\n    _transitionsEnabled = false;\n    /** All drawers in the container. Includes drawers from inside nested containers. */\n    _allDrawers;\n    /** Drawers that belong to this container. */\n    _drawers = new QueryList();\n    _content;\n    _userContent;\n    /** The drawer child with the `start` position. */\n    get start() {\n      return this._start;\n    }\n    /** The drawer child with the `end` position. */\n    get end() {\n      return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n    get autosize() {\n      return this._autosize;\n    }\n    set autosize(value) {\n      this._autosize = coerceBooleanProperty(value);\n    }\n    _autosize = inject(MAT_DRAWER_DEFAULT_AUTOSIZE);\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n    get hasBackdrop() {\n      return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n    }\n    set hasBackdrop(value) {\n      this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    _backdropOverride;\n    /** Event emitted when the drawer backdrop is clicked. */\n    backdropClick = new EventEmitter();\n    /** The drawer at the start/end position, independent of direction. */\n    _start;\n    _end;\n    /**\n     * The drawer at the left/right. When direction changes, these will change as well.\n     * They're used as aliases for the above to set the left/right style properly.\n     * In LTR, _left == _start and _right == _end.\n     * In RTL, _left == _end and _right == _start.\n     */\n    _left;\n    _right;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Emits on every ngDoCheck. Used for debouncing reflows. */\n    _doCheckSubject = new Subject();\n    /**\n     * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n     * drawer is open. We use margin rather than transform even for push mode because transform breaks\n     * fixed position elements inside of the transformed element.\n     */\n    _contentMargins = {\n      left: null,\n      right: null\n    };\n    _contentMarginChanges = new Subject();\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n    get scrollable() {\n      return this._userContent || this._content;\n    }\n    _injector = inject(Injector);\n    constructor() {\n      const platform = inject(Platform);\n      const viewportRuler = inject(ViewportRuler);\n      // If a `Dir` directive exists up the tree, listen direction changes\n      // and update the left/right properties to point to the proper start/end.\n      this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._validateDrawers();\n        this.updateContentMargins();\n      });\n      // Since the minimum width of the sidenav depends on the viewport width,\n      // we need to recompute the margins if the viewport changes.\n      viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n      if (!this._animationDisabled && platform.isBrowser) {\n        this._ngZone.runOutsideAngular(() => {\n          // Enable the animations after a delay in order to skip\n          // the initial transition if a drawer is open by default.\n          setTimeout(() => {\n            this._element.nativeElement.classList.add('mat-drawer-transition');\n            this._transitionsEnabled = true;\n          }, 200);\n        });\n      }\n    }\n    ngAfterContentInit() {\n      this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n        this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n        this._drawers.notifyOnChanges();\n      });\n      this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n        this._validateDrawers();\n        this._drawers.forEach(drawer => {\n          this._watchDrawerToggle(drawer);\n          this._watchDrawerPosition(drawer);\n          this._watchDrawerMode(drawer);\n        });\n        if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n          this.updateContentMargins();\n        }\n        this._changeDetectorRef.markForCheck();\n      });\n      // Avoid hitting the NgZone through the debounce timeout.\n      this._ngZone.runOutsideAngular(() => {\n        this._doCheckSubject.pipe(debounceTime(10),\n        // Arbitrary debounce time, less than a frame at 60fps\n        takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n      });\n    }\n    ngOnDestroy() {\n      this._contentMarginChanges.complete();\n      this._doCheckSubject.complete();\n      this._drawers.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n    open() {\n      this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n    close() {\n      this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n    updateContentMargins() {\n      // 1. For drawers in `over` mode, they don't affect the content.\n      // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n      //    left margin (for left drawer) or right margin (for right the drawer).\n      // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n      //    adding to the left or right margin and simultaneously subtracting the same amount of\n      //    margin from the other side.\n      let left = 0;\n      let right = 0;\n      if (this._left && this._left.opened) {\n        if (this._left.mode == 'side') {\n          left += this._left._getWidth();\n        } else if (this._left.mode == 'push') {\n          const width = this._left._getWidth();\n          left += width;\n          right -= width;\n        }\n      }\n      if (this._right && this._right.opened) {\n        if (this._right.mode == 'side') {\n          right += this._right._getWidth();\n        } else if (this._right.mode == 'push') {\n          const width = this._right._getWidth();\n          right += width;\n          left -= width;\n        }\n      }\n      // If either `right` or `left` is zero, don't set a style to the element. This\n      // allows users to specify a custom size via CSS class in SSR scenarios where the\n      // measured widths will always be zero. Note that we reset to `null` here, rather\n      // than below, in order to ensure that the types in the `if` below are consistent.\n      left = left || null;\n      right = right || null;\n      if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n        this._contentMargins = {\n          left,\n          right\n        };\n        // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n        // to do it only when something changed, otherwise we can end up hitting the zone too often.\n        this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n      }\n    }\n    ngDoCheck() {\n      // If users opted into autosizing, do a check every change detection cycle.\n      if (this._autosize && this._isPushed()) {\n        // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n      }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n    _watchDrawerToggle(drawer) {\n      drawer._animationStarted.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n        this.updateContentMargins();\n        this._changeDetectorRef.markForCheck();\n      });\n      if (drawer.mode !== 'side') {\n        drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n      }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n    _watchDrawerPosition(drawer) {\n      // NOTE: We need to wait for the microtask queue to be empty before validating,\n      // since both drawers may be swapping positions at the same time.\n      drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n        afterNextRender({\n          read: () => this._validateDrawers()\n        }, {\n          injector: this._injector\n        });\n      });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n    _watchDrawerMode(drawer) {\n      drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n        this.updateContentMargins();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n    _setContainerClass(isAdd) {\n      const classList = this._element.nativeElement.classList;\n      const className = 'mat-drawer-container-has-open';\n      if (isAdd) {\n        classList.add(className);\n      } else {\n        classList.remove(className);\n      }\n    }\n    /** Validate the state of the drawer children components. */\n    _validateDrawers() {\n      this._start = this._end = null;\n      // Ensure that we have at most one start and one end drawer.\n      this._drawers.forEach(drawer => {\n        if (drawer.position == 'end') {\n          if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatDuplicatedDrawerError('end');\n          }\n          this._end = drawer;\n        } else {\n          if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatDuplicatedDrawerError('start');\n          }\n          this._start = drawer;\n        }\n      });\n      this._right = this._left = null;\n      // Detect if we're LTR or RTL.\n      if (this._dir && this._dir.value === 'rtl') {\n        this._left = this._end;\n        this._right = this._start;\n      } else {\n        this._left = this._start;\n        this._right = this._end;\n      }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n    _isPushed() {\n      return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n    }\n    _onBackdropClicked() {\n      this.backdropClick.emit();\n      this._closeModalDrawersViaBackdrop();\n    }\n    _closeModalDrawersViaBackdrop() {\n      // Close all open drawers where closing is not disabled and the mode is not `side`.\n      [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n    }\n    _isShowingBackdrop() {\n      return this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start) || this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end);\n    }\n    _isDrawerOpen(drawer) {\n      return drawer != null && drawer.opened;\n    }\n    // Whether argument drawer should have a backdrop when it opens\n    _drawerHasBackdrop(drawer) {\n      if (this._backdropOverride == null) {\n        return !!drawer && drawer.mode !== 'side';\n      }\n      return this._backdropOverride;\n    }\n    static ɵfac = function MatDrawerContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDrawerContainer)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDrawerContainer,\n      selectors: [[\"mat-drawer-container\"]],\n      contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n        }\n      },\n      viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatDrawerContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-drawer-container\"],\n      hostVars: 2,\n      hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n        }\n      },\n      inputs: {\n        autosize: \"autosize\",\n        hasBackdrop: \"hasBackdrop\"\n      },\n      outputs: {\n        backdropClick: \"backdropClick\"\n      },\n      exportAs: [\"matDrawerContainer\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }])],\n      ngContentSelectors: _c3,\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n      template: function MatDrawerContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵconditionalCreate(0, MatDrawerContainer_Conditional_0_Template, 1, 2, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵconditionalCreate(3, MatDrawerContainer_Conditional_3_Template, 2, 0, \"mat-drawer-content\");\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.hasBackdrop ? 0 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(!ctx._content ? 3 : -1);\n        }\n      },\n      dependencies: [MatDrawerContent],\n      styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatDrawerContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSidenavContent = /*#__PURE__*/(() => {\n  class MatSidenavContent extends MatDrawerContent {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatSidenavContent_BaseFactory;\n      return function MatSidenavContent_Factory(__ngFactoryType__) {\n        return (ɵMatSidenavContent_BaseFactory || (ɵMatSidenavContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContent)))(__ngFactoryType__ || MatSidenavContent);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenavContent,\n      selectors: [[\"mat-sidenav-content\"]],\n      hostAttrs: [1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatSidenavContent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSidenavContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSidenav = /*#__PURE__*/(() => {\n  class MatSidenav extends MatDrawer {\n    /** Whether the sidenav is fixed in the viewport. */\n    get fixedInViewport() {\n      return this._fixedInViewport;\n    }\n    set fixedInViewport(value) {\n      this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    _fixedInViewport = false;\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n    get fixedTopGap() {\n      return this._fixedTopGap;\n    }\n    set fixedTopGap(value) {\n      this._fixedTopGap = coerceNumberProperty(value);\n    }\n    _fixedTopGap = 0;\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n    get fixedBottomGap() {\n      return this._fixedBottomGap;\n    }\n    set fixedBottomGap(value) {\n      this._fixedBottomGap = coerceNumberProperty(value);\n    }\n    _fixedBottomGap = 0;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatSidenav_BaseFactory;\n      return function MatSidenav_Factory(__ngFactoryType__) {\n        return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(__ngFactoryType__ || MatSidenav);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenav,\n      selectors: [[\"mat-sidenav\"]],\n      hostAttrs: [1, \"mat-drawer\", \"mat-sidenav\"],\n      hostVars: 16,\n      hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabIndex\", ctx.mode !== \"side\" ? \"-1\" : null)(\"align\", null);\n          i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n          i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n        }\n      },\n      inputs: {\n        fixedInViewport: \"fixedInViewport\",\n        fixedTopGap: \"fixedTopGap\",\n        fixedBottomGap: \"fixedBottomGap\"\n      },\n      exportAs: [\"matSidenav\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatDrawer,\n        useExisting: MatSidenav\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"]],\n      template: function MatSidenav_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CdkScrollable],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSidenav;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSidenavContainer = /*#__PURE__*/(() => {\n  class MatSidenavContainer extends MatDrawerContainer {\n    _allDrawers = undefined;\n    // We need an initializer here to avoid a TS error.\n    _content = undefined;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatSidenavContainer_BaseFactory;\n      return function MatSidenavContainer_Factory(__ngFactoryType__) {\n        return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(__ngFactoryType__ || MatSidenavContainer);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSidenavContainer,\n      selectors: [[\"mat-sidenav-container\"]],\n      contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n      hostVars: 2,\n      hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n        }\n      },\n      exportAs: [\"matSidenavContainer\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }, {\n        provide: MatDrawerContainer,\n        useExisting: MatSidenavContainer\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n      template: function MatSidenavContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c4);\n          i0.ɵɵconditionalCreate(0, MatSidenavContainer_Conditional_0_Template, 1, 2, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵconditionalCreate(3, MatSidenavContainer_Conditional_3_Template, 2, 0, \"mat-sidenav-content\");\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.hasBackdrop ? 0 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(!ctx._content ? 3 : -1);\n        }\n      },\n      dependencies: [MatSidenavContent],\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSidenavContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSidenavModule = /*#__PURE__*/(() => {\n  class MatSidenavModule {\n    static ɵfac = function MatSidenavModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSidenavModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSidenavModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n  return MatSidenavModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDrawerAnimations = {\n  // Represents\n  // trigger('transform', [\n  //   // We remove the `transform` here completely, rather than setting it to zero, because:\n  //   // 1. Having a transform can cause elements with ripples or an animated\n  //   //    transform to shift around in Chrome with an RTL layout (see #10023).\n  //   // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  //   state(\n  //     'open, open-instant',\n  //     style({\n  //       'transform': 'none',\n  //       'visibility': 'visible',\n  //     }),\n  //   ),\n  //   state(\n  //     'void',\n  //     style({\n  //       // Avoids the shadow showing up when closed in SSR.\n  //       'box-shadow': 'none',\n  //       'visibility': 'hidden',\n  //     }),\n  //   ),\n  //   transition('void => open-instant', animate('0ms')),\n  //   transition(\n  //     'void <=> open, open-instant => void',\n  //     animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'),\n  //   ),\n  // ])\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: {\n    type: 7,\n    name: 'transform',\n    definitions: [{\n      type: 0,\n      name: 'open, open-instant',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none',\n          visibility: 'visible'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          'box-shadow': 'none',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => open-instant',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '0ms'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void <=> open, open-instant => void',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '400ms cubic-bezier(0.25, 0.8, 0.25, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };", "map": {"version": 3, "names": ["FocusTrapFactory", "FocusMonitor", "InteractivityChecker", "Directionality", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "Platform", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "CdkScrollableModule", "i0", "InjectionToken", "inject", "ChangeDetectorRef", "ElementRef", "NgZone", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Renderer2", "DOCUMENT", "signal", "EventEmitter", "Injector", "afterNextRender", "Input", "Output", "ViewChild", "QueryList", "ContentChildren", "ContentChild", "NgModule", "Subject", "fromEvent", "merge", "filter", "map", "mapTo", "takeUntil", "take", "startWith", "debounceTime", "_", "_animationsDisabled", "M", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "MatDrawerContainer_Conditional_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatDrawerContainer_Conditional_0_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_onBackdropClicked", "ɵɵelementEnd", "ɵɵclassProp", "_isShowingBackdrop", "MatDrawerContainer_Conditional_3_Template", "ɵɵprojection", "_c4", "_c5", "MatSidenavContainer_Conditional_0_Template", "MatSidenavContainer_Conditional_0_Template_div_click_0_listener", "MatSidenavContainer_Conditional_3_Template", "_c6", "throwMatDuplicatedDrawerError", "position", "Error", "MAT_DRAWER_DEFAULT_AUTOSIZE", "providedIn", "factory", "MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY", "MAT_DRAWER_CONTAINER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_platform", "_changeDetectorRef", "_container", "Mat<PERSON>rawerContainer", "constructor", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "ngAfterContentInit", "_contentMarginChanges", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_shouldBeHidden", "<PERSON><PERSON><PERSON><PERSON>", "start", "end", "mode", "opened", "ɵfac", "MatDrawerContent_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDrawerContent_HostBindings", "ɵɵstyleProp", "_contentMargins", "left", "right", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "template", "MatDrawerContent_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "ngDevMode", "<PERSON><PERSON><PERSON><PERSON>", "_elementRef", "_focusTrapFactory", "_focusMonitor", "_ngZone", "_renderer", "_interactivityC<PERSON>cker", "_doc", "optional", "_focusTrap", "_elementFocusedBeforeDrawerWasOpened", "_eventCleanups", "_isAttached", "_anchor", "_position", "value", "_updatePositionInParent", "onPositionChanged", "emit", "_mode", "_updateFocusTrapState", "_modeChanged", "next", "disableClose", "_disableClose", "autoFocus", "_autoFocus", "_opened", "toggle", "_openedVia", "_animationStarted", "_animationEnd", "openedChange", "_openedStream", "pipe", "o", "openedStart", "undefined", "_closedStream", "closedStart", "_destroyed", "_content", "_injector", "activeElement", "_takeFocus", "_isFocusWithinDrawer", "_restoreFocus", "runOutsideAngular", "element", "nativeElement", "event", "keyCode", "run", "close", "stopPropagation", "preventDefault", "listen", "_handleTransitionEvent", "_forceFocus", "options", "isFocusable", "tabIndex", "callback", "cleanupBlur", "cleanupMousedown", "removeAttribute", "focus", "_focusByCssSelector", "selector", "elementToFocus", "querySelector", "hasMovedFocus", "focusInitialElement", "injector", "<PERSON><PERSON><PERSON><PERSON>", "focusVia", "blur", "activeEl", "contains", "ngAfterViewInit", "create", "ngOnDestroy", "for<PERSON>ach", "cleanup", "destroy", "remove", "complete", "open", "openedVia", "_closeViaBackdropClick", "_setOpen", "isOpen", "result", "restoreFocus", "Promise", "resolve", "set", "_transitionsEnabled", "_setIsAnimating", "setTimeout", "classList", "isAnimating", "_getWidth", "offsetWidth", "enabled", "hasBackdrop", "newPosition", "parent", "parentNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "target", "MatDrawer_Factory", "viewQuery", "MatDrawer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "MatDrawer_HostBindings", "ɵɵattribute", "inputs", "outputs", "exportAs", "consts", "MatDrawer_Template", "dependencies", "_dir", "_element", "_animationDisabled", "_allDrawers", "_drawers", "_userContent", "_start", "_end", "autosize", "_autosize", "_drawerHasBackdrop", "_backdropOverride", "backdropClick", "_left", "_right", "_doCheckSubject", "scrollable", "platform", "viewportRuler", "change", "_validateDrawers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "changes", "drawer", "reset", "item", "notifyOn<PERSON><PERSON>es", "_watchDrawerToggle", "_watchDrawerPosition", "_watchDrawerMode", "length", "_isDrawerOpen", "width", "ngDoCheck", "_isPushed", "_setContainerClass", "read", "isAdd", "className", "_closeModalDrawersViaBackdrop", "MatDrawerContainer_Factory", "contentQueries", "MatDrawerContainer_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatDrawerContainer_Query", "MatDrawerContainer_HostBindings", "MatDrawerContainer_Template", "ɵɵconditionalCreate", "ɵɵconditional", "ɵɵadvance", "styles", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵMatSidenavContent_BaseFactory", "MatSidenavContent_Factory", "ɵɵgetInheritedFactory", "MatSidenavContent_Template", "<PERSON><PERSON><PERSON><PERSON>", "fixedInViewport", "_fixedInViewport", "fixedTopGap", "_fixedTopGap", "fixedBottomGap", "_fixedBottomGap", "ɵMatSidenav_BaseFactory", "MatSidenav_Factory", "MatSidenav_HostBindings", "MatSidenav_Template", "Mat<PERSON>idenav<PERSON><PERSON>r", "ɵMatSidenavContainer_BaseFactory", "MatSidenavContainer_Factory", "MatSidenavContainer_ContentQueries", "MatSidenavContainer_HostBindings", "MatSidenavContainer_Template", "MatSidenavModule", "MatSidenavModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "matDrawerAnimations", "transformDrawer", "name", "definitions", "transform", "visibility", "offset", "expr", "animation", "timings"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/sidenav.mjs"], "sourcesContent": ["import { FocusTrapFactory, FocusMonitor, InteractivityChecker } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Platform } from '@angular/cdk/platform';\nimport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, NgZone, Component, ChangeDetectionStrategy, ViewEncapsulation, Renderer2, DOCUMENT, signal, EventEmitter, Injector, afterNextRender, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, take, startWith, debounceTime } from 'rxjs/operators';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n    throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n    providedIn: 'root',\n    factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n    return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n    _platform = inject(Platform);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _container = inject(MatDrawerContainer);\n    constructor() {\n        const elementRef = inject(ElementRef);\n        const scrollDispatcher = inject(ScrollDispatcher);\n        const ngZone = inject(NgZone);\n        super(elementRef, scrollDispatcher, ngZone);\n    }\n    ngAfterContentInit() {\n        this._container._contentMarginChanges.subscribe(() => {\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Determines whether the content element should be hidden from the user. */\n    _shouldBeHidden() {\n        // In some modes the content is pushed based on the width of the opened sidenavs, however on\n        // the server we can't measure the sidenav so the margin is always zero. This can cause the\n        // content to jump around when it's rendered on the server and hydrated on the client. We\n        // avoid it by hiding the content on the initial render and then showing it once the sidenav\n        // has been measured on the client.\n        if (this._platform.isBrowser) {\n            return false;\n        }\n        const { start, end } = this._container;\n        return ((start != null && start.mode !== 'over' && start.opened) ||\n            (end != null && end.mode !== 'over' && end.opened));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawerContent, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDrawerContent, isStandalone: true, selector: \"mat-drawer-content\", host: { properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\", \"class.mat-drawer-content-hidden\": \"_shouldBeHidden()\" }, classAttribute: \"mat-drawer-content\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useExisting: MatDrawerContent,\n            },\n        ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawerContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-drawer-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                        '[class.mat-drawer-content-hidden]': '_shouldBeHidden()',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatDrawerContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: () => [] });\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _focusMonitor = inject(FocusMonitor);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _interactivityChecker = inject(InteractivityChecker);\n    _doc = inject(DOCUMENT, { optional: true });\n    _container = inject(MAT_DRAWER_CONTAINER, { optional: true });\n    _focusTrap = null;\n    _elementFocusedBeforeDrawerWasOpened = null;\n    _eventCleanups;\n    /** Whether the view of the component has been attached. */\n    _isAttached;\n    /** Anchor node used to restore the drawer to its initial position. */\n    _anchor;\n    /** The side that the drawer is attached to. */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        // Make sure we have a valid value.\n        value = value === 'end' ? 'end' : 'start';\n        if (value !== this._position) {\n            // Static inputs in Ivy are set before the element is in the DOM.\n            if (this._isAttached) {\n                this._updatePositionInParent(value);\n            }\n            this._position = value;\n            this.onPositionChanged.emit();\n        }\n    }\n    _position = 'start';\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        this._mode = value;\n        this._updateFocusTrapState();\n        this._modeChanged.next();\n    }\n    _mode = 'over';\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n    get disableClose() {\n        return this._disableClose;\n    }\n    set disableClose(value) {\n        this._disableClose = coerceBooleanProperty(value);\n    }\n    _disableClose = false;\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n    get autoFocus() {\n        const value = this._autoFocus;\n        // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n        // because we don't know how the sidenav is being used, but in some cases it still makes\n        // sense to do it. The consumer can explicitly set `autoFocus`.\n        if (value == null) {\n            if (this.mode === 'side') {\n                return 'dialog';\n            }\n            else {\n                return 'first-tabbable';\n            }\n        }\n        return value;\n    }\n    set autoFocus(value) {\n        if (value === 'true' || value === 'false' || value == null) {\n            value = coerceBooleanProperty(value);\n        }\n        this._autoFocus = value;\n    }\n    _autoFocus;\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n    get opened() {\n        return this._opened();\n    }\n    set opened(value) {\n        this.toggle(coerceBooleanProperty(value));\n    }\n    _opened = signal(false);\n    /** How the sidenav was opened (keypress, mouse click etc.) */\n    _openedVia;\n    /** Emits whenever the drawer has started animating. */\n    _animationStarted = new Subject();\n    /** Emits whenever the drawer is done animating. */\n    _animationEnd = new Subject();\n    /** Event emitted when the drawer open state is changed. */\n    openedChange = \n    // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter(/* isAsync */ true);\n    /** Event emitted when the drawer has been opened. */\n    _openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n    /** Event emitted when the drawer has started opening. */\n    openedStart = this._animationStarted.pipe(filter(() => this.opened), mapTo(undefined));\n    /** Event emitted when the drawer has been closed. */\n    _closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n    /** Event emitted when the drawer has started closing. */\n    closedStart = this._animationStarted.pipe(filter(() => !this.opened), mapTo(undefined));\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Event emitted when the drawer's position changes. */\n    // tslint:disable-next-line:no-output-on-prefix\n    onPositionChanged = new EventEmitter();\n    /** Reference to the inner element that contains all the content. */\n    _content;\n    /**\n     * An observable that emits when the drawer mode changes. This is used by the drawer container to\n     * to know when to when the mode changes so it can adapt the margins on the content.\n     */\n    _modeChanged = new Subject();\n    _injector = inject(Injector);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    constructor() {\n        this.openedChange.pipe(takeUntil(this._destroyed)).subscribe((opened) => {\n            if (opened) {\n                if (this._doc) {\n                    this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n                }\n                this._takeFocus();\n            }\n            else if (this._isFocusWithinDrawer()) {\n                this._restoreFocus(this._openedVia || 'program');\n            }\n        });\n        /**\n         * Listen to `keydown` events outside the zone so that change detection is not run every\n         * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n         * and we don't have close disabled.\n         */\n        this._ngZone.runOutsideAngular(() => {\n            const element = this._elementRef.nativeElement;\n            fromEvent(element, 'keydown')\n                .pipe(filter(event => {\n                return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n            }), takeUntil(this._destroyed))\n                .subscribe(event => this._ngZone.run(() => {\n                this.close();\n                event.stopPropagation();\n                event.preventDefault();\n            }));\n            this._eventCleanups = [\n                this._renderer.listen(element, 'transitionrun', this._handleTransitionEvent),\n                this._renderer.listen(element, 'transitionend', this._handleTransitionEvent),\n                this._renderer.listen(element, 'transitioncancel', this._handleTransitionEvent),\n            ];\n        });\n        this._animationEnd.subscribe(() => {\n            this.openedChange.emit(this.opened);\n        });\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    cleanupBlur();\n                    cleanupMousedown();\n                    element.removeAttribute('tabindex');\n                };\n                const cleanupBlur = this._renderer.listen(element, 'blur', callback);\n                const cleanupMousedown = this._renderer.listen(element, 'mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n    _takeFocus() {\n        if (!this._focusTrap) {\n            return;\n        }\n        const element = this._elementRef.nativeElement;\n        // When autoFocus is not on the sidenav, if the element cannot be focused or does\n        // not exist, focus the sidenav itself so the keyboard navigation still works.\n        // We need to check that `focus` is a function due to Universal.\n        switch (this.autoFocus) {\n            case false:\n            case 'dialog':\n                return;\n            case true:\n            case 'first-tabbable':\n                afterNextRender(() => {\n                    const hasMovedFocus = this._focusTrap.focusInitialElement();\n                    if (!hasMovedFocus && typeof element.focus === 'function') {\n                        element.focus();\n                    }\n                }, { injector: this._injector });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this.autoFocus);\n                break;\n        }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n    _restoreFocus(focusOrigin) {\n        if (this.autoFocus === 'dialog') {\n            return;\n        }\n        if (this._elementFocusedBeforeDrawerWasOpened) {\n            this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n        }\n        else {\n            this._elementRef.nativeElement.blur();\n        }\n        this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n    _isFocusWithinDrawer() {\n        const activeEl = this._doc.activeElement;\n        return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n    ngAfterViewInit() {\n        this._isAttached = true;\n        // Only update the DOM position when the sidenav is positioned at\n        // the end since we project the sidenav before the content by default.\n        if (this._position === 'end') {\n            this._updatePositionInParent('end');\n        }\n        // Needs to happen after the position is updated\n        // so the focus trap anchors are in the right place.\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            this._updateFocusTrapState();\n        }\n    }\n    ngOnDestroy() {\n        this._eventCleanups.forEach(cleanup => cleanup());\n        this._focusTrap?.destroy();\n        this._anchor?.remove();\n        this._anchor = null;\n        this._animationStarted.complete();\n        this._animationEnd.complete();\n        this._modeChanged.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    open(openedVia) {\n        return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n    close() {\n        return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n    _closeViaBackdropClick() {\n        // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n        // don't need to check whether focus is currently in the drawer, as clicking on the\n        // backdrop causes blurs the active element.\n        return this._setOpen(/* isOpen */ false, /* restoreFocus */ true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    toggle(isOpen = !this.opened, openedVia) {\n        // If the focus is currently inside the drawer content and we are closing the drawer,\n        // restore the focus to the initially focused element (when the drawer opened).\n        if (isOpen && openedVia) {\n            this._openedVia = openedVia;\n        }\n        const result = this._setOpen(isOpen, \n        /* restoreFocus */ !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n        if (!isOpen) {\n            this._openedVia = null;\n        }\n        return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n        if (isOpen === this.opened) {\n            return Promise.resolve(isOpen ? 'open' : 'close');\n        }\n        this._opened.set(isOpen);\n        if (this._container?._transitionsEnabled) {\n            // Note: it's importatnt to set this as early as possible,\n            // otherwise the animation can look glitchy in some cases.\n            this._setIsAnimating(true);\n        }\n        else {\n            // Simulate the animation events if animations are disabled.\n            setTimeout(() => {\n                this._animationStarted.next();\n                this._animationEnd.next();\n            });\n        }\n        this._elementRef.nativeElement.classList.toggle('mat-drawer-opened', isOpen);\n        if (!isOpen && restoreFocus) {\n            this._restoreFocus(focusOrigin);\n        }\n        // Needed to ensure that the closing sequence fires off correctly.\n        this._changeDetectorRef.markForCheck();\n        this._updateFocusTrapState();\n        return new Promise(resolve => {\n            this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n        });\n    }\n    /** Toggles whether the drawer is currently animating. */\n    _setIsAnimating(isAnimating) {\n        this._elementRef.nativeElement.classList.toggle('mat-drawer-animating', isAnimating);\n    }\n    _getWidth() {\n        return this._elementRef.nativeElement.offsetWidth || 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n    _updateFocusTrapState() {\n        if (this._focusTrap) {\n            // Trap focus only if the backdrop is enabled. Otherwise, allow end user to interact with the\n            // sidenav content.\n            this._focusTrap.enabled = !!this._container?.hasBackdrop && this.opened;\n        }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n    _updatePositionInParent(newPosition) {\n        // Don't move the DOM node around on the server, because it can throw off hydration.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const element = this._elementRef.nativeElement;\n        const parent = element.parentNode;\n        if (newPosition === 'end') {\n            if (!this._anchor) {\n                this._anchor = this._doc.createComment('mat-drawer-anchor');\n                parent.insertBefore(this._anchor, element);\n            }\n            parent.appendChild(element);\n        }\n        else if (this._anchor) {\n            this._anchor.parentNode.insertBefore(element, this._anchor);\n        }\n    }\n    /** Event handler for animation events. */\n    _handleTransitionEvent = (event) => {\n        const element = this._elementRef.nativeElement;\n        if (event.target === element) {\n            this._ngZone.run(() => {\n                if (event.type === 'transitionrun') {\n                    this._animationStarted.next(event);\n                }\n                else {\n                    // Don't toggle the animating state on `transitioncancel` since another animation should\n                    // start afterwards. This prevents the drawer from blinking if an animation is interrupted.\n                    if (event.type === 'transitionend') {\n                        this._setIsAnimating(false);\n                    }\n                    this._animationEnd.next(event);\n                }\n            });\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDrawer, isStandalone: true, selector: \"mat-drawer\", inputs: { position: \"position\", mode: \"mode\", disableClose: \"disableClose\", autoFocus: \"autoFocus\", opened: \"opened\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", openedStart: \"openedStart\", _closedStream: \"closed\", closedStart: \"closedStart\", onPositionChanged: \"positionChanged\" }, host: { properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"style.visibility\": \"(!_container && !opened) ? \\\"hidden\\\" : null\", \"attr.tabIndex\": \"(mode !== \\\"side\\\") ? \\\"-1\\\" : null\" }, classAttribute: \"mat-drawer\" }, viewQueries: [{ propertyName: \"_content\", first: true, predicate: [\"content\"], descendants: true }], exportAs: [\"matDrawer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer', exportAs: 'matDrawer', host: {\n                        'class': 'mat-drawer',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        // The styles that render the sidenav off-screen come from the drawer container. Prior to #30235\n                        // this was also done by the animations module which some internal tests seem to depend on.\n                        // Simulate it by toggling the `hidden` attribute instead.\n                        '[style.visibility]': '(!_container && !opened) ? \"hidden\" : null',\n                        // The sidenav container should not be focused on when used in side mode. See b/286459024 for\n                        // reference. Updates tabIndex of drawer/container to default to null if in side mode.\n                        '[attr.tabIndex]': '(mode !== \"side\") ? \"-1\" : null',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [CdkScrollable], template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { position: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], disableClose: [{\n                type: Input\n            }], autoFocus: [{\n                type: Input\n            }], opened: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], openedStart: [{\n                type: Output\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], closedStart: [{\n                type: Output\n            }], onPositionChanged: [{\n                type: Output,\n                args: ['positionChanged']\n            }], _content: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n    _dir = inject(Directionality, { optional: true });\n    _element = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationDisabled = _animationsDisabled();\n    _transitionsEnabled = false;\n    /** All drawers in the container. Includes drawers from inside nested containers. */\n    _allDrawers;\n    /** Drawers that belong to this container. */\n    _drawers = new QueryList();\n    _content;\n    _userContent;\n    /** The drawer child with the `start` position. */\n    get start() {\n        return this._start;\n    }\n    /** The drawer child with the `end` position. */\n    get end() {\n        return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n    get autosize() {\n        return this._autosize;\n    }\n    set autosize(value) {\n        this._autosize = coerceBooleanProperty(value);\n    }\n    _autosize = inject(MAT_DRAWER_DEFAULT_AUTOSIZE);\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n    get hasBackdrop() {\n        return this._drawerHasBackdrop(this._start) || this._drawerHasBackdrop(this._end);\n    }\n    set hasBackdrop(value) {\n        this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    _backdropOverride;\n    /** Event emitted when the drawer backdrop is clicked. */\n    backdropClick = new EventEmitter();\n    /** The drawer at the start/end position, independent of direction. */\n    _start;\n    _end;\n    /**\n     * The drawer at the left/right. When direction changes, these will change as well.\n     * They're used as aliases for the above to set the left/right style properly.\n     * In LTR, _left == _start and _right == _end.\n     * In RTL, _left == _end and _right == _start.\n     */\n    _left;\n    _right;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Emits on every ngDoCheck. Used for debouncing reflows. */\n    _doCheckSubject = new Subject();\n    /**\n     * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n     * drawer is open. We use margin rather than transform even for push mode because transform breaks\n     * fixed position elements inside of the transformed element.\n     */\n    _contentMargins = { left: null, right: null };\n    _contentMarginChanges = new Subject();\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n    get scrollable() {\n        return this._userContent || this._content;\n    }\n    _injector = inject(Injector);\n    constructor() {\n        const platform = inject(Platform);\n        const viewportRuler = inject(ViewportRuler);\n        // If a `Dir` directive exists up the tree, listen direction changes\n        // and update the left/right properties to point to the proper start/end.\n        this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._validateDrawers();\n            this.updateContentMargins();\n        });\n        // Since the minimum width of the sidenav depends on the viewport width,\n        // we need to recompute the margins if the viewport changes.\n        viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateContentMargins());\n        if (!this._animationDisabled && platform.isBrowser) {\n            this._ngZone.runOutsideAngular(() => {\n                // Enable the animations after a delay in order to skip\n                // the initial transition if a drawer is open by default.\n                setTimeout(() => {\n                    this._element.nativeElement.classList.add('mat-drawer-transition');\n                    this._transitionsEnabled = true;\n                }, 200);\n            });\n        }\n    }\n    ngAfterContentInit() {\n        this._allDrawers.changes\n            .pipe(startWith(this._allDrawers), takeUntil(this._destroyed))\n            .subscribe((drawer) => {\n            this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n            this._drawers.notifyOnChanges();\n        });\n        this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n            this._validateDrawers();\n            this._drawers.forEach((drawer) => {\n                this._watchDrawerToggle(drawer);\n                this._watchDrawerPosition(drawer);\n                this._watchDrawerMode(drawer);\n            });\n            if (!this._drawers.length ||\n                this._isDrawerOpen(this._start) ||\n                this._isDrawerOpen(this._end)) {\n                this.updateContentMargins();\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n        // Avoid hitting the NgZone through the debounce timeout.\n        this._ngZone.runOutsideAngular(() => {\n            this._doCheckSubject\n                .pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n            takeUntil(this._destroyed))\n                .subscribe(() => this.updateContentMargins());\n        });\n    }\n    ngOnDestroy() {\n        this._contentMarginChanges.complete();\n        this._doCheckSubject.complete();\n        this._drawers.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n    open() {\n        this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n    close() {\n        this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n    updateContentMargins() {\n        // 1. For drawers in `over` mode, they don't affect the content.\n        // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n        //    left margin (for left drawer) or right margin (for right the drawer).\n        // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n        //    adding to the left or right margin and simultaneously subtracting the same amount of\n        //    margin from the other side.\n        let left = 0;\n        let right = 0;\n        if (this._left && this._left.opened) {\n            if (this._left.mode == 'side') {\n                left += this._left._getWidth();\n            }\n            else if (this._left.mode == 'push') {\n                const width = this._left._getWidth();\n                left += width;\n                right -= width;\n            }\n        }\n        if (this._right && this._right.opened) {\n            if (this._right.mode == 'side') {\n                right += this._right._getWidth();\n            }\n            else if (this._right.mode == 'push') {\n                const width = this._right._getWidth();\n                right += width;\n                left -= width;\n            }\n        }\n        // If either `right` or `left` is zero, don't set a style to the element. This\n        // allows users to specify a custom size via CSS class in SSR scenarios where the\n        // measured widths will always be zero. Note that we reset to `null` here, rather\n        // than below, in order to ensure that the types in the `if` below are consistent.\n        left = left || null;\n        right = right || null;\n        if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n            this._contentMargins = { left, right };\n            // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n            // to do it only when something changed, otherwise we can end up hitting the zone too often.\n            this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n        }\n    }\n    ngDoCheck() {\n        // If users opted into autosizing, do a check every change detection cycle.\n        if (this._autosize && this._isPushed()) {\n            // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n            this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n        }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n    _watchDrawerToggle(drawer) {\n        drawer._animationStarted.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n            this.updateContentMargins();\n            this._changeDetectorRef.markForCheck();\n        });\n        if (drawer.mode !== 'side') {\n            drawer.openedChange\n                .pipe(takeUntil(this._drawers.changes))\n                .subscribe(() => this._setContainerClass(drawer.opened));\n        }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n    _watchDrawerPosition(drawer) {\n        // NOTE: We need to wait for the microtask queue to be empty before validating,\n        // since both drawers may be swapping positions at the same time.\n        drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n            afterNextRender({ read: () => this._validateDrawers() }, { injector: this._injector });\n        });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n    _watchDrawerMode(drawer) {\n        drawer._modeChanged\n            .pipe(takeUntil(merge(this._drawers.changes, this._destroyed)))\n            .subscribe(() => {\n            this.updateContentMargins();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n    _setContainerClass(isAdd) {\n        const classList = this._element.nativeElement.classList;\n        const className = 'mat-drawer-container-has-open';\n        if (isAdd) {\n            classList.add(className);\n        }\n        else {\n            classList.remove(className);\n        }\n    }\n    /** Validate the state of the drawer children components. */\n    _validateDrawers() {\n        this._start = this._end = null;\n        // Ensure that we have at most one start and one end drawer.\n        this._drawers.forEach(drawer => {\n            if (drawer.position == 'end') {\n                if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('end');\n                }\n                this._end = drawer;\n            }\n            else {\n                if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('start');\n                }\n                this._start = drawer;\n            }\n        });\n        this._right = this._left = null;\n        // Detect if we're LTR or RTL.\n        if (this._dir && this._dir.value === 'rtl') {\n            this._left = this._end;\n            this._right = this._start;\n        }\n        else {\n            this._left = this._start;\n            this._right = this._end;\n        }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n    _isPushed() {\n        return ((this._isDrawerOpen(this._start) && this._start.mode != 'over') ||\n            (this._isDrawerOpen(this._end) && this._end.mode != 'over'));\n    }\n    _onBackdropClicked() {\n        this.backdropClick.emit();\n        this._closeModalDrawersViaBackdrop();\n    }\n    _closeModalDrawersViaBackdrop() {\n        // Close all open drawers where closing is not disabled and the mode is not `side`.\n        [this._start, this._end]\n            .filter(drawer => drawer && !drawer.disableClose && this._drawerHasBackdrop(drawer))\n            .forEach(drawer => drawer._closeViaBackdropClick());\n    }\n    _isShowingBackdrop() {\n        return ((this._isDrawerOpen(this._start) && this._drawerHasBackdrop(this._start)) ||\n            (this._isDrawerOpen(this._end) && this._drawerHasBackdrop(this._end)));\n    }\n    _isDrawerOpen(drawer) {\n        return drawer != null && drawer.opened;\n    }\n    // Whether argument drawer should have a backdrop when it opens\n    _drawerHasBackdrop(drawer) {\n        if (this._backdropOverride == null) {\n            return !!drawer && drawer.mode !== 'side';\n        }\n        return this._backdropOverride;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawerContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatDrawerContainer, isStandalone: true, selector: \"mat-drawer-container\", inputs: { autosize: \"autosize\", hasBackdrop: \"hasBackdrop\" }, outputs: { backdropClick: \"backdropClick\" }, host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container\" }, providers: [\n            {\n                provide: MAT_DRAWER_CONTAINER,\n                useExisting: MatDrawerContainer,\n            },\n        ], queries: [{ propertyName: \"_content\", first: true, predicate: MatDrawerContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatDrawer, descendants: true }], viewQueries: [{ propertyName: \"_userContent\", first: true, predicate: MatDrawerContent, descendants: true }], exportAs: [\"matDrawerContainer\"], ngImport: i0, template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-drawer-content>\\n    <ng-content></ng-content>\\n  </mat-drawer-content>\\n}\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"], dependencies: [{ kind: \"component\", type: MatDrawerContent, selector: \"mat-drawer-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDrawerContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer-container', exportAs: 'matDrawerContainer', host: {\n                        'class': 'mat-drawer-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatDrawerContainer,\n                        },\n                    ], imports: [MatDrawerContent], template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-drawer-content>\\n    <ng-content></ng-content>\\n  </mat-drawer-content>\\n}\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatDrawer, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatDrawerContent]\n            }], _userContent: [{\n                type: ViewChild,\n                args: [MatDrawerContent]\n            }], autosize: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], backdropClick: [{\n                type: Output\n            }] } });\n\nclass MatSidenavContent extends MatDrawerContent {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavContent, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSidenavContent, isStandalone: true, selector: \"mat-sidenav-content\", host: { classAttribute: \"mat-drawer-content mat-sidenav-content\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useExisting: MatSidenavContent,\n            },\n        ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-sidenav-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content mat-sidenav-content',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatSidenavContent,\n                        },\n                    ],\n                }]\n        }] });\nclass MatSidenav extends MatDrawer {\n    /** Whether the sidenav is fixed in the viewport. */\n    get fixedInViewport() {\n        return this._fixedInViewport;\n    }\n    set fixedInViewport(value) {\n        this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    _fixedInViewport = false;\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n    get fixedTopGap() {\n        return this._fixedTopGap;\n    }\n    set fixedTopGap(value) {\n        this._fixedTopGap = coerceNumberProperty(value);\n    }\n    _fixedTopGap = 0;\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n    get fixedBottomGap() {\n        return this._fixedBottomGap;\n    }\n    set fixedBottomGap(value) {\n        this._fixedBottomGap = coerceNumberProperty(value);\n    }\n    _fixedBottomGap = 0;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenav, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSidenav, isStandalone: true, selector: \"mat-sidenav\", inputs: { fixedInViewport: \"fixedInViewport\", fixedTopGap: \"fixedTopGap\", fixedBottomGap: \"fixedBottomGap\" }, host: { properties: { \"attr.tabIndex\": \"(mode !== \\\"side\\\") ? \\\"-1\\\" : null\", \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-sidenav-fixed\": \"fixedInViewport\", \"style.top.px\": \"fixedInViewport ? fixedTopGap : null\", \"style.bottom.px\": \"fixedInViewport ? fixedBottomGap : null\" }, classAttribute: \"mat-drawer mat-sidenav\" }, providers: [{ provide: MatDrawer, useExisting: MatSidenav }], exportAs: [\"matSidenav\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenav, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav', exportAs: 'matSidenav', host: {\n                        'class': 'mat-drawer mat-sidenav',\n                        // The sidenav container should not be focused on when used in side mode. See b/286459024 for\n                        // reference. Updates tabIndex of drawer/container to default to null if in side mode.\n                        '[attr.tabIndex]': '(mode !== \"side\") ? \"-1\" : null',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n                        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n                        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [CdkScrollable], providers: [{ provide: MatDrawer, useExisting: MatSidenav }], template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], propDecorators: { fixedInViewport: [{\n                type: Input\n            }], fixedTopGap: [{\n                type: Input\n            }], fixedBottomGap: [{\n                type: Input\n            }] } });\nclass MatSidenavContainer extends MatDrawerContainer {\n    _allDrawers = undefined;\n    // We need an initializer here to avoid a TS error.\n    _content = undefined;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatSidenavContainer, isStandalone: true, selector: \"mat-sidenav-container\", host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container mat-sidenav-container\" }, providers: [\n            {\n                provide: MAT_DRAWER_CONTAINER,\n                useExisting: MatSidenavContainer,\n            },\n            {\n                provide: MatDrawerContainer,\n                useExisting: MatSidenavContainer,\n            },\n        ], queries: [{ propertyName: \"_content\", first: true, predicate: MatSidenavContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatSidenav, descendants: true }], exportAs: [\"matSidenavContainer\"], usesInheritance: true, ngImport: i0, template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-sidenav-content>\\n    <ng-content></ng-content>\\n  </mat-sidenav-content>\\n}\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"], dependencies: [{ kind: \"component\", type: MatSidenavContent, selector: \"mat-sidenav-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav-container', exportAs: 'matSidenavContainer', host: {\n                        'class': 'mat-drawer-container mat-sidenav-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatSidenavContainer,\n                        },\n                        {\n                            provide: MatDrawerContainer,\n                            useExisting: MatSidenavContainer,\n                        },\n                    ], imports: [MatSidenavContent], template: \"@if (hasBackdrop) {\\n  <div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\"\\n       [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n}\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n\\n@if (!_content) {\\n  <mat-sidenav-content>\\n    <ng-content></ng-content>\\n  </mat-sidenav-content>\\n}\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;color:var(--mat-sidenav-content-text-color, var(--mat-sys-on-background));background-color:var(--mat-sidenav-content-background-color, var(--mat-sys-background));box-sizing:border-box;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible;background-color:var(--mat-sidenav-scrim-color, color-mix(in srgb, var(--mat-sys-neutral-variant20) 40%, transparent))}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}@media(forced-colors: active){.mat-drawer-backdrop{opacity:.5}}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-content.mat-drawer-content-hidden{opacity:0}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;color:var(--mat-sidenav-container-text-color, var(--mat-sys-on-surface-variant));box-shadow:var(--mat-sidenav-container-elevation-shadow, none);background-color:var(--mat-sidenav-container-background-color, var(--mat-sys-surface));border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));width:var(--mat-sidenav-container-width, 360px);display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}@media(forced-colors: active){.mat-drawer,[dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}}@media(forced-colors: active){[dir=rtl] .mat-drawer,.mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0);border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl] .mat-drawer{border-top-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-left-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-right-radius:0;border-bottom-right-radius:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{border-top-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-bottom-right-radius:var(--mat-sidenav-container-shape, var(--mat-sys-corner-large));border-top-left-radius:0;border-bottom-left-radius:0;left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer-transition .mat-drawer{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating){visibility:hidden;box-shadow:none}.mat-drawer:not(.mat-drawer-opened):not(.mat-drawer-animating) .mat-drawer-inner-container{display:none}.mat-drawer.mat-drawer-opened.mat-drawer-opened{transform:none}.mat-drawer-side{box-shadow:none;border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid}.mat-drawer-side.mat-drawer-end{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side{border-left-color:var(--mat-sidenav-container-divider-color, transparent);border-left-width:1px;border-left-style:solid;border-right:none}[dir=rtl] .mat-drawer-side.mat-drawer-end{border-right-color:var(--mat-sidenav-container-divider-color, transparent);border-right-width:1px;border-right-style:solid;border-left:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto}.mat-sidenav-fixed{position:fixed}\\n\"] }]\n        }], propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatSidenav, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatSidenavContent]\n            }] } });\n\nclass MatSidenavModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavModule, imports: [MatCommonModule,\n            CdkScrollableModule,\n            MatDrawer,\n            MatDrawerContainer,\n            MatDrawerContent,\n            MatSidenav,\n            MatSidenavContainer,\n            MatSidenavContent], exports: [CdkScrollableModule,\n            MatCommonModule,\n            MatDrawer,\n            MatDrawerContainer,\n            MatDrawerContent,\n            MatSidenav,\n            MatSidenavContainer,\n            MatSidenavContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavModule, imports: [MatCommonModule,\n            CdkScrollableModule, CdkScrollableModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSidenavModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CdkScrollableModule,\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatCommonModule,\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matDrawerAnimations = {\n    // Represents\n    // trigger('transform', [\n    //   // We remove the `transform` here completely, rather than setting it to zero, because:\n    //   // 1. Having a transform can cause elements with ripples or an animated\n    //   //    transform to shift around in Chrome with an RTL layout (see #10023).\n    //   // 2. 3d transforms causes text to appear blurry on IE and Edge.\n    //   state(\n    //     'open, open-instant',\n    //     style({\n    //       'transform': 'none',\n    //       'visibility': 'visible',\n    //     }),\n    //   ),\n    //   state(\n    //     'void',\n    //     style({\n    //       // Avoids the shadow showing up when closed in SSR.\n    //       'box-shadow': 'none',\n    //       'visibility': 'hidden',\n    //     }),\n    //   ),\n    //   transition('void => open-instant', animate('0ms')),\n    //   transition(\n    //     'void <=> open, open-instant => void',\n    //     animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'),\n    //   ),\n    // ])\n    /** Animation that slides a drawer in and out. */\n    transformDrawer: {\n        type: 7,\n        name: 'transform',\n        definitions: [\n            {\n                type: 0,\n                name: 'open, open-instant',\n                styles: {\n                    type: 6,\n                    styles: { transform: 'none', visibility: 'visible' },\n                    offset: null,\n                },\n            },\n            {\n                type: 0,\n                name: 'void',\n                styles: {\n                    type: 6,\n                    styles: { 'box-shadow': 'none', visibility: 'hidden' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => open-instant',\n                animation: { type: 4, styles: null, timings: '0ms' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: 'void <=> open, open-instant => void',\n                animation: {\n                    type: 4,\n                    styles: null,\n                    timings: '400ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,mBAAmB;AACxF,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC5G,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxS,SAASC,OAAO,EAAEC,SAAS,EAAEC,KAAK,QAAQ,MAAM;AAChD,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAC7F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAsD6F3C,EAAE,CAAA4C,gBAAA;IAAF5C,EAAE,CAAA6C,cAAA,YAiyByZ,CAAC;IAjyB5Z7C,EAAE,CAAA8C,UAAA,mBAAAC,+DAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAiyByUF,MAAA,CAAAG,kBAAA,CAAmB,CAAC;IAAA,CAAC,CAAC;IAjyBjWpD,EAAE,CAAAqD,YAAA,CAiyB+Z,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAjyBlajD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAsD,WAAA,qBAAAL,MAAA,CAAAM,kBAAA,EAiyBwZ,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjyB3ZzC,EAAE,CAAA6C,cAAA,wBAiyB+jB,CAAC;IAjyBlkB7C,EAAE,CAAAyD,YAAA,KAiyB8lB,CAAC;IAjyBjmBzD,EAAE,CAAAqD,YAAA,CAiyBunB,CAAC;EAAA;AAAA;AAAA,MAAAK,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,2CAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAjyB1nB3C,EAAE,CAAA4C,gBAAA;IAAF5C,EAAE,CAAA6C,cAAA,YAs6BqU,CAAC;IAt6BxU7C,EAAE,CAAA8C,UAAA,mBAAAe,gEAAA;MAAF7D,EAAE,CAAAgD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAs6BqPF,MAAA,CAAAG,kBAAA,CAAmB,CAAC;IAAA,CAAC,CAAC;IAt6B7QpD,EAAE,CAAAqD,YAAA,CAs6B2U,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAQ,MAAA,GAt6B9UjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAsD,WAAA,qBAAAL,MAAA,CAAAM,kBAAA,EAs6BoU,CAAC;EAAA;AAAA;AAAA,SAAAO,2CAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAt6BvUzC,EAAE,CAAA6C,cAAA,yBAs6B8e,CAAC;IAt6Bjf7C,EAAE,CAAAyD,YAAA,KAs6B6gB,CAAC;IAt6BhhBzD,EAAE,CAAAqD,YAAA,CAs6BuiB,CAAC;EAAA;AAAA;AAAA,MAAAU,GAAA;AAx9BvoB,SAASC,6BAA6BA,CAACC,QAAQ,EAAE;EAC7C,MAAMC,KAAK,CAAC,gDAAgDD,QAAQ,IAAI,CAAC;AAC7E;AACA;AACA,MAAME,2BAA2B,gBAAG,IAAIlE,cAAc,CAAC,6BAA6B,EAAE;EAClFmE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,gBAAG,IAAItE,cAAc,CAAC,sBAAsB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAASqE,mCAAmCA,CAAA,EAAG;EAC3C,OAAO,KAAK;AAChB;AAAC,IACKE,gBAAgB;EAAtB,MAAMA,gBAAgB,SAAS5E,aAAa,CAAC;IACzC6E,SAAS,GAAGvE,MAAM,CAACP,QAAQ,CAAC;IAC5B+E,kBAAkB,GAAGxE,MAAM,CAACC,iBAAiB,CAAC;IAC9CwE,UAAU,GAAGzE,MAAM,CAAC0E,kBAAkB,CAAC;IACvCC,WAAWA,CAAA,EAAG;MACV,MAAMC,UAAU,GAAG5E,MAAM,CAACE,UAAU,CAAC;MACrC,MAAM2E,gBAAgB,GAAG7E,MAAM,CAACL,gBAAgB,CAAC;MACjD,MAAMmF,MAAM,GAAG9E,MAAM,CAACG,MAAM,CAAC;MAC7B,KAAK,CAACyE,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;IAC/C;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACN,UAAU,CAACO,qBAAqB,CAACC,SAAS,CAAC,MAAM;QAClD,IAAI,CAACT,kBAAkB,CAACU,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACAC,eAAeA,CAAA,EAAG;MACd;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACZ,SAAS,CAACa,SAAS,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA,MAAM;QAAEC,KAAK;QAAEC;MAAI,CAAC,GAAG,IAAI,CAACb,UAAU;MACtC,OAASY,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACE,IAAI,KAAK,MAAM,IAAIF,KAAK,CAACG,MAAM,IAC1DF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACC,IAAI,KAAK,MAAM,IAAID,GAAG,CAACE,MAAO;IAC1D;IACA,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFrB,gBAAgB;IAAA;IACnH,OAAOsB,IAAI,kBAD8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EACJxB,gBAAgB;MAAAyB,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,8BAAA5D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADdzC,EAAE,CAAAsG,WAAA,gBAAA5D,GAAA,CAAAiC,UAAA,CAAA4B,eAAA,CAAAC,IAAA,MACW,CAAC,iBAAA9D,GAAA,CAAAiC,UAAA,CAAA4B,eAAA,CAAAE,KAAA,MAAD,CAAC;UADdzG,EAAE,CAAAsD,WAAA,8BACJZ,GAAA,CAAA2C,eAAA,CAAgB,CAAD,CAAC;QAAA;MAAA;MAAAqB,QAAA,GADd1G,EAAE,CAAA2G,kBAAA,CAC6T,CAChZ;QACIC,OAAO,EAAEhH,aAAa;QACtBiH,WAAW,EAAErC;MACjB,CAAC,CACJ,GANoFxE,EAAE,CAAA8G,0BAAA;MAAAC,kBAAA,EAAA3E,GAAA;MAAA4E,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAA1E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAoH,eAAA;UAAFpH,EAAE,CAAAyD,YAAA,EAMZ,CAAC;QAAA;MAAA;MAAA4D,aAAA;MAAAC,eAAA;IAAA;EACpF;EAAC,OApCK9C,gBAAgB;AAAA;AAqCtB;EAAA,QAAA+C,SAAA,oBAAAA,SAAA;AAAA;AAqBA;AACA;AACA;AAFA,IAGMC,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZC,WAAW,GAAGvH,MAAM,CAACE,UAAU,CAAC;IAChCsH,iBAAiB,GAAGxH,MAAM,CAACf,gBAAgB,CAAC;IAC5CwI,aAAa,GAAGzH,MAAM,CAACd,YAAY,CAAC;IACpCqF,SAAS,GAAGvE,MAAM,CAACP,QAAQ,CAAC;IAC5BiI,OAAO,GAAG1H,MAAM,CAACG,MAAM,CAAC;IACxBwH,SAAS,GAAG3H,MAAM,CAACO,SAAS,CAAC;IAC7BqH,qBAAqB,GAAG5H,MAAM,CAACb,oBAAoB,CAAC;IACpD0I,IAAI,GAAG7H,MAAM,CAACQ,QAAQ,EAAE;MAAEsH,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3CrD,UAAU,GAAGzE,MAAM,CAACqE,oBAAoB,EAAE;MAAEyD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7DC,UAAU,GAAG,IAAI;IACjBC,oCAAoC,GAAG,IAAI;IAC3CC,cAAc;IACd;IACAC,WAAW;IACX;IACAC,OAAO;IACP;IACA,IAAIpE,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACqE,SAAS;IACzB;IACA,IAAIrE,QAAQA,CAACsE,KAAK,EAAE;MAChB;MACAA,KAAK,GAAGA,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,OAAO;MACzC,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;QAC1B;QACA,IAAI,IAAI,CAACF,WAAW,EAAE;UAClB,IAAI,CAACI,uBAAuB,CAACD,KAAK,CAAC;QACvC;QACA,IAAI,CAACD,SAAS,GAAGC,KAAK;QACtB,IAAI,CAACE,iBAAiB,CAACC,IAAI,CAAC,CAAC;MACjC;IACJ;IACAJ,SAAS,GAAG,OAAO;IACnB;IACA,IAAI7C,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACkD,KAAK;IACrB;IACA,IAAIlD,IAAIA,CAAC8C,KAAK,EAAE;MACZ,IAAI,CAACI,KAAK,GAAGJ,KAAK;MAClB,IAAI,CAACK,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;IACAH,KAAK,GAAG,MAAM;IACd;IACA,IAAII,YAAYA,CAAA,EAAG;MACf,OAAO,IAAI,CAACC,aAAa;IAC7B;IACA,IAAID,YAAYA,CAACR,KAAK,EAAE;MACpB,IAAI,CAACS,aAAa,GAAGzJ,qBAAqB,CAACgJ,KAAK,CAAC;IACrD;IACAS,aAAa,GAAG,KAAK;IACrB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIC,SAASA,CAAA,EAAG;MACZ,MAAMV,KAAK,GAAG,IAAI,CAACW,UAAU;MAC7B;MACA;MACA;MACA,IAAIX,KAAK,IAAI,IAAI,EAAE;QACf,IAAI,IAAI,CAAC9C,IAAI,KAAK,MAAM,EAAE;UACtB,OAAO,QAAQ;QACnB,CAAC,MACI;UACD,OAAO,gBAAgB;QAC3B;MACJ;MACA,OAAO8C,KAAK;IAChB;IACA,IAAIU,SAASA,CAACV,KAAK,EAAE;MACjB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,IAAI,IAAI,EAAE;QACxDA,KAAK,GAAGhJ,qBAAqB,CAACgJ,KAAK,CAAC;MACxC;MACA,IAAI,CAACW,UAAU,GAAGX,KAAK;IAC3B;IACAW,UAAU;IACV;AACJ;AACA;AACA;IACI,IAAIxD,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACyD,OAAO,CAAC,CAAC;IACzB;IACA,IAAIzD,MAAMA,CAAC6C,KAAK,EAAE;MACd,IAAI,CAACa,MAAM,CAAC7J,qBAAqB,CAACgJ,KAAK,CAAC,CAAC;IAC7C;IACAY,OAAO,GAAGxI,MAAM,CAAC,KAAK,CAAC;IACvB;IACA0I,UAAU;IACV;IACAC,iBAAiB,GAAG,IAAIhI,OAAO,CAAC,CAAC;IACjC;IACAiI,aAAa,GAAG,IAAIjI,OAAO,CAAC,CAAC;IAC7B;IACAkI,YAAY;IACZ;IACA,IAAI5I,YAAY,CAAC,aAAc,IAAI,CAAC;IACpC;IACA6I,aAAa,GAAG,IAAI,CAACD,YAAY,CAACE,IAAI,CAACjI,MAAM,CAACkI,CAAC,IAAIA,CAAC,CAAC,EAAEjI,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IACtE;IACAkI,WAAW,GAAG,IAAI,CAACN,iBAAiB,CAACI,IAAI,CAACjI,MAAM,CAAC,MAAM,IAAI,CAACiE,MAAM,CAAC,EAAE/D,KAAK,CAACkI,SAAS,CAAC,CAAC;IACtF;IACAC,aAAa,GAAG,IAAI,CAACN,YAAY,CAACE,IAAI,CAACjI,MAAM,CAACkI,CAAC,IAAI,CAACA,CAAC,CAAC,EAAEjI,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IACvE;IACAqI,WAAW,GAAG,IAAI,CAACT,iBAAiB,CAACI,IAAI,CAACjI,MAAM,CAAC,MAAM,CAAC,IAAI,CAACiE,MAAM,CAAC,EAAE/D,KAAK,CAACkI,SAAS,CAAC,CAAC;IACvF;IACAG,UAAU,GAAG,IAAI1I,OAAO,CAAC,CAAC;IAC1B;IACA;IACAmH,iBAAiB,GAAG,IAAI7H,YAAY,CAAC,CAAC;IACtC;IACAqJ,QAAQ;IACR;AACJ;AACA;AACA;IACIpB,YAAY,GAAG,IAAIvH,OAAO,CAAC,CAAC;IAC5B4I,SAAS,GAAGhK,MAAM,CAACW,QAAQ,CAAC;IAC5B6D,kBAAkB,GAAGxE,MAAM,CAACC,iBAAiB,CAAC;IAC9C0E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2E,YAAY,CAACE,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CAAC7E,SAAS,CAAEO,MAAM,IAAK;QACrE,IAAIA,MAAM,EAAE;UACR,IAAI,IAAI,CAACqC,IAAI,EAAE;YACX,IAAI,CAACG,oCAAoC,GAAG,IAAI,CAACH,IAAI,CAACoC,aAAa;UACvE;UACA,IAAI,CAACC,UAAU,CAAC,CAAC;QACrB,CAAC,MACI,IAAI,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAAE;UAClC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACjB,UAAU,IAAI,SAAS,CAAC;QACpD;MACJ,CAAC,CAAC;MACF;AACR;AACA;AACA;AACA;MACQ,IAAI,CAACzB,OAAO,CAAC2C,iBAAiB,CAAC,MAAM;QACjC,MAAMC,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACgD,aAAa;QAC9ClJ,SAAS,CAACiJ,OAAO,EAAE,SAAS,CAAC,CACxBd,IAAI,CAACjI,MAAM,CAACiJ,KAAK,IAAI;UACtB,OAAOA,KAAK,CAACC,OAAO,KAAKlL,MAAM,IAAI,CAAC,IAAI,CAACsJ,YAAY,IAAI,CAACrJ,cAAc,CAACgL,KAAK,CAAC;QACnF,CAAC,CAAC,EAAE9I,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CAC1B7E,SAAS,CAACuF,KAAK,IAAI,IAAI,CAAC9C,OAAO,CAACgD,GAAG,CAAC,MAAM;UAC3C,IAAI,CAACC,KAAK,CAAC,CAAC;UACZH,KAAK,CAACI,eAAe,CAAC,CAAC;UACvBJ,KAAK,CAACK,cAAc,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC5C,cAAc,GAAG,CAClB,IAAI,CAACN,SAAS,CAACmD,MAAM,CAACR,OAAO,EAAE,eAAe,EAAE,IAAI,CAACS,sBAAsB,CAAC,EAC5E,IAAI,CAACpD,SAAS,CAACmD,MAAM,CAACR,OAAO,EAAE,eAAe,EAAE,IAAI,CAACS,sBAAsB,CAAC,EAC5E,IAAI,CAACpD,SAAS,CAACmD,MAAM,CAACR,OAAO,EAAE,kBAAkB,EAAE,IAAI,CAACS,sBAAsB,CAAC,CAClF;MACL,CAAC,CAAC;MACF,IAAI,CAAC1B,aAAa,CAACpE,SAAS,CAAC,MAAM;QAC/B,IAAI,CAACqE,YAAY,CAACd,IAAI,CAAC,IAAI,CAAChD,MAAM,CAAC;MACvC,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;AACA;IACIwF,WAAWA,CAACV,OAAO,EAAEW,OAAO,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACrD,qBAAqB,CAACsD,WAAW,CAACZ,OAAO,CAAC,EAAE;QAClDA,OAAO,CAACa,QAAQ,GAAG,CAAC,CAAC;QACrB;QACA,IAAI,CAACzD,OAAO,CAAC2C,iBAAiB,CAAC,MAAM;UACjC,MAAMe,QAAQ,GAAGA,CAAA,KAAM;YACnBC,WAAW,CAAC,CAAC;YACbC,gBAAgB,CAAC,CAAC;YAClBhB,OAAO,CAACiB,eAAe,CAAC,UAAU,CAAC;UACvC,CAAC;UACD,MAAMF,WAAW,GAAG,IAAI,CAAC1D,SAAS,CAACmD,MAAM,CAACR,OAAO,EAAE,MAAM,EAAEc,QAAQ,CAAC;UACpE,MAAME,gBAAgB,GAAG,IAAI,CAAC3D,SAAS,CAACmD,MAAM,CAACR,OAAO,EAAE,WAAW,EAAEc,QAAQ,CAAC;QAClF,CAAC,CAAC;MACN;MACAd,OAAO,CAACkB,KAAK,CAACP,OAAO,CAAC;IAC1B;IACA;AACJ;AACA;AACA;IACIQ,mBAAmBA,CAACC,QAAQ,EAAET,OAAO,EAAE;MACnC,IAAIU,cAAc,GAAG,IAAI,CAACpE,WAAW,CAACgD,aAAa,CAACqB,aAAa,CAACF,QAAQ,CAAC;MAC3E,IAAIC,cAAc,EAAE;QAChB,IAAI,CAACX,WAAW,CAACW,cAAc,EAAEV,OAAO,CAAC;MAC7C;IACJ;IACA;AACJ;AACA;AACA;IACIf,UAAUA,CAAA,EAAG;MACT,IAAI,CAAC,IAAI,CAACnC,UAAU,EAAE;QAClB;MACJ;MACA,MAAMuC,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACgD,aAAa;MAC9C;MACA;MACA;MACA,QAAQ,IAAI,CAACxB,SAAS;QAClB,KAAK,KAAK;QACV,KAAK,QAAQ;UACT;QACJ,KAAK,IAAI;QACT,KAAK,gBAAgB;UACjBnI,eAAe,CAAC,MAAM;YAClB,MAAMiL,aAAa,GAAG,IAAI,CAAC9D,UAAU,CAAC+D,mBAAmB,CAAC,CAAC;YAC3D,IAAI,CAACD,aAAa,IAAI,OAAOvB,OAAO,CAACkB,KAAK,KAAK,UAAU,EAAE;cACvDlB,OAAO,CAACkB,KAAK,CAAC,CAAC;YACnB;UACJ,CAAC,EAAE;YAAEO,QAAQ,EAAE,IAAI,CAAC/B;UAAU,CAAC,CAAC;UAChC;QACJ,KAAK,eAAe;UAChB,IAAI,CAACyB,mBAAmB,CAAC,0CAA0C,CAAC;UACpE;QACJ;UACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAAC1C,SAAS,CAAC;UACxC;MACR;IACJ;IACA;AACJ;AACA;AACA;IACIqB,aAAaA,CAAC4B,WAAW,EAAE;MACvB,IAAI,IAAI,CAACjD,SAAS,KAAK,QAAQ,EAAE;QAC7B;MACJ;MACA,IAAI,IAAI,CAACf,oCAAoC,EAAE;QAC3C,IAAI,CAACP,aAAa,CAACwE,QAAQ,CAAC,IAAI,CAACjE,oCAAoC,EAAEgE,WAAW,CAAC;MACvF,CAAC,MACI;QACD,IAAI,CAACzE,WAAW,CAACgD,aAAa,CAAC2B,IAAI,CAAC,CAAC;MACzC;MACA,IAAI,CAAClE,oCAAoC,GAAG,IAAI;IACpD;IACA;IACAmC,oBAAoBA,CAAA,EAAG;MACnB,MAAMgC,QAAQ,GAAG,IAAI,CAACtE,IAAI,CAACoC,aAAa;MACxC,OAAO,CAAC,CAACkC,QAAQ,IAAI,IAAI,CAAC5E,WAAW,CAACgD,aAAa,CAAC6B,QAAQ,CAACD,QAAQ,CAAC;IAC1E;IACAE,eAAeA,CAAA,EAAG;MACd,IAAI,CAACnE,WAAW,GAAG,IAAI;MACvB;MACA;MACA,IAAI,IAAI,CAACE,SAAS,KAAK,KAAK,EAAE;QAC1B,IAAI,CAACE,uBAAuB,CAAC,KAAK,CAAC;MACvC;MACA;MACA;MACA,IAAI,IAAI,CAAC/D,SAAS,CAACa,SAAS,EAAE;QAC1B,IAAI,CAAC2C,UAAU,GAAG,IAAI,CAACP,iBAAiB,CAAC8E,MAAM,CAAC,IAAI,CAAC/E,WAAW,CAACgD,aAAa,CAAC;QAC/E,IAAI,CAAC7B,qBAAqB,CAAC,CAAC;MAChC;IACJ;IACA6D,WAAWA,CAAA,EAAG;MACV,IAAI,CAACtE,cAAc,CAACuE,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MACjD,IAAI,CAAC1E,UAAU,EAAE2E,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACvE,OAAO,EAAEwE,MAAM,CAAC,CAAC;MACtB,IAAI,CAACxE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACiB,iBAAiB,CAACwD,QAAQ,CAAC,CAAC;MACjC,IAAI,CAACvD,aAAa,CAACuD,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACjE,YAAY,CAACiE,QAAQ,CAAC,CAAC;MAC5B,IAAI,CAAC9C,UAAU,CAAClB,IAAI,CAAC,CAAC;MACtB,IAAI,CAACkB,UAAU,CAAC8C,QAAQ,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;IACIC,IAAIA,CAACC,SAAS,EAAE;MACZ,OAAO,IAAI,CAAC5D,MAAM,CAAC,IAAI,EAAE4D,SAAS,CAAC;IACvC;IACA;IACAnC,KAAKA,CAAA,EAAG;MACJ,OAAO,IAAI,CAACzB,MAAM,CAAC,KAAK,CAAC;IAC7B;IACA;IACA6D,sBAAsBA,CAAA,EAAG;MACrB;MACA;MACA;MACA,OAAO,IAAI,CAACC,QAAQ,CAAC,YAAa,KAAK,EAAE,kBAAmB,IAAI,EAAE,OAAO,CAAC;IAC9E;IACA;AACJ;AACA;AACA;AACA;AACA;IACI9D,MAAMA,CAAC+D,MAAM,GAAG,CAAC,IAAI,CAACzH,MAAM,EAAEsH,SAAS,EAAE;MACrC;MACA;MACA,IAAIG,MAAM,IAAIH,SAAS,EAAE;QACrB,IAAI,CAAC3D,UAAU,GAAG2D,SAAS;MAC/B;MACA,MAAMI,MAAM,GAAG,IAAI,CAACF,QAAQ,CAACC,MAAM,EACnC,kBAAmB,CAACA,MAAM,IAAI,IAAI,CAAC9C,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAChB,UAAU,IAAI,SAAS,CAAC;MACxF,IAAI,CAAC8D,MAAM,EAAE;QACT,IAAI,CAAC9D,UAAU,GAAG,IAAI;MAC1B;MACA,OAAO+D,MAAM;IACjB;IACA;AACJ;AACA;AACA;AACA;AACA;IACIF,QAAQA,CAACC,MAAM,EAAEE,YAAY,EAAEnB,WAAW,EAAE;MACxC,IAAIiB,MAAM,KAAK,IAAI,CAACzH,MAAM,EAAE;QACxB,OAAO4H,OAAO,CAACC,OAAO,CAACJ,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;MACrD;MACA,IAAI,CAAChE,OAAO,CAACqE,GAAG,CAACL,MAAM,CAAC;MACxB,IAAI,IAAI,CAACxI,UAAU,EAAE8I,mBAAmB,EAAE;QACtC;QACA;QACA,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9B,CAAC,MACI;QACD;QACAC,UAAU,CAAC,MAAM;UACb,IAAI,CAACrE,iBAAiB,CAACR,IAAI,CAAC,CAAC;UAC7B,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN;MACA,IAAI,CAACrB,WAAW,CAACgD,aAAa,CAACmD,SAAS,CAACxE,MAAM,CAAC,mBAAmB,EAAE+D,MAAM,CAAC;MAC5E,IAAI,CAACA,MAAM,IAAIE,YAAY,EAAE;QACzB,IAAI,CAAC/C,aAAa,CAAC4B,WAAW,CAAC;MACnC;MACA;MACA,IAAI,CAACxH,kBAAkB,CAACU,YAAY,CAAC,CAAC;MACtC,IAAI,CAACwD,qBAAqB,CAAC,CAAC;MAC5B,OAAO,IAAI0E,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAI,CAAC/D,YAAY,CAACE,IAAI,CAAC7H,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsD,SAAS,CAAC4H,IAAI,IAAIQ,OAAO,CAACR,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;MACvF,CAAC,CAAC;IACN;IACA;IACAW,eAAeA,CAACG,WAAW,EAAE;MACzB,IAAI,CAACpG,WAAW,CAACgD,aAAa,CAACmD,SAAS,CAACxE,MAAM,CAAC,sBAAsB,EAAEyE,WAAW,CAAC;IACxF;IACAC,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACrG,WAAW,CAACgD,aAAa,CAACsD,WAAW,IAAI,CAAC;IAC1D;IACA;IACAnF,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACX,UAAU,EAAE;QACjB;QACA;QACA,IAAI,CAACA,UAAU,CAAC+F,OAAO,GAAG,CAAC,CAAC,IAAI,CAACrJ,UAAU,EAAEsJ,WAAW,IAAI,IAAI,CAACvI,MAAM;MAC3E;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACI8C,uBAAuBA,CAAC0F,WAAW,EAAE;MACjC;MACA,IAAI,CAAC,IAAI,CAACzJ,SAAS,CAACa,SAAS,EAAE;QAC3B;MACJ;MACA,MAAMkF,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACgD,aAAa;MAC9C,MAAM0D,MAAM,GAAG3D,OAAO,CAAC4D,UAAU;MACjC,IAAIF,WAAW,KAAK,KAAK,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC7F,OAAO,EAAE;UACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACN,IAAI,CAACsG,aAAa,CAAC,mBAAmB,CAAC;UAC3DF,MAAM,CAACG,YAAY,CAAC,IAAI,CAACjG,OAAO,EAAEmC,OAAO,CAAC;QAC9C;QACA2D,MAAM,CAACI,WAAW,CAAC/D,OAAO,CAAC;MAC/B,CAAC,MACI,IAAI,IAAI,CAACnC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,CAAC+F,UAAU,CAACE,YAAY,CAAC9D,OAAO,EAAE,IAAI,CAACnC,OAAO,CAAC;MAC/D;IACJ;IACA;IACA4C,sBAAsB,GAAIP,KAAK,IAAK;MAChC,MAAMF,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACgD,aAAa;MAC9C,IAAIC,KAAK,CAAC8D,MAAM,KAAKhE,OAAO,EAAE;QAC1B,IAAI,CAAC5C,OAAO,CAACgD,GAAG,CAAC,MAAM;UACnB,IAAIF,KAAK,CAAC1E,IAAI,KAAK,eAAe,EAAE;YAChC,IAAI,CAACsD,iBAAiB,CAACR,IAAI,CAAC4B,KAAK,CAAC;UACtC,CAAC,MACI;YACD;YACA;YACA,IAAIA,KAAK,CAAC1E,IAAI,KAAK,eAAe,EAAE;cAChC,IAAI,CAAC0H,eAAe,CAAC,KAAK,CAAC;YAC/B;YACA,IAAI,CAACnE,aAAa,CAACT,IAAI,CAAC4B,KAAK,CAAC;UAClC;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACD,OAAO/E,IAAI,YAAA8I,kBAAA5I,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2B,SAAS;IAAA;IAC5G,OAAO1B,IAAI,kBAnb8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EAmbJwB,SAAS;MAAAvB,SAAA;MAAAyI,SAAA,WAAAC,gBAAAlM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnbPzC,EAAE,CAAA4O,WAAA,CAAAvM,GAAA;QAAA;QAAA,IAAAI,EAAA;UAAA,IAAAoM,EAAA;UAAF7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAuH,QAAA,GAAA4E,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA9I,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA6I,uBAAAxM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAkP,WAAA,UAmbJ,IAAI,cAAAxM,GAAA,CAAA+C,IAAA,KAAM,MAAM,GAAI,IAAI,GAAG,IAAI;UAnb7BzF,EAAE,CAAAsG,WAAA,gBAAA5D,GAAA,CAAAiC,UAAA,KAAAjC,GAAA,CAAAgD,MAAA,GAmbuB,QAAQ,GAAG,IAA9B,CAAC;UAnbP1F,EAAE,CAAAsD,WAAA,mBAAAZ,GAAA,CAAAuB,QAAA,KAmbS,KAAL,CAAC,oBAAAvB,GAAA,CAAA+C,IAAA,WAAD,CAAC,oBAAA/C,GAAA,CAAA+C,IAAA,WAAD,CAAC,oBAAA/C,GAAA,CAAA+C,IAAA,WAAD,CAAC;QAAA;MAAA;MAAA0J,MAAA;QAAAlL,QAAA;QAAAwB,IAAA;QAAAsD,YAAA;QAAAE,SAAA;QAAAvD,MAAA;MAAA;MAAA0J,OAAA;QAAA5F,YAAA;QAAAC,aAAA;QAAAG,WAAA;QAAAE,aAAA;QAAAC,WAAA;QAAAtB,iBAAA;MAAA;MAAA4G,QAAA;MAAAtI,kBAAA,EAAA3E,GAAA;MAAA4E,KAAA;MAAAC,IAAA;MAAAqI,MAAA;MAAApI,QAAA,WAAAqI,mBAAA9M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnbPzC,EAAE,CAAAoH,eAAA;UAAFpH,EAAE,CAAA6C,cAAA,eAmbi8B,CAAC;UAnbp8B7C,EAAE,CAAAyD,YAAA,EAmbg+B,CAAC;UAnbn+BzD,EAAE,CAAAqD,YAAA,CAmb0+B,CAAC;QAAA;MAAA;MAAAmM,YAAA,GAAiD5P,aAAa;MAAAyH,aAAA;MAAAC,eAAA;IAAA;EACxoC;EAAC,OApZKE,SAAS;AAAA;AAqZf;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AA+CA;AACA;AACA;AACA;AACA;AACA;AALA,IAMM3C,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB6K,IAAI,GAAGvP,MAAM,CAACZ,cAAc,EAAE;MAAE0I,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjD0H,QAAQ,GAAGxP,MAAM,CAACE,UAAU,CAAC;IAC7BwH,OAAO,GAAG1H,MAAM,CAACG,MAAM,CAAC;IACxBqE,kBAAkB,GAAGxE,MAAM,CAACC,iBAAiB,CAAC;IAC9CwP,kBAAkB,GAAG1N,mBAAmB,CAAC,CAAC;IAC1CwL,mBAAmB,GAAG,KAAK;IAC3B;IACAmC,WAAW;IACX;IACAC,QAAQ,GAAG,IAAI3O,SAAS,CAAC,CAAC;IAC1B+I,QAAQ;IACR6F,YAAY;IACZ;IACA,IAAIvK,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACwK,MAAM;IACtB;IACA;IACA,IAAIvK,GAAGA,CAAA,EAAG;MACN,OAAO,IAAI,CAACwK,IAAI;IACpB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAAC1H,KAAK,EAAE;MAChB,IAAI,CAAC2H,SAAS,GAAG3Q,qBAAqB,CAACgJ,KAAK,CAAC;IACjD;IACA2H,SAAS,GAAGhQ,MAAM,CAACiE,2BAA2B,CAAC;IAC/C;AACJ;AACA;AACA;AACA;IACI,IAAI8J,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACkC,kBAAkB,CAAC,IAAI,CAACJ,MAAM,CAAC,IAAI,IAAI,CAACI,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAC;IACrF;IACA,IAAI/B,WAAWA,CAAC1F,KAAK,EAAE;MACnB,IAAI,CAAC6H,iBAAiB,GAAG7H,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGhJ,qBAAqB,CAACgJ,KAAK,CAAC;IAChF;IACA6H,iBAAiB;IACjB;IACAC,aAAa,GAAG,IAAIzP,YAAY,CAAC,CAAC;IAClC;IACAmP,MAAM;IACNC,IAAI;IACJ;AACJ;AACA;AACA;AACA;AACA;IACIM,KAAK;IACLC,MAAM;IACN;IACAvG,UAAU,GAAG,IAAI1I,OAAO,CAAC,CAAC;IAC1B;IACAkP,eAAe,GAAG,IAAIlP,OAAO,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;IACIiF,eAAe,GAAG;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;IAC7CvB,qBAAqB,GAAG,IAAI5D,OAAO,CAAC,CAAC;IACrC;IACA,IAAImP,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACX,YAAY,IAAI,IAAI,CAAC7F,QAAQ;IAC7C;IACAC,SAAS,GAAGhK,MAAM,CAACW,QAAQ,CAAC;IAC5BgE,WAAWA,CAAA,EAAG;MACV,MAAM6L,QAAQ,GAAGxQ,MAAM,CAACP,QAAQ,CAAC;MACjC,MAAMgR,aAAa,GAAGzQ,MAAM,CAACJ,aAAa,CAAC;MAC3C;MACA;MACA,IAAI,CAAC2P,IAAI,EAAEmB,MAAM,CAAClH,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CAAC7E,SAAS,CAAC,MAAM;QAC/D,IAAI,CAAC0L,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B,CAAC,CAAC;MACF;MACA;MACAH,aAAa,CACRC,MAAM,CAAC,CAAC,CACRlH,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CAChC7E,SAAS,CAAC,MAAM,IAAI,CAAC2L,oBAAoB,CAAC,CAAC,CAAC;MACjD,IAAI,CAAC,IAAI,CAACnB,kBAAkB,IAAIe,QAAQ,CAACpL,SAAS,EAAE;QAChD,IAAI,CAACsC,OAAO,CAAC2C,iBAAiB,CAAC,MAAM;UACjC;UACA;UACAoD,UAAU,CAAC,MAAM;YACb,IAAI,CAAC+B,QAAQ,CAACjF,aAAa,CAACmD,SAAS,CAACmD,GAAG,CAAC,uBAAuB,CAAC;YAClE,IAAI,CAACtD,mBAAmB,GAAG,IAAI;UACnC,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,CAAC;MACN;IACJ;IACAxI,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC2K,WAAW,CAACoB,OAAO,CACnBtH,IAAI,CAAC5H,SAAS,CAAC,IAAI,CAAC8N,WAAW,CAAC,EAAEhO,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CAC7D7E,SAAS,CAAE8L,MAAM,IAAK;QACvB,IAAI,CAACpB,QAAQ,CAACqB,KAAK,CAACD,MAAM,CAACxP,MAAM,CAAC0P,IAAI,IAAI,CAACA,IAAI,CAACxM,UAAU,IAAIwM,IAAI,CAACxM,UAAU,KAAK,IAAI,CAAC,CAAC;QACxF,IAAI,CAACkL,QAAQ,CAACuB,eAAe,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAACvB,QAAQ,CAACmB,OAAO,CAACtH,IAAI,CAAC5H,SAAS,CAAC,IAAI,CAAC,CAAC,CAACqD,SAAS,CAAC,MAAM;QACxD,IAAI,CAAC0L,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAAChB,QAAQ,CAACnD,OAAO,CAAEuE,MAAM,IAAK;UAC9B,IAAI,CAACI,kBAAkB,CAACJ,MAAM,CAAC;UAC/B,IAAI,CAACK,oBAAoB,CAACL,MAAM,CAAC;UACjC,IAAI,CAACM,gBAAgB,CAACN,MAAM,CAAC;QACjC,CAAC,CAAC;QACF,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC2B,MAAM,IACrB,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC1B,MAAM,CAAC,IAC/B,IAAI,CAAC0B,aAAa,CAAC,IAAI,CAACzB,IAAI,CAAC,EAAE;UAC/B,IAAI,CAACc,oBAAoB,CAAC,CAAC;QAC/B;QACA,IAAI,CAACpM,kBAAkB,CAACU,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF;MACA,IAAI,CAACwC,OAAO,CAAC2C,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACiG,eAAe,CACf9G,IAAI,CAAC3H,YAAY,CAAC,EAAE,CAAC;QAAE;QAC5BH,SAAS,CAAC,IAAI,CAACoI,UAAU,CAAC,CAAC,CACtB7E,SAAS,CAAC,MAAM,IAAI,CAAC2L,oBAAoB,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC;IACN;IACArE,WAAWA,CAAA,EAAG;MACV,IAAI,CAACvH,qBAAqB,CAAC4H,QAAQ,CAAC,CAAC;MACrC,IAAI,CAAC0D,eAAe,CAAC1D,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAAC+C,QAAQ,CAACjD,OAAO,CAAC,CAAC;MACvB,IAAI,CAAC5C,UAAU,CAAClB,IAAI,CAAC,CAAC;MACtB,IAAI,CAACkB,UAAU,CAAC8C,QAAQ,CAAC,CAAC;IAC9B;IACA;IACAC,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC8C,QAAQ,CAACnD,OAAO,CAACuE,MAAM,IAAIA,MAAM,CAAClE,IAAI,CAAC,CAAC,CAAC;IAClD;IACA;IACAlC,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACgF,QAAQ,CAACnD,OAAO,CAACuE,MAAM,IAAIA,MAAM,CAACpG,KAAK,CAAC,CAAC,CAAC;IACnD;IACA;AACJ;AACA;AACA;IACIiG,oBAAoBA,CAAA,EAAG;MACnB;MACA;MACA;MACA;MACA;MACA;MACA,IAAItK,IAAI,GAAG,CAAC;MACZ,IAAIC,KAAK,GAAG,CAAC;MACb,IAAI,IAAI,CAAC6J,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC5K,MAAM,EAAE;QACjC,IAAI,IAAI,CAAC4K,KAAK,CAAC7K,IAAI,IAAI,MAAM,EAAE;UAC3Be,IAAI,IAAI,IAAI,CAAC8J,KAAK,CAACxC,SAAS,CAAC,CAAC;QAClC,CAAC,MACI,IAAI,IAAI,CAACwC,KAAK,CAAC7K,IAAI,IAAI,MAAM,EAAE;UAChC,MAAMiM,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACxC,SAAS,CAAC,CAAC;UACpCtH,IAAI,IAAIkL,KAAK;UACbjL,KAAK,IAAIiL,KAAK;QAClB;MACJ;MACA,IAAI,IAAI,CAACnB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC7K,MAAM,EAAE;QACnC,IAAI,IAAI,CAAC6K,MAAM,CAAC9K,IAAI,IAAI,MAAM,EAAE;UAC5BgB,KAAK,IAAI,IAAI,CAAC8J,MAAM,CAACzC,SAAS,CAAC,CAAC;QACpC,CAAC,MACI,IAAI,IAAI,CAACyC,MAAM,CAAC9K,IAAI,IAAI,MAAM,EAAE;UACjC,MAAMiM,KAAK,GAAG,IAAI,CAACnB,MAAM,CAACzC,SAAS,CAAC,CAAC;UACrCrH,KAAK,IAAIiL,KAAK;UACdlL,IAAI,IAAIkL,KAAK;QACjB;MACJ;MACA;MACA;MACA;MACA;MACAlL,IAAI,GAAGA,IAAI,IAAI,IAAI;MACnBC,KAAK,GAAGA,KAAK,IAAI,IAAI;MACrB,IAAID,IAAI,KAAK,IAAI,CAACD,eAAe,CAACC,IAAI,IAAIC,KAAK,KAAK,IAAI,CAACF,eAAe,CAACE,KAAK,EAAE;QAC5E,IAAI,CAACF,eAAe,GAAG;UAAEC,IAAI;UAAEC;QAAM,CAAC;QACtC;QACA;QACA,IAAI,CAACmB,OAAO,CAACgD,GAAG,CAAC,MAAM,IAAI,CAAC1F,qBAAqB,CAAC4D,IAAI,CAAC,IAAI,CAACvC,eAAe,CAAC,CAAC;MACjF;IACJ;IACAoL,SAASA,CAAA,EAAG;MACR;MACA,IAAI,IAAI,CAACzB,SAAS,IAAI,IAAI,CAAC0B,SAAS,CAAC,CAAC,EAAE;QACpC;QACA,IAAI,CAAChK,OAAO,CAAC2C,iBAAiB,CAAC,MAAM,IAAI,CAACiG,eAAe,CAAC1H,IAAI,CAAC,CAAC,CAAC;MACrE;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIuI,kBAAkBA,CAACJ,MAAM,EAAE;MACvBA,MAAM,CAAC3H,iBAAiB,CAACI,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACiO,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAAC7L,SAAS,CAAC,MAAM;QAC5E,IAAI,CAAC2L,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACpM,kBAAkB,CAACU,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;MACF,IAAI6L,MAAM,CAACxL,IAAI,KAAK,MAAM,EAAE;QACxBwL,MAAM,CAACzH,YAAY,CACdE,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACiO,QAAQ,CAACmB,OAAO,CAAC,CAAC,CACtC7L,SAAS,CAAC,MAAM,IAAI,CAAC0M,kBAAkB,CAACZ,MAAM,CAACvL,MAAM,CAAC,CAAC;MAChE;IACJ;IACA;AACJ;AACA;AACA;IACI4L,oBAAoBA,CAACL,MAAM,EAAE;MACzB;MACA;MACAA,MAAM,CAACxI,iBAAiB,CAACiB,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACiO,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAAC7L,SAAS,CAAC,MAAM;QAC5ErE,eAAe,CAAC;UAAEgR,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACjB,gBAAgB,CAAC;QAAE,CAAC,EAAE;UAAE5E,QAAQ,EAAE,IAAI,CAAC/B;QAAU,CAAC,CAAC;MAC1F,CAAC,CAAC;IACN;IACA;IACAqH,gBAAgBA,CAACN,MAAM,EAAE;MACrBA,MAAM,CAACpI,YAAY,CACda,IAAI,CAAC9H,SAAS,CAACJ,KAAK,CAAC,IAAI,CAACqO,QAAQ,CAACmB,OAAO,EAAE,IAAI,CAAChH,UAAU,CAAC,CAAC,CAAC,CAC9D7E,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC2L,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACpM,kBAAkB,CAACU,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACAyM,kBAAkBA,CAACE,KAAK,EAAE;MACtB,MAAMnE,SAAS,GAAG,IAAI,CAAC8B,QAAQ,CAACjF,aAAa,CAACmD,SAAS;MACvD,MAAMoE,SAAS,GAAG,+BAA+B;MACjD,IAAID,KAAK,EAAE;QACPnE,SAAS,CAACmD,GAAG,CAACiB,SAAS,CAAC;MAC5B,CAAC,MACI;QACDpE,SAAS,CAACf,MAAM,CAACmF,SAAS,CAAC;MAC/B;IACJ;IACA;IACAnB,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACd,MAAM,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI;MAC9B;MACA,IAAI,CAACH,QAAQ,CAACnD,OAAO,CAACuE,MAAM,IAAI;QAC5B,IAAIA,MAAM,CAAChN,QAAQ,IAAI,KAAK,EAAE;UAC1B,IAAI,IAAI,CAAC+L,IAAI,IAAI,IAAI,KAAK,OAAOzI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;YACtEvD,6BAA6B,CAAC,KAAK,CAAC;UACxC;UACA,IAAI,CAACgM,IAAI,GAAGiB,MAAM;QACtB,CAAC,MACI;UACD,IAAI,IAAI,CAAClB,MAAM,IAAI,IAAI,KAAK,OAAOxI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;YACxEvD,6BAA6B,CAAC,OAAO,CAAC;UAC1C;UACA,IAAI,CAAC+L,MAAM,GAAGkB,MAAM;QACxB;MACJ,CAAC,CAAC;MACF,IAAI,CAACV,MAAM,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI;MAC/B;MACA,IAAI,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAAClH,KAAK,KAAK,KAAK,EAAE;QACxC,IAAI,CAAC+H,KAAK,GAAG,IAAI,CAACN,IAAI;QACtB,IAAI,CAACO,MAAM,GAAG,IAAI,CAACR,MAAM;MAC7B,CAAC,MACI;QACD,IAAI,CAACO,KAAK,GAAG,IAAI,CAACP,MAAM;QACxB,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACP,IAAI;MAC3B;IACJ;IACA;IACA4B,SAASA,CAAA,EAAG;MACR,OAAS,IAAI,CAACH,aAAa,CAAC,IAAI,CAAC1B,MAAM,CAAC,IAAI,IAAI,CAACA,MAAM,CAACtK,IAAI,IAAI,MAAM,IACjE,IAAI,CAACgM,aAAa,CAAC,IAAI,CAACzB,IAAI,CAAC,IAAI,IAAI,CAACA,IAAI,CAACvK,IAAI,IAAI,MAAO;IACnE;IACArC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACiN,aAAa,CAAC3H,IAAI,CAAC,CAAC;MACzB,IAAI,CAACuJ,6BAA6B,CAAC,CAAC;IACxC;IACAA,6BAA6BA,CAAA,EAAG;MAC5B;MACA,CAAC,IAAI,CAAClC,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC,CACnBvO,MAAM,CAACwP,MAAM,IAAIA,MAAM,IAAI,CAACA,MAAM,CAAClI,YAAY,IAAI,IAAI,CAACoH,kBAAkB,CAACc,MAAM,CAAC,CAAC,CACnFvE,OAAO,CAACuE,MAAM,IAAIA,MAAM,CAAChE,sBAAsB,CAAC,CAAC,CAAC;IAC3D;IACA1J,kBAAkBA,CAAA,EAAG;MACjB,OAAS,IAAI,CAACkO,aAAa,CAAC,IAAI,CAAC1B,MAAM,CAAC,IAAI,IAAI,CAACI,kBAAkB,CAAC,IAAI,CAACJ,MAAM,CAAC,IAC3E,IAAI,CAAC0B,aAAa,CAAC,IAAI,CAACzB,IAAI,CAAC,IAAI,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAE;IAC7E;IACAyB,aAAaA,CAACR,MAAM,EAAE;MAClB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACvL,MAAM;IAC1C;IACA;IACAyK,kBAAkBA,CAACc,MAAM,EAAE;MACvB,IAAI,IAAI,CAACb,iBAAiB,IAAI,IAAI,EAAE;QAChC,OAAO,CAAC,CAACa,MAAM,IAAIA,MAAM,CAACxL,IAAI,KAAK,MAAM;MAC7C;MACA,OAAO,IAAI,CAAC2K,iBAAiB;IACjC;IACA,OAAOzK,IAAI,YAAAuM,2BAAArM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFjB,kBAAkB;IAAA;IACrH,OAAOkB,IAAI,kBA5xB8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EA4xBJpB,kBAAkB;MAAAqB,SAAA;MAAAkM,cAAA,WAAAC,kCAAA3P,EAAA,EAAAC,GAAA,EAAA2P,QAAA;QAAA,IAAA5P,EAAA;UA5xBhBzC,EAAE,CAAAsS,cAAA,CAAAD,QAAA,EAiyBtB7N,gBAAgB;UAjyBIxE,EAAE,CAAAsS,cAAA,CAAAD,QAAA,EAiyB2D7K,SAAS;QAAA;QAAA,IAAA/E,EAAA;UAAA,IAAAoM,EAAA;UAjyBtE7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAuH,QAAA,GAAA4E,EAAA,CAAAG,KAAA;UAAFhP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAkN,WAAA,GAAAf,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAA6D,yBAAA9P,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA4O,WAAA,CAiyBkKpK,gBAAgB;QAAA;QAAA,IAAA/B,EAAA;UAAA,IAAAoM,EAAA;UAjyBpL7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAoN,YAAA,GAAAjB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA9I,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAoM,gCAAA/P,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAsD,WAAA,2CAAAZ,GAAA,CAAA0N,iBA4xBa,CAAC;QAAA;MAAA;MAAAjB,MAAA;QAAAc,QAAA;QAAAhC,WAAA;MAAA;MAAAmB,OAAA;QAAAiB,aAAA;MAAA;MAAAhB,QAAA;MAAA3I,QAAA,GA5xBhB1G,EAAE,CAAA2G,kBAAA,CA4xBmU,CACtZ;QACIC,OAAO,EAAErC,oBAAoB;QAC7BsC,WAAW,EAAEjC;MACjB,CAAC,CACJ;MAAAmC,kBAAA,EAAAxE,GAAA;MAAAyE,KAAA;MAAAC,IAAA;MAAAqI,MAAA;MAAApI,QAAA,WAAAuL,4BAAAhQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjyBoFzC,EAAE,CAAAoH,eAAA,CAAA9E,GAAA;UAAFtC,EAAE,CAAA0S,mBAAA,IAAAlQ,yCAAA,gBAiyBuR,CAAC;UAjyB1RxC,EAAE,CAAAyD,YAAA,EAiyBqd,CAAC;UAjyBxdzD,EAAE,CAAAyD,YAAA,KAiyBkhB,CAAC;UAjyBrhBzD,EAAE,CAAA0S,mBAAA,IAAAlP,yCAAA,4BAiyBuiB,CAAC;QAAA;QAAA,IAAAf,EAAA;UAjyB1iBzC,EAAE,CAAA2S,aAAA,CAAAjQ,GAAA,CAAAuL,WAAA,SAiyBka,CAAC;UAjyBrajO,EAAE,CAAA4S,SAAA,EAiyB0nB,CAAC;UAjyB7nB5S,EAAE,CAAA2S,aAAA,EAAAjQ,GAAA,CAAAuH,QAAA,SAiyB0nB,CAAC;QAAA;MAAA;MAAAuF,YAAA,GAA4jJhL,gBAAgB;MAAAqO,MAAA;MAAAxL,aAAA;MAAAC,eAAA;IAAA;EACtyK;EAAC,OAxTK1C,kBAAkB;AAAA;AAyTxB;EAAA,QAAA2C,SAAA,oBAAAA,SAAA;AAAA;AA8BoB,IAEduL,iBAAiB;EAAvB,MAAMA,iBAAiB,SAAStO,gBAAgB,CAAC;IAC7C,OAAOmB,IAAI;MAAA,IAAAoN,8BAAA;MAAA,gBAAAC,0BAAAnN,iBAAA;QAAA,QAAAkN,8BAAA,KAAAA,8BAAA,GAp0B8E/S,EAAE,CAAAiT,qBAAA,CAo0BQH,iBAAiB,IAAAjN,iBAAA,IAAjBiN,iBAAiB;MAAA;IAAA;IACpH,OAAOhN,IAAI,kBAr0B8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EAq0BJ8M,iBAAiB;MAAA7M,SAAA;MAAAC,SAAA;MAAAQ,QAAA,GAr0Bf1G,EAAE,CAAA2G,kBAAA,CAq0BmJ,CACtO;QACIC,OAAO,EAAEhH,aAAa;QACtBiH,WAAW,EAAEiM;MACjB,CAAC,CACJ,GA10BoF9S,EAAE,CAAA8G,0BAAA;MAAAC,kBAAA,EAAA3E,GAAA;MAAA4E,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAgM,2BAAAzQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAoH,eAAA;UAAFpH,EAAE,CAAAyD,YAAA,EA00BZ,CAAC;QAAA;MAAA;MAAA4D,aAAA;MAAAC,eAAA;IAAA;EACpF;EAAC,OARKwL,iBAAiB;AAAA;AASvB;EAAA,QAAAvL,SAAA,oBAAAA,SAAA;AAAA;AAiBc,IACR4L,UAAU;EAAhB,MAAMA,UAAU,SAAS3L,SAAS,CAAC;IAC/B;IACA,IAAI4L,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACC,gBAAgB;IAChC;IACA,IAAID,eAAeA,CAAC7K,KAAK,EAAE;MACvB,IAAI,CAAC8K,gBAAgB,GAAG9T,qBAAqB,CAACgJ,KAAK,CAAC;IACxD;IACA8K,gBAAgB,GAAG,KAAK;IACxB;AACJ;AACA;AACA;IACI,IAAIC,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,YAAY;IAC5B;IACA,IAAID,WAAWA,CAAC/K,KAAK,EAAE;MACnB,IAAI,CAACgL,YAAY,GAAG/T,oBAAoB,CAAC+I,KAAK,CAAC;IACnD;IACAgL,YAAY,GAAG,CAAC;IAChB;AACJ;AACA;AACA;IACI,IAAIC,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACC,eAAe;IAC/B;IACA,IAAID,cAAcA,CAACjL,KAAK,EAAE;MACtB,IAAI,CAACkL,eAAe,GAAGjU,oBAAoB,CAAC+I,KAAK,CAAC;IACtD;IACAkL,eAAe,GAAG,CAAC;IACnB,OAAO9N,IAAI;MAAA,IAAA+N,uBAAA;MAAA,gBAAAC,mBAAA9N,iBAAA;QAAA,QAAA6N,uBAAA,KAAAA,uBAAA,GA73B8E1T,EAAE,CAAAiT,qBAAA,CA63BQE,UAAU,IAAAtN,iBAAA,IAAVsN,UAAU;MAAA;IAAA;IAC7G,OAAOrN,IAAI,kBA93B8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EA83BJmN,UAAU;MAAAlN,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAwN,wBAAAnR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA93BRzC,EAAE,CAAAkP,WAAA,aAAAxM,GAAA,CAAA+C,IAAA,KA83BM,MAAM,GAAI,IAAI,GAAG,IAAI,WAA/B,IAAI;UA93BFzF,EAAE,CAAAsG,WAAA,QAAA5D,GAAA,CAAA0Q,eAAA,GAAA1Q,GAAA,CAAA4Q,WAAA,GA83B4B,IAAI,MAA3B,CAAC,WAAA5Q,GAAA,CAAA0Q,eAAA,GAAA1Q,GAAA,CAAA8Q,cAAA,GAAyB,IAAI,MAA9B,CAAC;UA93BRxT,EAAE,CAAAsD,WAAA,mBAAAZ,GAAA,CAAAuB,QAAA,KA83BS,KAAJ,CAAC,oBAAAvB,GAAA,CAAA+C,IAAA,KAAD,OAAC,oBAAA/C,GAAA,CAAA+C,IAAA,KAAD,OAAC,oBAAA/C,GAAA,CAAA+C,IAAA,KAAD,OAAC,sBAAA/C,GAAA,CAAA0Q,eAAD,CAAC;QAAA;MAAA;MAAAjE,MAAA;QAAAiE,eAAA;QAAAE,WAAA;QAAAE,cAAA;MAAA;MAAAnE,QAAA;MAAA3I,QAAA,GA93BR1G,EAAE,CAAA2G,kBAAA,CA83B8pB,CAAC;QAAEC,OAAO,EAAEY,SAAS;QAAEX,WAAW,EAAEsM;MAAW,CAAC,CAAC,GA93BjtBnT,EAAE,CAAA8G,0BAAA;MAAAC,kBAAA,EAAA3E,GAAA;MAAA4E,KAAA;MAAAC,IAAA;MAAAqI,MAAA;MAAApI,QAAA,WAAA2M,oBAAApR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAoH,eAAA;UAAFpH,EAAE,CAAA6C,cAAA,eA83B21B,CAAC;UA93B91B7C,EAAE,CAAAyD,YAAA,EA83B03B,CAAC;UA93B73BzD,EAAE,CAAAqD,YAAA,CA83Bo4B,CAAC;QAAA;MAAA;MAAAmM,YAAA,GAAiD5P,aAAa;MAAAyH,aAAA;MAAAC,eAAA;IAAA;EACliC;EAAC,OAjCK6L,UAAU;AAAA;AAkChB;EAAA,QAAA5L,SAAA,oBAAAA,SAAA;AAAA;AAuBoB,IACduM,mBAAmB;EAAzB,MAAMA,mBAAmB,SAASlP,kBAAkB,CAAC;IACjDgL,WAAW,GAAG/F,SAAS;IACvB;IACAI,QAAQ,GAAGJ,SAAS;IACpB,OAAOlE,IAAI;MAAA,IAAAoO,gCAAA;MAAA,gBAAAC,4BAAAnO,iBAAA;QAAA,QAAAkO,gCAAA,KAAAA,gCAAA,GA55B8E/T,EAAE,CAAAiT,qBAAA,CA45BQa,mBAAmB,IAAAjO,iBAAA,IAAnBiO,mBAAmB;MAAA;IAAA;IACtH,OAAOhO,IAAI,kBA75B8E9F,EAAE,CAAA+F,iBAAA;MAAAC,IAAA,EA65BJ8N,mBAAmB;MAAA7N,SAAA;MAAAkM,cAAA,WAAA8B,mCAAAxR,EAAA,EAAAC,GAAA,EAAA2P,QAAA;QAAA,IAAA5P,EAAA;UA75BjBzC,EAAE,CAAAsS,cAAA,CAAAD,QAAA,EAs6BtBS,iBAAiB;UAt6BG9S,EAAE,CAAAsS,cAAA,CAAAD,QAAA,EAs6B4Dc,UAAU;QAAA;QAAA,IAAA1Q,EAAA;UAAA,IAAAoM,EAAA;UAt6BxE7O,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAuH,QAAA,GAAA4E,EAAA,CAAAG,KAAA;UAAFhP,EAAE,CAAA8O,cAAA,CAAAD,EAAA,GAAF7O,EAAE,CAAA+O,WAAA,QAAArM,GAAA,CAAAkN,WAAA,GAAAf,EAAA;QAAA;MAAA;MAAA3I,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA8N,iCAAAzR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAsD,WAAA,2CAAAZ,GAAA,CAAA0N,iBA65Bc,CAAC;QAAA;MAAA;MAAAf,QAAA;MAAA3I,QAAA,GA75BjB1G,EAAE,CAAA2G,kBAAA,CA65BgP,CACnU;QACIC,OAAO,EAAErC,oBAAoB;QAC7BsC,WAAW,EAAEiN;MACjB,CAAC,EACD;QACIlN,OAAO,EAAEhC,kBAAkB;QAC3BiC,WAAW,EAAEiN;MACjB,CAAC,CACJ,GAt6BoF9T,EAAE,CAAA8G,0BAAA;MAAAC,kBAAA,EAAApD,GAAA;MAAAqD,KAAA;MAAAC,IAAA;MAAAqI,MAAA;MAAApI,QAAA,WAAAiN,6BAAA1R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAoH,eAAA,CAAA1D,GAAA;UAAF1D,EAAE,CAAA0S,mBAAA,IAAA9O,0CAAA,gBAs6BmM,CAAC;UAt6BtM5D,EAAE,CAAAyD,YAAA,EAs6BkY,CAAC;UAt6BrYzD,EAAE,CAAAyD,YAAA,KAs6Bgc,CAAC;UAt6BnczD,EAAE,CAAA0S,mBAAA,IAAA5O,0CAAA,6BAs6Bqd,CAAC;QAAA;QAAA,IAAArB,EAAA;UAt6BxdzC,EAAE,CAAA2S,aAAA,CAAAjQ,GAAA,CAAAuL,WAAA,SAs6B8U,CAAC;UAt6BjVjO,EAAE,CAAA4S,SAAA,EAs6B0iB,CAAC;UAt6B7iB5S,EAAE,CAAA2S,aAAA,EAAAjQ,GAAA,CAAAuH,QAAA,SAs6B0iB,CAAC;QAAA;MAAA;MAAAuF,YAAA,GAA4jJsD,iBAAiB;MAAAD,MAAA,GAAA9O,GAAA;MAAAsD,aAAA;MAAAC,eAAA;IAAA;EACvtK;EAAC,OAfKwM,mBAAmB;AAAA;AAgBzB;EAAA,QAAAvM,SAAA,oBAAAA,SAAA;AAAA;AAyBoB,IAEd6M,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB,OAAOzO,IAAI,YAAA0O,yBAAAxO,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuO,gBAAgB;IAAA;IACnH,OAAOE,IAAI,kBAr8B8EtU,EAAE,CAAAuU,gBAAA;MAAAvO,IAAA,EAq8BSoO;IAAgB;IAepH,OAAOI,IAAI,kBAp9B8ExU,EAAE,CAAAyU,gBAAA;MAAAC,OAAA,GAo9BqCvS,eAAe,EACvIpC,mBAAmB,EAAEA,mBAAmB,EACxCoC,eAAe;IAAA;EAC3B;EAAC,OApBKiS,gBAAgB;AAAA;AAqBtB;EAAA,QAAA7M,SAAA,oBAAAA,SAAA;AAAA;;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoN,mBAAmB,GAAG;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,eAAe,EAAE;IACb5O,IAAI,EAAE,CAAC;IACP6O,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,CACT;MACI9O,IAAI,EAAE,CAAC;MACP6O,IAAI,EAAE,oBAAoB;MAC1BhC,MAAM,EAAE;QACJ7M,IAAI,EAAE,CAAC;QACP6M,MAAM,EAAE;UAAEkC,SAAS,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAU,CAAC;QACpDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIjP,IAAI,EAAE,CAAC;MACP6O,IAAI,EAAE,MAAM;MACZhC,MAAM,EAAE;QACJ7M,IAAI,EAAE,CAAC;QACP6M,MAAM,EAAE;UAAE,YAAY,EAAE,MAAM;UAAEmC,UAAU,EAAE;QAAS,CAAC;QACtDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIjP,IAAI,EAAE,CAAC;MACPkP,IAAI,EAAE,sBAAsB;MAC5BC,SAAS,EAAE;QAAEnP,IAAI,EAAE,CAAC;QAAE6M,MAAM,EAAE,IAAI;QAAEuC,OAAO,EAAE;MAAM,CAAC;MACpDjK,OAAO,EAAE;IACb,CAAC,EACD;MACInF,IAAI,EAAE,CAAC;MACPkP,IAAI,EAAE,qCAAqC;MAC3CC,SAAS,EAAE;QACPnP,IAAI,EAAE,CAAC;QACP6M,MAAM,EAAE,IAAI;QACZuC,OAAO,EAAE;MACb,CAAC;MACDjK,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAShH,2BAA2B,EAAEG,mCAAmC,EAAEkD,SAAS,EAAE5C,kBAAkB,EAAEJ,gBAAgB,EAAE2O,UAAU,EAAEW,mBAAmB,EAAEhB,iBAAiB,EAAEsB,gBAAgB,EAAEO,mBAAmB,EAAE3Q,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}