import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

// Angular Material Imports
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-layout-new',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatToolbarModule,
    MatSidenavModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    MatBadgeModule,
    MatFormFieldModule,
    MatInputModule,
    MatDividerModule
  ],
  templateUrl: './layout-new.component.html',
  styleUrls: ['./layout-new.component.scss']
})
export class LayoutNewComponent implements OnInit {
  
  // Layout State
  sidebarCollapsed = false;
  isMobile = false;
  sidebarVisible = true;
  
  // Navigation State
  expandedGroups = new Set<string>();
  
  // Search
  globalSearchTerm = '';
  
  // User Info
  currentUser = {
    name: 'أحمد محمد',
    role: 'مدير النظام',
    avatar: 'assets/images/user-avatar.svg'
  };

  constructor() {
    this.checkScreenSize();
  }

  ngOnInit(): void {
    // Initialize expanded groups based on current route
    this.initializeExpandedGroups();
    
    // Load user preferences
    this.loadUserPreferences();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    this.checkScreenSize();
  }

  /**
   * Check screen size and adjust layout accordingly
   */
  private checkScreenSize(): void {
    this.isMobile = window.innerWidth <= 1024;
    
    if (this.isMobile) {
      this.sidebarCollapsed = true;
      this.sidebarVisible = false;
    } else {
      this.sidebarVisible = true;
    }
  }

  /**
   * Toggle sidebar collapsed state
   */
  toggleSidebar(): void {
    if (this.isMobile) {
      this.sidebarVisible = !this.sidebarVisible;
    } else {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      
      // Collapse all groups when sidebar is collapsed
      if (this.sidebarCollapsed) {
        this.expandedGroups.clear();
      }
    }
    
    // Save preference
    this.saveUserPreferences();
  }

  /**
   * Toggle navigation group expansion
   */
  toggleNavGroup(groupName: string): void {
    if (this.sidebarCollapsed) {
      return; // Don't expand groups when sidebar is collapsed
    }

    if (this.expandedGroups.has(groupName)) {
      this.expandedGroups.delete(groupName);
    } else {
      this.expandedGroups.add(groupName);
    }
    
    // Save preference
    this.saveUserPreferences();
  }

  /**
   * Initialize expanded groups based on current route
   */
  private initializeExpandedGroups(): void {
    const currentUrl = window.location.pathname;
    
    // Auto-expand groups based on current route
    if (currentUrl.includes('/pos') || currentUrl.includes('/sales')) {
      this.expandedGroups.add('sales');
    }
    
    if (currentUrl.includes('/products') || currentUrl.includes('/categories') || currentUrl.includes('/inventory')) {
      this.expandedGroups.add('inventory');
    }
    
    if (currentUrl.includes('/purchases') || currentUrl.includes('/purchase-orders')) {
      this.expandedGroups.add('purchases');
    }
    
    if (currentUrl.includes('/accounts') || currentUrl.includes('/journal') || currentUrl.includes('/treasury')) {
      this.expandedGroups.add('finance');
    }
    
    if (currentUrl.includes('/employees') || currentUrl.includes('/payroll') || currentUrl.includes('/attendance')) {
      this.expandedGroups.add('hr');
    }
    
    if (currentUrl.includes('/reports')) {
      this.expandedGroups.add('reports');
    }
  }

  /**
   * Load user preferences from localStorage
   */
  private loadUserPreferences(): void {
    try {
      const preferences = localStorage.getItem('terra-layout-preferences');
      if (preferences) {
        const parsed = JSON.parse(preferences);
        
        if (!this.isMobile) {
          this.sidebarCollapsed = parsed.sidebarCollapsed || false;
        }
        
        if (parsed.expandedGroups && Array.isArray(parsed.expandedGroups)) {
          this.expandedGroups = new Set(parsed.expandedGroups);
        }
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  /**
   * Save user preferences to localStorage
   */
  private saveUserPreferences(): void {
    try {
      const preferences = {
        sidebarCollapsed: this.sidebarCollapsed,
        expandedGroups: Array.from(this.expandedGroups)
      };
      
      localStorage.setItem('terra-layout-preferences', JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  /**
   * Handle global search
   */
  onGlobalSearch(): void {
    if (this.globalSearchTerm.trim()) {
      // Implement global search functionality
      console.log('Searching for:', this.globalSearchTerm);
      
      // You can implement search across different modules here
      // For example, search in products, customers, suppliers, etc.
    }
  }

  /**
   * Handle user logout
   */
  logout(): void {
    // Clear user preferences
    localStorage.removeItem('terra-layout-preferences');
    
    // Implement logout logic
    console.log('User logged out');
    
    // Redirect to login page
    // this.router.navigate(['/login']);
  }

  /**
   * Navigate to user profile
   */
  navigateToProfile(): void {
    // Implement navigation to user profile
    console.log('Navigate to user profile');
  }

  /**
   * Navigate to settings
   */
  navigateToSettings(): void {
    // Implement navigation to settings
    console.log('Navigate to settings');
  }

  /**
   * Handle notification click
   */
  onNotificationClick(notification: any): void {
    // Implement notification handling
    console.log('Notification clicked:', notification);
  }

  /**
   * Mark all notifications as read
   */
  markAllNotificationsAsRead(): void {
    // Implement mark all as read functionality
    console.log('Mark all notifications as read');
  }

  /**
   * Get notification count
   */
  getNotificationCount(): number {
    // Return actual notification count from service
    return 3; // Mock data
  }

  /**
   * Check if current route is active
   */
  isRouteActive(route: string): boolean {
    return window.location.pathname.includes(route);
  }

  /**
   * Get current page title based on route
   */
  getCurrentPageTitle(): string {
    const currentUrl = window.location.pathname;
    
    const routeTitles: { [key: string]: string } = {
      '/dashboard': 'لوحة التحكم',
      '/customers': 'إدارة العملاء',
      '/products': 'إدارة المنتجات',
      '/suppliers': 'إدارة الموردين',
      '/pos': 'نقطة البيع',
      '/sales': 'المبيعات',
      '/purchases': 'المشتريات',
      '/inventory': 'المخزون',
      '/finance': 'المالية',
      '/hr': 'الموارد البشرية',
      '/reports': 'التقارير',
      '/settings': 'الإعدادات'
    };
    
    for (const route in routeTitles) {
      if (currentUrl.includes(route)) {
        return routeTitles[route];
      }
    }
    
    return 'Terra Retail ERP';
  }
}
