using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// ربط المنتجات بالموردين
    /// </summary>
    public class ProductSupplier : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// كود المنتج عند المورد
        /// </summary>
        [MaxLength(50)]
        public string? SupplierProductCode { get; set; }

        /// <summary>
        /// اسم المنتج عند المورد
        /// </summary>
        [MaxLength(200)]
        public string? SupplierProductName { get; set; }

        /// <summary>
        /// سعر الشراء من المورد
        /// </summary>
        public decimal PurchasePrice { get; set; } = 0;

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// الحد الأدنى لكمية الطلب
        /// </summary>
        public decimal? MinOrderQuantity { get; set; }

        /// <summary>
        /// كمية الطلب المفضلة
        /// </summary>
        public decimal? PreferredOrderQuantity { get; set; }

        /// <summary>
        /// مدة التوريد (بالأيام)
        /// </summary>
        public int? LeadTimeDays { get; set; }

        /// <summary>
        /// هل المورد رئيسي لهذا المنتج
        /// </summary>
        public bool IsPrimarySupplier { get; set; } = false;

        /// <summary>
        /// هل المورد نشط لهذا المنتج
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ آخر شراء
        /// </summary>
        public DateTime? LastPurchaseDate { get; set; }

        /// <summary>
        /// آخر سعر شراء
        /// </summary>
        public decimal? LastPurchasePrice { get; set; }

        /// <summary>
        /// إجمالي الكمية المشتراة
        /// </summary>
        public decimal TotalPurchasedQuantity { get; set; } = 0;

        /// <summary>
        /// إجمالي قيمة المشتريات
        /// </summary>
        public decimal TotalPurchaseValue { get; set; } = 0;

        /// <summary>
        /// تقييم المورد لهذا المنتج (1-5)
        /// </summary>
        public decimal? Rating { get; set; }

        /// <summary>
        /// ملاحظات خاصة بالمنتج والمورد
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// شروط خاصة
        /// </summary>
        [MaxLength(1000)]
        public string? SpecialTerms { get; set; }

        /// <summary>
        /// نسبة الخصم المتفق عليها
        /// </summary>
        public decimal? DiscountPercentage { get; set; }

        /// <summary>
        /// تاريخ بداية التعامل
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء التعامل
        /// </summary>
        public DateTime? EndDate { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Supplier Supplier { get; set; } = null!;
    }
}
