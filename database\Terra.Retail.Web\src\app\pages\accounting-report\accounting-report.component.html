<div class="accounting-report-container" dir="rtl">
  <!-- Header -->
  <div class="report-header">
    <mat-card class="header-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>account_balance</mat-icon>
          التقرير المحاسبي الشامل
        </mat-card-title>
        <mat-card-subtitle>
          نظام Terra Retail ERP - تحليل شامل للحسابات والمعاملات المالية
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-actions>
        <button mat-raised-button color="primary" (click)="refreshData()" [disabled]="isLoading">
          <mat-icon>refresh</mat-icon>
          تحديث البيانات
        </button>
        <button mat-raised-button color="accent" (click)="exportToExcel()" [disabled]="isLoading">
          <mat-icon>file_download</mat-icon>
          تصدير Excel
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>جاري تحميل البيانات المحاسبية...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="report-content">
    
    <!-- Tabs Container -->
    <mat-tab-group mat-align-tabs="center" class="report-tabs">
      
      <!-- Tab 1: Account Summary -->
      <mat-tab label="ملخص الحسابات">
        <div class="tab-content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>ملخص الأرصدة حسب نوع الحساب</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="accountSummary" class="summary-table">
                  
                  <ng-container matColumnDef="accountType">
                    <th mat-header-cell *matHeaderCellDef>نوع الحساب</th>
                    <td mat-cell *matCellDef="let element">
                      <mat-chip [color]="getAccountTypeColor(element.accountType)" selected>
                        {{element.accountType}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="accountCount">
                    <th mat-header-cell *matHeaderCellDef>عدد الحسابات</th>
                    <td mat-cell *matCellDef="let element">{{element.accountCount}}</td>
                  </ng-container>

                  <ng-container matColumnDef="totalDebit">
                    <th mat-header-cell *matHeaderCellDef>إجمالي المدين</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.totalDebit)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="totalCredit">
                    <th mat-header-cell *matHeaderCellDef>إجمالي الدائن</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.totalCredit)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="netBalance">
                    <th mat-header-cell *matHeaderCellDef>صافي الرصيد</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell" [style]="getBalanceColor(element.netBalance)">
                      {{formatCurrency(element.netBalance)}}
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="summaryColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: summaryColumns;"></tr>
                </table>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Tab 2: Chart of Accounts -->
      <mat-tab label="دليل الحسابات">
        <div class="tab-content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>دليل الحسابات مع الأرصدة</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="chartOfAccounts" class="accounts-table">
                  
                  <ng-container matColumnDef="accountCode">
                    <th mat-header-cell *matHeaderCellDef>كود الحساب</th>
                    <td mat-cell *matCellDef="let element">{{element.accountCode}}</td>
                  </ng-container>

                  <ng-container matColumnDef="accountNameAr">
                    <th mat-header-cell *matHeaderCellDef>اسم الحساب</th>
                    <td mat-cell *matCellDef="let element">{{element.accountNameAr}}</td>
                  </ng-container>

                  <ng-container matColumnDef="accountType">
                    <th mat-header-cell *matHeaderCellDef>نوع الحساب</th>
                    <td mat-cell *matCellDef="let element">
                      <mat-chip [color]="getAccountTypeColor(element.accountType)" selected>
                        {{element.accountType}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="debitBalance">
                    <th mat-header-cell *matHeaderCellDef>الرصيد المدين</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.debitBalance)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="creditBalance">
                    <th mat-header-cell *matHeaderCellDef>الرصيد الدائن</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.creditBalance)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="netBalance">
                    <th mat-header-cell *matHeaderCellDef>صافي الرصيد</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell" [style]="getBalanceColor(element.netBalance)">
                      {{formatCurrency(element.netBalance)}}
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="accountColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: accountColumns;"></tr>
                </table>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Tab 3: Transactions -->
      <mat-tab label="المعاملات المالية">
        <div class="tab-content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>المعاملات المالية مع التفاصيل</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="transactions" class="transactions-table">
                  
                  <ng-container matColumnDef="voucherId">
                    <th mat-header-cell *matHeaderCellDef>رقم السند</th>
                    <td mat-cell *matCellDef="let element">{{element.voucherId}}</td>
                  </ng-container>

                  <ng-container matColumnDef="transactionDate">
                    <th mat-header-cell *matHeaderCellDef>التاريخ</th>
                    <td mat-cell *matCellDef="let element">{{formatDate(element.transactionDate)}}</td>
                  </ng-container>

                  <ng-container matColumnDef="transactionType">
                    <th mat-header-cell *matHeaderCellDef>نوع المعاملة</th>
                    <td mat-cell *matCellDef="let element">{{element.transactionType}}</td>
                  </ng-container>

                  <ng-container matColumnDef="accountName">
                    <th mat-header-cell *matHeaderCellDef>الحساب</th>
                    <td mat-cell *matCellDef="let element">{{element.accountName}}</td>
                  </ng-container>

                  <ng-container matColumnDef="debit">
                    <th mat-header-cell *matHeaderCellDef>مدين</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.debit)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="credit">
                    <th mat-header-cell *matHeaderCellDef>دائن</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.credit)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="notes">
                    <th mat-header-cell *matHeaderCellDef>البيان</th>
                    <td mat-cell *matCellDef="let element" class="notes-cell">{{element.notes}}</td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="transactionColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: transactionColumns;"></tr>
                </table>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Tab 4: Transaction Types -->
      <mat-tab label="أنواع المعاملات">
        <div class="tab-content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>أنواع المعاملات مع عدد الاستخدام</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="table-container">
                <table mat-table [dataSource]="transactionTypes" class="types-table">
                  
                  <ng-container matColumnDef="nameAr">
                    <th mat-header-cell *matHeaderCellDef>نوع المعاملة</th>
                    <td mat-cell *matCellDef="let element">{{element.nameAr}}</td>
                  </ng-container>

                  <ng-container matColumnDef="transactionCount">
                    <th mat-header-cell *matHeaderCellDef>عدد المعاملات</th>
                    <td mat-cell *matCellDef="let element" class="count-cell">
                      <mat-chip color="primary" selected>{{element.transactionCount}}</mat-chip>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="totalDebit">
                    <th mat-header-cell *matHeaderCellDef>إجمالي المدين</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.totalDebit)}}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="totalCredit">
                    <th mat-header-cell *matHeaderCellDef>إجمالي الدائن</th>
                    <td mat-cell *matCellDef="let element" class="amount-cell">
                      {{formatCurrency(element.totalCredit)}}
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="typeColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: typeColumns;"></tr>
                </table>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

    </mat-tab-group>
  </div>
</div>
