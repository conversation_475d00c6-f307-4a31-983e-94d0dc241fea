using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الأكواد البديلة للمنتجات
    /// </summary>
    public class ProductCode : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// الكود البديل
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// نوع الكود
        /// </summary>
        public ProductCodeType CodeType { get; set; }

        /// <summary>
        /// وصف الكود
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// هل الكود نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// معرف المورد (إذا كان الكود خاص بمورد معين)
        /// </summary>
        public int? SupplierId { get; set; }

        /// <summary>
        /// تاريخ انتهاء صلاحية الكود
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Supplier? Supplier { get; set; }
    }

    /// <summary>
    /// أنواع أكواد المنتجات
    /// </summary>
    public enum ProductCodeType
    {
        /// <summary>
        /// باركود
        /// </summary>
        Barcode = 1,

        /// <summary>
        /// كود المورد
        /// </summary>
        SupplierCode = 2,

        /// <summary>
        /// كود الشركة المصنعة
        /// </summary>
        ManufacturerCode = 3,

        /// <summary>
        /// كود داخلي بديل
        /// </summary>
        AlternativeCode = 4,

        /// <summary>
        /// رقم الموديل
        /// </summary>
        ModelNumber = 5,

        /// <summary>
        /// الرقم التسلسلي
        /// </summary>
        SerialNumber = 6,

        /// <summary>
        /// كود QR
        /// </summary>
        QRCode = 7
    }
}
