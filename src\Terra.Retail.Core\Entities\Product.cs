using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// المنتجات
    /// </summary>
    public class Product : BaseEntity
    {
        /// <summary>
        /// كود المنتج (فريد)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم المنتج بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المنتج بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? NameEn { get; set; }

        /// <summary>
        /// وصف المنتج
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// الباركود
        /// </summary>
        [MaxLength(50)]
        public string? Barcode { get; set; }

        /// <summary>
        /// الفئة الرئيسية
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// وحدة القياس
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// هل المنتج يباع بالوزن
        /// </summary>
        public bool IsByWeight { get; set; } = false;

        /// <summary>
        /// سعر التكلفة
        /// </summary>
        public decimal CostPrice { get; set; } = 0;

        /// <summary>
        /// السعر الأساسي
        /// </summary>
        public decimal BasePrice { get; set; } = 0;

        /// <summary>
        /// هامش الربح (%)
        /// </summary>
        public decimal ProfitMargin { get; set; } = 0;

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public decimal MinimumStock { get; set; } = 0;

        /// <summary>
        /// الحد الأقصى للمخزون
        /// </summary>
        public decimal MaximumStock { get; set; } = 0;

        /// <summary>
        /// نقطة إعادة الطلب
        /// </summary>
        public decimal ReorderPoint { get; set; } = 0;

        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل المنتج قابل للبيع
        /// </summary>
        public bool IsSellable { get; set; } = true;

        /// <summary>
        /// هل المنتج قابل للشراء
        /// </summary>
        public bool IsPurchasable { get; set; } = true;

        /// <summary>
        /// هل يتم تتبع المخزون
        /// </summary>
        public bool TrackInventory { get; set; } = true;

        /// <summary>
        /// صورة المنتج الرئيسية
        /// </summary>
        [MaxLength(500)]
        public string? MainImage { get; set; }

        /// <summary>
        /// كلمات مفتاحية للبحث
        /// </summary>
        [MaxLength(500)]
        public string? Keywords { get; set; }

        /// <summary>
        /// الوزن (بالجرام)
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// الطول (بالسنتيمتر)
        /// </summary>
        public decimal? Length { get; set; }

        /// <summary>
        /// العرض (بالسنتيمتر)
        /// </summary>
        public decimal? Width { get; set; }

        /// <summary>
        /// الارتفاع (بالسنتيمتر)
        /// </summary>
        public decimal? Height { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية (للمنتجات القابلة للتلف)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// عدد أيام الصلاحية
        /// </summary>
        public int? ShelfLifeDays { get; set; }

        /// <summary>
        /// درجة حرارة التخزين المطلوبة
        /// </summary>
        [MaxLength(50)]
        public string? StorageTemperature { get; set; }

        /// <summary>
        /// ملاحظات خاصة
        /// </summary>
        [MaxLength(1000)]
        public string? SpecialNotes { get; set; }

        // Navigation Properties
        public virtual Category Category { get; set; } = null!;
        public virtual Unit Unit { get; set; } = null!;
        public virtual ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();
        public virtual ICollection<ProductCode> AlternativeCodes { get; set; } = new List<ProductCode>();
        public virtual ICollection<ProductSupplier> Suppliers { get; set; } = new List<ProductSupplier>();
        public virtual ICollection<ProductBranchPrice> BranchPrices { get; set; } = new List<ProductBranchPrice>();
        public virtual ICollection<ProductStock> Stock { get; set; } = new List<ProductStock>();
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
    }
}
