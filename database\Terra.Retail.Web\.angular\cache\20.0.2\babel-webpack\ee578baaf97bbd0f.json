{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/table\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/checkbox\";\nfunction SupplierTypesComponent_mat_card_19_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_mat_card_19_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_mat_card_19_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_mat_card_19_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 16)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 17)(9, \"div\", 18)(10, \"mat-form-field\", 19)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 20);\n    i0.ɵɵelementStart(14, \"mat-icon\", 21);\n    i0.ɵɵtext(15, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SupplierTypesComponent_mat_card_19_mat_error_16_Template, 2, 0, \"mat-error\", 22)(17, SupplierTypesComponent_mat_card_19_mat_error_17_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-form-field\", 19)(19, \"mat-label\");\n    i0.ɵɵtext(20, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 23);\n    i0.ɵɵelementStart(22, \"mat-icon\", 21);\n    i0.ɵɵtext(23, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SupplierTypesComponent_mat_card_19_mat_error_24_Template, 2, 0, \"mat-error\", 22)(25, SupplierTypesComponent_mat_card_19_mat_error_25_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 24)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"\\u0627\\u0644\\u0648\\u0635\\u0641\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"textarea\", 25);\n    i0.ɵɵelementStart(30, \"mat-icon\", 21);\n    i0.ɵɵtext(31, \"description\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 26)(33, \"mat-checkbox\", 27);\n    i0.ɵɵtext(34, \" \\u0646\\u0648\\u0639 \\u0645\\u0648\\u0631\\u062F \\u0646\\u0634\\u0637 \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(35, \"mat-card-actions\", 28)(36, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function SupplierTypesComponent_mat_card_19_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelForm());\n    });\n    i0.ɵɵelementStart(37, \"mat-icon\");\n    i0.ɵɵtext(38, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"\\u0625\\u0644\\u063A\\u0627\\u0621\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function SupplierTypesComponent_mat_card_19_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveSupplierType());\n    });\n    i0.ɵɵelementStart(42, \"mat-icon\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.editingType ? \"edit\" : \"add\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingType ? \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0646\\u0648\\u0639 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.supplierTypeForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.supplierTypeForm.get(\"nameAr\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.supplierTypeForm.get(\"nameAr\")) == null ? null : tmp_5_0.hasError(\"minlength\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.supplierTypeForm.get(\"nameEn\")) == null ? null : tmp_6_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r1.supplierTypeForm.get(\"nameEn\")) == null ? null : tmp_7_0.hasError(\"minlength\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingType ? \"save\" : \"add\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingType ? \"\\u062A\\u062D\\u062F\\u064A\\u062B\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629\");\n  }\n}\nfunction SupplierTypesComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"mat-spinner\", 32);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SupplierTypesComponent_div_29_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_div_29_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(type_r3.NameAr);\n  }\n}\nfunction SupplierTypesComponent_div_29_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_div_29_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(type_r4.NameEn);\n  }\n}\nfunction SupplierTypesComponent_div_29_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0648\\u0635\\u0641\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_div_29_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(type_r5.Description || \"-\");\n  }\n}\nfunction SupplierTypesComponent_div_29_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_div_29_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 46)(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const type_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", type_r6.IsActive ? \"active\" : \"inactive\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r6.IsActive ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n  }\n}\nfunction SupplierTypesComponent_div_29_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SupplierTypesComponent_div_29_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 46)(1, \"div\", 48)(2, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function SupplierTypesComponent_div_29_td_16_Template_button_click_2_listener() {\n      const type_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editSupplierType(type_r8));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SupplierTypesComponent_div_29_td_16_Template_button_click_5_listener() {\n      const type_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteSupplierType(type_r8));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SupplierTypesComponent_div_29_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction SupplierTypesComponent_div_29_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 52);\n  }\n}\nfunction SupplierTypesComponent_div_29_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"inbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0623\\u0646\\u0648\\u0627\\u0639 \\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0623\\u064A \\u0623\\u0646\\u0648\\u0627\\u0639 \\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0628\\u0639\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function SupplierTypesComponent_div_29_div_19_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showAddSupplierType());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0646\\u0648\\u0639 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SupplierTypesComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"table\", 34);\n    i0.ɵɵelementContainerStart(2, 35);\n    i0.ɵɵtemplate(3, SupplierTypesComponent_div_29_th_3_Template, 2, 0, \"th\", 36)(4, SupplierTypesComponent_div_29_td_4_Template, 2, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 38);\n    i0.ɵɵtemplate(6, SupplierTypesComponent_div_29_th_6_Template, 2, 0, \"th\", 36)(7, SupplierTypesComponent_div_29_td_7_Template, 2, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 39);\n    i0.ɵɵtemplate(9, SupplierTypesComponent_div_29_th_9_Template, 2, 0, \"th\", 36)(10, SupplierTypesComponent_div_29_td_10_Template, 2, 1, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 40);\n    i0.ɵɵtemplate(12, SupplierTypesComponent_div_29_th_12_Template, 2, 0, \"th\", 36)(13, SupplierTypesComponent_div_29_td_13_Template, 3, 2, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 41);\n    i0.ɵɵtemplate(15, SupplierTypesComponent_div_29_th_15_Template, 2, 0, \"th\", 36)(16, SupplierTypesComponent_div_29_td_16_Template, 8, 0, \"td\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(17, SupplierTypesComponent_div_29_tr_17_Template, 1, 0, \"tr\", 42)(18, SupplierTypesComponent_div_29_tr_18_Template, 1, 0, \"tr\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SupplierTypesComponent_div_29_div_19_Template, 12, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.supplierTypes);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.supplierTypes.length === 0);\n  }\n}\nfunction SupplierTypesComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"mat-spinner\", 56);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SupplierTypesComponent = /*#__PURE__*/(() => {\n  class SupplierTypesComponent {\n    http;\n    router;\n    fb;\n    snackBar;\n    dialog;\n    // Component State\n    isLoading = false;\n    showAddForm = false;\n    editingType = null;\n    // Data\n    supplierTypes = [];\n    // Form\n    supplierTypeForm;\n    // Table Configuration\n    displayedColumns = ['nameAr', 'nameEn', 'description', 'isActive', 'actions'];\n    // Subscriptions\n    subscriptions = [];\n    constructor(http, router, fb, snackBar, dialog) {\n      this.http = http;\n      this.router = router;\n      this.fb = fb;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.supplierTypeForm = this.createForm();\n    }\n    ngOnInit() {\n      this.loadSupplierTypes();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Create reactive form\n     */\n    createForm() {\n      return this.fb.group({\n        nameAr: ['', [Validators.required, Validators.minLength(2)]],\n        nameEn: ['', [Validators.required, Validators.minLength(2)]],\n        description: [''],\n        isActive: [true]\n      });\n    }\n    /**\n     * Load supplier types\n     */\n    loadSupplierTypes() {\n      this.isLoading = true;\n      const sub = this.http.get('http://localhost:5127/api/simple/supplier-types').subscribe({\n        next: response => {\n          console.log('Supplier types response:', response);\n          this.supplierTypes = response.supplierTypes || [];\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading supplier types:', error);\n          this.showError('خطأ في تحميل أنواع الموردين');\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Show add form\n     */\n    showAddSupplierType() {\n      this.showAddForm = true;\n      this.editingType = null;\n      this.supplierTypeForm.reset();\n      this.supplierTypeForm.patchValue({\n        isActive: true\n      });\n    }\n    /**\n     * Edit supplier type\n     */\n    editSupplierType(supplierType) {\n      this.showAddForm = true;\n      this.editingType = supplierType;\n      this.supplierTypeForm.patchValue({\n        nameAr: supplierType.NameAr,\n        nameEn: supplierType.NameEn,\n        description: supplierType.Description || '',\n        isActive: supplierType.IsActive\n      });\n    }\n    /**\n     * Save supplier type\n     */\n    saveSupplierType() {\n      if (this.supplierTypeForm.valid) {\n        this.isLoading = true;\n        const formValue = this.supplierTypeForm.value;\n        const request = {\n          nameAr: formValue.nameAr,\n          nameEn: formValue.nameEn,\n          description: formValue.description || null,\n          isActive: formValue.isActive\n        };\n        const apiCall = this.editingType ? this.http.put(`http://localhost:5127/api/simple/supplier-types/${this.editingType.Id}`, request) : this.http.post('http://localhost:5127/api/simple/supplier-types', request);\n        const sub = apiCall.subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess(this.editingType ? 'تم تحديث نوع المورد بنجاح' : 'تم إضافة نوع المورد بنجاح');\n            this.showAddForm = false;\n            this.loadSupplierTypes();\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error saving supplier type:', error);\n            this.showError('خطأ في حفظ نوع المورد');\n          }\n        });\n        this.subscriptions.push(sub);\n      } else {\n        this.markFormGroupTouched();\n        this.showError('يرجى تصحيح الأخطاء في النموذج');\n      }\n    }\n    /**\n     * Delete supplier type\n     */\n    deleteSupplierType(supplierType) {\n      if (confirm(`هل أنت متأكد من حذف نوع المورد \"${supplierType.NameAr}\"؟`)) {\n        this.isLoading = true;\n        const sub = this.http.delete(`http://localhost:5127/api/simple/supplier-types/${supplierType.Id}`).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess('تم حذف نوع المورد بنجاح');\n            this.loadSupplierTypes();\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error deleting supplier type:', error);\n            this.showError('خطأ في حذف نوع المورد');\n          }\n        });\n        this.subscriptions.push(sub);\n      }\n    }\n    /**\n     * Cancel form\n     */\n    cancelForm() {\n      this.showAddForm = false;\n      this.editingType = null;\n      this.supplierTypeForm.reset();\n    }\n    /**\n     * Go back to suppliers\n     */\n    goBack() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Mark all form fields as touched\n     */\n    markFormGroupTouched() {\n      Object.keys(this.supplierTypeForm.controls).forEach(key => {\n        const control = this.supplierTypeForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    /**\n     * Show success message\n     */\n    showSuccess(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        panelClass: ['success-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    /**\n     * Show error message\n     */\n    showError(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 5000,\n        panelClass: ['error-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    static ɵfac = function SupplierTypesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SupplierTypesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierTypesComponent,\n      selectors: [[\"app-supplier-types\"]],\n      decls: 31,\n      vars: 4,\n      consts: [[1, \"supplier-types-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-btn\", 3, \"click\"], [1, \"content\"], [\"class\", \"form-card\", 4, \"ngIf\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"form-card\"], [1, \"supplier-type-form\", 3, \"formGroup\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"nameAr\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\", \"required\", \"\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"nameEn\", \"placeholder\", \"Enter supplier type name in English\", \"required\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0648\\u0635\\u0641 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"], [1, \"checkbox-field\"], [\"formControlName\", \"isActive\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"supplier-types-table\", 3, \"dataSource\"], [\"matColumnDef\", \"nameAr\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"nameEn\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"isActive\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function SupplierTypesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SupplierTypesComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n          i0.ɵɵtext(9, \"\\u0623\\u0646\\u0648\\u0627\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 7);\n          i0.ɵɵtext(11, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0623\\u0646\\u0648\\u0627\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SupplierTypesComponent_Template_button_click_13_listener() {\n            return ctx.showAddSupplierType();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\");\n          i0.ɵɵtext(17, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0646\\u0648\\u0639 \\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(18, \"div\", 10);\n          i0.ɵɵtemplate(19, SupplierTypesComponent_mat_card_19_Template, 46, 10, \"mat-card\", 11);\n          i0.ɵɵelementStart(20, \"mat-card\", 12)(21, \"mat-card-header\")(22, \"mat-card-title\")(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0623\\u0646\\u0648\\u0627\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"mat-card-content\");\n          i0.ɵɵtemplate(28, SupplierTypesComponent_div_28_Template, 4, 0, \"div\", 13)(29, SupplierTypesComponent_div_29_Template, 20, 4, \"div\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(30, SupplierTypesComponent_div_30_Template, 4, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatFormFieldModule, i10.MatFormField, i10.MatLabel, i10.MatError, i10.MatSuffix, MatInputModule, i11.MatInput, MatTableModule, i12.MatTable, i12.MatHeaderCellDef, i12.MatHeaderRowDef, i12.MatColumnDef, i12.MatCellDef, i12.MatRowDef, i12.MatHeaderCell, i12.MatCell, i12.MatHeaderRow, i12.MatRow, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, i13.MatProgressSpinner, MatCheckboxModule, i14.MatCheckbox],\n      styles: [\"\\n\\n.supplier-types-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--warning-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateX(-2px);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: var(--spacing-xs) 0 0;\\n  opacity: 0.9;\\n}\\n.page-header[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--border-color);\\n  overflow: hidden;\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--primary-700);\\n  margin: 0;\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  background: var(--surface-50);\\n  border-top: 1px solid var(--border-color);\\n}\\n\\n\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-lg);\\n  align-items: start;\\n}\\n.form-grid[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.form-grid[_ngcontent-%COMP%]   .checkbox-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: var(--spacing-md) 0;\\n}\\n\\n\\n\\n.table-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--border-color);\\n  overflow: hidden;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--primary-700);\\n  margin: 0;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.supplier-types-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: white;\\n}\\n.supplier-types-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: var(--surface-100);\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  padding: var(--spacing-lg);\\n  border-bottom: 2px solid var(--border-color);\\n}\\n.supplier-types-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg);\\n  border-bottom: 1px solid var(--border-light);\\n  vertical-align: middle;\\n}\\n.supplier-types-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background: var(--surface-50);\\n}\\n\\n\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-full);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.status-badge.active[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n  border: 1px solid var(--success-200);\\n}\\n.status-badge.inactive[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n  border: 1px solid var(--error-200);\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n  align-items: center;\\n}\\n\\n\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--spacing-4xl);\\n  color: var(--text-secondary);\\n}\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: var(--text-disabled);\\n  margin-bottom: var(--spacing-lg);\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0 0 var(--spacing-sm);\\n  color: var(--text-primary);\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0 0 var(--spacing-xl);\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4xl);\\n  color: var(--text-secondary);\\n}\\n.loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n.loading-overlay[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-primary);\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .supplier-types-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .supplier-types-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md);\\n    font-size: 0.875rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SupplierTypesComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatTableModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "SupplierTypesComponent_mat_card_19_mat_error_16_Template", "SupplierTypesComponent_mat_card_19_mat_error_17_Template", "SupplierTypesComponent_mat_card_19_mat_error_24_Template", "SupplierTypesComponent_mat_card_19_mat_error_25_Template", "ɵɵlistener", "SupplierTypesComponent_mat_card_19_Template_button_click_36_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "cancelForm", "SupplierTypesComponent_mat_card_19_Template_button_click_41_listener", "saveSupplierType", "ɵɵadvance", "ɵɵtextInterpolate", "editingType", "ɵɵproperty", "supplierTypeForm", "tmp_4_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_5_0", "tmp_6_0", "tmp_7_0", "isLoading", "type_r3", "NameAr", "type_r4", "NameEn", "type_r5", "Description", "type_r6", "IsActive", "ɵɵtextInterpolate1", "SupplierTypesComponent_div_29_td_16_Template_button_click_2_listener", "type_r8", "_r7", "$implicit", "editSupplierType", "SupplierTypesComponent_div_29_td_16_Template_button_click_5_listener", "deleteSupplierType", "SupplierTypesComponent_div_29_div_19_Template_button_click_7_listener", "_r9", "showAddSupplierType", "ɵɵelementContainerStart", "SupplierTypesComponent_div_29_th_3_Template", "SupplierTypesComponent_div_29_td_4_Template", "SupplierTypesComponent_div_29_th_6_Template", "SupplierTypesComponent_div_29_td_7_Template", "SupplierTypesComponent_div_29_th_9_Template", "SupplierTypesComponent_div_29_td_10_Template", "SupplierTypesComponent_div_29_th_12_Template", "SupplierTypesComponent_div_29_td_13_Template", "SupplierTypesComponent_div_29_th_15_Template", "SupplierTypesComponent_div_29_td_16_Template", "SupplierTypesComponent_div_29_tr_17_Template", "SupplierTypesComponent_div_29_tr_18_Template", "SupplierTypesComponent_div_29_div_19_Template", "supplierTypes", "displayedColumns", "length", "SupplierTypesComponent", "http", "router", "fb", "snackBar", "dialog", "showAddForm", "subscriptions", "constructor", "createForm", "ngOnInit", "loadSupplierTypes", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "group", "nameAr", "required", "<PERSON><PERSON><PERSON><PERSON>", "nameEn", "description", "isActive", "subscribe", "next", "response", "console", "log", "error", "showError", "push", "reset", "patchValue", "supplierType", "valid", "formValue", "value", "request", "apiCall", "put", "Id", "post", "showSuccess", "markFormGroupTouched", "confirm", "delete", "goBack", "navigate", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "open", "duration", "panelClass", "horizontalPosition", "verticalPosition", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "Router", "i3", "FormBuilder", "i4", "MatSnackBar", "i5", "MatDialog", "selectors", "decls", "vars", "consts", "template", "SupplierTypesComponent_Template", "rf", "ctx", "SupplierTypesComponent_Template_button_click_4_listener", "SupplierTypesComponent_Template_button_click_13_listener", "SupplierTypesComponent_mat_card_19_Template", "SupplierTypesComponent_div_28_Template", "SupplierTypesComponent_div_29_Template", "SupplierTypesComponent_div_30_Template", "i6", "Ng<PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i7", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatInput", "i12", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i13", "MatProgressSpinner", "i14", "MatCheckbox", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-types\\supplier-types.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-types\\supplier-types.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\n// Interfaces\ninterface SupplierType {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n  Description?: string;\n  IsActive: boolean;\n  CreatedAt?: string;\n  UpdatedAt?: string;\n}\n\n@Component({\n  selector: 'app-supplier-types',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './supplier-types.component.html',\n  styleUrls: ['./supplier-types.component.scss']\n})\nexport class SupplierTypesComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  showAddForm = false;\n  editingType: SupplierType | null = null;\n  \n  // Data\n  supplierTypes: SupplierType[] = [];\n  \n  // Form\n  supplierTypeForm: FormGroup;\n  \n  // Table Configuration\n  displayedColumns: string[] = ['nameAr', 'nameEn', 'description', 'isActive', 'actions'];\n  \n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n    private fb: FormBuilder,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog\n  ) {\n    this.supplierTypeForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.loadSupplierTypes();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      nameAr: ['', [Validators.required, Validators.minLength(2)]],\n      nameEn: ['', [Validators.required, Validators.minLength(2)]],\n      description: [''],\n      isActive: [true]\n    });\n  }\n\n  /**\n   * Load supplier types\n   */\n  loadSupplierTypes(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({\n      next: (response) => {\n        console.log('Supplier types response:', response);\n        this.supplierTypes = response.supplierTypes || [];\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading supplier types:', error);\n        this.showError('خطأ في تحميل أنواع الموردين');\n        this.isLoading = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Show add form\n   */\n  showAddSupplierType(): void {\n    this.showAddForm = true;\n    this.editingType = null;\n    this.supplierTypeForm.reset();\n    this.supplierTypeForm.patchValue({ isActive: true });\n  }\n\n  /**\n   * Edit supplier type\n   */\n  editSupplierType(supplierType: SupplierType): void {\n    this.showAddForm = true;\n    this.editingType = supplierType;\n    this.supplierTypeForm.patchValue({\n      nameAr: supplierType.NameAr,\n      nameEn: supplierType.NameEn,\n      description: supplierType.Description || '',\n      isActive: supplierType.IsActive\n    });\n  }\n\n  /**\n   * Save supplier type\n   */\n  saveSupplierType(): void {\n    if (this.supplierTypeForm.valid) {\n      this.isLoading = true;\n      \n      const formValue = this.supplierTypeForm.value;\n      const request = {\n        nameAr: formValue.nameAr,\n        nameEn: formValue.nameEn,\n        description: formValue.description || null,\n        isActive: formValue.isActive\n      };\n\n      const apiCall = this.editingType \n        ? this.http.put<any>(`http://localhost:5127/api/simple/supplier-types/${this.editingType.Id}`, request)\n        : this.http.post<any>('http://localhost:5127/api/simple/supplier-types', request);\n\n      const sub = apiCall.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.showSuccess(this.editingType ? 'تم تحديث نوع المورد بنجاح' : 'تم إضافة نوع المورد بنجاح');\n          this.showAddForm = false;\n          this.loadSupplierTypes();\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error saving supplier type:', error);\n          this.showError('خطأ في حفظ نوع المورد');\n        }\n      });\n      this.subscriptions.push(sub);\n    } else {\n      this.markFormGroupTouched();\n      this.showError('يرجى تصحيح الأخطاء في النموذج');\n    }\n  }\n\n  /**\n   * Delete supplier type\n   */\n  deleteSupplierType(supplierType: SupplierType): void {\n    if (confirm(`هل أنت متأكد من حذف نوع المورد \"${supplierType.NameAr}\"؟`)) {\n      this.isLoading = true;\n\n      const sub = this.http.delete(`http://localhost:5127/api/simple/supplier-types/${supplierType.Id}`).subscribe({\n        next: (response: any) => {\n          this.isLoading = false;\n          this.showSuccess('تم حذف نوع المورد بنجاح');\n          this.loadSupplierTypes();\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error deleting supplier type:', error);\n          this.showError('خطأ في حذف نوع المورد');\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n  }\n\n  /**\n   * Cancel form\n   */\n  cancelForm(): void {\n    this.showAddForm = false;\n    this.editingType = null;\n    this.supplierTypeForm.reset();\n  }\n\n  /**\n   * Go back to suppliers\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Mark all form fields as touched\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.supplierTypeForm.controls).forEach(key => {\n      const control = this.supplierTypeForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Show success message\n   */\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      panelClass: ['success-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Show error message\n   */\n  private showError(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 5000,\n      panelClass: ['error-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n}\n", "<!-- Terra Retail ERP - Supplier Types -->\n<div class=\"supplier-types-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">أنواع الموردين</h1>\n          <p class=\"page-subtitle\">إدارة أنواع الموردين في النظام</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" class=\"add-btn\" (click)=\"showAddSupplierType()\">\n          <mat-icon>add</mat-icon>\n          <span>إضافة نوع مورد</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content -->\n  <div class=\"content\">\n    \n    <!-- Add/Edit Form -->\n    <mat-card class=\"form-card\" *ngIf=\"showAddForm\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>{{ editingType ? 'edit' : 'add' }}</mat-icon>\n          <span>{{ editingType ? 'تعديل نوع المورد' : 'إضافة نوع مورد جديد' }}</span>\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <form [formGroup]=\"supplierTypeForm\" class=\"supplier-type-form\">\n          <div class=\"form-grid\">\n            \n            <!-- Arabic Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الاسم بالعربية *</mat-label>\n              <input matInput formControlName=\"nameAr\" placeholder=\"أدخل اسم نوع المورد بالعربية\" required>\n              <mat-icon matSuffix>business</mat-icon>\n              <mat-error *ngIf=\"supplierTypeForm.get('nameAr')?.hasError('required')\">\n                الاسم بالعربية مطلوب\n              </mat-error>\n              <mat-error *ngIf=\"supplierTypeForm.get('nameAr')?.hasError('minlength')\">\n                الاسم يجب أن يكون حرفين على الأقل\n              </mat-error>\n            </mat-form-field>\n\n            <!-- English Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الاسم بالإنجليزية *</mat-label>\n              <input matInput formControlName=\"nameEn\" placeholder=\"Enter supplier type name in English\" required>\n              <mat-icon matSuffix>business</mat-icon>\n              <mat-error *ngIf=\"supplierTypeForm.get('nameEn')?.hasError('required')\">\n                الاسم بالإنجليزية مطلوب\n              </mat-error>\n              <mat-error *ngIf=\"supplierTypeForm.get('nameEn')?.hasError('minlength')\">\n                الاسم يجب أن يكون حرفين على الأقل\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Description -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>الوصف</mat-label>\n              <textarea matInput formControlName=\"description\" rows=\"3\" \n                        placeholder=\"أدخل وصف نوع المورد (اختياري)\"></textarea>\n              <mat-icon matSuffix>description</mat-icon>\n            </mat-form-field>\n\n            <!-- Is Active -->\n            <div class=\"checkbox-field\">\n              <mat-checkbox formControlName=\"isActive\">\n                نوع مورد نشط\n              </mat-checkbox>\n            </div>\n\n          </div>\n        </form>\n      </mat-card-content>\n      <mat-card-actions align=\"end\">\n        <button mat-button (click)=\"cancelForm()\">\n          <mat-icon>cancel</mat-icon>\n          <span>إلغاء</span>\n        </button>\n        <button mat-raised-button color=\"primary\" (click)=\"saveSupplierType()\" [disabled]=\"isLoading\">\n          <mat-icon>{{ editingType ? 'save' : 'add' }}</mat-icon>\n          <span>{{ editingType ? 'تحديث' : 'إضافة' }}</span>\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <!-- Supplier Types Table -->\n    <mat-card class=\"table-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>list</mat-icon>\n          <span>قائمة أنواع الموردين</span>\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        \n        <!-- Loading Spinner -->\n        <div class=\"loading-container\" *ngIf=\"isLoading\">\n          <mat-spinner diameter=\"40\"></mat-spinner>\n          <p>جاري تحميل البيانات...</p>\n        </div>\n\n        <!-- Table -->\n        <div class=\"table-container\" *ngIf=\"!isLoading\">\n          <table mat-table [dataSource]=\"supplierTypes\" class=\"supplier-types-table\">\n\n            <!-- Arabic Name Column -->\n            <ng-container matColumnDef=\"nameAr\">\n              <th mat-header-cell *matHeaderCellDef>الاسم بالعربية</th>\n              <td mat-cell *matCellDef=\"let type\">{{ type.NameAr }}</td>\n            </ng-container>\n\n            <!-- English Name Column -->\n            <ng-container matColumnDef=\"nameEn\">\n              <th mat-header-cell *matHeaderCellDef>الاسم بالإنجليزية</th>\n              <td mat-cell *matCellDef=\"let type\">{{ type.NameEn }}</td>\n            </ng-container>\n\n            <!-- Description Column -->\n            <ng-container matColumnDef=\"description\">\n              <th mat-header-cell *matHeaderCellDef>الوصف</th>\n              <td mat-cell *matCellDef=\"let type\">{{ type.Description || '-' }}</td>\n            </ng-container>\n\n            <!-- Status Column -->\n            <ng-container matColumnDef=\"isActive\">\n              <th mat-header-cell *matHeaderCellDef>الحالة</th>\n              <td mat-cell *matCellDef=\"let type\">\n                <span class=\"status-badge\" [ngClass]=\"type.IsActive ? 'active' : 'inactive'\">\n                  {{ type.IsActive ? 'نشط' : 'غير نشط' }}\n                </span>\n              </td>\n            </ng-container>\n\n            <!-- Actions Column -->\n            <ng-container matColumnDef=\"actions\">\n              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\n              <td mat-cell *matCellDef=\"let type\">\n                <div class=\"action-buttons\">\n                  <button mat-icon-button color=\"primary\" \n                          matTooltip=\"تعديل\"\n                          (click)=\"editSupplierType(type)\">\n                    <mat-icon>edit</mat-icon>\n                  </button>\n                  <button mat-icon-button color=\"warn\" \n                          matTooltip=\"حذف\"\n                          (click)=\"deleteSupplierType(type)\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                </div>\n              </td>\n            </ng-container>\n\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n\n          </table>\n\n          <!-- No Data Message -->\n          <div class=\"no-data\" *ngIf=\"supplierTypes.length === 0\">\n            <mat-icon>inbox</mat-icon>\n            <h3>لا توجد أنواع موردين</h3>\n            <p>لم يتم إضافة أي أنواع موردين بعد</p>\n            <button mat-raised-button color=\"primary\" (click)=\"showAddSupplierType()\">\n              <mat-icon>add</mat-icon>\n              <span>إضافة نوع مورد جديد</span>\n            </button>\n          </div>\n\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري المعالجة...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAKrG;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;;;IC2BhDC,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,uHACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,iLACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,yIACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,iLACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IA/BhBH,EAHN,CAAAC,cAAA,mBAAgD,sBAC7B,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAExEF,EAFwE,CAAAG,YAAA,EAAO,EAC5D,EACD;IAOVH,EANR,CAAAC,cAAA,uBAAkB,eACgD,cACvC,0BAGgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,yFAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAI,SAAA,iBAA6F;IAC7FJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAIvCH,EAHA,CAAAK,UAAA,KAAAC,wDAAA,wBAAwE,KAAAC,wDAAA,wBAGC;IAG3EP,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,2GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAI,SAAA,iBAAoG;IACpGJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAIvCH,EAHA,CAAAK,UAAA,KAAAG,wDAAA,wBAAwE,KAAAC,wDAAA,wBAGC;IAG3ET,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAI,SAAA,oBACiE;IACjEJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IACjCF,EADiC,CAAAG,YAAA,EAAW,EAC3B;IAIfH,EADF,CAAAC,cAAA,eAA4B,wBACe;IACvCD,EAAA,CAAAE,MAAA,wEACF;IAKRF,EALQ,CAAAG,YAAA,EAAe,EACX,EAEF,EACD,EACU;IAEjBH,EADF,CAAAC,cAAA,4BAA8B,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IACvCjB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IACbF,EADa,CAAAG,YAAA,EAAO,EACX;IACTH,EAAA,CAAAC,cAAA,kBAA8F;IAApDD,EAAA,CAAAU,UAAA,mBAAAQ,qEAAA;MAAAlB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAK,gBAAA,EAAkB;IAAA,EAAC;IACpEnB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvDH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAGjDF,EAHiD,CAAAG,YAAA,EAAO,EAC3C,EACQ,EACV;;;;;;;;IA9DKH,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAqB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,kBAAkC;IACtCtB,EAAA,CAAAoB,SAAA,GAA8D;IAA9DpB,EAAA,CAAAqB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,oMAA8D;IAIhEtB,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAuB,UAAA,cAAAT,MAAA,CAAAU,gBAAA,CAA8B;IAQlBxB,EAAA,CAAAoB,SAAA,GAA0D;IAA1DpB,EAAA,CAAAuB,UAAA,UAAAE,OAAA,GAAAX,MAAA,CAAAU,gBAAA,CAAAE,GAAA,6BAAAD,OAAA,CAAAE,QAAA,aAA0D;IAG1D3B,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAuB,UAAA,UAAAK,OAAA,GAAAd,MAAA,CAAAU,gBAAA,CAAAE,GAAA,6BAAAE,OAAA,CAAAD,QAAA,cAA2D;IAU3D3B,EAAA,CAAAoB,SAAA,GAA0D;IAA1DpB,EAAA,CAAAuB,UAAA,UAAAM,OAAA,GAAAf,MAAA,CAAAU,gBAAA,CAAAE,GAAA,6BAAAG,OAAA,CAAAF,QAAA,aAA0D;IAG1D3B,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAuB,UAAA,UAAAO,OAAA,GAAAhB,MAAA,CAAAU,gBAAA,CAAAE,GAAA,6BAAAI,OAAA,CAAAH,QAAA,cAA2D;IA4BN3B,EAAA,CAAAoB,SAAA,IAAsB;IAAtBpB,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAAiB,SAAA,CAAsB;IACjF/B,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAqB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,kBAAkC;IACtCtB,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAqB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,uEAAqC;;;;;IAgB7CtB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAI,SAAA,sBAAyC;IACzCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;;IAQAH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,sFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACzDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtBH,EAAA,CAAAoB,SAAA,EAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAW,OAAA,CAAAC,MAAA,CAAiB;;;;;IAKrDjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,wGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC5DH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtBH,EAAA,CAAAoB,SAAA,EAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAa,OAAA,CAAAC,MAAA,CAAiB;;;;;IAKrDnC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAChDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlCH,EAAA,CAAAoB,SAAA,EAA6B;IAA7BpB,EAAA,CAAAqB,iBAAA,CAAAe,OAAA,CAAAC,WAAA,QAA6B;;;;;IAKjErC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAoC,eAC2C;IAC3ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAHwBH,EAAA,CAAAoB,SAAA,EAAiD;IAAjDpB,EAAA,CAAAuB,UAAA,YAAAe,OAAA,CAAAC,QAAA,yBAAiD;IAC1EvC,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,MAAAF,OAAA,CAAAC,QAAA,uEACF;;;;;IAMFvC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAoC,cACN,iBAGe;IAAjCD,EAAA,CAAAU,UAAA,mBAAA+B,qEAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAY,aAAA,CAAA+B,GAAA,EAAAC,SAAA;MAAA,MAAA9B,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+B,gBAAA,CAAAH,OAAA,CAAsB;IAAA,EAAC;IACtC1C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAE2C;IAAnCD,EAAA,CAAAU,UAAA,mBAAAoC,qEAAA;MAAA,MAAAJ,OAAA,GAAA1C,EAAA,CAAAY,aAAA,CAAA+B,GAAA,EAAAC,SAAA;MAAA,MAAA9B,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiC,kBAAA,CAAAL,OAAA,CAAwB;IAAA,EAAC;IACxC1C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAGtBF,EAHsB,CAAAG,YAAA,EAAW,EACpB,EACL,EACH;;;;;IAGPH,EAAA,CAAAI,SAAA,aAA4D;;;;;IAC5DJ,EAAA,CAAAI,SAAA,aAAkE;;;;;;IAMlEJ,EADF,CAAAC,cAAA,cAAwD,eAC5C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gHAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yKAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvCH,EAAA,CAAAC,cAAA,iBAA0E;IAAhCD,EAAA,CAAAU,UAAA,mBAAAsC,sEAAA;MAAAhD,EAAA,CAAAY,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoC,mBAAA,EAAqB;IAAA,EAAC;IACvElD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2GAAmB;IAE7BF,EAF6B,CAAAG,YAAA,EAAO,EACzB,EACL;;;;;IA/DNH,EADF,CAAAC,cAAA,cAAgD,gBAC6B;IAGzED,EAAA,CAAAmD,uBAAA,OAAoC;IAElCnD,EADA,CAAAK,UAAA,IAAA+C,2CAAA,iBAAsC,IAAAC,2CAAA,iBACF;;IAItCrD,EAAA,CAAAmD,uBAAA,OAAoC;IAElCnD,EADA,CAAAK,UAAA,IAAAiD,2CAAA,iBAAsC,IAAAC,2CAAA,iBACF;;IAItCvD,EAAA,CAAAmD,uBAAA,OAAyC;IAEvCnD,EADA,CAAAK,UAAA,IAAAmD,2CAAA,iBAAsC,KAAAC,4CAAA,iBACF;;IAItCzD,EAAA,CAAAmD,uBAAA,QAAsC;IAEpCnD,EADA,CAAAK,UAAA,KAAAqD,4CAAA,iBAAsC,KAAAC,4CAAA,iBACF;;IAQtC3D,EAAA,CAAAmD,uBAAA,QAAqC;IAEnCnD,EADA,CAAAK,UAAA,KAAAuD,4CAAA,iBAAsC,KAAAC,4CAAA,iBACF;;IAiBtC7D,EADA,CAAAK,UAAA,KAAAyD,4CAAA,iBAAuD,KAAAC,4CAAA,iBACM;IAE/D/D,EAAA,CAAAG,YAAA,EAAQ;IAGRH,EAAA,CAAAK,UAAA,KAAA2D,6CAAA,mBAAwD;IAU1DhE,EAAA,CAAAG,YAAA,EAAM;;;;IAjEaH,EAAA,CAAAoB,SAAA,EAA4B;IAA5BpB,EAAA,CAAAuB,UAAA,eAAAT,MAAA,CAAAmD,aAAA,CAA4B;IAiDvBjE,EAAA,CAAAoB,SAAA,IAAiC;IAAjCpB,EAAA,CAAAuB,UAAA,oBAAAT,MAAA,CAAAoD,gBAAA,CAAiC;IACpBlE,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAAuB,UAAA,qBAAAT,MAAA,CAAAoD,gBAAA,CAA0B;IAKvClE,EAAA,CAAAoB,SAAA,EAAgC;IAAhCpB,EAAA,CAAAuB,UAAA,SAAAT,MAAA,CAAAmD,aAAA,CAAAE,MAAA,OAAgC;;;;;IAiB9DnE,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAI,SAAA,sBAAyC;IACzCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mFAAgB;IACrBF,EADqB,CAAAG,YAAA,EAAI,EACnB;;;ADzIR,WAAaiE,sBAAsB;EAA7B,MAAOA,sBAAsB;IAoBvBC,IAAA;IACAC,MAAA;IACAC,EAAA;IACAC,QAAA;IACAC,MAAA;IAtBV;IACA1C,SAAS,GAAG,KAAK;IACjB2C,WAAW,GAAG,KAAK;IACnBpD,WAAW,GAAwB,IAAI;IAEvC;IACA2C,aAAa,GAAmB,EAAE;IAElC;IACAzC,gBAAgB;IAEhB;IACA0C,gBAAgB,GAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC;IAEvF;IACQS,aAAa,GAAmB,EAAE;IAE1CC,YACUP,IAAgB,EAChBC,MAAc,EACdC,EAAe,EACfC,QAAqB,EACrBC,MAAiB;MAJjB,KAAAJ,IAAI,GAAJA,IAAI;MACJ,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,MAAM,GAANA,MAAM;MAEd,IAAI,CAACjD,gBAAgB,GAAG,IAAI,CAACqD,UAAU,EAAE;IAC3C;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQN,UAAUA,CAAA;MAChB,OAAO,IAAI,CAACN,EAAE,CAACa,KAAK,CAAC;QACnBC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAChG,UAAU,CAACiG,QAAQ,EAAEjG,UAAU,CAACkG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACnG,UAAU,CAACiG,QAAQ,EAAEjG,UAAU,CAACkG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DE,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,QAAQ,EAAE,CAAC,IAAI;OAChB,CAAC;IACJ;IAEA;;;IAGAX,iBAAiBA,CAAA;MACf,IAAI,CAAChD,SAAS,GAAG,IAAI;MAErB,MAAMmD,GAAG,GAAG,IAAI,CAACb,IAAI,CAAC3C,GAAG,CAAM,iDAAiD,CAAC,CAACiE,SAAS,CAAC;QAC1FC,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,QAAQ,CAAC;UACjD,IAAI,CAAC5B,aAAa,GAAG4B,QAAQ,CAAC5B,aAAa,IAAI,EAAE;UACjD,IAAI,CAAClC,SAAS,GAAG,KAAK;QACxB,CAAC;QACDiE,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAI,CAACC,SAAS,CAAC,6BAA6B,CAAC;UAC7C,IAAI,CAAClE,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MACF,IAAI,CAAC4C,aAAa,CAACuB,IAAI,CAAChB,GAAG,CAAC;IAC9B;IAEA;;;IAGAhC,mBAAmBA,CAAA;MACjB,IAAI,CAACwB,WAAW,GAAG,IAAI;MACvB,IAAI,CAACpD,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,gBAAgB,CAAC2E,KAAK,EAAE;MAC7B,IAAI,CAAC3E,gBAAgB,CAAC4E,UAAU,CAAC;QAAEV,QAAQ,EAAE;MAAI,CAAE,CAAC;IACtD;IAEA;;;IAGA7C,gBAAgBA,CAACwD,YAA0B;MACzC,IAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB,IAAI,CAACpD,WAAW,GAAG+E,YAAY;MAC/B,IAAI,CAAC7E,gBAAgB,CAAC4E,UAAU,CAAC;QAC/Bf,MAAM,EAAEgB,YAAY,CAACpE,MAAM;QAC3BuD,MAAM,EAAEa,YAAY,CAAClE,MAAM;QAC3BsD,WAAW,EAAEY,YAAY,CAAChE,WAAW,IAAI,EAAE;QAC3CqD,QAAQ,EAAEW,YAAY,CAAC9D;OACxB,CAAC;IACJ;IAEA;;;IAGApB,gBAAgBA,CAAA;MACd,IAAI,IAAI,CAACK,gBAAgB,CAAC8E,KAAK,EAAE;QAC/B,IAAI,CAACvE,SAAS,GAAG,IAAI;QAErB,MAAMwE,SAAS,GAAG,IAAI,CAAC/E,gBAAgB,CAACgF,KAAK;QAC7C,MAAMC,OAAO,GAAG;UACdpB,MAAM,EAAEkB,SAAS,CAAClB,MAAM;UACxBG,MAAM,EAAEe,SAAS,CAACf,MAAM;UACxBC,WAAW,EAAEc,SAAS,CAACd,WAAW,IAAI,IAAI;UAC1CC,QAAQ,EAAEa,SAAS,CAACb;SACrB;QAED,MAAMgB,OAAO,GAAG,IAAI,CAACpF,WAAW,GAC5B,IAAI,CAAC+C,IAAI,CAACsC,GAAG,CAAM,mDAAmD,IAAI,CAACrF,WAAW,CAACsF,EAAE,EAAE,EAAEH,OAAO,CAAC,GACrG,IAAI,CAACpC,IAAI,CAACwC,IAAI,CAAM,iDAAiD,EAAEJ,OAAO,CAAC;QAEnF,MAAMvB,GAAG,GAAGwB,OAAO,CAACf,SAAS,CAAC;UAC5BC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAC9D,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC+E,WAAW,CAAC,IAAI,CAACxF,WAAW,GAAG,2BAA2B,GAAG,2BAA2B,CAAC;YAC9F,IAAI,CAACoD,WAAW,GAAG,KAAK;YACxB,IAAI,CAACK,iBAAiB,EAAE;UAC1B,CAAC;UACDiB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjE,SAAS,GAAG,KAAK;YACtB+D,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;YACnD,IAAI,CAACC,SAAS,CAAC,uBAAuB,CAAC;UACzC;SACD,CAAC;QACF,IAAI,CAACtB,aAAa,CAACuB,IAAI,CAAChB,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC6B,oBAAoB,EAAE;QAC3B,IAAI,CAACd,SAAS,CAAC,+BAA+B,CAAC;MACjD;IACF;IAEA;;;IAGAlD,kBAAkBA,CAACsD,YAA0B;MAC3C,IAAIW,OAAO,CAAC,mCAAmCX,YAAY,CAACpE,MAAM,IAAI,CAAC,EAAE;QACvE,IAAI,CAACF,SAAS,GAAG,IAAI;QAErB,MAAMmD,GAAG,GAAG,IAAI,CAACb,IAAI,CAAC4C,MAAM,CAAC,mDAAmDZ,YAAY,CAACO,EAAE,EAAE,CAAC,CAACjB,SAAS,CAAC;UAC3GC,IAAI,EAAGC,QAAa,IAAI;YACtB,IAAI,CAAC9D,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC+E,WAAW,CAAC,yBAAyB,CAAC;YAC3C,IAAI,CAAC/B,iBAAiB,EAAE;UAC1B,CAAC;UACDiB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjE,SAAS,GAAG,KAAK;YACtB+D,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,IAAI,CAACC,SAAS,CAAC,uBAAuB,CAAC;UACzC;SACD,CAAC;QACF,IAAI,CAACtB,aAAa,CAACuB,IAAI,CAAChB,GAAG,CAAC;MAC9B;IACF;IAEA;;;IAGAjE,UAAUA,CAAA;MACR,IAAI,CAACyD,WAAW,GAAG,KAAK;MACxB,IAAI,CAACpD,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,gBAAgB,CAAC2E,KAAK,EAAE;IAC/B;IAEA;;;IAGAe,MAAMA,CAAA;MACJ,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGQJ,oBAAoBA,CAAA;MAC1BK,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7F,gBAAgB,CAAC8F,QAAQ,CAAC,CAACrC,OAAO,CAACsC,GAAG,IAAG;QACxD,MAAMC,OAAO,GAAG,IAAI,CAAChG,gBAAgB,CAACE,GAAG,CAAC6F,GAAG,CAAC;QAC9CC,OAAO,EAAEC,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA;;;IAGQX,WAAWA,CAACY,OAAe;MACjC,IAAI,CAAClD,QAAQ,CAACmD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;;;IAGQ9B,SAASA,CAACyB,OAAe;MAC/B,IAAI,CAAClD,QAAQ,CAACmD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;;uCA5MW3D,sBAAsB,EAAApE,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAxI,EAAA,CAAAgI,iBAAA,CAAAS,EAAA,CAAAC,SAAA;IAAA;;YAAtBtE,sBAAsB;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5C3BjJ,EANR,CAAAC,cAAA,aAAsC,aAGX,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAU,UAAA,mBAAAyI,wDAAA;YAAA,OAASD,GAAA,CAAAhC,MAAA,EAAQ;UAAA,EAAC;UACzDlH,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,sFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1CH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,wKAA8B;UAE3DF,EAF2D,CAAAG,YAAA,EAAI,EACvD,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBACgE;UAAhCD,EAAA,CAAAU,UAAA,mBAAA0I,yDAAA;YAAA,OAASF,GAAA,CAAAhG,mBAAA,EAAqB;UAAA,EAAC;UACvFlD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kFAAc;UAI5BF,EAJ4B,CAAAG,YAAA,EAAO,EACpB,EACL,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAAqB;UAGnBD,EAAA,CAAAK,UAAA,KAAAgJ,2CAAA,yBAAgD;UAuE1CrJ,EAHN,CAAAC,cAAA,oBAA6B,uBACV,sBACC,gBACJ;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sHAAoB;UAE9BF,EAF8B,CAAAG,YAAA,EAAO,EAClB,EACD;UAClBH,EAAA,CAAAC,cAAA,wBAAkB;UAShBD,EANA,CAAAK,UAAA,KAAAiJ,sCAAA,kBAAiD,KAAAC,sCAAA,mBAMD;UAsEtDvJ,EAHI,CAAAG,YAAA,EAAmB,EACV,EAEP;UAGNH,EAAA,CAAAK,UAAA,KAAAmJ,sCAAA,kBAA+C;UAKjDxJ,EAAA,CAAAG,YAAA,EAAM;;;UAlK2BH,EAAA,CAAAoB,SAAA,IAAiB;UAAjBpB,EAAA,CAAAuB,UAAA,SAAA2H,GAAA,CAAAxE,WAAA,CAAiB;UA8EV1E,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAuB,UAAA,SAAA2H,GAAA,CAAAnH,SAAA,CAAe;UAMjB/B,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAuB,UAAA,UAAA2H,GAAA,CAAAnH,SAAA,CAAgB;UAyEtB/B,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAuB,UAAA,SAAA2H,GAAA,CAAAnH,SAAA,CAAe;;;qBDvJ3C7C,YAAY,EAAAuK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxK,WAAW,EAAAkJ,EAAA,CAAAuB,aAAA,EAAAvB,EAAA,CAAAwB,oBAAA,EAAAxB,EAAA,CAAAyB,eAAA,EAAAzB,EAAA,CAAA0B,oBAAA,EAAA1B,EAAA,CAAA2B,iBAAA,EACX5K,mBAAmB,EAAAiJ,EAAA,CAAA4B,kBAAA,EAAA5B,EAAA,CAAA6B,eAAA,EACnB5K,aAAa,EAAA6K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,YAAA,EACbjL,eAAe,EAAAkL,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfnL,aAAa,EAAAoL,EAAA,CAAAC,OAAA,EACbpL,kBAAkB,EAAAqL,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EAClBxL,cAAc,EAAAyL,GAAA,CAAAC,QAAA,EACdzL,cAAc,EAAA0L,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdnM,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EAAAkM,GAAA,CAAAC,kBAAA,EACxBlM,iBAAiB,EAAAmM,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;;SAKRhI,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}