{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { registerLocaleData } from '@angular/common';\nimport localeAr from '@angular/common/locales/ar';\nimport { appConfig } from './app/app.config';\nimport { App } from './app/app';\n// Register Arabic locale\nregisterLocaleData(localeAr);\nbootstrapApplication(App, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "registerLocaleData", "localeAr", "appConfig", "App", "catch", "err", "console", "error"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { registerLocaleData } from '@angular/common';\r\nimport localeAr from '@angular/common/locales/ar';\r\nimport { appConfig } from './app/app.config';\r\nimport { App } from './app/app';\r\n\r\n// Register Arabic locale\r\nregisterLocaleData(localeAr);\r\n\r\nbootstrapApplication(App, appConfig)\r\n  .catch((err: any) => console.error(err));\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,GAAG,QAAQ,WAAW;AAE/B;AACAH,kBAAkB,CAACC,QAAQ,CAAC;AAE5BF,oBAAoB,CAACI,GAAG,EAAED,SAAS,CAAC,CACjCE,KAAK,CAAEC,GAAQ,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}