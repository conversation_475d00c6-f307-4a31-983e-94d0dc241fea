<!-- <PERSON> Retail ERP - Countries -->
<div class="countries-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">البلدان</h1>
          <p class="page-subtitle">إدارة البلدان في النظام</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" class="add-btn" (click)="showAddCountry()">
          <mat-icon>add</mat-icon>
          <span>إضافة بلد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content">
    
    <!-- Add/Edit Form -->
    <mat-card class="form-card" *ngIf="showAddForm">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>{{ editingCountry ? 'edit' : 'add' }}</mat-icon>
          <span>{{ editingCountry ? 'تعديل البلد' : 'إضافة بلد جديد' }}</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="countryForm" class="country-form">
          <div class="form-grid">
            
            <!-- Arabic Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم البلد بالعربية *</mat-label>
              <input matInput formControlName="nameAr" placeholder="أدخل اسم البلد بالعربية" required>
              <mat-icon matSuffix>public</mat-icon>
              <mat-error *ngIf="countryForm.get('nameAr')?.hasError('required')">
                اسم البلد بالعربية مطلوب
              </mat-error>
              <mat-error *ngIf="countryForm.get('nameAr')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- English Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم البلد بالإنجليزية *</mat-label>
              <input matInput formControlName="nameEn" placeholder="Enter country name in English" required>
              <mat-icon matSuffix>public</mat-icon>
              <mat-error *ngIf="countryForm.get('nameEn')?.hasError('required')">
                اسم البلد بالإنجليزية مطلوب
              </mat-error>
              <mat-error *ngIf="countryForm.get('nameEn')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- Code -->
            <mat-form-field appearance="outline">
              <mat-label>كود البلد *</mat-label>
              <input matInput formControlName="code" placeholder="EG" required maxlength="5" style="text-transform: uppercase;">
              <mat-icon matSuffix>code</mat-icon>
              <mat-error *ngIf="countryForm.get('code')?.hasError('required')">
                كود البلد مطلوب
              </mat-error>
              <mat-error *ngIf="countryForm.get('code')?.hasError('minlength')">
                الكود يجب أن يكون حرفين على الأقل
              </mat-error>
              <mat-error *ngIf="countryForm.get('code')?.hasError('maxlength')">
                الكود يجب ألا يزيد عن 5 أحرف
              </mat-error>
            </mat-form-field>

            <!-- Phone Code -->
            <mat-form-field appearance="outline">
              <mat-label>كود الهاتف</mat-label>
              <input matInput formControlName="phoneCode" placeholder="+20">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="countryForm.get('phoneCode')?.hasError('pattern')">
                كود الهاتف يجب أن يبدأ بـ + ويحتوي على أرقام فقط
              </mat-error>
            </mat-form-field>

            <!-- Is Active -->
            <div class="checkbox-field">
              <mat-checkbox formControlName="isActive">
                بلد نشط
              </mat-checkbox>
            </div>

          </div>
        </form>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button (click)="cancelForm()">
          <mat-icon>cancel</mat-icon>
          <span>إلغاء</span>
        </button>
        <button mat-raised-button color="primary" (click)="saveCountry()" [disabled]="isLoading">
          <mat-icon>{{ editingCountry ? 'save' : 'add' }}</mat-icon>
          <span>{{ editingCountry ? 'تحديث' : 'إضافة' }}</span>
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Countries Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>list</mat-icon>
          <span>قائمة البلدان</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        
        <!-- Loading Spinner -->
        <div class="loading-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>جاري تحميل البيانات...</p>
        </div>

        <!-- Table -->
        <div class="table-container" *ngIf="!isLoading">
          <table mat-table [dataSource]="countries" class="countries-table">

            <!-- Arabic Name Column -->
            <ng-container matColumnDef="nameAr">
              <th mat-header-cell *matHeaderCellDef>الاسم بالعربية</th>
              <td mat-cell *matCellDef="let country">{{ country.NameAr }}</td>
            </ng-container>

            <!-- English Name Column -->
            <ng-container matColumnDef="nameEn">
              <th mat-header-cell *matHeaderCellDef>الاسم بالإنجليزية</th>
              <td mat-cell *matCellDef="let country">{{ country.NameEn }}</td>
            </ng-container>

            <!-- Code Column -->
            <ng-container matColumnDef="code">
              <th mat-header-cell *matHeaderCellDef>الكود</th>
              <td mat-cell *matCellDef="let country">
                <span class="code-badge">{{ country.Code }}</span>
              </td>
            </ng-container>

            <!-- Phone Code Column -->
            <ng-container matColumnDef="phoneCode">
              <th mat-header-cell *matHeaderCellDef>كود الهاتف</th>
              <td mat-cell *matCellDef="let country">
                <span class="phone-code" *ngIf="country.PhoneCode">{{ country.PhoneCode }}</span>
                <span class="no-data" *ngIf="!country.PhoneCode">-</span>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="isActive">
              <th mat-header-cell *matHeaderCellDef>الحالة</th>
              <td mat-cell *matCellDef="let country">
                <span class="status-badge" [ngClass]="country.IsActive ? 'active' : 'inactive'">
                  {{ country.IsActive ? 'نشط' : 'غير نشط' }}
                </span>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
              <td mat-cell *matCellDef="let country">
                <div class="action-buttons">
                  <button mat-icon-button color="primary" 
                          matTooltip="تعديل"
                          (click)="editCountry(country)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" 
                          matTooltip="حذف"
                          (click)="deleteCountry(country)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          </table>

          <!-- No Data Message -->
          <div class="no-data" *ngIf="countries.length === 0">
            <mat-icon>public_off</mat-icon>
            <h3>لا توجد بلدان</h3>
            <p>لم يتم إضافة أي بلدان بعد</p>
            <button mat-raised-button color="primary" (click)="showAddCountry()">
              <mat-icon>add</mat-icon>
              <span>إضافة بلد جديد</span>
            </button>
          </div>

        </div>
      </mat-card-content>
    </mat-card>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري المعالجة...</p>
  </div>

</div>
