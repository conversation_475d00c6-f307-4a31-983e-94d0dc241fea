using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// طرق الدفع
    /// </summary>
    public class PaymentMethod : BaseEntity
    {
        /// <summary>
        /// اسم طريقة الدفع بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم طريقة الدفع بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود طريقة الدفع
        /// </summary>
        [MaxLength(10)]
        public string? Code { get; set; }

        /// <summary>
        /// نوع طريقة الدفع
        /// </summary>
        public PaymentMethodType PaymentType { get; set; }

        /// <summary>
        /// وصف طريقة الدفع
        /// </summary>
        [Max<PERSON>ength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// هل طريقة الدفع نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل طريقة الدفع افتراضية
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// أيقونة طريقة الدفع
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// لون طريقة الدفع (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// هل تتطلب تأكيد إضافي
        /// </summary>
        public bool RequiresConfirmation { get; set; } = false;

        /// <summary>
        /// الحد الأدنى للمبلغ
        /// </summary>
        public decimal? MinAmount { get; set; }

        /// <summary>
        /// الحد الأقصى للمبلغ
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// نسبة العمولة (%)
        /// </summary>
        public decimal CommissionPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ العمولة الثابت
        /// </summary>
        public decimal CommissionAmount { get; set; } = 0;

        /// <summary>
        /// معرف الحساب المالي المرتبط
        /// </summary>
        public int? AccountId { get; set; }

        /// <summary>
        /// إعدادات إضافية (JSON)
        /// </summary>
        [MaxLength(1000)]
        public string? Settings { get; set; }

        // Navigation Properties
        public virtual Account? Account { get; set; }
        public virtual ICollection<SalePayment> SalePayments { get; set; } = new List<SalePayment>();
        public virtual ICollection<CustomerTransaction> CustomerTransactions { get; set; } = new List<CustomerTransaction>();
        public virtual ICollection<CashTransaction> CashTransactions { get; set; } = new List<CashTransaction>();
    }

    /// <summary>
    /// أنواع طرق الدفع
    /// </summary>
    public enum PaymentMethodType
    {
        /// <summary>
        /// نقدي
        /// </summary>
        Cash = 1,

        /// <summary>
        /// بطاقة ائتمان
        /// </summary>
        CreditCard = 2,

        /// <summary>
        /// بطاقة خصم
        /// </summary>
        DebitCard = 3,

        /// <summary>
        /// تحويل بنكي
        /// </summary>
        BankTransfer = 4,

        /// <summary>
        /// شيك
        /// </summary>
        Check = 5,

        /// <summary>
        /// محفظة إلكترونية
        /// </summary>
        EWallet = 6,

        /// <summary>
        /// آجل
        /// </summary>
        Credit = 7,

        /// <summary>
        /// قسيمة شرائية
        /// </summary>
        Voucher = 8,

        /// <summary>
        /// نقاط ولاء
        /// </summary>
        LoyaltyPoints = 9,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 10
    }
}
