-- شجرة الحسابات الكاملة
-- Complete Chart of Accounts

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);

-- المستوى الأول - الحسابات الرئيسية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('1', N'الأصول', 'Assets', 1, NULL, 1, 1, 0, 1, GETDATE()),
('2', N'الخصوم', 'Liabilities', 2, NULL, 1, 1, 0, 1, GETDATE()),
('3', N'حقوق الملكية', 'Equity', 3, NULL, 1, 1, 0, 1, GETDATE()),
('4', N'الإيرادات', 'Revenue', 4, NULL, 1, 1, 0, 1, GETDATE()),
('5', N'المصروفات', 'Expenses', 5, NULL, 1, 1, 0, 1, GETDATE());

-- المستوى الثاني - الأصول
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11', N'الأصول المتداولة', 'Current Assets', 1, 1, 2, 1, 0, 1, GETDATE()),
('12', N'الأصول الثابتة', 'Fixed Assets', 1, 1, 2, 1, 0, 1, GETDATE()),
('13', N'الأصول الأخرى', 'Other Assets', 1, 1, 2, 1, 0, 1, GETDATE());

-- المستوى الثالث - الأصول المتداولة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('111', N'النقدية والبنوك', 'Cash & Banks', 1, 6, 3, 1, 0, 1, GETDATE()),
('112', N'العملاء', 'Accounts Receivable', 1, 6, 3, 1, 0, 1, GETDATE()),
('113', N'المخزون', 'Inventory', 1, 6, 3, 1, 0, 1, GETDATE()),
('114', N'مصروفات مدفوعة مقدماً', 'Prepaid Expenses', 1, 6, 3, 1, 0, 1, GETDATE()),
('115', N'أرصدة مدينة أخرى', 'Other Debit Balances', 1, 6, 3, 1, 0, 1, GETDATE());

-- المستوى الرابع - النقدية والبنوك
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11101', N'الصندوق الرئيسي', 'Main Cash', 1, 9, 4, 0, 1, 1, GETDATE()),
('11102', N'صندوق الفرع الأول', 'Branch 1 Cash', 1, 9, 4, 0, 1, 1, GETDATE()),
('11103', N'صندوق الفرع الثاني', 'Branch 2 Cash', 1, 9, 4, 0, 1, 1, GETDATE()),
('11104', N'صندوق العملة الأجنبية', 'Foreign Currency Cash', 1, 9, 4, 0, 1, 1, GETDATE()),
('11105', N'البنك الأهلي المصري - ج.م', 'National Bank of Egypt - EGP', 1, 9, 4, 0, 1, 1, GETDATE()),
('11106', N'بنك مصر - ج.م', 'Banque Misr - EGP', 1, 9, 4, 0, 1, 1, GETDATE()),
('11107', N'البنك التجاري الدولي - ج.م', 'CIB - EGP', 1, 9, 4, 0, 1, 1, GETDATE()),
('11108', N'بنك القاهرة - ج.م', 'Banque du Caire - EGP', 1, 9, 4, 0, 1, 1, GETDATE()),
('11109', N'البنك الأهلي المصري - دولار', 'National Bank of Egypt - USD', 1, 9, 4, 0, 1, 1, GETDATE()),
('11110', N'بنك مصر - دولار', 'Banque Misr - USD', 1, 9, 4, 0, 1, 1, GETDATE()),
('11111', N'حسابات بنكية أخرى', 'Other Bank Accounts', 1, 9, 4, 0, 1, 1, GETDATE());

-- العملاء
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11201', N'عملاء محليون', 'Local Customers', 1, 10, 4, 0, 1, 1, GETDATE()),
('11202', N'عملاء أجانب', 'Foreign Customers', 1, 10, 4, 0, 1, 1, GETDATE()),
('11203', N'أوراق قبض', 'Notes Receivable', 1, 10, 4, 0, 1, 1, GETDATE()),
('11204', N'عملاء شيكات تحت التحصيل', 'Customers Checks Under Collection', 1, 10, 4, 0, 1, 1, GETDATE()),
('11205', N'مخصص ديون مشكوك فيها', 'Allowance for Doubtful Debts', 1, 10, 4, 0, 1, 1, GETDATE()),
('11206', N'عملاء بطاقات ائتمان', 'Credit Card Customers', 1, 10, 4, 0, 1, 1, GETDATE());

-- المخزون
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11301', N'مخزون البضاعة', 'Merchandise Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11302', N'مخزون المواد الخام', 'Raw Materials Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11303', N'مخزون الإنتاج تحت التشغيل', 'Work in Process Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11304', N'مخزون البضاعة التامة', 'Finished Goods Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11305', N'مخزون قطع الغيار', 'Spare Parts Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11306', N'مخزون المستلزمات', 'Supplies Inventory', 1, 11, 4, 0, 1, 1, GETDATE()),
('11307', N'مخزون البضاعة في الطريق', 'Goods in Transit', 1, 11, 4, 0, 1, 1, GETDATE());

-- مصروفات مدفوعة مقدماً
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11401', N'إيجار مدفوع مقدماً', 'Prepaid Rent', 1, 12, 4, 0, 1, 1, GETDATE()),
('11402', N'تأمين مدفوع مقدماً', 'Prepaid Insurance', 1, 12, 4, 0, 1, 1, GETDATE()),
('11403', N'مصروفات دعاية مدفوعة مقدماً', 'Prepaid Advertising', 1, 12, 4, 0, 1, 1, GETDATE()),
('11404', N'رسوم ترخيص مدفوعة مقدماً', 'Prepaid License Fees', 1, 12, 4, 0, 1, 1, GETDATE());

-- أرصدة مدينة أخرى
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11501', N'سلف الموظفين', 'Employee Advances', 1, 13, 4, 0, 1, 1, GETDATE()),
('11502', N'عهد نقدية', 'Cash Custody', 1, 13, 4, 0, 1, 1, GETDATE()),
('11503', N'عهد عينية', 'Material Custody', 1, 13, 4, 0, 1, 1, GETDATE()),
('11504', N'أمانات مدفوعة', 'Deposits Paid', 1, 13, 4, 0, 1, 1, GETDATE()),
('11505', N'ضرائب مدفوعة مقدماً', 'Prepaid Taxes', 1, 13, 4, 0, 1, 1, GETDATE()),
('11506', N'مدينون متنوعون', 'Sundry Debtors', 1, 13, 4, 0, 1, 1, GETDATE());

-- الأصول الثابتة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('121', N'الأراضي والمباني', 'Land & Buildings', 1, 7, 3, 1, 0, 1, GETDATE()),
('122', N'الآلات والمعدات', 'Machinery & Equipment', 1, 7, 3, 1, 0, 1, GETDATE()),
('123', N'الأثاث والتجهيزات', 'Furniture & Fixtures', 1, 7, 3, 1, 0, 1, GETDATE()),
('124', N'وسائل النقل', 'Vehicles', 1, 7, 3, 1, 0, 1, GETDATE()),
('125', N'أصول أخرى', 'Other Assets', 1, 7, 3, 1, 0, 1, GETDATE());

INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('12101', N'الأراضي', 'Land', 1, 35, 4, 0, 1, 1, GETDATE()),
('12102', N'المباني', 'Buildings', 1, 35, 4, 0, 1, 1, GETDATE()),
('12103', N'مجمع إهلاك المباني', 'Accumulated Depreciation - Buildings', 1, 35, 4, 0, 1, 1, GETDATE()),
('12201', N'آلات ومعدات', 'Machinery', 1, 36, 4, 0, 1, 1, GETDATE()),
('12202', N'مجمع إهلاك الآلات والمعدات', 'Accumulated Depreciation - Machinery', 1, 36, 4, 0, 1, 1, GETDATE()),
('12301', N'أثاث ومفروشات', 'Furniture & Furnishings', 1, 37, 4, 0, 1, 1, GETDATE()),
('12302', N'مجمع إهلاك الأثاث', 'Accumulated Depreciation - Furniture', 1, 37, 4, 0, 1, 1, GETDATE()),
('12401', N'سيارات', 'Cars', 1, 38, 4, 0, 1, 1, GETDATE()),
('12402', N'شاحنات', 'Trucks', 1, 38, 4, 0, 1, 1, GETDATE()),
('12403', N'مجمع إهلاك وسائل النقل', 'Accumulated Depreciation - Vehicles', 1, 38, 4, 0, 1, 1, GETDATE()),
('12501', N'أصول غير ملموسة', 'Intangible Assets', 1, 39, 4, 0, 1, 1, GETDATE()),
('12502', N'استثمارات طويلة الأجل', 'Long-term Investments', 1, 39, 4, 0, 1, 1, GETDATE());

-- الخصوم - المستوى الثاني
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21', N'الخصوم المتداولة', 'Current Liabilities', 2, 2, 2, 1, 0, 1, GETDATE()),
('22', N'الخصوم طويلة الأجل', 'Long-term Liabilities', 2, 2, 2, 1, 0, 1, GETDATE());

-- الخصوم المتداولة - المستوى الثالث
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('211', N'الموردون', 'Accounts Payable', 2, 48, 3, 1, 0, 1, GETDATE()),
('212', N'مصروفات مستحقة', 'Accrued Expenses', 2, 48, 3, 1, 0, 1, GETDATE()),
('213', N'ضرائب مستحقة', 'Accrued Taxes', 2, 48, 3, 1, 0, 1, GETDATE()),
('214', N'أرصدة دائنة أخرى', 'Other Credit Balances', 2, 48, 3, 1, 0, 1, GETDATE());

-- الموردون - المستوى الرابع
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21101', N'موردون محليون', 'Local Suppliers', 2, 50, 4, 0, 1, 1, GETDATE()),
('21102', N'موردون أجانب', 'Foreign Suppliers', 2, 50, 4, 0, 1, 1, GETDATE()),
('21103', N'أوراق دفع', 'Notes Payable', 2, 50, 4, 0, 1, 1, GETDATE()),
('21104', N'موردون شيكات مؤجلة', 'Suppliers Post-dated Checks', 2, 50, 4, 0, 1, 1, GETDATE());

-- مصروفات مستحقة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21201', N'رواتب مستحقة', 'Accrued Salaries', 2, 51, 4, 0, 1, 1, GETDATE()),
('21202', N'إيجار مستحق', 'Accrued Rent', 2, 51, 4, 0, 1, 1, GETDATE()),
('21203', N'فوائد مستحقة', 'Accrued Interest', 2, 51, 4, 0, 1, 1, GETDATE()),
('21204', N'مصروفات أخرى مستحقة', 'Other Accrued Expenses', 2, 51, 4, 0, 1, 1, GETDATE());

-- ضرائب مستحقة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21301', N'ضريبة الدخل المستحقة', 'Accrued Income Tax', 2, 52, 4, 0, 1, 1, GETDATE()),
('21302', N'ضريبة القيمة المضافة', 'VAT Payable', 2, 52, 4, 0, 1, 1, GETDATE()),
('21303', N'ضرائب أخرى مستحقة', 'Other Accrued Taxes', 2, 52, 4, 0, 1, 1, GETDATE());

-- أرصدة دائنة أخرى
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21401', N'أمانات مستلمة', 'Deposits Received', 2, 53, 4, 0, 1, 1, GETDATE()),
('21402', N'دائنون متنوعون', 'Sundry Creditors', 2, 53, 4, 0, 1, 1, GETDATE()),
('21403', N'إيرادات مقبوضة مقدماً', 'Unearned Revenue', 2, 53, 4, 0, 1, 1, GETDATE());

-- الخصوم طويلة الأجل
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('22101', N'قروض طويلة الأجل', 'Long-term Loans', 2, 49, 3, 0, 1, 1, GETDATE()),
('22102', N'سندات مستحقة الدفع', 'Bonds Payable', 2, 49, 3, 0, 1, 1, GETDATE());

-- حقوق الملكية - المستوى الثاني
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('31', N'رأس المال', 'Capital', 3, 3, 2, 1, 0, 1, GETDATE()),
('32', N'الاحتياطيات', 'Reserves', 3, 3, 2, 1, 0, 1, GETDATE()),
('33', N'الأرباح المحتجزة', 'Retained Earnings', 3, 3, 2, 1, 0, 1, GETDATE());

-- رأس المال
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('31101', N'رأس المال المدفوع', 'Paid-in Capital', 3, 68, 3, 0, 1, 1, GETDATE()),
('31102', N'علاوة إصدار', 'Share Premium', 3, 68, 3, 0, 1, 1, GETDATE()),
('31103', N'مسحوبات شخصية', 'Owner Drawings', 3, 68, 3, 0, 1, 1, GETDATE());

-- الاحتياطيات
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('32101', N'احتياطي قانوني', 'Legal Reserve', 3, 69, 3, 0, 1, 1, GETDATE()),
('32102', N'احتياطي اختياري', 'Optional Reserve', 3, 69, 3, 0, 1, 1, GETDATE()),
('32103', N'احتياطي طوارئ', 'Emergency Reserve', 3, 69, 3, 0, 1, 1, GETDATE());

-- الأرباح المحتجزة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('33101', N'أرباح العام الحالي', 'Current Year Profit', 3, 70, 3, 0, 1, 1, GETDATE()),
('33102', N'أرباح السنوات السابقة', 'Prior Years Profit', 3, 70, 3, 0, 1, 1, GETDATE()),
('33103', N'خسائر مرحلة', 'Accumulated Losses', 3, 70, 3, 0, 1, 1, GETDATE());

-- الإيرادات - المستوى الثاني
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('41', N'إيرادات المبيعات', 'Sales Revenue', 4, 4, 2, 1, 0, 1, GETDATE()),
('42', N'إيرادات أخرى', 'Other Revenue', 4, 4, 2, 1, 0, 1, GETDATE());

-- إيرادات المبيعات - المستوى الثالث
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('411', N'مبيعات البضاعة', 'Merchandise Sales', 4, 79, 3, 1, 0, 1, GETDATE()),
('412', N'مبيعات الخدمات', 'Service Revenue', 4, 79, 3, 1, 0, 1, GETDATE()),
('413', N'خصومات ومردودات مبيعات', 'Sales Discounts & Returns', 4, 79, 3, 1, 0, 1, GETDATE());

-- مبيعات البضاعة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('41101', N'مبيعات محلية', 'Local Sales', 4, 81, 4, 0, 1, 1, GETDATE()),
('41102', N'مبيعات تصدير', 'Export Sales', 4, 81, 4, 0, 1, 1, GETDATE()),
('41103', N'مبيعات جملة', 'Wholesale Sales', 4, 81, 4, 0, 1, 1, GETDATE()),
('41104', N'مبيعات قطاعي', 'Retail Sales', 4, 81, 4, 0, 1, 1, GETDATE());

-- مبيعات الخدمات
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('41201', N'إيرادات خدمات فنية', 'Technical Services Revenue', 4, 82, 4, 0, 1, 1, GETDATE()),
('41202', N'إيرادات خدمات استشارية', 'Consulting Services Revenue', 4, 82, 4, 0, 1, 1, GETDATE()),
('41203', N'إيرادات خدمات صيانة', 'Maintenance Services Revenue', 4, 82, 4, 0, 1, 1, GETDATE());

-- خصومات ومردودات مبيعات
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('41301', N'خصومات مبيعات', 'Sales Discounts', 4, 83, 4, 0, 1, 1, GETDATE()),
('41302', N'مردودات مبيعات', 'Sales Returns', 4, 83, 4, 0, 1, 1, GETDATE()),
('41303', N'مسموحات مبيعات', 'Sales Allowances', 4, 83, 4, 0, 1, 1, GETDATE());

-- إيرادات أخرى
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('42101', N'إيرادات فوائد', 'Interest Income', 4, 80, 3, 0, 1, 1, GETDATE()),
('42102', N'إيرادات إيجار', 'Rental Income', 4, 80, 3, 0, 1, 1, GETDATE()),
('42103', N'أرباح بيع أصول', 'Gain on Sale of Assets', 4, 80, 3, 0, 1, 1, GETDATE()),
('42104', N'إيرادات متنوعة', 'Miscellaneous Income', 4, 80, 3, 0, 1, 1, GETDATE()),
('42105', N'أرباح صرف عملات أجنبية', 'Foreign Exchange Gains', 4, 80, 3, 0, 1, 1, GETDATE());

-- المصروفات - المستوى الثاني
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('51', N'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 6, 5, 2, 1, 0, 1, GETDATE()),
('52', N'مصروفات التشغيل', 'Operating Expenses', 5, 5, 2, 1, 0, 1, GETDATE()),
('53', N'مصروفات إدارية', 'Administrative Expenses', 5, 5, 2, 1, 0, 1, GETDATE()),
('54', N'مصروفات تسويقية', 'Marketing Expenses', 5, 5, 2, 1, 0, 1, GETDATE()),
('55', N'مصروفات مالية', 'Financial Expenses', 5, 5, 2, 1, 0, 1, GETDATE());

-- تكلفة البضاعة المباعة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('51101', N'تكلفة المبيعات', 'Cost of Sales', 6, 99, 3, 0, 1, 1, GETDATE()),
('51102', N'مشتريات', 'Purchases', 6, 99, 3, 0, 1, 1, GETDATE()),
('51103', N'مصروفات شراء', 'Purchase Expenses', 6, 99, 3, 0, 1, 1, GETDATE()),
('51104', N'مردودات ومسموحات مشتريات', 'Purchase Returns & Allowances', 6, 99, 3, 0, 1, 1, GETDATE());

-- مصروفات التشغيل
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('52101', N'رواتب وأجور', 'Salaries & Wages', 5, 100, 3, 0, 1, 1, GETDATE()),
('52102', N'إيجار', 'Rent Expense', 5, 100, 3, 0, 1, 1, GETDATE()),
('52103', N'كهرباء ومياه', 'Utilities', 5, 100, 3, 0, 1, 1, GETDATE()),
('52104', N'هاتف وإنترنت', 'Telephone & Internet', 5, 100, 3, 0, 1, 1, GETDATE()),
('52105', N'صيانة وإصلاح', 'Maintenance & Repairs', 5, 100, 3, 0, 1, 1, GETDATE()),
('52106', N'وقود ومواصلات', 'Fuel & Transportation', 5, 100, 3, 0, 1, 1, GETDATE()),
('52107', N'تأمينات', 'Insurance', 5, 100, 3, 0, 1, 1, GETDATE()),
('52108', N'إهلاك', 'Depreciation', 5, 100, 3, 0, 1, 1, GETDATE());

-- مصروفات إدارية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('53101', N'مصروفات إدارية عامة', 'General Administrative Expenses', 5, 101, 3, 0, 1, 1, GETDATE()),
('53102', N'مصروفات قانونية ومهنية', 'Legal & Professional Fees', 5, 101, 3, 0, 1, 1, GETDATE()),
('53103', N'مصروفات تدريب', 'Training Expenses', 5, 101, 3, 0, 1, 1, GETDATE()),
('53104', N'مصروفات سفر وانتقال', 'Travel & Transportation', 5, 101, 3, 0, 1, 1, GETDATE());

-- مصروفات تسويقية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('54101', N'دعاية وإعلان', 'Advertising & Marketing', 5, 102, 3, 0, 1, 1, GETDATE()),
('54102', N'عمولات مبيعات', 'Sales Commissions', 5, 102, 3, 0, 1, 1, GETDATE()),
('54103', N'مصروفات معارض', 'Exhibition Expenses', 5, 102, 3, 0, 1, 1, GETDATE()),
('54104', N'هدايا وضيافة', 'Gifts & Entertainment', 5, 102, 3, 0, 1, 1, GETDATE());

-- مصروفات مالية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('55101', N'فوائد القروض', 'Interest on Loans', 5, 103, 3, 0, 1, 1, GETDATE()),
('55102', N'مصروفات بنكية', 'Bank Charges', 5, 103, 3, 0, 1, 1, GETDATE()),
('55103', N'خسائر صرف عملات أجنبية', 'Foreign Exchange Losses', 5, 103, 3, 0, 1, 1, GETDATE()),
('55104', N'مصروفات مالية أخرى', 'Other Financial Expenses', 5, 103, 3, 0, 1, 1, GETDATE());
