<!-- Terra Retail ERP - Professional Suppliers Module -->
<div class="suppliers-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">إدارة الموردين</h1>
        <p class="page-subtitle">إدارة شاملة لجميع موردي المتجر والشركات</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" class="add-btn" (click)="openAddSupplier()">
          <mat-icon>business</mat-icon>
          <span>إضافة مورد جديد</span>
        </button>
        <button mat-stroked-button class="export-btn" (click)="exportSuppliers()">
          <mat-icon>file_download</mat-icon>
          <span>تصدير</span>
        </button>
        <button mat-stroked-button class="import-btn" (click)="importSuppliers()">
          <mat-icon>file_upload</mat-icon>
          <span>استيراد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <mat-card class="filters-card">
      <mat-card-content>
        <div class="filters-grid">
          
          <!-- Search -->
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>البحث</mat-label>
            <input matInput placeholder="ابحث بالاسم، الهاتف، أو الرقم..." 
                   [(ngModel)]="searchTerm" (input)="onSearch()">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <!-- Supplier Type Filter -->
          <mat-form-field appearance="outline">
            <mat-label>نوع المورد</mat-label>
            <mat-select [(ngModel)]="selectedSupplierType" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الأنواع</mat-option>
              <mat-option *ngFor="let type of supplierTypes" [value]="type.id">
                {{ type.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Country Filter -->
          <mat-form-field appearance="outline">
            <mat-label>البلد</mat-label>
            <mat-select [(ngModel)]="selectedCountry" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع البلدان</mat-option>
              <mat-option *ngFor="let country of countries" [value]="country.id">
                {{ country.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Status Filter -->
          <mat-form-field appearance="outline">
            <mat-label>الحالة</mat-label>
            <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الحالات</mat-option>
              <mat-option value="active">نشط</mat-option>
              <mat-option value="inactive">غير نشط</mat-option>
              <mat-option value="blocked">محظور</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Clear Filters -->
          <button mat-stroked-button class="clear-filters-btn" (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            <span>مسح الفلاتر</span>
          </button>

        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-section">
    <div class="stats-grid">
      
      <div class="stat-card total-suppliers">
        <div class="stat-icon">
          <mat-icon>business</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalSuppliers }}</div>
          <div class="stat-label">إجمالي الموردين</div>
        </div>
      </div>

      <div class="stat-card active-suppliers">
        <div class="stat-icon">
          <mat-icon>verified</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.activeSuppliers }}</div>
          <div class="stat-label">الموردين النشطين</div>
        </div>
      </div>

      <div class="stat-card local-suppliers">
        <div class="stat-icon">
          <mat-icon>location_on</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.localSuppliers }}</div>
          <div class="stat-label">موردين محليين</div>
        </div>
      </div>

      <div class="stat-card international-suppliers">
        <div class="stat-icon">
          <mat-icon>public</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.internationalSuppliers }}</div>
          <div class="stat-label">موردين دوليين</div>
        </div>
      </div>

    </div>
  </div>

  <!-- Suppliers Table -->
  <div class="table-section">
    <mat-card class="table-card">
      
      <!-- Table Header -->
      <div class="table-header">
        <div class="table-title">
          <h3>قائمة الموردين</h3>
          <span class="results-count">({{ filteredSuppliers.length }} مورد)</span>
        </div>
        <div class="table-actions">
          <mat-form-field appearance="outline" class="page-size-field">
            <mat-label>عدد الصفوف</mat-label>
            <mat-select [(ngModel)]="pageSize" (selectionChange)="onPageSizeChange()">
              <mat-option value="10">10</mat-option>
              <mat-option value="25">25</mat-option>
              <mat-option value="50">50</mat-option>
              <mat-option value="100">100</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Table Content -->
      <div class="table-container">
        <table mat-table [dataSource]="paginatedSuppliers" class="suppliers-table" matSort>

          <!-- Supplier Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>كود المورد</th>
            <td mat-cell *matCellDef="let supplier">
              <span class="supplier-code">{{ supplier.code }}</span>
            </td>
          </ng-container>

          <!-- Supplier Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم المورد</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="supplier-info">
                <div class="supplier-name">{{ supplier.name }}</div>
                <div class="supplier-type">{{ supplier.supplierTypeName }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Contact Info Column -->
          <ng-container matColumnDef="contact">
            <th mat-header-cell *matHeaderCellDef>معلومات الاتصال</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="contact-info">
                <div class="phone" *ngIf="supplier.phone">
                  <mat-icon>phone</mat-icon>
                  <span>{{ supplier.phone }}</span>
                </div>
                <div class="email" *ngIf="supplier.email">
                  <mat-icon>email</mat-icon>
                  <span>{{ supplier.email }}</span>
                </div>
                <div class="website" *ngIf="supplier.website">
                  <mat-icon>language</mat-icon>
                  <span>{{ supplier.website }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Location Column -->
          <ng-container matColumnDef="location">
            <th mat-header-cell *matHeaderCellDef>الموقع</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="location-info">
                <div class="country">
                  <mat-icon>flag</mat-icon>
                  <span>{{ supplier.countryName }}</span>
                </div>
                <div class="city" *ngIf="supplier.city">{{ supplier.city }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Products Count Column -->
          <ng-container matColumnDef="products">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>المنتجات</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="products-count">
                <span class="count">{{ supplier.productsCount }}</span>
                <span class="label">منتج</span>
              </div>
            </td>
          </ng-container>

          <!-- Balance Column -->
          <ng-container matColumnDef="balance">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="balance" [class]="getBalanceClass(supplier.balance)">
                {{ supplier.balance | number:'1.2-2' }} جنيه
              </div>
            </td>
          </ng-container>

          <!-- Rating Column -->
          <ng-container matColumnDef="rating">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>التقييم</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="rating">
                <div class="stars">
                  <mat-icon *ngFor="let star of getStarsArray(supplier.rating)" 
                           [class]="star ? 'filled' : 'empty'">
                    {{ star ? 'star' : 'star_border' }}
                  </mat-icon>
                </div>
                <span class="rating-value">({{ supplier.rating }}/5)</span>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>الحالة</th>
            <td mat-cell *matCellDef="let supplier">
              <span class="status-badge" [class]="getStatusClass(supplier.status)">
                {{ getStatusText(supplier.status) }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let supplier">
              <div class="actions-buttons">
                <button mat-icon-button color="primary" 
                        matTooltip="عرض التفاصيل" 
                        (click)="viewSupplier(supplier)">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button color="accent" 
                        matTooltip="تعديل" 
                        (click)="editSupplier(supplier)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" 
                        matTooltip="حذف" 
                        (click)="deleteSupplier(supplier)">
                  <mat-icon>delete</mat-icon>
                </button>
                <button mat-icon-button 
                        matTooltip="المنتجات" 
                        (click)="viewSupplierProducts(supplier)">
                  <mat-icon>inventory</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
              class="table-row" (click)="viewSupplier(row)"></tr>

        </table>
      </div>

      <!-- Pagination -->
      <mat-paginator 
        [length]="filteredSuppliers.length"
        [pageSize]="pageSize"
        [pageSizeOptions]="[10, 25, 50, 100]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>

    </mat-card>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل البيانات...</p>
  </div>

</div>
