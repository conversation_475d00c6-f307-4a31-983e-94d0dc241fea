-- إصلاح ذكي للترميز العربي
-- Smart Arabic Encoding Fix

USE TerraRetailERP;
GO

PRINT N'بدء الإصلاح الذكي للترميز العربي...';

-- تحديث أنواع العملاء الموجودة
UPDATE CustomerTypes SET 
    NameAr = N'عميل عادي',
    NameEn = N'Regular Customer'
WHERE Id = 1;

UPDATE CustomerTypes SET 
    NameAr = N'عميل جملة',
    NameEn = N'Wholesale Customer'
WHERE Id = 2;

UPDATE CustomerTypes SET 
    NameAr = N'عميل VIP',
    NameEn = N'VIP Customer'
WHERE Id = 3;

UPDATE CustomerTypes SET 
    NameAr = N'عميل مؤسسي',
    NameEn = N'Corporate Customer'
WHERE Id = 4;

UPDATE CustomerTypes SET 
    NameAr = N'عميل حكومي',
    NameEn = N'Government Customer'
WHERE Id = 5;

-- تحديث وحدات القياس
UPDATE Units SET 
    NameAr = N'قطعة',
    NameEn = N'Piece'
WHERE Id = 1;

UPDATE Units SET 
    NameAr = N'كيلوجرام',
    NameEn = N'Kilogram'
WHERE Id = 2;

UPDATE Units SET 
    NameAr = N'جرام',
    NameEn = N'Gram'
WHERE Id = 3;

UPDATE Units SET 
    NameAr = N'متر',
    NameEn = N'Meter'
WHERE Id = 4;

UPDATE Units SET 
    NameAr = N'سنتيمتر',
    NameEn = N'Centimeter'
WHERE Id = 5;

UPDATE Units SET 
    NameAr = N'لتر',
    NameEn = N'Liter'
WHERE Id = 6;

UPDATE Units SET 
    NameAr = N'مليلتر',
    NameEn = N'Milliliter'
WHERE Id = 7;

UPDATE Units SET 
    NameAr = N'علبة',
    NameEn = N'Box'
WHERE Id = 8;

UPDATE Units SET 
    NameAr = N'كرتون',
    NameEn = N'Carton'
WHERE Id = 9;

UPDATE Units SET 
    NameAr = N'دزينة',
    NameEn = N'Dozen'
WHERE Id = 10;

-- تحديث فئات الأسعار
UPDATE PriceCategories SET 
    NameAr = N'سعر التجزئة',
    NameEn = N'Retail Price'
WHERE Id = 1;

UPDATE PriceCategories SET 
    NameAr = N'سعر الجملة',
    NameEn = N'Wholesale Price'
WHERE Id = 2;

UPDATE PriceCategories SET 
    NameAr = N'سعر VIP',
    NameEn = N'VIP Price'
WHERE Id = 3;

UPDATE PriceCategories SET 
    NameAr = N'سعر المؤسسات',
    NameEn = N'Corporate Price'
WHERE Id = 4;

UPDATE PriceCategories SET 
    NameAr = N'سعر الموظفين',
    NameEn = N'Employee Price'
WHERE Id = 5;

-- تحديث فئات المنتجات
UPDATE Categories SET 
    NameAr = N'عام',
    NameEn = N'General'
WHERE Id = 1;

UPDATE Categories SET 
    NameAr = N'أغذية ومشروبات',
    NameEn = N'Food & Beverages'
WHERE Id = 2;

UPDATE Categories SET 
    NameAr = N'إلكترونيات',
    NameEn = N'Electronics'
WHERE Id = 3;

UPDATE Categories SET 
    NameAr = N'ملابس وأزياء',
    NameEn = N'Clothing & Fashion'
WHERE Id = 4;

UPDATE Categories SET 
    NameAr = N'منزل وحديقة',
    NameEn = N'Home & Garden'
WHERE Id = 5;

UPDATE Categories SET 
    NameAr = N'صحة وجمال',
    NameEn = N'Health & Beauty'
WHERE Id = 6;

UPDATE Categories SET 
    NameAr = N'رياضة وترفيه',
    NameEn = N'Sports & Recreation'
WHERE Id = 7;

UPDATE Categories SET 
    NameAr = N'كتب وقرطاسية',
    NameEn = N'Books & Stationery'
WHERE Id = 8;

UPDATE Categories SET 
    NameAr = N'ألعاب وهدايا',
    NameEn = N'Toys & Gifts'
WHERE Id = 9;

UPDATE Categories SET 
    NameAr = N'سيارات وقطع غيار',
    NameEn = N'Automotive & Parts'
WHERE Id = 10;

-- تحديث الفروع
UPDATE Branches SET 
    NameAr = N'الفرع الرئيسي',
    NameEn = N'Main Branch'
WHERE Code = 'MAIN';

-- تحديث طرق الدفع
UPDATE PaymentMethods SET 
    NameAr = N'نقدي',
    NameEn = N'Cash'
WHERE Code = 'CASH';

UPDATE PaymentMethods SET 
    NameAr = N'بطاقة ائتمان',
    NameEn = N'Credit Card'
WHERE Code = 'CREDIT';

UPDATE PaymentMethods SET 
    NameAr = N'آجل',
    NameEn = N'Credit Term'
WHERE Code = 'TERM';

-- تحديث العدادات
UPDATE Counters SET Description = N'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = N'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = N'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = N'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = N'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = N'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

-- إضافة أنواع الموردين إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM SupplierTypes WHERE Id = 1)
BEGIN
    INSERT INTO SupplierTypes (NameAr, NameEn, DefaultPaymentTerms, IsActive, CreatedAt) VALUES
    (N'مورد محلي', N'Local Supplier', 30, 1, GETUTCDATE()),
    (N'مورد دولي', N'International Supplier', 60, 1, GETUTCDATE()),
    (N'مورد حكومي', N'Government Supplier', 45, 1, GETUTCDATE());
END

PRINT N'تم الانتهاء من الإصلاح الذكي للترميز العربي!';

-- عرض النتائج
PRINT N'';
PRINT N'=== النتائج النهائية ===';

SELECT N'أنواع العملاء' as TableName, COUNT(*) as RecordCount FROM CustomerTypes
UNION ALL
SELECT N'وحدات القياس', COUNT(*) FROM Units
UNION ALL
SELECT N'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT N'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT N'طرق الدفع', COUNT(*) FROM PaymentMethods
UNION ALL
SELECT N'أنواع الموردين', COUNT(*) FROM SupplierTypes;

-- عرض عينة من البيانات المحدثة
PRINT N'';
PRINT N'عينة من أنواع العملاء:';
SELECT TOP 3 Id, NameAr, NameEn FROM CustomerTypes ORDER BY Id;

PRINT N'';
PRINT N'عينة من وحدات القياس:';
SELECT TOP 3 Id, NameAr, NameEn, Symbol FROM Units ORDER BY Id;

PRINT N'';
PRINT N'تم إصلاح جميع البيانات العربية بنجاح!';
