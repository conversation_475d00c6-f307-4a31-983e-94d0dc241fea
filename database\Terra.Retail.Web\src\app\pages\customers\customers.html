<div class="customers-page">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-info">
        <h1>إدارة العملاء</h1>
        <p>إدارة ومتابعة جميع عملاء المؤسسة التجارية</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="addCustomer()">
          <mat-icon>person_add</mat-icon>
          إضافة عميل جديد
        </button>
        <button mat-stroked-button (click)="exportCustomers()">
          <mat-icon>download</mat-icon>
          تصدير البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="stats-section">
    <mat-card class="stat-card total-customers">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>people</mat-icon>
          </div>
          <div class="stat-info">
            <h3>إجمالي العملاء</h3>
            <p class="stat-number">{{ totalCustomers }}</p>
            <span class="stat-change positive">+12 هذا الشهر</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card active-customers">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>trending_up</mat-icon>
          </div>
          <div class="stat-info">
            <h3>عملاء نشطون</h3>
            <p class="stat-number">{{ activeCustomers }}</p>
            <span class="stat-change positive">+8.5%</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card new-customers">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>person_add</mat-icon>
          </div>
          <div class="stat-info">
            <h3>عملاء جدد</h3>
            <p class="stat-number">{{ newCustomers }}</p>
            <span class="stat-change neutral">هذا الأسبوع</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card vip-customers">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>star</mat-icon>
          </div>
          <div class="stat-info">
            <h3>عملاء VIP</h3>
            <p class="stat-number">{{ vipCustomers }}</p>
            <span class="stat-change positive">مميزون</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- قسم البحث والفلاتر -->
  <div class="search-filters-wrapper">
    <!-- شريط البحث -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-input">
            <mat-label>البحث في العملاء</mat-label>
            <input matInput
                   [(ngModel)]="searchTerm"
                   (input)="onSearch()"
                   placeholder="ابحث بالاسم أو الهاتف أو البريد الإلكتروني">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- قسم الفلاتر -->
    <div class="filters-section">
      <div class="filters-card">
        <div class="filters-container">
          <div class="filter-item">
            <mat-form-field appearance="outline">
              <mat-label>نوع العميل</mat-label>
              <mat-select [(ngModel)]="selectedCustomerType" (selectionChange)="onFilterChange()">
                <mat-option value="">جميع الأنواع</mat-option>
                <mat-option *ngFor="let type of customerTypes" [value]="type.id">
                  {{ type.nameAr }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="filter-item">
            <mat-form-field appearance="outline">
              <mat-label>المنطقة</mat-label>
              <mat-select [(ngModel)]="selectedArea" (selectionChange)="onFilterChange()">
                <mat-option value="">جميع المناطق</mat-option>
                <mat-option *ngFor="let area of areas" [value]="area.id">
                  {{ area.nameAr }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="filter-item">
            <button mat-raised-button (click)="clearFilters()" class="clear-filters-btn">
              <mat-icon>refresh</mat-icon>
              مسح الفلاتر
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Customers Table -->
  <mat-card class="table-card">
    <mat-card-header>
      <mat-card-title>قائمة العملاء</mat-card-title>
      <mat-card-subtitle>{{ filteredCustomers.length }} عميل</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="filteredCustomers" class="customers-table" matSort>
          <!-- Avatar Column -->
          <ng-container matColumnDef="avatar">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let customer">
              <div class="customer-avatar">
                <mat-icon>account_circle</mat-icon>
              </div>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم العميل</th>
            <td mat-cell *matCellDef="let customer">
              <div class="customer-info">
                <span class="customer-name">{{ customer.fullName || 'غير محدد' }}</span>
                <span class="customer-code">{{ customer.customerCode || 'غير محدد' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Contact Column -->
          <ng-container matColumnDef="contact">
            <th mat-header-cell *matHeaderCellDef>بيانات الاتصال</th>
            <td mat-cell *matCellDef="let customer">
              <div class="contact-info">
                <span class="phone">{{ customer.phoneNumber || 'غير محدد' }}</span>
                <span class="email">{{ customer.email || 'غير محدد' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef>النوع</th>
            <td mat-cell *matCellDef="let customer">
              <span class="customer-type-badge" [class]="getCustomerTypeClass(customer.customerTypeId)">
                {{ getCustomerTypeName(customer.customerTypeId) }}
              </span>
            </td>
          </ng-container>

          <!-- Area Column -->
          <ng-container matColumnDef="area">
            <th mat-header-cell *matHeaderCellDef>المنطقة</th>
            <td mat-cell *matCellDef="let customer">
              <span class="area-name">{{ getAreaName(customer.areaId) }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>الحالة</th>
            <td mat-cell *matCellDef="let customer">
              <span class="status-badge" [class]="customer.isActive ? 'active' : 'inactive'">
                {{ customer.isActive ? 'نشط' : 'غير نشط' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let customer">
              <div class="action-buttons">
                <button mat-icon-button color="primary" (click)="viewCustomer(customer)" matTooltip="عرض">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button color="accent" (click)="editCustomer(customer)" matTooltip="تعديل">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteCustomer(customer)" matTooltip="حذف">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="viewCustomer(row)" class="customer-row"></tr>
        </table>
      </div>

      <!-- Pagination -->
      <mat-paginator
        [pageSizeOptions]="[10, 25, 50, 100]"
        [pageSize]="25"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل بيانات العملاء...</p>
  </div>
</div>
