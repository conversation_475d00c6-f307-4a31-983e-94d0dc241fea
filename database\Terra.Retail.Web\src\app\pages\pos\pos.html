<!-- نقطة البيع الاحترافية -->
<div class="pos-container">
  <!-- Header -->
  <div class="pos-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <mat-icon class="pos-icon">storefront</mat-icon>
          نقطة البيع
        </h1>
        <p>{{ currentUser?.branch?.nameAr || 'الفرع الرئيسي' }} - {{ getCurrentDate() }}</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="newSale()" [disabled]="isProcessing">
          <mat-icon>add_shopping_cart</mat-icon>
          بيع جديد
        </button>
        <button mat-stroked-button (click)="openDrawer()">
          <mat-icon>account_balance_wallet</mat-icon>
          فتح الدرج
        </button>
        <button mat-icon-button [matMenuTriggerFor]="posMenu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #posMenu="matMenu">
          <button mat-menu-item (click)="viewSalesHistory()">
            <mat-icon>history</mat-icon>
            <span>تاريخ المبيعات</span>
          </button>
          <button mat-menu-item (click)="printLastReceipt()">
            <mat-icon>print</mat-icon>
            <span>طباعة آخر فاتورة</span>
          </button>
          <button mat-menu-item (click)="endOfDay()">
            <mat-icon>event_note</mat-icon>
            <span>إقفال اليوم</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </div>

  <!-- Main POS Interface -->
  <div class="pos-main">
    <!-- Left Panel - Products -->
    <div class="products-panel">
      <!-- Search -->
      <div class="search-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>البحث في المنتجات</mat-label>
          <input matInput
                 [(ngModel)]="searchTerm"
                 (input)="searchProducts()"
                 placeholder="اسم المنتج أو الباركود"
                 #searchInput>
          <mat-icon matPrefix>search</mat-icon>
          <button mat-icon-button matSuffix (click)="scanBarcode()" matTooltip="مسح الباركود">
            <mat-icon>qr_code_scanner</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <!-- Categories -->
      <div class="categories-section">
        <div class="category-chips">
          <mat-chip-set>
            <mat-chip
              *ngFor="let category of categories"
              [class.selected]="selectedCategory === category.id"
              (click)="selectCategory(category.id)">
              {{ category.nameAr }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="products-grid">
        <div
          *ngFor="let product of filteredProducts"
          class="product-card"
          (click)="addToCart(product)"
          [class.out-of-stock]="product.stock <= 0">
          <div class="product-image">
            <mat-icon>inventory</mat-icon>
          </div>
          <div class="product-info">
            <h4>{{ product.nameAr }}</h4>
            <p class="product-code">{{ product.productCode }}</p>
            <div class="product-price">{{ product.price | currency:'EGP':'symbol':'1.0-2' }}</div>
            <div class="product-stock" [class.low-stock]="product.stock <= 10">
              المخزون: {{ product.stock }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel - Cart & Payment -->
    <div class="cart-panel">
      <!-- Customer Selection -->
      <div class="customer-section">
        <mat-form-field appearance="outline" class="customer-field">
          <mat-label>العميل</mat-label>
          <mat-select [(ngModel)]="selectedCustomer">
            <mat-option value="">عميل نقدي</mat-option>
            <mat-option *ngFor="let customer of customers" [value]="customer.id">
              {{ customer.fullName }}
            </mat-option>
          </mat-select>
          <button mat-icon-button matSuffix (click)="addNewCustomer()" matTooltip="إضافة عميل جديد">
            <mat-icon>person_add</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <!-- Cart Items -->
      <div class="cart-section">
        <div class="cart-header">
          <h3>عناصر الفاتورة</h3>
          <span class="items-count">{{ cartItems.length }} عنصر</span>
        </div>

        <div class="cart-items" *ngIf="cartItems.length > 0; else emptyCart">
          <div *ngFor="let item of cartItems; let i = index" class="cart-item">
            <div class="item-info">
              <h4>{{ item.product.nameAr }}</h4>
              <p>{{ item.product.productCode }}</p>
            </div>
            <div class="item-controls">
              <div class="quantity-controls">
                <button mat-icon-button (click)="decreaseQuantity(i)" [disabled]="item.quantity <= 1">
                  <mat-icon>remove</mat-icon>
                </button>
                <span class="quantity">{{ item.quantity }}</span>
                <button mat-icon-button (click)="increaseQuantity(i)">
                  <mat-icon>add</mat-icon>
                </button>
              </div>
              <div class="item-price">{{ getItemTotal(item) | currency:'EGP':'symbol':'1.0-2' }}</div>
              <button mat-icon-button color="warn" (click)="removeFromCart(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <ng-template #emptyCart>
          <div class="empty-cart">
            <mat-icon>shopping_cart</mat-icon>
            <p>لا توجد عناصر في الفاتورة</p>
            <p>ابحث عن المنتجات وأضفها للفاتورة</p>
          </div>
        </ng-template>
      </div>

      <!-- Totals -->
      <div class="totals-section" *ngIf="cartItems.length > 0">
        <div class="total-row">
          <span>المجموع الفرعي:</span>
          <span>{{ getSubtotal() | currency:'EGP':'symbol':'1.0-2' }}</span>
        </div>
        <div class="total-row">
          <span>الضريبة ({{ taxRate }}%):</span>
          <span>{{ getTax() | currency:'EGP':'symbol':'1.0-2' }}</span>
        </div>
        <div class="total-row discount" *ngIf="discount > 0">
          <span>الخصم:</span>
          <span>-{{ discount | currency:'EGP':'symbol':'1.0-2' }}</span>
        </div>
        <div class="total-row final">
          <span>الإجمالي:</span>
          <span>{{ getTotal() | currency:'EGP':'symbol':'1.0-2' }}</span>
        </div>
      </div>

      <!-- Payment Section -->
      <div class="payment-section" *ngIf="cartItems.length > 0">
        <div class="payment-methods">
          <button
            mat-raised-button
            *ngFor="let method of paymentMethods"
            [color]="selectedPaymentMethod === method.id ? 'primary' : ''"
            (click)="selectPaymentMethod(method.id)"
            class="payment-btn">
            <mat-icon>{{ method.icon }}</mat-icon>
            {{ method.nameAr }}
          </button>
        </div>

        <div class="payment-amount" *ngIf="selectedPaymentMethod === 'cash'">
          <mat-form-field appearance="outline" class="amount-field">
            <mat-label>المبلغ المدفوع</mat-label>
            <input matInput
                   type="number"
                   [(ngModel)]="paidAmount"
                   placeholder="0.00"
                   (input)="calculateChange()">
            <span matSuffix>جنيه</span>
          </mat-form-field>
          <div class="change-amount" *ngIf="getChange() !== 0">
            <span>الباقي: {{ getChange() | currency:'EGP':'symbol':'1.0-2' }}</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button
            mat-raised-button
            color="primary"
            (click)="completeSale()"
            [disabled]="!canCompleteSale()"
            class="complete-btn">
            <mat-icon>check_circle</mat-icon>
            إتمام البيع
          </button>
          <button
            mat-stroked-button
            (click)="clearCart()"
            class="clear-btn">
            <mat-icon>clear</mat-icon>
            مسح الفاتورة
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="isProcessing" class="loading-overlay">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري معالجة العملية...</p>
  </div>
</div>
