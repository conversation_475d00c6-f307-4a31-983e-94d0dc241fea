{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, inject, ChangeDetectorRef, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { j as MatFormField } from './form-field-C9DZXojn.mjs';\nimport { g as MatSelect, M as MatSelectModule } from './module-BDiw_nWS.mjs';\nimport { e as MatTooltip, h as MatTooltipModule } from './module-CWxMD37a.mjs';\nimport { M as MatOption } from './option-BzhYL_xC.mjs';\nimport { M as MatIconButton } from './icon-button-DxiIc1ex.mjs';\nimport { MatButtonModule } from './button.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/cdk/keycodes';\nimport '@angular/forms';\nimport './error-options-DCNQlTOA.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DwiL-HGk.mjs';\nimport './index-BFRo2fUq.mjs';\nimport './common-module-cKSwHniA.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\nimport './module-DzZHEh7B.mjs';\nimport '@angular/cdk/observers';\nimport '@angular/cdk/portal';\nimport './structural-styles-CObeNzjn.mjs';\nimport './ripple-loader-BnMiRtmT.mjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_Conditional_2_Conditional_3_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 14)(1, \"mat-select\", 16, 0);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(3, MatPaginator_Conditional_2_Conditional_3_For_4_Template, 2, 2, \"mat-option\", 17, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const selectRef_r4 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(selectRef_r4.open());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵconditionalCreate(3, MatPaginator_Conditional_2_Conditional_3_Template, 6, 7, \"mat-form-field\", 14);\n    i0.ɵɵconditionalCreate(4, MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(0, ctx_r1._previousButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"disabled\", ctx_r1._previousButtonsDisabled())(\"tabindex\", ctx_r1._previousButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(ctx_r1.getNumberOfPages() - 1, ctx_r1._nextButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"disabled\", ctx_r1._nextButtonsDisabled())(\"tabindex\", ctx_r1._nextButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nlet MatPaginatorIntl = /*#__PURE__*/(() => {\n  class MatPaginatorIntl {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    changes = new Subject();\n    /** A label for the page size selector. */\n    itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n    static ɵfac = function MatPaginatorIntl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorIntl)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatPaginatorIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n  /** The current page index. */\n  pageIndex;\n  /**\n   * Index of the page that was selected previously.\n   * @breaking-change 8.0.0 To be made into a required property.\n   */\n  previousPageIndex;\n  /** The current page size. */\n  pageSize;\n  /** The current total number of items being paged. */\n  length;\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nlet MatPaginator = /*#__PURE__*/(() => {\n  class MatPaginator {\n    _intl = inject(MatPaginatorIntl);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** If set, styles the \"page size\" form field with the designated style. */\n    _formFieldAppearance;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    _pageSizeLabelId = inject(_IdGenerator).getId('mat-paginator-page-size-label-');\n    _intlChanges;\n    _isInitialized = false;\n    _initializedStream = new ReplaySubject(1);\n    /**\n     * Theme color of the underlying form controls. This API is supported in M2\n     * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n      return this._pageIndex;\n    }\n    set pageIndex(value) {\n      this._pageIndex = Math.max(value || 0, 0);\n      this._changeDetectorRef.markForCheck();\n    }\n    _pageIndex = 0;\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n      return this._length;\n    }\n    set length(value) {\n      this._length = value || 0;\n      this._changeDetectorRef.markForCheck();\n    }\n    _length = 0;\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n      return this._pageSize;\n    }\n    set pageSize(value) {\n      this._pageSize = Math.max(value || 0, 0);\n      this._updateDisplayedPageSizeOptions();\n    }\n    _pageSize;\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n      return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n      this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n      this._updateDisplayedPageSizeOptions();\n    }\n    _pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    selectConfig = {};\n    /** Whether the paginator is disabled. */\n    disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    page = new EventEmitter();\n    /** Displayed set of page size options. Will be sorted and include current page size. */\n    _displayedPageSizeOptions;\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor() {\n      const _intl = this._intl;\n      const defaults = inject(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n      if (defaults) {\n        const {\n          pageSize,\n          pageSizeOptions,\n          hidePageSize,\n          showFirstLastButtons\n        } = defaults;\n        if (pageSize != null) {\n          this._pageSize = pageSize;\n        }\n        if (pageSizeOptions != null) {\n          this._pageSizeOptions = pageSizeOptions;\n        }\n        if (hidePageSize != null) {\n          this.hidePageSize = hidePageSize;\n        }\n        if (showFirstLastButtons != null) {\n          this.showFirstLastButtons = showFirstLastButtons;\n        }\n      }\n      this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    ngOnInit() {\n      this._isInitialized = true;\n      this._updateDisplayedPageSizeOptions();\n      this._initializedStream.next();\n    }\n    ngOnDestroy() {\n      this._initializedStream.complete();\n      this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n      if (this.hasNextPage()) {\n        this._navigate(this.pageIndex + 1);\n      }\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n      if (this.hasPreviousPage()) {\n        this._navigate(this.pageIndex - 1);\n      }\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n      // hasPreviousPage being false implies at the start\n      if (this.hasPreviousPage()) {\n        this._navigate(0);\n      }\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n      // hasNextPage being false implies at the end\n      if (this.hasNextPage()) {\n        this._navigate(this.getNumberOfPages() - 1);\n      }\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n      return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n      const maxPageIndex = this.getNumberOfPages() - 1;\n      return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n      if (!this.pageSize) {\n        return 0;\n      }\n      return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n      // Current page needs to be updated to reflect the new page size. Navigate to the page\n      // containing the previous page's first item.\n      const startIndex = this.pageIndex * this.pageSize;\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n      this.pageSize = pageSize;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n      return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n      return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n      if (!this._isInitialized) {\n        return;\n      }\n      // If no page size is provided, use the first page size option or the default page size.\n      if (!this.pageSize) {\n        this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n      }\n      this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n      if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n        this._displayedPageSizeOptions.push(this.pageSize);\n      }\n      // Sort the numbers using a number-specific sort function.\n      this._displayedPageSizeOptions.sort((a, b) => a - b);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n      this.page.emit({\n        previousPageIndex,\n        pageIndex: this.pageIndex,\n        pageSize: this.pageSize,\n        length: this.length\n      });\n    }\n    /** Navigates to a specific page index. */\n    _navigate(index) {\n      const previousIndex = this.pageIndex;\n      if (index !== previousIndex) {\n        this.pageIndex = index;\n        this._emitPageEvent(previousIndex);\n      }\n    }\n    /**\n     * Callback invoked when one of the navigation buttons is called.\n     * @param targetIndex Index to which the paginator should navigate.\n     * @param isDisabled Whether the button is disabled.\n     */\n    _buttonClicked(targetIndex, isDisabled) {\n      // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n      // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n      // check here to avoid the navigation.\n      if (!isDisabled) {\n        this._navigate(targetIndex);\n      }\n    }\n    static ɵfac = function MatPaginator_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginator)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        color: \"color\",\n        pageIndex: [2, \"pageIndex\", \"pageIndex\", numberAttribute],\n        length: [2, \"length\", \"length\", numberAttribute],\n        pageSize: [2, \"pageSize\", \"pageSize\", numberAttribute],\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: [2, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n        showFirstLastButtons: [2, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n        selectConfig: \"selectConfig\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        page: \"page\"\n      },\n      exportAs: [\"matPaginator\"],\n      decls: 14,\n      vars: 14,\n      consts: [[\"selectRef\", \"\"], [1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [1, \"mat-mdc-paginator-touch-target\", 3, \"click\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"matIconButton\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵconditionalCreate(2, MatPaginator_Conditional_2_Template, 5, 4, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(6, MatPaginator_Conditional_6_Template, 3, 5, \"button\", 6);\n          i0.ɵɵelementStart(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx._buttonClicked(ctx.pageIndex - 1, ctx._previousButtonsDisabled());\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 8);\n          i0.ɵɵelement(9, \"path\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx._buttonClicked(ctx.pageIndex + 1, ctx._nextButtonsDisabled());\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 8);\n          i0.ɵɵelement(12, \"path\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵconditionalCreate(13, MatPaginator_Conditional_13_Template, 3, 5, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.hidePageSize ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"disabled\", ctx._previousButtonsDisabled())(\"tabindex\", ctx._previousButtonsDisabled() ? -1 : null);\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"disabled\", ctx._nextButtonsDisabled())(\"tabindex\", ctx._nextButtonsDisabled() ? -1 : null);\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 13 : -1);\n        }\n      },\n      dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height: var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding: var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatPaginator;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatPaginatorModule = /*#__PURE__*/(() => {\n  class MatPaginatorModule {\n    static ɵfac = function MatPaginatorModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n    });\n  }\n  return MatPaginatorModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };", "map": {"version": 3, "names": ["i0", "Injectable", "Optional", "SkipSelf", "InjectionToken", "inject", "ChangeDetectorRef", "numberAttribute", "EventEmitter", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "Subject", "ReplaySubject", "_IdGenerator", "j", "MatFormField", "g", "MatSelect", "M", "MatSelectModule", "e", "MatTooltip", "h", "MatTooltipModule", "MatOption", "MatIconButton", "MatButtonModule", "MatPaginator_Conditional_2_Conditional_3_For_4_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "pageSizeOption_r3", "$implicit", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "MatPaginator_Conditional_2_Conditional_3_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_changePageSize", "value", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener", "selectRef_r4", "ɵɵreference", "open", "_formFieldAppearance", "color", "pageSize", "disabled", "_pageSizeLabelId", "selectConfig", "panelClass", "disableOptionCentering", "ɵɵrepeater", "_displayedPageSizeOptions", "MatPaginator_Conditional_2_Conditional_4_Template", "ɵɵtextInterpolate", "MatPaginator_Conditional_2_Template", "ɵɵconditionalCreate", "ɵɵattribute", "_intl", "itemsPerPageLabel", "ɵɵconditional", "length", "MatPaginator_Conditional_6_Template", "_r5", "MatPaginator_Conditional_6_Template_button_click_0_listener", "_buttonClicked", "_previousButtonsDisabled", "ɵɵnamespaceSVG", "ɵɵelement", "firstPageLabel", "MatPaginator_Conditional_13_Template", "_r6", "MatPaginator_Conditional_13_Template_button_click_0_listener", "getNumberOfPages", "_nextButtonsDisabled", "lastPageLabel", "MatPaginatorIntl", "changes", "nextPageLabel", "previousPageLabel", "getRangeLabel", "page", "Math", "max", "startIndex", "endIndex", "min", "ɵfac", "MatPaginatorIntl_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "MAT_PAGINATOR_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_PAGINATOR_INTL_PROVIDER", "provide", "deps", "useFactory", "DEFAULT_PAGE_SIZE", "PageEvent", "pageIndex", "previousPageIndex", "MAT_PAGINATOR_DEFAULT_OPTIONS", "MatPaginator", "_changeDetectorRef", "getId", "_intlChanges", "_isInitialized", "_initializedStream", "_pageIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_length", "_pageSize", "_updateDisplayedPageSizeOptions", "pageSizeOptions", "_pageSizeOptions", "map", "p", "hidePageSize", "showFirstLastButtons", "initialized", "constructor", "defaults", "optional", "subscribe", "formFieldAppearance", "ngOnInit", "next", "ngOnDestroy", "complete", "unsubscribe", "nextPage", "hasNextPage", "_navigate", "previousPage", "hasPreviousPage", "firstPage", "lastPage", "maxPageIndex", "ceil", "floor", "_emitPageEvent", "slice", "indexOf", "push", "sort", "a", "b", "emit", "index", "previousIndex", "targetIndex", "isDisabled", "MatPaginator_Factory", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "outputs", "exportAs", "decls", "vars", "consts", "template", "MatPaginator_Template", "MatPaginator_Template_button_click_7_listener", "ɵɵnamespaceHTML", "MatPaginator_Template_button_click_10_listener", "dependencies", "styles", "encapsulation", "changeDetection", "MatPaginatorModule", "MatPaginatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, inject, ChangeDetectorRef, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { j as MatFormField } from './form-field-C9DZXojn.mjs';\nimport { g as MatSelect, M as MatSelectModule } from './module-BDiw_nWS.mjs';\nimport { e as MatTooltip, h as MatTooltipModule } from './module-CWxMD37a.mjs';\nimport { M as MatOption } from './option-BzhYL_xC.mjs';\nimport { M as MatIconButton } from './icon-button-DxiIc1ex.mjs';\nimport { MatButtonModule } from './button.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/cdk/keycodes';\nimport '@angular/forms';\nimport './error-options-DCNQlTOA.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DwiL-HGk.mjs';\nimport './index-BFRo2fUq.mjs';\nimport './common-module-cKSwHniA.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\nimport './module-DzZHEh7B.mjs';\nimport '@angular/cdk/observers';\nimport '@angular/cdk/portal';\nimport './structural-styles-CObeNzjn.mjs';\nimport './ripple-loader-BnMiRtmT.mjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    changes = new Subject();\n    /** A label for the page size selector. */\n    itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    getRangeLabel = (page, pageSize, length) => {\n        if (length == 0 || pageSize == 0) {\n            return `0 of ${length}`;\n        }\n        length = Math.max(length, 0);\n        const startIndex = page * pageSize;\n        // If the start index exceeds the list length, do not try and fix the end index to the end.\n        const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n        return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorIntl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatPaginatorIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n    // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n    provide: MatPaginatorIntl,\n    deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n    useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n    /** The current page index. */\n    pageIndex;\n    /**\n     * Index of the page that was selected previously.\n     * @breaking-change 8.0.0 To be made into a required property.\n     */\n    previousPageIndex;\n    /** The current page size. */\n    pageSize;\n    /** The current total number of items being paged. */\n    length;\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n    _intl = inject(MatPaginatorIntl);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** If set, styles the \"page size\" form field with the designated style. */\n    _formFieldAppearance;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    _pageSizeLabelId = inject(_IdGenerator).getId('mat-paginator-page-size-label-');\n    _intlChanges;\n    _isInitialized = false;\n    _initializedStream = new ReplaySubject(1);\n    /**\n     * Theme color of the underlying form controls. This API is supported in M2\n     * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n        return this._pageIndex;\n    }\n    set pageIndex(value) {\n        this._pageIndex = Math.max(value || 0, 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    _pageIndex = 0;\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n        return this._length;\n    }\n    set length(value) {\n        this._length = value || 0;\n        this._changeDetectorRef.markForCheck();\n    }\n    _length = 0;\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n        return this._pageSize;\n    }\n    set pageSize(value) {\n        this._pageSize = Math.max(value || 0, 0);\n        this._updateDisplayedPageSizeOptions();\n    }\n    _pageSize;\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n        return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n        this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n        this._updateDisplayedPageSizeOptions();\n    }\n    _pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    selectConfig = {};\n    /** Whether the paginator is disabled. */\n    disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    page = new EventEmitter();\n    /** Displayed set of page size options. Will be sorted and include current page size. */\n    _displayedPageSizeOptions;\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor() {\n        const _intl = this._intl;\n        const defaults = inject(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n        if (defaults) {\n            const { pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons } = defaults;\n            if (pageSize != null) {\n                this._pageSize = pageSize;\n            }\n            if (pageSizeOptions != null) {\n                this._pageSizeOptions = pageSizeOptions;\n            }\n            if (hidePageSize != null) {\n                this.hidePageSize = hidePageSize;\n            }\n            if (showFirstLastButtons != null) {\n                this.showFirstLastButtons = showFirstLastButtons;\n            }\n        }\n        this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._updateDisplayedPageSizeOptions();\n        this._initializedStream.next();\n    }\n    ngOnDestroy() {\n        this._initializedStream.complete();\n        this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n        if (this.hasNextPage()) {\n            this._navigate(this.pageIndex + 1);\n        }\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n        if (this.hasPreviousPage()) {\n            this._navigate(this.pageIndex - 1);\n        }\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n        // hasPreviousPage being false implies at the start\n        if (this.hasPreviousPage()) {\n            this._navigate(0);\n        }\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n        // hasNextPage being false implies at the end\n        if (this.hasNextPage()) {\n            this._navigate(this.getNumberOfPages() - 1);\n        }\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n        return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n        const maxPageIndex = this.getNumberOfPages() - 1;\n        return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n        if (!this.pageSize) {\n            return 0;\n        }\n        return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n        // Current page needs to be updated to reflect the new page size. Navigate to the page\n        // containing the previous page's first item.\n        const startIndex = this.pageIndex * this.pageSize;\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n        this.pageSize = pageSize;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n        return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n        return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n        if (!this._isInitialized) {\n            return;\n        }\n        // If no page size is provided, use the first page size option or the default page size.\n        if (!this.pageSize) {\n            this._pageSize =\n                this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n        }\n        this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n        if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n            this._displayedPageSizeOptions.push(this.pageSize);\n        }\n        // Sort the numbers using a number-specific sort function.\n        this._displayedPageSizeOptions.sort((a, b) => a - b);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n        this.page.emit({\n            previousPageIndex,\n            pageIndex: this.pageIndex,\n            pageSize: this.pageSize,\n            length: this.length,\n        });\n    }\n    /** Navigates to a specific page index. */\n    _navigate(index) {\n        const previousIndex = this.pageIndex;\n        if (index !== previousIndex) {\n            this.pageIndex = index;\n            this._emitPageEvent(previousIndex);\n        }\n    }\n    /**\n     * Callback invoked when one of the navigation buttons is called.\n     * @param targetIndex Index to which the paginator should navigate.\n     * @param isDisabled Whether the button is disabled.\n     */\n    _buttonClicked(targetIndex, isDisabled) {\n        // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n        // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n        // check here to avoid the navigation.\n        if (!isDisabled) {\n            this._navigate(targetIndex);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginator, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatPaginator, isStandalone: true, selector: \"mat-paginator\", inputs: { color: \"color\", pageIndex: [\"pageIndex\", \"pageIndex\", numberAttribute], length: [\"length\", \"length\", numberAttribute], pageSize: [\"pageSize\", \"pageSize\", numberAttribute], pageSizeOptions: \"pageSizeOptions\", hidePageSize: [\"hidePageSize\", \"hidePageSize\", booleanAttribute], showFirstLastButtons: [\"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute], selectConfig: \"selectConfig\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { page: \"page\" }, host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-paginator\" }, exportAs: [\"matPaginator\"], ngImport: i0, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <!--\\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\\n      otherwise focus is moved to the document body. However, users should not be able to navigate\\n      into these buttons, so `tabindex` is set to -1 when disabled.\\n      -->\\n\\n      @if (showFirstLastButtons) {\\n        <button matIconButton type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"_buttonClicked(0, _previousButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\"\\n                [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button matIconButton type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button matIconButton type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button matIconButton type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\"\\n                [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height: var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding: var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"], dependencies: [{ kind: \"component\", type: MatFormField, selector: \"mat-form-field\", inputs: [\"hideRequiredMarker\", \"color\", \"floatLabel\", \"appearance\", \"subscriptSizing\", \"hintLabel\"], exportAs: [\"matFormField\"] }, { kind: \"component\", type: MatSelect, selector: \"mat-select\", inputs: [\"aria-describedby\", \"panelClass\", \"disabled\", \"disableRipple\", \"tabIndex\", \"hideSingleSelectionIndicator\", \"placeholder\", \"required\", \"multiple\", \"disableOptionCentering\", \"compareWith\", \"value\", \"aria-label\", \"aria-labelledby\", \"errorStateMatcher\", \"typeaheadDebounceInterval\", \"sortComparator\", \"id\", \"panelWidth\", \"canSelectNullableOptions\"], outputs: [\"openedChange\", \"opened\", \"closed\", \"selectionChange\", \"valueChange\"], exportAs: [\"matSelect\"] }, { kind: \"component\", type: MatOption, selector: \"mat-option\", inputs: [\"value\", \"id\", \"disabled\"], outputs: [\"onSelectionChange\"], exportAs: [\"matOption\"] }, { kind: \"component\", type: MatIconButton, selector: \"button[mat-icon-button], a[mat-icon-button], button[matIconButton], a[matIconButton]\", exportAs: [\"matButton\", \"matAnchor\"] }, { kind: \"directive\", type: MatTooltip, selector: \"[matTooltip]\", inputs: [\"matTooltipPosition\", \"matTooltipPositionAtOrigin\", \"matTooltipDisabled\", \"matTooltipShowDelay\", \"matTooltipHideDelay\", \"matTooltipTouchGestures\", \"matTooltip\", \"matTooltipClass\"], exportAs: [\"matTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginator, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-paginator', exportAs: 'matPaginator', host: {\n                        'class': 'mat-mdc-paginator',\n                        'role': 'group',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip], template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <!--\\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\\n      otherwise focus is moved to the document body. However, users should not be able to navigate\\n      into these buttons, so `tabindex` is set to -1 when disabled.\\n      -->\\n\\n      @if (showFirstLastButtons) {\\n        <button matIconButton type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"_buttonClicked(0, _previousButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\"\\n                [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button matIconButton type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button matIconButton type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button matIconButton type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\"\\n                [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height: var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding: var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], length: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSize: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSizeOptions: [{\n                type: Input\n            }], hidePageSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showFirstLastButtons: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectConfig: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], page: [{\n                type: Output\n            }] } });\n\nclass MatPaginatorModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorModule, imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator], exports: [MatPaginator] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorModule, providers: [MAT_PAGINATOR_INTL_PROVIDER], imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n                    exports: [MatPaginator],\n                    providers: [MAT_PAGINATOR_INTL_PROVIDER],\n                }]\n        }] });\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC1O,SAASC,OAAO,EAAEC,aAAa,QAAQ,MAAM;AAC7C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,CAAC,IAAIC,YAAY,QAAQ,2BAA2B;AAC7D,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,eAAe,QAAQ,uBAAuB;AAC5E,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AAC9E,SAASL,CAAC,IAAIM,SAAS,QAAQ,uBAAuB;AACtD,SAASN,CAAC,IAAIO,aAAa,QAAQ,4BAA4B;AAC/D,SAASC,eAAe,QAAQ,cAAc;AAC9C,OAAO,mBAAmB;AAC1B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,iBAAiB;AACxB,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,sBAAsB;AAC7B,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AACjC,OAAO,uBAAuB;AAC9B,OAAO,gBAAgB;AACvB,OAAO,8BAA8B;AACrC,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,uCAAuC;AAC9C,OAAO,gCAAgC;AACvC,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAC/B,OAAO,qBAAqB;AAC5B,OAAO,kCAAkC;AACzC,OAAO,8BAA8B;;AAErC;AACA;AACA;AACA;AAHA,SAAAC,wDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+B6FjC,EAAE,CAAAmC,cAAA,oBAgRywD,CAAC;IAhR5wDnC,EAAE,CAAAoC,MAAA,EAgRi0D,CAAC;IAhRp0DpC,EAAE,CAAAqC,YAAA,CAgR80D,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,iBAAA,GAAAJ,GAAA,CAAAK,SAAA;IAhRj1DvC,EAAE,CAAAwC,UAAA,UAAAF,iBAgRwwD,CAAC;IAhR3wDtC,EAAE,CAAAyC,SAAA,CAgRi0D,CAAC;IAhRp0DzC,EAAE,CAAA0C,kBAAA,MAAAJ,iBAAA,KAgRi0D,CAAC;EAAA;AAAA;AAAA,SAAAK,kDAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAW,GAAA,GAhRp0D5C,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAAmC,cAAA,wBAgRosC,CAAC,uBAAib,CAAC;IAhRznDnC,EAAE,CAAA8C,UAAA,6BAAAC,wFAAAC,MAAA;MAAFhD,EAAE,CAAAiD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAA,OAAFnD,EAAE,CAAAoD,WAAA,CAgR2iDF,MAAA,CAAAG,eAAA,CAAAL,MAAA,CAAAM,KAA4B,CAAC;IAAA,CAAC,CAAC;IAhR5kDtD,EAAE,CAAAuD,gBAAA,IAAAvB,uDAAA,0BAAFhC,EAAE,CAAAwD,yBAgR+1D,CAAC;IAhRl2DxD,EAAE,CAAAqC,YAAA,CAgR03D,CAAC;IAhR73DrC,EAAE,CAAAmC,cAAA,aAgRi9D,CAAC;IAhRp9DnC,EAAE,CAAA8C,UAAA,mBAAAW,uEAAA;MAAFzD,EAAE,CAAAiD,aAAA,CAAAL,GAAA;MAAA,MAAAc,YAAA,GAAF1D,EAAE,CAAA2D,WAAA;MAAA,OAAF3D,EAAE,CAAAoD,WAAA,CAgR+7DM,YAAA,CAAAE,IAAA,CAAe,CAAC;IAAA,CAAC,CAAC;IAhRn9D5D,EAAE,CAAAqC,YAAA,CAgRu9D,CAAC,CAA4B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAhRv/DlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAwC,UAAA,eAAAU,MAAA,CAAAW,oBAgR0mC,CAAC,UAAAX,MAAA,CAAAY,KAA8B,CAAC;IAhR5oC9D,EAAE,CAAAyC,SAAA,CAgR2xC,CAAC;IAhR9xCzC,EAAE,CAAAwC,UAAA,UAAAU,MAAA,CAAAa,QAgR2xC,CAAC,aAAAb,MAAA,CAAAc,QAAsC,CAAC,oBAAAd,MAAA,CAAAe,gBAAqD,CAAC,eAAAf,MAAA,CAAAgB,YAAA,CAAAC,UAAA,MAA6D,CAAC,2BAAAjB,MAAA,CAAAgB,YAAA,CAAAE,sBAA+E,CAAC;IAhRzgDpE,EAAE,CAAAyC,SAAA,EAgR+1D,CAAC;IAhRl2DzC,EAAE,CAAAqE,UAAA,CAAAnB,MAAA,CAAAoB,yBAgR+1D,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhRl2DjC,EAAE,CAAAmC,cAAA,aAgRqnE,CAAC;IAhRxnEnC,EAAE,CAAAoC,MAAA,EAgRioE,CAAC;IAhRpoEpC,EAAE,CAAAqC,YAAA,CAgRuoE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAhR1oElD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAyC,SAAA,CAgRioE,CAAC;IAhRpoEzC,EAAE,CAAAwE,iBAAA,CAAAtB,MAAA,CAAAa,QAgRioE,CAAC;EAAA;AAAA;AAAA,SAAAU,oCAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhRpoEjC,EAAE,CAAAmC,cAAA,YAgRk1B,CAAC,aAAyF,CAAC;IAhR/6BnC,EAAE,CAAAoC,MAAA,EAgR69B,CAAC;IAhRh+BpC,EAAE,CAAAqC,YAAA,CAgRm+B,CAAC;IAhRt+BrC,EAAE,CAAA0E,mBAAA,IAAA/B,iDAAA,4BAgR2hC,CAAC;IAhR9hC3C,EAAE,CAAA0E,mBAAA,IAAAH,iDAAA,iBAgRwjE,CAAC;IAhR3jEvE,EAAE,CAAAqC,YAAA,CAgRgqE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAhRnqElD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAyC,SAAA,CAgR26B,CAAC;IAhR96BzC,EAAE,CAAA2E,WAAA,OAAAzB,MAAA,CAAAe,gBAAA;IAAFjE,EAAE,CAAAyC,SAAA,CAgR69B,CAAC;IAhRh+BzC,EAAE,CAAA0C,kBAAA,MAAAQ,MAAA,CAAA0B,KAAA,CAAAC,iBAAA,KAgR69B,CAAC;IAhRh+B7E,EAAE,CAAAyC,SAAA,CAgR+/D,CAAC;IAhRlgEzC,EAAE,CAAA8E,aAAA,CAAA5B,MAAA,CAAAoB,yBAAA,CAAAS,MAAA,aAgR+/D,CAAC;IAhRlgE/E,EAAE,CAAAyC,SAAA,CAgRkpE,CAAC;IAhRrpEzC,EAAE,CAAA8E,aAAA,CAAA5B,MAAA,CAAAoB,yBAAA,CAAAS,MAAA,cAgRkpE,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgD,GAAA,GAhRrpEjF,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAAmC,cAAA,gBAgRswG,CAAC;IAhRzwGnC,EAAE,CAAA8C,UAAA,mBAAAoC,4DAAA;MAAFlF,EAAE,CAAAiD,aAAA,CAAAgC,GAAA;MAAA,MAAA/B,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAA,OAAFnD,EAAE,CAAAoD,WAAA,CAgR00FF,MAAA,CAAAiC,cAAA,CAAe,CAAC,EAAEjC,MAAA,CAAAkC,wBAAA,CAAyB,CAAC,CAAC;IAAA,CAAC,CAAC;IAhR33FpF,EAAE,CAAAqF,cAAA;IAAFrF,EAAE,CAAAmC,cAAA,YAgRo6G,CAAC;IAhRv6GnC,EAAE,CAAAsF,SAAA,cAgR0/G,CAAC;IAhR7/GtF,EAAE,CAAAqC,YAAA,CAgR4gH,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAhRliHlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAwC,UAAA,eAAAU,MAAA,CAAA0B,KAAA,CAAAW,cAgR2+F,CAAC,uBAAArC,MAAA,CAAAkC,wBAAA,EAAoE,CAAC,aAAAlC,MAAA,CAAAkC,wBAAA,EAAwG,CAAC,aAAAlC,MAAA,CAAAkC,wBAAA,cAAsE,CAAC;IAhRnuGpF,EAAE,CAAA2E,WAAA,eAAAzB,MAAA,CAAA0B,KAAA,CAAAW,cAAA;EAAA;AAAA;AAAA,SAAAC,qCAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwD,GAAA,GAAFzF,EAAE,CAAA6C,gBAAA;IAAF7C,EAAE,CAAAmC,cAAA,gBAgRywL,CAAC;IAhR5wLnC,EAAE,CAAA8C,UAAA,mBAAA4C,6DAAA;MAAF1F,EAAE,CAAAiD,aAAA,CAAAwC,GAAA;MAAA,MAAAvC,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAA,OAAFnD,EAAE,CAAAoD,WAAA,CAgR00KF,MAAA,CAAAiC,cAAA,CAAejC,MAAA,CAAAyC,gBAAA,CAAiB,CAAC,GAAG,CAAC,EAAEzC,MAAA,CAAA0C,oBAAA,CAAqB,CAAC,CAAC;IAAA,CAAC,CAAC;IAhR54K5F,EAAE,CAAAqF,cAAA;IAAFrF,EAAE,CAAAmC,cAAA,YAgRu6L,CAAC;IAhR16LnC,EAAE,CAAAsF,SAAA,cAgR6/L,CAAC;IAhRhgMtF,EAAE,CAAAqC,YAAA,CAgR+gM,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GAhRriMlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAwC,UAAA,eAAAU,MAAA,CAAA0B,KAAA,CAAAiB,aAgR0/K,CAAC,uBAAA3C,MAAA,CAAA0C,oBAAA,EAAgE,CAAC,aAAA1C,MAAA,CAAA0C,oBAAA,EAAoG,CAAC,aAAA1C,MAAA,CAAA0C,oBAAA,cAAkE,CAAC;IAhRtuL5F,EAAE,CAAA2E,WAAA,eAAAzB,MAAA,CAAA0B,KAAA,CAAAiB,aAAA;EAAA;AAAA;AAAA,IA3BzFC,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB;AACJ;AACA;AACA;IACIC,OAAO,GAAG,IAAI/E,OAAO,CAAC,CAAC;IACvB;IACA6D,iBAAiB,GAAG,iBAAiB;IACrC;IACAmB,aAAa,GAAG,WAAW;IAC3B;IACAC,iBAAiB,GAAG,eAAe;IACnC;IACAV,cAAc,GAAG,YAAY;IAC7B;IACAM,aAAa,GAAG,WAAW;IAC3B;IACAK,aAAa,GAAGA,CAACC,IAAI,EAAEpC,QAAQ,EAAEgB,MAAM,KAAK;MACxC,IAAIA,MAAM,IAAI,CAAC,IAAIhB,QAAQ,IAAI,CAAC,EAAE;QAC9B,OAAO,QAAQgB,MAAM,EAAE;MAC3B;MACAA,MAAM,GAAGqB,IAAI,CAACC,GAAG,CAACtB,MAAM,EAAE,CAAC,CAAC;MAC5B,MAAMuB,UAAU,GAAGH,IAAI,GAAGpC,QAAQ;MAClC;MACA,MAAMwC,QAAQ,GAAGD,UAAU,GAAGvB,MAAM,GAAGqB,IAAI,CAACI,GAAG,CAACF,UAAU,GAAGvC,QAAQ,EAAEgB,MAAM,CAAC,GAAGuB,UAAU,GAAGvC,QAAQ;MACtG,OAAO,GAAGuC,UAAU,GAAG,CAAC,MAAMC,QAAQ,OAAOxB,MAAM,EAAE;IACzD,CAAC;IACD,OAAO0B,IAAI,YAAAC,yBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFb,gBAAgB;IAAA;IACnH,OAAOc,KAAK,kBAD6E5G,EAAE,CAAA6G,kBAAA;MAAAC,KAAA,EACYhB,gBAAgB;MAAAiB,OAAA,EAAhBjB,gBAAgB,CAAAW,IAAA;MAAAO,UAAA,EAAc;IAAM;EAC/I;EAAC,OA7BKlB,gBAAgB;AAAA;AA8BtB;EAAA,QAAAmB,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAACC,UAAU,EAAE;EACrD,OAAOA,UAAU,IAAI,IAAIrB,gBAAgB,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,2BAA2B,GAAG;EAChC;EACAC,OAAO,EAAEvB,gBAAgB;EACzBwB,IAAI,EAAE,CAAC,cAAC,IAAIpH,QAAQ,CAAC,CAAC,eAAE,IAAIC,QAAQ,CAAC,CAAC,EAAE2F,gBAAgB,CAAC,CAAC;EAC1DyB,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA,MAAMM,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;EACAC,SAAS;EACT;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;EACA5D,QAAQ;EACR;EACAgB,MAAM;AACV;AACA;AACA,MAAM6C,6BAA6B,gBAAG,IAAIxH,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA;AACA;AACA;AAJA,IAKMyH,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfjD,KAAK,GAAGvE,MAAM,CAACyF,gBAAgB,CAAC;IAChCgC,kBAAkB,GAAGzH,MAAM,CAACC,iBAAiB,CAAC;IAC9C;IACAuD,oBAAoB;IACpB;IACAI,gBAAgB,GAAG5D,MAAM,CAACa,YAAY,CAAC,CAAC6G,KAAK,CAAC,gCAAgC,CAAC;IAC/EC,YAAY;IACZC,cAAc,GAAG,KAAK;IACtBC,kBAAkB,GAAG,IAAIjH,aAAa,CAAC,CAAC,CAAC;IACzC;AACJ;AACA;AACA;AACA;AACA;AACA;IACI6C,KAAK;IACL;IACA,IAAI4D,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACS,UAAU;IAC1B;IACA,IAAIT,SAASA,CAACpE,KAAK,EAAE;MACjB,IAAI,CAAC6E,UAAU,GAAG/B,IAAI,CAACC,GAAG,CAAC/C,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;MACzC,IAAI,CAACwE,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;IACAD,UAAU,GAAG,CAAC;IACd;IACA,IAAIpD,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACsD,OAAO;IACvB;IACA,IAAItD,MAAMA,CAACzB,KAAK,EAAE;MACd,IAAI,CAAC+E,OAAO,GAAG/E,KAAK,IAAI,CAAC;MACzB,IAAI,CAACwE,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;IACAC,OAAO,GAAG,CAAC;IACX;IACA,IAAItE,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACuE,SAAS;IACzB;IACA,IAAIvE,QAAQA,CAACT,KAAK,EAAE;MAChB,IAAI,CAACgF,SAAS,GAAGlC,IAAI,CAACC,GAAG,CAAC/C,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;MACxC,IAAI,CAACiF,+BAA+B,CAAC,CAAC;IAC1C;IACAD,SAAS;IACT;IACA,IAAIE,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACC,gBAAgB;IAChC;IACA,IAAID,eAAeA,CAAClF,KAAK,EAAE;MACvB,IAAI,CAACmF,gBAAgB,GAAG,CAACnF,KAAK,IAAI,EAAE,EAAEoF,GAAG,CAACC,CAAC,IAAIpI,eAAe,CAACoI,CAAC,EAAE,CAAC,CAAC,CAAC;MACrE,IAAI,CAACJ,+BAA+B,CAAC,CAAC;IAC1C;IACAE,gBAAgB,GAAG,EAAE;IACrB;IACAG,YAAY,GAAG,KAAK;IACpB;IACAC,oBAAoB,GAAG,KAAK;IAC5B;IACA3E,YAAY,GAAG,CAAC,CAAC;IACjB;IACAF,QAAQ,GAAG,KAAK;IAChB;IACAmC,IAAI,GAAG,IAAI3F,YAAY,CAAC,CAAC;IACzB;IACA8D,yBAAyB;IACzB;IACAwE,WAAW,GAAG,IAAI,CAACZ,kBAAkB;IACrCa,WAAWA,CAAA,EAAG;MACV,MAAMnE,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMoE,QAAQ,GAAG3I,MAAM,CAACuH,6BAA6B,EAAE;QACnDqB,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACjB,YAAY,GAAGpD,KAAK,CAACmB,OAAO,CAACmD,SAAS,CAAC,MAAM,IAAI,CAACpB,kBAAkB,CAACM,YAAY,CAAC,CAAC,CAAC;MACzF,IAAIY,QAAQ,EAAE;QACV,MAAM;UAAEjF,QAAQ;UAAEyE,eAAe;UAAEI,YAAY;UAAEC;QAAqB,CAAC,GAAGG,QAAQ;QAClF,IAAIjF,QAAQ,IAAI,IAAI,EAAE;UAClB,IAAI,CAACuE,SAAS,GAAGvE,QAAQ;QAC7B;QACA,IAAIyE,eAAe,IAAI,IAAI,EAAE;UACzB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;QAC3C;QACA,IAAII,YAAY,IAAI,IAAI,EAAE;UACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;QACpC;QACA,IAAIC,oBAAoB,IAAI,IAAI,EAAE;UAC9B,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB;QACpD;MACJ;MACA,IAAI,CAAChF,oBAAoB,GAAGmF,QAAQ,EAAEG,mBAAmB,IAAI,SAAS;IAC1E;IACAC,QAAQA,CAAA,EAAG;MACP,IAAI,CAACnB,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACM,+BAA+B,CAAC,CAAC;MACtC,IAAI,CAACL,kBAAkB,CAACmB,IAAI,CAAC,CAAC;IAClC;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACpB,kBAAkB,CAACqB,QAAQ,CAAC,CAAC;MAClC,IAAI,CAACvB,YAAY,CAACwB,WAAW,CAAC,CAAC;IACnC;IACA;IACAC,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjC,SAAS,GAAG,CAAC,CAAC;MACtC;IACJ;IACA;IACAkC,YAAYA,CAAA,EAAG;MACX,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACF,SAAS,CAAC,IAAI,CAACjC,SAAS,GAAG,CAAC,CAAC;MACtC;IACJ;IACA;IACAoC,SAASA,CAAA,EAAG;MACR;MACA,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;MACrB;IACJ;IACA;IACAI,QAAQA,CAAA,EAAG;MACP;MACA,IAAI,IAAI,CAACL,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChE,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;MAC/C;IACJ;IACA;IACAkE,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACnC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC3D,QAAQ,IAAI,CAAC;IACpD;IACA;IACA2F,WAAWA,CAAA,EAAG;MACV,MAAMM,YAAY,GAAG,IAAI,CAACrE,gBAAgB,CAAC,CAAC,GAAG,CAAC;MAChD,OAAO,IAAI,CAAC+B,SAAS,GAAGsC,YAAY,IAAI,IAAI,CAACjG,QAAQ,IAAI,CAAC;IAC9D;IACA;IACA4B,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE;QAChB,OAAO,CAAC;MACZ;MACA,OAAOqC,IAAI,CAAC6D,IAAI,CAAC,IAAI,CAAClF,MAAM,GAAG,IAAI,CAAChB,QAAQ,CAAC;IACjD;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIV,eAAeA,CAACU,QAAQ,EAAE;MACtB;MACA;MACA,MAAMuC,UAAU,GAAG,IAAI,CAACoB,SAAS,GAAG,IAAI,CAAC3D,QAAQ;MACjD,MAAM4D,iBAAiB,GAAG,IAAI,CAACD,SAAS;MACxC,IAAI,CAACA,SAAS,GAAGtB,IAAI,CAAC8D,KAAK,CAAC5D,UAAU,GAAGvC,QAAQ,CAAC,IAAI,CAAC;MACvD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACoG,cAAc,CAACxC,iBAAiB,CAAC;IAC1C;IACA;IACA/B,oBAAoBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAAC5B,QAAQ,IAAI,CAAC,IAAI,CAAC0F,WAAW,CAAC,CAAC;IAC/C;IACA;IACAtE,wBAAwBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACpB,QAAQ,IAAI,CAAC,IAAI,CAAC6F,eAAe,CAAC,CAAC;IACnD;IACA;AACJ;AACA;AACA;IACItB,+BAA+BA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAACN,cAAc,EAAE;QACtB;MACJ;MACA;MACA,IAAI,CAAC,IAAI,CAAClE,QAAQ,EAAE;QAChB,IAAI,CAACuE,SAAS,GACV,IAAI,CAACE,eAAe,CAACzD,MAAM,IAAI,CAAC,GAAG,IAAI,CAACyD,eAAe,CAAC,CAAC,CAAC,GAAGhB,iBAAiB;MACtF;MACA,IAAI,CAAClD,yBAAyB,GAAG,IAAI,CAACkE,eAAe,CAAC4B,KAAK,CAAC,CAAC;MAC7D,IAAI,IAAI,CAAC9F,yBAAyB,CAAC+F,OAAO,CAAC,IAAI,CAACtG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9D,IAAI,CAACO,yBAAyB,CAACgG,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC;MACtD;MACA;MACA,IAAI,CAACO,yBAAyB,CAACiG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;MACpD,IAAI,CAAC3C,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;IACA;IACA+B,cAAcA,CAACxC,iBAAiB,EAAE;MAC9B,IAAI,CAACxB,IAAI,CAACuE,IAAI,CAAC;QACX/C,iBAAiB;QACjBD,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB3D,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBgB,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;IACN;IACA;IACA4E,SAASA,CAACgB,KAAK,EAAE;MACb,MAAMC,aAAa,GAAG,IAAI,CAAClD,SAAS;MACpC,IAAIiD,KAAK,KAAKC,aAAa,EAAE;QACzB,IAAI,CAAClD,SAAS,GAAGiD,KAAK;QACtB,IAAI,CAACR,cAAc,CAACS,aAAa,CAAC;MACtC;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIzF,cAAcA,CAAC0F,WAAW,EAAEC,UAAU,EAAE;MACpC;MACA;MACA;MACA,IAAI,CAACA,UAAU,EAAE;QACb,IAAI,CAACnB,SAAS,CAACkB,WAAW,CAAC;MAC/B;IACJ;IACA,OAAOpE,IAAI,YAAAsE,qBAAApE,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkB,YAAY;IAAA;IAC/G,OAAOmD,IAAI,kBAhR8EhL,EAAE,CAAAiL,iBAAA;MAAAC,IAAA,EAgRJrD,YAAY;MAAAsD,SAAA;MAAAC,SAAA,WAAsjB,OAAO;MAAAC,MAAA;QAAAvH,KAAA;QAAA4D,SAAA,gCAA5cnH,eAAe;QAAAwE,MAAA,0BAAgCxE,eAAe;QAAAwD,QAAA,8BAAsCxD,eAAe;QAAAiI,eAAA;QAAAI,YAAA,sCAAsFnI,gBAAgB;QAAAoI,oBAAA,sDAA0EpI,gBAAgB;QAAAyD,YAAA;QAAAF,QAAA,8BAAoEvD,gBAAgB;MAAA;MAAA6K,OAAA;QAAAnF,IAAA;MAAA;MAAAoF,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAA3J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhRlgBjC,EAAE,CAAAmC,cAAA,YAgRqtB,CAAC,YAA8C,CAAC;UAhRvwBnC,EAAE,CAAA0E,mBAAA,IAAAD,mCAAA,gBAgR+xB,CAAC;UAhRlyBzE,EAAE,CAAAmC,cAAA,YAgR8tE,CAAC,YAAyE,CAAC;UAhR3yEnC,EAAE,CAAAoC,MAAA,EAgR82E,CAAC;UAhRj3EpC,EAAE,CAAAqC,YAAA,CAgRo3E,CAAC;UAhRv3ErC,EAAE,CAAA0E,mBAAA,IAAAM,mCAAA,mBAgRgsF,CAAC;UAhRnsFhF,EAAE,CAAAmC,cAAA,eAgR+mI,CAAC;UAhRlnInC,EAAE,CAAA8C,UAAA,mBAAA+I,8CAAA;YAAA,OAgR+qH3J,GAAA,CAAAiD,cAAA,CAAAjD,GAAA,CAAAwF,SAAA,GAA2B,CAAC,EAAExF,GAAA,CAAAkD,wBAAA,CAAyB,CAAC,CAAC;UAAA,CAAC,CAAC;UAhR5uHpF,EAAE,CAAAqF,cAAA;UAAFrF,EAAE,CAAAmC,cAAA,YAgRwwI,CAAC;UAhR3wInC,EAAE,CAAAsF,SAAA,aAgR+0I,CAAC;UAhRl1ItF,EAAE,CAAAqC,YAAA,CAgR+1I,CAAC,CAAgB,CAAC;UAhRn3IrC,EAAE,CAAA8L,eAAA;UAAF9L,EAAE,CAAAmC,cAAA,iBAgR25J,CAAC;UAhR95JnC,EAAE,CAAA8C,UAAA,mBAAAiJ,+CAAA;YAAA,OAgRm/I7J,GAAA,CAAAiD,cAAA,CAAAjD,GAAA,CAAAwF,SAAA,GAA2B,CAAC,EAAExF,GAAA,CAAA0D,oBAAA,CAAqB,CAAC,CAAC;UAAA,CAAC,CAAC;UAhR5iJ5F,EAAE,CAAAqF,cAAA;UAAFrF,EAAE,CAAAmC,cAAA,aAgRojK,CAAC;UAhRvjKnC,EAAE,CAAAsF,SAAA,eAgR4nK,CAAC;UAhR/nKtF,EAAE,CAAAqC,YAAA,CAgR4oK,CAAC,CAAgB,CAAC;UAhRhqKrC,EAAE,CAAA0E,mBAAA,KAAAc,oCAAA,oBAgRisK,CAAC;UAhRpsKxF,EAAE,CAAAqC,YAAA,CAgRujM,CAAC,CAAS,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAhR5kMjC,EAAE,CAAAyC,SAAA,EAgRuqE,CAAC;UAhR1qEzC,EAAE,CAAA8E,aAAA,EAAA5C,GAAA,CAAA0G,YAAA,SAgRuqE,CAAC;UAhR1qE5I,EAAE,CAAAyC,SAAA,EAgR82E,CAAC;UAhRj3EzC,EAAE,CAAA0C,kBAAA,MAAAR,GAAA,CAAA0C,KAAA,CAAAsB,aAAA,CAAAhE,GAAA,CAAAwF,SAAA,EAAAxF,GAAA,CAAA6B,QAAA,EAAA7B,GAAA,CAAA6C,MAAA,MAgR82E,CAAC;UAhRj3E/E,EAAE,CAAAyC,SAAA,CAgRwiH,CAAC;UAhR3iHzC,EAAE,CAAA8E,aAAA,CAAA5C,GAAA,CAAA2G,oBAAA,SAgRwiH,CAAC;UAhR3iH7I,EAAE,CAAAyC,SAAA,CAgR81H,CAAC;UAhRj2HzC,EAAE,CAAAwC,UAAA,eAAAN,GAAA,CAAA0C,KAAA,CAAAqB,iBAgR81H,CAAC,uBAAA/D,GAAA,CAAAkD,wBAAA,EAAkE,CAAC,aAAAlD,GAAA,CAAAkD,wBAAA,EAAoG,CAAC,aAAAlD,GAAA,CAAAkD,wBAAA,cAAoE,CAAC;UAhR9kIpF,EAAE,CAAA2E,WAAA,eAAAzC,GAAA,CAAA0C,KAAA,CAAAqB,iBAAA;UAAFjG,EAAE,CAAAyC,SAAA,EAgRspJ,CAAC;UAhRzpJzC,EAAE,CAAAwC,UAAA,eAAAN,GAAA,CAAA0C,KAAA,CAAAoB,aAgRspJ,CAAC,uBAAA9D,GAAA,CAAA0D,oBAAA,EAA8D,CAAC,aAAA1D,GAAA,CAAA0D,oBAAA,EAAgG,CAAC,aAAA1D,GAAA,CAAA0D,oBAAA,cAAgE,CAAC;UAhR13J5F,EAAE,CAAA2E,WAAA,eAAAzC,GAAA,CAAA0C,KAAA,CAAAoB,aAAA;UAAFhG,EAAE,CAAAyC,SAAA,EAgR2iM,CAAC;UAhR9iMzC,EAAE,CAAA8E,aAAA,CAAA5C,GAAA,CAAA2G,oBAAA,UAgR2iM,CAAC;QAAA;MAAA;MAAAmD,YAAA,GAAqrF5K,YAAY,EAA4LE,SAAS,EAAogBO,SAAS,EAAqJC,aAAa,EAAuKJ,UAAU;MAAAuK,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACj3T;EAAC,OA5NKtE,YAAY;AAAA;AA6NlB;EAAA,QAAAZ,SAAA,oBAAAA,SAAA;AAAA;AAgCoB,IAEdmF,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAO3F,IAAI,YAAA4F,2BAAA1F,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyF,kBAAkB;IAAA;IACrH,OAAOE,IAAI,kBAtT8EtM,EAAE,CAAAuM,gBAAA;MAAArB,IAAA,EAsTSkB;IAAkB;IACtH,OAAOI,IAAI,kBAvT8ExM,EAAE,CAAAyM,gBAAA;MAAAC,SAAA,EAuTwC,CAACtF,2BAA2B,CAAC;MAAAuF,OAAA,GAAY5K,eAAe,EAAEP,eAAe,EAAEI,gBAAgB,EAAEiG,YAAY;IAAA;EAChP;EAAC,OAJKuE,kBAAkB;AAAA;AAKxB;EAAA,QAAAnF,SAAA,oBAAAA,SAAA;AAAA;AASA,SAASW,6BAA6B,EAAER,2BAA2B,EAAEF,mCAAmC,EAAEW,YAAY,EAAE/B,gBAAgB,EAAEsG,kBAAkB,EAAE3E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}