<!-- Professional Add Product Page -->
<div class="professional-add-product">
  <!-- Professional Header -->
  <div class="professional-header animate-slide-up">
    <div class="header-content">
      <div class="header-info">
        <h1 class="header-title">
          <mat-icon class="header-icon">add_business</mat-icon>
          إضافة منتج جديد
        </h1>
        <p class="header-subtitle">إنشاء منتج جديد وإضافته إلى نظام المخزون بطريقة احترافية</p>
      </div>
      <div class="header-actions">
        <button mat-stroked-button class="professional-button secondary" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
          العودة للمنتجات
        </button>
        <button mat-raised-button class="professional-button primary" (click)="saveProduct()" [disabled]="isLoading">
          <mat-icon>save</mat-icon>
          حفظ المنتج
        </button>
      </div>
    </div>
    
    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
        <div class="step-circle">
          <mat-icon *ngIf="currentStep > 1">check</mat-icon>
          <span *ngIf="currentStep <= 1">1</span>
        </div>
        <span class="step-label">المعلومات الأساسية</span>
      </div>
      <div class="step-line" [class.completed]="currentStep > 1"></div>
      <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
        <div class="step-circle">
          <mat-icon *ngIf="currentStep > 2">check</mat-icon>
          <span *ngIf="currentStep <= 2">2</span>
        </div>
        <span class="step-label">التسعير والمخزون</span>
      </div>
      <div class="step-line" [class.completed]="currentStep > 2"></div>
      <div class="step" [class.active]="currentStep >= 3" [class.completed]="currentStep > 3">
        <div class="step-circle">
          <mat-icon *ngIf="currentStep > 3">check</mat-icon>
          <span *ngIf="currentStep <= 3">3</span>
        </div>
        <span class="step-label">الموردين والإعدادات</span>
      </div>
      <div class="step-line" [class.completed]="currentStep > 3"></div>
      <div class="step" [class.active]="currentStep >= 4">
        <div class="step-circle">
          <span>4</span>
        </div>
        <span class="step-label">المراجعة والحفظ</span>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="professional-content">
    <form [formGroup]="productForm" class="professional-form">
      
      <!-- Step 1: Basic Information -->
      <div class="form-step" *ngIf="currentStep === 1">
        <div class="step-content">
          <h2 class="step-title">
            <mat-icon>info</mat-icon>
            المعلومات الأساسية
          </h2>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>تفاصيل المنتج</h3>
              <p>أدخل المعلومات الأساسية للمنتج</p>
            </div>
            
            <div class="card-content">
              <!-- Product Type & Code -->
              <div class="form-grid">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>نوع المنتج</mat-label>
                    <mat-select formControlName="productType" required>
                      <mat-option value="محلي">
                        <div class="option-content">
                          <strong>منتج محلي</strong>
                          <small>يبدأ من 2000000000001</small>
                        </div>
                      </mat-option>
                      <mat-option value="دولي">
                        <div class="option-content">
                          <strong>منتج دولي</strong>
                          <small>كود يدوي</small>
                        </div>
                      </mat-option>
                      <mat-option value="موزون">
                        <div class="option-content">
                          <strong>منتج موزون</strong>
                          <small>للميزان الإلكتروني</small>
                        </div>
                      </mat-option>
                    </mat-select>
                    <mat-error>نوع المنتج مطلوب</mat-error>
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>كود المنتج</mat-label>
                    <input matInput formControlName="productCode" placeholder="كود المنتج">
                    <button mat-icon-button matSuffix type="button" (click)="generateCode()" 
                            [disabled]="productForm.get('productType')?.value === 'دولي'">
                      <mat-icon>auto_awesome</mat-icon>
                    </button>
                    <mat-error>كود المنتج مطلوب</mat-error>
                  </mat-form-field>
                </div>
              </div>
              
              <!-- Product Names -->
              <div class="form-grid">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>اسم المنتج بالعربية</mat-label>
                    <input matInput formControlName="nameAr" placeholder="اسم المنتج بالعربية" required>
                    <mat-error>اسم المنتج بالعربية مطلوب</mat-error>
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>اسم المنتج بالإنجليزية</mat-label>
                    <input matInput formControlName="nameEn" placeholder="Product Name in English">
                  </mat-form-field>
                </div>
              </div>
              
              <!-- Description -->
              <div class="form-group full-width">
                <mat-form-field appearance="outline" class="professional-input">
                  <mat-label>وصف المنتج</mat-label>
                  <textarea matInput formControlName="description" rows="3" 
                           placeholder="وصف تفصيلي للمنتج"></textarea>
                </mat-form-field>
              </div>
              
              <!-- Category & Unit -->
              <div class="form-grid">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>الفئة</mat-label>
                    <mat-select formControlName="categoryId" required>
                      <mat-option *ngFor="let category of categories" [value]="category.id">
                        {{ category.nameAr }}
                      </mat-option>
                    </mat-select>
                    <mat-error>الفئة مطلوبة</mat-error>
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>وحدة القياس</mat-label>
                    <mat-select formControlName="unitId" required>
                      <mat-option *ngFor="let unit of units" [value]="unit.id">
                        {{ unit.nameAr }} ({{ unit.symbol }})
                      </mat-option>
                    </mat-select>
                    <mat-error>وحدة القياس مطلوبة</mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Pricing & Inventory -->
      <div class="form-step" *ngIf="currentStep === 2">
        <div class="step-content">
          <h2 class="step-title">
            <mat-icon>attach_money</mat-icon>
            التسعير والمخزون
          </h2>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>الأسعار</h3>
              <p>حدد أسعار الشراء والبيع</p>
            </div>
            
            <div class="card-content">
              <div class="form-grid">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>سعر الشراء</mat-label>
                    <input matInput type="number" formControlName="purchasePrice" 
                           placeholder="0.00" min="0" step="0.01">
                    <span matSuffix>جنيه</span>
                    <mat-error>سعر الشراء مطلوب</mat-error>
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>سعر البيع</mat-label>
                    <input matInput type="number" formControlName="salePrice" 
                           placeholder="0.00" min="0" step="0.01">
                    <span matSuffix>جنيه</span>
                    <mat-error>سعر البيع مطلوب</mat-error>
                  </mat-form-field>
                </div>
              </div>
              
              <!-- Profit Margin Display -->
              <div class="profit-margin" *ngIf="profitMargin">
                <mat-icon>trending_up</mat-icon>
                <span>هامش الربح: {{ profitMargin }}%</span>
              </div>
            </div>
          </div>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>إدارة المخزون</h3>
              <p>حدد مستويات المخزون</p>
            </div>
            
            <div class="card-content">
              <div class="form-grid">
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>المخزون الحالي</mat-label>
                    <input matInput type="number" formControlName="currentStock" 
                           placeholder="0" min="0">
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>الحد الأدنى</mat-label>
                    <input matInput type="number" formControlName="minStock" 
                           placeholder="0" min="0">
                  </mat-form-field>
                </div>
                
                <div class="form-group">
                  <mat-form-field appearance="outline" class="professional-input">
                    <mat-label>الحد الأقصى</mat-label>
                    <input matInput type="number" formControlName="maxStock" 
                           placeholder="0" min="0">
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Suppliers & Settings -->
      <div class="form-step" *ngIf="currentStep === 3">
        <div class="step-content">
          <h2 class="step-title">
            <mat-icon>business</mat-icon>
            الموردين والإعدادات
          </h2>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>الموردين</h3>
              <p>اختر المورد الرئيسي والموردين الإضافيين</p>
            </div>
            
            <div class="card-content">
              <div class="form-group">
                <mat-form-field appearance="outline" class="professional-input">
                  <mat-label>المورد الرئيسي</mat-label>
                  <mat-select formControlName="mainSupplierId" required>
                    <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
                      {{ supplier.nameAr }} - {{ supplier.supplierCode }}
                    </mat-option>
                  </mat-select>
                  <mat-error>المورد الرئيسي مطلوب</mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>إعدادات المنتج</h3>
              <p>خصائص إضافية للمنتج</p>
            </div>
            
            <div class="card-content">
              <div class="settings-grid">
                <mat-checkbox formControlName="isActive" class="professional-checkbox">
                  <span class="checkbox-label">منتج نشط</span>
                  <small>المنتج متاح للبيع</small>
                </mat-checkbox>
                
                <mat-checkbox formControlName="isWeighted" class="professional-checkbox">
                  <span class="checkbox-label">منتج موزون</span>
                  <small>يباع بالوزن</small>
                </mat-checkbox>
                
                <mat-checkbox formControlName="hasExpiry" class="professional-checkbox">
                  <span class="checkbox-label">له تاريخ انتهاء</span>
                  <small>يتطلب تتبع الصلاحية</small>
                </mat-checkbox>
                
                <mat-checkbox formControlName="trackSerial" class="professional-checkbox">
                  <span class="checkbox-label">تتبع الأرقام التسلسلية</span>
                  <small>لكل قطعة رقم مميز</small>
                </mat-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Review -->
      <div class="form-step" *ngIf="currentStep === 4">
        <div class="step-content">
          <h2 class="step-title">
            <mat-icon>preview</mat-icon>
            مراجعة البيانات
          </h2>
          
          <div class="professional-card">
            <div class="card-header">
              <h3>ملخص المنتج</h3>
              <p>راجع جميع البيانات قبل الحفظ</p>
            </div>
            
            <div class="card-content">
              <div class="review-grid">
                <div class="review-item">
                  <label>اسم المنتج:</label>
                  <span>{{ productForm.get('nameAr')?.value }}</span>
                </div>
                <div class="review-item">
                  <label>كود المنتج:</label>
                  <span>{{ productForm.get('productCode')?.value }}</span>
                </div>
                <div class="review-item">
                  <label>نوع المنتج:</label>
                  <span>{{ productForm.get('productType')?.value }}</span>
                </div>
                <div class="review-item">
                  <label>سعر البيع:</label>
                  <span>{{ productForm.get('salePrice')?.value }} جنيه</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
    
    <!-- Navigation Buttons -->
    <div class="navigation-buttons">
      <button mat-stroked-button class="professional-button secondary" 
              (click)="previousStep()" [disabled]="currentStep === 1">
        <mat-icon>navigate_before</mat-icon>
        السابق
      </button>
      
      <button mat-raised-button class="professional-button primary" 
              (click)="nextStep()" *ngIf="currentStep < 4">
        التالي
        <mat-icon>navigate_next</mat-icon>
      </button>
      
      <button mat-raised-button class="professional-button primary" 
              (click)="saveProduct()" *ngIf="currentStep === 4" [disabled]="isLoading">
        <mat-icon>save</mat-icon>
        حفظ المنتج
      </button>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="loading-content">
    <mat-progress-spinner diameter="50"></mat-progress-spinner>
    <p class="loading-text">جاري حفظ المنتج...</p>
  </div>
</div>
