@echo off
title Terra Retail ERP - System Launcher
color 0A
chcp 65001 >nul

echo.
echo ========================================
echo    🏪 Terra Retail ERP System
echo    نظام إدارة متكامل للمتاجر
echo ========================================
echo.

echo 🔧 Preparing system...
echo تحضير النظام...
echo.

echo 📊 Checking database...
echo فحص قاعدة البيانات...
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "SELECT COUNT(*) as Tables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h -1 >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ Database connected
    echo ✅ قاعدة البيانات متصلة
) else (
    echo ❌ Database connection failed
    echo ❌ فشل الاتصال بقاعدة البيانات
    echo.
    echo Please run database setup first:
    echo يرجى تشغيل إعداد قاعدة البيانات أولاً:
    echo cd database ^&^& run_database.bat
    pause
    exit /b 1
)

echo.
echo 🚀 Starting API Server...
echo تشغيل خادم API...
start "Terra Retail API" /min cmd /c "cd src\Terra.Retail.API && echo Starting API Server... && dotnet run --urls http://localhost:5000"

echo ⏳ Waiting for API to initialize...
echo انتظار تهيئة API...
timeout /t 8 /nobreak >nul

echo.
echo 🌐 Starting Angular Application...
echo تشغيل تطبيق Angular...
start "Terra Retail Web" cmd /c "cd src\Terra.Retail.Web && echo Starting Angular App... && ng serve --port 4200 --open"

echo.
echo ⏳ Waiting for Angular to compile...
echo انتظار تجميع Angular...
timeout /t 15 /nobreak >nul

echo.
echo ========================================
echo ✅ System Started Successfully!
echo ✅ تم تشغيل النظام بنجاح!
echo ========================================
echo.
echo 📍 Access URLs | روابط الوصول:
echo.
echo 🔗 Angular App:  http://localhost:4200
echo 🔗 API Server:   http://localhost:5000  
echo 📖 API Docs:     http://localhost:5000/swagger
echo 🏥 Health Check: http://localhost:5000/health
echo.
echo 💡 Both applications are running in separate windows
echo 💡 التطبيقان يعملان في نوافذ منفصلة
echo.
echo 🔄 To stop the system, close both command windows
echo 🔄 لإيقاف النظام، أغلق نافذتي الأوامر
echo.

echo 🌐 Opening Angular App in browser...
echo فتح تطبيق Angular في المتصفح...
timeout /t 5 /nobreak >nul
start http://localhost:4200

echo.
echo ========================================
echo 🎉 Terra Retail ERP is now running!
echo 🎉 Terra Retail ERP يعمل الآن!
echo ========================================
echo.
echo Press any key to view system status...
echo اضغط أي مفتاح لعرض حالة النظام...
pause >nul

echo.
echo 📊 System Status | حالة النظام:
echo.

echo Testing API Health...
echo اختبار صحة API...
curl -s http://localhost:5000/health >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ API is running
    echo ✅ API يعمل
) else (
    echo ❌ API not responding
    echo ❌ API لا يستجيب
)

echo.
echo Testing Angular App...
echo اختبار تطبيق Angular...
curl -s http://localhost:4200 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Angular App is running
    echo ✅ تطبيق Angular يعمل
) else (
    echo ⏳ Angular App still loading...
    echo ⏳ تطبيق Angular لا يزال يحمل...
)

echo.
echo 🎯 System is ready for use!
echo 🎯 النظام جاهز للاستخدام!
echo.

pause
