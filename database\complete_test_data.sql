-- 🏪 بيانات تجريبية كاملة لسنتر مفروشات مصري
-- مع ضمان صحة اللغة العربية والسعة اللامحدودة

-- 🗑️ مسح البيانات القديمة وإعادة تعيين العدادات
DELETE FROM JournalEntryDetails;
DELETE FROM JournalEntries;
DELETE FROM CashReceipts;
DELETE FROM CashPayments;
DELETE FROM FinancialTransactions;
DELETE FROM ActivityLog;
DELETE FROM Products WHERE Id > 1;
DELETE FROM Suppliers WHERE Id > 1;
DELETE FROM Customers WHERE Id > 1;
DELETE FROM Branches WHERE Id > 2;
DELETE FROM Users WHERE Id > 2;
DELETE FROM Categories WHERE Id > 4;
DELETE FROM BarcodeTypes;
UPDATE Counters SET CurrentValue = 0;

-- 📊 إنشاء أنواع الباركود
INSERT INTO BarcodeTypes (TypeCode, NameAr, NameEn, Format, IsActive, CreatedAt) VALUES
('EAN13', N'باركود أوروبي 13 رقم', 'European Article Number 13', 'EAN-13', 1, GETDATE()),
('CODE128', N'كود 128', 'Code 128', 'Code128', 1, GETDATE()),
('QR', N'رمز الاستجابة السريعة', 'QR Code', 'QR', 1, GETDATE()),
('UPC', N'رمز المنتج العالمي', 'Universal Product Code', 'UPC-A', 1, GETDATE());

-- 🏢 إنشاء الفروع
INSERT INTO Branches (Code, NameAr, NameEn, Address, Phone, Email, IsActive, IsMainBranch, CreatedAt) VALUES
('BR001', N'الفرع الرئيسي - القاهرة', 'Main Branch - Cairo', N'شارع التحرير، وسط البلد، القاهرة', '02-25555555', '<EMAIL>', 1, 1, GETDATE()),
('BR002', N'فرع الإسكندرية', 'Alexandria Branch', N'شارع الكورنيش، الإسكندرية', '03-4888888', '<EMAIL>', 1, 0, GETDATE()),
('BR003', N'فرع الجيزة', 'Giza Branch', N'شارع الهرم، الجيزة', '02-33777777', '<EMAIL>', 1, 0, GETDATE());

-- 👥 إنشاء المستخدمين
INSERT INTO Users (Username, Email, PasswordHash, FullName, IsActive, IsSystemAdmin, CreatedAt) VALUES
('admin', '<EMAIL>', 'hashed_password_123', N'أحمد محمد الإدارة', 1, 1, GETDATE()),
('sales1', '<EMAIL>', 'hashed_password_456', N'فاطمة علي المبيعات', 1, 0, GETDATE()),
('cashier1', '<EMAIL>', 'hashed_password_789', N'محمد سعد الكاشير', 1, 0, GETDATE()),
('manager1', '<EMAIL>', 'hashed_password_101', N'سارة أحمد المدير', 1, 0, GETDATE());

-- 🏷️ إنشاء فئات المنتجات
INSERT INTO Categories (NameAr, NameEn, ParentId, DisplayOrder, IsActive, CreatedAt) VALUES
(N'غرف النوم', 'Bedrooms', NULL, 1, 1, GETDATE()),
(N'غرف المعيشة', 'Living Rooms', NULL, 2, 1, GETDATE()),
(N'غرف الطعام', 'Dining Rooms', NULL, 3, 1, GETDATE()),
(N'المطابخ', 'Kitchens', NULL, 4, 1, GETDATE()),
(N'الأدوات الكهربائية', 'Electrical Appliances', NULL, 5, 1, GETDATE()),
(N'الأدوات المنزلية', 'Home Accessories', NULL, 6, 1, GETDATE()),
(N'الإضاءة', 'Lighting', NULL, 7, 1, GETDATE()),
(N'السجاد والموكيت', 'Carpets & Rugs', NULL, 8, 1, GETDATE());

-- 📏 إنشاء وحدات القياس
INSERT INTO Units (NameAr, NameEn, Symbol, IsActive, CreatedAt) VALUES
(N'قطعة', 'Piece', 'PCS', 1, GETDATE()),
(N'طقم', 'Set', 'SET', 1, GETDATE()),
(N'متر مربع', 'Square Meter', 'M2', 1, GETDATE()),
(N'كيلوجرام', 'Kilogram', 'KG', 1, GETDATE()),
(N'صندوق', 'Box', 'BOX', 1, GETDATE()),
(N'زوج', 'Pair', 'PAIR', 1, GETDATE());

-- 💰 إنشاء فئات الأسعار
INSERT INTO PriceCategories (NameAr, NameEn, DiscountPercentage, MarkupPercentage, IsActive, CreatedAt) VALUES
(N'سعر التجزئة', 'Retail Price', 0.00, 0.00, 1, GETDATE()),
(N'سعر الجملة', 'Wholesale Price', 15.00, 0.00, 1, GETDATE()),
(N'عملاء مميزين', 'VIP Customers', 25.00, 0.00, 1, GETDATE()),
(N'سعر الموظفين', 'Employee Price', 30.00, 0.00, 1, GETDATE());

-- 🏪 إنشاء أنواع العملاء
INSERT INTO CustomerTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'عميل تجزئة', 'Retail Customer', 1, 1, GETDATE()),
(N'عميل جملة', 'Wholesale Customer', 2, 1, GETDATE()),
(N'عميل مميز', 'VIP Customer', 3, 1, GETDATE()),
(N'عميل مؤسسي', 'Corporate Customer', 4, 1, GETDATE());

-- 🏭 إنشاء أنواع الموردين
INSERT INTO SupplierTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'مورد محلي', 'Local Supplier', 1, 1, GETDATE()),
(N'مورد مستورد', 'Import Supplier', 2, 1, GETDATE()),
(N'مصنع', 'Factory', 3, 1, GETDATE()),
(N'وكيل تجاري', 'Commercial Agent', 4, 1, GETDATE());

-- 💳 إنشاء طرق الدفع
INSERT INTO PaymentMethods (NameAr, NameEn, IsActive, CreatedAt) VALUES
(N'نقدي', 'Cash', 1, GETDATE()),
(N'فيزا', 'Visa Card', 1, GETDATE()),
(N'ماستر كارد', 'Master Card', 1, GETDATE()),
(N'تقسيط', 'Installment', 1, GETDATE()),
(N'تحويل بنكي', 'Bank Transfer', 1, GETDATE());

-- 🏭 إنشاء الموردين
INSERT INTO Suppliers (SupplierCode, NameAr, NameEn, SupplierTypeId, Phone1, Email, Address, PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('SUP001', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 1, '02-********', '<EMAIL>', N'المنطقة الصناعية، القاهرة', 30, 100000.00, 0.00, 0.00, 1, GETDATE()),
('SUP002', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 3, '03-5678901', '<EMAIL>', N'برج العرب، الإسكندرية', 45, 150000.00, 0.00, 0.00, 1, GETDATE()),
('SUP003', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, '02-********', '<EMAIL>', N'مدينة نصر، القاهرة', 30, 80000.00, 0.00, 0.00, 1, GETDATE()),
('SUP004', N'مستوردو الإضاءة الأوروبية', 'European Lighting Importers', 2, '02-********', '<EMAIL>', N'مصر الجديدة، القاهرة', 60, 200000.00, 0.00, 0.00, 1, GETDATE()),
('SUP005', N'مصنع السجاد الشرقي', 'Oriental Carpet Factory', 3, '02-44567890', '<EMAIL>', N'حلوان، القاهرة', 30, 120000.00, 0.00, 0.00, 1, GETDATE());

-- 👥 إنشاء العملاء
INSERT INTO Customers (CustomerCode, NameAr, NameEn, CustomerTypeId, Phone1, Email, Address, PriceCategoryId, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('CUS001', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, '01012345678', '<EMAIL>', N'شارع الجمهورية، وسط البلد، القاهرة', 1, 0.00, 0.00, 1, GETDATE()),
('CUS002', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, '01098765432', '<EMAIL>', N'شارع الكورنيش، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE()),
('CUS003', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 4, '02-********', '<EMAIL>', N'شارع النيل، الجيزة', 2, 0.00, 0.00, 1, GETDATE()),
('CUS004', N'محمد سعد الدين', 'Mohamed Saad El Din', 3, '01076543210', '<EMAIL>', N'شارع التحرير، القاهرة', 3, 0.00, 0.00, 1, GETDATE()),
('CUS005', N'سارة علي محمود', 'Sara Ali Mahmoud', 1, '01055555555', '<EMAIL>', N'شارع سعد زغلول، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE()),
('CUS006', N'شركة المقاولات الكبرى', 'Major Contracting Company', 4, '02-44567890', '<EMAIL>', N'مدينة نصر، القاهرة', 2, 0.00, 0.00, 1, GETDATE());

-- 🛏️ إنشاء المنتجات - غرف النوم
INSERT INTO Products (ProductCode, NameAr, NameEn, Description, CategoryId, UnitId, BarcodeTypeId, Barcode, CostPrice, BasePrice, ProfitMargin, MinimumStock, MaximumStock, IsActive, CreatedAt) VALUES
('PRD001', N'سرير خشب زان مقاس 180×200', 'Beech Wood Bed 180x200', N'سرير من خشب الزان الطبيعي مع كومودينو', 5, 1, 1, '1234567890123', 2500.00, 3500.00, 40.00, 5, 50, 1, GETDATE()),
('PRD002', N'دولاب ملابس 6 أبواب', '6-Door Wardrobe', N'دولاب ملابس من الخشب المضغوط مع مرآة', 5, 1, 1, '1234567890124', 3000.00, 4200.00, 40.00, 3, 30, 1, GETDATE()),
('PRD003', N'تسريحة مع مرآة ومقعد', 'Dressing Table with Mirror', N'تسريحة خشبية مع مرآة كبيرة ومقعد مبطن', 5, 1, 1, '1234567890125', 1200.00, 1680.00, 40.00, 5, 40, 1, GETDATE()),

-- 🛋️ إنشاء المنتجات - غرف المعيشة
('PRD004', N'طقم صالون جلد طبيعي 3+2+1', 'Leather Sofa Set 3+2+1', N'طقم صالون من الجلد الطبيعي مع إطار خشبي', 6, 2, 1, '1234567890126', 8000.00, 12000.00, 50.00, 2, 20, 1, GETDATE()),
('PRD005', N'طاولة وسط زجاجية', 'Glass Coffee Table', N'طاولة وسط من الزجاج المقسى مع قاعدة معدنية', 6, 1, 1, '1234567890127', 800.00, 1200.00, 50.00, 5, 30, 1, GETDATE()),
('PRD006', N'مكتبة خشبية 5 أرفف', '5-Shelf Wooden Bookcase', N'مكتبة من الخشب الطبيعي بـ 5 أرفف', 6, 1, 1, '1234567890128', 1500.00, 2100.00, 40.00, 3, 25, 1, GETDATE()),

-- 🍽️ إنشاء المنتجات - غرف الطعام
('PRD007', N'طاولة طعام خشبية 8 أشخاص', 'Wooden Dining Table 8 Seats', N'طاولة طعام من الخشب الطبيعي تتسع لـ 8 أشخاص', 7, 1, 1, '1234567890129', 2000.00, 2800.00, 40.00, 2, 15, 1, GETDATE()),
('PRD008', N'كراسي طعام مبطنة - 6 قطع', 'Upholstered Dining Chairs - 6pcs', N'مجموعة من 6 كراسي طعام مبطنة بالقماش', 7, 2, 1, '1234567890130', 1800.00, 2520.00, 40.00, 3, 20, 1, GETDATE()),
('PRD009', N'بوفيه مع مرآة', 'Buffet with Mirror', N'بوفيه خشبي مع مرآة وأدراج للتخزين', 7, 1, 1, '1234567890131', 2500.00, 3500.00, 40.00, 2, 12, 1, GETDATE()),

-- 🔌 إنشاء المنتجات - الأدوات الكهربائية
('PRD010', N'ثلاجة سامسونج 18 قدم', 'Samsung Refrigerator 18ft', N'ثلاجة سامسونج نوفروست 18 قدم مع فريزر', 9, 1, 1, '1234567890132', 8000.00, 11200.00, 40.00, 5, 25, 1, GETDATE()),
('PRD011', N'غسالة أتوماتيك LG 7 كيلو', 'LG Automatic Washer 7kg', N'غسالة أتوماتيك من LG سعة 7 كيلو', 9, 1, 1, '1234567890133', 4500.00, 6300.00, 40.00, 3, 20, 1, GETDATE()),
('PRD012', N'مكيف شارب 1.5 حصان', 'Sharp AC 1.5 HP', N'مكيف هواء شارب 1.5 حصان بارد فقط', 9, 1, 1, '1234567890134', 3000.00, 4200.00, 40.00, 5, 30, 1, GETDATE()),

-- 🏠 إنشاء المنتجات - الأدوات المنزلية
('PRD013', N'طقم أواني طبخ تيفال 12 قطعة', 'Tefal Cookware Set 12pcs', N'طقم أواني طبخ تيفال مع طلاء تيفلون', 10, 2, 1, '1234567890135', 800.00, 1120.00, 40.00, 10, 50, 1, GETDATE()),
('PRD014', N'مجموعة أطباق بورسلين 24 قطعة', 'Porcelain Dinnerware Set 24pcs', N'مجموعة أطباق من البورسلين الفاخر', 10, 2, 1, '1234567890136', 600.00, 840.00, 40.00, 8, 40, 1, GETDATE()),
('PRD015', N'مكنسة كهربائية توشيبا', 'Toshiba Vacuum Cleaner', N'مكنسة كهربائية توشيبا بقوة شفط عالية', 10, 1, 1, '1234567890137', 1200.00, 1680.00, 40.00, 5, 25, 1, GETDATE()),

-- 💡 إنشاء المنتجات - الإضاءة
('PRD016', N'نجفة كريستال كلاسيكية', 'Classic Crystal Chandelier', N'نجفة من الكريستال الطبيعي تصميم كلاسيكي', 11, 1, 1, '1234567890138', 2000.00, 2800.00, 40.00, 3, 15, 1, GETDATE()),
('PRD017', N'أباجورة طاولة LED', 'LED Table Lamp', N'أباجورة طاولة بإضاءة LED قابلة للتحكم', 11, 1, 1, '1234567890139', 300.00, 420.00, 40.00, 10, 50, 1, GETDATE()),
('PRD018', N'إضاءة سقف دائرية LED', 'Round LED Ceiling Light', N'إضاءة سقف دائرية بتقنية LED موفرة للطاقة', 11, 1, 1, '1234567890140', 500.00, 700.00, 40.00, 8, 40, 1, GETDATE()),

-- 🏺 إنشاء المنتجات - السجاد والموكيت
('PRD019', N'سجادة فارسية 3×4 متر', 'Persian Carpet 3x4m', N'سجادة فارسية أصلية مصنوعة يدوياً', 12, 1, 1, '1234567890141', 3000.00, 4200.00, 40.00, 2, 10, 1, GETDATE()),
('PRD020', N'موكيت تركي مقاس 2×3 متر', 'Turkish Carpet 2x3m', N'موكيت تركي عالي الجودة بألوان متنوعة', 12, 1, 1, '1234567890142', 1500.00, 2100.00, 40.00, 5, 25, 1, GETDATE());

PRINT N'تم إنشاء جميع البيانات التجريبية بنجاح! 🎉';
PRINT N'البرنامج جاهز للاستخدام مع دعم السعة اللامحدودة ✅';
