using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// المستخدمين
    /// </summary>
    public class User : BaseEntity
    {
        /// <summary>
        /// اسم المستخدم (فريد)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// البريد الإلكتروني (فريد)
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور المشفرة
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [MaxLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// صورة المستخدم
        /// </summary>
        [MaxLength(500)]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// هل المستخدم نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل البريد الإلكتروني مؤكد
        /// </summary>
        public bool IsEmailConfirmed { get; set; } = false;

        /// <summary>
        /// هل رقم الهاتف مؤكد
        /// </summary>
        public bool IsPhoneConfirmed { get; set; } = false;

        /// <summary>
        /// تاريخ آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// عنوان IP لآخر تسجيل دخول
        /// </summary>
        [MaxLength(45)]
        public string? LastLoginIP { get; set; }

        /// <summary>
        /// عدد محاولات تسجيل الدخول الفاشلة
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// تاريخ قفل الحساب
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// رمز إعادة تعيين كلمة المرور
        /// </summary>
        [MaxLength(255)]
        public string? PasswordResetToken { get; set; }

        /// <summary>
        /// تاريخ انتهاء رمز إعادة تعيين كلمة المرور
        /// </summary>
        public DateTime? PasswordResetExpiry { get; set; }

        /// <summary>
        /// رمز تأكيد البريد الإلكتروني
        /// </summary>
        [MaxLength(255)]
        public string? EmailConfirmationToken { get; set; }

        /// <summary>
        /// اللغة المفضلة
        /// </summary>
        [MaxLength(5)]
        public string PreferredLanguage { get; set; } = "ar";

        /// <summary>
        /// المنطقة الزمنية
        /// </summary>
        [MaxLength(50)]
        public string TimeZone { get; set; } = "Asia/Riyadh";

        /// <summary>
        /// إعدادات المستخدم (JSON)
        /// </summary>
        [MaxLength(2000)]
        public string? Settings { get; set; }

        /// <summary>
        /// معرف الموظف المرتبط
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// الفرع الافتراضي للمستخدم
        /// </summary>
        public int? DefaultBranchId { get; set; }

        /// <summary>
        /// هل المستخدم مدير نظام
        /// </summary>
        public bool IsSuperAdmin { get; set; } = false;

        /// <summary>
        /// تاريخ انتهاء صلاحية الحساب
        /// </summary>
        public DateTime? AccountExpiryDate { get; set; }

        /// <summary>
        /// هل يجب تغيير كلمة المرور في التسجيل التالي
        /// </summary>
        public bool MustChangePassword { get; set; } = false;

        // Navigation Properties
        public virtual Employee? Employee { get; set; }
        public virtual Branch? DefaultBranch { get; set; }
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<UserBranch> UserBranches { get; set; } = new List<UserBranch>();
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
        public virtual ICollection<CustomerTransaction> CustomerTransactions { get; set; } = new List<CustomerTransaction>();
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();
    }
}
