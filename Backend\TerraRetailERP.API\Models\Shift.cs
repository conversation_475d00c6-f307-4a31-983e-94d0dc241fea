using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Shifts")]
    public class Shift
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string ShiftName { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string ShiftCode { get; set; } = string.Empty;

        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }

        public bool IsOvernightShift { get; set; } = false;

        [Column(TypeName = "decimal(4,2)")]
        public decimal WorkingHours { get; set; }

        public int BreakDuration { get; set; } = 0; // in minutes
        public int GracePeriodMinutes { get; set; } = 15;
        public int OvertimeAfterMinutes { get; set; } = 0;

        public int BranchId { get; set; }
        public bool IsActive { get; set; } = true;
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
        public virtual ICollection<ShiftBreak> ShiftBreaks { get; set; } = new List<ShiftBreak>();
    }

    [Table("ShiftBreaks")]
    public class ShiftBreak
    {
        [Key]
        public int Id { get; set; }

        public int ShiftId { get; set; }

        [Required]
        [StringLength(50)]
        public string BreakName { get; set; } = string.Empty;

        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public int DurationMinutes { get; set; }
        public bool IsPaid { get; set; } = true;
        public bool IsOptional { get; set; } = false;
        public int DisplayOrder { get; set; } = 1;

        // Navigation Properties
        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; } = null!;
    }

    [Table("EmployeeShifts")]
    public class EmployeeShift
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsTemporary { get; set; } = false;
        public int AssignedBy { get; set; }
        public DateTime AssignedAt { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; } = null!;

        [ForeignKey("AssignedBy")]
        public virtual User AssignedByUser { get; set; } = null!;
    }
}
