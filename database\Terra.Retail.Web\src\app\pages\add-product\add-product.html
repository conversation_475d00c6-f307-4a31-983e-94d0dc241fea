<!-- صفحة إضافة منتج جديد -->
<div class="add-product-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <mat-icon>add_box</mat-icon>
          إضافة منتج جديد
        </h1>
        <p>إضافة منتج جديد إلى المخزون</p>
      </div>
      <div class="header-actions">
        <button mat-stroked-button (click)="cancel()">
          <mat-icon>cancel</mat-icon>
          إلغاء
        </button>
        <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isLoading">
          <mat-icon>save</mat-icon>
          حفظ المنتج
        </button>
      </div>
    </div>
  </div>

  <!-- Form Content -->
  <form [formGroup]="productForm" (ngSubmit)="onSubmit()">
    <div class="form-container">
      
      <!-- نوع المنتج وتوليد الكود -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>qr_code</mat-icon>
            نوع المنتج والكود
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <!-- نوع المنتج -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>نوع المنتج</mat-label>
              <mat-select formControlName="productType" required>
                <mat-option *ngFor="let type of productTypes" [value]="type.value">
                  <div class="product-type-option">
                    <strong>{{ type.label }}</strong>
                    <small>{{ type.description }}</small>
                  </div>
                </mat-option>
              </mat-select>
              <mat-error>نوع المنتج مطلوب</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- كود المنتج -->
            <mat-form-field appearance="outline" class="flex-2">
              <mat-label>كود المنتج</mat-label>
              <input matInput
                     formControlName="productCode"
                     placeholder="كود المنتج"
                     (blur)="checkProductCode()">
              <mat-error *ngIf="productForm.get('productCode')?.hasError('required')">
                كود المنتج مطلوب
              </mat-error>
              <mat-error *ngIf="productForm.get('productCode')?.hasError('duplicate')">
                هذا الكود مستخدم بالفعل
              </mat-error>
            </mat-form-field>

            <!-- زر توليد الكود -->
            <button mat-raised-button 
                    type="button"
                    color="accent" 
                    (click)="generateProductCode()"
                    [disabled]="isGeneratingCode || productForm.get('productType')?.value === 'دولي'"
                    class="generate-code-btn">
              <mat-icon *ngIf="!isGeneratingCode">auto_awesome</mat-icon>
              <mat-icon *ngIf="isGeneratingCode" class="spinning">hourglass_empty</mat-icon>
              {{ isGeneratingCode ? 'جاري التوليد...' : 'توليد كود' }}
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- المعلومات الأساسية -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>info</mat-icon>
            المعلومات الأساسية
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="flex-2">
              <mat-label>اسم المنتج بالعربية</mat-label>
              <input matInput formControlName="nameAr" placeholder="اسم المنتج بالعربية" required>
              <mat-error>اسم المنتج بالعربية مطلوب</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>اسم المنتج بالإنجليزية</mat-label>
              <input matInput formControlName="nameEn" placeholder="اسم المنتج بالإنجليزية">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>وصف المنتج</mat-label>
              <textarea matInput 
                        formControlName="description" 
                        placeholder="وصف تفصيلي للمنتج"
                        rows="3"></textarea>
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- التصنيف والوحدة -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>category</mat-icon>
            التصنيف والوحدة
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>الفئة</mat-label>
              <mat-select formControlName="categoryId" required>
                <mat-option *ngFor="let category of categories" [value]="category.id">
                  {{ category.nameAr }}
                </mat-option>
              </mat-select>
              <mat-error>الفئة مطلوبة</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>وحدة القياس</mat-label>
              <mat-select formControlName="unitId" required>
                <mat-option *ngFor="let unit of units" [value]="unit.id">
                  {{ unit.nameAr }} ({{ unit.symbol }})
                </mat-option>
              </mat-select>
              <mat-error>وحدة القياس مطلوبة</mat-error>
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- الأسعار والمخزون -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>attach_money</mat-icon>
            الأسعار والمخزون
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>سعر الشراء</mat-label>
              <input matInput 
                     type="number" 
                     formControlName="purchasePrice" 
                     placeholder="0.00"
                     min="0"
                     step="0.01">
              <span matSuffix>جنيه</span>
              <mat-error>سعر الشراء مطلوب</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>سعر البيع</mat-label>
              <input matInput 
                     type="number" 
                     formControlName="salePrice" 
                     placeholder="0.00"
                     min="0"
                     step="0.01">
              <span matSuffix>جنيه</span>
              <mat-error>سعر البيع مطلوب</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>الحد الأدنى للمخزون</mat-label>
              <input matInput 
                     type="number" 
                     formControlName="minStock" 
                     placeholder="0"
                     min="0">
            </mat-form-field>

            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>الحد الأقصى للمخزون</mat-label>
              <input matInput 
                     type="number" 
                     formControlName="maxStock" 
                     placeholder="0"
                     min="0">
            </mat-form-field>

            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>المخزون الحالي</mat-label>
              <input matInput 
                     type="number" 
                     formControlName="currentStock" 
                     placeholder="0"
                     min="0">
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- الموردين -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>business</mat-icon>
            الموردين
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>المورد الرئيسي</mat-label>
              <mat-select formControlName="mainSupplierId" required>
                <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
                  {{ supplier.nameAr }} - {{ supplier.supplierCode }}
                </mat-option>
              </mat-select>
              <mat-error>المورد الرئيسي مطلوب</mat-error>
            </mat-form-field>
          </div>

          <!-- الموردين الإضافيين -->
          <div class="additional-suppliers">
            <h4>موردين إضافيين</h4>
            <div class="suppliers-chips">
              <mat-chip-set>
                <mat-chip *ngFor="let supplier of selectedSuppliers" 
                          (removed)="removeSupplier(supplier)"
                          [removable]="true">
                  {{ supplier.nameAr }}
                  <mat-icon matChipRemove>cancel</mat-icon>
                </mat-chip>
              </mat-chip-set>
            </div>
            
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>إضافة مورد</mat-label>
              <mat-select (selectionChange)="addSupplier($event.value)" [value]="">
                <mat-option *ngFor="let supplier of suppliers" [value]="supplier">
                  {{ supplier.nameAr }} - {{ supplier.supplierCode }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- الإعدادات -->
      <mat-card class="form-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>settings</mat-icon>
            إعدادات المنتج
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="settings-grid">
            <mat-checkbox formControlName="isActive">منتج نشط</mat-checkbox>
            <mat-checkbox formControlName="isWeighted">
              منتج موزون
            </mat-checkbox>
            <mat-checkbox formControlName="hasExpiry">له تاريخ انتهاء</mat-checkbox>
            <mat-checkbox formControlName="trackSerial">تتبع الأرقام التسلسلية</mat-checkbox>
          </div>
        </mat-card-content>
      </mat-card>

    </div>
  </form>

  <!-- Loading Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p>جاري حفظ المنتج...</p>
  </div>
</div>
