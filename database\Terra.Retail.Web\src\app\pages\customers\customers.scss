/* Terra Retail ERP - Customers Page */

.customers-page {
  padding: 2rem;
  direction: rtl;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  max-width: 100%;
  overflow-x: hidden;
  font-family: '<PERSON><PERSON><PERSON>', 'IBM Plex Sans Arabic', sans-serif;

  * {
    box-sizing: border-box;
  }

  /* تحسين المسافات والتباعد */
  .mat-mdc-form-field {
    margin-bottom: 0;

    .mat-mdc-form-field-wrapper {
      margin-bottom: 0;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }

  /* تحسين الأزرار */
  .mat-mdc-raised-button,
  .mat-mdc-outlined-button {
    font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 500;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

/* رأس الصفحة الاحترافي */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2.5rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0.5;
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h1 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.header-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.header-actions button {
  border-radius: 10px;
  font-weight: 500;
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.total-customers {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.active-customers {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.new-customers {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.vip-customers {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
}

.stat-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;

  mat-icon {
    font-size: 2rem !important;
    width: 2rem;
    height: 2rem;
  }
}

.stat-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  opacity: 0.9;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.stat-change {
  font-size: 0.9rem;
  opacity: 0.8;

  &.positive {
    color: #27ae60;
  }

  &.negative {
    color: #e74c3c;
  }

  &.neutral {
    opacity: 0.7;
  }
}

/* قسم البحث والفلاتر الجديد */
.search-filters-wrapper {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-section {
  width: 100%;
}

.search-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  background: white;

  .mat-mdc-card-content {
    padding: 1.5rem;
  }
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.search-input {
  width: 100%;
  max-width: 600px;

  ::ng-deep {
    .mat-mdc-form-field-wrapper {
      background-color: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #cbd5e1;
        background-color: #f1f5f9;
      }

      &.mdc-text-field--focused {
        border-color: #667eea;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding: 1rem 0.75rem;
    }
  }
}

.filters-section {
  width: 100%;
}

.filters-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  background: white;

  .mat-mdc-card-content {
    padding: 1.5rem;
  }
}

.filters-container {
  display: flex;
  gap: 1.5rem;
  align-items: flex-end;
  flex-wrap: wrap;
  justify-content: space-between;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.filter-select {
  width: 100%;

  ::ng-deep {
    .mat-mdc-form-field-wrapper {
      background-color: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #cbd5e1;
        background-color: #f1f5f9;
      }

      &.mdc-text-field--focused {
        border-color: #667eea;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-infix {
      padding: 1rem 0.75rem;
    }
  }
}

.clear-filters-btn {
  height: 56px;
  padding: 0 2rem;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.95rem;
  white-space: nowrap;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }

  mat-icon {
    margin-left: 0.5rem;
  }
}

/* Table Card */
.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  background: white;
}

.table-container {
  overflow-x: auto;
  background: white;
}

.customers-table {
  width: 100%;
  background: white;

  .mat-mdc-header-cell {
    background: #f8fafc;
    color: #374151;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
  }

  .mat-mdc-cell {
    border-bottom: 1px solid #f1f5f9;
    padding: 1rem 0.75rem;
    vertical-align: middle;
  }
}

.customer-row {
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8fafc;
  }
}

.customer-avatar {
  display: flex;
  align-items: center;
  justify-content: center;

  mat-icon {
    font-size: 2.5rem !important;
    color: #64748b;
  }
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.customer-code {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.2rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.phone {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.email {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.2rem;
}

.customer-type-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;

  &.regular {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.wholesale {
    background: #f3e5f5;
    color: #7b1fa2;
  }

  &.vip {
    background: #fff3e0;
    color: #f57c00;
  }

  &.merchant {
    background: #e8f5e8;
    color: #388e3c;
  }

  &.corporate {
    background: #fce4ec;
    color: #c2185b;
  }
}

.area-name {
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;

  &.active {
    background: #e8f5e8;
    color: #388e3c;
  }

  &.inactive {
    background: #ffebee;
    color: #d32f2f;
  }
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #64748b;

  p {
    margin-top: 1rem;
    font-size: 1rem;
  }
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filter-item {
    min-width: auto;
    width: 100%;
  }

  .clear-filters-btn {
    width: 100%;
    margin-top: 0.5rem;
  }
}

@media (max-width: 768px) {
  .customers-page {
    padding: 1rem;
  }

  .page-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 12px;
  }

  .header-info h1 {
    font-size: 1.5rem;
  }

  .stats-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-content {
    padding: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .search-filters-wrapper {
    gap: 1rem;
  }

  .search-card,
  .filters-card {
    border-radius: 12px;

    .mat-mdc-card-content {
      padding: 1rem;
    }
  }

  .search-input {
    max-width: none;
  }

  .filters-container {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-item {
    width: 100%;
    min-width: auto;
  }

  .clear-filters-btn {
    width: 100%;
    margin-top: 0;
  }

  .customers-table {
    font-size: 0.85rem;

    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 0.75rem 0.5rem;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.5s ease forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }