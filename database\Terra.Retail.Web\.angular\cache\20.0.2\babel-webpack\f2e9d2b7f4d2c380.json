{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, DOCUMENT, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"animationstart\", function MatMenu_ng_template_0_Template_div_animationstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event.animationName));\n    })(\"animationend\", function MatMenu_ng_template_0_Template_div_animationend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    })(\"animationcancel\", function MatMenu_ng_template_0_Template_div_animationcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-menu-panel-animations-disabled\", ctx_r1._animationsDisabled)(\"mat-menu-panel-exit-animation\", ctx_r1._panelAnimationState === \"void\")(\"mat-menu-panel-animating\", ctx_r1._isAnimating);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nconst MAT_MENU_PANEL = /*#__PURE__*/new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nlet MatMenuItem = /*#__PURE__*/(() => {\n  class MatMenuItem {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    _focusMonitor = inject(FocusMonitor);\n    _parentMenu = inject(MAT_MENU_PANEL, {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** ARIA role for the menu item. */\n    role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    _hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    _focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    _highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _triggersSubmenu = false;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      this._parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n      } else {\n        this._getHostElement().focus(options);\n      }\n      this._focused.next(this);\n    }\n    ngAfterViewInit() {\n      if (this._focusMonitor) {\n        // Start monitoring the element, so it gets the appropriate focused classes. We want\n        // to show the focus style for menu items only when the focus was not caused by a\n        // mouse or touch interaction.\n        this._focusMonitor.monitor(this._elementRef, false);\n      }\n    }\n    ngOnDestroy() {\n      if (this._focusMonitor) {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n      }\n      if (this._parentMenu && this._parentMenu.removeItem) {\n        this._parentMenu.removeItem(this);\n      }\n      this._hovered.complete();\n      this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n      return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n      return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n      this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n      const clone = this._elementRef.nativeElement.cloneNode(true);\n      const icons = clone.querySelectorAll('mat-icon, .material-icons');\n      // Strip away icons, so they don't show up in the text.\n      for (let i = 0; i < icons.length; i++) {\n        icons[i].remove();\n      }\n      return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n      // We need to mark this for check for the case where the content is coming from a\n      // `matMenuContent` whose change detection tree is at the declaration position,\n      // not the insertion position. See #23175.\n      this._highlighted = isHighlighted;\n      this._changeDetectorRef.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n      this._triggersSubmenu = triggersSubmenu;\n      this._changeDetectorRef.markForCheck();\n    }\n    _hasFocus() {\n      return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static ɵfac = function MatMenuItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuItem)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenuItem,\n      selectors: [[\"\", \"mat-menu-item\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-focus-indicator\"],\n      hostVars: 8,\n      hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n            return ctx._checkDisabled($event);\n          })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n            return ctx._handleMouseEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n        }\n      },\n      inputs: {\n        role: \"role\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n      },\n      exportAs: [\"matMenuItem\"],\n      attrs: _c0,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n      template: function MatMenuItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 1);\n          i0.ɵɵconditionalCreate(4, MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._triggersSubmenu ? 4 : -1);\n        }\n      },\n      dependencies: [MatRipple],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatMenuItem;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = /*#__PURE__*/new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nlet MatMenuContent = /*#__PURE__*/(() => {\n  class MatMenuContent {\n    _template = inject(TemplateRef);\n    _appRef = inject(ApplicationRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _document = inject(DOCUMENT);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _portal;\n    _outlet;\n    /** Emits when the menu content has been attached. */\n    _attached = new Subject();\n    constructor() {}\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n      if (!this._portal) {\n        this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n      }\n      this.detach();\n      if (!this._outlet) {\n        this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._appRef, this._injector);\n      }\n      const element = this._template.elementRef.nativeElement;\n      // Because we support opening the same menu from different triggers (which in turn have their\n      // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n      // risk it staying attached to a pane that's no longer in the DOM.\n      element.parentNode.insertBefore(this._outlet.outletElement, element);\n      // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n      // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n      // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n      // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n      // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n      this._changeDetectorRef.markForCheck();\n      this._portal.attach(this._outlet, context);\n      this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n      if (this._portal?.isAttached) {\n        this._portal.detach();\n      }\n    }\n    ngOnDestroy() {\n      this.detach();\n      this._outlet?.dispose();\n    }\n    static ɵfac = function MatMenuContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuContent,\n      selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }])]\n    });\n  }\n  return MatMenuContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nlet MatMenu = /*#__PURE__*/(() => {\n  class MatMenu {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _keyManager;\n    _xPosition;\n    _yPosition;\n    _firstItemFocusRef;\n    _exitFallbackTimeout;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _allItems;\n    /** Only the direct descendant menu items. */\n    _directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    _classList = {};\n    /** Current state of the panel animation. */\n    _panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    _animationDone = new Subject();\n    /** Whether the menu is animating. */\n    _isAnimating = false;\n    /** Parent menu of the current menu panel. */\n    parentMenu;\n    /** Layout direction of the menu. */\n    direction;\n    /** Class or list of classes to be added to the overlay panel. */\n    overlayPanelClass;\n    /** Class to be added to the backdrop element. */\n    backdropClass;\n    /** aria-label for the menu panel. */\n    ariaLabel;\n    /** aria-labelledby for the menu panel. */\n    ariaLabelledby;\n    /** aria-describedby for the menu panel. */\n    ariaDescribedby;\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n      return this._xPosition;\n    }\n    set xPosition(value) {\n      if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionX();\n      }\n      this._xPosition = value;\n      this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n      return this._yPosition;\n    }\n    set yPosition(value) {\n      if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionY();\n      }\n      this._yPosition = value;\n      this.setPositionClasses();\n    }\n    /** @docs-private */\n    templateRef;\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    items;\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    lazyContent;\n    /** Whether the menu should overlap its trigger. */\n    overlapTrigger;\n    /** Whether the menu has a backdrop. */\n    hasBackdrop;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n      const previousPanelClass = this._previousPanelClass;\n      const newClassList = {\n        ...this._classList\n      };\n      if (previousPanelClass && previousPanelClass.length) {\n        previousPanelClass.split(' ').forEach(className => {\n          newClassList[className] = false;\n        });\n      }\n      this._previousPanelClass = classes;\n      if (classes && classes.length) {\n        classes.split(' ').forEach(className => {\n          newClassList[className] = true;\n        });\n        this._elementRef.nativeElement.className = '';\n      }\n      this._classList = newClassList;\n    }\n    _previousPanelClass;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n      return this.panelClass;\n    }\n    set classList(classes) {\n      this.panelClass = classes;\n    }\n    /** Event emitted when the menu is closed. */\n    closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    close = this.closed;\n    panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n    constructor() {\n      const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n      this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n      this._xPosition = defaultOptions.xPosition;\n      this._yPosition = defaultOptions.yPosition;\n      this.backdropClass = defaultOptions.backdropClass;\n      this.overlapTrigger = defaultOptions.overlapTrigger;\n      this.hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n      this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n      this._updateDirectDescendants();\n      this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n      this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n      // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n      // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n      // is internal and we know that it gets completed on destroy.\n      this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n      this._directDescendantItems.changes.subscribe(itemsList => {\n        // Move focus to another item, if the active item is removed from the list.\n        // We need to debounce the callback, because multiple items might be removed\n        // in quick succession.\n        const manager = this._keyManager;\n        if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n          const items = itemsList.toArray();\n          const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n          if (items[index] && !items[index].disabled) {\n            manager.setActiveItem(index);\n          } else {\n            manager.setNextItemActive();\n          }\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._directDescendantItems.destroy();\n      this.closed.complete();\n      this._firstItemFocusRef?.destroy();\n      clearTimeout(this._exitFallbackTimeout);\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n      // Coerce the `changes` property because Angular types it as `Observable<any>`\n      const itemChanges = this._directDescendantItems.changes;\n      return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) {}\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) {}\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      switch (keyCode) {\n        case ESCAPE:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n            this.closed.emit('keydown');\n          }\n          break;\n        case LEFT_ARROW:\n          if (this.parentMenu && this.direction === 'ltr') {\n            this.closed.emit('keydown');\n          }\n          break;\n        case RIGHT_ARROW:\n          if (this.parentMenu && this.direction === 'rtl') {\n            this.closed.emit('keydown');\n          }\n          break;\n        default:\n          if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n            manager.setFocusOrigin('keyboard');\n          }\n          manager.onKeydown(event);\n          return;\n      }\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n      // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n      this._firstItemFocusRef?.destroy();\n      this._firstItemFocusRef = afterNextRender(() => {\n        const menuPanel = this._resolvePanel();\n        // If an item in the menuPanel is already focused, avoid overriding the focus.\n        if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n          const manager = this._keyManager;\n          manager.setFocusOrigin(origin).setFirstItemActive();\n          // If there's no active item at this point, it means that all the items are disabled.\n          // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n          // give _some_ feedback to screen readers.\n          if (!manager.activeItem && menuPanel) {\n            menuPanel.focus();\n          }\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n      this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * @deprecated No longer used and will be removed.\n     * @breaking-change 21.0.0\n     */\n    setElevation(_depth) {}\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n      this._classList = {\n        ...this._classList,\n        ['mat-menu-before']: posX === 'before',\n        ['mat-menu-after']: posX === 'after',\n        ['mat-menu-above']: posY === 'above',\n        ['mat-menu-below']: posY === 'below'\n      };\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(state) {\n      const isExit = state === EXIT_ANIMATION;\n      if (isExit || state === ENTER_ANIMATION) {\n        if (isExit) {\n          clearTimeout(this._exitFallbackTimeout);\n          this._exitFallbackTimeout = undefined;\n        }\n        this._animationDone.next(isExit ? 'void' : 'enter');\n        this._isAnimating = false;\n      }\n    }\n    _onAnimationStart(state) {\n      if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n        this._isAnimating = true;\n      }\n    }\n    _setIsOpen(isOpen) {\n      this._panelAnimationState = isOpen ? 'enter' : 'void';\n      if (isOpen) {\n        if (this._keyManager.activeItemIndex === 0) {\n          // Scroll the content element to the top as soon as the animation starts. This is necessary,\n          // because we move focus to the first item while it's still being animated, which can throw\n          // the browser off when it determines the scroll position. Alternatively we can move focus\n          // when the animation is done, however moving focus asynchronously will interrupt screen\n          // readers which are in the process of reading out the menu already. We take the `element`\n          // from the `event` since we can't use a `ViewChild` to access the pane.\n          const menuPanel = this._resolvePanel();\n          if (menuPanel) {\n            menuPanel.scrollTop = 0;\n          }\n        }\n      } else if (!this._animationsDisabled) {\n        // Some apps do `* { animation: none !important; }` in tests which will prevent the\n        // `animationend` event from firing. Since the exit animation is loading-bearing for\n        // removing the content from the DOM, add a fallback timer.\n        this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n      }\n      // Animation events won't fire when animations are disabled so we simulate them.\n      if (this._animationsDisabled) {\n        setTimeout(() => {\n          this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n      this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n        this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n        this._directDescendantItems.notifyOnChanges();\n      });\n    }\n    /** Gets the menu panel DOM node. */\n    _resolvePanel() {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      return menuPanel;\n    }\n    static ɵfac = function MatMenu_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenu)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenu,\n      selectors: [[\"mat-menu\"]],\n      contentQueries: function MatMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function MatMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function MatMenu_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n        }\n      },\n      inputs: {\n        backdropClass: \"backdropClass\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        xPosition: \"xPosition\",\n        yPosition: \"yPosition\",\n        overlapTrigger: [2, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n        hasBackdrop: [2, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n        panelClass: [0, \"class\", \"panelClass\"],\n        classList: \"classList\"\n      },\n      outputs: {\n        closed: \"closed\",\n        close: \"close\"\n      },\n      exportAs: [\"matMenu\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }])],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", 3, \"click\", \"animationstart\", \"animationend\", \"animationcancel\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n      template: function MatMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 12, \"ng-template\");\n        }\n      },\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatMenu;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = /*#__PURE__*/new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nlet MatMenuTrigger = /*#__PURE__*/(() => {\n  class MatMenuTrigger {\n    _element = inject(ElementRef);\n    _viewContainerRef = inject(ViewContainerRef);\n    _menuItemInstance = inject(MatMenuItem, {\n      optional: true,\n      self: true\n    });\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _focusMonitor = inject(FocusMonitor);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationsDisabled = _animationsDisabled();\n    _cleanupTouchstart;\n    _portal;\n    _overlayRef = null;\n    _menuOpen = false;\n    _closingActionsSubscription = Subscription.EMPTY;\n    _hoverSubscription = Subscription.EMPTY;\n    _menuCloseSubscription = Subscription.EMPTY;\n    _pendingRemoval;\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _parentMaterialMenu;\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _parentInnerPadding;\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _openedBy = undefined;\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n      return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n      this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n      return this._menu;\n    }\n    set menu(menu) {\n      if (menu === this._menu) {\n        return;\n      }\n      this._menu = menu;\n      this._menuCloseSubscription.unsubscribe();\n      if (menu) {\n        if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatMenuRecursiveError();\n        }\n        this._menuCloseSubscription = menu.close.subscribe(reason => {\n          this._destroyMenu(reason);\n          // If a click closed the menu, we should close the entire chain of nested menus.\n          if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n            this._parentMaterialMenu.closed.emit(reason);\n          }\n        });\n      }\n      this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    _menu;\n    /** Data to be passed along to any lazily-rendered content. */\n    menuData;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuClose = this.menuClosed;\n    constructor() {\n      const parentMenu = inject(MAT_MENU_PANEL, {\n        optional: true\n      });\n      const renderer = inject(Renderer2);\n      this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n      this._cleanupTouchstart = renderer.listen(this._element.nativeElement, 'touchstart', event => {\n        if (!isFakeTouchstartFromScreenReader(event)) {\n          this._openedBy = 'touch';\n        }\n      }, {\n        passive: true\n      });\n    }\n    ngAfterContentInit() {\n      this._handleHover();\n    }\n    ngOnDestroy() {\n      if (this.menu && this._ownsMenu(this.menu)) {\n        PANELS_TO_TRIGGERS.delete(this.menu);\n      }\n      this._cleanupTouchstart();\n      this._pendingRemoval?.unsubscribe();\n      this._menuCloseSubscription.unsubscribe();\n      this._closingActionsSubscription.unsubscribe();\n      this._hoverSubscription.unsubscribe();\n      if (this._overlayRef) {\n        this._overlayRef.dispose();\n        this._overlayRef = null;\n      }\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n      return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n      return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n      return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n      const menu = this.menu;\n      if (this._menuOpen || !menu) {\n        return;\n      }\n      this._pendingRemoval?.unsubscribe();\n      const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n      PANELS_TO_TRIGGERS.set(menu, this);\n      // If the same menu is currently attached to another trigger,\n      // we need to close it so it doesn't end up in a broken state.\n      if (previousTrigger && previousTrigger !== this) {\n        previousTrigger.closeMenu();\n      }\n      const overlayRef = this._createOverlay(menu);\n      const overlayConfig = overlayRef.getConfig();\n      const positionStrategy = overlayConfig.positionStrategy;\n      this._setPosition(menu, positionStrategy);\n      overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n      // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n      // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n      if (!overlayRef.hasAttached()) {\n        overlayRef.attach(this._getPortal(menu));\n        menu.lazyContent?.attach(this.menuData);\n      }\n      this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n      menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n      menu.direction = this.dir;\n      menu.focusFirstItem(this._openedBy || 'program');\n      this._setIsMenuOpen(true);\n      if (menu instanceof MatMenu) {\n        menu._setIsOpen(true);\n        menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n          // Re-adjust the position without locking when the amount of items\n          // changes so that the overlay is allowed to pick a new optimal position.\n          positionStrategy.withLockedPosition(false).reapplyLastPosition();\n          positionStrategy.withLockedPosition(true);\n        });\n      }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n      this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n      this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n      const overlayRef = this._overlayRef;\n      const menu = this._menu;\n      if (!overlayRef || !this.menuOpen) {\n        return;\n      }\n      this._closingActionsSubscription.unsubscribe();\n      this._pendingRemoval?.unsubscribe();\n      // Note that we don't wait for the animation to finish if another trigger took\n      // over the menu, because the panel will end up empty which looks glitchy.\n      if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n        this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n          overlayRef.detach();\n          menu.lazyContent?.detach();\n        });\n        menu._setIsOpen(false);\n      } else {\n        overlayRef.detach();\n        menu?.lazyContent?.detach();\n      }\n      if (menu && this._ownsMenu(menu)) {\n        PANELS_TO_TRIGGERS.delete(menu);\n      }\n      // Always restore focus if the user is navigating using the keyboard or the menu was opened\n      // programmatically. We don't restore for non-root triggers, because it can prevent focus\n      // from making it back to the root trigger when closing a long chain of menus by clicking\n      // on the backdrop.\n      if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n        this.focus(this._openedBy);\n      }\n      this._openedBy = undefined;\n      this._setIsMenuOpen(false);\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n      if (isOpen !== this._menuOpen) {\n        this._menuOpen = isOpen;\n        this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n        if (this.triggersSubmenu()) {\n          this._menuItemInstance._setHighlighted(isOpen);\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n      if (!this._overlayRef) {\n        const config = this._getOverlayConfig(menu);\n        this._subscribeToPositions(menu, config.positionStrategy);\n        this._overlayRef = createOverlayRef(this._injector, config);\n        this._overlayRef.keydownEvents().subscribe(event => {\n          if (this.menu instanceof MatMenu) {\n            this.menu._handleKeydown(event);\n          }\n        });\n      }\n      return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n      return new OverlayConfig({\n        positionStrategy: createFlexibleConnectedPositionStrategy(this._injector, this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n        backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n        panelClass: menu.overlayPanelClass,\n        scrollStrategy: this._scrollStrategy(),\n        direction: this._dir || 'ltr',\n        disableAnimations: this._animationsDisabled\n      });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n      if (menu.setPositionClasses) {\n        position.positionChanges.subscribe(change => {\n          this._ngZone.run(() => {\n            const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n            const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n            menu.setPositionClasses(posX, posY);\n          });\n        });\n      }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n      let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n      let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n      let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n      let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n      let offsetY = 0;\n      if (this.triggersSubmenu()) {\n        // When the menu is a sub-menu, it should always align itself\n        // to the edges of the trigger, instead of overlapping it.\n        overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n        originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n        if (this._parentMaterialMenu) {\n          if (this._parentInnerPadding == null) {\n            const firstItem = this._parentMaterialMenu.items.first;\n            this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n          }\n          offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n        }\n      } else if (!menu.overlapTrigger) {\n        originY = overlayY === 'top' ? 'bottom' : 'top';\n        originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n      }\n      positionStrategy.withPositions([{\n        originX,\n        originY,\n        overlayX,\n        overlayY,\n        offsetY\n      }, {\n        originX: originFallbackX,\n        originY,\n        overlayX: overlayFallbackX,\n        overlayY,\n        offsetY\n      }, {\n        originX,\n        originY: originFallbackY,\n        overlayX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }, {\n        originX: originFallbackX,\n        originY: originFallbackY,\n        overlayX: overlayFallbackX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n      const backdrop = this._overlayRef.backdropClick();\n      const detachments = this._overlayRef.detachments();\n      const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n      const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => this._menuOpen && active !== this._menuItemInstance)) : of();\n      return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n      if (!isFakeMousedownFromScreenReader(event)) {\n        // Since right or middle button clicks won't trigger the `click` event,\n        // we shouldn't consider the menu as opened by mouse in those cases.\n        this._openedBy = event.button === 0 ? 'mouse' : undefined;\n        // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n        // we should prevent focus from moving onto it via click to avoid the\n        // highlight from lingering on the menu item.\n        if (this.triggersSubmenu()) {\n          event.preventDefault();\n        }\n      }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      // Pressing enter on the trigger will trigger the click handler later.\n      if (keyCode === ENTER || keyCode === SPACE) {\n        this._openedBy = 'keyboard';\n      }\n      if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n        this._openedBy = 'keyboard';\n        this.openMenu();\n      }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n      if (this.triggersSubmenu()) {\n        // Stop event propagation to avoid closing the parent menu.\n        event.stopPropagation();\n        this.openMenu();\n      } else {\n        this.toggleMenu();\n      }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n      // Subscribe to changes in the hovered item in order to toggle the panel.\n      if (this.triggersSubmenu() && this._parentMaterialMenu) {\n        this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n          if (active === this._menuItemInstance && !active.disabled) {\n            this._openedBy = 'mouse';\n            this.openMenu();\n          }\n        });\n      }\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n      // Note that we can avoid this check by keeping the portal on the menu panel.\n      // While it would be cleaner, we'd have to introduce another required method on\n      // `MatMenuPanel`, making it harder to consume.\n      if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n        this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n      }\n      return this._portal;\n    }\n    /**\n     * Determines whether the trigger owns a specific menu panel, at the current point in time.\n     * This allows us to distinguish the case where the same panel is passed into multiple triggers\n     * and multiple are open at a time.\n     */\n    _ownsMenu(menu) {\n      return PANELS_TO_TRIGGERS.get(menu) === this;\n    }\n    static ɵfac = function MatMenuTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuTrigger)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuTrigger,\n      selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n      hostVars: 3,\n      hostBindings: function MatMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuTrigger_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"mousedown\", function MatMenuTrigger_mousedown_HostBindingHandler($event) {\n            return ctx._handleMousedown($event);\n          })(\"keydown\", function MatMenuTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu == null ? null : ctx.menu.panelId : null);\n        }\n      },\n      inputs: {\n        _deprecatedMatMenuTriggerFor: [0, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n        menu: [0, \"matMenuTriggerFor\", \"menu\"],\n        menuData: [0, \"matMenuTriggerData\", \"menuData\"],\n        restoreFocus: [0, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n      },\n      outputs: {\n        menuOpened: \"menuOpened\",\n        onMenuOpen: \"onMenuOpen\",\n        menuClosed: \"menuClosed\",\n        onMenuClose: \"onMenuClose\"\n      },\n      exportAs: [\"matMenuTrigger\"]\n    });\n  }\n  return MatMenuTrigger;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatMenuModule = /*#__PURE__*/(() => {\n  class MatMenuModule {\n    static ɵfac = function MatMenuModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatMenuModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n  return MatMenuModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n  // Represents:\n  // trigger('transformMenu', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => enter',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  // ])\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: {\n    type: 7,\n    name: 'transformMenu',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms 25ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('fadeInItems', [\n  //   // TODO(crisbeto): this is inside the `transformMenu`\n  //   // now. Remove next time we do breaking changes.\n  //   state('showing', style({opacity: 1})),\n  //   transition('void => *', [\n  //     style({opacity: 0}),\n  //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: {\n    type: 7,\n    name: 'fadeInItems',\n    definitions: [{\n      type: 0,\n      name: 'showing',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => *',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "DOCUMENT", "ChangeDetectorRef", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "TemplateRef", "ApplicationRef", "Injector", "ViewContainerRef", "Directive", "QueryList", "EventEmitter", "afterNextRender", "ContentChildren", "ViewChild", "ContentChild", "Output", "NgZone", "Renderer2", "NgModule", "FocusMonitor", "_IdGenerator", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "startWith", "switchMap", "takeUntil", "take", "filter", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "TemplatePortal", "DomPortalOutlet", "_animationsDisabled", "Directionality", "createRepositionScrollStrategy", "createOverlayRef", "OverlayConfig", "createFlexibleConnectedPositionStrategy", "OverlayModule", "CdkScrollableModule", "MatRippleModule", "MatCommonModule", "_c0", "_c1", "_c2", "MatMenuItem_Conditional_4_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "_c3", "MatMenu_ng_template_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "MatMenu_ng_template_0_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "closed", "emit", "MatMenu_ng_template_0_Template_div_animationstart_0_listener", "$event", "_onAnimationStart", "animationName", "MatMenu_ng_template_0_Template_div_animationend_0_listener", "_onAnimationDone", "MatMenu_ng_template_0_Template_div_animationcancel_0_listener", "ɵɵprojection", "ɵɵclassMap", "_classList", "ɵɵclassProp", "_panelAnimationState", "_isAnimating", "ɵɵproperty", "panelId", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "MAT_MENU_PANEL", "MatMenuItem", "_elementRef", "_document", "_focusMonitor", "_parentMenu", "optional", "_changeDetectorRef", "role", "disabled", "disable<PERSON><PERSON><PERSON>", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "constructor", "load", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "next", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_hasFocus", "activeElement", "ɵfac", "MatMenuItem_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatMenuItem_HostBindings", "MatMenuItem_click_HostBindingHandler", "MatMenuItem_mouseenter_HostBindingHandler", "inputs", "exportAs", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatMenuItem_Template", "ɵɵprojectionDef", "ɵɵconditionalCreate", "ɵɵadvance", "ɵɵconditional", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_template", "_appRef", "_injector", "_viewContainerRef", "_portal", "_outlet", "_attached", "attach", "context", "detach", "createElement", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "isAttached", "dispose", "MatMenuContent_Factory", "ɵdir", "ɵɵdefineDirective", "features", "ɵɵProvidersFeature", "provide", "useExisting", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatMenu", "_keyManager", "_xPosition", "_yPosition", "_firstItemFocusRef", "_exitFallbackTimeout", "_allItems", "_directDescendantItems", "_animationDone", "parentMenu", "direction", "overlayPanelClass", "value", "setPositionClasses", "templateRef", "items", "lazyContent", "hasBackdrop", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "newClassList", "split", "for<PERSON>ach", "className", "classList", "close", "getId", "defaultOptions", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "changes", "pipe", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "clearTimeout", "itemChanges", "_item", "_handleKeydown", "keyCode", "setFocusOrigin", "onKeydown", "focusFirstItem", "menuPanel", "_resolvePanel", "contains", "document", "setFirstItemActive", "injector", "resetActiveItem", "setElevation", "_depth", "posX", "posY", "state", "isExit", "undefined", "_setIsOpen", "isOpen", "scrollTop", "setTimeout", "reset", "notifyOn<PERSON><PERSON>es", "first", "closest", "MatMenu_Factory", "contentQueries", "MatMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "MatMenu_Query", "ɵɵviewQuery", "MatMenu_HostBindings", "outputs", "MatMenu_Template", "ɵɵtemplate", "styles", "MAT_MENU_SCROLL_STRATEGY", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "_overlay", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "MENU_PANEL_TOP_PADDING", "PANELS_TO_TRIGGERS", "WeakMap", "MatMenuTrigger", "_element", "_menuItemInstance", "self", "_dir", "_ngZone", "_scrollStrategy", "_cleanupTouchstart", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "EMPTY", "_hoverSubscription", "_menuCloseSubscription", "_pending<PERSON><PERSON><PERSON><PERSON>", "_parentMaterialMenu", "_parentInnerPadding", "_openedBy", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "unsubscribe", "reason", "_destroyMenu", "menuData", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "renderer", "listen", "passive", "_handleHover", "_ownsMenu", "delete", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "previousTrigger", "get", "set", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "has<PERSON>tta<PERSON>", "_getPortal", "_menuClosingActions", "_setIsMenuOpen", "withLockedPosition", "reapplyLastPosition", "updatePosition", "config", "_getOverlayConfig", "_subscribeToPositions", "keydownEvents", "withGrowAfterOpen", "withTransformOriginOn", "scrollStrategy", "disableAnimations", "position", "position<PERSON><PERSON>es", "change", "run", "connectionPair", "overlayX", "overlayY", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "MatMenuTrigger_Factory", "MatMenuTrigger_HostBindings", "MatMenuTrigger_click_HostBindingHandler", "MatMenuTrigger_mousedown_HostBindingHandler", "MatMenuTrigger_keydown_HostBindingHandler", "MatMenuModule", "MatMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "matMenuAnimations", "transformMenu", "name", "definitions", "opacity", "transform", "offset", "expr", "animation", "timings", "fadeInItems"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, DOCUMENT, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    _focusMonitor = inject(FocusMonitor);\n    _parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** ARIA role for the menu item. */\n    role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    _hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    _focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    _highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _triggersSubmenu = false;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        this._parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element, so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons, so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuItem, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatMenuItem, isStandalone: true, selector: \"[mat-menu-item]\", inputs: { role: \"role\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-focus-indicator\" }, exportAs: [\"matMenuItem\"], ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\", dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-focus-indicator',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [MatRipple], template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n    _template = inject(TemplateRef);\n    _appRef = inject(ApplicationRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _document = inject(DOCUMENT);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _portal;\n    _outlet;\n    /** Emits when the menu content has been attached. */\n    _attached = new Subject();\n    constructor() { }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        this._changeDetectorRef.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal?.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._outlet?.dispose();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatMenuContent, isStandalone: true, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }], ctorParameters: () => [] });\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nclass MatMenu {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _keyManager;\n    _xPosition;\n    _yPosition;\n    _firstItemFocusRef;\n    _exitFallbackTimeout;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _allItems;\n    /** Only the direct descendant menu items. */\n    _directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    _classList = {};\n    /** Current state of the panel animation. */\n    _panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    _animationDone = new Subject();\n    /** Whether the menu is animating. */\n    _isAnimating = false;\n    /** Parent menu of the current menu panel. */\n    parentMenu;\n    /** Layout direction of the menu. */\n    direction;\n    /** Class or list of classes to be added to the overlay panel. */\n    overlayPanelClass;\n    /** Class to be added to the backdrop element. */\n    backdropClass;\n    /** aria-label for the menu panel. */\n    ariaLabel;\n    /** aria-labelledby for the menu panel. */\n    ariaLabelledby;\n    /** aria-describedby for the menu panel. */\n    ariaDescribedby;\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** @docs-private */\n    templateRef;\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    items;\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    lazyContent;\n    /** Whether the menu should overlap its trigger. */\n    overlapTrigger;\n    /** Whether the menu has a backdrop. */\n    hasBackdrop;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        const newClassList = { ...this._classList };\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                newClassList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                newClassList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n        this._classList = newClassList;\n    }\n    _previousPanelClass;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    /** Event emitted when the menu is closed. */\n    closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    close = this.closed;\n    panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n    constructor() {\n        const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n        this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n        this._xPosition = defaultOptions.xPosition;\n        this._yPosition = defaultOptions.yPosition;\n        this.backdropClass = defaultOptions.backdropClass;\n        this.overlapTrigger = defaultOptions.overlapTrigger;\n        this.hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusRef?.destroy();\n        clearTimeout(this._exitFallbackTimeout);\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusRef?.destroy();\n        this._firstItemFocusRef = afterNextRender(() => {\n            const menuPanel = this._resolvePanel();\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        }, { injector: this._injector });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * @deprecated No longer used and will be removed.\n     * @breaking-change 21.0.0\n     */\n    setElevation(_depth) { }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        this._classList = {\n            ...this._classList,\n            ['mat-menu-before']: posX === 'before',\n            ['mat-menu-after']: posX === 'after',\n            ['mat-menu-above']: posY === 'above',\n            ['mat-menu-below']: posY === 'below',\n        };\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(state) {\n        const isExit = state === EXIT_ANIMATION;\n        if (isExit || state === ENTER_ANIMATION) {\n            if (isExit) {\n                clearTimeout(this._exitFallbackTimeout);\n                this._exitFallbackTimeout = undefined;\n            }\n            this._animationDone.next(isExit ? 'void' : 'enter');\n            this._isAnimating = false;\n        }\n    }\n    _onAnimationStart(state) {\n        if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n            this._isAnimating = true;\n        }\n    }\n    _setIsOpen(isOpen) {\n        this._panelAnimationState = isOpen ? 'enter' : 'void';\n        if (isOpen) {\n            if (this._keyManager.activeItemIndex === 0) {\n                // Scroll the content element to the top as soon as the animation starts. This is necessary,\n                // because we move focus to the first item while it's still being animated, which can throw\n                // the browser off when it determines the scroll position. Alternatively we can move focus\n                // when the animation is done, however moving focus asynchronously will interrupt screen\n                // readers which are in the process of reading out the menu already. We take the `element`\n                // from the `event` since we can't use a `ViewChild` to access the pane.\n                const menuPanel = this._resolvePanel();\n                if (menuPanel) {\n                    menuPanel.scrollTop = 0;\n                }\n            }\n        }\n        else if (!this._animationsDisabled) {\n            // Some apps do `* { animation: none !important; }` in tests which will prevent the\n            // `animationend` event from firing. Since the exit animation is loading-bearing for\n            // removing the content from the DOM, add a fallback timer.\n            this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n        }\n        // Animation events won't fire when animations are disabled so we simulate them.\n        if (this._animationsDisabled) {\n            setTimeout(() => {\n                this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n            });\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n    /** Gets the menu panel DOM node. */\n    _resolvePanel() {\n        let menuPanel = null;\n        if (this._directDescendantItems.length) {\n            // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n            // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n            // because the panel is inside an `ng-template`. We work around it by starting from one of\n            // the items and walking up the DOM.\n            menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n        }\n        return menuPanel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenu, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatMenu, isStandalone: true, selector: \"mat-menu\", inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: [\"overlapTrigger\", \"overlapTrigger\", booleanAttribute], hasBackdrop: [\"hasBackdrop\", \"hasBackdrop\", (value) => (value == null ? null : booleanAttribute(value))], panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], exportAs: [\"matMenu\"], ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? null : booleanAttribute(value)) }]\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createRepositionScrollStrategy(injector);\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(_overlay) {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n    _element = inject(ElementRef);\n    _viewContainerRef = inject(ViewContainerRef);\n    _menuItemInstance = inject(MatMenuItem, { optional: true, self: true });\n    _dir = inject(Directionality, { optional: true });\n    _focusMonitor = inject(FocusMonitor);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationsDisabled = _animationsDisabled();\n    _cleanupTouchstart;\n    _portal;\n    _overlayRef = null;\n    _menuOpen = false;\n    _closingActionsSubscription = Subscription.EMPTY;\n    _hoverSubscription = Subscription.EMPTY;\n    _menuCloseSubscription = Subscription.EMPTY;\n    _pendingRemoval;\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _parentMaterialMenu;\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _parentInnerPadding;\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _openedBy = undefined;\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    _menu;\n    /** Data to be passed along to any lazily-rendered content. */\n    menuData;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuClose = this.menuClosed;\n    constructor() {\n        const parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n        const renderer = inject(Renderer2);\n        this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n        this._cleanupTouchstart = renderer.listen(this._element.nativeElement, 'touchstart', (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        }, { passive: true });\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this.menu && this._ownsMenu(this.menu)) {\n            PANELS_TO_TRIGGERS.delete(this.menu);\n        }\n        this._cleanupTouchstart();\n        this._pendingRemoval?.unsubscribe();\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        this._pendingRemoval?.unsubscribe();\n        const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n        PANELS_TO_TRIGGERS.set(menu, this);\n        // If the same menu is currently attached to another trigger,\n        // we need to close it so it doesn't end up in a broken state.\n        if (previousTrigger && previousTrigger !== this) {\n            previousTrigger.closeMenu();\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n        // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n        if (!overlayRef.hasAttached()) {\n            overlayRef.attach(this._getPortal(menu));\n            menu.lazyContent?.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n        if (menu instanceof MatMenu) {\n            menu._setIsOpen(true);\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        const overlayRef = this._overlayRef;\n        const menu = this._menu;\n        if (!overlayRef || !this.menuOpen) {\n            return;\n        }\n        this._closingActionsSubscription.unsubscribe();\n        this._pendingRemoval?.unsubscribe();\n        // Note that we don't wait for the animation to finish if another trigger took\n        // over the menu, because the panel will end up empty which looks glitchy.\n        if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n            this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n                overlayRef.detach();\n                menu.lazyContent?.detach();\n            });\n            menu._setIsOpen(false);\n        }\n        else {\n            overlayRef.detach();\n            menu?.lazyContent?.detach();\n        }\n        if (menu && this._ownsMenu(menu)) {\n            PANELS_TO_TRIGGERS.delete(menu);\n        }\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        this._setIsMenuOpen(false);\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = createOverlayRef(this._injector, config);\n            this._overlayRef.keydownEvents().subscribe(event => {\n                if (this.menu instanceof MatMenu) {\n                    this.menu._handleKeydown(event);\n                }\n            });\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: createFlexibleConnectedPositionStrategy(this._injector, this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir || 'ltr',\n            disableAnimations: this._animationsDisabled,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                this._ngZone.run(() => {\n                    const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                    const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                    menu.setPositionClasses(posX, posY);\n                });\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu\n                ._hovered()\n                .pipe(filter(active => this._menuOpen && active !== this._menuItemInstance))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (this.triggersSubmenu() && this._parentMaterialMenu) {\n            this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n                if (active === this._menuItemInstance && !active.disabled) {\n                    this._openedBy = 'mouse';\n                    this.openMenu();\n                }\n            });\n        }\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n    /**\n     * Determines whether the trigger owns a specific menu panel, at the current point in time.\n     * This allows us to distinguish the case where the same panel is passed into multiple triggers\n     * and multiple are open at a time.\n     */\n    _ownsMenu(menu) {\n        return PANELS_TO_TRIGGERS.get(menu) === this;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatMenuTrigger, isStandalone: true, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu?.panelId : null\" }, classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu?.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }], ctorParameters: () => [], propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n\nclass MatMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule,\n            MatMenu,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger], exports: [CdkScrollableModule,\n            MatMenu,\n            MatCommonModule,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule, CdkScrollableModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatRippleModule,\n                        MatCommonModule,\n                        OverlayModule,\n                        MatMenu,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n    // Represents:\n    // trigger('transformMenu', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => enter',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n    // ])\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: {\n        type: 7,\n        name: 'transformMenu',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: { type: 6, styles: { opacity: 0, transform: 'scale(0.8)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => enter',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 1, transform: 'scale(1)' }, offset: null },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms 25ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('fadeInItems', [\n    //   // TODO(crisbeto): this is inside the `transformMenu`\n    //   // now. Remove next time we do breaking changes.\n    //   state('showing', style({opacity: 1})),\n    //   transition('void => *', [\n    //     style({opacity: 0}),\n    //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    //   ]),\n    // ])\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: {\n        type: 7,\n        name: 'fadeInItems',\n        definitions: [\n            {\n                type: 0,\n                name: 'showing',\n                styles: { type: 6, styles: { opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => *',\n                animation: [\n                    { type: 6, styles: { opacity: 0 }, offset: null },\n                    { type: 4, styles: null, timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)' },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC5W,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AAClJ,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvD,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC9E,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASL,CAAC,IAAIM,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,8BAA8B,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,uCAAuC,EAAEC,aAAa,QAAQ,sBAAsB;AAC9J,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASX,CAAC,IAAIY,eAAe,QAAQ,sBAAsB;AAC3D,SAASZ,CAAC,IAAIa,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwG6FrE,EAAE,CAAAuE,cAAA;IAAFvE,EAAE,CAAAwE,cAAA,YACknC,CAAC;IADrnCxE,EAAE,CAAAyE,SAAA,gBACopC,CAAC;IADvpCzE,EAAE,CAAA0E,YAAA,CAC0pC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAQ,GAAA,GAD7pC7E,EAAE,CAAA8E,gBAAA;IAAF9E,EAAE,CAAAwE,cAAA,YA4e21D,CAAC;IA5e91DxE,EAAE,CAAA+E,UAAA,mBAAAC,oDAAA;MAAFhF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4e67CF,MAAA,CAAAG,MAAA,CAAAC,IAAA,CAAY,OAAO,CAAC;IAAA,CAAC,CAAC,4BAAAC,6DAAAC,MAAA;MA5er9CxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4eohDF,MAAA,CAAAO,iBAAA,CAAAD,MAAA,CAAAE,aAAsC,CAAC;IAAA,CAAC,CAAC,0BAAAC,2DAAAH,MAAA;MA5e/jDxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4eolDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC,6BAAAG,8DAAAL,MAAA;MA5e9nDxF,EAAE,CAAAiF,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFlF,EAAE,CAAAmF,aAAA;MAAA,OAAFnF,EAAE,CAAAoF,WAAA,CA4espDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC;IA5ehsD1F,EAAE,CAAAwE,cAAA,YA4eq4D,CAAC;IA5ex4DxE,EAAE,CAAA8F,YAAA,EA4es6D,CAAC;IA5ez6D9F,EAAE,CAAA0E,YAAA,CA4ek7D,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAa,MAAA,GA5e/7DlF,EAAE,CAAAmF,aAAA;IAAFnF,EAAE,CAAA+F,UAAA,CAAAb,MAAA,CAAAc,UA4e8tC,CAAC;IA5ejuChG,EAAE,CAAAiG,WAAA,uCAAAf,MAAA,CAAA3B,mBA4esyC,CAAC,kCAAA2B,MAAA,CAAAgB,oBAAA,WAA8E,CAAC,6BAAAhB,MAAA,CAAAiB,YAAsD,CAAC;IA5e/6CnG,EAAE,CAAAoG,UAAA,OAAAlB,MAAA,CAAAmB,OA4eksC,CAAC;IA5ersCrG,EAAE,CAAAsG,WAAA,eAAApB,MAAA,CAAAqB,SAAA,6BAAArB,MAAA,CAAAsB,cAAA,8BAAAtB,MAAA,CAAAuB,eAAA;EAAA;AAAA;AApG/F,MAAMC,cAAc,gBAAG,IAAIzG,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA;AAFA,IAGM0G,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdC,WAAW,GAAG1G,MAAM,CAACC,UAAU,CAAC;IAChC0G,SAAS,GAAG3G,MAAM,CAACE,QAAQ,CAAC;IAC5B0G,aAAa,GAAG5G,MAAM,CAACwB,YAAY,CAAC;IACpCqF,WAAW,GAAG7G,MAAM,CAACwG,cAAc,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxDC,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;IAC9C;IACA6G,IAAI,GAAG,UAAU;IACjB;IACAC,QAAQ,GAAG,KAAK;IAChB;IACAC,aAAa,GAAG,KAAK;IACrB;IACAC,QAAQ,GAAG,IAAI9E,OAAO,CAAC,CAAC;IACxB;IACA+E,QAAQ,GAAG,IAAI/E,OAAO,CAAC,CAAC;IACxB;IACAgF,YAAY,GAAG,KAAK;IACpB;IACAC,gBAAgB,GAAG,KAAK;IACxBC,WAAWA,CAAA,EAAG;MACVvH,MAAM,CAAC8C,sBAAsB,CAAC,CAAC0E,IAAI,CAACxE,uBAAuB,CAAC;MAC5D,IAAI,CAAC6D,WAAW,EAAEY,OAAO,GAAG,IAAI,CAAC;IACrC;IACA;IACAC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;MACnB,IAAI,IAAI,CAAChB,aAAa,IAAIe,MAAM,EAAE;QAC9B,IAAI,CAACf,aAAa,CAACiB,QAAQ,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAEH,MAAM,EAAEC,OAAO,CAAC;MACxE,CAAC,MACI;QACD,IAAI,CAACE,eAAe,CAAC,CAAC,CAACJ,KAAK,CAACE,OAAO,CAAC;MACzC;MACA,IAAI,CAACR,QAAQ,CAACW,IAAI,CAAC,IAAI,CAAC;IAC5B;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACpB,aAAa,EAAE;QACpB;QACA;QACA;QACA,IAAI,CAACA,aAAa,CAACqB,OAAO,CAAC,IAAI,CAACvB,WAAW,EAAE,KAAK,CAAC;MACvD;IACJ;IACAwB,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACtB,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACuB,cAAc,CAAC,IAAI,CAACzB,WAAW,CAAC;MACvD;MACA,IAAI,IAAI,CAACG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,UAAU,EAAE;QACjD,IAAI,CAACvB,WAAW,CAACuB,UAAU,CAAC,IAAI,CAAC;MACrC;MACA,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACjB,QAAQ,CAACiB,QAAQ,CAAC,CAAC;IAC5B;IACA;IACAC,YAAYA,CAAA,EAAG;MACX,OAAO,IAAI,CAACrB,QAAQ,GAAG,IAAI,GAAG,GAAG;IACrC;IACA;IACAa,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACpB,WAAW,CAAC6B,aAAa;IACzC;IACA;IACAC,cAAcA,CAACC,KAAK,EAAE;MAClB,IAAI,IAAI,CAACxB,QAAQ,EAAE;QACfwB,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;MAC3B;IACJ;IACA;IACAC,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACzB,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC;IAC5B;IACA;IACAc,QAAQA,CAAA,EAAG;MACP,MAAMC,KAAK,GAAG,IAAI,CAACpC,WAAW,CAAC6B,aAAa,CAACQ,SAAS,CAAC,IAAI,CAAC;MAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;MACjE;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;MACrB;MACA,OAAON,KAAK,CAACO,WAAW,EAAEC,IAAI,CAAC,CAAC,IAAI,EAAE;IAC1C;IACAC,eAAeA,CAACC,aAAa,EAAE;MAC3B;MACA;MACA;MACA,IAAI,CAACnC,YAAY,GAAGmC,aAAa;MACjC,IAAI,CAACzC,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IAC1C;IACAC,mBAAmBA,CAACC,eAAe,EAAE;MACjC,IAAI,CAACrC,gBAAgB,GAAGqC,eAAe;MACvC,IAAI,CAAC5C,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IAC1C;IACAG,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACjD,SAAS,IAAI,IAAI,CAACA,SAAS,CAACkD,aAAa,KAAK,IAAI,CAAC/B,eAAe,CAAC,CAAC;IACpF;IACA,OAAOgC,IAAI,YAAAC,oBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFvD,WAAW;IAAA;IAC9G,OAAOwD,IAAI,kBAD8EnK,EAAE,CAAAoK,iBAAA;MAAAC,IAAA,EACJ1D,WAAW;MAAA2D,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAArG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTrE,EAAE,CAAA+E,UAAA,mBAAA4F,qCAAAnF,MAAA;YAAA,OACJlB,GAAA,CAAAoE,cAAA,CAAAlD,MAAqB,CAAC;UAAA,CAAZ,CAAC,wBAAAoF,0CAAA;YAAA,OAAXtG,GAAA,CAAAwE,iBAAA,CAAkB,CAAC;UAAA,CAAT,CAAC;QAAA;QAAA,IAAAzE,EAAA;UADTrE,EAAE,CAAAsG,WAAA,SAAAhC,GAAA,CAAA4C,IAAA,cACJ5C,GAAA,CAAAkE,YAAA,CAAa,CAAC,mBAAAlE,GAAA,CAAA6C,QAAA,cAAA7C,GAAA,CAAA6C,QAAA,IAAF,IAAI;UADdnH,EAAE,CAAAiG,WAAA,kCAAA3B,GAAA,CAAAiD,YACM,CAAC,sCAAAjD,GAAA,CAAAkD,gBAAD,CAAC;QAAA;MAAA;MAAAqD,MAAA;QAAA3D,IAAA;QAAAC,QAAA,8BAA8G7G,gBAAgB;QAAA8G,aAAA,wCAAqD9G,gBAAgB;MAAA;MAAAwK,QAAA;MAAAC,KAAA,EAAA9G,GAAA;MAAA+G,kBAAA,EAAA7G,GAAA;MAAA8G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAhH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD5MrE,EAAE,CAAAsL,eAAA,CAAApH,GAAA;UAAFlE,EAAE,CAAA8F,YAAA,EACiuB,CAAC;UADpuB9F,EAAE,CAAAwE,cAAA,aAC0wB,CAAC;UAD7wBxE,EAAE,CAAA8F,YAAA,KACmyB,CAAC;UADtyB9F,EAAE,CAAA0E,YAAA,CAC0yB,CAAC;UAD7yB1E,EAAE,CAAAyE,SAAA,YACw8B,CAAC;UAD38BzE,EAAE,CAAAuL,mBAAA,IAAAnH,kCAAA,qBACo+B,CAAC;QAAA;QAAA,IAAAC,EAAA;UADv+BrE,EAAE,CAAAwL,SAAA,EACg5B,CAAC;UADn5BxL,EAAE,CAAAoG,UAAA,sBAAA9B,GAAA,CAAA8C,aAAA,IAAA9C,GAAA,CAAA6C,QACg5B,CAAC,qBAAA7C,GAAA,CAAA0D,eAAA,EAA8C,CAAC;UADl8BhI,EAAE,CAAAwL,SAAA,CAC6pC,CAAC;UADhqCxL,EAAE,CAAAyL,aAAA,CAAAnH,GAAA,CAAAkD,gBAAA,SAC6pC,CAAC;QAAA;MAAA;MAAAkE,YAAA,GAA+CtI,SAAS;MAAAuI,aAAA;MAAAC,eAAA;IAAA;EACrzC;EAAC,OAjGKjF,WAAW;AAAA;AAkGjB;EAAA,QAAAkF,SAAA,oBAAAA,SAAA;AAAA;;AAuBA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,KAAK,CAAC;AAChB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMD,KAAK,CAAC;AAChB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,MAAMF,KAAK,CAAC,gFAAgF,GACxF,sEAAsE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,gBAAG,IAAIjM,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AAAA,IACMkM,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBC,SAAS,GAAGlM,MAAM,CAACS,WAAW,CAAC;IAC/B0L,OAAO,GAAGnM,MAAM,CAACU,cAAc,CAAC;IAChC0L,SAAS,GAAGpM,MAAM,CAACW,QAAQ,CAAC;IAC5B0L,iBAAiB,GAAGrM,MAAM,CAACY,gBAAgB,CAAC;IAC5C+F,SAAS,GAAG3G,MAAM,CAACE,QAAQ,CAAC;IAC5B6G,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;IAC9CmM,OAAO;IACPC,OAAO;IACP;IACAC,SAAS,GAAG,IAAInK,OAAO,CAAC,CAAC;IACzBkF,WAAWA,CAAA,EAAG,CAAE;IAChB;AACJ;AACA;AACA;IACIkF,MAAMA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;MACjB,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAG,IAAInJ,cAAc,CAAC,IAAI,CAAC+I,SAAS,EAAE,IAAI,CAACG,iBAAiB,CAAC;MAC7E;MACA,IAAI,CAACM,MAAM,CAAC,CAAC;MACb,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAG,IAAInJ,eAAe,CAAC,IAAI,CAACuD,SAAS,CAACiG,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAACT,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;MACzG;MACA,MAAMS,OAAO,GAAG,IAAI,CAACX,SAAS,CAACY,UAAU,CAACvE,aAAa;MACvD;MACA;MACA;MACAsE,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACT,OAAO,CAACU,aAAa,EAAEJ,OAAO,CAAC;MACpE;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC9F,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC6C,OAAO,CAACG,MAAM,CAAC,IAAI,CAACF,OAAO,EAAEG,OAAO,CAAC;MAC1C,IAAI,CAACF,SAAS,CAACzE,IAAI,CAAC,CAAC;IACzB;IACA;AACJ;AACA;AACA;IACI4E,MAAMA,CAAA,EAAG;MACL,IAAI,IAAI,CAACL,OAAO,EAAEY,UAAU,EAAE;QAC1B,IAAI,CAACZ,OAAO,CAACK,MAAM,CAAC,CAAC;MACzB;IACJ;IACAzE,WAAWA,CAAA,EAAG;MACV,IAAI,CAACyE,MAAM,CAAC,CAAC;MACb,IAAI,CAACJ,OAAO,EAAEY,OAAO,CAAC,CAAC;IAC3B;IACA,OAAOrD,IAAI,YAAAsD,uBAAApD,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiC,cAAc;IAAA;IACjH,OAAOoB,IAAI,kBAjH8EvN,EAAE,CAAAwN,iBAAA;MAAAnD,IAAA,EAiHJ8B,cAAc;MAAA7B,SAAA;MAAAmD,QAAA,GAjHZzN,EAAE,CAAA0N,kBAAA,CAiHoF,CAAC;QAAEC,OAAO,EAAEzB,gBAAgB;QAAE0B,WAAW,EAAEzB;MAAe,CAAC,CAAC;IAAA;EAC/O;EAAC,OArDKA,cAAc;AAAA;AAsDpB;EAAA,QAAAN,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA,MAAMgC,wBAAwB,gBAAG,IAAI5N,cAAc,CAAC,0BAA0B,EAAE;EAC5E6N,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,gCAAgCA,CAAA,EAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC;AACA,MAAMC,cAAc,GAAG,gBAAgB;AAAC,IAClCC,OAAO;EAAb,MAAMA,OAAO,CAAC;IACV3H,WAAW,GAAG1G,MAAM,CAACC,UAAU,CAAC;IAChC8G,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;IAC9CiM,SAAS,GAAGpM,MAAM,CAACW,QAAQ,CAAC;IAC5B2N,WAAW;IACXC,UAAU;IACVC,UAAU;IACVC,kBAAkB;IAClBC,oBAAoB;IACpB;IACArL,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C;IACAsL,SAAS;IACT;IACAC,sBAAsB,GAAG,IAAI9N,SAAS,CAAC,CAAC;IACxC;IACAgF,UAAU,GAAG,CAAC,CAAC;IACf;IACAE,oBAAoB,GAAG,MAAM;IAC7B;IACA6I,cAAc,GAAG,IAAIxM,OAAO,CAAC,CAAC;IAC9B;IACA4D,YAAY,GAAG,KAAK;IACpB;IACA6I,UAAU;IACV;IACAC,SAAS;IACT;IACAC,iBAAiB;IACjB;IACAd,aAAa;IACb;IACA7H,SAAS;IACT;IACAC,cAAc;IACd;IACAC,eAAe;IACf;IACA,IAAIyH,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACO,UAAU;IAC1B;IACA,IAAIP,SAASA,CAACiB,KAAK,EAAE;MACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAOtD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjDC,4BAA4B,CAAC,CAAC;MAClC;MACA,IAAI,CAAC2C,UAAU,GAAGU,KAAK;MACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACA,IAAIjB,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACO,UAAU;IAC1B;IACA,IAAIP,SAASA,CAACgB,KAAK,EAAE;MACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAOtD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC3FG,4BAA4B,CAAC,CAAC;MAClC;MACA,IAAI,CAAC0C,UAAU,GAAGS,KAAK;MACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACAC,WAAW;IACX;AACJ;AACA;AACA;AACA;IACIC,KAAK;IACL;AACJ;AACA;AACA;IACIC,WAAW;IACX;IACAtB,cAAc;IACd;IACAuB,WAAW;IACX;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIC,UAAUA,CAACC,OAAO,EAAE;MACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;MACnD,MAAMC,YAAY,GAAG;QAAE,GAAG,IAAI,CAAC7J;MAAW,CAAC;MAC3C,IAAI2J,kBAAkB,IAAIA,kBAAkB,CAACtG,MAAM,EAAE;QACjDsG,kBAAkB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;UACjDH,YAAY,CAACG,SAAS,CAAC,GAAG,KAAK;QACnC,CAAC,CAAC;MACN;MACA,IAAI,CAACJ,mBAAmB,GAAGF,OAAO;MAClC,IAAIA,OAAO,IAAIA,OAAO,CAACrG,MAAM,EAAE;QAC3BqG,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;UACtCH,YAAY,CAACG,SAAS,CAAC,GAAG,IAAI;QAClC,CAAC,CAAC;QACF,IAAI,CAACpJ,WAAW,CAAC6B,aAAa,CAACuH,SAAS,GAAG,EAAE;MACjD;MACA,IAAI,CAAChK,UAAU,GAAG6J,YAAY;IAClC;IACAD,mBAAmB;IACnB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIK,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACR,UAAU;IAC1B;IACA,IAAIQ,SAASA,CAACP,OAAO,EAAE;MACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;IAC7B;IACA;IACArK,MAAM,GAAG,IAAIpE,YAAY,CAAC,CAAC;IAC3B;AACJ;AACA;AACA;AACA;IACIiP,KAAK,GAAG,IAAI,CAAC7K,MAAM;IACnBgB,OAAO,GAAGnG,MAAM,CAACyB,YAAY,CAAC,CAACwO,KAAK,CAAC,iBAAiB,CAAC;IACvD1I,WAAWA,CAAA,EAAG;MACV,MAAM2I,cAAc,GAAGlQ,MAAM,CAAC2N,wBAAwB,CAAC;MACvD,IAAI,CAACqB,iBAAiB,GAAGkB,cAAc,CAAClB,iBAAiB,IAAI,EAAE;MAC/D,IAAI,CAACT,UAAU,GAAG2B,cAAc,CAAClC,SAAS;MAC1C,IAAI,CAACQ,UAAU,GAAG0B,cAAc,CAACjC,SAAS;MAC1C,IAAI,CAACC,aAAa,GAAGgC,cAAc,CAAChC,aAAa;MACjD,IAAI,CAACH,cAAc,GAAGmC,cAAc,CAACnC,cAAc;MACnD,IAAI,CAACuB,WAAW,GAAGY,cAAc,CAACZ,WAAW;IACjD;IACAa,QAAQA,CAAA,EAAG;MACP,IAAI,CAACjB,kBAAkB,CAAC,CAAC;IAC7B;IACAkB,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAAC/B,WAAW,GAAG,IAAI5M,eAAe,CAAC,IAAI,CAACkN,sBAAsB,CAAC,CAC9D0B,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC;MACrB,IAAI,CAAClC,WAAW,CAACmC,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACvL,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;MAChE;MACA;MACA;MACA,IAAI,CAACwJ,sBAAsB,CAAC+B,OAAO,CAC9BC,IAAI,CAACnO,SAAS,CAAC,IAAI,CAACmM,sBAAsB,CAAC,EAAElM,SAAS,CAAC0M,KAAK,IAAI9M,KAAK,CAAC,GAAG8M,KAAK,CAACyB,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC1J,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9GsJ,SAAS,CAACK,WAAW,IAAI,IAAI,CAACzC,WAAW,CAAC0C,gBAAgB,CAACD,WAAW,CAAC,CAAC;MAC7E,IAAI,CAACnC,sBAAsB,CAAC+B,OAAO,CAACD,SAAS,CAAEO,SAAS,IAAK;QACzD;QACA;QACA;QACA,MAAMC,OAAO,GAAG,IAAI,CAAC5C,WAAW;QAChC,IAAI,IAAI,CAACtI,oBAAoB,KAAK,OAAO,IAAIkL,OAAO,CAACC,UAAU,EAAEvH,SAAS,CAAC,CAAC,EAAE;UAC1E,MAAMwF,KAAK,GAAG6B,SAAS,CAACG,OAAO,CAAC,CAAC;UACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACpC,KAAK,CAACjG,MAAM,GAAG,CAAC,EAAE+H,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;UACnF,IAAIrC,KAAK,CAACiC,KAAK,CAAC,IAAI,CAACjC,KAAK,CAACiC,KAAK,CAAC,CAACpK,QAAQ,EAAE;YACxCiK,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;UAChC,CAAC,MACI;YACDH,OAAO,CAACS,iBAAiB,CAAC,CAAC;UAC/B;QACJ;MACJ,CAAC,CAAC;IACN;IACAzJ,WAAWA,CAAA,EAAG;MACV,IAAI,CAACoG,WAAW,EAAEsD,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAChD,sBAAsB,CAACgD,OAAO,CAAC,CAAC;MACrC,IAAI,CAACzM,MAAM,CAACkD,QAAQ,CAAC,CAAC;MACtB,IAAI,CAACoG,kBAAkB,EAAEmD,OAAO,CAAC,CAAC;MAClCC,YAAY,CAAC,IAAI,CAACnD,oBAAoB,CAAC;IAC3C;IACA;IACAvH,QAAQA,CAAA,EAAG;MACP;MACA,MAAM2K,WAAW,GAAG,IAAI,CAAClD,sBAAsB,CAAC+B,OAAO;MACvD,OAAOmB,WAAW,CAAClB,IAAI,CAACnO,SAAS,CAAC,IAAI,CAACmM,sBAAsB,CAAC,EAAElM,SAAS,CAAC0M,KAAK,IAAI9M,KAAK,CAAC,GAAG8M,KAAK,CAACyB,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC3J,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrI;IACA;AACJ;AACA;AACA;AACA;AACA;IACIM,OAAOA,CAACsK,KAAK,EAAE,CAAE;IACjB;AACJ;AACA;AACA;AACA;AACA;IACI3J,UAAUA,CAAC2J,KAAK,EAAE,CAAE;IACpB;IACAC,cAAcA,CAACvJ,KAAK,EAAE;MAClB,MAAMwJ,OAAO,GAAGxJ,KAAK,CAACwJ,OAAO;MAC7B,MAAMf,OAAO,GAAG,IAAI,CAAC5C,WAAW;MAChC,QAAQ2D,OAAO;QACX,KAAKhQ,MAAM;UACP,IAAI,CAACC,cAAc,CAACuG,KAAK,CAAC,EAAE;YACxBA,KAAK,CAACC,cAAc,CAAC,CAAC;YACtB,IAAI,CAACvD,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;UAC/B;UACA;QACJ,KAAKpD,UAAU;UACX,IAAI,IAAI,CAAC8M,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;YAC7C,IAAI,CAAC5J,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;UAC/B;UACA;QACJ,KAAKrD,WAAW;UACZ,IAAI,IAAI,CAAC+M,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;YAC7C,IAAI,CAAC5J,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;UAC/B;UACA;QACJ;UACI,IAAI6M,OAAO,KAAKpQ,QAAQ,IAAIoQ,OAAO,KAAKnQ,UAAU,EAAE;YAChDoP,OAAO,CAACgB,cAAc,CAAC,UAAU,CAAC;UACtC;UACAhB,OAAO,CAACiB,SAAS,CAAC1J,KAAK,CAAC;UACxB;MACR;IACJ;IACA;AACJ;AACA;AACA;IACI2J,cAAcA,CAACzK,MAAM,GAAG,SAAS,EAAE;MAC/B;MACA,IAAI,CAAC8G,kBAAkB,EAAEmD,OAAO,CAAC,CAAC;MAClC,IAAI,CAACnD,kBAAkB,GAAGzN,eAAe,CAAC,MAAM;QAC5C,MAAMqR,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;QACtC;QACA,IAAI,CAACD,SAAS,IAAI,CAACA,SAAS,CAACE,QAAQ,CAACC,QAAQ,CAAC3I,aAAa,CAAC,EAAE;UAC3D,MAAMqH,OAAO,GAAG,IAAI,CAAC5C,WAAW;UAChC4C,OAAO,CAACgB,cAAc,CAACvK,MAAM,CAAC,CAAC8K,kBAAkB,CAAC,CAAC;UACnD;UACA;UACA;UACA,IAAI,CAACvB,OAAO,CAACC,UAAU,IAAIkB,SAAS,EAAE;YAClCA,SAAS,CAAC3K,KAAK,CAAC,CAAC;UACrB;QACJ;MACJ,CAAC,EAAE;QAAEgL,QAAQ,EAAE,IAAI,CAACtG;MAAU,CAAC,CAAC;IACpC;IACA;AACJ;AACA;AACA;IACIuG,eAAeA,CAAA,EAAG;MACd,IAAI,CAACrE,WAAW,CAACoD,aAAa,CAAC,CAAC,CAAC,CAAC;IACtC;IACA;AACJ;AACA;AACA;IACIkB,YAAYA,CAACC,MAAM,EAAE,CAAE;IACvB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI3D,kBAAkBA,CAAC4D,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE+E,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE;MAC7D,IAAI,CAACnI,UAAU,GAAG;QACd,GAAG,IAAI,CAACA,UAAU;QAClB,CAAC,iBAAiB,GAAGgN,IAAI,KAAK,QAAQ;QACtC,CAAC,gBAAgB,GAAGA,IAAI,KAAK,OAAO;QACpC,CAAC,gBAAgB,GAAGC,IAAI,KAAK,OAAO;QACpC,CAAC,gBAAgB,GAAGA,IAAI,KAAK;MACjC,CAAC;MACD,IAAI,CAAChM,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IAC1C;IACA;IACA/D,gBAAgBA,CAACsN,KAAK,EAAE;MACpB,MAAMC,MAAM,GAAGD,KAAK,KAAK5E,cAAc;MACvC,IAAI6E,MAAM,IAAID,KAAK,KAAK7E,eAAe,EAAE;QACrC,IAAI8E,MAAM,EAAE;UACRpB,YAAY,CAAC,IAAI,CAACnD,oBAAoB,CAAC;UACvC,IAAI,CAACA,oBAAoB,GAAGwE,SAAS;QACzC;QACA,IAAI,CAACrE,cAAc,CAAC9G,IAAI,CAACkL,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;QACnD,IAAI,CAAChN,YAAY,GAAG,KAAK;MAC7B;IACJ;IACAV,iBAAiBA,CAACyN,KAAK,EAAE;MACrB,IAAIA,KAAK,KAAK7E,eAAe,IAAI6E,KAAK,KAAK5E,cAAc,EAAE;QACvD,IAAI,CAACnI,YAAY,GAAG,IAAI;MAC5B;IACJ;IACAkN,UAAUA,CAACC,MAAM,EAAE;MACf,IAAI,CAACpN,oBAAoB,GAAGoN,MAAM,GAAG,OAAO,GAAG,MAAM;MACrD,IAAIA,MAAM,EAAE;QACR,IAAI,IAAI,CAAC9E,WAAW,CAACmD,eAAe,KAAK,CAAC,EAAE;UACxC;UACA;UACA;UACA;UACA;UACA;UACA,MAAMY,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;UACtC,IAAID,SAAS,EAAE;YACXA,SAAS,CAACgB,SAAS,GAAG,CAAC;UAC3B;QACJ;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAChQ,mBAAmB,EAAE;QAChC;QACA;QACA;QACA,IAAI,CAACqL,oBAAoB,GAAG4E,UAAU,CAAC,MAAM,IAAI,CAAC5N,gBAAgB,CAAC0I,cAAc,CAAC,EAAE,GAAG,CAAC;MAC5F;MACA;MACA,IAAI,IAAI,CAAC/K,mBAAmB,EAAE;QAC1BiQ,UAAU,CAAC,MAAM;UACb,IAAI,CAAC5N,gBAAgB,CAAC0N,MAAM,GAAGjF,eAAe,GAAGC,cAAc,CAAC;QACpE,CAAC,CAAC;MACN;MACA,IAAI,CAACrH,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;IAC1C;IACA;AACJ;AACA;AACA;AACA;AACA;IACI4G,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC1B,SAAS,CAACgC,OAAO,CACjBC,IAAI,CAACnO,SAAS,CAAC,IAAI,CAACkM,SAAS,CAAC,CAAC,CAC/B+B,SAAS,CAAEtB,KAAK,IAAK;QACtB,IAAI,CAACR,sBAAsB,CAAC2E,KAAK,CAACnE,KAAK,CAACvM,MAAM,CAACiO,IAAI,IAAIA,IAAI,CAACjK,WAAW,KAAK,IAAI,CAAC,CAAC;QAClF,IAAI,CAAC+H,sBAAsB,CAAC4E,eAAe,CAAC,CAAC;MACjD,CAAC,CAAC;IACN;IACA;IACAlB,aAAaA,CAAA,EAAG;MACZ,IAAID,SAAS,GAAG,IAAI;MACpB,IAAI,IAAI,CAACzD,sBAAsB,CAACzF,MAAM,EAAE;QACpC;QACA;QACA;QACA;QACAkJ,SAAS,GAAG,IAAI,CAACzD,sBAAsB,CAAC6E,KAAK,CAAC3L,eAAe,CAAC,CAAC,CAAC4L,OAAO,CAAC,eAAe,CAAC;MAC5F;MACA,OAAOrB,SAAS;IACpB;IACA,OAAOvI,IAAI,YAAA6J,gBAAA3J,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqE,OAAO;IAAA;IAC1G,OAAOpE,IAAI,kBA5e8EnK,EAAE,CAAAoK,iBAAA;MAAAC,IAAA,EA4eJkE,OAAO;MAAAjE,SAAA;MAAAwJ,cAAA,WAAAC,uBAAA1P,EAAA,EAAAC,GAAA,EAAA0P,QAAA;QAAA,IAAA3P,EAAA;UA5eLrE,EAAE,CAAAiU,cAAA,CAAAD,QAAA,EA4eyzB9H,gBAAgB;UA5e30BlM,EAAE,CAAAiU,cAAA,CAAAD,QAAA,EA4ew4BrN,WAAW;UA5er5B3G,EAAE,CAAAiU,cAAA,CAAAD,QAAA,EA4e88BrN,WAAW;QAAA;QAAA,IAAAtC,EAAA;UAAA,IAAA6P,EAAA;UA5e39BlU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAA9P,GAAA,CAAAiL,WAAA,GAAA2E,EAAA,CAAAP,KAAA;UAAF3T,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAA9P,GAAA,CAAAuK,SAAA,GAAAqF,EAAA;UAAFlU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAA9P,GAAA,CAAAgL,KAAA,GAAA4E,EAAA;QAAA;MAAA;MAAAG,SAAA,WAAAC,cAAAjQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAuU,WAAA,CA4emiC5T,WAAW;QAAA;QAAA,IAAA0D,EAAA;UAAA,IAAA6P,EAAA;UA5ehjClU,EAAE,CAAAmU,cAAA,CAAAD,EAAA,GAAFlU,EAAE,CAAAoU,WAAA,QAAA9P,GAAA,CAAA+K,WAAA,GAAA6E,EAAA,CAAAP,KAAA;QAAA;MAAA;MAAAnJ,QAAA;MAAAC,YAAA,WAAA+J,qBAAAnQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAsG,WAAA,eA4eJ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;QAAA;MAAA;MAAAuE,MAAA;QAAAuD,aAAA;QAAA7H,SAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAyH,SAAA;QAAAC,SAAA;QAAAF,cAAA,0CAAuV3N,gBAAgB;QAAAkP,WAAA,oCAAgDL,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG7O,gBAAgB,CAAC6O,KAAK,CAAE;QAAAM,UAAA;QAAAQ,SAAA;MAAA;MAAAwE,OAAA;QAAApP,MAAA;QAAA6K,KAAA;MAAA;MAAApF,QAAA;MAAA2C,QAAA,GA5endzN,EAAE,CAAA0N,kBAAA,CA4emsB,CAAC;QAAEC,OAAO,EAAEjH,cAAc;QAAEkH,WAAW,EAAEW;MAAQ,CAAC,CAAC;MAAAvD,kBAAA,EAAArG,GAAA;MAAAsG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsJ,iBAAArQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5exvBrE,EAAE,CAAAsL,eAAA;UAAFtL,EAAE,CAAA2U,UAAA,IAAA/P,8BAAA,sBA4ekoC,CAAC;QAAA;MAAA;MAAAgQ,MAAA;MAAAjJ,aAAA;MAAAC,eAAA;IAAA;EACluC;EAAC,OA5VK2C,OAAO;AAAA;AA6Vb;EAAA,QAAA1C,SAAA,oBAAAA,SAAA;AAAA;;AAmDA;AACA,MAAMgJ,wBAAwB,gBAAG,IAAI5U,cAAc,CAAC,0BAA0B,EAAE;EAC5E6N,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM6E,QAAQ,GAAG1S,MAAM,CAACW,QAAQ,CAAC;IACjC,OAAO,MAAM4C,8BAA8B,CAACmP,QAAQ,CAAC;EACzD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASkC,gCAAgCA,CAACC,QAAQ,EAAE;EAChD,MAAMnC,QAAQ,GAAG1S,MAAM,CAACW,QAAQ,CAAC;EACjC,OAAO,MAAM4C,8BAA8B,CAACmP,QAAQ,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,yCAAyC,GAAG;EAC9CrH,OAAO,EAAEkH,wBAAwB;EACjCI,IAAI,EAAE,EAAE;EACRC,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMK,sBAAsB,GAAG,CAAC;AAChC;AACA,MAAMC,kBAAkB,gBAAG,IAAIC,OAAO,CAAC,CAAC;AACxC;AAAA,IACMC,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBC,QAAQ,GAAGrV,MAAM,CAACC,UAAU,CAAC;IAC7BoM,iBAAiB,GAAGrM,MAAM,CAACY,gBAAgB,CAAC;IAC5C0U,iBAAiB,GAAGtV,MAAM,CAACyG,WAAW,EAAE;MAAEK,QAAQ,EAAE,IAAI;MAAEyO,IAAI,EAAE;IAAK,CAAC,CAAC;IACvEC,IAAI,GAAGxV,MAAM,CAACsD,cAAc,EAAE;MAAEwD,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjDF,aAAa,GAAG5G,MAAM,CAACwB,YAAY,CAAC;IACpCiU,OAAO,GAAGzV,MAAM,CAACqB,MAAM,CAAC;IACxB+K,SAAS,GAAGpM,MAAM,CAACW,QAAQ,CAAC;IAC5B+U,eAAe,GAAG1V,MAAM,CAAC2U,wBAAwB,CAAC;IAClD5N,kBAAkB,GAAG/G,MAAM,CAACG,iBAAiB,CAAC;IAC9CkD,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3CsS,kBAAkB;IAClBrJ,OAAO;IACPsJ,WAAW,GAAG,IAAI;IAClBC,SAAS,GAAG,KAAK;IACjBC,2BAA2B,GAAGvT,YAAY,CAACwT,KAAK;IAChDC,kBAAkB,GAAGzT,YAAY,CAACwT,KAAK;IACvCE,sBAAsB,GAAG1T,YAAY,CAACwT,KAAK;IAC3CG,eAAe;IACf;AACJ;AACA;AACA;IACIC,mBAAmB;IACnB;AACJ;AACA;AACA;IACIC,mBAAmB;IACnB;IACA;IACAC,SAAS,GAAGnD,SAAS;IACrB;AACJ;AACA;AACA;IACI,IAAIoD,4BAA4BA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAACC,IAAI;IACpB;IACA,IAAID,4BAA4BA,CAACE,CAAC,EAAE;MAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;IACjB;IACA;IACA,IAAID,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACE,KAAK;IACrB;IACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;MACX,IAAIA,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;QACrB;MACJ;MACA,IAAI,CAACA,KAAK,GAAGF,IAAI;MACjB,IAAI,CAACN,sBAAsB,CAACS,WAAW,CAAC,CAAC;MACzC,IAAIH,IAAI,EAAE;QACN,IAAIA,IAAI,KAAK,IAAI,CAACJ,mBAAmB,KAAK,OAAOxK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UACtFI,0BAA0B,CAAC,CAAC;QAChC;QACA,IAAI,CAACkK,sBAAsB,GAAGM,IAAI,CAACvG,KAAK,CAACU,SAAS,CAAEiG,MAAM,IAAK;UAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;UACzB;UACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACR,mBAAmB,EAAE;YACtE,IAAI,CAACA,mBAAmB,CAAChR,MAAM,CAACC,IAAI,CAACuR,MAAM,CAAC;UAChD;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACrB,iBAAiB,EAAE5L,mBAAmB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;IACvE;IACA8M,KAAK;IACL;IACAI,QAAQ;IACR;AACJ;AACA;AACA;AACA;IACIC,YAAY,GAAG,IAAI;IACnB;IACAC,UAAU,GAAG,IAAIhW,YAAY,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;IACI;IACAiW,UAAU,GAAG,IAAI,CAACD,UAAU;IAC5B;IACAE,UAAU,GAAG,IAAIlW,YAAY,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;IACI;IACAmW,WAAW,GAAG,IAAI,CAACD,UAAU;IAC7B1P,WAAWA,CAAA,EAAG;MACV,MAAMuH,UAAU,GAAG9O,MAAM,CAACwG,cAAc,EAAE;QAAEM,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC7D,MAAMqQ,QAAQ,GAAGnX,MAAM,CAACsB,SAAS,CAAC;MAClC,IAAI,CAAC6U,mBAAmB,GAAGrH,UAAU,YAAYT,OAAO,GAAGS,UAAU,GAAGoE,SAAS;MACjF,IAAI,CAACyC,kBAAkB,GAAGwB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC/B,QAAQ,CAAC9M,aAAa,EAAE,YAAY,EAAGE,KAAK,IAAK;QAC5F,IAAI,CAAC9G,gCAAgC,CAAC8G,KAAK,CAAC,EAAE;UAC1C,IAAI,CAAC4N,SAAS,GAAG,OAAO;QAC5B;MACJ,CAAC,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC;IACzB;IACAjH,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACkH,YAAY,CAAC,CAAC;IACvB;IACApP,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACqO,IAAI,IAAI,IAAI,CAACgB,SAAS,CAAC,IAAI,CAAChB,IAAI,CAAC,EAAE;QACxCrB,kBAAkB,CAACsC,MAAM,CAAC,IAAI,CAACjB,IAAI,CAAC;MACxC;MACA,IAAI,CAACZ,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACO,eAAe,EAAEQ,WAAW,CAAC,CAAC;MACnC,IAAI,CAACT,sBAAsB,CAACS,WAAW,CAAC,CAAC;MACzC,IAAI,CAACZ,2BAA2B,CAACY,WAAW,CAAC,CAAC;MAC9C,IAAI,CAACV,kBAAkB,CAACU,WAAW,CAAC,CAAC;MACrC,IAAI,IAAI,CAACd,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACzI,OAAO,CAAC,CAAC;QAC1B,IAAI,CAACyI,WAAW,GAAG,IAAI;MAC3B;IACJ;IACA;IACA,IAAI6B,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC5B,SAAS;IACzB;IACA;IACA,IAAI6B,GAAGA,CAAA,EAAG;MACN,OAAO,IAAI,CAAClC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACvG,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;IACjE;IACA;IACAtF,eAAeA,CAAA,EAAG;MACd,OAAO,CAAC,EAAE,IAAI,CAAC2L,iBAAiB,IAAI,IAAI,CAACa,mBAAmB,IAAI,IAAI,CAACI,IAAI,CAAC;IAC9E;IACA;IACAoB,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAAC+B,SAAS,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC9D;IACA;IACAA,QAAQA,CAAA,EAAG;MACP,MAAMtB,IAAI,GAAG,IAAI,CAACA,IAAI;MACtB,IAAI,IAAI,CAACV,SAAS,IAAI,CAACU,IAAI,EAAE;QACzB;MACJ;MACA,IAAI,CAACL,eAAe,EAAEQ,WAAW,CAAC,CAAC;MACnC,MAAMoB,eAAe,GAAG5C,kBAAkB,CAAC6C,GAAG,CAACxB,IAAI,CAAC;MACpDrB,kBAAkB,CAAC8C,GAAG,CAACzB,IAAI,EAAE,IAAI,CAAC;MAClC;MACA;MACA,IAAIuB,eAAe,IAAIA,eAAe,KAAK,IAAI,EAAE;QAC7CA,eAAe,CAACF,SAAS,CAAC,CAAC;MAC/B;MACA,MAAMK,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC3B,IAAI,CAAC;MAC5C,MAAM4B,aAAa,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC;MAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;MACvD,IAAI,CAACC,YAAY,CAAC/B,IAAI,EAAE8B,gBAAgB,CAAC;MACzCF,aAAa,CAAC7I,WAAW,GACrBiH,IAAI,CAACjH,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC3F,eAAe,CAAC,CAAC,GAAG4M,IAAI,CAACjH,WAAW;MACzE;MACA;MACA,IAAI,CAAC2I,UAAU,CAACM,WAAW,CAAC,CAAC,EAAE;QAC3BN,UAAU,CAACxL,MAAM,CAAC,IAAI,CAAC+L,UAAU,CAACjC,IAAI,CAAC,CAAC;QACxCA,IAAI,CAAClH,WAAW,EAAE5C,MAAM,CAAC,IAAI,CAACoK,QAAQ,CAAC;MAC3C;MACA,IAAI,CAACf,2BAA2B,GAAG,IAAI,CAAC2C,mBAAmB,CAAC,CAAC,CAAC/H,SAAS,CAAC,MAAM,IAAI,CAACkH,SAAS,CAAC,CAAC,CAAC;MAC/FrB,IAAI,CAACzH,UAAU,GAAG,IAAI,CAACnF,eAAe,CAAC,CAAC,GAAG,IAAI,CAACwM,mBAAmB,GAAGjD,SAAS;MAC/EqD,IAAI,CAACxH,SAAS,GAAG,IAAI,CAAC2I,GAAG;MACzBnB,IAAI,CAACnE,cAAc,CAAC,IAAI,CAACiE,SAAS,IAAI,SAAS,CAAC;MAChD,IAAI,CAACqC,cAAc,CAAC,IAAI,CAAC;MACzB,IAAInC,IAAI,YAAYlI,OAAO,EAAE;QACzBkI,IAAI,CAACpD,UAAU,CAAC,IAAI,CAAC;QACrBoD,IAAI,CAAC3H,sBAAsB,CAAC+B,OAAO,CAACC,IAAI,CAACjO,SAAS,CAAC4T,IAAI,CAACvG,KAAK,CAAC,CAAC,CAACU,SAAS,CAAC,MAAM;UAC5E;UACA;UACA2H,gBAAgB,CAACM,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,CAAC,CAAC;UAChEP,gBAAgB,CAACM,kBAAkB,CAAC,IAAI,CAAC;QAC7C,CAAC,CAAC;MACN;IACJ;IACA;IACAf,SAASA,CAAA,EAAG;MACR,IAAI,CAACrB,IAAI,EAAEvG,KAAK,CAAC5K,IAAI,CAAC,CAAC;IAC3B;IACA;AACJ;AACA;AACA;IACIsC,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;MACnB,IAAI,IAAI,CAAChB,aAAa,IAAIe,MAAM,EAAE;QAC9B,IAAI,CAACf,aAAa,CAACiB,QAAQ,CAAC,IAAI,CAACwN,QAAQ,EAAE1N,MAAM,EAAEC,OAAO,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAACyN,QAAQ,CAAC9M,aAAa,CAACb,KAAK,CAACE,OAAO,CAAC;MAC9C;IACJ;IACA;AACJ;AACA;IACIiR,cAAcA,CAAA,EAAG;MACb,IAAI,CAACjD,WAAW,EAAEiD,cAAc,CAAC,CAAC;IACtC;IACA;IACAjC,YAAYA,CAACD,MAAM,EAAE;MACjB,MAAMsB,UAAU,GAAG,IAAI,CAACrC,WAAW;MACnC,MAAMW,IAAI,GAAG,IAAI,CAACE,KAAK;MACvB,IAAI,CAACwB,UAAU,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;QAC/B;MACJ;MACA,IAAI,CAAC3B,2BAA2B,CAACY,WAAW,CAAC,CAAC;MAC9C,IAAI,CAACR,eAAe,EAAEQ,WAAW,CAAC,CAAC;MACnC;MACA;MACA,IAAIH,IAAI,YAAYlI,OAAO,IAAI,IAAI,CAACkJ,SAAS,CAAChB,IAAI,CAAC,EAAE;QACjD,IAAI,CAACL,eAAe,GAAGK,IAAI,CAAC1H,cAAc,CAAC+B,IAAI,CAAChO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC8N,SAAS,CAAC,MAAM;UACrEuH,UAAU,CAACtL,MAAM,CAAC,CAAC;UACnB4J,IAAI,CAAClH,WAAW,EAAE1C,MAAM,CAAC,CAAC;QAC9B,CAAC,CAAC;QACF4J,IAAI,CAACpD,UAAU,CAAC,KAAK,CAAC;MAC1B,CAAC,MACI;QACD8E,UAAU,CAACtL,MAAM,CAAC,CAAC;QACnB4J,IAAI,EAAElH,WAAW,EAAE1C,MAAM,CAAC,CAAC;MAC/B;MACA,IAAI4J,IAAI,IAAI,IAAI,CAACgB,SAAS,CAAChB,IAAI,CAAC,EAAE;QAC9BrB,kBAAkB,CAACsC,MAAM,CAACjB,IAAI,CAAC;MACnC;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACO,YAAY,KAAKH,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACN,SAAS,IAAI,CAAC,IAAI,CAAC1M,eAAe,CAAC,CAAC,CAAC,EAAE;QAC3F,IAAI,CAACjC,KAAK,CAAC,IAAI,CAAC2O,SAAS,CAAC;MAC9B;MACA,IAAI,CAACA,SAAS,GAAGnD,SAAS;MAC1B,IAAI,CAACwF,cAAc,CAAC,KAAK,CAAC;IAC9B;IACA;IACAA,cAAcA,CAACtF,MAAM,EAAE;MACnB,IAAIA,MAAM,KAAK,IAAI,CAACyC,SAAS,EAAE;QAC3B,IAAI,CAACA,SAAS,GAAGzC,MAAM;QACvB,IAAI,CAACyC,SAAS,GAAG,IAAI,CAACkB,UAAU,CAAC3R,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC6R,UAAU,CAAC7R,IAAI,CAAC,CAAC;QAChE,IAAI,IAAI,CAACuE,eAAe,CAAC,CAAC,EAAE;UACxB,IAAI,CAAC2L,iBAAiB,CAAC/L,eAAe,CAAC6J,MAAM,CAAC;QAClD;QACA,IAAI,CAACrM,kBAAkB,CAAC0C,YAAY,CAAC,CAAC;MAC1C;IACJ;IACA;AACJ;AACA;AACA;IACIyO,cAAcA,CAAC3B,IAAI,EAAE;MACjB,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;QACnB,MAAMkD,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACxC,IAAI,CAAC;QAC3C,IAAI,CAACyC,qBAAqB,CAACzC,IAAI,EAAEuC,MAAM,CAACT,gBAAgB,CAAC;QACzD,IAAI,CAACzC,WAAW,GAAGpS,gBAAgB,CAAC,IAAI,CAAC4I,SAAS,EAAE0M,MAAM,CAAC;QAC3D,IAAI,CAAClD,WAAW,CAACqD,aAAa,CAAC,CAAC,CAACvI,SAAS,CAACjI,KAAK,IAAI;UAChD,IAAI,IAAI,CAAC8N,IAAI,YAAYlI,OAAO,EAAE;YAC9B,IAAI,CAACkI,IAAI,CAACvE,cAAc,CAACvJ,KAAK,CAAC;UACnC;QACJ,CAAC,CAAC;MACN;MACA,OAAO,IAAI,CAACmN,WAAW;IAC3B;IACA;AACJ;AACA;AACA;IACImD,iBAAiBA,CAACxC,IAAI,EAAE;MACpB,OAAO,IAAI9S,aAAa,CAAC;QACrB4U,gBAAgB,EAAE3U,uCAAuC,CAAC,IAAI,CAAC0I,SAAS,EAAE,IAAI,CAACiJ,QAAQ,CAAC,CACnFsD,kBAAkB,CAAC,CAAC,CACpBO,iBAAiB,CAAC,CAAC,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;QAClEjL,aAAa,EAAEqI,IAAI,CAACrI,aAAa,IAAI,kCAAkC;QACvEqB,UAAU,EAAEgH,IAAI,CAACvH,iBAAiB;QAClCoK,cAAc,EAAE,IAAI,CAAC1D,eAAe,CAAC,CAAC;QACtC3G,SAAS,EAAE,IAAI,CAACyG,IAAI,IAAI,KAAK;QAC7B6D,iBAAiB,EAAE,IAAI,CAAChW;MAC5B,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;AACA;IACI2V,qBAAqBA,CAACzC,IAAI,EAAE+C,QAAQ,EAAE;MAClC,IAAI/C,IAAI,CAACrH,kBAAkB,EAAE;QACzBoK,QAAQ,CAACC,eAAe,CAAC7I,SAAS,CAAC8I,MAAM,IAAI;UACzC,IAAI,CAAC/D,OAAO,CAACgE,GAAG,CAAC,MAAM;YACnB,MAAM3G,IAAI,GAAG0G,MAAM,CAACE,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;YAC5E,MAAM5G,IAAI,GAAGyG,MAAM,CAACE,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;YACzErD,IAAI,CAACrH,kBAAkB,CAAC4D,IAAI,EAAEC,IAAI,CAAC;UACvC,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIuF,YAAYA,CAAC/B,IAAI,EAAE8B,gBAAgB,EAAE;MACjC,IAAI,CAACwB,OAAO,EAAEC,eAAe,CAAC,GAAGvD,IAAI,CAACvI,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;MAClG,IAAI,CAAC4L,QAAQ,EAAEG,gBAAgB,CAAC,GAAGxD,IAAI,CAACtI,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;MACrG,IAAI,CAAC+L,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACL,QAAQ,EAAEG,gBAAgB,CAAC;MAC7D,IAAI,CAACJ,QAAQ,EAAEO,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;MAC7D,IAAIK,OAAO,GAAG,CAAC;MACf,IAAI,IAAI,CAACxQ,eAAe,CAAC,CAAC,EAAE;QACxB;QACA;QACAuQ,gBAAgB,GAAGL,OAAO,GAAGtD,IAAI,CAACvI,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;QAC1E8L,eAAe,GAAGH,QAAQ,GAAGE,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;QAChE,IAAI,IAAI,CAAC1D,mBAAmB,EAAE;UAC1B,IAAI,IAAI,CAACC,mBAAmB,IAAI,IAAI,EAAE;YAClC,MAAMgE,SAAS,GAAG,IAAI,CAACjE,mBAAmB,CAAC/G,KAAK,CAACqE,KAAK;YACtD,IAAI,CAAC2C,mBAAmB,GAAGgE,SAAS,GAAGA,SAAS,CAACtS,eAAe,CAAC,CAAC,CAACuS,SAAS,GAAG,CAAC;UACpF;UACAF,OAAO,GAAGP,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACxD,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;QAC1F;MACJ,CAAC,MACI,IAAI,CAACG,IAAI,CAACxI,cAAc,EAAE;QAC3BiM,OAAO,GAAGJ,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;QAC/CK,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MACnE;MACA1B,gBAAgB,CAACiC,aAAa,CAAC,CAC3B;QAAET,OAAO;QAAEG,OAAO;QAAEL,QAAQ;QAAEC,QAAQ;QAAEO;MAAQ,CAAC,EACjD;QAAEN,OAAO,EAAEC,eAAe;QAAEE,OAAO;QAAEL,QAAQ,EAAEO,gBAAgB;QAAEN,QAAQ;QAAEO;MAAQ,CAAC,EACpF;QACIN,OAAO;QACPG,OAAO,EAAEC,eAAe;QACxBN,QAAQ;QACRC,QAAQ,EAAEG,gBAAgB;QAC1BI,OAAO,EAAE,CAACA;MACd,CAAC,EACD;QACIN,OAAO,EAAEC,eAAe;QACxBE,OAAO,EAAEC,eAAe;QACxBN,QAAQ,EAAEO,gBAAgB;QAC1BN,QAAQ,EAAEG,gBAAgB;QAC1BI,OAAO,EAAE,CAACA;MACd,CAAC,CACJ,CAAC;IACN;IACA;IACA1B,mBAAmBA,CAAA,EAAG;MAClB,MAAM8B,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,aAAa,CAAC,CAAC;MACjD,MAAMC,WAAW,GAAG,IAAI,CAAC7E,WAAW,CAAC6E,WAAW,CAAC,CAAC;MAClD,MAAMC,WAAW,GAAG,IAAI,CAACvE,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAChR,MAAM,GAAG3C,EAAE,CAAC,CAAC;MACrF,MAAMmY,KAAK,GAAG,IAAI,CAACxE,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CACrBhP,QAAQ,CAAC,CAAC,CACVyJ,IAAI,CAAC/N,MAAM,CAAC+X,MAAM,IAAI,IAAI,CAAC/E,SAAS,IAAI+E,MAAM,KAAK,IAAI,CAACtF,iBAAiB,CAAC,CAAC,GAC9E9S,EAAE,CAAC,CAAC;MACV,OAAOF,KAAK,CAACiY,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;IAC3D;IACA;IACAI,gBAAgBA,CAACpS,KAAK,EAAE;MACpB,IAAI,CAAC7G,+BAA+B,CAAC6G,KAAK,CAAC,EAAE;QACzC;QACA;QACA,IAAI,CAAC4N,SAAS,GAAG5N,KAAK,CAACqS,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG5H,SAAS;QACzD;QACA;QACA;QACA,IAAI,IAAI,CAACvJ,eAAe,CAAC,CAAC,EAAE;UACxBlB,KAAK,CAACC,cAAc,CAAC,CAAC;QAC1B;MACJ;IACJ;IACA;IACAsJ,cAAcA,CAACvJ,KAAK,EAAE;MAClB,MAAMwJ,OAAO,GAAGxJ,KAAK,CAACwJ,OAAO;MAC7B;MACA,IAAIA,OAAO,KAAK9P,KAAK,IAAI8P,OAAO,KAAK7P,KAAK,EAAE;QACxC,IAAI,CAACiU,SAAS,GAAG,UAAU;MAC/B;MACA,IAAI,IAAI,CAAC1M,eAAe,CAAC,CAAC,KACpBsI,OAAO,KAAKlQ,WAAW,IAAI,IAAI,CAAC2V,GAAG,KAAK,KAAK,IAC1CzF,OAAO,KAAKjQ,UAAU,IAAI,IAAI,CAAC0V,GAAG,KAAK,KAAM,CAAC,EAAE;QACrD,IAAI,CAACrB,SAAS,GAAG,UAAU;QAC3B,IAAI,CAACwB,QAAQ,CAAC,CAAC;MACnB;IACJ;IACA;IACAkD,YAAYA,CAACtS,KAAK,EAAE;MAChB,IAAI,IAAI,CAACkB,eAAe,CAAC,CAAC,EAAE;QACxB;QACAlB,KAAK,CAACE,eAAe,CAAC,CAAC;QACvB,IAAI,CAACkP,QAAQ,CAAC,CAAC;MACnB,CAAC,MACI;QACD,IAAI,CAACF,UAAU,CAAC,CAAC;MACrB;IACJ;IACA;IACAL,YAAYA,CAAA,EAAG;MACX;MACA,IAAI,IAAI,CAAC3N,eAAe,CAAC,CAAC,IAAI,IAAI,CAACwM,mBAAmB,EAAE;QACpD,IAAI,CAACH,kBAAkB,GAAG,IAAI,CAACG,mBAAmB,CAAChP,QAAQ,CAAC,CAAC,CAACuJ,SAAS,CAACkK,MAAM,IAAI;UAC9E,IAAIA,MAAM,KAAK,IAAI,CAACtF,iBAAiB,IAAI,CAACsF,MAAM,CAAC3T,QAAQ,EAAE;YACvD,IAAI,CAACoP,SAAS,GAAG,OAAO;YACxB,IAAI,CAACwB,QAAQ,CAAC,CAAC;UACnB;QACJ,CAAC,CAAC;MACN;IACJ;IACA;IACAW,UAAUA,CAACjC,IAAI,EAAE;MACb;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACjK,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,WAAW,KAAKoH,IAAI,CAACpH,WAAW,EAAE;QAChE,IAAI,CAAC7C,OAAO,GAAG,IAAInJ,cAAc,CAACoT,IAAI,CAACpH,WAAW,EAAE,IAAI,CAAC9C,iBAAiB,CAAC;MAC/E;MACA,OAAO,IAAI,CAACC,OAAO;IACvB;IACA;AACJ;AACA;AACA;AACA;IACIiL,SAASA,CAAChB,IAAI,EAAE;MACZ,OAAOrB,kBAAkB,CAAC6C,GAAG,CAACxB,IAAI,CAAC,KAAK,IAAI;IAChD;IACA,OAAOzM,IAAI,YAAAkR,uBAAAhR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoL,cAAc;IAAA;IACjH,OAAO/H,IAAI,kBA7+B8EvN,EAAE,CAAAwN,iBAAA;MAAAnD,IAAA,EA6+BJiL,cAAc;MAAAhL,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA0Q,4BAAA9W,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7+BZrE,EAAE,CAAA+E,UAAA,mBAAAqW,wCAAA5V,MAAA;YAAA,OA6+BJlB,GAAA,CAAA2W,YAAA,CAAAzV,MAAmB,CAAC;UAAA,CAAP,CAAC,uBAAA6V,4CAAA7V,MAAA;YAAA,OAAdlB,GAAA,CAAAyW,gBAAA,CAAAvV,MAAuB,CAAC;UAAA,CAAX,CAAC,qBAAA8V,0CAAA9V,MAAA;YAAA,OAAdlB,GAAA,CAAA4N,cAAA,CAAA1M,MAAqB,CAAC;UAAA,CAAT,CAAC;QAAA;QAAA,IAAAnB,EAAA;UA7+BZrE,EAAE,CAAAsG,WAAA,kBAAAhC,GAAA,CAAAmS,IAAA,GA6+BG,MAAM,GAAG,IAAI,mBAAAnS,GAAA,CAAAqT,QAAA,mBAAArT,GAAA,CAAAqT,QAAA,GAAArT,GAAA,CAAAmS,IAAA,kBAAAnS,GAAA,CAAAmS,IAAA,CAAApQ,OAAA,GAAO,IAAI;QAAA;MAAA;MAAAwE,MAAA;QAAA2L,4BAAA;QAAAC,IAAA;QAAAM,QAAA;QAAAC,YAAA;MAAA;MAAAvC,OAAA;QAAAwC,UAAA;QAAAC,UAAA;QAAAC,UAAA;QAAAC,WAAA;MAAA;MAAAtM,QAAA;IAAA;EAC1H;EAAC,OAzaKwK,cAAc;AAAA;AA0apB;EAAA,QAAAzJ,SAAA,oBAAAA,SAAA;AAAA;AAmCoB,IAEd0P,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB,OAAOvR,IAAI,YAAAwR,sBAAAtR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqR,aAAa;IAAA;IAChH,OAAOE,IAAI,kBAthC8Ezb,EAAE,CAAA0b,gBAAA;MAAArR,IAAA,EAshCSkR;IAAa;IAYjH,OAAOI,IAAI,kBAliC8E3b,EAAE,CAAA4b,gBAAA;MAAAC,SAAA,EAkiCmC,CAAC7G,yCAAyC,CAAC;MAAA8G,OAAA,GAAY/X,eAAe,EAC5LC,eAAe,EACfH,aAAa,EAAEC,mBAAmB,EAClCE,eAAe;IAAA;EAC3B;EAAC,OAlBKuX,aAAa;AAAA;AAmBnB;EAAA,QAAA1P,SAAA,oBAAAA,SAAA;AAAA;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkQ,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAE;IACX3R,IAAI,EAAE,CAAC;IACP4R,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,CACT;MACI7R,IAAI,EAAE,CAAC;MACP4R,IAAI,EAAE,MAAM;MACZrH,MAAM,EAAE;QAAEvK,IAAI,EAAE,CAAC;QAAEuK,MAAM,EAAE;UAAEuH,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAa,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACIhS,IAAI,EAAE,CAAC;MACPiS,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE;QACPlS,IAAI,EAAE,CAAC;QACPuK,MAAM,EAAE;UAAEvK,IAAI,EAAE,CAAC;UAAEuK,MAAM,EAAE;YAAEuH,OAAO,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAW,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QAChFG,OAAO,EAAE;MACb,CAAC;MACD1U,OAAO,EAAE;IACb,CAAC,EACD;MACIuC,IAAI,EAAE,CAAC;MACPiS,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE;QACPlS,IAAI,EAAE,CAAC;QACPuK,MAAM,EAAE;UAAEvK,IAAI,EAAE,CAAC;UAAEuK,MAAM,EAAE;YAAEuH,OAAO,EAAE;UAAE,CAAC;UAAEE,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACD1U,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACI2U,WAAW,EAAE;IACTpS,IAAI,EAAE,CAAC;IACP4R,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,CACT;MACI7R,IAAI,EAAE,CAAC;MACP4R,IAAI,EAAE,SAAS;MACfrH,MAAM,EAAE;QAAEvK,IAAI,EAAE,CAAC;QAAEuK,MAAM,EAAE;UAAEuH,OAAO,EAAE;QAAE,CAAC;QAAEE,MAAM,EAAE;MAAK;IAC5D,CAAC,EACD;MACIhS,IAAI,EAAE,CAAC;MACPiS,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,CACP;QAAElS,IAAI,EAAE,CAAC;QAAEuK,MAAM,EAAE;UAAEuH,OAAO,EAAE;QAAE,CAAC;QAAEE,MAAM,EAAE;MAAK,CAAC,EACjD;QAAEhS,IAAI,EAAE,CAAC;QAAEuK,MAAM,EAAE,IAAI;QAAE4H,OAAO,EAAE;MAA+C,CAAC,CACrF;MACD1U,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM2U,WAAW,GAAGV,iBAAiB,CAACU,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMT,aAAa,GAAGD,iBAAiB,CAACC,aAAa;AAErD,SAAS9P,gBAAgB,EAAE2B,wBAAwB,EAAEnH,cAAc,EAAEmO,wBAAwB,EAAEG,yCAAyC,EAAEG,sBAAsB,EAAE5G,OAAO,EAAEpC,cAAc,EAAExF,WAAW,EAAE4U,aAAa,EAAEjG,cAAc,EAAEmH,WAAW,EAAEV,iBAAiB,EAAEC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}