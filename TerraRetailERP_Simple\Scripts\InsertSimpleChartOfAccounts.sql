-- إدخال شجرة حسابات مبسطة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);

-- المستوى الأول - الحسابات الرئيسية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('1', 'الأصول', 'Assets', 1, NULL, 1, 1, 0, 1, GETDATE()),
('2', 'الخصوم', 'Liabilities', 2, NULL, 1, 1, 0, 1, GETDATE()),
('3', 'حقوق الملكية', 'Equity', 3, NULL, 1, 1, 0, 1, GETDATE()),
('4', 'الإيرادات', 'Revenue', 4, NULL, 1, 1, 0, 1, GETDATE()),
('5', 'المصروفات', 'Expenses', 5, NULL, 1, 1, 0, 1, GETDATE());

-- المستوى الثاني - الأصول
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11', 'الأصول المتداولة', 'Current Assets', 1, 1, 2, 1, 0, 1, GETDATE()),
('12', 'الأصول الثابتة', 'Fixed Assets', 1, 1, 2, 1, 0, 1, GETDATE());

-- المستوى الثالث - الأصول المتداولة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('111', 'النقدية والبنوك', 'Cash & Banks', 1, 6, 3, 1, 0, 1, GETDATE()),
('112', 'العملاء', 'Accounts Receivable', 1, 6, 3, 1, 0, 1, GETDATE()),
('113', 'المخزون', 'Inventory', 1, 6, 3, 1, 0, 1, GETDATE()),
('114', 'مصروفات مدفوعة مقدماً', 'Prepaid Expenses', 1, 6, 3, 1, 0, 1, GETDATE());

-- المستوى الرابع - النقدية والبنوك
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11101', 'الصندوق الرئيسي', 'Main Cash', 1, 8, 4, 0, 1, 1, GETDATE()),
('11102', 'صندوق الفرع الأول', 'Branch 1 Cash', 1, 8, 4, 0, 1, 1, GETDATE()),
('11103', 'البنك الأهلي المصري', 'National Bank of Egypt', 1, 8, 4, 0, 1, 1, GETDATE()),
('11104', 'بنك مصر', 'Banque Misr', 1, 8, 4, 0, 1, 1, GETDATE());

-- العملاء
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11201', 'عملاء محليين', 'Local Customers', 1, 9, 4, 0, 1, 1, GETDATE()),
('11202', 'عملاء أجانب', 'Foreign Customers', 1, 9, 4, 0, 1, 1, GETDATE());

-- المخزون
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('11301', 'مخزون البضاعة', 'Merchandise Inventory', 1, 10, 4, 0, 1, 1, GETDATE()),
('11302', 'مخزون المواد الخام', 'Raw Materials', 1, 10, 4, 0, 1, 1, GETDATE());

-- الأصول الثابتة
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('121', 'الأراضي والمباني', 'Land & Buildings', 1, 7, 3, 1, 0, 1, GETDATE()),
('122', 'الآلات والمعدات', 'Machinery & Equipment', 1, 7, 3, 1, 0, 1, GETDATE()),
('123', 'وسائل النقل', 'Vehicles', 1, 7, 3, 1, 0, 1, GETDATE());

INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('12101', 'الأراضي', 'Land', 1, 19, 4, 0, 1, 1, GETDATE()),
('12102', 'المباني', 'Buildings', 1, 19, 4, 0, 1, 1, GETDATE()),
('12201', 'آلات ومعدات', 'Machinery', 1, 20, 4, 0, 1, 1, GETDATE()),
('12301', 'سيارات', 'Cars', 1, 21, 4, 0, 1, 1, GETDATE());

-- الخصوم
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21', 'الخصوم المتداولة', 'Current Liabilities', 2, 2, 2, 1, 0, 1, GETDATE()),
('211', 'الموردين', 'Accounts Payable', 2, 26, 3, 1, 0, 1, GETDATE()),
('212', 'مصروفات مستحقة', 'Accrued Expenses', 2, 26, 3, 1, 0, 1, GETDATE());

INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('21101', 'موردين محليين', 'Local Suppliers', 2, 27, 4, 0, 1, 1, GETDATE()),
('21102', 'موردين أجانب', 'Foreign Suppliers', 2, 27, 4, 0, 1, 1, GETDATE()),
('21201', 'رواتب مستحقة', 'Accrued Salaries', 2, 28, 4, 0, 1, 1, GETDATE());

-- حقوق الملكية
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('31', 'رأس المال', 'Capital', 3, 3, 2, 1, 0, 1, GETDATE()),
('31101', 'رأس المال المدفوع', 'Paid Capital', 3, 32, 3, 0, 1, 1, GETDATE()),
('31102', 'أرباح محتجزة', 'Retained Earnings', 3, 32, 3, 0, 1, 1, GETDATE());

-- الإيرادات
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('41', 'إيرادات المبيعات', 'Sales Revenue', 4, 4, 2, 1, 0, 1, GETDATE()),
('41101', 'مبيعات محلية', 'Local Sales', 4, 35, 3, 0, 1, 1, GETDATE()),
('41102', 'مبيعات تصدير', 'Export Sales', 4, 35, 3, 0, 1, 1, GETDATE());

-- المصروفات
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('51', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 6, 5, 2, 1, 0, 1, GETDATE()),
('52', 'مصروفات التشغيل', 'Operating Expenses', 5, 5, 2, 1, 0, 1, GETDATE());

INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('51101', 'تكلفة المبيعات', 'Cost of Sales', 6, 38, 3, 0, 1, 1, GETDATE()),
('52101', 'رواتب وأجور', 'Salaries & Wages', 5, 39, 3, 0, 1, 1, GETDATE()),
('52102', 'إيجار', 'Rent Expense', 5, 39, 3, 0, 1, 1, GETDATE()),
('52103', 'كهرباء ومياه', 'Utilities', 5, 39, 3, 0, 1, 1, GETDATE()),
('52104', 'مصروفات متنوعة', 'Miscellaneous Expenses', 5, 39, 3, 0, 1, 1, GETDATE());
