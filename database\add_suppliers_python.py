#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة موردين مصريين حقيقيين لقاعدة البيانات
Add Real Egyptian Suppliers to Database with Arabic Support
"""

import pyodbc
import sys
from datetime import datetime

# إعدادات الاتصال بقاعدة البيانات
SERVER = 'localhost'
DATABASE = 'TerraRetailERP'
USERNAME = 'sa'
PASSWORD = '@a123admin4'

# سلسلة الاتصال
CONNECTION_STRING = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};'

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = pyodbc.connect(CONNECTION_STRING)
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def clear_suppliers(cursor):
    """حذف الموردين الموجودين"""
    try:
        cursor.execute("DELETE FROM Suppliers")
        cursor.execute("DBCC CHECKIDENT ('Suppliers', RESEED, 0)")
        print("🗑️ تم حذف الموردين الموجودين")
    except Exception as e:
        print(f"❌ خطأ في حذف الموردين: {e}")

def add_suppliers(cursor):
    """إضافة الموردين المصريين"""
    
    suppliers_data = [
        # موردين محليين - القاهرة
        {
            'code': 'SUP001',
            'name_ar': 'شركة النيل للتجارة والتوريدات',
            'name_en': 'Nile Trading & Supply Co.',
            'type_id': 1,  # مورد محلي
            'phone1': '+************',
            'phone2': '+201*********',
            'email': '<EMAIL>',
            'website': 'www.nile-trading.com',
            'address': 'شارع التحرير، وسط البلد، القاهرة',
            'area_id': 1,  # القاهرة
            'contact_name': 'أحمد محمد علي',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 100000.00,
            'opening_balance': -25000.00,
            'current_balance': -25000.00,
            'tax_number': '*********',
            'commercial_register': 'CR123456',
            'bank_name': 'البنك الأهلي المصري',
            'bank_account': '************',
            'rating': 4,
            'notes': 'مورد موثوق للمواد الغذائية والمشروبات'
        },
        {
            'code': 'SUP002',
            'name_ar': 'مجموعة العربي للاستيراد والتصدير',
            'name_en': 'Al Arabi Import Export Group',
            'type_id': 2,  # مورد دولي
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.alarabi-group.com',
            'address': 'مدينة نصر، القاهرة',
            'area_id': 1,  # القاهرة
            'contact_name': 'فاطمة حسن إبراهيم',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 200000.00,
            'opening_balance': 50000.00,
            'current_balance': 50000.00,
            'tax_number': '*********',
            'commercial_register': 'CR987654',
            'bank_name': 'بنك مصر',
            'bank_account': '*********098',
            'rating': 5,
            'notes': 'مورد دولي للإلكترونيات والأجهزة المنزلية'
        },
        # موردين محليين - الجيزة
        {
            'code': 'SUP003',
            'name_ar': 'مصنع الأهرام للمنسوجات',
            'name_en': 'Pyramids Textile Factory',
            'type_id': 1,  # مورد محلي
            'phone1': '+20*********0',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.pyramids-textile.com',
            'address': 'المنطقة الصناعية، 6 أكتوبر، الجيزة',
            'area_id': 2,  # الجيزة
            'contact_name': 'محمود عبدالله أحمد',
            'contact_phone': '+20*********0',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 150000.00,
            'opening_balance': 0.00,
            'current_balance': 0.00,
            'tax_number': '*********',
            'commercial_register': 'CR456789',
            'bank_name': 'بنك القاهرة',
            'bank_account': '*********456',
            'rating': 3,
            'notes': 'مصنع محلي للملابس والمنسوجات القطنية'
        },
        {
            'code': 'SUP004',
            'name_ar': 'شركة دلتا للكيماويات',
            'name_en': 'Delta Chemicals Company',
            'type_id': 1,  # مورد محلي
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.delta-chemicals.com',
            'address': 'الدقي، الجيزة',
            'area_id': 2,  # الجيزة
            'contact_name': 'نورا سامي محمد',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 60,
            'credit_limit': 300000.00,
            'opening_balance': -75000.00,
            'current_balance': -75000.00,
            'tax_number': '*********',
            'commercial_register': 'CR789123',
            'bank_name': 'البنك التجاري الدولي',
            'bank_account': '*********789',
            'rating': 4,
            'notes': 'مورد للمواد الكيماوية ومنتجات التنظيف'
        },
        # موردين محليين - الإسكندرية
        {
            'code': 'SUP005',
            'name_ar': 'مجموعة البحر المتوسط للأغذية',
            'name_en': 'Mediterranean Food Group',
            'type_id': 1,  # مورد محلي
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.med-food.com',
            'address': 'منطقة الميناء، الإسكندرية',
            'area_id': 3,  # الإسكندرية
            'contact_name': 'عمر خالد حسن',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 30,
            'credit_limit': 120000.00,
            'opening_balance': 30000.00,
            'current_balance': 30000.00,
            'tax_number': '*********',
            'commercial_register': 'CR321654',
            'bank_name': 'بنك الإسكندرية',
            'bank_account': '*********321',
            'rating': 4,
            'notes': 'مورد للأسماك والمأكولات البحرية المجمدة'
        },
        {
            'code': 'SUP006',
            'name_ar': 'شركة الإسكندرية للأدوية',
            'name_en': 'Alexandria Pharmaceuticals',
            'type_id': 1,  # مورد محلي
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.alex-pharma.com',
            'address': 'المنطقة الصناعية، برج العرب، الإسكندرية',
            'area_id': 3,  # الإسكندرية
            'contact_name': 'سارة أحمد محمود',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 500000.00,
            'opening_balance': -100000.00,
            'current_balance': -100000.00,
            'tax_number': '*********',
            'commercial_register': 'CR654987',
            'bank_name': 'بنك الاتحاد الوطني',
            'bank_account': '*********654',
            'rating': 5,
            'notes': 'مورد للأدوية والمستلزمات الطبية'
        },
        # موردين دوليين
        {
            'code': 'SUP007',
            'name_ar': 'الشركة الصينية للإلكترونيات',
            'name_en': 'China Electronics International',
            'type_id': 2,  # مورد دولي
            'phone1': '+*************',
            'phone2': '+*************',
            'email': '<EMAIL>',
            'website': 'www.china-electronics.com',
            'address': 'شنزن، الصين - مكتب القاهرة: مدينة نصر',
            'area_id': 1,  # القاهرة
            'contact_name': 'يوسف إبراهيم علي',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 60,
            'credit_limit': 400000.00,
            'opening_balance': 80000.00,
            'current_balance': 80000.00,
            'tax_number': 'INT001',
            'commercial_register': 'CRINT001',
            'bank_name': 'بنك HSBC',
            'bank_account': 'INT*********',
            'rating': 5,
            'notes': 'مورد دولي للإلكترونيات والهواتف الذكية'
        },
        {
            'code': 'SUP008',
            'name_ar': 'الشركة التركية للمنسوجات',
            'name_en': 'Turkish Textile Corporation',
            'type_id': 2,  # مورد دولي
            'phone1': '+************',
            'phone2': '+************',
            'email': '<EMAIL>',
            'website': 'www.turkish-textile.com',
            'address': 'إسطنبول، تركيا - مكتب الإسكندرية',
            'area_id': 3,  # الإسكندرية
            'contact_name': 'مريم عبدالرحمن',
            'contact_phone': '+************',
            'contact_email': '<EMAIL>',
            'payment_terms': 45,
            'credit_limit': 250000.00,
            'opening_balance': -40000.00,
            'current_balance': -40000.00,
            'tax_number': 'INT002',
            'commercial_register': 'TRINT002',
            'bank_name': 'بنك QNB',
            'bank_account': 'INT*********',
            'rating': 4,
            'notes': 'مورد دولي للأقمشة والملابس الجاهزة'
        },
        # موردين حكوميين
        {
            'code': 'SUP009',
            'name_ar': 'الشركة المصرية للأدوية',
            'name_en': 'Egyptian Pharmaceutical Company',
            'type_id': 3,  # مورد حكومي
            'phone1': '+***********',
            'phone2': '+***********',
            'email': '<EMAIL>',
            'website': 'www.epc.gov.eg',
            'address': 'مدينة العبور، القليوبية',
            'area_id': 6,  # القليوبية
            'contact_name': 'هالة محمد أحمد',
            'contact_phone': '+***********',
            'contact_email': '<EMAIL>',
            'payment_terms': 90,
            'credit_limit': 1000000.00,
            'opening_balance': 0.00,
            'current_balance': 0.00,
            'tax_number': 'GOV001',
            'commercial_register': 'CRGOV001',
            'bank_name': 'البنك المركزي المصري',
            'bank_account': 'GOV*********',
            'rating': 5,
            'notes': 'شركة حكومية لتصنيع الأدوية والمستلزمات الطبية'
        },
        {
            'code': 'SUP010',
            'name_ar': 'الهيئة العامة للسلع التموينية',
            'name_en': 'General Authority for Supply Commodities',
            'type_id': 3,  # مورد حكومي
            'phone1': '+***********',
            'phone2': '+***********',
            'email': '<EMAIL>',
            'website': 'www.gasc.gov.eg',
            'address': 'مدينة نصر، القاهرة',
            'area_id': 1,  # القاهرة
            'contact_name': 'خالد عبدالعزيز',
            'contact_phone': '+***********',
            'contact_email': '<EMAIL>',
            'payment_terms': 120,
            'credit_limit': 2000000.00,
            'opening_balance': 150000.00,
            'current_balance': 150000.00,
            'tax_number': 'GOV002',
            'commercial_register': 'CRGOV002',
            'bank_name': 'البنك المركزي المصري',
            'bank_account': 'GOV*********',
            'rating': 4,
            'notes': 'هيئة حكومية لتوريد السلع الاستراتيجية'
        }
    ]
    
    # SQL لإدراج المورد
    insert_sql = """
    INSERT INTO Suppliers (
        SupplierCode, NameAr, NameEn, SupplierTypeId,
        Phone1, Phone2, Email, Website, Address, AreaId,
        ContactPersonName, ContactPersonPhone, ContactPersonEmail,
        PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance,
        TaxNumber, CommercialRegister, BankName, BankAccountNumber,
        IsActive, Rating, Notes, CreatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    try:
        for supplier in suppliers_data:
            cursor.execute(insert_sql, (
                supplier['code'],
                supplier['name_ar'],
                supplier['name_en'],
                supplier['type_id'],
                supplier['phone1'],
                supplier['phone2'],
                supplier['email'],
                supplier['website'],
                supplier['address'],
                supplier['area_id'],
                supplier['contact_name'],
                supplier['contact_phone'],
                supplier['contact_email'],
                supplier['payment_terms'],
                supplier['credit_limit'],
                supplier['opening_balance'],
                supplier['current_balance'],
                supplier['tax_number'],
                supplier['commercial_register'],
                supplier['bank_name'],
                supplier['bank_account'],
                1,  # IsActive
                supplier['rating'],
                supplier['notes'],
                datetime.now()
            ))
            print(f"✅ تم إضافة المورد: {supplier['name_ar']}")
        
        # تحديث العداد
        cursor.execute("UPDATE Counters SET CurrentValue = ? WHERE CounterName = 'SUPPLIER'", len(suppliers_data))
        
        print(f"🎉 تم إضافة {len(suppliers_data)} مورد مصري بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الموردين: {e}")
        raise

def show_statistics(cursor):
    """عرض إحصائيات الموردين"""
    try:
        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM Suppliers WHERE IsActive = 1")
        total_suppliers = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT st.NameAr, COUNT(s.Id) as Count
            FROM SupplierTypes st
            LEFT JOIN Suppliers s ON st.Id = s.SupplierTypeId AND s.IsActive = 1
            WHERE st.IsActive = 1
            GROUP BY st.Id, st.NameAr
            ORDER BY st.Id
        """)
        
        print("\n📊 إحصائيات الموردين:")
        print(f"إجمالي الموردين النشطين: {total_suppliers}")
        print("\nتوزيع الموردين حسب النوع:")
        
        for row in cursor.fetchall():
            print(f"  • {row[0]}: {row[1]} مورد")
        
        # عرض الموردين مع أرصدتهم
        cursor.execute("""
            SELECT s.SupplierCode, s.NameAr, st.NameAr as TypeName, 
                   a.NameAr as AreaName, s.CurrentBalance, s.Rating
            FROM Suppliers s
            INNER JOIN SupplierTypes st ON s.SupplierTypeId = st.Id
            LEFT JOIN Areas a ON s.AreaId = a.Id
            WHERE s.IsActive = 1
            ORDER BY s.SupplierCode
        """)
        
        print("\n📋 قائمة الموردين:")
        print("الكود\t\tالاسم\t\t\t\tالنوع\t\t\tالمحافظة\t\tالرصيد\t\tالتقييم")
        print("-" * 100)
        
        for row in cursor.fetchall():
            code, name, type_name, area_name, balance, rating = row
            print(f"{code}\t{name[:20]:<20}\t{type_name[:15]:<15}\t{area_name or 'غير محدد':<10}\t{balance:>10.2f}\t{rating}/5")
            
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إضافة الموردين المصريين...")
    
    # الاتصال بقاعدة البيانات
    conn = connect_to_database()
    if not conn:
        sys.exit(1)
    
    try:
        cursor = conn.cursor()
        
        # حذف الموردين الموجودين
        clear_suppliers(cursor)
        
        # إضافة الموردين الجدد
        add_suppliers(cursor)
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الإحصائيات
        show_statistics(cursor)
        
        print("\n✅ تم إكمال العملية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        conn.rollback()
        sys.exit(1)
    
    finally:
        conn.close()
        print("🔒 تم إغلاق الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    main()
