using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("📦 Product Management")]
    public class ProductsController : ControllerBase
    {
        private readonly AppDbContext _context;

        public ProductsController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Product>>> GetProducts(
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.NameAr.Contains(search) || 
                                           p.NameEn!.Contains(search) || 
                                           p.ProductCode.Contains(search) ||
                                           p.Barcode!.Contains(search));
                }

                if (categoryId.HasValue)
                    query = query.Where(p => p.CategoryId == categoryId);

                if (isActive.HasValue)
                    query = query.Where(p => p.IsActive == isActive);

                var products = await query
                    .OrderBy(p => p.NameAr)
                    .Select(p => new
                    {
                        p.Id,
                        p.ProductCode,
                        p.NameAr,
                        p.NameEn,
                        p.Description,
                        p.Barcode,
                        p.CostPrice,
                        p.BasePrice,
                        p.ProfitMargin,
                        p.MinimumStock,
                        p.MaximumStock,
                        p.ReorderPoint,
                        p.Weight,
                        p.IsActive,
                        Category = new { p.Category.Id, p.Category.NameAr, p.Category.NameEn },
                        Unit = new { p.Unit.Id, p.Unit.NameAr, p.Unit.Symbol },
                        p.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المنتجات بنجاح",
                    data = products,
                    count = products.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetProduct(int id)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المنتج غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات المنتج بنجاح",
                    data = product
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Product>> CreateProduct(CreateProductRequest request)
        {
            try
            {
                // Generate product code
                var lastProduct = await _context.Products
                    .OrderByDescending(p => p.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastProduct?.Id ?? 0) + 1;
                var productCode = $"PRD{nextId:D6}";

                var product = new Product
                {
                    ProductCode = productCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    CategoryId = request.CategoryId,
                    UnitId = request.UnitId,
                    Barcode = request.Barcode,
                    CostPrice = request.CostPrice,
                    BasePrice = request.BasePrice,
                    ProfitMargin = request.ProfitMargin,
                    MinimumStock = request.MinimumStock,
                    MaximumStock = request.MaximumStock,
                    ReorderPoint = request.ReorderPoint,
                    Weight = request.Weight,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                // Load related data for response
                await _context.Entry(product)
                    .Reference(p => p.Category)
                    .LoadAsync();
                await _context.Entry(product)
                    .Reference(p => p.Unit)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, new
                {
                    success = true,
                    message = "تم إضافة المنتج بنجاح",
                    data = product
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProduct(int id, UpdateProductRequest request)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المنتج غير موجود" 
                    });

                product.NameAr = request.NameAr;
                product.NameEn = request.NameEn;
                product.Description = request.Description;
                product.CategoryId = request.CategoryId;
                product.UnitId = request.UnitId;
                product.Barcode = request.Barcode;
                product.CostPrice = request.CostPrice;
                product.BasePrice = request.BasePrice;
                product.ProfitMargin = request.ProfitMargin;
                product.MinimumStock = request.MinimumStock;
                product.MaximumStock = request.MaximumStock;
                product.ReorderPoint = request.ReorderPoint;
                product.Weight = request.Weight;
                product.IsActive = request.IsActive;
                product.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم تحديث بيانات المنتج بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المنتج غير موجود" 
                    });

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم حذف المنتج بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("categories")]
        public async Task<ActionResult<IEnumerable<Category>>> GetCategories()
        {
            try
            {
                var categories = await _context.Categories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الفئات بنجاح",
                    data = categories,
                    count = categories.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("units")]
        public async Task<ActionResult<IEnumerable<Unit>>> GetUnits()
        {
            try
            {
                var units = await _context.Units
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.DisplayOrder)
                    .ThenBy(u => u.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الوحدات بنجاح",
                    data = units,
                    count = units.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("barcode/{barcode}")]
        public async Task<ActionResult> GetProductByBarcode(string barcode)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .FirstOrDefaultAsync(p => p.Barcode == barcode && p.IsActive);

                if (product == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المنتج غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم العثور على المنتج بنجاح",
                    data = product
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs
    public class CreateProductRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int CategoryId { get; set; }
        public int UnitId { get; set; }
        public string? Barcode { get; set; }
        public decimal CostPrice { get; set; } = 0;
        public decimal BasePrice { get; set; } = 0;
        public decimal ProfitMargin { get; set; } = 0;
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public decimal? Weight { get; set; }
    }

    public class UpdateProductRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int CategoryId { get; set; }
        public int UnitId { get; set; }
        public string? Barcode { get; set; }
        public decimal CostPrice { get; set; } = 0;
        public decimal BasePrice { get; set; } = 0;
        public decimal ProfitMargin { get; set; } = 0;
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public decimal? Weight { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
