using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Data
{
    public class TerraRetailDbContext : DbContext
    {
        public TerraRetailDbContext(DbContextOptions<TerraRetailDbContext> options) : base(options)
        {
        }

        // User Management
        public DbSet<User> Users { get; set; }
        public DbSet<UserBranch> UserBranches { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }

        // Company Structure
        public DbSet<Branch> Branches { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }

        // HR Management
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Shift> Shifts { get; set; }
        public DbSet<EmployeeShift> EmployeeShifts { get; set; }
        public DbSet<AttendanceRecord> AttendanceRecords { get; set; }
        public DbSet<LeaveType> LeaveTypes { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<EmployeeDocument> EmployeeDocuments { get; set; }

        // Product Management
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<ProductStock> ProductStocks { get; set; }
        public DbSet<ProductBatch> ProductBatches { get; set; }
        public DbSet<ProductImage> ProductImages { get; set; }
        public DbSet<ProductAlternativeCode> ProductAlternativeCodes { get; set; }
        public DbSet<ProductBranchPrice> ProductBranchPrices { get; set; }
        public DbSet<ProductSupplier> ProductSuppliers { get; set; }
        public DbSet<PriceCategory> PriceCategories { get; set; }

        // Customer & Supplier Management
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerType> CustomerTypes { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<SupplierType> SupplierTypes { get; set; }

        // Sales Management
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<SalePayment> SalePayments { get; set; }

        // Purchase Management
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }
        public DbSet<PurchasePayment> PurchasePayments { get; set; }

        // Inventory Management
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<BranchTransfer> BranchTransfers { get; set; }
        public DbSet<BranchTransferDetail> BranchTransferDetails { get; set; }
        public DbSet<StockAdjustment> StockAdjustments { get; set; }
        public DbSet<StockAdjustmentDetail> StockAdjustmentDetails { get; set; }
        public DbSet<InventoryCount> InventoryCounts { get; set; }
        public DbSet<InventoryCountDetail> InventoryCountDetails { get; set; }

        // Financial Management
        public DbSet<ChartOfAccount> ChartOfAccounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }
        public DbSet<FinancialTransaction> FinancialTransactions { get; set; }

        // Treasury Management
        public DbSet<Receipt> Receipts { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<CashBox> CashBoxes { get; set; }
        public DbSet<CashBoxTransaction> CashBoxTransactions { get; set; }
        public DbSet<BankAccount> BankAccounts { get; set; }
        public DbSet<BankTransaction> BankTransactions { get; set; }

        // Common/Reference Data
        public DbSet<Country> Countries { get; set; }
        public DbSet<Area> Areas { get; set; }
        public DbSet<PaymentMethod> PaymentMethods { get; set; }
        public DbSet<Counter> Counters { get; set; }
        public DbSet<AuditTrail> AuditTrails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User relationships
            ConfigureUserRelationships(modelBuilder);

            // Configure Branch relationships
            ConfigureBranchRelationships(modelBuilder);

            // Configure Product relationships
            ConfigureProductRelationships(modelBuilder);

            // Configure Financial relationships
            ConfigureFinancialRelationships(modelBuilder);

            // Configure HR relationships
            ConfigureHRRelationships(modelBuilder);

            // Configure Inventory relationships
            ConfigureInventoryRelationships(modelBuilder);

            // Configure unique constraints and indexes
            ConfigureIndexes(modelBuilder);

            // Configure decimal precision
            ConfigureDecimalPrecision(modelBuilder);

            // Seed initial data
            SeedInitialData(modelBuilder);
        }

        private void ConfigureUserRelationships(ModelBuilder modelBuilder)
        {
            // User self-referencing relationships
            modelBuilder.Entity<User>()
                .HasOne(u => u.Employee)
                .WithMany(e => e.Users)
                .HasForeignKey(u => u.EmployeeId)
                .OnDelete(DeleteBehavior.Restrict);

            // UserBranch unique constraint
            modelBuilder.Entity<UserBranch>()
                .HasIndex(ub => new { ub.UserId, ub.BranchId })
                .IsUnique();

            // UserBranch default branch constraint
            modelBuilder.Entity<UserBranch>()
                .HasIndex(ub => new { ub.UserId, ub.IsDefault })
                .HasFilter("[IsDefault] = 1")
                .IsUnique();
        }

        private void ConfigureBranchRelationships(ModelBuilder modelBuilder)
        {
            // Branch transfer relationships
            modelBuilder.Entity<BranchTransfer>()
                .HasOne(bt => bt.FromBranch)
                .WithMany(b => b.FromBranchTransfers)
                .HasForeignKey(bt => bt.FromBranchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BranchTransfer>()
                .HasOne(bt => bt.ToBranch)
                .WithMany(b => b.ToBranchTransfers)
                .HasForeignKey(bt => bt.ToBranchId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureProductRelationships(ModelBuilder modelBuilder)
        {
            // Product unique constraints
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.ProductCode)
                .IsUnique();

            // Category self-referencing
            modelBuilder.Entity<Category>()
                .HasOne(c => c.ParentCategory)
                .WithMany(c => c.SubCategories)
                .HasForeignKey(c => c.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Unit self-referencing
            modelBuilder.Entity<Unit>()
                .HasOne(u => u.BaseUnit)
                .WithMany(u => u.DerivedUnits)
                .HasForeignKey(u => u.BaseUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // ProductBranchPrice unique constraint
            modelBuilder.Entity<ProductBranchPrice>()
                .HasIndex(pbp => new { pbp.ProductId, pbp.BranchId, pbp.PriceCategoryId })
                .IsUnique();
        }

        private void ConfigureFinancialRelationships(ModelBuilder modelBuilder)
        {
            // ChartOfAccount self-referencing
            modelBuilder.Entity<ChartOfAccount>()
                .HasOne(c => c.ParentAccount)
                .WithMany(c => c.SubAccounts)
                .HasForeignKey(c => c.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // CostCenter self-referencing
            modelBuilder.Entity<CostCenter>()
                .HasOne(c => c.ParentCostCenter)
                .WithMany(c => c.SubCostCenters)
                .HasForeignKey(c => c.ParentCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureHRRelationships(ModelBuilder modelBuilder)
        {
            // Employee unique constraints
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.BiometricId)
                .IsUnique()
                .HasFilter("[BiometricId] IS NOT NULL");

            // Department Manager relationship
            modelBuilder.Entity<Department>()
                .HasOne(d => d.Manager)
                .WithMany()
                .HasForeignKey(d => d.ManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Payroll unique constraint
            modelBuilder.Entity<Payroll>()
                .HasIndex(p => new { p.EmployeeId, p.Year, p.Month })
                .IsUnique();

            // EmployeeLeaveBalance unique constraint
            modelBuilder.Entity<EmployeeLeaveBalance>()
                .HasIndex(elb => new { elb.EmployeeId, elb.LeaveTypeId, elb.Year })
                .IsUnique();
        }

        private void ConfigureInventoryRelationships(ModelBuilder modelBuilder)
        {
            // ProductStock unique constraint
            modelBuilder.Entity<ProductStock>()
                .HasIndex(ps => new { ps.ProductId, ps.BranchId })
                .IsUnique();
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Sale unique constraints
            modelBuilder.Entity<Sale>()
                .HasIndex(s => s.InvoiceNumber)
                .IsUnique();

            // PurchaseInvoice unique constraints
            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            // Customer unique constraints
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.CustomerCode)
                .IsUnique();

            // Supplier unique constraints
            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.SupplierCode)
                .IsUnique();

            // JournalEntry unique constraints
            modelBuilder.Entity<JournalEntry>()
                .HasIndex(je => je.JournalNumber)
                .IsUnique();

            // Receipt unique constraints
            modelBuilder.Entity<Receipt>()
                .HasIndex(r => r.ReceiptNumber)
                .IsUnique();

            // Payment unique constraints
            modelBuilder.Entity<Payment>()
                .HasIndex(p => p.PaymentNumber)
                .IsUnique();
        }

        private void ConfigureDecimalPrecision(ModelBuilder modelBuilder)
        {
            // Configure all decimal properties to have consistent precision
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                    {
                        if (property.Name.Contains("Price") || property.Name.Contains("Amount") || 
                            property.Name.Contains("Cost") || property.Name.Contains("Salary") ||
                            property.Name.Contains("Total") || property.Name.Contains("Tax") ||
                            property.Name.Contains("Discount") || property.Name.Contains("Balance") ||
                            property.Name.Contains("Value") || property.Name.Contains("Fee"))
                        {
                            property.SetColumnType("decimal(18,2)");
                        }
                        else if (property.Name.Contains("Quantity") || property.Name.Contains("Stock") ||
                                property.Name.Contains("Weight") || property.Name.Contains("Length") ||
                                property.Name.Contains("Width") || property.Name.Contains("Height"))
                        {
                            property.SetColumnType("decimal(18,3)");
                        }
                        else if (property.Name.Contains("Hours"))
                        {
                            property.SetColumnType("decimal(5,2)");
                        }
                        else if (property.Name.Contains("Percentage") || property.Name.Contains("Margin"))
                        {
                            property.SetColumnType("decimal(5,2)");
                        }
                        else if (property.Name.Contains("Rate") || property.Name.Contains("Factor"))
                        {
                            property.SetColumnType("decimal(18,6)");
                        }
                    }
                }
            }
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // Seed Countries
            modelBuilder.Entity<Country>().HasData(
                new Country { Id = 1, NameAr = "مصر", NameEn = "Egypt", Code = "EG", PhoneCode = "+20", CurrencyCode = "EGP", CurrencyNameAr = "جنيه مصري", CurrencyNameEn = "Egyptian Pound", IsActive = true },
                new Country { Id = 2, NameAr = "السعودية", NameEn = "Saudi Arabia", Code = "SA", PhoneCode = "+966", CurrencyCode = "SAR", CurrencyNameAr = "ريال سعودي", CurrencyNameEn = "Saudi Riyal", IsActive = true },
                new Country { Id = 3, NameAr = "الإمارات", NameEn = "UAE", Code = "AE", PhoneCode = "+971", CurrencyCode = "AED", CurrencyNameAr = "درهم إماراتي", CurrencyNameEn = "UAE Dirham", IsActive = true }
            );

            // Seed Areas
            modelBuilder.Entity<Area>().HasData(
                new Area { Id = 1, NameAr = "القاهرة", NameEn = "Cairo", CountryId = 1, Code = "CAI", IsActive = true },
                new Area { Id = 2, NameAr = "الجيزة", NameEn = "Giza", CountryId = 1, Code = "GIZ", IsActive = true },
                new Area { Id = 3, NameAr = "الإسكندرية", NameEn = "Alexandria", CountryId = 1, Code = "ALX", IsActive = true },
                new Area { Id = 4, NameAr = "الرياض", NameEn = "Riyadh", CountryId = 2, Code = "RYD", IsActive = true },
                new Area { Id = 5, NameAr = "جدة", NameEn = "Jeddah", CountryId = 2, Code = "JED", IsActive = true }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, NameAr = "إلكترونيات", NameEn = "Electronics", Code = "ELEC", Level = 1, IsActive = true },
                new Category { Id = 2, NameAr = "ملابس", NameEn = "Clothing", Code = "CLOTH", Level = 1, IsActive = true },
                new Category { Id = 3, NameAr = "أجهزة كمبيوتر", NameEn = "Computers", Code = "COMP", Level = 1, IsActive = true },
                new Category { Id = 4, NameAr = "هواتف ذكية", NameEn = "Smartphones", Code = "PHONE", ParentCategoryId = 1, Level = 2, IsActive = true },
                new Category { Id = 5, NameAr = "لابتوب", NameEn = "Laptops", Code = "LAPTOP", ParentCategoryId = 3, Level = 2, IsActive = true }
            );

            // Seed Units
            modelBuilder.Entity<Unit>().HasData(
                new Unit { Id = 1, NameAr = "قطعة", NameEn = "Piece", Symbol = "PC", UnitType = 1, ConversionFactor = 1, IsActive = true, IsDefault = true },
                new Unit { Id = 2, NameAr = "كيلو", NameEn = "Kilogram", Symbol = "KG", UnitType = 2, ConversionFactor = 1, IsActive = true },
                new Unit { Id = 3, NameAr = "متر", NameEn = "Meter", Symbol = "M", UnitType = 3, ConversionFactor = 1, IsActive = true },
                new Unit { Id = 4, NameAr = "جرام", NameEn = "Gram", Symbol = "G", UnitType = 2, BaseUnitId = 2, ConversionFactor = 0.001m, IsActive = true },
                new Unit { Id = 5, NameAr = "سنتيمتر", NameEn = "Centimeter", Symbol = "CM", UnitType = 3, BaseUnitId = 3, ConversionFactor = 0.01m, IsActive = true }
            );

            // Seed Payment Methods
            modelBuilder.Entity<PaymentMethod>().HasData(
                new PaymentMethod { Id = 1, NameAr = "نقدي", NameEn = "Cash", Code = "CASH", PaymentType = 1, IsDefault = true, IsActive = true },
                new PaymentMethod { Id = 2, NameAr = "بطاقة ائتمان", NameEn = "Credit Card", Code = "CARD", PaymentType = 2, RequireReference = true, IsActive = true },
                new PaymentMethod { Id = 3, NameAr = "تحويل بنكي", NameEn = "Bank Transfer", Code = "BANK", PaymentType = 3, RequireReference = true, RequireApproval = true, IsActive = true },
                new PaymentMethod { Id = 4, NameAr = "شيك", NameEn = "Check", Code = "CHECK", PaymentType = 4, RequireReference = true, RequireApproval = true, IsActive = true }
            );

            // Seed Customer Types
            modelBuilder.Entity<CustomerType>().HasData(
                new CustomerType { Id = 1, NameAr = "عميل عادي", NameEn = "Regular Customer", DefaultDiscountPercentage = 0, DefaultCreditLimit = 0, IsActive = true },
                new CustomerType { Id = 2, NameAr = "عميل مميز", NameEn = "VIP Customer", DefaultDiscountPercentage = 5, DefaultCreditLimit = 10000, IsActive = true },
                new CustomerType { Id = 3, NameAr = "عميل جملة", NameEn = "Wholesale Customer", DefaultDiscountPercentage = 10, DefaultCreditLimit = 50000, IsActive = true }
            );

            // Seed Supplier Types
            modelBuilder.Entity<SupplierType>().HasData(
                new SupplierType { Id = 1, NameAr = "مورد محلي", NameEn = "Local Supplier", DefaultPaymentTerms = 30, IsActive = true },
                new SupplierType { Id = 2, NameAr = "مورد دولي", NameEn = "International Supplier", DefaultPaymentTerms = 60, IsActive = true },
                new SupplierType { Id = 3, NameAr = "مورد حكومي", NameEn = "Government Supplier", DefaultPaymentTerms = 90, IsActive = true }
            );

            // Seed Price Categories
            modelBuilder.Entity<PriceCategory>().HasData(
                new PriceCategory { Id = 1, NameAr = "سعر التجزئة", NameEn = "Retail Price", Code = "RETAIL", PriceAdjustmentPercentage = 0, IsDefault = true, IsActive = true },
                new PriceCategory { Id = 2, NameAr = "سعر الجملة", NameEn = "Wholesale Price", Code = "WHOLESALE", PriceAdjustmentPercentage = -10, MinimumQuantity = 10, IsActive = true },
                new PriceCategory { Id = 3, NameAr = "سعر العملاء المميزين", NameEn = "VIP Price", Code = "VIP", PriceAdjustmentPercentage = -5, IsActive = true }
            );

            // Seed Leave Types
            modelBuilder.Entity<LeaveType>().HasData(
                new LeaveType { Id = 1, NameAr = "إجازة سنوية", NameEn = "Annual Leave", MaxDaysPerYear = 21, IsPaid = true, RequireApproval = true, CanCarryForward = true, IsActive = true },
                new LeaveType { Id = 2, NameAr = "إجازة مرضية", NameEn = "Sick Leave", MaxDaysPerYear = 15, IsPaid = true, RequireApproval = false, CanCarryForward = false, IsActive = true },
                new LeaveType { Id = 3, NameAr = "إجازة طارئة", NameEn = "Emergency Leave", MaxDaysPerYear = 7, IsPaid = false, RequireApproval = true, CanCarryForward = false, IsActive = true },
                new LeaveType { Id = 4, NameAr = "إجازة أمومة", NameEn = "Maternity Leave", MaxDaysPerYear = 90, IsPaid = true, RequireApproval = true, CanCarryForward = false, IsActive = true }
            );

            // Seed Roles
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 1, NameAr = "مدير النظام", NameEn = "System Administrator", Description = "Full system access", IsSystemRole = true, IsActive = true },
                new Role { Id = 2, NameAr = "مدير الفرع", NameEn = "Branch Manager", Description = "Branch management access", IsActive = true },
                new Role { Id = 3, NameAr = "محاسب", NameEn = "Accountant", Description = "Financial management access", IsActive = true },
                new Role { Id = 4, NameAr = "أمين المخزن", NameEn = "Warehouse Keeper", Description = "Inventory management access", IsActive = true },
                new Role { Id = 5, NameAr = "موظف مبيعات", NameEn = "Sales Employee", Description = "Sales operations access", IsActive = true },
                new Role { Id = 6, NameAr = "موظف موارد بشرية", NameEn = "HR Employee", Description = "Human resources access", IsActive = true }
            );
        }
    }
}
