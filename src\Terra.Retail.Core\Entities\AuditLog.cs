using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// سجل المراجعة والتدقيق
    /// </summary>
    public class AuditLog : BaseEntity
    {
        /// <summary>
        /// معرف المستخدم الذي قام بالعملية
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        [MaxLength(100)]
        public string? Username { get; set; }

        /// <summary>
        /// نوع العملية
        /// </summary>
        public AuditAction Action { get; set; }

        /// <summary>
        /// اسم الجدول/الكيان
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// معرف السجل المتأثر
        /// </summary>
        public int? RecordId { get; set; }

        /// <summary>
        /// القيم القديمة (JSON)
        /// </summary>
        public string? OldValues { get; set; }

        /// <summary>
        /// القيم الجديدة (JSON)
        /// </summary>
        public string? NewValues { get; set; }

        /// <summary>
        /// الحقول المتغيرة
        /// </summary>
        [MaxLength(1000)]
        public string? ChangedFields { get; set; }

        /// <summary>
        /// وصف العملية
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// عنوان IP
        /// </summary>
        [MaxLength(45)]
        public string? IPAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح
        /// </summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// تاريخ ووقت العملية
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف الجلسة
        /// </summary>
        [MaxLength(255)]
        public string? SessionId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int? BranchId { get; set; }

        /// <summary>
        /// مدة تنفيذ العملية (بالميلي ثانية)
        /// </summary>
        public long? ExecutionTime { get; set; }

        /// <summary>
        /// حالة العملية
        /// </summary>
        public OperationStatus Status { get; set; } = OperationStatus.Success;

        /// <summary>
        /// رسالة الخطأ (إن وجدت)
        /// </summary>
        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// معلومات إضافية (JSON)
        /// </summary>
        public string? AdditionalInfo { get; set; }

        // Navigation Properties
        public virtual User? User { get; set; }
        public virtual Branch? Branch { get; set; }
    }

    /// <summary>
    /// أنواع عمليات المراجعة
    /// </summary>
    public enum AuditAction
    {
        /// <summary>
        /// إنشاء
        /// </summary>
        Create = 1,

        /// <summary>
        /// قراءة/عرض
        /// </summary>
        Read = 2,

        /// <summary>
        /// تعديل
        /// </summary>
        Update = 3,

        /// <summary>
        /// حذف
        /// </summary>
        Delete = 4,

        /// <summary>
        /// تسجيل دخول
        /// </summary>
        Login = 5,

        /// <summary>
        /// تسجيل خروج
        /// </summary>
        Logout = 6,

        /// <summary>
        /// تصدير
        /// </summary>
        Export = 7,

        /// <summary>
        /// طباعة
        /// </summary>
        Print = 8,

        /// <summary>
        /// موافقة
        /// </summary>
        Approve = 9,

        /// <summary>
        /// رفض
        /// </summary>
        Reject = 10,

        /// <summary>
        /// إلغاء
        /// </summary>
        Cancel = 11,

        /// <summary>
        /// استعادة
        /// </summary>
        Restore = 12
    }

    /// <summary>
    /// حالة العملية
    /// </summary>
    public enum OperationStatus
    {
        /// <summary>
        /// نجحت
        /// </summary>
        Success = 1,

        /// <summary>
        /// فشلت
        /// </summary>
        Failed = 2,

        /// <summary>
        /// تحذير
        /// </summary>
        Warning = 3
    }
}
