using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("CustomerTypes")]
    public class CustomerType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int DisplayOrder { get; set; } = 1;

        [StringLength(20)]
        public string? Color { get; set; }

        [StringLength(50)]
        public string? Icon { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }

    [Table("SupplierTypes")]
    public class SupplierType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int DisplayOrder { get; set; } = 1;

        [StringLength(20)]
        public string? Color { get; set; }

        [StringLength(50)]
        public string? Icon { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("Countries")]
    public class Country
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(10)]
        public string? Code { get; set; } // ISO Code

        [StringLength(10)]
        public string? PhoneCode { get; set; } // +20, +966

        [StringLength(10)]
        public string? Currency { get; set; } // EGP, SAR

        [StringLength(50)]
        public string? Flag { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<City> Cities { get; set; } = new List<City>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("Cities")]
    public class City
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int CountryId { get; set; }

        [StringLength(20)]
        public string? PostalCode { get; set; }

        [StringLength(20)]
        public string? AreaCode { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Country Country { get; set; } = null!;
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("PriceCategories")]
    public class PriceCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal MarkupPercentage { get; set; } = 0;

        public int DisplayOrder { get; set; } = 1;

        [StringLength(20)]
        public string? Color { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<ProductPrice> ProductPrices { get; set; } = new List<ProductPrice>();
    }

    [Table("ProductPrices")]
    public class ProductPrice
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        public int PriceCategoryId { get; set; }

        public int? BranchId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? DiscountPercentage { get; set; }

        public DateTime EffectiveDate { get; set; } = DateTime.Now;

        public DateTime? ExpiryDate { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual PriceCategory PriceCategory { get; set; } = null!;
        public virtual Branch? Branch { get; set; }
    }

    [Table("Taxes")]
    public class Tax
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal Percentage { get; set; }

        public int TaxType { get; set; } = 1; // 1=VAT, 2=Sales Tax, 3=Service Tax

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsDefault { get; set; } = false;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    [Table("Discounts")]
    public class Discount
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        public int DiscountType { get; set; } = 1; // 1=Percentage, 2=Fixed Amount

        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinimumAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaximumDiscount { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? MaxUsageCount { get; set; }

        public int CurrentUsageCount { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }
    }

    [Table("WorkRegulations")]
    public class WorkRegulation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int RegulationType { get; set; } = 1; // 1=General, 2=Attendance, 3=Leave, 4=Overtime

        [StringLength(2000)]
        public string? RegulationText { get; set; }

        public int DisplayOrder { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }
    }



    [Table("ReferralSources")]
    public class ReferralSource
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public int DisplayOrder { get; set; } = 1;

        [StringLength(50)]
        public string? Color { get; set; }

        [StringLength(50)]
        public string? Icon { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }

    [Table("Counters")]
    public class Counter
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string CounterName { get; set; } = string.Empty;

        [StringLength(10)]
        public string? Prefix { get; set; }

        public int CurrentValue { get; set; } = 0;

        public int NumberLength { get; set; } = 6;

        public int? BranchId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Branch? Branch { get; set; }
    }

    [Table("Branches")]
    public class Branch
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Email { get; set; }

        public bool IsMainBranch { get; set; } = false;

        public int? DefaultPriceCategoryId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual PriceCategory? DefaultPriceCategory { get; set; }
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Counter> Counters { get; set; } = new List<Counter>();
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
    }

    [Table("PaymentMethods")]
    public class PaymentMethod
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public int PaymentType { get; set; } // 1=Cash, 2=Card, 3=Bank, 4=Check, 5=Online

        public int? AccountId { get; set; }

        public bool IsDefault { get; set; } = false;

        public int DisplayOrder { get; set; } = 1;

        [StringLength(50)]
        public string? Color { get; set; }

        [StringLength(50)]
        public string? Icon { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ChartOfAccount? Account { get; set; }
        public virtual ICollection<Receipt> Receipts { get; set; } = new List<Receipt>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
