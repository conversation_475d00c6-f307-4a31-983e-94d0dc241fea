using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// جدول العملاء
    /// </summary>
    public class Customer : BaseEntity
    {
        /// <summary>
        /// كود العميل (فريد)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string CustomerCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم العميل بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم العميل بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// نوع العميل
        /// </summary>
        public int CustomerTypeId { get; set; }

        /// <summary>
        /// المنطقة
        /// </summary>
        public int? AreaId { get; set; }

        /// <summary>
        /// الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// فئة السعر
        /// </summary>
        public int? PriceCategoryId { get; set; }

        /// <summary>
        /// رقم الهاتف الرئيسي
        /// </summary>
        [MaxLength(20)]
        public string? Phone1 { get; set; }

        /// <summary>
        /// رقم الهاتف الإضافي
        /// </summary>
        [MaxLength(20)]
        public string? Phone2 { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// نسبة الخصم (%)
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// الحد الائتماني
        /// </summary>
        public decimal CreditLimit { get; set; } = 0;

        /// <summary>
        /// هل العميل نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ آخر معاملة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        /// <summary>
        /// رقم الهوية/السجل التجاري
        /// </summary>
        [MaxLength(50)]
        public string? IdentityNumber { get; set; }

        /// <summary>
        /// الرقم الضريبي
        /// </summary>
        [MaxLength(50)]
        public string? TaxNumber { get; set; }

        // Navigation Properties
        public virtual CustomerType CustomerType { get; set; } = null!;
        public virtual Area? Area { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual PriceCategory? PriceCategory { get; set; }
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<CustomerTransaction> Transactions { get; set; } = new List<CustomerTransaction>();
    }
}
