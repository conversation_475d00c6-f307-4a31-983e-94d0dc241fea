using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<TestController> _logger;

        public TestController(TerraRetailDbContext context, ILogger<TestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        [HttpGet("database")]
        public async Task<ActionResult> TestDatabase()
        {
            try
            {
                var canConnect = await _context.Database.CanConnectAsync();
                return Ok(new { 
                    canConnect = canConnect,
                    message = canConnect ? "الاتصال بقاعدة البيانات ناجح" : "فشل الاتصال بقاعدة البيانات"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في الاتصال", error = ex.Message });
            }
        }

        /// <summary>
        /// اختبار المحافظات العربية
        /// </summary>
        [HttpGet("areas")]
        public async Task<ActionResult> TestAreas()
        {
            try
            {
                var areas = await _context.Areas
                    .Where(a => a.IsActive)
                    .Take(5)
                    .Select(a => new { 
                        id = a.Id, 
                        nameAr = a.NameAr, 
                        nameEn = a.NameEn 
                    })
                    .ToListAsync();

                return Ok(new { 
                    count = areas.Count,
                    areas = areas,
                    message = "تم تحميل المحافظات بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل المحافظات", error = ex.Message });
            }
        }

        /// <summary>
        /// اختبار الفروع العربية
        /// </summary>
        [HttpGet("branches")]
        public async Task<ActionResult> TestBranches()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .Select(b => new { 
                        id = b.Id, 
                        nameAr = b.NameAr, 
                        nameEn = b.NameEn,
                        isMainBranch = b.IsMainBranch
                    })
                    .ToListAsync();

                return Ok(new { 
                    count = branches.Count,
                    branches = branches,
                    message = "تم تحميل الفروع بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل الفروع", error = ex.Message });
            }
        }

        /// <summary>
        /// اختبار العملاء العرب
        /// </summary>
        [HttpGet("customers")]
        public async Task<ActionResult> TestCustomers()
        {
            try
            {
                var customers = await _context.Customers
                    .Where(c => c.IsActive)
                    .Take(5)
                    .Select(c => new { 
                        id = c.Id, 
                        nameAr = c.NameAr, 
                        customerCode = c.CustomerCode,
                        phone = c.Phone1
                    })
                    .ToListAsync();

                return Ok(new { 
                    count = customers.Count,
                    customers = customers,
                    message = "تم تحميل العملاء بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل العملاء", error = ex.Message });
            }
        }

        /// <summary>
        /// اختبار أنواع العملاء
        /// </summary>
        [HttpGet("customer-types")]
        public async Task<ActionResult> TestCustomerTypes()
        {
            try
            {
                var customerTypes = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .Select(ct => new { 
                        id = ct.Id, 
                        nameAr = ct.NameAr, 
                        nameEn = ct.NameEn
                    })
                    .ToListAsync();

                return Ok(new { 
                    count = customerTypes.Count,
                    customerTypes = customerTypes,
                    message = "تم تحميل أنواع العملاء بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل أنواع العملاء", error = ex.Message });
            }
        }

        /// <summary>
        /// اختبار شامل للبيانات العربية
        /// </summary>
        [HttpGet("arabic-data")]
        public async Task<ActionResult> TestArabicData()
        {
            try
            {
                var result = new
                {
                    areas = await _context.Areas.CountAsync(a => a.IsActive),
                    branches = await _context.Branches.CountAsync(b => b.IsActive),
                    customers = await _context.Customers.CountAsync(c => c.IsActive),
                    customerTypes = await _context.CustomerTypes.CountAsync(ct => ct.IsActive),
                    products = await _context.Products.CountAsync(p => p.IsActive),
                    sampleArea = await _context.Areas.Where(a => a.IsActive).Select(a => a.NameAr).FirstOrDefaultAsync(),
                    sampleBranch = await _context.Branches.Where(b => b.IsActive).Select(b => b.NameAr).FirstOrDefaultAsync(),
                    sampleCustomer = await _context.Customers.Where(c => c.IsActive).Select(c => c.NameAr).FirstOrDefaultAsync(),
                    message = "تم تحميل جميع البيانات العربية بنجاح"
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل البيانات", error = ex.Message });
            }
        }
    }
}
