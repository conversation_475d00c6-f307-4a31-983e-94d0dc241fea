import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';

// Interfaces
interface Country {
  Id: number;
  NameAr: string;
  NameEn: string;
  Code: string;
  PhoneCode?: string;
  IsActive: boolean;
  CreatedAt?: string;
  UpdatedAt?: string;
}

@Component({
  selector: 'app-countries',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCheckboxModule
  ],
  templateUrl: './countries.component.html',
  styleUrls: ['./countries.component.scss']
})
export class CountriesComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  showAddForm = false;
  editingCountry: Country | null = null;
  
  // Data
  countries: Country[] = [];
  
  // Form
  countryForm: FormGroup;
  
  // Table Configuration
  displayedColumns: string[] = ['nameAr', 'nameEn', 'code', 'phoneCode', 'isActive', 'actions'];
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private router: Router,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.countryForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadCountries();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required, Validators.minLength(2)]],
      code: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(5)]],
      phoneCode: ['', [Validators.pattern(/^\+\d{1,4}$/)]],
      isActive: [true]
    });
  }

  /**
   * Load countries
   */
  loadCountries(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>('http://localhost:5127/api/simple/countries').subscribe({
      next: (response) => {
        console.log('Countries response:', response);
        this.countries = response.countries || [];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        this.showError('خطأ في تحميل البلدان');
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Show add form
   */
  showAddCountry(): void {
    this.showAddForm = true;
    this.editingCountry = null;
    this.countryForm.reset();
    this.countryForm.patchValue({ isActive: true });
  }

  /**
   * Edit country
   */
  editCountry(country: Country): void {
    this.showAddForm = true;
    this.editingCountry = country;
    this.countryForm.patchValue({
      nameAr: country.NameAr,
      nameEn: country.NameEn,
      code: country.Code,
      phoneCode: country.PhoneCode || '',
      isActive: country.IsActive
    });
  }

  /**
   * Save country
   */
  saveCountry(): void {
    if (this.countryForm.valid) {
      this.isLoading = true;
      
      const formValue = this.countryForm.value;
      const request = {
        nameAr: formValue.nameAr,
        nameEn: formValue.nameEn,
        code: formValue.code.toUpperCase(),
        phoneCode: formValue.phoneCode || null,
        isActive: formValue.isActive
      };

      const apiCall = this.editingCountry 
        ? this.http.put<any>(`http://localhost:5127/api/simple/countries/${this.editingCountry.Id}`, request)
        : this.http.post<any>('http://localhost:5127/api/simple/countries', request);

      const sub = apiCall.subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccess(this.editingCountry ? 'تم تحديث البلد بنجاح' : 'تم إضافة البلد بنجاح');
          this.showAddForm = false;
          this.loadCountries();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error saving country:', error);
          this.showError('خطأ في حفظ البلد');
        }
      });
      this.subscriptions.push(sub);
    } else {
      this.markFormGroupTouched();
      this.showError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  /**
   * Delete country
   */
  deleteCountry(country: Country): void {
    if (confirm(`هل أنت متأكد من حذف البلد "${country.NameAr}"؟`)) {
      this.isLoading = true;

      const sub = this.http.delete(`http://localhost:5127/api/simple/countries/${country.Id}`).subscribe({
        next: (response: any) => {
          this.isLoading = false;
          this.showSuccess('تم حذف البلد بنجاح');
          this.loadCountries();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error deleting country:', error);
          this.showError('خطأ في حذف البلد');
        }
      });
      this.subscriptions.push(sub);
    }
  }

  /**
   * Cancel form
   */
  cancelForm(): void {
    this.showAddForm = false;
    this.editingCountry = null;
    this.countryForm.reset();
  }

  /**
   * Go back to suppliers
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.countryForm.controls).forEach(key => {
      const control = this.countryForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
