.accounting-report-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  direction: rtl;

  .report-header {
    margin-bottom: 24px;

    .header-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 600;

        mat-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }
      }

      mat-card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        margin-top: 8px;
        font-size: 14px;
      }

      mat-card-actions {
        padding-top: 16px;

        button {
          margin-left: 12px;
          
          mat-icon {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    mat-spinner {
      margin-bottom: 20px;
    }

    p {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .report-content {
    .report-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      ::ng-deep .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: 1px solid #e0e0e0;
        }

        .mat-mdc-tab-label {
          font-weight: 500;
          font-size: 14px;
        }

        .mat-mdc-tab-body-wrapper {
          padding: 0;
        }
      }

      .tab-content {
        padding: 24px;

        mat-card {
          box-shadow: none;
          border: 1px solid #e0e0e0;
          border-radius: 8px;

          mat-card-header {
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;

            mat-card-title {
              font-size: 18px;
              font-weight: 600;
              color: #333;
            }
          }

          mat-card-content {
            padding-top: 20px;
          }
        }
      }
    }

    .table-container {
      overflow-x: auto;
      border-radius: 8px;
      border: 1px solid #e0e0e0;

      table {
        width: 100%;
        background: white;

        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          text-align: right;
          border-bottom: 2px solid #e0e0e0;
        }

        td {
          padding: 12px;
          border-bottom: 1px solid #f0f0f0;
          text-align: right;

          &.amount-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
            text-align: left;
            direction: ltr;
          }

          &.count-cell {
            text-align: center;
          }

          &.notes-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        tr:hover {
          background-color: #f8f9fa;
        }

        tr:nth-child(even) {
          background-color: #fafafa;
        }
      }
    }

    // Specific table styles
    .summary-table {
      th, td {
        font-size: 14px;
      }

      .mat-mdc-chip {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .accounts-table {
      th, td {
        font-size: 13px;
      }

      td:first-child {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #2196f3;
      }
    }

    .transactions-table {
      th, td {
        font-size: 12px;
      }

      td:first-child {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #4caf50;
      }
    }

    .types-table {
      th, td {
        font-size: 14px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .accounting-report-container {
    padding: 12px;

    .report-header .header-card {
      mat-card-title {
        font-size: 20px;
      }

      mat-card-actions {
        button {
          margin-left: 8px;
          padding: 8px 16px;
        }
      }
    }

    .report-content .tab-content {
      padding: 16px;
    }

    .table-container {
      font-size: 12px;

      th, td {
        padding: 8px 6px;
      }
    }
  }
}

// Snackbar styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }

  .info-snackbar {
    background-color: #2196f3 !important;
    color: white !important;
  }
}

// Print styles
@media print {
  .accounting-report-container {
    .report-header mat-card-actions {
      display: none;
    }

    .report-tabs ::ng-deep .mat-mdc-tab-header {
      display: none;
    }

    .table-container {
      border: none;
      
      table {
        font-size: 10px;
        
        th, td {
          padding: 4px;
        }
      }
    }
  }
}
