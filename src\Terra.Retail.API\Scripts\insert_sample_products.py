#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
import sys
import random

def get_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=localhost;"
            "DATABASE=TerraRetailERP;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "Encrypt=no;"
        )
        return pyodbc.connect(connection_string)
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def get_suppliers(cursor):
    """الحصول على قائمة الموردين"""
    cursor.execute("SELECT Id, NameAr FROM Suppliers WHERE IsActive = 1")
    return cursor.fetchall()

def insert_sample_products(cursor):
    """إدراج أصناف مبدئية مربوطة بالموردين"""
    print("📦 إدراج أصناف مبدئية...")
    
    # الحصول على الموردين
    suppliers = get_suppliers(cursor)
    if not suppliers:
        print("❌ لا توجد موردين في قاعدة البيانات")
        return
    
    print(f"✅ تم العثور على {len(suppliers)} مورد")
    
    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM Products")
    
    # أصناف مبدئية
    products = [
        # منتجات غذائية
        {
            'nameAr': 'أرز أبيض مصري',
            'nameEn': 'Egyptian White Rice',
            'code': '2000000000001',
            'barcode': '6221234567890',
            'categoryAr': 'حبوب ومواد غذائية',
            'categoryEn': 'Grains & Food',
            'unitAr': 'كيلو',
            'unitEn': 'KG',
            'costPrice': 15.50,
            'sellingPrice': 18.00,
            'minStock': 100,
            'maxStock': 1000
        },
        {
            'nameAr': 'زيت عباد الشمس',
            'nameEn': 'Sunflower Oil',
            'code': '2000000000002',
            'barcode': '6221234567891',
            'categoryAr': 'زيوت ودهون',
            'categoryEn': 'Oils & Fats',
            'unitAr': 'لتر',
            'unitEn': 'Liter',
            'costPrice': 45.00,
            'sellingPrice': 52.00,
            'minStock': 50,
            'maxStock': 500
        },
        {
            'nameAr': 'سكر أبيض',
            'nameEn': 'White Sugar',
            'code': '2000000000003',
            'barcode': '6221234567892',
            'categoryAr': 'حبوب ومواد غذائية',
            'categoryEn': 'Grains & Food',
            'unitAr': 'كيلو',
            'unitEn': 'KG',
            'costPrice': 12.00,
            'sellingPrice': 14.50,
            'minStock': 200,
            'maxStock': 2000
        },
        {
            'nameAr': 'دقيق أبيض',
            'nameEn': 'White Flour',
            'code': '2000000000004',
            'barcode': '6221234567893',
            'categoryAr': 'حبوب ومواد غذائية',
            'categoryEn': 'Grains & Food',
            'unitAr': 'كيلو',
            'unitEn': 'KG',
            'costPrice': 8.50,
            'sellingPrice': 10.00,
            'minStock': 150,
            'maxStock': 1500
        },
        {
            'nameAr': 'شاي أحمر',
            'nameEn': 'Black Tea',
            'code': '2000000000005',
            'barcode': '6221234567894',
            'categoryAr': 'مشروبات',
            'categoryEn': 'Beverages',
            'unitAr': 'علبة',
            'unitEn': 'Box',
            'costPrice': 25.00,
            'sellingPrice': 30.00,
            'minStock': 30,
            'maxStock': 300
        },
        # منتجات تنظيف
        {
            'nameAr': 'مسحوق غسيل',
            'nameEn': 'Washing Powder',
            'code': '2000000000006',
            'barcode': '6221234567895',
            'categoryAr': 'منظفات',
            'categoryEn': 'Cleaning Products',
            'unitAr': 'كيلو',
            'unitEn': 'KG',
            'costPrice': 35.00,
            'sellingPrice': 42.00,
            'minStock': 25,
            'maxStock': 250
        },
        {
            'nameAr': 'صابون سائل',
            'nameEn': 'Liquid Soap',
            'code': '2000000000007',
            'barcode': '6221234567896',
            'categoryAr': 'منظفات',
            'categoryEn': 'Cleaning Products',
            'unitAr': 'لتر',
            'unitEn': 'Liter',
            'costPrice': 18.00,
            'sellingPrice': 22.00,
            'minStock': 40,
            'maxStock': 400
        },
        # منتجات شخصية
        {
            'nameAr': 'شامبو للشعر',
            'nameEn': 'Hair Shampoo',
            'code': '2000000000008',
            'barcode': '6221234567897',
            'categoryAr': 'منتجات شخصية',
            'categoryEn': 'Personal Care',
            'unitAr': 'زجاجة',
            'unitEn': 'Bottle',
            'costPrice': 28.00,
            'sellingPrice': 35.00,
            'minStock': 20,
            'maxStock': 200
        },
        {
            'nameAr': 'معجون أسنان',
            'nameEn': 'Toothpaste',
            'code': '2000000000009',
            'barcode': '6221234567898',
            'categoryAr': 'منتجات شخصية',
            'categoryEn': 'Personal Care',
            'unitAr': 'أنبوب',
            'unitEn': 'Tube',
            'costPrice': 15.00,
            'sellingPrice': 18.50,
            'minStock': 50,
            'maxStock': 500
        },
        # منتجات ألبان
        {
            'nameAr': 'لبن طازج',
            'nameEn': 'Fresh Milk',
            'code': '2000000000010',
            'barcode': '6221234567899',
            'categoryAr': 'ألبان ومنتجاتها',
            'categoryEn': 'Dairy Products',
            'unitAr': 'لتر',
            'unitEn': 'Liter',
            'costPrice': 12.00,
            'sellingPrice': 15.00,
            'minStock': 30,
            'maxStock': 300
        }
    ]
    
    # إدراج المنتجات مع ربطها بموردين عشوائيين
    for i, product in enumerate(products):
        # اختيار مورد عشوائي
        supplier = random.choice(suppliers)
        supplier_id = supplier[0]
        supplier_name = supplier[1]
        
        cursor.execute("""
            INSERT INTO Products (
                ProductCode, NameAr, NameEn, Barcode, CategoryAr, CategoryEn,
                UnitAr, UnitEn, CostPrice, SellingPrice, MinStock, MaxStock,
                SupplierId, CurrentStock, IsActive, CreatedAt
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
        """, (
            product['code'],
            product['nameAr'],
            product['nameEn'],
            product['barcode'],
            product['categoryAr'],
            product['categoryEn'],
            product['unitAr'],
            product['unitEn'],
            product['costPrice'],
            product['sellingPrice'],
            product['minStock'],
            product['maxStock'],
            supplier_id,
            random.randint(product['minStock'], product['maxStock']),  # مخزون حالي عشوائي
            True
        ))
        
        print(f"✅ تم إدراج المنتج: {product['nameAr']} - مورد: {supplier_name}")
    
    print(f"✅ تم إدراج {len(products)} منتج بنجاح")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إدراج أصناف مبدئية مربوطة بالموردين...")
    
    connection = get_connection()
    if not connection:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        sys.exit(1)
    
    try:
        cursor = connection.cursor()
        
        # إدراج الأصناف
        insert_sample_products(cursor)
        
        # حفظ التغييرات
        connection.commit()
        print("✅ تم حفظ جميع البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ أثناء إدراج البيانات: {e}")
        connection.rollback()
        sys.exit(1)
    
    finally:
        connection.close()
        print("🔒 تم إغلاق الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    main()
