import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

interface Customer {
  id?: number;
  customerCode?: string;
  nameAr: string;
  nameEn: string;
  phone1: string;
  phone2?: string;
  email?: string;
  address?: string;
  customerTypeId: number;
  priceCategoryId: number;
  creditLimit?: number;
  openingBalance?: number;
  branchId?: number;
  referralSource: string;
  notes?: string;
  isActive: boolean;
}

interface CustomerType {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface PriceCategory {
  id: number;
  nameAr: string;
  nameEn: string;
  discountPercentage: number;
}

interface Branch {
  id: number;
  nameAr: string;
  nameEn: string;
  isMainBranch: boolean;
  defaultPriceCategoryId?: number;
}

interface Country {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
}

interface ReferralSource {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface City {
  id: number;
  nameAr: string;
  nameEn: string;
  countryId: number;
}

@Component({
  selector: 'app-customer-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './customer-form.component.html',
  styleUrls: ['./customer-form.component.scss']
})
export class CustomerFormComponent implements OnInit {
  customerForm: FormGroup;
  editMode = false;
  customerId: number | null = null;
  isSubmitting = false;

  // Lookup data
  customerTypes: CustomerType[] = [];
  priceCategories: PriceCategory[] = [];
  branches: Branch[] = [];
  countries: Country[] = [];
  cities: City[] = [];
  filteredCities: City[] = [];
  referralSources: ReferralSource[] = [];

  // Current selections
  selectedCountryId: number | null = null;
  currentUserBranchId: number = 1; // سيتم جلبه من الجلسة
  nextCustomerCode: string = '';

  private apiUrl = 'http://localhost:5233/api';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient
  ) {
    this.customerForm = this.createForm();
  }

  ngOnInit() {
    this.loadLookupData();
    this.loadCountries();
    this.loadCities();
    this.checkEditMode();

    // حجز الكود التلقائي عند فتح الصفحة
    if (!this.editMode) {
      this.reserveNextCustomerCode();
    }

    // تعيين القيم الافتراضية
    setTimeout(() => {
      this.setDefaultPriceCategory();
      this.selectedCountryId = 1; // مصر افتراضياً
      this.filterCitiesByCountry();
    }, 1000);
  }

  createForm(): FormGroup {
    return this.fb.group({
      customerCode: [''],
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required, Validators.minLength(2)]],
      phone1: ['', [Validators.required, Validators.pattern(/^01[0-9]{9}$/)]],
      phone2: ['', [Validators.pattern(/^01[0-9]{9}$/)]],
      email: ['', [Validators.email]],
      address: [''],
      countryId: [1], // مصر افتراضياً
      cityId: [''],
      customerTypeId: ['', Validators.required],
      priceCategoryId: ['', Validators.required],
      creditLimit: [0, [Validators.min(0)]],
      openingBalance: [0],
      branchId: [this.currentUserBranchId], // الفرع الحالي للمستخدم
      referralSource: ['', Validators.required],
      notes: [''],
      isActive: [true]
    });
  }

  // حجز الكود التلقائي من الكونتر وإظهاره فوراً
  reserveNextCustomerCode() {
    this.http.get<any>(`${this.apiUrl}/customers/next-code`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.nextCustomerCode = response.data.customerCode;
          this.customerForm.patchValue({
            customerCode: this.nextCustomerCode
          });
          console.log('تم حجز كود العميل وإظهاره:', this.nextCustomerCode);
        } else {
          console.error('Failed to reserve customer code:', response.message);
          // إنشاء كود مؤقت
          this.generateTempCustomerCode();
        }
      },
      error: (error) => {
        console.error('Error reserving customer code:', error);
        // إنشاء كود مؤقت في حالة الخطأ
        this.generateTempCustomerCode();
      }
    });
  }

  // إنشاء كود مؤقت للعرض
  generateTempCustomerCode() {
    const timestamp = Date.now().toString().slice(-4);
    this.nextCustomerCode = `CUS${timestamp}`;
    this.customerForm.patchValue({
      customerCode: this.nextCustomerCode + ' (مؤقت)'
    });
    console.log('تم إنشاء كود مؤقت:', this.nextCustomerCode);
  }

  loadLookupData() {
    // Load customer types
    this.http.get<any>(`${this.apiUrl}/lookup/customer-types`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customerTypes = response.data;
        } else {
          console.error('Failed to load customer types:', response.message);
          alert('فشل في تحميل أنواع العملاء. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading customer types:', error);
        alert('حدث خطأ في تحميل أنواع العملاء. يرجى التحقق من الاتصال بالخادم.');
      }
    });

    // Load price categories
    this.http.get<any>(`${this.apiUrl}/lookup/price-categories`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.priceCategories = response.data;
        } else {
          console.error('Failed to load price categories:', response.message);
          alert('فشل في تحميل فئات الأسعار. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading price categories:', error);
        alert('حدث خطأ في تحميل فئات الأسعار. يرجى التحقق من الاتصال بالخادم.');
      }
    });

    // Load branches
    this.http.get<any>(`${this.apiUrl}/lookup/branches`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.branches = response.data;
        } else {
          console.error('Failed to load branches:', response.message);
          alert('فشل في تحميل الفروع. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading branches:', error);
        alert('حدث خطأ في تحميل الفروع. يرجى التحقق من الاتصال بالخادم.');
      }
    });

    // Load referral sources
    this.http.get<any>(`${this.apiUrl}/lookup/referral-sources`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.referralSources = response.data;
        } else {
          console.error('Failed to load referral sources:', response.message);
          alert('فشل في تحميل مصادر التعرف. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading referral sources:', error);
        alert('حدث خطأ في تحميل مصادر التعرف. يرجى التحقق من الاتصال بالخادم.');
      }
    });
  }

  loadCountries() {
    this.http.get<any>(`${this.apiUrl}/lookup/countries`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.countries = response.data;
        } else {
          console.error('Failed to load countries:', response.message);
          alert('فشل في تحميل البلدان. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        alert('حدث خطأ في تحميل البلدان. يرجى التحقق من الاتصال بالخادم.');
      }
    });
  }

  loadCities() {
    this.http.get<any>(`${this.apiUrl}/lookup/cities`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.cities = response.data;
          this.filterCitiesByCountry(); // تصفية المدن حسب البلد المختار
        } else {
          console.error('Failed to load cities:', response.message);
          alert('فشل في تحميل المدن. يرجى المحاولة مرة أخرى.');
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        alert('حدث خطأ في تحميل المدن. يرجى التحقق من الاتصال بالخادم.');
      }
    });
  }

  onCountryChange() {
    const countryId = this.customerForm.get('countryId')?.value;
    this.selectedCountryId = countryId ? +countryId : null;
    this.customerForm.patchValue({ cityId: '' }); // إعادة تعيين المدينة
    this.filterCitiesByCountry();
  }

  filterCitiesByCountry() {
    if (this.selectedCountryId) {
      this.filteredCities = this.cities.filter(city => city.countryId === this.selectedCountryId);
    } else {
      this.filteredCities = [];
    }
  }

  setDefaultPriceCategory() {
    // تعيين فئة السعر الافتراضية بناءً على الفرع
    const currentBranch = this.branches.find(b => b.id === this.currentUserBranchId);
    if (currentBranch && currentBranch.defaultPriceCategoryId) {
      this.customerForm.patchValue({
        priceCategoryId: currentBranch.defaultPriceCategoryId
      });
    }
  }

  checkEditMode() {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.editMode = true;
        this.customerId = +params['id'];
        this.loadCustomer(this.customerId);
      }
    });
  }

  loadCustomer(id: number) {
    this.http.get<any>(`${this.apiUrl}/customers/${id}`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customerForm.patchValue(response.data);
        }
      },
      error: (error) => {
        console.error('Error loading customer:', error);
        alert('حدث خطأ في تحميل بيانات العميل');
        this.goBack();
      }
    });
  }

  // تحويل الاسم العربي إلى إنجليزي تلقائياً
  onArabicNameChange(event: any) {
    const arabicName = event.target.value;
    console.log('Arabic name input:', arabicName);

    if (arabicName && arabicName.trim() !== '') {
      const englishName = this.translateArabicToEnglish(arabicName);
      console.log('Translated to English:', englishName);

      this.customerForm.patchValue({
        nameEn: englishName
      });

      // إجبار التحديث
      this.customerForm.get('nameEn')?.updateValueAndValidity();
    } else {
      this.customerForm.patchValue({
        nameEn: ''
      });
    }
  }

  // دالة تحويل الأسماء العربية إلى إنجليزية محسنة
  translateArabicToEnglish(arabicText: string): string {
    if (!arabicText || arabicText.trim() === '') {
      return '';
    }

    // تنظيف النص من المسافات الزائدة
    let cleanText = arabicText.trim().replace(/\s+/g, ' ');

    // قاموس شامل للأسماء العربية والإنجليزية
    const arabicToEnglishMap: { [key: string]: string } = {
      // الأسماء المركبة مع "عبد" - يجب أن تكون أولاً
      'عبد الله': 'Abdullah',
      'عبد الرحمن': 'Abdulrahman',
      'عبد الرحيم': 'Abdulrahim',
      'عبد العزيز': 'Abdulaziz',
      'عبد الكريم': 'Abdulkarim',
      'عبد الحميد': 'Abdulhamid',
      'عبد المجيد': 'Abdulmajid',
      'عبد الناصر': 'Abdulnasser',
      'عبد الفتاح': 'Abdulfatah',
      'عبد الحليم': 'Abdulhalim',
      'عبد السلام': 'Abdulsalam',
      'عبد الوهاب': 'Abdulwahab',
      'عبد الغني': 'Abdulghani',
      'عبد الباسط': 'Abdulbasit',
      'عبد الرؤوف': 'Abdulraouf',
      'عبد المنعم': 'Abdulmonem',
      'عبد الصبور': 'Abdulsabour',
      'عبد الستار': 'Abdulsattar',
      'عبد الجليل': 'Abduljalil',
      'عبد الحكيم': 'Abdulhakim',
      'عبد الخالق': 'Abdulkhaliq',
      'عبد الرازق': 'Abdulraziq',
      'عبد الصمد': 'Abdulsamad',
      'عبد العال': 'Abdulaal',
      'عبد القادر': 'Abdulqader',
      'عبد المالك': 'Abdulmalik',
      'عبد المؤمن': 'Abdulmoamen',
      'عبد الهادي': 'Abdulhadi',

      // أسماء الأنبياء والصحابة
      'محمد': 'Mohamed',
      'أحمد': 'Ahmed',
      'إبراهيم': 'Ibrahim',
      'إسماعيل': 'Ismail',
      'يوسف': 'Youssef',
      'موسى': 'Musa',
      'عيسى': 'Issa',
      'يحيى': 'Yahya',
      'زكريا': 'Zakaria',
      'داود': 'Dawood',
      'سليمان': 'Soliman',
      'أيوب': 'Ayoub',
      'يونس': 'Younes',
      'هارون': 'Haroun',
      'إلياس': 'Elias',
      'اليسع': 'Elyasa',
      'ذو الكفل': 'Zulkifl',
      'إدريس': 'Idris',
      'نوح': 'Noah',
      'هود': 'Houd',
      'صالح': 'Saleh',
      'شعيب': 'Shoaib',
      'لوط': 'Lout',

      // أسماء الصحابة
      'علي': 'Ali',
      'عمر': 'Omar',
      'عثمان': 'Othman',
      'أبو بكر': 'Abu Bakr',
      'حسن': 'Hassan',
      'حسين': 'Hussein',
      'طلحة': 'Talha',
      'الزبير': 'Alzubair',
      'سعد': 'Saad',
      'سعيد': 'Said',
      'أبو عبيدة': 'Abu Obaida',
      'معاذ': 'Moaaz',
      'أسامة': 'Osama',
      'خالد': 'Khaled',
      'عمرو': 'Amr',
      'بلال': 'Bilal',
      'صهيب': 'Sohaib',
      'سلمان': 'Salman',

      // أسماء النساء في الإسلام
      'فاطمة': 'Fatma',
      'عائشة': 'Aisha',
      'خديجة': 'Khadija',
      'زينب': 'Zeinab',
      'رقية': 'Ruqaya',
      'أم كلثوم': 'Um Kulthum',
      'حفصة': 'Hafsa',
      'سودة': 'Sawda',
      'جويرية': 'Juwayriya',
      'صفية': 'Safiya',
      'ميمونة': 'Maymuna',
      'أم سلمة': 'Um Salama',
      'أم حبيبة': 'Um Habiba',
      'مارية': 'Maria',

      // أسماء عربية حديثة - رجال
      'كريم': 'Karim',
      'طارق': 'Tarek',
      'وليد': 'Walid',
      'سامي': 'Sami',
      'رامي': 'Rami',
      'هاني': 'Hani',
      'مصطفى': 'Mostafa',
      'محمود': 'Mahmoud',
      'حمدي': 'Hamdy',
      'عماد': 'Emad',
      'هشام': 'Hisham',
      'وائل': 'Wael',
      'أيمن': 'Ayman',
      'شريف': 'Sherif',
      'عصام': 'Essam',
      'جمال': 'Gamal',
      'كمال': 'Kamal',
      'فؤاد': 'Fouad',
      'رضا': 'Reda',
      'عادل': 'Adel',
      'ماجد': 'Majed',
      'فريد': 'Farid',
      'سمير': 'Samir',
      'أمير': 'Amir',
      'باسم': 'Basem',
      'تامر': 'Tamer',
      'حازم': 'Hazem',
      'راشد': 'Rashed',
      'فارس': 'Fares',
      'ماهر': 'Maher',
      'ناصر': 'Nasser',
      'وسام': 'Wessam',
      'حاتم': 'Hatem',
      'ياسر': 'Yasser',
      'نبيل': 'Nabil',
      'عاطف': 'Atef',
      'منير': 'Mounir',
      'أنور': 'Anwar',
      'فتحي': 'Fathy',
      'مجدي': 'Magdy',
      'شوقي': 'Shawky',
      'فوزي': 'Fawzy',
      'رفعت': 'Refaat',
      'صبري': 'Sabry',
      'حلمي': 'Helmy',

      // أسماء عربية حديثة - نساء
      'مريم': 'Mariam',
      'سارة': 'Sara',
      'ليلى': 'Layla',
      'نور': 'Nour',
      'أمل': 'Amal',
      'هدى': 'Hoda',
      'منى': 'Mona',
      'سلمى': 'Salma',
      'دينا': 'Dina',
      'ريم': 'Reem',
      'نادية': 'Nadia',
      'سميرة': 'Samira',
      'نهى': 'Noha',
      'رانيا': 'Rania',
      'هالة': 'Hala',
      'إيمان': 'Iman',
      'سماح': 'Samah',
      'نجلاء': 'Naglaa',
      'إنجي': 'Engy',
      'داليا': 'Dalia',
      'شيماء': 'Shimaa',
      'إسراء': 'Esraa',
      'آية': 'Aya',
      'ندى': 'Nada',
      'رحاب': 'Rehab',
      'سهير': 'Sohair',
      'نيفين': 'Neveen',
      'غادة': 'Ghada',
      'وفاء': 'Wafaa',
      'رشا': 'Rasha',
      'هبة': 'Heba',
      'نسرين': 'Nesreen',
      'ياسمين': 'Yasmin',
      'جيهان': 'Gehan',
      'سحر': 'Sahar',
      'أميرة': 'Amira',
      'نورهان': 'Nourhan',

      // ألقاب وكنى
      'أبو': 'Abu',
      'أم': 'Um',
      'ابن': 'Ibn',
      'بنت': 'Bint',
      'الدين': 'Eldeen',
      'الإسلام': 'Elislam',

      // كلمات مساعدة
      'بن': 'Ben',
      'ولد': 'Walad'
    };

    let result = cleanText;

    // ترتيب المعالجة: الأسماء المركبة أولاً ثم المفردة
    const sortedKeys = Object.keys(arabicToEnglishMap).sort((a, b) => b.length - a.length);

    // تطبيق التحويل
    sortedKeys.forEach(arabicName => {
      const englishName = arabicToEnglishMap[arabicName];
      // استخدام regex للبحث عن الكلمة كاملة
      const regex = new RegExp('\\b' + arabicName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'g');
      result = result.replace(regex, englishName);
    });

    // تنظيف النتيجة النهائية
    result = result.trim().replace(/\s+/g, ' ');

    // تحويل أول حرف من كل كلمة إلى حرف كبير
    result = result.replace(/\b\w/g, letter => letter.toUpperCase());

    return result;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.customerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // التحقق من وجود قيمة في الحقل المطلوب
  hasValue(fieldName: string): boolean {
    const field = this.customerForm.get(fieldName);
    return !!(field && field.value && field.value.toString().trim() !== '');
  }

  // التحقق من كون الحقل مطلوب
  isRequired(fieldName: string): boolean {
    const field = this.customerForm.get(fieldName);
    if (field && field.validator) {
      const validator = field.validator({} as any);
      return !!(validator && validator['required']);
    }
    return false;
  }

  // الحصول على CSS classes للحقل
  getFieldClasses(fieldName: string): string {
    const classes = ['form-control'];

    if (this.isRequired(fieldName)) {
      classes.push('required-field');

      if (this.hasValue(fieldName)) {
        classes.push('has-value');
      }
    }

    if (this.isFieldInvalid(fieldName)) {
      classes.push('is-invalid');
    }

    return classes.join(' ');
  }

  onSubmit() {
    if (this.customerForm.valid) {
      this.isSubmitting = true;
      const formData = this.customerForm.value;

      if (this.editMode) {
        this.updateCustomer(formData);
      } else {
        this.createCustomer(formData);
      }
    } else {
      this.markFormGroupTouched();
      this.showValidationErrors();
    }
  }

  showValidationErrors() {
    const errors: string[] = [];

    if (this.customerForm.get('nameAr')?.invalid) {
      errors.push('الاسم العربي مطلوب');
    }
    if (this.customerForm.get('phone1')?.invalid) {
      errors.push('رقم الهاتف الأول مطلوب');
    }
    if (this.customerForm.get('customerTypeId')?.invalid) {
      errors.push('نوع العميل مطلوب');
    }
    if (this.customerForm.get('priceCategoryId')?.invalid) {
      errors.push('فئة السعر مطلوبة');
    }
    if (this.customerForm.get('referralSourceId')?.invalid) {
      errors.push('مصدر التعرف مطلوب');
    }

    if (errors.length > 0) {
      alert('يرجى إكمال البيانات المطلوبة:\n' + errors.join('\n'));
    }
  }

  createCustomer(customerData: Customer) {
    // إزالة الكود المؤقت قبل الإرسال
    const dataToSend = { ...customerData };
    if (dataToSend.customerCode === 'سيتم إنشاؤه تلقائياً عند الحفظ') {
      dataToSend.customerCode = '';
    }

    this.http.post<any>(`${this.apiUrl}/customers`, dataToSend).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم إنشاء العميل بنجاح!');
          console.log('تم إنشاء العميل بالكود:', response.data.customerCode);

          // إنشاء قيد اليومية للرصيد الافتتاحي إذا كان موجود
          if (customerData.openingBalance && customerData.openingBalance !== 0) {
            this.createOpeningBalanceEntry(response.data.id, customerData.openingBalance, response.data.customerCode);
          }

          this.goBack();
        } else {
          alert('حدث خطأ في إنشاء العميل: ' + response.message);
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error creating customer:', error);
        alert('حدث خطأ في إنشاء العميل');
        this.isSubmitting = false;
      }
    });
  }

  updateCustomer(customerData: Customer) {
    this.http.put<any>(`${this.apiUrl}/customers/${this.customerId}`, customerData).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم تحديث بيانات العميل بنجاح!');
          this.goBack();
        } else {
          alert('حدث خطأ في تحديث العميل: ' + response.message);
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error updating customer:', error);
        alert('حدث خطأ في تحديث العميل');
        this.isSubmitting = false;
      }
    });
  }

  // إنشاء قيد يومية للرصيد الافتتاحي
  createOpeningBalanceEntry(customerId: number, amount: number, customerCode: string) {
    const journalEntry = {
      description: `رصيد افتتاحي للعميل ${customerCode}`,
      transactionType: 'opening_balance',
      customerId: customerId,
      amount: amount,
      details: [
        {
          accountCode: customerCode, // كود حساب العميل
          debit: amount > 0 ? amount : 0,
          credit: amount < 0 ? Math.abs(amount) : 0,
          description: `رصيد افتتاحي للعميل ${customerCode}`
        },
        {
          accountCode: '1001', // حساب الصندوق أو البنك
          debit: amount < 0 ? Math.abs(amount) : 0,
          credit: amount > 0 ? amount : 0,
          description: `رصيد افتتاحي للعميل ${customerCode}`
        }
      ]
    };

    this.http.post<any>(`${this.apiUrl}/journal-entries`, journalEntry).subscribe({
      next: (response) => {
        console.log('Opening balance journal entry created successfully');
      },
      error: (error) => {
        console.error('Error creating opening balance entry:', error);
      }
    });
  }

  markFormGroupTouched() {
    Object.keys(this.customerForm.controls).forEach(key => {
      const control = this.customerForm.get(key);
      control?.markAsTouched();
    });
  }

  resetForm() {
    this.customerForm.reset();
    this.customerForm.patchValue({
      isActive: true,
      branchId: this.currentUserBranchId,
      countryId: 1,
      creditLimit: 0,
      openingBalance: 0
    });

    // إعادة حجز كود جديد
    if (!this.editMode) {
      this.reserveNextCustomerCode();
    }

    // إعادة تعيين القيم الافتراضية
    setTimeout(() => {
      this.setDefaultPriceCategory();
      this.selectedCountryId = 1;
      this.filterCitiesByCountry();
    }, 100);
  }

  goBack() {
    this.router.navigate(['/customers']);
  }

  // دوال فتح النوافذ المنبثقة لإضافة البيانات
  openAddCustomerTypeModal() {
    const name = prompt('أدخل اسم نوع العميل الجديد:');
    if (name && name.trim()) {
      const newCustomerType = {
        nameAr: name.trim(),
        nameEn: this.translateArabicToEnglish(name.trim()),
        description: '',
        displayOrder: 1
      };

      this.http.post<any>(`${this.apiUrl}/lookup/customer-types`, newCustomerType).subscribe({
        next: (response) => {
          if (response.success) {
            alert('تم إضافة نوع العميل بنجاح!');
            this.loadLookupData(); // إعادة تحميل القائمة
            this.customerForm.patchValue({ customerTypeId: response.data.id });
          } else {
            alert('حدث خطأ في إضافة نوع العميل: ' + response.message);
          }
        },
        error: (error) => {
          console.error('Error adding customer type:', error);
          alert('حدث خطأ في إضافة نوع العميل');
        }
      });
    }
  }

  openAddReferralSourceModal() {
    const name = prompt('أدخل اسم مصدر التعرف الجديد:');
    if (name && name.trim()) {
      const newReferralSource = {
        nameAr: name.trim(),
        nameEn: this.translateArabicToEnglish(name.trim()),
        code: name.trim().substring(0, 10).toUpperCase()
      };

      this.http.post<any>(`${this.apiUrl}/lookup/referral-sources`, newReferralSource).subscribe({
        next: (response) => {
          if (response.success) {
            alert('تم إضافة مصدر التعرف بنجاح!');
            this.loadLookupData(); // إعادة تحميل القائمة
            this.customerForm.patchValue({ referralSourceId: response.data.id });
          } else {
            alert('حدث خطأ في إضافة مصدر التعرف: ' + response.message);
          }
        },
        error: (error) => {
          console.error('Error adding referral source:', error);
          alert('حدث خطأ في إضافة مصدر التعرف');
        }
      });
    }
  }

  openAddCountryModal() {
    const name = prompt('أدخل اسم البلد الجديد:');
    if (name && name.trim()) {
      const newCountry = {
        nameAr: name.trim(),
        nameEn: this.translateArabicToEnglish(name.trim()),
        code: name.trim().substring(0, 3).toUpperCase(),
        phoneCode: '+000',
        currency: 'USD'
      };

      this.http.post<any>(`${this.apiUrl}/lookup/countries`, newCountry).subscribe({
        next: (response) => {
          if (response.success) {
            alert('تم إضافة البلد بنجاح!');
            this.loadLookupData(); // إعادة تحميل القائمة
            this.customerForm.patchValue({ countryId: response.data.id });
            this.selectedCountryId = response.data.id;
            this.filterCitiesByCountry();
          } else {
            alert('حدث خطأ في إضافة البلد: ' + response.message);
          }
        },
        error: (error) => {
          console.error('Error adding country:', error);
          alert('حدث خطأ في إضافة البلد');
        }
      });
    }
  }

  openAddCityModal() {
    if (!this.selectedCountryId) {
      alert('يرجى اختيار البلد أولاً');
      return;
    }

    const name = prompt('أدخل اسم المدينة الجديدة:');
    if (name && name.trim()) {
      const newCity = {
        nameAr: name.trim(),
        nameEn: this.translateArabicToEnglish(name.trim()),
        countryId: this.selectedCountryId
      };

      this.http.post<any>(`${this.apiUrl}/lookup/cities`, newCity).subscribe({
        next: (response) => {
          if (response.success) {
            alert('تم إضافة المدينة بنجاح!');
            this.loadLookupData(); // إعادة تحميل القائمة
            this.filterCitiesByCountry();
            this.customerForm.patchValue({ cityId: response.data.id });
          } else {
            alert('حدث خطأ في إضافة المدينة: ' + response.message);
          }
        },
        error: (error) => {
          console.error('Error adding city:', error);
          alert('حدث خطأ في إضافة المدينة');
        }
      });
    }
  }
}
