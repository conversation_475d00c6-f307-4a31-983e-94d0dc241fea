import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

interface Customer {
  id?: number;
  customerCode?: string;
  nameAr: string;
  nameEn: string;
  phone1: string;
  phone2?: string;
  email?: string;
  address?: string;
  customerTypeId: number;
  priceCategoryId: number;
  creditLimit?: number;
  openingBalance?: number;
  branchId?: number;
  referralSource: string;
  notes?: string;
  isActive: boolean;
}

interface CustomerType {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface PriceCategory {
  id: number;
  nameAr: string;
  nameEn: string;
  discountPercentage: number;
}

interface Branch {
  id: number;
  nameAr: string;
  nameEn: string;
  isMainBranch: boolean;
  defaultPriceCategoryId?: number;
}

interface Country {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
}

interface ReferralSource {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface City {
  id: number;
  nameAr: string;
  nameEn: string;
  countryId: number;
}

@Component({
  selector: 'app-customer-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './customer-form.component.html',
  styleUrls: ['./customer-form.component.scss']
})
export class CustomerFormComponent implements OnInit {
  customerForm: FormGroup;
  editMode = false;
  customerId: number | null = null;
  isSubmitting = false;

  // Lookup data
  customerTypes: CustomerType[] = [];
  priceCategories: PriceCategory[] = [];
  branches: Branch[] = [];
  countries: Country[] = [];
  cities: City[] = [];
  filteredCities: City[] = [];
  referralSources: ReferralSource[] = [];

  // Current selections
  selectedCountryId: number | null = null;
  currentUserBranchId: number = 1; // سيتم جلبه من الجلسة
  nextCustomerCode: string = '';

  private apiUrl = 'http://localhost:5233/api';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient
  ) {
    this.customerForm = this.createForm();
  }

  ngOnInit() {
    this.loadLookupData();
    this.loadCountries();
    this.loadCities();
    this.checkEditMode();

    // عرض رسالة بدلاً من حجز الكود مسبقاً
    if (!this.editMode) {
      this.customerForm.patchValue({
        customerCode: 'سيتم إنشاؤه تلقائياً عند الحفظ'
      });
    }

    // تعيين القيم الافتراضية
    setTimeout(() => {
      this.setDefaultPriceCategory();
      this.selectedCountryId = 1; // مصر افتراضياً
      this.filterCitiesByCountry();
    }, 1000);
  }

  createForm(): FormGroup {
    return this.fb.group({
      customerCode: [''],
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required, Validators.minLength(2)]],
      phone1: ['', [Validators.required, Validators.pattern(/^01[0-9]{9}$/)]],
      phone2: ['', [Validators.pattern(/^01[0-9]{9}$/)]],
      email: ['', [Validators.email]],
      address: [''],
      countryId: [1], // مصر افتراضياً
      cityId: [''],
      customerTypeId: ['', Validators.required],
      priceCategoryId: ['', Validators.required],
      creditLimit: [0, [Validators.min(0)]],
      openingBalance: [0],
      branchId: [this.currentUserBranchId], // الفرع الحالي للمستخدم
      referralSource: ['', Validators.required],
      notes: [''],
      isActive: [true]
    });
  }

  loadLookupData() {
    // Load customer types
    this.http.get<any>(`${this.apiUrl}/customer-types`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customerTypes = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading customer types:', error);
        // Fallback data
        this.customerTypes = [
          { id: 1, nameAr: 'عميل تجزئة', nameEn: 'Retail Customer' },
          { id: 2, nameAr: 'عميل جملة', nameEn: 'Wholesale Customer' },
          { id: 3, nameAr: 'عميل مؤسسي', nameEn: 'Corporate Customer' },
          { id: 4, nameAr: 'عميل VIP', nameEn: 'VIP Customer' }
        ];
      }
    });

    // Load price categories
    this.http.get<any>(`${this.apiUrl}/price-categories`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.priceCategories = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading price categories:', error);
        // Fallback data
        this.priceCategories = [
          { id: 1, nameAr: 'سعر التجزئة', nameEn: 'Retail Price', discountPercentage: 0 },
          { id: 2, nameAr: 'سعر الجملة', nameEn: 'Wholesale Price', discountPercentage: 10 },
          { id: 3, nameAr: 'سعر المؤسسات', nameEn: 'Corporate Price', discountPercentage: 15 },
          { id: 4, nameAr: 'سعر VIP', nameEn: 'VIP Price', discountPercentage: 20 }
        ];
      }
    });

    // Load branches
    this.http.get<any>(`${this.apiUrl}/branches`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.branches = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading branches:', error);
        // Fallback data
        this.branches = [
          { id: 1, nameAr: 'الفرع الرئيسي', nameEn: 'Main Branch', isMainBranch: true, defaultPriceCategoryId: 12 },
          { id: 2, nameAr: 'فرع المعادي', nameEn: 'Maadi Branch', isMainBranch: false, defaultPriceCategoryId: 12 },
          { id: 3, nameAr: 'فرع الإسكندرية', nameEn: 'Alexandria Branch', isMainBranch: false, defaultPriceCategoryId: 13 }
        ];
      }
    });

    // Load referral sources
    this.http.get<any>(`${this.apiUrl}/referral-sources`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.referralSources = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading referral sources:', error);
        // Fallback data
        this.referralSources = [
          { id: 1, nameAr: 'إعلان', nameEn: 'Advertisement' },
          { id: 2, nameAr: 'صديق', nameEn: 'Friend' },
          { id: 3, nameAr: 'موقع إلكتروني', nameEn: 'Website' },
          { id: 4, nameAr: 'وسائل التواصل الاجتماعي', nameEn: 'Social Media' },
          { id: 5, nameAr: 'عميل سابق', nameEn: 'Previous Customer' }
        ];
      }
    });
  }

  checkEditMode() {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.editMode = true;
        this.customerId = +params['id'];
        this.loadCustomer(this.customerId);
      }
    });
  }

  loadCustomer(id: number) {
    this.http.get<any>(`${this.apiUrl}/customers/${id}`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customerForm.patchValue(response.data);
        }
      },
      error: (error) => {
        console.error('Error loading customer:', error);
        alert('حدث خطأ في تحميل بيانات العميل');
        this.goBack();
      }
    });
  }

  // تحويل الاسم العربي إلى إنجليزي تلقائياً
  onArabicNameChange(event: any) {
    const arabicName = event.target.value;
    if (arabicName && !this.editMode) {
      const englishName = this.translateArabicToEnglish(arabicName);
      this.customerForm.patchValue({ nameEn: englishName });
    }
  }

  // دالة تحويل الأسماء العربية إلى إنجليزية (محسنة لحل مشكلة الأسماء المركبة)
  translateArabicToEnglish(arabicText: string): string {
    const arabicToEnglishMap: { [key: string]: string } = {
      // الأسماء المركبة أولاً (لتجنب التداخل)
      'عبد الله': 'Abdullah',
      'عبد الرحمن': 'Abdulrahman',
      'عبد الرحيم': 'Abdulrahim',
      'عبد العزيز': 'Abdulaziz',
      'عبد الكريم': 'Abdulkarim',
      'عبد الحميد': 'Abdulhamid',
      'عبد المجيد': 'Abdulmajid',
      'عبد الناصر': 'Abdulnasser',
      'عبد الفتاح': 'Abdulfatah',
      'عبد الحليم': 'Abdulhalim',
      'عبد السلام': 'Abdulsalam',
      'عبد الوهاب': 'Abdulwahab',
      'عبد الغني': 'Abdulghani',
      'عبد الباسط': 'Abdulbasit',
      'عبد الرؤوف': 'Abdulraouf',
      'عبد المنعم': 'Abdulmonem',
      'عبد الصبور': 'Abdulsabour',

      // الأسماء المفردة
      'أحمد': 'Ahmed',
      'محمد': 'Mohamed',
      'علي': 'Ali',
      'فاطمة': 'Fatma',
      'عائشة': 'Aisha',
      'خديجة': 'Khadija',
      'حسن': 'Hassan',
      'حسين': 'Hussein',
      'إبراهيم': 'Ibrahim',
      'إسماعيل': 'Ismail',
      'يوسف': 'Youssef',
      'موسى': 'Musa',
      'عيسى': 'Issa',
      'مريم': 'Mariam',
      'زينب': 'Zeinab',
      'رقية': 'Ruqaya',
      'سارة': 'Sara',
      'ليلى': 'Layla',
      'نور': 'Nour',
      'أمل': 'Amal',
      'هدى': 'Hoda',
      'منى': 'Mona',
      'سلمى': 'Salma',
      'دينا': 'Dina',
      'ريم': 'Reem',
      'نادية': 'Nadia',
      'سميرة': 'Samira',
      'كريم': 'Karim',
      'طارق': 'Tarek',
      'عمر': 'Omar',
      'خالد': 'Khaled',
      'وليد': 'Walid',
      'سامي': 'Sami',
      'رامي': 'Rami',
      'هاني': 'Hani',
      'مصطفى': 'Mostafa',
      'محمود': 'Mahmoud',
      'حمدي': 'Hamdy',
      'سعد': 'Saad',
      'عماد': 'Emad',
      'أسامة': 'Osama',
      'هشام': 'Hisham',
      'وائل': 'Wael',
      'أيمن': 'Ayman',
      'شريف': 'Sherif',
      'عصام': 'Essam',
      'جمال': 'Gamal',
      'كمال': 'Kamal',
      'فؤاد': 'Fouad',
      'رضا': 'Reda',
      'عادل': 'Adel',
      'ماجد': 'Majed',
      'فريد': 'Farid',
      'سمير': 'Samir',
      'أمير': 'Amir',
      'باسم': 'Basem',
      'تامر': 'Tamer',
      'حازم': 'Hazem',
      'راشد': 'Rashed',
      'صالح': 'Saleh',
      'فارس': 'Fares',
      'ماهر': 'Maher',
      'ناصر': 'Nasser',
      'وسام': 'Wessam',
      'يحيى': 'Yahya',
      'زكريا': 'Zakaria',
      'عبد': 'Abd', // يجب أن يكون بعد الأسماء المركبة
      'الله': 'Allah',
      'الرحمن': 'Rahman',
      'الرحيم': 'Rahim'
    };

    let englishName = arabicText;

    // تحويل الأسماء المعروفة (الأسماء المركبة أولاً)
    Object.keys(arabicToEnglishMap).forEach(arabic => {
      const regex = new RegExp('\\b' + arabic + '\\b', 'g');
      englishName = englishName.replace(regex, arabicToEnglishMap[arabic]);
    });

    // إزالة المسافات الزائدة وتنسيق النص
    englishName = englishName.trim().replace(/\s+/g, ' ');

    // تحويل أول حرف من كل كلمة إلى كبير
    englishName = englishName.replace(/\b\w/g, l => l.toUpperCase());

    return englishName;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.customerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  onSubmit() {
    if (this.customerForm.valid) {
      this.isSubmitting = true;
      const formData = this.customerForm.value;

      if (this.editMode) {
        this.updateCustomer(formData);
      } else {
        this.createCustomer(formData);
      }
    } else {
      this.markFormGroupTouched();
      this.showValidationErrors();
    }
  }

  showValidationErrors() {
    const errors: string[] = [];

    if (this.customerForm.get('nameAr')?.invalid) {
      errors.push('الاسم العربي مطلوب');
    }
    if (this.customerForm.get('phone1')?.invalid) {
      errors.push('رقم الهاتف الأول مطلوب');
    }
    if (this.customerForm.get('customerTypeId')?.invalid) {
      errors.push('نوع العميل مطلوب');
    }
    if (this.customerForm.get('priceCategoryId')?.invalid) {
      errors.push('فئة السعر مطلوبة');
    }
    if (this.customerForm.get('referralSourceId')?.invalid) {
      errors.push('مصدر التعرف مطلوب');
    }

    if (errors.length > 0) {
      alert('يرجى إكمال البيانات المطلوبة:\n' + errors.join('\n'));
    }
  }

  createCustomer(customerData: Customer) {
    // إزالة الكود المؤقت قبل الإرسال
    const dataToSend = { ...customerData };
    if (dataToSend.customerCode === 'سيتم إنشاؤه تلقائياً عند الحفظ') {
      dataToSend.customerCode = '';
    }

    this.http.post<any>(`${this.apiUrl}/customers`, dataToSend).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم إنشاء العميل بنجاح!');
          console.log('تم إنشاء العميل بالكود:', response.data.customerCode);

          // إنشاء قيد اليومية للرصيد الافتتاحي إذا كان موجود
          if (customerData.openingBalance && customerData.openingBalance !== 0) {
            this.createOpeningBalanceEntry(response.data.id, customerData.openingBalance, response.data.customerCode);
          }

          this.goBack();
        } else {
          alert('حدث خطأ في إنشاء العميل: ' + response.message);
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error creating customer:', error);
        alert('حدث خطأ في إنشاء العميل');
        this.isSubmitting = false;
      }
    });
  }

  updateCustomer(customerData: Customer) {
    this.http.put<any>(`${this.apiUrl}/customers/${this.customerId}`, customerData).subscribe({
      next: (response) => {
        if (response.success) {
          alert('تم تحديث بيانات العميل بنجاح!');
          this.goBack();
        } else {
          alert('حدث خطأ في تحديث العميل: ' + response.message);
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error updating customer:', error);
        alert('حدث خطأ في تحديث العميل');
        this.isSubmitting = false;
      }
    });
  }

  // إنشاء قيد يومية للرصيد الافتتاحي
  createOpeningBalanceEntry(customerId: number, amount: number, customerCode: string) {
    const journalEntry = {
      description: `رصيد افتتاحي للعميل ${customerCode}`,
      transactionType: 'opening_balance',
      customerId: customerId,
      amount: amount,
      details: [
        {
          accountCode: customerCode, // كود حساب العميل
          debit: amount > 0 ? amount : 0,
          credit: amount < 0 ? Math.abs(amount) : 0,
          description: `رصيد افتتاحي للعميل ${customerCode}`
        },
        {
          accountCode: '1001', // حساب الصندوق أو البنك
          debit: amount < 0 ? Math.abs(amount) : 0,
          credit: amount > 0 ? amount : 0,
          description: `رصيد افتتاحي للعميل ${customerCode}`
        }
      ]
    };

    this.http.post<any>(`${this.apiUrl}/journal-entries`, journalEntry).subscribe({
      next: (response) => {
        console.log('Opening balance journal entry created successfully');
      },
      error: (error) => {
        console.error('Error creating opening balance entry:', error);
      }
    });
  }

  markFormGroupTouched() {
    Object.keys(this.customerForm.controls).forEach(key => {
      const control = this.customerForm.get(key);
      control?.markAsTouched();
    });
  }

  resetForm() {
    this.customerForm.reset();
    this.customerForm.patchValue({
      isActive: true,
      creditLimit: 0,
      openingBalance: 0
    });
  }

  goBack() {
    this.router.navigate(['/customers']);
  }

  // تحميل البلدان
  loadCountries() {
    this.http.get<any>(`${this.apiUrl}/countries`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.countries = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        // Fallback data
        this.countries = [
          { id: 1, nameAr: 'مصر', nameEn: 'Egypt', code: 'EG' },
          { id: 2, nameAr: 'السعودية', nameEn: 'Saudi Arabia', code: 'SA' },
          { id: 3, nameAr: 'الإمارات', nameEn: 'UAE', code: 'AE' }
        ];
      }
    });
  }

  // تحميل المدن
  loadCities() {
    this.http.get<any>(`${this.apiUrl}/cities`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.cities = response.data;
          this.filterCitiesByCountry();
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        // Fallback data
        this.cities = [
          { id: 1, nameAr: 'القاهرة', nameEn: 'Cairo', countryId: 1 },
          { id: 2, nameAr: 'الإسكندرية', nameEn: 'Alexandria', countryId: 1 },
          { id: 3, nameAr: 'الجيزة', nameEn: 'Giza', countryId: 1 },
          { id: 4, nameAr: 'طنطا', nameEn: 'Tanta', countryId: 1 }
        ];
        this.filterCitiesByCountry();
      }
    });
  }

  // تصفية المدن حسب البلد المختار
  onCountryChange() {
    this.selectedCountryId = this.customerForm.get('countryId')?.value;
    this.customerForm.patchValue({ cityId: '' }); // إعادة تعيين المدينة
    this.filterCitiesByCountry();
  }

  filterCitiesByCountry() {
    if (this.selectedCountryId) {
      this.filteredCities = this.cities.filter(city => city.countryId === this.selectedCountryId);
    } else {
      this.filteredCities = this.cities;
    }
  }

  // الحصول على الكود التالي من API (محدث من الكونتر)
  getNextCustomerCodeFromAPI() {
    this.http.get<any>(`${this.apiUrl}/customers/next-code`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.nextCustomerCode = response.data.nextCode;
          this.customerForm.patchValue({ customerCode: this.nextCustomerCode });
          console.log('تم تحديث الكود من الكونتر:', this.nextCustomerCode);
        }
      },
      error: (error) => {
        console.error('Error getting next customer code from API:', error);
        // Fallback: استخدام الكونتر المحلي
        this.generateFallbackCustomerCode();
      }
    });
  }

  // إنشاء كود العميل التالي تلقائياً (للتوافق مع الكود القديم)
  generateNextCustomerCode() {
    this.getNextCustomerCodeFromAPI();
  }

  // إنشاء كود احتياطي للعميل (مبسط)
  generateFallbackCustomerCode() {
    // الحصول على آخر كود من قائمة العملاء
    this.http.get<any>(`${this.apiUrl}/customers`).subscribe({
      next: (response) => {
        if (response.success && response.data && response.data.length > 0) {
          // البحث عن آخر كود
          const lastCustomer = response.data[response.data.length - 1];
          const lastCode = lastCustomer.customerCode;

          if (lastCode && lastCode.startsWith('CUS')) {
            // استخراج الرقم من آخر كود (مبسط)
            const lastNumber = parseInt(lastCode.substring(3));
            const nextNumber = lastNumber + 1;
            this.nextCustomerCode = 'CUS' + nextNumber;
          } else {
            this.nextCustomerCode = 'CUS1';
          }
        } else {
          // أول عميل
          this.nextCustomerCode = 'CUS1';
        }

        this.customerForm.patchValue({ customerCode: this.nextCustomerCode });
      },
      error: (error) => {
        console.error('Error getting customers for code generation:', error);
        // آخر حل احتياطي
        this.nextCustomerCode = 'CUS1';
        this.customerForm.patchValue({ customerCode: this.nextCustomerCode });
      }
    });
  }

  // تعيين فئة السعر الافتراضية للفرع
  setDefaultPriceCategory() {
    const currentBranch = this.branches.find(b => b.id === this.currentUserBranchId);
    if (currentBranch && currentBranch.defaultPriceCategoryId) {
      this.customerForm.patchValue({ priceCategoryId: currentBranch.defaultPriceCategoryId });
    } else {
      // افتراضي: سعر التجزئة
      this.customerForm.patchValue({ priceCategoryId: 1 });
    }
  }

  // فتح نافذة إضافة نوع عميل جديد
  openAddCustomerTypeModal() {
    console.log('Open add customer type modal');
    // TODO: Implement add customer type modal
    alert('سيتم إضافة هذه الميزة قريباً');
  }

  // فتح نافذة إضافة مصدر تعرف جديد
  openAddReferralSourceModal() {
    console.log('Open add referral source modal');
    // TODO: Implement add referral source modal
    alert('سيتم إضافة هذه الميزة قريباً');
  }

  // فتح نافذة إضافة بلد جديد
  openAddCountryModal() {
    console.log('Open add country modal');
    // TODO: Implement add country modal
    alert('سيتم إضافة هذه الميزة قريباً');
  }

  // فتح نافذة إضافة منطقة جديدة
  openAddCityModal() {
    console.log('Open add city modal');
    // TODO: Implement add city modal
    alert('سيتم إضافة هذه الميزة قريباً');
  }
}
