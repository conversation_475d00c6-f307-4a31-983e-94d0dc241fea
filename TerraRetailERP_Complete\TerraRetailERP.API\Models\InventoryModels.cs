using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("StockMovements")]
    public class StockMovement
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }
        public int BranchId { get; set; }

        [Required]
        [StringLength(40)]
        public string MovementNumber { get; set; } = string.Empty;

        public int MovementType { get; set; } = 1; // 1=In, 2=Out, 3=Transfer, 4=Adjustment

        [StringLength(100)]
        public string? MovementReason { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceBefore { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceAfter { get; set; } = 0;

        public DateTime MovementDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? StorageLocation { get; set; }

        public int? SourceId { get; set; } // Reference to source transaction

        [StringLength(100)]
        public string? SourceType { get; set; } // Sale, Purchase, Transfer, etc.

        public int UserId { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    [Table("BranchTransfers")]
    public class BranchTransfer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string TransferNumber { get; set; } = string.Empty;

        public int FromBranchId { get; set; }
        public int ToBranchId { get; set; }

        public DateTime TransferDate { get; set; } = DateTime.Now;
        public DateTime? ReceivedDate { get; set; }

        public int Status { get; set; } = 1; // 1=Pending, 2=Sent, 3=Received, 4=Cancelled

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(1000)]
        public string? InternalNotes { get; set; }

        public int RequestedBy { get; set; }
        public int? SentBy { get; set; }
        public int? ReceivedBy { get; set; }

        public DateTime? SentAt { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; } = 0;

        public int TotalItems { get; set; } = 0;

        [StringLength(200)]
        public string? ShippingCompany { get; set; }

        [StringLength(100)]
        public string? TrackingNumber { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        public int? CancelledBy { get; set; }
        public DateTime? CancelledAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("FromBranchId")]
        public virtual Branch FromBranch { get; set; } = null!;

        [ForeignKey("ToBranchId")]
        public virtual Branch ToBranch { get; set; } = null!;

        [ForeignKey("RequestedBy")]
        public virtual User RequestedByUser { get; set; } = null!;

        [ForeignKey("SentBy")]
        public virtual User? SentByUser { get; set; }

        [ForeignKey("ReceivedBy")]
        public virtual User? ReceivedByUser { get; set; }

        [ForeignKey("CancelledBy")]
        public virtual User? CancelledByUser { get; set; }

        public virtual ICollection<BranchTransferDetail> BranchTransferDetails { get; set; } = new List<BranchTransferDetail>();
    }

    [Table("BranchTransferDetails")]
    public class BranchTransferDetail
    {
        [Key]
        public int Id { get; set; }

        public int BranchTransferId { get; set; }
        public int ProductId { get; set; }

        public int LineNumber { get; set; } = 1;

        [Column(TypeName = "decimal(18,3)")]
        public decimal RequestedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal SentQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReceivedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } = 0;

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [StringLength(1000)]
        public string? ItemNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("BranchTransferId")]
        public virtual BranchTransfer BranchTransfer { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }

    [Table("StockAdjustments")]
    public class StockAdjustment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string AdjustmentNumber { get; set; } = string.Empty;

        public int BranchId { get; set; }

        public DateTime AdjustmentDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(100)]
        public string AdjustmentReason { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int Status { get; set; } = 1; // 1=Draft, 2=Approved, 3=Posted

        public int UserId { get; set; }
        public int? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAdjustmentValue { get; set; } = 0;

        public int TotalItems { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        public virtual ICollection<StockAdjustmentDetail> StockAdjustmentDetails { get; set; } = new List<StockAdjustmentDetail>();
    }

    [Table("StockAdjustmentDetails")]
    public class StockAdjustmentDetail
    {
        [Key]
        public int Id { get; set; }

        public int StockAdjustmentId { get; set; }
        public int ProductId { get; set; }

        public int LineNumber { get; set; } = 1;

        [Column(TypeName = "decimal(18,3)")]
        public decimal SystemQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal PhysicalQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal AdjustmentQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AdjustmentValue { get; set; } = 0;

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [StringLength(1000)]
        public string? ItemNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("StockAdjustmentId")]
        public virtual StockAdjustment StockAdjustment { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }

    [Table("InventoryCount")]
    public class InventoryCount
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string CountNumber { get; set; } = string.Empty;

        public int BranchId { get; set; }

        public DateTime CountDate { get; set; } = DateTime.Now;

        [StringLength(1000)]
        public string? Description { get; set; }

        public int Status { get; set; } = 1; // 1=In Progress, 2=Completed, 3=Posted

        public int UserId { get; set; }
        public int? CompletedBy { get; set; }
        public DateTime? CompletedAt { get; set; }

        public int TotalItems { get; set; } = 0;
        public int CountedItems { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalVarianceValue { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CompletedBy")]
        public virtual User? CompletedByUser { get; set; }

        public virtual ICollection<InventoryCountDetail> InventoryCountDetails { get; set; } = new List<InventoryCountDetail>();
    }

    [Table("InventoryCountDetails")]
    public class InventoryCountDetail
    {
        [Key]
        public int Id { get; set; }

        public int InventoryCountId { get; set; }
        public int ProductId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal SystemQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? CountedQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal VarianceQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal VarianceValue { get; set; } = 0;

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsCounted { get; set; } = false;
        public DateTime? CountedAt { get; set; }
        public int? CountedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("InventoryCountId")]
        public virtual InventoryCount InventoryCount { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("CountedBy")]
        public virtual User? CountedByUser { get; set; }
    }
}
