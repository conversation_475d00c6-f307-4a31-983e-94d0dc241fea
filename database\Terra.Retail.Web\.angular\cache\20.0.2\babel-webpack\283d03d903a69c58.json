{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, DOCUMENT, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { auditTime } from 'rxjs/operators';\nimport '@angular/common';\n\n/** Component used to load the structural styles of the text field. */\nlet _CdkTextFieldStyleLoader = /*#__PURE__*/(() => {\n  class _CdkTextFieldStyleLoader {\n    static ɵfac = function _CdkTextFieldStyleLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _CdkTextFieldStyleLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _CdkTextFieldStyleLoader,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"cdk-text-field-style-loader\", \"\"],\n      decls: 0,\n      vars: 0,\n      template: function _CdkTextFieldStyleLoader_Template(rf, ctx) {},\n      styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _CdkTextFieldStyleLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = {\n  passive: true\n};\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nlet AutofillMonitor = /*#__PURE__*/(() => {\n  class AutofillMonitor {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    _monitoredElements = new Map();\n    constructor() {}\n    monitor(elementOrRef) {\n      if (!this._platform.isBrowser) {\n        return EMPTY;\n      }\n      this._styleLoader.load(_CdkTextFieldStyleLoader);\n      const element = coerceElement(elementOrRef);\n      const info = this._monitoredElements.get(element);\n      if (info) {\n        return info.subject;\n      }\n      const subject = new Subject();\n      const cssClass = 'cdk-text-field-autofilled';\n      const listener = event => {\n        // Animation events fire on initial element render, we check for the presence of the autofill\n        // CSS class to make sure this is a real change in state, not just the initial render before\n        // we fire off events.\n        if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n          element.classList.add(cssClass);\n          this._ngZone.run(() => subject.next({\n            target: event.target,\n            isAutofilled: true\n          }));\n        } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n          element.classList.remove(cssClass);\n          this._ngZone.run(() => subject.next({\n            target: event.target,\n            isAutofilled: false\n          }));\n        }\n      };\n      const unlisten = this._ngZone.runOutsideAngular(() => {\n        element.classList.add('cdk-text-field-autofill-monitored');\n        return this._renderer.listen(element, 'animationstart', listener, listenerOptions);\n      });\n      this._monitoredElements.set(element, {\n        subject,\n        unlisten\n      });\n      return subject;\n    }\n    stopMonitoring(elementOrRef) {\n      const element = coerceElement(elementOrRef);\n      const info = this._monitoredElements.get(element);\n      if (info) {\n        info.unlisten();\n        info.subject.complete();\n        element.classList.remove('cdk-text-field-autofill-monitored');\n        element.classList.remove('cdk-text-field-autofilled');\n        this._monitoredElements.delete(element);\n      }\n    }\n    ngOnDestroy() {\n      this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static ɵfac = function AutofillMonitor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutofillMonitor)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutofillMonitor,\n      factory: AutofillMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AutofillMonitor;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nlet CdkAutofill = /*#__PURE__*/(() => {\n  class CdkAutofill {\n    _elementRef = inject(ElementRef);\n    _autofillMonitor = inject(AutofillMonitor);\n    /** Emits when the autofill state of the element changes. */\n    cdkAutofill = new EventEmitter();\n    constructor() {}\n    ngOnInit() {\n      this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n      this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static ɵfac = function CdkAutofill_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAutofill)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAutofill,\n      selectors: [[\"\", \"cdkAutofill\", \"\"]],\n      outputs: {\n        cdkAutofill: \"cdkAutofill\"\n      }\n    });\n  }\n  return CdkAutofill;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nlet CdkTextareaAutosize = /*#__PURE__*/(() => {\n  class CdkTextareaAutosize {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _resizeEvents = new Subject();\n    /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n    _previousValue;\n    _initialHeight;\n    _destroyed = new Subject();\n    _listenerCleanups;\n    _minRows;\n    _maxRows;\n    _enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    _previousMinRows = -1;\n    _textareaElement;\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n      return this._minRows;\n    }\n    set minRows(value) {\n      this._minRows = coerceNumberProperty(value);\n      this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n      return this._maxRows;\n    }\n    set maxRows(value) {\n      this._maxRows = coerceNumberProperty(value);\n      this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n      return this._enabled;\n    }\n    set enabled(value) {\n      // Only act if the actual value changed. This specifically helps to not run\n      // resizeToFitContent too early (i.e. before ngAfterViewInit)\n      if (this._enabled !== value) {\n        (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n      }\n    }\n    get placeholder() {\n      return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n      this._cachedPlaceholderHeight = undefined;\n      if (value) {\n        this._textareaElement.setAttribute('placeholder', value);\n      } else {\n        this._textareaElement.removeAttribute('placeholder');\n      }\n      this._cacheTextareaPlaceholderHeight();\n    }\n    /** Cached height of a textarea with a single row. */\n    _cachedLineHeight;\n    /** Cached height of a textarea with only the placeholder. */\n    _cachedPlaceholderHeight;\n    /** Cached scroll top of a textarea */\n    _cachedScrollTop;\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, {\n      optional: true\n    });\n    _hasFocus;\n    _isViewInited = false;\n    constructor() {\n      const styleLoader = inject(_CdkPrivateStyleLoader);\n      styleLoader.load(_CdkTextFieldStyleLoader);\n      this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n      const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n      if (minHeight) {\n        this._textareaElement.style.minHeight = minHeight;\n      }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n      const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n      if (maxHeight) {\n        this._textareaElement.style.maxHeight = maxHeight;\n      }\n    }\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        // Remember the height which we started with in case autosizing is disabled\n        this._initialHeight = this._textareaElement.style.height;\n        this.resizeToFitContent();\n        this._ngZone.runOutsideAngular(() => {\n          this._listenerCleanups = [this._renderer.listen('window', 'resize', () => this._resizeEvents.next()), this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent), this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent)];\n          this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n            // Clear the cached heights since the styles can change\n            // when the window is resized (e.g. by media queries).\n            this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n            this.resizeToFitContent(true);\n          });\n        });\n        this._isViewInited = true;\n        this.resizeToFitContent(true);\n      }\n    }\n    ngOnDestroy() {\n      this._listenerCleanups?.forEach(cleanup => cleanup());\n      this._resizeEvents.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n      if (this._cachedLineHeight) {\n        return;\n      }\n      // Use a clone element because we have to override some styles.\n      const textareaClone = this._textareaElement.cloneNode(false);\n      const cloneStyles = textareaClone.style;\n      textareaClone.rows = 1;\n      // Use `position: absolute` so that this doesn't cause a browser layout and use\n      // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n      // would affect the height.\n      cloneStyles.position = 'absolute';\n      cloneStyles.visibility = 'hidden';\n      cloneStyles.border = 'none';\n      cloneStyles.padding = '0';\n      cloneStyles.height = '';\n      cloneStyles.minHeight = '';\n      cloneStyles.maxHeight = '';\n      // App styles might be messing with the height through the positioning properties.\n      cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n      // In Firefox it happens that textarea elements are always bigger than the specified amount\n      // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n      // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n      // to hidden. This ensures that there is no invalid calculation of the line height.\n      // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n      cloneStyles.overflow = 'hidden';\n      this._textareaElement.parentNode.appendChild(textareaClone);\n      this._cachedLineHeight = textareaClone.clientHeight;\n      textareaClone.remove();\n      // Min and max heights have to be re-calculated if the cached line height changes\n      this._setMinHeight();\n      this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n      const element = this._textareaElement;\n      const previousMargin = element.style.marginBottom || '';\n      const isFirefox = this._platform.FIREFOX;\n      const needsMarginFiller = isFirefox && this._hasFocus;\n      const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n      // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n      // work around it by assigning a temporary margin with the same height as the `textarea` so that\n      // it occupies the same amount of space. See #23233.\n      if (needsMarginFiller) {\n        element.style.marginBottom = `${element.clientHeight}px`;\n      }\n      // Reset the textarea height to auto in order to shrink back to its default size.\n      // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n      element.classList.add(measuringClass);\n      // The measuring class includes a 2px padding to workaround an issue with Chrome,\n      // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n      const scrollHeight = element.scrollHeight - 4;\n      element.classList.remove(measuringClass);\n      if (needsMarginFiller) {\n        element.style.marginBottom = previousMargin;\n      }\n      return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n      if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n        return;\n      }\n      if (!this.placeholder) {\n        this._cachedPlaceholderHeight = 0;\n        return;\n      }\n      const value = this._textareaElement.value;\n      this._textareaElement.value = this._textareaElement.placeholder;\n      this._cachedPlaceholderHeight = this._measureScrollHeight();\n      this._textareaElement.value = value;\n    }\n    /** Handles `focus` and `blur` events. */\n    _handleFocusEvent = event => {\n      this._hasFocus = event.type === 'focus';\n    };\n    ngDoCheck() {\n      if (this._platform.isBrowser) {\n        this.resizeToFitContent();\n      }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n      // If autosizing is disabled, just skip everything else\n      if (!this._enabled) {\n        return;\n      }\n      this._cacheTextareaLineHeight();\n      this._cacheTextareaPlaceholderHeight();\n      this._cachedScrollTop = this._textareaElement.scrollTop;\n      // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n      // in checking the height of the textarea.\n      if (!this._cachedLineHeight) {\n        return;\n      }\n      const textarea = this._elementRef.nativeElement;\n      const value = textarea.value;\n      // Only resize if the value or minRows have changed since these calculations can be expensive.\n      if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n        return;\n      }\n      const scrollHeight = this._measureScrollHeight();\n      const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n      // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n      textarea.style.height = `${height}px`;\n      this._ngZone.runOutsideAngular(() => {\n        if (typeof requestAnimationFrame !== 'undefined') {\n          requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n        } else {\n          setTimeout(() => this._scrollToCaretPosition(textarea));\n        }\n      });\n      this._previousValue = value;\n      this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n      // Do not try to change the textarea, if the initialHeight has not been determined yet\n      // This might potentially remove styles when reset() is called before ngAfterViewInit\n      if (this._initialHeight !== undefined) {\n        this._textareaElement.style.height = this._initialHeight;\n      }\n    }\n    _noopInputHandler() {\n      // no-op handler that ensures we're running change detection on input events.\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n      const {\n        selectionStart,\n        selectionEnd\n      } = textarea;\n      // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n      // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n      // between the time we requested the animation frame and when it was executed.\n      // Also note that we have to assert that the textarea is focused before we set the\n      // selection range. Setting the selection range on a non-focused textarea will cause\n      // it to receive focus on IE and Edge.\n      if (!this._destroyed.isStopped && this._hasFocus) {\n        textarea.setSelectionRange(selectionStart, selectionEnd);\n        textarea.scrollTop = this._cachedScrollTop;\n      }\n    }\n    static ɵfac = function CdkTextareaAutosize_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTextareaAutosize)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTextareaAutosize,\n      selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n      hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n      hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n            return ctx._noopInputHandler();\n          });\n        }\n      },\n      inputs: {\n        minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n        maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n        enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n        placeholder: \"placeholder\"\n      },\n      exportAs: [\"cdkTextareaAutosize\"]\n    });\n  }\n  return CdkTextareaAutosize;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TextFieldModule = /*#__PURE__*/(() => {\n  class TextFieldModule {\n    static ɵfac = function TextFieldModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TextFieldModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TextFieldModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return TextFieldModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "EventEmitter", "Directive", "Output", "Renderer2", "DOCUMENT", "booleanAttribute", "Input", "NgModule", "EMPTY", "Subject", "P", "Platform", "_", "_CdkPrivateStyleLoader", "a", "coerceElement", "c", "coerceNumberProperty", "auditTime", "_CdkTextFieldStyleLoader", "ɵfac", "_CdkTextFieldStyleLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkTextFieldStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "listenerOptions", "passive", "AutofillMonitor", "_platform", "_ngZone", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_styleLoader", "_monitoredElements", "Map", "constructor", "monitor", "elementOrRef", "<PERSON><PERSON><PERSON><PERSON>", "load", "element", "info", "get", "subject", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "run", "next", "target", "isAutofilled", "remove", "unlisten", "runOutsideAngular", "listen", "set", "stopMonitoring", "complete", "delete", "ngOnDestroy", "for<PERSON>ach", "_info", "AutofillMonitor_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "CdkAutofill", "_elementRef", "_autofillMonitor", "cdkAutofill", "ngOnInit", "subscribe", "emit", "CdkAutofill_Factory", "ɵdir", "ɵɵdefineDirective", "outputs", "CdkTextareaAutosize", "_resizeEvents", "_previousValue", "_initialHeight", "_destroyed", "_listenerCleanups", "_minRows", "_maxRows", "_enabled", "_previousMinRows", "_textareaElement", "minRows", "value", "_setMinHeight", "maxRows", "_setMaxHeight", "enabled", "resizeToFitContent", "reset", "placeholder", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "_cachedLineHeight", "_cachedScrollTop", "_document", "optional", "_hasFocus", "_isViewInited", "<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "minHeight", "style", "maxHeight", "ngAfterViewInit", "height", "_handleFocusEvent", "pipe", "cleanup", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "cloneStyles", "rows", "position", "visibility", "border", "padding", "top", "bottom", "left", "right", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "scrollTop", "textarea", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "CdkTextareaAutosize_Factory", "hostBindings", "CdkTextareaAutosize_HostBindings", "ɵɵlistener", "CdkTextareaAutosize_input_HostBindingHandler", "inputs", "exportAs", "TextFieldModule", "TextFieldModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/text-field.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, DOCUMENT, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { auditTime } from 'rxjs/operators';\nimport '@angular/common';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _CdkTextFieldStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _CdkTextFieldStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-text-field-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _CdkTextFieldStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-text-field-style-loader': '' }, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"] }]\n        }] });\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = { passive: true };\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    _monitoredElements = new Map();\n    constructor() { }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        this._styleLoader.load(_CdkTextFieldStyleLoader);\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const subject = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = (event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: false }));\n            }\n        };\n        const unlisten = this._ngZone.runOutsideAngular(() => {\n            element.classList.add('cdk-text-field-autofill-monitored');\n            return this._renderer.listen(element, 'animationstart', listener, listenerOptions);\n        });\n        this._monitoredElements.set(element, { subject, unlisten });\n        return subject;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AutofillMonitor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    _elementRef = inject(ElementRef);\n    _autofillMonitor = inject(AutofillMonitor);\n    /** Emits when the autofill state of the element changes. */\n    cdkAutofill = new EventEmitter();\n    constructor() { }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkAutofill, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkAutofill, isStandalone: true, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _resizeEvents = new Subject();\n    /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n    _previousValue;\n    _initialHeight;\n    _destroyed = new Subject();\n    _listenerCleanups;\n    _minRows;\n    _maxRows;\n    _enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    _previousMinRows = -1;\n    _textareaElement;\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    /** Cached height of a textarea with a single row. */\n    _cachedLineHeight;\n    /** Cached height of a textarea with only the placeholder. */\n    _cachedPlaceholderHeight;\n    /** Cached scroll top of a textarea */\n    _cachedScrollTop;\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    _hasFocus;\n    _isViewInited = false;\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_CdkTextFieldStyleLoader);\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                this._listenerCleanups = [\n                    this._renderer.listen('window', 'resize', () => this._resizeEvents.next()),\n                    this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent),\n                    this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent),\n                ];\n                this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n                    // Clear the cached heights since the styles can change\n                    // when the window is resized (e.g. by media queries).\n                    this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n                    this.resizeToFitContent(true);\n                });\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._listenerCleanups?.forEach(cleanup => cleanup());\n        this._resizeEvents.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        const textareaClone = this._textareaElement.cloneNode(false);\n        const cloneStyles = textareaClone.style;\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        cloneStyles.position = 'absolute';\n        cloneStyles.visibility = 'hidden';\n        cloneStyles.border = 'none';\n        cloneStyles.padding = '0';\n        cloneStyles.height = '';\n        cloneStyles.minHeight = '';\n        cloneStyles.maxHeight = '';\n        // App styles might be messing with the height through the positioning properties.\n        cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        cloneStyles.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    /** Handles `focus` and `blur` events. */\n    _handleFocusEvent = (event) => {\n        this._hasFocus = event.type === 'focus';\n    };\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        this._cachedScrollTop = this._textareaElement.scrollTop;\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n            textarea.scrollTop = this._cachedScrollTop;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTextareaAutosize, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkTextareaAutosize, isStandalone: true, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\", booleanAttribute], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTextareaAutosize', transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }] } });\n\nclass TextFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: TextFieldModule, imports: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: TextFieldModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACxO,SAASC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,wBAAwB;AACtF,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,iBAAiB;;AAExB;AAAA,IACMC,wBAAwB;EAA9B,MAAMA,wBAAwB,CAAC;IAC3B,OAAOC,IAAI,YAAAC,iCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,wBAAwB;IAAA;IAC3H,OAAOI,IAAI,kBAD8EhC,EAAE,CAAAiC,iBAAA;MAAAC,IAAA,EACJN,wBAAwB;MAAAO,SAAA;MAAAC,SAAA,kCAAqG,EAAE;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC1N;EAAC,OAHKjB,wBAAwB;AAAA;AAI9B;EAAA,QAAAkB,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA,MAAMC,eAAe,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AACzC;AACA;AACA;AACA;AACA;AAJA,IAKMC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBC,SAAS,GAAG9C,MAAM,CAACgB,QAAQ,CAAC;IAC5B+B,OAAO,GAAG/C,MAAM,CAACC,MAAM,CAAC;IACxB+C,SAAS,GAAGhD,MAAM,CAACE,gBAAgB,CAAC,CAAC+C,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/DC,YAAY,GAAGlD,MAAM,CAACkB,sBAAsB,CAAC;IAC7CiC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9BC,WAAWA,CAAA,EAAG,CAAE;IAChBC,OAAOA,CAACC,YAAY,EAAE;MAClB,IAAI,CAAC,IAAI,CAACT,SAAS,CAACU,SAAS,EAAE;QAC3B,OAAO3C,KAAK;MAChB;MACA,IAAI,CAACqC,YAAY,CAACO,IAAI,CAACjC,wBAAwB,CAAC;MAChD,MAAMkC,OAAO,GAAGtC,aAAa,CAACmC,YAAY,CAAC;MAC3C,MAAMI,IAAI,GAAG,IAAI,CAACR,kBAAkB,CAACS,GAAG,CAACF,OAAO,CAAC;MACjD,IAAIC,IAAI,EAAE;QACN,OAAOA,IAAI,CAACE,OAAO;MACvB;MACA,MAAMA,OAAO,GAAG,IAAI/C,OAAO,CAAC,CAAC;MAC7B,MAAMgD,QAAQ,GAAG,2BAA2B;MAC5C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;QACxB;QACA;QACA;QACA,IAAIA,KAAK,CAACC,aAAa,KAAK,+BAA+B,IACvD,CAACP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;UACvCJ,OAAO,CAACQ,SAAS,CAACE,GAAG,CAACN,QAAQ,CAAC;UAC/B,IAAI,CAACf,OAAO,CAACsB,GAAG,CAAC,MAAMR,OAAO,CAACS,IAAI,CAAC;YAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;YAAEC,YAAY,EAAE;UAAK,CAAC,CAAC,CAAC;QACtF,CAAC,MACI,IAAIR,KAAK,CAACC,aAAa,KAAK,6BAA6B,IAC1DP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;UACtCJ,OAAO,CAACQ,SAAS,CAACO,MAAM,CAACX,QAAQ,CAAC;UAClC,IAAI,CAACf,OAAO,CAACsB,GAAG,CAAC,MAAMR,OAAO,CAACS,IAAI,CAAC;YAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;YAAEC,YAAY,EAAE;UAAM,CAAC,CAAC,CAAC;QACvF;MACJ,CAAC;MACD,MAAME,QAAQ,GAAG,IAAI,CAAC3B,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;QAClDjB,OAAO,CAACQ,SAAS,CAACE,GAAG,CAAC,mCAAmC,CAAC;QAC1D,OAAO,IAAI,CAACpB,SAAS,CAAC4B,MAAM,CAAClB,OAAO,EAAE,gBAAgB,EAAEK,QAAQ,EAAEpB,eAAe,CAAC;MACtF,CAAC,CAAC;MACF,IAAI,CAACQ,kBAAkB,CAAC0B,GAAG,CAACnB,OAAO,EAAE;QAAEG,OAAO;QAAEa;MAAS,CAAC,CAAC;MAC3D,OAAOb,OAAO;IAClB;IACAiB,cAAcA,CAACvB,YAAY,EAAE;MACzB,MAAMG,OAAO,GAAGtC,aAAa,CAACmC,YAAY,CAAC;MAC3C,MAAMI,IAAI,GAAG,IAAI,CAACR,kBAAkB,CAACS,GAAG,CAACF,OAAO,CAAC;MACjD,IAAIC,IAAI,EAAE;QACNA,IAAI,CAACe,QAAQ,CAAC,CAAC;QACff,IAAI,CAACE,OAAO,CAACkB,QAAQ,CAAC,CAAC;QACvBrB,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC,mCAAmC,CAAC;QAC7Df,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC,2BAA2B,CAAC;QACrD,IAAI,CAACtB,kBAAkB,CAAC6B,MAAM,CAACtB,OAAO,CAAC;MAC3C;IACJ;IACAuB,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC9B,kBAAkB,CAAC+B,OAAO,CAAC,CAACC,KAAK,EAAEzB,OAAO,KAAK,IAAI,CAACoB,cAAc,CAACpB,OAAO,CAAC,CAAC;IACrF;IACA,OAAOjC,IAAI,YAAA2D,wBAAAzD,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkB,eAAe;IAAA;IAClH,OAAOwC,KAAK,kBAvE6EzF,EAAE,CAAA0F,kBAAA;MAAAC,KAAA,EAuEY1C,eAAe;MAAA2C,OAAA,EAAf3C,eAAe,CAAApB,IAAA;MAAAgE,UAAA,EAAc;IAAM;EAC9I;EAAC,OAzDK5C,eAAe;AAAA;AA0DrB;EAAA,QAAAH,SAAA,oBAAAA,SAAA;AAAA;AAIA;AAAA,IACMgD,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdC,WAAW,GAAG3F,MAAM,CAACI,UAAU,CAAC;IAChCwF,gBAAgB,GAAG5F,MAAM,CAAC6C,eAAe,CAAC;IAC1C;IACAgD,WAAW,GAAG,IAAIxF,YAAY,CAAC,CAAC;IAChCgD,WAAWA,CAAA,EAAG,CAAE;IAChByC,QAAQA,CAAA,EAAG;MACP,IAAI,CAACF,gBAAgB,CAChBtC,OAAO,CAAC,IAAI,CAACqC,WAAW,CAAC,CACzBI,SAAS,CAAC/B,KAAK,IAAI,IAAI,CAAC6B,WAAW,CAACG,IAAI,CAAChC,KAAK,CAAC,CAAC;IACzD;IACAiB,WAAWA,CAAA,EAAG;MACV,IAAI,CAACW,gBAAgB,CAACd,cAAc,CAAC,IAAI,CAACa,WAAW,CAAC;IAC1D;IACA,OAAOlE,IAAI,YAAAwE,oBAAAtE,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+D,WAAW;IAAA;IAC9G,OAAOQ,IAAI,kBA7F8EtG,EAAE,CAAAuG,iBAAA;MAAArE,IAAA,EA6FJ4D,WAAW;MAAA3D,SAAA;MAAAqE,OAAA;QAAAP,WAAA;MAAA;IAAA;EACtG;EAAC,OAhBKH,WAAW;AAAA;AAiBjB;EAAA,QAAAhD,SAAA,oBAAAA,SAAA;AAAA;;AASA;AAAA,IACM2D,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtBV,WAAW,GAAG3F,MAAM,CAACI,UAAU,CAAC;IAChC0C,SAAS,GAAG9C,MAAM,CAACgB,QAAQ,CAAC;IAC5B+B,OAAO,GAAG/C,MAAM,CAACC,MAAM,CAAC;IACxB+C,SAAS,GAAGhD,MAAM,CAACQ,SAAS,CAAC;IAC7B8F,aAAa,GAAG,IAAIxF,OAAO,CAAC,CAAC;IAC7B;IACAyF,cAAc;IACdC,cAAc;IACdC,UAAU,GAAG,IAAI3F,OAAO,CAAC,CAAC;IAC1B4F,iBAAiB;IACjBC,QAAQ;IACRC,QAAQ;IACRC,QAAQ,GAAG,IAAI;IACf;AACJ;AACA;AACA;AACA;IACIC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,gBAAgB;IAChB;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACL,QAAQ;IACxB;IACA,IAAIK,OAAOA,CAACC,KAAK,EAAE;MACf,IAAI,CAACN,QAAQ,GAAGrF,oBAAoB,CAAC2F,KAAK,CAAC;MAC3C,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACP,QAAQ;IACxB;IACA,IAAIO,OAAOA,CAACF,KAAK,EAAE;MACf,IAAI,CAACL,QAAQ,GAAGtF,oBAAoB,CAAC2F,KAAK,CAAC;MAC3C,IAAI,CAACG,aAAa,CAAC,CAAC;IACxB;IACA;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACR,QAAQ;IACxB;IACA,IAAIQ,OAAOA,CAACJ,KAAK,EAAE;MACf;MACA;MACA,IAAI,IAAI,CAACJ,QAAQ,KAAKI,KAAK,EAAE;QACzB,CAAC,IAAI,CAACJ,QAAQ,GAAGI,KAAK,IAAI,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;MAC1E;IACJ;IACA,IAAIC,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACT,gBAAgB,CAACS,WAAW;IAC5C;IACA,IAAIA,WAAWA,CAACP,KAAK,EAAE;MACnB,IAAI,CAACQ,wBAAwB,GAAGC,SAAS;MACzC,IAAIT,KAAK,EAAE;QACP,IAAI,CAACF,gBAAgB,CAACY,YAAY,CAAC,aAAa,EAAEV,KAAK,CAAC;MAC5D,CAAC,MACI;QACD,IAAI,CAACF,gBAAgB,CAACa,eAAe,CAAC,aAAa,CAAC;MACxD;MACA,IAAI,CAACC,+BAA+B,CAAC,CAAC;IAC1C;IACA;IACAC,iBAAiB;IACjB;IACAL,wBAAwB;IACxB;IACAM,gBAAgB;IAChB;IACAC,SAAS,GAAGhI,MAAM,CAACS,QAAQ,EAAE;MAAEwH,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChDC,SAAS;IACTC,aAAa,GAAG,KAAK;IACrB9E,WAAWA,CAAA,EAAG;MACV,MAAM+E,WAAW,GAAGpI,MAAM,CAACkB,sBAAsB,CAAC;MAClDkH,WAAW,CAAC3E,IAAI,CAACjC,wBAAwB,CAAC;MAC1C,IAAI,CAACuF,gBAAgB,GAAG,IAAI,CAACpB,WAAW,CAAC0C,aAAa;IAC1D;IACA;IACAnB,aAAaA,CAAA,EAAG;MACZ,MAAMoB,SAAS,GAAG,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACc,iBAAiB,GAAG,GAAG,IAAI,CAACd,OAAO,GAAG,IAAI,CAACc,iBAAiB,IAAI,GAAG,IAAI;MAC9G,IAAIQ,SAAS,EAAE;QACX,IAAI,CAACvB,gBAAgB,CAACwB,KAAK,CAACD,SAAS,GAAGA,SAAS;MACrD;IACJ;IACA;IACAlB,aAAaA,CAAA,EAAG;MACZ,MAAMoB,SAAS,GAAG,IAAI,CAACrB,OAAO,IAAI,IAAI,CAACW,iBAAiB,GAAG,GAAG,IAAI,CAACX,OAAO,GAAG,IAAI,CAACW,iBAAiB,IAAI,GAAG,IAAI;MAC9G,IAAIU,SAAS,EAAE;QACX,IAAI,CAACzB,gBAAgB,CAACwB,KAAK,CAACC,SAAS,GAAGA,SAAS;MACrD;IACJ;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAAC3F,SAAS,CAACU,SAAS,EAAE;QAC1B;QACA,IAAI,CAACgD,cAAc,GAAG,IAAI,CAACO,gBAAgB,CAACwB,KAAK,CAACG,MAAM;QACxD,IAAI,CAACpB,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACvE,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;UACjC,IAAI,CAAC+B,iBAAiB,GAAG,CACrB,IAAI,CAAC1D,SAAS,CAAC4B,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC0B,aAAa,CAAChC,IAAI,CAAC,CAAC,CAAC,EAC1E,IAAI,CAACtB,SAAS,CAAC4B,MAAM,CAAC,IAAI,CAACmC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC4B,iBAAiB,CAAC,EAC7E,IAAI,CAAC3F,SAAS,CAAC4B,MAAM,CAAC,IAAI,CAACmC,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC4B,iBAAiB,CAAC,CAC/E;UACD,IAAI,CAACrC,aAAa,CAACsC,IAAI,CAACrH,SAAS,CAAC,EAAE,CAAC,CAAC,CAACwE,SAAS,CAAC,MAAM;YACnD;YACA;YACA,IAAI,CAAC+B,iBAAiB,GAAG,IAAI,CAACL,wBAAwB,GAAGC,SAAS;YAClE,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAC;UACjC,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAI,CAACa,aAAa,GAAG,IAAI;QACzB,IAAI,CAACb,kBAAkB,CAAC,IAAI,CAAC;MACjC;IACJ;IACArC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACyB,iBAAiB,EAAExB,OAAO,CAAC2D,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MACrD,IAAI,CAACvC,aAAa,CAACvB,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC0B,UAAU,CAACnC,IAAI,CAAC,CAAC;MACtB,IAAI,CAACmC,UAAU,CAAC1B,QAAQ,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI+D,wBAAwBA,CAAA,EAAG;MACvB,IAAI,IAAI,CAAChB,iBAAiB,EAAE;QACxB;MACJ;MACA;MACA,MAAMiB,aAAa,GAAG,IAAI,CAAChC,gBAAgB,CAACiC,SAAS,CAAC,KAAK,CAAC;MAC5D,MAAMC,WAAW,GAAGF,aAAa,CAACR,KAAK;MACvCQ,aAAa,CAACG,IAAI,GAAG,CAAC;MACtB;MACA;MACA;MACAD,WAAW,CAACE,QAAQ,GAAG,UAAU;MACjCF,WAAW,CAACG,UAAU,GAAG,QAAQ;MACjCH,WAAW,CAACI,MAAM,GAAG,MAAM;MAC3BJ,WAAW,CAACK,OAAO,GAAG,GAAG;MACzBL,WAAW,CAACP,MAAM,GAAG,EAAE;MACvBO,WAAW,CAACX,SAAS,GAAG,EAAE;MAC1BW,WAAW,CAACT,SAAS,GAAG,EAAE;MAC1B;MACAS,WAAW,CAACM,GAAG,GAAGN,WAAW,CAACO,MAAM,GAAGP,WAAW,CAACQ,IAAI,GAAGR,WAAW,CAACS,KAAK,GAAG,MAAM;MACpF;MACA;MACA;MACA;MACA;MACAT,WAAW,CAACU,QAAQ,GAAG,QAAQ;MAC/B,IAAI,CAAC5C,gBAAgB,CAAC6C,UAAU,CAACC,WAAW,CAACd,aAAa,CAAC;MAC3D,IAAI,CAACjB,iBAAiB,GAAGiB,aAAa,CAACe,YAAY;MACnDf,aAAa,CAACtE,MAAM,CAAC,CAAC;MACtB;MACA,IAAI,CAACyC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACE,aAAa,CAAC,CAAC;IACxB;IACA2C,oBAAoBA,CAAA,EAAG;MACnB,MAAMrG,OAAO,GAAG,IAAI,CAACqD,gBAAgB;MACrC,MAAMiD,cAAc,GAAGtG,OAAO,CAAC6E,KAAK,CAAC0B,YAAY,IAAI,EAAE;MACvD,MAAMC,SAAS,GAAG,IAAI,CAACpH,SAAS,CAACqH,OAAO;MACxC,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,IAAI,CAAChC,SAAS;MACrD,MAAMmC,cAAc,GAAGH,SAAS,GAC1B,yCAAyC,GACzC,iCAAiC;MACvC;MACA;MACA;MACA,IAAIE,iBAAiB,EAAE;QACnB1G,OAAO,CAAC6E,KAAK,CAAC0B,YAAY,GAAG,GAAGvG,OAAO,CAACoG,YAAY,IAAI;MAC5D;MACA;MACA;MACApG,OAAO,CAACQ,SAAS,CAACE,GAAG,CAACiG,cAAc,CAAC;MACrC;MACA;MACA,MAAMC,YAAY,GAAG5G,OAAO,CAAC4G,YAAY,GAAG,CAAC;MAC7C5G,OAAO,CAACQ,SAAS,CAACO,MAAM,CAAC4F,cAAc,CAAC;MACxC,IAAID,iBAAiB,EAAE;QACnB1G,OAAO,CAAC6E,KAAK,CAAC0B,YAAY,GAAGD,cAAc;MAC/C;MACA,OAAOM,YAAY;IACvB;IACAzC,+BAA+BA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAACM,aAAa,IAAI,IAAI,CAACV,wBAAwB,IAAIC,SAAS,EAAE;QACnE;MACJ;MACA,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;QACnB,IAAI,CAACC,wBAAwB,GAAG,CAAC;QACjC;MACJ;MACA,MAAMR,KAAK,GAAG,IAAI,CAACF,gBAAgB,CAACE,KAAK;MACzC,IAAI,CAACF,gBAAgB,CAACE,KAAK,GAAG,IAAI,CAACF,gBAAgB,CAACS,WAAW;MAC/D,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACsC,oBAAoB,CAAC,CAAC;MAC3D,IAAI,CAAChD,gBAAgB,CAACE,KAAK,GAAGA,KAAK;IACvC;IACA;IACA0B,iBAAiB,GAAI3E,KAAK,IAAK;MAC3B,IAAI,CAACkE,SAAS,GAAGlE,KAAK,CAAClC,IAAI,KAAK,OAAO;IAC3C,CAAC;IACDyI,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACzH,SAAS,CAACU,SAAS,EAAE;QAC1B,IAAI,CAAC8D,kBAAkB,CAAC,CAAC;MAC7B;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIA,kBAAkBA,CAACkD,KAAK,GAAG,KAAK,EAAE;MAC9B;MACA,IAAI,CAAC,IAAI,CAAC3D,QAAQ,EAAE;QAChB;MACJ;MACA,IAAI,CAACiC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACjB,+BAA+B,CAAC,CAAC;MACtC,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAAChB,gBAAgB,CAAC0D,SAAS;MACvD;MACA;MACA,IAAI,CAAC,IAAI,CAAC3C,iBAAiB,EAAE;QACzB;MACJ;MACA,MAAM4C,QAAQ,GAAG,IAAI,CAAC/E,WAAW,CAAC0C,aAAa;MAC/C,MAAMpB,KAAK,GAAGyD,QAAQ,CAACzD,KAAK;MAC5B;MACA,IAAI,CAACuD,KAAK,IAAI,IAAI,CAAC7D,QAAQ,KAAK,IAAI,CAACG,gBAAgB,IAAIG,KAAK,KAAK,IAAI,CAACV,cAAc,EAAE;QACpF;MACJ;MACA,MAAM+D,YAAY,GAAG,IAAI,CAACP,oBAAoB,CAAC,CAAC;MAChD,MAAMrB,MAAM,GAAGiC,IAAI,CAACC,GAAG,CAACN,YAAY,EAAE,IAAI,CAAC7C,wBAAwB,IAAI,CAAC,CAAC;MACzE;MACAiD,QAAQ,CAACnC,KAAK,CAACG,MAAM,GAAG,GAAGA,MAAM,IAAI;MACrC,IAAI,CAAC3F,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;QACjC,IAAI,OAAOkG,qBAAqB,KAAK,WAAW,EAAE;UAC9CA,qBAAqB,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAACJ,QAAQ,CAAC,CAAC;QACtE,CAAC,MACI;UACDK,UAAU,CAAC,MAAM,IAAI,CAACD,sBAAsB,CAACJ,QAAQ,CAAC,CAAC;QAC3D;MACJ,CAAC,CAAC;MACF,IAAI,CAACnE,cAAc,GAAGU,KAAK;MAC3B,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACH,QAAQ;IACzC;IACA;AACJ;AACA;IACIY,KAAKA,CAAA,EAAG;MACJ;MACA;MACA,IAAI,IAAI,CAACf,cAAc,KAAKkB,SAAS,EAAE;QACnC,IAAI,CAACX,gBAAgB,CAACwB,KAAK,CAACG,MAAM,GAAG,IAAI,CAAClC,cAAc;MAC5D;IACJ;IACAwE,iBAAiBA,CAAA,EAAG;MAChB;IAAA;IAEJ;AACJ;AACA;AACA;AACA;IACIF,sBAAsBA,CAACJ,QAAQ,EAAE;MAC7B,MAAM;QAAEO,cAAc;QAAEC;MAAa,CAAC,GAAGR,QAAQ;MACjD;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACjE,UAAU,CAAC0E,SAAS,IAAI,IAAI,CAACjD,SAAS,EAAE;QAC9CwC,QAAQ,CAACU,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;QACxDR,QAAQ,CAACD,SAAS,GAAG,IAAI,CAAC1C,gBAAgB;MAC9C;IACJ;IACA,OAAOtG,IAAI,YAAA4J,4BAAA1J,iBAAA;MAAA,YAAAA,iBAAA,IAAwF0E,mBAAmB;IAAA;IACtH,OAAOH,IAAI,kBA9X8EtG,EAAE,CAAAuG,iBAAA;MAAArE,IAAA,EA8XJuE,mBAAmB;MAAAtE,SAAA;MAAAC,SAAA,WAA8R,GAAG;MAAAsJ,YAAA,WAAAC,iCAAAlJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9XlTzC,EAAE,CAAA4L,UAAA,mBAAAC,6CAAA;YAAA,OA8XJnJ,GAAA,CAAA0I,iBAAA,CAAkB,CAAC;UAAA,CAAD,CAAC;QAAA;MAAA;MAAAU,MAAA;QAAA1E,OAAA;QAAAG,OAAA;QAAAE,OAAA,wCAA+M3G,gBAAgB;QAAA8G,WAAA;MAAA;MAAAmE,QAAA;IAAA;EAC7U;EAAC,OAtRKtF,mBAAmB;AAAA;AAuRzB;EAAA,QAAA3D,SAAA,oBAAAA,SAAA;AAAA;AAwBoB,IAEdkJ,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOnK,IAAI,YAAAoK,wBAAAlK,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiK,eAAe;IAAA;IAClH,OAAOE,IAAI,kBA5Z8ElM,EAAE,CAAAmM,gBAAA;MAAAjK,IAAA,EA4ZS8J;IAAe;IACnH,OAAOI,IAAI,kBA7Z8EpM,EAAE,CAAAqM,gBAAA;EA8Z/F;EAAC,OAJKL,eAAe;AAAA;AAKrB;EAAA,QAAAlJ,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAASG,eAAe,EAAE6C,WAAW,EAAEW,mBAAmB,EAAEuF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}