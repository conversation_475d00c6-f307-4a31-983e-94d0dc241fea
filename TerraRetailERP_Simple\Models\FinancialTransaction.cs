using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("FinancialTransactions")]
    public class FinancialTransaction
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(40)]
        public string TransactionNumber { get; set; } = string.Empty;

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        public TransactionType TransactionType { get; set; } = TransactionType.Sale;

        [StringLength(50)]
        public string? SourceType { get; set; } // Sale, Purchase, Receipt, Payment, etc.

        public int? SourceId { get; set; } // ID of source document

        [StringLength(40)]
        public string? SourceNumber { get; set; } // Number of source document

        // الحساب الرئيسي والفرعي بدلاً من CustomerId/SupplierId منفصلين
        public int MainAccountId { get; set; } // رقم الحساب الرئيسي من شجرة الحسابات

        public int? SubAccountId { get; set; } // رقم الحساب الفرعي (العميل/المورد/الخ)

        // معلومات إضافية للربط
        public int? CustomerId { get; set; } // للربط مع جدول العملاء

        public int? SupplierId { get; set; } // للربط مع جدول الموردين

        public int BranchId { get; set; }

        public int UserId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; } = 0;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Reference { get; set; }

        public TransactionStatus Status { get; set; } = TransactionStatus.Draft;

        public int? PaymentMethodId { get; set; }

        public int? JournalEntryId { get; set; }

        [StringLength(100)]
        public string? CheckNumber { get; set; }

        [StringLength(100)]
        public string? BankReference { get; set; }

        public DateTime? DueDate { get; set; }

        public DateTime? ClearanceDate { get; set; }

        [StringLength(20)]
        public string? CurrencyCode { get; set; } = "EGP";

        [Column(TypeName = "decimal(10,4)")]
        public decimal ExchangeRate { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        public decimal? TransactionFee { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsReconciled { get; set; } = false;

        public DateTime? ReconciledAt { get; set; }

        public int? ReconciledBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        public int? CancelledBy { get; set; }

        public DateTime? CancelledAt { get; set; }

        [StringLength(500)]
        public string? CancellationReason { get; set; }

        // Navigation Properties
        public virtual ChartOfAccount MainAccount { get; set; } = null!; // الحساب الرئيسي
        public virtual ChartOfAccount? SubAccount { get; set; } // الحساب الفرعي
        public virtual Customer? Customer { get; set; }
        public virtual Supplier? Supplier { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual PaymentMethod? PaymentMethod { get; set; }
        public virtual JournalEntry? JournalEntry { get; set; }
        public virtual User? ReconciledByUser { get; set; }
        public virtual User? CancelledByUser { get; set; }
    }

    [Table("TransactionTracking")]
    public class TransactionTracking
    {
        [Key]
        public long Id { get; set; }

        public long FinancialTransactionId { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // Created, Updated, Cancelled, Reconciled, etc.

        [StringLength(50)]
        public string? OldStatus { get; set; }

        [StringLength(50)]
        public string? NewStatus { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? OldAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? NewAmount { get; set; }

        [StringLength(2000)]
        public string? ChangeDetails { get; set; } // JSON of changes

        [StringLength(1000)]
        public string? Reason { get; set; }

        public int UserId { get; set; }

        public DateTime ActionDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        // Navigation Properties
        public virtual FinancialTransaction FinancialTransaction { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    [Table("AuditLog")]
    public class AuditLog
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(100)]
        public string TableName { get; set; } = string.Empty;

        public int RecordId { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // INSERT, UPDATE, DELETE

        [StringLength(2000)]
        public string? OldValues { get; set; } // JSON

        [StringLength(2000)]
        public string? NewValues { get; set; } // JSON

        [StringLength(2000)]
        public string? ChangedFields { get; set; } // JSON array of changed field names

        public int UserId { get; set; }

        public DateTime ActionDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(100)]
        public string? SessionId { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
    }

    [Table("SystemLogs")]
    public class SystemLog
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(50)]
        public string LogLevel { get; set; } = string.Empty; // INFO, WARNING, ERROR, CRITICAL

        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty; // Authentication, Database, API, etc.

        [Required]
        [StringLength(500)]
        public string Message { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? Details { get; set; } // JSON with additional details

        [StringLength(2000)]
        public string? StackTrace { get; set; }

        public int? UserId { get; set; }

        [StringLength(50)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(100)]
        public string? RequestPath { get; set; }

        [StringLength(20)]
        public string? HttpMethod { get; set; }

        public int? ResponseStatusCode { get; set; }

        public long? ResponseTime { get; set; } // in milliseconds

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User? User { get; set; }
    }

    [Table("NotificationLog")]
    public class NotificationLog
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(50)]
        public string NotificationType { get; set; } = string.Empty; // Email, SMS, Push, System

        [Required]
        [StringLength(200)]
        public string Recipient { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string Status { get; set; } = "Pending"; // Pending, Sent, Failed, Delivered

        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        public int? UserId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? SentAt { get; set; }

        public DateTime? DeliveredAt { get; set; }

        public int RetryCount { get; set; } = 0;

        public DateTime? NextRetryAt { get; set; }

        [StringLength(100)]
        public string? ExternalId { get; set; } // ID from external service

        [StringLength(2000)]
        public string? Metadata { get; set; } // JSON with additional data

        // Navigation Properties
        public virtual User? User { get; set; }
    }
}
