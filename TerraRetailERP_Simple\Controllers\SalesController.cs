using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("💰 Sales Management")]
    public class SalesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public SalesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Sale>>> GetSales(
            [FromQuery] string? search = null,
            [FromQuery] int? customerId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Sales
                    .Include(s => s.Customer)
                    .Include(s => s.Branch)
                    .Include(s => s.User)
                    .Include(s => s.SaleItems)
                        .ThenInclude(si => si.Product)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(s => s.InvoiceNumber.Contains(search) || 
                                           s.CustomerName!.Contains(search) ||
                                           (s.Customer != null && s.Customer.NameAr.Contains(search)));
                }

                if (customerId.HasValue)
                    query = query.Where(s => s.CustomerId == customerId);

                if (status.HasValue)
                    query = query.Where(s => s.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate);

                var sales = await query
                    .OrderByDescending(s => s.InvoiceDate)
                    .Select(s => new
                    {
                        s.Id,
                        s.InvoiceNumber,
                        s.InvoiceDate,
                        s.CustomerId,
                        CustomerName = s.Customer != null ? s.Customer.NameAr : s.CustomerName,
                        BranchName = s.Branch.NameAr,
                        s.Status,
                        StatusName = s.Status == 1 ? "نشط" : "ملغي",
                        s.SaleType,
                        SaleTypeName = s.SaleType == 1 ? "نقدي" : "آجل",
                        s.SubTotal,
                        s.DiscountAmount,
                        s.TaxAmount,
                        s.TotalAmount,
                        s.PaidAmount,
                        s.RemainingAmount,
                        ItemsCount = s.SaleItems.Count,
                        s.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المبيعات بنجاح",
                    data = sales,
                    count = sales.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Sale>> GetSale(int id)
        {
            try
            {
                var sale = await _context.Sales
                    .Include(s => s.Customer)
                    .Include(s => s.Branch)
                    .Include(s => s.User)
                    .Include(s => s.SaleItems)
                        .ThenInclude(si => si.Product)
                            .ThenInclude(p => p.Unit)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (sale == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "فاتورة المبيعات غير موجودة" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات الفاتورة بنجاح",
                    data = sale
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Sale>> CreateSale(CreateSaleRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate invoice number
                var lastSale = await _context.Sales
                    .OrderByDescending(s => s.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastSale?.Id ?? 0) + 1;
                var invoiceNumber = $"SAL{nextId:D8}";

                // Calculate totals
                var subTotal = request.Items.Sum(i => i.Quantity * i.UnitPrice);
                var discountAmount = subTotal * (request.DiscountPercentage / 100);
                var taxableAmount = subTotal - discountAmount;
                var taxAmount = taxableAmount * (request.TaxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount;

                var sale = new Sale
                {
                    InvoiceNumber = invoiceNumber,
                    CustomerId = request.CustomerId,
                    CustomerName = request.CustomerName,
                    BranchId = request.BranchId,
                    UserId = request.UserId,
                    InvoiceDate = request.InvoiceDate ?? DateTime.Now,
                    Status = 1, // Active
                    SaleType = request.SaleType,
                    SubTotal = subTotal,
                    DiscountPercentage = request.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TaxPercentage = request.TaxPercentage,
                    TaxAmount = taxAmount,
                    TotalAmount = totalAmount,
                    PaidAmount = request.PaidAmount,
                    RemainingAmount = totalAmount - request.PaidAmount,
                    Notes = request.Notes,
                    CreatedAt = DateTime.Now
                };

                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Add sale items
                int lineNumber = 1;
                foreach (var item in request.Items)
                {
                    var lineTotal = item.Quantity * item.UnitPrice;
                    var lineDiscountAmount = lineTotal * (item.DiscountPercentage / 100);
                    var netLineTotal = lineTotal - lineDiscountAmount;
                    var lineTaxAmount = netLineTotal * (item.TaxPercentage / 100);
                    var finalTotal = netLineTotal + lineTaxAmount;

                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        UnitCostPrice = item.UnitCostPrice,
                        DiscountPercentage = item.DiscountPercentage,
                        DiscountAmount = lineDiscountAmount,
                        LineTotal = lineTotal,
                        TaxPercentage = item.TaxPercentage,
                        TaxAmount = lineTaxAmount,
                        FinalTotal = finalTotal,
                        ItemNotes = item.ItemNotes,
                        CreatedAt = DateTime.Now
                    };

                    _context.SaleItems.Add(saleItem);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Load related data for response
                await _context.Entry(sale)
                    .Reference(s => s.Customer)
                    .LoadAsync();
                await _context.Entry(sale)
                    .Reference(s => s.Branch)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetSale), new { id = sale.Id }, new
                {
                    success = true,
                    message = "تم إنشاء فاتورة المبيعات بنجاح",
                    data = sale
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}/cancel")]
        public async Task<IActionResult> CancelSale(int id, [FromBody] CancelSaleRequest request)
        {
            try
            {
                var sale = await _context.Sales.FindAsync(id);
                if (sale == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "فاتورة المبيعات غير موجودة" 
                    });

                if (sale.Status == 2)
                    return BadRequest(new 
                    { 
                        success = false,
                        message = "الفاتورة ملغية مسبقاً" 
                    });

                sale.Status = 2; // Cancelled
                sale.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم إلغاء الفاتورة بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetSalesDashboard([FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;

                // Today's sales
                var todaySales = await _context.Sales
                    .Where(s => s.InvoiceDate.Date == targetDate && s.Status == 1)
                    .GroupBy(s => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(s => s.TotalAmount),
                        Paid = g.Sum(s => s.PaidAmount),
                        Remaining = g.Sum(s => s.RemainingAmount)
                    })
                    .FirstOrDefaultAsync();

                // This month's sales
                var monthStart = new DateTime(targetDate.Year, targetDate.Month, 1);
                var monthSales = await _context.Sales
                    .Where(s => s.InvoiceDate >= monthStart && s.InvoiceDate < monthStart.AddMonths(1) && s.Status == 1)
                    .GroupBy(s => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(s => s.TotalAmount)
                    })
                    .FirstOrDefaultAsync();

                // Top selling products today
                var topProducts = await _context.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate.Date == targetDate && si.Sale.Status == 1)
                    .GroupBy(si => new { si.ProductId, si.Product.NameAr })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.NameAr,
                        Quantity = g.Sum(si => si.Quantity),
                        Amount = g.Sum(si => si.FinalTotal)
                    })
                    .OrderByDescending(p => p.Quantity)
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب لوحة تحكم المبيعات بنجاح",
                    data = new
                    {
                        today = todaySales ?? new { Count = 0, Total = 0m, Paid = 0m, Remaining = 0m },
                        month = monthSales ?? new { Count = 0, Total = 0m },
                        topProducts
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs
    public class CreateSaleRequest
    {
        public int? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public int BranchId { get; set; } = 1;
        public int UserId { get; set; } = 1;
        public DateTime? InvoiceDate { get; set; }
        public int SaleType { get; set; } = 1; // 1=Cash, 2=Credit
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public string? Notes { get; set; }
        public List<CreateSaleItemRequest> Items { get; set; } = new();
    }

    public class CreateSaleItemRequest
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal UnitCostPrice { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public string? ItemNotes { get; set; }
    }

    public class CancelSaleRequest
    {
        public string Reason { get; set; } = string.Empty;
    }
}
