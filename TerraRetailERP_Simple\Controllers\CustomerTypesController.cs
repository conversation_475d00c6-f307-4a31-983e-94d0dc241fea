using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomerTypesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public CustomerTypesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetCustomerTypes()
        {
            try
            {
                var customerTypes = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.DisplayOrder)
                    .ThenBy(ct => ct.NameAr)
                    .Select(ct => new
                    {
                        id = ct.Id,
                        nameAr = ct.NameAr,
                        nameEn = ct.NameEn,
                        description = ct.Description,
                        displayOrder = ct.DisplayOrder,
                        color = ct.Color,
                        icon = ct.Icon,
                        isActive = ct.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة أنواع العملاء بنجاح",
                    data = customerTypes,
                    count = customerTypes.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة أنواع العملاء",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetCustomerType(int id)
        {
            try
            {
                var customerType = await _context.CustomerTypes
                    .Where(ct => ct.Id == id && ct.IsActive)
                    .Select(ct => new
                    {
                        id = ct.Id,
                        nameAr = ct.NameAr,
                        nameEn = ct.NameEn,
                        description = ct.Description,
                        displayOrder = ct.DisplayOrder,
                        color = ct.Color,
                        icon = ct.Icon,
                        isActive = ct.IsActive,
                        createdAt = ct.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (customerType == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "نوع العميل غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات نوع العميل بنجاح",
                    data = customerType
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات نوع العميل",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreateCustomerType(CreateCustomerTypeRequest request)
        {
            try
            {
                var customerType = new CustomerType
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    DisplayOrder = request.DisplayOrder,
                    Color = request.Color,
                    Icon = request.Icon,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.CustomerTypes.Add(customerType);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCustomerType), new { id = customerType.Id }, new
                {
                    success = true,
                    message = "تم إضافة نوع العميل بنجاح",
                    data = new
                    {
                        id = customerType.Id,
                        nameAr = customerType.NameAr,
                        nameEn = customerType.NameEn,
                        description = customerType.Description,
                        displayOrder = customerType.DisplayOrder,
                        color = customerType.Color,
                        icon = customerType.Icon,
                        isActive = customerType.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة نوع العميل",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdateCustomerType(int id, UpdateCustomerTypeRequest request)
        {
            try
            {
                var customerType = await _context.CustomerTypes.FindAsync(id);
                if (customerType == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "نوع العميل غير موجود"
                    });
                }

                customerType.NameAr = request.NameAr;
                customerType.NameEn = request.NameEn;
                customerType.Description = request.Description;
                customerType.DisplayOrder = request.DisplayOrder;
                customerType.Color = request.Color;
                customerType.Icon = request.Icon;
                customerType.IsActive = request.IsActive;
                customerType.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات نوع العميل بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث نوع العميل",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteCustomerType(int id)
        {
            try
            {
                var customerType = await _context.CustomerTypes.FindAsync(id);
                if (customerType == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "نوع العميل غير موجود"
                    });
                }

                customerType.IsActive = false;
                customerType.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف نوع العميل بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف نوع العميل",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreateCustomerTypeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; } = 1;
        public string? Color { get; set; }
        public string? Icon { get; set; }
    }

    public class UpdateCustomerTypeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; } = 1;
        public string? Color { get; set; }
        public string? Icon { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
