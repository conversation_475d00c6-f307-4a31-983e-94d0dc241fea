using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الصلاحيات
    /// </summary>
    public class Permission : BaseEntity
    {
        /// <summary>
        /// اسم الصلاحية بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الصلاحية بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود الصلاحية (فريد)
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// وصف الصلاحية
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// المجموعة التي تنتمي إليها الصلاحية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Group { get; set; } = string.Empty;

        /// <summary>
        /// نوع الصلاحية
        /// </summary>
        public PermissionType PermissionType { get; set; }

        /// <summary>
        /// هل الصلاحية نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الصلاحية مدمجة في النظام
        /// </summary>
        public bool IsSystemPermission { get; set; } = false;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// الصلاحية الأب (للصلاحيات الهرمية)
        /// </summary>
        public int? ParentPermissionId { get; set; }

        /// <summary>
        /// مستوى الصلاحية في الهرم
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// أيقونة الصلاحية
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// لون الصلاحية (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// المسار في النظام (للصلاحيات المرتبطة بصفحات)
        /// </summary>
        [MaxLength(200)]
        public string? Path { get; set; }

        /// <summary>
        /// الطريقة HTTP المطلوبة
        /// </summary>
        [MaxLength(10)]
        public string? HttpMethod { get; set; }

        /// <summary>
        /// هل تتطلب تأكيد إضافي
        /// </summary>
        public bool RequiresConfirmation { get; set; } = false;

        // Navigation Properties
        public virtual Permission? ParentPermission { get; set; }
        public virtual ICollection<Permission> SubPermissions { get; set; } = new List<Permission>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }

    /// <summary>
    /// أنواع الصلاحيات
    /// </summary>
    public enum PermissionType
    {
        /// <summary>
        /// عرض/قراءة
        /// </summary>
        Read = 1,

        /// <summary>
        /// إنشاء/إضافة
        /// </summary>
        Create = 2,

        /// <summary>
        /// تعديل
        /// </summary>
        Update = 3,

        /// <summary>
        /// حذف
        /// </summary>
        Delete = 4,

        /// <summary>
        /// تصدير
        /// </summary>
        Export = 5,

        /// <summary>
        /// طباعة
        /// </summary>
        Print = 6,

        /// <summary>
        /// موافقة
        /// </summary>
        Approve = 7,

        /// <summary>
        /// رفض
        /// </summary>
        Reject = 8,

        /// <summary>
        /// إلغاء
        /// </summary>
        Cancel = 9,

        /// <summary>
        /// إدارة
        /// </summary>
        Manage = 10,

        /// <summary>
        /// تقارير
        /// </summary>
        Reports = 11,

        /// <summary>
        /// إعدادات
        /// </summary>
        Settings = 12,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 13
    }
}
