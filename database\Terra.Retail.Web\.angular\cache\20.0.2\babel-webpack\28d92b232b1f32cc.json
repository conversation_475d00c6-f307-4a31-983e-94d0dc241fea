{"ast": null, "code": "/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { validateStyleProperty, containsElement, getParentElement, invokeQuery, dashCaseToCamelCase, invalidCssUnitValue, invalidExpression, invalidTransitionAlias, visitDslNode, invalidTrigger, invalidDefinition, extractStyleParams, invalidState, invalidStyleValue, SUBSTITUTION_EXPR_START, invalidParallelAnimation, validateStyleParams, invalidKeyframes, invalidOffset, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, getOrSetDefaultValue, invalidStagger, resolveTiming, NG_TRIGGER_SELECTOR, NG_ANIMATING_SELECTOR, normalizeAnimationEntry, resolveTimingValue, interpolateParams, invalidQuery, registerFailed, normalizeKeyframes, LEAVE_CLASSNAME, ENTER_CLASSNAME, missingOrDestroyedAnimation, createAnimationFailed, optimizeGroupPlayer, missingPlayer, listenOnPlayer, makeAnimationEvent, triggerTransitionsFailed, eraseStyles, setStyles, transitionFailed, missingTrigger, missingEvent, unsupportedTriggerEvent, NG_TRIGGER_CLASSNAME, unregisteredTrigger, NG_ANIMATING_CLASSNAME, triggerBuildFailed, parseTimelineCommand, computeStyle, camelCaseToDashCase, validateWebAnimatableStyleProperty, allowPreviousPlayerStylesMerge, normalizeKeyframes$1, balancePreviousStylesIntoKeyframes, validationFailed, normalizeStyles, buildingFailed } from './util-CPU6TNml.mjs';\nimport { NoopAnimationPlayer, AnimationMetadataType, style, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationGroupPlayer } from './private_export-B_vy_9K7.mjs';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nlet NoopAnimationDriver = /*#__PURE__*/(() => {\n  class NoopAnimationDriver {\n    /**\n     * @returns Whether `prop` is a valid CSS property\n     */\n    validateStyleProperty(prop) {\n      return validateStyleProperty(prop);\n    }\n    /**\n     *\n     * @returns Whether elm1 contains elm2.\n     */\n    containsElement(elm1, elm2) {\n      return containsElement(elm1, elm2);\n    }\n    /**\n     * @returns Rhe parent of the given element or `null` if the element is the `document`\n     */\n    getParentElement(element) {\n      return getParentElement(element);\n    }\n    /**\n     * @returns The result of the query selector on the element. The array will contain up to 1 item\n     *     if `multi` is  `false`.\n     */\n    query(element, selector, multi) {\n      return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @returns The `defaultValue` or empty string\n     */\n    computeStyle(element, prop, defaultValue) {\n      return defaultValue || '';\n    }\n    /**\n     * @returns An `NoopAnimationPlayer`\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n      return new NoopAnimationPlayer(duration, delay);\n    }\n    static ɵfac = function NoopAnimationDriver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoopAnimationDriver)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NoopAnimationDriver,\n      factory: NoopAnimationDriver.ɵfac\n    });\n  }\n  return NoopAnimationDriver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @publicApi\n */\nclass AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static NOOP = /*#__PURE__*/new NoopAnimationDriver();\n}\nclass AnimationStyleNormalizer {}\nclass NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return propertyName;\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    return value;\n  }\n}\nconst DIMENSIONAL_PROP_SET = /*#__PURE__*/new Set(['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight', 'left', 'top', 'bottom', 'right', 'fontSize', 'outlineWidth', 'outlineOffset', 'paddingTop', 'paddingLeft', 'paddingBottom', 'paddingRight', 'marginTop', 'marginLeft', 'marginBottom', 'marginRight', 'borderRadius', 'borderWidth', 'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth', 'textIndent', 'perspective']);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return dashCaseToCamelCase(propertyName);\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    let unit = '';\n    const strVal = value.toString().trim();\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\nfunction createListOfWarnings(warnings) {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings.filter(Boolean).map(warning => warning).join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n  console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n  console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n  console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n  const expressions = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue.split(/\\s*,\\s*/).forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(transitionValue);\n  }\n  return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\nfunction parseAnimationAlias(alias, errors) {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n  return (fromState, toState) => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n    return lhsMatch && rhsMatch;\n  };\n}\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n  _driver;\n  constructor(_driver) {\n    this._driver = _driver;\n  }\n  build(metadata, errors, warnings) {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = visitDslNode(this, normalizeAnimationEntry(metadata), context);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [...context.unsupportedCSSPropertiesFound.keys()]);\n      }\n    }\n    return ast;\n  }\n  _resetContextStyleTimingState(context) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n  visitTrigger(metadata, context) {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states = [];\n    const transitions = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null\n    };\n  }\n  visitState(metadata, context) {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = metadata.options && metadata.options.params || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set();\n      const params = astParams || {};\n      styleAst.styles.forEach(style => {\n        if (style instanceof Map) {\n          style.forEach(value => {\n            extractStyleParams(value).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {\n        params: astParams\n      } : null\n    };\n  }\n  visitTransition(metadata, context) {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitSequence(metadata, context) {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitGroup(metadata, context) {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimate(metadata, context) {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst;\n    let styleMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n  visitStyle(metadata, context) {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n  _makeStyleAst(metadata, context) {\n    const styles = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n    let containsDynamicStyles = false;\n    let collectedEasing = null;\n    styles.forEach(styleData => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing');\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null\n    };\n  }\n  _validateStyleAst(ast, context) {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n    ast.styles.forEach(tuple => {\n      if (typeof tuple === 'string') return;\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime && endTime <= collectedEntry.endTime) {\n            context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n            updateCollectedStyle = false;\n          }\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {\n            startTime,\n            endTime\n          });\n        }\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n  visitKeyframes(metadata, context) {\n    const ast = {\n      type: AnimationMetadataType.Keyframes,\n      styles: [],\n      options: null\n    };\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n    const MAX_KEYFRAME_OFFSET = 1;\n    let totalKeyframesWithOffsets = 0;\n    const offsets = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset = 0;\n    const keyframes = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? i == limit ? 1 : generatedOffset * i : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n      ast.styles.push(kf);\n    });\n    return ast;\n  }\n  visitReference(metadata, context) {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateChild(metadata, context) {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateRef(metadata, context) {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitQuery(metadata, context) {\n    const parentSelector = context.currentQuerySelector;\n    const options = metadata.options || {};\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length ? parentSelector + ' ' + selector : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitStagger(metadata, context) {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings = metadata.timings === 'full' ? {\n      duration: 0,\n      delay: 0,\n      easing: 'full'\n    } : resolveTiming(metadata.timings, context.errors, true);\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null\n    };\n  }\n}\nfunction normalizeSelector(selector) {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR).replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.slice(1)).replace(/:animating/g, NG_ANIMATING_SELECTOR);\n  return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n  return obj ? {\n    ...obj\n  } : null;\n}\nclass AnimationAstBuilderContext {\n  errors;\n  queryCount = 0;\n  depCount = 0;\n  currentTransition = null;\n  currentQuery = null;\n  currentQuerySelector = null;\n  currentAnimateTimings = null;\n  currentTime = 0;\n  collectedStyles = /*#__PURE__*/new Map();\n  options = null;\n  unsupportedCSSPropertiesFound = /*#__PURE__*/new Set();\n  constructor(errors) {\n    this.errors = errors;\n  }\n}\nfunction consumeOffset(styles) {\n  if (typeof styles == 'string') return null;\n  let offset = null;\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset'));\n    obj.delete('offset');\n  }\n  return offset;\n}\nfunction constructTimingAst(value, errors) {\n  if (value.hasOwnProperty('duration')) {\n    return value;\n  }\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n  const strValue = value;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '');\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast;\n  }\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n  if (options) {\n    options = {\n      ...options\n    };\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']);\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n  return {\n    type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline\n  };\n}\nclass ElementInstructionMap {\n  _map = /*#__PURE__*/new Map();\n  get(element) {\n    return this._map.get(element) || [];\n  }\n  append(element, instructions) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n  has(element) {\n    return this._map.has(element);\n  }\n  clear() {\n    this._map.clear();\n  }\n}\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n  buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n    visitDslNode(this, ast, context);\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n  visitTrigger(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitState(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitTransition(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitAnimateChild(ast, context) {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitAnimateRef(ast, context) {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n  _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue = typeof animationDelay === 'number' ? animationDelay : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n  _visitSubInstructions(instructions, context, options) {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n    return furthestTime;\n  }\n  visitReference(ast, context) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n  visitSequence(ast, context) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx));\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitGroup(ast, context) {\n    const innerTimelines = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n  _visitTiming(ast, context) {\n    if (ast.dynamic) {\n      const strValue = ast.strValue;\n      const timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {\n        duration: ast.duration,\n        delay: ast.delay,\n        easing: ast.easing\n      };\n    }\n  }\n  visitAnimate(ast, context) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style, context);\n      timeline.applyStylesToKeyframe();\n    }\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n  visitStyle(ast, context) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings;\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n    const easing = timings && timings.easing || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n    context.previousNode = ast;\n  }\n  visitKeyframes(ast, context) {\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const startTime = context.currentTimeline.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n    ast.styles.forEach(step => {\n      const offset = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n  visitQuery(ast, context) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = ast.options || {};\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    if (delay && (context.previousNode.type === AnimationMetadataType.Style || startTime == 0 && context.currentTimeline.hasCurrentStyleProperties())) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n      visitDslNode(this, ast.animation, innerContext);\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n    context.previousNode = ast;\n  }\n  visitStagger(ast, context) {\n    const parentContext = context.parentContext;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime = tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n  _driver;\n  element;\n  subInstructions;\n  _enterClassName;\n  _leaveClassName;\n  errors;\n  timelines;\n  parentContext = null;\n  currentTimeline;\n  currentAnimateTimings = null;\n  previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n  subContextCount = 0;\n  options = {};\n  currentQueryIndex = 0;\n  currentQueryTotal = 0;\n  currentStaggerTime = 0;\n  constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n    this._driver = _driver;\n    this.element = element;\n    this.subInstructions = subInstructions;\n    this._enterClassName = _enterClassName;\n    this._leaveClassName = _leaveClassName;\n    this.errors = errors;\n    this.timelines = timelines;\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n  get params() {\n    return this.options.params;\n  }\n  updateOptions(options, skipIfExists) {\n    if (!options) return;\n    const newOptions = options;\n    let optionsToUpdate = this.options;\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n    }\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate = optionsToUpdate.params;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n  _copyOptions() {\n    const options = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params = options['params'] = {};\n        Object.keys(oldParams).forEach(name => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n  createSubContext(options = null, element, newTime) {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n  transformIntoNewTimeline(newTime) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n  appendInstructionToTimeline(instruction, duration, delay) {\n    const updatedTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n  incrementTime(time) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n  delayNextStep(delay) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n  invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n    let results = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\nclass TimelineBuilder {\n  _driver;\n  element;\n  startTime;\n  _elementTimelineStylesLookup;\n  duration = 0;\n  easing = null;\n  _previousKeyframe = /*#__PURE__*/new Map();\n  _currentKeyframe = /*#__PURE__*/new Map();\n  _keyframes = /*#__PURE__*/new Map();\n  _styleSummary = /*#__PURE__*/new Map();\n  _localTimelineStyles = /*#__PURE__*/new Map();\n  _globalTimelineStyles;\n  _pendingStyles = /*#__PURE__*/new Map();\n  _backFill = /*#__PURE__*/new Map();\n  _currentEmptyStepKeyframe = null;\n  constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n    this._driver = _driver;\n    this.element = element;\n    this.startTime = startTime;\n    this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map();\n    }\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n  containsAnimation() {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n  hasCurrentStyleProperties() {\n    return this._currentKeyframe.size > 0;\n  }\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n  delayNextStep(delay) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n  fork(element, currentTime) {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n  _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration);\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n  forwardTime(time) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n  _updateStyle(prop, value) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {\n      time: this.currentTime,\n      value\n    });\n  }\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n  applyEmptyStep(easing) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n  setStyles(input, easing, errors, options) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = options && options.params || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n  get properties() {\n    const properties = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n  mergeTimelineCollectedStyles(timeline) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n  buildKeyframes() {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set();\n    const postStyleProps = new Set();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n    let finalKeyframes = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === _PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n    const preProps = [...preStyleProps.values()];\n    const postProps = [...postStyleProps.values()];\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n    return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n  }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n  keyframes;\n  preStyleProps;\n  postStyleProps;\n  _stretchStartingKeyframe;\n  timings;\n  constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n    super(driver, element, timings.delay);\n    this.keyframes = keyframes;\n    this.preStyleProps = preStyleProps;\n    this.postStyleProps = postStyleProps;\n    this._stretchStartingKeyframe = _stretchStartingKeyframe;\n    this.timings = {\n      duration: timings.duration,\n      delay: timings.delay,\n      easing: timings.easing\n    };\n  }\n  containsAnimation() {\n    return this.keyframes.length > 1;\n  }\n  buildKeyframes() {\n    let keyframes = this.keyframes;\n    let {\n      delay,\n      duration,\n      easing\n    } = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n               delay=1000, duration=1000, keyframes = 0 .5 1\n               turns into\n               delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset');\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n      keyframes = newKeyframes;\n    }\n    return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n  }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n  const styles = new Map();\n  let allProperties;\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n  return {\n    type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n  _triggerName;\n  ast;\n  _stateStyles;\n  constructor(_triggerName, ast, _stateStyles) {\n    this._triggerName = _triggerName;\n    this.ast = ast;\n    this._stateStyles = _stateStyles;\n  }\n  match(currentState, nextState, element, params) {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n  buildStyles(stateName, params, errors) {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n  build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n    const errors = [];\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n    const queriedElements = new Set();\n    const preStyleMap = new Map();\n    const postStyleMap = new Map();\n    const isRemoval = nextState === 'void';\n    const animationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay\n    };\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n    let totalTime = 0;\n    timelines.forEach(tl => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n    if (errors.length) {\n      return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n      tl.preStyleProps.forEach(prop => preProps.add(prop));\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n      tl.postStyleProps.forEach(prop => postProps.add(prop));\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n    return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n  }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n  const allowedNonAnimatableProps = new Set([\n  // 'easing' is a utility/synthetic prop we use to represent\n  // easing functions, it represents a property of the animation\n  // which is not animatable but different values can be used\n  // in different steps\n  'easing']);\n  const invalidNonAnimatableProps = new Set();\n  timelines.forEach(({\n    keyframes\n  }) => {\n    const nonAnimatablePropsInitialValues = new Map();\n    keyframes.forEach(keyframe => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` + ' not animatable properties: ' + Array.from(invalidNonAnimatableProps).join(', ') + '\\n' + '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n  }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n  const result = {\n    ...defaults\n  };\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\nclass AnimationStateStyles {\n  styles;\n  defaultParams;\n  normalizer;\n  constructor(styles, defaultParams, normalizer) {\n    this.styles = styles;\n    this.defaultParams = defaultParams;\n    this.normalizer = normalizer;\n  }\n  buildStyles(params, errors) {\n    const finalStyles = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\nfunction buildTrigger(name, ast, normalizer) {\n  return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n  name;\n  ast;\n  _normalizer;\n  transitionFactories = [];\n  fallbackTransition;\n  states = /*#__PURE__*/new Map();\n  constructor(name, ast, _normalizer) {\n    this.name = name;\n    this.ast = ast;\n    this._normalizer = _normalizer;\n    ast.states.forEach(ast => {\n      const defaultParams = ast.options && ast.options.params || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n  matchTransition(currentState, nextState, element, params) {\n    const entry = this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n  matchStyles(currentState, params, errors) {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n  const matchers = [(fromState, toState) => true];\n  const animation = {\n    type: AnimationMetadataType.Sequence,\n    steps: [],\n    options: null\n  };\n  const transition = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1));\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2));\n  }\n}\nconst EMPTY_INSTRUCTION_MAP = /* @__PURE__ */new ElementInstructionMap();\nclass TimelineAnimationEngine {\n  bodyNode;\n  _driver;\n  _normalizer;\n  _animations = /*#__PURE__*/new Map();\n  _playersById = /*#__PURE__*/new Map();\n  players = [];\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n  }\n  register(id, metadata) {\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnRegister(warnings);\n        }\n      }\n      this._animations.set(id, ast);\n    }\n  }\n  _buildPlayer(i, preStyles, postStyles) {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n  create(id, element, options = {}) {\n    const errors = [];\n    const ast = this._animations.get(id);\n    let instructions;\n    const autoStylesMap = new Map();\n    if (ast) {\n      instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n        inst.postStyleProps.forEach(prop => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n    this.players.push(player);\n    return player;\n  }\n  destroy(id) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n  _getPlayer(id) {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n  listen(id, element, eventName, callback) {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n  command(id, element, command, args) {\n    if (command == 'register') {\n      this.register(id, args[0]);\n      return;\n    }\n    if (command == 'create') {\n      const options = args[0] || {};\n      this.create(id, element, options);\n      return;\n    }\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0]));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n  namespaceId;\n  value;\n  options;\n  get params() {\n    return this.options.params;\n  }\n  constructor(input, namespaceId = '') {\n    this.namespaceId = namespaceId;\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {\n        value,\n        ...options\n      } = input;\n      this.options = options;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n  absorbOptions(options) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /* @__PURE__ */new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n  id;\n  hostElement;\n  _engine;\n  players = [];\n  _triggers = /*#__PURE__*/new Map();\n  _queue = [];\n  _elementListeners = /*#__PURE__*/new Map();\n  _hostClassName;\n  constructor(id, hostElement, _engine) {\n    this.id = id;\n    this.hostElement = hostElement;\n    this._engine = _engine;\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n  listen(element, name, phase, callback) {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {\n      name,\n      phase,\n      callback\n    };\n    listeners.push(data);\n    const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n  register(name, ast) {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n  _getTrigger(name) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n  trigger(element, triggerName, value, defaultToFallback = true) {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = new Map());\n    }\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n    triggersWithStates.set(triggerName, toState);\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n    const isRemoval = toState.value === VOID_VALUE;\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n    const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n    let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition\n    });\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n    this.players.push(player);\n    playersOnElement.push(player);\n    return player;\n  }\n  deregister(name) {\n    this._triggers.delete(name);\n    this._engine.statesByElement.forEach(stateMap => stateMap.delete(name));\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(element, listeners.filter(entry => {\n        return entry.name != name;\n      }));\n    });\n  }\n  clearElementCache(element) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n  _signalRemovalForInnerTriggers(rootElement, context) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() => elements.forEach(elm => this.clearElementCache(elm)));\n  }\n  triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map();\n    if (triggerStates) {\n      const players = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  prepareLeaveAnimationListeners(element) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n        const trigger = this._triggers.get(triggerName);\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n  removeNode(element, context) {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n  insertNode(element, parent) {\n    addClass(element, this._hostClassName);\n  }\n  drainQueuedTransitions(microtaskId) {\n    const instructions = [];\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach(listener => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            baseEvent['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n    this._queue = [];\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n  destroy(context) {\n    this.players.forEach(p => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\nclass TransitionAnimationEngine {\n  bodyNode;\n  driver;\n  _normalizer;\n  players = [];\n  newHostElements = /*#__PURE__*/new Map();\n  playersByElement = /*#__PURE__*/new Map();\n  playersByQueriedElement = /*#__PURE__*/new Map();\n  statesByElement = /*#__PURE__*/new Map();\n  disabledNodes = /*#__PURE__*/new Set();\n  totalAnimations = 0;\n  totalQueuedPlayers = 0;\n  _namespaceLookup = {};\n  _namespaceList = [];\n  _flushFns = [];\n  _whenQuietFns = [];\n  namespacesByHostElement = /*#__PURE__*/new Map();\n  collectedEnterElements = [];\n  collectedLeaveElements = [];\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  /** @internal */\n  _onRemovalComplete(element, context) {\n    this.onRemovalComplete(element, context);\n  }\n  constructor(bodyNode, driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this.driver = driver;\n    this._normalizer = _normalizer;\n  }\n  get queuedPlayers() {\n    const players = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n  createNamespace(namespaceId, hostElement) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n  _balanceNamespaceList(ns, hostElement) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n  register(namespaceId, hostElement) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n  registerTrigger(namespaceId, name, trigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n  destroy(namespaceId, context) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n  _fetchNamespace(id) {\n    return this._namespaceLookup[id];\n  }\n  fetchNamespacesByElement(element) {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n  trigger(namespaceId, element, name, value) {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n  insertNode(namespaceId, element, parent, insertBefore) {\n    if (!isElementNode(element)) return;\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n  collectEnterElement(element) {\n    this.collectedEnterElements.push(element);\n  }\n  markElementAsDisabled(element, value) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n  removeNode(namespaceId, element, context) {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n  markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues\n    };\n  }\n  listen(namespaceId, element, name, phase, callback) {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n  _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n    return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n  destroyInnerAnimations(containerElement) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n    if (this.playersByQueriedElement.size == 0) return;\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n  destroyActiveAnimationsForElement(element) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n  finishActiveQueriedAnimationOnElement(element) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n  whenRenderingDone() {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n  processLeaveNode(element) {\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n  flush(microtaskId = -1) {\n    let players = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n    if (this._namespaceList.length && (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach(fn => fn());\n        });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n  reportError(errors) {\n    throw triggerTransitionsFailed(errors);\n  }\n  _flushAnimations(cleanupFns, microtaskId) {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers = [];\n    const skippedPlayersMap = new Map();\n    const queuedInstructions = [];\n    const queriedElements = new Map();\n    const allPreStyleElements = new Map();\n    const allPostStyleElements = new Map();\n    const disabledElementsSet = new Set();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    const allLeaveNodes = [];\n    const mergedLeaveNodes = new Set();\n    const leaveNodesWithoutAnimations = new Set();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG];\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n    const leaveNodeMapIds = new Map();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      allLeaveNodes.forEach(element => {\n        this.processLeaveNode(element);\n      });\n    });\n    const allPlayers = [];\n    const erroneousTransitions = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG];\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (details.previousTriggersValues && details.previousTriggersValues.has(entry.triggerName)) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName);\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName);\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n            player.destroy();\n            return;\n          }\n        }\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element);\n        const enterClassName = enterNodeMapIds.get(element);\n        const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines = [];\n        instruction.timelines.forEach(tl => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n        subTimelines.append(element, instruction.timelines);\n        const tuple = {\n          instruction,\n          player,\n          element\n        };\n        queuedInstructions.push(tuple);\n        instruction.queriedElements.forEach(element => getOrSetDefaultValue(queriedElements, element, []).push(player));\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal = allPreStyleElements.get(element);\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set());\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal = allPostStyleElements.get(element);\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set());\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n    if (erroneousTransitions.length) {\n      const errors = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n      });\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n    const allPreviousPlayersMap = new Map();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, _PRE_STYLE);\n    });\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n    const rootPlayers = [];\n    const subPlayers = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {\n        element,\n        player,\n        instruction\n      } = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd = [];\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n        const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n        player.setRealPlayer(innerPlayer);\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach(player => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG];\n      removeClass(element, LEAVE_CLASSNAME);\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n      let players = [];\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      const activePlayers = players.filter(p => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n    return rootPlayers;\n  }\n  afterFlush(callback) {\n    this._flushFns.push(callback);\n  }\n  afterFlushAnimationsDone(callback) {\n    this._whenQuietFns.push(callback);\n  }\n  _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n    let players = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n  _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer();\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n  _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers = [];\n    const allConsumedElements = new Set();\n    const allSubElements = new Set();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map(p => p.getRealPlayer())).filter(p => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p;\n        return pp.element ? pp.element === element : false;\n      });\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n      return player;\n    });\n    allQueriedPlayers.forEach(player => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(element => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n    return player;\n  }\n  _buildPlayer(instruction, keyframes, previousPlayers) {\n    if (keyframes.length > 0) {\n      return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n    }\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\nclass TransitionAnimationPlayer {\n  namespaceId;\n  triggerName;\n  element;\n  _player = /*#__PURE__*/new NoopAnimationPlayer();\n  _containsRealPlayer = false;\n  _queuedCallbacks = /*#__PURE__*/new Map();\n  destroyed = false;\n  parentPlayer = null;\n  markedForDestroy = false;\n  disabled = false;\n  queued = true;\n  totalTime = 0;\n  constructor(namespaceId, triggerName, element) {\n    this.namespaceId = namespaceId;\n    this.triggerName = triggerName;\n    this.element = element;\n  }\n  setRealPlayer(player) {\n    if (this._containsRealPlayer) return;\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    this.queued = false;\n  }\n  getRealPlayer() {\n    return this._player;\n  }\n  overrideTotalTime(totalTime) {\n    this.totalTime = totalTime;\n  }\n  syncPlayerEvents(player) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n  _queueEvent(name, callback) {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n  onDone(fn) {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n  onStart(fn) {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n  onDestroy(fn) {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n  init() {\n    this._player.init();\n  }\n  hasStarted() {\n    return this.queued ? false : this._player.hasStarted();\n  }\n  play() {\n    !this.queued && this._player.play();\n  }\n  pause() {\n    !this.queued && this._player.pause();\n  }\n  restart() {\n    !this.queued && this._player.restart();\n  }\n  finish() {\n    this._player.finish();\n  }\n  destroy() {\n    this.destroyed = true;\n    this._player.destroy();\n  }\n  reset() {\n    !this.queued && this._player.reset();\n  }\n  setPosition(p) {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n  getPosition() {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\nfunction isElementNode(node) {\n  return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n  return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n  const cloakVals = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n  const failedElements = [];\n  elementPropsMap.forEach((props, element) => {\n    const styles = new Map();\n    props.forEach(prop => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n  return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n  const rootMap = new Map();\n  roots.forEach(root => rootMap.set(root, []));\n  if (nodes.length == 0) return rootMap;\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map();\n  function getRoot(node) {\n    if (!node) return NULL_NODE;\n    let root = localRootMap.get(node);\n    if (root) return root;\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n    localRootMap.set(node, root);\n    return root;\n  }\n  nodes.forEach(node => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root).push(node);\n    }\n  });\n  return rootMap;\n}\nfunction addClass(element, className) {\n  element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n  element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n  const finalPlayers = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\nfunction objEquals(a, b) {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach(data => preEntry.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n  allPostStyleElements.delete(element);\n  return true;\n}\nclass AnimationEngine {\n  _driver;\n  _normalizer;\n  _transitionEngine;\n  _timelineEngine;\n  _triggerCache = {};\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  constructor(doc, _driver, _normalizer) {\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n    this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n  }\n  registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors = [];\n      const warnings = [];\n      const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnTriggerBuild(name, warnings);\n        }\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n  register(namespaceId, hostElement) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n  destroy(namespaceId, context) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n  onInsert(namespaceId, element, parent, insertBefore) {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n  onRemove(namespaceId, element, context) {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n  disableAnimations(element, disable) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n  process(namespaceId, element, property, value) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value;\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n  listen(namespaceId, element, eventName, eventPhase, callback) {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n  flush(microtaskId = -1) {\n    this._transitionEngine.flush(microtaskId);\n  }\n  get players() {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n  whenRenderingDone() {\n    return this._transitionEngine.whenRenderingDone();\n  }\n  afterFlushAnimationsDone(cb) {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n  let startStyles = null;\n  let endStyles = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nlet SpecialCasedStyles = /*#__PURE__*/(() => {\n  class SpecialCasedStyles {\n    _element;\n    _startStyles;\n    _endStyles;\n    static initialStylesByElement = /* @__PURE__ */new WeakMap();\n    _state = 0 /* SpecialCasedStylesState.Pending */;\n    _initialStyles;\n    constructor(_element, _startStyles, _endStyles) {\n      this._element = _element;\n      this._startStyles = _startStyles;\n      this._endStyles = _endStyles;\n      let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n      if (!initialStyles) {\n        SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = new Map());\n      }\n      this._initialStyles = initialStyles;\n    }\n    start() {\n      if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n        if (this._startStyles) {\n          setStyles(this._element, this._startStyles, this._initialStyles);\n        }\n        this._state = 1 /* SpecialCasedStylesState.Started */;\n      }\n    }\n    finish() {\n      this.start();\n      if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n        setStyles(this._element, this._initialStyles);\n        if (this._endStyles) {\n          setStyles(this._element, this._endStyles);\n          this._endStyles = null;\n        }\n        this._state = 1 /* SpecialCasedStylesState.Started */;\n      }\n    }\n    destroy() {\n      this.finish();\n      if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n        SpecialCasedStyles.initialStylesByElement.delete(this._element);\n        if (this._startStyles) {\n          eraseStyles(this._element, this._startStyles);\n          this._endStyles = null;\n        }\n        if (this._endStyles) {\n          eraseStyles(this._element, this._endStyles);\n          this._endStyles = null;\n        }\n        setStyles(this._element, this._initialStyles);\n        this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n      }\n    }\n  }\n  return SpecialCasedStyles;\n})();\nfunction filterNonAnimatableStyles(styles) {\n  let result = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\nfunction isNonAnimatableStyle(prop) {\n  return prop === 'display' || prop === 'position';\n}\nclass WebAnimationsPlayer {\n  element;\n  keyframes;\n  options;\n  _specialStyles;\n  _onDoneFns = [];\n  _onStartFns = [];\n  _onDestroyFns = [];\n  _duration;\n  _delay;\n  _initialized = false;\n  _finished = false;\n  _started = false;\n  _destroyed = false;\n  _finalKeyframe;\n  // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n  // and are used to reset the fns to their original values upon reset()\n  // (since the _onStartFns and _onDoneFns get deleted after they are called)\n  _originalOnDoneFns = [];\n  _originalOnStartFns = [];\n  // using non-null assertion because it's re(set) by init();\n  domPlayer;\n  time = 0;\n  parentPlayer = null;\n  currentSnapshot = /*#__PURE__*/new Map();\n  constructor(element, keyframes, options, _specialStyles) {\n    this.element = element;\n    this.keyframes = keyframes;\n    this.options = options;\n    this._specialStyles = _specialStyles;\n    this._duration = options['duration'];\n    this._delay = options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  init() {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n  _buildPlayer() {\n    if (this._initialized) return;\n    this._initialized = true;\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n  _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n  _convertKeyframesToObject(keyframes) {\n    const kfs = [];\n    keyframes.forEach(frame => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n  /** @internal */\n  _triggerWebAnimation(element, keyframes, options) {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  play() {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n  pause() {\n    this.init();\n    this.domPlayer.pause();\n  }\n  finish() {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n  reset() {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n  restart() {\n    this.reset();\n    this.play();\n  }\n  hasStarted() {\n    return this._started;\n  }\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  setPosition(p) {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n  getPosition() {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n  get totalTime() {\n    return this._delay + this._duration;\n  }\n  beforeDestroy() {\n    const styles = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\nclass WebAnimationsDriver {\n  validateStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n  validateAnimatableStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  computeStyle(element, prop, defaultValue) {\n    return computeStyle(element, prop);\n  }\n  animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions = {\n      duration,\n      delay,\n      fill\n    };\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n    const previousStyles = new Map();\n    const previousWebAnimationPlayers = previousPlayers.filter(player => player instanceof WebAnimationsPlayer);\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n    let _keyframes = normalizeKeyframes$1(keyframes).map(styles => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\nfunction createEngine(type, doc) {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n  }\n  return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\nclass Animation {\n  _driver;\n  _animationAst;\n  constructor(_driver, input) {\n    this._driver = _driver;\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (warnings.length) {\n        warnValidation(warnings);\n      }\n    }\n    this._animationAst = ast;\n  }\n  buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : destinationStyles;\n    const errors = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n  namespaceId;\n  delegate;\n  engine;\n  _onDestroy;\n  // We need to explicitly type this property because of an api-extractor bug\n  // See https://github.com/microsoft/rushstack/issues/4390\n  ɵtype = 0 /* AnimationRendererType.Regular */;\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n    // doesn't have a parent. There appears to be some animation-specific downstream logic that\n    // depends on the null check happening before the animation engine. This check keeps the old\n    // behavior while allowing `core` to not have to check for the parent element anymore.\n    if (this.parentNode(oldChild)) {\n      this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback, options) {\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  factory;\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback, options) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass AnimationRendererFactory {\n  delegate;\n  engine;\n  _zone;\n  _currentId = 0;\n  _microtaskId = 1;\n  _animationCallbacksBuffer = [];\n  _rendererCache = /*#__PURE__*/new Map();\n  _cdRecurDepth = 0;\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    engine.onRemovalComplete = (element, delegate) => {\n      delegate?.removeChild(null, element);\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this.engine.flush();\n    this.delegate.componentReplaced?.(componentId);\n  }\n}\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, ENTER_CLASSNAME as ɵENTER_CLASSNAME, LEAVE_CLASSNAME as ɵLEAVE_CLASSNAME, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, TransitionAnimationPlayer as ɵTransitionAnimationPlayer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes$1 as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };", "map": {"version": 3, "names": ["i0", "Injectable", "validateStyleProperty", "containsElement", "getParentElement", "invoke<PERSON><PERSON>y", "dashCaseToCamelCase", "invalidCssUnitValue", "invalidExpression", "invalidTransitionAlias", "visitDslNode", "invalid<PERSON><PERSON>ger", "invalidDefinition", "extractStyleParams", "invalidState", "invalidStyleV<PERSON>ue", "SUBSTITUTION_EXPR_START", "invalidParallelAnimation", "validateStyleParams", "invalidKeyframes", "invalidOffset", "keyframeOffsetsOutOfOrder", "keyframesMissingOffsets", "getOrSetDefaultValue", "invalidStagger", "resolveTiming", "NG_TRIGGER_SELECTOR", "NG_ANIMATING_SELECTOR", "normalizeAnimationEntry", "resolveTimingValue", "interpolateParams", "<PERSON><PERSON><PERSON><PERSON>", "registerFailed", "normalizeKeyframes", "LEAVE_CLASSNAME", "ENTER_CLASSNAME", "missingOrDestroyedAnimation", "createAnimationFailed", "optimizeGroupPlayer", "missingPlayer", "listenOnPlayer", "makeAnimationEvent", "triggerTransitionsFailed", "eraseStyles", "setStyles", "transitionFailed", "missing<PERSON><PERSON>ger", "missingEvent", "unsupportedTriggerEvent", "NG_TRIGGER_CLASSNAME", "unregisteredTrigger", "NG_ANIMATING_CLASSNAME", "triggerBuildFailed", "parseTimelineCommand", "computeStyle", "camelCaseToDashCase", "validateWebAnimatableStyleProperty", "allowPreviousPlayerStylesMerge", "normalizeKeyframes$1", "balancePreviousStylesIntoKeyframes", "validationFailed", "normalizeStyles", "buildingFailed", "NoopAnimationPlayer", "AnimationMetadataType", "style", "AUTO_STYLE", "ɵPRE_STYLE", "_PRE_STYLE", "AnimationGroupPlayer", "NoopAnimationDriver", "prop", "elm1", "elm2", "element", "query", "selector", "multi", "defaultValue", "animate", "keyframes", "duration", "delay", "easing", "previousPlayers", "scrubberAccessRequested", "ɵfac", "NoopAnimationDriver_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "AnimationDriver", "NOOP", "AnimationStyleNormalizer", "NoopAnimationStyleNormalizer", "normalizePropertyName", "propertyName", "errors", "normalizeStyleValue", "userProvidedProperty", "normalizedProperty", "value", "DIMENSIONAL_PROP_SET", "Set", "WebAnimationsStyleNormalizer", "unit", "strVal", "toString", "trim", "has", "valAndSuffixMatch", "match", "length", "push", "createListOfWarnings", "warnings", "LINE_START", "filter", "Boolean", "map", "warning", "join", "warnValidation", "console", "warn", "warnTriggerBuild", "name", "warnRegister", "pushUnrecognizedPropertiesWarning", "props", "ANY_STATE", "parseTransitionExpr", "transitionValue", "expressions", "split", "for<PERSON>ach", "str", "parseInnerTransitionStr", "eventStr", "result", "parseAnimationAlias", "fromState", "separator", "toState", "makeLambdaFromStates", "isFullAnyStateExpr", "alias", "parseFloat", "TRUE_BOOLEAN_VALUES", "FALSE_BOOLEAN_VALUES", "lhs", "rhs", "LHS_MATCH_BOOLEAN", "RHS_MATCH_BOOLEAN", "lhsMatch", "rhsMatch", "SELF_TOKEN", "SELF_TOKEN_REGEX", "RegExp", "buildAnimationAst", "driver", "metadata", "AnimationAstBuilderVisitor", "build", "ROOT_SELECTOR", "_driver", "constructor", "context", "AnimationAstBuilderContext", "_resetContextStyleTimingState", "ast", "unsupportedCSSPropertiesFound", "size", "keys", "currentQuerySelector", "collectedStyles", "Map", "set", "currentTime", "visitTrigger", "queryCount", "depCount", "states", "transitions", "char<PERSON>t", "definitions", "def", "type", "State", "stateDef", "n", "visitState", "Transition", "transition", "visitTransition", "<PERSON><PERSON>", "options", "styleAst", "visitStyle", "styles", "astParams", "params", "containsDynamicStyles", "missingSubs", "sub", "hasOwnProperty", "add", "values", "animation", "matchers", "expr", "normalizeAnimationOptions", "visitSequence", "Sequence", "steps", "s", "visitGroup", "furthestTime", "step", "innerAst", "Math", "max", "Group", "visitAnimate", "timingAst", "constructTimingAst", "timings", "currentAnimateTimings", "styleMetadata", "Keyframes", "visitKeyframes", "isEmpty", "newStyleData", "_styleAst", "isEmptyStep", "Animate", "_makeStyleAst", "_validateStyleAst", "metadataStyles", "Array", "isArray", "styleTuple", "Object", "entries", "collectedEasing", "styleData", "get", "delete", "indexOf", "Style", "offset", "endTime", "startTime", "tuple", "collectedEntry", "updateCollectedStyle", "MAX_KEYFRAME_OFFSET", "totalKeyframesWithOffsets", "offsets", "offsetsOutOfOrder", "keyframesOutOfRange", "previousOffset", "offsetVal", "consumeOffset", "generatedOffset", "limit", "animateDuration", "kf", "i", "durationUpToThisFrame", "visitReference", "Reference", "visitAnimateChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visitAnimateRef", "AnimateRef", "visit<PERSON><PERSON><PERSON>", "parentSelector", "<PERSON><PERSON><PERSON><PERSON>", "includeSelf", "normalizeSelector", "Query", "optional", "originalSelector", "visitStagger", "Stagger", "hasAmpersand", "find", "replace", "slice", "normalizeParams", "obj", "currentTransition", "makeTimingAst", "strValue", "isDynamic", "some", "v", "dynamic", "createTimelineInstruction", "preStyleProps", "postStyleProps", "subTimeline", "totalTime", "ElementInstructionMap", "_map", "append", "instructions", "existingInstructions", "clear", "ONE_FRAME_IN_MILLISECONDS", "ENTER_TOKEN", "ENTER_TOKEN_REGEX", "LEAVE_TOKEN", "LEAVE_TOKEN_REGEX", "buildAnimationTimelines", "rootElement", "enterClassName", "leaveClassName", "startingStyles", "finalStyles", "subInstructions", "AnimationTimelineBuilderVisitor", "buildKeyframes", "AnimationTimelineContext", "currentTimeline", "delayNextStep", "timelines", "timeline", "containsAnimation", "lastRootTimeline", "allowOnlyTimelineStyles", "elementInstructions", "innerContext", "createSubContext", "_visitSubInstructions", "transformIntoNewTimeline", "previousNode", "_applyAnimationRefDelays", "animationsRefsOptions", "animationRefOptions", "animationDelay", "animationDelayValue", "instruction", "instructionTimings", "appendInstructionToTimeline", "updateOptions", "subContextCount", "ctx", "snapshotCurrentStyles", "DEFAULT_NOOP_PREVIOUS_NODE", "applyStylesToKeyframe", "innerTimelines", "mergeTimelineCollectedStyles", "_visitTiming", "timingValue", "incrementTime", "hasCurrentStyleProperties", "<PERSON><PERSON><PERSON><PERSON>", "applyEmptyStep", "innerTimeline", "forwardTime", "elms", "currentQueryTotal", "sameElementTimeline", "currentQueryIndex", "parentContext", "tl", "abs", "maxTime", "staggerTransformer", "currentStaggerTime", "startingTime", "_enterClassName", "_leaveClassName", "initialTimeline", "TimelineBuilder", "skipIfExists", "newOptions", "optionsToUpdate", "newParams", "paramsToUpdate", "_copyOptions", "oldParams", "newTime", "target", "fork", "updatedTimings", "builder", "SubTimelineBuilder", "stretchStartingKeyframe", "time", "results", "elements", "_elementTimelineStylesLookup", "_previousKeyframe", "_currentKeyframe", "_keyframes", "_styleSummary", "_localTimelineStyles", "_globalTimelineStyles", "_pendingStyles", "_backFill", "_currentEmptyStepKeyframe", "_loadKeyframe", "hasPreStyleStep", "_updateStyle", "input", "flattenStyles", "val", "getFinalKeyframe", "properties", "details1", "details0", "finalKeyframes", "keyframe", "finalKeyframe", "preProps", "postProps", "kf0", "kf1", "_stretchStartingKeyframe", "newKeyframes", "startingGap", "newFirstKeyframe", "oldFirstKeyframe", "roundOffset", "oldOffset", "timeAtKeyframe", "decimalPoints", "mult", "pow", "round", "allStyles", "allProperties", "createTransitionInstruction", "triggerName", "isRemovalTransition", "fromStyles", "to<PERSON><PERSON>les", "queriedElements", "EMPTY_OBJECT", "AnimationTransitionFactory", "_triggerName", "_stateStyles", "currentState", "nextState", "oneOrMoreTransitionsMatch", "buildStyles", "stateName", "styler", "undefined", "currentOptions", "nextOptions", "skipAstBuild", "transitionAnimationParams", "currentAnimationParams", "currentStateStyles", "nextAnimationParams", "nextStateStyles", "preStyleMap", "postStyleMap", "isRemoval", "animationOptions", "applyParamDefaults", "elm", "checkNonAnimatableInTimelines", "validateAnimatableStyleProperty", "allowedNonAnimatableProps", "invalidNonAnimatableProps", "nonAnimatablePropsInitialValues", "entriesToCheck", "from", "propInitialValue", "matchFns", "fn", "userParams", "defaults", "key", "AnimationStateStyles", "defaultParams", "normalizer", "combinedParams", "normalizedProp", "buildTrigger", "AnimationTrigger", "_normalizer", "transitionFactories", "fallbackTransition", "balanceProperties", "createFallbackTransition", "containsQueries", "matchTransition", "entry", "f", "matchStyles", "stateMap", "key1", "key2", "EMPTY_INSTRUCTION_MAP", "TimelineAnimationEngine", "bodyNode", "_animations", "_playersById", "players", "register", "id", "_buildPlayer", "preStyles", "postStyles", "create", "autoStylesMap", "inst", "_", "player", "onDestroy", "destroy", "_getPlayer", "index", "splice", "listen", "eventName", "callback", "baseEvent", "command", "args", "play", "pause", "reset", "restart", "finish", "init", "setPosition", "QUEUED_CLASSNAME", "QUEUED_SELECTOR", "DISABLED_CLASSNAME", "DISABLED_SELECTOR", "STAR_CLASSNAME", "STAR_SELECTOR", "EMPTY_PLAYER_ARRAY", "NULL_REMOVAL_STATE", "namespaceId", "setForRemoval", "setForMove", "hasAnimation", "removed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NULL_REMOVED_QUERIED_STATE", "REMOVAL_FLAG", "StateValue", "isObj", "normalizeTriggerValue", "absorbOptions", "VOID_VALUE", "DEFAULT_STATE_VALUE", "AnimationTransitionNamespace", "hostElement", "_engine", "_triggers", "_queue", "_elementListeners", "_hostClassName", "addClass", "phase", "isTriggerEventValid", "listeners", "data", "triggersWithStates", "statesByElement", "afterFlush", "_getTrigger", "trigger", "defaultToFallback", "TransitionAnimationPlayer", "obj<PERSON><PERSON><PERSON>", "reportError", "playersOnElement", "players<PERSON>y<PERSON><PERSON>", "queued", "isFallbackTransition", "totalQueuedPlayers", "onStart", "removeClass", "onDone", "deregister", "clearElementCache", "elementPlayers", "_signalRemovalForInnerTriggers", "namespaces", "fetchNamespacesByElement", "ns", "triggerLeaveAnimation", "afterFlushAnimationsDone", "destroyAfterComplete", "triggerStates", "previousTriggersValues", "state", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>oved", "processLeaveNode", "prepareLeaveAnimationListeners", "elementStates", "visitedTriggers", "listener", "removeNode", "engine", "childElementCount", "containsPotentialParentTransition", "totalAnimations", "currentPlayers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "parentNode", "triggers", "removalFlag", "destroyInnerAnimations", "_onRemovalComplete", "insertNode", "drainQueuedTransitions", "microtaskId", "destroyed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "d0", "d1", "p", "TransitionAnimationEngine", "newHostElements", "disabledNodes", "_namespaceLookup", "_namespaceList", "_flushFns", "_whenQuietFns", "namespacesByHostElement", "collectedEnterElements", "collectedLeaveElements", "onRemovalComplete", "queuedPlayers", "createNamespace", "_balanceNamespaceList", "collectEnterElement", "namespaceList", "found", "ancestor", "ancestorNs", "unshift", "registerTrigger", "_fetchNamespace", "stateValue", "isElementNode", "insertBefore", "details", "markElementAsDisabled", "hostNS", "_buildInstruction", "subTimelines", "skipBuildAst", "containerElement", "destroyActiveAnimationsForElement", "finishActiveQueriedAnimationOnElement", "whenRenderingDone", "Promise", "resolve", "classList", "contains", "node", "flush", "cleanupFns", "_flushAnimations", "quietFns", "skippedPlayers", "skippedPlayersMap", "queuedInstructions", "allPreStyleElements", "allPostStyleElements", "disabledElementsSet", "nodesThatAreDisabled", "allTriggerElements", "enterNodeMap", "buildRootMap", "enterNodeMapIds", "nodes", "root", "className", "allLeaveNodes", "mergedLeaveNodes", "leaveNodesWithoutAnimations", "leaveNodeMapIds", "leaveNodeMap", "allPlayers", "erroneousTransitions", "previousValue", "nodeIsOrphaned", "stringMap", "setVal", "allPreviousPlayersMap", "animationElementMap", "_beforeAnimationBuild", "_getPreviousPlayers", "prevPlayer", "replaceNodes", "replacePostStylesAsPre", "postStylesMap", "allLeaveQueriedNodes", "cloakAndComputeStyles", "preStylesMap", "post", "pre", "rootPlayers", "subPlayers", "NO_PARENT_ANIMATION_ELEMENT_DETECTED", "disabled", "overrideTotalTime", "parentWithAnimation", "parentsToAdd", "detectedParent", "innerPlayer", "_buildAnimation", "setRealPlayer", "parentPlayers", "parentPlayer", "playersFor<PERSON>lement", "syncPlayerEvents", "queriedPlayerResults", "queriedInnerElements", "j", "queriedPlayers", "activePlayers", "removeNodesAfterAnimationDone", "isQueriedElement", "toStateValue", "queriedElementPlayers", "isRemovalAnimation", "targetNameSpaceId", "targetTriggerName", "timelineInstruction", "realPlayer", "getRealPlayer", "<PERSON><PERSON><PERSON><PERSON>", "allQueriedPlayers", "allConsumedElements", "allSubElements", "allNewPlayers", "flattenGroupPlayers", "pp", "wrappedPlayer", "deleteOrUnsetInMap", "_player", "_containsRealPlayer", "_queuedCallbacks", "callbacks", "triggerCallback", "_queueEvent", "hasStarted", "getPosition", "phaseName", "currentV<PERSON>ues", "cloakElement", "oldValue", "display", "valuesMap", "elementPropsMap", "defaultStyle", "cloakVals", "failedElements", "roots", "rootMap", "NULL_NODE", "nodeSet", "localRootMap", "getRoot", "remove", "finalPlayers", "_flattenGroupPlayersRecur", "k1", "k2", "postEntry", "preEntry", "AnimationEngine", "_transitionEngine", "_timelineEngine", "_triggerCache", "doc", "body", "componentId", "cache<PERSON>ey", "onInsert", "onRemove", "disableAnimations", "disable", "process", "property", "action", "eventPhase", "cb", "packageNonAnimatableStyles", "startStyles", "endStyles", "filterNonAnimatableStyles", "SpecialCasedStyles", "_element", "_startStyles", "_endStyles", "initialStylesByElement", "WeakMap", "_state", "_initialStyles", "initialStyles", "start", "isNonAnimatableStyle", "WebAnimationsPlayer", "_specialStyles", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_duration", "_delay", "_initialized", "_finished", "_started", "_destroyed", "_finalKeyframe", "_originalOnDoneFns", "_originalOnStartFns", "domPlayer", "currentSnapshot", "_onFinish", "_preparePlayerBeforeStart", "_triggerWebAnimation", "onFinish", "addEventListener", "removeEventListener", "_resetDomPlayerState", "_convertKeyframesToObject", "kfs", "frame", "fromEntries", "cancel", "methods", "WebAnimationsDriver", "cssProp", "fill", "playerOptions", "previousStyles", "previousWebAnimationPlayers", "specialStyles", "createEngine", "Animation", "_animationAst", "buildTimelines", "destinationStyles", "dest", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "BaseAnimationRenderer", "delegate", "_onD<PERSON>roy", "ɵtype", "destroyNode", "queueMicrotask", "createElement", "namespace", "createComment", "createText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "setStyle", "flags", "removeStyle", "setProperty", "setValue", "<PERSON><PERSON><PERSON><PERSON>", "resolveElementFromTarget", "parseTriggerCallbackName", "event", "countId", "scheduleListenerCallback", "document", "window", "dotIndex", "substring", "AnimationRendererFactory", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "_cdRecurDepth", "<PERSON><PERSON><PERSON><PERSON>", "EMPTY_NAMESPACE_ID", "cache", "renderer", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animationTriggers", "begin", "_scheduleCountTask", "count", "run", "animationCallbacksBuffer", "end", "runOutsideAngular", "componentReplaced", "ɵAnimation", "ɵAnimationEngine", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵAnimationStyleNormalizer", "ɵBaseAnimationRenderer", "ɵENTER_CLASSNAME", "ɵLEAVE_CLASSNAME", "ɵNoopAnimationStyleNormalizer", "ɵTransitionAnimationPlayer", "ɵWebAnimationsDriver", "ɵWebAnimationsPlayer", "ɵWebAnimationsStyleNormalizer", "ɵallowPreviousPlayerStylesMerge", "ɵcamelCaseToDashCase", "ɵcontainsElement", "ɵcreateEngine", "ɵgetParentElement", "ɵinvokeQuery", "ɵnormalizeKeyframes", "ɵvalidateStyleProperty", "ɵvalidateWebAnimatableStyleProperty"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/animations/fesm2022/browser.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { validateStyleProperty, containsElement, getParentElement, invokeQuery, dashCaseToCamelCase, invalidCssUnitValue, invalidExpression, invalidTransitionAlias, visitDslNode, invalidTrigger, invalidDefinition, extractStyleParams, invalidState, invalidStyleValue, SUBSTITUTION_EXPR_START, invalidParallelAnimation, validateStyleParams, invalidKeyframes, invalidOffset, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, getOrSetDefaultValue, invalidStagger, resolveTiming, NG_TRIGGER_SELECTOR, NG_ANIMATING_SELECTOR, normalizeAnimationEntry, resolveTimingValue, interpolateParams, invalidQuery, registerFailed, normalizeKeyframes, LEAVE_CLASSNAME, ENTER_CLASSNAME, missingOrDestroyedAnimation, createAnimationFailed, optimizeGroupPlayer, missingPlayer, listenOnPlayer, makeAnimationEvent, triggerTransitionsFailed, eraseStyles, setStyles, transitionFailed, missingTrigger, missingEvent, unsupportedTriggerEvent, NG_TRIGGER_CLASSNAME, unregisteredTrigger, NG_ANIMATING_CLASSNAME, triggerBuildFailed, parseTimelineCommand, computeStyle, camelCaseToDashCase, validateWebAnimatableStyleProperty, allowPreviousPlayerStylesMerge, normalizeKeyframes$1, balancePreviousStylesIntoKeyframes, validationFailed, normalizeStyles, buildingFailed } from './util-CPU6TNml.mjs';\nimport { NoopAnimationPlayer, AnimationMetadataType, style, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationGroupPlayer } from './private_export-B_vy_9K7.mjs';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nclass NoopAnimationDriver {\n    /**\n     * @returns Whether `prop` is a valid CSS property\n     */\n    validateStyleProperty(prop) {\n        return validateStyleProperty(prop);\n    }\n    /**\n     *\n     * @returns Whether elm1 contains elm2.\n     */\n    containsElement(elm1, elm2) {\n        return containsElement(elm1, elm2);\n    }\n    /**\n     * @returns Rhe parent of the given element or `null` if the element is the `document`\n     */\n    getParentElement(element) {\n        return getParentElement(element);\n    }\n    /**\n     * @returns The result of the query selector on the element. The array will contain up to 1 item\n     *     if `multi` is  `false`.\n     */\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @returns The `defaultValue` or empty string\n     */\n    computeStyle(element, prop, defaultValue) {\n        return defaultValue || '';\n    }\n    /**\n     * @returns An `NoopAnimationPlayer`\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n        return new NoopAnimationPlayer(duration, delay);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: NoopAnimationDriver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: NoopAnimationDriver });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: NoopAnimationDriver, decorators: [{\n            type: Injectable\n        }] });\n/**\n * @publicApi\n */\nclass AnimationDriver {\n    /**\n     * @deprecated Use the NoopAnimationDriver class.\n     */\n    static NOOP = new NoopAnimationDriver();\n}\n\nclass AnimationStyleNormalizer {\n}\nclass NoopAnimationStyleNormalizer {\n    normalizePropertyName(propertyName, errors) {\n        return propertyName;\n    }\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        return value;\n    }\n}\n\nconst DIMENSIONAL_PROP_SET = new Set([\n    'width',\n    'height',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'left',\n    'top',\n    'bottom',\n    'right',\n    'fontSize',\n    'outlineWidth',\n    'outlineOffset',\n    'paddingTop',\n    'paddingLeft',\n    'paddingBottom',\n    'paddingRight',\n    'marginTop',\n    'marginLeft',\n    'marginBottom',\n    'marginRight',\n    'borderRadius',\n    'borderWidth',\n    'borderTopWidth',\n    'borderLeftWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'textIndent',\n    'perspective',\n]);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n    normalizePropertyName(propertyName, errors) {\n        return dashCaseToCamelCase(propertyName);\n    }\n    normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n        let unit = '';\n        const strVal = value.toString().trim();\n        if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n            if (typeof value === 'number') {\n                unit = 'px';\n            }\n            else {\n                const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n                if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n                    errors.push(invalidCssUnitValue(userProvidedProperty, value));\n                }\n            }\n        }\n        return strVal + unit;\n    }\n}\n\nfunction createListOfWarnings(warnings) {\n    const LINE_START = '\\n - ';\n    return `${LINE_START}${warnings\n        .filter(Boolean)\n        .map((warning) => warning)\n        .join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n    console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n    console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n    console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n    if (props.length) {\n        warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n    }\n}\n\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n    const expressions = [];\n    if (typeof transitionValue == 'string') {\n        transitionValue\n            .split(/\\s*,\\s*/)\n            .forEach((str) => parseInnerTransitionStr(str, expressions, errors));\n    }\n    else {\n        expressions.push(transitionValue);\n    }\n    return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n    if (eventStr[0] == ':') {\n        const result = parseAnimationAlias(eventStr, errors);\n        if (typeof result == 'function') {\n            expressions.push(result);\n            return;\n        }\n        eventStr = result;\n    }\n    const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n    if (match == null || match.length < 4) {\n        errors.push(invalidExpression(eventStr));\n        return expressions;\n    }\n    const fromState = match[1];\n    const separator = match[2];\n    const toState = match[3];\n    expressions.push(makeLambdaFromStates(fromState, toState));\n    const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n    if (separator[0] == '<' && !isFullAnyStateExpr) {\n        expressions.push(makeLambdaFromStates(toState, fromState));\n    }\n    return;\n}\nfunction parseAnimationAlias(alias, errors) {\n    switch (alias) {\n        case ':enter':\n            return 'void => *';\n        case ':leave':\n            return '* => void';\n        case ':increment':\n            return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n        case ':decrement':\n            return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n        default:\n            errors.push(invalidTransitionAlias(alias));\n            return '* => *';\n    }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n    const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n    const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n    return (fromState, toState) => {\n        let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n        let rhsMatch = rhs == ANY_STATE || rhs == toState;\n        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n        }\n        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n        }\n        return lhsMatch && rhsMatch;\n    };\n}\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */ new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n    return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n    _driver;\n    constructor(_driver) {\n        this._driver = _driver;\n    }\n    build(metadata, errors, warnings) {\n        const context = new AnimationAstBuilderContext(errors);\n        this._resetContextStyleTimingState(context);\n        const ast = (visitDslNode(this, normalizeAnimationEntry(metadata), context));\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (context.unsupportedCSSPropertiesFound.size) {\n                pushUnrecognizedPropertiesWarning(warnings, [\n                    ...context.unsupportedCSSPropertiesFound.keys(),\n                ]);\n            }\n        }\n        return ast;\n    }\n    _resetContextStyleTimingState(context) {\n        context.currentQuerySelector = ROOT_SELECTOR;\n        context.collectedStyles = new Map();\n        context.collectedStyles.set(ROOT_SELECTOR, new Map());\n        context.currentTime = 0;\n    }\n    visitTrigger(metadata, context) {\n        let queryCount = (context.queryCount = 0);\n        let depCount = (context.depCount = 0);\n        const states = [];\n        const transitions = [];\n        if (metadata.name.charAt(0) == '@') {\n            context.errors.push(invalidTrigger());\n        }\n        metadata.definitions.forEach((def) => {\n            this._resetContextStyleTimingState(context);\n            if (def.type == AnimationMetadataType.State) {\n                const stateDef = def;\n                const name = stateDef.name;\n                name\n                    .toString()\n                    .split(/\\s*,\\s*/)\n                    .forEach((n) => {\n                    stateDef.name = n;\n                    states.push(this.visitState(stateDef, context));\n                });\n                stateDef.name = name;\n            }\n            else if (def.type == AnimationMetadataType.Transition) {\n                const transition = this.visitTransition(def, context);\n                queryCount += transition.queryCount;\n                depCount += transition.depCount;\n                transitions.push(transition);\n            }\n            else {\n                context.errors.push(invalidDefinition());\n            }\n        });\n        return {\n            type: AnimationMetadataType.Trigger,\n            name: metadata.name,\n            states,\n            transitions,\n            queryCount,\n            depCount,\n            options: null,\n        };\n    }\n    visitState(metadata, context) {\n        const styleAst = this.visitStyle(metadata.styles, context);\n        const astParams = (metadata.options && metadata.options.params) || null;\n        if (styleAst.containsDynamicStyles) {\n            const missingSubs = new Set();\n            const params = astParams || {};\n            styleAst.styles.forEach((style) => {\n                if (style instanceof Map) {\n                    style.forEach((value) => {\n                        extractStyleParams(value).forEach((sub) => {\n                            if (!params.hasOwnProperty(sub)) {\n                                missingSubs.add(sub);\n                            }\n                        });\n                    });\n                }\n            });\n            if (missingSubs.size) {\n                context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n            }\n        }\n        return {\n            type: AnimationMetadataType.State,\n            name: metadata.name,\n            style: styleAst,\n            options: astParams ? { params: astParams } : null,\n        };\n    }\n    visitTransition(metadata, context) {\n        context.queryCount = 0;\n        context.depCount = 0;\n        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        const matchers = parseTransitionExpr(metadata.expr, context.errors);\n        return {\n            type: AnimationMetadataType.Transition,\n            matchers,\n            animation,\n            queryCount: context.queryCount,\n            depCount: context.depCount,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitSequence(metadata, context) {\n        return {\n            type: AnimationMetadataType.Sequence,\n            steps: metadata.steps.map((s) => visitDslNode(this, s, context)),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitGroup(metadata, context) {\n        const currentTime = context.currentTime;\n        let furthestTime = 0;\n        const steps = metadata.steps.map((step) => {\n            context.currentTime = currentTime;\n            const innerAst = visitDslNode(this, step, context);\n            furthestTime = Math.max(furthestTime, context.currentTime);\n            return innerAst;\n        });\n        context.currentTime = furthestTime;\n        return {\n            type: AnimationMetadataType.Group,\n            steps,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimate(metadata, context) {\n        const timingAst = constructTimingAst(metadata.timings, context.errors);\n        context.currentAnimateTimings = timingAst;\n        let styleAst;\n        let styleMetadata = metadata.styles\n            ? metadata.styles\n            : style({});\n        if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n            styleAst = this.visitKeyframes(styleMetadata, context);\n        }\n        else {\n            let styleMetadata = metadata.styles;\n            let isEmpty = false;\n            if (!styleMetadata) {\n                isEmpty = true;\n                const newStyleData = {};\n                if (timingAst.easing) {\n                    newStyleData['easing'] = timingAst.easing;\n                }\n                styleMetadata = style(newStyleData);\n            }\n            context.currentTime += timingAst.duration + timingAst.delay;\n            const _styleAst = this.visitStyle(styleMetadata, context);\n            _styleAst.isEmptyStep = isEmpty;\n            styleAst = _styleAst;\n        }\n        context.currentAnimateTimings = null;\n        return {\n            type: AnimationMetadataType.Animate,\n            timings: timingAst,\n            style: styleAst,\n            options: null,\n        };\n    }\n    visitStyle(metadata, context) {\n        const ast = this._makeStyleAst(metadata, context);\n        this._validateStyleAst(ast, context);\n        return ast;\n    }\n    _makeStyleAst(metadata, context) {\n        const styles = [];\n        const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n        for (let styleTuple of metadataStyles) {\n            if (typeof styleTuple === 'string') {\n                if (styleTuple === AUTO_STYLE) {\n                    styles.push(styleTuple);\n                }\n                else {\n                    context.errors.push(invalidStyleValue(styleTuple));\n                }\n            }\n            else {\n                styles.push(new Map(Object.entries(styleTuple)));\n            }\n        }\n        let containsDynamicStyles = false;\n        let collectedEasing = null;\n        styles.forEach((styleData) => {\n            if (styleData instanceof Map) {\n                if (styleData.has('easing')) {\n                    collectedEasing = styleData.get('easing');\n                    styleData.delete('easing');\n                }\n                if (!containsDynamicStyles) {\n                    for (let value of styleData.values()) {\n                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n                            containsDynamicStyles = true;\n                            break;\n                        }\n                    }\n                }\n            }\n        });\n        return {\n            type: AnimationMetadataType.Style,\n            styles,\n            easing: collectedEasing,\n            offset: metadata.offset,\n            containsDynamicStyles,\n            options: null,\n        };\n    }\n    _validateStyleAst(ast, context) {\n        const timings = context.currentAnimateTimings;\n        let endTime = context.currentTime;\n        let startTime = context.currentTime;\n        if (timings && startTime > 0) {\n            startTime -= timings.duration + timings.delay;\n        }\n        ast.styles.forEach((tuple) => {\n            if (typeof tuple === 'string')\n                return;\n            tuple.forEach((value, prop) => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    if (!this._driver.validateStyleProperty(prop)) {\n                        tuple.delete(prop);\n                        context.unsupportedCSSPropertiesFound.add(prop);\n                        return;\n                    }\n                }\n                // This is guaranteed to have a defined Map at this querySelector location making it\n                // safe to add the assertion here. It is set as a default empty map in prior methods.\n                const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n                const collectedEntry = collectedStyles.get(prop);\n                let updateCollectedStyle = true;\n                if (collectedEntry) {\n                    if (startTime != endTime &&\n                        startTime >= collectedEntry.startTime &&\n                        endTime <= collectedEntry.endTime) {\n                        context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n                        updateCollectedStyle = false;\n                    }\n                    // we always choose the smaller start time value since we\n                    // want to have a record of the entire animation window where\n                    // the style property is being animated in between\n                    startTime = collectedEntry.startTime;\n                }\n                if (updateCollectedStyle) {\n                    collectedStyles.set(prop, { startTime, endTime });\n                }\n                if (context.options) {\n                    validateStyleParams(value, context.options, context.errors);\n                }\n            });\n        });\n    }\n    visitKeyframes(metadata, context) {\n        const ast = { type: AnimationMetadataType.Keyframes, styles: [], options: null };\n        if (!context.currentAnimateTimings) {\n            context.errors.push(invalidKeyframes());\n            return ast;\n        }\n        const MAX_KEYFRAME_OFFSET = 1;\n        let totalKeyframesWithOffsets = 0;\n        const offsets = [];\n        let offsetsOutOfOrder = false;\n        let keyframesOutOfRange = false;\n        let previousOffset = 0;\n        const keyframes = metadata.steps.map((styles) => {\n            const style = this._makeStyleAst(styles, context);\n            let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n            let offset = 0;\n            if (offsetVal != null) {\n                totalKeyframesWithOffsets++;\n                offset = style.offset = offsetVal;\n            }\n            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n            previousOffset = offset;\n            offsets.push(offset);\n            return style;\n        });\n        if (keyframesOutOfRange) {\n            context.errors.push(invalidOffset());\n        }\n        if (offsetsOutOfOrder) {\n            context.errors.push(keyframeOffsetsOutOfOrder());\n        }\n        const length = metadata.steps.length;\n        let generatedOffset = 0;\n        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n            context.errors.push(keyframesMissingOffsets());\n        }\n        else if (totalKeyframesWithOffsets == 0) {\n            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n        }\n        const limit = length - 1;\n        const currentTime = context.currentTime;\n        const currentAnimateTimings = context.currentAnimateTimings;\n        const animateDuration = currentAnimateTimings.duration;\n        keyframes.forEach((kf, i) => {\n            const offset = generatedOffset > 0 ? (i == limit ? 1 : generatedOffset * i) : offsets[i];\n            const durationUpToThisFrame = offset * animateDuration;\n            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n            currentAnimateTimings.duration = durationUpToThisFrame;\n            this._validateStyleAst(kf, context);\n            kf.offset = offset;\n            ast.styles.push(kf);\n        });\n        return ast;\n    }\n    visitReference(metadata, context) {\n        return {\n            type: AnimationMetadataType.Reference,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimateChild(metadata, context) {\n        context.depCount++;\n        return {\n            type: AnimationMetadataType.AnimateChild,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitAnimateRef(metadata, context) {\n        return {\n            type: AnimationMetadataType.AnimateRef,\n            animation: this.visitReference(metadata.animation, context),\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitQuery(metadata, context) {\n        const parentSelector = context.currentQuerySelector;\n        const options = (metadata.options || {});\n        context.queryCount++;\n        context.currentQuery = metadata;\n        const [selector, includeSelf] = normalizeSelector(metadata.selector);\n        context.currentQuerySelector = parentSelector.length\n            ? parentSelector + ' ' + selector\n            : selector;\n        getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n        context.currentQuery = null;\n        context.currentQuerySelector = parentSelector;\n        return {\n            type: AnimationMetadataType.Query,\n            selector,\n            limit: options.limit || 0,\n            optional: !!options.optional,\n            includeSelf,\n            animation,\n            originalSelector: metadata.selector,\n            options: normalizeAnimationOptions(metadata.options),\n        };\n    }\n    visitStagger(metadata, context) {\n        if (!context.currentQuery) {\n            context.errors.push(invalidStagger());\n        }\n        const timings = metadata.timings === 'full'\n            ? { duration: 0, delay: 0, easing: 'full' }\n            : resolveTiming(metadata.timings, context.errors, true);\n        return {\n            type: AnimationMetadataType.Stagger,\n            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n            timings,\n            options: null,\n        };\n    }\n}\nfunction normalizeSelector(selector) {\n    const hasAmpersand = selector.split(/\\s*,\\s*/).find((token) => token == SELF_TOKEN)\n        ? true\n        : false;\n    if (hasAmpersand) {\n        selector = selector.replace(SELF_TOKEN_REGEX, '');\n    }\n    // Note: the :enter and :leave aren't normalized here since those\n    // selectors are filled in at runtime during timeline building\n    selector = selector\n        .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n        .replace(/@\\w+/g, (match) => NG_TRIGGER_SELECTOR + '-' + match.slice(1))\n        .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n    return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n    return obj ? { ...obj } : null;\n}\nclass AnimationAstBuilderContext {\n    errors;\n    queryCount = 0;\n    depCount = 0;\n    currentTransition = null;\n    currentQuery = null;\n    currentQuerySelector = null;\n    currentAnimateTimings = null;\n    currentTime = 0;\n    collectedStyles = new Map();\n    options = null;\n    unsupportedCSSPropertiesFound = new Set();\n    constructor(errors) {\n        this.errors = errors;\n    }\n}\nfunction consumeOffset(styles) {\n    if (typeof styles == 'string')\n        return null;\n    let offset = null;\n    if (Array.isArray(styles)) {\n        styles.forEach((styleTuple) => {\n            if (styleTuple instanceof Map && styleTuple.has('offset')) {\n                const obj = styleTuple;\n                offset = parseFloat(obj.get('offset'));\n                obj.delete('offset');\n            }\n        });\n    }\n    else if (styles instanceof Map && styles.has('offset')) {\n        const obj = styles;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n    }\n    return offset;\n}\nfunction constructTimingAst(value, errors) {\n    if (value.hasOwnProperty('duration')) {\n        return value;\n    }\n    if (typeof value == 'number') {\n        const duration = resolveTiming(value, errors).duration;\n        return makeTimingAst(duration, 0, '');\n    }\n    const strValue = value;\n    const isDynamic = strValue.split(/\\s+/).some((v) => v.charAt(0) == '{' && v.charAt(1) == '{');\n    if (isDynamic) {\n        const ast = makeTimingAst(0, 0, '');\n        ast.dynamic = true;\n        ast.strValue = strValue;\n        return ast;\n    }\n    const timings = resolveTiming(strValue, errors);\n    return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n    if (options) {\n        options = { ...options };\n        if (options['params']) {\n            options['params'] = normalizeParams(options['params']);\n        }\n    }\n    else {\n        options = {};\n    }\n    return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n    return { duration, delay, easing };\n}\n\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n    return {\n        type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n        element,\n        keyframes,\n        preStyleProps,\n        postStyleProps,\n        duration,\n        delay,\n        totalTime: duration + delay,\n        easing,\n        subTimeline,\n    };\n}\n\nclass ElementInstructionMap {\n    _map = new Map();\n    get(element) {\n        return this._map.get(element) || [];\n    }\n    append(element, instructions) {\n        let existingInstructions = this._map.get(element);\n        if (!existingInstructions) {\n            this._map.set(element, (existingInstructions = []));\n        }\n        existingInstructions.push(...instructions);\n    }\n    has(element) {\n        return this._map.has(element);\n    }\n    clear() {\n        this._map.clear();\n    }\n}\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */ new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */ new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n    return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n    buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n        context.options = options;\n        const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        context.currentTimeline.delayNextStep(delay);\n        context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n        visitDslNode(this, ast, context);\n        // this checks to see if an actual animation happened\n        const timelines = context.timelines.filter((timeline) => timeline.containsAnimation());\n        // note: we just want to apply the final styles for the rootElement, so we do not\n        //       just apply the styles to the last timeline but the last timeline which\n        //       element is the root one (basically `*`-styles are replaced with the actual\n        //       state style values only for the root element)\n        if (timelines.length && finalStyles.size) {\n            let lastRootTimeline;\n            for (let i = timelines.length - 1; i >= 0; i--) {\n                const timeline = timelines[i];\n                if (timeline.element === rootElement) {\n                    lastRootTimeline = timeline;\n                    break;\n                }\n            }\n            if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n                lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n            }\n        }\n        return timelines.length\n            ? timelines.map((timeline) => timeline.buildKeyframes())\n            : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n    }\n    visitTrigger(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitState(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitTransition(ast, context) {\n        // these values are not visited in this AST\n    }\n    visitAnimateChild(ast, context) {\n        const elementInstructions = context.subInstructions.get(context.element);\n        if (elementInstructions) {\n            const innerContext = context.createSubContext(ast.options);\n            const startTime = context.currentTimeline.currentTime;\n            const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n            if (startTime != endTime) {\n                // we do this on the upper context because we created a sub context for\n                // the sub child animations\n                context.transformIntoNewTimeline(endTime);\n            }\n        }\n        context.previousNode = ast;\n    }\n    visitAnimateRef(ast, context) {\n        const innerContext = context.createSubContext(ast.options);\n        innerContext.transformIntoNewTimeline();\n        this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n        this.visitReference(ast.animation, innerContext);\n        context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n        context.previousNode = ast;\n    }\n    _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n        for (const animationRefOptions of animationsRefsOptions) {\n            const animationDelay = animationRefOptions?.delay;\n            if (animationDelay) {\n                const animationDelayValue = typeof animationDelay === 'number'\n                    ? animationDelay\n                    : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n                innerContext.delayNextStep(animationDelayValue);\n            }\n        }\n    }\n    _visitSubInstructions(instructions, context, options) {\n        const startTime = context.currentTimeline.currentTime;\n        let furthestTime = startTime;\n        // this is a special-case for when a user wants to skip a sub\n        // animation from being fired entirely.\n        const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n        const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n        if (duration !== 0) {\n            instructions.forEach((instruction) => {\n                const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n                furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n            });\n        }\n        return furthestTime;\n    }\n    visitReference(ast, context) {\n        context.updateOptions(ast.options, true);\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n    }\n    visitSequence(ast, context) {\n        const subContextCount = context.subContextCount;\n        let ctx = context;\n        const options = ast.options;\n        if (options && (options.params || options.delay)) {\n            ctx = context.createSubContext(options);\n            ctx.transformIntoNewTimeline();\n            if (options.delay != null) {\n                if (ctx.previousNode.type == AnimationMetadataType.Style) {\n                    ctx.currentTimeline.snapshotCurrentStyles();\n                    ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n                }\n                const delay = resolveTimingValue(options.delay);\n                ctx.delayNextStep(delay);\n            }\n        }\n        if (ast.steps.length) {\n            ast.steps.forEach((s) => visitDslNode(this, s, ctx));\n            // this is here just in case the inner steps only contain or end with a style() call\n            ctx.currentTimeline.applyStylesToKeyframe();\n            // this means that some animation function within the sequence\n            // ended up creating a sub timeline (which means the current\n            // timeline cannot overlap with the contents of the sequence)\n            if (ctx.subContextCount > subContextCount) {\n                ctx.transformIntoNewTimeline();\n            }\n        }\n        context.previousNode = ast;\n    }\n    visitGroup(ast, context) {\n        const innerTimelines = [];\n        let furthestTime = context.currentTimeline.currentTime;\n        const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n        ast.steps.forEach((s) => {\n            const innerContext = context.createSubContext(ast.options);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            visitDslNode(this, s, innerContext);\n            furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n            innerTimelines.push(innerContext.currentTimeline);\n        });\n        // this operation is run after the AST loop because otherwise\n        // if the parent timeline's collected styles were updated then\n        // it would pass in invalid data into the new-to-be forked items\n        innerTimelines.forEach((timeline) => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n        context.transformIntoNewTimeline(furthestTime);\n        context.previousNode = ast;\n    }\n    _visitTiming(ast, context) {\n        if (ast.dynamic) {\n            const strValue = ast.strValue;\n            const timingValue = context.params\n                ? interpolateParams(strValue, context.params, context.errors)\n                : strValue;\n            return resolveTiming(timingValue, context.errors);\n        }\n        else {\n            return { duration: ast.duration, delay: ast.delay, easing: ast.easing };\n        }\n    }\n    visitAnimate(ast, context) {\n        const timings = (context.currentAnimateTimings = this._visitTiming(ast.timings, context));\n        const timeline = context.currentTimeline;\n        if (timings.delay) {\n            context.incrementTime(timings.delay);\n            timeline.snapshotCurrentStyles();\n        }\n        const style = ast.style;\n        if (style.type == AnimationMetadataType.Keyframes) {\n            this.visitKeyframes(style, context);\n        }\n        else {\n            context.incrementTime(timings.duration);\n            this.visitStyle(style, context);\n            timeline.applyStylesToKeyframe();\n        }\n        context.currentAnimateTimings = null;\n        context.previousNode = ast;\n    }\n    visitStyle(ast, context) {\n        const timeline = context.currentTimeline;\n        const timings = context.currentAnimateTimings;\n        // this is a special case for when a style() call\n        // directly follows  an animate() call (but not inside of an animate() call)\n        if (!timings && timeline.hasCurrentStyleProperties()) {\n            timeline.forwardFrame();\n        }\n        const easing = (timings && timings.easing) || ast.easing;\n        if (ast.isEmptyStep) {\n            timeline.applyEmptyStep(easing);\n        }\n        else {\n            timeline.setStyles(ast.styles, easing, context.errors, context.options);\n        }\n        context.previousNode = ast;\n    }\n    visitKeyframes(ast, context) {\n        const currentAnimateTimings = context.currentAnimateTimings;\n        const startTime = context.currentTimeline.duration;\n        const duration = currentAnimateTimings.duration;\n        const innerContext = context.createSubContext();\n        const innerTimeline = innerContext.currentTimeline;\n        innerTimeline.easing = currentAnimateTimings.easing;\n        ast.styles.forEach((step) => {\n            const offset = step.offset || 0;\n            innerTimeline.forwardTime(offset * duration);\n            innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n            innerTimeline.applyStylesToKeyframe();\n        });\n        // this will ensure that the parent timeline gets all the styles from\n        // the child even if the new timeline below is not used\n        context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n        // we do this because the window between this timeline and the sub timeline\n        // should ensure that the styles within are exactly the same as they were before\n        context.transformIntoNewTimeline(startTime + duration);\n        context.previousNode = ast;\n    }\n    visitQuery(ast, context) {\n        // in the event that the first step before this is a style step we need\n        // to ensure the styles are applied before the children are animated\n        const startTime = context.currentTimeline.currentTime;\n        const options = (ast.options || {});\n        const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n        if (delay &&\n            (context.previousNode.type === AnimationMetadataType.Style ||\n                (startTime == 0 && context.currentTimeline.hasCurrentStyleProperties()))) {\n            context.currentTimeline.snapshotCurrentStyles();\n            context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        let furthestTime = startTime;\n        const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n        context.currentQueryTotal = elms.length;\n        let sameElementTimeline = null;\n        elms.forEach((element, i) => {\n            context.currentQueryIndex = i;\n            const innerContext = context.createSubContext(ast.options, element);\n            if (delay) {\n                innerContext.delayNextStep(delay);\n            }\n            if (element === context.element) {\n                sameElementTimeline = innerContext.currentTimeline;\n            }\n            visitDslNode(this, ast.animation, innerContext);\n            // this is here just incase the inner steps only contain or end\n            // with a style() call (which is here to signal that this is a preparatory\n            // call to style an element before it is animated again)\n            innerContext.currentTimeline.applyStylesToKeyframe();\n            const endTime = innerContext.currentTimeline.currentTime;\n            furthestTime = Math.max(furthestTime, endTime);\n        });\n        context.currentQueryIndex = 0;\n        context.currentQueryTotal = 0;\n        context.transformIntoNewTimeline(furthestTime);\n        if (sameElementTimeline) {\n            context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n            context.currentTimeline.snapshotCurrentStyles();\n        }\n        context.previousNode = ast;\n    }\n    visitStagger(ast, context) {\n        const parentContext = context.parentContext;\n        const tl = context.currentTimeline;\n        const timings = ast.timings;\n        const duration = Math.abs(timings.duration);\n        const maxTime = duration * (context.currentQueryTotal - 1);\n        let delay = duration * context.currentQueryIndex;\n        let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n        switch (staggerTransformer) {\n            case 'reverse':\n                delay = maxTime - delay;\n                break;\n            case 'full':\n                delay = parentContext.currentStaggerTime;\n                break;\n        }\n        const timeline = context.currentTimeline;\n        if (delay) {\n            timeline.delayNextStep(delay);\n        }\n        const startingTime = timeline.currentTime;\n        visitDslNode(this, ast.animation, context);\n        context.previousNode = ast;\n        // time = duration + delay\n        // the reason why this computation is so complex is because\n        // the inner timeline may either have a delay value or a stretched\n        // keyframe depending on if a subtimeline is not used or is used.\n        parentContext.currentStaggerTime =\n            tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n    }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n    _driver;\n    element;\n    subInstructions;\n    _enterClassName;\n    _leaveClassName;\n    errors;\n    timelines;\n    parentContext = null;\n    currentTimeline;\n    currentAnimateTimings = null;\n    previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    subContextCount = 0;\n    options = {};\n    currentQueryIndex = 0;\n    currentQueryTotal = 0;\n    currentStaggerTime = 0;\n    constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n        this._driver = _driver;\n        this.element = element;\n        this.subInstructions = subInstructions;\n        this._enterClassName = _enterClassName;\n        this._leaveClassName = _leaveClassName;\n        this.errors = errors;\n        this.timelines = timelines;\n        this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n        timelines.push(this.currentTimeline);\n    }\n    get params() {\n        return this.options.params;\n    }\n    updateOptions(options, skipIfExists) {\n        if (!options)\n            return;\n        const newOptions = options;\n        let optionsToUpdate = this.options;\n        // NOTE: this will get patched up when other animation methods support duration overrides\n        if (newOptions.duration != null) {\n            optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n        }\n        if (newOptions.delay != null) {\n            optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n        }\n        const newParams = newOptions.params;\n        if (newParams) {\n            let paramsToUpdate = optionsToUpdate.params;\n            if (!paramsToUpdate) {\n                paramsToUpdate = this.options.params = {};\n            }\n            Object.keys(newParams).forEach((name) => {\n                if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n                    paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n                }\n            });\n        }\n    }\n    _copyOptions() {\n        const options = {};\n        if (this.options) {\n            const oldParams = this.options.params;\n            if (oldParams) {\n                const params = (options['params'] = {});\n                Object.keys(oldParams).forEach((name) => {\n                    params[name] = oldParams[name];\n                });\n            }\n        }\n        return options;\n    }\n    createSubContext(options = null, element, newTime) {\n        const target = element || this.element;\n        const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n        context.previousNode = this.previousNode;\n        context.currentAnimateTimings = this.currentAnimateTimings;\n        context.options = this._copyOptions();\n        context.updateOptions(options);\n        context.currentQueryIndex = this.currentQueryIndex;\n        context.currentQueryTotal = this.currentQueryTotal;\n        context.parentContext = this;\n        this.subContextCount++;\n        return context;\n    }\n    transformIntoNewTimeline(newTime) {\n        this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n        this.timelines.push(this.currentTimeline);\n        return this.currentTimeline;\n    }\n    appendInstructionToTimeline(instruction, duration, delay) {\n        const updatedTimings = {\n            duration: duration != null ? duration : instruction.duration,\n            delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n            easing: '',\n        };\n        const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n        this.timelines.push(builder);\n        return updatedTimings;\n    }\n    incrementTime(time) {\n        this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n    }\n    delayNextStep(delay) {\n        // negative delays are not yet supported\n        if (delay > 0) {\n            this.currentTimeline.delayNextStep(delay);\n        }\n    }\n    invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n        let results = [];\n        if (includeSelf) {\n            results.push(this.element);\n        }\n        if (selector.length > 0) {\n            // only if :self is used then the selector can be empty\n            selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n            selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n            const multi = limit != 1;\n            let elements = this._driver.query(this.element, selector, multi);\n            if (limit !== 0) {\n                elements =\n                    limit < 0\n                        ? elements.slice(elements.length + limit, elements.length)\n                        : elements.slice(0, limit);\n            }\n            results.push(...elements);\n        }\n        if (!optional && results.length == 0) {\n            errors.push(invalidQuery(originalSelector));\n        }\n        return results;\n    }\n}\nclass TimelineBuilder {\n    _driver;\n    element;\n    startTime;\n    _elementTimelineStylesLookup;\n    duration = 0;\n    easing = null;\n    _previousKeyframe = new Map();\n    _currentKeyframe = new Map();\n    _keyframes = new Map();\n    _styleSummary = new Map();\n    _localTimelineStyles = new Map();\n    _globalTimelineStyles;\n    _pendingStyles = new Map();\n    _backFill = new Map();\n    _currentEmptyStepKeyframe = null;\n    constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n        this._driver = _driver;\n        this.element = element;\n        this.startTime = startTime;\n        this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n        if (!this._elementTimelineStylesLookup) {\n            this._elementTimelineStylesLookup = new Map();\n        }\n        this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n        if (!this._globalTimelineStyles) {\n            this._globalTimelineStyles = this._localTimelineStyles;\n            this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n        }\n        this._loadKeyframe();\n    }\n    containsAnimation() {\n        switch (this._keyframes.size) {\n            case 0:\n                return false;\n            case 1:\n                return this.hasCurrentStyleProperties();\n            default:\n                return true;\n        }\n    }\n    hasCurrentStyleProperties() {\n        return this._currentKeyframe.size > 0;\n    }\n    get currentTime() {\n        return this.startTime + this.duration;\n    }\n    delayNextStep(delay) {\n        // in the event that a style() step is placed right before a stagger()\n        // and that style() step is the very first style() value in the animation\n        // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n        // properly applies the style() values to work with the stagger...\n        const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n        if (this.duration || hasPreStyleStep) {\n            this.forwardTime(this.currentTime + delay);\n            if (hasPreStyleStep) {\n                this.snapshotCurrentStyles();\n            }\n        }\n        else {\n            this.startTime += delay;\n        }\n    }\n    fork(element, currentTime) {\n        this.applyStylesToKeyframe();\n        return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n    }\n    _loadKeyframe() {\n        if (this._currentKeyframe) {\n            this._previousKeyframe = this._currentKeyframe;\n        }\n        this._currentKeyframe = this._keyframes.get(this.duration);\n        if (!this._currentKeyframe) {\n            this._currentKeyframe = new Map();\n            this._keyframes.set(this.duration, this._currentKeyframe);\n        }\n    }\n    forwardFrame() {\n        this.duration += ONE_FRAME_IN_MILLISECONDS;\n        this._loadKeyframe();\n    }\n    forwardTime(time) {\n        this.applyStylesToKeyframe();\n        this.duration = time;\n        this._loadKeyframe();\n    }\n    _updateStyle(prop, value) {\n        this._localTimelineStyles.set(prop, value);\n        this._globalTimelineStyles.set(prop, value);\n        this._styleSummary.set(prop, { time: this.currentTime, value });\n    }\n    allowOnlyTimelineStyles() {\n        return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n    }\n    applyEmptyStep(easing) {\n        if (easing) {\n            this._previousKeyframe.set('easing', easing);\n        }\n        // special case for animate(duration):\n        // all missing styles are filled with a `*` value then\n        // if any destination styles are filled in later on the same\n        // keyframe then they will override the overridden styles\n        // We use `_globalTimelineStyles` here because there may be\n        // styles in previous keyframes that are not present in this timeline\n        for (let [prop, value] of this._globalTimelineStyles) {\n            this._backFill.set(prop, value || AUTO_STYLE);\n            this._currentKeyframe.set(prop, AUTO_STYLE);\n        }\n        this._currentEmptyStepKeyframe = this._currentKeyframe;\n    }\n    setStyles(input, easing, errors, options) {\n        if (easing) {\n            this._previousKeyframe.set('easing', easing);\n        }\n        const params = (options && options.params) || {};\n        const styles = flattenStyles(input, this._globalTimelineStyles);\n        for (let [prop, value] of styles) {\n            const val = interpolateParams(value, params, errors);\n            this._pendingStyles.set(prop, val);\n            if (!this._localTimelineStyles.has(prop)) {\n                this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n            }\n            this._updateStyle(prop, val);\n        }\n    }\n    applyStylesToKeyframe() {\n        if (this._pendingStyles.size == 0)\n            return;\n        this._pendingStyles.forEach((val, prop) => {\n            this._currentKeyframe.set(prop, val);\n        });\n        this._pendingStyles.clear();\n        this._localTimelineStyles.forEach((val, prop) => {\n            if (!this._currentKeyframe.has(prop)) {\n                this._currentKeyframe.set(prop, val);\n            }\n        });\n    }\n    snapshotCurrentStyles() {\n        for (let [prop, val] of this._localTimelineStyles) {\n            this._pendingStyles.set(prop, val);\n            this._updateStyle(prop, val);\n        }\n    }\n    getFinalKeyframe() {\n        return this._keyframes.get(this.duration);\n    }\n    get properties() {\n        const properties = [];\n        for (let prop in this._currentKeyframe) {\n            properties.push(prop);\n        }\n        return properties;\n    }\n    mergeTimelineCollectedStyles(timeline) {\n        timeline._styleSummary.forEach((details1, prop) => {\n            const details0 = this._styleSummary.get(prop);\n            if (!details0 || details1.time > details0.time) {\n                this._updateStyle(prop, details1.value);\n            }\n        });\n    }\n    buildKeyframes() {\n        this.applyStylesToKeyframe();\n        const preStyleProps = new Set();\n        const postStyleProps = new Set();\n        const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n        let finalKeyframes = [];\n        this._keyframes.forEach((keyframe, time) => {\n            const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n            finalKeyframe.forEach((value, prop) => {\n                if (value === _PRE_STYLE) {\n                    preStyleProps.add(prop);\n                }\n                else if (value === AUTO_STYLE) {\n                    postStyleProps.add(prop);\n                }\n            });\n            if (!isEmpty) {\n                finalKeyframe.set('offset', time / this.duration);\n            }\n            finalKeyframes.push(finalKeyframe);\n        });\n        const preProps = [...preStyleProps.values()];\n        const postProps = [...postStyleProps.values()];\n        // special case for a 0-second animation (which is designed just to place styles onscreen)\n        if (isEmpty) {\n            const kf0 = finalKeyframes[0];\n            const kf1 = new Map(kf0);\n            kf0.set('offset', 0);\n            kf1.set('offset', 1);\n            finalKeyframes = [kf0, kf1];\n        }\n        return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n    }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n    keyframes;\n    preStyleProps;\n    postStyleProps;\n    _stretchStartingKeyframe;\n    timings;\n    constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n        super(driver, element, timings.delay);\n        this.keyframes = keyframes;\n        this.preStyleProps = preStyleProps;\n        this.postStyleProps = postStyleProps;\n        this._stretchStartingKeyframe = _stretchStartingKeyframe;\n        this.timings = { duration: timings.duration, delay: timings.delay, easing: timings.easing };\n    }\n    containsAnimation() {\n        return this.keyframes.length > 1;\n    }\n    buildKeyframes() {\n        let keyframes = this.keyframes;\n        let { delay, duration, easing } = this.timings;\n        if (this._stretchStartingKeyframe && delay) {\n            const newKeyframes = [];\n            const totalTime = duration + delay;\n            const startingGap = delay / totalTime;\n            // the original starting keyframe now starts once the delay is done\n            const newFirstKeyframe = new Map(keyframes[0]);\n            newFirstKeyframe.set('offset', 0);\n            newKeyframes.push(newFirstKeyframe);\n            const oldFirstKeyframe = new Map(keyframes[0]);\n            oldFirstKeyframe.set('offset', roundOffset(startingGap));\n            newKeyframes.push(oldFirstKeyframe);\n            /*\n              When the keyframe is stretched then it means that the delay before the animation\n              starts is gone. Instead the first keyframe is placed at the start of the animation\n              and it is then copied to where it starts when the original delay is over. This basically\n              means nothing animates during that delay, but the styles are still rendered. For this\n              to work the original offset values that exist in the original keyframes must be \"warped\"\n              so that they can take the new keyframe + delay into account.\n      \n              delay=1000, duration=1000, keyframes = 0 .5 1\n      \n              turns into\n      \n              delay=0, duration=2000, keyframes = 0 .33 .66 1\n             */\n            // offsets between 1 ... n -1 are all warped by the keyframe stretch\n            const limit = keyframes.length - 1;\n            for (let i = 1; i <= limit; i++) {\n                let kf = new Map(keyframes[i]);\n                const oldOffset = kf.get('offset');\n                const timeAtKeyframe = delay + oldOffset * duration;\n                kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n                newKeyframes.push(kf);\n            }\n            // the new starting keyframe should be added at the start\n            duration = totalTime;\n            delay = 0;\n            easing = '';\n            keyframes = newKeyframes;\n        }\n        return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n    }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n    const mult = Math.pow(10, decimalPoints - 1);\n    return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n    const styles = new Map();\n    let allProperties;\n    input.forEach((token) => {\n        if (token === '*') {\n            allProperties ??= allStyles.keys();\n            for (let prop of allProperties) {\n                styles.set(prop, AUTO_STYLE);\n            }\n        }\n        else {\n            for (let [prop, val] of token) {\n                styles.set(prop, val);\n            }\n        }\n    });\n    return styles;\n}\n\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n    return {\n        type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n        element,\n        triggerName,\n        isRemovalTransition,\n        fromState,\n        fromStyles,\n        toState,\n        toStyles,\n        timelines,\n        queriedElements,\n        preStyleProps,\n        postStyleProps,\n        totalTime,\n        errors,\n    };\n}\n\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n    _triggerName;\n    ast;\n    _stateStyles;\n    constructor(_triggerName, ast, _stateStyles) {\n        this._triggerName = _triggerName;\n        this.ast = ast;\n        this._stateStyles = _stateStyles;\n    }\n    match(currentState, nextState, element, params) {\n        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n    }\n    buildStyles(stateName, params, errors) {\n        let styler = this._stateStyles.get('*');\n        if (stateName !== undefined) {\n            styler = this._stateStyles.get(stateName?.toString()) || styler;\n        }\n        return styler ? styler.buildStyles(params, errors) : new Map();\n    }\n    build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n        const errors = [];\n        const transitionAnimationParams = (this.ast.options && this.ast.options.params) || EMPTY_OBJECT;\n        const currentAnimationParams = (currentOptions && currentOptions.params) || EMPTY_OBJECT;\n        const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n        const nextAnimationParams = (nextOptions && nextOptions.params) || EMPTY_OBJECT;\n        const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n        const queriedElements = new Set();\n        const preStyleMap = new Map();\n        const postStyleMap = new Map();\n        const isRemoval = nextState === 'void';\n        const animationOptions = {\n            params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n            delay: this.ast.options?.delay,\n        };\n        const timelines = skipAstBuild\n            ? []\n            : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n        let totalTime = 0;\n        timelines.forEach((tl) => {\n            totalTime = Math.max(tl.duration + tl.delay, totalTime);\n        });\n        if (errors.length) {\n            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n        }\n        timelines.forEach((tl) => {\n            const elm = tl.element;\n            const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n            tl.preStyleProps.forEach((prop) => preProps.add(prop));\n            const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n            tl.postStyleProps.forEach((prop) => postProps.add(prop));\n            if (elm !== element) {\n                queriedElements.add(elm);\n            }\n        });\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n        }\n        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n    }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n    if (!driver.validateAnimatableStyleProperty) {\n        return;\n    }\n    const allowedNonAnimatableProps = new Set([\n        // 'easing' is a utility/synthetic prop we use to represent\n        // easing functions, it represents a property of the animation\n        // which is not animatable but different values can be used\n        // in different steps\n        'easing',\n    ]);\n    const invalidNonAnimatableProps = new Set();\n    timelines.forEach(({ keyframes }) => {\n        const nonAnimatablePropsInitialValues = new Map();\n        keyframes.forEach((keyframe) => {\n            const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n            for (const [prop, value] of entriesToCheck) {\n                if (!driver.validateAnimatableStyleProperty(prop)) {\n                    if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n                        const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n                        if (propInitialValue !== value) {\n                            invalidNonAnimatableProps.add(prop);\n                        }\n                    }\n                    else {\n                        nonAnimatablePropsInitialValues.set(prop, value);\n                    }\n                }\n            }\n        });\n    });\n    if (invalidNonAnimatableProps.size > 0) {\n        console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` +\n            ' not animatable properties: ' +\n            Array.from(invalidNonAnimatableProps).join(', ') +\n            '\\n' +\n            '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n    }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n    return matchFns.some((fn) => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n    const result = { ...defaults };\n    Object.entries(userParams).forEach(([key, value]) => {\n        if (value != null) {\n            result[key] = value;\n        }\n    });\n    return result;\n}\nclass AnimationStateStyles {\n    styles;\n    defaultParams;\n    normalizer;\n    constructor(styles, defaultParams, normalizer) {\n        this.styles = styles;\n        this.defaultParams = defaultParams;\n        this.normalizer = normalizer;\n    }\n    buildStyles(params, errors) {\n        const finalStyles = new Map();\n        const combinedParams = applyParamDefaults(params, this.defaultParams);\n        this.styles.styles.forEach((value) => {\n            if (typeof value !== 'string') {\n                value.forEach((val, prop) => {\n                    if (val) {\n                        val = interpolateParams(val, combinedParams, errors);\n                    }\n                    const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n                    val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n                    finalStyles.set(prop, val);\n                });\n            }\n        });\n        return finalStyles;\n    }\n}\n\nfunction buildTrigger(name, ast, normalizer) {\n    return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n    name;\n    ast;\n    _normalizer;\n    transitionFactories = [];\n    fallbackTransition;\n    states = new Map();\n    constructor(name, ast, _normalizer) {\n        this.name = name;\n        this.ast = ast;\n        this._normalizer = _normalizer;\n        ast.states.forEach((ast) => {\n            const defaultParams = (ast.options && ast.options.params) || {};\n            this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n        });\n        balanceProperties(this.states, 'true', '1');\n        balanceProperties(this.states, 'false', '0');\n        ast.transitions.forEach((ast) => {\n            this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n        });\n        this.fallbackTransition = createFallbackTransition(name, this.states);\n    }\n    get containsQueries() {\n        return this.ast.queryCount > 0;\n    }\n    matchTransition(currentState, nextState, element, params) {\n        const entry = this.transitionFactories.find((f) => f.match(currentState, nextState, element, params));\n        return entry || null;\n    }\n    matchStyles(currentState, params, errors) {\n        return this.fallbackTransition.buildStyles(currentState, params, errors);\n    }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n    const matchers = [(fromState, toState) => true];\n    const animation = { type: AnimationMetadataType.Sequence, steps: [], options: null };\n    const transition = {\n        type: AnimationMetadataType.Transition,\n        animation,\n        matchers,\n        options: null,\n        queryCount: 0,\n        depCount: 0,\n    };\n    return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n    if (stateMap.has(key1)) {\n        if (!stateMap.has(key2)) {\n            stateMap.set(key2, stateMap.get(key1));\n        }\n    }\n    else if (stateMap.has(key2)) {\n        stateMap.set(key1, stateMap.get(key2));\n    }\n}\n\nconst EMPTY_INSTRUCTION_MAP = /* @__PURE__ */ new ElementInstructionMap();\nclass TimelineAnimationEngine {\n    bodyNode;\n    _driver;\n    _normalizer;\n    _animations = new Map();\n    _playersById = new Map();\n    players = [];\n    constructor(bodyNode, _driver, _normalizer) {\n        this.bodyNode = bodyNode;\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n    }\n    register(id, metadata) {\n        const errors = [];\n        const warnings = [];\n        const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n        if (errors.length) {\n            throw registerFailed(errors);\n        }\n        else {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                if (warnings.length) {\n                    warnRegister(warnings);\n                }\n            }\n            this._animations.set(id, ast);\n        }\n    }\n    _buildPlayer(i, preStyles, postStyles) {\n        const element = i.element;\n        const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n    }\n    create(id, element, options = {}) {\n        const errors = [];\n        const ast = this._animations.get(id);\n        let instructions;\n        const autoStylesMap = new Map();\n        if (ast) {\n            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n            instructions.forEach((inst) => {\n                const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n                inst.postStyleProps.forEach((prop) => styles.set(prop, null));\n            });\n        }\n        else {\n            errors.push(missingOrDestroyedAnimation());\n            instructions = [];\n        }\n        if (errors.length) {\n            throw createAnimationFailed(errors);\n        }\n        autoStylesMap.forEach((styles, element) => {\n            styles.forEach((_, prop) => {\n                styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n            });\n        });\n        const players = instructions.map((i) => {\n            const styles = autoStylesMap.get(i.element);\n            return this._buildPlayer(i, new Map(), styles);\n        });\n        const player = optimizeGroupPlayer(players);\n        this._playersById.set(id, player);\n        player.onDestroy(() => this.destroy(id));\n        this.players.push(player);\n        return player;\n    }\n    destroy(id) {\n        const player = this._getPlayer(id);\n        player.destroy();\n        this._playersById.delete(id);\n        const index = this.players.indexOf(player);\n        if (index >= 0) {\n            this.players.splice(index, 1);\n        }\n    }\n    _getPlayer(id) {\n        const player = this._playersById.get(id);\n        if (!player) {\n            throw missingPlayer(id);\n        }\n        return player;\n    }\n    listen(id, element, eventName, callback) {\n        // triggerName, fromState, toState are all ignored for timeline animations\n        const baseEvent = makeAnimationEvent(element, '', '', '');\n        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n        return () => { };\n    }\n    command(id, element, command, args) {\n        if (command == 'register') {\n            this.register(id, args[0]);\n            return;\n        }\n        if (command == 'create') {\n            const options = (args[0] || {});\n            this.create(id, element, options);\n            return;\n        }\n        const player = this._getPlayer(id);\n        switch (command) {\n            case 'play':\n                player.play();\n                break;\n            case 'pause':\n                player.pause();\n                break;\n            case 'reset':\n                player.reset();\n                break;\n            case 'restart':\n                player.restart();\n                break;\n            case 'finish':\n                player.finish();\n                break;\n            case 'init':\n                player.init();\n                break;\n            case 'setPosition':\n                player.setPosition(parseFloat(args[0]));\n                break;\n            case 'destroy':\n                this.destroy(id);\n                break;\n        }\n    }\n}\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n    namespaceId: '',\n    setForRemoval: false,\n    setForMove: false,\n    hasAnimation: false,\n    removedBeforeQueried: false,\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n    namespaceId: '',\n    setForMove: false,\n    setForRemoval: false,\n    hasAnimation: false,\n    removedBeforeQueried: true,\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n    namespaceId;\n    value;\n    options;\n    get params() {\n        return this.options.params;\n    }\n    constructor(input, namespaceId = '') {\n        this.namespaceId = namespaceId;\n        const isObj = input && input.hasOwnProperty('value');\n        const value = isObj ? input['value'] : input;\n        this.value = normalizeTriggerValue(value);\n        if (isObj) {\n            // we drop the value property from options.\n            const { value, ...options } = input;\n            this.options = options;\n        }\n        else {\n            this.options = {};\n        }\n        if (!this.options.params) {\n            this.options.params = {};\n        }\n    }\n    absorbOptions(options) {\n        const newParams = options.params;\n        if (newParams) {\n            const oldParams = this.options.params;\n            Object.keys(newParams).forEach((prop) => {\n                if (oldParams[prop] == null) {\n                    oldParams[prop] = newParams[prop];\n                }\n            });\n        }\n    }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /* @__PURE__ */ new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n    id;\n    hostElement;\n    _engine;\n    players = [];\n    _triggers = new Map();\n    _queue = [];\n    _elementListeners = new Map();\n    _hostClassName;\n    constructor(id, hostElement, _engine) {\n        this.id = id;\n        this.hostElement = hostElement;\n        this._engine = _engine;\n        this._hostClassName = 'ng-tns-' + id;\n        addClass(hostElement, this._hostClassName);\n    }\n    listen(element, name, phase, callback) {\n        if (!this._triggers.has(name)) {\n            throw missingTrigger(phase, name);\n        }\n        if (phase == null || phase.length == 0) {\n            throw missingEvent(name);\n        }\n        if (!isTriggerEventValid(phase)) {\n            throw unsupportedTriggerEvent(phase, name);\n        }\n        const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n        const data = { name, phase, callback };\n        listeners.push(data);\n        const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n        if (!triggersWithStates.has(name)) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n            triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n        }\n        return () => {\n            // the event listener is removed AFTER the flush has occurred such\n            // that leave animations callbacks can fire (otherwise if the node\n            // is removed in between then the listeners would be deregistered)\n            this._engine.afterFlush(() => {\n                const index = listeners.indexOf(data);\n                if (index >= 0) {\n                    listeners.splice(index, 1);\n                }\n                if (!this._triggers.has(name)) {\n                    triggersWithStates.delete(name);\n                }\n            });\n        };\n    }\n    register(name, ast) {\n        if (this._triggers.has(name)) {\n            // throw\n            return false;\n        }\n        else {\n            this._triggers.set(name, ast);\n            return true;\n        }\n    }\n    _getTrigger(name) {\n        const trigger = this._triggers.get(name);\n        if (!trigger) {\n            throw unregisteredTrigger(name);\n        }\n        return trigger;\n    }\n    trigger(element, triggerName, value, defaultToFallback = true) {\n        const trigger = this._getTrigger(triggerName);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        let triggersWithStates = this._engine.statesByElement.get(element);\n        if (!triggersWithStates) {\n            addClass(element, NG_TRIGGER_CLASSNAME);\n            addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n            this._engine.statesByElement.set(element, (triggersWithStates = new Map()));\n        }\n        let fromState = triggersWithStates.get(triggerName);\n        const toState = new StateValue(value, this.id);\n        const isObj = value && value.hasOwnProperty('value');\n        if (!isObj && fromState) {\n            toState.absorbOptions(fromState.options);\n        }\n        triggersWithStates.set(triggerName, toState);\n        if (!fromState) {\n            fromState = DEFAULT_STATE_VALUE;\n        }\n        const isRemoval = toState.value === VOID_VALUE;\n        // normally this isn't reached by here, however, if an object expression\n        // is passed in then it may be a new object each time. Comparing the value\n        // is important since that will stay the same despite there being a new object.\n        // The removal arc here is special cased because the same element is triggered\n        // twice in the event that it contains animations on the outer/inner portions\n        // of the host container\n        if (!isRemoval && fromState.value === toState.value) {\n            // this means that despite the value not changing, some inner params\n            // have changed which means that the animation final styles need to be applied\n            if (!objEquals(fromState.params, toState.params)) {\n                const errors = [];\n                const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n                const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n                if (errors.length) {\n                    this._engine.reportError(errors);\n                }\n                else {\n                    this._engine.afterFlush(() => {\n                        eraseStyles(element, fromStyles);\n                        setStyles(element, toStyles);\n                    });\n                }\n            }\n            return;\n        }\n        const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n        playersOnElement.forEach((player) => {\n            // only remove the player if it is queued on the EXACT same trigger/namespace\n            // we only also deal with queued players here because if the animation has\n            // started then we want to keep the player alive until the flush happens\n            // (which is where the previousPlayers are passed into the new player)\n            if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n                player.destroy();\n            }\n        });\n        let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n        let isFallbackTransition = false;\n        if (!transition) {\n            if (!defaultToFallback)\n                return;\n            transition = trigger.fallbackTransition;\n            isFallbackTransition = true;\n        }\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n            element,\n            triggerName,\n            transition,\n            fromState,\n            toState,\n            player,\n            isFallbackTransition,\n        });\n        if (!isFallbackTransition) {\n            addClass(element, QUEUED_CLASSNAME);\n            player.onStart(() => {\n                removeClass(element, QUEUED_CLASSNAME);\n            });\n        }\n        player.onDone(() => {\n            let index = this.players.indexOf(player);\n            if (index >= 0) {\n                this.players.splice(index, 1);\n            }\n            const players = this._engine.playersByElement.get(element);\n            if (players) {\n                let index = players.indexOf(player);\n                if (index >= 0) {\n                    players.splice(index, 1);\n                }\n            }\n        });\n        this.players.push(player);\n        playersOnElement.push(player);\n        return player;\n    }\n    deregister(name) {\n        this._triggers.delete(name);\n        this._engine.statesByElement.forEach((stateMap) => stateMap.delete(name));\n        this._elementListeners.forEach((listeners, element) => {\n            this._elementListeners.set(element, listeners.filter((entry) => {\n                return entry.name != name;\n            }));\n        });\n    }\n    clearElementCache(element) {\n        this._engine.statesByElement.delete(element);\n        this._elementListeners.delete(element);\n        const elementPlayers = this._engine.playersByElement.get(element);\n        if (elementPlayers) {\n            elementPlayers.forEach((player) => player.destroy());\n            this._engine.playersByElement.delete(element);\n        }\n    }\n    _signalRemovalForInnerTriggers(rootElement, context) {\n        const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n        // emulate a leave animation for all inner nodes within this node.\n        // If there are no animations found for any of the nodes then clear the cache\n        // for the element.\n        elements.forEach((elm) => {\n            // this means that an inner remove() operation has already kicked off\n            // the animation on this element...\n            if (elm[REMOVAL_FLAG])\n                return;\n            const namespaces = this._engine.fetchNamespacesByElement(elm);\n            if (namespaces.size) {\n                namespaces.forEach((ns) => ns.triggerLeaveAnimation(elm, context, false, true));\n            }\n            else {\n                this.clearElementCache(elm);\n            }\n        });\n        // If the child elements were removed along with the parent, their animations might not\n        // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n        this._engine.afterFlushAnimationsDone(() => elements.forEach((elm) => this.clearElementCache(elm)));\n    }\n    triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n        const triggerStates = this._engine.statesByElement.get(element);\n        const previousTriggersValues = new Map();\n        if (triggerStates) {\n            const players = [];\n            triggerStates.forEach((state, triggerName) => {\n                previousTriggersValues.set(triggerName, state.value);\n                // this check is here in the event that an element is removed\n                // twice (both on the host level and the component level)\n                if (this._triggers.has(triggerName)) {\n                    const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n                    if (player) {\n                        players.push(player);\n                    }\n                }\n            });\n            if (players.length) {\n                this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n                if (destroyAfterComplete) {\n                    optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    prepareLeaveAnimationListeners(element) {\n        const listeners = this._elementListeners.get(element);\n        const elementStates = this._engine.statesByElement.get(element);\n        // if this statement fails then it means that the element was picked up\n        // by an earlier flush (or there are no listeners at all to track the leave).\n        if (listeners && elementStates) {\n            const visitedTriggers = new Set();\n            listeners.forEach((listener) => {\n                const triggerName = listener.name;\n                if (visitedTriggers.has(triggerName))\n                    return;\n                visitedTriggers.add(triggerName);\n                const trigger = this._triggers.get(triggerName);\n                const transition = trigger.fallbackTransition;\n                const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n                const toState = new StateValue(VOID_VALUE);\n                const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n                this._engine.totalQueuedPlayers++;\n                this._queue.push({\n                    element,\n                    triggerName,\n                    transition,\n                    fromState,\n                    toState,\n                    player,\n                    isFallbackTransition: true,\n                });\n            });\n        }\n    }\n    removeNode(element, context) {\n        const engine = this._engine;\n        if (element.childElementCount) {\n            this._signalRemovalForInnerTriggers(element, context);\n        }\n        // this means that a * => VOID animation was detected and kicked off\n        if (this.triggerLeaveAnimation(element, context, true))\n            return;\n        // find the player that is animating and make sure that the\n        // removal is delayed until that player has completed\n        let containsPotentialParentTransition = false;\n        if (engine.totalAnimations) {\n            const currentPlayers = engine.players.length\n                ? engine.playersByQueriedElement.get(element)\n                : [];\n            // when this `if statement` does not continue forward it means that\n            // a previous animation query has selected the current element and\n            // is animating it. In this situation want to continue forwards and\n            // allow the element to be queued up for animation later.\n            if (currentPlayers && currentPlayers.length) {\n                containsPotentialParentTransition = true;\n            }\n            else {\n                let parent = element;\n                while ((parent = parent.parentNode)) {\n                    const triggers = engine.statesByElement.get(parent);\n                    if (triggers) {\n                        containsPotentialParentTransition = true;\n                        break;\n                    }\n                }\n            }\n        }\n        // at this stage we know that the element will either get removed\n        // during flush or will be picked up by a parent query. Either way\n        // we need to fire the listeners for this element when it DOES get\n        // removed (once the query parent animation is done or after flush)\n        this.prepareLeaveAnimationListeners(element);\n        // whether or not a parent has an animation we need to delay the deferral of the leave\n        // operation until we have more information (which we do after flush() has been called)\n        if (containsPotentialParentTransition) {\n            engine.markElementAsRemoved(this.id, element, false, context);\n        }\n        else {\n            const removalFlag = element[REMOVAL_FLAG];\n            if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n                // we do this after the flush has occurred such\n                // that the callbacks can be fired\n                engine.afterFlush(() => this.clearElementCache(element));\n                engine.destroyInnerAnimations(element);\n                engine._onRemovalComplete(element, context);\n            }\n        }\n    }\n    insertNode(element, parent) {\n        addClass(element, this._hostClassName);\n    }\n    drainQueuedTransitions(microtaskId) {\n        const instructions = [];\n        this._queue.forEach((entry) => {\n            const player = entry.player;\n            if (player.destroyed)\n                return;\n            const element = entry.element;\n            const listeners = this._elementListeners.get(element);\n            if (listeners) {\n                listeners.forEach((listener) => {\n                    if (listener.name == entry.triggerName) {\n                        const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n                        baseEvent['_data'] = microtaskId;\n                        listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n                    }\n                });\n            }\n            if (player.markedForDestroy) {\n                this._engine.afterFlush(() => {\n                    // now we can destroy the element properly since the event listeners have\n                    // been bound to the player\n                    player.destroy();\n                });\n            }\n            else {\n                instructions.push(entry);\n            }\n        });\n        this._queue = [];\n        return instructions.sort((a, b) => {\n            // if depCount == 0 them move to front\n            // otherwise if a contains b then move back\n            const d0 = a.transition.ast.depCount;\n            const d1 = b.transition.ast.depCount;\n            if (d0 == 0 || d1 == 0) {\n                return d0 - d1;\n            }\n            return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n        });\n    }\n    destroy(context) {\n        this.players.forEach((p) => p.destroy());\n        this._signalRemovalForInnerTriggers(this.hostElement, context);\n    }\n}\nclass TransitionAnimationEngine {\n    bodyNode;\n    driver;\n    _normalizer;\n    players = [];\n    newHostElements = new Map();\n    playersByElement = new Map();\n    playersByQueriedElement = new Map();\n    statesByElement = new Map();\n    disabledNodes = new Set();\n    totalAnimations = 0;\n    totalQueuedPlayers = 0;\n    _namespaceLookup = {};\n    _namespaceList = [];\n    _flushFns = [];\n    _whenQuietFns = [];\n    namespacesByHostElement = new Map();\n    collectedEnterElements = [];\n    collectedLeaveElements = [];\n    // this method is designed to be overridden by the code that uses this engine\n    onRemovalComplete = (element, context) => { };\n    /** @internal */\n    _onRemovalComplete(element, context) {\n        this.onRemovalComplete(element, context);\n    }\n    constructor(bodyNode, driver, _normalizer) {\n        this.bodyNode = bodyNode;\n        this.driver = driver;\n        this._normalizer = _normalizer;\n    }\n    get queuedPlayers() {\n        const players = [];\n        this._namespaceList.forEach((ns) => {\n            ns.players.forEach((player) => {\n                if (player.queued) {\n                    players.push(player);\n                }\n            });\n        });\n        return players;\n    }\n    createNamespace(namespaceId, hostElement) {\n        const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n        if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n            this._balanceNamespaceList(ns, hostElement);\n        }\n        else {\n            // defer this later until flush during when the host element has\n            // been inserted so that we know exactly where to place it in\n            // the namespace list\n            this.newHostElements.set(hostElement, ns);\n            // given that this host element is a part of the animation code, it\n            // may or may not be inserted by a parent node that is of an\n            // animation renderer type. If this happens then we can still have\n            // access to this item when we query for :enter nodes. If the parent\n            // is a renderer then the set data-structure will normalize the entry\n            this.collectEnterElement(hostElement);\n        }\n        return (this._namespaceLookup[namespaceId] = ns);\n    }\n    _balanceNamespaceList(ns, hostElement) {\n        const namespaceList = this._namespaceList;\n        const namespacesByHostElement = this.namespacesByHostElement;\n        const limit = namespaceList.length - 1;\n        if (limit >= 0) {\n            let found = false;\n            // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n            // establishing a top-down ordering of namespaces in `this._namespaceList`.\n            let ancestor = this.driver.getParentElement(hostElement);\n            while (ancestor) {\n                const ancestorNs = namespacesByHostElement.get(ancestor);\n                if (ancestorNs) {\n                    // An animation namespace has been registered for this ancestor, so we insert `ns`\n                    // right after it to establish top-down ordering of animation namespaces.\n                    const index = namespaceList.indexOf(ancestorNs);\n                    namespaceList.splice(index + 1, 0, ns);\n                    found = true;\n                    break;\n                }\n                ancestor = this.driver.getParentElement(ancestor);\n            }\n            if (!found) {\n                // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n                // ensure that any existing descendants are ordered after `ns`, retaining the desired\n                // top-down ordering.\n                namespaceList.unshift(ns);\n            }\n        }\n        else {\n            namespaceList.push(ns);\n        }\n        namespacesByHostElement.set(hostElement, ns);\n        return ns;\n    }\n    register(namespaceId, hostElement) {\n        let ns = this._namespaceLookup[namespaceId];\n        if (!ns) {\n            ns = this.createNamespace(namespaceId, hostElement);\n        }\n        return ns;\n    }\n    registerTrigger(namespaceId, name, trigger) {\n        let ns = this._namespaceLookup[namespaceId];\n        if (ns && ns.register(name, trigger)) {\n            this.totalAnimations++;\n        }\n    }\n    destroy(namespaceId, context) {\n        if (!namespaceId)\n            return;\n        this.afterFlush(() => { });\n        this.afterFlushAnimationsDone(() => {\n            const ns = this._fetchNamespace(namespaceId);\n            this.namespacesByHostElement.delete(ns.hostElement);\n            const index = this._namespaceList.indexOf(ns);\n            if (index >= 0) {\n                this._namespaceList.splice(index, 1);\n            }\n            ns.destroy(context);\n            delete this._namespaceLookup[namespaceId];\n        });\n    }\n    _fetchNamespace(id) {\n        return this._namespaceLookup[id];\n    }\n    fetchNamespacesByElement(element) {\n        // normally there should only be one namespace per element, however\n        // if @triggers are placed on both the component element and then\n        // its host element (within the component code) then there will be\n        // two namespaces returned. We use a set here to simply deduplicate\n        // the namespaces in case (for the reason described above) there are multiple triggers\n        const namespaces = new Set();\n        const elementStates = this.statesByElement.get(element);\n        if (elementStates) {\n            for (let stateValue of elementStates.values()) {\n                if (stateValue.namespaceId) {\n                    const ns = this._fetchNamespace(stateValue.namespaceId);\n                    if (ns) {\n                        namespaces.add(ns);\n                    }\n                }\n            }\n        }\n        return namespaces;\n    }\n    trigger(namespaceId, element, name, value) {\n        if (isElementNode(element)) {\n            const ns = this._fetchNamespace(namespaceId);\n            if (ns) {\n                ns.trigger(element, name, value);\n                return true;\n            }\n        }\n        return false;\n    }\n    insertNode(namespaceId, element, parent, insertBefore) {\n        if (!isElementNode(element))\n            return;\n        // special case for when an element is removed and reinserted (move operation)\n        // when this occurs we do not want to use the element for deletion later\n        const details = element[REMOVAL_FLAG];\n        if (details && details.setForRemoval) {\n            details.setForRemoval = false;\n            details.setForMove = true;\n            const index = this.collectedLeaveElements.indexOf(element);\n            if (index >= 0) {\n                this.collectedLeaveElements.splice(index, 1);\n            }\n        }\n        // in the event that the namespaceId is blank then the caller\n        // code does not contain any animation code in it, but it is\n        // just being called so that the node is marked as being inserted\n        if (namespaceId) {\n            const ns = this._fetchNamespace(namespaceId);\n            // This if-statement is a workaround for router issue #21947.\n            // The router sometimes hits a race condition where while a route\n            // is being instantiated a new navigation arrives, triggering leave\n            // animation of DOM that has not been fully initialized, until this\n            // is resolved, we need to handle the scenario when DOM is not in a\n            // consistent state during the animation.\n            if (ns) {\n                ns.insertNode(element, parent);\n            }\n        }\n        // only *directives and host elements are inserted before\n        if (insertBefore) {\n            this.collectEnterElement(element);\n        }\n    }\n    collectEnterElement(element) {\n        this.collectedEnterElements.push(element);\n    }\n    markElementAsDisabled(element, value) {\n        if (value) {\n            if (!this.disabledNodes.has(element)) {\n                this.disabledNodes.add(element);\n                addClass(element, DISABLED_CLASSNAME);\n            }\n        }\n        else if (this.disabledNodes.has(element)) {\n            this.disabledNodes.delete(element);\n            removeClass(element, DISABLED_CLASSNAME);\n        }\n    }\n    removeNode(namespaceId, element, context) {\n        if (isElementNode(element)) {\n            const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n            if (ns) {\n                ns.removeNode(element, context);\n            }\n            else {\n                this.markElementAsRemoved(namespaceId, element, false, context);\n            }\n            const hostNS = this.namespacesByHostElement.get(element);\n            if (hostNS && hostNS.id !== namespaceId) {\n                hostNS.removeNode(element, context);\n            }\n        }\n        else {\n            this._onRemovalComplete(element, context);\n        }\n    }\n    markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n        this.collectedLeaveElements.push(element);\n        element[REMOVAL_FLAG] = {\n            namespaceId,\n            setForRemoval: context,\n            hasAnimation,\n            removedBeforeQueried: false,\n            previousTriggersValues,\n        };\n    }\n    listen(namespaceId, element, name, phase, callback) {\n        if (isElementNode(element)) {\n            return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n        }\n        return () => { };\n    }\n    _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n        return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n    }\n    destroyInnerAnimations(containerElement) {\n        let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n        elements.forEach((element) => this.destroyActiveAnimationsForElement(element));\n        if (this.playersByQueriedElement.size == 0)\n            return;\n        elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n        elements.forEach((element) => this.finishActiveQueriedAnimationOnElement(element));\n    }\n    destroyActiveAnimationsForElement(element) {\n        const players = this.playersByElement.get(element);\n        if (players) {\n            players.forEach((player) => {\n                // special case for when an element is set for destruction, but hasn't started.\n                // in this situation we want to delay the destruction until the flush occurs\n                // so that any event listeners attached to the player are triggered.\n                if (player.queued) {\n                    player.markedForDestroy = true;\n                }\n                else {\n                    player.destroy();\n                }\n            });\n        }\n    }\n    finishActiveQueriedAnimationOnElement(element) {\n        const players = this.playersByQueriedElement.get(element);\n        if (players) {\n            players.forEach((player) => player.finish());\n        }\n    }\n    whenRenderingDone() {\n        return new Promise((resolve) => {\n            if (this.players.length) {\n                return optimizeGroupPlayer(this.players).onDone(() => resolve());\n            }\n            else {\n                resolve();\n            }\n        });\n    }\n    processLeaveNode(element) {\n        const details = element[REMOVAL_FLAG];\n        if (details && details.setForRemoval) {\n            // this will prevent it from removing it twice\n            element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n            if (details.namespaceId) {\n                this.destroyInnerAnimations(element);\n                const ns = this._fetchNamespace(details.namespaceId);\n                if (ns) {\n                    ns.clearElementCache(element);\n                }\n            }\n            this._onRemovalComplete(element, details.setForRemoval);\n        }\n        if (element.classList?.contains(DISABLED_CLASSNAME)) {\n            this.markElementAsDisabled(element, false);\n        }\n        this.driver.query(element, DISABLED_SELECTOR, true).forEach((node) => {\n            this.markElementAsDisabled(node, false);\n        });\n    }\n    flush(microtaskId = -1) {\n        let players = [];\n        if (this.newHostElements.size) {\n            this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n            this.newHostElements.clear();\n        }\n        if (this.totalAnimations && this.collectedEnterElements.length) {\n            for (let i = 0; i < this.collectedEnterElements.length; i++) {\n                const elm = this.collectedEnterElements[i];\n                addClass(elm, STAR_CLASSNAME);\n            }\n        }\n        if (this._namespaceList.length &&\n            (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n            const cleanupFns = [];\n            try {\n                players = this._flushAnimations(cleanupFns, microtaskId);\n            }\n            finally {\n                for (let i = 0; i < cleanupFns.length; i++) {\n                    cleanupFns[i]();\n                }\n            }\n        }\n        else {\n            for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n                const element = this.collectedLeaveElements[i];\n                this.processLeaveNode(element);\n            }\n        }\n        this.totalQueuedPlayers = 0;\n        this.collectedEnterElements.length = 0;\n        this.collectedLeaveElements.length = 0;\n        this._flushFns.forEach((fn) => fn());\n        this._flushFns = [];\n        if (this._whenQuietFns.length) {\n            // we move these over to a variable so that\n            // if any new callbacks are registered in another\n            // flush they do not populate the existing set\n            const quietFns = this._whenQuietFns;\n            this._whenQuietFns = [];\n            if (players.length) {\n                optimizeGroupPlayer(players).onDone(() => {\n                    quietFns.forEach((fn) => fn());\n                });\n            }\n            else {\n                quietFns.forEach((fn) => fn());\n            }\n        }\n    }\n    reportError(errors) {\n        throw triggerTransitionsFailed(errors);\n    }\n    _flushAnimations(cleanupFns, microtaskId) {\n        const subTimelines = new ElementInstructionMap();\n        const skippedPlayers = [];\n        const skippedPlayersMap = new Map();\n        const queuedInstructions = [];\n        const queriedElements = new Map();\n        const allPreStyleElements = new Map();\n        const allPostStyleElements = new Map();\n        const disabledElementsSet = new Set();\n        this.disabledNodes.forEach((node) => {\n            disabledElementsSet.add(node);\n            const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n            for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n                disabledElementsSet.add(nodesThatAreDisabled[i]);\n            }\n        });\n        const bodyNode = this.bodyNode;\n        const allTriggerElements = Array.from(this.statesByElement.keys());\n        const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n        // this must occur before the instructions are built below such that\n        // the :enter queries match the elements (since the timeline queries\n        // are fired during instruction building).\n        const enterNodeMapIds = new Map();\n        let i = 0;\n        enterNodeMap.forEach((nodes, root) => {\n            const className = ENTER_CLASSNAME + i++;\n            enterNodeMapIds.set(root, className);\n            nodes.forEach((node) => addClass(node, className));\n        });\n        const allLeaveNodes = [];\n        const mergedLeaveNodes = new Set();\n        const leaveNodesWithoutAnimations = new Set();\n        for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n            const element = this.collectedLeaveElements[i];\n            const details = element[REMOVAL_FLAG];\n            if (details && details.setForRemoval) {\n                allLeaveNodes.push(element);\n                mergedLeaveNodes.add(element);\n                if (details.hasAnimation) {\n                    this.driver\n                        .query(element, STAR_SELECTOR, true)\n                        .forEach((elm) => mergedLeaveNodes.add(elm));\n                }\n                else {\n                    leaveNodesWithoutAnimations.add(element);\n                }\n            }\n        }\n        const leaveNodeMapIds = new Map();\n        const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n        leaveNodeMap.forEach((nodes, root) => {\n            const className = LEAVE_CLASSNAME + i++;\n            leaveNodeMapIds.set(root, className);\n            nodes.forEach((node) => addClass(node, className));\n        });\n        cleanupFns.push(() => {\n            enterNodeMap.forEach((nodes, root) => {\n                const className = enterNodeMapIds.get(root);\n                nodes.forEach((node) => removeClass(node, className));\n            });\n            leaveNodeMap.forEach((nodes, root) => {\n                const className = leaveNodeMapIds.get(root);\n                nodes.forEach((node) => removeClass(node, className));\n            });\n            allLeaveNodes.forEach((element) => {\n                this.processLeaveNode(element);\n            });\n        });\n        const allPlayers = [];\n        const erroneousTransitions = [];\n        for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n            const ns = this._namespaceList[i];\n            ns.drainQueuedTransitions(microtaskId).forEach((entry) => {\n                const player = entry.player;\n                const element = entry.element;\n                allPlayers.push(player);\n                if (this.collectedEnterElements.length) {\n                    const details = element[REMOVAL_FLAG];\n                    // animations for move operations (elements being removed and reinserted,\n                    // e.g. when the order of an *ngFor list changes) are currently not supported\n                    if (details && details.setForMove) {\n                        if (details.previousTriggersValues &&\n                            details.previousTriggersValues.has(entry.triggerName)) {\n                            const previousValue = details.previousTriggersValues.get(entry.triggerName);\n                            // we need to restore the previous trigger value since the element has\n                            // only been moved and hasn't actually left the DOM\n                            const triggersWithStates = this.statesByElement.get(entry.element);\n                            if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                                const state = triggersWithStates.get(entry.triggerName);\n                                state.value = previousValue;\n                                triggersWithStates.set(entry.triggerName, state);\n                            }\n                        }\n                        player.destroy();\n                        return;\n                    }\n                }\n                const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n                const leaveClassName = leaveNodeMapIds.get(element);\n                const enterClassName = enterNodeMapIds.get(element);\n                const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n                if (instruction.errors && instruction.errors.length) {\n                    erroneousTransitions.push(instruction);\n                    return;\n                }\n                // even though the element may not be in the DOM, it may still\n                // be added at a later point (due to the mechanics of content\n                // projection and/or dynamic component insertion) therefore it's\n                // important to still style the element.\n                if (nodeIsOrphaned) {\n                    player.onStart(() => eraseStyles(element, instruction.fromStyles));\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // if an unmatched transition is queued and ready to go\n                // then it SHOULD NOT render an animation and cancel the\n                // previously running animations.\n                if (entry.isFallbackTransition) {\n                    player.onStart(() => eraseStyles(element, instruction.fromStyles));\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this means that if a parent animation uses this animation as a sub-trigger\n                // then it will instruct the timeline builder not to add a player delay, but\n                // instead stretch the first keyframe gap until the animation starts. This is\n                // important in order to prevent extra initialization styles from being\n                // required by the user for the animation.\n                const timelines = [];\n                instruction.timelines.forEach((tl) => {\n                    tl.stretchStartingKeyframe = true;\n                    if (!this.disabledNodes.has(tl.element)) {\n                        timelines.push(tl);\n                    }\n                });\n                instruction.timelines = timelines;\n                subTimelines.append(element, instruction.timelines);\n                const tuple = { instruction, player, element };\n                queuedInstructions.push(tuple);\n                instruction.queriedElements.forEach((element) => getOrSetDefaultValue(queriedElements, element, []).push(player));\n                instruction.preStyleProps.forEach((stringMap, element) => {\n                    if (stringMap.size) {\n                        let setVal = allPreStyleElements.get(element);\n                        if (!setVal) {\n                            allPreStyleElements.set(element, (setVal = new Set()));\n                        }\n                        stringMap.forEach((_, prop) => setVal.add(prop));\n                    }\n                });\n                instruction.postStyleProps.forEach((stringMap, element) => {\n                    let setVal = allPostStyleElements.get(element);\n                    if (!setVal) {\n                        allPostStyleElements.set(element, (setVal = new Set()));\n                    }\n                    stringMap.forEach((_, prop) => setVal.add(prop));\n                });\n            });\n        }\n        if (erroneousTransitions.length) {\n            const errors = [];\n            erroneousTransitions.forEach((instruction) => {\n                errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n            });\n            allPlayers.forEach((player) => player.destroy());\n            this.reportError(errors);\n        }\n        const allPreviousPlayersMap = new Map();\n        // this map tells us which element in the DOM tree is contained by\n        // which animation. Further down this map will get populated once\n        // the players are built and in doing so we can use it to efficiently\n        // figure out if a sub player is skipped due to a parent player having priority.\n        const animationElementMap = new Map();\n        queuedInstructions.forEach((entry) => {\n            const element = entry.element;\n            if (subTimelines.has(element)) {\n                animationElementMap.set(element, element);\n                this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n            }\n        });\n        skippedPlayers.forEach((player) => {\n            const element = player.element;\n            const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n            previousPlayers.forEach((prevPlayer) => {\n                getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n                prevPlayer.destroy();\n            });\n        });\n        // this is a special case for nodes that will be removed either by\n        // having their own leave animations or by being queried in a container\n        // that will be removed once a parent animation is complete. The idea\n        // here is that * styles must be identical to ! styles because of\n        // backwards compatibility (* is also filled in by default in many places).\n        // Otherwise * styles will return an empty value or \"auto\" since the element\n        // passed to getComputedStyle will not be visible (since * === destination)\n        const replaceNodes = allLeaveNodes.filter((node) => {\n            return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n        });\n        // POST STAGE: fill the * styles\n        const postStylesMap = new Map();\n        const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n        allLeaveQueriedNodes.forEach((node) => {\n            if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n                replaceNodes.push(node);\n            }\n        });\n        // PRE STAGE: fill the ! styles\n        const preStylesMap = new Map();\n        enterNodeMap.forEach((nodes, root) => {\n            cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, _PRE_STYLE);\n        });\n        replaceNodes.forEach((node) => {\n            const post = postStylesMap.get(node);\n            const pre = preStylesMap.get(node);\n            postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n        });\n        const rootPlayers = [];\n        const subPlayers = [];\n        const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n        queuedInstructions.forEach((entry) => {\n            const { element, player, instruction } = entry;\n            // this means that it was never consumed by a parent animation which\n            // means that it is independent and therefore should be set for animation\n            if (subTimelines.has(element)) {\n                if (disabledElementsSet.has(element)) {\n                    player.onDestroy(() => setStyles(element, instruction.toStyles));\n                    player.disabled = true;\n                    player.overrideTotalTime(instruction.totalTime);\n                    skippedPlayers.push(player);\n                    return;\n                }\n                // this will flow up the DOM and query the map to figure out\n                // if a parent animation has priority over it. In the situation\n                // that a parent is detected then it will cancel the loop. If\n                // nothing is detected, or it takes a few hops to find a parent,\n                // then it will fill in the missing nodes and signal them as having\n                // a detected parent (or a NO_PARENT value via a special constant).\n                let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n                if (animationElementMap.size > 1) {\n                    let elm = element;\n                    const parentsToAdd = [];\n                    while ((elm = elm.parentNode)) {\n                        const detectedParent = animationElementMap.get(elm);\n                        if (detectedParent) {\n                            parentWithAnimation = detectedParent;\n                            break;\n                        }\n                        parentsToAdd.push(elm);\n                    }\n                    parentsToAdd.forEach((parent) => animationElementMap.set(parent, parentWithAnimation));\n                }\n                const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n                player.setRealPlayer(innerPlayer);\n                if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n                    rootPlayers.push(player);\n                }\n                else {\n                    const parentPlayers = this.playersByElement.get(parentWithAnimation);\n                    if (parentPlayers && parentPlayers.length) {\n                        player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n                    }\n                    skippedPlayers.push(player);\n                }\n            }\n            else {\n                eraseStyles(element, instruction.fromStyles);\n                player.onDestroy(() => setStyles(element, instruction.toStyles));\n                // there still might be a ancestor player animating this\n                // element therefore we will still add it as a sub player\n                // even if its animation may be disabled\n                subPlayers.push(player);\n                if (disabledElementsSet.has(element)) {\n                    skippedPlayers.push(player);\n                }\n            }\n        });\n        // find all of the sub players' corresponding inner animation players\n        subPlayers.forEach((player) => {\n            // even if no players are found for a sub animation it\n            // will still complete itself after the next tick since it's Noop\n            const playersForElement = skippedPlayersMap.get(player.element);\n            if (playersForElement && playersForElement.length) {\n                const innerPlayer = optimizeGroupPlayer(playersForElement);\n                player.setRealPlayer(innerPlayer);\n            }\n        });\n        // the reason why we don't actually play the animation is\n        // because all that a skipped player is designed to do is to\n        // fire the start/done transition callback events\n        skippedPlayers.forEach((player) => {\n            if (player.parentPlayer) {\n                player.syncPlayerEvents(player.parentPlayer);\n            }\n            else {\n                player.destroy();\n            }\n        });\n        // run through all of the queued removals and see if they\n        // were picked up by a query. If not then perform the removal\n        // operation right away unless a parent animation is ongoing.\n        for (let i = 0; i < allLeaveNodes.length; i++) {\n            const element = allLeaveNodes[i];\n            const details = element[REMOVAL_FLAG];\n            removeClass(element, LEAVE_CLASSNAME);\n            // this means the element has a removal animation that is being\n            // taken care of and therefore the inner elements will hang around\n            // until that animation is over (or the parent queried animation)\n            if (details && details.hasAnimation)\n                continue;\n            let players = [];\n            // if this element is queried or if it contains queried children\n            // then we want for the element not to be removed from the page\n            // until the queried animations have finished\n            if (queriedElements.size) {\n                let queriedPlayerResults = queriedElements.get(element);\n                if (queriedPlayerResults && queriedPlayerResults.length) {\n                    players.push(...queriedPlayerResults);\n                }\n                let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n                for (let j = 0; j < queriedInnerElements.length; j++) {\n                    let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n                    if (queriedPlayers && queriedPlayers.length) {\n                        players.push(...queriedPlayers);\n                    }\n                }\n            }\n            const activePlayers = players.filter((p) => !p.destroyed);\n            if (activePlayers.length) {\n                removeNodesAfterAnimationDone(this, element, activePlayers);\n            }\n            else {\n                this.processLeaveNode(element);\n            }\n        }\n        // this is required so the cleanup method doesn't remove them\n        allLeaveNodes.length = 0;\n        rootPlayers.forEach((player) => {\n            this.players.push(player);\n            player.onDone(() => {\n                player.destroy();\n                const index = this.players.indexOf(player);\n                this.players.splice(index, 1);\n            });\n            player.play();\n        });\n        return rootPlayers;\n    }\n    afterFlush(callback) {\n        this._flushFns.push(callback);\n    }\n    afterFlushAnimationsDone(callback) {\n        this._whenQuietFns.push(callback);\n    }\n    _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n        let players = [];\n        if (isQueriedElement) {\n            const queriedElementPlayers = this.playersByQueriedElement.get(element);\n            if (queriedElementPlayers) {\n                players = queriedElementPlayers;\n            }\n        }\n        else {\n            const elementPlayers = this.playersByElement.get(element);\n            if (elementPlayers) {\n                const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n                elementPlayers.forEach((player) => {\n                    if (player.queued)\n                        return;\n                    if (!isRemovalAnimation && player.triggerName != triggerName)\n                        return;\n                    players.push(player);\n                });\n            }\n        }\n        if (namespaceId || triggerName) {\n            players = players.filter((player) => {\n                if (namespaceId && namespaceId != player.namespaceId)\n                    return false;\n                if (triggerName && triggerName != player.triggerName)\n                    return false;\n                return true;\n            });\n        }\n        return players;\n    }\n    _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n        const triggerName = instruction.triggerName;\n        const rootElement = instruction.element;\n        // when a removal animation occurs, ALL previous players are collected\n        // and destroyed (even if they are outside of the current namespace)\n        const targetNameSpaceId = instruction.isRemovalTransition\n            ? undefined\n            : namespaceId;\n        const targetTriggerName = instruction.isRemovalTransition\n            ? undefined\n            : triggerName;\n        for (const timelineInstruction of instruction.timelines) {\n            const element = timelineInstruction.element;\n            const isQueriedElement = element !== rootElement;\n            const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n            const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n            previousPlayers.forEach((player) => {\n                const realPlayer = player.getRealPlayer();\n                if (realPlayer.beforeDestroy) {\n                    realPlayer.beforeDestroy();\n                }\n                player.destroy();\n                players.push(player);\n            });\n        }\n        // this needs to be done so that the PRE/POST styles can be\n        // computed properly without interfering with the previous animation\n        eraseStyles(rootElement, instruction.fromStyles);\n    }\n    _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n        const triggerName = instruction.triggerName;\n        const rootElement = instruction.element;\n        // we first run this so that the previous animation player\n        // data can be passed into the successive animation players\n        const allQueriedPlayers = [];\n        const allConsumedElements = new Set();\n        const allSubElements = new Set();\n        const allNewPlayers = instruction.timelines.map((timelineInstruction) => {\n            const element = timelineInstruction.element;\n            allConsumedElements.add(element);\n            // FIXME (matsko): make sure to-be-removed animations are removed properly\n            const details = element[REMOVAL_FLAG];\n            if (details && details.removedBeforeQueried)\n                return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n            const isQueriedElement = element !== rootElement;\n            const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map((p) => p.getRealPlayer())).filter((p) => {\n                // the `element` is not apart of the AnimationPlayer definition, but\n                // Mock/WebAnimations\n                // use the element within their implementation. This will be added in Angular5 to\n                // AnimationPlayer\n                const pp = p;\n                return pp.element ? pp.element === element : false;\n            });\n            const preStyles = preStylesMap.get(element);\n            const postStyles = postStylesMap.get(element);\n            const keyframes = normalizeKeyframes(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n            const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n            // this means that this particular player belongs to a sub trigger. It is\n            // important that we match this player up with the corresponding (@trigger.listener)\n            if (timelineInstruction.subTimeline && skippedPlayersMap) {\n                allSubElements.add(element);\n            }\n            if (isQueriedElement) {\n                const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n                wrappedPlayer.setRealPlayer(player);\n                allQueriedPlayers.push(wrappedPlayer);\n            }\n            return player;\n        });\n        allQueriedPlayers.forEach((player) => {\n            getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n            player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n        });\n        allConsumedElements.forEach((element) => addClass(element, NG_ANIMATING_CLASSNAME));\n        const player = optimizeGroupPlayer(allNewPlayers);\n        player.onDestroy(() => {\n            allConsumedElements.forEach((element) => removeClass(element, NG_ANIMATING_CLASSNAME));\n            setStyles(rootElement, instruction.toStyles);\n        });\n        // this basically makes all of the callbacks for sub element animations\n        // be dependent on the upper players for when they finish\n        allSubElements.forEach((element) => {\n            getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n        });\n        return player;\n    }\n    _buildPlayer(instruction, keyframes, previousPlayers) {\n        if (keyframes.length > 0) {\n            return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n        }\n        // special case for when an empty transition|definition is provided\n        // ... there is no point in rendering an empty animation\n        return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n    }\n}\nclass TransitionAnimationPlayer {\n    namespaceId;\n    triggerName;\n    element;\n    _player = new NoopAnimationPlayer();\n    _containsRealPlayer = false;\n    _queuedCallbacks = new Map();\n    destroyed = false;\n    parentPlayer = null;\n    markedForDestroy = false;\n    disabled = false;\n    queued = true;\n    totalTime = 0;\n    constructor(namespaceId, triggerName, element) {\n        this.namespaceId = namespaceId;\n        this.triggerName = triggerName;\n        this.element = element;\n    }\n    setRealPlayer(player) {\n        if (this._containsRealPlayer)\n            return;\n        this._player = player;\n        this._queuedCallbacks.forEach((callbacks, phase) => {\n            callbacks.forEach((callback) => listenOnPlayer(player, phase, undefined, callback));\n        });\n        this._queuedCallbacks.clear();\n        this._containsRealPlayer = true;\n        this.overrideTotalTime(player.totalTime);\n        this.queued = false;\n    }\n    getRealPlayer() {\n        return this._player;\n    }\n    overrideTotalTime(totalTime) {\n        this.totalTime = totalTime;\n    }\n    syncPlayerEvents(player) {\n        const p = this._player;\n        if (p.triggerCallback) {\n            player.onStart(() => p.triggerCallback('start'));\n        }\n        player.onDone(() => this.finish());\n        player.onDestroy(() => this.destroy());\n    }\n    _queueEvent(name, callback) {\n        getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n    }\n    onDone(fn) {\n        if (this.queued) {\n            this._queueEvent('done', fn);\n        }\n        this._player.onDone(fn);\n    }\n    onStart(fn) {\n        if (this.queued) {\n            this._queueEvent('start', fn);\n        }\n        this._player.onStart(fn);\n    }\n    onDestroy(fn) {\n        if (this.queued) {\n            this._queueEvent('destroy', fn);\n        }\n        this._player.onDestroy(fn);\n    }\n    init() {\n        this._player.init();\n    }\n    hasStarted() {\n        return this.queued ? false : this._player.hasStarted();\n    }\n    play() {\n        !this.queued && this._player.play();\n    }\n    pause() {\n        !this.queued && this._player.pause();\n    }\n    restart() {\n        !this.queued && this._player.restart();\n    }\n    finish() {\n        this._player.finish();\n    }\n    destroy() {\n        this.destroyed = true;\n        this._player.destroy();\n    }\n    reset() {\n        !this.queued && this._player.reset();\n    }\n    setPosition(p) {\n        if (!this.queued) {\n            this._player.setPosition(p);\n        }\n    }\n    getPosition() {\n        return this.queued ? 0 : this._player.getPosition();\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const p = this._player;\n        if (p.triggerCallback) {\n            p.triggerCallback(phaseName);\n        }\n    }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n    let currentValues = map.get(key);\n    if (currentValues) {\n        if (currentValues.length) {\n            const index = currentValues.indexOf(value);\n            currentValues.splice(index, 1);\n        }\n        if (currentValues.length == 0) {\n            map.delete(key);\n        }\n    }\n    return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n    // we use `!= null` here because it's the most simple\n    // way to test against a \"falsy\" value without mixing\n    // in empty strings or a zero value. DO NOT OPTIMIZE.\n    return value != null ? value : null;\n}\nfunction isElementNode(node) {\n    return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n    return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n    const oldValue = element.style.display;\n    element.style.display = value != null ? value : 'none';\n    return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n    const cloakVals = [];\n    elements.forEach((element) => cloakVals.push(cloakElement(element)));\n    const failedElements = [];\n    elementPropsMap.forEach((props, element) => {\n        const styles = new Map();\n        props.forEach((prop) => {\n            const value = driver.computeStyle(element, prop, defaultStyle);\n            styles.set(prop, value);\n            // there is no easy way to detect this because a sub element could be removed\n            // by a parent animation element being detached.\n            if (!value || value.length == 0) {\n                element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n                failedElements.push(element);\n            }\n        });\n        valuesMap.set(element, styles);\n    });\n    // we use a index variable here since Set.forEach(a, i) does not return\n    // an index value for the closure (but instead just the value)\n    let i = 0;\n    elements.forEach((element) => cloakElement(element, cloakVals[i++]));\n    return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n    const rootMap = new Map();\n    roots.forEach((root) => rootMap.set(root, []));\n    if (nodes.length == 0)\n        return rootMap;\n    const NULL_NODE = 1;\n    const nodeSet = new Set(nodes);\n    const localRootMap = new Map();\n    function getRoot(node) {\n        if (!node)\n            return NULL_NODE;\n        let root = localRootMap.get(node);\n        if (root)\n            return root;\n        const parent = node.parentNode;\n        if (rootMap.has(parent)) {\n            // ngIf inside @trigger\n            root = parent;\n        }\n        else if (nodeSet.has(parent)) {\n            // ngIf inside ngIf\n            root = NULL_NODE;\n        }\n        else {\n            // recurse upwards\n            root = getRoot(parent);\n        }\n        localRootMap.set(node, root);\n        return root;\n    }\n    nodes.forEach((node) => {\n        const root = getRoot(node);\n        if (root !== NULL_NODE) {\n            rootMap.get(root).push(node);\n        }\n    });\n    return rootMap;\n}\nfunction addClass(element, className) {\n    element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n    element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n    optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n    const finalPlayers = [];\n    _flattenGroupPlayersRecur(players, finalPlayers);\n    return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n    for (let i = 0; i < players.length; i++) {\n        const player = players[i];\n        if (player instanceof AnimationGroupPlayer) {\n            _flattenGroupPlayersRecur(player.players, finalPlayers);\n        }\n        else {\n            finalPlayers.push(player);\n        }\n    }\n}\nfunction objEquals(a, b) {\n    const k1 = Object.keys(a);\n    const k2 = Object.keys(b);\n    if (k1.length != k2.length)\n        return false;\n    for (let i = 0; i < k1.length; i++) {\n        const prop = k1[i];\n        if (!b.hasOwnProperty(prop) || a[prop] !== b[prop])\n            return false;\n    }\n    return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n    const postEntry = allPostStyleElements.get(element);\n    if (!postEntry)\n        return false;\n    let preEntry = allPreStyleElements.get(element);\n    if (preEntry) {\n        postEntry.forEach((data) => preEntry.add(data));\n    }\n    else {\n        allPreStyleElements.set(element, postEntry);\n    }\n    allPostStyleElements.delete(element);\n    return true;\n}\n\nclass AnimationEngine {\n    _driver;\n    _normalizer;\n    _transitionEngine;\n    _timelineEngine;\n    _triggerCache = {};\n    // this method is designed to be overridden by the code that uses this engine\n    onRemovalComplete = (element, context) => { };\n    constructor(doc, _driver, _normalizer) {\n        this._driver = _driver;\n        this._normalizer = _normalizer;\n        this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n        this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n        this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n    }\n    registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n        const cacheKey = componentId + '-' + name;\n        let trigger = this._triggerCache[cacheKey];\n        if (!trigger) {\n            const errors = [];\n            const warnings = [];\n            const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n            if (errors.length) {\n                throw triggerBuildFailed(name, errors);\n            }\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                if (warnings.length) {\n                    warnTriggerBuild(name, warnings);\n                }\n            }\n            trigger = buildTrigger(name, ast, this._normalizer);\n            this._triggerCache[cacheKey] = trigger;\n        }\n        this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n    }\n    register(namespaceId, hostElement) {\n        this._transitionEngine.register(namespaceId, hostElement);\n    }\n    destroy(namespaceId, context) {\n        this._transitionEngine.destroy(namespaceId, context);\n    }\n    onInsert(namespaceId, element, parent, insertBefore) {\n        this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n    }\n    onRemove(namespaceId, element, context) {\n        this._transitionEngine.removeNode(namespaceId, element, context);\n    }\n    disableAnimations(element, disable) {\n        this._transitionEngine.markElementAsDisabled(element, disable);\n    }\n    process(namespaceId, element, property, value) {\n        if (property.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(property);\n            const args = value;\n            this._timelineEngine.command(id, element, action, args);\n        }\n        else {\n            this._transitionEngine.trigger(namespaceId, element, property, value);\n        }\n    }\n    listen(namespaceId, element, eventName, eventPhase, callback) {\n        // @@listen\n        if (eventName.charAt(0) == '@') {\n            const [id, action] = parseTimelineCommand(eventName);\n            return this._timelineEngine.listen(id, element, action, callback);\n        }\n        return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n    }\n    flush(microtaskId = -1) {\n        this._transitionEngine.flush(microtaskId);\n    }\n    get players() {\n        return [...this._transitionEngine.players, ...this._timelineEngine.players];\n    }\n    whenRenderingDone() {\n        return this._transitionEngine.whenRenderingDone();\n    }\n    afterFlushAnimationsDone(cb) {\n        this._transitionEngine.afterFlushAnimationsDone(cb);\n    }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n    let startStyles = null;\n    let endStyles = null;\n    if (Array.isArray(styles) && styles.length) {\n        startStyles = filterNonAnimatableStyles(styles[0]);\n        if (styles.length > 1) {\n            endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n        }\n    }\n    else if (styles instanceof Map) {\n        startStyles = filterNonAnimatableStyles(styles);\n    }\n    return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nclass SpecialCasedStyles {\n    _element;\n    _startStyles;\n    _endStyles;\n    static initialStylesByElement = /* @__PURE__ */ new WeakMap();\n    _state = 0 /* SpecialCasedStylesState.Pending */;\n    _initialStyles;\n    constructor(_element, _startStyles, _endStyles) {\n        this._element = _element;\n        this._startStyles = _startStyles;\n        this._endStyles = _endStyles;\n        let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n        if (!initialStyles) {\n            SpecialCasedStyles.initialStylesByElement.set(_element, (initialStyles = new Map()));\n        }\n        this._initialStyles = initialStyles;\n    }\n    start() {\n        if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n            if (this._startStyles) {\n                setStyles(this._element, this._startStyles, this._initialStyles);\n            }\n            this._state = 1 /* SpecialCasedStylesState.Started */;\n        }\n    }\n    finish() {\n        this.start();\n        if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n            setStyles(this._element, this._initialStyles);\n            if (this._endStyles) {\n                setStyles(this._element, this._endStyles);\n                this._endStyles = null;\n            }\n            this._state = 1 /* SpecialCasedStylesState.Started */;\n        }\n    }\n    destroy() {\n        this.finish();\n        if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n            SpecialCasedStyles.initialStylesByElement.delete(this._element);\n            if (this._startStyles) {\n                eraseStyles(this._element, this._startStyles);\n                this._endStyles = null;\n            }\n            if (this._endStyles) {\n                eraseStyles(this._element, this._endStyles);\n                this._endStyles = null;\n            }\n            setStyles(this._element, this._initialStyles);\n            this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n        }\n    }\n}\nfunction filterNonAnimatableStyles(styles) {\n    let result = null;\n    styles.forEach((val, prop) => {\n        if (isNonAnimatableStyle(prop)) {\n            result = result || new Map();\n            result.set(prop, val);\n        }\n    });\n    return result;\n}\nfunction isNonAnimatableStyle(prop) {\n    return prop === 'display' || prop === 'position';\n}\n\nclass WebAnimationsPlayer {\n    element;\n    keyframes;\n    options;\n    _specialStyles;\n    _onDoneFns = [];\n    _onStartFns = [];\n    _onDestroyFns = [];\n    _duration;\n    _delay;\n    _initialized = false;\n    _finished = false;\n    _started = false;\n    _destroyed = false;\n    _finalKeyframe;\n    // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n    // and are used to reset the fns to their original values upon reset()\n    // (since the _onStartFns and _onDoneFns get deleted after they are called)\n    _originalOnDoneFns = [];\n    _originalOnStartFns = [];\n    // using non-null assertion because it's re(set) by init();\n    domPlayer;\n    time = 0;\n    parentPlayer = null;\n    currentSnapshot = new Map();\n    constructor(element, keyframes, options, _specialStyles) {\n        this.element = element;\n        this.keyframes = keyframes;\n        this.options = options;\n        this._specialStyles = _specialStyles;\n        this._duration = options['duration'];\n        this._delay = options['delay'] || 0;\n        this.time = this._duration + this._delay;\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    init() {\n        this._buildPlayer();\n        this._preparePlayerBeforeStart();\n    }\n    _buildPlayer() {\n        if (this._initialized)\n            return;\n        this._initialized = true;\n        const keyframes = this.keyframes;\n        // @ts-expect-error overwriting a readonly property\n        this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n        this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n        const onFinish = () => this._onFinish();\n        this.domPlayer.addEventListener('finish', onFinish);\n        this.onDestroy(() => {\n            // We must remove the `finish` event listener once an animation has completed all its\n            // iterations. This action is necessary to prevent a memory leak since the listener captures\n            // `this`, creating a closure that prevents `this` from being garbage collected.\n            this.domPlayer.removeEventListener('finish', onFinish);\n        });\n    }\n    _preparePlayerBeforeStart() {\n        // this is required so that the player doesn't start to animate right away\n        if (this._delay) {\n            this._resetDomPlayerState();\n        }\n        else {\n            this.domPlayer.pause();\n        }\n    }\n    _convertKeyframesToObject(keyframes) {\n        const kfs = [];\n        keyframes.forEach((frame) => {\n            kfs.push(Object.fromEntries(frame));\n        });\n        return kfs;\n    }\n    /** @internal */\n    _triggerWebAnimation(element, keyframes, options) {\n        return element.animate(this._convertKeyframesToObject(keyframes), options);\n    }\n    onStart(fn) {\n        this._originalOnStartFns.push(fn);\n        this._onStartFns.push(fn);\n    }\n    onDone(fn) {\n        this._originalOnDoneFns.push(fn);\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    play() {\n        this._buildPlayer();\n        if (!this.hasStarted()) {\n            this._onStartFns.forEach((fn) => fn());\n            this._onStartFns = [];\n            this._started = true;\n            if (this._specialStyles) {\n                this._specialStyles.start();\n            }\n        }\n        this.domPlayer.play();\n    }\n    pause() {\n        this.init();\n        this.domPlayer.pause();\n    }\n    finish() {\n        this.init();\n        if (this._specialStyles) {\n            this._specialStyles.finish();\n        }\n        this._onFinish();\n        this.domPlayer.finish();\n    }\n    reset() {\n        this._resetDomPlayerState();\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n        this._onStartFns = this._originalOnStartFns;\n        this._onDoneFns = this._originalOnDoneFns;\n    }\n    _resetDomPlayerState() {\n        if (this.domPlayer) {\n            this.domPlayer.cancel();\n        }\n    }\n    restart() {\n        this.reset();\n        this.play();\n    }\n    hasStarted() {\n        return this._started;\n    }\n    destroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._resetDomPlayerState();\n            this._onFinish();\n            if (this._specialStyles) {\n                this._specialStyles.destroy();\n            }\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    setPosition(p) {\n        if (this.domPlayer === undefined) {\n            this.init();\n        }\n        this.domPlayer.currentTime = p * this.time;\n    }\n    getPosition() {\n        // tsc is complaining with TS2362 without the conversion to number\n        return +(this.domPlayer.currentTime ?? 0) / this.time;\n    }\n    get totalTime() {\n        return this._delay + this._duration;\n    }\n    beforeDestroy() {\n        const styles = new Map();\n        if (this.hasStarted()) {\n            // note: this code is invoked only when the `play` function was called prior to this\n            // (thus `hasStarted` returns true), this implies that the code that initializes\n            // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n            const finalKeyframe = this._finalKeyframe;\n            finalKeyframe.forEach((val, prop) => {\n                if (prop !== 'offset') {\n                    styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n                }\n            });\n        }\n        this.currentSnapshot = styles;\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\nclass WebAnimationsDriver {\n    validateStyleProperty(prop) {\n        // Perform actual validation in dev mode only, in prod mode this check is a noop.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            return validateStyleProperty(prop);\n        }\n        return true;\n    }\n    validateAnimatableStyleProperty(prop) {\n        // Perform actual validation in dev mode only, in prod mode this check is a noop.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const cssProp = camelCaseToDashCase(prop);\n            return validateWebAnimatableStyleProperty(cssProp);\n        }\n        return true;\n    }\n    containsElement(elm1, elm2) {\n        return containsElement(elm1, elm2);\n    }\n    getParentElement(element) {\n        return getParentElement(element);\n    }\n    query(element, selector, multi) {\n        return invokeQuery(element, selector, multi);\n    }\n    computeStyle(element, prop, defaultValue) {\n        return computeStyle(element, prop);\n    }\n    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n        const fill = delay == 0 ? 'both' : 'forwards';\n        const playerOptions = { duration, delay, fill };\n        // we check for this to avoid having a null|undefined value be present\n        // for the easing (which results in an error for certain browsers #9752)\n        if (easing) {\n            playerOptions['easing'] = easing;\n        }\n        const previousStyles = new Map();\n        const previousWebAnimationPlayers = (previousPlayers.filter((player) => player instanceof WebAnimationsPlayer));\n        if (allowPreviousPlayerStylesMerge(duration, delay)) {\n            previousWebAnimationPlayers.forEach((player) => {\n                player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n            });\n        }\n        let _keyframes = normalizeKeyframes$1(keyframes).map((styles) => new Map(styles));\n        _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n        const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n        return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n    }\n}\n\nfunction createEngine(type, doc) {\n    // TODO: find a way to make this tree shakable.\n    if (type === 'noop') {\n        return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n    }\n    return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\n\nclass Animation {\n    _driver;\n    _animationAst;\n    constructor(_driver, input) {\n        this._driver = _driver;\n        const errors = [];\n        const warnings = [];\n        const ast = buildAnimationAst(_driver, input, errors, warnings);\n        if (errors.length) {\n            throw validationFailed(errors);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (warnings.length) {\n                warnValidation(warnings);\n            }\n        }\n        this._animationAst = ast;\n    }\n    buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n        const start = Array.isArray(startingStyles)\n            ? normalizeStyles(startingStyles)\n            : startingStyles;\n        const dest = Array.isArray(destinationStyles)\n            ? normalizeStyles(destinationStyles)\n            : destinationStyles;\n        const errors = [];\n        subInstructions = subInstructions || new ElementInstructionMap();\n        const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n        if (errors.length) {\n            throw buildingFailed(errors);\n        }\n        return result;\n    }\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n    namespaceId;\n    delegate;\n    engine;\n    _onDestroy;\n    // We need to explicitly type this property because of an api-extractor bug\n    // See https://github.com/microsoft/rushstack/issues/4390\n    ɵtype = 0 /* AnimationRendererType.Regular */;\n    constructor(namespaceId, delegate, engine, _onDestroy) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this._onDestroy = _onDestroy;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroyNode(node) {\n        this.delegate.destroyNode?.(node);\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.engine.afterFlushAnimationsDone(() => {\n            // Call the renderer destroy method after the animations has finished as otherwise\n            // styles will be removed too early which will cause an unstyled animation.\n            queueMicrotask(() => {\n                this.delegate.destroy();\n            });\n        });\n        this._onDestroy?.();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n        // doesn't have a parent. There appears to be some animation-specific downstream logic that\n        // depends on the null check happening before the animation engine. This check keeps the old\n        // behavior while allowing `core` to not have to check for the parent element anymore.\n        if (this.parentNode(oldChild)) {\n            this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback, options) {\n        return this.delegate.listen(target, eventName, callback, options);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    factory;\n    constructor(factory, namespaceId, delegate, engine, onDestroy) {\n        super(namespaceId, delegate, engine, onDestroy);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.slice(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback, options) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.slice(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, (event) => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback, options);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.slice(dotIndex + 1);\n    return [trigger, phase];\n}\n\nclass AnimationRendererFactory {\n    delegate;\n    engine;\n    _zone;\n    _currentId = 0;\n    _microtaskId = 1;\n    _animationCallbacksBuffer = [];\n    _rendererCache = new Map();\n    _cdRecurDepth = 0;\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        engine.onRemovalComplete = (element, delegate) => {\n            delegate?.removeChild(null, element);\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type?.data?.['animation']) {\n            const cache = this._rendererCache;\n            let renderer = cache.get(delegate);\n            if (!renderer) {\n                // Ensure that the renderer is removed from the cache on destroy\n                // since it may contain references to detached DOM nodes.\n                const onRendererDestroy = () => cache.delete(delegate);\n                renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n                // only cache this result when the base renderer is used\n                cache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        queueMicrotask(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        const animationCallbacksBuffer = this._animationCallbacksBuffer;\n        if (animationCallbacksBuffer.length == 0) {\n            queueMicrotask(() => {\n                this._zone.run(() => {\n                    animationCallbacksBuffer.forEach((tuple) => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        // Flush the engine since the renderer destruction waits for animations to be done.\n        this.engine.flush();\n        this.delegate.componentReplaced?.(componentId);\n    }\n}\n\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, ENTER_CLASSNAME as ɵENTER_CLASSNAME, LEAVE_CLASSNAME as ɵLEAVE_CLASSNAME, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, TransitionAnimationPlayer as ɵTransitionAnimationPlayer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes$1 as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,qBAAqB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,kCAAkC,EAAEC,8BAA8B,EAAEC,oBAAoB,EAAEC,kCAAkC,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AAChvC,SAASC,mBAAmB,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,IAAIC,UAAU,EAAEC,oBAAoB,QAAQ,+BAA+B;;AAE7J;AACA;AACA;AACA;AACA;AAJA,IAKMC,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtB;AACJ;AACA;IACIpE,qBAAqBA,CAACqE,IAAI,EAAE;MACxB,OAAOrE,qBAAqB,CAACqE,IAAI,CAAC;IACtC;IACA;AACJ;AACA;AACA;IACIpE,eAAeA,CAACqE,IAAI,EAAEC,IAAI,EAAE;MACxB,OAAOtE,eAAe,CAACqE,IAAI,EAAEC,IAAI,CAAC;IACtC;IACA;AACJ;AACA;IACIrE,gBAAgBA,CAACsE,OAAO,EAAE;MACtB,OAAOtE,gBAAgB,CAACsE,OAAO,CAAC;IACpC;IACA;AACJ;AACA;AACA;IACIC,KAAKA,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,EAAE;MAC5B,OAAOxE,WAAW,CAACqE,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;IAChD;IACA;AACJ;AACA;IACIvB,YAAYA,CAACoB,OAAO,EAAEH,IAAI,EAAEO,YAAY,EAAE;MACtC,OAAOA,YAAY,IAAI,EAAE;IAC7B;IACA;AACJ;AACA;IACIC,OAAOA,CAACL,OAAO,EAAEM,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,GAAG,EAAE,EAAEC,uBAAuB,EAAE;MAChG,OAAO,IAAItB,mBAAmB,CAACkB,QAAQ,EAAEC,KAAK,CAAC;IACnD;IACA,OAAOI,IAAI,YAAAC,4BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFlB,mBAAmB;IAAA;IACtH,OAAOmB,KAAK,kBAD6EzF,EAAE,CAAA0F,kBAAA;MAAAC,KAAA,EACYrB,mBAAmB;MAAAsB,OAAA,EAAnBtB,mBAAmB,CAAAgB;IAAA;EAC9H;EAAC,OAzCKhB,mBAAmB;AAAA;AA0CzB;EAAA,QAAAuB,SAAA,oBAAAA,SAAA;AAAA;AAGA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;EACI,OAAOC,IAAI,gBAAG,IAAIzB,mBAAmB,CAAC,CAAC;AAC3C;AAEA,MAAM0B,wBAAwB,CAAC;AAE/B,MAAMC,4BAA4B,CAAC;EAC/BC,qBAAqBA,CAACC,YAAY,EAAEC,MAAM,EAAE;IACxC,OAAOD,YAAY;EACvB;EACAE,mBAAmBA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEJ,MAAM,EAAE;IACzE,OAAOI,KAAK;EAChB;AACJ;AAEA,MAAMC,oBAAoB,gBAAG,IAAIC,GAAG,CAAC,CACjC,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,UAAU,EACV,WAAW,EACX,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,cAAc,EACd,eAAe,EACf,YAAY,EACZ,aAAa,EACb,eAAe,EACf,cAAc,EACd,WAAW,EACX,YAAY,EACZ,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,mBAAmB,EACnB,YAAY,EACZ,aAAa,CAChB,CAAC;AACF,MAAMC,4BAA4B,SAASX,wBAAwB,CAAC;EAChEE,qBAAqBA,CAACC,YAAY,EAAEC,MAAM,EAAE;IACxC,OAAO9F,mBAAmB,CAAC6F,YAAY,CAAC;EAC5C;EACAE,mBAAmBA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEJ,MAAM,EAAE;IACzE,IAAIQ,IAAI,GAAG,EAAE;IACb,MAAMC,MAAM,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IACtC,IAAIN,oBAAoB,CAACO,GAAG,CAACT,kBAAkB,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,GAAG,EAAE;MAC9E,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BI,IAAI,GAAG,IAAI;MACf,CAAC,MACI;QACD,MAAMK,iBAAiB,GAAGT,KAAK,CAACU,KAAK,CAAC,wBAAwB,CAAC;QAC/D,IAAID,iBAAiB,IAAIA,iBAAiB,CAAC,CAAC,CAAC,CAACE,MAAM,IAAI,CAAC,EAAE;UACvDf,MAAM,CAACgB,IAAI,CAAC7G,mBAAmB,CAAC+F,oBAAoB,EAAEE,KAAK,CAAC,CAAC;QACjE;MACJ;IACJ;IACA,OAAOK,MAAM,GAAGD,IAAI;EACxB;AACJ;AAEA,SAASS,oBAAoBA,CAACC,QAAQ,EAAE;EACpC,MAAMC,UAAU,GAAG,OAAO;EAC1B,OAAO,GAAGA,UAAU,GAAGD,QAAQ,CAC1BE,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAAC,CACzBC,IAAI,CAACL,UAAU,CAAC,EAAE;AAC3B;AACA,SAASM,cAAcA,CAACP,QAAQ,EAAE;EAC9BQ,OAAO,CAACC,IAAI,CAAC,iCAAiCV,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AACnF;AACA,SAASU,gBAAgBA,CAACC,IAAI,EAAEX,QAAQ,EAAE;EACtCQ,OAAO,CAACC,IAAI,CAAC,0BAA0BE,IAAI,2CAA2CZ,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AAC3H;AACA,SAASY,YAAYA,CAACZ,QAAQ,EAAE;EAC5BQ,OAAO,CAACC,IAAI,CAAC,+CAA+CV,oBAAoB,CAACC,QAAQ,CAAC,EAAE,CAAC;AACjG;AACA,SAASa,iCAAiCA,CAACb,QAAQ,EAAEc,KAAK,EAAE;EACxD,IAAIA,KAAK,CAACjB,MAAM,EAAE;IACdG,QAAQ,CAACF,IAAI,CAAC,yDAAyDgB,KAAK,CAACR,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC9F;AACJ;AAEA,MAAMS,SAAS,GAAG,GAAG;AACrB,SAASC,mBAAmBA,CAACC,eAAe,EAAEnC,MAAM,EAAE;EAClD,MAAMoC,WAAW,GAAG,EAAE;EACtB,IAAI,OAAOD,eAAe,IAAI,QAAQ,EAAE;IACpCA,eAAe,CACVE,KAAK,CAAC,SAAS,CAAC,CAChBC,OAAO,CAAEC,GAAG,IAAKC,uBAAuB,CAACD,GAAG,EAAEH,WAAW,EAAEpC,MAAM,CAAC,CAAC;EAC5E,CAAC,MACI;IACDoC,WAAW,CAACpB,IAAI,CAACmB,eAAe,CAAC;EACrC;EACA,OAAOC,WAAW;AACtB;AACA,SAASI,uBAAuBA,CAACC,QAAQ,EAAEL,WAAW,EAAEpC,MAAM,EAAE;EAC5D,IAAIyC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;IACpB,MAAMC,MAAM,GAAGC,mBAAmB,CAACF,QAAQ,EAAEzC,MAAM,CAAC;IACpD,IAAI,OAAO0C,MAAM,IAAI,UAAU,EAAE;MAC7BN,WAAW,CAACpB,IAAI,CAAC0B,MAAM,CAAC;MACxB;IACJ;IACAD,QAAQ,GAAGC,MAAM;EACrB;EACA,MAAM5B,KAAK,GAAG2B,QAAQ,CAAC3B,KAAK,CAAC,yCAAyC,CAAC;EACvE,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACnCf,MAAM,CAACgB,IAAI,CAAC5G,iBAAiB,CAACqI,QAAQ,CAAC,CAAC;IACxC,OAAOL,WAAW;EACtB;EACA,MAAMQ,SAAS,GAAG9B,KAAK,CAAC,CAAC,CAAC;EAC1B,MAAM+B,SAAS,GAAG/B,KAAK,CAAC,CAAC,CAAC;EAC1B,MAAMgC,OAAO,GAAGhC,KAAK,CAAC,CAAC,CAAC;EACxBsB,WAAW,CAACpB,IAAI,CAAC+B,oBAAoB,CAACH,SAAS,EAAEE,OAAO,CAAC,CAAC;EAC1D,MAAME,kBAAkB,GAAGJ,SAAS,IAAIX,SAAS,IAAIa,OAAO,IAAIb,SAAS;EACzE,IAAIY,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAACG,kBAAkB,EAAE;IAC5CZ,WAAW,CAACpB,IAAI,CAAC+B,oBAAoB,CAACD,OAAO,EAAEF,SAAS,CAAC,CAAC;EAC9D;EACA;AACJ;AACA,SAASD,mBAAmBA,CAACM,KAAK,EAAEjD,MAAM,EAAE;EACxC,QAAQiD,KAAK;IACT,KAAK,QAAQ;MACT,OAAO,WAAW;IACtB,KAAK,QAAQ;MACT,OAAO,WAAW;IACtB,KAAK,YAAY;MACb,OAAO,CAACL,SAAS,EAAEE,OAAO,KAAKI,UAAU,CAACJ,OAAO,CAAC,GAAGI,UAAU,CAACN,SAAS,CAAC;IAC9E,KAAK,YAAY;MACb,OAAO,CAACA,SAAS,EAAEE,OAAO,KAAKI,UAAU,CAACJ,OAAO,CAAC,GAAGI,UAAU,CAACN,SAAS,CAAC;IAC9E;MACI5C,MAAM,CAACgB,IAAI,CAAC3G,sBAAsB,CAAC4I,KAAK,CAAC,CAAC;MAC1C,OAAO,QAAQ;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,mBAAmB,gBAAG,IAAI7C,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAClD,MAAM8C,oBAAoB,gBAAG,IAAI9C,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACpD,SAASyC,oBAAoBA,CAACM,GAAG,EAAEC,GAAG,EAAE;EACpC,MAAMC,iBAAiB,GAAGJ,mBAAmB,CAACvC,GAAG,CAACyC,GAAG,CAAC,IAAID,oBAAoB,CAACxC,GAAG,CAACyC,GAAG,CAAC;EACvF,MAAMG,iBAAiB,GAAGL,mBAAmB,CAACvC,GAAG,CAAC0C,GAAG,CAAC,IAAIF,oBAAoB,CAACxC,GAAG,CAAC0C,GAAG,CAAC;EACvF,OAAO,CAACV,SAAS,EAAEE,OAAO,KAAK;IAC3B,IAAIW,QAAQ,GAAGJ,GAAG,IAAIpB,SAAS,IAAIoB,GAAG,IAAIT,SAAS;IACnD,IAAIc,QAAQ,GAAGJ,GAAG,IAAIrB,SAAS,IAAIqB,GAAG,IAAIR,OAAO;IACjD,IAAI,CAACW,QAAQ,IAAIF,iBAAiB,IAAI,OAAOX,SAAS,KAAK,SAAS,EAAE;MAClEa,QAAQ,GAAGb,SAAS,GAAGO,mBAAmB,CAACvC,GAAG,CAACyC,GAAG,CAAC,GAAGD,oBAAoB,CAACxC,GAAG,CAACyC,GAAG,CAAC;IACvF;IACA,IAAI,CAACK,QAAQ,IAAIF,iBAAiB,IAAI,OAAOV,OAAO,KAAK,SAAS,EAAE;MAChEY,QAAQ,GAAGZ,OAAO,GAAGK,mBAAmB,CAACvC,GAAG,CAAC0C,GAAG,CAAC,GAAGF,oBAAoB,CAACxC,GAAG,CAAC0C,GAAG,CAAC;IACrF;IACA,OAAOG,QAAQ,IAAIC,QAAQ;EAC/B,CAAC;AACL;AAEA,MAAMC,UAAU,GAAG,OAAO;AAC1B,MAAMC,gBAAgB,GAAG,eAAgB,IAAIC,MAAM,CAAC,KAAKF,UAAU,MAAM,EAAE,GAAG,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEC,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,EAAE;EAC3D,OAAO,IAAI+C,0BAA0B,CAACF,MAAM,CAAC,CAACG,KAAK,CAACF,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;AACnF;AACA,MAAMiD,aAAa,GAAG,EAAE;AACxB,MAAMF,0BAA0B,CAAC;EAC7BG,OAAO;EACPC,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAF,KAAKA,CAACF,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,EAAE;IAC9B,MAAMoD,OAAO,GAAG,IAAIC,0BAA0B,CAACvE,MAAM,CAAC;IACtD,IAAI,CAACwE,6BAA6B,CAACF,OAAO,CAAC;IAC3C,MAAMG,GAAG,GAAInK,YAAY,CAAC,IAAI,EAAEkB,uBAAuB,CAACwI,QAAQ,CAAC,EAAEM,OAAO,CAAE;IAC5E,IAAI,OAAO7E,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI6E,OAAO,CAACI,6BAA6B,CAACC,IAAI,EAAE;QAC5C5C,iCAAiC,CAACb,QAAQ,EAAE,CACxC,GAAGoD,OAAO,CAACI,6BAA6B,CAACE,IAAI,CAAC,CAAC,CAClD,CAAC;MACN;IACJ;IACA,OAAOH,GAAG;EACd;EACAD,6BAA6BA,CAACF,OAAO,EAAE;IACnCA,OAAO,CAACO,oBAAoB,GAAGV,aAAa;IAC5CG,OAAO,CAACQ,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCT,OAAO,CAACQ,eAAe,CAACE,GAAG,CAACb,aAAa,EAAE,IAAIY,GAAG,CAAC,CAAC,CAAC;IACrDT,OAAO,CAACW,WAAW,GAAG,CAAC;EAC3B;EACAC,YAAYA,CAAClB,QAAQ,EAAEM,OAAO,EAAE;IAC5B,IAAIa,UAAU,GAAIb,OAAO,CAACa,UAAU,GAAG,CAAE;IACzC,IAAIC,QAAQ,GAAId,OAAO,CAACc,QAAQ,GAAG,CAAE;IACrC,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAItB,QAAQ,CAACnC,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAChCjB,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACzG,cAAc,CAAC,CAAC,CAAC;IACzC;IACAyJ,QAAQ,CAACwB,WAAW,CAAClD,OAAO,CAAEmD,GAAG,IAAK;MAClC,IAAI,CAACjB,6BAA6B,CAACF,OAAO,CAAC;MAC3C,IAAImB,GAAG,CAACC,IAAI,IAAI9H,qBAAqB,CAAC+H,KAAK,EAAE;QACzC,MAAMC,QAAQ,GAAGH,GAAG;QACpB,MAAM5D,IAAI,GAAG+D,QAAQ,CAAC/D,IAAI;QAC1BA,IAAI,CACCnB,QAAQ,CAAC,CAAC,CACV2B,KAAK,CAAC,SAAS,CAAC,CAChBC,OAAO,CAAEuD,CAAC,IAAK;UAChBD,QAAQ,CAAC/D,IAAI,GAAGgE,CAAC;UACjBR,MAAM,CAACrE,IAAI,CAAC,IAAI,CAAC8E,UAAU,CAACF,QAAQ,EAAEtB,OAAO,CAAC,CAAC;QACnD,CAAC,CAAC;QACFsB,QAAQ,CAAC/D,IAAI,GAAGA,IAAI;MACxB,CAAC,MACI,IAAI4D,GAAG,CAACC,IAAI,IAAI9H,qBAAqB,CAACmI,UAAU,EAAE;QACnD,MAAMC,UAAU,GAAG,IAAI,CAACC,eAAe,CAACR,GAAG,EAAEnB,OAAO,CAAC;QACrDa,UAAU,IAAIa,UAAU,CAACb,UAAU;QACnCC,QAAQ,IAAIY,UAAU,CAACZ,QAAQ;QAC/BE,WAAW,CAACtE,IAAI,CAACgF,UAAU,CAAC;MAChC,CAAC,MACI;QACD1B,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACxG,iBAAiB,CAAC,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;IACF,OAAO;MACHkL,IAAI,EAAE9H,qBAAqB,CAACsI,OAAO;MACnCrE,IAAI,EAAEmC,QAAQ,CAACnC,IAAI;MACnBwD,MAAM;MACNC,WAAW;MACXH,UAAU;MACVC,QAAQ;MACRe,OAAO,EAAE;IACb,CAAC;EACL;EACAL,UAAUA,CAAC9B,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAM8B,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACrC,QAAQ,CAACsC,MAAM,EAAEhC,OAAO,CAAC;IAC1D,MAAMiC,SAAS,GAAIvC,QAAQ,CAACmC,OAAO,IAAInC,QAAQ,CAACmC,OAAO,CAACK,MAAM,IAAK,IAAI;IACvE,IAAIJ,QAAQ,CAACK,qBAAqB,EAAE;MAChC,MAAMC,WAAW,GAAG,IAAIpG,GAAG,CAAC,CAAC;MAC7B,MAAMkG,MAAM,GAAGD,SAAS,IAAI,CAAC,CAAC;MAC9BH,QAAQ,CAACE,MAAM,CAAChE,OAAO,CAAEzE,KAAK,IAAK;QAC/B,IAAIA,KAAK,YAAYkH,GAAG,EAAE;UACtBlH,KAAK,CAACyE,OAAO,CAAElC,KAAK,IAAK;YACrB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAACkC,OAAO,CAAEqE,GAAG,IAAK;cACvC,IAAI,CAACH,MAAM,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;gBAC7BD,WAAW,CAACG,GAAG,CAACF,GAAG,CAAC;cACxB;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,IAAID,WAAW,CAAC/B,IAAI,EAAE;QAClBL,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACtG,YAAY,CAACsJ,QAAQ,CAACnC,IAAI,EAAE,CAAC,GAAG6E,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/E;IACJ;IACA,OAAO;MACHpB,IAAI,EAAE9H,qBAAqB,CAAC+H,KAAK;MACjC9D,IAAI,EAAEmC,QAAQ,CAACnC,IAAI;MACnBhE,KAAK,EAAEuI,QAAQ;MACfD,OAAO,EAAEI,SAAS,GAAG;QAAEC,MAAM,EAAED;MAAU,CAAC,GAAG;IACjD,CAAC;EACL;EACAN,eAAeA,CAACjC,QAAQ,EAAEM,OAAO,EAAE;IAC/BA,OAAO,CAACa,UAAU,GAAG,CAAC;IACtBb,OAAO,CAACc,QAAQ,GAAG,CAAC;IACpB,MAAM2B,SAAS,GAAGzM,YAAY,CAAC,IAAI,EAAEkB,uBAAuB,CAACwI,QAAQ,CAAC+C,SAAS,CAAC,EAAEzC,OAAO,CAAC;IAC1F,MAAM0C,QAAQ,GAAG9E,mBAAmB,CAAC8B,QAAQ,CAACiD,IAAI,EAAE3C,OAAO,CAACtE,MAAM,CAAC;IACnE,OAAO;MACH0F,IAAI,EAAE9H,qBAAqB,CAACmI,UAAU;MACtCiB,QAAQ;MACRD,SAAS;MACT5B,UAAU,EAAEb,OAAO,CAACa,UAAU;MAC9BC,QAAQ,EAAEd,OAAO,CAACc,QAAQ;MAC1Be,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACAgB,aAAaA,CAACnD,QAAQ,EAAEM,OAAO,EAAE;IAC7B,OAAO;MACHoB,IAAI,EAAE9H,qBAAqB,CAACwJ,QAAQ;MACpCC,KAAK,EAAErD,QAAQ,CAACqD,KAAK,CAAC/F,GAAG,CAAEgG,CAAC,IAAKhN,YAAY,CAAC,IAAI,EAAEgN,CAAC,EAAEhD,OAAO,CAAC,CAAC;MAChE6B,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACAoB,UAAUA,CAACvD,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAMW,WAAW,GAAGX,OAAO,CAACW,WAAW;IACvC,IAAIuC,YAAY,GAAG,CAAC;IACpB,MAAMH,KAAK,GAAGrD,QAAQ,CAACqD,KAAK,CAAC/F,GAAG,CAAEmG,IAAI,IAAK;MACvCnD,OAAO,CAACW,WAAW,GAAGA,WAAW;MACjC,MAAMyC,QAAQ,GAAGpN,YAAY,CAAC,IAAI,EAAEmN,IAAI,EAAEnD,OAAO,CAAC;MAClDkD,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAElD,OAAO,CAACW,WAAW,CAAC;MAC1D,OAAOyC,QAAQ;IACnB,CAAC,CAAC;IACFpD,OAAO,CAACW,WAAW,GAAGuC,YAAY;IAClC,OAAO;MACH9B,IAAI,EAAE9H,qBAAqB,CAACiK,KAAK;MACjCR,KAAK;MACLlB,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACA2B,YAAYA,CAAC9D,QAAQ,EAAEM,OAAO,EAAE;IAC5B,MAAMyD,SAAS,GAAGC,kBAAkB,CAAChE,QAAQ,CAACiE,OAAO,EAAE3D,OAAO,CAACtE,MAAM,CAAC;IACtEsE,OAAO,CAAC4D,qBAAqB,GAAGH,SAAS;IACzC,IAAI3B,QAAQ;IACZ,IAAI+B,aAAa,GAAGnE,QAAQ,CAACsC,MAAM,GAC7BtC,QAAQ,CAACsC,MAAM,GACfzI,KAAK,CAAC,CAAC,CAAC,CAAC;IACf,IAAIsK,aAAa,CAACzC,IAAI,IAAI9H,qBAAqB,CAACwK,SAAS,EAAE;MACvDhC,QAAQ,GAAG,IAAI,CAACiC,cAAc,CAACF,aAAa,EAAE7D,OAAO,CAAC;IAC1D,CAAC,MACI;MACD,IAAI6D,aAAa,GAAGnE,QAAQ,CAACsC,MAAM;MACnC,IAAIgC,OAAO,GAAG,KAAK;MACnB,IAAI,CAACH,aAAa,EAAE;QAChBG,OAAO,GAAG,IAAI;QACd,MAAMC,YAAY,GAAG,CAAC,CAAC;QACvB,IAAIR,SAAS,CAAChJ,MAAM,EAAE;UAClBwJ,YAAY,CAAC,QAAQ,CAAC,GAAGR,SAAS,CAAChJ,MAAM;QAC7C;QACAoJ,aAAa,GAAGtK,KAAK,CAAC0K,YAAY,CAAC;MACvC;MACAjE,OAAO,CAACW,WAAW,IAAI8C,SAAS,CAAClJ,QAAQ,GAAGkJ,SAAS,CAACjJ,KAAK;MAC3D,MAAM0J,SAAS,GAAG,IAAI,CAACnC,UAAU,CAAC8B,aAAa,EAAE7D,OAAO,CAAC;MACzDkE,SAAS,CAACC,WAAW,GAAGH,OAAO;MAC/BlC,QAAQ,GAAGoC,SAAS;IACxB;IACAlE,OAAO,CAAC4D,qBAAqB,GAAG,IAAI;IACpC,OAAO;MACHxC,IAAI,EAAE9H,qBAAqB,CAAC8K,OAAO;MACnCT,OAAO,EAAEF,SAAS;MAClBlK,KAAK,EAAEuI,QAAQ;MACfD,OAAO,EAAE;IACb,CAAC;EACL;EACAE,UAAUA,CAACrC,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAMG,GAAG,GAAG,IAAI,CAACkE,aAAa,CAAC3E,QAAQ,EAAEM,OAAO,CAAC;IACjD,IAAI,CAACsE,iBAAiB,CAACnE,GAAG,EAAEH,OAAO,CAAC;IACpC,OAAOG,GAAG;EACd;EACAkE,aAAaA,CAAC3E,QAAQ,EAAEM,OAAO,EAAE;IAC7B,MAAMgC,MAAM,GAAG,EAAE;IACjB,MAAMuC,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAC/E,QAAQ,CAACsC,MAAM,CAAC,GAAGtC,QAAQ,CAACsC,MAAM,GAAG,CAACtC,QAAQ,CAACsC,MAAM,CAAC;IAC3F,KAAK,IAAI0C,UAAU,IAAIH,cAAc,EAAE;MACnC,IAAI,OAAOG,UAAU,KAAK,QAAQ,EAAE;QAChC,IAAIA,UAAU,KAAKlL,UAAU,EAAE;UAC3BwI,MAAM,CAACtF,IAAI,CAACgI,UAAU,CAAC;QAC3B,CAAC,MACI;UACD1E,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACrG,iBAAiB,CAACqO,UAAU,CAAC,CAAC;QACtD;MACJ,CAAC,MACI;QACD1C,MAAM,CAACtF,IAAI,CAAC,IAAI+D,GAAG,CAACkE,MAAM,CAACC,OAAO,CAACF,UAAU,CAAC,CAAC,CAAC;MACpD;IACJ;IACA,IAAIvC,qBAAqB,GAAG,KAAK;IACjC,IAAI0C,eAAe,GAAG,IAAI;IAC1B7C,MAAM,CAAChE,OAAO,CAAE8G,SAAS,IAAK;MAC1B,IAAIA,SAAS,YAAYrE,GAAG,EAAE;QAC1B,IAAIqE,SAAS,CAACxI,GAAG,CAAC,QAAQ,CAAC,EAAE;UACzBuI,eAAe,GAAGC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UACzCD,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;QAC9B;QACA,IAAI,CAAC7C,qBAAqB,EAAE;UACxB,KAAK,IAAIrG,KAAK,IAAIgJ,SAAS,CAACtC,MAAM,CAAC,CAAC,EAAE;YAClC,IAAI1G,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC6I,OAAO,CAAC3O,uBAAuB,CAAC,IAAI,CAAC,EAAE;cACxD6L,qBAAqB,GAAG,IAAI;cAC5B;YACJ;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,OAAO;MACHf,IAAI,EAAE9H,qBAAqB,CAAC4L,KAAK;MACjClD,MAAM;MACNvH,MAAM,EAAEoK,eAAe;MACvBM,MAAM,EAAEzF,QAAQ,CAACyF,MAAM;MACvBhD,qBAAqB;MACrBN,OAAO,EAAE;IACb,CAAC;EACL;EACAyC,iBAAiBA,CAACnE,GAAG,EAAEH,OAAO,EAAE;IAC5B,MAAM2D,OAAO,GAAG3D,OAAO,CAAC4D,qBAAqB;IAC7C,IAAIwB,OAAO,GAAGpF,OAAO,CAACW,WAAW;IACjC,IAAI0E,SAAS,GAAGrF,OAAO,CAACW,WAAW;IACnC,IAAIgD,OAAO,IAAI0B,SAAS,GAAG,CAAC,EAAE;MAC1BA,SAAS,IAAI1B,OAAO,CAACpJ,QAAQ,GAAGoJ,OAAO,CAACnJ,KAAK;IACjD;IACA2F,GAAG,CAAC6B,MAAM,CAAChE,OAAO,CAAEsH,KAAK,IAAK;MAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACzB;MACJA,KAAK,CAACtH,OAAO,CAAC,CAAClC,KAAK,EAAEjC,IAAI,KAAK;QAC3B,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAAC,IAAI,CAAC2E,OAAO,CAACtK,qBAAqB,CAACqE,IAAI,CAAC,EAAE;YAC3CyL,KAAK,CAACN,MAAM,CAACnL,IAAI,CAAC;YAClBmG,OAAO,CAACI,6BAA6B,CAACmC,GAAG,CAAC1I,IAAI,CAAC;YAC/C;UACJ;QACJ;QACA;QACA;QACA,MAAM2G,eAAe,GAAGR,OAAO,CAACQ,eAAe,CAACuE,GAAG,CAAC/E,OAAO,CAACO,oBAAoB,CAAC;QACjF,MAAMgF,cAAc,GAAG/E,eAAe,CAACuE,GAAG,CAAClL,IAAI,CAAC;QAChD,IAAI2L,oBAAoB,GAAG,IAAI;QAC/B,IAAID,cAAc,EAAE;UAChB,IAAIF,SAAS,IAAID,OAAO,IACpBC,SAAS,IAAIE,cAAc,CAACF,SAAS,IACrCD,OAAO,IAAIG,cAAc,CAACH,OAAO,EAAE;YACnCpF,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACnG,wBAAwB,CAACsD,IAAI,EAAE0L,cAAc,CAACF,SAAS,EAAEE,cAAc,CAACH,OAAO,EAAEC,SAAS,EAAED,OAAO,CAAC,CAAC;YACzHI,oBAAoB,GAAG,KAAK;UAChC;UACA;UACA;UACA;UACAH,SAAS,GAAGE,cAAc,CAACF,SAAS;QACxC;QACA,IAAIG,oBAAoB,EAAE;UACtBhF,eAAe,CAACE,GAAG,CAAC7G,IAAI,EAAE;YAAEwL,SAAS;YAAED;UAAQ,CAAC,CAAC;QACrD;QACA,IAAIpF,OAAO,CAAC6B,OAAO,EAAE;UACjBrL,mBAAmB,CAACsF,KAAK,EAAEkE,OAAO,CAAC6B,OAAO,EAAE7B,OAAO,CAACtE,MAAM,CAAC;QAC/D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAqI,cAAcA,CAACrE,QAAQ,EAAEM,OAAO,EAAE;IAC9B,MAAMG,GAAG,GAAG;MAAEiB,IAAI,EAAE9H,qBAAqB,CAACwK,SAAS;MAAE9B,MAAM,EAAE,EAAE;MAAEH,OAAO,EAAE;IAAK,CAAC;IAChF,IAAI,CAAC7B,OAAO,CAAC4D,qBAAqB,EAAE;MAChC5D,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAACjG,gBAAgB,CAAC,CAAC,CAAC;MACvC,OAAO0J,GAAG;IACd;IACA,MAAMsF,mBAAmB,GAAG,CAAC;IAC7B,IAAIC,yBAAyB,GAAG,CAAC;IACjC,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,cAAc,GAAG,CAAC;IACtB,MAAMxL,SAAS,GAAGoF,QAAQ,CAACqD,KAAK,CAAC/F,GAAG,CAAEgF,MAAM,IAAK;MAC7C,MAAMzI,KAAK,GAAG,IAAI,CAAC8K,aAAa,CAACrC,MAAM,EAAEhC,OAAO,CAAC;MACjD,IAAI+F,SAAS,GAAGxM,KAAK,CAAC4L,MAAM,IAAI,IAAI,GAAG5L,KAAK,CAAC4L,MAAM,GAAGa,aAAa,CAACzM,KAAK,CAACyI,MAAM,CAAC;MACjF,IAAImD,MAAM,GAAG,CAAC;MACd,IAAIY,SAAS,IAAI,IAAI,EAAE;QACnBL,yBAAyB,EAAE;QAC3BP,MAAM,GAAG5L,KAAK,CAAC4L,MAAM,GAAGY,SAAS;MACrC;MACAF,mBAAmB,GAAGA,mBAAmB,IAAIV,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,CAAC;MACrES,iBAAiB,GAAGA,iBAAiB,IAAIT,MAAM,GAAGW,cAAc;MAChEA,cAAc,GAAGX,MAAM;MACvBQ,OAAO,CAACjJ,IAAI,CAACyI,MAAM,CAAC;MACpB,OAAO5L,KAAK;IAChB,CAAC,CAAC;IACF,IAAIsM,mBAAmB,EAAE;MACrB7F,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAChG,aAAa,CAAC,CAAC,CAAC;IACxC;IACA,IAAIkP,iBAAiB,EAAE;MACnB5F,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC/F,yBAAyB,CAAC,CAAC,CAAC;IACpD;IACA,MAAM8F,MAAM,GAAGiD,QAAQ,CAACqD,KAAK,CAACtG,MAAM;IACpC,IAAIwJ,eAAe,GAAG,CAAC;IACvB,IAAIP,yBAAyB,GAAG,CAAC,IAAIA,yBAAyB,GAAGjJ,MAAM,EAAE;MACrEuD,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC9F,uBAAuB,CAAC,CAAC,CAAC;IAClD,CAAC,MACI,IAAI8O,yBAAyB,IAAI,CAAC,EAAE;MACrCO,eAAe,GAAGR,mBAAmB,IAAIhJ,MAAM,GAAG,CAAC,CAAC;IACxD;IACA,MAAMyJ,KAAK,GAAGzJ,MAAM,GAAG,CAAC;IACxB,MAAMkE,WAAW,GAAGX,OAAO,CAACW,WAAW;IACvC,MAAMiD,qBAAqB,GAAG5D,OAAO,CAAC4D,qBAAqB;IAC3D,MAAMuC,eAAe,GAAGvC,qBAAqB,CAACrJ,QAAQ;IACtDD,SAAS,CAAC0D,OAAO,CAAC,CAACoI,EAAE,EAAEC,CAAC,KAAK;MACzB,MAAMlB,MAAM,GAAGc,eAAe,GAAG,CAAC,GAAII,CAAC,IAAIH,KAAK,GAAG,CAAC,GAAGD,eAAe,GAAGI,CAAC,GAAIV,OAAO,CAACU,CAAC,CAAC;MACxF,MAAMC,qBAAqB,GAAGnB,MAAM,GAAGgB,eAAe;MACtDnG,OAAO,CAACW,WAAW,GAAGA,WAAW,GAAGiD,qBAAqB,CAACpJ,KAAK,GAAG8L,qBAAqB;MACvF1C,qBAAqB,CAACrJ,QAAQ,GAAG+L,qBAAqB;MACtD,IAAI,CAAChC,iBAAiB,CAAC8B,EAAE,EAAEpG,OAAO,CAAC;MACnCoG,EAAE,CAACjB,MAAM,GAAGA,MAAM;MAClBhF,GAAG,CAAC6B,MAAM,CAACtF,IAAI,CAAC0J,EAAE,CAAC;IACvB,CAAC,CAAC;IACF,OAAOjG,GAAG;EACd;EACAoG,cAAcA,CAAC7G,QAAQ,EAAEM,OAAO,EAAE;IAC9B,OAAO;MACHoB,IAAI,EAAE9H,qBAAqB,CAACkN,SAAS;MACrC/D,SAAS,EAAEzM,YAAY,CAAC,IAAI,EAAEkB,uBAAuB,CAACwI,QAAQ,CAAC+C,SAAS,CAAC,EAAEzC,OAAO,CAAC;MACnF6B,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACA4E,iBAAiBA,CAAC/G,QAAQ,EAAEM,OAAO,EAAE;IACjCA,OAAO,CAACc,QAAQ,EAAE;IAClB,OAAO;MACHM,IAAI,EAAE9H,qBAAqB,CAACoN,YAAY;MACxC7E,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACA8E,eAAeA,CAACjH,QAAQ,EAAEM,OAAO,EAAE;IAC/B,OAAO;MACHoB,IAAI,EAAE9H,qBAAqB,CAACsN,UAAU;MACtCnE,SAAS,EAAE,IAAI,CAAC8D,cAAc,CAAC7G,QAAQ,CAAC+C,SAAS,EAAEzC,OAAO,CAAC;MAC3D6B,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACAgF,UAAUA,CAACnH,QAAQ,EAAEM,OAAO,EAAE;IAC1B,MAAM8G,cAAc,GAAG9G,OAAO,CAACO,oBAAoB;IACnD,MAAMsB,OAAO,GAAInC,QAAQ,CAACmC,OAAO,IAAI,CAAC,CAAE;IACxC7B,OAAO,CAACa,UAAU,EAAE;IACpBb,OAAO,CAAC+G,YAAY,GAAGrH,QAAQ;IAC/B,MAAM,CAACxF,QAAQ,EAAE8M,WAAW,CAAC,GAAGC,iBAAiB,CAACvH,QAAQ,CAACxF,QAAQ,CAAC;IACpE8F,OAAO,CAACO,oBAAoB,GAAGuG,cAAc,CAACrK,MAAM,GAC9CqK,cAAc,GAAG,GAAG,GAAG5M,QAAQ,GAC/BA,QAAQ;IACdrD,oBAAoB,CAACmJ,OAAO,CAACQ,eAAe,EAAER,OAAO,CAACO,oBAAoB,EAAE,IAAIE,GAAG,CAAC,CAAC,CAAC;IACtF,MAAMgC,SAAS,GAAGzM,YAAY,CAAC,IAAI,EAAEkB,uBAAuB,CAACwI,QAAQ,CAAC+C,SAAS,CAAC,EAAEzC,OAAO,CAAC;IAC1FA,OAAO,CAAC+G,YAAY,GAAG,IAAI;IAC3B/G,OAAO,CAACO,oBAAoB,GAAGuG,cAAc;IAC7C,OAAO;MACH1F,IAAI,EAAE9H,qBAAqB,CAAC4N,KAAK;MACjChN,QAAQ;MACRgM,KAAK,EAAErE,OAAO,CAACqE,KAAK,IAAI,CAAC;MACzBiB,QAAQ,EAAE,CAAC,CAACtF,OAAO,CAACsF,QAAQ;MAC5BH,WAAW;MACXvE,SAAS;MACT2E,gBAAgB,EAAE1H,QAAQ,CAACxF,QAAQ;MACnC2H,OAAO,EAAEe,yBAAyB,CAAClD,QAAQ,CAACmC,OAAO;IACvD,CAAC;EACL;EACAwF,YAAYA,CAAC3H,QAAQ,EAAEM,OAAO,EAAE;IAC5B,IAAI,CAACA,OAAO,CAAC+G,YAAY,EAAE;MACvB/G,OAAO,CAACtE,MAAM,CAACgB,IAAI,CAAC5F,cAAc,CAAC,CAAC,CAAC;IACzC;IACA,MAAM6M,OAAO,GAAGjE,QAAQ,CAACiE,OAAO,KAAK,MAAM,GACrC;MAAEpJ,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAO,CAAC,GACzC1D,aAAa,CAAC2I,QAAQ,CAACiE,OAAO,EAAE3D,OAAO,CAACtE,MAAM,EAAE,IAAI,CAAC;IAC3D,OAAO;MACH0F,IAAI,EAAE9H,qBAAqB,CAACgO,OAAO;MACnC7E,SAAS,EAAEzM,YAAY,CAAC,IAAI,EAAEkB,uBAAuB,CAACwI,QAAQ,CAAC+C,SAAS,CAAC,EAAEzC,OAAO,CAAC;MACnF2D,OAAO;MACP9B,OAAO,EAAE;IACb,CAAC;EACL;AACJ;AACA,SAASoF,iBAAiBA,CAAC/M,QAAQ,EAAE;EACjC,MAAMqN,YAAY,GAAGrN,QAAQ,CAAC6D,KAAK,CAAC,SAAS,CAAC,CAACyJ,IAAI,CAAEvM,KAAK,IAAKA,KAAK,IAAIoE,UAAU,CAAC,GAC7E,IAAI,GACJ,KAAK;EACX,IAAIkI,YAAY,EAAE;IACdrN,QAAQ,GAAGA,QAAQ,CAACuN,OAAO,CAACnI,gBAAgB,EAAE,EAAE,CAAC;EACrD;EACA;EACA;EACApF,QAAQ,GAAGA,QAAQ,CACduN,OAAO,CAAC,MAAM,EAAEzQ,mBAAmB,CAAC,CACpCyQ,OAAO,CAAC,OAAO,EAAGjL,KAAK,IAAKxF,mBAAmB,GAAG,GAAG,GAAGwF,KAAK,CAACkL,KAAK,CAAC,CAAC,CAAC,CAAC,CACvED,OAAO,CAAC,aAAa,EAAExQ,qBAAqB,CAAC;EAClD,OAAO,CAACiD,QAAQ,EAAEqN,YAAY,CAAC;AACnC;AACA,SAASI,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,GAAG;IAAE,GAAGA;EAAI,CAAC,GAAG,IAAI;AAClC;AACA,MAAM3H,0BAA0B,CAAC;EAC7BvE,MAAM;EACNmF,UAAU,GAAG,CAAC;EACdC,QAAQ,GAAG,CAAC;EACZ+G,iBAAiB,GAAG,IAAI;EACxBd,YAAY,GAAG,IAAI;EACnBxG,oBAAoB,GAAG,IAAI;EAC3BqD,qBAAqB,GAAG,IAAI;EAC5BjD,WAAW,GAAG,CAAC;EACfH,eAAe,gBAAG,IAAIC,GAAG,CAAC,CAAC;EAC3BoB,OAAO,GAAG,IAAI;EACdzB,6BAA6B,gBAAG,IAAIpE,GAAG,CAAC,CAAC;EACzC+D,WAAWA,CAACrE,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,SAASsK,aAAaA,CAAChE,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,IAAI,QAAQ,EACzB,OAAO,IAAI;EACf,IAAImD,MAAM,GAAG,IAAI;EACjB,IAAIX,KAAK,CAACC,OAAO,CAACzC,MAAM,CAAC,EAAE;IACvBA,MAAM,CAAChE,OAAO,CAAE0G,UAAU,IAAK;MAC3B,IAAIA,UAAU,YAAYjE,GAAG,IAAIiE,UAAU,CAACpI,GAAG,CAAC,QAAQ,CAAC,EAAE;QACvD,MAAMsL,GAAG,GAAGlD,UAAU;QACtBS,MAAM,GAAGvG,UAAU,CAACgJ,GAAG,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC6C,GAAG,CAAC5C,MAAM,CAAC,QAAQ,CAAC;MACxB;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAIhD,MAAM,YAAYvB,GAAG,IAAIuB,MAAM,CAAC1F,GAAG,CAAC,QAAQ,CAAC,EAAE;IACpD,MAAMsL,GAAG,GAAG5F,MAAM;IAClBmD,MAAM,GAAGvG,UAAU,CAACgJ,GAAG,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC6C,GAAG,CAAC5C,MAAM,CAAC,QAAQ,CAAC;EACxB;EACA,OAAOG,MAAM;AACjB;AACA,SAASzB,kBAAkBA,CAAC5H,KAAK,EAAEJ,MAAM,EAAE;EACvC,IAAII,KAAK,CAACwG,cAAc,CAAC,UAAU,CAAC,EAAE;IAClC,OAAOxG,KAAK;EAChB;EACA,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC1B,MAAMvB,QAAQ,GAAGxD,aAAa,CAAC+E,KAAK,EAAEJ,MAAM,CAAC,CAACnB,QAAQ;IACtD,OAAOuN,aAAa,CAACvN,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;EACzC;EACA,MAAMwN,QAAQ,GAAGjM,KAAK;EACtB,MAAMkM,SAAS,GAAGD,QAAQ,CAAChK,KAAK,CAAC,KAAK,CAAC,CAACkK,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjH,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIiH,CAAC,CAACjH,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;EAC7F,IAAI+G,SAAS,EAAE;IACX,MAAM7H,GAAG,GAAG2H,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnC3H,GAAG,CAACgI,OAAO,GAAG,IAAI;IAClBhI,GAAG,CAAC4H,QAAQ,GAAGA,QAAQ;IACvB,OAAO5H,GAAG;EACd;EACA,MAAMwD,OAAO,GAAG5M,aAAa,CAACgR,QAAQ,EAAErM,MAAM,CAAC;EAC/C,OAAOoM,aAAa,CAACnE,OAAO,CAACpJ,QAAQ,EAAEoJ,OAAO,CAACnJ,KAAK,EAAEmJ,OAAO,CAAClJ,MAAM,CAAC;AACzE;AACA,SAASmI,yBAAyBA,CAACf,OAAO,EAAE;EACxC,IAAIA,OAAO,EAAE;IACTA,OAAO,GAAG;MAAE,GAAGA;IAAQ,CAAC;IACxB,IAAIA,OAAO,CAAC,QAAQ,CAAC,EAAE;MACnBA,OAAO,CAAC,QAAQ,CAAC,GAAG8F,eAAe,CAAC9F,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1D;EACJ,CAAC,MACI;IACDA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,OAAOA,OAAO;AAClB;AACA,SAASiG,aAAaA,CAACvN,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC5C,OAAO;IAAEF,QAAQ;IAAEC,KAAK;IAAEC;EAAO,CAAC;AACtC;AAEA,SAAS2N,yBAAyBA,CAACpO,OAAO,EAAEM,SAAS,EAAE+N,aAAa,EAAEC,cAAc,EAAE/N,QAAQ,EAAEC,KAAK,EAAEC,MAAM,GAAG,IAAI,EAAE8N,WAAW,GAAG,KAAK,EAAE;EACvI,OAAO;IACHnH,IAAI,EAAE,CAAC,CAAC;IACRpH,OAAO;IACPM,SAAS;IACT+N,aAAa;IACbC,cAAc;IACd/N,QAAQ;IACRC,KAAK;IACLgO,SAAS,EAAEjO,QAAQ,GAAGC,KAAK;IAC3BC,MAAM;IACN8N;EACJ,CAAC;AACL;AAEA,MAAME,qBAAqB,CAAC;EACxBC,IAAI,gBAAG,IAAIjI,GAAG,CAAC,CAAC;EAChBsE,GAAGA,CAAC/K,OAAO,EAAE;IACT,OAAO,IAAI,CAAC0O,IAAI,CAAC3D,GAAG,CAAC/K,OAAO,CAAC,IAAI,EAAE;EACvC;EACA2O,MAAMA,CAAC3O,OAAO,EAAE4O,YAAY,EAAE;IAC1B,IAAIC,oBAAoB,GAAG,IAAI,CAACH,IAAI,CAAC3D,GAAG,CAAC/K,OAAO,CAAC;IACjD,IAAI,CAAC6O,oBAAoB,EAAE;MACvB,IAAI,CAACH,IAAI,CAAChI,GAAG,CAAC1G,OAAO,EAAG6O,oBAAoB,GAAG,EAAG,CAAC;IACvD;IACAA,oBAAoB,CAACnM,IAAI,CAAC,GAAGkM,YAAY,CAAC;EAC9C;EACAtM,GAAGA,CAACtC,OAAO,EAAE;IACT,OAAO,IAAI,CAAC0O,IAAI,CAACpM,GAAG,CAACtC,OAAO,CAAC;EACjC;EACA8O,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACJ,IAAI,CAACI,KAAK,CAAC,CAAC;EACrB;AACJ;AAEA,MAAMC,yBAAyB,GAAG,CAAC;AACnC,MAAMC,WAAW,GAAG,QAAQ;AAC5B,MAAMC,iBAAiB,GAAG,eAAgB,IAAI1J,MAAM,CAACyJ,WAAW,EAAE,GAAG,CAAC;AACtE,MAAME,WAAW,GAAG,QAAQ;AAC5B,MAAMC,iBAAiB,GAAG,eAAgB,IAAI5J,MAAM,CAAC2J,WAAW,EAAE,GAAG,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAC3J,MAAM,EAAE4J,WAAW,EAAElJ,GAAG,EAAEmJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,GAAG,IAAI/I,GAAG,CAAC,CAAC,EAAEgJ,WAAW,GAAG,IAAIhJ,GAAG,CAAC,CAAC,EAAEoB,OAAO,EAAE6H,eAAe,EAAEhO,MAAM,GAAG,EAAE,EAAE;EACnL,OAAO,IAAIiO,+BAA+B,CAAC,CAAC,CAACC,cAAc,CAACnK,MAAM,EAAE4J,WAAW,EAAElJ,GAAG,EAAEmJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE5H,OAAO,EAAE6H,eAAe,EAAEhO,MAAM,CAAC;AACxL;AACA,MAAMiO,+BAA+B,CAAC;EAClCC,cAAcA,CAACnK,MAAM,EAAE4J,WAAW,EAAElJ,GAAG,EAAEmJ,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE5H,OAAO,EAAE6H,eAAe,EAAEhO,MAAM,GAAG,EAAE,EAAE;IACzIgO,eAAe,GAAGA,eAAe,IAAI,IAAIjB,qBAAqB,CAAC,CAAC;IAChE,MAAMzI,OAAO,GAAG,IAAI6J,wBAAwB,CAACpK,MAAM,EAAE4J,WAAW,EAAEK,eAAe,EAAEJ,cAAc,EAAEC,cAAc,EAAE7N,MAAM,EAAE,EAAE,CAAC;IAC9HsE,OAAO,CAAC6B,OAAO,GAAGA,OAAO;IACzB,MAAMrH,KAAK,GAAGqH,OAAO,CAACrH,KAAK,GAAGrD,kBAAkB,CAAC0K,OAAO,CAACrH,KAAK,CAAC,GAAG,CAAC;IACnEwF,OAAO,CAAC8J,eAAe,CAACC,aAAa,CAACvP,KAAK,CAAC;IAC5CwF,OAAO,CAAC8J,eAAe,CAAC5R,SAAS,CAAC,CAACsR,cAAc,CAAC,EAAE,IAAI,EAAExJ,OAAO,CAACtE,MAAM,EAAEmG,OAAO,CAAC;IAClF7L,YAAY,CAAC,IAAI,EAAEmK,GAAG,EAAEH,OAAO,CAAC;IAChC;IACA,MAAMgK,SAAS,GAAGhK,OAAO,CAACgK,SAAS,CAAClN,MAAM,CAAEmN,QAAQ,IAAKA,QAAQ,CAACC,iBAAiB,CAAC,CAAC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAIF,SAAS,CAACvN,MAAM,IAAIgN,WAAW,CAACpJ,IAAI,EAAE;MACtC,IAAI8J,gBAAgB;MACpB,KAAK,IAAI9D,CAAC,GAAG2D,SAAS,CAACvN,MAAM,GAAG,CAAC,EAAE4J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,MAAM4D,QAAQ,GAAGD,SAAS,CAAC3D,CAAC,CAAC;QAC7B,IAAI4D,QAAQ,CAACjQ,OAAO,KAAKqP,WAAW,EAAE;UAClCc,gBAAgB,GAAGF,QAAQ;UAC3B;QACJ;MACJ;MACA,IAAIE,gBAAgB,IAAI,CAACA,gBAAgB,CAACC,uBAAuB,CAAC,CAAC,EAAE;QACjED,gBAAgB,CAACjS,SAAS,CAAC,CAACuR,WAAW,CAAC,EAAE,IAAI,EAAEzJ,OAAO,CAACtE,MAAM,EAAEmG,OAAO,CAAC;MAC5E;IACJ;IACA,OAAOmI,SAAS,CAACvN,MAAM,GACjBuN,SAAS,CAAChN,GAAG,CAAEiN,QAAQ,IAAKA,QAAQ,CAACL,cAAc,CAAC,CAAC,CAAC,GACtD,CAACxB,yBAAyB,CAACiB,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE7O,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;EACnF;EACAoG,YAAYA,CAACT,GAAG,EAAEH,OAAO,EAAE;IACvB;EAAA;EAEJwB,UAAUA,CAACrB,GAAG,EAAEH,OAAO,EAAE;IACrB;EAAA;EAEJ2B,eAAeA,CAACxB,GAAG,EAAEH,OAAO,EAAE;IAC1B;EAAA;EAEJyG,iBAAiBA,CAACtG,GAAG,EAAEH,OAAO,EAAE;IAC5B,MAAMqK,mBAAmB,GAAGrK,OAAO,CAAC0J,eAAe,CAAC3E,GAAG,CAAC/E,OAAO,CAAChG,OAAO,CAAC;IACxE,IAAIqQ,mBAAmB,EAAE;MACrB,MAAMC,YAAY,GAAGtK,OAAO,CAACuK,gBAAgB,CAACpK,GAAG,CAAC0B,OAAO,CAAC;MAC1D,MAAMwD,SAAS,GAAGrF,OAAO,CAAC8J,eAAe,CAACnJ,WAAW;MACrD,MAAMyE,OAAO,GAAG,IAAI,CAACoF,qBAAqB,CAACH,mBAAmB,EAAEC,YAAY,EAAEA,YAAY,CAACzI,OAAO,CAAC;MACnG,IAAIwD,SAAS,IAAID,OAAO,EAAE;QACtB;QACA;QACApF,OAAO,CAACyK,wBAAwB,CAACrF,OAAO,CAAC;MAC7C;IACJ;IACApF,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACAwG,eAAeA,CAACxG,GAAG,EAAEH,OAAO,EAAE;IAC1B,MAAMsK,YAAY,GAAGtK,OAAO,CAACuK,gBAAgB,CAACpK,GAAG,CAAC0B,OAAO,CAAC;IAC1DyI,YAAY,CAACG,wBAAwB,CAAC,CAAC;IACvC,IAAI,CAACE,wBAAwB,CAAC,CAACxK,GAAG,CAAC0B,OAAO,EAAE1B,GAAG,CAACsC,SAAS,CAACZ,OAAO,CAAC,EAAE7B,OAAO,EAAEsK,YAAY,CAAC;IAC1F,IAAI,CAAC/D,cAAc,CAACpG,GAAG,CAACsC,SAAS,EAAE6H,YAAY,CAAC;IAChDtK,OAAO,CAACyK,wBAAwB,CAACH,YAAY,CAACR,eAAe,CAACnJ,WAAW,CAAC;IAC1EX,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACAwK,wBAAwBA,CAACC,qBAAqB,EAAE5K,OAAO,EAAEsK,YAAY,EAAE;IACnE,KAAK,MAAMO,mBAAmB,IAAID,qBAAqB,EAAE;MACrD,MAAME,cAAc,GAAGD,mBAAmB,EAAErQ,KAAK;MACjD,IAAIsQ,cAAc,EAAE;QAChB,MAAMC,mBAAmB,GAAG,OAAOD,cAAc,KAAK,QAAQ,GACxDA,cAAc,GACd3T,kBAAkB,CAACC,iBAAiB,CAAC0T,cAAc,EAAED,mBAAmB,EAAE3I,MAAM,IAAI,CAAC,CAAC,EAAElC,OAAO,CAACtE,MAAM,CAAC,CAAC;QAC9G4O,YAAY,CAACP,aAAa,CAACgB,mBAAmB,CAAC;MACnD;IACJ;EACJ;EACAP,qBAAqBA,CAAC5B,YAAY,EAAE5I,OAAO,EAAE6B,OAAO,EAAE;IAClD,MAAMwD,SAAS,GAAGrF,OAAO,CAAC8J,eAAe,CAACnJ,WAAW;IACrD,IAAIuC,YAAY,GAAGmC,SAAS;IAC5B;IACA;IACA,MAAM9K,QAAQ,GAAGsH,OAAO,CAACtH,QAAQ,IAAI,IAAI,GAAGpD,kBAAkB,CAAC0K,OAAO,CAACtH,QAAQ,CAAC,GAAG,IAAI;IACvF,MAAMC,KAAK,GAAGqH,OAAO,CAACrH,KAAK,IAAI,IAAI,GAAGrD,kBAAkB,CAAC0K,OAAO,CAACrH,KAAK,CAAC,GAAG,IAAI;IAC9E,IAAID,QAAQ,KAAK,CAAC,EAAE;MAChBqO,YAAY,CAAC5K,OAAO,CAAEgN,WAAW,IAAK;QAClC,MAAMC,kBAAkB,GAAGjL,OAAO,CAACkL,2BAA2B,CAACF,WAAW,EAAEzQ,QAAQ,EAAEC,KAAK,CAAC;QAC5F0I,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAE+H,kBAAkB,CAAC1Q,QAAQ,GAAG0Q,kBAAkB,CAACzQ,KAAK,CAAC;MACjG,CAAC,CAAC;IACN;IACA,OAAO0I,YAAY;EACvB;EACAqD,cAAcA,CAACpG,GAAG,EAAEH,OAAO,EAAE;IACzBA,OAAO,CAACmL,aAAa,CAAChL,GAAG,CAAC0B,OAAO,EAAE,IAAI,CAAC;IACxC7L,YAAY,CAAC,IAAI,EAAEmK,GAAG,CAACsC,SAAS,EAAEzC,OAAO,CAAC;IAC1CA,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACA0C,aAAaA,CAAC1C,GAAG,EAAEH,OAAO,EAAE;IACxB,MAAMoL,eAAe,GAAGpL,OAAO,CAACoL,eAAe;IAC/C,IAAIC,GAAG,GAAGrL,OAAO;IACjB,MAAM6B,OAAO,GAAG1B,GAAG,CAAC0B,OAAO;IAC3B,IAAIA,OAAO,KAAKA,OAAO,CAACK,MAAM,IAAIL,OAAO,CAACrH,KAAK,CAAC,EAAE;MAC9C6Q,GAAG,GAAGrL,OAAO,CAACuK,gBAAgB,CAAC1I,OAAO,CAAC;MACvCwJ,GAAG,CAACZ,wBAAwB,CAAC,CAAC;MAC9B,IAAI5I,OAAO,CAACrH,KAAK,IAAI,IAAI,EAAE;QACvB,IAAI6Q,GAAG,CAACX,YAAY,CAACtJ,IAAI,IAAI9H,qBAAqB,CAAC4L,KAAK,EAAE;UACtDmG,GAAG,CAACvB,eAAe,CAACwB,qBAAqB,CAAC,CAAC;UAC3CD,GAAG,CAACX,YAAY,GAAGa,0BAA0B;QACjD;QACA,MAAM/Q,KAAK,GAAGrD,kBAAkB,CAAC0K,OAAO,CAACrH,KAAK,CAAC;QAC/C6Q,GAAG,CAACtB,aAAa,CAACvP,KAAK,CAAC;MAC5B;IACJ;IACA,IAAI2F,GAAG,CAAC4C,KAAK,CAACtG,MAAM,EAAE;MAClB0D,GAAG,CAAC4C,KAAK,CAAC/E,OAAO,CAAEgF,CAAC,IAAKhN,YAAY,CAAC,IAAI,EAAEgN,CAAC,EAAEqI,GAAG,CAAC,CAAC;MACpD;MACAA,GAAG,CAACvB,eAAe,CAAC0B,qBAAqB,CAAC,CAAC;MAC3C;MACA;MACA;MACA,IAAIH,GAAG,CAACD,eAAe,GAAGA,eAAe,EAAE;QACvCC,GAAG,CAACZ,wBAAwB,CAAC,CAAC;MAClC;IACJ;IACAzK,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACA8C,UAAUA,CAAC9C,GAAG,EAAEH,OAAO,EAAE;IACrB,MAAMyL,cAAc,GAAG,EAAE;IACzB,IAAIvI,YAAY,GAAGlD,OAAO,CAAC8J,eAAe,CAACnJ,WAAW;IACtD,MAAMnG,KAAK,GAAG2F,GAAG,CAAC0B,OAAO,IAAI1B,GAAG,CAAC0B,OAAO,CAACrH,KAAK,GAAGrD,kBAAkB,CAACgJ,GAAG,CAAC0B,OAAO,CAACrH,KAAK,CAAC,GAAG,CAAC;IAC1F2F,GAAG,CAAC4C,KAAK,CAAC/E,OAAO,CAAEgF,CAAC,IAAK;MACrB,MAAMsH,YAAY,GAAGtK,OAAO,CAACuK,gBAAgB,CAACpK,GAAG,CAAC0B,OAAO,CAAC;MAC1D,IAAIrH,KAAK,EAAE;QACP8P,YAAY,CAACP,aAAa,CAACvP,KAAK,CAAC;MACrC;MACAxE,YAAY,CAAC,IAAI,EAAEgN,CAAC,EAAEsH,YAAY,CAAC;MACnCpH,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEoH,YAAY,CAACR,eAAe,CAACnJ,WAAW,CAAC;MAC/E8K,cAAc,CAAC/O,IAAI,CAAC4N,YAAY,CAACR,eAAe,CAAC;IACrD,CAAC,CAAC;IACF;IACA;IACA;IACA2B,cAAc,CAACzN,OAAO,CAAEiM,QAAQ,IAAKjK,OAAO,CAAC8J,eAAe,CAAC4B,4BAA4B,CAACzB,QAAQ,CAAC,CAAC;IACpGjK,OAAO,CAACyK,wBAAwB,CAACvH,YAAY,CAAC;IAC9ClD,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACAwL,YAAYA,CAACxL,GAAG,EAAEH,OAAO,EAAE;IACvB,IAAIG,GAAG,CAACgI,OAAO,EAAE;MACb,MAAMJ,QAAQ,GAAG5H,GAAG,CAAC4H,QAAQ;MAC7B,MAAM6D,WAAW,GAAG5L,OAAO,CAACkC,MAAM,GAC5B9K,iBAAiB,CAAC2Q,QAAQ,EAAE/H,OAAO,CAACkC,MAAM,EAAElC,OAAO,CAACtE,MAAM,CAAC,GAC3DqM,QAAQ;MACd,OAAOhR,aAAa,CAAC6U,WAAW,EAAE5L,OAAO,CAACtE,MAAM,CAAC;IACrD,CAAC,MACI;MACD,OAAO;QAAEnB,QAAQ,EAAE4F,GAAG,CAAC5F,QAAQ;QAAEC,KAAK,EAAE2F,GAAG,CAAC3F,KAAK;QAAEC,MAAM,EAAE0F,GAAG,CAAC1F;MAAO,CAAC;IAC3E;EACJ;EACA+I,YAAYA,CAACrD,GAAG,EAAEH,OAAO,EAAE;IACvB,MAAM2D,OAAO,GAAI3D,OAAO,CAAC4D,qBAAqB,GAAG,IAAI,CAAC+H,YAAY,CAACxL,GAAG,CAACwD,OAAO,EAAE3D,OAAO,CAAE;IACzF,MAAMiK,QAAQ,GAAGjK,OAAO,CAAC8J,eAAe;IACxC,IAAInG,OAAO,CAACnJ,KAAK,EAAE;MACfwF,OAAO,CAAC6L,aAAa,CAAClI,OAAO,CAACnJ,KAAK,CAAC;MACpCyP,QAAQ,CAACqB,qBAAqB,CAAC,CAAC;IACpC;IACA,MAAM/R,KAAK,GAAG4G,GAAG,CAAC5G,KAAK;IACvB,IAAIA,KAAK,CAAC6H,IAAI,IAAI9H,qBAAqB,CAACwK,SAAS,EAAE;MAC/C,IAAI,CAACC,cAAc,CAACxK,KAAK,EAAEyG,OAAO,CAAC;IACvC,CAAC,MACI;MACDA,OAAO,CAAC6L,aAAa,CAAClI,OAAO,CAACpJ,QAAQ,CAAC;MACvC,IAAI,CAACwH,UAAU,CAACxI,KAAK,EAAEyG,OAAO,CAAC;MAC/BiK,QAAQ,CAACuB,qBAAqB,CAAC,CAAC;IACpC;IACAxL,OAAO,CAAC4D,qBAAqB,GAAG,IAAI;IACpC5D,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACA4B,UAAUA,CAAC5B,GAAG,EAAEH,OAAO,EAAE;IACrB,MAAMiK,QAAQ,GAAGjK,OAAO,CAAC8J,eAAe;IACxC,MAAMnG,OAAO,GAAG3D,OAAO,CAAC4D,qBAAqB;IAC7C;IACA;IACA,IAAI,CAACD,OAAO,IAAIsG,QAAQ,CAAC6B,yBAAyB,CAAC,CAAC,EAAE;MAClD7B,QAAQ,CAAC8B,YAAY,CAAC,CAAC;IAC3B;IACA,MAAMtR,MAAM,GAAIkJ,OAAO,IAAIA,OAAO,CAAClJ,MAAM,IAAK0F,GAAG,CAAC1F,MAAM;IACxD,IAAI0F,GAAG,CAACgE,WAAW,EAAE;MACjB8F,QAAQ,CAAC+B,cAAc,CAACvR,MAAM,CAAC;IACnC,CAAC,MACI;MACDwP,QAAQ,CAAC/R,SAAS,CAACiI,GAAG,CAAC6B,MAAM,EAAEvH,MAAM,EAAEuF,OAAO,CAACtE,MAAM,EAAEsE,OAAO,CAAC6B,OAAO,CAAC;IAC3E;IACA7B,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACA4D,cAAcA,CAAC5D,GAAG,EAAEH,OAAO,EAAE;IACzB,MAAM4D,qBAAqB,GAAG5D,OAAO,CAAC4D,qBAAqB;IAC3D,MAAMyB,SAAS,GAAGrF,OAAO,CAAC8J,eAAe,CAACvP,QAAQ;IAClD,MAAMA,QAAQ,GAAGqJ,qBAAqB,CAACrJ,QAAQ;IAC/C,MAAM+P,YAAY,GAAGtK,OAAO,CAACuK,gBAAgB,CAAC,CAAC;IAC/C,MAAM0B,aAAa,GAAG3B,YAAY,CAACR,eAAe;IAClDmC,aAAa,CAACxR,MAAM,GAAGmJ,qBAAqB,CAACnJ,MAAM;IACnD0F,GAAG,CAAC6B,MAAM,CAAChE,OAAO,CAAEmF,IAAI,IAAK;MACzB,MAAMgC,MAAM,GAAGhC,IAAI,CAACgC,MAAM,IAAI,CAAC;MAC/B8G,aAAa,CAACC,WAAW,CAAC/G,MAAM,GAAG5K,QAAQ,CAAC;MAC5C0R,aAAa,CAAC/T,SAAS,CAACiL,IAAI,CAACnB,MAAM,EAAEmB,IAAI,CAAC1I,MAAM,EAAEuF,OAAO,CAACtE,MAAM,EAAEsE,OAAO,CAAC6B,OAAO,CAAC;MAClFoK,aAAa,CAACT,qBAAqB,CAAC,CAAC;IACzC,CAAC,CAAC;IACF;IACA;IACAxL,OAAO,CAAC8J,eAAe,CAAC4B,4BAA4B,CAACO,aAAa,CAAC;IACnE;IACA;IACAjM,OAAO,CAACyK,wBAAwB,CAACpF,SAAS,GAAG9K,QAAQ,CAAC;IACtDyF,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACA0G,UAAUA,CAAC1G,GAAG,EAAEH,OAAO,EAAE;IACrB;IACA;IACA,MAAMqF,SAAS,GAAGrF,OAAO,CAAC8J,eAAe,CAACnJ,WAAW;IACrD,MAAMkB,OAAO,GAAI1B,GAAG,CAAC0B,OAAO,IAAI,CAAC,CAAE;IACnC,MAAMrH,KAAK,GAAGqH,OAAO,CAACrH,KAAK,GAAGrD,kBAAkB,CAAC0K,OAAO,CAACrH,KAAK,CAAC,GAAG,CAAC;IACnE,IAAIA,KAAK,KACJwF,OAAO,CAAC0K,YAAY,CAACtJ,IAAI,KAAK9H,qBAAqB,CAAC4L,KAAK,IACrDG,SAAS,IAAI,CAAC,IAAIrF,OAAO,CAAC8J,eAAe,CAACgC,yBAAyB,CAAC,CAAE,CAAC,EAAE;MAC9E9L,OAAO,CAAC8J,eAAe,CAACwB,qBAAqB,CAAC,CAAC;MAC/CtL,OAAO,CAAC0K,YAAY,GAAGa,0BAA0B;IACrD;IACA,IAAIrI,YAAY,GAAGmC,SAAS;IAC5B,MAAM8G,IAAI,GAAGnM,OAAO,CAACrK,WAAW,CAACwK,GAAG,CAACjG,QAAQ,EAAEiG,GAAG,CAACiH,gBAAgB,EAAEjH,GAAG,CAAC+F,KAAK,EAAE/F,GAAG,CAAC6G,WAAW,EAAEnF,OAAO,CAACsF,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEnH,OAAO,CAACtE,MAAM,CAAC;IACjJsE,OAAO,CAACoM,iBAAiB,GAAGD,IAAI,CAAC1P,MAAM;IACvC,IAAI4P,mBAAmB,GAAG,IAAI;IAC9BF,IAAI,CAACnO,OAAO,CAAC,CAAChE,OAAO,EAAEqM,CAAC,KAAK;MACzBrG,OAAO,CAACsM,iBAAiB,GAAGjG,CAAC;MAC7B,MAAMiE,YAAY,GAAGtK,OAAO,CAACuK,gBAAgB,CAACpK,GAAG,CAAC0B,OAAO,EAAE7H,OAAO,CAAC;MACnE,IAAIQ,KAAK,EAAE;QACP8P,YAAY,CAACP,aAAa,CAACvP,KAAK,CAAC;MACrC;MACA,IAAIR,OAAO,KAAKgG,OAAO,CAAChG,OAAO,EAAE;QAC7BqS,mBAAmB,GAAG/B,YAAY,CAACR,eAAe;MACtD;MACA9T,YAAY,CAAC,IAAI,EAAEmK,GAAG,CAACsC,SAAS,EAAE6H,YAAY,CAAC;MAC/C;MACA;MACA;MACAA,YAAY,CAACR,eAAe,CAAC0B,qBAAqB,CAAC,CAAC;MACpD,MAAMpG,OAAO,GAAGkF,YAAY,CAACR,eAAe,CAACnJ,WAAW;MACxDuC,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEkC,OAAO,CAAC;IAClD,CAAC,CAAC;IACFpF,OAAO,CAACsM,iBAAiB,GAAG,CAAC;IAC7BtM,OAAO,CAACoM,iBAAiB,GAAG,CAAC;IAC7BpM,OAAO,CAACyK,wBAAwB,CAACvH,YAAY,CAAC;IAC9C,IAAImJ,mBAAmB,EAAE;MACrBrM,OAAO,CAAC8J,eAAe,CAAC4B,4BAA4B,CAACW,mBAAmB,CAAC;MACzErM,OAAO,CAAC8J,eAAe,CAACwB,qBAAqB,CAAC,CAAC;IACnD;IACAtL,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;EAC9B;EACAkH,YAAYA,CAAClH,GAAG,EAAEH,OAAO,EAAE;IACvB,MAAMuM,aAAa,GAAGvM,OAAO,CAACuM,aAAa;IAC3C,MAAMC,EAAE,GAAGxM,OAAO,CAAC8J,eAAe;IAClC,MAAMnG,OAAO,GAAGxD,GAAG,CAACwD,OAAO;IAC3B,MAAMpJ,QAAQ,GAAG8I,IAAI,CAACoJ,GAAG,CAAC9I,OAAO,CAACpJ,QAAQ,CAAC;IAC3C,MAAMmS,OAAO,GAAGnS,QAAQ,IAAIyF,OAAO,CAACoM,iBAAiB,GAAG,CAAC,CAAC;IAC1D,IAAI5R,KAAK,GAAGD,QAAQ,GAAGyF,OAAO,CAACsM,iBAAiB;IAChD,IAAIK,kBAAkB,GAAGhJ,OAAO,CAACpJ,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAGoJ,OAAO,CAAClJ,MAAM;IAC1E,QAAQkS,kBAAkB;MACtB,KAAK,SAAS;QACVnS,KAAK,GAAGkS,OAAO,GAAGlS,KAAK;QACvB;MACJ,KAAK,MAAM;QACPA,KAAK,GAAG+R,aAAa,CAACK,kBAAkB;QACxC;IACR;IACA,MAAM3C,QAAQ,GAAGjK,OAAO,CAAC8J,eAAe;IACxC,IAAItP,KAAK,EAAE;MACPyP,QAAQ,CAACF,aAAa,CAACvP,KAAK,CAAC;IACjC;IACA,MAAMqS,YAAY,GAAG5C,QAAQ,CAACtJ,WAAW;IACzC3K,YAAY,CAAC,IAAI,EAAEmK,GAAG,CAACsC,SAAS,EAAEzC,OAAO,CAAC;IAC1CA,OAAO,CAAC0K,YAAY,GAAGvK,GAAG;IAC1B;IACA;IACA;IACA;IACAoM,aAAa,CAACK,kBAAkB,GAC5BJ,EAAE,CAAC7L,WAAW,GAAGkM,YAAY,IAAIL,EAAE,CAACnH,SAAS,GAAGkH,aAAa,CAACzC,eAAe,CAACzE,SAAS,CAAC;EAChG;AACJ;AACA,MAAMkG,0BAA0B,GAAG,CAAC,CAAC;AACrC,MAAM1B,wBAAwB,CAAC;EAC3B/J,OAAO;EACP9F,OAAO;EACP0P,eAAe;EACfoD,eAAe;EACfC,eAAe;EACfrR,MAAM;EACNsO,SAAS;EACTuC,aAAa,GAAG,IAAI;EACpBzC,eAAe;EACflG,qBAAqB,GAAG,IAAI;EAC5B8G,YAAY,GAAGa,0BAA0B;EACzCH,eAAe,GAAG,CAAC;EACnBvJ,OAAO,GAAG,CAAC,CAAC;EACZyK,iBAAiB,GAAG,CAAC;EACrBF,iBAAiB,GAAG,CAAC;EACrBQ,kBAAkB,GAAG,CAAC;EACtB7M,WAAWA,CAACD,OAAO,EAAE9F,OAAO,EAAE0P,eAAe,EAAEoD,eAAe,EAAEC,eAAe,EAAErR,MAAM,EAAEsO,SAAS,EAAEgD,eAAe,EAAE;IACjH,IAAI,CAAClN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9F,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0P,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACoD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACrR,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACsO,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,eAAe,GAAGkD,eAAe,IAAI,IAAIC,eAAe,CAAC,IAAI,CAACnN,OAAO,EAAE9F,OAAO,EAAE,CAAC,CAAC;IACvFgQ,SAAS,CAACtN,IAAI,CAAC,IAAI,CAACoN,eAAe,CAAC;EACxC;EACA,IAAI5H,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,OAAO,CAACK,MAAM;EAC9B;EACAiJ,aAAaA,CAACtJ,OAAO,EAAEqL,YAAY,EAAE;IACjC,IAAI,CAACrL,OAAO,EACR;IACJ,MAAMsL,UAAU,GAAGtL,OAAO;IAC1B,IAAIuL,eAAe,GAAG,IAAI,CAACvL,OAAO;IAClC;IACA,IAAIsL,UAAU,CAAC5S,QAAQ,IAAI,IAAI,EAAE;MAC7B6S,eAAe,CAAC7S,QAAQ,GAAGpD,kBAAkB,CAACgW,UAAU,CAAC5S,QAAQ,CAAC;IACtE;IACA,IAAI4S,UAAU,CAAC3S,KAAK,IAAI,IAAI,EAAE;MAC1B4S,eAAe,CAAC5S,KAAK,GAAGrD,kBAAkB,CAACgW,UAAU,CAAC3S,KAAK,CAAC;IAChE;IACA,MAAM6S,SAAS,GAAGF,UAAU,CAACjL,MAAM;IACnC,IAAImL,SAAS,EAAE;MACX,IAAIC,cAAc,GAAGF,eAAe,CAAClL,MAAM;MAC3C,IAAI,CAACoL,cAAc,EAAE;QACjBA,cAAc,GAAG,IAAI,CAACzL,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC;MAC7C;MACAyC,MAAM,CAACrE,IAAI,CAAC+M,SAAS,CAAC,CAACrP,OAAO,CAAET,IAAI,IAAK;QACrC,IAAI,CAAC2P,YAAY,IAAI,CAACI,cAAc,CAAChL,cAAc,CAAC/E,IAAI,CAAC,EAAE;UACvD+P,cAAc,CAAC/P,IAAI,CAAC,GAAGnG,iBAAiB,CAACiW,SAAS,CAAC9P,IAAI,CAAC,EAAE+P,cAAc,EAAE,IAAI,CAAC5R,MAAM,CAAC;QAC1F;MACJ,CAAC,CAAC;IACN;EACJ;EACA6R,YAAYA,CAAA,EAAG;IACX,MAAM1L,OAAO,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,MAAM2L,SAAS,GAAG,IAAI,CAAC3L,OAAO,CAACK,MAAM;MACrC,IAAIsL,SAAS,EAAE;QACX,MAAMtL,MAAM,GAAIL,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAE;QACvC8C,MAAM,CAACrE,IAAI,CAACkN,SAAS,CAAC,CAACxP,OAAO,CAAET,IAAI,IAAK;UACrC2E,MAAM,CAAC3E,IAAI,CAAC,GAAGiQ,SAAS,CAACjQ,IAAI,CAAC;QAClC,CAAC,CAAC;MACN;IACJ;IACA,OAAOsE,OAAO;EAClB;EACA0I,gBAAgBA,CAAC1I,OAAO,GAAG,IAAI,EAAE7H,OAAO,EAAEyT,OAAO,EAAE;IAC/C,MAAMC,MAAM,GAAG1T,OAAO,IAAI,IAAI,CAACA,OAAO;IACtC,MAAMgG,OAAO,GAAG,IAAI6J,wBAAwB,CAAC,IAAI,CAAC/J,OAAO,EAAE4N,MAAM,EAAE,IAAI,CAAChE,eAAe,EAAE,IAAI,CAACoD,eAAe,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACrR,MAAM,EAAE,IAAI,CAACsO,SAAS,EAAE,IAAI,CAACF,eAAe,CAAC6D,IAAI,CAACD,MAAM,EAAED,OAAO,IAAI,CAAC,CAAC,CAAC;IAClNzN,OAAO,CAAC0K,YAAY,GAAG,IAAI,CAACA,YAAY;IACxC1K,OAAO,CAAC4D,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;IAC1D5D,OAAO,CAAC6B,OAAO,GAAG,IAAI,CAAC0L,YAAY,CAAC,CAAC;IACrCvN,OAAO,CAACmL,aAAa,CAACtJ,OAAO,CAAC;IAC9B7B,OAAO,CAACsM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAClDtM,OAAO,CAACoM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAClDpM,OAAO,CAACuM,aAAa,GAAG,IAAI;IAC5B,IAAI,CAACnB,eAAe,EAAE;IACtB,OAAOpL,OAAO;EAClB;EACAyK,wBAAwBA,CAACgD,OAAO,EAAE;IAC9B,IAAI,CAAC/C,YAAY,GAAGa,0BAA0B;IAC9C,IAAI,CAACzB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6D,IAAI,CAAC,IAAI,CAAC3T,OAAO,EAAEyT,OAAO,CAAC;IACvE,IAAI,CAACzD,SAAS,CAACtN,IAAI,CAAC,IAAI,CAACoN,eAAe,CAAC;IACzC,OAAO,IAAI,CAACA,eAAe;EAC/B;EACAoB,2BAA2BA,CAACF,WAAW,EAAEzQ,QAAQ,EAAEC,KAAK,EAAE;IACtD,MAAMoT,cAAc,GAAG;MACnBrT,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGyQ,WAAW,CAACzQ,QAAQ;MAC5DC,KAAK,EAAE,IAAI,CAACsP,eAAe,CAACnJ,WAAW,IAAInG,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,CAAC,GAAGwQ,WAAW,CAACxQ,KAAK;MACzFC,MAAM,EAAE;IACZ,CAAC;IACD,MAAMoT,OAAO,GAAG,IAAIC,kBAAkB,CAAC,IAAI,CAAChO,OAAO,EAAEkL,WAAW,CAAChR,OAAO,EAAEgR,WAAW,CAAC1Q,SAAS,EAAE0Q,WAAW,CAAC3C,aAAa,EAAE2C,WAAW,CAAC1C,cAAc,EAAEsF,cAAc,EAAE5C,WAAW,CAAC+C,uBAAuB,CAAC;IAC5M,IAAI,CAAC/D,SAAS,CAACtN,IAAI,CAACmR,OAAO,CAAC;IAC5B,OAAOD,cAAc;EACzB;EACA/B,aAAaA,CAACmC,IAAI,EAAE;IAChB,IAAI,CAAClE,eAAe,CAACoC,WAAW,CAAC,IAAI,CAACpC,eAAe,CAACvP,QAAQ,GAAGyT,IAAI,CAAC;EAC1E;EACAjE,aAAaA,CAACvP,KAAK,EAAE;IACjB;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,IAAI,CAACsP,eAAe,CAACC,aAAa,CAACvP,KAAK,CAAC;IAC7C;EACJ;EACA7E,WAAWA,CAACuE,QAAQ,EAAEkN,gBAAgB,EAAElB,KAAK,EAAEc,WAAW,EAAEG,QAAQ,EAAEzL,MAAM,EAAE;IAC1E,IAAIuS,OAAO,GAAG,EAAE;IAChB,IAAIjH,WAAW,EAAE;MACbiH,OAAO,CAACvR,IAAI,CAAC,IAAI,CAAC1C,OAAO,CAAC;IAC9B;IACA,IAAIE,QAAQ,CAACuC,MAAM,GAAG,CAAC,EAAE;MACrB;MACAvC,QAAQ,GAAGA,QAAQ,CAACuN,OAAO,CAACwB,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC6D,eAAe,CAAC;MAC1E5S,QAAQ,GAAGA,QAAQ,CAACuN,OAAO,CAAC0B,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC4D,eAAe,CAAC;MAC1E,MAAM5S,KAAK,GAAG+L,KAAK,IAAI,CAAC;MACxB,IAAIgI,QAAQ,GAAG,IAAI,CAACpO,OAAO,CAAC7F,KAAK,CAAC,IAAI,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;MAChE,IAAI+L,KAAK,KAAK,CAAC,EAAE;QACbgI,QAAQ,GACJhI,KAAK,GAAG,CAAC,GACHgI,QAAQ,CAACxG,KAAK,CAACwG,QAAQ,CAACzR,MAAM,GAAGyJ,KAAK,EAAEgI,QAAQ,CAACzR,MAAM,CAAC,GACxDyR,QAAQ,CAACxG,KAAK,CAAC,CAAC,EAAExB,KAAK,CAAC;MACtC;MACA+H,OAAO,CAACvR,IAAI,CAAC,GAAGwR,QAAQ,CAAC;IAC7B;IACA,IAAI,CAAC/G,QAAQ,IAAI8G,OAAO,CAACxR,MAAM,IAAI,CAAC,EAAE;MAClCf,MAAM,CAACgB,IAAI,CAACrF,YAAY,CAAC+P,gBAAgB,CAAC,CAAC;IAC/C;IACA,OAAO6G,OAAO;EAClB;AACJ;AACA,MAAMhB,eAAe,CAAC;EAClBnN,OAAO;EACP9F,OAAO;EACPqL,SAAS;EACT8I,4BAA4B;EAC5B5T,QAAQ,GAAG,CAAC;EACZE,MAAM,GAAG,IAAI;EACb2T,iBAAiB,gBAAG,IAAI3N,GAAG,CAAC,CAAC;EAC7B4N,gBAAgB,gBAAG,IAAI5N,GAAG,CAAC,CAAC;EAC5B6N,UAAU,gBAAG,IAAI7N,GAAG,CAAC,CAAC;EACtB8N,aAAa,gBAAG,IAAI9N,GAAG,CAAC,CAAC;EACzB+N,oBAAoB,gBAAG,IAAI/N,GAAG,CAAC,CAAC;EAChCgO,qBAAqB;EACrBC,cAAc,gBAAG,IAAIjO,GAAG,CAAC,CAAC;EAC1BkO,SAAS,gBAAG,IAAIlO,GAAG,CAAC,CAAC;EACrBmO,yBAAyB,GAAG,IAAI;EAChC7O,WAAWA,CAACD,OAAO,EAAE9F,OAAO,EAAEqL,SAAS,EAAE8I,4BAA4B,EAAE;IACnE,IAAI,CAACrO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9F,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC8I,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAAC,IAAI,CAACA,4BAA4B,EAAE;MACpC,IAAI,CAACA,4BAA4B,GAAG,IAAI1N,GAAG,CAAC,CAAC;IACjD;IACA,IAAI,CAACgO,qBAAqB,GAAG,IAAI,CAACN,4BAA4B,CAACpJ,GAAG,CAAC/K,OAAO,CAAC;IAC3E,IAAI,CAAC,IAAI,CAACyU,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAACD,oBAAoB;MACtD,IAAI,CAACL,4BAA4B,CAACzN,GAAG,CAAC1G,OAAO,EAAE,IAAI,CAACwU,oBAAoB,CAAC;IAC7E;IACA,IAAI,CAACK,aAAa,CAAC,CAAC;EACxB;EACA3E,iBAAiBA,CAAA,EAAG;IAChB,QAAQ,IAAI,CAACoE,UAAU,CAACjO,IAAI;MACxB,KAAK,CAAC;QACF,OAAO,KAAK;MAChB,KAAK,CAAC;QACF,OAAO,IAAI,CAACyL,yBAAyB,CAAC,CAAC;MAC3C;QACI,OAAO,IAAI;IACnB;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACuC,gBAAgB,CAAChO,IAAI,GAAG,CAAC;EACzC;EACA,IAAIM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC0E,SAAS,GAAG,IAAI,CAAC9K,QAAQ;EACzC;EACAwP,aAAaA,CAACvP,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA,MAAMsU,eAAe,GAAG,IAAI,CAACR,UAAU,CAACjO,IAAI,KAAK,CAAC,IAAI,IAAI,CAACqO,cAAc,CAACrO,IAAI;IAC9E,IAAI,IAAI,CAAC9F,QAAQ,IAAIuU,eAAe,EAAE;MAClC,IAAI,CAAC5C,WAAW,CAAC,IAAI,CAACvL,WAAW,GAAGnG,KAAK,CAAC;MAC1C,IAAIsU,eAAe,EAAE;QACjB,IAAI,CAACxD,qBAAqB,CAAC,CAAC;MAChC;IACJ,CAAC,MACI;MACD,IAAI,CAACjG,SAAS,IAAI7K,KAAK;IAC3B;EACJ;EACAmT,IAAIA,CAAC3T,OAAO,EAAE2G,WAAW,EAAE;IACvB,IAAI,CAAC6K,qBAAqB,CAAC,CAAC;IAC5B,OAAO,IAAIyB,eAAe,CAAC,IAAI,CAACnN,OAAO,EAAE9F,OAAO,EAAE2G,WAAW,IAAI,IAAI,CAACA,WAAW,EAAE,IAAI,CAACwN,4BAA4B,CAAC;EACzH;EACAU,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACvB,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAACC,gBAAgB;IAClD;IACA,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACC,UAAU,CAACvJ,GAAG,CAAC,IAAI,CAACxK,QAAQ,CAAC;IAC1D,IAAI,CAAC,IAAI,CAAC8T,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI5N,GAAG,CAAC,CAAC;MACjC,IAAI,CAAC6N,UAAU,CAAC5N,GAAG,CAAC,IAAI,CAACnG,QAAQ,EAAE,IAAI,CAAC8T,gBAAgB,CAAC;IAC7D;EACJ;EACAtC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACxR,QAAQ,IAAIwO,yBAAyB;IAC1C,IAAI,CAAC8F,aAAa,CAAC,CAAC;EACxB;EACA3C,WAAWA,CAAC8B,IAAI,EAAE;IACd,IAAI,CAACxC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACjR,QAAQ,GAAGyT,IAAI;IACpB,IAAI,CAACa,aAAa,CAAC,CAAC;EACxB;EACAE,YAAYA,CAAClV,IAAI,EAAEiC,KAAK,EAAE;IACtB,IAAI,CAAC0S,oBAAoB,CAAC9N,GAAG,CAAC7G,IAAI,EAAEiC,KAAK,CAAC;IAC1C,IAAI,CAAC2S,qBAAqB,CAAC/N,GAAG,CAAC7G,IAAI,EAAEiC,KAAK,CAAC;IAC3C,IAAI,CAACyS,aAAa,CAAC7N,GAAG,CAAC7G,IAAI,EAAE;MAAEmU,IAAI,EAAE,IAAI,CAACrN,WAAW;MAAE7E;IAAM,CAAC,CAAC;EACnE;EACAsO,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACwE,yBAAyB,KAAK,IAAI,CAACP,gBAAgB;EACnE;EACArC,cAAcA,CAACvR,MAAM,EAAE;IACnB,IAAIA,MAAM,EAAE;MACR,IAAI,CAAC2T,iBAAiB,CAAC1N,GAAG,CAAC,QAAQ,EAAEjG,MAAM,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAI,CAACZ,IAAI,EAAEiC,KAAK,CAAC,IAAI,IAAI,CAAC2S,qBAAqB,EAAE;MAClD,IAAI,CAACE,SAAS,CAACjO,GAAG,CAAC7G,IAAI,EAAEiC,KAAK,IAAItC,UAAU,CAAC;MAC7C,IAAI,CAAC6U,gBAAgB,CAAC3N,GAAG,CAAC7G,IAAI,EAAEL,UAAU,CAAC;IAC/C;IACA,IAAI,CAACoV,yBAAyB,GAAG,IAAI,CAACP,gBAAgB;EAC1D;EACAnW,SAASA,CAAC8W,KAAK,EAAEvU,MAAM,EAAEiB,MAAM,EAAEmG,OAAO,EAAE;IACtC,IAAIpH,MAAM,EAAE;MACR,IAAI,CAAC2T,iBAAiB,CAAC1N,GAAG,CAAC,QAAQ,EAAEjG,MAAM,CAAC;IAChD;IACA,MAAMyH,MAAM,GAAIL,OAAO,IAAIA,OAAO,CAACK,MAAM,IAAK,CAAC,CAAC;IAChD,MAAMF,MAAM,GAAGiN,aAAa,CAACD,KAAK,EAAE,IAAI,CAACP,qBAAqB,CAAC;IAC/D,KAAK,IAAI,CAAC5U,IAAI,EAAEiC,KAAK,CAAC,IAAIkG,MAAM,EAAE;MAC9B,MAAMkN,GAAG,GAAG9X,iBAAiB,CAAC0E,KAAK,EAAEoG,MAAM,EAAExG,MAAM,CAAC;MACpD,IAAI,CAACgT,cAAc,CAAChO,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;MAClC,IAAI,CAAC,IAAI,CAACV,oBAAoB,CAAClS,GAAG,CAACzC,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC8U,SAAS,CAACjO,GAAG,CAAC7G,IAAI,EAAE,IAAI,CAAC4U,qBAAqB,CAAC1J,GAAG,CAAClL,IAAI,CAAC,IAAIL,UAAU,CAAC;MAChF;MACA,IAAI,CAACuV,YAAY,CAAClV,IAAI,EAAEqV,GAAG,CAAC;IAChC;EACJ;EACA1D,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACkD,cAAc,CAACrO,IAAI,IAAI,CAAC,EAC7B;IACJ,IAAI,CAACqO,cAAc,CAAC1Q,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAK;MACvC,IAAI,CAACwU,gBAAgB,CAAC3N,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;IACxC,CAAC,CAAC;IACF,IAAI,CAACR,cAAc,CAAC5F,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC0F,oBAAoB,CAACxQ,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAK;MAC7C,IAAI,CAAC,IAAI,CAACwU,gBAAgB,CAAC/R,GAAG,CAACzC,IAAI,CAAC,EAAE;QAClC,IAAI,CAACwU,gBAAgB,CAAC3N,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA5D,qBAAqBA,CAAA,EAAG;IACpB,KAAK,IAAI,CAACzR,IAAI,EAAEqV,GAAG,CAAC,IAAI,IAAI,CAACV,oBAAoB,EAAE;MAC/C,IAAI,CAACE,cAAc,CAAChO,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;MAClC,IAAI,CAACH,YAAY,CAAClV,IAAI,EAAEqV,GAAG,CAAC;IAChC;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,UAAU,CAACvJ,GAAG,CAAC,IAAI,CAACxK,QAAQ,CAAC;EAC7C;EACA,IAAI6U,UAAUA,CAAA,EAAG;IACb,MAAMA,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIvV,IAAI,IAAI,IAAI,CAACwU,gBAAgB,EAAE;MACpCe,UAAU,CAAC1S,IAAI,CAAC7C,IAAI,CAAC;IACzB;IACA,OAAOuV,UAAU;EACrB;EACA1D,4BAA4BA,CAACzB,QAAQ,EAAE;IACnCA,QAAQ,CAACsE,aAAa,CAACvQ,OAAO,CAAC,CAACqR,QAAQ,EAAExV,IAAI,KAAK;MAC/C,MAAMyV,QAAQ,GAAG,IAAI,CAACf,aAAa,CAACxJ,GAAG,CAAClL,IAAI,CAAC;MAC7C,IAAI,CAACyV,QAAQ,IAAID,QAAQ,CAACrB,IAAI,GAAGsB,QAAQ,CAACtB,IAAI,EAAE;QAC5C,IAAI,CAACe,YAAY,CAAClV,IAAI,EAAEwV,QAAQ,CAACvT,KAAK,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACA8N,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC4B,qBAAqB,CAAC,CAAC;IAC5B,MAAMnD,aAAa,GAAG,IAAIrM,GAAG,CAAC,CAAC;IAC/B,MAAMsM,cAAc,GAAG,IAAItM,GAAG,CAAC,CAAC;IAChC,MAAMgI,OAAO,GAAG,IAAI,CAACsK,UAAU,CAACjO,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC9F,QAAQ,KAAK,CAAC;IACjE,IAAIgV,cAAc,GAAG,EAAE;IACvB,IAAI,CAACjB,UAAU,CAACtQ,OAAO,CAAC,CAACwR,QAAQ,EAAExB,IAAI,KAAK;MACxC,MAAMyB,aAAa,GAAG,IAAIhP,GAAG,CAAC,CAAC,GAAG,IAAI,CAACkO,SAAS,EAAE,GAAGa,QAAQ,CAAC,CAAC;MAC/DC,aAAa,CAACzR,OAAO,CAAC,CAAClC,KAAK,EAAEjC,IAAI,KAAK;QACnC,IAAIiC,KAAK,KAAKpC,UAAU,EAAE;UACtB2O,aAAa,CAAC9F,GAAG,CAAC1I,IAAI,CAAC;QAC3B,CAAC,MACI,IAAIiC,KAAK,KAAKtC,UAAU,EAAE;UAC3B8O,cAAc,CAAC/F,GAAG,CAAC1I,IAAI,CAAC;QAC5B;MACJ,CAAC,CAAC;MACF,IAAI,CAACmK,OAAO,EAAE;QACVyL,aAAa,CAAC/O,GAAG,CAAC,QAAQ,EAAEsN,IAAI,GAAG,IAAI,CAACzT,QAAQ,CAAC;MACrD;MACAgV,cAAc,CAAC7S,IAAI,CAAC+S,aAAa,CAAC;IACtC,CAAC,CAAC;IACF,MAAMC,QAAQ,GAAG,CAAC,GAAGrH,aAAa,CAAC7F,MAAM,CAAC,CAAC,CAAC;IAC5C,MAAMmN,SAAS,GAAG,CAAC,GAAGrH,cAAc,CAAC9F,MAAM,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIwB,OAAO,EAAE;MACT,MAAM4L,GAAG,GAAGL,cAAc,CAAC,CAAC,CAAC;MAC7B,MAAMM,GAAG,GAAG,IAAIpP,GAAG,CAACmP,GAAG,CAAC;MACxBA,GAAG,CAAClP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACpBmP,GAAG,CAACnP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACpB6O,cAAc,GAAG,CAACK,GAAG,EAAEC,GAAG,CAAC;IAC/B;IACA,OAAOzH,yBAAyB,CAAC,IAAI,CAACpO,OAAO,EAAEuV,cAAc,EAAEG,QAAQ,EAAEC,SAAS,EAAE,IAAI,CAACpV,QAAQ,EAAE,IAAI,CAAC8K,SAAS,EAAE,IAAI,CAAC5K,MAAM,EAAE,KAAK,CAAC;EAC1I;AACJ;AACA,MAAMqT,kBAAkB,SAASb,eAAe,CAAC;EAC7C3S,SAAS;EACT+N,aAAa;EACbC,cAAc;EACdwH,wBAAwB;EACxBnM,OAAO;EACP5D,WAAWA,CAACN,MAAM,EAAEzF,OAAO,EAAEM,SAAS,EAAE+N,aAAa,EAAEC,cAAc,EAAE3E,OAAO,EAAEmM,wBAAwB,GAAG,KAAK,EAAE;IAC9G,KAAK,CAACrQ,MAAM,EAAEzF,OAAO,EAAE2J,OAAO,CAACnJ,KAAK,CAAC;IACrC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC+N,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwH,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACnM,OAAO,GAAG;MAAEpJ,QAAQ,EAAEoJ,OAAO,CAACpJ,QAAQ;MAAEC,KAAK,EAAEmJ,OAAO,CAACnJ,KAAK;MAAEC,MAAM,EAAEkJ,OAAO,CAAClJ;IAAO,CAAC;EAC/F;EACAyP,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC5P,SAAS,CAACmC,MAAM,GAAG,CAAC;EACpC;EACAmN,cAAcA,CAAA,EAAG;IACb,IAAItP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI;MAAEE,KAAK;MAAED,QAAQ;MAAEE;IAAO,CAAC,GAAG,IAAI,CAACkJ,OAAO;IAC9C,IAAI,IAAI,CAACmM,wBAAwB,IAAItV,KAAK,EAAE;MACxC,MAAMuV,YAAY,GAAG,EAAE;MACvB,MAAMvH,SAAS,GAAGjO,QAAQ,GAAGC,KAAK;MAClC,MAAMwV,WAAW,GAAGxV,KAAK,GAAGgO,SAAS;MACrC;MACA,MAAMyH,gBAAgB,GAAG,IAAIxP,GAAG,CAACnG,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9C2V,gBAAgB,CAACvP,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;MACjCqP,YAAY,CAACrT,IAAI,CAACuT,gBAAgB,CAAC;MACnC,MAAMC,gBAAgB,GAAG,IAAIzP,GAAG,CAACnG,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9C4V,gBAAgB,CAACxP,GAAG,CAAC,QAAQ,EAAEyP,WAAW,CAACH,WAAW,CAAC,CAAC;MACxDD,YAAY,CAACrT,IAAI,CAACwT,gBAAgB,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAIY;MACA,MAAMhK,KAAK,GAAG5L,SAAS,CAACmC,MAAM,GAAG,CAAC;MAClC,KAAK,IAAI4J,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,KAAK,EAAEG,CAAC,EAAE,EAAE;QAC7B,IAAID,EAAE,GAAG,IAAI3F,GAAG,CAACnG,SAAS,CAAC+L,CAAC,CAAC,CAAC;QAC9B,MAAM+J,SAAS,GAAGhK,EAAE,CAACrB,GAAG,CAAC,QAAQ,CAAC;QAClC,MAAMsL,cAAc,GAAG7V,KAAK,GAAG4V,SAAS,GAAG7V,QAAQ;QACnD6L,EAAE,CAAC1F,GAAG,CAAC,QAAQ,EAAEyP,WAAW,CAACE,cAAc,GAAG7H,SAAS,CAAC,CAAC;QACzDuH,YAAY,CAACrT,IAAI,CAAC0J,EAAE,CAAC;MACzB;MACA;MACA7L,QAAQ,GAAGiO,SAAS;MACpBhO,KAAK,GAAG,CAAC;MACTC,MAAM,GAAG,EAAE;MACXH,SAAS,GAAGyV,YAAY;IAC5B;IACA,OAAO3H,yBAAyB,CAAC,IAAI,CAACpO,OAAO,EAAEM,SAAS,EAAE,IAAI,CAAC+N,aAAa,EAAE,IAAI,CAACC,cAAc,EAAE/N,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAE,IAAI,CAAC;EACrI;AACJ;AACA,SAAS0V,WAAWA,CAAChL,MAAM,EAAEmL,aAAa,GAAG,CAAC,EAAE;EAC5C,MAAMC,IAAI,GAAGlN,IAAI,CAACmN,GAAG,CAAC,EAAE,EAAEF,aAAa,GAAG,CAAC,CAAC;EAC5C,OAAOjN,IAAI,CAACoN,KAAK,CAACtL,MAAM,GAAGoL,IAAI,CAAC,GAAGA,IAAI;AAC3C;AACA,SAAStB,aAAaA,CAACD,KAAK,EAAE0B,SAAS,EAAE;EACrC,MAAM1O,MAAM,GAAG,IAAIvB,GAAG,CAAC,CAAC;EACxB,IAAIkQ,aAAa;EACjB3B,KAAK,CAAChR,OAAO,CAAE/C,KAAK,IAAK;IACrB,IAAIA,KAAK,KAAK,GAAG,EAAE;MACf0V,aAAa,KAAKD,SAAS,CAACpQ,IAAI,CAAC,CAAC;MAClC,KAAK,IAAIzG,IAAI,IAAI8W,aAAa,EAAE;QAC5B3O,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAEL,UAAU,CAAC;MAChC;IACJ,CAAC,MACI;MACD,KAAK,IAAI,CAACK,IAAI,EAAEqV,GAAG,CAAC,IAAIjU,KAAK,EAAE;QAC3B+G,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;MACzB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOlN,MAAM;AACjB;AAEA,SAAS4O,2BAA2BA,CAAC5W,OAAO,EAAE6W,WAAW,EAAEvS,SAAS,EAAEE,OAAO,EAAEsS,mBAAmB,EAAEC,UAAU,EAAEC,QAAQ,EAAEhH,SAAS,EAAEiH,eAAe,EAAE5I,aAAa,EAAEC,cAAc,EAAEE,SAAS,EAAE9M,MAAM,EAAE;EACpM,OAAO;IACH0F,IAAI,EAAE,CAAC,CAAC;IACRpH,OAAO;IACP6W,WAAW;IACXC,mBAAmB;IACnBxS,SAAS;IACTyS,UAAU;IACVvS,OAAO;IACPwS,QAAQ;IACRhH,SAAS;IACTiH,eAAe;IACf5I,aAAa;IACbC,cAAc;IACdE,SAAS;IACT9M;EACJ,CAAC;AACL;AAEA,MAAMwV,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,0BAA0B,CAAC;EAC7BC,YAAY;EACZjR,GAAG;EACHkR,YAAY;EACZtR,WAAWA,CAACqR,YAAY,EAAEjR,GAAG,EAAEkR,YAAY,EAAE;IACzC,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjR,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkR,YAAY,GAAGA,YAAY;EACpC;EACA7U,KAAKA,CAAC8U,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,EAAE;IAC5C,OAAOsP,yBAAyB,CAAC,IAAI,CAACrR,GAAG,CAACuC,QAAQ,EAAE4O,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,CAAC;EACjG;EACAuP,WAAWA,CAACC,SAAS,EAAExP,MAAM,EAAExG,MAAM,EAAE;IACnC,IAAIiW,MAAM,GAAG,IAAI,CAACN,YAAY,CAACtM,GAAG,CAAC,GAAG,CAAC;IACvC,IAAI2M,SAAS,KAAKE,SAAS,EAAE;MACzBD,MAAM,GAAG,IAAI,CAACN,YAAY,CAACtM,GAAG,CAAC2M,SAAS,EAAEtV,QAAQ,CAAC,CAAC,CAAC,IAAIuV,MAAM;IACnE;IACA,OAAOA,MAAM,GAAGA,MAAM,CAACF,WAAW,CAACvP,MAAM,EAAExG,MAAM,CAAC,GAAG,IAAI+E,GAAG,CAAC,CAAC;EAClE;EACAb,KAAKA,CAACH,MAAM,EAAEzF,OAAO,EAAEsX,YAAY,EAAEC,SAAS,EAAEjI,cAAc,EAAEC,cAAc,EAAEsI,cAAc,EAAEC,WAAW,EAAEpI,eAAe,EAAEqI,YAAY,EAAE;IACxI,MAAMrW,MAAM,GAAG,EAAE;IACjB,MAAMsW,yBAAyB,GAAI,IAAI,CAAC7R,GAAG,CAAC0B,OAAO,IAAI,IAAI,CAAC1B,GAAG,CAAC0B,OAAO,CAACK,MAAM,IAAKgP,YAAY;IAC/F,MAAMe,sBAAsB,GAAIJ,cAAc,IAAIA,cAAc,CAAC3P,MAAM,IAAKgP,YAAY;IACxF,MAAMgB,kBAAkB,GAAG,IAAI,CAACT,WAAW,CAACH,YAAY,EAAEW,sBAAsB,EAAEvW,MAAM,CAAC;IACzF,MAAMyW,mBAAmB,GAAIL,WAAW,IAAIA,WAAW,CAAC5P,MAAM,IAAKgP,YAAY;IAC/E,MAAMkB,eAAe,GAAG,IAAI,CAACX,WAAW,CAACF,SAAS,EAAEY,mBAAmB,EAAEzW,MAAM,CAAC;IAChF,MAAMuV,eAAe,GAAG,IAAIjV,GAAG,CAAC,CAAC;IACjC,MAAMqW,WAAW,GAAG,IAAI5R,GAAG,CAAC,CAAC;IAC7B,MAAM6R,YAAY,GAAG,IAAI7R,GAAG,CAAC,CAAC;IAC9B,MAAM8R,SAAS,GAAGhB,SAAS,KAAK,MAAM;IACtC,MAAMiB,gBAAgB,GAAG;MACrBtQ,MAAM,EAAEuQ,kBAAkB,CAACN,mBAAmB,EAAEH,yBAAyB,CAAC;MAC1ExX,KAAK,EAAE,IAAI,CAAC2F,GAAG,CAAC0B,OAAO,EAAErH;IAC7B,CAAC;IACD,MAAMwP,SAAS,GAAG+H,YAAY,GACxB,EAAE,GACF3I,uBAAuB,CAAC3J,MAAM,EAAEzF,OAAO,EAAE,IAAI,CAACmG,GAAG,CAACsC,SAAS,EAAE6G,cAAc,EAAEC,cAAc,EAAE2I,kBAAkB,EAAEE,eAAe,EAAEI,gBAAgB,EAAE9I,eAAe,EAAEhO,MAAM,CAAC;IAClL,IAAI8M,SAAS,GAAG,CAAC;IACjBwB,SAAS,CAAChM,OAAO,CAAEwO,EAAE,IAAK;MACtBhE,SAAS,GAAGnF,IAAI,CAACC,GAAG,CAACkJ,EAAE,CAACjS,QAAQ,GAAGiS,EAAE,CAAChS,KAAK,EAAEgO,SAAS,CAAC;IAC3D,CAAC,CAAC;IACF,IAAI9M,MAAM,CAACe,MAAM,EAAE;MACf,OAAOmU,2BAA2B,CAAC5W,OAAO,EAAE,IAAI,CAACoX,YAAY,EAAEE,YAAY,EAAEC,SAAS,EAAEgB,SAAS,EAAEL,kBAAkB,EAAEE,eAAe,EAAE,EAAE,EAAE,EAAE,EAAEC,WAAW,EAAEC,YAAY,EAAE9J,SAAS,EAAE9M,MAAM,CAAC;IACjM;IACAsO,SAAS,CAAChM,OAAO,CAAEwO,EAAE,IAAK;MACtB,MAAMkG,GAAG,GAAGlG,EAAE,CAACxS,OAAO;MACtB,MAAM0V,QAAQ,GAAG7Y,oBAAoB,CAACwb,WAAW,EAAEK,GAAG,EAAE,IAAI1W,GAAG,CAAC,CAAC,CAAC;MAClEwQ,EAAE,CAACnE,aAAa,CAACrK,OAAO,CAAEnE,IAAI,IAAK6V,QAAQ,CAACnN,GAAG,CAAC1I,IAAI,CAAC,CAAC;MACtD,MAAM8V,SAAS,GAAG9Y,oBAAoB,CAACyb,YAAY,EAAEI,GAAG,EAAE,IAAI1W,GAAG,CAAC,CAAC,CAAC;MACpEwQ,EAAE,CAAClE,cAAc,CAACtK,OAAO,CAAEnE,IAAI,IAAK8V,SAAS,CAACpN,GAAG,CAAC1I,IAAI,CAAC,CAAC;MACxD,IAAI6Y,GAAG,KAAK1Y,OAAO,EAAE;QACjBiX,eAAe,CAAC1O,GAAG,CAACmQ,GAAG,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,OAAOvX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CwX,6BAA6B,CAAC3I,SAAS,EAAE,IAAI,CAACoH,YAAY,EAAE3R,MAAM,CAAC;IACvE;IACA,OAAOmR,2BAA2B,CAAC5W,OAAO,EAAE,IAAI,CAACoX,YAAY,EAAEE,YAAY,EAAEC,SAAS,EAAEgB,SAAS,EAAEL,kBAAkB,EAAEE,eAAe,EAAEpI,SAAS,EAAE,CAAC,GAAGiH,eAAe,CAACzO,MAAM,CAAC,CAAC,CAAC,EAAE6P,WAAW,EAAEC,YAAY,EAAE9J,SAAS,CAAC;EAC3N;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmK,6BAA6BA,CAAC3I,SAAS,EAAE6G,WAAW,EAAEpR,MAAM,EAAE;EACnE,IAAI,CAACA,MAAM,CAACmT,+BAA+B,EAAE;IACzC;EACJ;EACA,MAAMC,yBAAyB,GAAG,IAAI7W,GAAG,CAAC;EACtC;EACA;EACA;EACA;EACA,QAAQ,CACX,CAAC;EACF,MAAM8W,yBAAyB,GAAG,IAAI9W,GAAG,CAAC,CAAC;EAC3CgO,SAAS,CAAChM,OAAO,CAAC,CAAC;IAAE1D;EAAU,CAAC,KAAK;IACjC,MAAMyY,+BAA+B,GAAG,IAAItS,GAAG,CAAC,CAAC;IACjDnG,SAAS,CAAC0D,OAAO,CAAEwR,QAAQ,IAAK;MAC5B,MAAMwD,cAAc,GAAGxO,KAAK,CAACyO,IAAI,CAACzD,QAAQ,CAAC5K,OAAO,CAAC,CAAC,CAAC,CAAC9H,MAAM,CAAC,CAAC,CAACjD,IAAI,CAAC,KAAK,CAACgZ,yBAAyB,CAACvW,GAAG,CAACzC,IAAI,CAAC,CAAC;MAC9G,KAAK,MAAM,CAACA,IAAI,EAAEiC,KAAK,CAAC,IAAIkX,cAAc,EAAE;QACxC,IAAI,CAACvT,MAAM,CAACmT,+BAA+B,CAAC/Y,IAAI,CAAC,EAAE;UAC/C,IAAIkZ,+BAA+B,CAACzW,GAAG,CAACzC,IAAI,CAAC,IAAI,CAACiZ,yBAAyB,CAACxW,GAAG,CAACzC,IAAI,CAAC,EAAE;YACnF,MAAMqZ,gBAAgB,GAAGH,+BAA+B,CAAChO,GAAG,CAAClL,IAAI,CAAC;YAClE,IAAIqZ,gBAAgB,KAAKpX,KAAK,EAAE;cAC5BgX,yBAAyB,CAACvQ,GAAG,CAAC1I,IAAI,CAAC;YACvC;UACJ,CAAC,MACI;YACDkZ,+BAA+B,CAACrS,GAAG,CAAC7G,IAAI,EAAEiC,KAAK,CAAC;UACpD;QACJ;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAIgX,yBAAyB,CAACzS,IAAI,GAAG,CAAC,EAAE;IACpCjD,OAAO,CAACC,IAAI,CAAC,mCAAmCwT,WAAW,0CAA0C,GACjG,8BAA8B,GAC9BrM,KAAK,CAACyO,IAAI,CAACH,yBAAyB,CAAC,CAAC5V,IAAI,CAAC,IAAI,CAAC,GAChD,IAAI,GACJ,iIAAiI,CAAC;EAC1I;AACJ;AACA,SAASsU,yBAAyBA,CAAC2B,QAAQ,EAAE7B,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,EAAE;EACnF,OAAOiR,QAAQ,CAAClL,IAAI,CAAEmL,EAAE,IAAKA,EAAE,CAAC9B,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,CAAC,CAAC;AAC9E;AACA,SAASuQ,kBAAkBA,CAACY,UAAU,EAAEC,QAAQ,EAAE;EAC9C,MAAMlV,MAAM,GAAG;IAAE,GAAGkV;EAAS,CAAC;EAC9B3O,MAAM,CAACC,OAAO,CAACyO,UAAU,CAAC,CAACrV,OAAO,CAAC,CAAC,CAACuV,GAAG,EAAEzX,KAAK,CAAC,KAAK;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACfsC,MAAM,CAACmV,GAAG,CAAC,GAAGzX,KAAK;IACvB;EACJ,CAAC,CAAC;EACF,OAAOsC,MAAM;AACjB;AACA,MAAMoV,oBAAoB,CAAC;EACvBxR,MAAM;EACNyR,aAAa;EACbC,UAAU;EACV3T,WAAWA,CAACiC,MAAM,EAAEyR,aAAa,EAAEC,UAAU,EAAE;IAC3C,IAAI,CAAC1R,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACyR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAjC,WAAWA,CAACvP,MAAM,EAAExG,MAAM,EAAE;IACxB,MAAM+N,WAAW,GAAG,IAAIhJ,GAAG,CAAC,CAAC;IAC7B,MAAMkT,cAAc,GAAGlB,kBAAkB,CAACvQ,MAAM,EAAE,IAAI,CAACuR,aAAa,CAAC;IACrE,IAAI,CAACzR,MAAM,CAACA,MAAM,CAAChE,OAAO,CAAElC,KAAK,IAAK;MAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,CAACkC,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAK;UACzB,IAAIqV,GAAG,EAAE;YACLA,GAAG,GAAG9X,iBAAiB,CAAC8X,GAAG,EAAEyE,cAAc,EAAEjY,MAAM,CAAC;UACxD;UACA,MAAMkY,cAAc,GAAG,IAAI,CAACF,UAAU,CAAClY,qBAAqB,CAAC3B,IAAI,EAAE6B,MAAM,CAAC;UAC1EwT,GAAG,GAAG,IAAI,CAACwE,UAAU,CAAC/X,mBAAmB,CAAC9B,IAAI,EAAE+Z,cAAc,EAAE1E,GAAG,EAAExT,MAAM,CAAC;UAC5E+N,WAAW,CAAC/I,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;QAC9B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,OAAOzF,WAAW;EACtB;AACJ;AAEA,SAASoK,YAAYA,CAACtW,IAAI,EAAE4C,GAAG,EAAEuT,UAAU,EAAE;EACzC,OAAO,IAAII,gBAAgB,CAACvW,IAAI,EAAE4C,GAAG,EAAEuT,UAAU,CAAC;AACtD;AACA,MAAMI,gBAAgB,CAAC;EACnBvW,IAAI;EACJ4C,GAAG;EACH4T,WAAW;EACXC,mBAAmB,GAAG,EAAE;EACxBC,kBAAkB;EAClBlT,MAAM,gBAAG,IAAIN,GAAG,CAAC,CAAC;EAClBV,WAAWA,CAACxC,IAAI,EAAE4C,GAAG,EAAE4T,WAAW,EAAE;IAChC,IAAI,CAACxW,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC4C,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC4T,WAAW,GAAGA,WAAW;IAC9B5T,GAAG,CAACY,MAAM,CAAC/C,OAAO,CAAEmC,GAAG,IAAK;MACxB,MAAMsT,aAAa,GAAItT,GAAG,CAAC0B,OAAO,IAAI1B,GAAG,CAAC0B,OAAO,CAACK,MAAM,IAAK,CAAC,CAAC;MAC/D,IAAI,CAACnB,MAAM,CAACL,GAAG,CAACP,GAAG,CAAC5C,IAAI,EAAE,IAAIiW,oBAAoB,CAACrT,GAAG,CAAC5G,KAAK,EAAEka,aAAa,EAAEM,WAAW,CAAC,CAAC;IAC9F,CAAC,CAAC;IACFG,iBAAiB,CAAC,IAAI,CAACnT,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC;IAC3CmT,iBAAiB,CAAC,IAAI,CAACnT,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC;IAC5CZ,GAAG,CAACa,WAAW,CAAChD,OAAO,CAAEmC,GAAG,IAAK;MAC7B,IAAI,CAAC6T,mBAAmB,CAACtX,IAAI,CAAC,IAAIyU,0BAA0B,CAAC5T,IAAI,EAAE4C,GAAG,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC;IACzF,CAAC,CAAC;IACF,IAAI,CAACkT,kBAAkB,GAAGE,wBAAwB,CAAC5W,IAAI,EAAE,IAAI,CAACwD,MAAM,CAAC;EACzE;EACA,IAAIqT,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACjU,GAAG,CAACU,UAAU,GAAG,CAAC;EAClC;EACAwT,eAAeA,CAAC/C,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,EAAE;IACtD,MAAMoS,KAAK,GAAG,IAAI,CAACN,mBAAmB,CAACxM,IAAI,CAAE+M,CAAC,IAAKA,CAAC,CAAC/X,KAAK,CAAC8U,YAAY,EAAEC,SAAS,EAAEvX,OAAO,EAAEkI,MAAM,CAAC,CAAC;IACrG,OAAOoS,KAAK,IAAI,IAAI;EACxB;EACAE,WAAWA,CAAClD,YAAY,EAAEpP,MAAM,EAAExG,MAAM,EAAE;IACtC,OAAO,IAAI,CAACuY,kBAAkB,CAACxC,WAAW,CAACH,YAAY,EAAEpP,MAAM,EAAExG,MAAM,CAAC;EAC5E;AACJ;AACA,SAASyY,wBAAwBA,CAACtD,WAAW,EAAE9P,MAAM,EAAE2S,UAAU,EAAE;EAC/D,MAAMhR,QAAQ,GAAG,CAAC,CAACpE,SAAS,EAAEE,OAAO,KAAK,IAAI,CAAC;EAC/C,MAAMiE,SAAS,GAAG;IAAErB,IAAI,EAAE9H,qBAAqB,CAACwJ,QAAQ;IAAEC,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAK,CAAC;EACpF,MAAMH,UAAU,GAAG;IACfN,IAAI,EAAE9H,qBAAqB,CAACmI,UAAU;IACtCgB,SAAS;IACTC,QAAQ;IACRb,OAAO,EAAE,IAAI;IACbhB,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACd,CAAC;EACD,OAAO,IAAIqQ,0BAA0B,CAACN,WAAW,EAAEnP,UAAU,EAAEX,MAAM,CAAC;AAC1E;AACA,SAASmT,iBAAiBA,CAACO,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC7C,IAAIF,QAAQ,CAACnY,GAAG,CAACoY,IAAI,CAAC,EAAE;IACpB,IAAI,CAACD,QAAQ,CAACnY,GAAG,CAACqY,IAAI,CAAC,EAAE;MACrBF,QAAQ,CAAC/T,GAAG,CAACiU,IAAI,EAAEF,QAAQ,CAAC1P,GAAG,CAAC2P,IAAI,CAAC,CAAC;IAC1C;EACJ,CAAC,MACI,IAAID,QAAQ,CAACnY,GAAG,CAACqY,IAAI,CAAC,EAAE;IACzBF,QAAQ,CAAC/T,GAAG,CAACgU,IAAI,EAAED,QAAQ,CAAC1P,GAAG,CAAC4P,IAAI,CAAC,CAAC;EAC1C;AACJ;AAEA,MAAMC,qBAAqB,GAAG,eAAgB,IAAInM,qBAAqB,CAAC,CAAC;AACzE,MAAMoM,uBAAuB,CAAC;EAC1BC,QAAQ;EACRhV,OAAO;EACPiU,WAAW;EACXgB,WAAW,gBAAG,IAAItU,GAAG,CAAC,CAAC;EACvBuU,YAAY,gBAAG,IAAIvU,GAAG,CAAC,CAAC;EACxBwU,OAAO,GAAG,EAAE;EACZlV,WAAWA,CAAC+U,QAAQ,EAAEhV,OAAO,EAAEiU,WAAW,EAAE;IACxC,IAAI,CAACe,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiU,WAAW,GAAGA,WAAW;EAClC;EACAmB,QAAQA,CAACC,EAAE,EAAEzV,QAAQ,EAAE;IACnB,MAAMhE,MAAM,GAAG,EAAE;IACjB,MAAMkB,QAAQ,GAAG,EAAE;IACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAAC,IAAI,CAACM,OAAO,EAAEJ,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;IACvE,IAAIlB,MAAM,CAACe,MAAM,EAAE;MACf,MAAMnF,cAAc,CAACoE,MAAM,CAAC;IAChC,CAAC,MACI;MACD,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAIyB,QAAQ,CAACH,MAAM,EAAE;UACjBe,YAAY,CAACZ,QAAQ,CAAC;QAC1B;MACJ;MACA,IAAI,CAACmY,WAAW,CAACrU,GAAG,CAACyU,EAAE,EAAEhV,GAAG,CAAC;IACjC;EACJ;EACAiV,YAAYA,CAAC/O,CAAC,EAAEgP,SAAS,EAAEC,UAAU,EAAE;IACnC,MAAMtb,OAAO,GAAGqM,CAAC,CAACrM,OAAO;IACzB,MAAMM,SAAS,GAAG/C,kBAAkB,CAAC,IAAI,CAACwc,WAAW,EAAE1N,CAAC,CAAC/L,SAAS,EAAE+a,SAAS,EAAEC,UAAU,CAAC;IAC1F,OAAO,IAAI,CAACxV,OAAO,CAACzF,OAAO,CAACL,OAAO,EAAEM,SAAS,EAAE+L,CAAC,CAAC9L,QAAQ,EAAE8L,CAAC,CAAC7L,KAAK,EAAE6L,CAAC,CAAC5L,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;EAC5F;EACA8a,MAAMA,CAACJ,EAAE,EAAEnb,OAAO,EAAE6H,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAMnG,MAAM,GAAG,EAAE;IACjB,MAAMyE,GAAG,GAAG,IAAI,CAAC4U,WAAW,CAAChQ,GAAG,CAACoQ,EAAE,CAAC;IACpC,IAAIvM,YAAY;IAChB,MAAM4M,aAAa,GAAG,IAAI/U,GAAG,CAAC,CAAC;IAC/B,IAAIN,GAAG,EAAE;MACLyI,YAAY,GAAGQ,uBAAuB,CAAC,IAAI,CAACtJ,OAAO,EAAE9F,OAAO,EAAEmG,GAAG,EAAE1I,eAAe,EAAED,eAAe,EAAE,IAAIiJ,GAAG,CAAC,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAEoB,OAAO,EAAE+S,qBAAqB,EAAElZ,MAAM,CAAC;MAClKkN,YAAY,CAAC5K,OAAO,CAAEyX,IAAI,IAAK;QAC3B,MAAMzT,MAAM,GAAGnL,oBAAoB,CAAC2e,aAAa,EAAEC,IAAI,CAACzb,OAAO,EAAE,IAAIyG,GAAG,CAAC,CAAC,CAAC;QAC3EgV,IAAI,CAACnN,cAAc,CAACtK,OAAO,CAAEnE,IAAI,IAAKmI,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAE,IAAI,CAAC,CAAC;MACjE,CAAC,CAAC;IACN,CAAC,MACI;MACD6B,MAAM,CAACgB,IAAI,CAAChF,2BAA2B,CAAC,CAAC,CAAC;MAC1CkR,YAAY,GAAG,EAAE;IACrB;IACA,IAAIlN,MAAM,CAACe,MAAM,EAAE;MACf,MAAM9E,qBAAqB,CAAC+D,MAAM,CAAC;IACvC;IACA8Z,aAAa,CAACxX,OAAO,CAAC,CAACgE,MAAM,EAAEhI,OAAO,KAAK;MACvCgI,MAAM,CAAChE,OAAO,CAAC,CAAC0X,CAAC,EAAE7b,IAAI,KAAK;QACxBmI,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAE,IAAI,CAACiG,OAAO,CAAClH,YAAY,CAACoB,OAAO,EAAEH,IAAI,EAAEL,UAAU,CAAC,CAAC;MAC1E,CAAC,CAAC;IACN,CAAC,CAAC;IACF,MAAMyb,OAAO,GAAGrM,YAAY,CAAC5L,GAAG,CAAEqJ,CAAC,IAAK;MACpC,MAAMrE,MAAM,GAAGwT,aAAa,CAACzQ,GAAG,CAACsB,CAAC,CAACrM,OAAO,CAAC;MAC3C,OAAO,IAAI,CAACob,YAAY,CAAC/O,CAAC,EAAE,IAAI5F,GAAG,CAAC,CAAC,EAAEuB,MAAM,CAAC;IAClD,CAAC,CAAC;IACF,MAAM2T,MAAM,GAAG/d,mBAAmB,CAACqd,OAAO,CAAC;IAC3C,IAAI,CAACD,YAAY,CAACtU,GAAG,CAACyU,EAAE,EAAEQ,MAAM,CAAC;IACjCA,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,OAAO,CAACV,EAAE,CAAC,CAAC;IACxC,IAAI,CAACF,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;IACzB,OAAOA,MAAM;EACjB;EACAE,OAAOA,CAACV,EAAE,EAAE;IACR,MAAMQ,MAAM,GAAG,IAAI,CAACG,UAAU,CAACX,EAAE,CAAC;IAClCQ,MAAM,CAACE,OAAO,CAAC,CAAC;IAChB,IAAI,CAACb,YAAY,CAAChQ,MAAM,CAACmQ,EAAE,CAAC;IAC5B,MAAMY,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;IAC1C,IAAII,KAAK,IAAI,CAAC,EAAE;MACZ,IAAI,CAACd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACjC;EACJ;EACAD,UAAUA,CAACX,EAAE,EAAE;IACX,MAAMQ,MAAM,GAAG,IAAI,CAACX,YAAY,CAACjQ,GAAG,CAACoQ,EAAE,CAAC;IACxC,IAAI,CAACQ,MAAM,EAAE;MACT,MAAM9d,aAAa,CAACsd,EAAE,CAAC;IAC3B;IACA,OAAOQ,MAAM;EACjB;EACAM,MAAMA,CAACd,EAAE,EAAEnb,OAAO,EAAEkc,SAAS,EAAEC,QAAQ,EAAE;IACrC;IACA,MAAMC,SAAS,GAAGre,kBAAkB,CAACiC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzDlC,cAAc,CAAC,IAAI,CAACge,UAAU,CAACX,EAAE,CAAC,EAAEe,SAAS,EAAEE,SAAS,EAAED,QAAQ,CAAC;IACnE,OAAO,MAAM,CAAE,CAAC;EACpB;EACAE,OAAOA,CAAClB,EAAE,EAAEnb,OAAO,EAAEqc,OAAO,EAAEC,IAAI,EAAE;IAChC,IAAID,OAAO,IAAI,UAAU,EAAE;MACvB,IAAI,CAACnB,QAAQ,CAACC,EAAE,EAAEmB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1B;IACJ;IACA,IAAID,OAAO,IAAI,QAAQ,EAAE;MACrB,MAAMxU,OAAO,GAAIyU,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAE;MAC/B,IAAI,CAACf,MAAM,CAACJ,EAAE,EAAEnb,OAAO,EAAE6H,OAAO,CAAC;MACjC;IACJ;IACA,MAAM8T,MAAM,GAAG,IAAI,CAACG,UAAU,CAACX,EAAE,CAAC;IAClC,QAAQkB,OAAO;MACX,KAAK,MAAM;QACPV,MAAM,CAACY,IAAI,CAAC,CAAC;QACb;MACJ,KAAK,OAAO;QACRZ,MAAM,CAACa,KAAK,CAAC,CAAC;QACd;MACJ,KAAK,OAAO;QACRb,MAAM,CAACc,KAAK,CAAC,CAAC;QACd;MACJ,KAAK,SAAS;QACVd,MAAM,CAACe,OAAO,CAAC,CAAC;QAChB;MACJ,KAAK,QAAQ;QACTf,MAAM,CAACgB,MAAM,CAAC,CAAC;QACf;MACJ,KAAK,MAAM;QACPhB,MAAM,CAACiB,IAAI,CAAC,CAAC;QACb;MACJ,KAAK,aAAa;QACdjB,MAAM,CAACkB,WAAW,CAACjY,UAAU,CAAC0X,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACJ,KAAK,SAAS;QACV,IAAI,CAACT,OAAO,CAACV,EAAE,CAAC;QAChB;IACR;EACJ;AACJ;AAEA,MAAM2B,gBAAgB,GAAG,mBAAmB;AAC5C,MAAMC,eAAe,GAAG,oBAAoB;AAC5C,MAAMC,kBAAkB,GAAG,qBAAqB;AAChD,MAAMC,iBAAiB,GAAG,sBAAsB;AAChD,MAAMC,cAAc,GAAG,kBAAkB;AACzC,MAAMC,aAAa,GAAG,mBAAmB;AACzC,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,kBAAkB,GAAG;EACvBC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE,KAAK;EACpBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,0BAA0B,GAAG;EAC/BL,WAAW,EAAE,EAAE;EACfE,UAAU,EAAE,KAAK;EACjBD,aAAa,EAAE,KAAK;EACpBE,YAAY,EAAE,KAAK;EACnBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAME,YAAY,GAAG,cAAc;AACnC,MAAMC,UAAU,CAAC;EACbP,WAAW;EACXxb,KAAK;EACL+F,OAAO;EACP,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,OAAO,CAACK,MAAM;EAC9B;EACAnC,WAAWA,CAACiP,KAAK,EAAEsI,WAAW,GAAG,EAAE,EAAE;IACjC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,MAAMQ,KAAK,GAAG9I,KAAK,IAAIA,KAAK,CAAC1M,cAAc,CAAC,OAAO,CAAC;IACpD,MAAMxG,KAAK,GAAGgc,KAAK,GAAG9I,KAAK,CAAC,OAAO,CAAC,GAAGA,KAAK;IAC5C,IAAI,CAAClT,KAAK,GAAGic,qBAAqB,CAACjc,KAAK,CAAC;IACzC,IAAIgc,KAAK,EAAE;MACP;MACA,MAAM;QAAEhc,KAAK;QAAE,GAAG+F;MAAQ,CAAC,GAAGmN,KAAK;MACnC,IAAI,CAACnN,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAG,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACA,OAAO,CAACK,MAAM,EAAE;MACtB,IAAI,CAACL,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC;IAC5B;EACJ;EACA8V,aAAaA,CAACnW,OAAO,EAAE;IACnB,MAAMwL,SAAS,GAAGxL,OAAO,CAACK,MAAM;IAChC,IAAImL,SAAS,EAAE;MACX,MAAMG,SAAS,GAAG,IAAI,CAAC3L,OAAO,CAACK,MAAM;MACrCyC,MAAM,CAACrE,IAAI,CAAC+M,SAAS,CAAC,CAACrP,OAAO,CAAEnE,IAAI,IAAK;QACrC,IAAI2T,SAAS,CAAC3T,IAAI,CAAC,IAAI,IAAI,EAAE;UACzB2T,SAAS,CAAC3T,IAAI,CAAC,GAAGwT,SAAS,CAACxT,IAAI,CAAC;QACrC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACA,MAAMoe,UAAU,GAAG,MAAM;AACzB,MAAMC,mBAAmB,GAAG,eAAgB,IAAIL,UAAU,CAACI,UAAU,CAAC;AACtE,MAAME,4BAA4B,CAAC;EAC/BhD,EAAE;EACFiD,WAAW;EACXC,OAAO;EACPpD,OAAO,GAAG,EAAE;EACZqD,SAAS,gBAAG,IAAI7X,GAAG,CAAC,CAAC;EACrB8X,MAAM,GAAG,EAAE;EACXC,iBAAiB,gBAAG,IAAI/X,GAAG,CAAC,CAAC;EAC7BgY,cAAc;EACd1Y,WAAWA,CAACoV,EAAE,EAAEiD,WAAW,EAAEC,OAAO,EAAE;IAClC,IAAI,CAAClD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACiD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,cAAc,GAAG,SAAS,GAAGtD,EAAE;IACpCuD,QAAQ,CAACN,WAAW,EAAE,IAAI,CAACK,cAAc,CAAC;EAC9C;EACAxC,MAAMA,CAACjc,OAAO,EAAEuD,IAAI,EAAEob,KAAK,EAAExC,QAAQ,EAAE;IACnC,IAAI,CAAC,IAAI,CAACmC,SAAS,CAAChc,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC3B,MAAMnF,cAAc,CAACugB,KAAK,EAAEpb,IAAI,CAAC;IACrC;IACA,IAAIob,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAClc,MAAM,IAAI,CAAC,EAAE;MACpC,MAAMpE,YAAY,CAACkF,IAAI,CAAC;IAC5B;IACA,IAAI,CAACqb,mBAAmB,CAACD,KAAK,CAAC,EAAE;MAC7B,MAAMrgB,uBAAuB,CAACqgB,KAAK,EAAEpb,IAAI,CAAC;IAC9C;IACA,MAAMsb,SAAS,GAAGhiB,oBAAoB,CAAC,IAAI,CAAC2hB,iBAAiB,EAAExe,OAAO,EAAE,EAAE,CAAC;IAC3E,MAAM8e,IAAI,GAAG;MAAEvb,IAAI;MAAEob,KAAK;MAAExC;IAAS,CAAC;IACtC0C,SAAS,CAACnc,IAAI,CAACoc,IAAI,CAAC;IACpB,MAAMC,kBAAkB,GAAGliB,oBAAoB,CAAC,IAAI,CAACwhB,OAAO,CAACW,eAAe,EAAEhf,OAAO,EAAE,IAAIyG,GAAG,CAAC,CAAC,CAAC;IACjG,IAAI,CAACsY,kBAAkB,CAACzc,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC/Bmb,QAAQ,CAAC1e,OAAO,EAAEzB,oBAAoB,CAAC;MACvCmgB,QAAQ,CAAC1e,OAAO,EAAEzB,oBAAoB,GAAG,GAAG,GAAGgF,IAAI,CAAC;MACpDwb,kBAAkB,CAACrY,GAAG,CAACnD,IAAI,EAAE2a,mBAAmB,CAAC;IACrD;IACA,OAAO,MAAM;MACT;MACA;MACA;MACA,IAAI,CAACG,OAAO,CAACY,UAAU,CAAC,MAAM;QAC1B,MAAMlD,KAAK,GAAG8C,SAAS,CAAC5T,OAAO,CAAC6T,IAAI,CAAC;QACrC,IAAI/C,KAAK,IAAI,CAAC,EAAE;UACZ8C,SAAS,CAAC7C,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAC9B;QACA,IAAI,CAAC,IAAI,CAACuC,SAAS,CAAChc,GAAG,CAACiB,IAAI,CAAC,EAAE;UAC3Bwb,kBAAkB,CAAC/T,MAAM,CAACzH,IAAI,CAAC;QACnC;MACJ,CAAC,CAAC;IACN,CAAC;EACL;EACA2X,QAAQA,CAAC3X,IAAI,EAAE4C,GAAG,EAAE;IAChB,IAAI,IAAI,CAACmY,SAAS,CAAChc,GAAG,CAACiB,IAAI,CAAC,EAAE;MAC1B;MACA,OAAO,KAAK;IAChB,CAAC,MACI;MACD,IAAI,CAAC+a,SAAS,CAAC5X,GAAG,CAACnD,IAAI,EAAE4C,GAAG,CAAC;MAC7B,OAAO,IAAI;IACf;EACJ;EACA+Y,WAAWA,CAAC3b,IAAI,EAAE;IACd,MAAM4b,OAAO,GAAG,IAAI,CAACb,SAAS,CAACvT,GAAG,CAACxH,IAAI,CAAC;IACxC,IAAI,CAAC4b,OAAO,EAAE;MACV,MAAM3gB,mBAAmB,CAAC+E,IAAI,CAAC;IACnC;IACA,OAAO4b,OAAO;EAClB;EACAA,OAAOA,CAACnf,OAAO,EAAE6W,WAAW,EAAE/U,KAAK,EAAEsd,iBAAiB,GAAG,IAAI,EAAE;IAC3D,MAAMD,OAAO,GAAG,IAAI,CAACD,WAAW,CAACrI,WAAW,CAAC;IAC7C,MAAM8E,MAAM,GAAG,IAAI0D,yBAAyB,CAAC,IAAI,CAAClE,EAAE,EAAEtE,WAAW,EAAE7W,OAAO,CAAC;IAC3E,IAAI+e,kBAAkB,GAAG,IAAI,CAACV,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAC/K,OAAO,CAAC;IAClE,IAAI,CAAC+e,kBAAkB,EAAE;MACrBL,QAAQ,CAAC1e,OAAO,EAAEzB,oBAAoB,CAAC;MACvCmgB,QAAQ,CAAC1e,OAAO,EAAEzB,oBAAoB,GAAG,GAAG,GAAGsY,WAAW,CAAC;MAC3D,IAAI,CAACwH,OAAO,CAACW,eAAe,CAACtY,GAAG,CAAC1G,OAAO,EAAG+e,kBAAkB,GAAG,IAAItY,GAAG,CAAC,CAAE,CAAC;IAC/E;IACA,IAAInC,SAAS,GAAGya,kBAAkB,CAAChU,GAAG,CAAC8L,WAAW,CAAC;IACnD,MAAMrS,OAAO,GAAG,IAAIqZ,UAAU,CAAC/b,KAAK,EAAE,IAAI,CAACqZ,EAAE,CAAC;IAC9C,MAAM2C,KAAK,GAAGhc,KAAK,IAAIA,KAAK,CAACwG,cAAc,CAAC,OAAO,CAAC;IACpD,IAAI,CAACwV,KAAK,IAAIxZ,SAAS,EAAE;MACrBE,OAAO,CAACwZ,aAAa,CAAC1Z,SAAS,CAACuD,OAAO,CAAC;IAC5C;IACAkX,kBAAkB,CAACrY,GAAG,CAACmQ,WAAW,EAAErS,OAAO,CAAC;IAC5C,IAAI,CAACF,SAAS,EAAE;MACZA,SAAS,GAAG4Z,mBAAmB;IACnC;IACA,MAAM3F,SAAS,GAAG/T,OAAO,CAAC1C,KAAK,KAAKmc,UAAU;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC1F,SAAS,IAAIjU,SAAS,CAACxC,KAAK,KAAK0C,OAAO,CAAC1C,KAAK,EAAE;MACjD;MACA;MACA,IAAI,CAACwd,SAAS,CAAChb,SAAS,CAAC4D,MAAM,EAAE1D,OAAO,CAAC0D,MAAM,CAAC,EAAE;QAC9C,MAAMxG,MAAM,GAAG,EAAE;QACjB,MAAMqV,UAAU,GAAGoI,OAAO,CAAC3E,WAAW,CAAClW,SAAS,CAACxC,KAAK,EAAEwC,SAAS,CAAC4D,MAAM,EAAExG,MAAM,CAAC;QACjF,MAAMsV,QAAQ,GAAGmI,OAAO,CAAC3E,WAAW,CAAChW,OAAO,CAAC1C,KAAK,EAAE0C,OAAO,CAAC0D,MAAM,EAAExG,MAAM,CAAC;QAC3E,IAAIA,MAAM,CAACe,MAAM,EAAE;UACf,IAAI,CAAC4b,OAAO,CAACkB,WAAW,CAAC7d,MAAM,CAAC;QACpC,CAAC,MACI;UACD,IAAI,CAAC2c,OAAO,CAACY,UAAU,CAAC,MAAM;YAC1BhhB,WAAW,CAAC+B,OAAO,EAAE+W,UAAU,CAAC;YAChC7Y,SAAS,CAAC8B,OAAO,EAAEgX,QAAQ,CAAC;UAChC,CAAC,CAAC;QACN;MACJ;MACA;IACJ;IACA,MAAMwI,gBAAgB,GAAG3iB,oBAAoB,CAAC,IAAI,CAACwhB,OAAO,CAACoB,gBAAgB,EAAEzf,OAAO,EAAE,EAAE,CAAC;IACzFwf,gBAAgB,CAACxb,OAAO,CAAE2X,MAAM,IAAK;MACjC;MACA;MACA;MACA;MACA,IAAIA,MAAM,CAAC2B,WAAW,IAAI,IAAI,CAACnC,EAAE,IAAIQ,MAAM,CAAC9E,WAAW,IAAIA,WAAW,IAAI8E,MAAM,CAAC+D,MAAM,EAAE;QACrF/D,MAAM,CAACE,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;IACF,IAAInU,UAAU,GAAGyX,OAAO,CAAC9E,eAAe,CAAC/V,SAAS,CAACxC,KAAK,EAAE0C,OAAO,CAAC1C,KAAK,EAAE9B,OAAO,EAAEwE,OAAO,CAAC0D,MAAM,CAAC;IACjG,IAAIyX,oBAAoB,GAAG,KAAK;IAChC,IAAI,CAACjY,UAAU,EAAE;MACb,IAAI,CAAC0X,iBAAiB,EAClB;MACJ1X,UAAU,GAAGyX,OAAO,CAAClF,kBAAkB;MACvC0F,oBAAoB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACtB,OAAO,CAACuB,kBAAkB,EAAE;IACjC,IAAI,CAACrB,MAAM,CAAC7b,IAAI,CAAC;MACb1C,OAAO;MACP6W,WAAW;MACXnP,UAAU;MACVpD,SAAS;MACTE,OAAO;MACPmX,MAAM;MACNgE;IACJ,CAAC,CAAC;IACF,IAAI,CAACA,oBAAoB,EAAE;MACvBjB,QAAQ,CAAC1e,OAAO,EAAE8c,gBAAgB,CAAC;MACnCnB,MAAM,CAACkE,OAAO,CAAC,MAAM;QACjBC,WAAW,CAAC9f,OAAO,EAAE8c,gBAAgB,CAAC;MAC1C,CAAC,CAAC;IACN;IACAnB,MAAM,CAACoE,MAAM,CAAC,MAAM;MAChB,IAAIhE,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;MACxC,IAAII,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACjC;MACA,MAAMd,OAAO,GAAG,IAAI,CAACoD,OAAO,CAACoB,gBAAgB,CAAC1U,GAAG,CAAC/K,OAAO,CAAC;MAC1D,IAAIib,OAAO,EAAE;QACT,IAAIc,KAAK,GAAGd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;QACnC,IAAII,KAAK,IAAI,CAAC,EAAE;UACZd,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACd,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;IACzB6D,gBAAgB,CAAC9c,IAAI,CAACiZ,MAAM,CAAC;IAC7B,OAAOA,MAAM;EACjB;EACAqE,UAAUA,CAACzc,IAAI,EAAE;IACb,IAAI,CAAC+a,SAAS,CAACtT,MAAM,CAACzH,IAAI,CAAC;IAC3B,IAAI,CAAC8a,OAAO,CAACW,eAAe,CAAChb,OAAO,CAAEyW,QAAQ,IAAKA,QAAQ,CAACzP,MAAM,CAACzH,IAAI,CAAC,CAAC;IACzE,IAAI,CAACib,iBAAiB,CAACxa,OAAO,CAAC,CAAC6a,SAAS,EAAE7e,OAAO,KAAK;MACnD,IAAI,CAACwe,iBAAiB,CAAC9X,GAAG,CAAC1G,OAAO,EAAE6e,SAAS,CAAC/b,MAAM,CAAEwX,KAAK,IAAK;QAC5D,OAAOA,KAAK,CAAC/W,IAAI,IAAIA,IAAI;MAC7B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACA0c,iBAAiBA,CAACjgB,OAAO,EAAE;IACvB,IAAI,CAACqe,OAAO,CAACW,eAAe,CAAChU,MAAM,CAAChL,OAAO,CAAC;IAC5C,IAAI,CAACwe,iBAAiB,CAACxT,MAAM,CAAChL,OAAO,CAAC;IACtC,MAAMkgB,cAAc,GAAG,IAAI,CAAC7B,OAAO,CAACoB,gBAAgB,CAAC1U,GAAG,CAAC/K,OAAO,CAAC;IACjE,IAAIkgB,cAAc,EAAE;MAChBA,cAAc,CAAClc,OAAO,CAAE2X,MAAM,IAAKA,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC;MACpD,IAAI,CAACwC,OAAO,CAACoB,gBAAgB,CAACzU,MAAM,CAAChL,OAAO,CAAC;IACjD;EACJ;EACAmgB,8BAA8BA,CAAC9Q,WAAW,EAAErJ,OAAO,EAAE;IACjD,MAAMkO,QAAQ,GAAG,IAAI,CAACmK,OAAO,CAAC5Y,MAAM,CAACxF,KAAK,CAACoP,WAAW,EAAErS,mBAAmB,EAAE,IAAI,CAAC;IAClF;IACA;IACA;IACAkX,QAAQ,CAAClQ,OAAO,CAAE0U,GAAG,IAAK;MACtB;MACA;MACA,IAAIA,GAAG,CAACkF,YAAY,CAAC,EACjB;MACJ,MAAMwC,UAAU,GAAG,IAAI,CAAC/B,OAAO,CAACgC,wBAAwB,CAAC3H,GAAG,CAAC;MAC7D,IAAI0H,UAAU,CAAC/Z,IAAI,EAAE;QACjB+Z,UAAU,CAACpc,OAAO,CAAEsc,EAAE,IAAKA,EAAE,CAACC,qBAAqB,CAAC7H,GAAG,EAAE1S,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;MACnF,CAAC,MACI;QACD,IAAI,CAACia,iBAAiB,CAACvH,GAAG,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC2F,OAAO,CAACmC,wBAAwB,CAAC,MAAMtM,QAAQ,CAAClQ,OAAO,CAAE0U,GAAG,IAAK,IAAI,CAACuH,iBAAiB,CAACvH,GAAG,CAAC,CAAC,CAAC;EACvG;EACA6H,qBAAqBA,CAACvgB,OAAO,EAAEgG,OAAO,EAAEya,oBAAoB,EAAErB,iBAAiB,EAAE;IAC7E,MAAMsB,aAAa,GAAG,IAAI,CAACrC,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAC/K,OAAO,CAAC;IAC/D,MAAM2gB,sBAAsB,GAAG,IAAIla,GAAG,CAAC,CAAC;IACxC,IAAIia,aAAa,EAAE;MACf,MAAMzF,OAAO,GAAG,EAAE;MAClByF,aAAa,CAAC1c,OAAO,CAAC,CAAC4c,KAAK,EAAE/J,WAAW,KAAK;QAC1C8J,sBAAsB,CAACja,GAAG,CAACmQ,WAAW,EAAE+J,KAAK,CAAC9e,KAAK,CAAC;QACpD;QACA;QACA,IAAI,IAAI,CAACwc,SAAS,CAAChc,GAAG,CAACuU,WAAW,CAAC,EAAE;UACjC,MAAM8E,MAAM,GAAG,IAAI,CAACwD,OAAO,CAACnf,OAAO,EAAE6W,WAAW,EAAEoH,UAAU,EAAEmB,iBAAiB,CAAC;UAChF,IAAIzD,MAAM,EAAE;YACRV,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;UACxB;QACJ;MACJ,CAAC,CAAC;MACF,IAAIV,OAAO,CAACxY,MAAM,EAAE;QAChB,IAAI,CAAC4b,OAAO,CAACwC,oBAAoB,CAAC,IAAI,CAAC1F,EAAE,EAAEnb,OAAO,EAAE,IAAI,EAAEgG,OAAO,EAAE2a,sBAAsB,CAAC;QAC1F,IAAIF,oBAAoB,EAAE;UACtB7iB,mBAAmB,CAACqd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAM,IAAI,CAAC1B,OAAO,CAACyC,gBAAgB,CAAC9gB,OAAO,CAAC,CAAC;QACrF;QACA,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACA+gB,8BAA8BA,CAAC/gB,OAAO,EAAE;IACpC,MAAM6e,SAAS,GAAG,IAAI,CAACL,iBAAiB,CAACzT,GAAG,CAAC/K,OAAO,CAAC;IACrD,MAAMghB,aAAa,GAAG,IAAI,CAAC3C,OAAO,CAACW,eAAe,CAACjU,GAAG,CAAC/K,OAAO,CAAC;IAC/D;IACA;IACA,IAAI6e,SAAS,IAAImC,aAAa,EAAE;MAC5B,MAAMC,eAAe,GAAG,IAAIjf,GAAG,CAAC,CAAC;MACjC6c,SAAS,CAAC7a,OAAO,CAAEkd,QAAQ,IAAK;QAC5B,MAAMrK,WAAW,GAAGqK,QAAQ,CAAC3d,IAAI;QACjC,IAAI0d,eAAe,CAAC3e,GAAG,CAACuU,WAAW,CAAC,EAChC;QACJoK,eAAe,CAAC1Y,GAAG,CAACsO,WAAW,CAAC;QAChC,MAAMsI,OAAO,GAAG,IAAI,CAACb,SAAS,CAACvT,GAAG,CAAC8L,WAAW,CAAC;QAC/C,MAAMnP,UAAU,GAAGyX,OAAO,CAAClF,kBAAkB;QAC7C,MAAM3V,SAAS,GAAG0c,aAAa,CAACjW,GAAG,CAAC8L,WAAW,CAAC,IAAIqH,mBAAmB;QACvE,MAAM1Z,OAAO,GAAG,IAAIqZ,UAAU,CAACI,UAAU,CAAC;QAC1C,MAAMtC,MAAM,GAAG,IAAI0D,yBAAyB,CAAC,IAAI,CAAClE,EAAE,EAAEtE,WAAW,EAAE7W,OAAO,CAAC;QAC3E,IAAI,CAACqe,OAAO,CAACuB,kBAAkB,EAAE;QACjC,IAAI,CAACrB,MAAM,CAAC7b,IAAI,CAAC;UACb1C,OAAO;UACP6W,WAAW;UACXnP,UAAU;UACVpD,SAAS;UACTE,OAAO;UACPmX,MAAM;UACNgE,oBAAoB,EAAE;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACAwB,UAAUA,CAACnhB,OAAO,EAAEgG,OAAO,EAAE;IACzB,MAAMob,MAAM,GAAG,IAAI,CAAC/C,OAAO;IAC3B,IAAIre,OAAO,CAACqhB,iBAAiB,EAAE;MAC3B,IAAI,CAAClB,8BAA8B,CAACngB,OAAO,EAAEgG,OAAO,CAAC;IACzD;IACA;IACA,IAAI,IAAI,CAACua,qBAAqB,CAACvgB,OAAO,EAAEgG,OAAO,EAAE,IAAI,CAAC,EAClD;IACJ;IACA;IACA,IAAIsb,iCAAiC,GAAG,KAAK;IAC7C,IAAIF,MAAM,CAACG,eAAe,EAAE;MACxB,MAAMC,cAAc,GAAGJ,MAAM,CAACnG,OAAO,CAACxY,MAAM,GACtC2e,MAAM,CAACK,uBAAuB,CAAC1W,GAAG,CAAC/K,OAAO,CAAC,GAC3C,EAAE;MACR;MACA;MACA;MACA;MACA,IAAIwhB,cAAc,IAAIA,cAAc,CAAC/e,MAAM,EAAE;QACzC6e,iCAAiC,GAAG,IAAI;MAC5C,CAAC,MACI;QACD,IAAII,MAAM,GAAG1hB,OAAO;QACpB,OAAQ0hB,MAAM,GAAGA,MAAM,CAACC,UAAU,EAAG;UACjC,MAAMC,QAAQ,GAAGR,MAAM,CAACpC,eAAe,CAACjU,GAAG,CAAC2W,MAAM,CAAC;UACnD,IAAIE,QAAQ,EAAE;YACVN,iCAAiC,GAAG,IAAI;YACxC;UACJ;QACJ;MACJ;IACJ;IACA;IACA;IACA;IACA;IACA,IAAI,CAACP,8BAA8B,CAAC/gB,OAAO,CAAC;IAC5C;IACA;IACA,IAAIshB,iCAAiC,EAAE;MACnCF,MAAM,CAACP,oBAAoB,CAAC,IAAI,CAAC1F,EAAE,EAAEnb,OAAO,EAAE,KAAK,EAAEgG,OAAO,CAAC;IACjE,CAAC,MACI;MACD,MAAM6b,WAAW,GAAG7hB,OAAO,CAAC4d,YAAY,CAAC;MACzC,IAAI,CAACiE,WAAW,IAAIA,WAAW,KAAKxE,kBAAkB,EAAE;QACpD;QACA;QACA+D,MAAM,CAACnC,UAAU,CAAC,MAAM,IAAI,CAACgB,iBAAiB,CAACjgB,OAAO,CAAC,CAAC;QACxDohB,MAAM,CAACU,sBAAsB,CAAC9hB,OAAO,CAAC;QACtCohB,MAAM,CAACW,kBAAkB,CAAC/hB,OAAO,EAAEgG,OAAO,CAAC;MAC/C;IACJ;EACJ;EACAgc,UAAUA,CAAChiB,OAAO,EAAE0hB,MAAM,EAAE;IACxBhD,QAAQ,CAAC1e,OAAO,EAAE,IAAI,CAACye,cAAc,CAAC;EAC1C;EACAwD,sBAAsBA,CAACC,WAAW,EAAE;IAChC,MAAMtT,YAAY,GAAG,EAAE;IACvB,IAAI,CAAC2P,MAAM,CAACva,OAAO,CAAEsW,KAAK,IAAK;MAC3B,MAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAM;MAC3B,IAAIA,MAAM,CAACwG,SAAS,EAChB;MACJ,MAAMniB,OAAO,GAAGsa,KAAK,CAACta,OAAO;MAC7B,MAAM6e,SAAS,GAAG,IAAI,CAACL,iBAAiB,CAACzT,GAAG,CAAC/K,OAAO,CAAC;MACrD,IAAI6e,SAAS,EAAE;QACXA,SAAS,CAAC7a,OAAO,CAAEkd,QAAQ,IAAK;UAC5B,IAAIA,QAAQ,CAAC3d,IAAI,IAAI+W,KAAK,CAACzD,WAAW,EAAE;YACpC,MAAMuF,SAAS,GAAGre,kBAAkB,CAACiC,OAAO,EAAEsa,KAAK,CAACzD,WAAW,EAAEyD,KAAK,CAAChW,SAAS,CAACxC,KAAK,EAAEwY,KAAK,CAAC9V,OAAO,CAAC1C,KAAK,CAAC;YAC5Gsa,SAAS,CAAC,OAAO,CAAC,GAAG8F,WAAW;YAChCpkB,cAAc,CAACwc,KAAK,CAACqB,MAAM,EAAEuF,QAAQ,CAACvC,KAAK,EAAEvC,SAAS,EAAE8E,QAAQ,CAAC/E,QAAQ,CAAC;UAC9E;QACJ,CAAC,CAAC;MACN;MACA,IAAIR,MAAM,CAACyG,gBAAgB,EAAE;QACzB,IAAI,CAAC/D,OAAO,CAACY,UAAU,CAAC,MAAM;UAC1B;UACA;UACAtD,MAAM,CAACE,OAAO,CAAC,CAAC;QACpB,CAAC,CAAC;MACN,CAAC,MACI;QACDjN,YAAY,CAAClM,IAAI,CAAC4X,KAAK,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,CAACiE,MAAM,GAAG,EAAE;IAChB,OAAO3P,YAAY,CAACyT,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B;MACA;MACA,MAAMC,EAAE,GAAGF,CAAC,CAAC5a,UAAU,CAACvB,GAAG,CAACW,QAAQ;MACpC,MAAM2b,EAAE,GAAGF,CAAC,CAAC7a,UAAU,CAACvB,GAAG,CAACW,QAAQ;MACpC,IAAI0b,EAAE,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,EAAE;QACpB,OAAOD,EAAE,GAAGC,EAAE;MAClB;MACA,OAAO,IAAI,CAACpE,OAAO,CAAC5Y,MAAM,CAAChK,eAAe,CAAC6mB,CAAC,CAACtiB,OAAO,EAAEuiB,CAAC,CAACviB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7E,CAAC,CAAC;EACN;EACA6b,OAAOA,CAAC7V,OAAO,EAAE;IACb,IAAI,CAACiV,OAAO,CAACjX,OAAO,CAAE0e,CAAC,IAAKA,CAAC,CAAC7G,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,CAACsE,8BAA8B,CAAC,IAAI,CAAC/B,WAAW,EAAEpY,OAAO,CAAC;EAClE;AACJ;AACA,MAAM2c,yBAAyB,CAAC;EAC5B7H,QAAQ;EACRrV,MAAM;EACNsU,WAAW;EACXkB,OAAO,GAAG,EAAE;EACZ2H,eAAe,gBAAG,IAAInc,GAAG,CAAC,CAAC;EAC3BgZ,gBAAgB,gBAAG,IAAIhZ,GAAG,CAAC,CAAC;EAC5Bgb,uBAAuB,gBAAG,IAAIhb,GAAG,CAAC,CAAC;EACnCuY,eAAe,gBAAG,IAAIvY,GAAG,CAAC,CAAC;EAC3Boc,aAAa,gBAAG,IAAI7gB,GAAG,CAAC,CAAC;EACzBuf,eAAe,GAAG,CAAC;EACnB3B,kBAAkB,GAAG,CAAC;EACtBkD,gBAAgB,GAAG,CAAC,CAAC;EACrBC,cAAc,GAAG,EAAE;EACnBC,SAAS,GAAG,EAAE;EACdC,aAAa,GAAG,EAAE;EAClBC,uBAAuB,gBAAG,IAAIzc,GAAG,CAAC,CAAC;EACnC0c,sBAAsB,GAAG,EAAE;EAC3BC,sBAAsB,GAAG,EAAE;EAC3B;EACAC,iBAAiB,GAAGA,CAACrjB,OAAO,EAAEgG,OAAO,KAAK,CAAE,CAAC;EAC7C;EACA+b,kBAAkBA,CAAC/hB,OAAO,EAAEgG,OAAO,EAAE;IACjC,IAAI,CAACqd,iBAAiB,CAACrjB,OAAO,EAAEgG,OAAO,CAAC;EAC5C;EACAD,WAAWA,CAAC+U,QAAQ,EAAErV,MAAM,EAAEsU,WAAW,EAAE;IACvC,IAAI,CAACe,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACrV,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACsU,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIuJ,aAAaA,CAAA,EAAG;IAChB,MAAMrI,OAAO,GAAG,EAAE;IAClB,IAAI,CAAC8H,cAAc,CAAC/e,OAAO,CAAEsc,EAAE,IAAK;MAChCA,EAAE,CAACrF,OAAO,CAACjX,OAAO,CAAE2X,MAAM,IAAK;QAC3B,IAAIA,MAAM,CAAC+D,MAAM,EAAE;UACfzE,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;QACxB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOV,OAAO;EAClB;EACAsI,eAAeA,CAACjG,WAAW,EAAEc,WAAW,EAAE;IACtC,MAAMkC,EAAE,GAAG,IAAInC,4BAA4B,CAACb,WAAW,EAAEc,WAAW,EAAE,IAAI,CAAC;IAC3E,IAAI,IAAI,CAACtD,QAAQ,IAAI,IAAI,CAACrV,MAAM,CAAChK,eAAe,CAAC,IAAI,CAACqf,QAAQ,EAAEsD,WAAW,CAAC,EAAE;MAC1E,IAAI,CAACoF,qBAAqB,CAAClD,EAAE,EAAElC,WAAW,CAAC;IAC/C,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACwE,eAAe,CAAClc,GAAG,CAAC0X,WAAW,EAAEkC,EAAE,CAAC;MACzC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACmD,mBAAmB,CAACrF,WAAW,CAAC;IACzC;IACA,OAAQ,IAAI,CAAC0E,gBAAgB,CAACxF,WAAW,CAAC,GAAGgD,EAAE;EACnD;EACAkD,qBAAqBA,CAAClD,EAAE,EAAElC,WAAW,EAAE;IACnC,MAAMsF,aAAa,GAAG,IAAI,CAACX,cAAc;IACzC,MAAMG,uBAAuB,GAAG,IAAI,CAACA,uBAAuB;IAC5D,MAAMhX,KAAK,GAAGwX,aAAa,CAACjhB,MAAM,GAAG,CAAC;IACtC,IAAIyJ,KAAK,IAAI,CAAC,EAAE;MACZ,IAAIyX,KAAK,GAAG,KAAK;MACjB;MACA;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACne,MAAM,CAAC/J,gBAAgB,CAAC0iB,WAAW,CAAC;MACxD,OAAOwF,QAAQ,EAAE;QACb,MAAMC,UAAU,GAAGX,uBAAuB,CAACnY,GAAG,CAAC6Y,QAAQ,CAAC;QACxD,IAAIC,UAAU,EAAE;UACZ;UACA;UACA,MAAM9H,KAAK,GAAG2H,aAAa,CAACzY,OAAO,CAAC4Y,UAAU,CAAC;UAC/CH,aAAa,CAAC1H,MAAM,CAACD,KAAK,GAAG,CAAC,EAAE,CAAC,EAAEuE,EAAE,CAAC;UACtCqD,KAAK,GAAG,IAAI;UACZ;QACJ;QACAC,QAAQ,GAAG,IAAI,CAACne,MAAM,CAAC/J,gBAAgB,CAACkoB,QAAQ,CAAC;MACrD;MACA,IAAI,CAACD,KAAK,EAAE;QACR;QACA;QACA;QACAD,aAAa,CAACI,OAAO,CAACxD,EAAE,CAAC;MAC7B;IACJ,CAAC,MACI;MACDoD,aAAa,CAAChhB,IAAI,CAAC4d,EAAE,CAAC;IAC1B;IACA4C,uBAAuB,CAACxc,GAAG,CAAC0X,WAAW,EAAEkC,EAAE,CAAC;IAC5C,OAAOA,EAAE;EACb;EACApF,QAAQA,CAACoC,WAAW,EAAEc,WAAW,EAAE;IAC/B,IAAIkC,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAACxF,WAAW,CAAC;IAC3C,IAAI,CAACgD,EAAE,EAAE;MACLA,EAAE,GAAG,IAAI,CAACiD,eAAe,CAACjG,WAAW,EAAEc,WAAW,CAAC;IACvD;IACA,OAAOkC,EAAE;EACb;EACAyD,eAAeA,CAACzG,WAAW,EAAE/Z,IAAI,EAAE4b,OAAO,EAAE;IACxC,IAAImB,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAACxF,WAAW,CAAC;IAC3C,IAAIgD,EAAE,IAAIA,EAAE,CAACpF,QAAQ,CAAC3X,IAAI,EAAE4b,OAAO,CAAC,EAAE;MAClC,IAAI,CAACoC,eAAe,EAAE;IAC1B;EACJ;EACA1F,OAAOA,CAACyB,WAAW,EAAEtX,OAAO,EAAE;IAC1B,IAAI,CAACsX,WAAW,EACZ;IACJ,IAAI,CAAC2B,UAAU,CAAC,MAAM,CAAE,CAAC,CAAC;IAC1B,IAAI,CAACuB,wBAAwB,CAAC,MAAM;MAChC,MAAMF,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C,IAAI,CAAC4F,uBAAuB,CAAClY,MAAM,CAACsV,EAAE,CAAClC,WAAW,CAAC;MACnD,MAAMrC,KAAK,GAAG,IAAI,CAACgH,cAAc,CAAC9X,OAAO,CAACqV,EAAE,CAAC;MAC7C,IAAIvE,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACgH,cAAc,CAAC/G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACxC;MACAuE,EAAE,CAACzE,OAAO,CAAC7V,OAAO,CAAC;MACnB,OAAO,IAAI,CAAC8c,gBAAgB,CAACxF,WAAW,CAAC;IAC7C,CAAC,CAAC;EACN;EACA0G,eAAeA,CAAC7I,EAAE,EAAE;IAChB,OAAO,IAAI,CAAC2H,gBAAgB,CAAC3H,EAAE,CAAC;EACpC;EACAkF,wBAAwBA,CAACrgB,OAAO,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA,MAAMogB,UAAU,GAAG,IAAIpe,GAAG,CAAC,CAAC;IAC5B,MAAMgf,aAAa,GAAG,IAAI,CAAChC,eAAe,CAACjU,GAAG,CAAC/K,OAAO,CAAC;IACvD,IAAIghB,aAAa,EAAE;MACf,KAAK,IAAIiD,UAAU,IAAIjD,aAAa,CAACxY,MAAM,CAAC,CAAC,EAAE;QAC3C,IAAIyb,UAAU,CAAC3G,WAAW,EAAE;UACxB,MAAMgD,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAACC,UAAU,CAAC3G,WAAW,CAAC;UACvD,IAAIgD,EAAE,EAAE;YACJF,UAAU,CAAC7X,GAAG,CAAC+X,EAAE,CAAC;UACtB;QACJ;MACJ;IACJ;IACA,OAAOF,UAAU;EACrB;EACAjB,OAAOA,CAAC7B,WAAW,EAAEtd,OAAO,EAAEuD,IAAI,EAAEzB,KAAK,EAAE;IACvC,IAAIoiB,aAAa,CAAClkB,OAAO,CAAC,EAAE;MACxB,MAAMsgB,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAACnB,OAAO,CAACnf,OAAO,EAAEuD,IAAI,EAAEzB,KAAK,CAAC;QAChC,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAkgB,UAAUA,CAAC1E,WAAW,EAAEtd,OAAO,EAAE0hB,MAAM,EAAEyC,YAAY,EAAE;IACnD,IAAI,CAACD,aAAa,CAAClkB,OAAO,CAAC,EACvB;IACJ;IACA;IACA,MAAMokB,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;IACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;MAClC6G,OAAO,CAAC7G,aAAa,GAAG,KAAK;MAC7B6G,OAAO,CAAC5G,UAAU,GAAG,IAAI;MACzB,MAAMzB,KAAK,GAAG,IAAI,CAACqH,sBAAsB,CAACnY,OAAO,CAACjL,OAAO,CAAC;MAC1D,IAAI+b,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACqH,sBAAsB,CAACpH,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAChD;IACJ;IACA;IACA;IACA;IACA,IAAIuB,WAAW,EAAE;MACb,MAAMgD,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAAC1G,WAAW,CAAC;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAAC0B,UAAU,CAAChiB,OAAO,EAAE0hB,MAAM,CAAC;MAClC;IACJ;IACA;IACA,IAAIyC,YAAY,EAAE;MACd,IAAI,CAACV,mBAAmB,CAACzjB,OAAO,CAAC;IACrC;EACJ;EACAyjB,mBAAmBA,CAACzjB,OAAO,EAAE;IACzB,IAAI,CAACmjB,sBAAsB,CAACzgB,IAAI,CAAC1C,OAAO,CAAC;EAC7C;EACAqkB,qBAAqBA,CAACrkB,OAAO,EAAE8B,KAAK,EAAE;IAClC,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC,IAAI,CAAC+gB,aAAa,CAACvgB,GAAG,CAACtC,OAAO,CAAC,EAAE;QAClC,IAAI,CAAC6iB,aAAa,CAACta,GAAG,CAACvI,OAAO,CAAC;QAC/B0e,QAAQ,CAAC1e,OAAO,EAAEgd,kBAAkB,CAAC;MACzC;IACJ,CAAC,MACI,IAAI,IAAI,CAAC6F,aAAa,CAACvgB,GAAG,CAACtC,OAAO,CAAC,EAAE;MACtC,IAAI,CAAC6iB,aAAa,CAAC7X,MAAM,CAAChL,OAAO,CAAC;MAClC8f,WAAW,CAAC9f,OAAO,EAAEgd,kBAAkB,CAAC;IAC5C;EACJ;EACAmE,UAAUA,CAAC7D,WAAW,EAAEtd,OAAO,EAAEgG,OAAO,EAAE;IACtC,IAAIke,aAAa,CAAClkB,OAAO,CAAC,EAAE;MACxB,MAAMsgB,EAAE,GAAGhD,WAAW,GAAG,IAAI,CAAC0G,eAAe,CAAC1G,WAAW,CAAC,GAAG,IAAI;MACjE,IAAIgD,EAAE,EAAE;QACJA,EAAE,CAACa,UAAU,CAACnhB,OAAO,EAAEgG,OAAO,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAAC6a,oBAAoB,CAACvD,WAAW,EAAEtd,OAAO,EAAE,KAAK,EAAEgG,OAAO,CAAC;MACnE;MACA,MAAMse,MAAM,GAAG,IAAI,CAACpB,uBAAuB,CAACnY,GAAG,CAAC/K,OAAO,CAAC;MACxD,IAAIskB,MAAM,IAAIA,MAAM,CAACnJ,EAAE,KAAKmC,WAAW,EAAE;QACrCgH,MAAM,CAACnD,UAAU,CAACnhB,OAAO,EAAEgG,OAAO,CAAC;MACvC;IACJ,CAAC,MACI;MACD,IAAI,CAAC+b,kBAAkB,CAAC/hB,OAAO,EAAEgG,OAAO,CAAC;IAC7C;EACJ;EACA6a,oBAAoBA,CAACvD,WAAW,EAAEtd,OAAO,EAAEyd,YAAY,EAAEzX,OAAO,EAAE2a,sBAAsB,EAAE;IACtF,IAAI,CAACyC,sBAAsB,CAAC1gB,IAAI,CAAC1C,OAAO,CAAC;IACzCA,OAAO,CAAC4d,YAAY,CAAC,GAAG;MACpBN,WAAW;MACXC,aAAa,EAAEvX,OAAO;MACtByX,YAAY;MACZC,oBAAoB,EAAE,KAAK;MAC3BiD;IACJ,CAAC;EACL;EACA1E,MAAMA,CAACqB,WAAW,EAAEtd,OAAO,EAAEuD,IAAI,EAAEob,KAAK,EAAExC,QAAQ,EAAE;IAChD,IAAI+H,aAAa,CAAClkB,OAAO,CAAC,EAAE;MACxB,OAAO,IAAI,CAACgkB,eAAe,CAAC1G,WAAW,CAAC,CAACrB,MAAM,CAACjc,OAAO,EAAEuD,IAAI,EAAEob,KAAK,EAAExC,QAAQ,CAAC;IACnF;IACA,OAAO,MAAM,CAAE,CAAC;EACpB;EACAoI,iBAAiBA,CAACjK,KAAK,EAAEkK,YAAY,EAAElV,cAAc,EAAEC,cAAc,EAAEkV,YAAY,EAAE;IACjF,OAAOnK,KAAK,CAAC5S,UAAU,CAAC9B,KAAK,CAAC,IAAI,CAACH,MAAM,EAAE6U,KAAK,CAACta,OAAO,EAAEsa,KAAK,CAAChW,SAAS,CAACxC,KAAK,EAAEwY,KAAK,CAAC9V,OAAO,CAAC1C,KAAK,EAAEwN,cAAc,EAAEC,cAAc,EAAE+K,KAAK,CAAChW,SAAS,CAACuD,OAAO,EAAEyS,KAAK,CAAC9V,OAAO,CAACqD,OAAO,EAAE2c,YAAY,EAAEC,YAAY,CAAC;EACrN;EACA3C,sBAAsBA,CAAC4C,gBAAgB,EAAE;IACrC,IAAIxQ,QAAQ,GAAG,IAAI,CAACzO,MAAM,CAACxF,KAAK,CAACykB,gBAAgB,EAAE1nB,mBAAmB,EAAE,IAAI,CAAC;IAC7EkX,QAAQ,CAAClQ,OAAO,CAAEhE,OAAO,IAAK,IAAI,CAAC2kB,iCAAiC,CAAC3kB,OAAO,CAAC,CAAC;IAC9E,IAAI,IAAI,CAACyhB,uBAAuB,CAACpb,IAAI,IAAI,CAAC,EACtC;IACJ6N,QAAQ,GAAG,IAAI,CAACzO,MAAM,CAACxF,KAAK,CAACykB,gBAAgB,EAAEznB,qBAAqB,EAAE,IAAI,CAAC;IAC3EiX,QAAQ,CAAClQ,OAAO,CAAEhE,OAAO,IAAK,IAAI,CAAC4kB,qCAAqC,CAAC5kB,OAAO,CAAC,CAAC;EACtF;EACA2kB,iCAAiCA,CAAC3kB,OAAO,EAAE;IACvC,MAAMib,OAAO,GAAG,IAAI,CAACwE,gBAAgB,CAAC1U,GAAG,CAAC/K,OAAO,CAAC;IAClD,IAAIib,OAAO,EAAE;MACTA,OAAO,CAACjX,OAAO,CAAE2X,MAAM,IAAK;QACxB;QACA;QACA;QACA,IAAIA,MAAM,CAAC+D,MAAM,EAAE;UACf/D,MAAM,CAACyG,gBAAgB,GAAG,IAAI;QAClC,CAAC,MACI;UACDzG,MAAM,CAACE,OAAO,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN;EACJ;EACA+I,qCAAqCA,CAAC5kB,OAAO,EAAE;IAC3C,MAAMib,OAAO,GAAG,IAAI,CAACwG,uBAAuB,CAAC1W,GAAG,CAAC/K,OAAO,CAAC;IACzD,IAAIib,OAAO,EAAE;MACTA,OAAO,CAACjX,OAAO,CAAE2X,MAAM,IAAKA,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC;IAChD;EACJ;EACAkI,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,IAAI,CAAC9J,OAAO,CAACxY,MAAM,EAAE;QACrB,OAAO7E,mBAAmB,CAAC,IAAI,CAACqd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAMgF,OAAO,CAAC,CAAC,CAAC;MACpE,CAAC,MACI;QACDA,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,CAAC;EACN;EACAjE,gBAAgBA,CAAC9gB,OAAO,EAAE;IACtB,MAAMokB,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;IACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;MAClC;MACAvd,OAAO,CAAC4d,YAAY,CAAC,GAAGP,kBAAkB;MAC1C,IAAI+G,OAAO,CAAC9G,WAAW,EAAE;QACrB,IAAI,CAACwE,sBAAsB,CAAC9hB,OAAO,CAAC;QACpC,MAAMsgB,EAAE,GAAG,IAAI,CAAC0D,eAAe,CAACI,OAAO,CAAC9G,WAAW,CAAC;QACpD,IAAIgD,EAAE,EAAE;UACJA,EAAE,CAACL,iBAAiB,CAACjgB,OAAO,CAAC;QACjC;MACJ;MACA,IAAI,CAAC+hB,kBAAkB,CAAC/hB,OAAO,EAAEokB,OAAO,CAAC7G,aAAa,CAAC;IAC3D;IACA,IAAIvd,OAAO,CAACglB,SAAS,EAAEC,QAAQ,CAACjI,kBAAkB,CAAC,EAAE;MACjD,IAAI,CAACqH,qBAAqB,CAACrkB,OAAO,EAAE,KAAK,CAAC;IAC9C;IACA,IAAI,CAACyF,MAAM,CAACxF,KAAK,CAACD,OAAO,EAAEid,iBAAiB,EAAE,IAAI,CAAC,CAACjZ,OAAO,CAAEkhB,IAAI,IAAK;MAClE,IAAI,CAACb,qBAAqB,CAACa,IAAI,EAAE,KAAK,CAAC;IAC3C,CAAC,CAAC;EACN;EACAC,KAAKA,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAE;IACpB,IAAIjH,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC2H,eAAe,CAACvc,IAAI,EAAE;MAC3B,IAAI,CAACuc,eAAe,CAAC5e,OAAO,CAAC,CAACsc,EAAE,EAAEtgB,OAAO,KAAK,IAAI,CAACwjB,qBAAqB,CAAClD,EAAE,EAAEtgB,OAAO,CAAC,CAAC;MACtF,IAAI,CAAC4iB,eAAe,CAAC9T,KAAK,CAAC,CAAC;IAChC;IACA,IAAI,IAAI,CAACyS,eAAe,IAAI,IAAI,CAAC4B,sBAAsB,CAAC1gB,MAAM,EAAE;MAC5D,KAAK,IAAI4J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8W,sBAAsB,CAAC1gB,MAAM,EAAE4J,CAAC,EAAE,EAAE;QACzD,MAAMqM,GAAG,GAAG,IAAI,CAACyK,sBAAsB,CAAC9W,CAAC,CAAC;QAC1CqS,QAAQ,CAAChG,GAAG,EAAEwE,cAAc,CAAC;MACjC;IACJ;IACA,IAAI,IAAI,CAAC6F,cAAc,CAACtgB,MAAM,KACzB,IAAI,CAACmd,kBAAkB,IAAI,IAAI,CAACwD,sBAAsB,CAAC3gB,MAAM,CAAC,EAAE;MACjE,MAAM2iB,UAAU,GAAG,EAAE;MACrB,IAAI;QACAnK,OAAO,GAAG,IAAI,CAACoK,gBAAgB,CAACD,UAAU,EAAElD,WAAW,CAAC;MAC5D,CAAC,SACO;QACJ,KAAK,IAAI7V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Y,UAAU,CAAC3iB,MAAM,EAAE4J,CAAC,EAAE,EAAE;UACxC+Y,UAAU,CAAC/Y,CAAC,CAAC,CAAC,CAAC;QACnB;MACJ;IACJ,CAAC,MACI;MACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+W,sBAAsB,CAAC3gB,MAAM,EAAE4J,CAAC,EAAE,EAAE;QACzD,MAAMrM,OAAO,GAAG,IAAI,CAACojB,sBAAsB,CAAC/W,CAAC,CAAC;QAC9C,IAAI,CAACyU,gBAAgB,CAAC9gB,OAAO,CAAC;MAClC;IACJ;IACA,IAAI,CAAC4f,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACuD,sBAAsB,CAAC1gB,MAAM,GAAG,CAAC;IACtC,IAAI,CAAC2gB,sBAAsB,CAAC3gB,MAAM,GAAG,CAAC;IACtC,IAAI,CAACugB,SAAS,CAAChf,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC4J,SAAS,GAAG,EAAE;IACnB,IAAI,IAAI,CAACC,aAAa,CAACxgB,MAAM,EAAE;MAC3B;MACA;MACA;MACA,MAAM6iB,QAAQ,GAAG,IAAI,CAACrC,aAAa;MACnC,IAAI,CAACA,aAAa,GAAG,EAAE;MACvB,IAAIhI,OAAO,CAACxY,MAAM,EAAE;QAChB7E,mBAAmB,CAACqd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAM;UACtCuF,QAAQ,CAACthB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC;MACN,CAAC,MACI;QACDkM,QAAQ,CAACthB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MAClC;IACJ;EACJ;EACAmG,WAAWA,CAAC7d,MAAM,EAAE;IAChB,MAAM1D,wBAAwB,CAAC0D,MAAM,CAAC;EAC1C;EACA2jB,gBAAgBA,CAACD,UAAU,EAAElD,WAAW,EAAE;IACtC,MAAMsC,YAAY,GAAG,IAAI/V,qBAAqB,CAAC,CAAC;IAChD,MAAM8W,cAAc,GAAG,EAAE;IACzB,MAAMC,iBAAiB,GAAG,IAAI/e,GAAG,CAAC,CAAC;IACnC,MAAMgf,kBAAkB,GAAG,EAAE;IAC7B,MAAMxO,eAAe,GAAG,IAAIxQ,GAAG,CAAC,CAAC;IACjC,MAAMif,mBAAmB,GAAG,IAAIjf,GAAG,CAAC,CAAC;IACrC,MAAMkf,oBAAoB,GAAG,IAAIlf,GAAG,CAAC,CAAC;IACtC,MAAMmf,mBAAmB,GAAG,IAAI5jB,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC6gB,aAAa,CAAC7e,OAAO,CAAEkhB,IAAI,IAAK;MACjCU,mBAAmB,CAACrd,GAAG,CAAC2c,IAAI,CAAC;MAC7B,MAAMW,oBAAoB,GAAG,IAAI,CAACpgB,MAAM,CAACxF,KAAK,CAACilB,IAAI,EAAEnI,eAAe,EAAE,IAAI,CAAC;MAC3E,KAAK,IAAI1Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwZ,oBAAoB,CAACpjB,MAAM,EAAE4J,CAAC,EAAE,EAAE;QAClDuZ,mBAAmB,CAACrd,GAAG,CAACsd,oBAAoB,CAACxZ,CAAC,CAAC,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,MAAMyO,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMgL,kBAAkB,GAAGtb,KAAK,CAACyO,IAAI,CAAC,IAAI,CAAC+F,eAAe,CAAC1Y,IAAI,CAAC,CAAC,CAAC;IAClE,MAAMyf,YAAY,GAAGC,YAAY,CAACF,kBAAkB,EAAE,IAAI,CAAC3C,sBAAsB,CAAC;IAClF;IACA;IACA;IACA,MAAM8C,eAAe,GAAG,IAAIxf,GAAG,CAAC,CAAC;IACjC,IAAI4F,CAAC,GAAG,CAAC;IACT0Z,YAAY,CAAC/hB,OAAO,CAAC,CAACkiB,KAAK,EAAEC,IAAI,KAAK;MAClC,MAAMC,SAAS,GAAG3oB,eAAe,GAAG4O,CAAC,EAAE;MACvC4Z,eAAe,CAACvf,GAAG,CAACyf,IAAI,EAAEC,SAAS,CAAC;MACpCF,KAAK,CAACliB,OAAO,CAAEkhB,IAAI,IAAKxG,QAAQ,CAACwG,IAAI,EAAEkB,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,gBAAgB,GAAG,IAAItkB,GAAG,CAAC,CAAC;IAClC,MAAMukB,2BAA2B,GAAG,IAAIvkB,GAAG,CAAC,CAAC;IAC7C,KAAK,IAAIqK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+W,sBAAsB,CAAC3gB,MAAM,EAAE4J,CAAC,EAAE,EAAE;MACzD,MAAMrM,OAAO,GAAG,IAAI,CAACojB,sBAAsB,CAAC/W,CAAC,CAAC;MAC9C,MAAM+X,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;MACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC7G,aAAa,EAAE;QAClC8I,aAAa,CAAC3jB,IAAI,CAAC1C,OAAO,CAAC;QAC3BsmB,gBAAgB,CAAC/d,GAAG,CAACvI,OAAO,CAAC;QAC7B,IAAIokB,OAAO,CAAC3G,YAAY,EAAE;UACtB,IAAI,CAAChY,MAAM,CACNxF,KAAK,CAACD,OAAO,EAAEmd,aAAa,EAAE,IAAI,CAAC,CACnCnZ,OAAO,CAAE0U,GAAG,IAAK4N,gBAAgB,CAAC/d,GAAG,CAACmQ,GAAG,CAAC,CAAC;QACpD,CAAC,MACI;UACD6N,2BAA2B,CAAChe,GAAG,CAACvI,OAAO,CAAC;QAC5C;MACJ;IACJ;IACA,MAAMwmB,eAAe,GAAG,IAAI/f,GAAG,CAAC,CAAC;IACjC,MAAMggB,YAAY,GAAGT,YAAY,CAACF,kBAAkB,EAAEtb,KAAK,CAACyO,IAAI,CAACqN,gBAAgB,CAAC,CAAC;IACnFG,YAAY,CAACziB,OAAO,CAAC,CAACkiB,KAAK,EAAEC,IAAI,KAAK;MAClC,MAAMC,SAAS,GAAG5oB,eAAe,GAAG6O,CAAC,EAAE;MACvCma,eAAe,CAAC9f,GAAG,CAACyf,IAAI,EAAEC,SAAS,CAAC;MACpCF,KAAK,CAACliB,OAAO,CAAEkhB,IAAI,IAAKxG,QAAQ,CAACwG,IAAI,EAAEkB,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC;IACFhB,UAAU,CAAC1iB,IAAI,CAAC,MAAM;MAClBqjB,YAAY,CAAC/hB,OAAO,CAAC,CAACkiB,KAAK,EAAEC,IAAI,KAAK;QAClC,MAAMC,SAAS,GAAGH,eAAe,CAAClb,GAAG,CAACob,IAAI,CAAC;QAC3CD,KAAK,CAACliB,OAAO,CAAEkhB,IAAI,IAAKpF,WAAW,CAACoF,IAAI,EAAEkB,SAAS,CAAC,CAAC;MACzD,CAAC,CAAC;MACFK,YAAY,CAACziB,OAAO,CAAC,CAACkiB,KAAK,EAAEC,IAAI,KAAK;QAClC,MAAMC,SAAS,GAAGI,eAAe,CAACzb,GAAG,CAACob,IAAI,CAAC;QAC3CD,KAAK,CAACliB,OAAO,CAAEkhB,IAAI,IAAKpF,WAAW,CAACoF,IAAI,EAAEkB,SAAS,CAAC,CAAC;MACzD,CAAC,CAAC;MACFC,aAAa,CAACriB,OAAO,CAAEhE,OAAO,IAAK;QAC/B,IAAI,CAAC8gB,gBAAgB,CAAC9gB,OAAO,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,MAAM0mB,UAAU,GAAG,EAAE;IACrB,MAAMC,oBAAoB,GAAG,EAAE;IAC/B,KAAK,IAAIta,CAAC,GAAG,IAAI,CAAC0W,cAAc,CAACtgB,MAAM,GAAG,CAAC,EAAE4J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtD,MAAMiU,EAAE,GAAG,IAAI,CAACyC,cAAc,CAAC1W,CAAC,CAAC;MACjCiU,EAAE,CAAC2B,sBAAsB,CAACC,WAAW,CAAC,CAACle,OAAO,CAAEsW,KAAK,IAAK;QACtD,MAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAM;QAC3B,MAAM3b,OAAO,GAAGsa,KAAK,CAACta,OAAO;QAC7B0mB,UAAU,CAAChkB,IAAI,CAACiZ,MAAM,CAAC;QACvB,IAAI,IAAI,CAACwH,sBAAsB,CAAC1gB,MAAM,EAAE;UACpC,MAAM2hB,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;UACrC;UACA;UACA,IAAIwG,OAAO,IAAIA,OAAO,CAAC5G,UAAU,EAAE;YAC/B,IAAI4G,OAAO,CAACzD,sBAAsB,IAC9ByD,OAAO,CAACzD,sBAAsB,CAACre,GAAG,CAACgY,KAAK,CAACzD,WAAW,CAAC,EAAE;cACvD,MAAM+P,aAAa,GAAGxC,OAAO,CAACzD,sBAAsB,CAAC5V,GAAG,CAACuP,KAAK,CAACzD,WAAW,CAAC;cAC3E;cACA;cACA,MAAMkI,kBAAkB,GAAG,IAAI,CAACC,eAAe,CAACjU,GAAG,CAACuP,KAAK,CAACta,OAAO,CAAC;cAClE,IAAI+e,kBAAkB,IAAIA,kBAAkB,CAACzc,GAAG,CAACgY,KAAK,CAACzD,WAAW,CAAC,EAAE;gBACjE,MAAM+J,KAAK,GAAG7B,kBAAkB,CAAChU,GAAG,CAACuP,KAAK,CAACzD,WAAW,CAAC;gBACvD+J,KAAK,CAAC9e,KAAK,GAAG8kB,aAAa;gBAC3B7H,kBAAkB,CAACrY,GAAG,CAAC4T,KAAK,CAACzD,WAAW,EAAE+J,KAAK,CAAC;cACpD;YACJ;YACAjF,MAAM,CAACE,OAAO,CAAC,CAAC;YAChB;UACJ;QACJ;QACA,MAAMgL,cAAc,GAAG,CAAC/L,QAAQ,IAAI,CAAC,IAAI,CAACrV,MAAM,CAAChK,eAAe,CAACqf,QAAQ,EAAE9a,OAAO,CAAC;QACnF,MAAMuP,cAAc,GAAGiX,eAAe,CAACzb,GAAG,CAAC/K,OAAO,CAAC;QACnD,MAAMsP,cAAc,GAAG2W,eAAe,CAAClb,GAAG,CAAC/K,OAAO,CAAC;QACnD,MAAMgR,WAAW,GAAG,IAAI,CAACuT,iBAAiB,CAACjK,KAAK,EAAEkK,YAAY,EAAElV,cAAc,EAAEC,cAAc,EAAEsX,cAAc,CAAC;QAC/G,IAAI7V,WAAW,CAACtP,MAAM,IAAIsP,WAAW,CAACtP,MAAM,CAACe,MAAM,EAAE;UACjDkkB,oBAAoB,CAACjkB,IAAI,CAACsO,WAAW,CAAC;UACtC;QACJ;QACA;QACA;QACA;QACA;QACA,IAAI6V,cAAc,EAAE;UAChBlL,MAAM,CAACkE,OAAO,CAAC,MAAM5hB,WAAW,CAAC+B,OAAO,EAAEgR,WAAW,CAAC+F,UAAU,CAAC,CAAC;UAClE4E,MAAM,CAACC,SAAS,CAAC,MAAM1d,SAAS,CAAC8B,OAAO,EAAEgR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChEuO,cAAc,CAAC7iB,IAAI,CAACiZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA,IAAIrB,KAAK,CAACqF,oBAAoB,EAAE;UAC5BhE,MAAM,CAACkE,OAAO,CAAC,MAAM5hB,WAAW,CAAC+B,OAAO,EAAEgR,WAAW,CAAC+F,UAAU,CAAC,CAAC;UAClE4E,MAAM,CAACC,SAAS,CAAC,MAAM1d,SAAS,CAAC8B,OAAO,EAAEgR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChEuO,cAAc,CAAC7iB,IAAI,CAACiZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA;QACA;QACA,MAAM3L,SAAS,GAAG,EAAE;QACpBgB,WAAW,CAAChB,SAAS,CAAChM,OAAO,CAAEwO,EAAE,IAAK;UAClCA,EAAE,CAACuB,uBAAuB,GAAG,IAAI;UACjC,IAAI,CAAC,IAAI,CAAC8O,aAAa,CAACvgB,GAAG,CAACkQ,EAAE,CAACxS,OAAO,CAAC,EAAE;YACrCgQ,SAAS,CAACtN,IAAI,CAAC8P,EAAE,CAAC;UACtB;QACJ,CAAC,CAAC;QACFxB,WAAW,CAAChB,SAAS,GAAGA,SAAS;QACjCwU,YAAY,CAAC7V,MAAM,CAAC3O,OAAO,EAAEgR,WAAW,CAAChB,SAAS,CAAC;QACnD,MAAM1E,KAAK,GAAG;UAAE0F,WAAW;UAAE2K,MAAM;UAAE3b;QAAQ,CAAC;QAC9CylB,kBAAkB,CAAC/iB,IAAI,CAAC4I,KAAK,CAAC;QAC9B0F,WAAW,CAACiG,eAAe,CAACjT,OAAO,CAAEhE,OAAO,IAAKnD,oBAAoB,CAACoa,eAAe,EAAEjX,OAAO,EAAE,EAAE,CAAC,CAAC0C,IAAI,CAACiZ,MAAM,CAAC,CAAC;QACjH3K,WAAW,CAAC3C,aAAa,CAACrK,OAAO,CAAC,CAAC8iB,SAAS,EAAE9mB,OAAO,KAAK;UACtD,IAAI8mB,SAAS,CAACzgB,IAAI,EAAE;YAChB,IAAI0gB,MAAM,GAAGrB,mBAAmB,CAAC3a,GAAG,CAAC/K,OAAO,CAAC;YAC7C,IAAI,CAAC+mB,MAAM,EAAE;cACTrB,mBAAmB,CAAChf,GAAG,CAAC1G,OAAO,EAAG+mB,MAAM,GAAG,IAAI/kB,GAAG,CAAC,CAAE,CAAC;YAC1D;YACA8kB,SAAS,CAAC9iB,OAAO,CAAC,CAAC0X,CAAC,EAAE7b,IAAI,KAAKknB,MAAM,CAACxe,GAAG,CAAC1I,IAAI,CAAC,CAAC;UACpD;QACJ,CAAC,CAAC;QACFmR,WAAW,CAAC1C,cAAc,CAACtK,OAAO,CAAC,CAAC8iB,SAAS,EAAE9mB,OAAO,KAAK;UACvD,IAAI+mB,MAAM,GAAGpB,oBAAoB,CAAC5a,GAAG,CAAC/K,OAAO,CAAC;UAC9C,IAAI,CAAC+mB,MAAM,EAAE;YACTpB,oBAAoB,CAACjf,GAAG,CAAC1G,OAAO,EAAG+mB,MAAM,GAAG,IAAI/kB,GAAG,CAAC,CAAE,CAAC;UAC3D;UACA8kB,SAAS,CAAC9iB,OAAO,CAAC,CAAC0X,CAAC,EAAE7b,IAAI,KAAKknB,MAAM,CAACxe,GAAG,CAAC1I,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI8mB,oBAAoB,CAAClkB,MAAM,EAAE;MAC7B,MAAMf,MAAM,GAAG,EAAE;MACjBilB,oBAAoB,CAAC3iB,OAAO,CAAEgN,WAAW,IAAK;QAC1CtP,MAAM,CAACgB,IAAI,CAACvE,gBAAgB,CAAC6S,WAAW,CAAC6F,WAAW,EAAE7F,WAAW,CAACtP,MAAM,CAAC,CAAC;MAC9E,CAAC,CAAC;MACFglB,UAAU,CAAC1iB,OAAO,CAAE2X,MAAM,IAAKA,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC0D,WAAW,CAAC7d,MAAM,CAAC;IAC5B;IACA,MAAMslB,qBAAqB,GAAG,IAAIvgB,GAAG,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA,MAAMwgB,mBAAmB,GAAG,IAAIxgB,GAAG,CAAC,CAAC;IACrCgf,kBAAkB,CAACzhB,OAAO,CAAEsW,KAAK,IAAK;MAClC,MAAMta,OAAO,GAAGsa,KAAK,CAACta,OAAO;MAC7B,IAAIwkB,YAAY,CAACliB,GAAG,CAACtC,OAAO,CAAC,EAAE;QAC3BinB,mBAAmB,CAACvgB,GAAG,CAAC1G,OAAO,EAAEA,OAAO,CAAC;QACzC,IAAI,CAACknB,qBAAqB,CAAC5M,KAAK,CAACqB,MAAM,CAAC2B,WAAW,EAAEhD,KAAK,CAACtJ,WAAW,EAAEgW,qBAAqB,CAAC;MAClG;IACJ,CAAC,CAAC;IACFzB,cAAc,CAACvhB,OAAO,CAAE2X,MAAM,IAAK;MAC/B,MAAM3b,OAAO,GAAG2b,MAAM,CAAC3b,OAAO;MAC9B,MAAMU,eAAe,GAAG,IAAI,CAACymB,mBAAmB,CAACnnB,OAAO,EAAE,KAAK,EAAE2b,MAAM,CAAC2B,WAAW,EAAE3B,MAAM,CAAC9E,WAAW,EAAE,IAAI,CAAC;MAC9GnW,eAAe,CAACsD,OAAO,CAAEojB,UAAU,IAAK;QACpCvqB,oBAAoB,CAACmqB,qBAAqB,EAAEhnB,OAAO,EAAE,EAAE,CAAC,CAAC0C,IAAI,CAAC0kB,UAAU,CAAC;QACzEA,UAAU,CAACvL,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwL,YAAY,GAAGhB,aAAa,CAACvjB,MAAM,CAAEoiB,IAAI,IAAK;MAChD,OAAOoC,sBAAsB,CAACpC,IAAI,EAAEQ,mBAAmB,EAAEC,oBAAoB,CAAC;IAClF,CAAC,CAAC;IACF;IACA,MAAM4B,aAAa,GAAG,IAAI9gB,GAAG,CAAC,CAAC;IAC/B,MAAM+gB,oBAAoB,GAAGC,qBAAqB,CAACF,aAAa,EAAE,IAAI,CAAC9hB,MAAM,EAAE8gB,2BAA2B,EAAEZ,oBAAoB,EAAEnmB,UAAU,CAAC;IAC7IgoB,oBAAoB,CAACxjB,OAAO,CAAEkhB,IAAI,IAAK;MACnC,IAAIoC,sBAAsB,CAACpC,IAAI,EAAEQ,mBAAmB,EAAEC,oBAAoB,CAAC,EAAE;QACzE0B,YAAY,CAAC3kB,IAAI,CAACwiB,IAAI,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF;IACA,MAAMwC,YAAY,GAAG,IAAIjhB,GAAG,CAAC,CAAC;IAC9Bsf,YAAY,CAAC/hB,OAAO,CAAC,CAACkiB,KAAK,EAAEC,IAAI,KAAK;MAClCsB,qBAAqB,CAACC,YAAY,EAAE,IAAI,CAACjiB,MAAM,EAAE,IAAIzD,GAAG,CAACkkB,KAAK,CAAC,EAAER,mBAAmB,EAAEhmB,UAAU,CAAC;IACrG,CAAC,CAAC;IACF2nB,YAAY,CAACrjB,OAAO,CAAEkhB,IAAI,IAAK;MAC3B,MAAMyC,IAAI,GAAGJ,aAAa,CAACxc,GAAG,CAACma,IAAI,CAAC;MACpC,MAAM0C,GAAG,GAAGF,YAAY,CAAC3c,GAAG,CAACma,IAAI,CAAC;MAClCqC,aAAa,CAAC7gB,GAAG,CAACwe,IAAI,EAAE,IAAIze,GAAG,CAAC,CAAC,IAAIkhB,IAAI,EAAE/c,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,IAAIgd,GAAG,EAAEhd,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7F,CAAC,CAAC;IACF,MAAMid,WAAW,GAAG,EAAE;IACtB,MAAMC,UAAU,GAAG,EAAE;IACrB,MAAMC,oCAAoC,GAAG,CAAC,CAAC;IAC/CtC,kBAAkB,CAACzhB,OAAO,CAAEsW,KAAK,IAAK;MAClC,MAAM;QAAEta,OAAO;QAAE2b,MAAM;QAAE3K;MAAY,CAAC,GAAGsJ,KAAK;MAC9C;MACA;MACA,IAAIkK,YAAY,CAACliB,GAAG,CAACtC,OAAO,CAAC,EAAE;QAC3B,IAAI4lB,mBAAmB,CAACtjB,GAAG,CAACtC,OAAO,CAAC,EAAE;UAClC2b,MAAM,CAACC,SAAS,CAAC,MAAM1d,SAAS,CAAC8B,OAAO,EAAEgR,WAAW,CAACgG,QAAQ,CAAC,CAAC;UAChE2E,MAAM,CAACqM,QAAQ,GAAG,IAAI;UACtBrM,MAAM,CAACsM,iBAAiB,CAACjX,WAAW,CAACxC,SAAS,CAAC;UAC/C+W,cAAc,CAAC7iB,IAAI,CAACiZ,MAAM,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIuM,mBAAmB,GAAGH,oCAAoC;QAC9D,IAAId,mBAAmB,CAAC5gB,IAAI,GAAG,CAAC,EAAE;UAC9B,IAAIqS,GAAG,GAAG1Y,OAAO;UACjB,MAAMmoB,YAAY,GAAG,EAAE;UACvB,OAAQzP,GAAG,GAAGA,GAAG,CAACiJ,UAAU,EAAG;YAC3B,MAAMyG,cAAc,GAAGnB,mBAAmB,CAAClc,GAAG,CAAC2N,GAAG,CAAC;YACnD,IAAI0P,cAAc,EAAE;cAChBF,mBAAmB,GAAGE,cAAc;cACpC;YACJ;YACAD,YAAY,CAACzlB,IAAI,CAACgW,GAAG,CAAC;UAC1B;UACAyP,YAAY,CAACnkB,OAAO,CAAE0d,MAAM,IAAKuF,mBAAmB,CAACvgB,GAAG,CAACgb,MAAM,EAAEwG,mBAAmB,CAAC,CAAC;QAC1F;QACA,MAAMG,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC3M,MAAM,CAAC2B,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAExB,iBAAiB,EAAEkC,YAAY,EAAEH,aAAa,CAAC;QAChJ5L,MAAM,CAAC4M,aAAa,CAACF,WAAW,CAAC;QACjC,IAAIH,mBAAmB,KAAKH,oCAAoC,EAAE;UAC9DF,WAAW,CAACnlB,IAAI,CAACiZ,MAAM,CAAC;QAC5B,CAAC,MACI;UACD,MAAM6M,aAAa,GAAG,IAAI,CAAC/I,gBAAgB,CAAC1U,GAAG,CAACmd,mBAAmB,CAAC;UACpE,IAAIM,aAAa,IAAIA,aAAa,CAAC/lB,MAAM,EAAE;YACvCkZ,MAAM,CAAC8M,YAAY,GAAG7qB,mBAAmB,CAAC4qB,aAAa,CAAC;UAC5D;UACAjD,cAAc,CAAC7iB,IAAI,CAACiZ,MAAM,CAAC;QAC/B;MACJ,CAAC,MACI;QACD1d,WAAW,CAAC+B,OAAO,EAAEgR,WAAW,CAAC+F,UAAU,CAAC;QAC5C4E,MAAM,CAACC,SAAS,CAAC,MAAM1d,SAAS,CAAC8B,OAAO,EAAEgR,WAAW,CAACgG,QAAQ,CAAC,CAAC;QAChE;QACA;QACA;QACA8Q,UAAU,CAACplB,IAAI,CAACiZ,MAAM,CAAC;QACvB,IAAIiK,mBAAmB,CAACtjB,GAAG,CAACtC,OAAO,CAAC,EAAE;UAClCulB,cAAc,CAAC7iB,IAAI,CAACiZ,MAAM,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;IACF;IACAmM,UAAU,CAAC9jB,OAAO,CAAE2X,MAAM,IAAK;MAC3B;MACA;MACA,MAAM+M,iBAAiB,GAAGlD,iBAAiB,CAACza,GAAG,CAAC4Q,MAAM,CAAC3b,OAAO,CAAC;MAC/D,IAAI0oB,iBAAiB,IAAIA,iBAAiB,CAACjmB,MAAM,EAAE;QAC/C,MAAM4lB,WAAW,GAAGzqB,mBAAmB,CAAC8qB,iBAAiB,CAAC;QAC1D/M,MAAM,CAAC4M,aAAa,CAACF,WAAW,CAAC;MACrC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA9C,cAAc,CAACvhB,OAAO,CAAE2X,MAAM,IAAK;MAC/B,IAAIA,MAAM,CAAC8M,YAAY,EAAE;QACrB9M,MAAM,CAACgN,gBAAgB,CAAChN,MAAM,CAAC8M,YAAY,CAAC;MAChD,CAAC,MACI;QACD9M,MAAM,CAACE,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA,KAAK,IAAIxP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGga,aAAa,CAAC5jB,MAAM,EAAE4J,CAAC,EAAE,EAAE;MAC3C,MAAMrM,OAAO,GAAGqmB,aAAa,CAACha,CAAC,CAAC;MAChC,MAAM+X,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;MACrCkC,WAAW,CAAC9f,OAAO,EAAExC,eAAe,CAAC;MACrC;MACA;MACA;MACA,IAAI4mB,OAAO,IAAIA,OAAO,CAAC3G,YAAY,EAC/B;MACJ,IAAIxC,OAAO,GAAG,EAAE;MAChB;MACA;MACA;MACA,IAAIhE,eAAe,CAAC5Q,IAAI,EAAE;QACtB,IAAIuiB,oBAAoB,GAAG3R,eAAe,CAAClM,GAAG,CAAC/K,OAAO,CAAC;QACvD,IAAI4oB,oBAAoB,IAAIA,oBAAoB,CAACnmB,MAAM,EAAE;UACrDwY,OAAO,CAACvY,IAAI,CAAC,GAAGkmB,oBAAoB,CAAC;QACzC;QACA,IAAIC,oBAAoB,GAAG,IAAI,CAACpjB,MAAM,CAACxF,KAAK,CAACD,OAAO,EAAE/C,qBAAqB,EAAE,IAAI,CAAC;QAClF,KAAK,IAAI6rB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,CAACpmB,MAAM,EAAEqmB,CAAC,EAAE,EAAE;UAClD,IAAIC,cAAc,GAAG9R,eAAe,CAAClM,GAAG,CAAC8d,oBAAoB,CAACC,CAAC,CAAC,CAAC;UACjE,IAAIC,cAAc,IAAIA,cAAc,CAACtmB,MAAM,EAAE;YACzCwY,OAAO,CAACvY,IAAI,CAAC,GAAGqmB,cAAc,CAAC;UACnC;QACJ;MACJ;MACA,MAAMC,aAAa,GAAG/N,OAAO,CAACnY,MAAM,CAAE4f,CAAC,IAAK,CAACA,CAAC,CAACP,SAAS,CAAC;MACzD,IAAI6G,aAAa,CAACvmB,MAAM,EAAE;QACtBwmB,6BAA6B,CAAC,IAAI,EAAEjpB,OAAO,EAAEgpB,aAAa,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAAClI,gBAAgB,CAAC9gB,OAAO,CAAC;MAClC;IACJ;IACA;IACAqmB,aAAa,CAAC5jB,MAAM,GAAG,CAAC;IACxBolB,WAAW,CAAC7jB,OAAO,CAAE2X,MAAM,IAAK;MAC5B,IAAI,CAACV,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;MACzBA,MAAM,CAACoE,MAAM,CAAC,MAAM;QAChBpE,MAAM,CAACE,OAAO,CAAC,CAAC;QAChB,MAAME,KAAK,GAAG,IAAI,CAACd,OAAO,CAAChQ,OAAO,CAAC0Q,MAAM,CAAC;QAC1C,IAAI,CAACV,OAAO,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC,CAAC;MACFJ,MAAM,CAACY,IAAI,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,OAAOsL,WAAW;EACtB;EACA5I,UAAUA,CAAC9C,QAAQ,EAAE;IACjB,IAAI,CAAC6G,SAAS,CAACtgB,IAAI,CAACyZ,QAAQ,CAAC;EACjC;EACAqE,wBAAwBA,CAACrE,QAAQ,EAAE;IAC/B,IAAI,CAAC8G,aAAa,CAACvgB,IAAI,CAACyZ,QAAQ,CAAC;EACrC;EACAgL,mBAAmBA,CAACnnB,OAAO,EAAEkpB,gBAAgB,EAAE5L,WAAW,EAAEzG,WAAW,EAAEsS,YAAY,EAAE;IACnF,IAAIlO,OAAO,GAAG,EAAE;IAChB,IAAIiO,gBAAgB,EAAE;MAClB,MAAME,qBAAqB,GAAG,IAAI,CAAC3H,uBAAuB,CAAC1W,GAAG,CAAC/K,OAAO,CAAC;MACvE,IAAIopB,qBAAqB,EAAE;QACvBnO,OAAO,GAAGmO,qBAAqB;MACnC;IACJ,CAAC,MACI;MACD,MAAMlJ,cAAc,GAAG,IAAI,CAACT,gBAAgB,CAAC1U,GAAG,CAAC/K,OAAO,CAAC;MACzD,IAAIkgB,cAAc,EAAE;QAChB,MAAMmJ,kBAAkB,GAAG,CAACF,YAAY,IAAIA,YAAY,IAAIlL,UAAU;QACtEiC,cAAc,CAAClc,OAAO,CAAE2X,MAAM,IAAK;UAC/B,IAAIA,MAAM,CAAC+D,MAAM,EACb;UACJ,IAAI,CAAC2J,kBAAkB,IAAI1N,MAAM,CAAC9E,WAAW,IAAIA,WAAW,EACxD;UACJoE,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;QACxB,CAAC,CAAC;MACN;IACJ;IACA,IAAI2B,WAAW,IAAIzG,WAAW,EAAE;MAC5BoE,OAAO,GAAGA,OAAO,CAACnY,MAAM,CAAE6Y,MAAM,IAAK;QACjC,IAAI2B,WAAW,IAAIA,WAAW,IAAI3B,MAAM,CAAC2B,WAAW,EAChD,OAAO,KAAK;QAChB,IAAIzG,WAAW,IAAIA,WAAW,IAAI8E,MAAM,CAAC9E,WAAW,EAChD,OAAO,KAAK;QAChB,OAAO,IAAI;MACf,CAAC,CAAC;IACN;IACA,OAAOoE,OAAO;EAClB;EACAiM,qBAAqBA,CAAC5J,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAE;IACnE,MAAMnQ,WAAW,GAAG7F,WAAW,CAAC6F,WAAW;IAC3C,MAAMxH,WAAW,GAAG2B,WAAW,CAAChR,OAAO;IACvC;IACA;IACA,MAAMspB,iBAAiB,GAAGtY,WAAW,CAAC8F,mBAAmB,GACnDc,SAAS,GACT0F,WAAW;IACjB,MAAMiM,iBAAiB,GAAGvY,WAAW,CAAC8F,mBAAmB,GACnDc,SAAS,GACTf,WAAW;IACjB,KAAK,MAAM2S,mBAAmB,IAAIxY,WAAW,CAAChB,SAAS,EAAE;MACrD,MAAMhQ,OAAO,GAAGwpB,mBAAmB,CAACxpB,OAAO;MAC3C,MAAMkpB,gBAAgB,GAAGlpB,OAAO,KAAKqP,WAAW;MAChD,MAAM4L,OAAO,GAAGpe,oBAAoB,CAACmqB,qBAAqB,EAAEhnB,OAAO,EAAE,EAAE,CAAC;MACxE,MAAMU,eAAe,GAAG,IAAI,CAACymB,mBAAmB,CAACnnB,OAAO,EAAEkpB,gBAAgB,EAAEI,iBAAiB,EAAEC,iBAAiB,EAAEvY,WAAW,CAACxM,OAAO,CAAC;MACtI9D,eAAe,CAACsD,OAAO,CAAE2X,MAAM,IAAK;QAChC,MAAM8N,UAAU,GAAG9N,MAAM,CAAC+N,aAAa,CAAC,CAAC;QACzC,IAAID,UAAU,CAACE,aAAa,EAAE;UAC1BF,UAAU,CAACE,aAAa,CAAC,CAAC;QAC9B;QACAhO,MAAM,CAACE,OAAO,CAAC,CAAC;QAChBZ,OAAO,CAACvY,IAAI,CAACiZ,MAAM,CAAC;MACxB,CAAC,CAAC;IACN;IACA;IACA;IACA1d,WAAW,CAACoR,WAAW,EAAE2B,WAAW,CAAC+F,UAAU,CAAC;EACpD;EACAuR,eAAeA,CAAChL,WAAW,EAAEtM,WAAW,EAAEgW,qBAAqB,EAAExB,iBAAiB,EAAEkC,YAAY,EAAEH,aAAa,EAAE;IAC7G,MAAM1Q,WAAW,GAAG7F,WAAW,CAAC6F,WAAW;IAC3C,MAAMxH,WAAW,GAAG2B,WAAW,CAAChR,OAAO;IACvC;IACA;IACA,MAAM4pB,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,mBAAmB,GAAG,IAAI7nB,GAAG,CAAC,CAAC;IACrC,MAAM8nB,cAAc,GAAG,IAAI9nB,GAAG,CAAC,CAAC;IAChC,MAAM+nB,aAAa,GAAG/Y,WAAW,CAAChB,SAAS,CAAChN,GAAG,CAAEwmB,mBAAmB,IAAK;MACrE,MAAMxpB,OAAO,GAAGwpB,mBAAmB,CAACxpB,OAAO;MAC3C6pB,mBAAmB,CAACthB,GAAG,CAACvI,OAAO,CAAC;MAChC;MACA,MAAMokB,OAAO,GAAGpkB,OAAO,CAAC4d,YAAY,CAAC;MACrC,IAAIwG,OAAO,IAAIA,OAAO,CAAC1G,oBAAoB,EACvC,OAAO,IAAIre,mBAAmB,CAACmqB,mBAAmB,CAACjpB,QAAQ,EAAEipB,mBAAmB,CAAChpB,KAAK,CAAC;MAC3F,MAAM0oB,gBAAgB,GAAGlpB,OAAO,KAAKqP,WAAW;MAChD,MAAM3O,eAAe,GAAGspB,mBAAmB,CAAC,CAAChD,qBAAqB,CAACjc,GAAG,CAAC/K,OAAO,CAAC,IAAIod,kBAAkB,EAAEpa,GAAG,CAAE0f,CAAC,IAAKA,CAAC,CAACgH,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC5mB,MAAM,CAAE4f,CAAC,IAAK;QAChJ;QACA;QACA;QACA;QACA,MAAMuH,EAAE,GAAGvH,CAAC;QACZ,OAAOuH,EAAE,CAACjqB,OAAO,GAAGiqB,EAAE,CAACjqB,OAAO,KAAKA,OAAO,GAAG,KAAK;MACtD,CAAC,CAAC;MACF,MAAMqb,SAAS,GAAGqM,YAAY,CAAC3c,GAAG,CAAC/K,OAAO,CAAC;MAC3C,MAAMsb,UAAU,GAAGiM,aAAa,CAACxc,GAAG,CAAC/K,OAAO,CAAC;MAC7C,MAAMM,SAAS,GAAG/C,kBAAkB,CAAC,IAAI,CAACwc,WAAW,EAAEyP,mBAAmB,CAAClpB,SAAS,EAAE+a,SAAS,EAAEC,UAAU,CAAC;MAC5G,MAAMK,MAAM,GAAG,IAAI,CAACP,YAAY,CAACoO,mBAAmB,EAAElpB,SAAS,EAAEI,eAAe,CAAC;MACjF;MACA;MACA,IAAI8oB,mBAAmB,CAACjb,WAAW,IAAIiX,iBAAiB,EAAE;QACtDsE,cAAc,CAACvhB,GAAG,CAACvI,OAAO,CAAC;MAC/B;MACA,IAAIkpB,gBAAgB,EAAE;QAClB,MAAMgB,aAAa,GAAG,IAAI7K,yBAAyB,CAAC/B,WAAW,EAAEzG,WAAW,EAAE7W,OAAO,CAAC;QACtFkqB,aAAa,CAAC3B,aAAa,CAAC5M,MAAM,CAAC;QACnCiO,iBAAiB,CAAClnB,IAAI,CAACwnB,aAAa,CAAC;MACzC;MACA,OAAOvO,MAAM;IACjB,CAAC,CAAC;IACFiO,iBAAiB,CAAC5lB,OAAO,CAAE2X,MAAM,IAAK;MAClC9e,oBAAoB,CAAC,IAAI,CAAC4kB,uBAAuB,EAAE9F,MAAM,CAAC3b,OAAO,EAAE,EAAE,CAAC,CAAC0C,IAAI,CAACiZ,MAAM,CAAC;MACnFA,MAAM,CAACoE,MAAM,CAAC,MAAMoK,kBAAkB,CAAC,IAAI,CAAC1I,uBAAuB,EAAE9F,MAAM,CAAC3b,OAAO,EAAE2b,MAAM,CAAC,CAAC;IACjG,CAAC,CAAC;IACFkO,mBAAmB,CAAC7lB,OAAO,CAAEhE,OAAO,IAAK0e,QAAQ,CAAC1e,OAAO,EAAEvB,sBAAsB,CAAC,CAAC;IACnF,MAAMkd,MAAM,GAAG/d,mBAAmB,CAACmsB,aAAa,CAAC;IACjDpO,MAAM,CAACC,SAAS,CAAC,MAAM;MACnBiO,mBAAmB,CAAC7lB,OAAO,CAAEhE,OAAO,IAAK8f,WAAW,CAAC9f,OAAO,EAAEvB,sBAAsB,CAAC,CAAC;MACtFP,SAAS,CAACmR,WAAW,EAAE2B,WAAW,CAACgG,QAAQ,CAAC;IAChD,CAAC,CAAC;IACF;IACA;IACA8S,cAAc,CAAC9lB,OAAO,CAAEhE,OAAO,IAAK;MAChCnD,oBAAoB,CAAC2oB,iBAAiB,EAAExlB,OAAO,EAAE,EAAE,CAAC,CAAC0C,IAAI,CAACiZ,MAAM,CAAC;IACrE,CAAC,CAAC;IACF,OAAOA,MAAM;EACjB;EACAP,YAAYA,CAACpK,WAAW,EAAE1Q,SAAS,EAAEI,eAAe,EAAE;IAClD,IAAIJ,SAAS,CAACmC,MAAM,GAAG,CAAC,EAAE;MACtB,OAAO,IAAI,CAACgD,MAAM,CAACpF,OAAO,CAAC2Q,WAAW,CAAChR,OAAO,EAAEM,SAAS,EAAE0Q,WAAW,CAACzQ,QAAQ,EAAEyQ,WAAW,CAACxQ,KAAK,EAAEwQ,WAAW,CAACvQ,MAAM,EAAEC,eAAe,CAAC;IAC5I;IACA;IACA;IACA,OAAO,IAAIrB,mBAAmB,CAAC2R,WAAW,CAACzQ,QAAQ,EAAEyQ,WAAW,CAACxQ,KAAK,CAAC;EAC3E;AACJ;AACA,MAAM6e,yBAAyB,CAAC;EAC5B/B,WAAW;EACXzG,WAAW;EACX7W,OAAO;EACPoqB,OAAO,gBAAG,IAAI/qB,mBAAmB,CAAC,CAAC;EACnCgrB,mBAAmB,GAAG,KAAK;EAC3BC,gBAAgB,gBAAG,IAAI7jB,GAAG,CAAC,CAAC;EAC5B0b,SAAS,GAAG,KAAK;EACjBsG,YAAY,GAAG,IAAI;EACnBrG,gBAAgB,GAAG,KAAK;EACxB4F,QAAQ,GAAG,KAAK;EAChBtI,MAAM,GAAG,IAAI;EACblR,SAAS,GAAG,CAAC;EACbzI,WAAWA,CAACuX,WAAW,EAAEzG,WAAW,EAAE7W,OAAO,EAAE;IAC3C,IAAI,CAACsd,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACzG,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC7W,OAAO,GAAGA,OAAO;EAC1B;EACAuoB,aAAaA,CAAC5M,MAAM,EAAE;IAClB,IAAI,IAAI,CAAC0O,mBAAmB,EACxB;IACJ,IAAI,CAACD,OAAO,GAAGzO,MAAM;IACrB,IAAI,CAAC2O,gBAAgB,CAACtmB,OAAO,CAAC,CAACumB,SAAS,EAAE5L,KAAK,KAAK;MAChD4L,SAAS,CAACvmB,OAAO,CAAEmY,QAAQ,IAAKre,cAAc,CAAC6d,MAAM,EAAEgD,KAAK,EAAE/G,SAAS,EAAEuE,QAAQ,CAAC,CAAC;IACvF,CAAC,CAAC;IACF,IAAI,CAACmO,gBAAgB,CAACxb,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACub,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACpC,iBAAiB,CAACtM,MAAM,CAACnN,SAAS,CAAC;IACxC,IAAI,CAACkR,MAAM,GAAG,KAAK;EACvB;EACAgK,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACU,OAAO;EACvB;EACAnC,iBAAiBA,CAACzZ,SAAS,EAAE;IACzB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAma,gBAAgBA,CAAChN,MAAM,EAAE;IACrB,MAAM+G,CAAC,GAAG,IAAI,CAAC0H,OAAO;IACtB,IAAI1H,CAAC,CAAC8H,eAAe,EAAE;MACnB7O,MAAM,CAACkE,OAAO,CAAC,MAAM6C,CAAC,CAAC8H,eAAe,CAAC,OAAO,CAAC,CAAC;IACpD;IACA7O,MAAM,CAACoE,MAAM,CAAC,MAAM,IAAI,CAACpD,MAAM,CAAC,CAAC,CAAC;IAClChB,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EAC1C;EACA4O,WAAWA,CAAClnB,IAAI,EAAE4Y,QAAQ,EAAE;IACxBtf,oBAAoB,CAAC,IAAI,CAACytB,gBAAgB,EAAE/mB,IAAI,EAAE,EAAE,CAAC,CAACb,IAAI,CAACyZ,QAAQ,CAAC;EACxE;EACA4D,MAAMA,CAAC3G,EAAE,EAAE;IACP,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,MAAM,EAAErR,EAAE,CAAC;IAChC;IACA,IAAI,CAACgR,OAAO,CAACrK,MAAM,CAAC3G,EAAE,CAAC;EAC3B;EACAyG,OAAOA,CAACzG,EAAE,EAAE;IACR,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,OAAO,EAAErR,EAAE,CAAC;IACjC;IACA,IAAI,CAACgR,OAAO,CAACvK,OAAO,CAACzG,EAAE,CAAC;EAC5B;EACAwC,SAASA,CAACxC,EAAE,EAAE;IACV,IAAI,IAAI,CAACsG,MAAM,EAAE;MACb,IAAI,CAAC+K,WAAW,CAAC,SAAS,EAAErR,EAAE,CAAC;IACnC;IACA,IAAI,CAACgR,OAAO,CAACxO,SAAS,CAACxC,EAAE,CAAC;EAC9B;EACAwD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACwN,OAAO,CAACxN,IAAI,CAAC,CAAC;EACvB;EACA8N,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAChL,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC0K,OAAO,CAACM,UAAU,CAAC,CAAC;EAC1D;EACAnO,IAAIA,CAAA,EAAG;IACH,CAAC,IAAI,CAACmD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC7N,IAAI,CAAC,CAAC;EACvC;EACAC,KAAKA,CAAA,EAAG;IACJ,CAAC,IAAI,CAACkD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC5N,KAAK,CAAC,CAAC;EACxC;EACAE,OAAOA,CAAA,EAAG;IACN,CAAC,IAAI,CAACgD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC1N,OAAO,CAAC,CAAC;EAC1C;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyN,OAAO,CAACzN,MAAM,CAAC,CAAC;EACzB;EACAd,OAAOA,CAAA,EAAG;IACN,IAAI,CAACsG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACiI,OAAO,CAACvO,OAAO,CAAC,CAAC;EAC1B;EACAY,KAAKA,CAAA,EAAG;IACJ,CAAC,IAAI,CAACiD,MAAM,IAAI,IAAI,CAAC0K,OAAO,CAAC3N,KAAK,CAAC,CAAC;EACxC;EACAI,WAAWA,CAAC6F,CAAC,EAAE;IACX,IAAI,CAAC,IAAI,CAAChD,MAAM,EAAE;MACd,IAAI,CAAC0K,OAAO,CAACvN,WAAW,CAAC6F,CAAC,CAAC;IAC/B;EACJ;EACAiI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjL,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC0K,OAAO,CAACO,WAAW,CAAC,CAAC;EACvD;EACA;EACAH,eAAeA,CAACI,SAAS,EAAE;IACvB,MAAMlI,CAAC,GAAG,IAAI,CAAC0H,OAAO;IACtB,IAAI1H,CAAC,CAAC8H,eAAe,EAAE;MACnB9H,CAAC,CAAC8H,eAAe,CAACI,SAAS,CAAC;IAChC;EACJ;AACJ;AACA,SAAST,kBAAkBA,CAACnnB,GAAG,EAAEuW,GAAG,EAAEzX,KAAK,EAAE;EACzC,IAAI+oB,aAAa,GAAG7nB,GAAG,CAAC+H,GAAG,CAACwO,GAAG,CAAC;EAChC,IAAIsR,aAAa,EAAE;IACf,IAAIA,aAAa,CAACpoB,MAAM,EAAE;MACtB,MAAMsZ,KAAK,GAAG8O,aAAa,CAAC5f,OAAO,CAACnJ,KAAK,CAAC;MAC1C+oB,aAAa,CAAC7O,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC;IACA,IAAI8O,aAAa,CAACpoB,MAAM,IAAI,CAAC,EAAE;MAC3BO,GAAG,CAACgI,MAAM,CAACuO,GAAG,CAAC;IACnB;EACJ;EACA,OAAOsR,aAAa;AACxB;AACA,SAAS9M,qBAAqBA,CAACjc,KAAK,EAAE;EAClC;EACA;EACA;EACA,OAAOA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,IAAI;AACvC;AACA,SAASoiB,aAAaA,CAACgB,IAAI,EAAE;EACzB,OAAOA,IAAI,IAAIA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACzC;AACA,SAAStG,mBAAmBA,CAAC1C,SAAS,EAAE;EACpC,OAAOA,SAAS,IAAI,OAAO,IAAIA,SAAS,IAAI,MAAM;AACtD;AACA,SAAS4O,YAAYA,CAAC9qB,OAAO,EAAE8B,KAAK,EAAE;EAClC,MAAMipB,QAAQ,GAAG/qB,OAAO,CAACT,KAAK,CAACyrB,OAAO;EACtChrB,OAAO,CAACT,KAAK,CAACyrB,OAAO,GAAGlpB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,MAAM;EACtD,OAAOipB,QAAQ;AACnB;AACA,SAAStD,qBAAqBA,CAACwD,SAAS,EAAExlB,MAAM,EAAEyO,QAAQ,EAAEgX,eAAe,EAAEC,YAAY,EAAE;EACvF,MAAMC,SAAS,GAAG,EAAE;EACpBlX,QAAQ,CAAClQ,OAAO,CAAEhE,OAAO,IAAKorB,SAAS,CAAC1oB,IAAI,CAACooB,YAAY,CAAC9qB,OAAO,CAAC,CAAC,CAAC;EACpE,MAAMqrB,cAAc,GAAG,EAAE;EACzBH,eAAe,CAAClnB,OAAO,CAAC,CAACN,KAAK,EAAE1D,OAAO,KAAK;IACxC,MAAMgI,MAAM,GAAG,IAAIvB,GAAG,CAAC,CAAC;IACxB/C,KAAK,CAACM,OAAO,CAAEnE,IAAI,IAAK;MACpB,MAAMiC,KAAK,GAAG2D,MAAM,CAAC7G,YAAY,CAACoB,OAAO,EAAEH,IAAI,EAAEsrB,YAAY,CAAC;MAC9DnjB,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAEiC,KAAK,CAAC;MACvB;MACA;MACA,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACW,MAAM,IAAI,CAAC,EAAE;QAC7BzC,OAAO,CAAC4d,YAAY,CAAC,GAAGD,0BAA0B;QAClD0N,cAAc,CAAC3oB,IAAI,CAAC1C,OAAO,CAAC;MAChC;IACJ,CAAC,CAAC;IACFirB,SAAS,CAACvkB,GAAG,CAAC1G,OAAO,EAAEgI,MAAM,CAAC;EAClC,CAAC,CAAC;EACF;EACA;EACA,IAAIqE,CAAC,GAAG,CAAC;EACT6H,QAAQ,CAAClQ,OAAO,CAAEhE,OAAO,IAAK8qB,YAAY,CAAC9qB,OAAO,EAAEorB,SAAS,CAAC/e,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,OAAOgf,cAAc;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrF,YAAYA,CAACsF,KAAK,EAAEpF,KAAK,EAAE;EAChC,MAAMqF,OAAO,GAAG,IAAI9kB,GAAG,CAAC,CAAC;EACzB6kB,KAAK,CAACtnB,OAAO,CAAEmiB,IAAI,IAAKoF,OAAO,CAAC7kB,GAAG,CAACyf,IAAI,EAAE,EAAE,CAAC,CAAC;EAC9C,IAAID,KAAK,CAACzjB,MAAM,IAAI,CAAC,EACjB,OAAO8oB,OAAO;EAClB,MAAMC,SAAS,GAAG,CAAC;EACnB,MAAMC,OAAO,GAAG,IAAIzpB,GAAG,CAACkkB,KAAK,CAAC;EAC9B,MAAMwF,YAAY,GAAG,IAAIjlB,GAAG,CAAC,CAAC;EAC9B,SAASklB,OAAOA,CAACzG,IAAI,EAAE;IACnB,IAAI,CAACA,IAAI,EACL,OAAOsG,SAAS;IACpB,IAAIrF,IAAI,GAAGuF,YAAY,CAAC3gB,GAAG,CAACma,IAAI,CAAC;IACjC,IAAIiB,IAAI,EACJ,OAAOA,IAAI;IACf,MAAMzE,MAAM,GAAGwD,IAAI,CAACvD,UAAU;IAC9B,IAAI4J,OAAO,CAACjpB,GAAG,CAACof,MAAM,CAAC,EAAE;MACrB;MACAyE,IAAI,GAAGzE,MAAM;IACjB,CAAC,MACI,IAAI+J,OAAO,CAACnpB,GAAG,CAACof,MAAM,CAAC,EAAE;MAC1B;MACAyE,IAAI,GAAGqF,SAAS;IACpB,CAAC,MACI;MACD;MACArF,IAAI,GAAGwF,OAAO,CAACjK,MAAM,CAAC;IAC1B;IACAgK,YAAY,CAAChlB,GAAG,CAACwe,IAAI,EAAEiB,IAAI,CAAC;IAC5B,OAAOA,IAAI;EACf;EACAD,KAAK,CAACliB,OAAO,CAAEkhB,IAAI,IAAK;IACpB,MAAMiB,IAAI,GAAGwF,OAAO,CAACzG,IAAI,CAAC;IAC1B,IAAIiB,IAAI,KAAKqF,SAAS,EAAE;MACpBD,OAAO,CAACxgB,GAAG,CAACob,IAAI,CAAC,CAACzjB,IAAI,CAACwiB,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;EACF,OAAOqG,OAAO;AAClB;AACA,SAAS7M,QAAQA,CAAC1e,OAAO,EAAEomB,SAAS,EAAE;EAClCpmB,OAAO,CAACglB,SAAS,EAAEzc,GAAG,CAAC6d,SAAS,CAAC;AACrC;AACA,SAAStG,WAAWA,CAAC9f,OAAO,EAAEomB,SAAS,EAAE;EACrCpmB,OAAO,CAACglB,SAAS,EAAE4G,MAAM,CAACxF,SAAS,CAAC;AACxC;AACA,SAAS6C,6BAA6BA,CAAC7H,MAAM,EAAEphB,OAAO,EAAEib,OAAO,EAAE;EAC7Drd,mBAAmB,CAACqd,OAAO,CAAC,CAAC8E,MAAM,CAAC,MAAMqB,MAAM,CAACN,gBAAgB,CAAC9gB,OAAO,CAAC,CAAC;AAC/E;AACA,SAASgqB,mBAAmBA,CAAC/O,OAAO,EAAE;EAClC,MAAM4Q,YAAY,GAAG,EAAE;EACvBC,yBAAyB,CAAC7Q,OAAO,EAAE4Q,YAAY,CAAC;EAChD,OAAOA,YAAY;AACvB;AACA,SAASC,yBAAyBA,CAAC7Q,OAAO,EAAE4Q,YAAY,EAAE;EACtD,KAAK,IAAIxf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4O,OAAO,CAACxY,MAAM,EAAE4J,CAAC,EAAE,EAAE;IACrC,MAAMsP,MAAM,GAAGV,OAAO,CAAC5O,CAAC,CAAC;IACzB,IAAIsP,MAAM,YAAYhc,oBAAoB,EAAE;MACxCmsB,yBAAyB,CAACnQ,MAAM,CAACV,OAAO,EAAE4Q,YAAY,CAAC;IAC3D,CAAC,MACI;MACDA,YAAY,CAACnpB,IAAI,CAACiZ,MAAM,CAAC;IAC7B;EACJ;AACJ;AACA,SAAS2D,SAASA,CAACgD,CAAC,EAAEC,CAAC,EAAE;EACrB,MAAMwJ,EAAE,GAAGphB,MAAM,CAACrE,IAAI,CAACgc,CAAC,CAAC;EACzB,MAAM0J,EAAE,GAAGrhB,MAAM,CAACrE,IAAI,CAACic,CAAC,CAAC;EACzB,IAAIwJ,EAAE,CAACtpB,MAAM,IAAIupB,EAAE,CAACvpB,MAAM,EACtB,OAAO,KAAK;EAChB,KAAK,IAAI4J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0f,EAAE,CAACtpB,MAAM,EAAE4J,CAAC,EAAE,EAAE;IAChC,MAAMxM,IAAI,GAAGksB,EAAE,CAAC1f,CAAC,CAAC;IAClB,IAAI,CAACkW,CAAC,CAACja,cAAc,CAACzI,IAAI,CAAC,IAAIyiB,CAAC,CAACziB,IAAI,CAAC,KAAK0iB,CAAC,CAAC1iB,IAAI,CAAC,EAC9C,OAAO,KAAK;EACpB;EACA,OAAO,IAAI;AACf;AACA,SAASynB,sBAAsBA,CAACtnB,OAAO,EAAE0lB,mBAAmB,EAAEC,oBAAoB,EAAE;EAChF,MAAMsG,SAAS,GAAGtG,oBAAoB,CAAC5a,GAAG,CAAC/K,OAAO,CAAC;EACnD,IAAI,CAACisB,SAAS,EACV,OAAO,KAAK;EAChB,IAAIC,QAAQ,GAAGxG,mBAAmB,CAAC3a,GAAG,CAAC/K,OAAO,CAAC;EAC/C,IAAIksB,QAAQ,EAAE;IACVD,SAAS,CAACjoB,OAAO,CAAE8a,IAAI,IAAKoN,QAAQ,CAAC3jB,GAAG,CAACuW,IAAI,CAAC,CAAC;EACnD,CAAC,MACI;IACD4G,mBAAmB,CAAChf,GAAG,CAAC1G,OAAO,EAAEisB,SAAS,CAAC;EAC/C;EACAtG,oBAAoB,CAAC3a,MAAM,CAAChL,OAAO,CAAC;EACpC,OAAO,IAAI;AACf;AAEA,MAAMmsB,eAAe,CAAC;EAClBrmB,OAAO;EACPiU,WAAW;EACXqS,iBAAiB;EACjBC,eAAe;EACfC,aAAa,GAAG,CAAC,CAAC;EAClB;EACAjJ,iBAAiB,GAAGA,CAACrjB,OAAO,EAAEgG,OAAO,KAAK,CAAE,CAAC;EAC7CD,WAAWA,CAACwmB,GAAG,EAAEzmB,OAAO,EAAEiU,WAAW,EAAE;IACnC,IAAI,CAACjU,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiU,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqS,iBAAiB,GAAG,IAAIzJ,yBAAyB,CAAC4J,GAAG,CAACC,IAAI,EAAE1mB,OAAO,EAAEiU,WAAW,CAAC;IACtF,IAAI,CAACsS,eAAe,GAAG,IAAIxR,uBAAuB,CAAC0R,GAAG,CAACC,IAAI,EAAE1mB,OAAO,EAAEiU,WAAW,CAAC;IAClF,IAAI,CAACqS,iBAAiB,CAAC/I,iBAAiB,GAAG,CAACrjB,OAAO,EAAEgG,OAAO,KAAK,IAAI,CAACqd,iBAAiB,CAACrjB,OAAO,EAAEgG,OAAO,CAAC;EAC7G;EACA+d,eAAeA,CAAC0I,WAAW,EAAEnP,WAAW,EAAEc,WAAW,EAAE7a,IAAI,EAAEmC,QAAQ,EAAE;IACnE,MAAMgnB,QAAQ,GAAGD,WAAW,GAAG,GAAG,GAAGlpB,IAAI;IACzC,IAAI4b,OAAO,GAAG,IAAI,CAACmN,aAAa,CAACI,QAAQ,CAAC;IAC1C,IAAI,CAACvN,OAAO,EAAE;MACV,MAAMzd,MAAM,GAAG,EAAE;MACjB,MAAMkB,QAAQ,GAAG,EAAE;MACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAAC,IAAI,CAACM,OAAO,EAAEJ,QAAQ,EAAEhE,MAAM,EAAEkB,QAAQ,CAAC;MACvE,IAAIlB,MAAM,CAACe,MAAM,EAAE;QACf,MAAM/D,kBAAkB,CAAC6E,IAAI,EAAE7B,MAAM,CAAC;MAC1C;MACA,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAIyB,QAAQ,CAACH,MAAM,EAAE;UACjBa,gBAAgB,CAACC,IAAI,EAAEX,QAAQ,CAAC;QACpC;MACJ;MACAuc,OAAO,GAAGtF,YAAY,CAACtW,IAAI,EAAE4C,GAAG,EAAE,IAAI,CAAC4T,WAAW,CAAC;MACnD,IAAI,CAACuS,aAAa,CAACI,QAAQ,CAAC,GAAGvN,OAAO;IAC1C;IACA,IAAI,CAACiN,iBAAiB,CAACrI,eAAe,CAACzG,WAAW,EAAE/Z,IAAI,EAAE4b,OAAO,CAAC;EACtE;EACAjE,QAAQA,CAACoC,WAAW,EAAEc,WAAW,EAAE;IAC/B,IAAI,CAACgO,iBAAiB,CAAClR,QAAQ,CAACoC,WAAW,EAAEc,WAAW,CAAC;EAC7D;EACAvC,OAAOA,CAACyB,WAAW,EAAEtX,OAAO,EAAE;IAC1B,IAAI,CAAComB,iBAAiB,CAACvQ,OAAO,CAACyB,WAAW,EAAEtX,OAAO,CAAC;EACxD;EACA2mB,QAAQA,CAACrP,WAAW,EAAEtd,OAAO,EAAE0hB,MAAM,EAAEyC,YAAY,EAAE;IACjD,IAAI,CAACiI,iBAAiB,CAACpK,UAAU,CAAC1E,WAAW,EAAEtd,OAAO,EAAE0hB,MAAM,EAAEyC,YAAY,CAAC;EACjF;EACAyI,QAAQA,CAACtP,WAAW,EAAEtd,OAAO,EAAEgG,OAAO,EAAE;IACpC,IAAI,CAAComB,iBAAiB,CAACjL,UAAU,CAAC7D,WAAW,EAAEtd,OAAO,EAAEgG,OAAO,CAAC;EACpE;EACA6mB,iBAAiBA,CAAC7sB,OAAO,EAAE8sB,OAAO,EAAE;IAChC,IAAI,CAACV,iBAAiB,CAAC/H,qBAAqB,CAACrkB,OAAO,EAAE8sB,OAAO,CAAC;EAClE;EACAC,OAAOA,CAACzP,WAAW,EAAEtd,OAAO,EAAEgtB,QAAQ,EAAElrB,KAAK,EAAE;IAC3C,IAAIkrB,QAAQ,CAAC/lB,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAC3B,MAAM,CAACkU,EAAE,EAAE8R,MAAM,CAAC,GAAGtuB,oBAAoB,CAACquB,QAAQ,CAAC;MACnD,MAAM1Q,IAAI,GAAGxa,KAAK;MAClB,IAAI,CAACuqB,eAAe,CAAChQ,OAAO,CAAClB,EAAE,EAAEnb,OAAO,EAAEitB,MAAM,EAAE3Q,IAAI,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAAC8P,iBAAiB,CAACjN,OAAO,CAAC7B,WAAW,EAAEtd,OAAO,EAAEgtB,QAAQ,EAAElrB,KAAK,CAAC;IACzE;EACJ;EACAma,MAAMA,CAACqB,WAAW,EAAEtd,OAAO,EAAEkc,SAAS,EAAEgR,UAAU,EAAE/Q,QAAQ,EAAE;IAC1D;IACA,IAAID,SAAS,CAACjV,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAC5B,MAAM,CAACkU,EAAE,EAAE8R,MAAM,CAAC,GAAGtuB,oBAAoB,CAACud,SAAS,CAAC;MACpD,OAAO,IAAI,CAACmQ,eAAe,CAACpQ,MAAM,CAACd,EAAE,EAAEnb,OAAO,EAAEitB,MAAM,EAAE9Q,QAAQ,CAAC;IACrE;IACA,OAAO,IAAI,CAACiQ,iBAAiB,CAACnQ,MAAM,CAACqB,WAAW,EAAEtd,OAAO,EAAEkc,SAAS,EAAEgR,UAAU,EAAE/Q,QAAQ,CAAC;EAC/F;EACAgJ,KAAKA,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAE;IACpB,IAAI,CAACkK,iBAAiB,CAACjH,KAAK,CAACjD,WAAW,CAAC;EAC7C;EACA,IAAIjH,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,GAAG,IAAI,CAACmR,iBAAiB,CAACnR,OAAO,EAAE,GAAG,IAAI,CAACoR,eAAe,CAACpR,OAAO,CAAC;EAC/E;EACA4J,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACuH,iBAAiB,CAACvH,iBAAiB,CAAC,CAAC;EACrD;EACArE,wBAAwBA,CAAC2M,EAAE,EAAE;IACzB,IAAI,CAACf,iBAAiB,CAAC5L,wBAAwB,CAAC2M,EAAE,CAAC;EACvD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACptB,OAAO,EAAEgI,MAAM,EAAE;EACjD,IAAIqlB,WAAW,GAAG,IAAI;EACtB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAI9iB,KAAK,CAACC,OAAO,CAACzC,MAAM,CAAC,IAAIA,MAAM,CAACvF,MAAM,EAAE;IACxC4qB,WAAW,GAAGE,yBAAyB,CAACvlB,MAAM,CAAC,CAAC,CAAC,CAAC;IAClD,IAAIA,MAAM,CAACvF,MAAM,GAAG,CAAC,EAAE;MACnB6qB,SAAS,GAAGC,yBAAyB,CAACvlB,MAAM,CAACA,MAAM,CAACvF,MAAM,GAAG,CAAC,CAAC,CAAC;IACpE;EACJ,CAAC,MACI,IAAIuF,MAAM,YAAYvB,GAAG,EAAE;IAC5B4mB,WAAW,GAAGE,yBAAyB,CAACvlB,MAAM,CAAC;EACnD;EACA,OAAOqlB,WAAW,IAAIC,SAAS,GAAG,IAAIE,kBAAkB,CAACxtB,OAAO,EAAEqtB,WAAW,EAAEC,SAAS,CAAC,GAAG,IAAI;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,IAQME,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrBC,QAAQ;IACRC,YAAY;IACZC,UAAU;IACV,OAAOC,sBAAsB,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;IAC7DC,MAAM,GAAG,CAAC,CAAC;IACXC,cAAc;IACdhoB,WAAWA,CAAC0nB,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAE;MAC5C,IAAI,CAACF,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B,IAAIK,aAAa,GAAGR,kBAAkB,CAACI,sBAAsB,CAAC7iB,GAAG,CAAC0iB,QAAQ,CAAC;MAC3E,IAAI,CAACO,aAAa,EAAE;QAChBR,kBAAkB,CAACI,sBAAsB,CAAClnB,GAAG,CAAC+mB,QAAQ,EAAGO,aAAa,GAAG,IAAIvnB,GAAG,CAAC,CAAE,CAAC;MACxF;MACA,IAAI,CAACsnB,cAAc,GAAGC,aAAa;IACvC;IACAC,KAAKA,CAAA,EAAG;MACJ,IAAI,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,uCAAuC;QACvD,IAAI,IAAI,CAACJ,YAAY,EAAE;UACnBxvB,SAAS,CAAC,IAAI,CAACuvB,QAAQ,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAACK,cAAc,CAAC;QACpE;QACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;MACpB;IACJ;IACAnR,MAAMA,CAAA,EAAG;MACL,IAAI,CAACsR,KAAK,CAAC,CAAC;MACZ,IAAI,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,wCAAwC;QACxD5vB,SAAS,CAAC,IAAI,CAACuvB,QAAQ,EAAE,IAAI,CAACM,cAAc,CAAC;QAC7C,IAAI,IAAI,CAACJ,UAAU,EAAE;UACjBzvB,SAAS,CAAC,IAAI,CAACuvB,QAAQ,EAAE,IAAI,CAACE,UAAU,CAAC;UACzC,IAAI,CAACA,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MACpB;IACJ;IACAjS,OAAOA,CAAA,EAAG;MACN,IAAI,CAACc,MAAM,CAAC,CAAC;MACb,IAAI,IAAI,CAACmR,MAAM,GAAG,CAAC,CAAC,yCAAyC;QACzDN,kBAAkB,CAACI,sBAAsB,CAAC5iB,MAAM,CAAC,IAAI,CAACyiB,QAAQ,CAAC;QAC/D,IAAI,IAAI,CAACC,YAAY,EAAE;UACnBzvB,WAAW,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;UAC7C,IAAI,CAACC,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI,IAAI,CAACA,UAAU,EAAE;UACjB1vB,WAAW,CAAC,IAAI,CAACwvB,QAAQ,EAAE,IAAI,CAACE,UAAU,CAAC;UAC3C,IAAI,CAACA,UAAU,GAAG,IAAI;QAC1B;QACAzvB,SAAS,CAAC,IAAI,CAACuvB,QAAQ,EAAE,IAAI,CAACM,cAAc,CAAC;QAC7C,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;MACpB;IACJ;EACJ;EAAC,OApDKN,kBAAkB;AAAA;AAqDxB,SAASD,yBAAyBA,CAACvlB,MAAM,EAAE;EACvC,IAAI5D,MAAM,GAAG,IAAI;EACjB4D,MAAM,CAAChE,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAK;IAC1B,IAAIquB,oBAAoB,CAACruB,IAAI,CAAC,EAAE;MAC5BuE,MAAM,GAAGA,MAAM,IAAI,IAAIqC,GAAG,CAAC,CAAC;MAC5BrC,MAAM,CAACsC,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC;IACzB;EACJ,CAAC,CAAC;EACF,OAAO9Q,MAAM;AACjB;AACA,SAAS8pB,oBAAoBA,CAACruB,IAAI,EAAE;EAChC,OAAOA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,UAAU;AACpD;AAEA,MAAMsuB,mBAAmB,CAAC;EACtBnuB,OAAO;EACPM,SAAS;EACTuH,OAAO;EACPumB,cAAc;EACdC,UAAU,GAAG,EAAE;EACfC,WAAW,GAAG,EAAE;EAChBC,aAAa,GAAG,EAAE;EAClBC,SAAS;EACTC,MAAM;EACNC,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,QAAQ,GAAG,KAAK;EAChBC,UAAU,GAAG,KAAK;EAClBC,cAAc;EACd;EACA;EACA;EACAC,kBAAkB,GAAG,EAAE;EACvBC,mBAAmB,GAAG,EAAE;EACxB;EACAC,SAAS;EACTjb,IAAI,GAAG,CAAC;EACRyU,YAAY,GAAG,IAAI;EACnByG,eAAe,gBAAG,IAAIzoB,GAAG,CAAC,CAAC;EAC3BV,WAAWA,CAAC/F,OAAO,EAAEM,SAAS,EAAEuH,OAAO,EAAEumB,cAAc,EAAE;IACrD,IAAI,CAACpuB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACuH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACumB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACI,SAAS,GAAG3mB,OAAO,CAAC,UAAU,CAAC;IACpC,IAAI,CAAC4mB,MAAM,GAAG5mB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;IACnC,IAAI,CAACmM,IAAI,GAAG,IAAI,CAACwa,SAAS,GAAG,IAAI,CAACC,MAAM;EAC5C;EACAU,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACR,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACN,UAAU,CAACrqB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACrC,IAAI,CAACiV,UAAU,GAAG,EAAE;IACxB;EACJ;EACAzR,IAAIA,CAAA,EAAG;IACH,IAAI,CAACxB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACgU,yBAAyB,CAAC,CAAC;EACpC;EACAhU,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACsT,YAAY,EACjB;IACJ,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,MAAMpuB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA,IAAI,CAAC2uB,SAAS,GAAG,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACrvB,OAAO,EAAEM,SAAS,EAAE,IAAI,CAACuH,OAAO,CAAC;IACjF,IAAI,CAACinB,cAAc,GAAGxuB,SAAS,CAACmC,MAAM,GAAGnC,SAAS,CAACA,SAAS,CAACmC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIgE,GAAG,CAAC,CAAC;IACpF,MAAM6oB,QAAQ,GAAGA,CAAA,KAAM,IAAI,CAACH,SAAS,CAAC,CAAC;IACvC,IAAI,CAACF,SAAS,CAACM,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IACnD,IAAI,CAAC1T,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACqT,SAAS,CAACO,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC1D,CAAC,CAAC;EACN;EACAF,yBAAyBA,CAAA,EAAG;IACxB;IACA,IAAI,IAAI,CAACX,MAAM,EAAE;MACb,IAAI,CAACgB,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACR,SAAS,CAACzS,KAAK,CAAC,CAAC;IAC1B;EACJ;EACAkT,yBAAyBA,CAACpvB,SAAS,EAAE;IACjC,MAAMqvB,GAAG,GAAG,EAAE;IACdrvB,SAAS,CAAC0D,OAAO,CAAE4rB,KAAK,IAAK;MACzBD,GAAG,CAACjtB,IAAI,CAACiI,MAAM,CAACklB,WAAW,CAACD,KAAK,CAAC,CAAC;IACvC,CAAC,CAAC;IACF,OAAOD,GAAG;EACd;EACA;EACAN,oBAAoBA,CAACrvB,OAAO,EAAEM,SAAS,EAAEuH,OAAO,EAAE;IAC9C,OAAO7H,OAAO,CAACK,OAAO,CAAC,IAAI,CAACqvB,yBAAyB,CAACpvB,SAAS,CAAC,EAAEuH,OAAO,CAAC;EAC9E;EACAgY,OAAOA,CAACzG,EAAE,EAAE;IACR,IAAI,CAAC4V,mBAAmB,CAACtsB,IAAI,CAAC0W,EAAE,CAAC;IACjC,IAAI,CAACkV,WAAW,CAAC5rB,IAAI,CAAC0W,EAAE,CAAC;EAC7B;EACA2G,MAAMA,CAAC3G,EAAE,EAAE;IACP,IAAI,CAAC2V,kBAAkB,CAACrsB,IAAI,CAAC0W,EAAE,CAAC;IAChC,IAAI,CAACiV,UAAU,CAAC3rB,IAAI,CAAC0W,EAAE,CAAC;EAC5B;EACAwC,SAASA,CAACxC,EAAE,EAAE;IACV,IAAI,CAACmV,aAAa,CAAC7rB,IAAI,CAAC0W,EAAE,CAAC;EAC/B;EACAmD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnB,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC,IAAI,CAACsP,UAAU,CAAC,CAAC,EAAE;MACpB,IAAI,CAAC4D,WAAW,CAACtqB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACtC,IAAI,CAACkV,WAAW,GAAG,EAAE;MACrB,IAAI,CAACM,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACR,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAACH,KAAK,CAAC,CAAC;MAC/B;IACJ;IACA,IAAI,CAACgB,SAAS,CAAC1S,IAAI,CAAC,CAAC;EACzB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACI,IAAI,CAAC,CAAC;IACX,IAAI,CAACqS,SAAS,CAACzS,KAAK,CAAC,CAAC;EAC1B;EACAG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACwR,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACzR,MAAM,CAAC,CAAC;IAChC;IACA,IAAI,CAACwS,SAAS,CAAC,CAAC;IAChB,IAAI,CAACF,SAAS,CAACtS,MAAM,CAAC,CAAC;EAC3B;EACAF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACgT,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACZ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACN,WAAW,GAAG,IAAI,CAACU,mBAAmB;IAC3C,IAAI,CAACX,UAAU,GAAG,IAAI,CAACU,kBAAkB;EAC7C;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACa,MAAM,CAAC,CAAC;IAC3B;EACJ;EACApT,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACF,IAAI,CAAC,CAAC;EACf;EACAmO,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACkE,QAAQ;EACxB;EACA/S,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACgT,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACY,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACN,SAAS,CAAC,CAAC;MAChB,IAAI,IAAI,CAACf,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAACvS,OAAO,CAAC,CAAC;MACjC;MACA,IAAI,CAAC0S,aAAa,CAACvqB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACxC,IAAI,CAACmV,aAAa,GAAG,EAAE;IAC3B;EACJ;EACA1R,WAAWA,CAAC6F,CAAC,EAAE;IACX,IAAI,IAAI,CAACuM,SAAS,KAAKrX,SAAS,EAAE;MAC9B,IAAI,CAACgF,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACqS,SAAS,CAACtoB,WAAW,GAAG+b,CAAC,GAAG,IAAI,CAAC1O,IAAI;EAC9C;EACA2W,WAAWA,CAAA,EAAG;IACV;IACA,OAAO,EAAE,IAAI,CAACsE,SAAS,CAACtoB,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,CAACqN,IAAI;EACzD;EACA,IAAIxF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACigB,MAAM,GAAG,IAAI,CAACD,SAAS;EACvC;EACA7E,aAAaA,CAAA,EAAG;IACZ,MAAM3hB,MAAM,GAAG,IAAIvB,GAAG,CAAC,CAAC;IACxB,IAAI,IAAI,CAACikB,UAAU,CAAC,CAAC,EAAE;MACnB;MACA;MACA;MACA,MAAMjV,aAAa,GAAG,IAAI,CAACqZ,cAAc;MACzCrZ,aAAa,CAACzR,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAK;QACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;UACnBmI,MAAM,CAACtB,GAAG,CAAC7G,IAAI,EAAE,IAAI,CAAC8uB,SAAS,GAAGzZ,GAAG,GAAGtW,YAAY,CAAC,IAAI,CAACoB,OAAO,EAAEH,IAAI,CAAC,CAAC;QAC7E;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACqvB,eAAe,GAAGlnB,MAAM;EACjC;EACA;EACAwiB,eAAeA,CAACI,SAAS,EAAE;IACvB,MAAMmF,OAAO,GAAGnF,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC0D,WAAW,GAAG,IAAI,CAACD,UAAU;IAC1E0B,OAAO,CAAC/rB,OAAO,CAAEoV,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IAC7B2W,OAAO,CAACttB,MAAM,GAAG,CAAC;EACtB;AACJ;AAEA,MAAMutB,mBAAmB,CAAC;EACtBx0B,qBAAqBA,CAACqE,IAAI,EAAE;IACxB;IACA,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,OAAO3F,qBAAqB,CAACqE,IAAI,CAAC;IACtC;IACA,OAAO,IAAI;EACf;EACA+Y,+BAA+BA,CAAC/Y,IAAI,EAAE;IAClC;IACA,IAAI,OAAOsB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAM8uB,OAAO,GAAGpxB,mBAAmB,CAACgB,IAAI,CAAC;MACzC,OAAOf,kCAAkC,CAACmxB,OAAO,CAAC;IACtD;IACA,OAAO,IAAI;EACf;EACAx0B,eAAeA,CAACqE,IAAI,EAAEC,IAAI,EAAE;IACxB,OAAOtE,eAAe,CAACqE,IAAI,EAAEC,IAAI,CAAC;EACtC;EACArE,gBAAgBA,CAACsE,OAAO,EAAE;IACtB,OAAOtE,gBAAgB,CAACsE,OAAO,CAAC;EACpC;EACAC,KAAKA,CAACD,OAAO,EAAEE,QAAQ,EAAEC,KAAK,EAAE;IAC5B,OAAOxE,WAAW,CAACqE,OAAO,EAAEE,QAAQ,EAAEC,KAAK,CAAC;EAChD;EACAvB,YAAYA,CAACoB,OAAO,EAAEH,IAAI,EAAEO,YAAY,EAAE;IACtC,OAAOxB,YAAY,CAACoB,OAAO,EAAEH,IAAI,CAAC;EACtC;EACAQ,OAAOA,CAACL,OAAO,EAAEM,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,GAAG,EAAE,EAAE;IACvE,MAAMwvB,IAAI,GAAG1vB,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU;IAC7C,MAAM2vB,aAAa,GAAG;MAAE5vB,QAAQ;MAAEC,KAAK;MAAE0vB;IAAK,CAAC;IAC/C;IACA;IACA,IAAIzvB,MAAM,EAAE;MACR0vB,aAAa,CAAC,QAAQ,CAAC,GAAG1vB,MAAM;IACpC;IACA,MAAM2vB,cAAc,GAAG,IAAI3pB,GAAG,CAAC,CAAC;IAChC,MAAM4pB,2BAA2B,GAAI3vB,eAAe,CAACoC,MAAM,CAAE6Y,MAAM,IAAKA,MAAM,YAAYwS,mBAAmB,CAAE;IAC/G,IAAIpvB,8BAA8B,CAACwB,QAAQ,EAAEC,KAAK,CAAC,EAAE;MACjD6vB,2BAA2B,CAACrsB,OAAO,CAAE2X,MAAM,IAAK;QAC5CA,MAAM,CAACuT,eAAe,CAAClrB,OAAO,CAAC,CAACkR,GAAG,EAAErV,IAAI,KAAKuwB,cAAc,CAAC1pB,GAAG,CAAC7G,IAAI,EAAEqV,GAAG,CAAC,CAAC;MAChF,CAAC,CAAC;IACN;IACA,IAAIZ,UAAU,GAAGtV,oBAAoB,CAACsB,SAAS,CAAC,CAAC0C,GAAG,CAAEgF,MAAM,IAAK,IAAIvB,GAAG,CAACuB,MAAM,CAAC,CAAC;IACjFsM,UAAU,GAAGrV,kCAAkC,CAACe,OAAO,EAAEsU,UAAU,EAAE8b,cAAc,CAAC;IACpF,MAAME,aAAa,GAAGlD,0BAA0B,CAACptB,OAAO,EAAEsU,UAAU,CAAC;IACrE,OAAO,IAAI6Z,mBAAmB,CAACnuB,OAAO,EAAEsU,UAAU,EAAE6b,aAAa,EAAEG,aAAa,CAAC;EACrF;AACJ;AAEA,SAASC,YAAYA,CAACnpB,IAAI,EAAEmlB,GAAG,EAAE;EAC7B;EACA,IAAInlB,IAAI,KAAK,MAAM,EAAE;IACjB,OAAO,IAAI+kB,eAAe,CAACI,GAAG,EAAE,IAAI3sB,mBAAmB,CAAC,CAAC,EAAE,IAAI2B,4BAA4B,CAAC,CAAC,CAAC;EAClG;EACA,OAAO,IAAI4qB,eAAe,CAACI,GAAG,EAAE,IAAIyD,mBAAmB,CAAC,CAAC,EAAE,IAAI/tB,4BAA4B,CAAC,CAAC,CAAC;AAClG;AAEA,MAAMuuB,SAAS,CAAC;EACZ1qB,OAAO;EACP2qB,aAAa;EACb1qB,WAAWA,CAACD,OAAO,EAAEkP,KAAK,EAAE;IACxB,IAAI,CAAClP,OAAO,GAAGA,OAAO;IACtB,MAAMpE,MAAM,GAAG,EAAE;IACjB,MAAMkB,QAAQ,GAAG,EAAE;IACnB,MAAMuD,GAAG,GAAGX,iBAAiB,CAACM,OAAO,EAAEkP,KAAK,EAAEtT,MAAM,EAAEkB,QAAQ,CAAC;IAC/D,IAAIlB,MAAM,CAACe,MAAM,EAAE;MACf,MAAMvD,gBAAgB,CAACwC,MAAM,CAAC;IAClC;IACA,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAIyB,QAAQ,CAACH,MAAM,EAAE;QACjBU,cAAc,CAACP,QAAQ,CAAC;MAC5B;IACJ;IACA,IAAI,CAAC6tB,aAAa,GAAGtqB,GAAG;EAC5B;EACAuqB,cAAcA,CAAC1wB,OAAO,EAAEwP,cAAc,EAAEmhB,iBAAiB,EAAE9oB,OAAO,EAAE6H,eAAe,EAAE;IACjF,MAAMue,KAAK,GAAGzjB,KAAK,CAACC,OAAO,CAAC+E,cAAc,CAAC,GACrCrQ,eAAe,CAACqQ,cAAc,CAAC,GAC/BA,cAAc;IACpB,MAAMohB,IAAI,GAAGpmB,KAAK,CAACC,OAAO,CAACkmB,iBAAiB,CAAC,GACvCxxB,eAAe,CAACwxB,iBAAiB,CAAC,GAClCA,iBAAiB;IACvB,MAAMjvB,MAAM,GAAG,EAAE;IACjBgO,eAAe,GAAGA,eAAe,IAAI,IAAIjB,qBAAqB,CAAC,CAAC;IAChE,MAAMrK,MAAM,GAAGgL,uBAAuB,CAAC,IAAI,CAACtJ,OAAO,EAAE9F,OAAO,EAAE,IAAI,CAACywB,aAAa,EAAEhzB,eAAe,EAAED,eAAe,EAAEywB,KAAK,EAAE2C,IAAI,EAAE/oB,OAAO,EAAE6H,eAAe,EAAEhO,MAAM,CAAC;IAClK,IAAIA,MAAM,CAACe,MAAM,EAAE;MACf,MAAMrD,cAAc,CAACsC,MAAM,CAAC;IAChC;IACA,OAAO0C,MAAM;EACjB;AACJ;AAEA,MAAMysB,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,uBAAuB,GAAG,YAAY;AAC5C,MAAMC,qBAAqB,CAAC;EACxBzT,WAAW;EACX0T,QAAQ;EACR5P,MAAM;EACN6P,UAAU;EACV;EACA;EACAC,KAAK,GAAG,CAAC,CAAC;EACVnrB,WAAWA,CAACuX,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAE6P,UAAU,EAAE;IACnD,IAAI,CAAC3T,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC0T,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5P,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6P,UAAU,GAAGA,UAAU;EAChC;EACA,IAAInS,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkS,QAAQ,CAAClS,IAAI;EAC7B;EACAqS,WAAWA,CAACjM,IAAI,EAAE;IACd,IAAI,CAAC8L,QAAQ,CAACG,WAAW,GAAGjM,IAAI,CAAC;EACrC;EACArJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuF,MAAM,CAACvF,OAAO,CAAC,IAAI,CAACyB,WAAW,EAAE,IAAI,CAAC0T,QAAQ,CAAC;IACpD,IAAI,CAAC5P,MAAM,CAACZ,wBAAwB,CAAC,MAAM;MACvC;MACA;MACA4Q,cAAc,CAAC,MAAM;QACjB,IAAI,CAACJ,QAAQ,CAACnV,OAAO,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACoV,UAAU,GAAG,CAAC;EACvB;EACAI,aAAaA,CAAC9tB,IAAI,EAAE+tB,SAAS,EAAE;IAC3B,OAAO,IAAI,CAACN,QAAQ,CAACK,aAAa,CAAC9tB,IAAI,EAAE+tB,SAAS,CAAC;EACvD;EACAC,aAAaA,CAACzvB,KAAK,EAAE;IACjB,OAAO,IAAI,CAACkvB,QAAQ,CAACO,aAAa,CAACzvB,KAAK,CAAC;EAC7C;EACA0vB,UAAUA,CAAC1vB,KAAK,EAAE;IACd,OAAO,IAAI,CAACkvB,QAAQ,CAACQ,UAAU,CAAC1vB,KAAK,CAAC;EAC1C;EACA2vB,WAAWA,CAAC/P,MAAM,EAAEgQ,QAAQ,EAAE;IAC1B,IAAI,CAACV,QAAQ,CAACS,WAAW,CAAC/P,MAAM,EAAEgQ,QAAQ,CAAC;IAC3C,IAAI,CAACtQ,MAAM,CAACuL,QAAQ,CAAC,IAAI,CAACrP,WAAW,EAAEoU,QAAQ,EAAEhQ,MAAM,EAAE,KAAK,CAAC;EACnE;EACAyC,YAAYA,CAACzC,MAAM,EAAEgQ,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,GAAG,IAAI,EAAE;IACpD,IAAI,CAACZ,QAAQ,CAAC7M,YAAY,CAACzC,MAAM,EAAEgQ,QAAQ,EAAEC,QAAQ,CAAC;IACtD;IACA,IAAI,CAACvQ,MAAM,CAACuL,QAAQ,CAAC,IAAI,CAACrP,WAAW,EAAEoU,QAAQ,EAAEhQ,MAAM,EAAEkQ,MAAM,CAAC;EACpE;EACAC,WAAWA,CAACnQ,MAAM,EAAEoQ,QAAQ,EAAEC,aAAa,EAAE;IACzC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACpQ,UAAU,CAACmQ,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAAC1Q,MAAM,CAACwL,QAAQ,CAAC,IAAI,CAACtP,WAAW,EAAEwU,QAAQ,EAAE,IAAI,CAACd,QAAQ,CAAC;IACnE;EACJ;EACAgB,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,OAAO,IAAI,CAAClB,QAAQ,CAACgB,iBAAiB,CAACC,cAAc,EAAEC,eAAe,CAAC;EAC3E;EACAvQ,UAAUA,CAACuD,IAAI,EAAE;IACb,OAAO,IAAI,CAAC8L,QAAQ,CAACrP,UAAU,CAACuD,IAAI,CAAC;EACzC;EACAiN,WAAWA,CAACjN,IAAI,EAAE;IACd,OAAO,IAAI,CAAC8L,QAAQ,CAACmB,WAAW,CAACjN,IAAI,CAAC;EAC1C;EACAkN,YAAYA,CAACC,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,EAAEwvB,SAAS,EAAE;IACrC,IAAI,CAACN,QAAQ,CAACoB,YAAY,CAACC,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,EAAEwvB,SAAS,CAAC;EAC1D;EACAgB,eAAeA,CAACD,EAAE,EAAE9uB,IAAI,EAAE+tB,SAAS,EAAE;IACjC,IAAI,CAACN,QAAQ,CAACsB,eAAe,CAACD,EAAE,EAAE9uB,IAAI,EAAE+tB,SAAS,CAAC;EACtD;EACA5S,QAAQA,CAAC2T,EAAE,EAAE9uB,IAAI,EAAE;IACf,IAAI,CAACytB,QAAQ,CAACtS,QAAQ,CAAC2T,EAAE,EAAE9uB,IAAI,CAAC;EACpC;EACAuc,WAAWA,CAACuS,EAAE,EAAE9uB,IAAI,EAAE;IAClB,IAAI,CAACytB,QAAQ,CAAClR,WAAW,CAACuS,EAAE,EAAE9uB,IAAI,CAAC;EACvC;EACAgvB,QAAQA,CAACF,EAAE,EAAE9yB,KAAK,EAAEuC,KAAK,EAAE0wB,KAAK,EAAE;IAC9B,IAAI,CAACxB,QAAQ,CAACuB,QAAQ,CAACF,EAAE,EAAE9yB,KAAK,EAAEuC,KAAK,EAAE0wB,KAAK,CAAC;EACnD;EACAC,WAAWA,CAACJ,EAAE,EAAE9yB,KAAK,EAAEizB,KAAK,EAAE;IAC1B,IAAI,CAACxB,QAAQ,CAACyB,WAAW,CAACJ,EAAE,EAAE9yB,KAAK,EAAEizB,KAAK,CAAC;EAC/C;EACAE,WAAWA,CAACL,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,EAAE;IACzB,IAAIyB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI4pB,gBAAgB,IAAIttB,IAAI,IAAIutB,uBAAuB,EAAE;MACvE,IAAI,CAACjE,iBAAiB,CAACwF,EAAE,EAAE,CAAC,CAACvwB,KAAK,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAACkvB,QAAQ,CAAC0B,WAAW,CAACL,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,CAAC;IAC9C;EACJ;EACA6wB,QAAQA,CAACzN,IAAI,EAAEpjB,KAAK,EAAE;IAClB,IAAI,CAACkvB,QAAQ,CAAC2B,QAAQ,CAACzN,IAAI,EAAEpjB,KAAK,CAAC;EACvC;EACAma,MAAMA,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,EAAE;IACzC,OAAO,IAAI,CAACmpB,QAAQ,CAAC/U,MAAM,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,CAAC;EACrE;EACAglB,iBAAiBA,CAAC7sB,OAAO,EAAE8B,KAAK,EAAE;IAC9B,IAAI,CAACsf,MAAM,CAACyL,iBAAiB,CAAC7sB,OAAO,EAAE8B,KAAK,CAAC;EACjD;AACJ;AACA,MAAM8wB,iBAAiB,SAAS7B,qBAAqB,CAAC;EAClD7vB,OAAO;EACP6E,WAAWA,CAAC7E,OAAO,EAAEoc,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAExF,SAAS,EAAE;IAC3D,KAAK,CAAC0B,WAAW,EAAE0T,QAAQ,EAAE5P,MAAM,EAAExF,SAAS,CAAC;IAC/C,IAAI,CAAC1a,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoc,WAAW,GAAGA,WAAW;EAClC;EACAoV,WAAWA,CAACL,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,EAAE;IACzB,IAAIyB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI4pB,gBAAgB,EAAE;MACpC,IAAIttB,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI1D,IAAI,IAAIutB,uBAAuB,EAAE;QAC1DhvB,KAAK,GAAGA,KAAK,KAAK8V,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC9V,KAAK;QAC5C,IAAI,CAAC+qB,iBAAiB,CAACwF,EAAE,EAAEvwB,KAAK,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACsf,MAAM,CAAC2L,OAAO,CAAC,IAAI,CAACzP,WAAW,EAAE+U,EAAE,EAAE9uB,IAAI,CAACmK,KAAK,CAAC,CAAC,CAAC,EAAE5L,KAAK,CAAC;MACnE;IACJ,CAAC,MACI;MACD,IAAI,CAACkvB,QAAQ,CAAC0B,WAAW,CAACL,EAAE,EAAE9uB,IAAI,EAAEzB,KAAK,CAAC;IAC9C;EACJ;EACAma,MAAMA,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,EAAE;IACzC,IAAIqU,SAAS,CAACjV,MAAM,CAAC,CAAC,CAAC,IAAI4pB,gBAAgB,EAAE;MACzC,MAAM7wB,OAAO,GAAG6yB,wBAAwB,CAACnf,MAAM,CAAC;MAChD,IAAInQ,IAAI,GAAG2Y,SAAS,CAACxO,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIiR,KAAK,GAAG,EAAE;MACd;MACA;MACA,IAAIpb,IAAI,CAAC0D,MAAM,CAAC,CAAC,CAAC,IAAI4pB,gBAAgB,EAAE;QACpC,CAACttB,IAAI,EAAEob,KAAK,CAAC,GAAGmU,wBAAwB,CAACvvB,IAAI,CAAC;MAClD;MACA,OAAO,IAAI,CAAC6d,MAAM,CAACnF,MAAM,CAAC,IAAI,CAACqB,WAAW,EAAEtd,OAAO,EAAEuD,IAAI,EAAEob,KAAK,EAAGoU,KAAK,IAAK;QACzE,MAAMC,OAAO,GAAGD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC7xB,OAAO,CAAC+xB,wBAAwB,CAACD,OAAO,EAAE7W,QAAQ,EAAE4W,KAAK,CAAC;MACnE,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC/B,QAAQ,CAAC/U,MAAM,CAACvI,MAAM,EAAEwI,SAAS,EAAEC,QAAQ,EAAEtU,OAAO,CAAC;EACrE;AACJ;AACA,SAASgrB,wBAAwBA,CAACnf,MAAM,EAAE;EACtC,QAAQA,MAAM;IACV,KAAK,MAAM;MACP,OAAOwf,QAAQ,CAAC1G,IAAI;IACxB,KAAK,UAAU;MACX,OAAO0G,QAAQ;IACnB,KAAK,QAAQ;MACT,OAAOC,MAAM;IACjB;MACI,OAAOzf,MAAM;EACrB;AACJ;AACA,SAASof,wBAAwBA,CAACjc,WAAW,EAAE;EAC3C,MAAMuc,QAAQ,GAAGvc,WAAW,CAAC5L,OAAO,CAAC,GAAG,CAAC;EACzC,MAAMkU,OAAO,GAAGtI,WAAW,CAACwc,SAAS,CAAC,CAAC,EAAED,QAAQ,CAAC;EAClD,MAAMzU,KAAK,GAAG9H,WAAW,CAACnJ,KAAK,CAAC0lB,QAAQ,GAAG,CAAC,CAAC;EAC7C,OAAO,CAACjU,OAAO,EAAER,KAAK,CAAC;AAC3B;AAEA,MAAM2U,wBAAwB,CAAC;EAC3BtC,QAAQ;EACR5P,MAAM;EACNmS,KAAK;EACLC,UAAU,GAAG,CAAC;EACdC,YAAY,GAAG,CAAC;EAChBC,yBAAyB,GAAG,EAAE;EAC9BC,cAAc,gBAAG,IAAIltB,GAAG,CAAC,CAAC;EAC1BmtB,aAAa,GAAG,CAAC;EACjB7tB,WAAWA,CAACirB,QAAQ,EAAE5P,MAAM,EAAEmS,KAAK,EAAE;IACjC,IAAI,CAACvC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5P,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACmS,KAAK,GAAGA,KAAK;IAClBnS,MAAM,CAACiC,iBAAiB,GAAG,CAACrjB,OAAO,EAAEgxB,QAAQ,KAAK;MAC9CA,QAAQ,EAAEa,WAAW,CAAC,IAAI,EAAE7xB,OAAO,CAAC;IACxC,CAAC;EACL;EACA6zB,cAAcA,CAACzV,WAAW,EAAEhX,IAAI,EAAE;IAC9B,MAAM0sB,kBAAkB,GAAG,EAAE;IAC7B;IACA;IACA,MAAM9C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC6C,cAAc,CAACzV,WAAW,EAAEhX,IAAI,CAAC;IAChE,IAAI,CAACgX,WAAW,IAAI,CAAChX,IAAI,EAAE0X,IAAI,GAAG,WAAW,CAAC,EAAE;MAC5C,MAAMiV,KAAK,GAAG,IAAI,CAACJ,cAAc;MACjC,IAAIK,QAAQ,GAAGD,KAAK,CAAChpB,GAAG,CAACimB,QAAQ,CAAC;MAClC,IAAI,CAACgD,QAAQ,EAAE;QACX;QACA;QACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAMF,KAAK,CAAC/oB,MAAM,CAACgmB,QAAQ,CAAC;QACtDgD,QAAQ,GAAG,IAAIjD,qBAAqB,CAAC+C,kBAAkB,EAAE9C,QAAQ,EAAE,IAAI,CAAC5P,MAAM,EAAE6S,iBAAiB,CAAC;QAClG;QACAF,KAAK,CAACrtB,GAAG,CAACsqB,QAAQ,EAAEgD,QAAQ,CAAC;MACjC;MACA,OAAOA,QAAQ;IACnB;IACA,MAAMvH,WAAW,GAAGrlB,IAAI,CAAC+T,EAAE;IAC3B,MAAMmC,WAAW,GAAGlW,IAAI,CAAC+T,EAAE,GAAG,GAAG,GAAG,IAAI,CAACqY,UAAU;IACnD,IAAI,CAACA,UAAU,EAAE;IACjB,IAAI,CAACpS,MAAM,CAAClG,QAAQ,CAACoC,WAAW,EAAEc,WAAW,CAAC;IAC9C,MAAM2F,eAAe,GAAI5E,OAAO,IAAK;MACjC,IAAI3U,KAAK,CAACC,OAAO,CAAC0U,OAAO,CAAC,EAAE;QACxBA,OAAO,CAACnb,OAAO,CAAC+f,eAAe,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAAC3C,MAAM,CAAC2C,eAAe,CAAC0I,WAAW,EAAEnP,WAAW,EAAEc,WAAW,EAAEe,OAAO,CAAC5b,IAAI,EAAE4b,OAAO,CAAC;MAC7F;IACJ,CAAC;IACD,MAAM+U,iBAAiB,GAAG9sB,IAAI,CAAC0X,IAAI,CAAC,WAAW,CAAC;IAChDoV,iBAAiB,CAAClwB,OAAO,CAAC+f,eAAe,CAAC;IAC1C,OAAO,IAAI6O,iBAAiB,CAAC,IAAI,EAAEtV,WAAW,EAAE0T,QAAQ,EAAE,IAAI,CAAC5P,MAAM,CAAC;EAC1E;EACA+S,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,aAAa,EAAE;IACpB,IAAI,IAAI,CAAC5C,QAAQ,CAACmD,KAAK,EAAE;MACrB,IAAI,CAACnD,QAAQ,CAACmD,KAAK,CAAC,CAAC;IACzB;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjBhD,cAAc,CAAC,MAAM;MACjB,IAAI,CAACqC,YAAY,EAAE;IACvB,CAAC,CAAC;EACN;EACA;EACAR,wBAAwBA,CAACoB,KAAK,EAAEjb,EAAE,EAAE0F,IAAI,EAAE;IACtC,IAAIuV,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACZ,YAAY,EAAE;MACzC,IAAI,CAACF,KAAK,CAACe,GAAG,CAAC,MAAMlb,EAAE,CAAC0F,IAAI,CAAC,CAAC;MAC9B;IACJ;IACA,MAAMyV,wBAAwB,GAAG,IAAI,CAACb,yBAAyB;IAC/D,IAAIa,wBAAwB,CAAC9xB,MAAM,IAAI,CAAC,EAAE;MACtC2uB,cAAc,CAAC,MAAM;QACjB,IAAI,CAACmC,KAAK,CAACe,GAAG,CAAC,MAAM;UACjBC,wBAAwB,CAACvwB,OAAO,CAAEsH,KAAK,IAAK;YACxC,MAAM,CAAC8N,EAAE,EAAE0F,IAAI,CAAC,GAAGxT,KAAK;YACxB8N,EAAE,CAAC0F,IAAI,CAAC;UACZ,CAAC,CAAC;UACF,IAAI,CAAC4U,yBAAyB,GAAG,EAAE;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACAa,wBAAwB,CAAC7xB,IAAI,CAAC,CAAC0W,EAAE,EAAE0F,IAAI,CAAC,CAAC;EAC7C;EACA0V,GAAGA,CAAA,EAAG;IACF,IAAI,CAACZ,aAAa,EAAE;IACpB;IACA;IACA,IAAI,IAAI,CAACA,aAAa,IAAI,CAAC,EAAE;MACzB,IAAI,CAACL,KAAK,CAACkB,iBAAiB,CAAC,MAAM;QAC/B,IAAI,CAACL,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAAChT,MAAM,CAAC+D,KAAK,CAAC,IAAI,CAACsO,YAAY,CAAC;MACxC,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACzC,QAAQ,CAACwD,GAAG,EAAE;MACnB,IAAI,CAACxD,QAAQ,CAACwD,GAAG,CAAC,CAAC;IACvB;EACJ;EACA3P,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzD,MAAM,CAACyD,iBAAiB,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI6P,iBAAiBA,CAACjI,WAAW,EAAE;IAC3B;IACA,IAAI,CAACrL,MAAM,CAAC+D,KAAK,CAAC,CAAC;IACnB,IAAI,CAAC6L,QAAQ,CAAC0D,iBAAiB,GAAGjI,WAAW,CAAC;EAClD;AACJ;AAEA,SAASrrB,eAAe,EAAExB,mBAAmB,EAAE4wB,SAAS,IAAImE,UAAU,EAAExI,eAAe,IAAIyI,gBAAgB,EAAEhC,iBAAiB,IAAIiC,kBAAkB,EAAEvB,wBAAwB,IAAIwB,yBAAyB,EAAExzB,wBAAwB,IAAIyzB,yBAAyB,EAAEhE,qBAAqB,IAAIiE,sBAAsB,EAAEv3B,eAAe,IAAIw3B,gBAAgB,EAAEz3B,eAAe,IAAI03B,gBAAgB,EAAE3zB,4BAA4B,IAAI4zB,6BAA6B,EAAE9V,yBAAyB,IAAI+V,0BAA0B,EAAEpF,mBAAmB,IAAIqF,oBAAoB,EAAElH,mBAAmB,IAAImH,oBAAoB,EAAErzB,4BAA4B,IAAIszB,6BAA6B,EAAEx2B,8BAA8B,IAAIy2B,+BAA+B,EAAE32B,mBAAmB,IAAI42B,oBAAoB,EAAEh6B,eAAe,IAAIi6B,gBAAgB,EAAEnF,YAAY,IAAIoF,aAAa,EAAEj6B,gBAAgB,IAAIk6B,iBAAiB,EAAEj6B,WAAW,IAAIk6B,YAAY,EAAE72B,oBAAoB,IAAI82B,mBAAmB,EAAEt6B,qBAAqB,IAAIu6B,sBAAsB,EAAEj3B,kCAAkC,IAAIk3B,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}