using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CitiesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public CitiesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetCities()
        {
            try
            {
                var cities = await _context.Cities
                    .Include(c => c.Country)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        id = c.Id,
                        nameAr = c.NameAr,
                        nameEn = c.NameEn,
                        countryId = c.CountryId,
                        countryName = c.Country != null ? c.Country.NameAr : "غير محدد",
                        postalCode = c.PostalCode,
                        areaCode = c.AreaCode,
                        isActive = c.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المدن بنجاح",
                    data = cities,
                    count = cities.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة المدن",
                    error = ex.Message
                });
            }
        }

        [HttpGet("by-country/{countryId}")]
        public async Task<ActionResult<object>> GetCitiesByCountry(int countryId)
        {
            try
            {
                var cities = await _context.Cities
                    .Include(c => c.Country)
                    .Where(c => c.CountryId == countryId && c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        id = c.Id,
                        nameAr = c.NameAr,
                        nameEn = c.NameEn,
                        countryId = c.CountryId,
                        countryName = c.Country != null ? c.Country.NameAr : "غير محدد",
                        postalCode = c.PostalCode,
                        areaCode = c.AreaCode,
                        isActive = c.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المدن للبلد بنجاح",
                    data = cities,
                    count = cities.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة المدن",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetCity(int id)
        {
            try
            {
                var city = await _context.Cities
                    .Include(c => c.Country)
                    .Where(c => c.Id == id && c.IsActive)
                    .Select(c => new
                    {
                        id = c.Id,
                        nameAr = c.NameAr,
                        nameEn = c.NameEn,
                        countryId = c.CountryId,
                        countryName = c.Country != null ? c.Country.NameAr : "غير محدد",
                        postalCode = c.PostalCode,
                        areaCode = c.AreaCode,
                        isActive = c.IsActive,
                        createdAt = c.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (city == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "المدينة غير موجودة"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات المدينة بنجاح",
                    data = city
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات المدينة",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreateCity(CreateCityRequest request)
        {
            try
            {
                var city = new City
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    CountryId = request.CountryId,
                    PostalCode = request.PostalCode,
                    AreaCode = request.AreaCode,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Cities.Add(city);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCity), new { id = city.Id }, new
                {
                    success = true,
                    message = "تم إضافة المدينة بنجاح",
                    data = new
                    {
                        id = city.Id,
                        nameAr = city.NameAr,
                        nameEn = city.NameEn,
                        countryId = city.CountryId,
                        postalCode = city.PostalCode,
                        areaCode = city.AreaCode,
                        isActive = city.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة المدينة",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdateCity(int id, UpdateCityRequest request)
        {
            try
            {
                var city = await _context.Cities.FindAsync(id);
                if (city == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "المدينة غير موجودة"
                    });
                }

                city.NameAr = request.NameAr;
                city.NameEn = request.NameEn;
                city.CountryId = request.CountryId;
                city.PostalCode = request.PostalCode;
                city.AreaCode = request.AreaCode;
                city.IsActive = request.IsActive;
                city.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات المدينة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث المدينة",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteCity(int id)
        {
            try
            {
                var city = await _context.Cities.FindAsync(id);
                if (city == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "المدينة غير موجودة"
                    });
                }

                city.IsActive = false;
                city.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف المدينة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف المدينة",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreateCityRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int CountryId { get; set; }
        public string? PostalCode { get; set; }
        public string? AreaCode { get; set; }
    }

    public class UpdateCityRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int CountryId { get; set; }
        public string? PostalCode { get; set; }
        public string? AreaCode { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
