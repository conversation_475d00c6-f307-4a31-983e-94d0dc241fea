using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// جدول العدادات لمنع التضارب في الأكواد
    /// </summary>
    public class Counter : BaseEntity
    {
        /// <summary>
        /// اسم العداد (مثل: CUSTOMER, PRODUCT, INVOICE)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string CounterName { get; set; } = string.Empty;

        /// <summary>
        /// بادئة الكود (مثل: CUS, PRD, INV)
        /// </summary>
        [MaxLength(10)]
        public string? Prefix { get; set; }

        /// <summary>
        /// القيمة الحالية للعداد
        /// </summary>
        public long CurrentValue { get; set; } = 0;

        /// <summary>
        /// طول الرقم (عدد الأرقام)
        /// </summary>
        public int NumberLength { get; set; } = 6;

        /// <summary>
        /// معرف الفرع (إذا كان العداد خاص بفرع معين)
        /// </summary>
        public int? BranchId { get; set; }

        /// <summary>
        /// هل العداد نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// وصف العداد
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// تنسيق الكود النهائي (مثل: {PREFIX}{YEAR}{NUMBER})
        /// </summary>
        [MaxLength(100)]
        public string? CodeFormat { get; set; }

        /// <summary>
        /// هل يتم إعادة تعيين العداد سنوياً
        /// </summary>
        public bool ResetYearly { get; set; } = false;

        /// <summary>
        /// السنة الحالية للعداد
        /// </summary>
        public int? CurrentYear { get; set; }

        // Navigation Properties
        public virtual Branch? Branch { get; set; }
    }
}
