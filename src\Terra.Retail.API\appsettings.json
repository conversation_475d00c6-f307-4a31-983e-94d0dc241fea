{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=TerraRetailERP;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/terra-retail-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "TerraRetailERP_SecretKey_2024_VeryLongAndSecureKey123!@#", "Issuer": "TerraRetailERP", "Audience": "TerraRetailERP_Users", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "AppSettings": {"DefaultLanguage": "ar", "DefaultCurrency": "SAR", "DefaultTimeZone": "Asia/Riyadh", "MaxFileUploadSize": 10485760, "AllowedFileExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "EnableAuditLog": true, "SessionTimeoutMinutes": 30}}