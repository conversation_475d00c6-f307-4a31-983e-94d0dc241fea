{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, DOCUMENT, Injector, afterNextRender, ViewChild, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { OverlayConfig, createGlobalPositionStrategy, createOverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './icon-button-DxiIc1ex.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-BnMiRtmT.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-CObeNzjn.mjs';\nimport './index-BFRo2fUq.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = /*#__PURE__*/Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  _overlayRef;\n  /** The instance of the component making up the content of the snack bar. */\n  instance;\n  /**\n   * The instance of the component making up the content of the snack bar.\n   * @docs-private\n   */\n  containerInstance;\n  /** Subject for notifying the user that the snack bar has been dismissed. */\n  _afterDismissed = /*#__PURE__*/new Subject();\n  /** Subject for notifying the user that the snack bar has opened and appeared. */\n  _afterOpened = /*#__PURE__*/new Subject();\n  /** Subject for notifying the user that the snack bar action was called. */\n  _onAction = /*#__PURE__*/new Subject();\n  /**\n   * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n   * dismissed before the duration passes.\n   */\n  _durationTimeoutId;\n  /** Whether the snack bar was dismissed using the action button. */\n  _dismissedByAction = false;\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = /*#__PURE__*/new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n  politeness = 'polite';\n  /**\n   * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n   * component or template, the announcement message will default to the specified message.\n   */\n  announcementMessage = '';\n  /**\n   * The view container that serves as the parent for the snackbar for the purposes of dependency\n   * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n   */\n  viewContainerRef;\n  /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n  duration = 0;\n  /** Extra CSS classes to be added to the snack bar container. */\n  panelClass;\n  /** Text layout direction for the snack bar. */\n  direction;\n  /** Data being injected into the child component. */\n  data = null;\n  /** The horizontal position to place the snack bar. */\n  horizontalPosition = 'center';\n  /** The vertical position to place the snack bar. */\n  verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nlet MatSnackBarLabel = /*#__PURE__*/(() => {\n  class MatSnackBarLabel {\n    static ɵfac = function MatSnackBarLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBarLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarLabel,\n      selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n    });\n  }\n  return MatSnackBarLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nlet MatSnackBarActions = /*#__PURE__*/(() => {\n  class MatSnackBarActions {\n    static ɵfac = function MatSnackBarActions_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBarActions)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarActions,\n      selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n    });\n  }\n  return MatSnackBarActions;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nlet MatSnackBarAction = /*#__PURE__*/(() => {\n  class MatSnackBarAction {\n    static ɵfac = function MatSnackBarAction_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBarAction)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarAction,\n      selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n    });\n  }\n  return MatSnackBarAction;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SimpleSnackBar = /*#__PURE__*/(() => {\n  class SimpleSnackBar {\n    snackBarRef = inject(MatSnackBarRef);\n    data = inject(MAT_SNACK_BAR_DATA);\n    constructor() {}\n    /** Performs the action on the snack bar. */\n    action() {\n      this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n      return !!this.data.action;\n    }\n    static ɵfac = function SimpleSnackBar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SimpleSnackBar)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SimpleSnackBar,\n      selectors: [[\"simple-snack-bar\"]],\n      hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n      exportAs: [\"matSnackBar\"],\n      decls: 3,\n      vars: 2,\n      consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"matButton\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n      template: function SimpleSnackBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasAction ? 2 : -1);\n        }\n      },\n      dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return SimpleSnackBar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nlet MatSnackBarContainer = /*#__PURE__*/(() => {\n  class MatSnackBarContainer extends BasePortalOutlet {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _platform = inject(Platform);\n    _animationsDisabled = _animationsDisabled();\n    snackBarConfig = inject(MatSnackBarConfig);\n    _document = inject(DOCUMENT);\n    _trackedModals = new Set();\n    _enterFallback;\n    _exitFallback;\n    _injector = inject(Injector);\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _announceDelay = 150;\n    /** The timeout for announcing the snack bar's content. */\n    _announceTimeoutId;\n    /** Whether the component has been destroyed. */\n    _destroyed = false;\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _portalOutlet;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    _onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    _animationState = 'void';\n    /** aria-live value for the live region. */\n    _live;\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _label;\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _role;\n    /** Unique ID of the aria-live element. */\n    _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n    constructor() {\n      super();\n      const config = this.snackBarConfig;\n      // Use aria-live rather than a live role like 'alert' or 'status'\n      // because NVDA and JAWS have show inconsistent behavior with live roles.\n      if (config.politeness === 'assertive' && !config.announcementMessage) {\n        this._live = 'assertive';\n      } else if (config.politeness === 'off') {\n        this._live = 'off';\n      } else {\n        this._live = 'polite';\n      }\n      // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n      // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n      if (this._platform.FIREFOX) {\n        if (this._live === 'polite') {\n          this._role = 'status';\n        }\n        if (this._live === 'assertive') {\n          this._role = 'alert';\n        }\n      }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachComponentPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n      this._afterPortalAttached();\n      return result;\n    }\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    };\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(animationName) {\n      if (animationName === EXIT_ANIMATION) {\n        this._completeExit();\n      } else if (animationName === ENTER_ANIMATION) {\n        clearTimeout(this._enterFallback);\n        this._ngZone.run(() => {\n          this._onEnter.next();\n          this._onEnter.complete();\n        });\n      }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n      if (!this._destroyed) {\n        this._animationState = 'visible';\n        // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n        // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n        this._changeDetectorRef.markForCheck();\n        this._changeDetectorRef.detectChanges();\n        this._screenReaderAnnounce();\n        if (this._animationsDisabled) {\n          afterNextRender(() => {\n            this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n          }, {\n            injector: this._injector\n          });\n        } else {\n          clearTimeout(this._enterFallback);\n          this._enterFallback = setTimeout(() => {\n            // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n            // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n            this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n            this.onAnimationEnd(ENTER_ANIMATION);\n          }, 200);\n        }\n      }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n      if (this._destroyed) {\n        return of(undefined);\n      }\n      // It's common for snack bars to be opened by random outside calls like HTTP requests or\n      // errors. Run inside the NgZone to ensure that it functions correctly.\n      this._ngZone.run(() => {\n        // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n        // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n        // `MatSnackBar.open`).\n        this._animationState = 'hidden';\n        this._changeDetectorRef.markForCheck();\n        // Mark this element with an 'exit' attribute to indicate that the snackbar has\n        // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n        // test harness.\n        this._elementRef.nativeElement.setAttribute('mat-exit', '');\n        // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n        // long enough to visually read it either, so clear the timeout for announcing.\n        clearTimeout(this._announceTimeoutId);\n        if (this._animationsDisabled) {\n          afterNextRender(() => {\n            this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n          }, {\n            injector: this._injector\n          });\n        } else {\n          clearTimeout(this._exitFallback);\n          this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n        }\n      });\n      return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n      this._destroyed = true;\n      this._clearFromModals();\n      this._completeExit();\n    }\n    _completeExit() {\n      clearTimeout(this._exitFallback);\n      queueMicrotask(() => {\n        this._onExit.next();\n        this._onExit.complete();\n      });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n      const element = this._elementRef.nativeElement;\n      const panelClasses = this.snackBarConfig.panelClass;\n      if (panelClasses) {\n        if (Array.isArray(panelClasses)) {\n          // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n          panelClasses.forEach(cssClass => element.classList.add(cssClass));\n        } else {\n          element.classList.add(panelClasses);\n        }\n      }\n      this._exposeToModals();\n      // Check to see if the attached component or template uses the MDC template structure,\n      // specifically the MDC label. If not, the container should apply the MDC label class to this\n      // component's label container, which will apply MDC's label styles to the attached view.\n      const label = this._label.nativeElement;\n      const labelClass = 'mdc-snackbar__label';\n      label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n      // `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const id = this._liveElementId;\n      const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      for (let i = 0; i < modals.length; i++) {\n        const modal = modals[i];\n        const ariaOwns = modal.getAttribute('aria-owns');\n        this._trackedModals.add(modal);\n        if (!ariaOwns) {\n          modal.setAttribute('aria-owns', id);\n        } else if (ariaOwns.indexOf(id) === -1) {\n          modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n        }\n      }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n      this._trackedModals.forEach(modal => {\n        const ariaOwns = modal.getAttribute('aria-owns');\n        if (ariaOwns) {\n          const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n          if (newValue.length > 0) {\n            modal.setAttribute('aria-owns', newValue);\n          } else {\n            modal.removeAttribute('aria-owns');\n          }\n        }\n      });\n      this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Attempting to attach snack bar content after content is already attached');\n      }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n      if (this._announceTimeoutId) {\n        return;\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          if (this._destroyed) {\n            return;\n          }\n          const element = this._elementRef.nativeElement;\n          const inertElement = element.querySelector('[aria-hidden]');\n          const liveElement = element.querySelector('[aria-live]');\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement = null;\n            if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n              focusedElement = document.activeElement;\n            }\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n    static ɵfac = function MatSnackBarContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBarContainer)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSnackBarContainer,\n      selectors: [[\"mat-snack-bar-container\"]],\n      viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\"],\n      hostVars: 6,\n      hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"animationend\", function MatSnackBarContainer_animationend_HostBindingHandler($event) {\n            return ctx.onAnimationEnd($event.animationName);\n          })(\"animationcancel\", function MatSnackBarContainer_animationcancel_HostBindingHandler($event) {\n            return ctx.onAnimationEnd($event.animationName);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-snack-bar-container-enter\", ctx._animationState === \"visible\")(\"mat-snack-bar-container-exit\", ctx._animationState === \"hidden\")(\"mat-snack-bar-container-animations-enabled\", !ctx._animationsDisabled);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\", \"mat-mdc-snackbar-surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatSnackBarContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return MatSnackBarContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nlet MatSnackBar = /*#__PURE__*/(() => {\n  class MatSnackBar {\n    _live = inject(LiveAnnouncer);\n    _injector = inject(Injector);\n    _breakpointObserver = inject(BreakpointObserver);\n    _parentSnackBar = inject(MatSnackBar, {\n      optional: true,\n      skipSelf: true\n    });\n    _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n    _animationsDisabled = _animationsDisabled();\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    handsetCssClass = 'mat-mdc-snack-bar-handset';\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n      const parent = this._parentSnackBar;\n      return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n      if (this._parentSnackBar) {\n        this._parentSnackBar._openedSnackBarRef = value;\n      } else {\n        this._snackBarRefAtThisLevel = value;\n      }\n    }\n    constructor() {}\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n      return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n      return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n      const _config = {\n        ...this._defaultConfig,\n        ...config\n      };\n      // Since the user doesn't have access to the component, we can\n      // override the data to pass in our own message and action.\n      _config.data = {\n        message,\n        action\n      };\n      // Since the snack bar has `role=\"alert\"`, we don't\n      // want to announce the same message twice.\n      if (_config.announcementMessage === message) {\n        _config.announcementMessage = undefined;\n      }\n      return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n      if (this._openedSnackBarRef) {\n        this._openedSnackBarRef.dismiss();\n      }\n    }\n    ngOnDestroy() {\n      // Only dismiss the snack bar at the current level on destroy.\n      if (this._snackBarRefAtThisLevel) {\n        this._snackBarRefAtThisLevel.dismiss();\n      }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      const injector = Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarConfig,\n          useValue: config\n        }]\n      });\n      const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n      const containerRef = overlayRef.attach(containerPortal);\n      containerRef.instance.snackBarConfig = config;\n      return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n      const config = {\n        ...new MatSnackBarConfig(),\n        ...this._defaultConfig,\n        ...userConfig\n      };\n      const overlayRef = this._createOverlay(config);\n      const container = this._attachSnackBarContainer(overlayRef, config);\n      const snackBarRef = new MatSnackBarRef(container, overlayRef);\n      if (content instanceof TemplateRef) {\n        const portal = new TemplatePortal(content, null, {\n          $implicit: config.data,\n          snackBarRef\n        });\n        snackBarRef.instance = container.attachTemplatePortal(portal);\n      } else {\n        const injector = this._createInjector(config, snackBarRef);\n        const portal = new ComponentPortal(content, undefined, injector);\n        const contentRef = container.attachComponentPortal(portal);\n        // We can't pass this via the injector, because the injector is created earlier.\n        snackBarRef.instance = contentRef.instance;\n      }\n      // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n      // appropriate. This class is applied to the overlay element because the overlay must expand to\n      // fill the width of the screen for full width snackbars.\n      this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n        overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n      });\n      if (config.announcementMessage) {\n        // Wait until the snack bar contents have been announced then deliver this message.\n        container._onAnnounce.subscribe(() => {\n          this._live.announce(config.announcementMessage, config.politeness);\n        });\n      }\n      this._animateSnackBar(snackBarRef, config);\n      this._openedSnackBarRef = snackBarRef;\n      return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n      // When the snackbar is dismissed, clear the reference to it.\n      snackBarRef.afterDismissed().subscribe(() => {\n        // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n        if (this._openedSnackBarRef == snackBarRef) {\n          this._openedSnackBarRef = null;\n        }\n        if (config.announcementMessage) {\n          this._live.clear();\n        }\n      });\n      // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n      if (config.duration && config.duration > 0) {\n        snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n      }\n      if (this._openedSnackBarRef) {\n        // If a snack bar is already in view, dismiss it and enter the\n        // new snack bar after exit animation is complete.\n        this._openedSnackBarRef.afterDismissed().subscribe(() => {\n          snackBarRef.containerInstance.enter();\n        });\n        this._openedSnackBarRef.dismiss();\n      } else {\n        // If no snack bar is in view, enter the new snack bar.\n        snackBarRef.containerInstance.enter();\n      }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n      const overlayConfig = new OverlayConfig();\n      overlayConfig.direction = config.direction;\n      const positionStrategy = createGlobalPositionStrategy(this._injector);\n      // Set horizontal position.\n      const isRtl = config.direction === 'rtl';\n      const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n      const isRight = !isLeft && config.horizontalPosition !== 'center';\n      if (isLeft) {\n        positionStrategy.left('0');\n      } else if (isRight) {\n        positionStrategy.right('0');\n      } else {\n        positionStrategy.centerHorizontally();\n      }\n      // Set horizontal position.\n      if (config.verticalPosition === 'top') {\n        positionStrategy.top('0');\n      } else {\n        positionStrategy.bottom('0');\n      }\n      overlayConfig.positionStrategy = positionStrategy;\n      overlayConfig.disableAnimations = this._animationsDisabled;\n      return createOverlayRef(this._injector, overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      return Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarRef,\n          useValue: snackBarRef\n        }, {\n          provide: MAT_SNACK_BAR_DATA,\n          useValue: config.data\n        }]\n      });\n    }\n    static ɵfac = function MatSnackBar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBar)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSnackBar,\n      factory: MatSnackBar.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatSnackBar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nlet MatSnackBarModule = /*#__PURE__*/(() => {\n  class MatSnackBarModule {\n    static ɵfac = function MatSnackBarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSnackBarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSnackBarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MatSnackBar],\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n    });\n  }\n  return MatSnackBarModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n  // Represents\n  // trigger('state', [\n  //   state(\n  //     'void, hidden',\n  //     style({\n  //       transform: 'scale(0.8)',\n  //       opacity: 0,\n  //     }),\n  //   ),\n  //   state(\n  //     'visible',\n  //     style({\n  //       transform: 'scale(1)',\n  //       opacity: 1,\n  //     }),\n  //   ),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition(\n  //     '* => void, * => hidden',\n  //     animate(\n  //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n  //       style({\n  //         opacity: 0,\n  //       }),\n  //     ),\n  //   ),\n  // ])\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: {\n    type: 7,\n    name: 'state',\n    'definitions': [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(0.8)',\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)',\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void, * => hidden',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgZone", "ElementRef", "ChangeDetectorRef", "DOCUMENT", "Injector", "afterNextRender", "ViewChild", "TemplateRef", "Injectable", "NgModule", "Subject", "of", "MatButton", "MatButtonModule", "_IdGenerator", "LiveAnnouncer", "Platform", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "_", "_animationsDisabled", "BreakpointObserver", "Breakpoints", "OverlayConfig", "createGlobalPositionStrategy", "createOverlayRef", "OverlayModule", "takeUntil", "M", "MatCommonModule", "SimpleSnackBar_Conditional_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SimpleSnackBar_Conditional_2_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "MatSnackBarContainer_ng_template_4_Template", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "_overlayRef", "instance", "containerInstance", "_afterDismissed", "_afterOpened", "_onAction", "_durationTimeoutId", "_dismissedByAction", "constructor", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "viewContainerRef", "panelClass", "direction", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "MatSnackBarLabel_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "MatSnackBarActions", "MatSnackBarActions_Factory", "MatSnackBarAction", "MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "SimpleSnackBar_Factory", "ɵcmp", "ɵɵdefineComponent", "exportAs", "decls", "vars", "consts", "template", "SimpleSnackBar_Template", "ɵɵconditionalCreate", "message", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatSnackBarContainer", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "snackBarConfig", "_document", "_trackedModals", "Set", "_enterFallback", "_exitFallback", "_injector", "_announce<PERSON><PERSON>y", "_announceTimeoutId", "_destroyed", "_portalOutlet", "_onAnnounce", "_animationState", "_live", "_label", "_role", "_liveElementId", "getId", "config", "FIREFOX", "attachComponentPortal", "portal", "_assertNotAttached", "result", "_afterPortalAttached", "attachTemplatePortal", "attachDomPortal", "onAnimationEnd", "animationName", "_completeExit", "run", "enter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_screenReaderAnnounce", "queueMicrotask", "injector", "nativeElement", "classList", "add", "undefined", "setAttribute", "ngOnDestroy", "_clearFromModals", "element", "panelClasses", "Array", "isArray", "for<PERSON>ach", "cssClass", "_exposeToModals", "label", "labelClass", "toggle", "querySelector", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "MatSnackBarContainer_Factory", "viewQuery", "MatSnackBarContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "MatSnackBarContainer_HostBindings", "MatSnackBarContainer_animationend_HostBindingHandler", "$event", "MatSnackBarContainer_animationcancel_HostBindingHandler", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "MatSnackBarContainer_Template", "ɵɵtemplate", "ɵɵelement", "ɵɵattribute", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "MatSnackBar", "_breakpointObserver", "_parentSnackBar", "optional", "skipSelf", "_defaultConfig", "_snackBarRefAtThisLevel", "simpleSnackBarComponent", "snackBarContainerComponent", "handsetCssClass", "_openedSnackBarRef", "parent", "value", "openFromComponent", "component", "_attach", "openFromTemplate", "open", "_config", "_attachSnackBarContainer", "overlayRef", "userInjector", "create", "providers", "provide", "useValue", "containerPortal", "containerRef", "attach", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "pipe", "detachments", "state", "overlayElement", "matches", "announce", "_animateSnackBar", "overlayConfig", "positionStrategy", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "disableAnimations", "MatSnackBar_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "DIRECTIVES", "MatSnackBarModule", "MatSnackBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "matSnackBarAnimations", "snackBarState", "name", "transform", "opacity", "offset", "expr", "animation", "timings", "options"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, DOCUMENT, Injector, afterNextRender, ViewChild, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { OverlayConfig, createGlobalPositionStrategy, createOverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './icon-button-DxiIc1ex.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-BnMiRtmT.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-CObeNzjn.mjs';\nimport './index-BFRo2fUq.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    _overlayRef;\n    /** The instance of the component making up the content of the snack bar. */\n    instance;\n    /**\n     * The instance of the component making up the content of the snack bar.\n     * @docs-private\n     */\n    containerInstance;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    _afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    _onAction = new Subject();\n    /**\n     * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n     * dismissed before the duration passes.\n     */\n    _durationTimeoutId;\n    /** Whether the snack bar was dismissed using the action button. */\n    _dismissedByAction = false;\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    politeness = 'polite';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    announcementMessage = '';\n    /**\n     * The view container that serves as the parent for the snackbar for the purposes of dependency\n     * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n     */\n    viewContainerRef;\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    duration = 0;\n    /** Extra CSS classes to be added to the snack bar container. */\n    panelClass;\n    /** Text layout direction for the snack bar. */\n    direction;\n    /** Data being injected into the child component. */\n    data = null;\n    /** The horizontal position to place the snack bar. */\n    horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarLabel, isStandalone: true, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarActions, isStandalone: true, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarAction, isStandalone: true, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    snackBarRef = inject(MatSnackBarRef);\n    data = inject(MAT_SNACK_BAR_DATA);\n    constructor() { }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SimpleSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: SimpleSnackBar, isStandalone: true, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"], dependencies: [{ kind: \"component\", type: MatButton, selector: \"    button[matButton], a[matButton], button[mat-button], button[mat-raised-button],    button[mat-flat-button], button[mat-stroked-button], a[mat-button], a[mat-raised-button],    a[mat-flat-button], a[mat-stroked-button]  \", inputs: [\"matButton\"], exportAs: [\"matButton\", \"matAnchor\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _platform = inject(Platform);\n    _animationsDisabled = _animationsDisabled();\n    snackBarConfig = inject(MatSnackBarConfig);\n    _document = inject(DOCUMENT);\n    _trackedModals = new Set();\n    _enterFallback;\n    _exitFallback;\n    _injector = inject(Injector);\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _announceDelay = 150;\n    /** The timeout for announcing the snack bar's content. */\n    _announceTimeoutId;\n    /** Whether the component has been destroyed. */\n    _destroyed = false;\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _portalOutlet;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    _onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    _animationState = 'void';\n    /** aria-live value for the live region. */\n    _live;\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _label;\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _role;\n    /** Unique ID of the aria-live element. */\n    _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n    constructor() {\n        super();\n        const config = this.snackBarConfig;\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (config.politeness === 'assertive' && !config.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (config.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    };\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(animationName) {\n        if (animationName === EXIT_ANIMATION) {\n            this._completeExit();\n        }\n        else if (animationName === ENTER_ANIMATION) {\n            clearTimeout(this._enterFallback);\n            this._ngZone.run(() => {\n                this._onEnter.next();\n                this._onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n            // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n            this._changeDetectorRef.markForCheck();\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n            if (this._animationsDisabled) {\n                afterNextRender(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n                }, { injector: this._injector });\n            }\n            else {\n                clearTimeout(this._enterFallback);\n                this._enterFallback = setTimeout(() => {\n                    // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n                    // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n                    this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n                    this.onAnimationEnd(ENTER_ANIMATION);\n                }, 200);\n            }\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        if (this._destroyed) {\n            return of(undefined);\n        }\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            this._changeDetectorRef.markForCheck();\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n            if (this._animationsDisabled) {\n                afterNextRender(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n                }, { injector: this._injector });\n            }\n            else {\n                clearTimeout(this._exitFallback);\n                this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n            }\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n    }\n    _completeExit() {\n        clearTimeout(this._exitFallback);\n        queueMicrotask(() => {\n            this._onExit.next();\n            this._onExit.complete();\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (this._announceTimeoutId) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._announceTimeoutId = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                const element = this._elementRef.nativeElement;\n                const inertElement = element.querySelector('[aria-hidden]');\n                const liveElement = element.querySelector('[aria-live]');\n                if (inertElement && liveElement) {\n                    // If an element in the snack bar content is focused before being moved\n                    // track it and restore focus after moving to the live region.\n                    let focusedElement = null;\n                    if (this._platform.isBrowser &&\n                        document.activeElement instanceof HTMLElement &&\n                        inertElement.contains(document.activeElement)) {\n                        focusedElement = document.activeElement;\n                    }\n                    inertElement.removeAttribute('aria-hidden');\n                    liveElement.appendChild(inertElement);\n                    focusedElement?.focus();\n                    this._onAnnounce.next();\n                    this._onAnnounce.complete();\n                }\n            }, this._announceDelay);\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatSnackBarContainer, isStandalone: true, selector: \"mat-snack-bar-container\", host: { listeners: { \"animationend\": \"onAnimationEnd($event.animationName)\", \"animationcancel\": \"onAnimationEnd($event.animationName)\" }, properties: { \"class.mat-snack-bar-container-enter\": \"_animationState === \\\"visible\\\"\", \"class.mat-snack-bar-container-exit\": \"_animationState === \\\"hidden\\\"\", \"class.mat-snack-bar-container-animations-enabled\": \"!_animationsDisabled\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, imports: [CdkPortalOutlet], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n                        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n                        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n                        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n                        '(animationend)': 'onAnimationEnd($event.animationName)',\n                        '(animationcancel)': 'onAnimationEnd($event.animationName)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }], _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n    _live = inject(LiveAnnouncer);\n    _injector = inject(Injector);\n    _breakpointObserver = inject(BreakpointObserver);\n    _parentSnackBar = inject(MatSnackBar, { optional: true, skipSelf: true });\n    _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n    _animationsDisabled = _animationsDisabled();\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    handsetCssClass = 'mat-mdc-snack-bar-handset';\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor() { }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        const positionStrategy = createGlobalPositionStrategy(this._injector);\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        overlayConfig.disableAnimations = this._animationsDisabled;\n        return createOverlayRef(this._injector, overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, providers: [MatSnackBar], imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        PortalModule,\n                        MatButtonModule,\n                        MatCommonModule,\n                        SimpleSnackBar,\n                        ...DIRECTIVES,\n                    ],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatSnackBar],\n                }]\n        }] });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n    // Represents\n    // trigger('state', [\n    //   state(\n    //     'void, hidden',\n    //     style({\n    //       transform: 'scale(0.8)',\n    //       opacity: 0,\n    //     }),\n    //   ),\n    //   state(\n    //     'visible',\n    //     style({\n    //       transform: 'scale(1)',\n    //       opacity: 1,\n    //     }),\n    //   ),\n    //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    //   transition(\n    //     '* => void, * => hidden',\n    //     animate(\n    //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n    //       style({\n    //         opacity: 0,\n    //       }),\n    //     ),\n    //   ),\n    // ])\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: {\n        type: 7,\n        name: 'state',\n        'definitions': [\n            {\n                type: 0,\n                name: 'void, hidden',\n                styles: { type: 6, styles: { transform: 'scale(0.8)', opacity: 0 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'visible',\n                styles: { type: 6, styles: { transform: 'scale(1)', opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => visible',\n                animation: { type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void, * => hidden',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAClP,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,eAAe,QAAQ,cAAc;AACzD,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,aAAa,EAAEC,4BAA4B,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACnH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,kCAAkC;AACzC,OAAO,sBAAsB;AAC7B,OAAO,mBAAmB;;AAE1B;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgI6F3C,EAAE,CAAA4C,gBAAA;IAAF5C,EAAE,CAAA6C,cAAA,YAsD0Q,CAAC,eAA8D,CAAC;IAtD5U7C,EAAE,CAAA8C,UAAA,mBAAAC,8DAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAsD+TF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAtD3UpD,EAAE,CAAAqD,MAAA,EAsDsW,CAAC;IAtDzWrD,EAAE,CAAAsD,YAAA,CAsD+W,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAtD5XjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuD,SAAA,EAsDsW,CAAC;IAtDzWvD,EAAE,CAAAwD,kBAAA,MAAAP,MAAA,CAAAQ,IAAA,CAAAL,MAAA,KAsDsW,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,SAAAC,4CAAAlB,EAAA,EAAAC,GAAA;AArLtc,MAAMkB,WAAW,GAAG,aAAAC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAW;EACX;EACAC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;EACAC,eAAe,gBAAG,IAAIlD,OAAO,CAAC,CAAC;EAC/B;EACAmD,YAAY,gBAAG,IAAInD,OAAO,CAAC,CAAC;EAC5B;EACAoD,SAAS,gBAAG,IAAIpD,OAAO,CAAC,CAAC;EACzB;AACJ;AACA;AACA;EACIqD,kBAAkB;EAClB;EACAC,kBAAkB,GAAG,KAAK;EAC1BC,WAAWA,CAACN,iBAAiB,EAAEF,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACO,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACT,eAAe,CAACU,MAAM,EAAE;MAC9B,IAAI,CAACX,iBAAiB,CAACY,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACX,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACN,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACF,SAAS,CAACY,IAAI,CAAC,CAAC;MACrB,IAAI,CAACZ,SAAS,CAACa,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACN,OAAO,CAAC,CAAC;IAClB;IACAG,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIa,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAI,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACf,kBAAkB,GAAGgB,UAAU,CAAC,MAAM,IAAI,CAACV,OAAO,CAAC,CAAC,EAAEf,IAAI,CAAC0B,GAAG,CAACF,QAAQ,EAAEzB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA4B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACS,MAAM,EAAE;MAC3B,IAAI,CAACT,YAAY,CAACa,IAAI,CAAC,CAAC;MACxB,IAAI,CAACb,YAAY,CAACc,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAP,cAAcA,CAAA,EAAG;IACb,IAAI,CAACX,WAAW,CAACyB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACR,SAAS,CAACa,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACf,eAAe,CAACc,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACJ,eAAe,CAACe,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxB,eAAe;EAC/B;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAM0B,kBAAkB,gBAAG,IAAI9F,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM+F,iBAAiB,CAAC;EACpB;EACAC,UAAU,GAAG,QAAQ;EACrB;AACJ;AACA;AACA;EACIC,mBAAmB,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;EACAd,QAAQ,GAAG,CAAC;EACZ;EACAe,UAAU;EACV;EACAC,SAAS;EACT;EACA5C,IAAI,GAAG,IAAI;EACX;EACA6C,kBAAkB,GAAG,QAAQ;EAC7B;EACAC,gBAAgB,GAAG,QAAQ;AAC/B;;AAEA;AAAA,IACMC,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,gBAAgB;IAAA;IACnH,OAAOI,IAAI,kBAD8E5G,EAAE,CAAA6G,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;IAAA;EAC3G;EAAC,OAHKR,gBAAgB;AAAA;AAItB;EAAA,QAAAS,SAAA,oBAAAA,SAAA;AAAA;AASA;AAAA,IACMC,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAOT,IAAI,YAAAU,2BAAAR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,kBAAkB;IAAA;IACrH,OAAON,IAAI,kBAf8E5G,EAAE,CAAA6G,iBAAA;MAAAC,IAAA,EAeJI,kBAAkB;MAAAH,SAAA;MAAAC,SAAA;IAAA;EAC7G;EAAC,OAHKE,kBAAkB;AAAA;AAIxB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AASA;AAAA,IACMG,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB,OAAOX,IAAI,YAAAY,0BAAAV,iBAAA;MAAA,YAAAA,iBAAA,IAAwFS,iBAAiB;IAAA;IACpH,OAAOR,IAAI,kBA7B8E5G,EAAE,CAAA6G,iBAAA;MAAAC,IAAA,EA6BJM,iBAAiB;MAAAL,SAAA;MAAAC,SAAA;IAAA;EAC5G;EAAC,OAHKI,iBAAiB;AAAA;AAIvB;EAAA,QAAAH,SAAA,oBAAAA,SAAA;AAAA;AAQc,IAERK,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBC,WAAW,GAAGpH,MAAM,CAAC4D,cAAc,CAAC;IACpCN,IAAI,GAAGtD,MAAM,CAAC4F,kBAAkB,CAAC;IACjCvB,WAAWA,CAAA,EAAG,CAAE;IAChB;IACApB,MAAMA,CAAA,EAAG;MACL,IAAI,CAACmE,WAAW,CAACvC,iBAAiB,CAAC,CAAC;IACxC;IACA;IACA,IAAIwC,SAASA,CAAA,EAAG;MACZ,OAAO,CAAC,CAAC,IAAI,CAAC/D,IAAI,CAACL,MAAM;IAC7B;IACA,OAAOqD,IAAI,YAAAgB,uBAAAd,iBAAA;MAAA,YAAAA,iBAAA,IAAwFW,cAAc;IAAA;IACjH,OAAOI,IAAI,kBAtD8E1H,EAAE,CAAA2H,iBAAA;MAAAb,IAAA,EAsDJQ,cAAc;MAAAP,SAAA;MAAAC,SAAA;MAAAY,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAxF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtDZzC,EAAE,CAAA6C,cAAA,YAsD6L,CAAC;UAtDhM7C,EAAE,CAAAqD,MAAA,EAsDmN,CAAC;UAtDtNrD,EAAE,CAAAsD,YAAA,CAsDyN,CAAC;UAtD5NtD,EAAE,CAAAkI,mBAAA,IAAA1F,qCAAA,gBAsD8O,CAAC;QAAA;QAAA,IAAAC,EAAA;UAtDjPzC,EAAE,CAAAuD,SAAA,CAsDmN,CAAC;UAtDtNvD,EAAE,CAAAwD,kBAAA,MAAAd,GAAA,CAAAe,IAAA,CAAA0E,OAAA,MAsDmN,CAAC;UAtDtNnI,EAAE,CAAAuD,SAAA,CAsD4X,CAAC;UAtD/XvD,EAAE,CAAAoI,aAAA,CAAA1F,GAAA,CAAA8E,SAAA,SAsD4X,CAAC;QAAA;MAAA;MAAAa,YAAA,GAAsGlH,SAAS,EAAyUqF,gBAAgB,EAA+DU,kBAAkB,EAAiEE,iBAAiB;MAAAkB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACvkC;EAAC,OAdKlB,cAAc;AAAA;AAepB;EAAA,QAAAL,SAAA,oBAAAA,SAAA;AAAA;AAOA,MAAMwB,eAAe,GAAG,sBAAsB;AAC9C,MAAMC,cAAc,GAAG,qBAAqB;AAC5C;AACA;AACA;AACA;AAHA,IAIMC,oBAAoB;EAA1B,MAAMA,oBAAoB,SAASnH,gBAAgB,CAAC;IAChDoH,OAAO,GAAGzI,MAAM,CAACI,MAAM,CAAC;IACxBsI,WAAW,GAAG1I,MAAM,CAACK,UAAU,CAAC;IAChCsI,kBAAkB,GAAG3I,MAAM,CAACM,iBAAiB,CAAC;IAC9CsI,SAAS,GAAG5I,MAAM,CAACoB,QAAQ,CAAC;IAC5BO,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3CkH,cAAc,GAAG7I,MAAM,CAAC6F,iBAAiB,CAAC;IAC1CiD,SAAS,GAAG9I,MAAM,CAACO,QAAQ,CAAC;IAC5BwI,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1BC,cAAc;IACdC,aAAa;IACbC,SAAS,GAAGnJ,MAAM,CAACQ,QAAQ,CAAC;IAC5B;IACA4I,cAAc,GAAG,GAAG;IACpB;IACAC,kBAAkB;IAClB;IACAC,UAAU,GAAG,KAAK;IAClB;IACAC,aAAa;IACb;IACAC,WAAW,GAAG,IAAI1I,OAAO,CAAC,CAAC;IAC3B;IACAwD,OAAO,GAAG,IAAIxD,OAAO,CAAC,CAAC;IACvB;IACA4E,QAAQ,GAAG,IAAI5E,OAAO,CAAC,CAAC;IACxB;IACA2I,eAAe,GAAG,MAAM;IACxB;IACAC,KAAK;IACL;AACJ;AACA;AACA;AACA;IACIC,MAAM;IACN;AACJ;AACA;AACA;IACIC,KAAK;IACL;IACAC,cAAc,GAAG7J,MAAM,CAACkB,YAAY,CAAC,CAAC4I,KAAK,CAAC,+BAA+B,CAAC;IAC5EzF,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAM0F,MAAM,GAAG,IAAI,CAAClB,cAAc;MAClC;MACA;MACA,IAAIkB,MAAM,CAACjE,UAAU,KAAK,WAAW,IAAI,CAACiE,MAAM,CAAChE,mBAAmB,EAAE;QAClE,IAAI,CAAC2D,KAAK,GAAG,WAAW;MAC5B,CAAC,MACI,IAAIK,MAAM,CAACjE,UAAU,KAAK,KAAK,EAAE;QAClC,IAAI,CAAC4D,KAAK,GAAG,KAAK;MACtB,CAAC,MACI;QACD,IAAI,CAACA,KAAK,GAAG,QAAQ;MACzB;MACA;MACA;MACA,IAAI,IAAI,CAACd,SAAS,CAACoB,OAAO,EAAE;QACxB,IAAI,IAAI,CAACN,KAAK,KAAK,QAAQ,EAAE;UACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;QACzB;QACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;UAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;QACxB;MACJ;IACJ;IACA;IACAK,qBAAqBA,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACU,qBAAqB,CAACC,MAAM,CAAC;MAC/D,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,OAAOD,MAAM;IACjB;IACA;IACAE,oBAAoBA,CAACJ,MAAM,EAAE;MACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACe,oBAAoB,CAACJ,MAAM,CAAC;MAC9D,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,OAAOD,MAAM;IACjB;IACA;AACJ;AACA;AACA;AACA;IACIG,eAAe,GAAIL,MAAM,IAAK;MAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACb,aAAa,CAACgB,eAAe,CAACL,MAAM,CAAC;MACzD,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,OAAOD,MAAM;IACjB,CAAC;IACD;IACAI,cAAcA,CAACC,aAAa,EAAE;MAC1B,IAAIA,aAAa,KAAKlC,cAAc,EAAE;QAClC,IAAI,CAACmC,aAAa,CAAC,CAAC;MACxB,CAAC,MACI,IAAID,aAAa,KAAKnC,eAAe,EAAE;QACxC1D,YAAY,CAAC,IAAI,CAACqE,cAAc,CAAC;QACjC,IAAI,CAACR,OAAO,CAACkC,GAAG,CAAC,MAAM;UACnB,IAAI,CAACjF,QAAQ,CAACZ,IAAI,CAAC,CAAC;UACpB,IAAI,CAACY,QAAQ,CAACX,QAAQ,CAAC,CAAC;QAC5B,CAAC,CAAC;MACN;IACJ;IACA;IACA6F,KAAKA,CAAA,EAAG;MACJ,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;QAClB,IAAI,CAACG,eAAe,GAAG,SAAS;QAChC;QACA;QACA,IAAI,CAACd,kBAAkB,CAACkC,YAAY,CAAC,CAAC;QACtC,IAAI,CAAClC,kBAAkB,CAACmC,aAAa,CAAC,CAAC;QACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACpJ,mBAAmB,EAAE;UAC1BlB,eAAe,CAAC,MAAM;YAClB,IAAI,CAACgI,OAAO,CAACkC,GAAG,CAAC,MAAMK,cAAc,CAAC,MAAM,IAAI,CAACR,cAAc,CAAClC,eAAe,CAAC,CAAC,CAAC;UACtF,CAAC,EAAE;YAAE2C,QAAQ,EAAE,IAAI,CAAC9B;UAAU,CAAC,CAAC;QACpC,CAAC,MACI;UACDvE,YAAY,CAAC,IAAI,CAACqE,cAAc,CAAC;UACjC,IAAI,CAACA,cAAc,GAAG9D,UAAU,CAAC,MAAM;YACnC;YACA;YACA,IAAI,CAACuD,WAAW,CAACwC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC9E,IAAI,CAACZ,cAAc,CAAClC,eAAe,CAAC;UACxC,CAAC,EAAE,GAAG,CAAC;QACX;MACJ;IACJ;IACA;IACA3D,IAAIA,CAAA,EAAG;MACH,IAAI,IAAI,CAAC2E,UAAU,EAAE;QACjB,OAAOvI,EAAE,CAACsK,SAAS,CAAC;MACxB;MACA;MACA;MACA,IAAI,CAAC5C,OAAO,CAACkC,GAAG,CAAC,MAAM;QACnB;QACA;QACA;QACA,IAAI,CAAClB,eAAe,GAAG,QAAQ;QAC/B,IAAI,CAACd,kBAAkB,CAACkC,YAAY,CAAC,CAAC;QACtC;QACA;QACA;QACA,IAAI,CAACnC,WAAW,CAACwC,aAAa,CAACI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;QAC3D;QACA;QACA1G,YAAY,CAAC,IAAI,CAACyE,kBAAkB,CAAC;QACrC,IAAI,IAAI,CAAC1H,mBAAmB,EAAE;UAC1BlB,eAAe,CAAC,MAAM;YAClB,IAAI,CAACgI,OAAO,CAACkC,GAAG,CAAC,MAAMK,cAAc,CAAC,MAAM,IAAI,CAACR,cAAc,CAACjC,cAAc,CAAC,CAAC,CAAC;UACrF,CAAC,EAAE;YAAE0C,QAAQ,EAAE,IAAI,CAAC9B;UAAU,CAAC,CAAC;QACpC,CAAC,MACI;UACDvE,YAAY,CAAC,IAAI,CAACsE,aAAa,CAAC;UAChC,IAAI,CAACA,aAAa,GAAG/D,UAAU,CAAC,MAAM,IAAI,CAACqF,cAAc,CAACjC,cAAc,CAAC,EAAE,GAAG,CAAC;QACnF;MACJ,CAAC,CAAC;MACF,OAAO,IAAI,CAACjE,OAAO;IACvB;IACA;IACAiH,WAAWA,CAAA,EAAG;MACV,IAAI,CAACjC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACkC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACd,aAAa,CAAC,CAAC;IACxB;IACAA,aAAaA,CAAA,EAAG;MACZ9F,YAAY,CAAC,IAAI,CAACsE,aAAa,CAAC;MAChC8B,cAAc,CAAC,MAAM;QACjB,IAAI,CAAC1G,OAAO,CAACQ,IAAI,CAAC,CAAC;QACnB,IAAI,CAACR,OAAO,CAACS,QAAQ,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIsF,oBAAoBA,CAAA,EAAG;MACnB,MAAMoB,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACwC,aAAa;MAC9C,MAAMQ,YAAY,GAAG,IAAI,CAAC7C,cAAc,CAAC5C,UAAU;MACnD,IAAIyF,YAAY,EAAE;QACd,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;UAC7B;UACAA,YAAY,CAACG,OAAO,CAACC,QAAQ,IAAIL,OAAO,CAACN,SAAS,CAACC,GAAG,CAACU,QAAQ,CAAC,CAAC;QACrE,CAAC,MACI;UACDL,OAAO,CAACN,SAAS,CAACC,GAAG,CAACM,YAAY,CAAC;QACvC;MACJ;MACA,IAAI,CAACK,eAAe,CAAC,CAAC;MACtB;MACA;MACA;MACA,MAAMC,KAAK,GAAG,IAAI,CAACrC,MAAM,CAACuB,aAAa;MACvC,MAAMe,UAAU,GAAG,qBAAqB;MACxCD,KAAK,CAACb,SAAS,CAACe,MAAM,CAACD,UAAU,EAAE,CAACD,KAAK,CAACG,aAAa,CAAC,IAAIF,UAAU,EAAE,CAAC,CAAC;IAC9E;IACA;AACJ;AACA;AACA;AACA;IACIF,eAAeA,CAAA,EAAG;MACd;MACA;MACA;MACA;MACA;MACA;MACA,MAAMK,EAAE,GAAG,IAAI,CAACvC,cAAc;MAC9B,MAAMwC,MAAM,GAAG,IAAI,CAACvD,SAAS,CAACwD,gBAAgB,CAAC,mDAAmD,CAAC;MACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;QACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;QAChD,IAAI,CAAC5D,cAAc,CAACqC,GAAG,CAACqB,KAAK,CAAC;QAC9B,IAAI,CAACC,QAAQ,EAAE;UACXD,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEc,EAAE,CAAC;QACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;UAClCK,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEoB,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;QACxD;MACJ;IACJ;IACA;IACAZ,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACzC,cAAc,CAAC8C,OAAO,CAACY,KAAK,IAAI;QACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;QAChD,IAAID,QAAQ,EAAE;UACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE,CAAC,CAACkD,IAAI,CAAC,CAAC;UACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;YACrBC,KAAK,CAACnB,YAAY,CAAC,WAAW,EAAEuB,QAAQ,CAAC;UAC7C,CAAC,MACI;YACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;UACtC;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,CAACjE,cAAc,CAACkE,KAAK,CAAC,CAAC;IAC/B;IACA;IACA9C,kBAAkBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACZ,aAAa,CAAC2D,WAAW,CAAC,CAAC,KAAK,OAAOpG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrF,MAAMqG,KAAK,CAAC,0EAA0E,CAAC;MAC3F;IACJ;IACA;AACJ;AACA;AACA;IACIpC,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAC1B,kBAAkB,EAAE;QACzB;MACJ;MACA,IAAI,CAACZ,OAAO,CAAC2E,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAC/D,kBAAkB,GAAGlE,UAAU,CAAC,MAAM;UACvC,IAAI,IAAI,CAACmE,UAAU,EAAE;YACjB;UACJ;UACA,MAAMmC,OAAO,GAAG,IAAI,CAAC/C,WAAW,CAACwC,aAAa;UAC9C,MAAMmC,YAAY,GAAG5B,OAAO,CAACU,aAAa,CAAC,eAAe,CAAC;UAC3D,MAAMmB,WAAW,GAAG7B,OAAO,CAACU,aAAa,CAAC,aAAa,CAAC;UACxD,IAAIkB,YAAY,IAAIC,WAAW,EAAE;YAC7B;YACA;YACA,IAAIC,cAAc,GAAG,IAAI;YACzB,IAAI,IAAI,CAAC3E,SAAS,CAAC4E,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CN,YAAY,CAACO,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;cAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;YAC3C;YACAL,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;YAC3CM,WAAW,CAACO,WAAW,CAACR,YAAY,CAAC;YACrCE,cAAc,EAAEO,KAAK,CAAC,CAAC;YACvB,IAAI,CAACtE,WAAW,CAAC1E,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC0E,WAAW,CAACzE,QAAQ,CAAC,CAAC;UAC/B;QACJ,CAAC,EAAE,IAAI,CAACqE,cAAc,CAAC;MAC3B,CAAC,CAAC;IACN;IACA,OAAO9C,IAAI,YAAAyH,6BAAAvH,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgC,oBAAoB;IAAA;IACvH,OAAOjB,IAAI,kBAhW8E1H,EAAE,CAAA2H,iBAAA;MAAAb,IAAA,EAgWJ6B,oBAAoB;MAAA5B,SAAA;MAAAoH,SAAA,WAAAC,2BAAA3L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhWlBzC,EAAE,CAAAqO,WAAA,CAgWwkB5M,eAAe;UAhWzlBzB,EAAE,CAAAqO,WAAA,CAAA3K,GAAA;QAAA;QAAA,IAAAjB,EAAA;UAAA,IAAA6L,EAAA;UAAFtO,EAAE,CAAAuO,cAAA,CAAAD,EAAA,GAAFtO,EAAE,CAAAwO,WAAA,QAAA9L,GAAA,CAAAgH,aAAA,GAAA4E,EAAA,CAAAG,KAAA;UAAFzO,EAAE,CAAAuO,cAAA,CAAAD,EAAA,GAAFtO,EAAE,CAAAwO,WAAA,QAAA9L,GAAA,CAAAoH,MAAA,GAAAwE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAzH,SAAA;MAAA0H,QAAA;MAAAC,YAAA,WAAAC,kCAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA8C,UAAA,0BAAA+L,qDAAAC,MAAA;YAAA,OAgWJpM,GAAA,CAAAiI,cAAA,CAAAmE,MAAA,CAAAlE,aAAmC,CAAC;UAAA,CAAjB,CAAC,6BAAAmE,wDAAAD,MAAA;YAAA,OAApBpM,GAAA,CAAAiI,cAAA,CAAAmE,MAAA,CAAAlE,aAAmC,CAAC;UAAA,CAAjB,CAAC;QAAA;QAAA,IAAAnI,EAAA;UAhWlBzC,EAAE,CAAAgP,WAAA,kCAAAtM,GAAA,CAAAkH,eAAA,KAgWgB,SAAD,CAAC,iCAAAlH,GAAA,CAAAkH,eAAA,aAAD,CAAC,gDAAAlH,GAAA,CAAAZ,mBAAD,CAAC;QAAA;MAAA;MAAAmN,QAAA,GAhWlBjP,EAAE,CAAAkP,0BAAA;MAAArH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmH,8BAAA1M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA6C,cAAA,YAgW00B,CAAC,eAA6M,CAAC,YAAqI,CAAC;UAhWjqC7C,EAAE,CAAAoP,UAAA,IAAAzL,2CAAA,wBAgWqsC,CAAC;UAhWxsC3D,EAAE,CAAAsD,YAAA,CAgWitC,CAAC;UAhWptCtD,EAAE,CAAAqP,SAAA,SAgWu6C,CAAC;UAhW16CrP,EAAE,CAAAsD,YAAA,CAgWi7C,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAb,EAAA;UAhW57CzC,EAAE,CAAAuD,SAAA,EAgW62C,CAAC;UAhWh3CvD,EAAE,CAAAsP,WAAA,cAAA5M,GAAA,CAAAmH,KAAA,UAAAnH,GAAA,CAAAqH,KAAA,QAAArH,GAAA,CAAAsH,cAAA;QAAA;MAAA;MAAA3B,YAAA,GAgWm3I5G,eAAe;MAAA6G,MAAA;MAAAC,aAAA;IAAA;EACj+I;EAAC,OA5RKI,oBAAoB;AAAA;AA6R1B;EAAA,QAAA1B,SAAA,oBAAAA,SAAA;AAAA;;AAkBA;AACA;AACA;AACA;AACA;AACA,SAASsI,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIvJ,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMwJ,6BAA6B,gBAAG,IAAIvP,cAAc,CAAC,+BAA+B,EAAE;EACtFwP,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AAFA,IAGMI,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACd9F,KAAK,GAAG1J,MAAM,CAACmB,aAAa,CAAC;IAC7BgI,SAAS,GAAGnJ,MAAM,CAACQ,QAAQ,CAAC;IAC5BiP,mBAAmB,GAAGzP,MAAM,CAAC4B,kBAAkB,CAAC;IAChD8N,eAAe,GAAG1P,MAAM,CAACwP,WAAW,EAAE;MAAEG,QAAQ,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACzEC,cAAc,GAAG7P,MAAM,CAACqP,6BAA6B,CAAC;IACtD1N,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C;AACJ;AACA;AACA;AACA;IACImO,uBAAuB,GAAG,IAAI;IAC9B;IACAC,uBAAuB,GAAG5I,cAAc;IACxC;IACA6I,0BAA0B,GAAGxH,oBAAoB;IACjD;IACAyH,eAAe,GAAG,2BAA2B;IAC7C;IACA,IAAIC,kBAAkBA,CAAA,EAAG;MACrB,MAAMC,MAAM,GAAG,IAAI,CAACT,eAAe;MACnC,OAAOS,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACJ,uBAAuB;IAC5E;IACA,IAAII,kBAAkBA,CAACE,KAAK,EAAE;MAC1B,IAAI,IAAI,CAACV,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAACQ,kBAAkB,GAAGE,KAAK;MACnD,CAAC,MACI;QACD,IAAI,CAACN,uBAAuB,GAAGM,KAAK;MACxC;IACJ;IACA/L,WAAWA,CAAA,EAAG,CAAE;IAChB;AACJ;AACA;AACA;AACA;AACA;AACA;IACIgM,iBAAiBA,CAACC,SAAS,EAAEvG,MAAM,EAAE;MACjC,OAAO,IAAI,CAACwG,OAAO,CAACD,SAAS,EAAEvG,MAAM,CAAC;IAC1C;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIyG,gBAAgBA,CAAC3I,QAAQ,EAAEkC,MAAM,EAAE;MAC/B,OAAO,IAAI,CAACwG,OAAO,CAAC1I,QAAQ,EAAEkC,MAAM,CAAC;IACzC;IACA;AACJ;AACA;AACA;AACA;AACA;IACI0G,IAAIA,CAACzI,OAAO,EAAE/E,MAAM,GAAG,EAAE,EAAE8G,MAAM,EAAE;MAC/B,MAAM2G,OAAO,GAAG;QAAE,GAAG,IAAI,CAACb,cAAc;QAAE,GAAG9F;MAAO,CAAC;MACrD;MACA;MACA2G,OAAO,CAACpN,IAAI,GAAG;QAAE0E,OAAO;QAAE/E;MAAO,CAAC;MAClC;MACA;MACA,IAAIyN,OAAO,CAAC3K,mBAAmB,KAAKiC,OAAO,EAAE;QACzC0I,OAAO,CAAC3K,mBAAmB,GAAGsF,SAAS;MAC3C;MACA,OAAO,IAAI,CAACgF,iBAAiB,CAAC,IAAI,CAACN,uBAAuB,EAAEW,OAAO,CAAC;IACxE;IACA;AACJ;AACA;IACIjM,OAAOA,CAAA,EAAG;MACN,IAAI,IAAI,CAACyL,kBAAkB,EAAE;QACzB,IAAI,CAACA,kBAAkB,CAACzL,OAAO,CAAC,CAAC;MACrC;IACJ;IACA8G,WAAWA,CAAA,EAAG;MACV;MACA,IAAI,IAAI,CAACuE,uBAAuB,EAAE;QAC9B,IAAI,CAACA,uBAAuB,CAACrL,OAAO,CAAC,CAAC;MAC1C;IACJ;IACA;AACJ;AACA;IACIkM,wBAAwBA,CAACC,UAAU,EAAE7G,MAAM,EAAE;MACzC,MAAM8G,YAAY,GAAG9G,MAAM,IAAIA,MAAM,CAAC/D,gBAAgB,IAAI+D,MAAM,CAAC/D,gBAAgB,CAACiF,QAAQ;MAC1F,MAAMA,QAAQ,GAAGzK,QAAQ,CAACsQ,MAAM,CAAC;QAC7BX,MAAM,EAAEU,YAAY,IAAI,IAAI,CAAC1H,SAAS;QACtC4H,SAAS,EAAE,CAAC;UAAEC,OAAO,EAAEnL,iBAAiB;UAAEoL,QAAQ,EAAElH;QAAO,CAAC;MAChE,CAAC,CAAC;MACF,MAAMmH,eAAe,GAAG,IAAI3P,eAAe,CAAC,IAAI,CAACyO,0BAA0B,EAAEjG,MAAM,CAAC/D,gBAAgB,EAAEiF,QAAQ,CAAC;MAC/G,MAAMkG,YAAY,GAAGP,UAAU,CAACQ,MAAM,CAACF,eAAe,CAAC;MACvDC,YAAY,CAACrN,QAAQ,CAAC+E,cAAc,GAAGkB,MAAM;MAC7C,OAAOoH,YAAY,CAACrN,QAAQ;IAChC;IACA;AACJ;AACA;IACIyM,OAAOA,CAACc,OAAO,EAAEC,UAAU,EAAE;MACzB,MAAMvH,MAAM,GAAG;QAAE,GAAG,IAAIlE,iBAAiB,CAAC,CAAC;QAAE,GAAG,IAAI,CAACgK,cAAc;QAAE,GAAGyB;MAAW,CAAC;MACpF,MAAMV,UAAU,GAAG,IAAI,CAACW,cAAc,CAACxH,MAAM,CAAC;MAC9C,MAAMyH,SAAS,GAAG,IAAI,CAACb,wBAAwB,CAACC,UAAU,EAAE7G,MAAM,CAAC;MACnE,MAAM3C,WAAW,GAAG,IAAIxD,cAAc,CAAC4N,SAAS,EAAEZ,UAAU,CAAC;MAC7D,IAAIS,OAAO,YAAY1Q,WAAW,EAAE;QAChC,MAAMuJ,MAAM,GAAG,IAAI1I,cAAc,CAAC6P,OAAO,EAAE,IAAI,EAAE;UAC7CI,SAAS,EAAE1H,MAAM,CAACzG,IAAI;UACtB8D;QACJ,CAAC,CAAC;QACFA,WAAW,CAACtD,QAAQ,GAAG0N,SAAS,CAAClH,oBAAoB,CAACJ,MAAM,CAAC;MACjE,CAAC,MACI;QACD,MAAMe,QAAQ,GAAG,IAAI,CAACyG,eAAe,CAAC3H,MAAM,EAAE3C,WAAW,CAAC;QAC1D,MAAM8C,MAAM,GAAG,IAAI3I,eAAe,CAAC8P,OAAO,EAAEhG,SAAS,EAAEJ,QAAQ,CAAC;QAChE,MAAM0G,UAAU,GAAGH,SAAS,CAACvH,qBAAqB,CAACC,MAAM,CAAC;QAC1D;QACA9C,WAAW,CAACtD,QAAQ,GAAG6N,UAAU,CAAC7N,QAAQ;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAAC2L,mBAAmB,CACnBmC,OAAO,CAAC/P,WAAW,CAACgQ,eAAe,CAAC,CACpCC,IAAI,CAAC5P,SAAS,CAAC0O,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzCxN,SAAS,CAACyN,KAAK,IAAI;QACpBpB,UAAU,CAACqB,cAAc,CAAC9G,SAAS,CAACe,MAAM,CAAC,IAAI,CAAC+D,eAAe,EAAE+B,KAAK,CAACE,OAAO,CAAC;MACnF,CAAC,CAAC;MACF,IAAInI,MAAM,CAAChE,mBAAmB,EAAE;QAC5B;QACAyL,SAAS,CAAChI,WAAW,CAACjF,SAAS,CAAC,MAAM;UAClC,IAAI,CAACmF,KAAK,CAACyI,QAAQ,CAACpI,MAAM,CAAChE,mBAAmB,EAAEgE,MAAM,CAACjE,UAAU,CAAC;QACtE,CAAC,CAAC;MACN;MACA,IAAI,CAACsM,gBAAgB,CAAChL,WAAW,EAAE2C,MAAM,CAAC;MAC1C,IAAI,CAACmG,kBAAkB,GAAG9I,WAAW;MACrC,OAAO,IAAI,CAAC8I,kBAAkB;IAClC;IACA;IACAkC,gBAAgBA,CAAChL,WAAW,EAAE2C,MAAM,EAAE;MAClC;MACA3C,WAAW,CAAC5B,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;QACzC;QACA,IAAI,IAAI,CAAC2L,kBAAkB,IAAI9I,WAAW,EAAE;UACxC,IAAI,CAAC8I,kBAAkB,GAAG,IAAI;QAClC;QACA,IAAInG,MAAM,CAAChE,mBAAmB,EAAE;UAC5B,IAAI,CAAC2D,KAAK,CAACuD,KAAK,CAAC,CAAC;QACtB;MACJ,CAAC,CAAC;MACF;MACA,IAAIlD,MAAM,CAAC7E,QAAQ,IAAI6E,MAAM,CAAC7E,QAAQ,GAAG,CAAC,EAAE;QACxCkC,WAAW,CAAC3B,WAAW,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAM6C,WAAW,CAACnC,aAAa,CAAC8E,MAAM,CAAC7E,QAAQ,CAAC,CAAC;MACzF;MACA,IAAI,IAAI,CAACgL,kBAAkB,EAAE;QACzB;QACA;QACA,IAAI,CAACA,kBAAkB,CAAC1K,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;UACrD6C,WAAW,CAACrD,iBAAiB,CAAC6G,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,IAAI,CAACsF,kBAAkB,CAACzL,OAAO,CAAC,CAAC;MACrC,CAAC,MACI;QACD;QACA2C,WAAW,CAACrD,iBAAiB,CAAC6G,KAAK,CAAC,CAAC;MACzC;IACJ;IACA;AACJ;AACA;AACA;IACI2G,cAAcA,CAACxH,MAAM,EAAE;MACnB,MAAMsI,aAAa,GAAG,IAAIvQ,aAAa,CAAC,CAAC;MACzCuQ,aAAa,CAACnM,SAAS,GAAG6D,MAAM,CAAC7D,SAAS;MAC1C,MAAMoM,gBAAgB,GAAGvQ,4BAA4B,CAAC,IAAI,CAACoH,SAAS,CAAC;MACrE;MACA,MAAMoJ,KAAK,GAAGxI,MAAM,CAAC7D,SAAS,KAAK,KAAK;MACxC,MAAMsM,MAAM,GAAGzI,MAAM,CAAC5D,kBAAkB,KAAK,MAAM,IAC9C4D,MAAM,CAAC5D,kBAAkB,KAAK,OAAO,IAAI,CAACoM,KAAM,IAChDxI,MAAM,CAAC5D,kBAAkB,KAAK,KAAK,IAAIoM,KAAM;MAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAIzI,MAAM,CAAC5D,kBAAkB,KAAK,QAAQ;MACjE,IAAIqM,MAAM,EAAE;QACRF,gBAAgB,CAACI,IAAI,CAAC,GAAG,CAAC;MAC9B,CAAC,MACI,IAAID,OAAO,EAAE;QACdH,gBAAgB,CAACK,KAAK,CAAC,GAAG,CAAC;MAC/B,CAAC,MACI;QACDL,gBAAgB,CAACM,kBAAkB,CAAC,CAAC;MACzC;MACA;MACA,IAAI7I,MAAM,CAAC3D,gBAAgB,KAAK,KAAK,EAAE;QACnCkM,gBAAgB,CAACO,GAAG,CAAC,GAAG,CAAC;MAC7B,CAAC,MACI;QACDP,gBAAgB,CAACQ,MAAM,CAAC,GAAG,CAAC;MAChC;MACAT,aAAa,CAACC,gBAAgB,GAAGA,gBAAgB;MACjDD,aAAa,CAACU,iBAAiB,GAAG,IAAI,CAACpR,mBAAmB;MAC1D,OAAOK,gBAAgB,CAAC,IAAI,CAACmH,SAAS,EAAEkJ,aAAa,CAAC;IAC1D;IACA;AACJ;AACA;AACA;AACA;IACIX,eAAeA,CAAC3H,MAAM,EAAE3C,WAAW,EAAE;MACjC,MAAMyJ,YAAY,GAAG9G,MAAM,IAAIA,MAAM,CAAC/D,gBAAgB,IAAI+D,MAAM,CAAC/D,gBAAgB,CAACiF,QAAQ;MAC1F,OAAOzK,QAAQ,CAACsQ,MAAM,CAAC;QACnBX,MAAM,EAAEU,YAAY,IAAI,IAAI,CAAC1H,SAAS;QACtC4H,SAAS,EAAE,CACP;UAAEC,OAAO,EAAEpN,cAAc;UAAEqN,QAAQ,EAAE7J;QAAY,CAAC,EAClD;UAAE4J,OAAO,EAAEpL,kBAAkB;UAAEqL,QAAQ,EAAElH,MAAM,CAACzG;QAAK,CAAC;MAE9D,CAAC,CAAC;IACN;IACA,OAAOgD,IAAI,YAAA0M,oBAAAxM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgJ,WAAW;IAAA;IAC9G,OAAOyD,KAAK,kBA/lB6EpT,EAAE,CAAAqT,kBAAA;MAAAC,KAAA,EA+lBY3D,WAAW;MAAAD,OAAA,EAAXC,WAAW,CAAAlJ,IAAA;MAAAgJ,UAAA,EAAc;IAAM;EAC1I;EAAC,OA5NKE,WAAW;AAAA;AA6NjB;EAAA,QAAA1I,SAAA,oBAAAA,SAAA;AAAA;AAKA,MAAMsM,UAAU,GAAG,CAAC5K,oBAAoB,EAAEnC,gBAAgB,EAAEU,kBAAkB,EAAEE,iBAAiB,CAAC;AAAC,IAC7FoM,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB,OAAO/M,IAAI,YAAAgN,0BAAA9M,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6M,iBAAiB;IAAA;IACpH,OAAOE,IAAI,kBAzmB8E1T,EAAE,CAAA2T,gBAAA;MAAA7M,IAAA,EAymBS0M;IAAiB;IAKrH,OAAOI,IAAI,kBA9mB8E5T,EAAE,CAAA6T,gBAAA;MAAA3C,SAAA,EA8mBuC,CAACvB,WAAW,CAAC;MAAAmE,OAAA,GAAY1R,aAAa,EAChKR,YAAY,EACZR,eAAe,EACfmB,eAAe,EACf+E,cAAc,EAAE/E,eAAe;IAAA;EAC3C;EAAC,OAZKiR,iBAAiB;AAAA;AAavB;EAAA,QAAAvM,SAAA,oBAAAA,SAAA;AAAA;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8M,qBAAqB,GAAG;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE;IACXlN,IAAI,EAAE,CAAC;IACPmN,IAAI,EAAE,OAAO;IACb,aAAa,EAAE,CACX;MACInN,IAAI,EAAE,CAAC;MACPmN,IAAI,EAAE,cAAc;MACpB3L,MAAM,EAAE;QAAExB,IAAI,EAAE,CAAC;QAAEwB,MAAM,EAAE;UAAE4L,SAAS,EAAE,YAAY;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACItN,IAAI,EAAE,CAAC;MACPmN,IAAI,EAAE,SAAS;MACf3L,MAAM,EAAE;QAAExB,IAAI,EAAE,CAAC;QAAEwB,MAAM,EAAE;UAAE4L,SAAS,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACnF,CAAC,EACD;MACItN,IAAI,EAAE,CAAC;MACPuN,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAE;QAAExN,IAAI,EAAE,CAAC;QAAEwB,MAAM,EAAE,IAAI;QAAEiM,OAAO,EAAE;MAAmC,CAAC;MACjFC,OAAO,EAAE;IACb,CAAC,EACD;MACI1N,IAAI,EAAE,CAAC;MACPuN,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE;QACPxN,IAAI,EAAE,CAAC;QACPwB,MAAM,EAAE;UAAExB,IAAI,EAAE,CAAC;UAAEwB,MAAM,EAAE;YAAE6L,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASzO,kBAAkB,EAAEyJ,6BAA6B,EAAED,qCAAqC,EAAEI,WAAW,EAAEvI,iBAAiB,EAAEF,kBAAkB,EAAElB,iBAAiB,EAAE2C,oBAAoB,EAAEnC,gBAAgB,EAAEgN,iBAAiB,EAAEzP,cAAc,EAAEuD,cAAc,EAAEyM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}