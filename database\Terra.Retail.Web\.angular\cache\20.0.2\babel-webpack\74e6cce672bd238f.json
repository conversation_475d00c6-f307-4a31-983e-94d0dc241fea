{"ast": null, "code": "import { CommonModule } from '@angular/common';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatListModule } from '@angular/material/list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/tabs\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction SupplierDetailsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h1\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.supplier.supplierCode, \" - \", ctx_r0.getSupplierTypeName(ctx_r0.supplier.supplierTypeId));\n  }\n}\nfunction SupplierDetailsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SupplierDetailsComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editSupplier());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.nameEn);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.supplier.website, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.website);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"tel:\" + ctx_r0.supplier.phone2, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.phone2);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"mailto:\" + ctx_r0.supplier.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.email);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.address);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_87_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"tel:\" + ctx_r0.supplier.contactPersonPhone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.contactPersonPhone);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_87_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"mailto:\" + ctx_r0.supplier.contactPersonEmail, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.contactPersonEmail);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 31)(9, \"span\", 32);\n    i0.ɵɵtext(10, \"\\u0627\\u0644\\u0627\\u0633\\u0645:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 33);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, SupplierDetailsComponent_div_9_mat_card_87_div_13_Template, 5, 2, \"div\", 34)(14, SupplierDetailsComponent_div_9_mat_card_87_div_14_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.contactPersonName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.contactPersonPhone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.contactPersonEmail);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.taxNumber);\n  }\n}\nfunction SupplierDetailsComponent_div_9_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.commercialRegister);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_113_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.bankAccountNumber);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"account_balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0628\\u0646\\u0643\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 31)(9, \"span\", 32);\n    i0.ɵɵtext(10, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 33);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, SupplierDetailsComponent_div_9_mat_card_113_div_13_Template, 5, 1, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.bankName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.bankAccountNumber);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 46)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"p\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.notes);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 48)(1, \"mat-card-content\")(2, \"div\", 49)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-chip\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 51)(8, \"div\", 52)(9, \"span\", 32);\n    i0.ɵɵtext(10, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 33);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 52)(14, \"span\", 32);\n    i0.ɵɵtext(15, \"\\u0627\\u0644\\u0633\\u0639\\u0631:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 33);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 52)(19, \"span\", 32);\n    i0.ɵɵtext(20, \"\\u0622\\u062E\\u0631 \\u0637\\u0644\\u0628:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 33);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 52)(24, \"span\", 32);\n    i0.ɵɵtext(25, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 33);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const product_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r3.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r3.categoryName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(product_r3.productCode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(product_r3.unitPrice));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(product_r3.lastOrderDate));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r3.totalOrdered);\n  }\n}\nfunction SupplierDetailsComponent_div_9_mat_card_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 53)(1, \"mat-card-content\")(2, \"div\", 54)(3, \"div\", 55)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 57);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 58)(11, \"span\", 59);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-chip\", 60);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const transaction_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(transaction_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(transaction_r4.referenceNumber);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getBalanceClass(transaction_r4.amount));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatCurrency(transaction_r4.amount), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(transaction_r4.date));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", transaction_r4.transactionType);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", transaction_r4.transactionType === \"purchase\" ? \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\" : \"\\u062F\\u0641\\u0639\\u0629\", \" \");\n  }\n}\nfunction SupplierDetailsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-card\", 16)(3, \"mat-card-content\")(4, \"div\", 17)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"account_balance_wallet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"mat-card\", 20)(13, \"mat-card-content\")(14, \"div\", 17)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"credit_card\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 18)(18, \"h3\");\n    i0.ɵɵtext(19, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 21);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"mat-card\", 22)(23, \"mat-card-content\")(24, \"div\", 17)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"star\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 18)(28, \"h3\");\n    i0.ɵɵtext(29, \"\\u0627\\u0644\\u062A\\u0642\\u064A\\u064A\\u0645\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 23);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"mat-card\", 24)(33, \"mat-card-content\")(34, \"div\", 17)(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 18)(38, \"h3\");\n    i0.ɵɵtext(39, \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0633\\u062F\\u0627\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 25);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(42, \"mat-tab-group\", 26)(43, \"mat-tab\", 27)(44, \"div\", 28)(45, \"div\", 29)(46, \"mat-card\", 30)(47, \"mat-card-header\")(48, \"mat-card-title\")(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"mat-card-content\")(54, \"div\", 31)(55, \"span\", 32);\n    i0.ɵɵtext(56, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 33);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 31)(60, \"span\", 32);\n    i0.ɵɵtext(61, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"span\", 33);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(64, SupplierDetailsComponent_div_9_div_64_Template, 5, 1, \"div\", 34);\n    i0.ɵɵelementStart(65, \"div\", 31)(66, \"span\", 32);\n    i0.ɵɵtext(67, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\", 33);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(70, SupplierDetailsComponent_div_9_div_70_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"mat-card\", 30)(72, \"mat-card-header\")(73, \"mat-card-title\")(74, \"mat-icon\");\n    i0.ɵɵtext(75, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\");\n    i0.ɵɵtext(77, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"mat-card-content\")(79, \"div\", 31)(80, \"span\", 32);\n    i0.ɵɵtext(81, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u0623\\u0648\\u0644:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"a\", 35);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(84, SupplierDetailsComponent_div_9_div_84_Template, 5, 2, \"div\", 34)(85, SupplierDetailsComponent_div_9_div_85_Template, 5, 2, \"div\", 34)(86, SupplierDetailsComponent_div_9_div_86_Template, 5, 1, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(87, SupplierDetailsComponent_div_9_mat_card_87_Template, 15, 3, \"mat-card\", 36);\n    i0.ɵɵelementStart(88, \"mat-card\", 30)(89, \"mat-card-header\")(90, \"mat-card-title\")(91, \"mat-icon\");\n    i0.ɵɵtext(92, \"account_balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\");\n    i0.ɵɵtext(94, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"mat-card-content\")(96, \"div\", 31)(97, \"span\", 32);\n    i0.ɵɵtext(98, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"span\", 37);\n    i0.ɵɵtext(100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"div\", 31)(102, \"span\", 32);\n    i0.ɵɵtext(103, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"span\", 33);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(106, \"div\", 31)(107, \"span\", 32);\n    i0.ɵɵtext(108, \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0633\\u062F\\u0627\\u062F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"span\", 33);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(111, SupplierDetailsComponent_div_9_div_111_Template, 5, 1, \"div\", 34)(112, SupplierDetailsComponent_div_9_div_112_Template, 5, 1, \"div\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(113, SupplierDetailsComponent_div_9_mat_card_113_Template, 14, 2, \"mat-card\", 36)(114, SupplierDetailsComponent_div_9_mat_card_114_Template, 10, 1, \"mat-card\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"mat-tab\", 39)(116, \"div\", 28)(117, \"div\", 40);\n    i0.ɵɵtemplate(118, SupplierDetailsComponent_div_9_mat_card_118_Template, 28, 6, \"mat-card\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(119, \"mat-tab\", 42)(120, \"div\", 28)(121, \"div\", 43);\n    i0.ɵɵtemplate(122, SupplierDetailsComponent_div_9_mat_card_122_Template, 15, 7, \"mat-card\", 44);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getBalanceClass(ctx_r0.supplier.currentBalance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatCurrency(ctx_r0.supplier.currentBalance), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.supplier.creditLimit));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r0.getRatingStars(ctx_r0.supplier.rating));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.supplier.paymentTerms, \" \\u064A\\u0648\\u0645\");\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.supplierCode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.nameAr);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.nameEn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getSupplierTypeName(ctx_r0.supplier.supplierTypeId));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.website);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"href\", \"tel:\" + ctx_r0.supplier.phone1, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.supplier.phone1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.phone2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.contactPersonName);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getBalanceClass(ctx_r0.supplier.currentBalance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatCurrency(ctx_r0.supplier.currentBalance), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.supplier.creditLimit));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.supplier.paymentTerms, \" \\u064A\\u0648\\u0645\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.taxNumber);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.commercialRegister);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.bankName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.supplier.notes);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.supplierProducts);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.supplierTransactions);\n  }\n}\nfunction SupplierDetailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"mat-spinner\", 62);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SupplierDetailsComponent = /*#__PURE__*/(() => {\n  class SupplierDetailsComponent {\n    router;\n    route;\n    http;\n    // Component State\n    isLoading = false;\n    supplierId = 0;\n    // Data\n    supplier = null;\n    supplierProducts = [];\n    supplierTransactions = [];\n    // Subscriptions\n    subscriptions = [];\n    constructor(router, route, http) {\n      this.router = router;\n      this.route = route;\n      this.http = http;\n    }\n    ngOnInit() {\n      this.supplierId = Number(this.route.snapshot.paramMap.get('id'));\n      if (this.supplierId) {\n        this.loadSupplierDetails();\n        this.loadSupplierProducts();\n        this.loadSupplierTransactions();\n      } else {\n        this.router.navigate(['/suppliers']);\n      }\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Load supplier details\n     */\n    loadSupplierDetails() {\n      this.isLoading = true;\n      const sub = this.http.get(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({\n        next: response => {\n          console.log('Supplier details API response:', response);\n          // Map API response to match our interface\n          if (response.supplier) {\n            this.supplier = {\n              id: response.supplier.Id || response.supplier.id,\n              supplierCode: response.supplier.SupplierCode || response.supplier.supplierCode,\n              nameAr: response.supplier.NameAr || response.supplier.nameAr,\n              nameEn: response.supplier.NameEn || response.supplier.nameEn,\n              supplierTypeId: response.supplier.SupplierTypeId || response.supplier.supplierTypeId,\n              phone1: response.supplier.Phone || response.supplier.phone1 || response.supplier.Phone1,\n              phone2: response.supplier.Phone2 || response.supplier.phone2,\n              email: response.supplier.Email || response.supplier.email,\n              website: response.supplier.Website || response.supplier.website,\n              address: response.supplier.Address || response.supplier.address,\n              contactPersonName: response.supplier.ContactPersonName || response.supplier.contactPersonName,\n              contactPersonPhone: response.supplier.ContactPersonPhone || response.supplier.contactPersonPhone,\n              contactPersonEmail: response.supplier.ContactPersonEmail || response.supplier.contactPersonEmail,\n              paymentTerms: response.supplier.PaymentTerms || response.supplier.paymentTerms || 30,\n              creditLimit: response.supplier.CreditLimit || response.supplier.creditLimit || 0,\n              currentBalance: response.supplier.CurrentBalance || response.supplier.currentBalance || 0,\n              taxNumber: response.supplier.TaxNumber || response.supplier.taxNumber,\n              commercialRegister: response.supplier.CommercialRegister || response.supplier.commercialRegister,\n              bankName: response.supplier.BankName || response.supplier.bankName,\n              bankAccountNumber: response.supplier.BankAccountNumber || response.supplier.bankAccountNumber,\n              rating: response.supplier.Rating || response.supplier.rating || 0,\n              notes: response.supplier.Notes || response.supplier.notes,\n              isActive: response.supplier.IsActive !== false,\n              createdAt: response.supplier.CreatedAt || response.supplier.createdAt || new Date().toISOString()\n            };\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading supplier details:', error);\n          this.supplier = this.getMockSupplierDetails();\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Load supplier products\n     */\n    loadSupplierProducts() {\n      // Mock data for now\n      this.supplierProducts = this.getMockSupplierProducts();\n    }\n    /**\n     * Load supplier transactions\n     */\n    loadSupplierTransactions() {\n      // Mock data for now\n      this.supplierTransactions = this.getMockSupplierTransactions();\n    }\n    /**\n     * Edit supplier\n     */\n    editSupplier() {\n      this.router.navigate(['/suppliers/edit', this.supplierId]);\n    }\n    /**\n     * Go back to suppliers list\n     */\n    goBack() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Get supplier type name\n     */\n    getSupplierTypeName(typeId) {\n      const types = {\n        1: 'مورد محلي',\n        2: 'مورد دولي',\n        3: 'مورد حكومي'\n      };\n      return types[typeId || 1] || 'مورد محلي';\n    }\n    /**\n     * Get balance class for styling\n     */\n    getBalanceClass(balance) {\n      if (balance > 0) return 'positive';\n      if (balance < 0) return 'negative';\n      return 'zero';\n    }\n    /**\n     * Format currency\n     */\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('ar-EG', {\n        style: 'currency',\n        currency: 'EGP'\n      }).format(amount);\n    }\n    /**\n     * Format date\n     */\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('ar-EG');\n    }\n    /**\n     * Get rating stars\n     */\n    getRatingStars(rating) {\n      const stars = rating || 0;\n      return '⭐'.repeat(Math.floor(stars)) + '☆'.repeat(5 - Math.floor(stars));\n    }\n    /**\n     * Get mock supplier details\n     */\n    getMockSupplierDetails() {\n      return {\n        id: this.supplierId,\n        supplierCode: 'SUP001',\n        nameAr: 'شركة الأهرام للتجارة',\n        nameEn: 'Al-Ahram Trading Company',\n        supplierTypeId: 1,\n        phone1: '+************',\n        phone2: '+201*********',\n        email: '<EMAIL>',\n        website: 'www.ahram-trading.com',\n        address: 'شارع الهرم، الجيزة، مصر',\n        contactPersonName: 'أحمد محمد علي',\n        contactPersonPhone: '+************',\n        contactPersonEmail: '<EMAIL>',\n        paymentTerms: 30,\n        creditLimit: 100000,\n        currentBalance: -15000,\n        taxNumber: '*********',\n        commercialRegister: 'CR123456',\n        bankName: 'البنك الأهلي المصري',\n        bankAccountNumber: '*********012',\n        rating: 4,\n        notes: 'مورد موثوق للمواد الغذائية والمشروبات',\n        isActive: true,\n        createdAt: new Date().toISOString()\n      };\n    }\n    /**\n     * Get mock supplier products\n     */\n    getMockSupplierProducts() {\n      return [{\n        id: 1,\n        productCode: 'PRD001',\n        nameAr: 'أرز مصري فاخر',\n        nameEn: 'Premium Egyptian Rice',\n        categoryName: 'حبوب',\n        unitPrice: 25.50,\n        lastOrderDate: '2024-01-15',\n        totalOrdered: 500\n      }, {\n        id: 2,\n        productCode: 'PRD002',\n        nameAr: 'زيت عباد الشمس',\n        nameEn: 'Sunflower Oil',\n        categoryName: 'زيوت',\n        unitPrice: 45.00,\n        lastOrderDate: '2024-01-10',\n        totalOrdered: 200\n      }, {\n        id: 3,\n        productCode: 'PRD003',\n        nameAr: 'سكر أبيض',\n        nameEn: 'White Sugar',\n        categoryName: 'سكريات',\n        unitPrice: 18.75,\n        lastOrderDate: '2024-01-08',\n        totalOrdered: 300\n      }];\n    }\n    /**\n     * Get mock supplier transactions\n     */\n    getMockSupplierTransactions() {\n      return [{\n        id: 1,\n        transactionType: 'purchase',\n        amount: -25000,\n        date: '2024-01-15',\n        description: 'فاتورة شراء رقم PUR001',\n        referenceNumber: 'PUR001'\n      }, {\n        id: 2,\n        transactionType: 'payment',\n        amount: 10000,\n        date: '2024-01-10',\n        description: 'دفعة نقدية',\n        referenceNumber: 'PAY001'\n      }, {\n        id: 3,\n        transactionType: 'purchase',\n        amount: -15000,\n        date: '2024-01-08',\n        description: 'فاتورة شراء رقم PUR002',\n        referenceNumber: 'PUR002'\n      }];\n    }\n    static ɵfac = function SupplierDetailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SupplierDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierDetailsComponent,\n      selectors: [[\"app-supplier-details\"]],\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"supplier-details-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [\"class\", \"header-text\", 4, \"ngIf\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [\"class\", \"content\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"edit-btn\", 3, \"click\"], [1, \"content\"], [1, \"overview-cards\"], [1, \"overview-card\", \"balance-card\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"balance-amount\", 3, \"ngClass\"], [1, \"overview-card\", \"credit-card\"], [1, \"credit-amount\"], [1, \"overview-card\", \"rating-card\"], [1, \"rating-stars\"], [1, \"overview-card\", \"payment-card\"], [1, \"payment-terms\"], [\"animationDuration\", \"300ms\", 1, \"details-tabs\"], [\"label\", \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"], [1, \"tab-content\"], [1, \"info-grid\"], [1, \"info-card\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"info-row\", 4, \"ngIf\"], [1, \"value\", \"link\", 3, \"href\"], [\"class\", \"info-card\", 4, \"ngIf\"], [1, \"value\", 3, \"ngClass\"], [\"class\", \"info-card full-width\", 4, \"ngIf\"], [\"label\", \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"], [1, \"products-list\"], [\"class\", \"product-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"\\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"], [1, \"transactions-list\"], [\"class\", \"transaction-card\", 4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"value\", \"link\", 3, \"href\"], [1, \"info-card\", \"full-width\"], [1, \"notes-text\"], [1, \"product-card\"], [1, \"product-header\"], [1, \"category-chip\"], [1, \"product-details\"], [1, \"detail-item\"], [1, \"transaction-card\"], [1, \"transaction-header\"], [1, \"transaction-info\"], [1, \"reference\"], [1, \"transaction-amount\", 3, \"ngClass\"], [1, \"transaction-footer\"], [1, \"date\"], [3, \"ngClass\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function SupplierDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SupplierDetailsComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, SupplierDetailsComponent_div_7_Template, 5, 3, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, SupplierDetailsComponent_div_8_Template, 6, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, SupplierDetailsComponent_div_9_Template, 123, 26, \"div\", 7)(10, SupplierDetailsComponent_div_10_Template, 4, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.supplier);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.supplier);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.supplier);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, MatCardModule, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardTitle, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatTabsModule, i7.MatTab, i7.MatTabGroup, MatChipsModule, i8.MatChip, MatProgressSpinnerModule, i9.MatProgressSpinner, MatDividerModule, MatListModule],\n      styles: [\"\\n\\n.supplier-details-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--warning-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2) !important;\\n  color: white !important;\\n  width: 48px !important;\\n  height: 48px !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .edit-btn[_ngcontent-%COMP%] {\\n  background: var(--warning-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .edit-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--warning-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n\\n\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) 0;\\n}\\n\\n\\n\\n.overview-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-xl);\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.overview-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.overview-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-lg) !important;\\n  padding: var(--spacing-xl) !important;\\n}\\n.overview-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: var(--radius-full);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.overview-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.overview-card[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.overview-card[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--gray-600);\\n  margin: 0 0 var(--spacing-xs) 0;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.overview-card[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n.overview-card.balance-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.overview-card.balance-card[_ngcontent-%COMP%]   .balance-amount.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.overview-card.balance-card[_ngcontent-%COMP%]   .balance-amount.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.overview-card.balance-card[_ngcontent-%COMP%]   .balance-amount.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-600);\\n}\\n.overview-card.credit-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.overview-card.credit-card[_ngcontent-%COMP%]   .credit-amount[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n}\\n.overview-card.rating-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.overview-card.rating-card[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%] {\\n  color: var(--warning-600);\\n  font-size: 1.25rem;\\n}\\n.overview-card.payment-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--info-500), var(--info-600));\\n}\\n.overview-card.payment-card[_ngcontent-%COMP%]   .payment-terms[_ngcontent-%COMP%] {\\n  color: var(--info-600);\\n}\\n\\n\\n\\n.details-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]   .mat-mdc-tab-header[_ngcontent-%COMP%] {\\n  border-bottom: 2px solid var(--gray-200) !important;\\n}\\n.details-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]   .mat-mdc-tab-header[_ngcontent-%COMP%]   .mat-mdc-tab[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n  font-size: 1rem !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n}\\n.details-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]   .mat-mdc-tab-header[_ngcontent-%COMP%]   .mat-mdc-tab.mdc-tab--active[_ngcontent-%COMP%] {\\n  color: var(--primary-600) !important;\\n}\\n.details-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]   .mat-mdc-tab-header[_ngcontent-%COMP%]   .mat-mdc-tab-header-pagination[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) 0;\\n}\\n\\n\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n.info-grid[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-md) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  border-bottom: 1px solid var(--gray-200) !important;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.125rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600) !important;\\n  font-size: 1.25rem !important;\\n  width: 1.25rem !important;\\n  height: 1.25rem !important;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n}\\n\\n.info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md) 0;\\n  border-bottom: 1px solid var(--gray-100);\\n}\\n.info-row[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-700);\\n  flex-shrink: 0;\\n  margin-left: var(--spacing-md);\\n}\\n.info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: var(--gray-900);\\n  text-align: left;\\n}\\n.info-row[_ngcontent-%COMP%]   .value.link[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n  text-decoration: none;\\n}\\n.info-row[_ngcontent-%COMP%]   .value.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.info-row[_ngcontent-%COMP%]   .value.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n  font-weight: 600;\\n}\\n.info-row[_ngcontent-%COMP%]   .value.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n  font-weight: 600;\\n}\\n.info-row[_ngcontent-%COMP%]   .value.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-600);\\n}\\n\\n.notes-text[_ngcontent-%COMP%] {\\n  color: var(--gray-700);\\n  line-height: 1.6;\\n  margin: 0;\\n}\\n\\n\\n\\n.products-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: var(--spacing-lg);\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-lg) !important;\\n  box-shadow: var(--shadow-md) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.product-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) !important;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: var(--spacing-md);\\n}\\n.product-card[_ngcontent-%COMP%]   .product-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0;\\n  flex: 1;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-header[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%] {\\n  background: var(--primary-100) !important;\\n  color: var(--primary-700) !important;\\n  font-size: 0.75rem !important;\\n  font-weight: 600 !important;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: var(--spacing-xs) 0;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-900);\\n  font-weight: 600;\\n}\\n\\n\\n\\n.transactions-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n\\n.transaction-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-lg) !important;\\n  box-shadow: var(--shadow-sm) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) !important;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: var(--spacing-md);\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin: 0 0 var(--spacing-xs) 0;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-info[_ngcontent-%COMP%]   .reference[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  margin: 0;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-amount[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-amount.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-header[_ngcontent-%COMP%]   .transaction-amount.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-footer[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-footer[_ngcontent-%COMP%]   .mat-mdc-chip[_ngcontent-%COMP%] {\\n  font-size: 0.75rem !important;\\n  font-weight: 600 !important;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-footer[_ngcontent-%COMP%]   .mat-mdc-chip.purchase[_ngcontent-%COMP%] {\\n  background: var(--error-100) !important;\\n  color: var(--error-700) !important;\\n}\\n.transaction-card[_ngcontent-%COMP%]   .transaction-footer[_ngcontent-%COMP%]   .mat-mdc-chip.payment[_ngcontent-%COMP%] {\\n  background: var(--success-100) !important;\\n  color: var(--success-700) !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  font-size: 1.125rem;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   .mat-mdc-progress-spinner[_ngcontent-%COMP%] {\\n  --mdc-circular-progress-active-indicator-color: var(--primary-500);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n  .overview-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .overview-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .products-list[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) 0;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .overview-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    text-align: center !important;\\n    gap: var(--spacing-md) !important;\\n  }\\n  .overview-card[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .info-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: var(--spacing-xs);\\n  }\\n  .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n  .transaction-header[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    gap: var(--spacing-sm) !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SupplierDetailsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatTabsModule", "MatChipsModule", "MatProgressSpinnerModule", "MatDividerModule", "MatListModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "supplier", "nameAr", "ɵɵtextInterpolate2", "supplierCode", "getSupplierTypeName", "supplierTypeId", "ɵɵlistener", "SupplierDetailsComponent_div_8_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "editSupplier", "nameEn", "ɵɵproperty", "website", "ɵɵsanitizeUrl", "phone2", "email", "address", "contactPersonPhone", "contactPersonEmail", "ɵɵtemplate", "SupplierDetailsComponent_div_9_mat_card_87_div_13_Template", "SupplierDetailsComponent_div_9_mat_card_87_div_14_Template", "contactPersonName", "taxNumber", "commercialRegister", "bankAccountNumber", "SupplierDetailsComponent_div_9_mat_card_113_div_13_Template", "bankName", "notes", "product_r3", "categoryName", "productCode", "formatCurrency", "unitPrice", "formatDate", "lastOrderDate", "totalOrdered", "transaction_r4", "description", "referenceNumber", "getBalanceClass", "amount", "ɵɵtextInterpolate1", "date", "transactionType", "SupplierDetailsComponent_div_9_div_64_Template", "SupplierDetailsComponent_div_9_div_70_Template", "SupplierDetailsComponent_div_9_div_84_Template", "SupplierDetailsComponent_div_9_div_85_Template", "SupplierDetailsComponent_div_9_div_86_Template", "SupplierDetailsComponent_div_9_mat_card_87_Template", "SupplierDetailsComponent_div_9_div_111_Template", "SupplierDetailsComponent_div_9_div_112_Template", "SupplierDetailsComponent_div_9_mat_card_113_Template", "SupplierDetailsComponent_div_9_mat_card_114_Template", "SupplierDetailsComponent_div_9_mat_card_118_Template", "SupplierDetailsComponent_div_9_mat_card_122_Template", "currentBalance", "creditLimit", "getRatingStars", "rating", "paymentTerms", "phone1", "supplierProducts", "supplierTransactions", "ɵɵelement", "SupplierDetailsComponent", "router", "route", "http", "isLoading", "supplierId", "subscriptions", "constructor", "ngOnInit", "Number", "snapshot", "paramMap", "get", "loadSupplierDetails", "loadSupplierProducts", "loadSupplierTransactions", "navigate", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "subscribe", "next", "response", "console", "log", "id", "Id", "SupplierCode", "NameAr", "NameEn", "SupplierTypeId", "Phone", "Phone1", "Phone2", "Email", "Website", "Address", "ContactPersonName", "ContactPersonPhone", "ContactPersonEmail", "PaymentTerms", "CreditLimit", "CurrentBalance", "TaxNumber", "CommercialRegister", "BankName", "BankAccountNumber", "Rating", "Notes", "isActive", "IsActive", "createdAt", "CreatedAt", "Date", "toISOString", "error", "getMockSupplierDetails", "push", "getMockSupplierProducts", "getMockSupplierTransactions", "goBack", "typeId", "types", "balance", "Intl", "NumberFormat", "style", "currency", "format", "toLocaleDateString", "stars", "repeat", "Math", "floor", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "SupplierDetailsComponent_Template", "rf", "ctx", "SupplierDetailsComponent_Template_button_click_4_listener", "SupplierDetailsComponent_div_7_Template", "SupplierDetailsComponent_div_8_Template", "SupplierDetailsComponent_div_9_Template", "SupplierDetailsComponent_div_10_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "Mat<PERSON><PERSON>", "MatTabGroup", "i8", "MatChip", "i9", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-details\\supplier-details.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-details\\supplier-details.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatListModule } from '@angular/material/list';\n\n// Interfaces\ninterface SupplierDetails {\n  id: number;\n  supplierCode: string;\n  nameAr: string;\n  nameEn?: string;\n  supplierTypeId?: number;\n  phone1: string;\n  phone2?: string;\n  email?: string;\n  website?: string;\n  address?: string;\n  contactPersonName?: string;\n  contactPersonPhone?: string;\n  contactPersonEmail?: string;\n  paymentTerms: number;\n  creditLimit: number;\n  currentBalance: number;\n  taxNumber?: string;\n  commercialRegister?: string;\n  bankName?: string;\n  bankAccountNumber?: string;\n  rating?: number;\n  notes?: string;\n  isActive: boolean;\n  createdAt: string;\n}\n\ninterface SupplierProduct {\n  id: number;\n  productCode: string;\n  nameAr: string;\n  nameEn: string;\n  categoryName: string;\n  unitPrice: number;\n  lastOrderDate: string;\n  totalOrdered: number;\n}\n\ninterface SupplierTransaction {\n  id: number;\n  transactionType: string;\n  amount: number;\n  date: string;\n  description: string;\n  referenceNumber: string;\n}\n\n@Component({\n  selector: 'app-supplier-details',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTabsModule,\n    MatChipsModule,\n    MatProgressSpinnerModule,\n    MatDividerModule,\n    MatListModule\n  ],\n  templateUrl: './supplier-details.component.html',\n  styleUrls: ['./supplier-details.component.scss']\n})\nexport class SupplierDetailsComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  supplierId: number = 0;\n  \n  // Data\n  supplier: SupplierDetails | null = null;\n  supplierProducts: SupplierProduct[] = [];\n  supplierTransactions: SupplierTransaction[] = [];\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    this.supplierId = Number(this.route.snapshot.paramMap.get('id'));\n    if (this.supplierId) {\n      this.loadSupplierDetails();\n      this.loadSupplierProducts();\n      this.loadSupplierTransactions();\n    } else {\n      this.router.navigate(['/suppliers']);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Load supplier details\n   */\n  private loadSupplierDetails(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({\n      next: (response) => {\n        console.log('Supplier details API response:', response);\n        // Map API response to match our interface\n        if (response.supplier) {\n          this.supplier = {\n            id: response.supplier.Id || response.supplier.id,\n            supplierCode: response.supplier.SupplierCode || response.supplier.supplierCode,\n            nameAr: response.supplier.NameAr || response.supplier.nameAr,\n            nameEn: response.supplier.NameEn || response.supplier.nameEn,\n            supplierTypeId: response.supplier.SupplierTypeId || response.supplier.supplierTypeId,\n            phone1: response.supplier.Phone || response.supplier.phone1 || response.supplier.Phone1,\n            phone2: response.supplier.Phone2 || response.supplier.phone2,\n            email: response.supplier.Email || response.supplier.email,\n            website: response.supplier.Website || response.supplier.website,\n            address: response.supplier.Address || response.supplier.address,\n            contactPersonName: response.supplier.ContactPersonName || response.supplier.contactPersonName,\n            contactPersonPhone: response.supplier.ContactPersonPhone || response.supplier.contactPersonPhone,\n            contactPersonEmail: response.supplier.ContactPersonEmail || response.supplier.contactPersonEmail,\n            paymentTerms: response.supplier.PaymentTerms || response.supplier.paymentTerms || 30,\n            creditLimit: response.supplier.CreditLimit || response.supplier.creditLimit || 0,\n            currentBalance: response.supplier.CurrentBalance || response.supplier.currentBalance || 0,\n            taxNumber: response.supplier.TaxNumber || response.supplier.taxNumber,\n            commercialRegister: response.supplier.CommercialRegister || response.supplier.commercialRegister,\n            bankName: response.supplier.BankName || response.supplier.bankName,\n            bankAccountNumber: response.supplier.BankAccountNumber || response.supplier.bankAccountNumber,\n            rating: response.supplier.Rating || response.supplier.rating || 0,\n            notes: response.supplier.Notes || response.supplier.notes,\n            isActive: response.supplier.IsActive !== false,\n            createdAt: response.supplier.CreatedAt || response.supplier.createdAt || new Date().toISOString()\n          };\n        }\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading supplier details:', error);\n        this.supplier = this.getMockSupplierDetails();\n        this.isLoading = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Load supplier products\n   */\n  private loadSupplierProducts(): void {\n    // Mock data for now\n    this.supplierProducts = this.getMockSupplierProducts();\n  }\n\n  /**\n   * Load supplier transactions\n   */\n  private loadSupplierTransactions(): void {\n    // Mock data for now\n    this.supplierTransactions = this.getMockSupplierTransactions();\n  }\n\n  /**\n   * Edit supplier\n   */\n  editSupplier(): void {\n    this.router.navigate(['/suppliers/edit', this.supplierId]);\n  }\n\n  /**\n   * Go back to suppliers list\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Get supplier type name\n   */\n  getSupplierTypeName(typeId?: number): string {\n    const types: { [key: number]: string } = {\n      1: 'مورد محلي',\n      2: 'مورد دولي',\n      3: 'مورد حكومي'\n    };\n    return types[typeId || 1] || 'مورد محلي';\n  }\n\n  /**\n   * Get balance class for styling\n   */\n  getBalanceClass(balance: number): string {\n    if (balance > 0) return 'positive';\n    if (balance < 0) return 'negative';\n    return 'zero';\n  }\n\n  /**\n   * Format currency\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('ar-EG', {\n      style: 'currency',\n      currency: 'EGP'\n    }).format(amount);\n  }\n\n  /**\n   * Format date\n   */\n  formatDate(date: string): string {\n    return new Date(date).toLocaleDateString('ar-EG');\n  }\n\n  /**\n   * Get rating stars\n   */\n  getRatingStars(rating?: number): string {\n    const stars = rating || 0;\n    return '⭐'.repeat(Math.floor(stars)) + '☆'.repeat(5 - Math.floor(stars));\n  }\n\n  /**\n   * Get mock supplier details\n   */\n  private getMockSupplierDetails(): SupplierDetails {\n    return {\n      id: this.supplierId,\n      supplierCode: 'SUP001',\n      nameAr: 'شركة الأهرام للتجارة',\n      nameEn: 'Al-Ahram Trading Company',\n      supplierTypeId: 1,\n      phone1: '+************',\n      phone2: '+201*********',\n      email: '<EMAIL>',\n      website: 'www.ahram-trading.com',\n      address: 'شارع الهرم، الجيزة، مصر',\n      contactPersonName: 'أحمد محمد علي',\n      contactPersonPhone: '+************',\n      contactPersonEmail: '<EMAIL>',\n      paymentTerms: 30,\n      creditLimit: 100000,\n      currentBalance: -15000,\n      taxNumber: '*********',\n      commercialRegister: 'CR123456',\n      bankName: 'البنك الأهلي المصري',\n      bankAccountNumber: '*********012',\n      rating: 4,\n      notes: 'مورد موثوق للمواد الغذائية والمشروبات',\n      isActive: true,\n      createdAt: new Date().toISOString()\n    };\n  }\n\n  /**\n   * Get mock supplier products\n   */\n  private getMockSupplierProducts(): SupplierProduct[] {\n    return [\n      {\n        id: 1,\n        productCode: 'PRD001',\n        nameAr: 'أرز مصري فاخر',\n        nameEn: 'Premium Egyptian Rice',\n        categoryName: 'حبوب',\n        unitPrice: 25.50,\n        lastOrderDate: '2024-01-15',\n        totalOrdered: 500\n      },\n      {\n        id: 2,\n        productCode: 'PRD002',\n        nameAr: 'زيت عباد الشمس',\n        nameEn: 'Sunflower Oil',\n        categoryName: 'زيوت',\n        unitPrice: 45.00,\n        lastOrderDate: '2024-01-10',\n        totalOrdered: 200\n      },\n      {\n        id: 3,\n        productCode: 'PRD003',\n        nameAr: 'سكر أبيض',\n        nameEn: 'White Sugar',\n        categoryName: 'سكريات',\n        unitPrice: 18.75,\n        lastOrderDate: '2024-01-08',\n        totalOrdered: 300\n      }\n    ];\n  }\n\n  /**\n   * Get mock supplier transactions\n   */\n  private getMockSupplierTransactions(): SupplierTransaction[] {\n    return [\n      {\n        id: 1,\n        transactionType: 'purchase',\n        amount: -25000,\n        date: '2024-01-15',\n        description: 'فاتورة شراء رقم PUR001',\n        referenceNumber: 'PUR001'\n      },\n      {\n        id: 2,\n        transactionType: 'payment',\n        amount: 10000,\n        date: '2024-01-10',\n        description: 'دفعة نقدية',\n        referenceNumber: 'PAY001'\n      },\n      {\n        id: 3,\n        transactionType: 'purchase',\n        amount: -15000,\n        date: '2024-01-08',\n        description: 'فاتورة شراء رقم PUR002',\n        referenceNumber: 'PUR002'\n      }\n    ];\n  }\n}\n", "<!-- Terra Retail ERP - Supplier Details -->\n<div class=\"supplier-details-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\" *ngIf=\"supplier\">\n          <h1 class=\"page-title\">{{ supplier.nameAr }}</h1>\n          <p class=\"page-subtitle\">{{ supplier.supplierCode }} - {{ getSupplierTypeName(supplier.supplierTypeId) }}</p>\n        </div>\n      </div>\n      <div class=\"header-actions\" *ngIf=\"supplier\">\n        <button mat-raised-button color=\"primary\" class=\"edit-btn\" (click)=\"editSupplier()\">\n          <mat-icon>edit</mat-icon>\n          <span>تعديل البيانات</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content -->\n  <div class=\"content\" *ngIf=\"!isLoading && supplier\">\n    \n    <!-- Supplier Overview Cards -->\n    <div class=\"overview-cards\">\n      \n      <!-- Balance Card -->\n      <mat-card class=\"overview-card balance-card\">\n        <mat-card-content>\n          <div class=\"card-icon\">\n            <mat-icon>account_balance_wallet</mat-icon>\n          </div>\n          <div class=\"card-info\">\n            <h3>الرصيد الحالي</h3>\n            <p class=\"balance-amount\" [ngClass]=\"getBalanceClass(supplier.currentBalance)\">\n              {{ formatCurrency(supplier.currentBalance) }}\n            </p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Credit Limit Card -->\n      <mat-card class=\"overview-card credit-card\">\n        <mat-card-content>\n          <div class=\"card-icon\">\n            <mat-icon>credit_card</mat-icon>\n          </div>\n          <div class=\"card-info\">\n            <h3>الحد الائتماني</h3>\n            <p class=\"credit-amount\">{{ formatCurrency(supplier.creditLimit) }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Rating Card -->\n      <mat-card class=\"overview-card rating-card\">\n        <mat-card-content>\n          <div class=\"card-icon\">\n            <mat-icon>star</mat-icon>\n          </div>\n          <div class=\"card-info\">\n            <h3>التقييم</h3>\n            <p class=\"rating-stars\">{{ getRatingStars(supplier.rating) }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Payment Terms Card -->\n      <mat-card class=\"overview-card payment-card\">\n        <mat-card-content>\n          <div class=\"card-icon\">\n            <mat-icon>schedule</mat-icon>\n          </div>\n          <div class=\"card-info\">\n            <h3>مدة السداد</h3>\n            <p class=\"payment-terms\">{{ supplier.paymentTerms }} يوم</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n    </div>\n\n    <!-- Tabs Content -->\n    <mat-tab-group class=\"details-tabs\" animationDuration=\"300ms\">\n      \n      <!-- Basic Information Tab -->\n      <mat-tab label=\"المعلومات الأساسية\">\n        <div class=\"tab-content\">\n          \n          <div class=\"info-grid\">\n            \n            <!-- Company Information -->\n            <mat-card class=\"info-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>business</mat-icon>\n                  <span>معلومات الشركة</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-row\">\n                  <span class=\"label\">كود المورد:</span>\n                  <span class=\"value\">{{ supplier.supplierCode }}</span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">الاسم بالعربية:</span>\n                  <span class=\"value\">{{ supplier.nameAr }}</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.nameEn\">\n                  <span class=\"label\">الاسم بالإنجليزية:</span>\n                  <span class=\"value\">{{ supplier.nameEn }}</span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">نوع المورد:</span>\n                  <span class=\"value\">{{ getSupplierTypeName(supplier.supplierTypeId) }}</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.website\">\n                  <span class=\"label\">الموقع الإلكتروني:</span>\n                  <a [href]=\"supplier.website\" target=\"_blank\" class=\"value link\">{{ supplier.website }}</a>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Contact Information -->\n            <mat-card class=\"info-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>contact_phone</mat-icon>\n                  <span>معلومات الاتصال</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-row\">\n                  <span class=\"label\">الهاتف الأول:</span>\n                  <a [href]=\"'tel:' + supplier.phone1\" class=\"value link\">{{ supplier.phone1 }}</a>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.phone2\">\n                  <span class=\"label\">الهاتف الثاني:</span>\n                  <a [href]=\"'tel:' + supplier.phone2\" class=\"value link\">{{ supplier.phone2 }}</a>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.email\">\n                  <span class=\"label\">البريد الإلكتروني:</span>\n                  <a [href]=\"'mailto:' + supplier.email\" class=\"value link\">{{ supplier.email }}</a>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.address\">\n                  <span class=\"label\">العنوان:</span>\n                  <span class=\"value\">{{ supplier.address }}</span>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Contact Person -->\n            <mat-card class=\"info-card\" *ngIf=\"supplier.contactPersonName\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>person</mat-icon>\n                  <span>الشخص المسؤول</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-row\">\n                  <span class=\"label\">الاسم:</span>\n                  <span class=\"value\">{{ supplier.contactPersonName }}</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.contactPersonPhone\">\n                  <span class=\"label\">الهاتف:</span>\n                  <a [href]=\"'tel:' + supplier.contactPersonPhone\" class=\"value link\">{{ supplier.contactPersonPhone }}</a>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.contactPersonEmail\">\n                  <span class=\"label\">البريد الإلكتروني:</span>\n                  <a [href]=\"'mailto:' + supplier.contactPersonEmail\" class=\"value link\">{{ supplier.contactPersonEmail }}</a>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Financial Information -->\n            <mat-card class=\"info-card\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>account_balance</mat-icon>\n                  <span>المعلومات المالية</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-row\">\n                  <span class=\"label\">الرصيد الحالي:</span>\n                  <span class=\"value\" [ngClass]=\"getBalanceClass(supplier.currentBalance)\">\n                    {{ formatCurrency(supplier.currentBalance) }}\n                  </span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">الحد الائتماني:</span>\n                  <span class=\"value\">{{ formatCurrency(supplier.creditLimit) }}</span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">مدة السداد:</span>\n                  <span class=\"value\">{{ supplier.paymentTerms }} يوم</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.taxNumber\">\n                  <span class=\"label\">الرقم الضريبي:</span>\n                  <span class=\"value\">{{ supplier.taxNumber }}</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.commercialRegister\">\n                  <span class=\"label\">السجل التجاري:</span>\n                  <span class=\"value\">{{ supplier.commercialRegister }}</span>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Bank Information -->\n            <mat-card class=\"info-card\" *ngIf=\"supplier.bankName\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>account_balance</mat-icon>\n                  <span>المعلومات البنكية</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"info-row\">\n                  <span class=\"label\">اسم البنك:</span>\n                  <span class=\"value\">{{ supplier.bankName }}</span>\n                </div>\n                <div class=\"info-row\" *ngIf=\"supplier.bankAccountNumber\">\n                  <span class=\"label\">رقم الحساب:</span>\n                  <span class=\"value\">{{ supplier.bankAccountNumber }}</span>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Additional Information -->\n            <mat-card class=\"info-card full-width\" *ngIf=\"supplier.notes\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon>note</mat-icon>\n                  <span>ملاحظات</span>\n                </mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <p class=\"notes-text\">{{ supplier.notes }}</p>\n              </mat-card-content>\n            </mat-card>\n\n          </div>\n        </div>\n      </mat-tab>\n\n      <!-- Products Tab -->\n      <mat-tab label=\"المنتجات\">\n        <div class=\"tab-content\">\n          <div class=\"products-list\">\n            <mat-card *ngFor=\"let product of supplierProducts\" class=\"product-card\">\n              <mat-card-content>\n                <div class=\"product-header\">\n                  <h3>{{ product.nameAr }}</h3>\n                  <mat-chip class=\"category-chip\">{{ product.categoryName }}</mat-chip>\n                </div>\n                <div class=\"product-details\">\n                  <div class=\"detail-item\">\n                    <span class=\"label\">كود المنتج:</span>\n                    <span class=\"value\">{{ product.productCode }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <span class=\"label\">السعر:</span>\n                    <span class=\"value\">{{ formatCurrency(product.unitPrice) }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <span class=\"label\">آخر طلب:</span>\n                    <span class=\"value\">{{ formatDate(product.lastOrderDate) }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <span class=\"label\">إجمالي المطلوب:</span>\n                    <span class=\"value\">{{ product.totalOrdered }}</span>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n      </mat-tab>\n\n      <!-- Transactions Tab -->\n      <mat-tab label=\"المعاملات المالية\">\n        <div class=\"tab-content\">\n          <div class=\"transactions-list\">\n            <mat-card *ngFor=\"let transaction of supplierTransactions\" class=\"transaction-card\">\n              <mat-card-content>\n                <div class=\"transaction-header\">\n                  <div class=\"transaction-info\">\n                    <h3>{{ transaction.description }}</h3>\n                    <p class=\"reference\">{{ transaction.referenceNumber }}</p>\n                  </div>\n                  <div class=\"transaction-amount\" [ngClass]=\"getBalanceClass(transaction.amount)\">\n                    {{ formatCurrency(transaction.amount) }}\n                  </div>\n                </div>\n                <div class=\"transaction-footer\">\n                  <span class=\"date\">{{ formatDate(transaction.date) }}</span>\n                  <mat-chip [ngClass]=\"transaction.transactionType\">\n                    {{ transaction.transactionType === 'purchase' ? 'مشتريات' : 'دفعة' }}\n                  </mat-chip>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n      </mat-tab>\n\n    </mat-tab-group>\n\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل بيانات المورد...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAK9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;;;ICH5CC,EADF,CAAAC,cAAA,aAA0C,aACjB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgF;IAC3GF,EAD2G,CAAAG,YAAA,EAAI,EACzG;;;;IAFmBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAqB;IACnBR,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAAS,kBAAA,KAAAH,MAAA,CAAAC,QAAA,CAAAG,YAAA,SAAAJ,MAAA,CAAAK,mBAAA,CAAAL,MAAA,CAAAC,QAAA,CAAAK,cAAA,EAAgF;;;;;;IAI3GZ,EADF,CAAAC,cAAA,cAA6C,iBACyC;IAAzBD,EAAA,CAAAa,UAAA,mBAAAC,gEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,YAAA,EAAc;IAAA,EAAC;IACjFnB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,sFAAc;IAExBF,EAFwB,CAAAG,YAAA,EAAO,EACpB,EACL;;;;;IA6FMH,EADF,CAAAC,cAAA,cAA8C,eACxB;IAAAD,EAAA,CAAAE,MAAA,yGAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IADgBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAa,MAAA,CAAqB;;;;;IAOzCpB,EADF,CAAAC,cAAA,cAA+C,eACzB;IAAAD,EAAA,CAAAE,MAAA,yGAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,YAAgE;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACxFF,EADwF,CAAAG,YAAA,EAAI,EACtF;;;;IADDH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAe,OAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAyB;IAAoCvB,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAe,OAAA,CAAsB;;;;;IAmBtFtB,EADF,CAAAC,cAAA,cAA8C,eACxB;IAAAD,EAAA,CAAAE,MAAA,iFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,YAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC/EF,EAD+E,CAAAG,YAAA,EAAI,EAC7E;;;;IADDH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,kBAAAf,MAAA,CAAAC,QAAA,CAAAiB,MAAA,EAAAxB,EAAA,CAAAuB,aAAA,CAAiC;IAAoBvB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAiB,MAAA,CAAqB;;;;;IAG7ExB,EADF,CAAAC,cAAA,cAA6C,eACvB;IAAAD,EAAA,CAAAE,MAAA,yGAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,YAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAChFF,EADgF,CAAAG,YAAA,EAAI,EAC9E;;;;IADDH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAqB,UAAA,qBAAAf,MAAA,CAAAC,QAAA,CAAAkB,KAAA,EAAAzB,EAAA,CAAAuB,aAAA,CAAmC;IAAoBvB,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAkB,KAAA,CAAoB;;;;;IAG9EzB,EADF,CAAAC,cAAA,cAA+C,eACzB;IAAAD,EAAA,CAAAE,MAAA,kDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;;;;IADgBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAmB,OAAA,CAAsB;;;;;IAmB1C1B,EADF,CAAAC,cAAA,cAA0D,eACpC;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,YAAoE;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IACvGF,EADuG,CAAAG,YAAA,EAAI,EACrG;;;;IADDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,UAAA,kBAAAf,MAAA,CAAAC,QAAA,CAAAoB,kBAAA,EAAA3B,EAAA,CAAAuB,aAAA,CAA6C;IAAoBvB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAoB,kBAAA,CAAiC;;;;;IAGrG3B,EADF,CAAAC,cAAA,cAA0D,eACpC;IAAAD,EAAA,CAAAE,MAAA,yGAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,YAAuE;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAC1GF,EAD0G,CAAAG,YAAA,EAAI,EACxG;;;;IADDH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAqB,UAAA,qBAAAf,MAAA,CAAAC,QAAA,CAAAqB,kBAAA,EAAA5B,EAAA,CAAAuB,aAAA,CAAgD;IAAoBvB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAqB,kBAAA,CAAiC;;;;;IAfxG5B,EAHN,CAAAC,cAAA,mBAA+D,sBAC5C,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,gFAAa;IAEvBF,EAFuB,CAAAG,YAAA,EAAO,EACX,EACD;IAGdH,EAFJ,CAAAC,cAAA,uBAAkB,cACM,eACA;IAAAD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;IAKNH,EAJA,CAAA6B,UAAA,KAAAC,0DAAA,kBAA0D,KAAAC,0DAAA,kBAIA;IAK9D/B,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAXeH,EAAA,CAAAI,SAAA,IAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAyB,iBAAA,CAAgC;IAE/BhC,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAoB,kBAAA,CAAiC;IAIjC3B,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAqB,kBAAA,CAAiC;;;;;IA+BtD5B,EADF,CAAAC,cAAA,cAAiD,eAC3B;IAAAD,EAAA,CAAAE,MAAA,iFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;;;;IADgBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAA0B,SAAA,CAAwB;;;;;IAG5CjC,EADF,CAAAC,cAAA,cAA0D,eACpC;IAAAD,EAAA,CAAAE,MAAA,iFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;;;;IADgBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAA2B,kBAAA,CAAiC;;;;;IAmBrDlC,EADF,CAAAC,cAAA,cAAyD,eACnC;IAAAD,EAAA,CAAAE,MAAA,+DAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;;;;IADgBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAA4B,iBAAA,CAAgC;;;;;IAXpDnC,EAHN,CAAAC,cAAA,mBAAsD,sBACnC,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,wGAAiB;IAE3BF,EAF2B,CAAAG,YAAA,EAAO,EACf,EACD;IAGdH,EAFJ,CAAAC,cAAA,uBAAkB,cACM,eACA;IAAAD,EAAA,CAAAE,MAAA,0DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IACNH,EAAA,CAAA6B,UAAA,KAAAO,2DAAA,kBAAyD;IAK7DpC,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAPeH,EAAA,CAAAI,SAAA,IAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAA8B,QAAA,CAAuB;IAEtBrC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAA4B,iBAAA,CAAgC;;;;;IAWrDnC,EAHN,CAAAC,cAAA,mBAA8D,sBAC3C,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAEjBF,EAFiB,CAAAG,YAAA,EAAO,EACL,EACD;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,YACM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAE9CF,EAF8C,CAAAG,YAAA,EAAI,EAC7B,EACV;;;;IAFeH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAA+B,KAAA,CAAoB;;;;;IAexCtC,EAHN,CAAAC,cAAA,mBAAwE,uBACpD,cACY,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,mBAAgC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC5DF,EAD4D,CAAAG,YAAA,EAAW,EACjE;IAGFH,EAFJ,CAAAC,cAAA,cAA6B,cACF,eACH;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAChD;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACH;IAAAD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACH;IAAAD,EAAA,CAAAE,MAAA,8CAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACH;IAAAD,EAAA,CAAAE,MAAA,wFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAItDF,EAJsD,CAAAG,YAAA,EAAO,EACjD,EACF,EACW,EACV;;;;;IAtBDH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAA/B,MAAA,CAAoB;IACQR,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAAC,YAAA,CAA0B;IAKpCxC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAAE,WAAA,CAAyB;IAIzBzC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAoC,cAAA,CAAAH,UAAA,CAAAI,SAAA,EAAuC;IAIvC3C,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAsC,UAAA,CAAAL,UAAA,CAAAM,aAAA,EAAuC;IAIvC7C,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAAO,YAAA,CAA0B;;;;;IAiB9C9C,EAJR,CAAAC,cAAA,mBAAoF,uBAChE,cACgB,cACA,SACxB;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IACxDF,EADwD,CAAAG,YAAA,EAAI,EACtD;IACNH,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAAgC,gBACX;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,oBAAkD;IAChDD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAW,EACP,EACW,EACV;;;;;IAdCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAA0C,cAAA,CAAAC,WAAA,CAA6B;IACZhD,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAA0C,cAAA,CAAAE,eAAA,CAAiC;IAExBjD,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAA4C,eAAA,CAAAH,cAAA,CAAAI,MAAA,EAA+C;IAC7EnD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAA9C,MAAA,CAAAoC,cAAA,CAAAK,cAAA,CAAAI,MAAA,OACF;IAGmBnD,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAsC,UAAA,CAAAG,cAAA,CAAAM,IAAA,EAAkC;IAC3CrD,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAqB,UAAA,YAAA0B,cAAA,CAAAO,eAAA,CAAuC;IAC/CtD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAAL,cAAA,CAAAO,eAAA,iGACF;;;;;IA7QNtD,EATV,CAAAC,cAAA,cAAoD,cAGtB,mBAGmB,uBACzB,cACO,eACX;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;IAEJH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,gFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,aAA+E;IAC7ED,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4C,wBACxB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAGzEF,EAHyE,CAAAG,YAAA,EAAI,EACnE,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4C,wBACxB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,kDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAGnEF,EAHmE,CAAAG,YAAA,EAAI,EAC7D,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA6C,wBACzB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAKhEF,EALgE,CAAAG,YAAA,EAAI,EACxD,EACW,EACV,EAEP;IAeQH,EAZd,CAAAC,cAAA,yBAA8D,mBAGxB,eACT,eAEA,oBAGO,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAExBF,EAFwB,CAAAG,YAAA,EAAO,EACZ,EACD;IAGdH,EAFJ,CAAAC,cAAA,wBAAkB,eACM,gBACA;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACjDF,EADiD,CAAAG,YAAA,EAAO,EAClD;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,wFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IACNH,EAAA,CAAA6B,UAAA,KAAA0B,8CAAA,kBAA8C;IAK5CvD,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,gEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IACNH,EAAA,CAAA6B,UAAA,KAAA2B,8CAAA,kBAA+C;IAKnDxD,EADE,CAAAG,YAAA,EAAmB,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAGdH,EAFJ,CAAAC,cAAA,wBAAkB,eACM,gBACA;IAAAD,EAAA,CAAAE,MAAA,4EAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAC/EF,EAD+E,CAAAG,YAAA,EAAI,EAC7E;IASNH,EARA,CAAA6B,UAAA,KAAA4B,8CAAA,kBAA8C,KAAAC,8CAAA,kBAID,KAAAC,8CAAA,kBAIE;IAKnD3D,EADE,CAAAG,YAAA,EAAmB,EACV;IAGXH,EAAA,CAAA6B,UAAA,KAAA+B,mDAAA,wBAA+D;IA2BzD5D,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yGAAiB;IAE3BF,EAF2B,CAAAG,YAAA,EAAO,EACf,EACD;IAGdH,EAFJ,CAAAC,cAAA,wBAAkB,eACM,gBACA;IAAAD,EAAA,CAAAE,MAAA,kFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,KACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsB,iBACA;IAAAD,EAAA,CAAAE,MAAA,yFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,KAA0C;IAChEF,EADgE,CAAAG,YAAA,EAAO,EACjE;IAEJH,EADF,CAAAC,cAAA,gBAAsB,iBACA;IAAAD,EAAA,CAAAE,MAAA,iEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,KAA+B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;IAKNH,EAJA,CAAA6B,UAAA,MAAAgC,+CAAA,kBAAiD,MAAAC,+CAAA,kBAIS;IAK9D9D,EADE,CAAAG,YAAA,EAAmB,EACV;IAuBXH,EApBA,CAAA6B,UAAA,MAAAkC,oDAAA,wBAAsD,MAAAC,oDAAA,wBAoBQ;IAcpEhE,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;IAKNH,EAFJ,CAAAC,cAAA,oBAA0B,gBACC,gBACI;IACzBD,EAAA,CAAA6B,UAAA,MAAAoC,oDAAA,wBAAwE;IA4B9EjE,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;IAKNH,EAFJ,CAAAC,cAAA,oBAAmC,gBACR,gBACQ;IAC7BD,EAAA,CAAA6B,UAAA,MAAAqC,oDAAA,wBAAoF;IAyB9FlE,EANQ,CAAAG,YAAA,EAAM,EACF,EACE,EAEI,EAEZ;;;;IAnR8BH,EAAA,CAAAI,SAAA,IAAoD;IAApDJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAA4C,eAAA,CAAA5C,MAAA,CAAAC,QAAA,CAAA4D,cAAA,EAAoD;IAC5EnE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAA9C,MAAA,CAAAoC,cAAA,CAAApC,MAAA,CAAAC,QAAA,CAAA4D,cAAA,OACF;IAayBnE,EAAA,CAAAI,SAAA,IAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAoC,cAAA,CAAApC,MAAA,CAAAC,QAAA,CAAA6D,WAAA,EAA0C;IAa3CpE,EAAA,CAAAI,SAAA,IAAqC;IAArCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAA+D,cAAA,CAAA/D,MAAA,CAAAC,QAAA,CAAA+D,MAAA,EAAqC;IAapCtE,EAAA,CAAAI,SAAA,IAA+B;IAA/BJ,EAAA,CAAAoD,kBAAA,KAAA9C,MAAA,CAAAC,QAAA,CAAAgE,YAAA,wBAA+B;IA2B9BvE,EAAA,CAAAI,SAAA,IAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAG,YAAA,CAA2B;IAI3BV,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAqB;IAEpBR,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAa,MAAA,CAAqB;IAMtBpB,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,mBAAA,CAAAL,MAAA,CAAAC,QAAA,CAAAK,cAAA,EAAkD;IAEjDZ,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAe,OAAA,CAAsB;IAkBxCtB,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,kBAAAf,MAAA,CAAAC,QAAA,CAAAiE,MAAA,EAAAxE,EAAA,CAAAuB,aAAA,CAAiC;IAAoBvB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAiE,MAAA,CAAqB;IAExDxE,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAiB,MAAA,CAAqB;IAIrBxB,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAkB,KAAA,CAAoB;IAIpBzB,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAmB,OAAA,CAAsB;IAQpB1B,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAAyB,iBAAA,CAAgC;IAkCnChC,EAAA,CAAAI,SAAA,IAAoD;IAApDJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAA4C,eAAA,CAAA5C,MAAA,CAAAC,QAAA,CAAA4D,cAAA,EAAoD;IACtEnE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoD,kBAAA,MAAA9C,MAAA,CAAAoC,cAAA,CAAApC,MAAA,CAAAC,QAAA,CAAA4D,cAAA,OACF;IAIoBnE,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAoC,cAAA,CAAApC,MAAA,CAAAC,QAAA,CAAA6D,WAAA,EAA0C;IAI1CpE,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAoD,kBAAA,KAAA9C,MAAA,CAAAC,QAAA,CAAAgE,YAAA,wBAA+B;IAE9BvE,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAA0B,SAAA,CAAwB;IAIxBjC,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAA2B,kBAAA,CAAiC;IAQ/BlC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAA8B,QAAA,CAAuB;IAoBZrC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,QAAA,CAAA+B,KAAA,CAAoB;IAoB9BtC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAmE,gBAAA,CAAmB;IAkCfzE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAoE,oBAAA,CAAuB;;;;;IA4BnE1E,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA2E,SAAA,sBAAyC;IACzC3E,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2IAA2B;IAChCF,EADgC,CAAAG,YAAA,EAAI,EAC9B;;;AD9OR,WAAayE,wBAAwB;EAA/B,MAAOA,wBAAwB;IAezBC,MAAA;IACAC,KAAA;IACAC,IAAA;IAfV;IACAC,SAAS,GAAG,KAAK;IACjBC,UAAU,GAAW,CAAC;IAEtB;IACA1E,QAAQ,GAA2B,IAAI;IACvCkE,gBAAgB,GAAsB,EAAE;IACxCC,oBAAoB,GAA0B,EAAE;IAEhD;IACQQ,aAAa,GAAmB,EAAE;IAE1CC,YACUN,MAAc,EACdC,KAAqB,EACrBC,IAAgB;MAFhB,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,IAAI,GAAJA,IAAI;IACX;IAEHK,QAAQA,CAAA;MACN,IAAI,CAACH,UAAU,GAAGI,MAAM,CAAC,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACP,UAAU,EAAE;QACnB,IAAI,CAACQ,mBAAmB,EAAE;QAC1B,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACC,wBAAwB,EAAE;MACjC,CAAC,MAAM;QACL,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACtC;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACX,aAAa,CAACY,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQP,mBAAmBA,CAAA;MACzB,IAAI,CAACT,SAAS,GAAG,IAAI;MAErB,MAAMe,GAAG,GAAG,IAAI,CAAChB,IAAI,CAACS,GAAG,CAAM,8CAA8C,IAAI,CAACP,UAAU,EAAE,CAAC,CAACgB,SAAS,CAAC;QACxGC,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,QAAQ,CAAC;UACvD;UACA,IAAIA,QAAQ,CAAC5F,QAAQ,EAAE;YACrB,IAAI,CAACA,QAAQ,GAAG;cACd+F,EAAE,EAAEH,QAAQ,CAAC5F,QAAQ,CAACgG,EAAE,IAAIJ,QAAQ,CAAC5F,QAAQ,CAAC+F,EAAE;cAChD5F,YAAY,EAAEyF,QAAQ,CAAC5F,QAAQ,CAACiG,YAAY,IAAIL,QAAQ,CAAC5F,QAAQ,CAACG,YAAY;cAC9EF,MAAM,EAAE2F,QAAQ,CAAC5F,QAAQ,CAACkG,MAAM,IAAIN,QAAQ,CAAC5F,QAAQ,CAACC,MAAM;cAC5DY,MAAM,EAAE+E,QAAQ,CAAC5F,QAAQ,CAACmG,MAAM,IAAIP,QAAQ,CAAC5F,QAAQ,CAACa,MAAM;cAC5DR,cAAc,EAAEuF,QAAQ,CAAC5F,QAAQ,CAACoG,cAAc,IAAIR,QAAQ,CAAC5F,QAAQ,CAACK,cAAc;cACpF4D,MAAM,EAAE2B,QAAQ,CAAC5F,QAAQ,CAACqG,KAAK,IAAIT,QAAQ,CAAC5F,QAAQ,CAACiE,MAAM,IAAI2B,QAAQ,CAAC5F,QAAQ,CAACsG,MAAM;cACvFrF,MAAM,EAAE2E,QAAQ,CAAC5F,QAAQ,CAACuG,MAAM,IAAIX,QAAQ,CAAC5F,QAAQ,CAACiB,MAAM;cAC5DC,KAAK,EAAE0E,QAAQ,CAAC5F,QAAQ,CAACwG,KAAK,IAAIZ,QAAQ,CAAC5F,QAAQ,CAACkB,KAAK;cACzDH,OAAO,EAAE6E,QAAQ,CAAC5F,QAAQ,CAACyG,OAAO,IAAIb,QAAQ,CAAC5F,QAAQ,CAACe,OAAO;cAC/DI,OAAO,EAAEyE,QAAQ,CAAC5F,QAAQ,CAAC0G,OAAO,IAAId,QAAQ,CAAC5F,QAAQ,CAACmB,OAAO;cAC/DM,iBAAiB,EAAEmE,QAAQ,CAAC5F,QAAQ,CAAC2G,iBAAiB,IAAIf,QAAQ,CAAC5F,QAAQ,CAACyB,iBAAiB;cAC7FL,kBAAkB,EAAEwE,QAAQ,CAAC5F,QAAQ,CAAC4G,kBAAkB,IAAIhB,QAAQ,CAAC5F,QAAQ,CAACoB,kBAAkB;cAChGC,kBAAkB,EAAEuE,QAAQ,CAAC5F,QAAQ,CAAC6G,kBAAkB,IAAIjB,QAAQ,CAAC5F,QAAQ,CAACqB,kBAAkB;cAChG2C,YAAY,EAAE4B,QAAQ,CAAC5F,QAAQ,CAAC8G,YAAY,IAAIlB,QAAQ,CAAC5F,QAAQ,CAACgE,YAAY,IAAI,EAAE;cACpFH,WAAW,EAAE+B,QAAQ,CAAC5F,QAAQ,CAAC+G,WAAW,IAAInB,QAAQ,CAAC5F,QAAQ,CAAC6D,WAAW,IAAI,CAAC;cAChFD,cAAc,EAAEgC,QAAQ,CAAC5F,QAAQ,CAACgH,cAAc,IAAIpB,QAAQ,CAAC5F,QAAQ,CAAC4D,cAAc,IAAI,CAAC;cACzFlC,SAAS,EAAEkE,QAAQ,CAAC5F,QAAQ,CAACiH,SAAS,IAAIrB,QAAQ,CAAC5F,QAAQ,CAAC0B,SAAS;cACrEC,kBAAkB,EAAEiE,QAAQ,CAAC5F,QAAQ,CAACkH,kBAAkB,IAAItB,QAAQ,CAAC5F,QAAQ,CAAC2B,kBAAkB;cAChGG,QAAQ,EAAE8D,QAAQ,CAAC5F,QAAQ,CAACmH,QAAQ,IAAIvB,QAAQ,CAAC5F,QAAQ,CAAC8B,QAAQ;cAClEF,iBAAiB,EAAEgE,QAAQ,CAAC5F,QAAQ,CAACoH,iBAAiB,IAAIxB,QAAQ,CAAC5F,QAAQ,CAAC4B,iBAAiB;cAC7FmC,MAAM,EAAE6B,QAAQ,CAAC5F,QAAQ,CAACqH,MAAM,IAAIzB,QAAQ,CAAC5F,QAAQ,CAAC+D,MAAM,IAAI,CAAC;cACjEhC,KAAK,EAAE6D,QAAQ,CAAC5F,QAAQ,CAACsH,KAAK,IAAI1B,QAAQ,CAAC5F,QAAQ,CAAC+B,KAAK;cACzDwF,QAAQ,EAAE3B,QAAQ,CAAC5F,QAAQ,CAACwH,QAAQ,KAAK,KAAK;cAC9CC,SAAS,EAAE7B,QAAQ,CAAC5F,QAAQ,CAAC0H,SAAS,IAAI9B,QAAQ,CAAC5F,QAAQ,CAACyH,SAAS,IAAI,IAAIE,IAAI,EAAE,CAACC,WAAW;aAChG;UACH;UACA,IAAI,CAACnD,SAAS,GAAG,KAAK;QACxB,CAAC;QACDoD,KAAK,EAAGA,KAAK,IAAI;UACfhC,OAAO,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAAC7H,QAAQ,GAAG,IAAI,CAAC8H,sBAAsB,EAAE;UAC7C,IAAI,CAACrD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MACF,IAAI,CAACE,aAAa,CAACoD,IAAI,CAACvC,GAAG,CAAC;IAC9B;IAEA;;;IAGQL,oBAAoBA,CAAA;MAC1B;MACA,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAAC8D,uBAAuB,EAAE;IACxD;IAEA;;;IAGQ5C,wBAAwBA,CAAA;MAC9B;MACA,IAAI,CAACjB,oBAAoB,GAAG,IAAI,CAAC8D,2BAA2B,EAAE;IAChE;IAEA;;;IAGArH,YAAYA,CAAA;MACV,IAAI,CAAC0D,MAAM,CAACe,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAACX,UAAU,CAAC,CAAC;IAC5D;IAEA;;;IAGAwD,MAAMA,CAAA;MACJ,IAAI,CAAC5D,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGAjF,mBAAmBA,CAAC+H,MAAe;MACjC,MAAMC,KAAK,GAA8B;QACvC,CAAC,EAAE,WAAW;QACd,CAAC,EAAE,WAAW;QACd,CAAC,EAAE;OACJ;MACD,OAAOA,KAAK,CAACD,MAAM,IAAI,CAAC,CAAC,IAAI,WAAW;IAC1C;IAEA;;;IAGAxF,eAAeA,CAAC0F,OAAe;MAC7B,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,OAAO,MAAM;IACf;IAEA;;;IAGAlG,cAAcA,CAACS,MAAc;MAC3B,OAAO,IAAI0F,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE;OACX,CAAC,CAACC,MAAM,CAAC9F,MAAM,CAAC;IACnB;IAEA;;;IAGAP,UAAUA,CAACS,IAAY;MACrB,OAAO,IAAI6E,IAAI,CAAC7E,IAAI,CAAC,CAAC6F,kBAAkB,CAAC,OAAO,CAAC;IACnD;IAEA;;;IAGA7E,cAAcA,CAACC,MAAe;MAC5B,MAAM6E,KAAK,GAAG7E,MAAM,IAAI,CAAC;MACzB,OAAO,GAAG,CAAC8E,MAAM,CAACC,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,CAAC,GAAG,GAAG,CAACC,MAAM,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,CAAC;IAC1E;IAEA;;;IAGQd,sBAAsBA,CAAA;MAC5B,OAAO;QACL/B,EAAE,EAAE,IAAI,CAACrB,UAAU;QACnBvE,YAAY,EAAE,QAAQ;QACtBF,MAAM,EAAE,sBAAsB;QAC9BY,MAAM,EAAE,0BAA0B;QAClCR,cAAc,EAAE,CAAC;QACjB4D,MAAM,EAAE,eAAe;QACvBhD,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,wBAAwB;QAC/BH,OAAO,EAAE,uBAAuB;QAChCI,OAAO,EAAE,yBAAyB;QAClCM,iBAAiB,EAAE,eAAe;QAClCL,kBAAkB,EAAE,eAAe;QACnCC,kBAAkB,EAAE,yBAAyB;QAC7C2C,YAAY,EAAE,EAAE;QAChBH,WAAW,EAAE,MAAM;QACnBD,cAAc,EAAE,CAAC,KAAK;QACtBlC,SAAS,EAAE,WAAW;QACtBC,kBAAkB,EAAE,UAAU;QAC9BG,QAAQ,EAAE,qBAAqB;QAC/BF,iBAAiB,EAAE,cAAc;QACjCmC,MAAM,EAAE,CAAC;QACThC,KAAK,EAAE,uCAAuC;QAC9CwF,QAAQ,EAAE,IAAI;QACdE,SAAS,EAAE,IAAIE,IAAI,EAAE,CAACC,WAAW;OAClC;IACH;IAEA;;;IAGQI,uBAAuBA,CAAA;MAC7B,OAAO,CACL;QACEjC,EAAE,EAAE,CAAC;QACL7D,WAAW,EAAE,QAAQ;QACrBjC,MAAM,EAAE,eAAe;QACvBY,MAAM,EAAE,uBAAuB;QAC/BoB,YAAY,EAAE,MAAM;QACpBG,SAAS,EAAE,KAAK;QAChBE,aAAa,EAAE,YAAY;QAC3BC,YAAY,EAAE;OACf,EACD;QACEwD,EAAE,EAAE,CAAC;QACL7D,WAAW,EAAE,QAAQ;QACrBjC,MAAM,EAAE,gBAAgB;QACxBY,MAAM,EAAE,eAAe;QACvBoB,YAAY,EAAE,MAAM;QACpBG,SAAS,EAAE,KAAK;QAChBE,aAAa,EAAE,YAAY;QAC3BC,YAAY,EAAE;OACf,EACD;QACEwD,EAAE,EAAE,CAAC;QACL7D,WAAW,EAAE,QAAQ;QACrBjC,MAAM,EAAE,UAAU;QAClBY,MAAM,EAAE,aAAa;QACrBoB,YAAY,EAAE,QAAQ;QACtBG,SAAS,EAAE,KAAK;QAChBE,aAAa,EAAE,YAAY;QAC3BC,YAAY,EAAE;OACf,CACF;IACH;IAEA;;;IAGQ0F,2BAA2BA,CAAA;MACjC,OAAO,CACL;QACElC,EAAE,EAAE,CAAC;QACLhD,eAAe,EAAE,UAAU;QAC3BH,MAAM,EAAE,CAAC,KAAK;QACdE,IAAI,EAAE,YAAY;QAClBL,WAAW,EAAE,wBAAwB;QACrCC,eAAe,EAAE;OAClB,EACD;QACEqD,EAAE,EAAE,CAAC;QACLhD,eAAe,EAAE,SAAS;QAC1BH,MAAM,EAAE,KAAK;QACbE,IAAI,EAAE,YAAY;QAClBL,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE;OAClB,EACD;QACEqD,EAAE,EAAE,CAAC;QACLhD,eAAe,EAAE,UAAU;QAC3BH,MAAM,EAAE,CAAC,KAAK;QACdE,IAAI,EAAE,YAAY;QAClBL,WAAW,EAAE,wBAAwB;QACrCC,eAAe,EAAE;OAClB,CACF;IACH;;uCApQW2B,wBAAwB,EAAA5E,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzJ,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA1J,EAAA,CAAAuJ,iBAAA,CAAAI,EAAA,CAAAC,UAAA;IAAA;;YAAxBhF,wBAAwB;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1E7BnK,EANR,CAAAC,cAAA,aAAwC,aAGb,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAa,UAAA,mBAAAwJ,0DAAA;YAAA,OAASD,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzDzI,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAA6B,UAAA,IAAAyI,uCAAA,iBAA0C;UAI5CtK,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA6B,UAAA,IAAA0I,uCAAA,iBAA6C;UAOjDvK,EADE,CAAAG,YAAA,EAAM,EACF;UAsSNH,EAnSA,CAAA6B,UAAA,IAAA2I,uCAAA,oBAAoD,KAAAC,wCAAA,iBAmSL;UAKjDzK,EAAA,CAAAG,YAAA,EAAM;;;UAvT4BH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAqB,UAAA,SAAA+I,GAAA,CAAA7J,QAAA,CAAc;UAKbP,EAAA,CAAAI,SAAA,EAAc;UAAdJ,EAAA,CAAAqB,UAAA,SAAA+I,GAAA,CAAA7J,QAAA,CAAc;UAUzBP,EAAA,CAAAI,SAAA,EAA4B;UAA5BJ,EAAA,CAAAqB,UAAA,UAAA+I,GAAA,CAAApF,SAAA,IAAAoF,GAAA,CAAA7J,QAAA,CAA4B;UAmSpBP,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAqB,UAAA,SAAA+I,GAAA,CAAApF,SAAA,CAAe;;;qBDxP3CzF,YAAY,EAAAmL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZrL,aAAa,EAAAsL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbzL,eAAe,EAAA0L,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3L,aAAa,EAAA4L,EAAA,CAAAC,OAAA,EACb5L,aAAa,EAAA6L,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACb9L,cAAc,EAAA+L,EAAA,CAAAC,OAAA,EACd/L,wBAAwB,EAAAgM,EAAA,CAAAC,kBAAA,EACxBhM,gBAAgB,EAChBC,aAAa;MAAAgM,MAAA;IAAA;;SAKJnH,wBAAwB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}