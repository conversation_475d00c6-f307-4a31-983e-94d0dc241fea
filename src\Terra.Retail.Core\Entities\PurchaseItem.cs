using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// أصناف فاتورة المشتريات
    /// </summary>
    public class PurchaseItem : BaseEntity
    {
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int PurchaseId { get; set; }

        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// رقم السطر في الفاتورة
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// الكمية المطلوبة
        /// </summary>
        public decimal OrderedQuantity { get; set; }

        /// <summary>
        /// الكمية المستلمة
        /// </summary>
        public decimal ReceivedQuantity { get; set; } = 0;

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// نسبة الخصم على الصنف (%)
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الخصم على الصنف
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// السعر بعد الخصم
        /// </summary>
        public decimal NetUnitPrice { get; set; }

        /// <summary>
        /// إجمالي السطر قبل الخصم
        /// </summary>
        public decimal LineTotal { get; set; }

        /// <summary>
        /// إجمالي السطر بعد الخصم
        /// </summary>
        public decimal NetLineTotal { get; set; }

        /// <summary>
        /// نسبة الضريبة على الصنف (%)
        /// </summary>
        public decimal TaxPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الضريبة على الصنف
        /// </summary>
        public decimal TaxAmount { get; set; } = 0;

        /// <summary>
        /// الإجمالي النهائي للسطر
        /// </summary>
        public decimal FinalTotal { get; set; }

        /// <summary>
        /// ملاحظات خاصة بالصنف
        /// </summary>
        [MaxLength(500)]
        public string? ItemNotes { get; set; }

        /// <summary>
        /// رقم التشغيل/الدفعة
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// تاريخ الإنتاج
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الرقم التسلسلي (للمنتجات المتسلسلة)
        /// </summary>
        [MaxLength(100)]
        public string? SerialNumber { get; set; }

        /// <summary>
        /// الوزن الفعلي (للمنتجات المشتراة بالوزن)
        /// </summary>
        public decimal? ActualWeight { get; set; }

        /// <summary>
        /// هل الصنف مستلم بالكامل
        /// </summary>
        public bool IsFullyReceived { get; set; } = false;

        /// <summary>
        /// هل الصنف مرتجع
        /// </summary>
        public bool IsReturned { get; set; } = false;

        /// <summary>
        /// الكمية المرتجعة
        /// </summary>
        public decimal ReturnedQuantity { get; set; } = 0;

        /// <summary>
        /// سبب الخصم (إن وجد)
        /// </summary>
        [MaxLength(200)]
        public string? DiscountReason { get; set; }

        /// <summary>
        /// تكلفة إضافية للصنف (شحن، تأمين، إلخ)
        /// </summary>
        public decimal AdditionalCost { get; set; } = 0;

        /// <summary>
        /// التكلفة النهائية للوحدة
        /// </summary>
        public decimal FinalUnitCost { get; set; }

        // Navigation Properties
        public virtual Purchase Purchase { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
        public virtual ICollection<PurchaseReturnItem> ReturnItems { get; set; } = new List<PurchaseReturnItem>();
    }
}
