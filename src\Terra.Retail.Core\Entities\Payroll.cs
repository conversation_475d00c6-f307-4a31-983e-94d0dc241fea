using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// كشوف الرواتب
    /// </summary>
    public class Payroll : BaseEntity
    {
        /// <summary>
        /// رقم كشف الراتب
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string PayrollNumber { get; set; } = string.Empty;

        /// <summary>
        /// الشهر
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// السنة
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// تاريخ بداية الفترة
        /// </summary>
        public DateTime PeriodStartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الفترة
        /// </summary>
        public DateTime PeriodEndDate { get; set; }

        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// حالة كشف الراتب
        /// </summary>
        public PayrollStatus Status { get; set; } = PayrollStatus.Draft;

        /// <summary>
        /// إجمالي الرواتب
        /// </summary>
        public decimal TotalSalaries { get; set; } = 0;

        /// <summary>
        /// إجمالي البدلات
        /// </summary>
        public decimal TotalAllowances { get; set; } = 0;

        /// <summary>
        /// إجمالي الإضافي
        /// </summary>
        public decimal TotalOvertime { get; set; } = 0;

        /// <summary>
        /// إجمالي الخصومات
        /// </summary>
        public decimal TotalDeductions { get; set; } = 0;

        /// <summary>
        /// صافي الرواتب
        /// </summary>
        public decimal NetSalaries { get; set; } = 0;

        /// <summary>
        /// عدد الموظفين
        /// </summary>
        public int EmployeeCount { get; set; } = 0;

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int? BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ الكشف
        /// </summary>
        public int PreparedById { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime PreparedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي اعتمد الكشف
        /// </summary>
        public int? ApprovedById { get; set; }

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Branch? Branch { get; set; }
        public virtual User PreparedBy { get; set; } = null!;
        public virtual User? ApprovedBy { get; set; }
        public virtual ICollection<PayrollItem> PayrollItems { get; set; } = new List<PayrollItem>();
    }

    /// <summary>
    /// بنود كشف الراتب
    /// </summary>
    public class PayrollItem : BaseEntity
    {
        /// <summary>
        /// معرف كشف الراتب
        /// </summary>
        public int PayrollId { get; set; }

        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        public decimal BasicSalary { get; set; } = 0;

        /// <summary>
        /// بدل السكن
        /// </summary>
        public decimal HousingAllowance { get; set; } = 0;

        /// <summary>
        /// بدل المواصلات
        /// </summary>
        public decimal TransportationAllowance { get; set; } = 0;

        /// <summary>
        /// بدلات أخرى
        /// </summary>
        public decimal OtherAllowances { get; set; } = 0;

        /// <summary>
        /// ساعات الإضافي
        /// </summary>
        public decimal OvertimeHours { get; set; } = 0;

        /// <summary>
        /// مبلغ الإضافي
        /// </summary>
        public decimal OvertimeAmount { get; set; } = 0;

        /// <summary>
        /// مكافآت
        /// </summary>
        public decimal Bonuses { get; set; } = 0;

        /// <summary>
        /// عمولات
        /// </summary>
        public decimal Commissions { get; set; } = 0;

        /// <summary>
        /// إجمالي الاستحقاقات
        /// </summary>
        public decimal TotalEarnings { get; set; } = 0;

        /// <summary>
        /// خصم التأمينات الاجتماعية
        /// </summary>
        public decimal SocialInsuranceDeduction { get; set; } = 0;

        /// <summary>
        /// خصم ضريبة الدخل
        /// </summary>
        public decimal IncomeTaxDeduction { get; set; } = 0;

        /// <summary>
        /// خصم الغياب
        /// </summary>
        public decimal AbsenceDeduction { get; set; } = 0;

        /// <summary>
        /// خصم التأخير
        /// </summary>
        public decimal LateDeduction { get; set; } = 0;

        /// <summary>
        /// خصم الإجازات غير مدفوعة الأجر
        /// </summary>
        public decimal UnpaidLeaveDeduction { get; set; } = 0;

        /// <summary>
        /// خصومات أخرى
        /// </summary>
        public decimal OtherDeductions { get; set; } = 0;

        /// <summary>
        /// إجمالي الخصومات
        /// </summary>
        public decimal TotalDeductions { get; set; } = 0;

        /// <summary>
        /// صافي الراتب
        /// </summary>
        public decimal NetSalary { get; set; } = 0;

        /// <summary>
        /// أيام العمل الفعلية
        /// </summary>
        public int WorkingDays { get; set; } = 0;

        /// <summary>
        /// أيام الغياب
        /// </summary>
        public int AbsentDays { get; set; } = 0;

        /// <summary>
        /// أيام الإجازات
        /// </summary>
        public int LeaveDays { get; set; } = 0;

        /// <summary>
        /// ساعات التأخير
        /// </summary>
        public decimal LateHours { get; set; } = 0;

        /// <summary>
        /// ملاحظات خاصة بالموظف
        /// </summary>
        [MaxLength(500)]
        public string? EmployeeNotes { get; set; }

        /// <summary>
        /// هل تم دفع الراتب
        /// </summary>
        public bool IsPaid { get; set; } = false;

        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime? PaidAt { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public int? PaymentMethodId { get; set; }

        /// <summary>
        /// رقم مرجعي للدفع
        /// </summary>
        [MaxLength(100)]
        public string? PaymentReference { get; set; }

        // Navigation Properties
        public virtual Payroll Payroll { get; set; } = null!;
        public virtual Employee Employee { get; set; } = null!;
        public virtual PaymentMethod? PaymentMethod { get; set; }
    }

    /// <summary>
    /// حالة كشف الراتب
    /// </summary>
    public enum PayrollStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,

        /// <summary>
        /// قيد المراجعة
        /// </summary>
        UnderReview = 2,

        /// <summary>
        /// معتمد
        /// </summary>
        Approved = 3,

        /// <summary>
        /// مدفوع جزئياً
        /// </summary>
        PartiallyPaid = 4,

        /// <summary>
        /// مدفوع بالكامل
        /// </summary>
        FullyPaid = 5,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 6
    }
}
