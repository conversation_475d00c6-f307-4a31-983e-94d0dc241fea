/* Terra Retail ERP - Supplier Management Styles */

.supplier-management-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-text {
    .page-title {
      font-size: 3rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-sm) 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .page-subtitle {
      font-size: 1.25rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 400;
    }
  }

  .header-actions {
    button {
      background: var(--warning-500) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-xl) !important;
      font-weight: 600 !important;
      box-shadow: var(--shadow-lg) !important;

      &:hover {
        background: var(--warning-600) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
      }
    }
  }
}

/* ===== STATISTICS SECTION ===== */
.statistics-section {
  margin-bottom: var(--spacing-3xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.stat-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-xl) !important;
  }

  .mat-mdc-card-content {
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-lg) !important;
    padding: var(--spacing-xl) !important;
  }

  .stat-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: white;
    }
  }

  .stat-info {
    flex: 1;

    h3 {
      font-size: 1rem;
      font-weight: 600;
      color: var(--gray-600);
      margin: 0 0 var(--spacing-xs) 0;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--gray-900);

      &.positive {
        color: var(--success-600);
      }

      &.negative {
        color: var(--error-600);
      }

      &.zero {
        color: var(--gray-600);
      }
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--gray-500);
      font-weight: 500;
    }
  }

  &.total-card .stat-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  }

  &.active-card .stat-icon {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
  }

  &.inactive-card .stat-icon {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  }

  &.balance-card .stat-icon {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
  }
}

/* ===== MANAGEMENT SECTIONS ===== */
.management-sections {
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--spacing-2xl) 0;
  text-align: center;
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}

.management-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    transform: translateY(-6px) !important;
    box-shadow: var(--shadow-2xl) !important;
  }

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-xl) !important;
    border-bottom: 1px solid var(--gray-200) !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-lg) !important;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: var(--radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: white;
      }

      &.primary-icon {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      }

      &.accent-icon {
        background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
      }

      &.warn-icon {
        background: linear-gradient(135deg, var(--error-500), var(--error-600));
      }
    }

    .mat-mdc-card-title {
      font-size: 1.5rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;
    }

    .mat-mdc-card-subtitle {
      font-size: 1rem !important;
      color: var(--gray-600) !important;
      margin: var(--spacing-xs) 0 0 0 !important;
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
  }

  .actions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .action-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-radius: var(--radius-lg);
      background: var(--gray-50);
      transition: all var(--transition-normal);
      cursor: pointer;

      &:hover {
        background: var(--gray-100);
        transform: translateX(var(--spacing-sm));
      }

      mat-icon {
        color: var(--primary-600);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      span {
        font-weight: 500;
        color: var(--gray-700);
      }
    }
  }

  .mat-mdc-card-actions {
    padding: var(--spacing-lg) var(--spacing-xl) !important;
    border-top: 1px solid var(--gray-200) !important;
    background: var(--gray-25) !important;

    button {
      font-weight: 600 !important;
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-sm) !important;
    }
  }

  &.primary-card {
    border-left: 4px solid var(--primary-500) !important;
  }

  &.accent-card {
    border-left: 4px solid var(--warning-500) !important;
  }

  &.warn-card {
    border-left: 4px solid var(--error-500) !important;
  }
}

/* ===== QUICK ACTIONS ===== */
.quick-actions-section {
  margin-bottom: var(--spacing-2xl);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.quick-action-card {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;
  cursor: pointer;
  text-align: center;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-lg) !important;
    border-color: var(--primary-300) !important;
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: var(--primary-600);
      margin-bottom: var(--spacing-md);
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--spacing-sm) 0;
    }

    p {
      font-size: 0.875rem;
      color: var(--gray-600);
      margin: 0;
      line-height: 1.5;
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: var(--primary-500);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .sections-grid {
    grid-template-columns: 1fr 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-text {
      .page-title {
        font-size: 2.5rem;
      }
    }
  }

  .sections-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-text {
      .page-title {
        font-size: 2rem;
      }

      .page-subtitle {
        font-size: 1rem;
      }
    }
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    .mat-mdc-card-content {
      flex-direction: column !important;
      text-align: center !important;
      gap: var(--spacing-md) !important;
    }
  }

  .management-card {
    .mat-mdc-card-header {
      flex-direction: column !important;
      text-align: center !important;
      gap: var(--spacing-md) !important;
    }
  }
}
