using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Core.Entities;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    /// <summary>
    /// تحكم في الفئات السعرية
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class PriceCategoriesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<PriceCategoriesController> _logger;

        public PriceCategoriesController(TerraRetailDbContext context, ILogger<PriceCategoriesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الفئات السعرية
        /// </summary>
        /// <returns>قائمة الفئات السعرية</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetPriceCategories()
        {
            try
            {
                var priceCategories = await _context.PriceCategories
                    .Where(pc => pc.IsActive && !pc.IsDeleted)
                    .OrderBy(pc => pc.CategoryNameAr)
                    .Select(pc => new
                    {
                        id = pc.Id,
                        categoryCode = pc.CategoryCode,
                        categoryNameAr = pc.CategoryNameAr,
                        categoryNameEn = pc.CategoryNameEn,
                        description = pc.Description,
                        discountPercentage = pc.DiscountPercentage,
                        isDefault = pc.IsDefault,
                        isActive = pc.IsActive,
                        createdAt = pc.CreatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع {Count} فئة سعرية", priceCategories.Count);
                return Ok(priceCategories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفئات السعرية");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على فئة سعرية محددة
        /// </summary>
        /// <param name="id">معرف الفئة السعرية</param>
        /// <returns>بيانات الفئة السعرية</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<PriceCategory>> GetPriceCategory(int id)
        {
            try
            {
                var priceCategory = await _context.PriceCategories
                    .FirstOrDefaultAsync(pc => pc.Id == id && pc.IsActive && !pc.IsDeleted);

                if (priceCategory == null)
                {
                    _logger.LogWarning("الفئة السعرية غير موجودة: {PriceCategoryId}", id);
                    return NotFound(new { message = "الفئة السعرية غير موجودة" });
                }

                _logger.LogInformation("تم استرجاع الفئة السعرية: {PriceCategoryName}", priceCategory.CategoryNameAr);
                return Ok(priceCategory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفئة السعرية {PriceCategoryId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء فئة سعرية جديدة
        /// </summary>
        /// <param name="priceCategory">بيانات الفئة السعرية</param>
        /// <returns>الفئة السعرية المنشأة</returns>
        [HttpPost]
        public async Task<ActionResult<PriceCategory>> CreatePriceCategory(PriceCategory priceCategory)
        {
            try
            {
                // التحقق من وجود كود الفئة السعرية
                var existingCategory = await _context.PriceCategories
                    .FirstOrDefaultAsync(pc => pc.CategoryCode == priceCategory.CategoryCode && !pc.IsDeleted);

                if (existingCategory != null)
                {
                    return BadRequest(new { message = "كود الفئة السعرية موجود بالفعل" });
                }

                // إنشاء كود تلقائي إذا لم يتم توفيره
                if (string.IsNullOrEmpty(priceCategory.CategoryCode))
                {
                    priceCategory.CategoryCode = await GeneratePriceCategoryCodeAsync();
                }

                priceCategory.CreatedAt = DateTime.UtcNow;
                priceCategory.CreatedBy = "System"; // يجب تحديثه ليأخذ من المستخدم الحالي
                _context.PriceCategories.Add(priceCategory);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء فئة سعرية جديدة: {PriceCategoryName} - {PriceCategoryCode}", 
                    priceCategory.CategoryNameAr, priceCategory.CategoryCode);

                return CreatedAtAction(nameof(GetPriceCategory), new { id = priceCategory.Id }, priceCategory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الفئة السعرية");
                return StatusCode(500, new { message = "حدث خطأ في إنشاء الفئة السعرية", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث فئة سعرية
        /// </summary>
        /// <param name="id">معرف الفئة السعرية</param>
        /// <param name="priceCategory">البيانات المحدثة</param>
        /// <returns>نتيجة التحديث</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePriceCategory(int id, PriceCategory priceCategory)
        {
            if (id != priceCategory.Id)
            {
                return BadRequest(new { message = "معرف الفئة السعرية غير متطابق" });
            }

            try
            {
                var existingCategory = await _context.PriceCategories.FindAsync(id);
                if (existingCategory == null || existingCategory.IsDeleted)
                {
                    return NotFound(new { message = "الفئة السعرية غير موجودة" });
                }

                existingCategory.CategoryNameAr = priceCategory.CategoryNameAr;
                existingCategory.CategoryNameEn = priceCategory.CategoryNameEn;
                existingCategory.Description = priceCategory.Description;
                existingCategory.DiscountPercentage = priceCategory.DiscountPercentage;
                existingCategory.IsDefault = priceCategory.IsDefault;
                existingCategory.IsActive = priceCategory.IsActive;
                existingCategory.UpdatedAt = DateTime.UtcNow;
                existingCategory.UpdatedBy = "System"; // يجب تحديثه ليأخذ من المستخدم الحالي

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الفئة السعرية: {PriceCategoryName}", existingCategory.CategoryNameAr);
                return Ok(new { message = "تم تحديث الفئة السعرية بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الفئة السعرية {PriceCategoryId}", id);
                return StatusCode(500, new { message = "حدث خطأ في تحديث الفئة السعرية", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف فئة سعرية (حذف آمن)
        /// </summary>
        /// <param name="id">معرف الفئة السعرية</param>
        /// <returns>نتيجة الحذف</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePriceCategory(int id)
        {
            try
            {
                var priceCategory = await _context.PriceCategories.FindAsync(id);
                if (priceCategory == null || priceCategory.IsDeleted)
                {
                    return NotFound(new { message = "الفئة السعرية غير موجودة" });
                }

                // التحقق من وجود عملاء أو منتجات مرتبطة بهذه الفئة
                var hasCustomers = await _context.Customers.AnyAsync(c => c.PriceCategoryId == id);
                var hasProductPrices = await _context.ProductPrices.AnyAsync(pp => pp.PriceCategoryId == id);

                if (hasCustomers || hasProductPrices)
                {
                    // حذف آمن - تعيين IsDeleted = true
                    priceCategory.IsDeleted = true;
                    priceCategory.IsActive = false;
                    priceCategory.UpdatedAt = DateTime.UtcNow;
                    priceCategory.UpdatedBy = "System";
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم الحذف الآمن للفئة السعرية: {PriceCategoryName}", priceCategory.CategoryNameAr);
                    return Ok(new { message = "تم حذف الفئة السعرية بنجاح" });
                }
                else
                {
                    // حذف فعلي إذا لم تكن مرتبطة بأي بيانات
                    _context.PriceCategories.Remove(priceCategory);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم الحذف الفعلي للفئة السعرية: {PriceCategoryName}", priceCategory.CategoryNameAr);
                    return Ok(new { message = "تم حذف الفئة السعرية بنجاح" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الفئة السعرية {PriceCategoryId}", id);
                return StatusCode(500, new { message = "حدث خطأ في حذف الفئة السعرية", error = ex.Message });
            }
        }

        /// <summary>
        /// توليد كود فئة سعرية تلقائي
        /// </summary>
        /// <returns>كود الفئة السعرية الجديد</returns>
        private async Task<string> GeneratePriceCategoryCodeAsync()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == "PriceCategoryCode");

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = "PriceCategoryCode",
                    Prefix = "PC",
                    CurrentValue = 1,
                    NumberLength = 3,
                    CreatedAt = DateTime.UtcNow
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }
    }
}
