using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Sales")]
    public class Sale
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string InvoiceNumber { get; set; } = string.Empty;

        public int? CustomerId { get; set; }

        [StringLength(200)]
        public string? CustomerName { get; set; }

        public int BranchId { get; set; }
        public int UserId { get; set; }

        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public DateTime? DueDate { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled, 3=Returned
        public int SaleType { get; set; } = 1; // 1=Cash, 2=Credit, 3=Mixed

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(2000)]
        public string? Notes { get; set; }

        [StringLength(2000)]
        public string? InternalNotes { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        public int? CancelledById { get; set; }
        public DateTime? CancelledAt { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        [StringLength(40)]
        public string? TableNumber { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CancelledById")]
        public virtual User? CancelledBy { get; set; }

        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<SalePayment> SalePayments { get; set; } = new List<SalePayment>();
    }

    [Table("SaleItems")]
    public class SaleItem
    {
        [Key]
        public int Id { get; set; }

        public int SaleId { get; set; }
        public int ProductId { get; set; }

        public int LineNumber { get; set; } = 1;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetUnitPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetLineTotal { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal FinalTotal { get; set; } = 0;

        public int? PriceCategoryId { get; set; }

        [StringLength(1000)]
        public string? ItemNotes { get; set; }

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ActualWeight { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReturnedQuantity { get; set; } = 0;

        [StringLength(400)]
        public string? DiscountReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("PriceCategoryId")]
        public virtual PriceCategory? PriceCategory { get; set; }
    }

    [Table("SalePayments")]
    public class SalePayment
    {
        [Key]
        public int Id { get; set; }

        public int SaleId { get; set; }

        [Required]
        [StringLength(40)]
        public string PaymentNumber { get; set; } = string.Empty;

        public int PaymentMethodId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ReceivedAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ChangeAmount { get; set; } = 0;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public int Status { get; set; } = 1; // 1=Completed, 2=Pending, 3=Failed

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int UserId { get; set; }

        public int? ConfirmedById { get; set; }
        public DateTime? ConfirmedAt { get; set; }

        [StringLength(40)]
        public string? ReceiptNumber { get; set; }

        [StringLength(200)]
        public string? ExternalTransactionId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TransactionFee { get; set; } = 0;

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public int? CashBoxId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ConfirmedById")]
        public virtual User? ConfirmedBy { get; set; }
    }
}
