-- تحويل البيانات للمصرية
-- Convert Data to Egyptian

USE TerraRetailERP;
GO

PRINT N'تحويل البيانات للمصرية...';

-- تحديث الفروع المصرية
DELETE FROM UserBranches;
DELETE FROM Branches WHERE Code != 'MAIN';

UPDATE Branches SET 
    NameAr = N'الفرع الرئيسي - القاهرة',
    NameEn = N'Main Branch - Cairo',
    Address = N'شارع التحرير، وسط البلد، القاهرة',
    Phone = '02-25555555'
WHERE Code = 'MAIN';

-- إضافة فروع مصرية
INSERT INTO Branches (NameAr, NameEn, Code, IsActive, IsMainBranch, Address, Phone, CreatedAt) VALUES
(N'فرع الإسكندرية', N'Alexandria Branch', 'ALEX', 1, 0, N'شارع الكورنيش، الإسكندرية', '03-4555555', GETUTCDATE()),
(N'فرع الجيزة', N'Giza Branch', 'GIZA', 1, 0, N'شارع الهرم، الجيزة', '02-35555555', GETUTCDATE()),
(N'فرع المنصورة', N'Mansoura Branch', 'MANS', 1, 0, N'شارع الجمهورية، المنصورة', '050-2555555', GETUTCDATE()),
(N'فرع أسيوط', N'Assiut Branch', 'ASST', 1, 0, N'شارع الثورة، أسيوط', '088-2555555', GETUTCDATE()),
(N'فرع طنطا', N'Tanta Branch', 'TANT', 1, 0, N'شارع الجلاء، طنطا', '040-3555555', GETUTCDATE()),
(N'فرع الزقازيق', N'Zagazig Branch', 'ZAG', 1, 0, N'شارع الجامعة، الزقازيق', '055-2555555', GETUTCDATE()),
(N'فرع أسوان', N'Aswan Branch', 'ASW', 1, 0, N'شارع النيل، أسوان', '097-2555555', GETUTCDATE());

-- تحديث المناطق المصرية
DELETE FROM Areas;

INSERT INTO Areas (NameAr, NameEn, Code, IsActive, DisplayOrder, CreatedAt) VALUES
(N'القاهرة', N'Cairo', 'CAI', 1, 1, GETUTCDATE()),
(N'الجيزة', N'Giza', 'GIZ', 1, 2, GETUTCDATE()),
(N'الإسكندرية', N'Alexandria', 'ALX', 1, 3, GETUTCDATE()),
(N'الدقهلية', N'Dakahlia', 'DAK', 1, 4, GETUTCDATE()),
(N'الشرقية', N'Sharqia', 'SHR', 1, 5, GETUTCDATE()),
(N'القليوبية', N'Qalyubia', 'QLY', 1, 6, GETUTCDATE()),
(N'كفر الشيخ', N'Kafr El Sheikh', 'KFS', 1, 7, GETUTCDATE()),
(N'الغربية', N'Gharbia', 'GHR', 1, 8, GETUTCDATE()),
(N'المنوفية', N'Monufia', 'MNF', 1, 9, GETUTCDATE()),
(N'البحيرة', N'Beheira', 'BHR', 1, 10, GETUTCDATE()),
(N'الإسماعيلية', N'Ismailia', 'ISM', 1, 11, GETUTCDATE()),
(N'بورسعيد', N'Port Said', 'PTS', 1, 12, GETUTCDATE()),
(N'السويس', N'Suez', 'SUZ', 1, 13, GETUTCDATE()),
(N'شمال سيناء', N'North Sinai', 'NSI', 1, 14, GETUTCDATE()),
(N'جنوب سيناء', N'South Sinai', 'SSI', 1, 15, GETUTCDATE()),
(N'الفيوم', N'Fayoum', 'FYM', 1, 16, GETUTCDATE()),
(N'بني سويف', N'Beni Suef', 'BSF', 1, 17, GETUTCDATE()),
(N'المنيا', N'Minya', 'MNY', 1, 18, GETUTCDATE()),
(N'أسيوط', N'Assiut', 'AST', 1, 19, GETUTCDATE()),
(N'سوهاج', N'Sohag', 'SOH', 1, 20, GETUTCDATE()),
(N'قنا', N'Qena', 'QNA', 1, 21, GETUTCDATE()),
(N'الأقصر', N'Luxor', 'LXR', 1, 22, GETUTCDATE()),
(N'أسوان', N'Aswan', 'ASN', 1, 23, GETUTCDATE()),
(N'البحر الأحمر', N'Red Sea', 'RDS', 1, 24, GETUTCDATE()),
(N'الوادي الجديد', N'New Valley', 'NVL', 1, 25, GETUTCDATE()),
(N'مطروح', N'Matrouh', 'MTR', 1, 26, GETUTCDATE());

-- تحديث طرق الدفع المصرية
UPDATE PaymentMethods SET NameAr = N'كاش' WHERE Code = 'CASH';
UPDATE PaymentMethods SET NameAr = N'فيزا' WHERE Code = 'CREDIT';
UPDATE PaymentMethods SET NameAr = N'آجل' WHERE Code = 'TERM';

-- إضافة طرق دفع مصرية
IF NOT EXISTS (SELECT * FROM PaymentMethods WHERE Code = 'MEEZA')
BEGIN
    INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsActive, DisplayOrder, CreatedAt) VALUES
    (N'ميزة', N'Meeza Card', 'MEEZA', 2, 1, 4, GETUTCDATE()),
    (N'فودافون كاش', N'Vodafone Cash', 'VFCASH', 2, 1, 5, GETUTCDATE()),
    (N'أورانج موني', N'Orange Money', 'ORANGE', 2, 1, 6, GETUTCDATE()),
    (N'إتصالات فلوس', N'Etisalat Flous', 'ETISALAT', 2, 1, 7, GETUTCDATE()),
    (N'فوري', N'Fawry', 'FAWRY', 2, 1, 8, GETUTCDATE()),
    (N'تحويل بنكي', N'Bank Transfer', 'TRANSFER', 3, 1, 9, GETUTCDATE()),
    (N'شيك', N'Check', 'CHECK', 5, 1, 10, GETUTCDATE());
END

-- تحديث فئات المنتجات المصرية
UPDATE Categories SET NameAr = N'عام' WHERE Id = 1;
UPDATE Categories SET NameAr = N'مواد غذائية ومشروبات' WHERE Id = 2;
UPDATE Categories SET NameAr = N'أجهزة كهربائية' WHERE Id = 3;
UPDATE Categories SET NameAr = N'ملابس وأحذية' WHERE Id = 4;
UPDATE Categories SET NameAr = N'أدوات منزلية' WHERE Id = 5;
UPDATE Categories SET NameAr = N'مستحضرات تجميل وعناية' WHERE Id = 6;
UPDATE Categories SET NameAr = N'رياضة ولياقة' WHERE Id = 7;
UPDATE Categories SET NameAr = N'مكتبة وقرطاسية' WHERE Id = 8;
UPDATE Categories SET NameAr = N'ألعاب وهدايا' WHERE Id = 9;
UPDATE Categories SET NameAr = N'قطع غيار وإكسسوارات' WHERE Id = 10;

-- تحديث أنواع العملاء المصرية
UPDATE CustomerTypes SET NameAr = N'عميل عادي' WHERE Id = 1;
UPDATE CustomerTypes SET NameAr = N'عميل جملة' WHERE Id = 2;
UPDATE CustomerTypes SET NameAr = N'عميل VIP' WHERE Id = 3;
UPDATE CustomerTypes SET NameAr = N'عميل تاجر' WHERE Id = 4;
UPDATE CustomerTypes SET NameAr = N'عميل مؤسسة' WHERE Id = 5;

-- تحديث وحدات القياس
UPDATE Units SET NameAr = N'حبة' WHERE Id = 1;
UPDATE Units SET NameAr = N'كيلو' WHERE Id = 2;
UPDATE Units SET NameAr = N'جرام' WHERE Id = 3;
UPDATE Units SET NameAr = N'متر' WHERE Id = 4;
UPDATE Units SET NameAr = N'سم' WHERE Id = 5;
UPDATE Units SET NameAr = N'لتر' WHERE Id = 6;
UPDATE Units SET NameAr = N'مل' WHERE Id = 7;
UPDATE Units SET NameAr = N'علبة' WHERE Id = 8;
UPDATE Units SET NameAr = N'كرتونة' WHERE Id = 9;
UPDATE Units SET NameAr = N'دستة' WHERE Id = 10;

-- تحديث فئات الأسعار
UPDATE PriceCategories SET NameAr = N'سعر التجزئة' WHERE Id = 1;
UPDATE PriceCategories SET NameAr = N'سعر الجملة' WHERE Id = 2;
UPDATE PriceCategories SET NameAr = N'سعر VIP' WHERE Id = 3;
UPDATE PriceCategories SET NameAr = N'سعر التجار' WHERE Id = 4;
UPDATE PriceCategories SET NameAr = N'سعر الموظفين' WHERE Id = 5;

-- تحديث أنواع الموردين
UPDATE SupplierTypes SET NameAr = N'مورد محلي' WHERE Id = 1;
UPDATE SupplierTypes SET NameAr = N'مورد مستورد' WHERE Id = 2;
UPDATE SupplierTypes SET NameAr = N'مورد حكومي' WHERE Id = 3;

-- تحديث العدادات
UPDATE Counters SET Description = N'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = N'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = N'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = N'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = N'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = N'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

-- إضافة المستخدم للفروع الجديدة
DECLARE @AdminUserId int = (SELECT Id FROM Users WHERE Username = 'admin');
IF @AdminUserId IS NOT NULL
BEGIN
    INSERT INTO UserBranches (UserId, BranchId, HasAccess, GrantedAt)
    SELECT @AdminUserId, Id, 1, GETUTCDATE() FROM Branches WHERE IsActive = 1;
END

PRINT N'تم تحويل جميع البيانات للمصرية بنجاح!';

-- عرض النتائج
SELECT N'الفروع المصرية' as TableName, COUNT(*) as RecordCount FROM Branches
UNION ALL
SELECT N'المحافظات المصرية', COUNT(*) FROM Areas
UNION ALL
SELECT N'طرق الدفع المصرية', COUNT(*) FROM PaymentMethods;
