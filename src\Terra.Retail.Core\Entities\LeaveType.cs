using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// أنواع الإجازات
    /// </summary>
    public class LeaveType : BaseEntity
    {
        /// <summary>
        /// اسم نوع الإجازة بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الإجازة بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود نوع الإجازة
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// وصف نوع الإجازة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// الحد الأقصى للأيام في السنة
        /// </summary>
        public int MaxDaysPerYear { get; set; } = 0;

        /// <summary>
        /// الحد الأدنى للأيام في الطلب الواحد
        /// </summary>
        public int MinDaysPerRequest { get; set; } = 1;

        /// <summary>
        /// الحد الأقصى للأيام في الطلب الواحد
        /// </summary>
        public int MaxDaysPerRequest { get; set; } = 30;

        /// <summary>
        /// هل الإجازة مدفوعة الأجر
        /// </summary>
        public bool IsPaid { get; set; } = true;

        /// <summary>
        /// هل تتطلب موافقة
        /// </summary>
        public bool RequiresApproval { get; set; } = true;

        /// <summary>
        /// عدد أيام الإشعار المسبق المطلوبة
        /// </summary>
        public int RequiredNoticeDays { get; set; } = 0;

        /// <summary>
        /// هل يمكن ترحيل الرصيد للسنة التالية
        /// </summary>
        public bool CanCarryForward { get; set; } = false;

        /// <summary>
        /// الحد الأقصى للترحيل
        /// </summary>
        public int MaxCarryForwardDays { get; set; } = 0;

        /// <summary>
        /// هل الإجازة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// لون نوع الإجازة (للعرض في التقويم)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة نوع الإجازة
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// هل تحتاج مستندات مرفقة
        /// </summary>
        public bool RequiresDocuments { get; set; } = false;

        /// <summary>
        /// أنواع المستندات المطلوبة (JSON)
        /// </summary>
        [MaxLength(500)]
        public string? RequiredDocuments { get; set; }

        /// <summary>
        /// الجنس المسموح له بهذا النوع
        /// </summary>
        public Gender? AllowedGender { get; set; }

        /// <summary>
        /// الحد الأدنى لسنوات الخدمة
        /// </summary>
        public int MinServiceYears { get; set; } = 0;

        /// <summary>
        /// هل يمكن أخذها في أيام العطل
        /// </summary>
        public bool CanTakeOnHolidays { get; set; } = false;

        /// <summary>
        /// هل تؤثر على الراتب
        /// </summary>
        public bool AffectsSalary { get; set; } = false;

        /// <summary>
        /// نسبة تأثير الراتب (%)
        /// </summary>
        public decimal SalaryImpactPercentage { get; set; } = 0;

        // Navigation Properties
        public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = new List<EmployeeLeave>();
        public virtual ICollection<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; } = new List<EmployeeLeaveBalance>();
    }
}
