import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatGridListModule } from '@angular/material/grid-list';

@Component({
  selector: 'app-supplier-management',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatGridListModule
  ],
  templateUrl: './supplier-management.component.html',
  styleUrls: ['./supplier-management.component.scss']
})
export class SupplierManagementComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  
  // Statistics
  stats = {
    totalSuppliers: 0,
    activeSuppliers: 0,
    inactiveSuppliers: 0,
    totalBalance: 0,
    positiveBalance: 0,
    negativeBalance: 0
  };

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Management Sections
  managementSections = [
    {
      title: 'إدارة الموردين',
      description: 'عرض وإدارة جميع الموردين',
      icon: 'people',
      route: '/suppliers',
      color: 'primary',
      actions: [
        { title: 'عرض الموردين', route: '/suppliers', icon: 'list' },
        { title: 'إضافة مورد', route: '/suppliers/add', icon: 'add' }
      ]
    },
    {
      title: 'تقييم الموردين',
      description: 'تقييم أداء الموردين وجودة الخدمة',
      icon: 'star_rate',
      route: '/suppliers/evaluation',
      color: 'accent',
      actions: [
        { title: 'تقييم الموردين', route: '/suppliers/evaluation', icon: 'assessment' },
        { title: 'تقارير التقييم', route: '/suppliers/evaluation-reports', icon: 'analytics' }
      ]
    },
    {
      title: 'عقود الموردين',
      description: 'إدارة العقود والاتفاقيات',
      icon: 'description',
      route: '/suppliers/contracts',
      color: 'warn',
      actions: [
        { title: 'العقود النشطة', route: '/suppliers/contracts/active', icon: 'assignment' },
        { title: 'إضافة عقد', route: '/suppliers/contracts/add', icon: 'add_box' }
      ]
    },
    {
      title: 'المدفوعات للموردين',
      description: 'إدارة المدفوعات والمستحقات',
      icon: 'payment',
      route: '/suppliers/payments',
      color: 'primary',
      actions: [
        { title: 'المدفوعات', route: '/suppliers/payments', icon: 'account_balance' },
        { title: 'إضافة دفعة', route: '/suppliers/payments/add', icon: 'add_card' }
      ]
    },
    {
      title: 'طلبات الشراء',
      description: 'إدارة طلبات الشراء من الموردين',
      icon: 'shopping_cart',
      route: '/suppliers/purchase-orders',
      color: 'accent',
      actions: [
        { title: 'طلبات الشراء', route: '/suppliers/purchase-orders', icon: 'shopping_basket' },
        { title: 'طلب جديد', route: '/suppliers/purchase-orders/add', icon: 'add_shopping_cart' }
      ]
    },
    {
      title: 'تقارير الموردين',
      description: 'تقارير شاملة عن أداء الموردين',
      icon: 'analytics',
      route: '/suppliers/reports',
      color: 'warn',
      actions: [
        { title: 'تقارير الأداء', route: '/suppliers/reports/performance', icon: 'trending_up' },
        { title: 'تقارير مالية', route: '/suppliers/reports/financial', icon: 'account_balance_wallet' }
      ]
    }
  ];

  constructor(
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loadStatistics();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load supplier statistics
   */
  private loadStatistics(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>('http://localhost:5127/api/simple/suppliers').subscribe({
      next: (response) => {
        const suppliers = response.suppliers || [];
        this.calculateStatistics(suppliers);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
        this.stats = this.getMockStatistics();
        this.isLoading = false;
      }
    });
    
    this.subscriptions.push(sub);
  }

  /**
   * Calculate statistics from suppliers data
   */
  private calculateStatistics(suppliers: any[]): void {
    this.stats.totalSuppliers = suppliers.length;
    this.stats.activeSuppliers = suppliers.filter(s => s.IsActive !== false).length;
    this.stats.inactiveSuppliers = this.stats.totalSuppliers - this.stats.activeSuppliers;
    
    const balances = suppliers.map(s => s.CurrentBalance || 0);
    this.stats.totalBalance = balances.reduce((sum, balance) => sum + balance, 0);
    this.stats.positiveBalance = balances.filter(b => b > 0).reduce((sum, balance) => sum + balance, 0);
    this.stats.negativeBalance = balances.filter(b => b < 0).reduce((sum, balance) => sum + balance, 0);
  }

  /**
   * Navigate to section
   */
  navigateToSection(route: string): void {
    if (route.includes('/suppliers/')) {
      // For now, redirect unimplemented routes to main suppliers page
      if (route === '/suppliers' || route === '/suppliers/add') {
        this.router.navigate([route]);
      } else {
        alert(`هذا القسم قيد التطوير: ${route}`);
      }
    } else {
      this.router.navigate([route]);
    }
  }

  /**
   * Navigate to action
   */
  navigateToAction(route: string): void {
    this.navigateToSection(route);
  }

  /**
   * Format currency
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP'
    }).format(amount);
  }

  /**
   * Get balance class for styling
   */
  getBalanceClass(balance: number): string {
    if (balance > 0) return 'positive';
    if (balance < 0) return 'negative';
    return 'zero';
  }

  /**
   * Get mock statistics
   */
  private getMockStatistics(): any {
    return {
      totalSuppliers: 10,
      activeSuppliers: 8,
      inactiveSuppliers: 2,
      totalBalance: -50000,
      positiveBalance: 200000,
      negativeBalance: -250000
    };
  }
}
