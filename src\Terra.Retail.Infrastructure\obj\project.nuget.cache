{"version": 2, "dgSpecHash": "N2uswc0IyFk=", "success": true, "projectFilePath": "X:\\barmaga\\Angler\\Erp 2\\src\\Terra.Retail.Infrastructure\\Terra.Retail.Infrastructure.csproj", "expectedPackageFiles": ["X:\\NugetCache\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "X:\\NugetCache\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "X:\\NugetCache\\azure.core\\1.25.0\\azure.core.1.25.0.nupkg.sha512", "X:\\NugetCache\\azure.identity\\1.7.0\\azure.identity.1.7.0.nupkg.sha512", "X:\\NugetCache\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "X:\\NugetCache\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.analyzers\\3.3.3\\microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.common\\4.5.0\\microsoft.codeanalysis.common.4.5.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.csharp\\4.5.0\\microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.csharp.workspaces\\4.5.0\\microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512", "X:\\NugetCache\\microsoft.codeanalysis.workspaces.common\\4.5.0\\microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512", "X:\\NugetCache\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "X:\\NugetCache\\microsoft.data.sqlclient\\5.1.1\\microsoft.data.sqlclient.5.1.1.nupkg.sha512", "X:\\NugetCache\\microsoft.data.sqlclient.sni.runtime\\5.1.0\\microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore\\8.0.0\\microsoft.entityframeworkcore.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.abstractions\\8.0.0\\microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.analyzers\\8.0.0\\microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.design\\8.0.0\\microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.relational\\8.0.0\\microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.sqlserver\\8.0.0\\microsoft.entityframeworkcore.sqlserver.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.entityframeworkcore.tools\\8.0.0\\microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.caching.memory\\8.0.0\\microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.dependencymodel\\8.0.0\\microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identity.client\\4.47.2\\microsoft.identity.client.4.47.2.nupkg.sha512", "X:\\NugetCache\\microsoft.identity.client.extensions.msal\\2.19.3\\microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.abstractions\\6.24.0\\microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.jsonwebtokens\\6.24.0\\microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.logging\\6.24.0\\microsoft.identitymodel.logging.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.protocols\\6.24.0\\microsoft.identitymodel.protocols.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.protocols.openidconnect\\6.24.0\\microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.identitymodel.tokens\\6.24.0\\microsoft.identitymodel.tokens.6.24.0.nupkg.sha512", "X:\\NugetCache\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "X:\\NugetCache\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "X:\\NugetCache\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "X:\\NugetCache\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "X:\\NugetCache\\mono.texttemplating\\2.2.1\\mono.texttemplating.2.2.1.nupkg.sha512", "X:\\NugetCache\\system.codedom\\4.4.0\\system.codedom.4.4.0.nupkg.sha512", "X:\\NugetCache\\system.collections.immutable\\6.0.0\\system.collections.immutable.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition\\6.0.0\\system.composition.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.attributedmodel\\6.0.0\\system.composition.attributedmodel.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.convention\\6.0.0\\system.composition.convention.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.hosting\\6.0.0\\system.composition.hosting.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.runtime\\6.0.0\\system.composition.runtime.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.composition.typedparts\\6.0.0\\system.composition.typedparts.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "X:\\NugetCache\\system.diagnostics.diagnosticsource\\6.0.0\\system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.formats.asn1\\5.0.0\\system.formats.asn1.5.0.0.nupkg.sha512", "X:\\NugetCache\\system.identitymodel.tokens.jwt\\6.24.0\\system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512", "X:\\NugetCache\\system.io.pipelines\\6.0.3\\system.io.pipelines.6.0.3.nupkg.sha512", "X:\\NugetCache\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "X:\\NugetCache\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "X:\\NugetCache\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "X:\\NugetCache\\system.reflection.metadata\\6.0.1\\system.reflection.metadata.6.0.1.nupkg.sha512", "X:\\NugetCache\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "X:\\NugetCache\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "X:\\NugetCache\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "X:\\NugetCache\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "X:\\NugetCache\\system.text.json\\8.0.0\\system.text.json.8.0.0.nupkg.sha512", "X:\\NugetCache\\system.threading.channels\\6.0.0\\system.threading.channels.6.0.0.nupkg.sha512", "X:\\NugetCache\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "X:\\NugetCache\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": []}