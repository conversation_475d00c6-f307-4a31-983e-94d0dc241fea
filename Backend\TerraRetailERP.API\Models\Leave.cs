using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("LeaveTypes")]
    public class LeaveType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        public int MaxDaysPerYear { get; set; }
        public bool IsPaid { get; set; } = true;
        public bool RequireApproval { get; set; } = true;
        public int MinAdvanceNoticeDays { get; set; } = 0;
        public bool CanCarryForward { get; set; } = false;

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = new List<EmployeeLeave>();
        public virtual ICollection<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; } = new List<EmployeeLeaveBalance>();
    }

    [Table("EmployeeLeaves")]
    public class EmployeeLeave
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int LeaveTypeId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalDays { get; set; }

        [Required]
        [StringLength(500)]
        public string Reason { get; set; } = string.Empty;

        [StringLength(20)]
        public string LeaveStatus { get; set; } = "Pending"; // Pending, Approved, Rejected, Cancelled

        public int? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }

        [StringLength(500)]
        public string? ApprovalNotes { get; set; }

        public DateTime RequestDate { get; set; } = DateTime.Now;
        public int RequestedBy { get; set; }

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("LeaveTypeId")]
        public virtual LeaveType LeaveType { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        [ForeignKey("RequestedBy")]
        public virtual User RequestedByUser { get; set; } = null!;
    }

    [Table("EmployeeLeaveBalances")]
    public class EmployeeLeaveBalance
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int LeaveTypeId { get; set; }
        public int Year { get; set; }
        public int EntitledDays { get; set; }
        public int UsedDays { get; set; } = 0;
        public int CarriedForwardDays { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Computed Property
        public int RemainingDays => EntitledDays + CarriedForwardDays - UsedDays;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("LeaveTypeId")]
        public virtual LeaveType LeaveType { get; set; } = null!;
    }
}
