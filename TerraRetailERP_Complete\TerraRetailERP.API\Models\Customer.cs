using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Customers")]
    public class Customer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int CustomerTypeId { get; set; }

        [StringLength(40)]
        public string? Phone1 { get; set; }

        [StringLength(40)]
        public string? Phone2 { get; set; }

        [StringLength(200)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(1000)]
        public string? Address { get; set; }

        public int? AreaId { get; set; }
        public int BranchId { get; set; }
        public int? PriceCategoryId { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;

        [StringLength(100)]
        public string? IdentityNumber { get; set; }

        [StringLength(100)]
        public string? TaxNumber { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        public int? ChartAccountId { get; set; }

        // Navigation Properties
        [ForeignKey("CustomerTypeId")]
        public virtual CustomerType CustomerType { get; set; } = null!;

        [ForeignKey("AreaId")]
        public virtual Area? Area { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("PriceCategoryId")]
        public virtual PriceCategory? PriceCategory { get; set; }

        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount? ChartAccount { get; set; }

        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Receipt> Receipts { get; set; } = new List<Receipt>();
        public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
    }

    [Table("CustomerTypes")]
    public class CustomerType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(400)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal DefaultDiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DefaultCreditLimit { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        [StringLength(14)]
        public string? Color { get; set; }

        public int DisplayOrder { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }
}
