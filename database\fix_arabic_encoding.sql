-- إصلاح ترميز النصوص العربية في قاعدة البيانات
-- Fix Arabic Text Encoding in Database

USE TerraRetailERP;
GO

PRINT N'بدء إصلاح ترميز النصوص العربية...';
PRINT 'Starting Arabic text encoding fix...';

-- حذف البيانات الخاطئة وإعادة إدراجها بالترميز الصحيح
-- Delete incorrect data and re-insert with correct encoding

-- إصلاح وحدات القياس
DELETE FROM Units;
DBCC CHECKIDENT ('Units', RESEED, 0);

INSERT INTO Units (NameAr, NameEn, Symbol, UnitType, IsDefault, IsActive, CreatedAt) VALUES
(N'قطعة', N'Piece', 'PC', 1, 1, 1, GETUTCDATE()),
(N'كيلوجرام', N'Kilogram', 'KG', 2, 0, 1, GETUTCDATE()),
(N'جرام', N'Gram', 'G', 2, 0, 1, GETUTCDATE()),
(N'متر', N'Meter', 'M', 3, 0, 1, GETUTCDATE()),
(N'سنتيمتر', N'Centimeter', 'CM', 3, 0, 1, GETUTCDATE()),
(N'لتر', N'Liter', 'L', 5, 0, 1, GETUTCDATE()),
(N'مليلتر', N'Milliliter', 'ML', 5, 0, 1, GETUTCDATE()),
(N'علبة', N'Box', 'BOX', 1, 0, 1, GETUTCDATE()),
(N'كرتون', N'Carton', 'CTN', 1, 0, 1, GETUTCDATE()),
(N'دزينة', N'Dozen', 'DOZ', 1, 0, 1, GETUTCDATE());

PRINT N'تم إصلاح وحدات القياس';

-- إصلاح أنواع العملاء
DELETE FROM CustomerTypes;
DBCC CHECKIDENT ('CustomerTypes', RESEED, 0);

SET IDENTITY_INSERT CustomerTypes ON;
INSERT INTO CustomerTypes (Id, NameAr, NameEn, DefaultDiscountPercentage, IsActive, CreatedAt) VALUES
(1, N'عميل عادي', N'Regular Customer', 0, 1, GETUTCDATE()),
(2, N'عميل جملة', N'Wholesale Customer', 5, 1, GETUTCDATE()),
(3, N'عميل VIP', N'VIP Customer', 10, 1, GETUTCDATE()),
(4, N'عميل مؤسسي', N'Corporate Customer', 7, 1, GETUTCDATE()),
(5, N'عميل حكومي', N'Government Customer', 3, 1, GETUTCDATE());
SET IDENTITY_INSERT CustomerTypes OFF;

PRINT N'تم إصلاح أنواع العملاء';

-- إصلاح فئات الأسعار
DELETE FROM PriceCategories;
DBCC CHECKIDENT ('PriceCategories', RESEED, 0);

INSERT INTO PriceCategories (NameAr, NameEn, Code, IsDefault, IsActive, PriceAdjustmentPercentage, CreatedAt) VALUES
(N'سعر التجزئة', N'Retail Price', 'RETAIL', 1, 1, 0, GETUTCDATE()),
(N'سعر الجملة', N'Wholesale Price', 'WHOLESALE', 0, 1, -10, GETUTCDATE()),
(N'سعر VIP', N'VIP Price', 'VIP', 0, 1, -15, GETUTCDATE()),
(N'سعر المؤسسات', N'Corporate Price', 'CORP', 0, 1, -8, GETUTCDATE()),
(N'سعر الموظفين', N'Employee Price', 'EMP', 0, 1, -20, GETUTCDATE());

PRINT N'تم إصلاح فئات الأسعار';

-- إصلاح فئات المنتجات
DELETE FROM Categories;
DBCC CHECKIDENT ('Categories', RESEED, 0);

INSERT INTO Categories (NameAr, NameEn, Code, Level, IsActive, CreatedAt) VALUES
(N'عام', N'General', 'GEN', 1, 1, GETUTCDATE()),
(N'أغذية ومشروبات', N'Food & Beverages', 'FOOD', 1, 1, GETUTCDATE()),
(N'إلكترونيات', N'Electronics', 'ELEC', 1, 1, GETUTCDATE()),
(N'ملابس وأزياء', N'Clothing & Fashion', 'CLOTH', 1, 1, GETUTCDATE()),
(N'منزل وحديقة', N'Home & Garden', 'HOME', 1, 1, GETUTCDATE()),
(N'صحة وجمال', N'Health & Beauty', 'HEALTH', 1, 1, GETUTCDATE()),
(N'رياضة وترفيه', N'Sports & Recreation', 'SPORT', 1, 1, GETUTCDATE()),
(N'كتب وقرطاسية', N'Books & Stationery', 'BOOKS', 1, 1, GETUTCDATE()),
(N'ألعاب وهدايا', N'Toys & Gifts', 'TOYS', 1, 1, GETUTCDATE()),
(N'سيارات وقطع غيار', N'Automotive & Parts', 'AUTO', 1, 1, GETUTCDATE());

PRINT N'تم إصلاح فئات المنتجات';

-- إصلاح الفروع
UPDATE Branches 
SET NameAr = N'الفرع الرئيسي', NameEn = N'Main Branch'
WHERE Code = 'MAIN';

PRINT N'تم إصلاح بيانات الفروع';

-- إصلاح العدادات
UPDATE Counters SET Description = N'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = N'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = N'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = N'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = N'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = N'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

PRINT N'تم إصلاح العدادات';

-- إضافة طرق دفع أساسية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PaymentMethods' AND xtype='U')
BEGIN
    CREATE TABLE PaymentMethods (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Code nvarchar(10) NULL,
        PaymentType int NOT NULL, -- 1=Cash, 2=CreditCard, 3=BankTransfer, 4=Credit, 5=Check
        Description nvarchar(200) NULL,
        IsDefault bit NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        DisplayOrder int NOT NULL DEFAULT 0,
        Icon nvarchar(50) NULL,
        Color nvarchar(7) NULL,
        MinAmount decimal(18,2) NULL,
        MaxAmount decimal(18,2) NULL,
        CommissionPercentage decimal(5,2) NOT NULL DEFAULT 0,
        CommissionAmount decimal(18,2) NOT NULL DEFAULT 0,
        AccountId int NULL,
        Settings nvarchar(1000) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT N'تم إنشاء جدول طرق الدفع';
END

-- إدراج طرق الدفع
IF NOT EXISTS (SELECT * FROM PaymentMethods WHERE Code = 'CASH')
BEGIN
    INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsDefault, IsActive, DisplayOrder) VALUES
    (N'نقدي', N'Cash', 'CASH', 1, 1, 1, 1),
    (N'بطاقة ائتمان', N'Credit Card', 'CREDIT', 2, 0, 1, 2),
    (N'بطاقة مدى', N'Debit Card', 'DEBIT', 2, 0, 1, 3),
    (N'تحويل بنكي', N'Bank Transfer', 'TRANSFER', 3, 0, 1, 4),
    (N'آجل', N'Credit', 'CREDIT_TERM', 4, 0, 1, 5),
    (N'شيك', N'Check', 'CHECK', 5, 0, 1, 6),
    (N'محفظة إلكترونية', N'E-Wallet', 'EWALLET', 2, 0, 1, 7);
    PRINT N'تم إدراج طرق الدفع';
END

-- التحقق من النتائج
PRINT N'';
PRINT N'=== تقرير النتائج ===';
PRINT N'Results Report';
PRINT N'==================';

SELECT N'وحدات القياس' as TableName, COUNT(*) as RecordCount FROM Units
UNION ALL
SELECT N'أنواع العملاء', COUNT(*) FROM CustomerTypes
UNION ALL
SELECT N'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT N'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT N'طرق الدفع', COUNT(*) FROM PaymentMethods;

-- عرض عينة من البيانات المصححة
PRINT N'';
PRINT N'عينة من وحدات القياس:';
SELECT TOP 5 Id, NameAr, NameEn, Symbol FROM Units ORDER BY Id;

PRINT N'';
PRINT N'عينة من أنواع العملاء:';
SELECT TOP 5 Id, NameAr, NameEn FROM CustomerTypes ORDER BY Id;

PRINT N'';
PRINT N'تم الانتهاء من إصلاح ترميز النصوص العربية بنجاح!';
PRINT 'Arabic text encoding fix completed successfully!';
