using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("🏭 Supplier Management")]
    public class SuppliersController : ControllerBase
    {
        private readonly AppDbContext _context;

        public SuppliersController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Supplier>>> GetSuppliers()
        {
            try
            {
                var suppliers = await _context.Suppliers
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الموردين بنجاح",
                    data = suppliers,
                    count = suppliers.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Supplier>> GetSupplier(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FindAsync(id);

                if (supplier == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المورد غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات المورد بنجاح",
                    data = supplier
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Supplier>> CreateSupplier(CreateSupplierRequest request)
        {
            try
            {
                // Generate supplier code
                var lastSupplier = await _context.Suppliers
                    .OrderByDescending(s => s.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastSupplier?.Id ?? 0) + 1;
                var supplierCode = $"SUP{nextId:D6}";

                var supplier = new Supplier
                {
                    SupplierCode = supplierCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Phone1 = request.Phone1,
                    Phone2 = request.Phone2,
                    Email = request.Email,
                    Address = request.Address,
                    ContactPerson = request.ContactPerson,
                    TaxNumber = request.TaxNumber,
                    CommercialRegister = request.CommercialRegister,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    CreditLimit = request.CreditLimit,
                    PaymentTerms = request.PaymentTerms,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Suppliers.Add(supplier);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetSupplier), new { id = supplier.Id }, new
                {
                    success = true,
                    message = "تم إضافة المورد بنجاح",
                    data = supplier
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSupplier(int id, UpdateSupplierRequest request)
        {
            try
            {
                var supplier = await _context.Suppliers.FindAsync(id);
                if (supplier == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المورد غير موجود" 
                    });

                supplier.NameAr = request.NameAr;
                supplier.NameEn = request.NameEn;
                supplier.Phone1 = request.Phone1;
                supplier.Phone2 = request.Phone2;
                supplier.Email = request.Email;
                supplier.Address = request.Address;
                supplier.ContactPerson = request.ContactPerson;
                supplier.TaxNumber = request.TaxNumber;
                supplier.CommercialRegister = request.CommercialRegister;
                supplier.CreditLimit = request.CreditLimit;
                supplier.PaymentTerms = request.PaymentTerms;
                supplier.IsActive = request.IsActive;
                supplier.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم تحديث بيانات المورد بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSupplier(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FindAsync(id);
                if (supplier == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المورد غير موجود" 
                    });

                _context.Suppliers.Remove(supplier);
                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم حذف المورد بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}/balance")]
        public async Task<ActionResult> GetSupplierBalance(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FindAsync(id);
                if (supplier == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "المورد غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب رصيد المورد بنجاح",
                    data = new
                    {
                        supplierId = supplier.Id,
                        supplierName = supplier.NameAr,
                        openingBalance = supplier.OpeningBalance,
                        currentBalance = supplier.CurrentBalance,
                        creditLimit = supplier.CreditLimit,
                        availableCredit = supplier.CreditLimit - supplier.CurrentBalance,
                        paymentTerms = supplier.PaymentTerms
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs
    public class CreateSupplierRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? ContactPerson { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CreditLimit { get; set; } = 0;
        public int PaymentTerms { get; set; } = 30;
    }

    public class UpdateSupplierRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? ContactPerson { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public decimal CreditLimit { get; set; } = 0;
        public int PaymentTerms { get; set; } = 30;
        public bool IsActive { get; set; } = true;
    }
}
