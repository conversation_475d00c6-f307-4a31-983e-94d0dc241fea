using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<Purchase> Purchases { get; set; }
        public DbSet<PurchaseItem> PurchaseItems { get; set; }
        public DbSet<Branch> Branches { get; set; }
        public DbSet<User> Users { get; set; }

        // Financial
        public DbSet<ChartOfAccount> ChartOfAccounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }
        public DbSet<Receipt> Receipts { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<PaymentMethod> PaymentMethods { get; set; }
        public DbSet<AccountBalance> AccountBalances { get; set; }

        // Inventory
        public DbSet<ProductStock> ProductStocks { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<BranchTransfer> BranchTransfers { get; set; }
        public DbSet<BranchTransferDetail> BranchTransferDetails { get; set; }
        public DbSet<ProductBatch> ProductBatches { get; set; }
        public DbSet<StockAdjustment> StockAdjustments { get; set; }
        public DbSet<StockAdjustmentDetail> StockAdjustmentDetails { get; set; }
        public DbSet<Counter> Counters { get; set; }

        // Employees
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<Shift> Shifts { get; set; }
        public DbSet<EmployeeShift> EmployeeShifts { get; set; }
        public DbSet<AttendanceRecord> AttendanceRecords { get; set; }
        public DbSet<LeaveType> LeaveTypes { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; }

        // Lookup Tables
        public DbSet<CustomerType> CustomerTypes { get; set; }
        public DbSet<SupplierType> SupplierTypes { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<City> Cities { get; set; }
        public DbSet<PriceCategory> PriceCategories { get; set; }
        public DbSet<ProductPrice> ProductPrices { get; set; }
        public DbSet<Tax> Taxes { get; set; }
        public DbSet<Discount> Discounts { get; set; }
        public DbSet<WorkRegulation> WorkRegulations { get; set; }

        // Financial Transactions & Tracking
        public DbSet<FinancialTransaction> FinancialTransactions { get; set; }
        public DbSet<TransactionTracking> TransactionTrackings { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<SystemLog> SystemLogs { get; set; }
        public DbSet<NotificationLog> NotificationLogs { get; set; }

        // Permissions & Security
        public DbSet<TransactionTypeEntity> TransactionTypeEntities { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<UserBranchPermission> UserBranchPermissions { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<ActivityLog> ActivityLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Unit)
                .WithMany(u => u.Products)
                .HasForeignKey(p => p.UnitId);

            modelBuilder.Entity<Category>()
                .HasOne(c => c.Parent)
                .WithMany(c => c.Children)
                .HasForeignKey(c => c.ParentId);

            modelBuilder.Entity<Sale>()
                .HasOne(s => s.Customer)
                .WithMany()
                .HasForeignKey(s => s.CustomerId);

            modelBuilder.Entity<Sale>()
                .HasOne(s => s.Branch)
                .WithMany(b => b.Sales)
                .HasForeignKey(s => s.BranchId);

            modelBuilder.Entity<Sale>()
                .HasOne(s => s.User)
                .WithMany(u => u.Sales)
                .HasForeignKey(s => s.UserId);

            modelBuilder.Entity<SaleItem>()
                .HasOne(si => si.Sale)
                .WithMany(s => s.SaleItems)
                .HasForeignKey(si => si.SaleId);

            modelBuilder.Entity<SaleItem>()
                .HasOne(si => si.Product)
                .WithMany()
                .HasForeignKey(si => si.ProductId);

            // Purchase relationships
            modelBuilder.Entity<Purchase>()
                .HasOne(p => p.Supplier)
                .WithMany()
                .HasForeignKey(p => p.SupplierId);

            modelBuilder.Entity<Purchase>()
                .HasOne(p => p.Branch)
                .WithMany()
                .HasForeignKey(p => p.BranchId);

            modelBuilder.Entity<Purchase>()
                .HasOne(p => p.User)
                .WithMany()
                .HasForeignKey(p => p.UserId);

            modelBuilder.Entity<PurchaseItem>()
                .HasOne(pi => pi.Purchase)
                .WithMany(p => p.PurchaseItems)
                .HasForeignKey(pi => pi.PurchaseId);

            modelBuilder.Entity<PurchaseItem>()
                .HasOne(pi => pi.Product)
                .WithMany()
                .HasForeignKey(pi => pi.ProductId);

            // Financial relationships
            modelBuilder.Entity<ChartOfAccount>()
                .HasOne(c => c.Parent)
                .WithMany(c => c.Children)
                .HasForeignKey(c => c.ParentId);

            modelBuilder.Entity<JournalEntry>()
                .HasOne(je => je.User)
                .WithMany()
                .HasForeignKey(je => je.UserId);

            modelBuilder.Entity<JournalEntry>()
                .HasOne(je => je.PostedByUser)
                .WithMany()
                .HasForeignKey(je => je.PostedBy);

            modelBuilder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.JournalEntry)
                .WithMany(je => je.JournalEntryDetails)
                .HasForeignKey(jed => jed.JournalEntryId);

            modelBuilder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.Account)
                .WithMany(a => a.JournalEntryDetails)
                .HasForeignKey(jed => jed.AccountId);

            modelBuilder.Entity<Receipt>()
                .HasOne(r => r.Customer)
                .WithMany()
                .HasForeignKey(r => r.CustomerId);

            modelBuilder.Entity<Receipt>()
                .HasOne(r => r.PaymentMethod)
                .WithMany(pm => pm.Receipts)
                .HasForeignKey(r => r.PaymentMethodId);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Supplier)
                .WithMany()
                .HasForeignKey(p => p.SupplierId);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.PaymentMethod)
                .WithMany(pm => pm.Payments)
                .HasForeignKey(p => p.PaymentMethodId);

            // Inventory relationships
            modelBuilder.Entity<ProductStock>()
                .HasOne(ps => ps.Product)
                .WithMany()
                .HasForeignKey(ps => ps.ProductId);

            modelBuilder.Entity<ProductStock>()
                .HasOne(ps => ps.Branch)
                .WithMany()
                .HasForeignKey(ps => ps.BranchId);

            modelBuilder.Entity<StockMovement>()
                .HasOne(sm => sm.Product)
                .WithMany()
                .HasForeignKey(sm => sm.ProductId);

            modelBuilder.Entity<BranchTransfer>()
                .HasOne(bt => bt.FromBranch)
                .WithMany()
                .HasForeignKey(bt => bt.FromBranchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BranchTransfer>()
                .HasOne(bt => bt.ToBranch)
                .WithMany()
                .HasForeignKey(bt => bt.ToBranchId)
                .OnDelete(DeleteBehavior.Restrict);

            // Employee relationships
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Department)
                .WithMany(d => d.Employees)
                .HasForeignKey(e => e.DepartmentId);

            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Position)
                .WithMany(p => p.Employees)
                .HasForeignKey(e => e.PositionId);

            modelBuilder.Entity<EmployeeShift>()
                .HasOne(es => es.Employee)
                .WithMany(e => e.EmployeeShifts)
                .HasForeignKey(es => es.EmployeeId);

            modelBuilder.Entity<AttendanceRecord>()
                .HasOne(ar => ar.Employee)
                .WithMany(e => e.AttendanceRecords)
                .HasForeignKey(ar => ar.EmployeeId);

            // Lookup relationships
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.CustomerType)
                .WithMany(ct => ct.Customers)
                .HasForeignKey(c => c.CustomerTypeId);

            modelBuilder.Entity<Customer>()
                .HasOne(c => c.Country)
                .WithMany(co => co.Customers)
                .HasForeignKey(c => c.CountryId);

            modelBuilder.Entity<Customer>()
                .HasOne(c => c.City)
                .WithMany(ci => ci.Customers)
                .HasForeignKey(c => c.CityId);

            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.SupplierType)
                .WithMany(st => st.Suppliers)
                .HasForeignKey(s => s.SupplierTypeId);

            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.Country)
                .WithMany(co => co.Suppliers)
                .HasForeignKey(s => s.CountryId);

            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.City)
                .WithMany(ci => ci.Suppliers)
                .HasForeignKey(s => s.CityId);

            modelBuilder.Entity<City>()
                .HasOne(c => c.Country)
                .WithMany(co => co.Cities)
                .HasForeignKey(c => c.CountryId);

            // Stock Adjustment relationships
            modelBuilder.Entity<StockAdjustment>()
                .HasOne(sa => sa.User)
                .WithMany()
                .HasForeignKey(sa => sa.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<StockAdjustment>()
                .HasOne(sa => sa.ApprovedByUser)
                .WithMany()
                .HasForeignKey(sa => sa.ApprovedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<StockAdjustmentDetail>()
                .HasOne(sad => sad.StockAdjustment)
                .WithMany(sa => sa.StockAdjustmentDetails)
                .HasForeignKey(sad => sad.StockAdjustmentId);

            // Journal Entry relationships fix
            modelBuilder.Entity<JournalEntry>()
                .HasOne(je => je.PostedByUser)
                .WithMany()
                .HasForeignKey(je => je.PostedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Employee Leave relationships
            modelBuilder.Entity<EmployeeLeave>()
                .HasOne(el => el.ApprovedByUser)
                .WithMany()
                .HasForeignKey(el => el.ApprovedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Department Manager relationship
            modelBuilder.Entity<Department>()
                .HasOne(d => d.Manager)
                .WithMany()
                .HasForeignKey(d => d.ManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product Batch relationships
            modelBuilder.Entity<ProductBatch>()
                .HasOne(pb => pb.Product)
                .WithMany()
                .HasForeignKey(pb => pb.ProductId);

            modelBuilder.Entity<ProductBatch>()
                .HasOne(pb => pb.Branch)
                .WithMany()
                .HasForeignKey(pb => pb.BranchId);

            modelBuilder.Entity<ProductBatch>()
                .HasOne(pb => pb.Supplier)
                .WithMany()
                .HasForeignKey(pb => pb.SupplierId);

            // Counter relationships
            modelBuilder.Entity<Counter>()
                .HasOne(c => c.Branch)
                .WithMany()
                .HasForeignKey(c => c.BranchId);

            // Product Price relationships
            modelBuilder.Entity<ProductPrice>()
                .HasOne(pp => pp.Product)
                .WithMany()
                .HasForeignKey(pp => pp.ProductId);

            modelBuilder.Entity<ProductPrice>()
                .HasOne(pp => pp.PriceCategory)
                .WithMany(pc => pc.ProductPrices)
                .HasForeignKey(pp => pp.PriceCategoryId);

            modelBuilder.Entity<ProductPrice>()
                .HasOne(pp => pp.Branch)
                .WithMany()
                .HasForeignKey(pp => pp.BranchId);

            // Customer PriceCategory relationship
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.PriceCategory)
                .WithMany(pc => pc.Customers)
                .HasForeignKey(c => c.PriceCategoryId);

            // Financial Transaction relationships
            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.MainAccount)
                .WithMany()
                .HasForeignKey(ft => ft.MainAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.SubAccount)
                .WithMany()
                .HasForeignKey(ft => ft.SubAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.Customer)
                .WithMany()
                .HasForeignKey(ft => ft.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.Supplier)
                .WithMany()
                .HasForeignKey(ft => ft.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.Branch)
                .WithMany()
                .HasForeignKey(ft => ft.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.User)
                .WithMany()
                .HasForeignKey(ft => ft.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.PaymentMethod)
                .WithMany()
                .HasForeignKey(ft => ft.PaymentMethodId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.JournalEntry)
                .WithMany()
                .HasForeignKey(ft => ft.JournalEntryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.ReconciledByUser)
                .WithMany()
                .HasForeignKey(ft => ft.ReconciledBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.CancelledByUser)
                .WithMany()
                .HasForeignKey(ft => ft.CancelledBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Transaction Tracking relationships
            modelBuilder.Entity<TransactionTracking>()
                .HasOne(tt => tt.FinancialTransaction)
                .WithMany()
                .HasForeignKey(tt => tt.FinancialTransactionId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<TransactionTracking>()
                .HasOne(tt => tt.User)
                .WithMany()
                .HasForeignKey(tt => tt.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Audit Log relationships
            modelBuilder.Entity<AuditLog>()
                .HasOne(al => al.User)
                .WithMany()
                .HasForeignKey(al => al.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // System Log relationships
            modelBuilder.Entity<SystemLog>()
                .HasOne(sl => sl.User)
                .WithMany()
                .HasForeignKey(sl => sl.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Notification Log relationships
            modelBuilder.Entity<NotificationLog>()
                .HasOne(nl => nl.User)
                .WithMany()
                .HasForeignKey(nl => nl.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Permission relationships
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany()
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Branch)
                .WithMany()
                .HasForeignKey(up => up.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.CreatedByUser)
                .WithMany()
                .HasForeignKey(up => up.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.UpdatedByUser)
                .WithMany()
                .HasForeignKey(up => up.UpdatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // User Branch Permission relationships
            modelBuilder.Entity<UserBranchPermission>()
                .HasOne(ubp => ubp.User)
                .WithMany()
                .HasForeignKey(ubp => ubp.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserBranchPermission>()
                .HasOne(ubp => ubp.Branch)
                .WithMany()
                .HasForeignKey(ubp => ubp.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserBranchPermission>()
                .HasOne(ubp => ubp.CreatedByUser)
                .WithMany()
                .HasForeignKey(ubp => ubp.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserBranchPermission>()
                .HasOne(ubp => ubp.UpdatedByUser)
                .WithMany()
                .HasForeignKey(ubp => ubp.UpdatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Role relationships
            modelBuilder.Entity<Role>()
                .HasOne(r => r.CreatedByUser)
                .WithMany()
                .HasForeignKey(r => r.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Role>()
                .HasOne(r => r.UpdatedByUser)
                .WithMany()
                .HasForeignKey(r => r.UpdatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // User Role relationships
            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.CreatedByUser)
                .WithMany()
                .HasForeignKey(ur => ur.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Role Permission relationships
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.CreatedByUser)
                .WithMany()
                .HasForeignKey(rp => rp.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // User Session relationships
            modelBuilder.Entity<UserSession>()
                .HasOne(us => us.User)
                .WithMany()
                .HasForeignKey(us => us.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserSession>()
                .HasOne(us => us.DefaultBranch)
                .WithMany()
                .HasForeignKey(us => us.DefaultBranchId)
                .OnDelete(DeleteBehavior.Restrict);

            // Activity Log relationships
            modelBuilder.Entity<ActivityLog>()
                .HasOne(al => al.User)
                .WithMany()
                .HasForeignKey(al => al.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ActivityLog>()
                .HasOne(al => al.Branch)
                .WithMany()
                .HasForeignKey(al => al.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2024, 1, 1);

            // Seed Users
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
                    FullName = "مدير النظام",
                    IsActive = true,
                    IsSystemAdmin = true,
                    CreatedAt = seedDate
                }
            );

            // Seed Branches
            modelBuilder.Entity<Branch>().HasData(
                new Branch
                {
                    Id = 1,
                    NameAr = "الفرع الرئيسي",
                    NameEn = "Main Branch",
                    Code = "MAIN",
                    Address = "العنوان الرئيسي",
                    Phone = "01234567890",
                    Email = "<EMAIL>",
                    IsActive = true,
                    IsMainBranch = true,
                    CreatedAt = seedDate
                }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, NameAr = "إلكترونيات", NameEn = "Electronics", DisplayOrder = 1, IsActive = true, CreatedAt = seedDate },
                new Category { Id = 2, NameAr = "ملابس", NameEn = "Clothing", DisplayOrder = 2, IsActive = true, CreatedAt = seedDate },
                new Category { Id = 3, NameAr = "أغذية", NameEn = "Food", DisplayOrder = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Units
            modelBuilder.Entity<Unit>().HasData(
                new Unit { Id = 1, NameAr = "قطعة", NameEn = "Piece", Symbol = "قطعة", DisplayOrder = 1, IsActive = true, CreatedAt = seedDate },
                new Unit { Id = 2, NameAr = "كيلوجرام", NameEn = "Kilogram", Symbol = "كجم", DisplayOrder = 2, IsActive = true, CreatedAt = seedDate },
                new Unit { Id = 3, NameAr = "متر", NameEn = "Meter", Symbol = "م", DisplayOrder = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Customers
            modelBuilder.Entity<Customer>().HasData(
                new Customer
                {
                    Id = 1,
                    CustomerCode = "CUS000001",
                    NameAr = "عميل تجريبي",
                    NameEn = "Test Customer",
                    Phone1 = "01234567890",
                    Email = "<EMAIL>",
                    Address = "عنوان تجريبي",
                    OpeningBalance = 1000,
                    CurrentBalance = 1000,
                    IsActive = true,
                    CreatedAt = seedDate
                }
            );

            // Seed Suppliers
            modelBuilder.Entity<Supplier>().HasData(
                new Supplier
                {
                    Id = 1,
                    SupplierCode = "SUP000001",
                    NameAr = "مورد تجريبي",
                    NameEn = "Test Supplier",
                    Phone1 = "01234567890",
                    Email = "<EMAIL>",
                    Address = "عنوان المورد",
                    ContactPerson = "أحمد محمد",
                    OpeningBalance = 5000,
                    CurrentBalance = 5000,
                    CreditLimit = 10000,
                    PaymentTerms = 30,
                    IsActive = true,
                    CreatedAt = seedDate
                }
            );

            // Seed Products
            modelBuilder.Entity<Product>().HasData(
                new Product
                {
                    Id = 1,
                    ProductCode = "PRD000001",
                    NameAr = "منتج تجريبي",
                    NameEn = "Test Product",
                    Description = "وصف المنتج التجريبي",
                    CategoryId = 1,
                    UnitId = 1,
                    Barcode = "1234567890123",
                    CostPrice = 100,
                    BasePrice = 150,
                    ProfitMargin = 50,
                    MinimumStock = 10,
                    MaximumStock = 100,
                    ReorderPoint = 20,
                    IsActive = true,
                    CreatedAt = seedDate
                }
            );

            // Seed Payment Methods
            modelBuilder.Entity<PaymentMethod>().HasData(
                new PaymentMethod { Id = 1, NameAr = "نقدي", NameEn = "Cash", Code = "CASH", PaymentType = 1, IsActive = true, IsDefault = true, CreatedAt = seedDate },
                new PaymentMethod { Id = 2, NameAr = "بنكي", NameEn = "Bank", Code = "BANK", PaymentType = 2, IsActive = true, CreatedAt = seedDate },
                new PaymentMethod { Id = 3, NameAr = "فيزا", NameEn = "Visa", Code = "VISA", PaymentType = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Departments
            modelBuilder.Entity<Department>().HasData(
                new Department { Id = 1, NameAr = "المبيعات", NameEn = "Sales", IsActive = true, CreatedAt = seedDate },
                new Department { Id = 2, NameAr = "المخازن", NameEn = "Warehouse", IsActive = true, CreatedAt = seedDate },
                new Department { Id = 3, NameAr = "المحاسبة", NameEn = "Accounting", IsActive = true, CreatedAt = seedDate }
            );

            // Seed Positions
            modelBuilder.Entity<Position>().HasData(
                new Position { Id = 1, NameAr = "مدير مبيعات", NameEn = "Sales Manager", MinSalary = 5000, MaxSalary = 10000, IsActive = true, CreatedAt = seedDate },
                new Position { Id = 2, NameAr = "موظف مبيعات", NameEn = "Sales Employee", MinSalary = 3000, MaxSalary = 6000, IsActive = true, CreatedAt = seedDate },
                new Position { Id = 3, NameAr = "أمين مخزن", NameEn = "Warehouse Keeper", MinSalary = 2500, MaxSalary = 5000, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Shifts
            modelBuilder.Entity<Shift>().HasData(
                new Shift { Id = 1, ShiftName = "الوردية الصباحية", StartTime = new TimeSpan(8, 0, 0), EndTime = new TimeSpan(16, 0, 0), BreakDuration = 60, GracePeriodMinutes = 15, IsActive = true, CreatedAt = seedDate },
                new Shift { Id = 2, ShiftName = "الوردية المسائية", StartTime = new TimeSpan(16, 0, 0), EndTime = new TimeSpan(24, 0, 0), BreakDuration = 60, GracePeriodMinutes = 15, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Leave Types
            modelBuilder.Entity<LeaveType>().HasData(
                new LeaveType { Id = 1, NameAr = "إجازة سنوية", NameEn = "Annual Leave", MaxDaysPerYear = 21, IsPaid = true, RequireApproval = true, IsActive = true, CreatedAt = seedDate },
                new LeaveType { Id = 2, NameAr = "إجازة مرضية", NameEn = "Sick Leave", MaxDaysPerYear = 30, IsPaid = true, RequireApproval = true, RequireDocument = true, IsActive = true, CreatedAt = seedDate },
                new LeaveType { Id = 3, NameAr = "إجازة طارئة", NameEn = "Emergency Leave", MaxDaysPerYear = 7, IsPaid = false, RequireApproval = true, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Chart of Accounts
            modelBuilder.Entity<ChartOfAccount>().HasData(
                new ChartOfAccount { Id = 1, AccountCode = "1000", NameAr = "الأصول", NameEn = "Assets", AccountType = 1, Level = 1, IsParent = true, AllowPosting = false, IsActive = true, CreatedAt = seedDate },
                new ChartOfAccount { Id = 2, AccountCode = "1100", NameAr = "الأصول المتداولة", NameEn = "Current Assets", AccountType = 1, ParentId = 1, Level = 2, IsParent = true, AllowPosting = false, IsActive = true, CreatedAt = seedDate },
                new ChartOfAccount { Id = 3, AccountCode = "1110", NameAr = "النقدية", NameEn = "Cash", AccountType = 1, ParentId = 2, Level = 3, IsParent = false, AllowPosting = true, IsActive = true, CreatedAt = seedDate },
                new ChartOfAccount { Id = 4, AccountCode = "1120", NameAr = "العملاء", NameEn = "Accounts Receivable", AccountType = 1, ParentId = 2, Level = 3, IsParent = false, AllowPosting = true, IsActive = true, CreatedAt = seedDate },
                new ChartOfAccount { Id = 5, AccountCode = "2000", NameAr = "الخصوم", NameEn = "Liabilities", AccountType = 2, Level = 1, IsParent = true, AllowPosting = false, IsActive = true, CreatedAt = seedDate },
                new ChartOfAccount { Id = 6, AccountCode = "2100", NameAr = "الموردين", NameEn = "Accounts Payable", AccountType = 2, ParentId = 5, Level = 2, IsParent = false, AllowPosting = true, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Counters
            modelBuilder.Entity<Counter>().HasData(
                new Counter { Id = 1, CounterName = CounterTypes.Customer.ToString(), Prefix = "CUS", CurrentValue = 1, NumberLength = 6, BranchId = 1, IsActive = true, CreatedAt = seedDate },
                new Counter { Id = 2, CounterName = CounterTypes.Supplier.ToString(), Prefix = "SUP", CurrentValue = 1, NumberLength = 6, BranchId = 1, IsActive = true, CreatedAt = seedDate },
                new Counter { Id = 3, CounterName = CounterTypes.Product.ToString(), Prefix = "PRD", CurrentValue = 1, NumberLength = 6, BranchId = 1, IsActive = true, CreatedAt = seedDate },
                new Counter { Id = 4, CounterName = CounterTypes.SaleInvoice.ToString(), Prefix = "SAL", CurrentValue = 0, NumberLength = 8, BranchId = 1, IsActive = true, CreatedAt = seedDate },
                new Counter { Id = 5, CounterName = CounterTypes.JournalEntry.ToString(), Prefix = "JE", CurrentValue = 0, NumberLength = 8, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Customer Types
            modelBuilder.Entity<CustomerType>().HasData(
                new CustomerType { Id = 1, NameAr = "عميل عادي", NameEn = "Regular Customer", DisplayOrder = 1, IsActive = true, CreatedAt = seedDate },
                new CustomerType { Id = 2, NameAr = "عميل VIP", NameEn = "VIP Customer", DisplayOrder = 2, IsActive = true, CreatedAt = seedDate },
                new CustomerType { Id = 3, NameAr = "عميل جملة", NameEn = "Wholesale Customer", DisplayOrder = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Supplier Types
            modelBuilder.Entity<SupplierType>().HasData(
                new SupplierType { Id = 1, NameAr = "مورد محلي", NameEn = "Local Supplier", DisplayOrder = 1, IsActive = true, CreatedAt = seedDate },
                new SupplierType { Id = 2, NameAr = "مورد دولي", NameEn = "International Supplier", DisplayOrder = 2, IsActive = true, CreatedAt = seedDate },
                new SupplierType { Id = 3, NameAr = "مورد خدمات", NameEn = "Service Provider", DisplayOrder = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Countries
            modelBuilder.Entity<Country>().HasData(
                new Country { Id = 1, NameAr = "مصر", NameEn = "Egypt", Code = "EG", PhoneCode = "+20", Currency = "EGP", IsActive = true, CreatedAt = seedDate },
                new Country { Id = 2, NameAr = "السعودية", NameEn = "Saudi Arabia", Code = "SA", PhoneCode = "+966", Currency = "SAR", IsActive = true, CreatedAt = seedDate },
                new Country { Id = 3, NameAr = "الإمارات", NameEn = "UAE", Code = "AE", PhoneCode = "+971", Currency = "AED", IsActive = true, CreatedAt = seedDate }
            );

            // Seed Cities
            modelBuilder.Entity<City>().HasData(
                new City { Id = 1, NameAr = "القاهرة", NameEn = "Cairo", CountryId = 1, IsActive = true, CreatedAt = seedDate },
                new City { Id = 2, NameAr = "الإسكندرية", NameEn = "Alexandria", CountryId = 1, IsActive = true, CreatedAt = seedDate },
                new City { Id = 3, NameAr = "الرياض", NameEn = "Riyadh", CountryId = 2, IsActive = true, CreatedAt = seedDate },
                new City { Id = 4, NameAr = "دبي", NameEn = "Dubai", CountryId = 3, IsActive = true, CreatedAt = seedDate }
            );

            // Seed Price Categories
            modelBuilder.Entity<PriceCategory>().HasData(
                new PriceCategory { Id = 1, NameAr = "سعر التجزئة", NameEn = "Retail Price", DiscountPercentage = 0, DisplayOrder = 1, IsActive = true, CreatedAt = seedDate },
                new PriceCategory { Id = 2, NameAr = "سعر الجملة", NameEn = "Wholesale Price", DiscountPercentage = 10, DisplayOrder = 2, IsActive = true, CreatedAt = seedDate },
                new PriceCategory { Id = 3, NameAr = "سعر VIP", NameEn = "VIP Price", DiscountPercentage = 15, DisplayOrder = 3, IsActive = true, CreatedAt = seedDate }
            );
        }
    }
}
