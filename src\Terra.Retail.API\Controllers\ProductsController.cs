using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Core.Entities;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    /// <summary>
    /// تحكم في المنتجات
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class ProductsController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<ProductsController> _logger;

        public ProductsController(TerraRetailDbContext context, ILogger<ProductsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        /// <param name="page">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="categoryId">معرف الفئة</param>
        /// <returns>قائمة المنتجات</returns>
        [HttpGet]
        public async Task<ActionResult> GetProducts(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 20,
            [FromQuery] int? categoryId = null)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .Where(p => p.IsActive);

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                var totalCount = await query.CountAsync();
                var products = await query
                    .OrderBy(p => p.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var result = new
                {
                    Data = products,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                _logger.LogInformation("تم استرجاع {Count} منتج من أصل {Total}", products.Count, totalCount);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع المنتجات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على منتج محدد
        /// </summary>
        /// <param name="id">معرف المنتج</param>
        /// <returns>بيانات المنتج</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetProduct(int id)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .Include(p => p.Images)
                    .Include(p => p.AlternativeCodes)
                    .Include(p => p.BranchPrices)
                        .ThenInclude(bp => bp.Branch)
                    .Include(p => p.BranchPrices)
                        .ThenInclude(bp => bp.PriceCategory)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                {
                    _logger.LogWarning("المنتج غير موجود: {ProductId}", id);
                    return NotFound(new { message = "المنتج غير موجود" });
                }

                _logger.LogInformation("تم استرجاع المنتج: {ProductName}", product.NameAr);
                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع المنتج {ProductId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء منتج جديد
        /// </summary>
        /// <param name="product">بيانات المنتج</param>
        /// <returns>المنتج المنشأ</returns>
        [HttpPost]
        public async Task<ActionResult<Product>> CreateProduct(Product product)
        {
            try
            {
                // التحقق من وجود كود المنتج
                var existingProduct = await _context.Products
                    .FirstOrDefaultAsync(p => p.ProductCode == product.ProductCode);

                if (existingProduct != null)
                {
                    return BadRequest(new { message = "كود المنتج موجود بالفعل" });
                }

                // التحقق من الباركود إذا تم توفيره
                if (!string.IsNullOrEmpty(product.Barcode))
                {
                    var existingBarcode = await _context.Products
                        .FirstOrDefaultAsync(p => p.Barcode == product.Barcode);

                    if (existingBarcode != null)
                    {
                        return BadRequest(new { message = "الباركود موجود بالفعل" });
                    }
                }

                // إنشاء كود تلقائي إذا لم يتم توفيره
                if (string.IsNullOrEmpty(product.ProductCode))
                {
                    product.ProductCode = await GenerateProductCodeAsync();
                }

                product.CreatedAt = DateTime.UtcNow;
                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء منتج جديد: {ProductName} - {ProductCode}", 
                    product.NameAr, product.ProductCode);

                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المنتج");
                return StatusCode(500, new { message = "حدث خطأ في إنشاء المنتج", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث بيانات منتج
        /// </summary>
        /// <param name="id">معرف المنتج</param>
        /// <param name="product">البيانات المحدثة</param>
        /// <returns>نتيجة التحديث</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProduct(int id, Product product)
        {
            if (id != product.Id)
            {
                return BadRequest(new { message = "معرف المنتج غير متطابق" });
            }

            try
            {
                var existingProduct = await _context.Products.FindAsync(id);
                if (existingProduct == null)
                {
                    return NotFound(new { message = "المنتج غير موجود" });
                }

                // تحديث البيانات
                existingProduct.NameAr = product.NameAr;
                existingProduct.NameEn = product.NameEn;
                existingProduct.Description = product.Description;
                existingProduct.CategoryId = product.CategoryId;
                existingProduct.UnitId = product.UnitId;
                existingProduct.Barcode = product.Barcode;
                existingProduct.CostPrice = product.CostPrice;
                existingProduct.BasePrice = product.BasePrice;
                existingProduct.ProfitMargin = product.ProfitMargin;
                existingProduct.MinimumStock = product.MinimumStock;
                existingProduct.MaximumStock = product.MaximumStock;
                existingProduct.ReorderPoint = product.ReorderPoint;
                existingProduct.IsActive = product.IsActive;
                existingProduct.Weight = product.Weight;
                existingProduct.Length = product.Length;
                existingProduct.Width = product.Width;
                existingProduct.Height = product.Height;
                existingProduct.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث المنتج: {ProductName}", existingProduct.NameAr);
                return Ok(new { message = "تم تحديث المنتج بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المنتج {ProductId}", id);
                return StatusCode(500, new { message = "حدث خطأ في تحديث المنتج", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        /// <param name="id">معرف المنتج</param>
        /// <returns>نتيجة الحذف</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return NotFound(new { message = "المنتج غير موجود" });
                }

                // التحقق من وجود معاملات للمنتج
                var hasTransactions = await _context.SaleItems.AnyAsync(si => si.ProductId == id) ||
                                     await _context.PurchaseItems.AnyAsync(pi => pi.ProductId == id);

                if (hasTransactions)
                {
                    // إلغاء تفعيل بدلاً من الحذف
                    product.IsActive = false;
                    product.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم إلغاء تفعيل المنتج: {ProductName}", product.NameAr);
                    return Ok(new { message = "تم إلغاء تفعيل المنتج بنجاح" });
                }
                else
                {
                    _context.Products.Remove(product);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم حذف المنتج: {ProductName}", product.NameAr);
                    return Ok(new { message = "تم حذف المنتج بنجاح" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المنتج {ProductId}", id);
                return StatusCode(500, new { message = "حدث خطأ في حذف المنتج", error = ex.Message });
            }
        }

        /// <summary>
        /// البحث في المنتجات
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="categoryId">معرف الفئة</param>
        /// <returns>نتائج البحث</returns>
        [HttpGet("search")]
        public async Task<ActionResult> SearchProducts(
            [FromQuery] string searchTerm, 
            [FromQuery] int? categoryId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(new { message = "يرجى إدخال كلمة البحث" });
                }

                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .Where(p => p.IsActive &&
                        (p.NameAr.Contains(searchTerm) ||
                         (p.NameEn != null && p.NameEn.Contains(searchTerm)) ||
                         p.ProductCode.Contains(searchTerm) ||
                         (p.Barcode != null && p.Barcode.Contains(searchTerm))));

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                var products = await query
                    .OrderBy(p => p.NameAr)
                    .Take(50) // تحديد عدد النتائج
                    .ToListAsync();

                _logger.LogInformation("تم البحث عن '{SearchTerm}' ووجد {Count} نتيجة", searchTerm, products.Count);
                return Ok(products);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن المنتجات");
                return StatusCode(500, new { message = "حدث خطأ في البحث", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على مخزون المنتج في الفروع
        /// </summary>
        /// <param name="id">معرف المنتج</param>
        /// <returns>مخزون المنتج</returns>
        [HttpGet("{id}/stock")]
        public async Task<ActionResult> GetProductStock(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return NotFound(new { message = "المنتج غير موجود" });
                }

                var stock = await _context.ProductStocks
                    .Include(ps => ps.Branch)
                    .Where(ps => ps.ProductId == id)
                    .Select(ps => new
                    {
                        BranchId = ps.BranchId,
                        BranchName = ps.Branch.NameAr,
                        AvailableQuantity = ps.AvailableQuantity,
                        ReservedQuantity = ps.ReservedQuantity,
                        OnOrderQuantity = ps.OnOrderQuantity,
                        AverageCostPrice = ps.AverageCostPrice,
                        StockValue = ps.StockValue,
                        LastUpdated = ps.UpdatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع مخزون المنتج: {ProductName}", product.NameAr);
                return Ok(stock);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع مخزون المنتج {ProductId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع المخزون", error = ex.Message });
            }
        }

        /// <summary>
        /// توليد كود منتج تلقائي
        /// </summary>
        /// <returns>كود المنتج الجديد</returns>
        private async Task<string> GenerateProductCodeAsync()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == "PRODUCT");

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = "PRODUCT",
                    Prefix = "PRD",
                    CurrentValue = 1,
                    NumberLength = 6,
                    CreatedAt = DateTime.UtcNow
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }
    }
}
