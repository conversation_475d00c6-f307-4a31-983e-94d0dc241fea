-- إنشاء قاعدة بيانات Terra Retail ERP
-- Terra Retail ERP Database Creation Script

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'TerraRetailERP')
BEGIN
    CREATE DATABASE TerraRetailERP
    COLLATE Arabic_CI_AS;
    PRINT 'تم إنشاء قاعدة البيانات TerraRetailERP بنجاح';
END
ELSE
BEGIN
    PRINT 'قاعدة البيانات TerraRetailERP موجودة بالفعل';
END
GO

USE TerraRetailERP;
GO

-- إن<PERSON><PERSON><PERSON> الجداول الأساسية

-- جدول الفروع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
BEGIN
    CREATE TABLE Branches (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(100) NOT NULL,
        NameEn nvarchar(100) NULL,
        Code nvarchar(10) NOT NULL UNIQUE,
        Address nvarchar(500) NULL,
        Phone nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        ManagerName nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsMainBranch bit NOT NULL DEFAULT 0,
        OpeningDate datetime2 NULL,
        WorkingHours nvarchar(100) NULL,
        Longitude decimal(18,6) NULL,
        Latitude decimal(18,6) NULL,
        Area decimal(18,2) NULL,
        EmployeeCount int NULL,
        TaxNumber nvarchar(50) NULL,
        CommercialRegister nvarchar(50) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT 'تم إنشاء جدول Branches';
END

-- جدول العدادات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Counters' AND xtype='U')
BEGIN
    CREATE TABLE Counters (
        Id int IDENTITY(1,1) PRIMARY KEY,
        CounterName nvarchar(50) NOT NULL,
        Prefix nvarchar(10) NULL,
        CurrentValue bigint NOT NULL DEFAULT 0,
        NumberLength int NOT NULL DEFAULT 6,
        Description nvarchar(200) NULL,
        BranchId int NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CodeFormat nvarchar(100) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    
    CREATE UNIQUE INDEX IX_Counters_CounterName_BranchId ON Counters(CounterName, BranchId);
    PRINT 'تم إنشاء جدول Counters';
END

-- جدول أنواع العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustomerTypes' AND xtype='U')
BEGIN
    CREATE TABLE CustomerTypes (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Description nvarchar(200) NULL,
        DefaultDiscountPercentage decimal(5,2) NOT NULL DEFAULT 0,
        DefaultCreditLimit decimal(18,2) NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        Color nvarchar(7) NULL,
        DisplayOrder int NOT NULL DEFAULT 0,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT 'تم إنشاء جدول CustomerTypes';
END

-- جدول المناطق
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Areas' AND xtype='U')
BEGIN
    CREATE TABLE Areas (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Code nvarchar(10) NULL,
        ParentAreaId int NULL,
        Description nvarchar(200) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        DisplayOrder int NOT NULL DEFAULT 0,
        Longitude decimal(18,6) NULL,
        Latitude decimal(18,6) NULL,
        CoverageRadius decimal(18,2) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (ParentAreaId) REFERENCES Areas(Id)
    );
    PRINT 'تم إنشاء جدول Areas';
END

-- جدول فئات الأسعار
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PriceCategories' AND xtype='U')
BEGIN
    CREATE TABLE PriceCategories (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Code nvarchar(10) NULL,
        Description nvarchar(200) NULL,
        PriceAdjustmentPercentage decimal(5,2) NOT NULL DEFAULT 0,
        IsDefault bit NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        DisplayOrder int NOT NULL DEFAULT 0,
        Color nvarchar(7) NULL,
        MinimumQuantity decimal(18,3) NULL,
        MaximumQuantity decimal(18,3) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT 'تم إنشاء جدول PriceCategories';
END

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        Id int IDENTITY(1,1) PRIMARY KEY,
        CustomerCode nvarchar(20) NOT NULL UNIQUE,
        NameAr nvarchar(100) NOT NULL,
        NameEn nvarchar(100) NULL,
        CustomerTypeId int NOT NULL,
        Phone1 nvarchar(20) NULL,
        Phone2 nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        Address nvarchar(500) NULL,
        AreaId int NULL,
        BranchId int NOT NULL,
        PriceCategoryId int NULL,
        DiscountPercentage decimal(5,2) NOT NULL DEFAULT 0,
        OpeningBalance decimal(18,2) NOT NULL DEFAULT 0,
        CurrentBalance decimal(18,2) NOT NULL DEFAULT 0,
        CreditLimit decimal(18,2) NOT NULL DEFAULT 0,
        IdentityNumber nvarchar(50) NULL,
        TaxNumber nvarchar(50) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (CustomerTypeId) REFERENCES CustomerTypes(Id),
        FOREIGN KEY (AreaId) REFERENCES Areas(Id),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (PriceCategoryId) REFERENCES PriceCategories(Id)
    );
    
    CREATE INDEX IX_Customers_NameAr_BranchId ON Customers(NameAr, BranchId);
    CREATE INDEX IX_Customers_Phone1 ON Customers(Phone1);
    CREATE INDEX IX_Customers_Email ON Customers(Email);
    PRINT 'تم إنشاء جدول Customers';
END

-- جدول وحدات القياس
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Units' AND xtype='U')
BEGIN
    CREATE TABLE Units (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Symbol nvarchar(10) NOT NULL UNIQUE,
        UnitType int NOT NULL, -- 1=Quantity, 2=Weight, 3=Length, 4=Area, 5=Volume, 6=Time
        BaseUnitId int NULL,
        ConversionFactor decimal(18,6) NOT NULL DEFAULT 1,
        Description nvarchar(200) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDefault bit NOT NULL DEFAULT 0,
        DisplayOrder int NOT NULL DEFAULT 0,
        DecimalPlaces int NOT NULL DEFAULT 2,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (BaseUnitId) REFERENCES Units(Id)
    );
    PRINT 'تم إنشاء جدول Units';
END

-- جدول فئات المنتجات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U')
BEGIN
    CREATE TABLE Categories (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(100) NOT NULL,
        NameEn nvarchar(100) NULL,
        Code nvarchar(20) NULL,
        ParentCategoryId int NULL,
        Level int NOT NULL DEFAULT 1,
        Path nvarchar(100) NULL,
        Description nvarchar(500) NULL,
        Image nvarchar(500) NULL,
        Icon nvarchar(50) NULL,
        Color nvarchar(7) NULL,
        DisplayOrder int NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        IsFeatured bit NOT NULL DEFAULT 0,
        ProductCount int NOT NULL DEFAULT 0,
        Keywords nvarchar(500) NULL,
        SeoTitle nvarchar(200) NULL,
        SeoDescription nvarchar(500) NULL,
        SeoKeywords nvarchar(500) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (ParentCategoryId) REFERENCES Categories(Id)
    );
    
    CREATE INDEX IX_Categories_ParentCategoryId_DisplayOrder ON Categories(ParentCategoryId, DisplayOrder);
    CREATE INDEX IX_Categories_Level ON Categories(Level);
    PRINT 'تم إنشاء جدول Categories';
END

-- إدراج البيانات الأولية
PRINT 'بدء إدراج البيانات الأولية...';

-- إدراج فرع رئيسي افتراضي
IF NOT EXISTS (SELECT * FROM Branches WHERE Code = 'MAIN')
BEGIN
    INSERT INTO Branches (NameAr, NameEn, Code, IsActive, IsMainBranch)
    VALUES (N'الفرع الرئيسي', N'Main Branch', 'MAIN', 1, 1);
    PRINT N'تم إدراج الفرع الرئيسي';
END

-- إدراج العدادات الأساسية
IF NOT EXISTS (SELECT * FROM Counters WHERE CounterName = 'CUSTOMER')
BEGIN
    INSERT INTO Counters (CounterName, Prefix, CurrentValue, NumberLength) VALUES
    ('CUSTOMER', 'CUS', 0, 6),
    ('PRODUCT', 'PRD', 0, 6),
    ('SALE', 'SAL', 0, 8),
    ('PURCHASE', 'PUR', 0, 8),
    ('SUPPLIER', 'SUP', 0, 6),
    ('EMPLOYEE', 'EMP', 0, 6);
    PRINT 'تم إدراج العدادات الأساسية';
END

-- إدراج أنواع العملاء
IF NOT EXISTS (SELECT * FROM CustomerTypes WHERE NameAr = N'عميل عادي')
BEGIN
    INSERT INTO CustomerTypes (NameAr, NameEn, DefaultDiscountPercentage, IsActive) VALUES
    (N'عميل عادي', N'Regular Customer', 0, 1),
    (N'عميل جملة', N'Wholesale Customer', 5, 1),
    (N'عميل VIP', N'VIP Customer', 10, 1);
    PRINT N'تم إدراج أنواع العملاء';
END

-- إدراج فئات الأسعار
IF NOT EXISTS (SELECT * FROM PriceCategories WHERE Code = 'RETAIL')
BEGIN
    INSERT INTO PriceCategories (NameAr, NameEn, Code, IsDefault, IsActive, PriceAdjustmentPercentage) VALUES
    (N'سعر التجزئة', N'Retail Price', 'RETAIL', 1, 1, 0),
    (N'سعر الجملة', N'Wholesale Price', 'WHOLESALE', 0, 1, -10),
    (N'سعر VIP', N'VIP Price', 'VIP', 0, 1, -15);
    PRINT N'تم إدراج فئات الأسعار';
END

-- إدراج وحدات القياس
IF NOT EXISTS (SELECT * FROM Units WHERE Symbol = 'PC')
BEGIN
    INSERT INTO Units (NameAr, NameEn, Symbol, UnitType, IsDefault, IsActive) VALUES
    (N'قطعة', N'Piece', 'PC', 1, 1, 1),
    (N'كيلوجرام', N'Kilogram', 'KG', 2, 0, 1),
    (N'متر', N'Meter', 'M', 3, 0, 1),
    (N'لتر', N'Liter', 'L', 5, 0, 1);
    PRINT N'تم إدراج وحدات القياس';
END

-- إدراج فئة منتجات افتراضية
IF NOT EXISTS (SELECT * FROM Categories WHERE NameAr = N'عام')
BEGIN
    INSERT INTO Categories (NameAr, NameEn, Code, Level, IsActive)
    VALUES (N'عام', N'General', 'GEN', 1, 1);
    PRINT N'تم إدراج فئة المنتجات الافتراضية';
END

PRINT 'تم الانتهاء من إنشاء قاعدة البيانات وإدراج البيانات الأولية بنجاح!';
PRINT 'قاعدة البيانات جاهزة للاستخدام';

-- عرض ملخص الجداول المنشأة
SELECT 
    TABLE_NAME as 'اسم الجدول',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as 'عدد الأعمدة'
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;
