using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Counters")]
    public class Counter
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string CounterName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Prefix { get; set; }

        public long CurrentValue { get; set; } = 0;

        public int NumberLength { get; set; } = 6;

        [StringLength(400)]
        public string? Description { get; set; }

        public int? BranchId { get; set; }

        public bool IsActive { get; set; } = true;

        [StringLength(200)]
        public string? CodeFormat { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch? Branch { get; set; }
    }

    public static class CounterTypes
    {
        public const string Customer = "CUSTOMER";
        public const string Supplier = "SUPPLIER";
        public const string ProductLocal = "PRODUCT_LOCAL";
        public const string ProductWeight = "PRODUCT_WEIGHT";
        public const string SaleInvoice = "SALE_INVOICE";
        public const string PurchaseInvoice = "PURCHASE_INVOICE";
        public const string JournalEntry = "JOURNAL_ENTRY";
        public const string Receipt = "RECEIPT";
        public const string Payment = "PAYMENT";
        public const string BranchTransfer = "BRANCH_TRANSFER";
        public const string Employee = "EMPLOYEE";
    }
}
