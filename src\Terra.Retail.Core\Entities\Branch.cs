using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الفروع
    /// </summary>
    public class Branch : BaseEntity
    {
        /// <summary>
        /// اسم الفرع بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الفرع بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود الفرع
        /// </summary>
        [Required]
        [MaxLength(10)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// العنوان
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [MaxLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// اسم المدير
        /// </summary>
        [MaxLength(100)]
        public string? ManagerName { get; set; }

        /// <summary>
        /// هل الفرع نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الفرع رئيسي
        /// </summary>
        public bool IsMainBranch { get; set; } = false;

        /// <summary>
        /// تاريخ الافتتاح
        /// </summary>
        public DateTime? OpeningDate { get; set; }

        /// <summary>
        /// ساعات العمل
        /// </summary>
        [MaxLength(100)]
        public string? WorkingHours { get; set; }

        /// <summary>
        /// إحداثيات الفرع (خط الطول)
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// إحداثيات الفرع (خط العرض)
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// مساحة الفرع (متر مربع)
        /// </summary>
        public decimal? Area { get; set; }

        /// <summary>
        /// عدد الموظفين
        /// </summary>
        public int? EmployeeCount { get; set; }

        /// <summary>
        /// الرقم الضريبي للفرع
        /// </summary>
        [MaxLength(50)]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// رقم السجل التجاري
        /// </summary>
        [MaxLength(50)]
        public string? CommercialRegister { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
        public virtual ICollection<Counter> Counters { get; set; } = new List<Counter>();
        public virtual ICollection<CashBox> CashBoxes { get; set; } = new List<CashBox>();
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}
