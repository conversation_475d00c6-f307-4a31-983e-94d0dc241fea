<div class="main-layout">
  <!-- Sidebar -->
  <app-sidebar
    [class.collapsed]="isSidebarCollapsed"
    (sidebarToggle)="onSidebarToggle($event)">
  </app-sidebar>

  <!-- Main Content Area -->
  <div class="content-area" [class.sidebar-collapsed]="isSidebarCollapsed">
    <!-- Top Header -->
    <header class="top-header">
      <div class="header-left">
        <button mat-icon-button class="menu-toggle" (click)="toggleSidebar()">
          <mat-icon>menu</mat-icon>
        </button>
        <div class="breadcrumb">
          <mat-icon>home</mat-icon>
          <span>{{ currentPageTitle }}</span>
        </div>
      </div>

      <div class="header-right">
        <!-- Notifications -->
        <button mat-icon-button [matMenuTriggerFor]="notificationMenu" class="notification-btn">
          <mat-icon matBadge="3" matBadgeColor="warn">notifications</mat-icon>
        </button>

        <!-- User Menu -->
        <button mat-button [matMenuTriggerFor]="userMenu" class="user-menu-btn">
          <mat-icon>account_circle</mat-icon>
          <span>{{ currentUser?.username }}</span>
          <mat-icon>keyboard_arrow_down</mat-icon>
        </button>
      </div>
    </header>

    <!-- Page Content -->
    <main class="page-content">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- Notification Menu -->
  <mat-menu #notificationMenu="matMenu" class="notification-menu">
    <div class="notification-header">
      <h4>الإشعارات</h4>
      <button mat-button color="primary">مشاهدة الكل</button>
    </div>
    <mat-divider></mat-divider>

    <button mat-menu-item class="notification-item">
      <mat-icon color="primary">shopping_cart</mat-icon>
      <div class="notification-content">
        <span class="notification-title">طلب جديد</span>
        <span class="notification-time">منذ 5 دقائق</span>
      </div>
    </button>

    <button mat-menu-item class="notification-item">
      <mat-icon color="warn">inventory</mat-icon>
      <div class="notification-content">
        <span class="notification-title">مخزون منخفض</span>
        <span class="notification-time">منذ 15 دقيقة</span>
      </div>
    </button>

    <button mat-menu-item class="notification-item">
      <mat-icon color="accent">people</mat-icon>
      <div class="notification-content">
        <span class="notification-title">عميل جديد</span>
        <span class="notification-time">منذ ساعة</span>
      </div>
    </button>
  </mat-menu>

  <!-- User Menu -->
  <mat-menu #userMenu="matMenu" class="user-menu">
    <button mat-menu-item>
      <mat-icon>person</mat-icon>
      <span>الملف الشخصي</span>
    </button>

    <button mat-menu-item>
      <mat-icon>business</mat-icon>
      <span>تغيير الفرع</span>
    </button>

    <button mat-menu-item>
      <mat-icon>settings</mat-icon>
      <span>الإعدادات</span>
    </button>

    <mat-divider></mat-divider>

    <button mat-menu-item (click)="logout()">
      <mat-icon color="warn">logout</mat-icon>
      <span>تسجيل الخروج</span>
    </button>
  </mat-menu>
</div>
