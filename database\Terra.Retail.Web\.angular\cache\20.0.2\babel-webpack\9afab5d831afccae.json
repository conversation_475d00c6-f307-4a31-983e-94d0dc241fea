{"ast": null, "code": "import { AriaDescriber, _IdGenerator, Interactivity<PERSON><PERSON><PERSON>, A11yModule } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, NgZone, ElementRef, Renderer2, DOCUMENT, HOST_TAG_NAME, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nlet _MatBadgeStyleLoader = /*#__PURE__*/(() => {\n  class _MatBadgeStyleLoader {\n    static ɵfac = function _MatBadgeStyleLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatBadgeStyleLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatBadgeStyleLoader,\n      selectors: [[\"ng-component\"]],\n      decls: 0,\n      vars: 0,\n      template: function _MatBadgeStyleLoader_Template(rf, ctx) {},\n      styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color, var(--mat-sys-error));color:var(--mat-badge-text-color, var(--mat-sys-on-error));font-family:var(--mat-badge-text-font, var(--mat-sys-label-small-font));font-weight:var(--mat-badge-text-weight, var(--mat-sys-label-small-weight));border-radius:var(--mat-badge-container-shape, var(--mat-sys-corner-full))}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}@media(forced-colors: active){.mat-badge-content{outline:solid 1px;border-radius:0}}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-error) 38%, transparent));color:var(--mat-badge-disabled-state-text-color, var(--mat-sys-on-error))}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, 6px);min-height:var(--mat-badge-small-size-container-size, 6px);line-height:var(--mat-badge-small-size-line-height, 6px);padding:var(--mat-badge-small-size-container-padding, 0);font-size:var(--mat-badge-small-size-text-size, 0);margin:var(--mat-badge-small-size-container-offset, -6px 0)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset, -6px)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, 16px);min-height:var(--mat-badge-container-size, 16px);line-height:var(--mat-badge-line-height, 16px);padding:var(--mat-badge-container-padding, 0 4px);font-size:var(--mat-badge-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-container-offset, -12px 0)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset, -12px)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, 16px);min-height:var(--mat-badge-large-size-container-size, 16px);line-height:var(--mat-badge-large-size-line-height, 16px);padding:var(--mat-badge-large-size-container-padding, 0 4px);font-size:var(--mat-badge-large-size-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-large-size-container-offset, -12px 0)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset, -12px)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _MatBadgeStyleLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive to display a text badge. */\nlet MatBadge = /*#__PURE__*/(() => {\n  class MatBadge {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber);\n    _renderer = inject(Renderer2);\n    _animationsDisabled = _animationsDisabled();\n    _idGenerator = inject(_IdGenerator);\n    /**\n     * Theme color of the badge. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/badge/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      this._setColor(value);\n      this._color = value;\n    }\n    _color = 'primary';\n    /** Whether the badge should overlap its contents or not */\n    overlap = true;\n    /** Whether the badge is disabled. */\n    disabled;\n    /**\n     * Position the badge should reside.\n     * Accepts any combination of 'above'|'below' and 'before'|'after'\n     */\n    position = 'above after';\n    /** The content for the badge */\n    get content() {\n      return this._content;\n    }\n    set content(newContent) {\n      this._updateRenderedContent(newContent);\n    }\n    _content;\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n      return this._description;\n    }\n    set description(newDescription) {\n      this._updateDescription(newDescription);\n    }\n    _description;\n    /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n    size = 'medium';\n    /** Whether the badge is hidden. */\n    hidden;\n    /** Visible badge element. */\n    _badgeElement;\n    /** Inline badge description. Used when the badge is applied to non-interactive host elements. */\n    _inlineBadgeDescription;\n    /** Whether the OnInit lifecycle hook has run yet */\n    _isInitialized = false;\n    /** InteractivityChecker to determine if the badge host is focusable. */\n    _interactivityChecker = inject(InteractivityChecker);\n    _document = inject(DOCUMENT);\n    constructor() {\n      const styleLoader = inject(_CdkPrivateStyleLoader);\n      styleLoader.load(_MatBadgeStyleLoader);\n      styleLoader.load(_VisuallyHiddenLoader);\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        const nativeElement = this._elementRef.nativeElement;\n        if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n          throw Error('matBadge must be attached to an element node.');\n        }\n        const tagName = inject(HOST_TAG_NAME);\n        // Heads-up for developers to avoid putting matBadge on <mat-icon>\n        // as it is aria-hidden by default docs mention this at:\n        // https://material.angular.dev/components/badge/overview#accessibility\n        if (tagName.toLowerCase() === 'mat-icon' && nativeElement.getAttribute('aria-hidden') === 'true') {\n          console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` + `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` + `\\n${nativeElement.outerHTML}`);\n        }\n      }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n      return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n      return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n      return this._badgeElement;\n    }\n    ngOnInit() {\n      // We may have server-side rendered badge that we need to clear.\n      // We need to do this in ngOnInit because the full content of the component\n      // on which the badge is attached won't necessarily be in the DOM until this point.\n      this._clearExistingBadges();\n      if (this.content && !this._badgeElement) {\n        this._badgeElement = this._createBadgeElement();\n        this._updateRenderedContent(this.content);\n      }\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n      // We have to destroy it ourselves, otherwise it'll be retained in memory.\n      if (this._renderer.destroyNode) {\n        this._renderer.destroyNode(this._badgeElement);\n        this._inlineBadgeDescription?.remove();\n      }\n      this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n      // Ignore visibility since it requires an expensive style caluclation.\n      return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n        ignoreVisibility: true\n      });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n      const badgeElement = this._renderer.createElement('span');\n      const activeClass = 'mat-badge-active';\n      badgeElement.setAttribute('id', this._idGenerator.getId('mat-badge-content-'));\n      // The badge is aria-hidden because we don't want it to appear in the page's navigation\n      // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n      badgeElement.setAttribute('aria-hidden', 'true');\n      badgeElement.classList.add(BADGE_CONTENT_CLASS);\n      if (this._animationsDisabled) {\n        badgeElement.classList.add('_mat-animation-noopable');\n      }\n      this._elementRef.nativeElement.appendChild(badgeElement);\n      // animate in after insertion\n      if (typeof requestAnimationFrame === 'function' && !this._animationsDisabled) {\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => {\n            badgeElement.classList.add(activeClass);\n          });\n        });\n      } else {\n        badgeElement.classList.add(activeClass);\n      }\n      return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n      const newContentNormalized = `${newContent ?? ''}`.trim();\n      // Don't create the badge element if the directive isn't initialized because we want to\n      // append the badge element to the *end* of the host element's content for backwards\n      // compatibility.\n      if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n        this._badgeElement = this._createBadgeElement();\n      }\n      if (this._badgeElement) {\n        this._badgeElement.textContent = newContentNormalized;\n      }\n      this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n      // Always start by removing the aria-describedby; we will add a new one if necessary.\n      this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n      // NOTE: We only check whether the host is interactive here, which happens during\n      // when then badge content changes. It is possible that the host changes\n      // interactivity status separate from one of these. However, watching the interactivity\n      // status of the host would require a `MutationObserver`, which is likely more code + overhead\n      // than it's worth; from usages inside Google, we see that the vats majority of badges either\n      // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n      if (!newDescription || this._isHostInteractive()) {\n        this._removeInlineDescription();\n      }\n      this._description = newDescription;\n      // We don't add `aria-describedby` for non-interactive hosts elements because we\n      // instead insert the description inline.\n      if (this._isHostInteractive()) {\n        this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n      } else {\n        this._updateInlineDescription();\n      }\n    }\n    _updateInlineDescription() {\n      // Create the inline description element if it doesn't exist\n      if (!this._inlineBadgeDescription) {\n        this._inlineBadgeDescription = this._document.createElement('span');\n        this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n      }\n      this._inlineBadgeDescription.textContent = this.description;\n      this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n      this._inlineBadgeDescription?.remove();\n      this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(`mat-badge-${this._color}`);\n      if (colorPalette) {\n        classList.add(`mat-badge-${colorPalette}`);\n      }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n      // Only check direct children of this host element in order to avoid deleting\n      // any badges that might exist in descendant elements.\n      const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n      for (const badgeElement of Array.from(badges)) {\n        if (badgeElement !== this._badgeElement) {\n          badgeElement.remove();\n        }\n      }\n    }\n    static ɵfac = function MatBadge_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatBadge)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatBadge,\n      selectors: [[\"\", \"matBadge\", \"\"]],\n      hostAttrs: [1, \"mat-badge\"],\n      hostVars: 20,\n      hostBindings: function MatBadge_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-badge-overlap\", ctx.overlap)(\"mat-badge-above\", ctx.isAbove())(\"mat-badge-below\", !ctx.isAbove())(\"mat-badge-before\", !ctx.isAfter())(\"mat-badge-after\", ctx.isAfter())(\"mat-badge-small\", ctx.size === \"small\")(\"mat-badge-medium\", ctx.size === \"medium\")(\"mat-badge-large\", ctx.size === \"large\")(\"mat-badge-hidden\", ctx.hidden || !ctx.content)(\"mat-badge-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        color: [0, \"matBadgeColor\", \"color\"],\n        overlap: [2, \"matBadgeOverlap\", \"overlap\", booleanAttribute],\n        disabled: [2, \"matBadgeDisabled\", \"disabled\", booleanAttribute],\n        position: [0, \"matBadgePosition\", \"position\"],\n        content: [0, \"matBadge\", \"content\"],\n        description: [0, \"matBadgeDescription\", \"description\"],\n        size: [0, \"matBadgeSize\", \"size\"],\n        hidden: [2, \"matBadgeHidden\", \"hidden\", booleanAttribute]\n      }\n    });\n  }\n  return MatBadge;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatBadgeModule = /*#__PURE__*/(() => {\n  class MatBadgeModule {\n    static ɵfac = function MatBadgeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatBadgeModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatBadgeModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [A11yModule, MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatBadgeModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatBadge, MatBadgeModule };", "map": {"version": 3, "names": ["AriaDescriber", "_IdGenerator", "InteractivityChecker", "A11yModule", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "NgZone", "ElementRef", "Renderer2", "DOCUMENT", "HOST_TAG_NAME", "booleanAttribute", "Directive", "Input", "NgModule", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "_", "_animationsDisabled", "M", "MatCommonModule", "BADGE_CONTENT_CLASS", "_MatBadgeStyleLoader", "ɵfac", "_MatBadgeStyleLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "template", "_MatBadgeStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "MatBadge", "_ngZone", "_elementRef", "_ariaDescriber", "_renderer", "_idGenerator", "color", "_color", "value", "_setColor", "overlap", "disabled", "position", "content", "_content", "newContent", "_updateR<PERSON><PERSON><PERSON><PERSON>nt", "description", "_description", "newDescription", "_updateDescription", "size", "hidden", "_badgeElement", "_inlineBadgeDescription", "_isInitialized", "_interactivityC<PERSON>cker", "_document", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "load", "nativeElement", "nodeType", "ELEMENT_NODE", "Error", "tagName", "toLowerCase", "getAttribute", "console", "warn", "outerHTML", "isAbove", "indexOf", "isAfter", "getBadgeElement", "ngOnInit", "_clearExistingBadges", "_createBadgeElement", "ngOnDestroy", "destroyNode", "remove", "removeDescription", "_isHostInteractive", "isFocusable", "ignoreVisibility", "badgeElement", "createElement", "activeClass", "setAttribute", "getId", "classList", "add", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "runOutsideAngular", "newContentNormalized", "trim", "textContent", "_removeInlineDescription", "describe", "_updateInlineDescription", "undefined", "colorPalette", "badges", "querySelectorAll", "Array", "from", "MatBadge_Factory", "ɵdir", "ɵɵdefineDirective", "hostAttrs", "hostVars", "hostBindings", "MatBadge_HostBindings", "ɵɵclassProp", "inputs", "MatBadgeModule", "MatBadgeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/badge.mjs"], "sourcesContent": ["import { AriaDescriber, _IdGenerator, Interactivity<PERSON><PERSON><PERSON>, A11yModule } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, NgZone, ElementRef, Renderer2, DOCUMENT, HOST_TAG_NAME, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nclass _MatBadgeStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatBadgeStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _MatBadgeStyleLoader, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: '', isInline: true, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color, var(--mat-sys-error));color:var(--mat-badge-text-color, var(--mat-sys-on-error));font-family:var(--mat-badge-text-font, var(--mat-sys-label-small-font));font-weight:var(--mat-badge-text-weight, var(--mat-sys-label-small-weight));border-radius:var(--mat-badge-container-shape, var(--mat-sys-corner-full))}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}@media(forced-colors: active){.mat-badge-content{outline:solid 1px;border-radius:0}}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-error) 38%, transparent));color:var(--mat-badge-disabled-state-text-color, var(--mat-sys-on-error))}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, 6px);min-height:var(--mat-badge-small-size-container-size, 6px);line-height:var(--mat-badge-small-size-line-height, 6px);padding:var(--mat-badge-small-size-container-padding, 0);font-size:var(--mat-badge-small-size-text-size, 0);margin:var(--mat-badge-small-size-container-offset, -6px 0)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset, -6px)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, 16px);min-height:var(--mat-badge-container-size, 16px);line-height:var(--mat-badge-line-height, 16px);padding:var(--mat-badge-container-padding, 0 4px);font-size:var(--mat-badge-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-container-offset, -12px 0)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset, -12px)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, 16px);min-height:var(--mat-badge-large-size-container-size, 16px);line-height:var(--mat-badge-large-size-line-height, 16px);padding:var(--mat-badge-large-size-container-padding, 0 4px);font-size:var(--mat-badge-large-size-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-large-size-container-offset, -12px 0)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset, -12px)}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatBadgeStyleLoader, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color, var(--mat-sys-error));color:var(--mat-badge-text-color, var(--mat-sys-on-error));font-family:var(--mat-badge-text-font, var(--mat-sys-label-small-font));font-weight:var(--mat-badge-text-weight, var(--mat-sys-label-small-weight));border-radius:var(--mat-badge-container-shape, var(--mat-sys-corner-full))}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}@media(forced-colors: active){.mat-badge-content{outline:solid 1px;border-radius:0}}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-error) 38%, transparent));color:var(--mat-badge-disabled-state-text-color, var(--mat-sys-on-error))}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, 6px);min-height:var(--mat-badge-small-size-container-size, 6px);line-height:var(--mat-badge-small-size-line-height, 6px);padding:var(--mat-badge-small-size-container-padding, 0);font-size:var(--mat-badge-small-size-text-size, 0);margin:var(--mat-badge-small-size-container-offset, -6px 0)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset, -6px)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, 16px);min-height:var(--mat-badge-container-size, 16px);line-height:var(--mat-badge-line-height, 16px);padding:var(--mat-badge-container-padding, 0 4px);font-size:var(--mat-badge-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-container-offset, -12px 0)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset, -12px)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, 16px);min-height:var(--mat-badge-large-size-container-size, 16px);line-height:var(--mat-badge-large-size-line-height, 16px);padding:var(--mat-badge-large-size-container-padding, 0 4px);font-size:var(--mat-badge-large-size-text-size, var(--mat-sys-label-small-size));margin:var(--mat-badge-large-size-container-offset, -12px 0)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset, -12px)}\\n\"] }]\n        }] });\n/** Directive to display a text badge. */\nclass MatBadge {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber);\n    _renderer = inject(Renderer2);\n    _animationsDisabled = _animationsDisabled();\n    _idGenerator = inject(_IdGenerator);\n    /**\n     * Theme color of the badge. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/badge/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color;\n    }\n    set color(value) {\n        this._setColor(value);\n        this._color = value;\n    }\n    _color = 'primary';\n    /** Whether the badge should overlap its contents or not */\n    overlap = true;\n    /** Whether the badge is disabled. */\n    disabled;\n    /**\n     * Position the badge should reside.\n     * Accepts any combination of 'above'|'below' and 'before'|'after'\n     */\n    position = 'above after';\n    /** The content for the badge */\n    get content() {\n        return this._content;\n    }\n    set content(newContent) {\n        this._updateRenderedContent(newContent);\n    }\n    _content;\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n        return this._description;\n    }\n    set description(newDescription) {\n        this._updateDescription(newDescription);\n    }\n    _description;\n    /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n    size = 'medium';\n    /** Whether the badge is hidden. */\n    hidden;\n    /** Visible badge element. */\n    _badgeElement;\n    /** Inline badge description. Used when the badge is applied to non-interactive host elements. */\n    _inlineBadgeDescription;\n    /** Whether the OnInit lifecycle hook has run yet */\n    _isInitialized = false;\n    /** InteractivityChecker to determine if the badge host is focusable. */\n    _interactivityChecker = inject(InteractivityChecker);\n    _document = inject(DOCUMENT);\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_MatBadgeStyleLoader);\n        styleLoader.load(_VisuallyHiddenLoader);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const nativeElement = this._elementRef.nativeElement;\n            if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n                throw Error('matBadge must be attached to an element node.');\n            }\n            const tagName = inject(HOST_TAG_NAME);\n            // Heads-up for developers to avoid putting matBadge on <mat-icon>\n            // as it is aria-hidden by default docs mention this at:\n            // https://material.angular.dev/components/badge/overview#accessibility\n            if (tagName.toLowerCase() === 'mat-icon' &&\n                nativeElement.getAttribute('aria-hidden') === 'true') {\n                console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` +\n                    `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` +\n                    `\\n${nativeElement.outerHTML}`);\n            }\n        }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n        return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n        return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n        return this._badgeElement;\n    }\n    ngOnInit() {\n        // We may have server-side rendered badge that we need to clear.\n        // We need to do this in ngOnInit because the full content of the component\n        // on which the badge is attached won't necessarily be in the DOM until this point.\n        this._clearExistingBadges();\n        if (this.content && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n            this._updateRenderedContent(this.content);\n        }\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n        // We have to destroy it ourselves, otherwise it'll be retained in memory.\n        if (this._renderer.destroyNode) {\n            this._renderer.destroyNode(this._badgeElement);\n            this._inlineBadgeDescription?.remove();\n        }\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n        // Ignore visibility since it requires an expensive style caluclation.\n        return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n            ignoreVisibility: true,\n        });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n        const badgeElement = this._renderer.createElement('span');\n        const activeClass = 'mat-badge-active';\n        badgeElement.setAttribute('id', this._idGenerator.getId('mat-badge-content-'));\n        // The badge is aria-hidden because we don't want it to appear in the page's navigation\n        // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n        badgeElement.setAttribute('aria-hidden', 'true');\n        badgeElement.classList.add(BADGE_CONTENT_CLASS);\n        if (this._animationsDisabled) {\n            badgeElement.classList.add('_mat-animation-noopable');\n        }\n        this._elementRef.nativeElement.appendChild(badgeElement);\n        // animate in after insertion\n        if (typeof requestAnimationFrame === 'function' && !this._animationsDisabled) {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    badgeElement.classList.add(activeClass);\n                });\n            });\n        }\n        else {\n            badgeElement.classList.add(activeClass);\n        }\n        return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n        const newContentNormalized = `${newContent ?? ''}`.trim();\n        // Don't create the badge element if the directive isn't initialized because we want to\n        // append the badge element to the *end* of the host element's content for backwards\n        // compatibility.\n        if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n        }\n        if (this._badgeElement) {\n            this._badgeElement.textContent = newContentNormalized;\n        }\n        this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n        // Always start by removing the aria-describedby; we will add a new one if necessary.\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n        // NOTE: We only check whether the host is interactive here, which happens during\n        // when then badge content changes. It is possible that the host changes\n        // interactivity status separate from one of these. However, watching the interactivity\n        // status of the host would require a `MutationObserver`, which is likely more code + overhead\n        // than it's worth; from usages inside Google, we see that the vats majority of badges either\n        // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n        if (!newDescription || this._isHostInteractive()) {\n            this._removeInlineDescription();\n        }\n        this._description = newDescription;\n        // We don't add `aria-describedby` for non-interactive hosts elements because we\n        // instead insert the description inline.\n        if (this._isHostInteractive()) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n        }\n        else {\n            this._updateInlineDescription();\n        }\n    }\n    _updateInlineDescription() {\n        // Create the inline description element if it doesn't exist\n        if (!this._inlineBadgeDescription) {\n            this._inlineBadgeDescription = this._document.createElement('span');\n            this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n        }\n        this._inlineBadgeDescription.textContent = this.description;\n        this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n        this._inlineBadgeDescription?.remove();\n        this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(`mat-badge-${this._color}`);\n        if (colorPalette) {\n            classList.add(`mat-badge-${colorPalette}`);\n        }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n        // Only check direct children of this host element in order to avoid deleting\n        // any badges that might exist in descendant elements.\n        const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n        for (const badgeElement of Array.from(badges)) {\n            if (badgeElement !== this._badgeElement) {\n                badgeElement.remove();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadge, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatBadge, isStandalone: true, selector: \"[matBadge]\", inputs: { color: [\"matBadgeColor\", \"color\"], overlap: [\"matBadgeOverlap\", \"overlap\", booleanAttribute], disabled: [\"matBadgeDisabled\", \"disabled\", booleanAttribute], position: [\"matBadgePosition\", \"position\"], content: [\"matBadge\", \"content\"], description: [\"matBadgeDescription\", \"description\"], size: [\"matBadgeSize\", \"size\"], hidden: [\"matBadgeHidden\", \"hidden\", booleanAttribute] }, host: { properties: { \"class.mat-badge-overlap\": \"overlap\", \"class.mat-badge-above\": \"isAbove()\", \"class.mat-badge-below\": \"!isAbove()\", \"class.mat-badge-before\": \"!isAfter()\", \"class.mat-badge-after\": \"isAfter()\", \"class.mat-badge-small\": \"size === \\\"small\\\"\", \"class.mat-badge-medium\": \"size === \\\"medium\\\"\", \"class.mat-badge-large\": \"size === \\\"large\\\"\", \"class.mat-badge-hidden\": \"hidden || !content\", \"class.mat-badge-disabled\": \"disabled\" }, classAttribute: \"mat-badge\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadge, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matBadge]',\n                    host: {\n                        'class': 'mat-badge',\n                        '[class.mat-badge-overlap]': 'overlap',\n                        '[class.mat-badge-above]': 'isAbove()',\n                        '[class.mat-badge-below]': '!isAbove()',\n                        '[class.mat-badge-before]': '!isAfter()',\n                        '[class.mat-badge-after]': 'isAfter()',\n                        '[class.mat-badge-small]': 'size === \"small\"',\n                        '[class.mat-badge-medium]': 'size === \"medium\"',\n                        '[class.mat-badge-large]': 'size === \"large\"',\n                        '[class.mat-badge-hidden]': 'hidden || !content',\n                        '[class.mat-badge-disabled]': 'disabled',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input,\n                args: ['matBadgeColor']\n            }], overlap: [{\n                type: Input,\n                args: [{ alias: 'matBadgeOverlap', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matBadgeDisabled', transform: booleanAttribute }]\n            }], position: [{\n                type: Input,\n                args: ['matBadgePosition']\n            }], content: [{\n                type: Input,\n                args: ['matBadge']\n            }], description: [{\n                type: Input,\n                args: ['matBadgeDescription']\n            }], size: [{\n                type: Input,\n                args: ['matBadgeSize']\n            }], hidden: [{\n                type: Input,\n                args: [{ alias: 'matBadgeHidden', transform: booleanAttribute }]\n            }] } });\n\nclass MatBadgeModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadgeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader], exports: [MatBadge, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatBadgeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n                    // but it seems to be necessary for tests.\n                    imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n                    exports: [MatBadge, MatCommonModule],\n                }]\n        }] });\n\nexport { MatBadge, MatBadgeModule };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,UAAU,QAAQ,mBAAmB;AACjG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACnM,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACpF,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;AAE1B,MAAMC,mBAAmB,GAAG,mBAAmB;AAC/C;AACA;AACA;AACA;AAHA,IAIMC,oBAAoB;EAA1B,MAAMA,oBAAoB,CAAC;IACvB,OAAOC,IAAI,YAAAC,6BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,oBAAoB;IAAA;IACvH,OAAOI,IAAI,kBAD8EzB,EAAE,CAAA0B,iBAAA;MAAAC,IAAA,EACJN,oBAAoB;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC/G;EAAC,OAHKhB,oBAAoB;AAAA;AAI1B;EAAA,QAAAiB,SAAA,oBAAAA,SAAA;AAAA;AAIA;AAAA,IACMC,QAAQ;EAAd,MAAMA,QAAQ,CAAC;IACXC,OAAO,GAAGpC,MAAM,CAACC,MAAM,CAAC;IACxBoC,WAAW,GAAGrC,MAAM,CAACE,UAAU,CAAC;IAChCoC,cAAc,GAAGtC,MAAM,CAACR,aAAa,CAAC;IACtC+C,SAAS,GAAGvC,MAAM,CAACG,SAAS,CAAC;IAC7BU,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C2B,YAAY,GAAGxC,MAAM,CAACP,YAAY,CAAC;IACnC;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIgD,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM;IACtB;IACA,IAAID,KAAKA,CAACE,KAAK,EAAE;MACb,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;MACrB,IAAI,CAACD,MAAM,GAAGC,KAAK;IACvB;IACAD,MAAM,GAAG,SAAS;IAClB;IACAG,OAAO,GAAG,IAAI;IACd;IACAC,QAAQ;IACR;AACJ;AACA;AACA;IACIC,QAAQ,GAAG,aAAa;IACxB;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACC,QAAQ;IACxB;IACA,IAAID,OAAOA,CAACE,UAAU,EAAE;MACpB,IAAI,CAACC,sBAAsB,CAACD,UAAU,CAAC;IAC3C;IACAD,QAAQ;IACR;IACA,IAAIG,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,YAAY;IAC5B;IACA,IAAID,WAAWA,CAACE,cAAc,EAAE;MAC5B,IAAI,CAACC,kBAAkB,CAACD,cAAc,CAAC;IAC3C;IACAD,YAAY;IACZ;IACAG,IAAI,GAAG,QAAQ;IACf;IACAC,MAAM;IACN;IACAC,aAAa;IACb;IACAC,uBAAuB;IACvB;IACAC,cAAc,GAAG,KAAK;IACtB;IACAC,qBAAqB,GAAG7D,MAAM,CAACN,oBAAoB,CAAC;IACpDoE,SAAS,GAAG9D,MAAM,CAACI,QAAQ,CAAC;IAC5B2D,WAAWA,CAAA,EAAG;MACV,MAAMC,WAAW,GAAGhE,MAAM,CAACU,sBAAsB,CAAC;MAClDsD,WAAW,CAACC,IAAI,CAAChD,oBAAoB,CAAC;MACtC+C,WAAW,CAACC,IAAI,CAACtD,qBAAqB,CAAC;MACvC,IAAI,OAAOuB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,MAAMgC,aAAa,GAAG,IAAI,CAAC7B,WAAW,CAAC6B,aAAa;QACpD,IAAIA,aAAa,CAACC,QAAQ,KAAKD,aAAa,CAACE,YAAY,EAAE;UACvD,MAAMC,KAAK,CAAC,+CAA+C,CAAC;QAChE;QACA,MAAMC,OAAO,GAAGtE,MAAM,CAACK,aAAa,CAAC;QACrC;QACA;QACA;QACA,IAAIiE,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,UAAU,IACpCL,aAAa,CAACM,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;UACtDC,OAAO,CAACC,IAAI,CAAC,wDAAwD,GACjE,gGAAgG,GAChG,KAAKR,aAAa,CAACS,SAAS,EAAE,CAAC;QACvC;MACJ;IACJ;IACA;IACAC,OAAOA,CAAA,EAAG;MACN,OAAO,IAAI,CAAC7B,QAAQ,CAAC8B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChD;IACA;IACAC,OAAOA,CAAA,EAAG;MACN,OAAO,IAAI,CAAC/B,QAAQ,CAAC8B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACjD;IACA;AACJ;AACA;AACA;IACIE,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACrB,aAAa;IAC7B;IACAsB,QAAQA,CAAA,EAAG;MACP;MACA;MACA;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,IAAI,CAACjC,OAAO,IAAI,CAAC,IAAI,CAACU,aAAa,EAAE;QACrC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACwB,mBAAmB,CAAC,CAAC;QAC/C,IAAI,CAAC/B,sBAAsB,CAAC,IAAI,CAACH,OAAO,CAAC;MAC7C;MACA,IAAI,CAACY,cAAc,GAAG,IAAI;IAC9B;IACAuB,WAAWA,CAAA,EAAG;MACV;MACA;MACA,IAAI,IAAI,CAAC5C,SAAS,CAAC6C,WAAW,EAAE;QAC5B,IAAI,CAAC7C,SAAS,CAAC6C,WAAW,CAAC,IAAI,CAAC1B,aAAa,CAAC;QAC9C,IAAI,CAACC,uBAAuB,EAAE0B,MAAM,CAAC,CAAC;MAC1C;MACA,IAAI,CAAC/C,cAAc,CAACgD,iBAAiB,CAAC,IAAI,CAACjD,WAAW,CAAC6B,aAAa,EAAE,IAAI,CAACd,WAAW,CAAC;IAC3F;IACA;IACAmC,kBAAkBA,CAAA,EAAG;MACjB;MACA,OAAO,IAAI,CAAC1B,qBAAqB,CAAC2B,WAAW,CAAC,IAAI,CAACnD,WAAW,CAAC6B,aAAa,EAAE;QAC1EuB,gBAAgB,EAAE;MACtB,CAAC,CAAC;IACN;IACA;IACAP,mBAAmBA,CAAA,EAAG;MAClB,MAAMQ,YAAY,GAAG,IAAI,CAACnD,SAAS,CAACoD,aAAa,CAAC,MAAM,CAAC;MACzD,MAAMC,WAAW,GAAG,kBAAkB;MACtCF,YAAY,CAACG,YAAY,CAAC,IAAI,EAAE,IAAI,CAACrD,YAAY,CAACsD,KAAK,CAAC,oBAAoB,CAAC,CAAC;MAC9E;MACA;MACAJ,YAAY,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAChDH,YAAY,CAACK,SAAS,CAACC,GAAG,CAAChF,mBAAmB,CAAC;MAC/C,IAAI,IAAI,CAACH,mBAAmB,EAAE;QAC1B6E,YAAY,CAACK,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACzD;MACA,IAAI,CAAC3D,WAAW,CAAC6B,aAAa,CAAC+B,WAAW,CAACP,YAAY,CAAC;MACxD;MACA,IAAI,OAAOQ,qBAAqB,KAAK,UAAU,IAAI,CAAC,IAAI,CAACrF,mBAAmB,EAAE;QAC1E,IAAI,CAACuB,OAAO,CAAC+D,iBAAiB,CAAC,MAAM;UACjCD,qBAAqB,CAAC,MAAM;YACxBR,YAAY,CAACK,SAAS,CAACC,GAAG,CAACJ,WAAW,CAAC;UAC3C,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACDF,YAAY,CAACK,SAAS,CAACC,GAAG,CAACJ,WAAW,CAAC;MAC3C;MACA,OAAOF,YAAY;IACvB;IACA;IACAvC,sBAAsBA,CAACD,UAAU,EAAE;MAC/B,MAAMkD,oBAAoB,GAAG,GAAGlD,UAAU,IAAI,EAAE,EAAE,CAACmD,IAAI,CAAC,CAAC;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAACzC,cAAc,IAAIwC,oBAAoB,IAAI,CAAC,IAAI,CAAC1C,aAAa,EAAE;QACpE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACwB,mBAAmB,CAAC,CAAC;MACnD;MACA,IAAI,IAAI,CAACxB,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC4C,WAAW,GAAGF,oBAAoB;MACzD;MACA,IAAI,CAACnD,QAAQ,GAAGmD,oBAAoB;IACxC;IACA;IACA7C,kBAAkBA,CAACD,cAAc,EAAE;MAC/B;MACA,IAAI,CAAChB,cAAc,CAACgD,iBAAiB,CAAC,IAAI,CAACjD,WAAW,CAAC6B,aAAa,EAAE,IAAI,CAACd,WAAW,CAAC;MACvF;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACE,cAAc,IAAI,IAAI,CAACiC,kBAAkB,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACgB,wBAAwB,CAAC,CAAC;MACnC;MACA,IAAI,CAAClD,YAAY,GAAGC,cAAc;MAClC;MACA;MACA,IAAI,IAAI,CAACiC,kBAAkB,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACjD,cAAc,CAACkE,QAAQ,CAAC,IAAI,CAACnE,WAAW,CAAC6B,aAAa,EAAEZ,cAAc,CAAC;MAChF,CAAC,MACI;QACD,IAAI,CAACmD,wBAAwB,CAAC,CAAC;MACnC;IACJ;IACAA,wBAAwBA,CAAA,EAAG;MACvB;MACA,IAAI,CAAC,IAAI,CAAC9C,uBAAuB,EAAE;QAC/B,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACG,SAAS,CAAC6B,aAAa,CAAC,MAAM,CAAC;QACnE,IAAI,CAAChC,uBAAuB,CAACoC,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACrE;MACA,IAAI,CAACrC,uBAAuB,CAAC2C,WAAW,GAAG,IAAI,CAAClD,WAAW;MAC3D,IAAI,CAACM,aAAa,EAAEuC,WAAW,CAAC,IAAI,CAACtC,uBAAuB,CAAC;IACjE;IACA4C,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC5C,uBAAuB,EAAE0B,MAAM,CAAC,CAAC;MACtC,IAAI,CAAC1B,uBAAuB,GAAG+C,SAAS;IAC5C;IACA;IACA9D,SAASA,CAAC+D,YAAY,EAAE;MACpB,MAAMZ,SAAS,GAAG,IAAI,CAAC1D,WAAW,CAAC6B,aAAa,CAAC6B,SAAS;MAC1DA,SAAS,CAACV,MAAM,CAAC,aAAa,IAAI,CAAC3C,MAAM,EAAE,CAAC;MAC5C,IAAIiE,YAAY,EAAE;QACdZ,SAAS,CAACC,GAAG,CAAC,aAAaW,YAAY,EAAE,CAAC;MAC9C;IACJ;IACA;IACA1B,oBAAoBA,CAAA,EAAG;MACnB;MACA;MACA,MAAM2B,MAAM,GAAG,IAAI,CAACvE,WAAW,CAAC6B,aAAa,CAAC2C,gBAAgB,CAAC,aAAa7F,mBAAmB,EAAE,CAAC;MAClG,KAAK,MAAM0E,YAAY,IAAIoB,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;QAC3C,IAAIlB,YAAY,KAAK,IAAI,CAAChC,aAAa,EAAE;UACrCgC,YAAY,CAACL,MAAM,CAAC,CAAC;QACzB;MACJ;IACJ;IACA,OAAOnE,IAAI,YAAA8F,iBAAA5F,iBAAA;MAAA,YAAAA,iBAAA,IAAwFe,QAAQ;IAAA;IAC3G,OAAO8E,IAAI,kBAnO8ErH,EAAE,CAAAsH,iBAAA;MAAA3F,IAAA,EAmOJY,QAAQ;MAAAX,SAAA;MAAA2F,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAzF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnONjC,EAAE,CAAA2H,WAAA,sBAAAzF,GAAA,CAAAe,OAmOG,CAAC,oBAARf,GAAA,CAAA8C,OAAA,CAAQ,CAAD,CAAC,qBAAP9C,GAAA,CAAA8C,OAAA,CAAQ,CAAF,CAAC,sBAAP9C,GAAA,CAAAgD,OAAA,CAAQ,CAAF,CAAC,oBAARhD,GAAA,CAAAgD,OAAA,CAAQ,CAAD,CAAC,oBAAAhD,GAAA,CAAA0B,IAAA,KAAC,OAAF,CAAC,qBAAA1B,GAAA,CAAA0B,IAAA,KAAC,QAAF,CAAC,oBAAA1B,GAAA,CAAA0B,IAAA,KAAC,OAAF,CAAC,qBAAA1B,GAAA,CAAA2B,MAAA,KAAA3B,GAAA,CAAAkB,OAAD,CAAC,uBAAAlB,GAAA,CAAAgB,QAAD,CAAC;QAAA;MAAA;MAAA0E,MAAA;QAAA/E,KAAA;QAAAI,OAAA,oCAAmIvC,gBAAgB;QAAAwC,QAAA,sCAA8CxC,gBAAgB;QAAAyC,QAAA;QAAAC,OAAA;QAAAI,WAAA;QAAAI,IAAA;QAAAC,MAAA,kCAA2MnD,gBAAgB;MAAA;IAAA;EAC/gB;EAAC,OA5NK6B,QAAQ;AAAA;AA6Nd;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AA0CoB,IAEduF,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB,OAAOvG,IAAI,YAAAwG,uBAAAtG,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqG,cAAc;IAAA;IACjH,OAAOE,IAAI,kBAnR8E/H,EAAE,CAAAgI,gBAAA;MAAArG,IAAA,EAmRSkG;IAAc;IAClH,OAAOI,IAAI,kBApR8EjI,EAAE,CAAAkI,gBAAA;MAAAC,OAAA,GAoRmCpI,UAAU,EAAEoB,eAAe,EAAEA,eAAe;IAAA;EAC9K;EAAC,OAJK0G,cAAc;AAAA;AAKpB;EAAA,QAAAvF,SAAA,oBAAAA,SAAA;AAAA;AAUA,SAASC,QAAQ,EAAEsF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}