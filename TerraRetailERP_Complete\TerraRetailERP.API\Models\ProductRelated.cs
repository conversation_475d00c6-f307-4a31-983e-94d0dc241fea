using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("ProductStocks")]
    public class ProductStock
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }
        public int BranchId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal AvailableQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReservedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OnOrderQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OpeningQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalInQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalOutQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageCostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LastCostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal StockValue { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchMinStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchMaxStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BranchReorderPoint { get; set; }

        [StringLength(200)]
        public string? StorageLocation { get; set; }

        [StringLength(40)]
        public string? ShelfNumber { get; set; }

        [StringLength(1000)]
        public string? StockNotes { get; set; }

        public bool IsAvailableForSale { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;
    }

    [Table("ProductBatches")]
    public class ProductBatch
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }
        public int BranchId { get; set; }

        [Required]
        [StringLength(100)]
        public string BatchNumber { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal RemainingQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }
        public int? PurchaseId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("PurchaseId")]
        public virtual PurchaseInvoice? PurchaseInvoice { get; set; }
    }

    [Table("ProductImages")]
    public class ProductImage
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        [Required]
        [StringLength(1000)]
        public string ImagePath { get; set; } = string.Empty;

        [StringLength(400)]
        public string? ImageName { get; set; }

        public bool IsMain { get; set; } = false;

        public int DisplayOrder { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }

    [Table("ProductAlternativeCodes")]
    public class ProductAlternativeCode
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }

        [Required]
        [StringLength(100)]
        public string AlternativeCode { get; set; } = string.Empty;

        [StringLength(40)]
        public string? CodeType { get; set; }

        [StringLength(400)]
        public string? Description { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }

    [Table("ProductBranchPrices")]
    public class ProductBranchPrice
    {
        [Key]
        public int Id { get; set; }

        public int ProductId { get; set; }
        public int BranchId { get; set; }
        public int PriceCategoryId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("PriceCategoryId")]
        public virtual PriceCategory PriceCategory { get; set; } = null!;
    }

    [Table("ProductSuppliers")]
    public class ProductSupplier
    {
        [Key]
        public long Id { get; set; }

        public int ProductId { get; set; }
        public int SupplierId { get; set; }

        [StringLength(100)]
        public string? SupplierProductCode { get; set; }

        [StringLength(400)]
        public string? SupplierProductName { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MinOrderQuantity { get; set; }

        public int? LeadTimeDays { get; set; }

        public bool IsPreferred { get; set; } = false;
        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [StringLength(200)]
        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? UpdatedAt { get; set; }

        [StringLength(200)]
        public string? UpdatedBy { get; set; }

        public bool IsDeleted { get; set; } = false;

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;
    }

    [Table("PriceCategories")]
    public class PriceCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        [StringLength(400)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal PriceAdjustmentPercentage { get; set; } = 0;

        public bool IsDefault { get; set; } = false;
        public bool IsActive { get; set; } = true;

        public int DisplayOrder { get; set; } = 1;

        [StringLength(14)]
        public string? Color { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MinimumQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaximumQuantity { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<ProductBranchPrice> ProductBranchPrices { get; set; } = new List<ProductBranchPrice>();
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
    }
}
