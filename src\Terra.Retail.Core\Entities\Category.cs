using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// فئات المنتجات (هرمية)
    /// </summary>
    public class Category : BaseEntity
    {
        /// <summary>
        /// اسم الفئة بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الفئة بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود الفئة
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// الفئة الأب
        /// </summary>
        public int? ParentCategoryId { get; set; }

        /// <summary>
        /// مستوى الفئة في الهرم (1-5)
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// مسار الفئة الهرمي (مثل: 1/2/3)
        /// </summary>
        [MaxLength(100)]
        public string? Path { get; set; }

        /// <summary>
        /// وصف الفئة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// صورة الفئة
        /// </summary>
        [MaxLength(500)]
        public string? Image { get; set; }

        /// <summary>
        /// أيقونة الفئة
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// لون الفئة (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// هل الفئة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الفئة مميزة
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// عدد المنتجات في الفئة
        /// </summary>
        public int ProductCount { get; set; } = 0;

        /// <summary>
        /// كلمات مفتاحية للبحث
        /// </summary>
        [MaxLength(500)]
        public string? Keywords { get; set; }

        /// <summary>
        /// معلومات SEO - العنوان
        /// </summary>
        [MaxLength(200)]
        public string? SeoTitle { get; set; }

        /// <summary>
        /// معلومات SEO - الوصف
        /// </summary>
        [MaxLength(500)]
        public string? SeoDescription { get; set; }

        /// <summary>
        /// معلومات SEO - الكلمات المفتاحية
        /// </summary>
        [MaxLength(500)]
        public string? SeoKeywords { get; set; }

        // Navigation Properties
        public virtual Category? ParentCategory { get; set; }
        public virtual ICollection<Category> SubCategories { get; set; } = new List<Category>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
