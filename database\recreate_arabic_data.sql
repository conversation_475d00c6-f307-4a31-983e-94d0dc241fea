-- حذف وإعادة إنشاء جميع البيانات العربية بترميز صحيح
-- Delete and Recreate All Arabic Data with Correct Encoding

USE TerraRetailERP;
GO

PRINT N'بدء حذف وإعادة إنشاء البيانات العربية...';

-- حذف جميع البيانات المرتبطة
DELETE FROM Customers;
DELETE FROM Products;
DELETE FROM Suppliers;
DELETE FROM Employees;
DELETE FROM Users;

-- حذف البيانات الأساسية
DELETE FROM Areas;
DELETE FROM Branches WHERE Id > 1; -- الاحتفاظ بالفرع الرئيسي
DELETE FROM CustomerTypes;
DELETE FROM Categories;
DELETE FROM Units;
DELETE FROM PriceCategories;
DELETE FROM PaymentMethods;
DELETE FROM SupplierTypes;

-- إعادة تعيين العدادات
DBCC CHECKIDENT ('Areas', RESEED, 0);
DBCC CHECKIDENT ('CustomerTypes', RESEED, 0);
DBCC CHECKIDENT ('Categories', RESEED, 0);
DBCC CHECKIDENT ('Units', RESEED, 0);
DBCC CHECKIDENT ('PriceCategories', RESEED, 0);
DBCC CHECKIDENT ('PaymentMethods', RESEED, 0);
DBCC CHECKIDENT ('SupplierTypes', RESEED, 0);

PRINT N'تم حذف البيانات القديمة';

-- إنشاء المحافظات المصرية بترميز صحيح
INSERT INTO Areas (NameAr, NameEn, Code, IsActive, DisplayOrder, CreatedAt) VALUES
(N'القاهرة', 'Cairo', 'CAI', 1, 1, GETUTCDATE()),
(N'الجيزة', 'Giza', 'GIZ', 1, 2, GETUTCDATE()),
(N'الإسكندرية', 'Alexandria', 'ALX', 1, 3, GETUTCDATE()),
(N'الدقهلية', 'Dakahlia', 'DAK', 1, 4, GETUTCDATE()),
(N'الشرقية', 'Sharqia', 'SHR', 1, 5, GETUTCDATE()),
(N'القليوبية', 'Qalyubia', 'QLY', 1, 6, GETUTCDATE()),
(N'كفر الشيخ', 'Kafr El Sheikh', 'KFS', 1, 7, GETUTCDATE()),
(N'الغربية', 'Gharbia', 'GHR', 1, 8, GETUTCDATE()),
(N'المنوفية', 'Monufia', 'MNF', 1, 9, GETUTCDATE()),
(N'البحيرة', 'Beheira', 'BHR', 1, 10, GETUTCDATE()),
(N'الإسماعيلية', 'Ismailia', 'ISM', 1, 11, GETUTCDATE()),
(N'بورسعيد', 'Port Said', 'PTS', 1, 12, GETUTCDATE()),
(N'السويس', 'Suez', 'SUZ', 1, 13, GETUTCDATE()),
(N'شمال سيناء', 'North Sinai', 'NSI', 1, 14, GETUTCDATE()),
(N'جنوب سيناء', 'South Sinai', 'SSI', 1, 15, GETUTCDATE()),
(N'الفيوم', 'Fayoum', 'FYM', 1, 16, GETUTCDATE()),
(N'بني سويف', 'Beni Suef', 'BSF', 1, 17, GETUTCDATE()),
(N'المنيا', 'Minya', 'MNY', 1, 18, GETUTCDATE()),
(N'أسيوط', 'Assiut', 'AST', 1, 19, GETUTCDATE()),
(N'سوهاج', 'Sohag', 'SOH', 1, 20, GETUTCDATE()),
(N'قنا', 'Qena', 'QNA', 1, 21, GETUTCDATE()),
(N'الأقصر', 'Luxor', 'LXR', 1, 22, GETUTCDATE()),
(N'أسوان', 'Aswan', 'ASN', 1, 23, GETUTCDATE()),
(N'البحر الأحمر', 'Red Sea', 'RDS', 1, 24, GETUTCDATE()),
(N'الوادي الجديد', 'New Valley', 'NVL', 1, 25, GETUTCDATE()),
(N'مطروح', 'Matrouh', 'MTR', 1, 26, GETUTCDATE());

PRINT N'تم إنشاء المحافظات المصرية';

-- إنشاء الفروع المصرية
UPDATE Branches SET NameAr = N'الفرع الرئيسي - القاهرة', Address = N'شارع التحرير، وسط البلد، القاهرة' WHERE Id = 1;

INSERT INTO Branches (NameAr, NameEn, Code, Address, Phone, IsMainBranch, IsActive, CreatedAt) VALUES
(N'فرع الإسكندرية', 'Alexandria Branch', 'ALX', N'شارع الكورنيش، الإسكندرية', '03-4567890', 0, 1, GETUTCDATE()),
(N'فرع الجيزة', 'Giza Branch', 'GIZ', N'شارع الهرم، الجيزة', '02-3456789', 0, 1, GETUTCDATE()),
(N'فرع المنصورة', 'Mansoura Branch', 'MAN', N'شارع الجمهورية، المنصورة', '050-2345678', 0, 1, GETUTCDATE()),
(N'فرع أسيوط', 'Assiut Branch', 'AST', N'شارع الثورة، أسيوط', '088-2345678', 0, 1, GETUTCDATE()),
(N'فرع طنطا', 'Tanta Branch', 'TNT', N'شارع الجلاء، طنطا', '040-3456789', 0, 1, GETUTCDATE()),
(N'فرع الزقازيق', 'Zagazig Branch', 'ZAG', N'شارع الجامعة، الزقازيق', '055-2345678', 0, 1, GETUTCDATE()),
(N'فرع أسوان', 'Aswan Branch', 'ASW', N'شارع النيل، أسوان', '097-2345678', 0, 1, GETUTCDATE());

PRINT N'تم إنشاء الفروع المصرية';

-- إنشاء أنواع العملاء
INSERT INTO CustomerTypes (NameAr, NameEn, DefaultDiscountPercentage, IsActive, CreatedAt) VALUES
(N'عميل عادي', 'Regular Customer', 0.00, 1, GETUTCDATE()),
(N'عميل جملة', 'Wholesale Customer', 5.00, 1, GETUTCDATE()),
(N'عميل VIP', 'VIP Customer', 10.00, 1, GETUTCDATE()),
(N'عميل تاجر', 'Merchant Customer', 7.50, 1, GETUTCDATE()),
(N'عميل مؤسسة', 'Corporate Customer', 12.00, 1, GETUTCDATE());

PRINT N'تم إنشاء أنواع العملاء';

-- إنشاء فئات المنتجات
INSERT INTO Categories (NameAr, NameEn, Code, IsActive, CreatedAt) VALUES
(N'عام', 'General', 'GEN', 1, GETUTCDATE()),
(N'مواد غذائية ومشروبات', 'Food & Beverages', 'FOOD', 1, GETUTCDATE()),
(N'أجهزة كهربائية', 'Electronics', 'ELEC', 1, GETUTCDATE()),
(N'ملابس وأحذية', 'Clothing & Shoes', 'CLOTH', 1, GETUTCDATE()),
(N'أدوات منزلية', 'Home & Kitchen', 'HOME', 1, GETUTCDATE()),
(N'مستحضرات تجميل وعناية', 'Beauty & Personal Care', 'BEAUTY', 1, GETUTCDATE()),
(N'رياضة ولياقة', 'Sports & Fitness', 'SPORT', 1, GETUTCDATE()),
(N'مكتبة وقرطاسية', 'Books & Stationery', 'BOOKS', 1, GETUTCDATE()),
(N'ألعاب وهدايا', 'Toys & Gifts', 'TOYS', 1, GETUTCDATE()),
(N'قطع غيار وإكسسوارات', 'Parts & Accessories', 'PARTS', 1, GETUTCDATE());

PRINT N'تم إنشاء فئات المنتجات';

-- إنشاء وحدات القياس
INSERT INTO Units (NameAr, NameEn, Code, IsActive, CreatedAt) VALUES
(N'حبة', 'Piece', 'PCS', 1, GETUTCDATE()),
(N'كيلو', 'Kilogram', 'KG', 1, GETUTCDATE()),
(N'جرام', 'Gram', 'GM', 1, GETUTCDATE()),
(N'متر', 'Meter', 'M', 1, GETUTCDATE()),
(N'سم', 'Centimeter', 'CM', 1, GETUTCDATE()),
(N'لتر', 'Liter', 'L', 1, GETUTCDATE()),
(N'مل', 'Milliliter', 'ML', 1, GETUTCDATE()),
(N'علبة', 'Box', 'BOX', 1, GETUTCDATE()),
(N'كرتونة', 'Carton', 'CTN', 1, GETUTCDATE()),
(N'دستة', 'Dozen', 'DOZ', 1, GETUTCDATE());

PRINT N'تم إنشاء وحدات القياس';

-- إنشاء فئات الأسعار
INSERT INTO PriceCategories (NameAr, NameEn, Code, IsActive, CreatedAt) VALUES
(N'سعر التجزئة', 'Retail Price', 'RETAIL', 1, GETUTCDATE()),
(N'سعر الجملة', 'Wholesale Price', 'WHOLESALE', 1, GETUTCDATE()),
(N'سعر VIP', 'VIP Price', 'VIP', 1, GETUTCDATE()),
(N'سعر التجار', 'Merchant Price', 'MERCHANT', 1, GETUTCDATE()),
(N'سعر الموظفين', 'Employee Price', 'EMPLOYEE', 1, GETUTCDATE());

PRINT N'تم إنشاء فئات الأسعار';

-- إنشاء طرق الدفع المصرية
INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsActive, DisplayOrder, CreatedAt) VALUES
(N'كاش', 'Cash', 'CASH', 1, 1, 1, GETUTCDATE()),
(N'فيزا', 'Visa Card', 'VISA', 2, 1, 2, GETUTCDATE()),
(N'ميزة', 'Meeza Card', 'MEEZA', 2, 1, 3, GETUTCDATE()),
(N'فودافون كاش', 'Vodafone Cash', 'VFCASH', 2, 1, 4, GETUTCDATE()),
(N'أورانج موني', 'Orange Money', 'ORANGE', 2, 1, 5, GETUTCDATE()),
(N'إتصالات فلوس', 'Etisalat Flous', 'ETISALAT', 2, 1, 6, GETUTCDATE()),
(N'فوري', 'Fawry', 'FAWRY', 2, 1, 7, GETUTCDATE()),
(N'تحويل بنكي', 'Bank Transfer', 'TRANSFER', 3, 1, 8, GETUTCDATE()),
(N'شيك', 'Check', 'CHECK', 5, 1, 9, GETUTCDATE()),
(N'آجل', 'Credit Terms', 'CREDIT', 4, 1, 10, GETUTCDATE());

PRINT N'تم إنشاء طرق الدفع المصرية';

-- إنشاء أنواع الموردين
INSERT INTO SupplierTypes (NameAr, NameEn, IsActive, CreatedAt) VALUES
(N'مورد محلي', 'Local Supplier', 1, GETUTCDATE()),
(N'مورد مستورد', 'Import Supplier', 1, GETUTCDATE()),
(N'مورد حكومي', 'Government Supplier', 1, GETUTCDATE());

PRINT N'تم إنشاء أنواع الموردين';

-- تحديث العدادات
UPDATE Counters SET Description = N'عداد العملاء' WHERE CounterName = 'CUSTOMER';
UPDATE Counters SET Description = N'عداد المنتجات' WHERE CounterName = 'PRODUCT';
UPDATE Counters SET Description = N'عداد المبيعات' WHERE CounterName = 'SALE';
UPDATE Counters SET Description = N'عداد المشتريات' WHERE CounterName = 'PURCHASE';
UPDATE Counters SET Description = N'عداد الموردين' WHERE CounterName = 'SUPPLIER';
UPDATE Counters SET Description = N'عداد الموظفين' WHERE CounterName = 'EMPLOYEE';

-- تحديث الأدوار
UPDATE Roles SET NameAr = N'مدير النظام' WHERE Code = 'SUPER_ADMIN';
UPDATE Roles SET NameAr = N'مدير عام' WHERE Code = 'GENERAL_MANAGER';
UPDATE Roles SET NameAr = N'مدير فرع' WHERE Code = 'BRANCH_MANAGER';
UPDATE Roles SET NameAr = N'محاسب' WHERE Code = 'ACCOUNTANT';
UPDATE Roles SET NameAr = N'كاشير' WHERE Code = 'CASHIER';
UPDATE Roles SET NameAr = N'مخزني' WHERE Code = 'INVENTORY_CLERK';
UPDATE Roles SET NameAr = N'مندوب مبيعات' WHERE Code = 'SALES_REP';
UPDATE Roles SET NameAr = N'مستخدم عادي' WHERE Code = 'USER';

PRINT N'تم تحديث الأدوار';

PRINT N'تم الانتهاء من إعادة إنشاء البيانات العربية بترميز صحيح!';

-- عرض النتائج
SELECT N'المحافظات المصرية' as TableName, COUNT(*) as RecordCount FROM Areas
UNION ALL
SELECT N'الفروع المصرية', COUNT(*) FROM Branches
UNION ALL
SELECT N'أنواع العملاء', COUNT(*) FROM CustomerTypes
UNION ALL
SELECT N'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT N'وحدات القياس', COUNT(*) FROM Units
UNION ALL
SELECT N'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT N'طرق الدفع', COUNT(*) FROM PaymentMethods
UNION ALL
SELECT N'أنواع الموردين', COUNT(*) FROM SupplierTypes;
