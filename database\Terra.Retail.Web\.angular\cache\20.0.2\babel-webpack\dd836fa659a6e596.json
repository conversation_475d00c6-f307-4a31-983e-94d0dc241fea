{"ast": null, "code": "/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isNotFound, getCurrentInjector, setCurrentInjector } from './primitives/di.mjs';\nimport { getActiveConsumer, SIGNAL, createSignal } from './signal-nCiHhWf6.mjs';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { NotFoundError, isNotFound as isNotFound$1 } from '@angular/core/primitives/di';\nimport { setActiveConsumer } from '@angular/core/primitives/signals';\n\n/**\n * Base URL for the error details page.\n *\n * Keep this constant in sync across:\n *  - packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.ts\n *  - packages/core/src/error_details_base_url.ts\n */\nconst ERROR_DETAILS_PAGE_BASE_URL = 'https://angular.dev/errors';\n/**\n * URL for the XSS security documentation.\n */\nconst XSS_SECURITY_URL = 'https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss';\n\n/**\n * Class that represents a runtime error.\n * Formats and outputs the error message in a consistent way.\n *\n * Example:\n * ```ts\n *  throw new RuntimeError(\n *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n *    ngDevMode && 'Injector has already been destroyed.');\n * ```\n *\n * Note: the `message` argument contains a descriptive error message as a string in development\n * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the\n * `message` argument becomes `false`, thus we account for it in the typings and the runtime\n * logic.\n */\nclass RuntimeError extends Error {\n  code;\n  constructor(code, message) {\n    super(formatRuntimeError(code, message));\n    this.code = code;\n  }\n}\nfunction formatRuntimeErrorCode(code) {\n  // Error code might be a negative number, which is a special marker that instructs the logic to\n  // generate a link to the error details page on angular.io.\n  // We also prepend `0` to non-compile-time errors.\n  return `NG0${Math.abs(code)}`;\n}\n/**\n * Called to format a runtime error.\n * See additional info on the `message` argument type in the `RuntimeError` class description.\n */\nfunction formatRuntimeError(code, message) {\n  const fullCode = formatRuntimeErrorCode(code);\n  let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;\n  if (ngDevMode && code < 0) {\n    const addPeriodSeparator = !errorMessage.match(/[.,;!?\\n]$/);\n    const separator = addPeriodSeparator ? '.' : '';\n    errorMessage = `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;\n  }\n  return errorMessage;\n}\nconst _global = globalThis;\nfunction ngDevModeResetPerfCounters() {\n  const locationString = typeof location !== 'undefined' ? location.toString() : '';\n  const newCounters = {\n    hydratedNodes: 0,\n    hydratedComponents: 0,\n    dehydratedViewsRemoved: 0,\n    dehydratedViewsCleanupRuns: 0,\n    componentsSkippedHydration: 0,\n    deferBlocksWithIncrementalHydration: 0\n  };\n  // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.\n  const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;\n  if (!allowNgDevModeTrue) {\n    _global['ngDevMode'] = false;\n  } else {\n    if (typeof _global['ngDevMode'] !== 'object') {\n      _global['ngDevMode'] = {};\n    }\n    Object.assign(_global['ngDevMode'], newCounters);\n  }\n  return newCounters;\n}\n/**\n * This function checks to see if the `ngDevMode` has been set. If yes,\n * then we honor it, otherwise we default to dev mode with additional checks.\n *\n * The idea is that unless we are doing production build where we explicitly\n * set `ngDevMode == false` we should be helping the developer by providing\n * as much early warning and errors as possible.\n *\n * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions\n * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode\n * is defined for the entire instruction set.\n *\n * When checking `ngDevMode` on toplevel, always init it before referencing it\n * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can\n *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.\n *\n * Details on possible values for `ngDevMode` can be found on its docstring.\n */\nfunction initNgDevMode() {\n  // The below checks are to ensure that calling `initNgDevMode` multiple times does not\n  // reset the counters.\n  // If the `ngDevMode` is not an object, then it means we have not created the perf counters\n  // yet.\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {\n      ngDevModeResetPerfCounters();\n    }\n    return typeof ngDevMode !== 'undefined' && !!ngDevMode;\n  }\n  return false;\n}\nfunction getClosureSafeProperty(objWithPropertyToExtract) {\n  for (let key in objWithPropertyToExtract) {\n    if (objWithPropertyToExtract[key] === getClosureSafeProperty) {\n      return key;\n    }\n  }\n  // Cannot change it to `RuntimeError` because the `util` target cannot\n  // circularly depend on the `core` target.\n  throw Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Could not find renamed property on target object.' : '');\n}\n/**\n * Sets properties on a target object from a source object, but only if\n * the property doesn't already exist on the target object.\n * @param target The target to set properties on\n * @param source The source of the property keys and values to set\n */\nfunction fillProperties(target, source) {\n  for (const key in source) {\n    if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {\n      target[key] = source[key];\n    }\n  }\n}\nfunction stringify(token) {\n  if (typeof token === 'string') {\n    return token;\n  }\n  if (Array.isArray(token)) {\n    return `[${token.map(stringify).join(', ')}]`;\n  }\n  if (token == null) {\n    return '' + token;\n  }\n  const name = token.overriddenName || token.name;\n  if (name) {\n    return `${name}`;\n  }\n  const result = token.toString();\n  if (result == null) {\n    return '' + result;\n  }\n  const newLineIndex = result.indexOf('\\n');\n  return newLineIndex >= 0 ? result.slice(0, newLineIndex) : result;\n}\n/**\n * Concatenates two strings with separator, allocating new strings only when necessary.\n *\n * @param before before string.\n * @param separator separator string.\n * @param after after string.\n * @returns concatenated string.\n */\nfunction concatStringsWithSpace(before, after) {\n  if (!before) return after || '';\n  if (!after) return before;\n  return `${before} ${after}`;\n}\n/**\n * Ellipses the string in the middle when longer than the max length\n *\n * @param string\n * @param maxLength of the output string\n * @returns ellipsed string with ... in the middle\n */\nfunction truncateMiddle(str, maxLength = 100) {\n  if (!str || maxLength < 1 || str.length <= maxLength) return str;\n  if (maxLength == 1) return str.substring(0, 1) + '...';\n  const halfLimit = Math.round(maxLength / 2);\n  return str.substring(0, halfLimit) + '...' + str.substring(str.length - halfLimit);\n}\nconst __forward_ref__ = /*#__PURE__*/getClosureSafeProperty({\n  __forward_ref__: getClosureSafeProperty\n});\n/**\n * Allows to refer to references which are not yet defined.\n *\n * For instance, `forwardRef` is used when the `token` which we need to refer to for the purposes of\n * DI is declared, but not yet defined. It is also used when the `token` which we use when creating\n * a query is not yet defined.\n *\n * `forwardRef` is also used to break circularities in standalone components imports.\n *\n * @usageNotes\n * ### Circular dependency example\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref'}\n *\n * ### Circular standalone reference import example\n * ```angular-ts\n * @Component({\n *   imports: [ChildComponent],\n *   selector: 'app-parent',\n *   template: `<app-child [hideParent]=\"hideParent()\"></app-child>`,\n * })\n * export class ParentComponent {\n *    hideParent = input.required<boolean>();\n * }\n *\n *\n * @Component({\n *   imports: [forwardRef(() => ParentComponent)],\n *   selector: 'app-child',\n *   template: `\n *    @if(!hideParent() {\n *       <app-parent/>\n *    }\n *  `,\n * })\n * export class ChildComponent {\n *    hideParent = input.required<boolean>();\n * }\n * ```\n *\n * @publicApi\n */\nfunction forwardRef(forwardRefFn) {\n  forwardRefFn.__forward_ref__ = forwardRef;\n  forwardRefFn.toString = function () {\n    return stringify(this());\n  };\n  return forwardRefFn;\n}\n/**\n * Lazily retrieves the reference value from a forwardRef.\n *\n * Acts as the identity function when given a non-forward-ref value.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='resolve_forward_ref'}\n *\n * @see {@link forwardRef}\n * @publicApi\n */\nfunction resolveForwardRef(type) {\n  return isForwardRef(type) ? type() : type;\n}\n/** Checks whether a function is wrapped by a `forwardRef`. */\nfunction isForwardRef(fn) {\n  return typeof fn === 'function' && fn.hasOwnProperty(__forward_ref__) && fn.__forward_ref__ === forwardRef;\n}\n\n// The functions in this file verify that the assumptions we are making\n// about state in an instruction are correct before implementing any logic.\n// They are meant only to be called in dev mode as sanity checks.\nfunction assertNumber(actual, msg) {\n  if (!(typeof actual === 'number')) {\n    throwError(msg, typeof actual, 'number', '===');\n  }\n}\nfunction assertNumberInRange(actual, minInclusive, maxInclusive) {\n  assertNumber(actual, 'Expected a number');\n  assertLessThanOrEqual(actual, maxInclusive, 'Expected number to be less than or equal to');\n  assertGreaterThanOrEqual(actual, minInclusive, 'Expected number to be greater than or equal to');\n}\nfunction assertString(actual, msg) {\n  if (!(typeof actual === 'string')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'string', '===');\n  }\n}\nfunction assertFunction(actual, msg) {\n  if (!(typeof actual === 'function')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'function', '===');\n  }\n}\nfunction assertEqual(actual, expected, msg) {\n  if (!(actual == expected)) {\n    throwError(msg, actual, expected, '==');\n  }\n}\nfunction assertNotEqual(actual, expected, msg) {\n  if (!(actual != expected)) {\n    throwError(msg, actual, expected, '!=');\n  }\n}\nfunction assertSame(actual, expected, msg) {\n  if (!(actual === expected)) {\n    throwError(msg, actual, expected, '===');\n  }\n}\nfunction assertNotSame(actual, expected, msg) {\n  if (!(actual !== expected)) {\n    throwError(msg, actual, expected, '!==');\n  }\n}\nfunction assertLessThan(actual, expected, msg) {\n  if (!(actual < expected)) {\n    throwError(msg, actual, expected, '<');\n  }\n}\nfunction assertLessThanOrEqual(actual, expected, msg) {\n  if (!(actual <= expected)) {\n    throwError(msg, actual, expected, '<=');\n  }\n}\nfunction assertGreaterThan(actual, expected, msg) {\n  if (!(actual > expected)) {\n    throwError(msg, actual, expected, '>');\n  }\n}\nfunction assertGreaterThanOrEqual(actual, expected, msg) {\n  if (!(actual >= expected)) {\n    throwError(msg, actual, expected, '>=');\n  }\n}\nfunction assertNotDefined(actual, msg) {\n  if (actual != null) {\n    throwError(msg, actual, null, '==');\n  }\n}\nfunction assertDefined(actual, msg) {\n  if (actual == null) {\n    throwError(msg, actual, null, '!=');\n  }\n}\nfunction throwError(msg, actual, expected, comparison) {\n  throw new Error(`ASSERTION ERROR: ${msg}` + (comparison == null ? '' : ` [Expected=> ${expected} ${comparison} ${actual} <=Actual]`));\n}\nfunction assertDomNode(node) {\n  if (!(node instanceof Node)) {\n    throwError(`The provided value must be an instance of a DOM Node but got ${stringify(node)}`);\n  }\n}\nfunction assertElement(node) {\n  if (!(node instanceof Element)) {\n    throwError(`The provided value must be an element but got ${stringify(node)}`);\n  }\n}\nfunction assertIndexInRange(arr, index) {\n  assertDefined(arr, 'Array must be defined.');\n  const maxLen = arr.length;\n  if (index < 0 || index >= maxLen) {\n    throwError(`Index expected to be less than ${maxLen} but got ${index}`);\n  }\n}\nfunction assertOneOf(value, ...validValues) {\n  if (validValues.indexOf(value) !== -1) return true;\n  throwError(`Expected value to be one of ${JSON.stringify(validValues)} but was ${JSON.stringify(value)}.`);\n}\nfunction assertNotReactive(fn) {\n  if (getActiveConsumer() !== null) {\n    throwError(`${fn}() should never be called in a reactive context.`);\n  }\n}\n\n/**\n * Construct an injectable definition which defines how a token will be constructed by the DI\n * system, and in which injectors (if any) it will be available.\n *\n * This should be assigned to a static `ɵprov` field on a type, which will then be an\n * `InjectableType`.\n *\n * Options:\n * * `providedIn` determines which injectors will include the injectable, by either associating it\n *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be\n *   provided in the `'root'` injector, which will be the application-level injector in most apps.\n * * `factory` gives the zero argument function which will create an instance of the injectable.\n *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection\n * of dependencies.\n *\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nfunction ɵɵdefineInjectable(opts) {\n  return {\n    token: opts.token,\n    providedIn: opts.providedIn || null,\n    factory: opts.factory,\n    value: undefined\n  };\n}\n/**\n * @deprecated in v8, delete after v10. This API should be used only by generated code, and that\n * code should now use ɵɵdefineInjectable instead.\n * @publicApi\n */\nconst defineInjectable = ɵɵdefineInjectable;\n/**\n * Construct an `InjectorDef` which configures an injector.\n *\n * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an\n * `InjectorType`.\n *\n * Options:\n *\n * * `providers`: an optional array of providers to add to the injector. Each provider must\n *   either have a factory or point to a type which has a `ɵprov` static property (the\n *   type must be an `InjectableType`).\n * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s\n *   whose providers will also be added to the injector. Locally provided types will override\n *   providers from imports.\n *\n * @codeGenApi\n */\nfunction ɵɵdefineInjector(options) {\n  return {\n    providers: options.providers || [],\n    imports: options.imports || []\n  };\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading\n * inherited value.\n *\n * @param type A type which may have its own (non-inherited) `ɵprov`.\n */\nfunction getInjectableDef(type) {\n  return getOwnDefinition(type, NG_PROV_DEF);\n}\nfunction isInjectable(type) {\n  return getInjectableDef(type) !== null;\n}\n/**\n * Return definition only if it is defined directly on `type` and is not inherited from a base\n * class of `type`.\n */\nfunction getOwnDefinition(type, field) {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  return type.hasOwnProperty(field) && type[field] || null;\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.\n *\n * @param type A type which may have `ɵprov`, via inheritance.\n *\n * @deprecated Will be removed in a future version of Angular, where an error will occur in the\n *     scenario if we find the `ɵprov` on an ancestor only.\n */\nfunction getInheritedInjectableDef(type) {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  const def = type?.[NG_PROV_DEF] ?? null;\n  if (def) {\n    ngDevMode && console.warn(`DEPRECATED: DI is instantiating a token \"${type.name}\" that inherits its @Injectable decorator but does not provide one itself.\\n` + `This will become an error in a future version of Angular. Please add @Injectable() to the \"${type.name}\" class.`);\n    return def;\n  } else {\n    return null;\n  }\n}\n/**\n * Read the injector def type in a way which is immune to accidentally reading inherited value.\n *\n * @param type type which may have an injector def (`ɵinj`)\n */\nfunction getInjectorDef(type) {\n  return type && type.hasOwnProperty(NG_INJ_DEF) ? type[NG_INJ_DEF] : null;\n}\nconst NG_PROV_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵprov: getClosureSafeProperty\n});\nconst NG_INJ_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵinj: getClosureSafeProperty\n});\n\n/**\n * Creates a token that can be used in a DI Provider.\n *\n * Use an `InjectionToken` whenever the type you are injecting is not reified (does not have a\n * runtime representation) such as when injecting an interface, callable type, array or\n * parameterized type.\n *\n * `InjectionToken` is parameterized on `T` which is the type of object which will be returned by\n * the `Injector`. This provides an additional level of type safety.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * **Important Note**: Ensure that you use the same instance of the `InjectionToken` in both the\n * provider and the injection call. Creating a new instance of `InjectionToken` in different places,\n * even with the same description, will be treated as different tokens by Angular's DI system,\n * leading to a `NullInjectorError`.\n *\n * </div>\n *\n * {@example injection-token/src/main.ts region='InjectionToken'}\n *\n * When creating an `InjectionToken`, you can optionally specify a factory function which returns\n * (possibly by creating) a default value of the parameterized type `T`. This sets up the\n * `InjectionToken` using this factory as a provider as if it was defined explicitly in the\n * application's root injector. If the factory function, which takes zero arguments, needs to inject\n * dependencies, it can do so using the [`inject`](api/core/inject) function.\n * As you can see in the Tree-shakable InjectionToken example below.\n *\n * Additionally, if a `factory` is specified you can also specify the `providedIn` option, which\n * overrides the above behavior and marks the token as belonging to a particular `@NgModule` (note:\n * this option is now deprecated). As mentioned above, `'root'` is the default value for\n * `providedIn`.\n *\n * The `providedIn: NgModule` and `providedIn: 'any'` options are deprecated.\n *\n * @usageNotes\n * ### Basic Examples\n *\n * ### Plain InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='InjectionToken'}\n *\n * ### Tree-shakable InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='ShakableInjectionToken'}\n *\n * @publicApi\n */\nclass InjectionToken {\n  _desc;\n  /** @internal */\n  ngMetadataName = 'InjectionToken';\n  ɵprov;\n  /**\n   * @param _desc   Description for the token,\n   *                used only for debugging purposes,\n   *                it should but does not need to be unique\n   * @param options Options for the token's usage, as described above\n   */\n  constructor(_desc, options) {\n    this._desc = _desc;\n    this.ɵprov = undefined;\n    if (typeof options == 'number') {\n      (typeof ngDevMode === 'undefined' || ngDevMode) && assertLessThan(options, 0, 'Only negative numbers are supported here');\n      // This is a special hack to assign __NG_ELEMENT_ID__ to this instance.\n      // See `InjectorMarkers`\n      this.__NG_ELEMENT_ID__ = options;\n    } else if (options !== undefined) {\n      this.ɵprov = ɵɵdefineInjectable({\n        token: this,\n        providedIn: options.providedIn || 'root',\n        factory: options.factory\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  get multi() {\n    return this;\n  }\n  toString() {\n    return `InjectionToken ${this._desc}`;\n  }\n}\nlet _injectorProfilerContext;\nfunction getInjectorProfilerContext() {\n  !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');\n  return _injectorProfilerContext;\n}\nfunction setInjectorProfilerContext(context) {\n  !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');\n  const previous = _injectorProfilerContext;\n  _injectorProfilerContext = context;\n  return previous;\n}\nconst injectorProfilerCallbacks = [];\nconst NOOP_PROFILER_REMOVAL = () => {};\nfunction removeProfiler(profiler) {\n  const profilerIdx = injectorProfilerCallbacks.indexOf(profiler);\n  if (profilerIdx !== -1) {\n    injectorProfilerCallbacks.splice(profilerIdx, 1);\n  }\n}\n/**\n * Adds a callback function which will be invoked during certain DI events within the\n * runtime (for example: injecting services, creating injectable instances, configuring providers).\n * Multiple profiler callbacks can be set: in this case profiling events are\n * reported to every registered callback.\n *\n * Warning: this function is *INTERNAL* and should not be relied upon in application's code.\n * The contract of the function might be changed in any release and/or the function can be removed\n * completely.\n *\n * @param profiler function provided by the caller or null value to disable profiling.\n * @returns a cleanup function that, when invoked, removes a given profiler callback.\n */\nfunction setInjectorProfiler(injectorProfiler) {\n  !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');\n  if (injectorProfiler !== null) {\n    if (!injectorProfilerCallbacks.includes(injectorProfiler)) {\n      injectorProfilerCallbacks.push(injectorProfiler);\n    }\n    return () => removeProfiler(injectorProfiler);\n  } else {\n    injectorProfilerCallbacks.length = 0;\n    return NOOP_PROFILER_REMOVAL;\n  }\n}\n/**\n * Injector profiler function which emits on DI events executed by the runtime.\n *\n * @param event InjectorProfilerEvent corresponding to the DI event being emitted\n */\nfunction injectorProfiler(event) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  for (let i = 0; i < injectorProfilerCallbacks.length; i++) {\n    const injectorProfilerCallback = injectorProfilerCallbacks[i];\n    injectorProfilerCallback(event);\n  }\n}\n/**\n * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the\n * emitted event includes the raw provider, as well as the token that provider is providing.\n *\n * @param eventProvider A provider object\n */\nfunction emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  let token;\n  // if the provider is a TypeProvider (typeof provider is function) then the token is the\n  // provider itself\n  if (typeof eventProvider === 'function') {\n    token = eventProvider;\n  }\n  // if the provider is an injection token, then the token is the injection token.\n  else if (eventProvider instanceof InjectionToken) {\n    token = eventProvider;\n  }\n  // in all other cases we can access the token via the `provide` property of the provider\n  else {\n    token = resolveForwardRef(eventProvider.provide);\n  }\n  let provider = eventProvider;\n  // Injection tokens may define their own default provider which gets attached to the token itself\n  // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the\n  // token itself.\n  if (eventProvider instanceof InjectionToken) {\n    provider = eventProvider.ɵprov || eventProvider;\n  }\n  injectorProfiler({\n    type: 2 /* InjectorProfilerEventType.ProviderConfigured */,\n    context: getInjectorProfilerContext(),\n    providerRecord: {\n      token,\n      provider,\n      isViewProvider\n    }\n  });\n}\n/**\n * Emits an event to the injector profiler when an instance corresponding to a given token is about to be created be an injector. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInjectorToCreateInstanceEvent(token) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 4 /* InjectorProfilerEventType.InjectorToCreateInstanceEvent */,\n    context: getInjectorProfilerContext(),\n    token: token\n  });\n}\n/**\n * Emits an event to the injector profiler with the instance that was created. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInstanceCreatedByInjectorEvent(instance) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,\n    context: getInjectorProfilerContext(),\n    instance: {\n      value: instance\n    }\n  });\n}\n/**\n * @param token DI token associated with injected service\n * @param value the instance of the injected service (i.e the result of `inject(token)`)\n * @param flags the flags that the token was injected with\n */\nfunction emitInjectEvent(token, value, flags) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 0 /* InjectorProfilerEventType.Inject */,\n    context: getInjectorProfilerContext(),\n    service: {\n      token,\n      value,\n      flags\n    }\n  });\n}\nfunction emitEffectCreatedEvent(effect) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 3 /* InjectorProfilerEventType.EffectCreated */,\n    context: getInjectorProfilerContext(),\n    effect\n  });\n}\nfunction runInInjectorProfilerContext(injector, token, callback) {\n  !ngDevMode && throwError('runInInjectorProfilerContext should never be called in production mode');\n  const prevInjectContext = setInjectorProfilerContext({\n    injector,\n    token\n  });\n  try {\n    callback();\n  } finally {\n    setInjectorProfilerContext(prevInjectContext);\n  }\n}\nfunction isEnvironmentProviders(value) {\n  return value && !!value.ɵproviders;\n}\nconst NG_COMP_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵcmp: getClosureSafeProperty\n});\nconst NG_DIR_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵdir: getClosureSafeProperty\n});\nconst NG_PIPE_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵpipe: getClosureSafeProperty\n});\nconst NG_MOD_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵmod: getClosureSafeProperty\n});\nconst NG_FACTORY_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵfac: getClosureSafeProperty\n});\n/**\n * If a directive is diPublic, bloomAdd sets a property on the type with this constant as\n * the key and the directive's unique ID as the value. This allows us to map directives to their\n * bloom filter bit for DI.\n */\n// TODO(misko): This is wrong. The NG_ELEMENT_ID should never be minified.\nconst NG_ELEMENT_ID = /*#__PURE__*/getClosureSafeProperty({\n  __NG_ELEMENT_ID__: getClosureSafeProperty\n});\n/**\n * The `NG_ENV_ID` field on a DI token indicates special processing in the `EnvironmentInjector`:\n * getting such tokens from the `EnvironmentInjector` will bypass the standard DI resolution\n * strategy and instead will return implementation produced by the `NG_ENV_ID` factory function.\n *\n * This particular retrieval of DI tokens is mostly done to eliminate circular dependencies and\n * improve tree-shaking.\n */\nconst NG_ENV_ID = /*#__PURE__*/getClosureSafeProperty({\n  __NG_ENV_ID__: getClosureSafeProperty\n});\n\n/**\n * Used for stringify render output in Ivy.\n * Important! This function is very performance-sensitive and we should\n * be extra careful not to introduce megamorphic reads in it.\n * Check `core/test/render3/perf/render_stringify` for benchmarks and alternate implementations.\n */\nfunction renderStringify(value) {\n  if (typeof value === 'string') return value;\n  if (value == null) return '';\n  // Use `String` so that it invokes the `toString` method of the value. Note that this\n  // appears to be faster than calling `value.toString` (see `render_stringify` benchmark).\n  return String(value);\n}\n/**\n * Used to stringify a value so that it can be displayed in an error message.\n *\n * Important! This function contains a megamorphic read and should only be\n * used for error messages.\n */\nfunction stringifyForError(value) {\n  if (typeof value === 'function') return value.name || value.toString();\n  if (typeof value === 'object' && value != null && typeof value.type === 'function') {\n    return value.type.name || value.type.toString();\n  }\n  return renderStringify(value);\n}\n/**\n * Used to stringify a `Type` and including the file path and line number in which it is defined, if\n * possible, for better debugging experience.\n *\n * Important! This function contains a megamorphic read and should only be used for error messages.\n */\nfunction debugStringifyTypeForError(type) {\n  // TODO(pmvald): Do some refactoring so that we can use getComponentDef here without creating\n  // circular deps.\n  let componentDef = type[NG_COMP_DEF] || null;\n  if (componentDef !== null && componentDef.debugInfo) {\n    return stringifyTypeFromDebugInfo(componentDef.debugInfo);\n  }\n  return stringifyForError(type);\n}\n// TODO(pmvald): Do some refactoring so that we can use the type ClassDebugInfo for the param\n// debugInfo here without creating circular deps.\nfunction stringifyTypeFromDebugInfo(debugInfo) {\n  if (!debugInfo.filePath || !debugInfo.lineNumber) {\n    return debugInfo.className;\n  } else {\n    return `${debugInfo.className} (at ${debugInfo.filePath}:${debugInfo.lineNumber})`;\n  }\n}\n\n/** Called when directives inject each other (creating a circular dependency) */\nfunction throwCyclicDependencyError(token, path) {\n  throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, ngDevMode ? `Circular dependency in DI detected for ${token}${path ? `. Dependency path: ${path.join(' > ')} > ${token}` : ''}` : token);\n}\nfunction throwMixedMultiProviderError() {\n  throw new Error(`Cannot mix multi providers and regular providers`);\n}\nfunction throwInvalidProviderError(ngModuleType, providers, provider) {\n  if (ngModuleType && providers) {\n    const providerDetail = providers.map(v => v == provider ? '?' + provider + '?' : '...');\n    throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);\n  } else if (isEnvironmentProviders(provider)) {\n    if (provider.ɵfromNgModule) {\n      throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);\n    } else {\n      throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);\n    }\n  } else {\n    throw new Error('Invalid provider');\n  }\n}\n/** Throws an error when a token is not found in DI. */\nfunction throwProviderNotFoundError(token, injectorName) {\n  const errorMessage = ngDevMode && `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;\n  throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, errorMessage);\n}\n\n/**\n * Current implementation of inject.\n *\n * By default, it is `injectInjectorOnly`, which makes it `Injector`-only aware. It can be changed\n * to `directiveInject`, which brings in the `NodeInjector` system of ivy. It is designed this\n * way for two reasons:\n *  1. `Injector` should not depend on ivy logic.\n *  2. To maintain tree shake-ability we don't want to bring in unnecessary code.\n */\nlet _injectImplementation;\nfunction getInjectImplementation() {\n  return _injectImplementation;\n}\n/**\n * Sets the current inject implementation.\n */\nfunction setInjectImplementation(impl) {\n  const previous = _injectImplementation;\n  _injectImplementation = impl;\n  return previous;\n}\n/**\n * Injects `root` tokens in limp mode.\n *\n * If no injector exists, we can still inject tree-shakable providers which have `providedIn` set to\n * `\"root\"`. This is known as the limp mode injection. In such case the value is stored in the\n * injectable definition.\n */\nfunction injectRootLimpMode(token, notFoundValue, flags) {\n  const injectableDef = getInjectableDef(token);\n  if (injectableDef && injectableDef.providedIn == 'root') {\n    return injectableDef.value === undefined ? injectableDef.value = injectableDef.factory() : injectableDef.value;\n  }\n  if (flags & 8 /* InternalInjectFlags.Optional */) return null;\n  if (notFoundValue !== undefined) return notFoundValue;\n  throwProviderNotFoundError(token, 'Injector');\n}\n/**\n * Assert that `_injectImplementation` is not `fn`.\n *\n * This is useful, to prevent infinite recursion.\n *\n * @param fn Function which it should not equal to\n */\nfunction assertInjectImplementationNotEqual(fn) {\n  ngDevMode && assertNotEqual(_injectImplementation, fn, 'Calling ɵɵinject would cause infinite recursion');\n}\nconst _THROW_IF_NOT_FOUND = {};\nconst THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;\n/*\n * Name of a property (that we patch onto DI decorator), which is used as an annotation of which\n * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators\n * in the code, thus making them tree-shakable.\n */\nconst DI_DECORATOR_FLAG = '__NG_DI_FLAG__';\n/**\n * A wrapper around an `Injector` that implements the `PrimitivesInjector` interface.\n *\n * This is used to allow the `inject` function to be used with the new primitives-based DI system.\n */\nclass RetrievingInjector {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  retrieve(token, options) {\n    const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n    try {\n      return this.injector.get(token,\n      // When a dependency is requested with an optional flag, DI returns null as the default value.\n      flags & 8 /* InternalInjectFlags.Optional */ ? null : THROW_IF_NOT_FOUND, flags);\n    } catch (e) {\n      if (isNotFound(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n}\nconst NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';\nconst NG_TOKEN_PATH = 'ngTokenPath';\nconst NEW_LINE = /\\n/gm;\nconst NO_NEW_LINE = 'ɵ';\nconst SOURCE = '__source';\nfunction injectInjectorOnly(token, flags = 0 /* InternalInjectFlags.Default */) {\n  const currentInjector = getCurrentInjector();\n  if (currentInjector === undefined) {\n    throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode && `The \\`${stringify(token)}\\` token injection failed. \\`inject()\\` function must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \\`runInInjectionContext\\`.`);\n  } else if (currentInjector === null) {\n    return injectRootLimpMode(token, undefined, flags);\n  } else {\n    const options = convertToInjectOptions(flags);\n    const value = currentInjector.retrieve(token, options);\n    ngDevMode && emitInjectEvent(token, value, flags);\n    if (isNotFound(value)) {\n      if (options.optional) {\n        return null;\n      }\n      throw value;\n    }\n    return value;\n  }\n}\nfunction ɵɵinject(token, flags = 0 /* InternalInjectFlags.Default */) {\n  return (getInjectImplementation() || injectInjectorOnly)(resolveForwardRef(token), flags);\n}\n/**\n * Throws an error indicating that a factory function could not be generated by the compiler for a\n * particular class.\n *\n * The name of the class is not mentioned here, but will be in the generated factory function name\n * and thus in the stack trace.\n *\n * @codeGenApi\n */\nfunction ɵɵinvalidFactoryDep(index) {\n  throw new RuntimeError(202 /* RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY */, ngDevMode && `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.\nThis can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.\n\nPlease check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`);\n}\n/**\n * Injects a token from the currently active injector.\n * `inject` is only supported in an [injection context](guide/di/dependency-injection-context). It\n * can be used during:\n * - Construction (via the `constructor`) of a class being instantiated by the DI system, such\n * as an `@Injectable` or `@Component`.\n * - In the initializer for fields of such classes.\n * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.\n * - In the `factory` function specified for an `InjectionToken`.\n * - In a stackframe of a function call in a DI context\n *\n * @param token A token that represents a dependency that should be injected.\n * @param flags Optional flags that control how injection is executed.\n * The flags correspond to injection strategies that can be specified with\n * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @usageNotes\n * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a\n * field initializer:\n *\n * ```ts\n * @Injectable({providedIn: 'root'})\n * export class Car {\n *   radio: Radio|undefined;\n *   // OK: field initializer\n *   spareTyre = inject(Tyre);\n *\n *   constructor() {\n *     // OK: constructor body\n *     this.radio = inject(Radio);\n *   }\n * }\n * ```\n *\n * It is also legal to call `inject` from a provider's factory:\n *\n * ```ts\n * providers: [\n *   {provide: Car, useFactory: () => {\n *     // OK: a class factory\n *     const engine = inject(Engine);\n *     return new Car(engine);\n *   }}\n * ]\n * ```\n *\n * Calls to the `inject()` function outside of the class creation context will result in error. Most\n * notably, calls to `inject()` are disallowed after a class instance was created, in methods\n * (including lifecycle hooks):\n *\n * ```ts\n * @Component({ ... })\n * export class CarComponent {\n *   ngOnInit() {\n *     // ERROR: too late, the component instance was already created\n *     const engine = inject(Engine);\n *     engine.start();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction inject(token, options) {\n  // The `as any` here _shouldn't_ be necessary, but without it JSCompiler\n  // throws a disambiguation  error due to the multiple signatures.\n  return ɵɵinject(token, convertToBitFlags(options));\n}\n// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).\nfunction convertToBitFlags(flags) {\n  if (typeof flags === 'undefined' || typeof flags === 'number') {\n    return flags;\n  }\n  // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in\n  // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from\n  // `InjectOptions` to `InjectFlags`.\n  return 0 /* InternalInjectFlags.Default */ | (\n  // comment to force a line break in the formatter\n  flags.optional && 8 /* InternalInjectFlags.Optional */) | (flags.host && 1 /* InternalInjectFlags.Host */) | (flags.self && 2 /* InternalInjectFlags.Self */) | (flags.skipSelf && 4 /* InternalInjectFlags.SkipSelf */);\n}\n// Converts bitflags to inject options\nfunction convertToInjectOptions(flags) {\n  return {\n    optional: !!(flags & 8 /* InternalInjectFlags.Optional */),\n    host: !!(flags & 1 /* InternalInjectFlags.Host */),\n    self: !!(flags & 2 /* InternalInjectFlags.Self */),\n    skipSelf: !!(flags & 4 /* InternalInjectFlags.SkipSelf */)\n  };\n}\nfunction injectArgs(types) {\n  const args = [];\n  for (let i = 0; i < types.length; i++) {\n    const arg = resolveForwardRef(types[i]);\n    if (Array.isArray(arg)) {\n      if (arg.length === 0) {\n        throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && 'Arguments array must have arguments.');\n      }\n      let type = undefined;\n      let flags = 0 /* InternalInjectFlags.Default */;\n      for (let j = 0; j < arg.length; j++) {\n        const meta = arg[j];\n        const flag = getInjectFlag(meta);\n        if (typeof flag === 'number') {\n          // Special case when we handle @Inject decorator.\n          if (flag === -1 /* DecoratorFlags.Inject */) {\n            type = meta.token;\n          } else {\n            flags |= flag;\n          }\n        } else {\n          type = meta;\n        }\n      }\n      args.push(ɵɵinject(type, flags));\n    } else {\n      args.push(ɵɵinject(arg));\n    }\n  }\n  return args;\n}\n/**\n * Attaches a given InjectFlag to a given decorator using monkey-patching.\n * Since DI decorators can be used in providers `deps` array (when provider is configured using\n * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we\n * attach the flag to make it available both as a static property and as a field on decorator\n * instance.\n *\n * @param decorator Provided DI decorator.\n * @param flag InjectFlag that should be applied.\n */\nfunction attachInjectFlag(decorator, flag) {\n  decorator[DI_DECORATOR_FLAG] = flag;\n  decorator.prototype[DI_DECORATOR_FLAG] = flag;\n  return decorator;\n}\n/**\n * Reads monkey-patched property that contains InjectFlag attached to a decorator.\n *\n * @param token Token that may contain monkey-patched DI flags property.\n */\nfunction getInjectFlag(token) {\n  return token[DI_DECORATOR_FLAG];\n}\nfunction catchInjectorError(e, token, injectorErrorName, source) {\n  const tokenPath = e[NG_TEMP_TOKEN_PATH];\n  if (token[SOURCE]) {\n    tokenPath.unshift(token[SOURCE]);\n  }\n  e.message = formatError('\\n' + e.message, tokenPath, injectorErrorName, source);\n  e[NG_TOKEN_PATH] = tokenPath;\n  e[NG_TEMP_TOKEN_PATH] = null;\n  throw e;\n}\nfunction formatError(text, obj, injectorErrorName, source = null) {\n  text = text && text.charAt(0) === '\\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;\n  let context = stringify(obj);\n  if (Array.isArray(obj)) {\n    context = obj.map(stringify).join(' -> ');\n  } else if (typeof obj === 'object') {\n    let parts = [];\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        let value = obj[key];\n        parts.push(key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)));\n      }\n    }\n    context = `{${parts.join(', ')}}`;\n  }\n  return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(NEW_LINE, '\\n  ')}`;\n}\nfunction getFactoryDef(type, throwNotFound) {\n  const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);\n  if (!hasFactoryDef && throwNotFound === true && ngDevMode) {\n    throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);\n  }\n  return hasFactoryDef ? type[NG_FACTORY_DEF] : null;\n}\n\n/**\n * Determines if the contents of two arrays is identical\n *\n * @param a first array\n * @param b second array\n * @param identityAccessor Optional function for extracting stable object identity from a value in\n *     the array.\n */\nfunction arrayEquals(a, b, identityAccessor) {\n  if (a.length !== b.length) return false;\n  for (let i = 0; i < a.length; i++) {\n    let valueA = a[i];\n    let valueB = b[i];\n    if (identityAccessor) {\n      valueA = identityAccessor(valueA);\n      valueB = identityAccessor(valueB);\n    }\n    if (valueB !== valueA) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Flattens an array.\n */\nfunction flatten(list) {\n  return list.flat(Number.POSITIVE_INFINITY);\n}\nfunction deepForEach(input, fn) {\n  input.forEach(value => Array.isArray(value) ? deepForEach(value, fn) : fn(value));\n}\nfunction addToArray(arr, index, value) {\n  // perf: array.push is faster than array.splice!\n  if (index >= arr.length) {\n    arr.push(value);\n  } else {\n    arr.splice(index, 0, value);\n  }\n}\nfunction removeFromArray(arr, index) {\n  // perf: array.pop is faster than array.splice!\n  if (index >= arr.length - 1) {\n    return arr.pop();\n  } else {\n    return arr.splice(index, 1)[0];\n  }\n}\nfunction newArray(size, value) {\n  const list = [];\n  for (let i = 0; i < size; i++) {\n    list.push(value);\n  }\n  return list;\n}\n/**\n * Remove item from array (Same as `Array.splice()` but faster.)\n *\n * `Array.splice()` is not as fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * https://jsperf.com/fast-array-splice (About 20x faster)\n *\n * @param array Array to splice\n * @param index Index of element in array to remove.\n * @param count Number of items to remove.\n */\nfunction arraySplice(array, index, count) {\n  const length = array.length - count;\n  while (index < length) {\n    array[index] = array[index + count];\n    index++;\n  }\n  while (count--) {\n    array.pop(); // shrink the array\n  }\n}\n/**\n * Same as `Array.splice2(index, 0, value1, value2)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value1 Value to add to array.\n * @param value2 Value to add to array.\n */\nfunction arrayInsert2(array, index, value1, value2) {\n  ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n  let end = array.length;\n  if (end == index) {\n    // inserting at the end.\n    array.push(value1, value2);\n  } else if (end === 1) {\n    // corner case when we have less items in array than we have items to insert.\n    array.push(value2, array[0]);\n    array[0] = value1;\n  } else {\n    end--;\n    array.push(array[end - 1], array[end]);\n    while (end > index) {\n      const previousEnd = end - 2;\n      array[end] = array[previousEnd];\n      end--;\n    }\n    array[index] = value1;\n    array[index + 1] = value2;\n  }\n}\n/**\n * Set a `value` for a `key`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or create.\n * @param value The value to set for a `key`.\n * @returns index (always even) of where the value vas set.\n */\nfunction keyValueArraySet(keyValueArray, key, value) {\n  let index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it set it.\n    keyValueArray[index | 1] = value;\n  } else {\n    index = ~index;\n    arrayInsert2(keyValueArray, index, key, value);\n  }\n  return index;\n}\n/**\n * Retrieve a `value` for a `key` (on `undefined` if not found.)\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @return The `value` stored at the `key` location or `undefined if not found.\n */\nfunction keyValueArrayGet(keyValueArray, key) {\n  const index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it retrieve it.\n    return keyValueArray[index | 1];\n  }\n  return undefined;\n}\n/**\n * Retrieve a `key` index value in the array or `-1` if not found.\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @returns index of where the key is (or should have been.)\n *   - positive (even) index if key found.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been inserted.)\n */\nfunction keyValueArrayIndexOf(keyValueArray, key) {\n  return _arrayIndexOfSorted(keyValueArray, key, 1);\n}\n/**\n * INTERNAL: Get an index of an `value` in a sorted `array` by grouping search by `shift`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @param shift grouping shift.\n *   - `0` means look at every location\n *   - `1` means only look at every other (even) location (the odd locations are to be ignored as\n *         they are values.)\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n * inserted)\n */\nfunction _arrayIndexOfSorted(array, value, shift) {\n  ngDevMode && assertEqual(Array.isArray(array), true, 'Expecting an array');\n  let start = 0;\n  let end = array.length >> shift;\n  while (end !== start) {\n    const middle = start + (end - start >> 1); // find the middle.\n    const current = array[middle << shift];\n    if (value === current) {\n      return middle << shift;\n    } else if (current > value) {\n      end = middle;\n    } else {\n      start = middle + 1; // We already searched middle so make it non-inclusive by adding 1\n    }\n  }\n  return ~(end << shift);\n}\n\n/**\n * This file contains reuseable \"empty\" symbols that can be used as default return values\n * in different parts of the rendering code. Because the same symbols are returned, this\n * allows for identity checks against these values to be consistently used by the framework\n * code.\n */\nconst EMPTY_OBJ = {};\nconst EMPTY_ARRAY = [];\n// freezing the values prevents any code from accidentally inserting new values in\nif ((typeof ngDevMode === 'undefined' || ngDevMode) && /*#__PURE__*/initNgDevMode()) {\n  // These property accesses can be ignored because ngDevMode will be set to false\n  // when optimizing code and the whole if statement will be dropped.\n  // tslint:disable-next-line:no-toplevel-property-access\n  /*#__PURE__*/Object.freeze(EMPTY_OBJ);\n  // tslint:disable-next-line:no-toplevel-property-access\n  /*#__PURE__*/Object.freeze(EMPTY_ARRAY);\n}\n\n/**\n * A multi-provider token for initialization functions that will run upon construction of an\n * environment injector.\n *\n * @deprecated from v19.0.0, use provideEnvironmentInitializer instead\n *\n * @see {@link provideEnvironmentInitializer}\n *\n * Note: As opposed to the `APP_INITIALIZER` token, the `ENVIRONMENT_INITIALIZER` functions are not awaited,\n * hence they should not be `async`.\n *\n * @publicApi\n */\nconst ENVIRONMENT_INITIALIZER = /*#__PURE__*/new InjectionToken(ngDevMode ? 'ENVIRONMENT_INITIALIZER' : '');\n\n/**\n * An InjectionToken that gets the current `Injector` for `createInjector()`-style injectors.\n *\n * Requesting this token instead of `Injector` allows `StaticInjector` to be tree-shaken from a\n * project.\n *\n * @publicApi\n */\nconst INJECTOR$1 = /*#__PURE__*/new InjectionToken(ngDevMode ? 'INJECTOR' : '',\n// Disable tslint because this is const enum which gets inlined not top level prop access.\n// tslint:disable-next-line: no-toplevel-property-access\n-1 /* InjectorMarkers.Injector */);\nconst INJECTOR_DEF_TYPES = /*#__PURE__*/new InjectionToken(ngDevMode ? 'INJECTOR_DEF_TYPES' : '');\nclass NullInjector {\n  get(token, notFoundValue = THROW_IF_NOT_FOUND) {\n    if (notFoundValue === THROW_IF_NOT_FOUND) {\n      const error = new NotFoundError(`NullInjectorError: No provider for ${stringify(token)}!`);\n      throw error;\n    }\n    return notFoundValue;\n  }\n}\nfunction getNgModuleDef(type) {\n  return type[NG_MOD_DEF] || null;\n}\nfunction getNgModuleDefOrThrow(type) {\n  const ngModuleDef = getNgModuleDef(type);\n  if (!ngModuleDef) {\n    throw new RuntimeError(915 /* RuntimeErrorCode.MISSING_NG_MODULE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Type ${stringify(type)} does not have 'ɵmod' property.`);\n  }\n  return ngModuleDef;\n}\n/**\n * The following getter methods retrieve the definition from the type. Currently the retrieval\n * honors inheritance, but in the future we may change the rule to require that definitions are\n * explicit. This would require some sort of migration strategy.\n */\nfunction getComponentDef(type) {\n  return type[NG_COMP_DEF] || null;\n}\nfunction getDirectiveDefOrThrow(type) {\n  const def = getDirectiveDef(type);\n  if (!def) {\n    throw new RuntimeError(916 /* RuntimeErrorCode.MISSING_DIRECTIVE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Type ${stringify(type)} does not have 'ɵdir' property.`);\n  }\n  return def;\n}\nfunction getDirectiveDef(type) {\n  return type[NG_DIR_DEF] || null;\n}\nfunction getPipeDef(type) {\n  return type[NG_PIPE_DEF] || null;\n}\n/**\n * Checks whether a given Component, Directive or Pipe is marked as standalone.\n * This will return false if passed anything other than a Component, Directive, or Pipe class\n * See [this guide](guide/components/importing) for additional information:\n *\n * @param type A reference to a Component, Directive or Pipe.\n * @publicApi\n */\nfunction isStandalone(type) {\n  const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);\n  return def !== null && def.standalone;\n}\n\n/**\n * Wrap an array of `Provider`s into `EnvironmentProviders`, preventing them from being accidentally\n * referenced in `@Component` in a component injector.\n *\n * @publicApi\n */\nfunction makeEnvironmentProviders(providers) {\n  return {\n    ɵproviders: providers\n  };\n}\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon construction\n * of an environment injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `ENVIRONMENT_INITIALIZER` token which is now deprecated.\n *\n * @see {@link ENVIRONMENT_INITIALIZER}\n *\n * @usageNotes\n * The following example illustrates how to configure an initialization function using\n * `provideEnvironmentInitializer()`\n * ```ts\n * createEnvironmentInjector(\n *   [\n *     provideEnvironmentInitializer(() => {\n *       console.log('environment initialized');\n *     }),\n *   ],\n *   parentInjector\n * );\n * ```\n *\n * @publicApi\n */\nfunction provideEnvironmentInitializer(initializerFn) {\n  return makeEnvironmentProviders([{\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useValue: initializerFn\n  }]);\n}\n/**\n * Collects providers from all NgModules and standalone components, including transitively imported\n * ones.\n *\n * Providers extracted via `importProvidersFrom` are only usable in an application injector or\n * another environment injector (such as a route injector). They should not be used in component\n * providers.\n *\n * More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The results of the `importProvidersFrom` call can be used in the `bootstrapApplication` call:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(NgModuleOne, NgModuleTwo)\n *   ]\n * });\n * ```\n *\n * You can also use the `importProvidersFrom` results in the `providers` field of a route, when a\n * standalone component is used:\n *\n * ```ts\n * export const ROUTES: Route[] = [\n *   {\n *     path: 'foo',\n *     providers: [\n *       importProvidersFrom(NgModuleOne, NgModuleTwo)\n *     ],\n *     component: YourStandaloneComponent\n *   }\n * ];\n * ```\n *\n * @returns Collected providers from the specified list of types.\n * @publicApi\n */\nfunction importProvidersFrom(...sources) {\n  return {\n    ɵproviders: internalImportProvidersFrom(true, sources),\n    ɵfromNgModule: true\n  };\n}\nfunction internalImportProvidersFrom(checkForStandaloneCmp, ...sources) {\n  const providersOut = [];\n  const dedup = new Set(); // already seen types\n  let injectorTypesWithProviders;\n  const collectProviders = provider => {\n    providersOut.push(provider);\n  };\n  deepForEach(sources, source => {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && checkForStandaloneCmp) {\n      const cmpDef = getComponentDef(source);\n      if (cmpDef?.standalone) {\n        throw new RuntimeError(800 /* RuntimeErrorCode.IMPORT_PROVIDERS_FROM_STANDALONE */, `Importing providers supports NgModule or ModuleWithProviders but got a standalone component \"${stringifyForError(source)}\"`);\n      }\n    }\n    // Narrow `source` to access the internal type analogue for `ModuleWithProviders`.\n    const internalSource = source;\n    if (walkProviderTree(internalSource, collectProviders, [], dedup)) {\n      injectorTypesWithProviders ||= [];\n      injectorTypesWithProviders.push(internalSource);\n    }\n  });\n  // Collect all providers from `ModuleWithProviders` types.\n  if (injectorTypesWithProviders !== undefined) {\n    processInjectorTypesWithProviders(injectorTypesWithProviders, collectProviders);\n  }\n  return providersOut;\n}\n/**\n * Collects all providers from the list of `ModuleWithProviders` and appends them to the provided\n * array.\n */\nfunction processInjectorTypesWithProviders(typesWithProviders, visitor) {\n  for (let i = 0; i < typesWithProviders.length; i++) {\n    const {\n      ngModule,\n      providers\n    } = typesWithProviders[i];\n    deepForEachProvider(providers, provider => {\n      ngDevMode && validateProvider(provider, providers || EMPTY_ARRAY, ngModule);\n      visitor(provider, ngModule);\n    });\n  }\n}\n/**\n * The logic visits an `InjectorType`, an `InjectorTypeWithProviders`, or a standalone\n * `ComponentType`, and all of its transitive providers and collects providers.\n *\n * If an `InjectorTypeWithProviders` that declares providers besides the type is specified,\n * the function will return \"true\" to indicate that the providers of the type definition need\n * to be processed. This allows us to process providers of injector types after all imports of\n * an injector definition are processed. (following View Engine semantics: see FW-1349)\n */\nfunction walkProviderTree(container, visitor, parents, dedup) {\n  container = resolveForwardRef(container);\n  if (!container) return false;\n  // The actual type which had the definition. Usually `container`, but may be an unwrapped type\n  // from `InjectorTypeWithProviders`.\n  let defType = null;\n  let injDef = getInjectorDef(container);\n  const cmpDef = !injDef && getComponentDef(container);\n  if (!injDef && !cmpDef) {\n    // `container` is not an injector type or a component type. It might be:\n    //  * An `InjectorTypeWithProviders` that wraps an injector type.\n    //  * A standalone directive or pipe that got pulled in from a standalone component's\n    //    dependencies.\n    // Try to unwrap it as an `InjectorTypeWithProviders` first.\n    const ngModule = container.ngModule;\n    injDef = getInjectorDef(ngModule);\n    if (injDef) {\n      defType = ngModule;\n    } else {\n      // Not a component or injector type, so ignore it.\n      return false;\n    }\n  } else if (cmpDef && !cmpDef.standalone) {\n    return false;\n  } else {\n    defType = container;\n  }\n  // Check for circular dependencies.\n  if (ngDevMode && parents.indexOf(defType) !== -1) {\n    const defName = stringify(defType);\n    const path = parents.map(stringify);\n    throwCyclicDependencyError(defName, path);\n  }\n  // Check for multiple imports of the same module\n  const isDuplicate = dedup.has(defType);\n  if (cmpDef) {\n    if (isDuplicate) {\n      // This component definition has already been processed.\n      return false;\n    }\n    dedup.add(defType);\n    if (cmpDef.dependencies) {\n      const deps = typeof cmpDef.dependencies === 'function' ? cmpDef.dependencies() : cmpDef.dependencies;\n      for (const dep of deps) {\n        walkProviderTree(dep, visitor, parents, dedup);\n      }\n    }\n  } else if (injDef) {\n    // First, include providers from any imports.\n    if (injDef.imports != null && !isDuplicate) {\n      // Before processing defType's imports, add it to the set of parents. This way, if it ends\n      // up deeply importing itself, this can be detected.\n      ngDevMode && parents.push(defType);\n      // Add it to the set of dedups. This way we can detect multiple imports of the same module\n      dedup.add(defType);\n      let importTypesWithProviders;\n      try {\n        deepForEach(injDef.imports, imported => {\n          if (walkProviderTree(imported, visitor, parents, dedup)) {\n            importTypesWithProviders ||= [];\n            // If the processed import is an injector type with providers, we store it in the\n            // list of import types with providers, so that we can process those afterwards.\n            importTypesWithProviders.push(imported);\n          }\n        });\n      } finally {\n        // Remove it from the parents set when finished.\n        ngDevMode && parents.pop();\n      }\n      // Imports which are declared with providers (TypeWithProviders) need to be processed\n      // after all imported modules are processed. This is similar to how View Engine\n      // processes/merges module imports in the metadata resolver. See: FW-1349.\n      if (importTypesWithProviders !== undefined) {\n        processInjectorTypesWithProviders(importTypesWithProviders, visitor);\n      }\n    }\n    if (!isDuplicate) {\n      // Track the InjectorType and add a provider for it.\n      // It's important that this is done after the def's imports.\n      const factory = getFactoryDef(defType) || (() => new defType());\n      // Append extra providers to make more info available for consumers (to retrieve an injector\n      // type), as well as internally (to calculate an injection scope correctly and eagerly\n      // instantiate a `defType` when an injector is created).\n      // Provider to create `defType` using its factory.\n      visitor({\n        provide: defType,\n        useFactory: factory,\n        deps: EMPTY_ARRAY\n      }, defType);\n      // Make this `defType` available to an internal logic that calculates injector scope.\n      visitor({\n        provide: INJECTOR_DEF_TYPES,\n        useValue: defType,\n        multi: true\n      }, defType);\n      // Provider to eagerly instantiate `defType` via `INJECTOR_INITIALIZER`.\n      visitor({\n        provide: ENVIRONMENT_INITIALIZER,\n        useValue: () => ɵɵinject(defType),\n        multi: true\n      }, defType);\n    }\n    // Next, include providers listed on the definition itself.\n    const defProviders = injDef.providers;\n    if (defProviders != null && !isDuplicate) {\n      const injectorType = container;\n      deepForEachProvider(defProviders, provider => {\n        ngDevMode && validateProvider(provider, defProviders, injectorType);\n        visitor(provider, injectorType);\n      });\n    }\n  } else {\n    // Should not happen, but just in case.\n    return false;\n  }\n  return defType !== container && container.providers !== undefined;\n}\nfunction validateProvider(provider, providers, containerType) {\n  if (isTypeProvider(provider) || isValueProvider(provider) || isFactoryProvider(provider) || isExistingProvider(provider)) {\n    return;\n  }\n  // Here we expect the provider to be a `useClass` provider (by elimination).\n  const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n  if (!classRef) {\n    throwInvalidProviderError(containerType, providers, provider);\n  }\n}\nfunction deepForEachProvider(providers, fn) {\n  for (let provider of providers) {\n    if (isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      deepForEachProvider(provider, fn);\n    } else {\n      fn(provider);\n    }\n  }\n}\nconst USE_VALUE = /*#__PURE__*/getClosureSafeProperty({\n  provide: String,\n  useValue: getClosureSafeProperty\n});\nfunction isValueProvider(value) {\n  return value !== null && typeof value == 'object' && USE_VALUE in value;\n}\nfunction isExistingProvider(value) {\n  return !!(value && value.useExisting);\n}\nfunction isFactoryProvider(value) {\n  return !!(value && value.useFactory);\n}\nfunction isTypeProvider(value) {\n  return typeof value === 'function';\n}\nfunction isClassProvider(value) {\n  return !!value.useClass;\n}\n\n/**\n * An internal token whose presence in an injector indicates that the injector should treat itself\n * as a root scoped injector when processing requests for unknown tokens which may indicate\n * they are provided in the root scope.\n */\nconst INJECTOR_SCOPE = /*#__PURE__*/new InjectionToken(ngDevMode ? 'Set Injector scope.' : '');\n\n/**\n * Marker which indicates that a value has not yet been created from the factory function.\n */\nconst NOT_YET = {};\n/**\n * Marker which indicates that the factory function for a token is in the process of being called.\n *\n * If the injector is asked to inject a token with its value set to CIRCULAR, that indicates\n * injection of a dependency has recursively attempted to inject the original token, and there is\n * a circular dependency among the providers.\n */\nconst CIRCULAR = {};\n/**\n * A lazily initialized NullInjector.\n */\nlet NULL_INJECTOR = undefined;\nfunction getNullInjector() {\n  if (NULL_INJECTOR === undefined) {\n    NULL_INJECTOR = new NullInjector();\n  }\n  return NULL_INJECTOR;\n}\n/**\n * An `Injector` that's part of the environment injector hierarchy, which exists outside of the\n * component tree.\n *\n * @publicApi\n */\nclass EnvironmentInjector {}\nclass R3Injector extends EnvironmentInjector {\n  parent;\n  source;\n  scopes;\n  /**\n   * Map of tokens to records which contain the instances of those tokens.\n   * - `null` value implies that we don't have the record. Used by tree-shakable injectors\n   * to prevent further searches.\n   */\n  records = /*#__PURE__*/new Map();\n  /**\n   * Set of values instantiated by this injector which contain `ngOnDestroy` lifecycle hooks.\n   */\n  _ngOnDestroyHooks = /*#__PURE__*/new Set();\n  _onDestroyHooks = [];\n  /**\n   * Flag indicating that this injector was previously destroyed.\n   */\n  get destroyed() {\n    return this._destroyed;\n  }\n  _destroyed = false;\n  injectorDefTypes;\n  constructor(providers, parent, source, scopes) {\n    super();\n    this.parent = parent;\n    this.source = source;\n    this.scopes = scopes;\n    // Start off by creating Records for every provider.\n    forEachSingleProvider(providers, provider => this.processProvider(provider));\n    // Make sure the INJECTOR token provides this injector.\n    this.records.set(INJECTOR$1, makeRecord(undefined, this));\n    // And `EnvironmentInjector` if the current injector is supposed to be env-scoped.\n    if (scopes.has('environment')) {\n      this.records.set(EnvironmentInjector, makeRecord(undefined, this));\n    }\n    // Detect whether this injector has the APP_ROOT_SCOPE token and thus should provide\n    // any injectable scoped to APP_ROOT_SCOPE.\n    const record = this.records.get(INJECTOR_SCOPE);\n    if (record != null && typeof record.value === 'string') {\n      this.scopes.add(record.value);\n    }\n    this.injectorDefTypes = new Set(this.get(INJECTOR_DEF_TYPES, EMPTY_ARRAY, {\n      self: true\n    }));\n  }\n  retrieve(token, options) {\n    const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n    try {\n      return this.get(token,\n      // When a dependency is requested with an optional flag, DI returns null as the default value.\n      THROW_IF_NOT_FOUND, flags);\n    } catch (e) {\n      if (isNotFound$1(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  /**\n   * Destroy the injector and release references to every instance or provider associated with it.\n   *\n   * Also calls the `OnDestroy` lifecycle hooks of every instance that was created for which a\n   * hook was found.\n   */\n  destroy() {\n    assertNotDestroyed(this);\n    // Set destroyed = true first, in case lifecycle hooks re-enter destroy().\n    this._destroyed = true;\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Call all the lifecycle hooks.\n      for (const service of this._ngOnDestroyHooks) {\n        service.ngOnDestroy();\n      }\n      const onDestroyHooks = this._onDestroyHooks;\n      // Reset the _onDestroyHooks array before iterating over it to prevent hooks that unregister\n      // themselves from mutating the array during iteration.\n      this._onDestroyHooks = [];\n      for (const hook of onDestroyHooks) {\n        hook();\n      }\n    } finally {\n      // Release all references.\n      this.records.clear();\n      this._ngOnDestroyHooks.clear();\n      this.injectorDefTypes.clear();\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  onDestroy(callback) {\n    assertNotDestroyed(this);\n    this._onDestroyHooks.push(callback);\n    return () => this.removeOnDestroy(callback);\n  }\n  runInContext(fn) {\n    assertNotDestroyed(this);\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: null\n      });\n    }\n    try {\n      return fn();\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n    }\n  }\n  get(token, notFoundValue = THROW_IF_NOT_FOUND, options) {\n    assertNotDestroyed(this);\n    if (token.hasOwnProperty(NG_ENV_ID)) {\n      return token[NG_ENV_ID](this);\n    }\n    const flags = convertToBitFlags(options);\n    // Set the injection context.\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: token\n      });\n    }\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    try {\n      // Check for the SkipSelf flag.\n      if (!(flags & 4 /* InternalInjectFlags.SkipSelf */)) {\n        // SkipSelf isn't set, check if the record belongs to this injector.\n        let record = this.records.get(token);\n        if (record === undefined) {\n          // No record, but maybe the token is scoped to this injector. Look for an injectable\n          // def with a scope matching this injector.\n          const def = couldBeInjectableType(token) && getInjectableDef(token);\n          if (def && this.injectableDefInScope(def)) {\n            // Found an injectable def and it's scoped to this injector. Pretend as if it was here\n            // all along.\n            if (ngDevMode) {\n              runInInjectorProfilerContext(this, token, () => {\n                emitProviderConfiguredEvent(token);\n              });\n            }\n            record = makeRecord(injectableDefOrInjectorDefFactory(token), NOT_YET);\n          } else {\n            record = null;\n          }\n          this.records.set(token, record);\n        }\n        // If a record was found, get the instance for it and return it.\n        if (record != null /* NOT null || undefined */) {\n          return this.hydrate(token, record);\n        }\n      }\n      // Select the next injector based on the Self flag - if self is set, the next injector is\n      // the NullInjector, otherwise it's the parent.\n      const nextInjector = !(flags & 2 /* InternalInjectFlags.Self */) ? this.parent : getNullInjector();\n      // Set the notFoundValue based on the Optional flag - if optional is set and notFoundValue\n      // is undefined, the value is null, otherwise it's the notFoundValue.\n      notFoundValue = flags & 8 /* InternalInjectFlags.Optional */ && notFoundValue === THROW_IF_NOT_FOUND ? null : notFoundValue;\n      return nextInjector.get(token, notFoundValue);\n    } catch (e) {\n      if (isNotFound$1(e)) {\n        // @ts-ignore\n        const path = e[NG_TEMP_TOKEN_PATH] = e[NG_TEMP_TOKEN_PATH] || [];\n        path.unshift(stringify(token));\n        if (previousInjector) {\n          // We still have a parent injector, keep throwing\n          throw e;\n        } else {\n          // Format & throw the final error message when we don't have any previous injector\n          return catchInjectorError(e, token, 'R3InjectorError', this.source);\n        }\n      } else {\n        throw e;\n      }\n    } finally {\n      // Lastly, restore the previous injection context.\n      setInjectImplementation(previousInjectImplementation);\n      setCurrentInjector(previousInjector);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n    }\n  }\n  /** @internal */\n  resolveInjectorInitializers() {\n    const prevConsumer = setActiveConsumer(null);\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: null\n      });\n    }\n    try {\n      const initializers = this.get(ENVIRONMENT_INITIALIZER, EMPTY_ARRAY, {\n        self: true\n      });\n      if (ngDevMode && !Array.isArray(initializers)) {\n        throw new RuntimeError(-209 /* RuntimeErrorCode.INVALID_MULTI_PROVIDER */, 'Unexpected type of the `ENVIRONMENT_INITIALIZER` token value ' + `(expected an array, but got ${typeof initializers}). ` + 'Please check that the `ENVIRONMENT_INITIALIZER` token is configured as a ' + '`multi: true` provider.');\n      }\n      for (const initializer of initializers) {\n        initializer();\n      }\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  toString() {\n    const tokens = [];\n    const records = this.records;\n    for (const token of records.keys()) {\n      tokens.push(stringify(token));\n    }\n    return `R3Injector[${tokens.join(', ')}]`;\n  }\n  /**\n   * Process a `SingleProvider` and add it.\n   */\n  processProvider(provider) {\n    // Determine the token from the provider. Either it's its own token, or has a {provide: ...}\n    // property.\n    provider = resolveForwardRef(provider);\n    let token = isTypeProvider(provider) ? provider : resolveForwardRef(provider && provider.provide);\n    // Construct a `Record` for the provider.\n    const record = providerToRecord(provider);\n    if (ngDevMode) {\n      runInInjectorProfilerContext(this, token, () => {\n        // Emit InjectorProfilerEventType.Create if provider is a value provider because\n        // these are the only providers that do not go through the value hydration logic\n        // where this event would normally be emitted from.\n        if (isValueProvider(provider)) {\n          emitInjectorToCreateInstanceEvent(token);\n          emitInstanceCreatedByInjectorEvent(provider.useValue);\n        }\n        emitProviderConfiguredEvent(provider);\n      });\n    }\n    if (!isTypeProvider(provider) && provider.multi === true) {\n      // If the provider indicates that it's a multi-provider, process it specially.\n      // First check whether it's been defined already.\n      let multiRecord = this.records.get(token);\n      if (multiRecord) {\n        // It has. Throw a nice error if\n        if (ngDevMode && multiRecord.multi === undefined) {\n          throwMixedMultiProviderError();\n        }\n      } else {\n        multiRecord = makeRecord(undefined, NOT_YET, true);\n        multiRecord.factory = () => injectArgs(multiRecord.multi);\n        this.records.set(token, multiRecord);\n      }\n      token = provider;\n      multiRecord.multi.push(provider);\n    } else {\n      if (ngDevMode) {\n        const existing = this.records.get(token);\n        if (existing && existing.multi !== undefined) {\n          throwMixedMultiProviderError();\n        }\n      }\n    }\n    this.records.set(token, record);\n  }\n  hydrate(token, record) {\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      if (record.value === CIRCULAR) {\n        throwCyclicDependencyError(stringify(token));\n      } else if (record.value === NOT_YET) {\n        record.value = CIRCULAR;\n        if (ngDevMode) {\n          runInInjectorProfilerContext(this, token, () => {\n            emitInjectorToCreateInstanceEvent(token);\n            record.value = record.factory();\n            emitInstanceCreatedByInjectorEvent(record.value);\n          });\n        } else {\n          record.value = record.factory();\n        }\n      }\n      if (typeof record.value === 'object' && record.value && hasOnDestroy(record.value)) {\n        this._ngOnDestroyHooks.add(record.value);\n      }\n      return record.value;\n    } finally {\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  injectableDefInScope(def) {\n    if (!def.providedIn) {\n      return false;\n    }\n    const providedIn = resolveForwardRef(def.providedIn);\n    if (typeof providedIn === 'string') {\n      return providedIn === 'any' || this.scopes.has(providedIn);\n    } else {\n      return this.injectorDefTypes.has(providedIn);\n    }\n  }\n  removeOnDestroy(callback) {\n    const destroyCBIdx = this._onDestroyHooks.indexOf(callback);\n    if (destroyCBIdx !== -1) {\n      this._onDestroyHooks.splice(destroyCBIdx, 1);\n    }\n  }\n}\nfunction injectableDefOrInjectorDefFactory(token) {\n  // Most tokens will have an injectable def directly on them, which specifies a factory directly.\n  const injectableDef = getInjectableDef(token);\n  const factory = injectableDef !== null ? injectableDef.factory : getFactoryDef(token);\n  if (factory !== null) {\n    return factory;\n  }\n  // InjectionTokens should have an injectable def (ɵprov) and thus should be handled above.\n  // If it's missing that, it's an error.\n  if (token instanceof InjectionToken) {\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Token ${stringify(token)} is missing a ɵprov definition.`);\n  }\n  // Undecorated types can sometimes be created if they have no constructor arguments.\n  if (token instanceof Function) {\n    return getUndecoratedInjectableFactory(token);\n  }\n  // There was no way to resolve a factory for this token.\n  throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && 'unreachable');\n}\nfunction getUndecoratedInjectableFactory(token) {\n  // If the token has parameters then it has dependencies that we cannot resolve implicitly.\n  const paramLength = token.length;\n  if (paramLength > 0) {\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Can't resolve all parameters for ${stringify(token)}: (${newArray(paramLength, '?').join(', ')}).`);\n  }\n  // The constructor function appears to have no parameters.\n  // This might be because it inherits from a super-class. In which case, use an injectable\n  // def from an ancestor if there is one.\n  // Otherwise this really is a simple class with no dependencies, so return a factory that\n  // just instantiates the zero-arg constructor.\n  const inheritedInjectableDef = getInheritedInjectableDef(token);\n  if (inheritedInjectableDef !== null) {\n    return () => inheritedInjectableDef.factory(token);\n  } else {\n    return () => new token();\n  }\n}\nfunction providerToRecord(provider) {\n  if (isValueProvider(provider)) {\n    return makeRecord(undefined, provider.useValue);\n  } else {\n    const factory = providerToFactory(provider);\n    return makeRecord(factory, NOT_YET);\n  }\n}\n/**\n * Converts a `SingleProvider` into a factory function.\n *\n * @param provider provider to convert to factory\n */\nfunction providerToFactory(provider, ngModuleType, providers) {\n  let factory = undefined;\n  if (ngDevMode && isEnvironmentProviders(provider)) {\n    throwInvalidProviderError(undefined, providers, provider);\n  }\n  if (isTypeProvider(provider)) {\n    const unwrappedProvider = resolveForwardRef(provider);\n    return getFactoryDef(unwrappedProvider) || injectableDefOrInjectorDefFactory(unwrappedProvider);\n  } else {\n    if (isValueProvider(provider)) {\n      factory = () => resolveForwardRef(provider.useValue);\n    } else if (isFactoryProvider(provider)) {\n      factory = () => provider.useFactory(...injectArgs(provider.deps || []));\n    } else if (isExistingProvider(provider)) {\n      factory = () => ɵɵinject(resolveForwardRef(provider.useExisting));\n    } else {\n      const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n      if (ngDevMode && !classRef) {\n        throwInvalidProviderError(ngModuleType, providers, provider);\n      }\n      if (hasDeps(provider)) {\n        factory = () => new classRef(...injectArgs(provider.deps));\n      } else {\n        return getFactoryDef(classRef) || injectableDefOrInjectorDefFactory(classRef);\n      }\n    }\n  }\n  return factory;\n}\nfunction assertNotDestroyed(injector) {\n  if (injector.destroyed) {\n    throw new RuntimeError(205 /* RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED */, ngDevMode && 'Injector has already been destroyed.');\n  }\n}\nfunction makeRecord(factory, value, multi = false) {\n  return {\n    factory: factory,\n    value: value,\n    multi: multi ? [] : undefined\n  };\n}\nfunction hasDeps(value) {\n  return !!value.deps;\n}\nfunction hasOnDestroy(value) {\n  return value !== null && typeof value === 'object' && typeof value.ngOnDestroy === 'function';\n}\nfunction couldBeInjectableType(value) {\n  return typeof value === 'function' || typeof value === 'object' && value.ngMetadataName === 'InjectionToken';\n}\nfunction forEachSingleProvider(providers, fn) {\n  for (const provider of providers) {\n    if (Array.isArray(provider)) {\n      forEachSingleProvider(provider, fn);\n    } else if (provider && isEnvironmentProviders(provider)) {\n      forEachSingleProvider(provider.ɵproviders, fn);\n    } else {\n      fn(provider);\n    }\n  }\n}\n\n/**\n * Runs the given function in the [context](guide/di/dependency-injection-context) of the given\n * `Injector`.\n *\n * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject dependencies\n * from the given `Injector`. Note that `inject` is only usable synchronously, and cannot be used in\n * any asynchronous callbacks or after any `await` points.\n *\n * @param injector the injector which will satisfy calls to [`inject`](api/core/inject) while `fn`\n *     is executing\n * @param fn the closure to be run in the context of `injector`\n * @returns the return value of the function, if any\n * @publicApi\n */\nfunction runInInjectionContext(injector, fn) {\n  let internalInjector;\n  if (injector instanceof R3Injector) {\n    assertNotDestroyed(injector);\n    internalInjector = injector;\n  } else {\n    internalInjector = new RetrievingInjector(injector);\n  }\n  let prevInjectorProfilerContext;\n  if (ngDevMode) {\n    prevInjectorProfilerContext = setInjectorProfilerContext({\n      injector,\n      token: null\n    });\n  }\n  const prevInjector = setCurrentInjector(internalInjector);\n  const previousInjectImplementation = setInjectImplementation(undefined);\n  try {\n    return fn();\n  } finally {\n    setCurrentInjector(prevInjector);\n    ngDevMode && setInjectorProfilerContext(prevInjectorProfilerContext);\n    setInjectImplementation(previousInjectImplementation);\n  }\n}\n/**\n * Whether the current stack frame is inside an injection context.\n */\nfunction isInInjectionContext() {\n  return getInjectImplementation() !== undefined || getCurrentInjector() != null;\n}\n/**\n * Asserts that the current stack frame is within an [injection\n * context](guide/di/dependency-injection-context) and has access to `inject`.\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertInInjectionContext(debugFn) {\n  // Taking a `Function` instead of a string name here prevents the unminified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (!isInInjectionContext()) {\n    throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode && debugFn.name + '() can only be used within an injection context such as a constructor, a factory function, a field initializer, or a function used with `runInInjectionContext`');\n  }\n}\n\n// Below are constants for LView indices to help us look up LView members\n// without having to remember the specific indices.\n// Uglify will inline these when minifying so there shouldn't be a cost.\nconst HOST = 0;\nconst TVIEW = 1;\n// Shared with LContainer\nconst FLAGS = 2;\nconst PARENT = 3;\nconst NEXT = 4;\nconst T_HOST = 5;\n// End shared with LContainer\nconst HYDRATION = 6;\nconst CLEANUP = 7;\nconst CONTEXT = 8;\nconst INJECTOR = 9;\nconst ENVIRONMENT = 10;\nconst RENDERER = 11;\nconst CHILD_HEAD = 12;\nconst CHILD_TAIL = 13;\n// FIXME(misko): Investigate if the three declarations aren't all same thing.\nconst DECLARATION_VIEW = 14;\nconst DECLARATION_COMPONENT_VIEW = 15;\nconst DECLARATION_LCONTAINER = 16;\nconst PREORDER_HOOK_FLAGS = 17;\nconst QUERIES = 18;\nconst ID = 19;\nconst EMBEDDED_VIEW_INJECTOR = 20;\nconst ON_DESTROY_HOOKS = 21;\nconst EFFECTS_TO_SCHEDULE = 22;\nconst EFFECTS = 23;\nconst REACTIVE_TEMPLATE_CONSUMER = 24;\nconst AFTER_RENDER_SEQUENCES_TO_ADD = 25;\n/**\n * Size of LView's header. Necessary to adjust for it when setting slots.\n *\n * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate\n * instruction index into `LView` index. All other indexes should be in the `LView` index space and\n * there should be no need to refer to `HEADER_OFFSET` anywhere else.\n */\nconst HEADER_OFFSET = 26;\n\n/**\n * Special location which allows easy identification of type. If we have an array which was\n * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is\n * `LContainer`.\n */\nconst TYPE = 1;\n/**\n * Below are constants for LContainer indices to help us look up LContainer members\n * without having to remember the specific indices.\n * Uglify will inline these when minifying so there shouldn't be a cost.\n */\n// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5\n// As we already have these constants in LView, we don't need to re-create them.\nconst DEHYDRATED_VIEWS = 6;\nconst NATIVE = 7;\nconst VIEW_REFS = 8;\nconst MOVED_VIEWS = 9;\n/**\n * Size of LContainer's header. Represents the index after which all views in the\n * container will be inserted. We need to keep a record of current views so we know\n * which views are already in the DOM (and don't need to be re-added) and so we can\n * remove views from the DOM when they are no longer required.\n */\nconst CONTAINER_HEADER_OFFSET = 10;\n\n/**\n * True if `value` is `LView`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLView(value) {\n  return Array.isArray(value) && typeof value[TYPE] === 'object';\n}\n/**\n * True if `value` is `LContainer`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLContainer(value) {\n  return Array.isArray(value) && value[TYPE] === true;\n}\nfunction isContentQueryHost(tNode) {\n  return (tNode.flags & 4 /* TNodeFlags.hasContentQuery */) !== 0;\n}\nfunction isComponentHost(tNode) {\n  return tNode.componentOffset > -1;\n}\nfunction isDirectiveHost(tNode) {\n  return (tNode.flags & 1 /* TNodeFlags.isDirectiveHost */) === 1 /* TNodeFlags.isDirectiveHost */;\n}\nfunction isComponentDef(def) {\n  return !!def.template;\n}\nfunction isRootView(target) {\n  // Determines whether a given LView is marked as a root view.\n  return (target[FLAGS] & 512 /* LViewFlags.IsRoot */) !== 0;\n}\nfunction isProjectionTNode(tNode) {\n  return (tNode.type & 16 /* TNodeType.Projection */) === 16 /* TNodeType.Projection */;\n}\nfunction hasI18n(lView) {\n  return (lView[FLAGS] & 32 /* LViewFlags.HasI18n */) === 32 /* LViewFlags.HasI18n */;\n}\nfunction isDestroyed(lView) {\n  // Determines whether a given LView is marked as destroyed.\n  return (lView[FLAGS] & 256 /* LViewFlags.Destroyed */) === 256 /* LViewFlags.Destroyed */;\n}\n\n// [Assert functions do not constraint type when they are guarded by a truthy\n// expression.](https://github.com/microsoft/TypeScript/issues/37295)\nfunction assertTNodeForLView(tNode, lView) {\n  assertTNodeForTView(tNode, lView[TVIEW]);\n}\nfunction assertTNodeForTView(tNode, tView) {\n  assertTNode(tNode);\n  const tData = tView.data;\n  for (let i = HEADER_OFFSET; i < tData.length; i++) {\n    if (tData[i] === tNode) {\n      return;\n    }\n  }\n  throwError('This TNode does not belong to this TView.');\n}\nfunction assertTNode(tNode) {\n  assertDefined(tNode, 'TNode must be defined');\n  if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {\n    throwError('Not of type TNode, got: ' + tNode);\n  }\n}\nfunction assertTIcu(tIcu) {\n  assertDefined(tIcu, 'Expected TIcu to be defined');\n  if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {\n    throwError('Object is not of TIcu type.');\n  }\n}\nfunction assertComponentType(actual, msg = \"Type passed in is not ComponentType, it does not have 'ɵcmp' property.\") {\n  if (!getComponentDef(actual)) {\n    throwError(msg);\n  }\n}\nfunction assertNgModuleType(actual, msg = \"Type passed in is not NgModuleType, it does not have 'ɵmod' property.\") {\n  if (!getNgModuleDef(actual)) {\n    throwError(msg);\n  }\n}\nfunction assertHasParent(tNode) {\n  assertDefined(tNode, 'currentTNode should exist!');\n  assertDefined(tNode.parent, 'currentTNode should have a parent');\n}\nfunction assertLContainer(value) {\n  assertDefined(value, 'LContainer must be defined');\n  assertEqual(isLContainer(value), true, 'Expecting LContainer');\n}\nfunction assertLViewOrUndefined(value) {\n  value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');\n}\nfunction assertLView(value) {\n  assertDefined(value, 'LView must be defined');\n  assertEqual(isLView(value), true, 'Expecting LView');\n}\nfunction assertFirstCreatePass(tView, errMessage) {\n  assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');\n}\nfunction assertFirstUpdatePass(tView, errMessage) {\n  assertEqual(tView.firstUpdatePass, true, 'Should only be called in first update pass.');\n}\n/**\n * This is a basic sanity check that an object is probably a directive def. DirectiveDef is\n * an interface, so we can't do a direct instanceof check.\n */\nfunction assertDirectiveDef(obj) {\n  if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {\n    throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);\n  }\n}\nfunction assertIndexInDeclRange(tView, index) {\n  assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);\n}\nfunction assertIndexInExpandoRange(lView, index) {\n  const tView = lView[1];\n  assertBetween(tView.expandoStartIndex, lView.length, index);\n}\nfunction assertBetween(lower, upper, index) {\n  if (!(lower <= index && index < upper)) {\n    throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);\n  }\n}\nfunction assertProjectionSlots(lView, errMessage) {\n  assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');\n  assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, 'Components with projection nodes (<ng-content>) must have projection slots defined.');\n}\nfunction assertParentView(lView, errMessage) {\n  assertDefined(lView, \"Component views should always have a parent view (component's host view)\");\n}\n/**\n * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a\n * NodeInjector data structure.\n *\n * @param lView `LView` which should be checked.\n * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.\n */\nfunction assertNodeInjector(lView, injectorIndex) {\n  assertIndexInExpandoRange(lView, injectorIndex);\n  assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);\n  assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');\n}\nconst SVG_NAMESPACE = 'svg';\nconst MATH_ML_NAMESPACE = 'math';\n\n/**\n * For efficiency reasons we often put several different data types (`RNode`, `LView`, `LContainer`)\n * in same location in `LView`. This is because we don't want to pre-allocate space for it\n * because the storage is sparse. This file contains utilities for dealing with such data types.\n *\n * How do we know what is stored at a given location in `LView`.\n * - `Array.isArray(value) === false` => `RNode` (The normal storage value)\n * - `Array.isArray(value) === true` => then the `value[0]` represents the wrapped value.\n *   - `typeof value[TYPE] === 'object'` => `LView`\n *      - This happens when we have a component at a given location\n *   - `typeof value[TYPE] === true` => `LContainer`\n *      - This happens when we have `LContainer` binding at a given location.\n *\n *\n * NOTE: it is assumed that `Array.isArray` and `typeof` operations are very efficient.\n */\n/**\n * Returns `RNode`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapRNode(value) {\n  while (Array.isArray(value)) {\n    value = value[HOST];\n  }\n  return value;\n}\n/**\n * Returns `LView` or `null` if not found.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapLView(value) {\n  while (Array.isArray(value)) {\n    // This check is same as `isLView()` but we don't call at as we don't want to call\n    // `Array.isArray()` twice and give JITer more work for inlining.\n    if (typeof value[TYPE] === 'object') return value;\n    value = value[HOST];\n  }\n  return null;\n}\n/**\n * Retrieves an element value from the provided `viewData`, by unwrapping\n * from any containers, component views, or style contexts.\n */\nfunction getNativeByIndex(index, lView) {\n  ngDevMode && assertIndexInRange(lView, index);\n  ngDevMode && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Expected to be past HEADER_OFFSET');\n  return unwrapRNode(lView[index]);\n}\n/**\n * Retrieve an `RNode` for a given `TNode` and `LView`.\n *\n * This function guarantees in dev mode to retrieve a non-null `RNode`.\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNode(tNode, lView) {\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  ngDevMode && assertIndexInRange(lView, tNode.index);\n  const node = unwrapRNode(lView[tNode.index]);\n  return node;\n}\n/**\n * Retrieve an `RNode` or `null` for a given `TNode` and `LView`.\n *\n * Some `TNode`s don't have associated `RNode`s. For example `Projection`\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNodeOrNull(tNode, lView) {\n  const index = tNode === null ? -1 : tNode.index;\n  if (index !== -1) {\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    const node = unwrapRNode(lView[index]);\n    return node;\n  }\n  return null;\n}\n// fixme(misko): The return Type should be `TNode|null`\nfunction getTNode(tView, index) {\n  ngDevMode && assertGreaterThan(index, -1, 'wrong index for TNode');\n  ngDevMode && assertLessThan(index, tView.data.length, 'wrong index for TNode');\n  const tNode = tView.data[index];\n  ngDevMode && tNode !== null && assertTNode(tNode);\n  return tNode;\n}\n/** Retrieves a value from any `LView` or `TData`. */\nfunction load(view, index) {\n  ngDevMode && assertIndexInRange(view, index);\n  return view[index];\n}\n/** Store a value in the `data` at a given `index`. */\nfunction store(tView, lView, index, value) {\n  // We don't store any static data for local variables, so the first time\n  // we see the template, we should store as null to avoid a sparse array\n  if (index >= tView.data.length) {\n    tView.data[index] = null;\n    tView.blueprint[index] = null;\n  }\n  lView[index] = value;\n}\nfunction getComponentLViewByIndex(nodeIndex, hostView) {\n  // Could be an LView or an LContainer. If LContainer, unwrap to find LView.\n  ngDevMode && assertIndexInRange(hostView, nodeIndex);\n  const slotValue = hostView[nodeIndex];\n  const lView = isLView(slotValue) ? slotValue : slotValue[HOST];\n  return lView;\n}\n/** Checks whether a given view is in creation mode */\nfunction isCreationMode(view) {\n  return (view[FLAGS] & 4 /* LViewFlags.CreationMode */) === 4 /* LViewFlags.CreationMode */;\n}\n/**\n * Returns a boolean for whether the view is attached to the change detection tree.\n *\n * Note: This determines whether a view should be checked, not whether it's inserted\n * into a container. For that, you'll want `viewAttachedToContainer` below.\n */\nfunction viewAttachedToChangeDetector(view) {\n  return (view[FLAGS] & 128 /* LViewFlags.Attached */) === 128 /* LViewFlags.Attached */;\n}\n/** Returns a boolean for whether the view is attached to a container. */\nfunction viewAttachedToContainer(view) {\n  return isLContainer(view[PARENT]);\n}\nfunction getConstant(consts, index) {\n  if (index === null || index === undefined) return null;\n  ngDevMode && assertIndexInRange(consts, index);\n  return consts[index];\n}\n/**\n * Resets the pre-order hook flags of the view.\n * @param lView the LView on which the flags are reset\n */\nfunction resetPreOrderHookFlags(lView) {\n  lView[PREORDER_HOOK_FLAGS] = 0;\n}\n/**\n * Adds the `RefreshView` flag from the lView and updates HAS_CHILD_VIEWS_TO_REFRESH flag of\n * parents.\n */\nfunction markViewForRefresh(lView) {\n  if (lView[FLAGS] & 1024 /* LViewFlags.RefreshView */) {\n    return;\n  }\n  lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n  if (viewAttachedToChangeDetector(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n/**\n * Walks up the LView hierarchy.\n * @param nestingLevel Number of times to walk up in hierarchy.\n * @param currentView View from which to start the lookup.\n */\nfunction walkUpViews(nestingLevel, currentView) {\n  while (nestingLevel > 0) {\n    ngDevMode && assertDefined(currentView[DECLARATION_VIEW], 'Declaration view should be defined if nesting level is greater than 0.');\n    currentView = currentView[DECLARATION_VIEW];\n    nestingLevel--;\n  }\n  return currentView;\n}\nfunction requiresRefreshOrTraversal(lView) {\n  return !!(lView[FLAGS] & (1024 /* LViewFlags.RefreshView */ | 8192 /* LViewFlags.HasChildViewsToRefresh */) || lView[REACTIVE_TEMPLATE_CONSUMER]?.dirty);\n}\n/**\n * Updates the `HasChildViewsToRefresh` flag on the parents of the `LView` as well as the\n * parents above.\n */\nfunction updateAncestorTraversalFlagsOnAttach(lView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(8 /* NotificationSource.ViewAttached */);\n  if (lView[FLAGS] & 64 /* LViewFlags.Dirty */) {\n    lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n  }\n  if (requiresRefreshOrTraversal(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n/**\n * Ensures views above the given `lView` are traversed during change detection even when they are\n * not dirty.\n *\n * This is done by setting the `HAS_CHILD_VIEWS_TO_REFRESH` flag up to the root, stopping when the\n * flag is already `true` or the `lView` is detached.\n */\nfunction markAncestorsForTraversal(lView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(0 /* NotificationSource.MarkAncestorsForTraversal */);\n  let parent = getLViewParent(lView);\n  while (parent !== null) {\n    // We stop adding markers to the ancestors once we reach one that already has the marker. This\n    // is to avoid needlessly traversing all the way to the root when the marker already exists.\n    if (parent[FLAGS] & 8192 /* LViewFlags.HasChildViewsToRefresh */) {\n      break;\n    }\n    parent[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n    if (!viewAttachedToChangeDetector(parent)) {\n      break;\n    }\n    parent = getLViewParent(parent);\n  }\n}\n/**\n * Stores a LView-specific destroy callback.\n */\nfunction storeLViewOnDestroy(lView, onDestroyCallback) {\n  if (isDestroyed(lView)) {\n    throw new RuntimeError(911 /* RuntimeErrorCode.VIEW_ALREADY_DESTROYED */, ngDevMode && 'View has already been destroyed.');\n  }\n  if (lView[ON_DESTROY_HOOKS] === null) {\n    lView[ON_DESTROY_HOOKS] = [];\n  }\n  lView[ON_DESTROY_HOOKS].push(onDestroyCallback);\n}\n/**\n * Removes previously registered LView-specific destroy callback.\n */\nfunction removeLViewOnDestroy(lView, onDestroyCallback) {\n  if (lView[ON_DESTROY_HOOKS] === null) return;\n  const destroyCBIdx = lView[ON_DESTROY_HOOKS].indexOf(onDestroyCallback);\n  if (destroyCBIdx !== -1) {\n    lView[ON_DESTROY_HOOKS].splice(destroyCBIdx, 1);\n  }\n}\n/**\n * Gets the parent LView of the passed LView, if the PARENT is an LContainer, will get the parent of\n * that LContainer, which is an LView\n * @param lView the lView whose parent to get\n */\nfunction getLViewParent(lView) {\n  ngDevMode && assertLView(lView);\n  const parent = lView[PARENT];\n  return isLContainer(parent) ? parent[PARENT] : parent;\n}\nfunction getOrCreateLViewCleanup(view) {\n  // top level variables should not be exported for performance reasons (PERF_NOTES.md)\n  return view[CLEANUP] ??= [];\n}\nfunction getOrCreateTViewCleanup(tView) {\n  return tView.cleanup ??= [];\n}\n/**\n * Saves context for this cleanup function in LView.cleanupInstances.\n *\n * On the first template pass, saves in TView:\n * - Cleanup function\n * - Index of context we just saved in LView.cleanupInstances\n */\nfunction storeCleanupWithContext(tView, lView, context, cleanupFn) {\n  const lCleanup = getOrCreateLViewCleanup(lView);\n  // Historically the `storeCleanupWithContext` was used to register both framework-level and\n  // user-defined cleanup callbacks, but over time those two types of cleanups were separated.\n  // This dev mode checks assures that user-level cleanup callbacks are _not_ stored in data\n  // structures reserved for framework-specific hooks.\n  ngDevMode && assertDefined(context, 'Cleanup context is mandatory when registering framework-level destroy hooks');\n  lCleanup.push(context);\n  if (tView.firstCreatePass) {\n    getOrCreateTViewCleanup(tView).push(cleanupFn, lCleanup.length - 1);\n  } else {\n    // Make sure that no new framework-level cleanup functions are registered after the first\n    // template pass is done (and TView data structures are meant to fully constructed).\n    if (ngDevMode) {\n      Object.freeze(getOrCreateTViewCleanup(tView));\n    }\n  }\n}\nconst instructionState = {\n  lFrame: /*#__PURE__*/createLFrame(null),\n  bindingsEnabled: true,\n  skipHydrationRootTNode: null\n};\nvar CheckNoChangesMode = /*#__PURE__*/function (CheckNoChangesMode) {\n  CheckNoChangesMode[CheckNoChangesMode[\"Off\"] = 0] = \"Off\";\n  CheckNoChangesMode[CheckNoChangesMode[\"Exhaustive\"] = 1] = \"Exhaustive\";\n  CheckNoChangesMode[CheckNoChangesMode[\"OnlyDirtyViews\"] = 2] = \"OnlyDirtyViews\";\n  return CheckNoChangesMode;\n}(CheckNoChangesMode || {});\n/**\n * In this mode, any changes in bindings will throw an ExpressionChangedAfterChecked error.\n *\n * Necessary to support ChangeDetectorRef.checkNoChanges().\n *\n * The `checkNoChanges` function is invoked only in ngDevMode=true and verifies that no unintended\n * changes exist in the change detector or its children.\n */\nlet _checkNoChangesMode = 0; /* CheckNoChangesMode.Off */\n/**\n * Flag used to indicate that we are in the middle running change detection on a view\n *\n * @see detectChangesInViewWhileDirty\n */\nlet _isRefreshingViews = false;\nfunction getElementDepthCount() {\n  return instructionState.lFrame.elementDepthCount;\n}\nfunction increaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount++;\n}\nfunction decreaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount--;\n}\nfunction getBindingsEnabled() {\n  return instructionState.bindingsEnabled;\n}\n/**\n * Returns true if currently inside a skip hydration block.\n * @returns boolean\n */\nfunction isInSkipHydrationBlock() {\n  return instructionState.skipHydrationRootTNode !== null;\n}\n/**\n * Returns true if this is the root TNode of the skip hydration block.\n * @param tNode the current TNode\n * @returns boolean\n */\nfunction isSkipHydrationRootTNode(tNode) {\n  return instructionState.skipHydrationRootTNode === tNode;\n}\n/**\n * Enables directive matching on elements.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵenableBindings() {\n  instructionState.bindingsEnabled = true;\n}\n/**\n * Sets a flag to specify that the TNode is in a skip hydration block.\n * @param tNode the current TNode\n */\nfunction enterSkipHydrationBlock(tNode) {\n  instructionState.skipHydrationRootTNode = tNode;\n}\n/**\n * Disables directive matching on element.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵdisableBindings() {\n  instructionState.bindingsEnabled = false;\n}\n/**\n * Clears the root skip hydration node when leaving a skip hydration block.\n */\nfunction leaveSkipHydrationBlock() {\n  instructionState.skipHydrationRootTNode = null;\n}\n/**\n * Return the current `LView`.\n */\nfunction getLView() {\n  return instructionState.lFrame.lView;\n}\n/**\n * Return the current `TView`.\n */\nfunction getTView() {\n  return instructionState.lFrame.tView;\n}\n/**\n * Restores `contextViewData` to the given OpaqueViewState instance.\n *\n * Used in conjunction with the getCurrentView() instruction to save a snapshot\n * of the current view and restore it when listeners are invoked. This allows\n * walking the declaration view tree in listeners to get vars from parent views.\n *\n * @param viewToRestore The OpaqueViewState instance to restore.\n * @returns Context of the restored OpaqueViewState instance.\n *\n * @codeGenApi\n */\nfunction ɵɵrestoreView(viewToRestore) {\n  instructionState.lFrame.contextLView = viewToRestore;\n  return viewToRestore[CONTEXT];\n}\n/**\n * Clears the view set in `ɵɵrestoreView` from memory. Returns the passed in\n * value so that it can be used as a return value of an instruction.\n *\n * @codeGenApi\n */\nfunction ɵɵresetView(value) {\n  instructionState.lFrame.contextLView = null;\n  return value;\n}\nfunction getCurrentTNode() {\n  let currentTNode = getCurrentTNodePlaceholderOk();\n  while (currentTNode !== null && currentTNode.type === 64 /* TNodeType.Placeholder */) {\n    currentTNode = currentTNode.parent;\n  }\n  return currentTNode;\n}\nfunction getCurrentTNodePlaceholderOk() {\n  return instructionState.lFrame.currentTNode;\n}\nfunction getCurrentParentTNode() {\n  const lFrame = instructionState.lFrame;\n  const currentTNode = lFrame.currentTNode;\n  return lFrame.isParent ? currentTNode : currentTNode.parent;\n}\nfunction setCurrentTNode(tNode, isParent) {\n  ngDevMode && tNode && assertTNodeForTView(tNode, instructionState.lFrame.tView);\n  const lFrame = instructionState.lFrame;\n  lFrame.currentTNode = tNode;\n  lFrame.isParent = isParent;\n}\nfunction isCurrentTNodeParent() {\n  return instructionState.lFrame.isParent;\n}\nfunction setCurrentTNodeAsNotParent() {\n  instructionState.lFrame.isParent = false;\n}\nfunction getContextLView() {\n  const contextLView = instructionState.lFrame.contextLView;\n  ngDevMode && assertDefined(contextLView, 'contextLView must be defined.');\n  return contextLView;\n}\nfunction isInCheckNoChangesMode() {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode !== CheckNoChangesMode.Off;\n}\nfunction isExhaustiveCheckNoChanges() {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode === CheckNoChangesMode.Exhaustive;\n}\nfunction setIsInCheckNoChangesMode(mode) {\n  !ngDevMode && throwError('Must never be called in production mode');\n  _checkNoChangesMode = mode;\n}\nfunction isRefreshingViews() {\n  return _isRefreshingViews;\n}\nfunction setIsRefreshingViews(mode) {\n  const prev = _isRefreshingViews;\n  _isRefreshingViews = mode;\n  return prev;\n}\n// top level variables should not be exported for performance reasons (PERF_NOTES.md)\nfunction getBindingRoot() {\n  const lFrame = instructionState.lFrame;\n  let index = lFrame.bindingRootIndex;\n  if (index === -1) {\n    index = lFrame.bindingRootIndex = lFrame.tView.bindingStartIndex;\n  }\n  return index;\n}\nfunction getBindingIndex() {\n  return instructionState.lFrame.bindingIndex;\n}\nfunction setBindingIndex(value) {\n  return instructionState.lFrame.bindingIndex = value;\n}\nfunction nextBindingIndex() {\n  return instructionState.lFrame.bindingIndex++;\n}\nfunction incrementBindingIndex(count) {\n  const lFrame = instructionState.lFrame;\n  const index = lFrame.bindingIndex;\n  lFrame.bindingIndex = lFrame.bindingIndex + count;\n  return index;\n}\nfunction isInI18nBlock() {\n  return instructionState.lFrame.inI18n;\n}\nfunction setInI18nBlock(isInI18nBlock) {\n  instructionState.lFrame.inI18n = isInI18nBlock;\n}\n/**\n * Set a new binding root index so that host template functions can execute.\n *\n * Bindings inside the host template are 0 index. But because we don't know ahead of time\n * how many host bindings we have we can't pre-compute them. For this reason they are all\n * 0 index and we just shift the root so that they match next available location in the LView.\n *\n * @param bindingRootIndex Root index for `hostBindings`\n * @param currentDirectiveIndex `TData[currentDirectiveIndex]` will point to the current directive\n *        whose `hostBindings` are being processed.\n */\nfunction setBindingRootForHostBindings(bindingRootIndex, currentDirectiveIndex) {\n  const lFrame = instructionState.lFrame;\n  lFrame.bindingIndex = lFrame.bindingRootIndex = bindingRootIndex;\n  setCurrentDirectiveIndex(currentDirectiveIndex);\n}\n/**\n * When host binding is executing this points to the directive index.\n * `TView.data[getCurrentDirectiveIndex()]` is `DirectiveDef`\n * `LView[getCurrentDirectiveIndex()]` is directive instance.\n */\nfunction getCurrentDirectiveIndex() {\n  return instructionState.lFrame.currentDirectiveIndex;\n}\n/**\n * Sets an index of a directive whose `hostBindings` are being processed.\n *\n * @param currentDirectiveIndex `TData` index where current directive instance can be found.\n */\nfunction setCurrentDirectiveIndex(currentDirectiveIndex) {\n  instructionState.lFrame.currentDirectiveIndex = currentDirectiveIndex;\n}\n/**\n * Retrieve the current `DirectiveDef` which is active when `hostBindings` instruction is being\n * executed.\n *\n * @param tData Current `TData` where the `DirectiveDef` will be looked up at.\n */\nfunction getCurrentDirectiveDef(tData) {\n  const currentDirectiveIndex = instructionState.lFrame.currentDirectiveIndex;\n  return currentDirectiveIndex === -1 ? null : tData[currentDirectiveIndex];\n}\nfunction getCurrentQueryIndex() {\n  return instructionState.lFrame.currentQueryIndex;\n}\nfunction setCurrentQueryIndex(value) {\n  instructionState.lFrame.currentQueryIndex = value;\n}\n/**\n * Returns a `TNode` of the location where the current `LView` is declared at.\n *\n * @param lView an `LView` that we want to find parent `TNode` for.\n */\nfunction getDeclarationTNode(lView) {\n  const tView = lView[TVIEW];\n  // Return the declaration parent for embedded views\n  if (tView.type === 2 /* TViewType.Embedded */) {\n    ngDevMode && assertDefined(tView.declTNode, 'Embedded TNodes should have declaration parents.');\n    return tView.declTNode;\n  }\n  // Components don't have `TView.declTNode` because each instance of component could be\n  // inserted in different location, hence `TView.declTNode` is meaningless.\n  // Falling back to `T_HOST` in case we cross component boundary.\n  if (tView.type === 1 /* TViewType.Component */) {\n    return lView[T_HOST];\n  }\n  // Remaining TNode type is `TViewType.Root` which doesn't have a parent TNode.\n  return null;\n}\n/**\n * This is a light weight version of the `enterView` which is needed by the DI system.\n *\n * @param lView `LView` location of the DI context.\n * @param tNode `TNode` for DI context\n * @param flags DI context flags. if `SkipSelf` flag is set than we walk up the declaration\n *     tree from `tNode`  until we find parent declared `TElementNode`.\n * @returns `true` if we have successfully entered DI associated with `tNode` (or with declared\n *     `TNode` if `flags` has  `SkipSelf`). Failing to enter DI implies that no associated\n *     `NodeInjector` can be found and we should instead use `ModuleInjector`.\n *     - If `true` than this call must be fallowed by `leaveDI`\n *     - If `false` than this call failed and we should NOT call `leaveDI`\n */\nfunction enterDI(lView, tNode, flags) {\n  ngDevMode && assertLViewOrUndefined(lView);\n  if (flags & 4 /* InternalInjectFlags.SkipSelf */) {\n    ngDevMode && assertTNodeForTView(tNode, lView[TVIEW]);\n    let parentTNode = tNode;\n    let parentLView = lView;\n    while (true) {\n      ngDevMode && assertDefined(parentTNode, 'Parent TNode should be defined');\n      parentTNode = parentTNode.parent;\n      if (parentTNode === null && !(flags & 1 /* InternalInjectFlags.Host */)) {\n        parentTNode = getDeclarationTNode(parentLView);\n        if (parentTNode === null) break;\n        // In this case, a parent exists and is definitely an element. So it will definitely\n        // have an existing lView as the declaration view, which is why we can assume it's defined.\n        ngDevMode && assertDefined(parentLView, 'Parent LView should be defined');\n        parentLView = parentLView[DECLARATION_VIEW];\n        // In Ivy there are Comment nodes that correspond to ngIf and NgFor embedded directives\n        // We want to skip those and look only at Elements and ElementContainers to ensure\n        // we're looking at true parent nodes, and not content or other types.\n        if (parentTNode.type & (2 /* TNodeType.Element */ | 8 /* TNodeType.ElementContainer */)) {\n          break;\n        }\n      } else {\n        break;\n      }\n    }\n    if (parentTNode === null) {\n      // If we failed to find a parent TNode this means that we should use module injector.\n      return false;\n    } else {\n      tNode = parentTNode;\n      lView = parentLView;\n    }\n  }\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  const lFrame = instructionState.lFrame = allocLFrame();\n  lFrame.currentTNode = tNode;\n  lFrame.lView = lView;\n  return true;\n}\n/**\n * Swap the current lView with a new lView.\n *\n * For performance reasons we store the lView in the top level of the module.\n * This way we minimize the number of properties to read. Whenever a new view\n * is entered we have to store the lView for later, and when the view is\n * exited the state has to be restored\n *\n * @param newView New lView to become active\n * @returns the previously active lView;\n */\nfunction enterView(newView) {\n  ngDevMode && assertNotEqual(newView[0], newView[1], '????');\n  ngDevMode && assertLViewOrUndefined(newView);\n  const newLFrame = allocLFrame();\n  if (ngDevMode) {\n    assertEqual(newLFrame.isParent, true, 'Expected clean LFrame');\n    assertEqual(newLFrame.lView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.tView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.selectedIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.elementDepthCount, 0, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentDirectiveIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentNamespace, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.bindingRootIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentQueryIndex, 0, 'Expected clean LFrame');\n  }\n  const tView = newView[TVIEW];\n  instructionState.lFrame = newLFrame;\n  ngDevMode && tView.firstChild && assertTNodeForTView(tView.firstChild, tView);\n  newLFrame.currentTNode = tView.firstChild;\n  newLFrame.lView = newView;\n  newLFrame.tView = tView;\n  newLFrame.contextLView = newView;\n  newLFrame.bindingIndex = tView.bindingStartIndex;\n  newLFrame.inI18n = false;\n}\n/**\n * Allocates next free LFrame. This function tries to reuse the `LFrame`s to lower memory pressure.\n */\nfunction allocLFrame() {\n  const currentLFrame = instructionState.lFrame;\n  const childLFrame = currentLFrame === null ? null : currentLFrame.child;\n  const newLFrame = childLFrame === null ? createLFrame(currentLFrame) : childLFrame;\n  return newLFrame;\n}\nfunction createLFrame(parent) {\n  const lFrame = {\n    currentTNode: null,\n    isParent: true,\n    lView: null,\n    tView: null,\n    selectedIndex: -1,\n    contextLView: null,\n    elementDepthCount: 0,\n    currentNamespace: null,\n    currentDirectiveIndex: -1,\n    bindingRootIndex: -1,\n    bindingIndex: -1,\n    currentQueryIndex: 0,\n    parent: parent,\n    child: null,\n    inI18n: false\n  };\n  parent !== null && (parent.child = lFrame); // link the new LFrame for reuse.\n  return lFrame;\n}\n/**\n * A lightweight version of leave which is used with DI.\n *\n * This function only resets `currentTNode` and `LView` as those are the only properties\n * used with DI (`enterDI()`).\n *\n * NOTE: This function is reexported as `leaveDI`. However `leaveDI` has return type of `void` where\n * as `leaveViewLight` has `LFrame`. This is so that `leaveViewLight` can be used in `leaveView`.\n */\nfunction leaveViewLight() {\n  const oldLFrame = instructionState.lFrame;\n  instructionState.lFrame = oldLFrame.parent;\n  oldLFrame.currentTNode = null;\n  oldLFrame.lView = null;\n  return oldLFrame;\n}\n/**\n * This is a lightweight version of the `leaveView` which is needed by the DI system.\n *\n * NOTE: this function is an alias so that we can change the type of the function to have `void`\n * return type.\n */\nconst leaveDI = leaveViewLight;\n/**\n * Leave the current `LView`\n *\n * This pops the `LFrame` with the associated `LView` from the stack.\n *\n * IMPORTANT: We must zero out the `LFrame` values here otherwise they will be retained. This is\n * because for performance reasons we don't release `LFrame` but rather keep it for next use.\n */\nfunction leaveView() {\n  const oldLFrame = leaveViewLight();\n  oldLFrame.isParent = true;\n  oldLFrame.tView = null;\n  oldLFrame.selectedIndex = -1;\n  oldLFrame.contextLView = null;\n  oldLFrame.elementDepthCount = 0;\n  oldLFrame.currentDirectiveIndex = -1;\n  oldLFrame.currentNamespace = null;\n  oldLFrame.bindingRootIndex = -1;\n  oldLFrame.bindingIndex = -1;\n  oldLFrame.currentQueryIndex = 0;\n}\nfunction nextContextImpl(level) {\n  const contextLView = instructionState.lFrame.contextLView = walkUpViews(level, instructionState.lFrame.contextLView);\n  return contextLView[CONTEXT];\n}\n/**\n * Gets the currently selected element index.\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n */\nfunction getSelectedIndex() {\n  return instructionState.lFrame.selectedIndex;\n}\n/**\n * Sets the most recent index passed to {@link select}\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n *\n * (Note that if an \"exit function\" was set earlier (via `setElementExitFn()`) then that will be\n * run if and when the provided `index` value is different from the current selected index value.)\n */\nfunction setSelectedIndex(index) {\n  ngDevMode && index !== -1 && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Index must be past HEADER_OFFSET (or -1).');\n  ngDevMode && assertLessThan(index, instructionState.lFrame.lView.length, \"Can't set index passed end of LView\");\n  instructionState.lFrame.selectedIndex = index;\n}\n/**\n * Gets the `tNode` that represents currently selected element.\n */\nfunction getSelectedTNode() {\n  const lFrame = instructionState.lFrame;\n  return getTNode(lFrame.tView, lFrame.selectedIndex);\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/2000/svg'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceSVG() {\n  instructionState.lFrame.currentNamespace = SVG_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/1998/MathML/'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceMathML() {\n  instructionState.lFrame.currentNamespace = MATH_ML_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceHTML() {\n  namespaceHTMLInternal();\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n */\nfunction namespaceHTMLInternal() {\n  instructionState.lFrame.currentNamespace = null;\n}\nfunction getNamespace() {\n  return instructionState.lFrame.currentNamespace;\n}\nlet _wasLastNodeCreated = true;\n/**\n * Retrieves a global flag that indicates whether the most recent DOM node\n * was created or hydrated.\n */\nfunction wasLastNodeCreated() {\n  return _wasLastNodeCreated;\n}\n/**\n * Sets a global flag to indicate whether the most recent DOM node\n * was created or hydrated.\n */\nfunction lastNodeWasCreated(flag) {\n  _wasLastNodeCreated = flag;\n}\n\n/**\n * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.\n */\nfunction createInjector(defType, parent = null, additionalProviders = null, name) {\n  const injector = createInjectorWithoutInjectorInstances(defType, parent, additionalProviders, name);\n  injector.resolveInjectorInitializers();\n  return injector;\n}\n/**\n * Creates a new injector without eagerly resolving its injector types. Can be used in places\n * where resolving the injector types immediately can lead to an infinite loop. The injector types\n * should be resolved at a later point by calling `_resolveInjectorDefTypes`.\n */\nfunction createInjectorWithoutInjectorInstances(defType, parent = null, additionalProviders = null, name, scopes = new Set()) {\n  const providers = [additionalProviders || EMPTY_ARRAY, importProvidersFrom(defType)];\n  name = name || (typeof defType === 'object' ? undefined : stringify(defType));\n  return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);\n}\n\n/**\n * Concrete injectors implement this interface. Injectors are configured\n * with [providers](guide/di/dependency-injection-providers) that associate\n * dependencies of various types with [injection tokens](guide/di/dependency-injection-providers).\n *\n * @see [DI Providers](guide/di/dependency-injection-providers).\n * @see {@link StaticProvider}\n *\n * @usageNotes\n *\n *  The following example creates a service injector instance.\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Usage example\n *\n * {@example core/di/ts/injector_spec.ts region='Injector'}\n *\n * `Injector` returns itself when given `Injector` as a token:\n *\n * {@example core/di/ts/injector_spec.ts region='injectInjector'}\n *\n * @publicApi\n */\nclass Injector {\n  static THROW_IF_NOT_FOUND = THROW_IF_NOT_FOUND;\n  static NULL = /*#__PURE__*/new NullInjector();\n  static create(options, parent) {\n    if (Array.isArray(options)) {\n      return createInjector({\n        name: ''\n      }, parent, options, '');\n    } else {\n      const name = options.name ?? '';\n      return createInjector({\n        name\n      }, options.parent, options.providers, name);\n    }\n  }\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n    token: Injector,\n    providedIn: 'any',\n    factory: () => ɵɵinject(INJECTOR$1)\n  });\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__ = -1 /* InjectorMarkers.Injector */;\n}\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [Domino](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = /*#__PURE__*/new InjectionToken(ngDevMode ? 'DocumentToken' : '');\n\n/**\n * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.\n * The scope of this destruction depends on where `DestroyRef` is injected. If `DestroyRef`\n * is injected in a component or directive, the callbacks run when that component or\n * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.\n *\n * @publicApi\n */\nlet DestroyRef = /*#__PURE__*/(() => {\n  class DestroyRef {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectDestroyRef;\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ENV_ID__ = injector => injector;\n  }\n  return DestroyRef;\n})();\nclass NodeInjectorDestroyRef extends DestroyRef {\n  _lView;\n  constructor(_lView) {\n    super();\n    this._lView = _lView;\n  }\n  get destroyed() {\n    return isDestroyed(this._lView);\n  }\n  onDestroy(callback) {\n    const lView = this._lView;\n    storeLViewOnDestroy(lView, callback);\n    return () => removeLViewOnDestroy(lView, callback);\n  }\n}\nfunction injectDestroyRef() {\n  return new NodeInjectorDestroyRef(getLView());\n}\n\n/**\n * Provides a hook for centralized exception handling.\n *\n * The default implementation of `ErrorHandler` prints error messages to the `console`. To\n * intercept error handling, write a custom exception handler that replaces this default as\n * appropriate for your app.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * class MyErrorHandler implements ErrorHandler {\n *   handleError(error) {\n *     // do something with the exception\n *   }\n * }\n *\n * // Provide in standalone apps\n * bootstrapApplication(AppComponent, {\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n *\n * // Provide in module-based apps\n * @NgModule({\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n * class MyModule {}\n * ```\n *\n * @publicApi\n */\nclass ErrorHandler {\n  /**\n   * @internal\n   */\n  _console = console;\n  handleError(error) {\n    this._console.error('ERROR', error);\n  }\n}\n/**\n * `InjectionToken` used to configure how to call the `ErrorHandler`.\n */\nconst INTERNAL_APPLICATION_ERROR_HANDLER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'internal error handler' : '', {\n  providedIn: 'root',\n  factory: () => {\n    // The user's error handler may depend on things that create a circular dependency\n    // so we inject it lazily.\n    const injector = inject(EnvironmentInjector);\n    let userErrorHandler;\n    return e => {\n      userErrorHandler ??= injector.get(ErrorHandler);\n      userErrorHandler.handleError(e);\n    };\n  }\n});\nconst errorHandlerEnvironmentInitializer = {\n  provide: ENVIRONMENT_INITIALIZER,\n  useValue: () => void inject(ErrorHandler),\n  multi: true\n};\nconst globalErrorListeners = /*#__PURE__*/new InjectionToken(ngDevMode ? 'GlobalErrorListeners' : '', {\n  providedIn: 'root',\n  factory: () => {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      return;\n    }\n    const window = inject(DOCUMENT).defaultView;\n    if (!window) {\n      return;\n    }\n    const errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    const rejectionListener = e => {\n      errorHandler(e.reason);\n      e.preventDefault();\n    };\n    const errorListener = e => {\n      errorHandler(e.error);\n      e.preventDefault();\n    };\n    const setupEventListeners = () => {\n      window.addEventListener('unhandledrejection', rejectionListener);\n      window.addEventListener('error', errorListener);\n    };\n    // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n    // the scope of this functionality.\n    if (typeof Zone !== 'undefined') {\n      Zone.root.run(setupEventListeners);\n    } else {\n      setupEventListeners();\n    }\n    inject(DestroyRef).onDestroy(() => {\n      window.removeEventListener('error', errorListener);\n      window.removeEventListener('unhandledrejection', rejectionListener);\n    });\n  }\n});\n/**\n * Provides an environment initializer which forwards unhandled errors to the ErrorHandler.\n *\n * The listeners added are for the window's 'unhandledrejection' and 'error' events.\n *\n * @publicApi\n */\nfunction provideBrowserGlobalErrorListeners() {\n  return makeEnvironmentProviders([provideEnvironmentInitializer(() => void inject(globalErrorListeners))]);\n}\n\n/**\n * Checks if the given `value` is a reactive `Signal`.\n *\n * @publicApi 17.0\n */\nfunction isSignal(value) {\n  return typeof value === 'function' && value[SIGNAL] !== undefined;\n}\n\n/**\n * Utility function used during template type checking to extract the value from a `WritableSignal`.\n * @codeGenApi\n */\nfunction ɵunwrapWritableSignal(value) {\n  // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,\n  // because the latter incorrectly unwraps non-signal getter functions.\n  return null;\n}\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction signal(initialValue, options) {\n  const [get, set, update] = createSignal(initialValue, options?.equal);\n  const signalFn = get;\n  const node = signalFn[SIGNAL];\n  signalFn.set = set;\n  signalFn.update = update;\n  signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);\n  if (ngDevMode) {\n    signalFn.toString = () => `[Signal: ${signalFn()}]`;\n    node.debugName = options?.debugName;\n  }\n  return signalFn;\n}\nfunction signalAsReadonlyFn() {\n  const node = this[SIGNAL];\n  if (node.readonlyFn === undefined) {\n    const readonlyFn = () => this();\n    readonlyFn[SIGNAL] = node;\n    node.readonlyFn = readonlyFn;\n  }\n  return node.readonlyFn;\n}\n/**\n * Checks if the given `value` is a writeable signal.\n */\nfunction isWritableSignal(value) {\n  return isSignal(value) && typeof value.set === 'function';\n}\n\n/**\n * Injectable that is notified when an `LView` is made aware of changes to application state.\n */\nclass ChangeDetectionScheduler {}\n/** Token used to indicate if zoneless was enabled via provideZonelessChangeDetection(). */\nconst ZONELESS_ENABLED = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless enabled' : '', {\n  providedIn: 'root',\n  factory: () => false\n});\n/** Token used to indicate `provideZonelessChangeDetection` was used. */\nconst PROVIDED_ZONELESS = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless provided' : '', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst ZONELESS_SCHEDULER_DISABLED = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'scheduler disabled' : '');\n// TODO(atscott): Remove in v19. Scheduler should be done with runOutsideAngular.\nconst SCHEDULE_IN_ROOT_ZONE = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'run changes outside zone in root' : '');\n\n/**\n * Asserts that the current stack frame is not within a reactive context. Useful\n * to disallow certain code from running inside a reactive context (see {@link /api/core/rxjs/toSignal toSignal})\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertNotInReactiveContext(debugFn, extraContext) {\n  // Taking a `Function` instead of a string name here prevents the un-minified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (getActiveConsumer() !== null) {\n    throw new RuntimeError(-602 /* RuntimeErrorCode.ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT */, ngDevMode && `${debugFn.name}() cannot be called from within a reactive context.${extraContext ? ` ${extraContext}` : ''}`);\n  }\n}\nlet ViewContext = /*#__PURE__*/(() => {\n  class ViewContext {\n    view;\n    node;\n    constructor(view, node) {\n      this.view = view;\n      this.node = node;\n    }\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectViewContext;\n  }\n  return ViewContext;\n})();\nfunction injectViewContext() {\n  return new ViewContext(getLView(), getCurrentTNode());\n}\n\n/**\n * Internal implementation of the pending tasks service.\n */\nlet PendingTasksInternal = /*#__PURE__*/(() => {\n  class PendingTasksInternal {\n    taskId = 0;\n    pendingTasks = new Set();\n    destroyed = false;\n    pendingTask = new BehaviorSubject(false);\n    get hasPendingTasks() {\n      // Accessing the value of a closed `BehaviorSubject` throws an error.\n      return this.destroyed ? false : this.pendingTask.value;\n    }\n    /**\n     * In case the service is about to be destroyed, return a self-completing observable.\n     * Otherwise, return the observable that emits the current state of pending tasks.\n     */\n    get hasPendingTasksObservable() {\n      if (this.destroyed) {\n        // Manually creating the observable pulls less symbols from RxJS than `of(false)`.\n        return new Observable(subscriber => {\n          subscriber.next(false);\n          subscriber.complete();\n        });\n      }\n      return this.pendingTask;\n    }\n    add() {\n      // Emitting a value to a closed subject throws an error.\n      if (!this.hasPendingTasks && !this.destroyed) {\n        this.pendingTask.next(true);\n      }\n      const taskId = this.taskId++;\n      this.pendingTasks.add(taskId);\n      return taskId;\n    }\n    has(taskId) {\n      return this.pendingTasks.has(taskId);\n    }\n    remove(taskId) {\n      this.pendingTasks.delete(taskId);\n      if (this.pendingTasks.size === 0 && this.hasPendingTasks) {\n        this.pendingTask.next(false);\n      }\n    }\n    ngOnDestroy() {\n      this.pendingTasks.clear();\n      if (this.hasPendingTasks) {\n        this.pendingTask.next(false);\n      }\n      // We call `unsubscribe()` to release observers, as users may forget to\n      // unsubscribe manually when subscribing to `isStable`. We do not call\n      // `complete()` because it is unsafe; if someone subscribes using the `first`\n      // operator and the observable completes before emitting a value,\n      // RxJS will throw an error.\n      this.destroyed = true;\n      this.pendingTask.unsubscribe();\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: PendingTasksInternal,\n      providedIn: 'root',\n      factory: () => new PendingTasksInternal()\n    });\n  }\n  return PendingTasksInternal;\n})();\n/**\n * Service that keeps track of pending tasks contributing to the stableness of Angular\n * application. While several existing Angular services (ex.: `HttpClient`) will internally manage\n * tasks influencing stability, this API gives control over stability to library and application\n * developers for specific cases not covered by Angular internals.\n *\n * The concept of stability comes into play in several important scenarios:\n * - SSR process needs to wait for the application stability before serializing and sending rendered\n * HTML;\n * - tests might want to delay assertions until the application becomes stable;\n *\n * @usageNotes\n * ```ts\n * const pendingTasks = inject(PendingTasks);\n * const taskCleanup = pendingTasks.add();\n * // do work that should block application's stability and then:\n * taskCleanup();\n * ```\n *\n * @publicApi 20.0\n */\nlet PendingTasks = /*#__PURE__*/(() => {\n  class PendingTasks {\n    internalPendingTasks = inject(PendingTasksInternal);\n    scheduler = inject(ChangeDetectionScheduler);\n    errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    /**\n     * Adds a new task that should block application's stability.\n     * @returns A cleanup function that removes a task when called.\n     */\n    add() {\n      const taskId = this.internalPendingTasks.add();\n      return () => {\n        if (!this.internalPendingTasks.has(taskId)) {\n          // This pending task has already been cleared.\n          return;\n        }\n        // Notifying the scheduler will hold application stability open until the next tick.\n        this.scheduler.notify(11 /* NotificationSource.PendingTaskRemoved */);\n        this.internalPendingTasks.remove(taskId);\n      };\n    }\n    /**\n     * Runs an asynchronous function and blocks the application's stability until the function completes.\n     *\n     * ```ts\n     * pendingTasks.run(async () => {\n     *   const userData = await fetch('/api/user');\n     *   this.userData.set(userData);\n     * });\n     * ```\n     *\n     * @param fn The asynchronous function to execute\n     * @developerPreview 19.0\n     */\n    run(fn) {\n      const removeTask = this.add();\n      fn().catch(this.errorHandler).finally(removeTask);\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: PendingTasks,\n      providedIn: 'root',\n      factory: () => new PendingTasks()\n    });\n  }\n  return PendingTasks;\n})();\nfunction noop(...args) {\n  // Do nothing.\n}\n\n/**\n * A scheduler which manages the execution of effects.\n */\nlet EffectScheduler = /*#__PURE__*/(() => {\n  class EffectScheduler {\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: EffectScheduler,\n      providedIn: 'root',\n      factory: () => new ZoneAwareEffectScheduler()\n    });\n  }\n  return EffectScheduler;\n})();\n/**\n * A wrapper around `ZoneAwareQueueingScheduler` that schedules flushing via the microtask queue\n * when.\n */\nclass ZoneAwareEffectScheduler {\n  dirtyEffectCount = 0;\n  queues = /*#__PURE__*/new Map();\n  add(handle) {\n    this.enqueue(handle);\n    this.schedule(handle);\n  }\n  schedule(handle) {\n    if (!handle.dirty) {\n      return;\n    }\n    this.dirtyEffectCount++;\n  }\n  remove(handle) {\n    const zone = handle.zone;\n    const queue = this.queues.get(zone);\n    if (!queue.has(handle)) {\n      return;\n    }\n    queue.delete(handle);\n    if (handle.dirty) {\n      this.dirtyEffectCount--;\n    }\n  }\n  enqueue(handle) {\n    const zone = handle.zone;\n    if (!this.queues.has(zone)) {\n      this.queues.set(zone, new Set());\n    }\n    const queue = this.queues.get(zone);\n    if (queue.has(handle)) {\n      return;\n    }\n    queue.add(handle);\n  }\n  /**\n   * Run all scheduled effects.\n   *\n   * Execution order of effects within the same zone is guaranteed to be FIFO, but there is no\n   * ordering guarantee between effects scheduled in different zones.\n   */\n  flush() {\n    while (this.dirtyEffectCount > 0) {\n      let ranOneEffect = false;\n      for (const [zone, queue] of this.queues) {\n        // `zone` here must be defined.\n        if (zone === null) {\n          ranOneEffect ||= this.flushQueue(queue);\n        } else {\n          ranOneEffect ||= zone.run(() => this.flushQueue(queue));\n        }\n      }\n      // Safeguard against infinite looping if somehow our dirty effect count gets out of sync with\n      // the dirty flag across all the effects.\n      if (!ranOneEffect) {\n        this.dirtyEffectCount = 0;\n      }\n    }\n  }\n  flushQueue(queue) {\n    let ranOneEffect = false;\n    for (const handle of queue) {\n      if (!handle.dirty) {\n        continue;\n      }\n      this.dirtyEffectCount--;\n      ranOneEffect = true;\n      // TODO: what happens if this throws an error?\n      handle.run();\n    }\n    return ranOneEffect;\n  }\n}\nexport { AFTER_RENDER_SEQUENCES_TO_ADD, CHILD_HEAD, CHILD_TAIL, CLEANUP, CONTAINER_HEADER_OFFSET, CONTEXT, ChangeDetectionScheduler, CheckNoChangesMode, DECLARATION_COMPONENT_VIEW, DECLARATION_LCONTAINER, DECLARATION_VIEW, DEHYDRATED_VIEWS, DOCUMENT, DestroyRef, EFFECTS, EFFECTS_TO_SCHEDULE, EMBEDDED_VIEW_INJECTOR, EMPTY_ARRAY, EMPTY_OBJ, ENVIRONMENT, ENVIRONMENT_INITIALIZER, EffectScheduler, EnvironmentInjector, ErrorHandler, FLAGS, HEADER_OFFSET, HOST, HYDRATION, ID, INJECTOR$1 as INJECTOR, INJECTOR as INJECTOR$1, INJECTOR_DEF_TYPES, INJECTOR_SCOPE, INTERNAL_APPLICATION_ERROR_HANDLER, InjectionToken, Injector, MATH_ML_NAMESPACE, MOVED_VIEWS, NATIVE, NEXT, NG_COMP_DEF, NG_DIR_DEF, NG_ELEMENT_ID, NG_FACTORY_DEF, NG_INJ_DEF, NG_MOD_DEF, NG_PIPE_DEF, NG_PROV_DEF, NodeInjectorDestroyRef, NullInjector, ON_DESTROY_HOOKS, PARENT, PREORDER_HOOK_FLAGS, PROVIDED_ZONELESS, PendingTasks, PendingTasksInternal, QUERIES, R3Injector, REACTIVE_TEMPLATE_CONSUMER, RENDERER, RuntimeError, SCHEDULE_IN_ROOT_ZONE, SVG_NAMESPACE, TVIEW, T_HOST, VIEW_REFS, ViewContext, XSS_SECURITY_URL, ZONELESS_ENABLED, ZONELESS_SCHEDULER_DISABLED, _global, addToArray, arrayEquals, arrayInsert2, arraySplice, assertComponentType, assertDefined, assertDirectiveDef, assertDomNode, assertElement, assertEqual, assertFirstCreatePass, assertFirstUpdatePass, assertFunction, assertGreaterThan, assertGreaterThanOrEqual, assertHasParent, assertInInjectionContext, assertIndexInDeclRange, assertIndexInExpandoRange, assertIndexInRange, assertInjectImplementationNotEqual, assertLContainer, assertLView, assertLessThan, assertNgModuleType, assertNodeInjector, assertNotDefined, assertNotEqual, assertNotInReactiveContext, assertNotReactive, assertNotSame, assertNumber, assertNumberInRange, assertOneOf, assertParentView, assertProjectionSlots, assertSame, assertString, assertTIcu, assertTNode, assertTNodeForLView, assertTNodeForTView, attachInjectFlag, concatStringsWithSpace, convertToBitFlags, createInjector, createInjectorWithoutInjectorInstances, debugStringifyTypeForError, decreaseElementDepthCount, deepForEach, defineInjectable, emitEffectCreatedEvent, emitInjectEvent, emitInjectorToCreateInstanceEvent, emitInstanceCreatedByInjectorEvent, emitProviderConfiguredEvent, enterDI, enterSkipHydrationBlock, enterView, errorHandlerEnvironmentInitializer, fillProperties, flatten, formatRuntimeError, forwardRef, getBindingIndex, getBindingRoot, getBindingsEnabled, getClosureSafeProperty, getComponentDef, getComponentLViewByIndex, getConstant, getContextLView, getCurrentDirectiveDef, getCurrentDirectiveIndex, getCurrentParentTNode, getCurrentQueryIndex, getCurrentTNode, getCurrentTNodePlaceholderOk, getDirectiveDef, getDirectiveDefOrThrow, getElementDepthCount, getFactoryDef, getInjectableDef, getInjectorDef, getLView, getLViewParent, getNamespace, getNativeByIndex, getNativeByTNode, getNativeByTNodeOrNull, getNgModuleDef, getNgModuleDefOrThrow, getNullInjector, getOrCreateLViewCleanup, getOrCreateTViewCleanup, getPipeDef, getSelectedIndex, getSelectedTNode, getTNode, getTView, hasI18n, importProvidersFrom, increaseElementDepthCount, incrementBindingIndex, initNgDevMode, inject, injectRootLimpMode, internalImportProvidersFrom, isClassProvider, isComponentDef, isComponentHost, isContentQueryHost, isCreationMode, isCurrentTNodeParent, isDestroyed, isDirectiveHost, isEnvironmentProviders, isExhaustiveCheckNoChanges, isForwardRef, isInCheckNoChangesMode, isInI18nBlock, isInInjectionContext, isInSkipHydrationBlock, isInjectable, isLContainer, isLView, isProjectionTNode, isRefreshingViews, isRootView, isSignal, isSkipHydrationRootTNode, isStandalone, isTypeProvider, isWritableSignal, keyValueArrayGet, keyValueArrayIndexOf, keyValueArraySet, lastNodeWasCreated, leaveDI, leaveSkipHydrationBlock, leaveView, load, makeEnvironmentProviders, markAncestorsForTraversal, markViewForRefresh, newArray, nextBindingIndex, nextContextImpl, noop, provideBrowserGlobalErrorListeners, provideEnvironmentInitializer, providerToFactory, removeFromArray, removeLViewOnDestroy, renderStringify, requiresRefreshOrTraversal, resetPreOrderHookFlags, resolveForwardRef, runInInjectionContext, runInInjectorProfilerContext, setBindingIndex, setBindingRootForHostBindings, setCurrentDirectiveIndex, setCurrentQueryIndex, setCurrentTNode, setCurrentTNodeAsNotParent, setInI18nBlock, setInjectImplementation, setInjectorProfiler, setInjectorProfilerContext, setIsInCheckNoChangesMode, setIsRefreshingViews, setSelectedIndex, signal, signalAsReadonlyFn, store, storeCleanupWithContext, storeLViewOnDestroy, stringify, stringifyForError, throwCyclicDependencyError, throwError, throwProviderNotFoundError, truncateMiddle, unwrapLView, unwrapRNode, updateAncestorTraversalFlagsOnAttach, viewAttachedToChangeDetector, viewAttachedToContainer, walkProviderTree, walkUpViews, wasLastNodeCreated, ɵunwrapWritableSignal, ɵɵdefineInjectable, ɵɵdefineInjector, ɵɵdisableBindings, ɵɵenableBindings, ɵɵinject, ɵɵinvalidFactoryDep, ɵɵnamespaceHTML, ɵɵnamespaceMathML, ɵɵnamespaceSVG, ɵɵresetView, ɵɵrestoreView };", "map": {"version": 3, "names": ["isNotFound", "getCurrentInjector", "setCurrentInjector", "getActiveConsumer", "SIGNAL", "createSignal", "BehaviorSubject", "Observable", "NotFoundError", "isNotFound$1", "setActiveConsumer", "ERROR_DETAILS_PAGE_BASE_URL", "XSS_SECURITY_URL", "RuntimeError", "Error", "code", "constructor", "message", "formatRuntimeError", "formatRuntimeErrorCode", "Math", "abs", "fullCode", "errorMessage", "ngDevMode", "addPeriodSeparator", "match", "separator", "_global", "globalThis", "ngDevModeResetPerfCounters", "locationString", "location", "toString", "newCounters", "hydratedNodes", "hydratedComponents", "dehydratedViewsRemoved", "dehydratedViewsCleanupRuns", "componentsSkippedHydration", "deferBlocksWithIncrementalHydration", "allowNgDevModeTrue", "indexOf", "Object", "assign", "initNgDevMode", "keys", "length", "getClosureSafeProperty", "objWithPropertyToExtract", "key", "fillProperties", "target", "source", "hasOwnProperty", "stringify", "token", "Array", "isArray", "map", "join", "name", "overriddenName", "result", "newLineIndex", "slice", "concatStringsWithSpace", "before", "after", "truncateMiddle", "str", "max<PERSON><PERSON><PERSON>", "substring", "halfLimit", "round", "__forward_ref__", "forwardRef", "forwardRefFn", "resolveForwardRef", "type", "isForwardRef", "fn", "assertNumber", "actual", "msg", "throwError", "assertNumberInRange", "minInclusive", "maxInclusive", "assertLessThanOrEqual", "assertGreaterThanOrEqual", "assertString", "assertFunction", "assertEqual", "expected", "assertNotEqual", "assertSame", "assertNotSame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assertNotDefined", "assertDefined", "comparison", "assertDomNode", "node", "Node", "assertElement", "Element", "assertIndexInRange", "arr", "index", "maxLen", "assertOneOf", "value", "validValues", "JSON", "assertNotReactive", "ɵɵdefineInjectable", "opts", "providedIn", "factory", "undefined", "defineInjectable", "ɵɵdefineInjector", "options", "providers", "imports", "getInjectableDef", "getOwnDefinition", "NG_PROV_DEF", "isInjectable", "field", "getInheritedInjectableDef", "def", "console", "warn", "getInjectorDef", "NG_INJ_DEF", "ɵprov", "ɵinj", "InjectionToken", "_desc", "ngMetadataName", "__NG_ELEMENT_ID__", "multi", "_injectorProfilerContext", "getInjectorProfilerContext", "setInjectorProfilerContext", "context", "previous", "injectorProfilerCallbacks", "NOOP_PROFILER_REMOVAL", "removeProfiler", "profiler", "profilerIdx", "splice", "setInjectorProfiler", "injectorProfiler", "includes", "push", "event", "i", "injectorProfilerCallback", "emitProviderConfiguredEvent", "eventProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provide", "provider", "providerRecord", "emitInjectorToCreateInstanceEvent", "emitInstanceCreatedByInjectorEvent", "instance", "emitInjectEvent", "flags", "service", "emitEffectCreatedEvent", "effect", "runInInjectorProfilerContext", "injector", "callback", "prevInjectContext", "isEnvironmentProviders", "ɵproviders", "NG_COMP_DEF", "ɵcmp", "NG_DIR_DEF", "ɵdir", "NG_PIPE_DEF", "ɵpipe", "NG_MOD_DEF", "ɵmod", "NG_FACTORY_DEF", "ɵfac", "NG_ELEMENT_ID", "NG_ENV_ID", "__NG_ENV_ID__", "renderStringify", "String", "stringifyForError", "debugStringifyTypeForError", "componentDef", "debugInfo", "stringifyTypeFromDebugInfo", "filePath", "lineNumber", "className", "throwCyclicDependencyError", "path", "throwMixedMultiProviderError", "throwInvalidProviderError", "ngModuleType", "providerDetail", "v", "ɵfromNgModule", "throwProviderNotFoundError", "injectorName", "_injectImplementation", "getInjectImplementation", "setInjectImplementation", "impl", "injectRootLimpMode", "notFoundValue", "injectableDef", "assertInjectImplementationNotEqual", "_THROW_IF_NOT_FOUND", "THROW_IF_NOT_FOUND", "DI_DECORATOR_FLAG", "RetrievingInjector", "retrieve", "convertToBitFlags", "get", "e", "NG_TEMP_TOKEN_PATH", "NG_TOKEN_PATH", "NEW_LINE", "NO_NEW_LINE", "SOURCE", "injectInjectorOnly", "currentInjector", "convertToInjectOptions", "optional", "ɵɵinject", "ɵɵinvalidFactoryDep", "inject", "host", "self", "skipSelf", "injectArgs", "types", "args", "arg", "j", "meta", "flag", "getInjectFlag", "attachInjectFlag", "decorator", "prototype", "catchInjectorError", "injectorErrorName", "tokenPath", "unshift", "formatError", "text", "obj", "char<PERSON>t", "parts", "replace", "getFactoryDef", "throwNotFound", "hasFactoryDef", "arrayEquals", "a", "b", "identityAccessor", "valueA", "valueB", "flatten", "list", "flat", "Number", "POSITIVE_INFINITY", "deepForEach", "input", "for<PERSON>ach", "addToArray", "removeFromArray", "pop", "newArray", "size", "arraySplice", "array", "count", "arrayInsert2", "value1", "value2", "end", "previousEnd", "keyValueArraySet", "key<PERSON><PERSON>ueArray", "keyValueArrayIndexOf", "keyValueArrayGet", "_arrayIndexOfSorted", "shift", "start", "middle", "current", "EMPTY_OBJ", "EMPTY_ARRAY", "freeze", "ENVIRONMENT_INITIALIZER", "INJECTOR$1", "INJECTOR_DEF_TYPES", "NullInjector", "error", "getNgModuleDef", "getNgModuleDefOrThrow", "ngModuleDef", "getComponentDef", "getDirectiveDefOrThrow", "getDirectiveDef", "getPipeDef", "isStandalone", "standalone", "makeEnvironmentProviders", "provideEnvironmentInitializer", "initializerFn", "useValue", "importProvidersFrom", "sources", "internalImportProvidersFrom", "checkForStandaloneCmp", "providersOut", "dedup", "Set", "injectorTypesWithProviders", "collectProviders", "cmpDef", "internalSource", "walkProviderTree", "processInjectorTypesWithProviders", "typesWithProviders", "visitor", "ngModule", "deepForEachProvider", "validate<PERSON><PERSON><PERSON>", "container", "parents", "defType", "injDef", "defName", "isDuplicate", "has", "add", "dependencies", "deps", "dep", "importTypesWithProviders", "imported", "useFactory", "defProviders", "injectorType", "containerType", "isTypeProvider", "is<PERSON><PERSON>ue<PERSON>rovider", "isFactoryProvider", "isExistingProvider", "classRef", "useClass", "USE_VALUE", "useExisting", "isClassProvider", "INJECTOR_SCOPE", "NOT_YET", "CIRCULAR", "NULL_INJECTOR", "getNullInjector", "EnvironmentInjector", "R3Injector", "parent", "scopes", "records", "Map", "_ngOnDestroyHooks", "_onDestroy<PERSON><PERSON>s", "destroyed", "_destroyed", "injectorDefTypes", "forEachSingleProvider", "processProvider", "set", "makeRecord", "record", "destroy", "assertNotDestroyed", "prevConsumer", "ngOnDestroy", "onDestroyHooks", "hook", "clear", "onDestroy", "removeOnDestroy", "runInContext", "previousInjector", "previousInjectImplementation", "couldBeInjectableType", "injectableDefInScope", "injectableDefOrInjectorDefFactory", "hydrate", "nextInjector", "resolveInjectorInitializers", "initializers", "initializer", "tokens", "providerToRecord", "multiRecord", "existing", "hasOnDestroy", "destroyCBIdx", "Function", "getUndecoratedInjectableFactory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inheritedInjectableDef", "providerToFactory", "unwrappedProvider", "hasDeps", "runInInjectionContext", "internalInjector", "prevInjectorProfilerContext", "prevInjector", "isInInjectionContext", "assertInInjectionContext", "debugFn", "HOST", "TVIEW", "FLAGS", "PARENT", "NEXT", "T_HOST", "HYDRATION", "CLEANUP", "CONTEXT", "INJECTOR", "ENVIRONMENT", "RENDERER", "CHILD_HEAD", "CHILD_TAIL", "DECLARATION_VIEW", "DECLARATION_COMPONENT_VIEW", "DECLARATION_LCONTAINER", "PREORDER_HOOK_FLAGS", "QUERIES", "ID", "EMBEDDED_VIEW_INJECTOR", "ON_DESTROY_HOOKS", "EFFECTS_TO_SCHEDULE", "EFFECTS", "REACTIVE_TEMPLATE_CONSUMER", "AFTER_RENDER_SEQUENCES_TO_ADD", "HEADER_OFFSET", "TYPE", "DEHYDRATED_VIEWS", "NATIVE", "VIEW_REFS", "MOVED_VIEWS", "CONTAINER_HEADER_OFFSET", "isLView", "isLC<PERSON>r", "isContentQueryHost", "tNode", "isComponentHost", "componentOffset", "isDirectiveHost", "isComponentDef", "template", "isRootView", "isProjectionTNode", "hasI18n", "lView", "isDestroyed", "assertTNodeForLView", "assertTNodeForTView", "tView", "assertTNode", "tData", "data", "assert<PERSON>cu", "tIcu", "currentCaseLViewIndex", "assertComponentType", "assertNgModuleType", "assertHasParent", "assertLContainer", "assertLViewOrUndefined", "assertLView", "assertFirstCreatePass", "errMessage", "firstCreatePass", "assertFirstUpdatePass", "firstUpdatePass", "assertDirectiveDef", "selectors", "inputs", "assertIndexInDeclRange", "assertBetween", "bindingStartIndex", "assertIndexInExpandoRange", "expandoStartIndex", "lower", "upper", "assertProjectionSlots", "projection", "assertParent<PERSON>iew", "assertNodeInjector", "injectorIndex", "SVG_NAMESPACE", "MATH_ML_NAMESPACE", "unwrapRNode", "unwrapL<PERSON>iew", "getNativeByIndex", "getNativeByTNode", "getNativeByTNodeOrNull", "getTNode", "load", "view", "store", "blueprint", "getComponentLViewByIndex", "nodeIndex", "<PERSON><PERSON><PERSON><PERSON>", "slotValue", "isCreationMode", "viewAttachedToChangeDetector", "viewAttachedToContainer", "getConstant", "consts", "resetPreOrderHookFlags", "markViewForRefresh", "markAncestorsForTraversal", "walkUpViews", "nestingLevel", "current<PERSON>iew", "requiresRefreshOrTraversal", "dirty", "updateAncestorTraversalFlagsOnAttach", "changeDetectionScheduler", "notify", "getLViewParent", "storeLViewOnDestroy", "onDestroyCallback", "removeLViewOnDestroy", "getOrCreateLViewCleanup", "getOrCreateTViewCleanup", "cleanup", "storeCleanupWithContext", "cleanupFn", "lCleanup", "instructionState", "lFrame", "createLFrame", "bindingsEnabled", "skipHydrationRootTNode", "CheckNoChangesMode", "_checkNoChangesMode", "_isRefreshingViews", "getElementDepthCount", "elementDepthCount", "increaseElementDepthCount", "decreaseElementDepthCount", "getBindingsEnabled", "isInSkipHydrationBlock", "isSkipHydrationRootTNode", "ɵɵenableBindings", "enterSkipHydrationBlock", "ɵɵdisableBindings", "leaveSkipHydrationBlock", "getLView", "getTView", "ɵɵrestoreView", "viewToRestore", "contextLView", "ɵɵresetView", "getCurrentTNode", "currentTNode", "getCurrentTNodePlaceholderOk", "getCurrentParentTNode", "isParent", "setCurrentTNode", "isCurrentTNodeParent", "setCurrentTNodeAsNotParent", "getContextLView", "isInCheckNoChangesMode", "Off", "isExhaustiveCheckNoChanges", "Exhaustive", "setIsInCheckNoChangesMode", "mode", "isRefreshingViews", "setIsRefreshingViews", "prev", "getBindingRoot", "bindingRootIndex", "getBindingIndex", "bindingIndex", "setBindingIndex", "nextBindingIndex", "incrementBindingIndex", "isInI18nBlock", "inI18n", "setInI18nBlock", "setBindingRootForHostBindings", "currentDirectiveIndex", "setCurrentDirectiveIndex", "getCurrentDirectiveIndex", "getCurrentDirectiveDef", "getCurrentQueryIndex", "currentQueryIndex", "setCurrentQueryIndex", "getDeclarationTNode", "declTNode", "enterDI", "parentTNode", "parentLView", "allocLFrame", "enterView", "newView", "newLFrame", "selectedIndex", "currentNamespace", "<PERSON><PERSON><PERSON><PERSON>", "currentLFrame", "<PERSON><PERSON><PERSON><PERSON>", "child", "leaveViewLight", "oldLFrame", "leaveDI", "leaveView", "nextContextImpl", "level", "getSelectedIndex", "setSelectedIndex", "getSelectedTNode", "ɵɵnamespaceSVG", "ɵɵnamespaceMathML", "ɵɵnamespaceHTML", "namespaceHTMLInternal", "getNamespace", "_wasLastNodeCreated", "wasLastNodeCreated", "lastNodeWasCreated", "createInjector", "additionalProviders", "createInjectorWithoutInjectorInstances", "Injector", "NULL", "create", "DOCUMENT", "DestroyRef", "injectDestroyRef", "NodeInjectorDestroyRef", "_l<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_console", "handleError", "INTERNAL_APPLICATION_ERROR_HANDLER", "userErrorHandler", "errorHandlerEnvironmentInitializer", "globalErrorListeners", "ngServerMode", "window", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "rejectionListener", "reason", "preventDefault", "errorListener", "setupEventListeners", "addEventListener", "Zone", "root", "run", "removeEventListener", "provideBrowserGlobalErrorListeners", "isSignal", "ɵunwrapWritableSignal", "signal", "initialValue", "update", "equal", "signalFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signalAsReadonlyFn", "bind", "debugName", "readonlyFn", "isWritableSignal", "ChangeDetectionScheduler", "ZONELESS_ENABLED", "PROVIDED_ZONELESS", "ZONELESS_SCHEDULER_DISABLED", "SCHEDULE_IN_ROOT_ZONE", "assertNotInReactiveContext", "extraContext", "ViewContext", "injectViewContext", "PendingTasksInternal", "taskId", "pendingTasks", "pendingTask", "hasPendingTasks", "hasPendingTasksObservable", "subscriber", "next", "complete", "remove", "delete", "unsubscribe", "PendingTasks", "internalPendingTasks", "scheduler", "removeTask", "catch", "finally", "noop", "EffectScheduler", "ZoneAwareEffectScheduler", "dirtyEffectCount", "queues", "handle", "enqueue", "schedule", "zone", "queue", "flush", "ranOneEffect", "flushQueue"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/core/fesm2022/root_effect_scheduler-DCy1y1b8.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isNotFound, getCurrentInjector, setCurrentInjector } from './primitives/di.mjs';\nimport { getActiveConsumer, SIGNAL, createSignal } from './signal-nCiHhWf6.mjs';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { NotFoundError, isNotFound as isNotFound$1 } from '@angular/core/primitives/di';\nimport { setActiveConsumer } from '@angular/core/primitives/signals';\n\n/**\n * Base URL for the error details page.\n *\n * Keep this constant in sync across:\n *  - packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.ts\n *  - packages/core/src/error_details_base_url.ts\n */\nconst ERROR_DETAILS_PAGE_BASE_URL = 'https://angular.dev/errors';\n/**\n * URL for the XSS security documentation.\n */\nconst XSS_SECURITY_URL = 'https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss';\n\n/**\n * Class that represents a runtime error.\n * Formats and outputs the error message in a consistent way.\n *\n * Example:\n * ```ts\n *  throw new RuntimeError(\n *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n *    ngDevMode && 'Injector has already been destroyed.');\n * ```\n *\n * Note: the `message` argument contains a descriptive error message as a string in development\n * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the\n * `message` argument becomes `false`, thus we account for it in the typings and the runtime\n * logic.\n */\nclass RuntimeError extends Error {\n    code;\n    constructor(code, message) {\n        super(formatRuntimeError(code, message));\n        this.code = code;\n    }\n}\nfunction formatRuntimeErrorCode(code) {\n    // Error code might be a negative number, which is a special marker that instructs the logic to\n    // generate a link to the error details page on angular.io.\n    // We also prepend `0` to non-compile-time errors.\n    return `NG0${Math.abs(code)}`;\n}\n/**\n * Called to format a runtime error.\n * See additional info on the `message` argument type in the `RuntimeError` class description.\n */\nfunction formatRuntimeError(code, message) {\n    const fullCode = formatRuntimeErrorCode(code);\n    let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;\n    if (ngDevMode && code < 0) {\n        const addPeriodSeparator = !errorMessage.match(/[.,;!?\\n]$/);\n        const separator = addPeriodSeparator ? '.' : '';\n        errorMessage = `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;\n    }\n    return errorMessage;\n}\n\nconst _global = globalThis;\n\nfunction ngDevModeResetPerfCounters() {\n    const locationString = typeof location !== 'undefined' ? location.toString() : '';\n    const newCounters = {\n        hydratedNodes: 0,\n        hydratedComponents: 0,\n        dehydratedViewsRemoved: 0,\n        dehydratedViewsCleanupRuns: 0,\n        componentsSkippedHydration: 0,\n        deferBlocksWithIncrementalHydration: 0,\n    };\n    // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.\n    const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;\n    if (!allowNgDevModeTrue) {\n        _global['ngDevMode'] = false;\n    }\n    else {\n        if (typeof _global['ngDevMode'] !== 'object') {\n            _global['ngDevMode'] = {};\n        }\n        Object.assign(_global['ngDevMode'], newCounters);\n    }\n    return newCounters;\n}\n/**\n * This function checks to see if the `ngDevMode` has been set. If yes,\n * then we honor it, otherwise we default to dev mode with additional checks.\n *\n * The idea is that unless we are doing production build where we explicitly\n * set `ngDevMode == false` we should be helping the developer by providing\n * as much early warning and errors as possible.\n *\n * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions\n * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode\n * is defined for the entire instruction set.\n *\n * When checking `ngDevMode` on toplevel, always init it before referencing it\n * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can\n *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.\n *\n * Details on possible values for `ngDevMode` can be found on its docstring.\n */\nfunction initNgDevMode() {\n    // The below checks are to ensure that calling `initNgDevMode` multiple times does not\n    // reset the counters.\n    // If the `ngDevMode` is not an object, then it means we have not created the perf counters\n    // yet.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {\n            ngDevModeResetPerfCounters();\n        }\n        return typeof ngDevMode !== 'undefined' && !!ngDevMode;\n    }\n    return false;\n}\n\nfunction getClosureSafeProperty(objWithPropertyToExtract) {\n    for (let key in objWithPropertyToExtract) {\n        if (objWithPropertyToExtract[key] === getClosureSafeProperty) {\n            return key;\n        }\n    }\n    // Cannot change it to `RuntimeError` because the `util` target cannot\n    // circularly depend on the `core` target.\n    throw Error(typeof ngDevMode !== 'undefined' && ngDevMode\n        ? 'Could not find renamed property on target object.'\n        : '');\n}\n/**\n * Sets properties on a target object from a source object, but only if\n * the property doesn't already exist on the target object.\n * @param target The target to set properties on\n * @param source The source of the property keys and values to set\n */\nfunction fillProperties(target, source) {\n    for (const key in source) {\n        if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {\n            target[key] = source[key];\n        }\n    }\n}\n\nfunction stringify(token) {\n    if (typeof token === 'string') {\n        return token;\n    }\n    if (Array.isArray(token)) {\n        return `[${token.map(stringify).join(', ')}]`;\n    }\n    if (token == null) {\n        return '' + token;\n    }\n    const name = token.overriddenName || token.name;\n    if (name) {\n        return `${name}`;\n    }\n    const result = token.toString();\n    if (result == null) {\n        return '' + result;\n    }\n    const newLineIndex = result.indexOf('\\n');\n    return newLineIndex >= 0 ? result.slice(0, newLineIndex) : result;\n}\n/**\n * Concatenates two strings with separator, allocating new strings only when necessary.\n *\n * @param before before string.\n * @param separator separator string.\n * @param after after string.\n * @returns concatenated string.\n */\nfunction concatStringsWithSpace(before, after) {\n    if (!before)\n        return after || '';\n    if (!after)\n        return before;\n    return `${before} ${after}`;\n}\n/**\n * Ellipses the string in the middle when longer than the max length\n *\n * @param string\n * @param maxLength of the output string\n * @returns ellipsed string with ... in the middle\n */\nfunction truncateMiddle(str, maxLength = 100) {\n    if (!str || maxLength < 1 || str.length <= maxLength)\n        return str;\n    if (maxLength == 1)\n        return str.substring(0, 1) + '...';\n    const halfLimit = Math.round(maxLength / 2);\n    return str.substring(0, halfLimit) + '...' + str.substring(str.length - halfLimit);\n}\n\nconst __forward_ref__ = getClosureSafeProperty({ __forward_ref__: getClosureSafeProperty });\n/**\n * Allows to refer to references which are not yet defined.\n *\n * For instance, `forwardRef` is used when the `token` which we need to refer to for the purposes of\n * DI is declared, but not yet defined. It is also used when the `token` which we use when creating\n * a query is not yet defined.\n *\n * `forwardRef` is also used to break circularities in standalone components imports.\n *\n * @usageNotes\n * ### Circular dependency example\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref'}\n *\n * ### Circular standalone reference import example\n * ```angular-ts\n * @Component({\n *   imports: [ChildComponent],\n *   selector: 'app-parent',\n *   template: `<app-child [hideParent]=\"hideParent()\"></app-child>`,\n * })\n * export class ParentComponent {\n *    hideParent = input.required<boolean>();\n * }\n *\n *\n * @Component({\n *   imports: [forwardRef(() => ParentComponent)],\n *   selector: 'app-child',\n *   template: `\n *    @if(!hideParent() {\n *       <app-parent/>\n *    }\n *  `,\n * })\n * export class ChildComponent {\n *    hideParent = input.required<boolean>();\n * }\n * ```\n *\n * @publicApi\n */\nfunction forwardRef(forwardRefFn) {\n    forwardRefFn.__forward_ref__ = forwardRef;\n    forwardRefFn.toString = function () {\n        return stringify(this());\n    };\n    return forwardRefFn;\n}\n/**\n * Lazily retrieves the reference value from a forwardRef.\n *\n * Acts as the identity function when given a non-forward-ref value.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='resolve_forward_ref'}\n *\n * @see {@link forwardRef}\n * @publicApi\n */\nfunction resolveForwardRef(type) {\n    return isForwardRef(type) ? type() : type;\n}\n/** Checks whether a function is wrapped by a `forwardRef`. */\nfunction isForwardRef(fn) {\n    return (typeof fn === 'function' &&\n        fn.hasOwnProperty(__forward_ref__) &&\n        fn.__forward_ref__ === forwardRef);\n}\n\n// The functions in this file verify that the assumptions we are making\n// about state in an instruction are correct before implementing any logic.\n// They are meant only to be called in dev mode as sanity checks.\nfunction assertNumber(actual, msg) {\n    if (!(typeof actual === 'number')) {\n        throwError(msg, typeof actual, 'number', '===');\n    }\n}\nfunction assertNumberInRange(actual, minInclusive, maxInclusive) {\n    assertNumber(actual, 'Expected a number');\n    assertLessThanOrEqual(actual, maxInclusive, 'Expected number to be less than or equal to');\n    assertGreaterThanOrEqual(actual, minInclusive, 'Expected number to be greater than or equal to');\n}\nfunction assertString(actual, msg) {\n    if (!(typeof actual === 'string')) {\n        throwError(msg, actual === null ? 'null' : typeof actual, 'string', '===');\n    }\n}\nfunction assertFunction(actual, msg) {\n    if (!(typeof actual === 'function')) {\n        throwError(msg, actual === null ? 'null' : typeof actual, 'function', '===');\n    }\n}\nfunction assertEqual(actual, expected, msg) {\n    if (!(actual == expected)) {\n        throwError(msg, actual, expected, '==');\n    }\n}\nfunction assertNotEqual(actual, expected, msg) {\n    if (!(actual != expected)) {\n        throwError(msg, actual, expected, '!=');\n    }\n}\nfunction assertSame(actual, expected, msg) {\n    if (!(actual === expected)) {\n        throwError(msg, actual, expected, '===');\n    }\n}\nfunction assertNotSame(actual, expected, msg) {\n    if (!(actual !== expected)) {\n        throwError(msg, actual, expected, '!==');\n    }\n}\nfunction assertLessThan(actual, expected, msg) {\n    if (!(actual < expected)) {\n        throwError(msg, actual, expected, '<');\n    }\n}\nfunction assertLessThanOrEqual(actual, expected, msg) {\n    if (!(actual <= expected)) {\n        throwError(msg, actual, expected, '<=');\n    }\n}\nfunction assertGreaterThan(actual, expected, msg) {\n    if (!(actual > expected)) {\n        throwError(msg, actual, expected, '>');\n    }\n}\nfunction assertGreaterThanOrEqual(actual, expected, msg) {\n    if (!(actual >= expected)) {\n        throwError(msg, actual, expected, '>=');\n    }\n}\nfunction assertNotDefined(actual, msg) {\n    if (actual != null) {\n        throwError(msg, actual, null, '==');\n    }\n}\nfunction assertDefined(actual, msg) {\n    if (actual == null) {\n        throwError(msg, actual, null, '!=');\n    }\n}\nfunction throwError(msg, actual, expected, comparison) {\n    throw new Error(`ASSERTION ERROR: ${msg}` +\n        (comparison == null ? '' : ` [Expected=> ${expected} ${comparison} ${actual} <=Actual]`));\n}\nfunction assertDomNode(node) {\n    if (!(node instanceof Node)) {\n        throwError(`The provided value must be an instance of a DOM Node but got ${stringify(node)}`);\n    }\n}\nfunction assertElement(node) {\n    if (!(node instanceof Element)) {\n        throwError(`The provided value must be an element but got ${stringify(node)}`);\n    }\n}\nfunction assertIndexInRange(arr, index) {\n    assertDefined(arr, 'Array must be defined.');\n    const maxLen = arr.length;\n    if (index < 0 || index >= maxLen) {\n        throwError(`Index expected to be less than ${maxLen} but got ${index}`);\n    }\n}\nfunction assertOneOf(value, ...validValues) {\n    if (validValues.indexOf(value) !== -1)\n        return true;\n    throwError(`Expected value to be one of ${JSON.stringify(validValues)} but was ${JSON.stringify(value)}.`);\n}\nfunction assertNotReactive(fn) {\n    if (getActiveConsumer() !== null) {\n        throwError(`${fn}() should never be called in a reactive context.`);\n    }\n}\n\n/**\n * Construct an injectable definition which defines how a token will be constructed by the DI\n * system, and in which injectors (if any) it will be available.\n *\n * This should be assigned to a static `ɵprov` field on a type, which will then be an\n * `InjectableType`.\n *\n * Options:\n * * `providedIn` determines which injectors will include the injectable, by either associating it\n *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be\n *   provided in the `'root'` injector, which will be the application-level injector in most apps.\n * * `factory` gives the zero argument function which will create an instance of the injectable.\n *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection\n * of dependencies.\n *\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nfunction ɵɵdefineInjectable(opts) {\n    return {\n        token: opts.token,\n        providedIn: opts.providedIn || null,\n        factory: opts.factory,\n        value: undefined,\n    };\n}\n/**\n * @deprecated in v8, delete after v10. This API should be used only by generated code, and that\n * code should now use ɵɵdefineInjectable instead.\n * @publicApi\n */\nconst defineInjectable = ɵɵdefineInjectable;\n/**\n * Construct an `InjectorDef` which configures an injector.\n *\n * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an\n * `InjectorType`.\n *\n * Options:\n *\n * * `providers`: an optional array of providers to add to the injector. Each provider must\n *   either have a factory or point to a type which has a `ɵprov` static property (the\n *   type must be an `InjectableType`).\n * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s\n *   whose providers will also be added to the injector. Locally provided types will override\n *   providers from imports.\n *\n * @codeGenApi\n */\nfunction ɵɵdefineInjector(options) {\n    return { providers: options.providers || [], imports: options.imports || [] };\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading\n * inherited value.\n *\n * @param type A type which may have its own (non-inherited) `ɵprov`.\n */\nfunction getInjectableDef(type) {\n    return getOwnDefinition(type, NG_PROV_DEF);\n}\nfunction isInjectable(type) {\n    return getInjectableDef(type) !== null;\n}\n/**\n * Return definition only if it is defined directly on `type` and is not inherited from a base\n * class of `type`.\n */\nfunction getOwnDefinition(type, field) {\n    // if the ɵprov prop exist but is undefined we still want to return null\n    return (type.hasOwnProperty(field) && type[field]) || null;\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.\n *\n * @param type A type which may have `ɵprov`, via inheritance.\n *\n * @deprecated Will be removed in a future version of Angular, where an error will occur in the\n *     scenario if we find the `ɵprov` on an ancestor only.\n */\nfunction getInheritedInjectableDef(type) {\n    // if the ɵprov prop exist but is undefined we still want to return null\n    const def = type?.[NG_PROV_DEF] ?? null;\n    if (def) {\n        ngDevMode &&\n            console.warn(`DEPRECATED: DI is instantiating a token \"${type.name}\" that inherits its @Injectable decorator but does not provide one itself.\\n` +\n                `This will become an error in a future version of Angular. Please add @Injectable() to the \"${type.name}\" class.`);\n        return def;\n    }\n    else {\n        return null;\n    }\n}\n/**\n * Read the injector def type in a way which is immune to accidentally reading inherited value.\n *\n * @param type type which may have an injector def (`ɵinj`)\n */\nfunction getInjectorDef(type) {\n    return type && type.hasOwnProperty(NG_INJ_DEF) ? type[NG_INJ_DEF] : null;\n}\nconst NG_PROV_DEF = getClosureSafeProperty({ ɵprov: getClosureSafeProperty });\nconst NG_INJ_DEF = getClosureSafeProperty({ ɵinj: getClosureSafeProperty });\n\n/**\n * Creates a token that can be used in a DI Provider.\n *\n * Use an `InjectionToken` whenever the type you are injecting is not reified (does not have a\n * runtime representation) such as when injecting an interface, callable type, array or\n * parameterized type.\n *\n * `InjectionToken` is parameterized on `T` which is the type of object which will be returned by\n * the `Injector`. This provides an additional level of type safety.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * **Important Note**: Ensure that you use the same instance of the `InjectionToken` in both the\n * provider and the injection call. Creating a new instance of `InjectionToken` in different places,\n * even with the same description, will be treated as different tokens by Angular's DI system,\n * leading to a `NullInjectorError`.\n *\n * </div>\n *\n * {@example injection-token/src/main.ts region='InjectionToken'}\n *\n * When creating an `InjectionToken`, you can optionally specify a factory function which returns\n * (possibly by creating) a default value of the parameterized type `T`. This sets up the\n * `InjectionToken` using this factory as a provider as if it was defined explicitly in the\n * application's root injector. If the factory function, which takes zero arguments, needs to inject\n * dependencies, it can do so using the [`inject`](api/core/inject) function.\n * As you can see in the Tree-shakable InjectionToken example below.\n *\n * Additionally, if a `factory` is specified you can also specify the `providedIn` option, which\n * overrides the above behavior and marks the token as belonging to a particular `@NgModule` (note:\n * this option is now deprecated). As mentioned above, `'root'` is the default value for\n * `providedIn`.\n *\n * The `providedIn: NgModule` and `providedIn: 'any'` options are deprecated.\n *\n * @usageNotes\n * ### Basic Examples\n *\n * ### Plain InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='InjectionToken'}\n *\n * ### Tree-shakable InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='ShakableInjectionToken'}\n *\n * @publicApi\n */\nclass InjectionToken {\n    _desc;\n    /** @internal */\n    ngMetadataName = 'InjectionToken';\n    ɵprov;\n    /**\n     * @param _desc   Description for the token,\n     *                used only for debugging purposes,\n     *                it should but does not need to be unique\n     * @param options Options for the token's usage, as described above\n     */\n    constructor(_desc, options) {\n        this._desc = _desc;\n        this.ɵprov = undefined;\n        if (typeof options == 'number') {\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                assertLessThan(options, 0, 'Only negative numbers are supported here');\n            // This is a special hack to assign __NG_ELEMENT_ID__ to this instance.\n            // See `InjectorMarkers`\n            this.__NG_ELEMENT_ID__ = options;\n        }\n        else if (options !== undefined) {\n            this.ɵprov = ɵɵdefineInjectable({\n                token: this,\n                providedIn: options.providedIn || 'root',\n                factory: options.factory,\n            });\n        }\n    }\n    /**\n     * @internal\n     */\n    get multi() {\n        return this;\n    }\n    toString() {\n        return `InjectionToken ${this._desc}`;\n    }\n}\n\nlet _injectorProfilerContext;\nfunction getInjectorProfilerContext() {\n    !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');\n    return _injectorProfilerContext;\n}\nfunction setInjectorProfilerContext(context) {\n    !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');\n    const previous = _injectorProfilerContext;\n    _injectorProfilerContext = context;\n    return previous;\n}\nconst injectorProfilerCallbacks = [];\nconst NOOP_PROFILER_REMOVAL = () => { };\nfunction removeProfiler(profiler) {\n    const profilerIdx = injectorProfilerCallbacks.indexOf(profiler);\n    if (profilerIdx !== -1) {\n        injectorProfilerCallbacks.splice(profilerIdx, 1);\n    }\n}\n/**\n * Adds a callback function which will be invoked during certain DI events within the\n * runtime (for example: injecting services, creating injectable instances, configuring providers).\n * Multiple profiler callbacks can be set: in this case profiling events are\n * reported to every registered callback.\n *\n * Warning: this function is *INTERNAL* and should not be relied upon in application's code.\n * The contract of the function might be changed in any release and/or the function can be removed\n * completely.\n *\n * @param profiler function provided by the caller or null value to disable profiling.\n * @returns a cleanup function that, when invoked, removes a given profiler callback.\n */\nfunction setInjectorProfiler(injectorProfiler) {\n    !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');\n    if (injectorProfiler !== null) {\n        if (!injectorProfilerCallbacks.includes(injectorProfiler)) {\n            injectorProfilerCallbacks.push(injectorProfiler);\n        }\n        return () => removeProfiler(injectorProfiler);\n    }\n    else {\n        injectorProfilerCallbacks.length = 0;\n        return NOOP_PROFILER_REMOVAL;\n    }\n}\n/**\n * Injector profiler function which emits on DI events executed by the runtime.\n *\n * @param event InjectorProfilerEvent corresponding to the DI event being emitted\n */\nfunction injectorProfiler(event) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    for (let i = 0; i < injectorProfilerCallbacks.length; i++) {\n        const injectorProfilerCallback = injectorProfilerCallbacks[i];\n        injectorProfilerCallback(event);\n    }\n}\n/**\n * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the\n * emitted event includes the raw provider, as well as the token that provider is providing.\n *\n * @param eventProvider A provider object\n */\nfunction emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    let token;\n    // if the provider is a TypeProvider (typeof provider is function) then the token is the\n    // provider itself\n    if (typeof eventProvider === 'function') {\n        token = eventProvider;\n    }\n    // if the provider is an injection token, then the token is the injection token.\n    else if (eventProvider instanceof InjectionToken) {\n        token = eventProvider;\n    }\n    // in all other cases we can access the token via the `provide` property of the provider\n    else {\n        token = resolveForwardRef(eventProvider.provide);\n    }\n    let provider = eventProvider;\n    // Injection tokens may define their own default provider which gets attached to the token itself\n    // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the\n    // token itself.\n    if (eventProvider instanceof InjectionToken) {\n        provider = eventProvider.ɵprov || eventProvider;\n    }\n    injectorProfiler({\n        type: 2 /* InjectorProfilerEventType.ProviderConfigured */,\n        context: getInjectorProfilerContext(),\n        providerRecord: { token, provider, isViewProvider },\n    });\n}\n/**\n * Emits an event to the injector profiler when an instance corresponding to a given token is about to be created be an injector. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInjectorToCreateInstanceEvent(token) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 4 /* InjectorProfilerEventType.InjectorToCreateInstanceEvent */,\n        context: getInjectorProfilerContext(),\n        token: token,\n    });\n}\n/**\n * Emits an event to the injector profiler with the instance that was created. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInstanceCreatedByInjectorEvent(instance) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,\n        context: getInjectorProfilerContext(),\n        instance: { value: instance },\n    });\n}\n/**\n * @param token DI token associated with injected service\n * @param value the instance of the injected service (i.e the result of `inject(token)`)\n * @param flags the flags that the token was injected with\n */\nfunction emitInjectEvent(token, value, flags) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 0 /* InjectorProfilerEventType.Inject */,\n        context: getInjectorProfilerContext(),\n        service: { token, value, flags },\n    });\n}\nfunction emitEffectCreatedEvent(effect) {\n    !ngDevMode && throwError('Injector profiler should never be called in production mode');\n    injectorProfiler({\n        type: 3 /* InjectorProfilerEventType.EffectCreated */,\n        context: getInjectorProfilerContext(),\n        effect,\n    });\n}\nfunction runInInjectorProfilerContext(injector, token, callback) {\n    !ngDevMode &&\n        throwError('runInInjectorProfilerContext should never be called in production mode');\n    const prevInjectContext = setInjectorProfilerContext({ injector, token });\n    try {\n        callback();\n    }\n    finally {\n        setInjectorProfilerContext(prevInjectContext);\n    }\n}\n\nfunction isEnvironmentProviders(value) {\n    return value && !!value.ɵproviders;\n}\n\nconst NG_COMP_DEF = getClosureSafeProperty({ ɵcmp: getClosureSafeProperty });\nconst NG_DIR_DEF = getClosureSafeProperty({ ɵdir: getClosureSafeProperty });\nconst NG_PIPE_DEF = getClosureSafeProperty({ ɵpipe: getClosureSafeProperty });\nconst NG_MOD_DEF = getClosureSafeProperty({ ɵmod: getClosureSafeProperty });\nconst NG_FACTORY_DEF = getClosureSafeProperty({ ɵfac: getClosureSafeProperty });\n/**\n * If a directive is diPublic, bloomAdd sets a property on the type with this constant as\n * the key and the directive's unique ID as the value. This allows us to map directives to their\n * bloom filter bit for DI.\n */\n// TODO(misko): This is wrong. The NG_ELEMENT_ID should never be minified.\nconst NG_ELEMENT_ID = getClosureSafeProperty({\n    __NG_ELEMENT_ID__: getClosureSafeProperty,\n});\n/**\n * The `NG_ENV_ID` field on a DI token indicates special processing in the `EnvironmentInjector`:\n * getting such tokens from the `EnvironmentInjector` will bypass the standard DI resolution\n * strategy and instead will return implementation produced by the `NG_ENV_ID` factory function.\n *\n * This particular retrieval of DI tokens is mostly done to eliminate circular dependencies and\n * improve tree-shaking.\n */\nconst NG_ENV_ID = getClosureSafeProperty({ __NG_ENV_ID__: getClosureSafeProperty });\n\n/**\n * Used for stringify render output in Ivy.\n * Important! This function is very performance-sensitive and we should\n * be extra careful not to introduce megamorphic reads in it.\n * Check `core/test/render3/perf/render_stringify` for benchmarks and alternate implementations.\n */\nfunction renderStringify(value) {\n    if (typeof value === 'string')\n        return value;\n    if (value == null)\n        return '';\n    // Use `String` so that it invokes the `toString` method of the value. Note that this\n    // appears to be faster than calling `value.toString` (see `render_stringify` benchmark).\n    return String(value);\n}\n/**\n * Used to stringify a value so that it can be displayed in an error message.\n *\n * Important! This function contains a megamorphic read and should only be\n * used for error messages.\n */\nfunction stringifyForError(value) {\n    if (typeof value === 'function')\n        return value.name || value.toString();\n    if (typeof value === 'object' && value != null && typeof value.type === 'function') {\n        return value.type.name || value.type.toString();\n    }\n    return renderStringify(value);\n}\n/**\n * Used to stringify a `Type` and including the file path and line number in which it is defined, if\n * possible, for better debugging experience.\n *\n * Important! This function contains a megamorphic read and should only be used for error messages.\n */\nfunction debugStringifyTypeForError(type) {\n    // TODO(pmvald): Do some refactoring so that we can use getComponentDef here without creating\n    // circular deps.\n    let componentDef = type[NG_COMP_DEF] || null;\n    if (componentDef !== null && componentDef.debugInfo) {\n        return stringifyTypeFromDebugInfo(componentDef.debugInfo);\n    }\n    return stringifyForError(type);\n}\n// TODO(pmvald): Do some refactoring so that we can use the type ClassDebugInfo for the param\n// debugInfo here without creating circular deps.\nfunction stringifyTypeFromDebugInfo(debugInfo) {\n    if (!debugInfo.filePath || !debugInfo.lineNumber) {\n        return debugInfo.className;\n    }\n    else {\n        return `${debugInfo.className} (at ${debugInfo.filePath}:${debugInfo.lineNumber})`;\n    }\n}\n\n/** Called when directives inject each other (creating a circular dependency) */\nfunction throwCyclicDependencyError(token, path) {\n    throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, ngDevMode\n        ? `Circular dependency in DI detected for ${token}${path ? `. Dependency path: ${path.join(' > ')} > ${token}` : ''}`\n        : token);\n}\nfunction throwMixedMultiProviderError() {\n    throw new Error(`Cannot mix multi providers and regular providers`);\n}\nfunction throwInvalidProviderError(ngModuleType, providers, provider) {\n    if (ngModuleType && providers) {\n        const providerDetail = providers.map((v) => (v == provider ? '?' + provider + '?' : '...'));\n        throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);\n    }\n    else if (isEnvironmentProviders(provider)) {\n        if (provider.ɵfromNgModule) {\n            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);\n        }\n        else {\n            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);\n        }\n    }\n    else {\n        throw new Error('Invalid provider');\n    }\n}\n/** Throws an error when a token is not found in DI. */\nfunction throwProviderNotFoundError(token, injectorName) {\n    const errorMessage = ngDevMode &&\n        `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;\n    throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, errorMessage);\n}\n\n/**\n * Current implementation of inject.\n *\n * By default, it is `injectInjectorOnly`, which makes it `Injector`-only aware. It can be changed\n * to `directiveInject`, which brings in the `NodeInjector` system of ivy. It is designed this\n * way for two reasons:\n *  1. `Injector` should not depend on ivy logic.\n *  2. To maintain tree shake-ability we don't want to bring in unnecessary code.\n */\nlet _injectImplementation;\nfunction getInjectImplementation() {\n    return _injectImplementation;\n}\n/**\n * Sets the current inject implementation.\n */\nfunction setInjectImplementation(impl) {\n    const previous = _injectImplementation;\n    _injectImplementation = impl;\n    return previous;\n}\n/**\n * Injects `root` tokens in limp mode.\n *\n * If no injector exists, we can still inject tree-shakable providers which have `providedIn` set to\n * `\"root\"`. This is known as the limp mode injection. In such case the value is stored in the\n * injectable definition.\n */\nfunction injectRootLimpMode(token, notFoundValue, flags) {\n    const injectableDef = getInjectableDef(token);\n    if (injectableDef && injectableDef.providedIn == 'root') {\n        return injectableDef.value === undefined\n            ? (injectableDef.value = injectableDef.factory())\n            : injectableDef.value;\n    }\n    if (flags & 8 /* InternalInjectFlags.Optional */)\n        return null;\n    if (notFoundValue !== undefined)\n        return notFoundValue;\n    throwProviderNotFoundError(token, 'Injector');\n}\n/**\n * Assert that `_injectImplementation` is not `fn`.\n *\n * This is useful, to prevent infinite recursion.\n *\n * @param fn Function which it should not equal to\n */\nfunction assertInjectImplementationNotEqual(fn) {\n    ngDevMode &&\n        assertNotEqual(_injectImplementation, fn, 'Calling ɵɵinject would cause infinite recursion');\n}\n\nconst _THROW_IF_NOT_FOUND = {};\nconst THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;\n/*\n * Name of a property (that we patch onto DI decorator), which is used as an annotation of which\n * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators\n * in the code, thus making them tree-shakable.\n */\nconst DI_DECORATOR_FLAG = '__NG_DI_FLAG__';\n/**\n * A wrapper around an `Injector` that implements the `PrimitivesInjector` interface.\n *\n * This is used to allow the `inject` function to be used with the new primitives-based DI system.\n */\nclass RetrievingInjector {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    retrieve(token, options) {\n        const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n        try {\n            return this.injector.get(token, \n            // When a dependency is requested with an optional flag, DI returns null as the default value.\n            (flags & 8 /* InternalInjectFlags.Optional */ ? null : THROW_IF_NOT_FOUND), flags);\n        }\n        catch (e) {\n            if (isNotFound(e)) {\n                return e;\n            }\n            throw e;\n        }\n    }\n}\nconst NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';\nconst NG_TOKEN_PATH = 'ngTokenPath';\nconst NEW_LINE = /\\n/gm;\nconst NO_NEW_LINE = 'ɵ';\nconst SOURCE = '__source';\nfunction injectInjectorOnly(token, flags = 0 /* InternalInjectFlags.Default */) {\n    const currentInjector = getCurrentInjector();\n    if (currentInjector === undefined) {\n        throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode &&\n            `The \\`${stringify(token)}\\` token injection failed. \\`inject()\\` function must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \\`runInInjectionContext\\`.`);\n    }\n    else if (currentInjector === null) {\n        return injectRootLimpMode(token, undefined, flags);\n    }\n    else {\n        const options = convertToInjectOptions(flags);\n        const value = currentInjector.retrieve(token, options);\n        ngDevMode && emitInjectEvent(token, value, flags);\n        if (isNotFound(value)) {\n            if (options.optional) {\n                return null;\n            }\n            throw value;\n        }\n        return value;\n    }\n}\nfunction ɵɵinject(token, flags = 0 /* InternalInjectFlags.Default */) {\n    return (getInjectImplementation() || injectInjectorOnly)(resolveForwardRef(token), flags);\n}\n/**\n * Throws an error indicating that a factory function could not be generated by the compiler for a\n * particular class.\n *\n * The name of the class is not mentioned here, but will be in the generated factory function name\n * and thus in the stack trace.\n *\n * @codeGenApi\n */\nfunction ɵɵinvalidFactoryDep(index) {\n    throw new RuntimeError(202 /* RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY */, ngDevMode &&\n        `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.\nThis can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.\n\nPlease check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`);\n}\n/**\n * Injects a token from the currently active injector.\n * `inject` is only supported in an [injection context](guide/di/dependency-injection-context). It\n * can be used during:\n * - Construction (via the `constructor`) of a class being instantiated by the DI system, such\n * as an `@Injectable` or `@Component`.\n * - In the initializer for fields of such classes.\n * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.\n * - In the `factory` function specified for an `InjectionToken`.\n * - In a stackframe of a function call in a DI context\n *\n * @param token A token that represents a dependency that should be injected.\n * @param flags Optional flags that control how injection is executed.\n * The flags correspond to injection strategies that can be specified with\n * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @usageNotes\n * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a\n * field initializer:\n *\n * ```ts\n * @Injectable({providedIn: 'root'})\n * export class Car {\n *   radio: Radio|undefined;\n *   // OK: field initializer\n *   spareTyre = inject(Tyre);\n *\n *   constructor() {\n *     // OK: constructor body\n *     this.radio = inject(Radio);\n *   }\n * }\n * ```\n *\n * It is also legal to call `inject` from a provider's factory:\n *\n * ```ts\n * providers: [\n *   {provide: Car, useFactory: () => {\n *     // OK: a class factory\n *     const engine = inject(Engine);\n *     return new Car(engine);\n *   }}\n * ]\n * ```\n *\n * Calls to the `inject()` function outside of the class creation context will result in error. Most\n * notably, calls to `inject()` are disallowed after a class instance was created, in methods\n * (including lifecycle hooks):\n *\n * ```ts\n * @Component({ ... })\n * export class CarComponent {\n *   ngOnInit() {\n *     // ERROR: too late, the component instance was already created\n *     const engine = inject(Engine);\n *     engine.start();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction inject(token, options) {\n    // The `as any` here _shouldn't_ be necessary, but without it JSCompiler\n    // throws a disambiguation  error due to the multiple signatures.\n    return ɵɵinject(token, convertToBitFlags(options));\n}\n// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).\nfunction convertToBitFlags(flags) {\n    if (typeof flags === 'undefined' || typeof flags === 'number') {\n        return flags;\n    }\n    // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in\n    // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from\n    // `InjectOptions` to `InjectFlags`.\n    return (0 /* InternalInjectFlags.Default */ | // comment to force a line break in the formatter\n        (flags.optional && 8 /* InternalInjectFlags.Optional */) |\n        (flags.host && 1 /* InternalInjectFlags.Host */) |\n        (flags.self && 2 /* InternalInjectFlags.Self */) |\n        (flags.skipSelf && 4 /* InternalInjectFlags.SkipSelf */));\n}\n// Converts bitflags to inject options\nfunction convertToInjectOptions(flags) {\n    return {\n        optional: !!(flags & 8 /* InternalInjectFlags.Optional */),\n        host: !!(flags & 1 /* InternalInjectFlags.Host */),\n        self: !!(flags & 2 /* InternalInjectFlags.Self */),\n        skipSelf: !!(flags & 4 /* InternalInjectFlags.SkipSelf */),\n    };\n}\nfunction injectArgs(types) {\n    const args = [];\n    for (let i = 0; i < types.length; i++) {\n        const arg = resolveForwardRef(types[i]);\n        if (Array.isArray(arg)) {\n            if (arg.length === 0) {\n                throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && 'Arguments array must have arguments.');\n            }\n            let type = undefined;\n            let flags = 0 /* InternalInjectFlags.Default */;\n            for (let j = 0; j < arg.length; j++) {\n                const meta = arg[j];\n                const flag = getInjectFlag(meta);\n                if (typeof flag === 'number') {\n                    // Special case when we handle @Inject decorator.\n                    if (flag === -1 /* DecoratorFlags.Inject */) {\n                        type = meta.token;\n                    }\n                    else {\n                        flags |= flag;\n                    }\n                }\n                else {\n                    type = meta;\n                }\n            }\n            args.push(ɵɵinject(type, flags));\n        }\n        else {\n            args.push(ɵɵinject(arg));\n        }\n    }\n    return args;\n}\n/**\n * Attaches a given InjectFlag to a given decorator using monkey-patching.\n * Since DI decorators can be used in providers `deps` array (when provider is configured using\n * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we\n * attach the flag to make it available both as a static property and as a field on decorator\n * instance.\n *\n * @param decorator Provided DI decorator.\n * @param flag InjectFlag that should be applied.\n */\nfunction attachInjectFlag(decorator, flag) {\n    decorator[DI_DECORATOR_FLAG] = flag;\n    decorator.prototype[DI_DECORATOR_FLAG] = flag;\n    return decorator;\n}\n/**\n * Reads monkey-patched property that contains InjectFlag attached to a decorator.\n *\n * @param token Token that may contain monkey-patched DI flags property.\n */\nfunction getInjectFlag(token) {\n    return token[DI_DECORATOR_FLAG];\n}\nfunction catchInjectorError(e, token, injectorErrorName, source) {\n    const tokenPath = e[NG_TEMP_TOKEN_PATH];\n    if (token[SOURCE]) {\n        tokenPath.unshift(token[SOURCE]);\n    }\n    e.message = formatError('\\n' + e.message, tokenPath, injectorErrorName, source);\n    e[NG_TOKEN_PATH] = tokenPath;\n    e[NG_TEMP_TOKEN_PATH] = null;\n    throw e;\n}\nfunction formatError(text, obj, injectorErrorName, source = null) {\n    text = text && text.charAt(0) === '\\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;\n    let context = stringify(obj);\n    if (Array.isArray(obj)) {\n        context = obj.map(stringify).join(' -> ');\n    }\n    else if (typeof obj === 'object') {\n        let parts = [];\n        for (let key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                let value = obj[key];\n                parts.push(key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)));\n            }\n        }\n        context = `{${parts.join(', ')}}`;\n    }\n    return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(NEW_LINE, '\\n  ')}`;\n}\n\nfunction getFactoryDef(type, throwNotFound) {\n    const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);\n    if (!hasFactoryDef && throwNotFound === true && ngDevMode) {\n        throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);\n    }\n    return hasFactoryDef ? type[NG_FACTORY_DEF] : null;\n}\n\n/**\n * Determines if the contents of two arrays is identical\n *\n * @param a first array\n * @param b second array\n * @param identityAccessor Optional function for extracting stable object identity from a value in\n *     the array.\n */\nfunction arrayEquals(a, b, identityAccessor) {\n    if (a.length !== b.length)\n        return false;\n    for (let i = 0; i < a.length; i++) {\n        let valueA = a[i];\n        let valueB = b[i];\n        if (identityAccessor) {\n            valueA = identityAccessor(valueA);\n            valueB = identityAccessor(valueB);\n        }\n        if (valueB !== valueA) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Flattens an array.\n */\nfunction flatten(list) {\n    return list.flat(Number.POSITIVE_INFINITY);\n}\nfunction deepForEach(input, fn) {\n    input.forEach((value) => (Array.isArray(value) ? deepForEach(value, fn) : fn(value)));\n}\nfunction addToArray(arr, index, value) {\n    // perf: array.push is faster than array.splice!\n    if (index >= arr.length) {\n        arr.push(value);\n    }\n    else {\n        arr.splice(index, 0, value);\n    }\n}\nfunction removeFromArray(arr, index) {\n    // perf: array.pop is faster than array.splice!\n    if (index >= arr.length - 1) {\n        return arr.pop();\n    }\n    else {\n        return arr.splice(index, 1)[0];\n    }\n}\nfunction newArray(size, value) {\n    const list = [];\n    for (let i = 0; i < size; i++) {\n        list.push(value);\n    }\n    return list;\n}\n/**\n * Remove item from array (Same as `Array.splice()` but faster.)\n *\n * `Array.splice()` is not as fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * https://jsperf.com/fast-array-splice (About 20x faster)\n *\n * @param array Array to splice\n * @param index Index of element in array to remove.\n * @param count Number of items to remove.\n */\nfunction arraySplice(array, index, count) {\n    const length = array.length - count;\n    while (index < length) {\n        array[index] = array[index + count];\n        index++;\n    }\n    while (count--) {\n        array.pop(); // shrink the array\n    }\n}\n/**\n * Same as `Array.splice2(index, 0, value1, value2)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value1 Value to add to array.\n * @param value2 Value to add to array.\n */\nfunction arrayInsert2(array, index, value1, value2) {\n    ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n    let end = array.length;\n    if (end == index) {\n        // inserting at the end.\n        array.push(value1, value2);\n    }\n    else if (end === 1) {\n        // corner case when we have less items in array than we have items to insert.\n        array.push(value2, array[0]);\n        array[0] = value1;\n    }\n    else {\n        end--;\n        array.push(array[end - 1], array[end]);\n        while (end > index) {\n            const previousEnd = end - 2;\n            array[end] = array[previousEnd];\n            end--;\n        }\n        array[index] = value1;\n        array[index + 1] = value2;\n    }\n}\n/**\n * Set a `value` for a `key`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or create.\n * @param value The value to set for a `key`.\n * @returns index (always even) of where the value vas set.\n */\nfunction keyValueArraySet(keyValueArray, key, value) {\n    let index = keyValueArrayIndexOf(keyValueArray, key);\n    if (index >= 0) {\n        // if we found it set it.\n        keyValueArray[index | 1] = value;\n    }\n    else {\n        index = ~index;\n        arrayInsert2(keyValueArray, index, key, value);\n    }\n    return index;\n}\n/**\n * Retrieve a `value` for a `key` (on `undefined` if not found.)\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @return The `value` stored at the `key` location or `undefined if not found.\n */\nfunction keyValueArrayGet(keyValueArray, key) {\n    const index = keyValueArrayIndexOf(keyValueArray, key);\n    if (index >= 0) {\n        // if we found it retrieve it.\n        return keyValueArray[index | 1];\n    }\n    return undefined;\n}\n/**\n * Retrieve a `key` index value in the array or `-1` if not found.\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @returns index of where the key is (or should have been.)\n *   - positive (even) index if key found.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been inserted.)\n */\nfunction keyValueArrayIndexOf(keyValueArray, key) {\n    return _arrayIndexOfSorted(keyValueArray, key, 1);\n}\n/**\n * INTERNAL: Get an index of an `value` in a sorted `array` by grouping search by `shift`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @param shift grouping shift.\n *   - `0` means look at every location\n *   - `1` means only look at every other (even) location (the odd locations are to be ignored as\n *         they are values.)\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n * inserted)\n */\nfunction _arrayIndexOfSorted(array, value, shift) {\n    ngDevMode && assertEqual(Array.isArray(array), true, 'Expecting an array');\n    let start = 0;\n    let end = array.length >> shift;\n    while (end !== start) {\n        const middle = start + ((end - start) >> 1); // find the middle.\n        const current = array[middle << shift];\n        if (value === current) {\n            return middle << shift;\n        }\n        else if (current > value) {\n            end = middle;\n        }\n        else {\n            start = middle + 1; // We already searched middle so make it non-inclusive by adding 1\n        }\n    }\n    return ~(end << shift);\n}\n\n/**\n * This file contains reuseable \"empty\" symbols that can be used as default return values\n * in different parts of the rendering code. Because the same symbols are returned, this\n * allows for identity checks against these values to be consistently used by the framework\n * code.\n */\nconst EMPTY_OBJ = {};\nconst EMPTY_ARRAY = [];\n// freezing the values prevents any code from accidentally inserting new values in\nif ((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode()) {\n    // These property accesses can be ignored because ngDevMode will be set to false\n    // when optimizing code and the whole if statement will be dropped.\n    // tslint:disable-next-line:no-toplevel-property-access\n    Object.freeze(EMPTY_OBJ);\n    // tslint:disable-next-line:no-toplevel-property-access\n    Object.freeze(EMPTY_ARRAY);\n}\n\n/**\n * A multi-provider token for initialization functions that will run upon construction of an\n * environment injector.\n *\n * @deprecated from v19.0.0, use provideEnvironmentInitializer instead\n *\n * @see {@link provideEnvironmentInitializer}\n *\n * Note: As opposed to the `APP_INITIALIZER` token, the `ENVIRONMENT_INITIALIZER` functions are not awaited,\n * hence they should not be `async`.\n *\n * @publicApi\n */\nconst ENVIRONMENT_INITIALIZER = new InjectionToken(ngDevMode ? 'ENVIRONMENT_INITIALIZER' : '');\n\n/**\n * An InjectionToken that gets the current `Injector` for `createInjector()`-style injectors.\n *\n * Requesting this token instead of `Injector` allows `StaticInjector` to be tree-shaken from a\n * project.\n *\n * @publicApi\n */\nconst INJECTOR$1 = new InjectionToken(ngDevMode ? 'INJECTOR' : '', \n// Disable tslint because this is const enum which gets inlined not top level prop access.\n// tslint:disable-next-line: no-toplevel-property-access\n-1 /* InjectorMarkers.Injector */);\n\nconst INJECTOR_DEF_TYPES = new InjectionToken(ngDevMode ? 'INJECTOR_DEF_TYPES' : '');\n\nclass NullInjector {\n    get(token, notFoundValue = THROW_IF_NOT_FOUND) {\n        if (notFoundValue === THROW_IF_NOT_FOUND) {\n            const error = new NotFoundError(`NullInjectorError: No provider for ${stringify(token)}!`);\n            throw error;\n        }\n        return notFoundValue;\n    }\n}\n\nfunction getNgModuleDef(type) {\n    return type[NG_MOD_DEF] || null;\n}\nfunction getNgModuleDefOrThrow(type) {\n    const ngModuleDef = getNgModuleDef(type);\n    if (!ngModuleDef) {\n        throw new RuntimeError(915 /* RuntimeErrorCode.MISSING_NG_MODULE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Type ${stringify(type)} does not have 'ɵmod' property.`);\n    }\n    return ngModuleDef;\n}\n/**\n * The following getter methods retrieve the definition from the type. Currently the retrieval\n * honors inheritance, but in the future we may change the rule to require that definitions are\n * explicit. This would require some sort of migration strategy.\n */\nfunction getComponentDef(type) {\n    return type[NG_COMP_DEF] || null;\n}\nfunction getDirectiveDefOrThrow(type) {\n    const def = getDirectiveDef(type);\n    if (!def) {\n        throw new RuntimeError(916 /* RuntimeErrorCode.MISSING_DIRECTIVE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Type ${stringify(type)} does not have 'ɵdir' property.`);\n    }\n    return def;\n}\nfunction getDirectiveDef(type) {\n    return type[NG_DIR_DEF] || null;\n}\nfunction getPipeDef(type) {\n    return type[NG_PIPE_DEF] || null;\n}\n/**\n * Checks whether a given Component, Directive or Pipe is marked as standalone.\n * This will return false if passed anything other than a Component, Directive, or Pipe class\n * See [this guide](guide/components/importing) for additional information:\n *\n * @param type A reference to a Component, Directive or Pipe.\n * @publicApi\n */\nfunction isStandalone(type) {\n    const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);\n    return def !== null && def.standalone;\n}\n\n/**\n * Wrap an array of `Provider`s into `EnvironmentProviders`, preventing them from being accidentally\n * referenced in `@Component` in a component injector.\n *\n * @publicApi\n */\nfunction makeEnvironmentProviders(providers) {\n    return {\n        ɵproviders: providers,\n    };\n}\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon construction\n * of an environment injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `ENVIRONMENT_INITIALIZER` token which is now deprecated.\n *\n * @see {@link ENVIRONMENT_INITIALIZER}\n *\n * @usageNotes\n * The following example illustrates how to configure an initialization function using\n * `provideEnvironmentInitializer()`\n * ```ts\n * createEnvironmentInjector(\n *   [\n *     provideEnvironmentInitializer(() => {\n *       console.log('environment initialized');\n *     }),\n *   ],\n *   parentInjector\n * );\n * ```\n *\n * @publicApi\n */\nfunction provideEnvironmentInitializer(initializerFn) {\n    return makeEnvironmentProviders([\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            multi: true,\n            useValue: initializerFn,\n        },\n    ]);\n}\n/**\n * Collects providers from all NgModules and standalone components, including transitively imported\n * ones.\n *\n * Providers extracted via `importProvidersFrom` are only usable in an application injector or\n * another environment injector (such as a route injector). They should not be used in component\n * providers.\n *\n * More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The results of the `importProvidersFrom` call can be used in the `bootstrapApplication` call:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(NgModuleOne, NgModuleTwo)\n *   ]\n * });\n * ```\n *\n * You can also use the `importProvidersFrom` results in the `providers` field of a route, when a\n * standalone component is used:\n *\n * ```ts\n * export const ROUTES: Route[] = [\n *   {\n *     path: 'foo',\n *     providers: [\n *       importProvidersFrom(NgModuleOne, NgModuleTwo)\n *     ],\n *     component: YourStandaloneComponent\n *   }\n * ];\n * ```\n *\n * @returns Collected providers from the specified list of types.\n * @publicApi\n */\nfunction importProvidersFrom(...sources) {\n    return {\n        ɵproviders: internalImportProvidersFrom(true, sources),\n        ɵfromNgModule: true,\n    };\n}\nfunction internalImportProvidersFrom(checkForStandaloneCmp, ...sources) {\n    const providersOut = [];\n    const dedup = new Set(); // already seen types\n    let injectorTypesWithProviders;\n    const collectProviders = (provider) => {\n        providersOut.push(provider);\n    };\n    deepForEach(sources, (source) => {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && checkForStandaloneCmp) {\n            const cmpDef = getComponentDef(source);\n            if (cmpDef?.standalone) {\n                throw new RuntimeError(800 /* RuntimeErrorCode.IMPORT_PROVIDERS_FROM_STANDALONE */, `Importing providers supports NgModule or ModuleWithProviders but got a standalone component \"${stringifyForError(source)}\"`);\n            }\n        }\n        // Narrow `source` to access the internal type analogue for `ModuleWithProviders`.\n        const internalSource = source;\n        if (walkProviderTree(internalSource, collectProviders, [], dedup)) {\n            injectorTypesWithProviders ||= [];\n            injectorTypesWithProviders.push(internalSource);\n        }\n    });\n    // Collect all providers from `ModuleWithProviders` types.\n    if (injectorTypesWithProviders !== undefined) {\n        processInjectorTypesWithProviders(injectorTypesWithProviders, collectProviders);\n    }\n    return providersOut;\n}\n/**\n * Collects all providers from the list of `ModuleWithProviders` and appends them to the provided\n * array.\n */\nfunction processInjectorTypesWithProviders(typesWithProviders, visitor) {\n    for (let i = 0; i < typesWithProviders.length; i++) {\n        const { ngModule, providers } = typesWithProviders[i];\n        deepForEachProvider(providers, (provider) => {\n            ngDevMode && validateProvider(provider, providers || EMPTY_ARRAY, ngModule);\n            visitor(provider, ngModule);\n        });\n    }\n}\n/**\n * The logic visits an `InjectorType`, an `InjectorTypeWithProviders`, or a standalone\n * `ComponentType`, and all of its transitive providers and collects providers.\n *\n * If an `InjectorTypeWithProviders` that declares providers besides the type is specified,\n * the function will return \"true\" to indicate that the providers of the type definition need\n * to be processed. This allows us to process providers of injector types after all imports of\n * an injector definition are processed. (following View Engine semantics: see FW-1349)\n */\nfunction walkProviderTree(container, visitor, parents, dedup) {\n    container = resolveForwardRef(container);\n    if (!container)\n        return false;\n    // The actual type which had the definition. Usually `container`, but may be an unwrapped type\n    // from `InjectorTypeWithProviders`.\n    let defType = null;\n    let injDef = getInjectorDef(container);\n    const cmpDef = !injDef && getComponentDef(container);\n    if (!injDef && !cmpDef) {\n        // `container` is not an injector type or a component type. It might be:\n        //  * An `InjectorTypeWithProviders` that wraps an injector type.\n        //  * A standalone directive or pipe that got pulled in from a standalone component's\n        //    dependencies.\n        // Try to unwrap it as an `InjectorTypeWithProviders` first.\n        const ngModule = container\n            .ngModule;\n        injDef = getInjectorDef(ngModule);\n        if (injDef) {\n            defType = ngModule;\n        }\n        else {\n            // Not a component or injector type, so ignore it.\n            return false;\n        }\n    }\n    else if (cmpDef && !cmpDef.standalone) {\n        return false;\n    }\n    else {\n        defType = container;\n    }\n    // Check for circular dependencies.\n    if (ngDevMode && parents.indexOf(defType) !== -1) {\n        const defName = stringify(defType);\n        const path = parents.map(stringify);\n        throwCyclicDependencyError(defName, path);\n    }\n    // Check for multiple imports of the same module\n    const isDuplicate = dedup.has(defType);\n    if (cmpDef) {\n        if (isDuplicate) {\n            // This component definition has already been processed.\n            return false;\n        }\n        dedup.add(defType);\n        if (cmpDef.dependencies) {\n            const deps = typeof cmpDef.dependencies === 'function' ? cmpDef.dependencies() : cmpDef.dependencies;\n            for (const dep of deps) {\n                walkProviderTree(dep, visitor, parents, dedup);\n            }\n        }\n    }\n    else if (injDef) {\n        // First, include providers from any imports.\n        if (injDef.imports != null && !isDuplicate) {\n            // Before processing defType's imports, add it to the set of parents. This way, if it ends\n            // up deeply importing itself, this can be detected.\n            ngDevMode && parents.push(defType);\n            // Add it to the set of dedups. This way we can detect multiple imports of the same module\n            dedup.add(defType);\n            let importTypesWithProviders;\n            try {\n                deepForEach(injDef.imports, (imported) => {\n                    if (walkProviderTree(imported, visitor, parents, dedup)) {\n                        importTypesWithProviders ||= [];\n                        // If the processed import is an injector type with providers, we store it in the\n                        // list of import types with providers, so that we can process those afterwards.\n                        importTypesWithProviders.push(imported);\n                    }\n                });\n            }\n            finally {\n                // Remove it from the parents set when finished.\n                ngDevMode && parents.pop();\n            }\n            // Imports which are declared with providers (TypeWithProviders) need to be processed\n            // after all imported modules are processed. This is similar to how View Engine\n            // processes/merges module imports in the metadata resolver. See: FW-1349.\n            if (importTypesWithProviders !== undefined) {\n                processInjectorTypesWithProviders(importTypesWithProviders, visitor);\n            }\n        }\n        if (!isDuplicate) {\n            // Track the InjectorType and add a provider for it.\n            // It's important that this is done after the def's imports.\n            const factory = getFactoryDef(defType) || (() => new defType());\n            // Append extra providers to make more info available for consumers (to retrieve an injector\n            // type), as well as internally (to calculate an injection scope correctly and eagerly\n            // instantiate a `defType` when an injector is created).\n            // Provider to create `defType` using its factory.\n            visitor({ provide: defType, useFactory: factory, deps: EMPTY_ARRAY }, defType);\n            // Make this `defType` available to an internal logic that calculates injector scope.\n            visitor({ provide: INJECTOR_DEF_TYPES, useValue: defType, multi: true }, defType);\n            // Provider to eagerly instantiate `defType` via `INJECTOR_INITIALIZER`.\n            visitor({ provide: ENVIRONMENT_INITIALIZER, useValue: () => ɵɵinject(defType), multi: true }, defType);\n        }\n        // Next, include providers listed on the definition itself.\n        const defProviders = injDef.providers;\n        if (defProviders != null && !isDuplicate) {\n            const injectorType = container;\n            deepForEachProvider(defProviders, (provider) => {\n                ngDevMode && validateProvider(provider, defProviders, injectorType);\n                visitor(provider, injectorType);\n            });\n        }\n    }\n    else {\n        // Should not happen, but just in case.\n        return false;\n    }\n    return (defType !== container && container.providers !== undefined);\n}\nfunction validateProvider(provider, providers, containerType) {\n    if (isTypeProvider(provider) ||\n        isValueProvider(provider) ||\n        isFactoryProvider(provider) ||\n        isExistingProvider(provider)) {\n        return;\n    }\n    // Here we expect the provider to be a `useClass` provider (by elimination).\n    const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n    if (!classRef) {\n        throwInvalidProviderError(containerType, providers, provider);\n    }\n}\nfunction deepForEachProvider(providers, fn) {\n    for (let provider of providers) {\n        if (isEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            deepForEachProvider(provider, fn);\n        }\n        else {\n            fn(provider);\n        }\n    }\n}\nconst USE_VALUE = getClosureSafeProperty({\n    provide: String,\n    useValue: getClosureSafeProperty,\n});\nfunction isValueProvider(value) {\n    return value !== null && typeof value == 'object' && USE_VALUE in value;\n}\nfunction isExistingProvider(value) {\n    return !!(value && value.useExisting);\n}\nfunction isFactoryProvider(value) {\n    return !!(value && value.useFactory);\n}\nfunction isTypeProvider(value) {\n    return typeof value === 'function';\n}\nfunction isClassProvider(value) {\n    return !!value.useClass;\n}\n\n/**\n * An internal token whose presence in an injector indicates that the injector should treat itself\n * as a root scoped injector when processing requests for unknown tokens which may indicate\n * they are provided in the root scope.\n */\nconst INJECTOR_SCOPE = new InjectionToken(ngDevMode ? 'Set Injector scope.' : '');\n\n/**\n * Marker which indicates that a value has not yet been created from the factory function.\n */\nconst NOT_YET = {};\n/**\n * Marker which indicates that the factory function for a token is in the process of being called.\n *\n * If the injector is asked to inject a token with its value set to CIRCULAR, that indicates\n * injection of a dependency has recursively attempted to inject the original token, and there is\n * a circular dependency among the providers.\n */\nconst CIRCULAR = {};\n/**\n * A lazily initialized NullInjector.\n */\nlet NULL_INJECTOR = undefined;\nfunction getNullInjector() {\n    if (NULL_INJECTOR === undefined) {\n        NULL_INJECTOR = new NullInjector();\n    }\n    return NULL_INJECTOR;\n}\n/**\n * An `Injector` that's part of the environment injector hierarchy, which exists outside of the\n * component tree.\n *\n * @publicApi\n */\nclass EnvironmentInjector {\n}\nclass R3Injector extends EnvironmentInjector {\n    parent;\n    source;\n    scopes;\n    /**\n     * Map of tokens to records which contain the instances of those tokens.\n     * - `null` value implies that we don't have the record. Used by tree-shakable injectors\n     * to prevent further searches.\n     */\n    records = new Map();\n    /**\n     * Set of values instantiated by this injector which contain `ngOnDestroy` lifecycle hooks.\n     */\n    _ngOnDestroyHooks = new Set();\n    _onDestroyHooks = [];\n    /**\n     * Flag indicating that this injector was previously destroyed.\n     */\n    get destroyed() {\n        return this._destroyed;\n    }\n    _destroyed = false;\n    injectorDefTypes;\n    constructor(providers, parent, source, scopes) {\n        super();\n        this.parent = parent;\n        this.source = source;\n        this.scopes = scopes;\n        // Start off by creating Records for every provider.\n        forEachSingleProvider(providers, (provider) => this.processProvider(provider));\n        // Make sure the INJECTOR token provides this injector.\n        this.records.set(INJECTOR$1, makeRecord(undefined, this));\n        // And `EnvironmentInjector` if the current injector is supposed to be env-scoped.\n        if (scopes.has('environment')) {\n            this.records.set(EnvironmentInjector, makeRecord(undefined, this));\n        }\n        // Detect whether this injector has the APP_ROOT_SCOPE token and thus should provide\n        // any injectable scoped to APP_ROOT_SCOPE.\n        const record = this.records.get(INJECTOR_SCOPE);\n        if (record != null && typeof record.value === 'string') {\n            this.scopes.add(record.value);\n        }\n        this.injectorDefTypes = new Set(this.get(INJECTOR_DEF_TYPES, EMPTY_ARRAY, { self: true }));\n    }\n    retrieve(token, options) {\n        const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n        try {\n            return this.get(token, \n            // When a dependency is requested with an optional flag, DI returns null as the default value.\n            THROW_IF_NOT_FOUND, flags);\n        }\n        catch (e) {\n            if (isNotFound$1(e)) {\n                return e;\n            }\n            throw e;\n        }\n    }\n    /**\n     * Destroy the injector and release references to every instance or provider associated with it.\n     *\n     * Also calls the `OnDestroy` lifecycle hooks of every instance that was created for which a\n     * hook was found.\n     */\n    destroy() {\n        assertNotDestroyed(this);\n        // Set destroyed = true first, in case lifecycle hooks re-enter destroy().\n        this._destroyed = true;\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            // Call all the lifecycle hooks.\n            for (const service of this._ngOnDestroyHooks) {\n                service.ngOnDestroy();\n            }\n            const onDestroyHooks = this._onDestroyHooks;\n            // Reset the _onDestroyHooks array before iterating over it to prevent hooks that unregister\n            // themselves from mutating the array during iteration.\n            this._onDestroyHooks = [];\n            for (const hook of onDestroyHooks) {\n                hook();\n            }\n        }\n        finally {\n            // Release all references.\n            this.records.clear();\n            this._ngOnDestroyHooks.clear();\n            this.injectorDefTypes.clear();\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    onDestroy(callback) {\n        assertNotDestroyed(this);\n        this._onDestroyHooks.push(callback);\n        return () => this.removeOnDestroy(callback);\n    }\n    runInContext(fn) {\n        assertNotDestroyed(this);\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: null });\n        }\n        try {\n            return fn();\n        }\n        finally {\n            setCurrentInjector(previousInjector);\n            setInjectImplementation(previousInjectImplementation);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n        }\n    }\n    get(token, notFoundValue = THROW_IF_NOT_FOUND, options) {\n        assertNotDestroyed(this);\n        if (token.hasOwnProperty(NG_ENV_ID)) {\n            return token[NG_ENV_ID](this);\n        }\n        const flags = convertToBitFlags(options);\n        // Set the injection context.\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: token });\n        }\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        try {\n            // Check for the SkipSelf flag.\n            if (!(flags & 4 /* InternalInjectFlags.SkipSelf */)) {\n                // SkipSelf isn't set, check if the record belongs to this injector.\n                let record = this.records.get(token);\n                if (record === undefined) {\n                    // No record, but maybe the token is scoped to this injector. Look for an injectable\n                    // def with a scope matching this injector.\n                    const def = couldBeInjectableType(token) && getInjectableDef(token);\n                    if (def && this.injectableDefInScope(def)) {\n                        // Found an injectable def and it's scoped to this injector. Pretend as if it was here\n                        // all along.\n                        if (ngDevMode) {\n                            runInInjectorProfilerContext(this, token, () => {\n                                emitProviderConfiguredEvent(token);\n                            });\n                        }\n                        record = makeRecord(injectableDefOrInjectorDefFactory(token), NOT_YET);\n                    }\n                    else {\n                        record = null;\n                    }\n                    this.records.set(token, record);\n                }\n                // If a record was found, get the instance for it and return it.\n                if (record != null /* NOT null || undefined */) {\n                    return this.hydrate(token, record);\n                }\n            }\n            // Select the next injector based on the Self flag - if self is set, the next injector is\n            // the NullInjector, otherwise it's the parent.\n            const nextInjector = !(flags & 2 /* InternalInjectFlags.Self */) ? this.parent : getNullInjector();\n            // Set the notFoundValue based on the Optional flag - if optional is set and notFoundValue\n            // is undefined, the value is null, otherwise it's the notFoundValue.\n            notFoundValue =\n                flags & 8 /* InternalInjectFlags.Optional */ && notFoundValue === THROW_IF_NOT_FOUND\n                    ? null\n                    : notFoundValue;\n            return nextInjector.get(token, notFoundValue);\n        }\n        catch (e) {\n            if (isNotFound$1(e)) {\n                // @ts-ignore\n                const path = (e[NG_TEMP_TOKEN_PATH] = e[NG_TEMP_TOKEN_PATH] || []);\n                path.unshift(stringify(token));\n                if (previousInjector) {\n                    // We still have a parent injector, keep throwing\n                    throw e;\n                }\n                else {\n                    // Format & throw the final error message when we don't have any previous injector\n                    return catchInjectorError(e, token, 'R3InjectorError', this.source);\n                }\n            }\n            else {\n                throw e;\n            }\n        }\n        finally {\n            // Lastly, restore the previous injection context.\n            setInjectImplementation(previousInjectImplementation);\n            setCurrentInjector(previousInjector);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n        }\n    }\n    /** @internal */\n    resolveInjectorInitializers() {\n        const prevConsumer = setActiveConsumer(null);\n        const previousInjector = setCurrentInjector(this);\n        const previousInjectImplementation = setInjectImplementation(undefined);\n        let prevInjectContext;\n        if (ngDevMode) {\n            prevInjectContext = setInjectorProfilerContext({ injector: this, token: null });\n        }\n        try {\n            const initializers = this.get(ENVIRONMENT_INITIALIZER, EMPTY_ARRAY, { self: true });\n            if (ngDevMode && !Array.isArray(initializers)) {\n                throw new RuntimeError(-209 /* RuntimeErrorCode.INVALID_MULTI_PROVIDER */, 'Unexpected type of the `ENVIRONMENT_INITIALIZER` token value ' +\n                    `(expected an array, but got ${typeof initializers}). ` +\n                    'Please check that the `ENVIRONMENT_INITIALIZER` token is configured as a ' +\n                    '`multi: true` provider.');\n            }\n            for (const initializer of initializers) {\n                initializer();\n            }\n        }\n        finally {\n            setCurrentInjector(previousInjector);\n            setInjectImplementation(previousInjectImplementation);\n            ngDevMode && setInjectorProfilerContext(prevInjectContext);\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    toString() {\n        const tokens = [];\n        const records = this.records;\n        for (const token of records.keys()) {\n            tokens.push(stringify(token));\n        }\n        return `R3Injector[${tokens.join(', ')}]`;\n    }\n    /**\n     * Process a `SingleProvider` and add it.\n     */\n    processProvider(provider) {\n        // Determine the token from the provider. Either it's its own token, or has a {provide: ...}\n        // property.\n        provider = resolveForwardRef(provider);\n        let token = isTypeProvider(provider)\n            ? provider\n            : resolveForwardRef(provider && provider.provide);\n        // Construct a `Record` for the provider.\n        const record = providerToRecord(provider);\n        if (ngDevMode) {\n            runInInjectorProfilerContext(this, token, () => {\n                // Emit InjectorProfilerEventType.Create if provider is a value provider because\n                // these are the only providers that do not go through the value hydration logic\n                // where this event would normally be emitted from.\n                if (isValueProvider(provider)) {\n                    emitInjectorToCreateInstanceEvent(token);\n                    emitInstanceCreatedByInjectorEvent(provider.useValue);\n                }\n                emitProviderConfiguredEvent(provider);\n            });\n        }\n        if (!isTypeProvider(provider) && provider.multi === true) {\n            // If the provider indicates that it's a multi-provider, process it specially.\n            // First check whether it's been defined already.\n            let multiRecord = this.records.get(token);\n            if (multiRecord) {\n                // It has. Throw a nice error if\n                if (ngDevMode && multiRecord.multi === undefined) {\n                    throwMixedMultiProviderError();\n                }\n            }\n            else {\n                multiRecord = makeRecord(undefined, NOT_YET, true);\n                multiRecord.factory = () => injectArgs(multiRecord.multi);\n                this.records.set(token, multiRecord);\n            }\n            token = provider;\n            multiRecord.multi.push(provider);\n        }\n        else {\n            if (ngDevMode) {\n                const existing = this.records.get(token);\n                if (existing && existing.multi !== undefined) {\n                    throwMixedMultiProviderError();\n                }\n            }\n        }\n        this.records.set(token, record);\n    }\n    hydrate(token, record) {\n        const prevConsumer = setActiveConsumer(null);\n        try {\n            if (record.value === CIRCULAR) {\n                throwCyclicDependencyError(stringify(token));\n            }\n            else if (record.value === NOT_YET) {\n                record.value = CIRCULAR;\n                if (ngDevMode) {\n                    runInInjectorProfilerContext(this, token, () => {\n                        emitInjectorToCreateInstanceEvent(token);\n                        record.value = record.factory();\n                        emitInstanceCreatedByInjectorEvent(record.value);\n                    });\n                }\n                else {\n                    record.value = record.factory();\n                }\n            }\n            if (typeof record.value === 'object' && record.value && hasOnDestroy(record.value)) {\n                this._ngOnDestroyHooks.add(record.value);\n            }\n            return record.value;\n        }\n        finally {\n            setActiveConsumer(prevConsumer);\n        }\n    }\n    injectableDefInScope(def) {\n        if (!def.providedIn) {\n            return false;\n        }\n        const providedIn = resolveForwardRef(def.providedIn);\n        if (typeof providedIn === 'string') {\n            return providedIn === 'any' || this.scopes.has(providedIn);\n        }\n        else {\n            return this.injectorDefTypes.has(providedIn);\n        }\n    }\n    removeOnDestroy(callback) {\n        const destroyCBIdx = this._onDestroyHooks.indexOf(callback);\n        if (destroyCBIdx !== -1) {\n            this._onDestroyHooks.splice(destroyCBIdx, 1);\n        }\n    }\n}\nfunction injectableDefOrInjectorDefFactory(token) {\n    // Most tokens will have an injectable def directly on them, which specifies a factory directly.\n    const injectableDef = getInjectableDef(token);\n    const factory = injectableDef !== null ? injectableDef.factory : getFactoryDef(token);\n    if (factory !== null) {\n        return factory;\n    }\n    // InjectionTokens should have an injectable def (ɵprov) and thus should be handled above.\n    // If it's missing that, it's an error.\n    if (token instanceof InjectionToken) {\n        throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Token ${stringify(token)} is missing a ɵprov definition.`);\n    }\n    // Undecorated types can sometimes be created if they have no constructor arguments.\n    if (token instanceof Function) {\n        return getUndecoratedInjectableFactory(token);\n    }\n    // There was no way to resolve a factory for this token.\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && 'unreachable');\n}\nfunction getUndecoratedInjectableFactory(token) {\n    // If the token has parameters then it has dependencies that we cannot resolve implicitly.\n    const paramLength = token.length;\n    if (paramLength > 0) {\n        throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode &&\n            `Can't resolve all parameters for ${stringify(token)}: (${newArray(paramLength, '?').join(', ')}).`);\n    }\n    // The constructor function appears to have no parameters.\n    // This might be because it inherits from a super-class. In which case, use an injectable\n    // def from an ancestor if there is one.\n    // Otherwise this really is a simple class with no dependencies, so return a factory that\n    // just instantiates the zero-arg constructor.\n    const inheritedInjectableDef = getInheritedInjectableDef(token);\n    if (inheritedInjectableDef !== null) {\n        return () => inheritedInjectableDef.factory(token);\n    }\n    else {\n        return () => new token();\n    }\n}\nfunction providerToRecord(provider) {\n    if (isValueProvider(provider)) {\n        return makeRecord(undefined, provider.useValue);\n    }\n    else {\n        const factory = providerToFactory(provider);\n        return makeRecord(factory, NOT_YET);\n    }\n}\n/**\n * Converts a `SingleProvider` into a factory function.\n *\n * @param provider provider to convert to factory\n */\nfunction providerToFactory(provider, ngModuleType, providers) {\n    let factory = undefined;\n    if (ngDevMode && isEnvironmentProviders(provider)) {\n        throwInvalidProviderError(undefined, providers, provider);\n    }\n    if (isTypeProvider(provider)) {\n        const unwrappedProvider = resolveForwardRef(provider);\n        return getFactoryDef(unwrappedProvider) || injectableDefOrInjectorDefFactory(unwrappedProvider);\n    }\n    else {\n        if (isValueProvider(provider)) {\n            factory = () => resolveForwardRef(provider.useValue);\n        }\n        else if (isFactoryProvider(provider)) {\n            factory = () => provider.useFactory(...injectArgs(provider.deps || []));\n        }\n        else if (isExistingProvider(provider)) {\n            factory = () => ɵɵinject(resolveForwardRef(provider.useExisting));\n        }\n        else {\n            const classRef = resolveForwardRef(provider &&\n                (provider.useClass || provider.provide));\n            if (ngDevMode && !classRef) {\n                throwInvalidProviderError(ngModuleType, providers, provider);\n            }\n            if (hasDeps(provider)) {\n                factory = () => new classRef(...injectArgs(provider.deps));\n            }\n            else {\n                return getFactoryDef(classRef) || injectableDefOrInjectorDefFactory(classRef);\n            }\n        }\n    }\n    return factory;\n}\nfunction assertNotDestroyed(injector) {\n    if (injector.destroyed) {\n        throw new RuntimeError(205 /* RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED */, ngDevMode && 'Injector has already been destroyed.');\n    }\n}\nfunction makeRecord(factory, value, multi = false) {\n    return {\n        factory: factory,\n        value: value,\n        multi: multi ? [] : undefined,\n    };\n}\nfunction hasDeps(value) {\n    return !!value.deps;\n}\nfunction hasOnDestroy(value) {\n    return (value !== null &&\n        typeof value === 'object' &&\n        typeof value.ngOnDestroy === 'function');\n}\nfunction couldBeInjectableType(value) {\n    return (typeof value === 'function' ||\n        (typeof value === 'object' && value.ngMetadataName === 'InjectionToken'));\n}\nfunction forEachSingleProvider(providers, fn) {\n    for (const provider of providers) {\n        if (Array.isArray(provider)) {\n            forEachSingleProvider(provider, fn);\n        }\n        else if (provider && isEnvironmentProviders(provider)) {\n            forEachSingleProvider(provider.ɵproviders, fn);\n        }\n        else {\n            fn(provider);\n        }\n    }\n}\n\n/**\n * Runs the given function in the [context](guide/di/dependency-injection-context) of the given\n * `Injector`.\n *\n * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject dependencies\n * from the given `Injector`. Note that `inject` is only usable synchronously, and cannot be used in\n * any asynchronous callbacks or after any `await` points.\n *\n * @param injector the injector which will satisfy calls to [`inject`](api/core/inject) while `fn`\n *     is executing\n * @param fn the closure to be run in the context of `injector`\n * @returns the return value of the function, if any\n * @publicApi\n */\nfunction runInInjectionContext(injector, fn) {\n    let internalInjector;\n    if (injector instanceof R3Injector) {\n        assertNotDestroyed(injector);\n        internalInjector = injector;\n    }\n    else {\n        internalInjector = new RetrievingInjector(injector);\n    }\n    let prevInjectorProfilerContext;\n    if (ngDevMode) {\n        prevInjectorProfilerContext = setInjectorProfilerContext({ injector, token: null });\n    }\n    const prevInjector = setCurrentInjector(internalInjector);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    try {\n        return fn();\n    }\n    finally {\n        setCurrentInjector(prevInjector);\n        ngDevMode && setInjectorProfilerContext(prevInjectorProfilerContext);\n        setInjectImplementation(previousInjectImplementation);\n    }\n}\n/**\n * Whether the current stack frame is inside an injection context.\n */\nfunction isInInjectionContext() {\n    return getInjectImplementation() !== undefined || getCurrentInjector() != null;\n}\n/**\n * Asserts that the current stack frame is within an [injection\n * context](guide/di/dependency-injection-context) and has access to `inject`.\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertInInjectionContext(debugFn) {\n    // Taking a `Function` instead of a string name here prevents the unminified name of the function\n    // from being retained in the bundle regardless of minification.\n    if (!isInInjectionContext()) {\n        throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode &&\n            debugFn.name +\n                '() can only be used within an injection context such as a constructor, a factory function, a field initializer, or a function used with `runInInjectionContext`');\n    }\n}\n\n// Below are constants for LView indices to help us look up LView members\n// without having to remember the specific indices.\n// Uglify will inline these when minifying so there shouldn't be a cost.\nconst HOST = 0;\nconst TVIEW = 1;\n// Shared with LContainer\nconst FLAGS = 2;\nconst PARENT = 3;\nconst NEXT = 4;\nconst T_HOST = 5;\n// End shared with LContainer\nconst HYDRATION = 6;\nconst CLEANUP = 7;\nconst CONTEXT = 8;\nconst INJECTOR = 9;\nconst ENVIRONMENT = 10;\nconst RENDERER = 11;\nconst CHILD_HEAD = 12;\nconst CHILD_TAIL = 13;\n// FIXME(misko): Investigate if the three declarations aren't all same thing.\nconst DECLARATION_VIEW = 14;\nconst DECLARATION_COMPONENT_VIEW = 15;\nconst DECLARATION_LCONTAINER = 16;\nconst PREORDER_HOOK_FLAGS = 17;\nconst QUERIES = 18;\nconst ID = 19;\nconst EMBEDDED_VIEW_INJECTOR = 20;\nconst ON_DESTROY_HOOKS = 21;\nconst EFFECTS_TO_SCHEDULE = 22;\nconst EFFECTS = 23;\nconst REACTIVE_TEMPLATE_CONSUMER = 24;\nconst AFTER_RENDER_SEQUENCES_TO_ADD = 25;\n/**\n * Size of LView's header. Necessary to adjust for it when setting slots.\n *\n * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate\n * instruction index into `LView` index. All other indexes should be in the `LView` index space and\n * there should be no need to refer to `HEADER_OFFSET` anywhere else.\n */\nconst HEADER_OFFSET = 26;\n\n/**\n * Special location which allows easy identification of type. If we have an array which was\n * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is\n * `LContainer`.\n */\nconst TYPE = 1;\n/**\n * Below are constants for LContainer indices to help us look up LContainer members\n * without having to remember the specific indices.\n * Uglify will inline these when minifying so there shouldn't be a cost.\n */\n// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5\n// As we already have these constants in LView, we don't need to re-create them.\nconst DEHYDRATED_VIEWS = 6;\nconst NATIVE = 7;\nconst VIEW_REFS = 8;\nconst MOVED_VIEWS = 9;\n/**\n * Size of LContainer's header. Represents the index after which all views in the\n * container will be inserted. We need to keep a record of current views so we know\n * which views are already in the DOM (and don't need to be re-added) and so we can\n * remove views from the DOM when they are no longer required.\n */\nconst CONTAINER_HEADER_OFFSET = 10;\n\n/**\n * True if `value` is `LView`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLView(value) {\n    return Array.isArray(value) && typeof value[TYPE] === 'object';\n}\n/**\n * True if `value` is `LContainer`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLContainer(value) {\n    return Array.isArray(value) && value[TYPE] === true;\n}\nfunction isContentQueryHost(tNode) {\n    return (tNode.flags & 4 /* TNodeFlags.hasContentQuery */) !== 0;\n}\nfunction isComponentHost(tNode) {\n    return tNode.componentOffset > -1;\n}\nfunction isDirectiveHost(tNode) {\n    return (tNode.flags & 1 /* TNodeFlags.isDirectiveHost */) === 1 /* TNodeFlags.isDirectiveHost */;\n}\nfunction isComponentDef(def) {\n    return !!def.template;\n}\nfunction isRootView(target) {\n    // Determines whether a given LView is marked as a root view.\n    return (target[FLAGS] & 512 /* LViewFlags.IsRoot */) !== 0;\n}\nfunction isProjectionTNode(tNode) {\n    return (tNode.type & 16 /* TNodeType.Projection */) === 16 /* TNodeType.Projection */;\n}\nfunction hasI18n(lView) {\n    return (lView[FLAGS] & 32 /* LViewFlags.HasI18n */) === 32 /* LViewFlags.HasI18n */;\n}\nfunction isDestroyed(lView) {\n    // Determines whether a given LView is marked as destroyed.\n    return (lView[FLAGS] & 256 /* LViewFlags.Destroyed */) === 256 /* LViewFlags.Destroyed */;\n}\n\n// [Assert functions do not constraint type when they are guarded by a truthy\n// expression.](https://github.com/microsoft/TypeScript/issues/37295)\nfunction assertTNodeForLView(tNode, lView) {\n    assertTNodeForTView(tNode, lView[TVIEW]);\n}\nfunction assertTNodeForTView(tNode, tView) {\n    assertTNode(tNode);\n    const tData = tView.data;\n    for (let i = HEADER_OFFSET; i < tData.length; i++) {\n        if (tData[i] === tNode) {\n            return;\n        }\n    }\n    throwError('This TNode does not belong to this TView.');\n}\nfunction assertTNode(tNode) {\n    assertDefined(tNode, 'TNode must be defined');\n    if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {\n        throwError('Not of type TNode, got: ' + tNode);\n    }\n}\nfunction assertTIcu(tIcu) {\n    assertDefined(tIcu, 'Expected TIcu to be defined');\n    if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {\n        throwError('Object is not of TIcu type.');\n    }\n}\nfunction assertComponentType(actual, msg = \"Type passed in is not ComponentType, it does not have 'ɵcmp' property.\") {\n    if (!getComponentDef(actual)) {\n        throwError(msg);\n    }\n}\nfunction assertNgModuleType(actual, msg = \"Type passed in is not NgModuleType, it does not have 'ɵmod' property.\") {\n    if (!getNgModuleDef(actual)) {\n        throwError(msg);\n    }\n}\nfunction assertHasParent(tNode) {\n    assertDefined(tNode, 'currentTNode should exist!');\n    assertDefined(tNode.parent, 'currentTNode should have a parent');\n}\nfunction assertLContainer(value) {\n    assertDefined(value, 'LContainer must be defined');\n    assertEqual(isLContainer(value), true, 'Expecting LContainer');\n}\nfunction assertLViewOrUndefined(value) {\n    value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');\n}\nfunction assertLView(value) {\n    assertDefined(value, 'LView must be defined');\n    assertEqual(isLView(value), true, 'Expecting LView');\n}\nfunction assertFirstCreatePass(tView, errMessage) {\n    assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');\n}\nfunction assertFirstUpdatePass(tView, errMessage) {\n    assertEqual(tView.firstUpdatePass, true, 'Should only be called in first update pass.');\n}\n/**\n * This is a basic sanity check that an object is probably a directive def. DirectiveDef is\n * an interface, so we can't do a direct instanceof check.\n */\nfunction assertDirectiveDef(obj) {\n    if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {\n        throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);\n    }\n}\nfunction assertIndexInDeclRange(tView, index) {\n    assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);\n}\nfunction assertIndexInExpandoRange(lView, index) {\n    const tView = lView[1];\n    assertBetween(tView.expandoStartIndex, lView.length, index);\n}\nfunction assertBetween(lower, upper, index) {\n    if (!(lower <= index && index < upper)) {\n        throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);\n    }\n}\nfunction assertProjectionSlots(lView, errMessage) {\n    assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');\n    assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, 'Components with projection nodes (<ng-content>) must have projection slots defined.');\n}\nfunction assertParentView(lView, errMessage) {\n    assertDefined(lView, \"Component views should always have a parent view (component's host view)\");\n}\n/**\n * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a\n * NodeInjector data structure.\n *\n * @param lView `LView` which should be checked.\n * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.\n */\nfunction assertNodeInjector(lView, injectorIndex) {\n    assertIndexInExpandoRange(lView, injectorIndex);\n    assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);\n    assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');\n    assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');\n}\n\nconst SVG_NAMESPACE = 'svg';\nconst MATH_ML_NAMESPACE = 'math';\n\n/**\n * For efficiency reasons we often put several different data types (`RNode`, `LView`, `LContainer`)\n * in same location in `LView`. This is because we don't want to pre-allocate space for it\n * because the storage is sparse. This file contains utilities for dealing with such data types.\n *\n * How do we know what is stored at a given location in `LView`.\n * - `Array.isArray(value) === false` => `RNode` (The normal storage value)\n * - `Array.isArray(value) === true` => then the `value[0]` represents the wrapped value.\n *   - `typeof value[TYPE] === 'object'` => `LView`\n *      - This happens when we have a component at a given location\n *   - `typeof value[TYPE] === true` => `LContainer`\n *      - This happens when we have `LContainer` binding at a given location.\n *\n *\n * NOTE: it is assumed that `Array.isArray` and `typeof` operations are very efficient.\n */\n/**\n * Returns `RNode`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapRNode(value) {\n    while (Array.isArray(value)) {\n        value = value[HOST];\n    }\n    return value;\n}\n/**\n * Returns `LView` or `null` if not found.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapLView(value) {\n    while (Array.isArray(value)) {\n        // This check is same as `isLView()` but we don't call at as we don't want to call\n        // `Array.isArray()` twice and give JITer more work for inlining.\n        if (typeof value[TYPE] === 'object')\n            return value;\n        value = value[HOST];\n    }\n    return null;\n}\n/**\n * Retrieves an element value from the provided `viewData`, by unwrapping\n * from any containers, component views, or style contexts.\n */\nfunction getNativeByIndex(index, lView) {\n    ngDevMode && assertIndexInRange(lView, index);\n    ngDevMode && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Expected to be past HEADER_OFFSET');\n    return unwrapRNode(lView[index]);\n}\n/**\n * Retrieve an `RNode` for a given `TNode` and `LView`.\n *\n * This function guarantees in dev mode to retrieve a non-null `RNode`.\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNode(tNode, lView) {\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    ngDevMode && assertIndexInRange(lView, tNode.index);\n    const node = unwrapRNode(lView[tNode.index]);\n    return node;\n}\n/**\n * Retrieve an `RNode` or `null` for a given `TNode` and `LView`.\n *\n * Some `TNode`s don't have associated `RNode`s. For example `Projection`\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNodeOrNull(tNode, lView) {\n    const index = tNode === null ? -1 : tNode.index;\n    if (index !== -1) {\n        ngDevMode && assertTNodeForLView(tNode, lView);\n        const node = unwrapRNode(lView[index]);\n        return node;\n    }\n    return null;\n}\n// fixme(misko): The return Type should be `TNode|null`\nfunction getTNode(tView, index) {\n    ngDevMode && assertGreaterThan(index, -1, 'wrong index for TNode');\n    ngDevMode && assertLessThan(index, tView.data.length, 'wrong index for TNode');\n    const tNode = tView.data[index];\n    ngDevMode && tNode !== null && assertTNode(tNode);\n    return tNode;\n}\n/** Retrieves a value from any `LView` or `TData`. */\nfunction load(view, index) {\n    ngDevMode && assertIndexInRange(view, index);\n    return view[index];\n}\n/** Store a value in the `data` at a given `index`. */\nfunction store(tView, lView, index, value) {\n    // We don't store any static data for local variables, so the first time\n    // we see the template, we should store as null to avoid a sparse array\n    if (index >= tView.data.length) {\n        tView.data[index] = null;\n        tView.blueprint[index] = null;\n    }\n    lView[index] = value;\n}\nfunction getComponentLViewByIndex(nodeIndex, hostView) {\n    // Could be an LView or an LContainer. If LContainer, unwrap to find LView.\n    ngDevMode && assertIndexInRange(hostView, nodeIndex);\n    const slotValue = hostView[nodeIndex];\n    const lView = isLView(slotValue) ? slotValue : slotValue[HOST];\n    return lView;\n}\n/** Checks whether a given view is in creation mode */\nfunction isCreationMode(view) {\n    return (view[FLAGS] & 4 /* LViewFlags.CreationMode */) === 4 /* LViewFlags.CreationMode */;\n}\n/**\n * Returns a boolean for whether the view is attached to the change detection tree.\n *\n * Note: This determines whether a view should be checked, not whether it's inserted\n * into a container. For that, you'll want `viewAttachedToContainer` below.\n */\nfunction viewAttachedToChangeDetector(view) {\n    return (view[FLAGS] & 128 /* LViewFlags.Attached */) === 128 /* LViewFlags.Attached */;\n}\n/** Returns a boolean for whether the view is attached to a container. */\nfunction viewAttachedToContainer(view) {\n    return isLContainer(view[PARENT]);\n}\nfunction getConstant(consts, index) {\n    if (index === null || index === undefined)\n        return null;\n    ngDevMode && assertIndexInRange(consts, index);\n    return consts[index];\n}\n/**\n * Resets the pre-order hook flags of the view.\n * @param lView the LView on which the flags are reset\n */\nfunction resetPreOrderHookFlags(lView) {\n    lView[PREORDER_HOOK_FLAGS] = 0;\n}\n/**\n * Adds the `RefreshView` flag from the lView and updates HAS_CHILD_VIEWS_TO_REFRESH flag of\n * parents.\n */\nfunction markViewForRefresh(lView) {\n    if (lView[FLAGS] & 1024 /* LViewFlags.RefreshView */) {\n        return;\n    }\n    lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n    if (viewAttachedToChangeDetector(lView)) {\n        markAncestorsForTraversal(lView);\n    }\n}\n/**\n * Walks up the LView hierarchy.\n * @param nestingLevel Number of times to walk up in hierarchy.\n * @param currentView View from which to start the lookup.\n */\nfunction walkUpViews(nestingLevel, currentView) {\n    while (nestingLevel > 0) {\n        ngDevMode &&\n            assertDefined(currentView[DECLARATION_VIEW], 'Declaration view should be defined if nesting level is greater than 0.');\n        currentView = currentView[DECLARATION_VIEW];\n        nestingLevel--;\n    }\n    return currentView;\n}\nfunction requiresRefreshOrTraversal(lView) {\n    return !!(lView[FLAGS] & (1024 /* LViewFlags.RefreshView */ | 8192 /* LViewFlags.HasChildViewsToRefresh */) ||\n        lView[REACTIVE_TEMPLATE_CONSUMER]?.dirty);\n}\n/**\n * Updates the `HasChildViewsToRefresh` flag on the parents of the `LView` as well as the\n * parents above.\n */\nfunction updateAncestorTraversalFlagsOnAttach(lView) {\n    lView[ENVIRONMENT].changeDetectionScheduler?.notify(8 /* NotificationSource.ViewAttached */);\n    if (lView[FLAGS] & 64 /* LViewFlags.Dirty */) {\n        lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n    }\n    if (requiresRefreshOrTraversal(lView)) {\n        markAncestorsForTraversal(lView);\n    }\n}\n/**\n * Ensures views above the given `lView` are traversed during change detection even when they are\n * not dirty.\n *\n * This is done by setting the `HAS_CHILD_VIEWS_TO_REFRESH` flag up to the root, stopping when the\n * flag is already `true` or the `lView` is detached.\n */\nfunction markAncestorsForTraversal(lView) {\n    lView[ENVIRONMENT].changeDetectionScheduler?.notify(0 /* NotificationSource.MarkAncestorsForTraversal */);\n    let parent = getLViewParent(lView);\n    while (parent !== null) {\n        // We stop adding markers to the ancestors once we reach one that already has the marker. This\n        // is to avoid needlessly traversing all the way to the root when the marker already exists.\n        if (parent[FLAGS] & 8192 /* LViewFlags.HasChildViewsToRefresh */) {\n            break;\n        }\n        parent[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n        if (!viewAttachedToChangeDetector(parent)) {\n            break;\n        }\n        parent = getLViewParent(parent);\n    }\n}\n/**\n * Stores a LView-specific destroy callback.\n */\nfunction storeLViewOnDestroy(lView, onDestroyCallback) {\n    if (isDestroyed(lView)) {\n        throw new RuntimeError(911 /* RuntimeErrorCode.VIEW_ALREADY_DESTROYED */, ngDevMode && 'View has already been destroyed.');\n    }\n    if (lView[ON_DESTROY_HOOKS] === null) {\n        lView[ON_DESTROY_HOOKS] = [];\n    }\n    lView[ON_DESTROY_HOOKS].push(onDestroyCallback);\n}\n/**\n * Removes previously registered LView-specific destroy callback.\n */\nfunction removeLViewOnDestroy(lView, onDestroyCallback) {\n    if (lView[ON_DESTROY_HOOKS] === null)\n        return;\n    const destroyCBIdx = lView[ON_DESTROY_HOOKS].indexOf(onDestroyCallback);\n    if (destroyCBIdx !== -1) {\n        lView[ON_DESTROY_HOOKS].splice(destroyCBIdx, 1);\n    }\n}\n/**\n * Gets the parent LView of the passed LView, if the PARENT is an LContainer, will get the parent of\n * that LContainer, which is an LView\n * @param lView the lView whose parent to get\n */\nfunction getLViewParent(lView) {\n    ngDevMode && assertLView(lView);\n    const parent = lView[PARENT];\n    return isLContainer(parent) ? parent[PARENT] : parent;\n}\nfunction getOrCreateLViewCleanup(view) {\n    // top level variables should not be exported for performance reasons (PERF_NOTES.md)\n    return (view[CLEANUP] ??= []);\n}\nfunction getOrCreateTViewCleanup(tView) {\n    return (tView.cleanup ??= []);\n}\n/**\n * Saves context for this cleanup function in LView.cleanupInstances.\n *\n * On the first template pass, saves in TView:\n * - Cleanup function\n * - Index of context we just saved in LView.cleanupInstances\n */\nfunction storeCleanupWithContext(tView, lView, context, cleanupFn) {\n    const lCleanup = getOrCreateLViewCleanup(lView);\n    // Historically the `storeCleanupWithContext` was used to register both framework-level and\n    // user-defined cleanup callbacks, but over time those two types of cleanups were separated.\n    // This dev mode checks assures that user-level cleanup callbacks are _not_ stored in data\n    // structures reserved for framework-specific hooks.\n    ngDevMode &&\n        assertDefined(context, 'Cleanup context is mandatory when registering framework-level destroy hooks');\n    lCleanup.push(context);\n    if (tView.firstCreatePass) {\n        getOrCreateTViewCleanup(tView).push(cleanupFn, lCleanup.length - 1);\n    }\n    else {\n        // Make sure that no new framework-level cleanup functions are registered after the first\n        // template pass is done (and TView data structures are meant to fully constructed).\n        if (ngDevMode) {\n            Object.freeze(getOrCreateTViewCleanup(tView));\n        }\n    }\n}\n\nconst instructionState = {\n    lFrame: createLFrame(null),\n    bindingsEnabled: true,\n    skipHydrationRootTNode: null,\n};\nvar CheckNoChangesMode;\n(function (CheckNoChangesMode) {\n    CheckNoChangesMode[CheckNoChangesMode[\"Off\"] = 0] = \"Off\";\n    CheckNoChangesMode[CheckNoChangesMode[\"Exhaustive\"] = 1] = \"Exhaustive\";\n    CheckNoChangesMode[CheckNoChangesMode[\"OnlyDirtyViews\"] = 2] = \"OnlyDirtyViews\";\n})(CheckNoChangesMode || (CheckNoChangesMode = {}));\n/**\n * In this mode, any changes in bindings will throw an ExpressionChangedAfterChecked error.\n *\n * Necessary to support ChangeDetectorRef.checkNoChanges().\n *\n * The `checkNoChanges` function is invoked only in ngDevMode=true and verifies that no unintended\n * changes exist in the change detector or its children.\n */\nlet _checkNoChangesMode = 0; /* CheckNoChangesMode.Off */\n/**\n * Flag used to indicate that we are in the middle running change detection on a view\n *\n * @see detectChangesInViewWhileDirty\n */\nlet _isRefreshingViews = false;\nfunction getElementDepthCount() {\n    return instructionState.lFrame.elementDepthCount;\n}\nfunction increaseElementDepthCount() {\n    instructionState.lFrame.elementDepthCount++;\n}\nfunction decreaseElementDepthCount() {\n    instructionState.lFrame.elementDepthCount--;\n}\nfunction getBindingsEnabled() {\n    return instructionState.bindingsEnabled;\n}\n/**\n * Returns true if currently inside a skip hydration block.\n * @returns boolean\n */\nfunction isInSkipHydrationBlock() {\n    return instructionState.skipHydrationRootTNode !== null;\n}\n/**\n * Returns true if this is the root TNode of the skip hydration block.\n * @param tNode the current TNode\n * @returns boolean\n */\nfunction isSkipHydrationRootTNode(tNode) {\n    return instructionState.skipHydrationRootTNode === tNode;\n}\n/**\n * Enables directive matching on elements.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵenableBindings() {\n    instructionState.bindingsEnabled = true;\n}\n/**\n * Sets a flag to specify that the TNode is in a skip hydration block.\n * @param tNode the current TNode\n */\nfunction enterSkipHydrationBlock(tNode) {\n    instructionState.skipHydrationRootTNode = tNode;\n}\n/**\n * Disables directive matching on element.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵdisableBindings() {\n    instructionState.bindingsEnabled = false;\n}\n/**\n * Clears the root skip hydration node when leaving a skip hydration block.\n */\nfunction leaveSkipHydrationBlock() {\n    instructionState.skipHydrationRootTNode = null;\n}\n/**\n * Return the current `LView`.\n */\nfunction getLView() {\n    return instructionState.lFrame.lView;\n}\n/**\n * Return the current `TView`.\n */\nfunction getTView() {\n    return instructionState.lFrame.tView;\n}\n/**\n * Restores `contextViewData` to the given OpaqueViewState instance.\n *\n * Used in conjunction with the getCurrentView() instruction to save a snapshot\n * of the current view and restore it when listeners are invoked. This allows\n * walking the declaration view tree in listeners to get vars from parent views.\n *\n * @param viewToRestore The OpaqueViewState instance to restore.\n * @returns Context of the restored OpaqueViewState instance.\n *\n * @codeGenApi\n */\nfunction ɵɵrestoreView(viewToRestore) {\n    instructionState.lFrame.contextLView = viewToRestore;\n    return viewToRestore[CONTEXT];\n}\n/**\n * Clears the view set in `ɵɵrestoreView` from memory. Returns the passed in\n * value so that it can be used as a return value of an instruction.\n *\n * @codeGenApi\n */\nfunction ɵɵresetView(value) {\n    instructionState.lFrame.contextLView = null;\n    return value;\n}\nfunction getCurrentTNode() {\n    let currentTNode = getCurrentTNodePlaceholderOk();\n    while (currentTNode !== null && currentTNode.type === 64 /* TNodeType.Placeholder */) {\n        currentTNode = currentTNode.parent;\n    }\n    return currentTNode;\n}\nfunction getCurrentTNodePlaceholderOk() {\n    return instructionState.lFrame.currentTNode;\n}\nfunction getCurrentParentTNode() {\n    const lFrame = instructionState.lFrame;\n    const currentTNode = lFrame.currentTNode;\n    return lFrame.isParent ? currentTNode : currentTNode.parent;\n}\nfunction setCurrentTNode(tNode, isParent) {\n    ngDevMode && tNode && assertTNodeForTView(tNode, instructionState.lFrame.tView);\n    const lFrame = instructionState.lFrame;\n    lFrame.currentTNode = tNode;\n    lFrame.isParent = isParent;\n}\nfunction isCurrentTNodeParent() {\n    return instructionState.lFrame.isParent;\n}\nfunction setCurrentTNodeAsNotParent() {\n    instructionState.lFrame.isParent = false;\n}\nfunction getContextLView() {\n    const contextLView = instructionState.lFrame.contextLView;\n    ngDevMode && assertDefined(contextLView, 'contextLView must be defined.');\n    return contextLView;\n}\nfunction isInCheckNoChangesMode() {\n    !ngDevMode && throwError('Must never be called in production mode');\n    return _checkNoChangesMode !== CheckNoChangesMode.Off;\n}\nfunction isExhaustiveCheckNoChanges() {\n    !ngDevMode && throwError('Must never be called in production mode');\n    return _checkNoChangesMode === CheckNoChangesMode.Exhaustive;\n}\nfunction setIsInCheckNoChangesMode(mode) {\n    !ngDevMode && throwError('Must never be called in production mode');\n    _checkNoChangesMode = mode;\n}\nfunction isRefreshingViews() {\n    return _isRefreshingViews;\n}\nfunction setIsRefreshingViews(mode) {\n    const prev = _isRefreshingViews;\n    _isRefreshingViews = mode;\n    return prev;\n}\n// top level variables should not be exported for performance reasons (PERF_NOTES.md)\nfunction getBindingRoot() {\n    const lFrame = instructionState.lFrame;\n    let index = lFrame.bindingRootIndex;\n    if (index === -1) {\n        index = lFrame.bindingRootIndex = lFrame.tView.bindingStartIndex;\n    }\n    return index;\n}\nfunction getBindingIndex() {\n    return instructionState.lFrame.bindingIndex;\n}\nfunction setBindingIndex(value) {\n    return (instructionState.lFrame.bindingIndex = value);\n}\nfunction nextBindingIndex() {\n    return instructionState.lFrame.bindingIndex++;\n}\nfunction incrementBindingIndex(count) {\n    const lFrame = instructionState.lFrame;\n    const index = lFrame.bindingIndex;\n    lFrame.bindingIndex = lFrame.bindingIndex + count;\n    return index;\n}\nfunction isInI18nBlock() {\n    return instructionState.lFrame.inI18n;\n}\nfunction setInI18nBlock(isInI18nBlock) {\n    instructionState.lFrame.inI18n = isInI18nBlock;\n}\n/**\n * Set a new binding root index so that host template functions can execute.\n *\n * Bindings inside the host template are 0 index. But because we don't know ahead of time\n * how many host bindings we have we can't pre-compute them. For this reason they are all\n * 0 index and we just shift the root so that they match next available location in the LView.\n *\n * @param bindingRootIndex Root index for `hostBindings`\n * @param currentDirectiveIndex `TData[currentDirectiveIndex]` will point to the current directive\n *        whose `hostBindings` are being processed.\n */\nfunction setBindingRootForHostBindings(bindingRootIndex, currentDirectiveIndex) {\n    const lFrame = instructionState.lFrame;\n    lFrame.bindingIndex = lFrame.bindingRootIndex = bindingRootIndex;\n    setCurrentDirectiveIndex(currentDirectiveIndex);\n}\n/**\n * When host binding is executing this points to the directive index.\n * `TView.data[getCurrentDirectiveIndex()]` is `DirectiveDef`\n * `LView[getCurrentDirectiveIndex()]` is directive instance.\n */\nfunction getCurrentDirectiveIndex() {\n    return instructionState.lFrame.currentDirectiveIndex;\n}\n/**\n * Sets an index of a directive whose `hostBindings` are being processed.\n *\n * @param currentDirectiveIndex `TData` index where current directive instance can be found.\n */\nfunction setCurrentDirectiveIndex(currentDirectiveIndex) {\n    instructionState.lFrame.currentDirectiveIndex = currentDirectiveIndex;\n}\n/**\n * Retrieve the current `DirectiveDef` which is active when `hostBindings` instruction is being\n * executed.\n *\n * @param tData Current `TData` where the `DirectiveDef` will be looked up at.\n */\nfunction getCurrentDirectiveDef(tData) {\n    const currentDirectiveIndex = instructionState.lFrame.currentDirectiveIndex;\n    return currentDirectiveIndex === -1 ? null : tData[currentDirectiveIndex];\n}\nfunction getCurrentQueryIndex() {\n    return instructionState.lFrame.currentQueryIndex;\n}\nfunction setCurrentQueryIndex(value) {\n    instructionState.lFrame.currentQueryIndex = value;\n}\n/**\n * Returns a `TNode` of the location where the current `LView` is declared at.\n *\n * @param lView an `LView` that we want to find parent `TNode` for.\n */\nfunction getDeclarationTNode(lView) {\n    const tView = lView[TVIEW];\n    // Return the declaration parent for embedded views\n    if (tView.type === 2 /* TViewType.Embedded */) {\n        ngDevMode && assertDefined(tView.declTNode, 'Embedded TNodes should have declaration parents.');\n        return tView.declTNode;\n    }\n    // Components don't have `TView.declTNode` because each instance of component could be\n    // inserted in different location, hence `TView.declTNode` is meaningless.\n    // Falling back to `T_HOST` in case we cross component boundary.\n    if (tView.type === 1 /* TViewType.Component */) {\n        return lView[T_HOST];\n    }\n    // Remaining TNode type is `TViewType.Root` which doesn't have a parent TNode.\n    return null;\n}\n/**\n * This is a light weight version of the `enterView` which is needed by the DI system.\n *\n * @param lView `LView` location of the DI context.\n * @param tNode `TNode` for DI context\n * @param flags DI context flags. if `SkipSelf` flag is set than we walk up the declaration\n *     tree from `tNode`  until we find parent declared `TElementNode`.\n * @returns `true` if we have successfully entered DI associated with `tNode` (or with declared\n *     `TNode` if `flags` has  `SkipSelf`). Failing to enter DI implies that no associated\n *     `NodeInjector` can be found and we should instead use `ModuleInjector`.\n *     - If `true` than this call must be fallowed by `leaveDI`\n *     - If `false` than this call failed and we should NOT call `leaveDI`\n */\nfunction enterDI(lView, tNode, flags) {\n    ngDevMode && assertLViewOrUndefined(lView);\n    if (flags & 4 /* InternalInjectFlags.SkipSelf */) {\n        ngDevMode && assertTNodeForTView(tNode, lView[TVIEW]);\n        let parentTNode = tNode;\n        let parentLView = lView;\n        while (true) {\n            ngDevMode && assertDefined(parentTNode, 'Parent TNode should be defined');\n            parentTNode = parentTNode.parent;\n            if (parentTNode === null && !(flags & 1 /* InternalInjectFlags.Host */)) {\n                parentTNode = getDeclarationTNode(parentLView);\n                if (parentTNode === null)\n                    break;\n                // In this case, a parent exists and is definitely an element. So it will definitely\n                // have an existing lView as the declaration view, which is why we can assume it's defined.\n                ngDevMode && assertDefined(parentLView, 'Parent LView should be defined');\n                parentLView = parentLView[DECLARATION_VIEW];\n                // In Ivy there are Comment nodes that correspond to ngIf and NgFor embedded directives\n                // We want to skip those and look only at Elements and ElementContainers to ensure\n                // we're looking at true parent nodes, and not content or other types.\n                if (parentTNode.type & (2 /* TNodeType.Element */ | 8 /* TNodeType.ElementContainer */)) {\n                    break;\n                }\n            }\n            else {\n                break;\n            }\n        }\n        if (parentTNode === null) {\n            // If we failed to find a parent TNode this means that we should use module injector.\n            return false;\n        }\n        else {\n            tNode = parentTNode;\n            lView = parentLView;\n        }\n    }\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    const lFrame = (instructionState.lFrame = allocLFrame());\n    lFrame.currentTNode = tNode;\n    lFrame.lView = lView;\n    return true;\n}\n/**\n * Swap the current lView with a new lView.\n *\n * For performance reasons we store the lView in the top level of the module.\n * This way we minimize the number of properties to read. Whenever a new view\n * is entered we have to store the lView for later, and when the view is\n * exited the state has to be restored\n *\n * @param newView New lView to become active\n * @returns the previously active lView;\n */\nfunction enterView(newView) {\n    ngDevMode && assertNotEqual(newView[0], newView[1], '????');\n    ngDevMode && assertLViewOrUndefined(newView);\n    const newLFrame = allocLFrame();\n    if (ngDevMode) {\n        assertEqual(newLFrame.isParent, true, 'Expected clean LFrame');\n        assertEqual(newLFrame.lView, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.tView, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.selectedIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.elementDepthCount, 0, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentDirectiveIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentNamespace, null, 'Expected clean LFrame');\n        assertEqual(newLFrame.bindingRootIndex, -1, 'Expected clean LFrame');\n        assertEqual(newLFrame.currentQueryIndex, 0, 'Expected clean LFrame');\n    }\n    const tView = newView[TVIEW];\n    instructionState.lFrame = newLFrame;\n    ngDevMode && tView.firstChild && assertTNodeForTView(tView.firstChild, tView);\n    newLFrame.currentTNode = tView.firstChild;\n    newLFrame.lView = newView;\n    newLFrame.tView = tView;\n    newLFrame.contextLView = newView;\n    newLFrame.bindingIndex = tView.bindingStartIndex;\n    newLFrame.inI18n = false;\n}\n/**\n * Allocates next free LFrame. This function tries to reuse the `LFrame`s to lower memory pressure.\n */\nfunction allocLFrame() {\n    const currentLFrame = instructionState.lFrame;\n    const childLFrame = currentLFrame === null ? null : currentLFrame.child;\n    const newLFrame = childLFrame === null ? createLFrame(currentLFrame) : childLFrame;\n    return newLFrame;\n}\nfunction createLFrame(parent) {\n    const lFrame = {\n        currentTNode: null,\n        isParent: true,\n        lView: null,\n        tView: null,\n        selectedIndex: -1,\n        contextLView: null,\n        elementDepthCount: 0,\n        currentNamespace: null,\n        currentDirectiveIndex: -1,\n        bindingRootIndex: -1,\n        bindingIndex: -1,\n        currentQueryIndex: 0,\n        parent: parent,\n        child: null,\n        inI18n: false,\n    };\n    parent !== null && (parent.child = lFrame); // link the new LFrame for reuse.\n    return lFrame;\n}\n/**\n * A lightweight version of leave which is used with DI.\n *\n * This function only resets `currentTNode` and `LView` as those are the only properties\n * used with DI (`enterDI()`).\n *\n * NOTE: This function is reexported as `leaveDI`. However `leaveDI` has return type of `void` where\n * as `leaveViewLight` has `LFrame`. This is so that `leaveViewLight` can be used in `leaveView`.\n */\nfunction leaveViewLight() {\n    const oldLFrame = instructionState.lFrame;\n    instructionState.lFrame = oldLFrame.parent;\n    oldLFrame.currentTNode = null;\n    oldLFrame.lView = null;\n    return oldLFrame;\n}\n/**\n * This is a lightweight version of the `leaveView` which is needed by the DI system.\n *\n * NOTE: this function is an alias so that we can change the type of the function to have `void`\n * return type.\n */\nconst leaveDI = leaveViewLight;\n/**\n * Leave the current `LView`\n *\n * This pops the `LFrame` with the associated `LView` from the stack.\n *\n * IMPORTANT: We must zero out the `LFrame` values here otherwise they will be retained. This is\n * because for performance reasons we don't release `LFrame` but rather keep it for next use.\n */\nfunction leaveView() {\n    const oldLFrame = leaveViewLight();\n    oldLFrame.isParent = true;\n    oldLFrame.tView = null;\n    oldLFrame.selectedIndex = -1;\n    oldLFrame.contextLView = null;\n    oldLFrame.elementDepthCount = 0;\n    oldLFrame.currentDirectiveIndex = -1;\n    oldLFrame.currentNamespace = null;\n    oldLFrame.bindingRootIndex = -1;\n    oldLFrame.bindingIndex = -1;\n    oldLFrame.currentQueryIndex = 0;\n}\nfunction nextContextImpl(level) {\n    const contextLView = (instructionState.lFrame.contextLView = walkUpViews(level, instructionState.lFrame.contextLView));\n    return contextLView[CONTEXT];\n}\n/**\n * Gets the currently selected element index.\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n */\nfunction getSelectedIndex() {\n    return instructionState.lFrame.selectedIndex;\n}\n/**\n * Sets the most recent index passed to {@link select}\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n *\n * (Note that if an \"exit function\" was set earlier (via `setElementExitFn()`) then that will be\n * run if and when the provided `index` value is different from the current selected index value.)\n */\nfunction setSelectedIndex(index) {\n    ngDevMode &&\n        index !== -1 &&\n        assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Index must be past HEADER_OFFSET (or -1).');\n    ngDevMode &&\n        assertLessThan(index, instructionState.lFrame.lView.length, \"Can't set index passed end of LView\");\n    instructionState.lFrame.selectedIndex = index;\n}\n/**\n * Gets the `tNode` that represents currently selected element.\n */\nfunction getSelectedTNode() {\n    const lFrame = instructionState.lFrame;\n    return getTNode(lFrame.tView, lFrame.selectedIndex);\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/2000/svg'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceSVG() {\n    instructionState.lFrame.currentNamespace = SVG_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/1998/MathML/'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceMathML() {\n    instructionState.lFrame.currentNamespace = MATH_ML_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceHTML() {\n    namespaceHTMLInternal();\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n */\nfunction namespaceHTMLInternal() {\n    instructionState.lFrame.currentNamespace = null;\n}\nfunction getNamespace() {\n    return instructionState.lFrame.currentNamespace;\n}\nlet _wasLastNodeCreated = true;\n/**\n * Retrieves a global flag that indicates whether the most recent DOM node\n * was created or hydrated.\n */\nfunction wasLastNodeCreated() {\n    return _wasLastNodeCreated;\n}\n/**\n * Sets a global flag to indicate whether the most recent DOM node\n * was created or hydrated.\n */\nfunction lastNodeWasCreated(flag) {\n    _wasLastNodeCreated = flag;\n}\n\n/**\n * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.\n */\nfunction createInjector(defType, parent = null, additionalProviders = null, name) {\n    const injector = createInjectorWithoutInjectorInstances(defType, parent, additionalProviders, name);\n    injector.resolveInjectorInitializers();\n    return injector;\n}\n/**\n * Creates a new injector without eagerly resolving its injector types. Can be used in places\n * where resolving the injector types immediately can lead to an infinite loop. The injector types\n * should be resolved at a later point by calling `_resolveInjectorDefTypes`.\n */\nfunction createInjectorWithoutInjectorInstances(defType, parent = null, additionalProviders = null, name, scopes = new Set()) {\n    const providers = [additionalProviders || EMPTY_ARRAY, importProvidersFrom(defType)];\n    name = name || (typeof defType === 'object' ? undefined : stringify(defType));\n    return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);\n}\n\n/**\n * Concrete injectors implement this interface. Injectors are configured\n * with [providers](guide/di/dependency-injection-providers) that associate\n * dependencies of various types with [injection tokens](guide/di/dependency-injection-providers).\n *\n * @see [DI Providers](guide/di/dependency-injection-providers).\n * @see {@link StaticProvider}\n *\n * @usageNotes\n *\n *  The following example creates a service injector instance.\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Usage example\n *\n * {@example core/di/ts/injector_spec.ts region='Injector'}\n *\n * `Injector` returns itself when given `Injector` as a token:\n *\n * {@example core/di/ts/injector_spec.ts region='injectInjector'}\n *\n * @publicApi\n */\nclass Injector {\n    static THROW_IF_NOT_FOUND = THROW_IF_NOT_FOUND;\n    static NULL = new NullInjector();\n    static create(options, parent) {\n        if (Array.isArray(options)) {\n            return createInjector({ name: '' }, parent, options, '');\n        }\n        else {\n            const name = options.name ?? '';\n            return createInjector({ name }, options.parent, options.providers, name);\n        }\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: Injector,\n        providedIn: 'any',\n        factory: () => ɵɵinject(INJECTOR$1),\n    });\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = -1 /* InjectorMarkers.Injector */;\n}\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [Domino](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = new InjectionToken(ngDevMode ? 'DocumentToken' : '');\n\n/**\n * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.\n * The scope of this destruction depends on where `DestroyRef` is injected. If `DestroyRef`\n * is injected in a component or directive, the callbacks run when that component or\n * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.\n *\n * @publicApi\n */\nclass DestroyRef {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectDestroyRef;\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ENV_ID__ = (injector) => injector;\n}\nclass NodeInjectorDestroyRef extends DestroyRef {\n    _lView;\n    constructor(_lView) {\n        super();\n        this._lView = _lView;\n    }\n    get destroyed() {\n        return isDestroyed(this._lView);\n    }\n    onDestroy(callback) {\n        const lView = this._lView;\n        storeLViewOnDestroy(lView, callback);\n        return () => removeLViewOnDestroy(lView, callback);\n    }\n}\nfunction injectDestroyRef() {\n    return new NodeInjectorDestroyRef(getLView());\n}\n\n/**\n * Provides a hook for centralized exception handling.\n *\n * The default implementation of `ErrorHandler` prints error messages to the `console`. To\n * intercept error handling, write a custom exception handler that replaces this default as\n * appropriate for your app.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * class MyErrorHandler implements ErrorHandler {\n *   handleError(error) {\n *     // do something with the exception\n *   }\n * }\n *\n * // Provide in standalone apps\n * bootstrapApplication(AppComponent, {\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n *\n * // Provide in module-based apps\n * @NgModule({\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n * class MyModule {}\n * ```\n *\n * @publicApi\n */\nclass ErrorHandler {\n    /**\n     * @internal\n     */\n    _console = console;\n    handleError(error) {\n        this._console.error('ERROR', error);\n    }\n}\n/**\n * `InjectionToken` used to configure how to call the `ErrorHandler`.\n */\nconst INTERNAL_APPLICATION_ERROR_HANDLER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'internal error handler' : '', {\n    providedIn: 'root',\n    factory: () => {\n        // The user's error handler may depend on things that create a circular dependency\n        // so we inject it lazily.\n        const injector = inject(EnvironmentInjector);\n        let userErrorHandler;\n        return (e) => {\n            userErrorHandler ??= injector.get(ErrorHandler);\n            userErrorHandler.handleError(e);\n        };\n    },\n});\nconst errorHandlerEnvironmentInitializer = {\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => void inject(ErrorHandler),\n    multi: true,\n};\nconst globalErrorListeners = new InjectionToken(ngDevMode ? 'GlobalErrorListeners' : '', {\n    providedIn: 'root',\n    factory: () => {\n        if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n            return;\n        }\n        const window = inject(DOCUMENT).defaultView;\n        if (!window) {\n            return;\n        }\n        const errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n        const rejectionListener = (e) => {\n            errorHandler(e.reason);\n            e.preventDefault();\n        };\n        const errorListener = (e) => {\n            errorHandler(e.error);\n            e.preventDefault();\n        };\n        const setupEventListeners = () => {\n            window.addEventListener('unhandledrejection', rejectionListener);\n            window.addEventListener('error', errorListener);\n        };\n        // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n        // the scope of this functionality.\n        if (typeof Zone !== 'undefined') {\n            Zone.root.run(setupEventListeners);\n        }\n        else {\n            setupEventListeners();\n        }\n        inject(DestroyRef).onDestroy(() => {\n            window.removeEventListener('error', errorListener);\n            window.removeEventListener('unhandledrejection', rejectionListener);\n        });\n    },\n});\n/**\n * Provides an environment initializer which forwards unhandled errors to the ErrorHandler.\n *\n * The listeners added are for the window's 'unhandledrejection' and 'error' events.\n *\n * @publicApi\n */\nfunction provideBrowserGlobalErrorListeners() {\n    return makeEnvironmentProviders([\n        provideEnvironmentInitializer(() => void inject(globalErrorListeners)),\n    ]);\n}\n\n/**\n * Checks if the given `value` is a reactive `Signal`.\n *\n * @publicApi 17.0\n */\nfunction isSignal(value) {\n    return typeof value === 'function' && value[SIGNAL] !== undefined;\n}\n\n/**\n * Utility function used during template type checking to extract the value from a `WritableSignal`.\n * @codeGenApi\n */\nfunction ɵunwrapWritableSignal(value) {\n    // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,\n    // because the latter incorrectly unwraps non-signal getter functions.\n    return null;\n}\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction signal(initialValue, options) {\n    const [get, set, update] = createSignal(initialValue, options?.equal);\n    const signalFn = get;\n    const node = signalFn[SIGNAL];\n    signalFn.set = set;\n    signalFn.update = update;\n    signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);\n    if (ngDevMode) {\n        signalFn.toString = () => `[Signal: ${signalFn()}]`;\n        node.debugName = options?.debugName;\n    }\n    return signalFn;\n}\nfunction signalAsReadonlyFn() {\n    const node = this[SIGNAL];\n    if (node.readonlyFn === undefined) {\n        const readonlyFn = () => this();\n        readonlyFn[SIGNAL] = node;\n        node.readonlyFn = readonlyFn;\n    }\n    return node.readonlyFn;\n}\n/**\n * Checks if the given `value` is a writeable signal.\n */\nfunction isWritableSignal(value) {\n    return isSignal(value) && typeof value.set === 'function';\n}\n\n/**\n * Injectable that is notified when an `LView` is made aware of changes to application state.\n */\nclass ChangeDetectionScheduler {\n}\n/** Token used to indicate if zoneless was enabled via provideZonelessChangeDetection(). */\nconst ZONELESS_ENABLED = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless enabled' : '', { providedIn: 'root', factory: () => false });\n/** Token used to indicate `provideZonelessChangeDetection` was used. */\nconst PROVIDED_ZONELESS = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless provided' : '', { providedIn: 'root', factory: () => false });\nconst ZONELESS_SCHEDULER_DISABLED = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'scheduler disabled' : '');\n// TODO(atscott): Remove in v19. Scheduler should be done with runOutsideAngular.\nconst SCHEDULE_IN_ROOT_ZONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'run changes outside zone in root' : '');\n\n/**\n * Asserts that the current stack frame is not within a reactive context. Useful\n * to disallow certain code from running inside a reactive context (see {@link /api/core/rxjs/toSignal toSignal})\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertNotInReactiveContext(debugFn, extraContext) {\n    // Taking a `Function` instead of a string name here prevents the un-minified name of the function\n    // from being retained in the bundle regardless of minification.\n    if (getActiveConsumer() !== null) {\n        throw new RuntimeError(-602 /* RuntimeErrorCode.ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT */, ngDevMode &&\n            `${debugFn.name}() cannot be called from within a reactive context.${extraContext ? ` ${extraContext}` : ''}`);\n    }\n}\n\nclass ViewContext {\n    view;\n    node;\n    constructor(view, node) {\n        this.view = view;\n        this.node = node;\n    }\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectViewContext;\n}\nfunction injectViewContext() {\n    return new ViewContext(getLView(), getCurrentTNode());\n}\n\n/**\n * Internal implementation of the pending tasks service.\n */\nclass PendingTasksInternal {\n    taskId = 0;\n    pendingTasks = new Set();\n    destroyed = false;\n    pendingTask = new BehaviorSubject(false);\n    get hasPendingTasks() {\n        // Accessing the value of a closed `BehaviorSubject` throws an error.\n        return this.destroyed ? false : this.pendingTask.value;\n    }\n    /**\n     * In case the service is about to be destroyed, return a self-completing observable.\n     * Otherwise, return the observable that emits the current state of pending tasks.\n     */\n    get hasPendingTasksObservable() {\n        if (this.destroyed) {\n            // Manually creating the observable pulls less symbols from RxJS than `of(false)`.\n            return new Observable((subscriber) => {\n                subscriber.next(false);\n                subscriber.complete();\n            });\n        }\n        return this.pendingTask;\n    }\n    add() {\n        // Emitting a value to a closed subject throws an error.\n        if (!this.hasPendingTasks && !this.destroyed) {\n            this.pendingTask.next(true);\n        }\n        const taskId = this.taskId++;\n        this.pendingTasks.add(taskId);\n        return taskId;\n    }\n    has(taskId) {\n        return this.pendingTasks.has(taskId);\n    }\n    remove(taskId) {\n        this.pendingTasks.delete(taskId);\n        if (this.pendingTasks.size === 0 && this.hasPendingTasks) {\n            this.pendingTask.next(false);\n        }\n    }\n    ngOnDestroy() {\n        this.pendingTasks.clear();\n        if (this.hasPendingTasks) {\n            this.pendingTask.next(false);\n        }\n        // We call `unsubscribe()` to release observers, as users may forget to\n        // unsubscribe manually when subscribing to `isStable`. We do not call\n        // `complete()` because it is unsafe; if someone subscribes using the `first`\n        // operator and the observable completes before emitting a value,\n        // RxJS will throw an error.\n        this.destroyed = true;\n        this.pendingTask.unsubscribe();\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: PendingTasksInternal,\n        providedIn: 'root',\n        factory: () => new PendingTasksInternal(),\n    });\n}\n/**\n * Service that keeps track of pending tasks contributing to the stableness of Angular\n * application. While several existing Angular services (ex.: `HttpClient`) will internally manage\n * tasks influencing stability, this API gives control over stability to library and application\n * developers for specific cases not covered by Angular internals.\n *\n * The concept of stability comes into play in several important scenarios:\n * - SSR process needs to wait for the application stability before serializing and sending rendered\n * HTML;\n * - tests might want to delay assertions until the application becomes stable;\n *\n * @usageNotes\n * ```ts\n * const pendingTasks = inject(PendingTasks);\n * const taskCleanup = pendingTasks.add();\n * // do work that should block application's stability and then:\n * taskCleanup();\n * ```\n *\n * @publicApi 20.0\n */\nclass PendingTasks {\n    internalPendingTasks = inject(PendingTasksInternal);\n    scheduler = inject(ChangeDetectionScheduler);\n    errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    /**\n     * Adds a new task that should block application's stability.\n     * @returns A cleanup function that removes a task when called.\n     */\n    add() {\n        const taskId = this.internalPendingTasks.add();\n        return () => {\n            if (!this.internalPendingTasks.has(taskId)) {\n                // This pending task has already been cleared.\n                return;\n            }\n            // Notifying the scheduler will hold application stability open until the next tick.\n            this.scheduler.notify(11 /* NotificationSource.PendingTaskRemoved */);\n            this.internalPendingTasks.remove(taskId);\n        };\n    }\n    /**\n     * Runs an asynchronous function and blocks the application's stability until the function completes.\n     *\n     * ```ts\n     * pendingTasks.run(async () => {\n     *   const userData = await fetch('/api/user');\n     *   this.userData.set(userData);\n     * });\n     * ```\n     *\n     * @param fn The asynchronous function to execute\n     * @developerPreview 19.0\n     */\n    run(fn) {\n        const removeTask = this.add();\n        fn().catch(this.errorHandler).finally(removeTask);\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: PendingTasks,\n        providedIn: 'root',\n        factory: () => new PendingTasks(),\n    });\n}\n\nfunction noop(...args) {\n    // Do nothing.\n}\n\n/**\n * A scheduler which manages the execution of effects.\n */\nclass EffectScheduler {\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n        token: EffectScheduler,\n        providedIn: 'root',\n        factory: () => new ZoneAwareEffectScheduler(),\n    });\n}\n/**\n * A wrapper around `ZoneAwareQueueingScheduler` that schedules flushing via the microtask queue\n * when.\n */\nclass ZoneAwareEffectScheduler {\n    dirtyEffectCount = 0;\n    queues = new Map();\n    add(handle) {\n        this.enqueue(handle);\n        this.schedule(handle);\n    }\n    schedule(handle) {\n        if (!handle.dirty) {\n            return;\n        }\n        this.dirtyEffectCount++;\n    }\n    remove(handle) {\n        const zone = handle.zone;\n        const queue = this.queues.get(zone);\n        if (!queue.has(handle)) {\n            return;\n        }\n        queue.delete(handle);\n        if (handle.dirty) {\n            this.dirtyEffectCount--;\n        }\n    }\n    enqueue(handle) {\n        const zone = handle.zone;\n        if (!this.queues.has(zone)) {\n            this.queues.set(zone, new Set());\n        }\n        const queue = this.queues.get(zone);\n        if (queue.has(handle)) {\n            return;\n        }\n        queue.add(handle);\n    }\n    /**\n     * Run all scheduled effects.\n     *\n     * Execution order of effects within the same zone is guaranteed to be FIFO, but there is no\n     * ordering guarantee between effects scheduled in different zones.\n     */\n    flush() {\n        while (this.dirtyEffectCount > 0) {\n            let ranOneEffect = false;\n            for (const [zone, queue] of this.queues) {\n                // `zone` here must be defined.\n                if (zone === null) {\n                    ranOneEffect ||= this.flushQueue(queue);\n                }\n                else {\n                    ranOneEffect ||= zone.run(() => this.flushQueue(queue));\n                }\n            }\n            // Safeguard against infinite looping if somehow our dirty effect count gets out of sync with\n            // the dirty flag across all the effects.\n            if (!ranOneEffect) {\n                this.dirtyEffectCount = 0;\n            }\n        }\n    }\n    flushQueue(queue) {\n        let ranOneEffect = false;\n        for (const handle of queue) {\n            if (!handle.dirty) {\n                continue;\n            }\n            this.dirtyEffectCount--;\n            ranOneEffect = true;\n            // TODO: what happens if this throws an error?\n            handle.run();\n        }\n        return ranOneEffect;\n    }\n}\n\nexport { AFTER_RENDER_SEQUENCES_TO_ADD, CHILD_HEAD, CHILD_TAIL, CLEANUP, CONTAINER_HEADER_OFFSET, CONTEXT, ChangeDetectionScheduler, CheckNoChangesMode, DECLARATION_COMPONENT_VIEW, DECLARATION_LCONTAINER, DECLARATION_VIEW, DEHYDRATED_VIEWS, DOCUMENT, DestroyRef, EFFECTS, EFFECTS_TO_SCHEDULE, EMBEDDED_VIEW_INJECTOR, EMPTY_ARRAY, EMPTY_OBJ, ENVIRONMENT, ENVIRONMENT_INITIALIZER, EffectScheduler, EnvironmentInjector, ErrorHandler, FLAGS, HEADER_OFFSET, HOST, HYDRATION, ID, INJECTOR$1 as INJECTOR, INJECTOR as INJECTOR$1, INJECTOR_DEF_TYPES, INJECTOR_SCOPE, INTERNAL_APPLICATION_ERROR_HANDLER, InjectionToken, Injector, MATH_ML_NAMESPACE, MOVED_VIEWS, NATIVE, NEXT, NG_COMP_DEF, NG_DIR_DEF, NG_ELEMENT_ID, NG_FACTORY_DEF, NG_INJ_DEF, NG_MOD_DEF, NG_PIPE_DEF, NG_PROV_DEF, NodeInjectorDestroyRef, NullInjector, ON_DESTROY_HOOKS, PARENT, PREORDER_HOOK_FLAGS, PROVIDED_ZONELESS, PendingTasks, PendingTasksInternal, QUERIES, R3Injector, REACTIVE_TEMPLATE_CONSUMER, RENDERER, RuntimeError, SCHEDULE_IN_ROOT_ZONE, SVG_NAMESPACE, TVIEW, T_HOST, VIEW_REFS, ViewContext, XSS_SECURITY_URL, ZONELESS_ENABLED, ZONELESS_SCHEDULER_DISABLED, _global, addToArray, arrayEquals, arrayInsert2, arraySplice, assertComponentType, assertDefined, assertDirectiveDef, assertDomNode, assertElement, assertEqual, assertFirstCreatePass, assertFirstUpdatePass, assertFunction, assertGreaterThan, assertGreaterThanOrEqual, assertHasParent, assertInInjectionContext, assertIndexInDeclRange, assertIndexInExpandoRange, assertIndexInRange, assertInjectImplementationNotEqual, assertLContainer, assertLView, assertLessThan, assertNgModuleType, assertNodeInjector, assertNotDefined, assertNotEqual, assertNotInReactiveContext, assertNotReactive, assertNotSame, assertNumber, assertNumberInRange, assertOneOf, assertParentView, assertProjectionSlots, assertSame, assertString, assertTIcu, assertTNode, assertTNodeForLView, assertTNodeForTView, attachInjectFlag, concatStringsWithSpace, convertToBitFlags, createInjector, createInjectorWithoutInjectorInstances, debugStringifyTypeForError, decreaseElementDepthCount, deepForEach, defineInjectable, emitEffectCreatedEvent, emitInjectEvent, emitInjectorToCreateInstanceEvent, emitInstanceCreatedByInjectorEvent, emitProviderConfiguredEvent, enterDI, enterSkipHydrationBlock, enterView, errorHandlerEnvironmentInitializer, fillProperties, flatten, formatRuntimeError, forwardRef, getBindingIndex, getBindingRoot, getBindingsEnabled, getClosureSafeProperty, getComponentDef, getComponentLViewByIndex, getConstant, getContextLView, getCurrentDirectiveDef, getCurrentDirectiveIndex, getCurrentParentTNode, getCurrentQueryIndex, getCurrentTNode, getCurrentTNodePlaceholderOk, getDirectiveDef, getDirectiveDefOrThrow, getElementDepthCount, getFactoryDef, getInjectableDef, getInjectorDef, getLView, getLViewParent, getNamespace, getNativeByIndex, getNativeByTNode, getNativeByTNodeOrNull, getNgModuleDef, getNgModuleDefOrThrow, getNullInjector, getOrCreateLViewCleanup, getOrCreateTViewCleanup, getPipeDef, getSelectedIndex, getSelectedTNode, getTNode, getTView, hasI18n, importProvidersFrom, increaseElementDepthCount, incrementBindingIndex, initNgDevMode, inject, injectRootLimpMode, internalImportProvidersFrom, isClassProvider, isComponentDef, isComponentHost, isContentQueryHost, isCreationMode, isCurrentTNodeParent, isDestroyed, isDirectiveHost, isEnvironmentProviders, isExhaustiveCheckNoChanges, isForwardRef, isInCheckNoChangesMode, isInI18nBlock, isInInjectionContext, isInSkipHydrationBlock, isInjectable, isLContainer, isLView, isProjectionTNode, isRefreshingViews, isRootView, isSignal, isSkipHydrationRootTNode, isStandalone, isTypeProvider, isWritableSignal, keyValueArrayGet, keyValueArrayIndexOf, keyValueArraySet, lastNodeWasCreated, leaveDI, leaveSkipHydrationBlock, leaveView, load, makeEnvironmentProviders, markAncestorsForTraversal, markViewForRefresh, newArray, nextBindingIndex, nextContextImpl, noop, provideBrowserGlobalErrorListeners, provideEnvironmentInitializer, providerToFactory, removeFromArray, removeLViewOnDestroy, renderStringify, requiresRefreshOrTraversal, resetPreOrderHookFlags, resolveForwardRef, runInInjectionContext, runInInjectorProfilerContext, setBindingIndex, setBindingRootForHostBindings, setCurrentDirectiveIndex, setCurrentQueryIndex, setCurrentTNode, setCurrentTNodeAsNotParent, setInI18nBlock, setInjectImplementation, setInjectorProfiler, setInjectorProfilerContext, setIsInCheckNoChangesMode, setIsRefreshingViews, setSelectedIndex, signal, signalAsReadonlyFn, store, storeCleanupWithContext, storeLViewOnDestroy, stringify, stringifyForError, throwCyclicDependencyError, throwError, throwProviderNotFoundError, truncateMiddle, unwrapLView, unwrapRNode, updateAncestorTraversalFlagsOnAttach, viewAttachedToChangeDetector, viewAttachedToContainer, walkProviderTree, walkUpViews, wasLastNodeCreated, ɵunwrapWritableSignal, ɵɵdefineInjectable, ɵɵdefineInjector, ɵɵdisableBindings, ɵɵenableBindings, ɵɵinject, ɵɵinvalidFactoryDep, ɵɵnamespaceHTML, ɵɵnamespaceMathML, ɵɵnamespaceSVG, ɵɵresetView, ɵɵrestoreView };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,kBAAkB,EAAEC,kBAAkB,QAAQ,qBAAqB;AACxF,SAASC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAClD,SAASC,aAAa,EAAER,UAAU,IAAIS,YAAY,QAAQ,6BAA6B;AACvF,SAASC,iBAAiB,QAAQ,kCAAkC;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,4BAA4B;AAChE;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,iFAAiF;;AAE1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASC,KAAK,CAAC;EAC7BC,IAAI;EACJC,WAAWA,CAACD,IAAI,EAAEE,OAAO,EAAE;IACvB,KAAK,CAACC,kBAAkB,CAACH,IAAI,EAAEE,OAAO,CAAC,CAAC;IACxC,IAAI,CAACF,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,SAASI,sBAAsBA,CAACJ,IAAI,EAAE;EAClC;EACA;EACA;EACA,OAAO,MAAMK,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACH,IAAI,EAAEE,OAAO,EAAE;EACvC,MAAMK,QAAQ,GAAGH,sBAAsB,CAACJ,IAAI,CAAC;EAC7C,IAAIQ,YAAY,GAAG,GAAGD,QAAQ,GAAGL,OAAO,GAAG,IAAI,GAAGA,OAAO,GAAG,EAAE,EAAE;EAChE,IAAIO,SAAS,IAAIT,IAAI,GAAG,CAAC,EAAE;IACvB,MAAMU,kBAAkB,GAAG,CAACF,YAAY,CAACG,KAAK,CAAC,YAAY,CAAC;IAC5D,MAAMC,SAAS,GAAGF,kBAAkB,GAAG,GAAG,GAAG,EAAE;IAC/CF,YAAY,GAAG,GAAGA,YAAY,GAAGI,SAAS,iBAAiBhB,2BAA2B,IAAIW,QAAQ,EAAE;EACxG;EACA,OAAOC,YAAY;AACvB;AAEA,MAAMK,OAAO,GAAGC,UAAU;AAE1B,SAASC,0BAA0BA,CAAA,EAAG;EAClC,MAAMC,cAAc,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACC,QAAQ,CAAC,CAAC,GAAG,EAAE;EACjF,MAAMC,WAAW,GAAG;IAChBC,aAAa,EAAE,CAAC;IAChBC,kBAAkB,EAAE,CAAC;IACrBC,sBAAsB,EAAE,CAAC;IACzBC,0BAA0B,EAAE,CAAC;IAC7BC,0BAA0B,EAAE,CAAC;IAC7BC,mCAAmC,EAAE;EACzC,CAAC;EACD;EACA,MAAMC,kBAAkB,GAAGV,cAAc,CAACW,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;EAC3E,IAAI,CAACD,kBAAkB,EAAE;IACrBb,OAAO,CAAC,WAAW,CAAC,GAAG,KAAK;EAChC,CAAC,MACI;IACD,IAAI,OAAOA,OAAO,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;MAC1CA,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7B;IACAe,MAAM,CAACC,MAAM,CAAChB,OAAO,CAAC,WAAW,CAAC,EAAEM,WAAW,CAAC;EACpD;EACA,OAAOA,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,aAAaA,CAAA,EAAG;EACrB;EACA;EACA;EACA;EACA,IAAI,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/C,IAAI,OAAOA,SAAS,KAAK,QAAQ,IAAImB,MAAM,CAACG,IAAI,CAACtB,SAAS,CAAC,CAACuB,MAAM,KAAK,CAAC,EAAE;MACtEjB,0BAA0B,CAAC,CAAC;IAChC;IACA,OAAO,OAAON,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;EAC1D;EACA,OAAO,KAAK;AAChB;AAEA,SAASwB,sBAAsBA,CAACC,wBAAwB,EAAE;EACtD,KAAK,IAAIC,GAAG,IAAID,wBAAwB,EAAE;IACtC,IAAIA,wBAAwB,CAACC,GAAG,CAAC,KAAKF,sBAAsB,EAAE;MAC1D,OAAOE,GAAG;IACd;EACJ;EACA;EACA;EACA,MAAMpC,KAAK,CAAC,OAAOU,SAAS,KAAK,WAAW,IAAIA,SAAS,GACnD,mDAAmD,GACnD,EAAE,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACpC,KAAK,MAAMH,GAAG,IAAIG,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACC,cAAc,CAACJ,GAAG,CAAC,IAAI,CAACE,MAAM,CAACE,cAAc,CAACJ,GAAG,CAAC,EAAE;MAC3DE,MAAM,CAACF,GAAG,CAAC,GAAGG,MAAM,CAACH,GAAG,CAAC;IAC7B;EACJ;AACJ;AAEA,SAASK,SAASA,CAACC,KAAK,EAAE;EACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOA,KAAK;EAChB;EACA,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACtB,OAAO,IAAIA,KAAK,CAACG,GAAG,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG;EACjD;EACA,IAAIJ,KAAK,IAAI,IAAI,EAAE;IACf,OAAO,EAAE,GAAGA,KAAK;EACrB;EACA,MAAMK,IAAI,GAAGL,KAAK,CAACM,cAAc,IAAIN,KAAK,CAACK,IAAI;EAC/C,IAAIA,IAAI,EAAE;IACN,OAAO,GAAGA,IAAI,EAAE;EACpB;EACA,MAAME,MAAM,GAAGP,KAAK,CAACvB,QAAQ,CAAC,CAAC;EAC/B,IAAI8B,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,EAAE,GAAGA,MAAM;EACtB;EACA,MAAMC,YAAY,GAAGD,MAAM,CAACrB,OAAO,CAAC,IAAI,CAAC;EACzC,OAAOsB,YAAY,IAAI,CAAC,GAAGD,MAAM,CAACE,KAAK,CAAC,CAAC,EAAED,YAAY,CAAC,GAAGD,MAAM;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,sBAAsBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC3C,IAAI,CAACD,MAAM,EACP,OAAOC,KAAK,IAAI,EAAE;EACtB,IAAI,CAACA,KAAK,EACN,OAAOD,MAAM;EACjB,OAAO,GAAGA,MAAM,IAAIC,KAAK,EAAE;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAEC,SAAS,GAAG,GAAG,EAAE;EAC1C,IAAI,CAACD,GAAG,IAAIC,SAAS,GAAG,CAAC,IAAID,GAAG,CAACvB,MAAM,IAAIwB,SAAS,EAChD,OAAOD,GAAG;EACd,IAAIC,SAAS,IAAI,CAAC,EACd,OAAOD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;EACtC,MAAMC,SAAS,GAAGrD,IAAI,CAACsD,KAAK,CAACH,SAAS,GAAG,CAAC,CAAC;EAC3C,OAAOD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAEC,SAAS,CAAC,GAAG,KAAK,GAAGH,GAAG,CAACE,SAAS,CAACF,GAAG,CAACvB,MAAM,GAAG0B,SAAS,CAAC;AACtF;AAEA,MAAME,eAAe,gBAAG3B,sBAAsB,CAAC;EAAE2B,eAAe,EAAE3B;AAAuB,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,UAAUA,CAACC,YAAY,EAAE;EAC9BA,YAAY,CAACF,eAAe,GAAGC,UAAU;EACzCC,YAAY,CAAC5C,QAAQ,GAAG,YAAY;IAChC,OAAOsB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOsB,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC7B,OAAOC,YAAY,CAACD,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,GAAGA,IAAI;AAC7C;AACA;AACA,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,OAAQ,OAAOA,EAAE,KAAK,UAAU,IAC5BA,EAAE,CAAC3B,cAAc,CAACqB,eAAe,CAAC,IAClCM,EAAE,CAACN,eAAe,KAAKC,UAAU;AACzC;;AAEA;AACA;AACA;AACA,SAASM,YAAYA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC/B,IAAI,EAAE,OAAOD,MAAM,KAAK,QAAQ,CAAC,EAAE;IAC/BE,UAAU,CAACD,GAAG,EAAE,OAAOD,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;EACnD;AACJ;AACA,SAASG,mBAAmBA,CAACH,MAAM,EAAEI,YAAY,EAAEC,YAAY,EAAE;EAC7DN,YAAY,CAACC,MAAM,EAAE,mBAAmB,CAAC;EACzCM,qBAAqB,CAACN,MAAM,EAAEK,YAAY,EAAE,6CAA6C,CAAC;EAC1FE,wBAAwB,CAACP,MAAM,EAAEI,YAAY,EAAE,gDAAgD,CAAC;AACpG;AACA,SAASI,YAAYA,CAACR,MAAM,EAAEC,GAAG,EAAE;EAC/B,IAAI,EAAE,OAAOD,MAAM,KAAK,QAAQ,CAAC,EAAE;IAC/BE,UAAU,CAACD,GAAG,EAAED,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;EAC9E;AACJ;AACA,SAASS,cAAcA,CAACT,MAAM,EAAEC,GAAG,EAAE;EACjC,IAAI,EAAE,OAAOD,MAAM,KAAK,UAAU,CAAC,EAAE;IACjCE,UAAU,CAACD,GAAG,EAAED,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;EAChF;AACJ;AACA,SAASU,WAAWA,CAACV,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EACxC,IAAI,EAAED,MAAM,IAAIW,QAAQ,CAAC,EAAE;IACvBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,IAAI,CAAC;EAC3C;AACJ;AACA,SAASC,cAAcA,CAACZ,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EAC3C,IAAI,EAAED,MAAM,IAAIW,QAAQ,CAAC,EAAE;IACvBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,IAAI,CAAC;EAC3C;AACJ;AACA,SAASE,UAAUA,CAACb,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EACvC,IAAI,EAAED,MAAM,KAAKW,QAAQ,CAAC,EAAE;IACxBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,KAAK,CAAC;EAC5C;AACJ;AACA,SAASG,aAAaA,CAACd,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EAC1C,IAAI,EAAED,MAAM,KAAKW,QAAQ,CAAC,EAAE;IACxBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,KAAK,CAAC;EAC5C;AACJ;AACA,SAASI,cAAcA,CAACf,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EAC3C,IAAI,EAAED,MAAM,GAAGW,QAAQ,CAAC,EAAE;IACtBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,GAAG,CAAC;EAC1C;AACJ;AACA,SAASL,qBAAqBA,CAACN,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EAClD,IAAI,EAAED,MAAM,IAAIW,QAAQ,CAAC,EAAE;IACvBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,IAAI,CAAC;EAC3C;AACJ;AACA,SAASK,iBAAiBA,CAAChB,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EAC9C,IAAI,EAAED,MAAM,GAAGW,QAAQ,CAAC,EAAE;IACtBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,GAAG,CAAC;EAC1C;AACJ;AACA,SAASJ,wBAAwBA,CAACP,MAAM,EAAEW,QAAQ,EAAEV,GAAG,EAAE;EACrD,IAAI,EAAED,MAAM,IAAIW,QAAQ,CAAC,EAAE;IACvBT,UAAU,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAE,IAAI,CAAC;EAC3C;AACJ;AACA,SAASM,gBAAgBA,CAACjB,MAAM,EAAEC,GAAG,EAAE;EACnC,IAAID,MAAM,IAAI,IAAI,EAAE;IAChBE,UAAU,CAACD,GAAG,EAAED,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;EACvC;AACJ;AACA,SAASkB,aAAaA,CAAClB,MAAM,EAAEC,GAAG,EAAE;EAChC,IAAID,MAAM,IAAI,IAAI,EAAE;IAChBE,UAAU,CAACD,GAAG,EAAED,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;EACvC;AACJ;AACA,SAASE,UAAUA,CAACD,GAAG,EAAED,MAAM,EAAEW,QAAQ,EAAEQ,UAAU,EAAE;EACnD,MAAM,IAAIxF,KAAK,CAAC,oBAAoBsE,GAAG,EAAE,IACpCkB,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,gBAAgBR,QAAQ,IAAIQ,UAAU,IAAInB,MAAM,YAAY,CAAC,CAAC;AACjG;AACA,SAASoB,aAAaA,CAACC,IAAI,EAAE;EACzB,IAAI,EAAEA,IAAI,YAAYC,IAAI,CAAC,EAAE;IACzBpB,UAAU,CAAC,gEAAgE9B,SAAS,CAACiD,IAAI,CAAC,EAAE,CAAC;EACjG;AACJ;AACA,SAASE,aAAaA,CAACF,IAAI,EAAE;EACzB,IAAI,EAAEA,IAAI,YAAYG,OAAO,CAAC,EAAE;IAC5BtB,UAAU,CAAC,iDAAiD9B,SAAS,CAACiD,IAAI,CAAC,EAAE,CAAC;EAClF;AACJ;AACA,SAASI,kBAAkBA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACpCT,aAAa,CAACQ,GAAG,EAAE,wBAAwB,CAAC;EAC5C,MAAME,MAAM,GAAGF,GAAG,CAAC9D,MAAM;EACzB,IAAI+D,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIC,MAAM,EAAE;IAC9B1B,UAAU,CAAC,kCAAkC0B,MAAM,YAAYD,KAAK,EAAE,CAAC;EAC3E;AACJ;AACA,SAASE,WAAWA,CAACC,KAAK,EAAE,GAAGC,WAAW,EAAE;EACxC,IAAIA,WAAW,CAACxE,OAAO,CAACuE,KAAK,CAAC,KAAK,CAAC,CAAC,EACjC,OAAO,IAAI;EACf5B,UAAU,CAAC,+BAA+B8B,IAAI,CAAC5D,SAAS,CAAC2D,WAAW,CAAC,YAAYC,IAAI,CAAC5D,SAAS,CAAC0D,KAAK,CAAC,GAAG,CAAC;AAC9G;AACA,SAASG,iBAAiBA,CAACnC,EAAE,EAAE;EAC3B,IAAI9E,iBAAiB,CAAC,CAAC,KAAK,IAAI,EAAE;IAC9BkF,UAAU,CAAC,GAAGJ,EAAE,kDAAkD,CAAC;EACvE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoC,kBAAkBA,CAACC,IAAI,EAAE;EAC9B,OAAO;IACH9D,KAAK,EAAE8D,IAAI,CAAC9D,KAAK;IACjB+D,UAAU,EAAED,IAAI,CAACC,UAAU,IAAI,IAAI;IACnCC,OAAO,EAAEF,IAAI,CAACE,OAAO;IACrBP,KAAK,EAAEQ;EACX,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGL,kBAAkB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,OAAO;IAAEC,SAAS,EAAED,OAAO,CAACC,SAAS,IAAI,EAAE;IAAEC,OAAO,EAAEF,OAAO,CAACE,OAAO,IAAI;EAAG,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAChD,IAAI,EAAE;EAC5B,OAAOiD,gBAAgB,CAACjD,IAAI,EAAEkD,WAAW,CAAC;AAC9C;AACA,SAASC,YAAYA,CAACnD,IAAI,EAAE;EACxB,OAAOgD,gBAAgB,CAAChD,IAAI,CAAC,KAAK,IAAI;AAC1C;AACA;AACA;AACA;AACA;AACA,SAASiD,gBAAgBA,CAACjD,IAAI,EAAEoD,KAAK,EAAE;EACnC;EACA,OAAQpD,IAAI,CAACzB,cAAc,CAAC6E,KAAK,CAAC,IAAIpD,IAAI,CAACoD,KAAK,CAAC,IAAK,IAAI;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAACrD,IAAI,EAAE;EACrC;EACA,MAAMsD,GAAG,GAAGtD,IAAI,GAAGkD,WAAW,CAAC,IAAI,IAAI;EACvC,IAAII,GAAG,EAAE;IACL7G,SAAS,IACL8G,OAAO,CAACC,IAAI,CAAC,4CAA4CxD,IAAI,CAAClB,IAAI,8EAA8E,GAC5I,8FAA8FkB,IAAI,CAAClB,IAAI,UAAU,CAAC;IAC1H,OAAOwE,GAAG;EACd,CAAC,MACI;IACD,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAACzD,IAAI,EAAE;EAC1B,OAAOA,IAAI,IAAIA,IAAI,CAACzB,cAAc,CAACmF,UAAU,CAAC,GAAG1D,IAAI,CAAC0D,UAAU,CAAC,GAAG,IAAI;AAC5E;AACA,MAAMR,WAAW,gBAAGjF,sBAAsB,CAAC;EAAE0F,KAAK,EAAE1F;AAAuB,CAAC,CAAC;AAC7E,MAAMyF,UAAU,gBAAGzF,sBAAsB,CAAC;EAAE2F,IAAI,EAAE3F;AAAuB,CAAC,CAAC;;AAE3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4F,cAAc,CAAC;EACjBC,KAAK;EACL;EACAC,cAAc,GAAG,gBAAgB;EACjCJ,KAAK;EACL;AACJ;AACA;AACA;AACA;AACA;EACI1H,WAAWA,CAAC6H,KAAK,EAAEjB,OAAO,EAAE;IACxB,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACH,KAAK,GAAGjB,SAAS;IACtB,IAAI,OAAOG,OAAO,IAAI,QAAQ,EAAE;MAC5B,CAAC,OAAOpG,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C0E,cAAc,CAAC0B,OAAO,EAAE,CAAC,EAAE,0CAA0C,CAAC;MAC1E;MACA;MACA,IAAI,CAACmB,iBAAiB,GAAGnB,OAAO;IACpC,CAAC,MACI,IAAIA,OAAO,KAAKH,SAAS,EAAE;MAC5B,IAAI,CAACiB,KAAK,GAAGrB,kBAAkB,CAAC;QAC5B7D,KAAK,EAAE,IAAI;QACX+D,UAAU,EAAEK,OAAO,CAACL,UAAU,IAAI,MAAM;QACxCC,OAAO,EAAEI,OAAO,CAACJ;MACrB,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACI,IAAIwB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACA/G,QAAQA,CAAA,EAAG;IACP,OAAO,kBAAkB,IAAI,CAAC4G,KAAK,EAAE;EACzC;AACJ;AAEA,IAAII,wBAAwB;AAC5B,SAASC,0BAA0BA,CAAA,EAAG;EAClC,CAAC1H,SAAS,IAAI6D,UAAU,CAAC,sEAAsE,CAAC;EAChG,OAAO4D,wBAAwB;AACnC;AACA,SAASE,0BAA0BA,CAACC,OAAO,EAAE;EACzC,CAAC5H,SAAS,IAAI6D,UAAU,CAAC,sEAAsE,CAAC;EAChG,MAAMgE,QAAQ,GAAGJ,wBAAwB;EACzCA,wBAAwB,GAAGG,OAAO;EAClC,OAAOC,QAAQ;AACnB;AACA,MAAMC,yBAAyB,GAAG,EAAE;AACpC,MAAMC,qBAAqB,GAAGA,CAAA,KAAM,CAAE,CAAC;AACvC,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC9B,MAAMC,WAAW,GAAGJ,yBAAyB,CAAC5G,OAAO,CAAC+G,QAAQ,CAAC;EAC/D,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE;IACpBJ,yBAAyB,CAACK,MAAM,CAACD,WAAW,EAAE,CAAC,CAAC;EACpD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACC,gBAAgB,EAAE;EAC3C,CAACrI,SAAS,IAAI6D,UAAU,CAAC,+DAA+D,CAAC;EACzF,IAAIwE,gBAAgB,KAAK,IAAI,EAAE;IAC3B,IAAI,CAACP,yBAAyB,CAACQ,QAAQ,CAACD,gBAAgB,CAAC,EAAE;MACvDP,yBAAyB,CAACS,IAAI,CAACF,gBAAgB,CAAC;IACpD;IACA,OAAO,MAAML,cAAc,CAACK,gBAAgB,CAAC;EACjD,CAAC,MACI;IACDP,yBAAyB,CAACvG,MAAM,GAAG,CAAC;IACpC,OAAOwG,qBAAqB;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAACG,KAAK,EAAE;EAC7B,CAACxI,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvF,KAAK,IAAI4E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,yBAAyB,CAACvG,MAAM,EAAEkH,CAAC,EAAE,EAAE;IACvD,MAAMC,wBAAwB,GAAGZ,yBAAyB,CAACW,CAAC,CAAC;IAC7DC,wBAAwB,CAACF,KAAK,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAACC,aAAa,EAAEC,cAAc,GAAG,KAAK,EAAE;EACxE,CAAC7I,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvF,IAAI7B,KAAK;EACT;EACA;EACA,IAAI,OAAO4G,aAAa,KAAK,UAAU,EAAE;IACrC5G,KAAK,GAAG4G,aAAa;EACzB;EACA;EAAA,KACK,IAAIA,aAAa,YAAYxB,cAAc,EAAE;IAC9CpF,KAAK,GAAG4G,aAAa;EACzB;EACA;EAAA,KACK;IACD5G,KAAK,GAAGsB,iBAAiB,CAACsF,aAAa,CAACE,OAAO,CAAC;EACpD;EACA,IAAIC,QAAQ,GAAGH,aAAa;EAC5B;EACA;EACA;EACA,IAAIA,aAAa,YAAYxB,cAAc,EAAE;IACzC2B,QAAQ,GAAGH,aAAa,CAAC1B,KAAK,IAAI0B,aAAa;EACnD;EACAP,gBAAgB,CAAC;IACb9E,IAAI,EAAE,CAAC,CAAC;IACRqE,OAAO,EAAEF,0BAA0B,CAAC,CAAC;IACrCsB,cAAc,EAAE;MAAEhH,KAAK;MAAE+G,QAAQ;MAAEF;IAAe;EACtD,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,iCAAiCA,CAACjH,KAAK,EAAE;EAC9C,CAAChC,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvFwE,gBAAgB,CAAC;IACb9E,IAAI,EAAE,CAAC,CAAC;IACRqE,OAAO,EAAEF,0BAA0B,CAAC,CAAC;IACrC1F,KAAK,EAAEA;EACX,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkH,kCAAkCA,CAACC,QAAQ,EAAE;EAClD,CAACnJ,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvFwE,gBAAgB,CAAC;IACb9E,IAAI,EAAE,CAAC,CAAC;IACRqE,OAAO,EAAEF,0BAA0B,CAAC,CAAC;IACrCyB,QAAQ,EAAE;MAAE1D,KAAK,EAAE0D;IAAS;EAChC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACpH,KAAK,EAAEyD,KAAK,EAAE4D,KAAK,EAAE;EAC1C,CAACrJ,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvFwE,gBAAgB,CAAC;IACb9E,IAAI,EAAE,CAAC,CAAC;IACRqE,OAAO,EAAEF,0BAA0B,CAAC,CAAC;IACrC4B,OAAO,EAAE;MAAEtH,KAAK;MAAEyD,KAAK;MAAE4D;IAAM;EACnC,CAAC,CAAC;AACN;AACA,SAASE,sBAAsBA,CAACC,MAAM,EAAE;EACpC,CAACxJ,SAAS,IAAI6D,UAAU,CAAC,6DAA6D,CAAC;EACvFwE,gBAAgB,CAAC;IACb9E,IAAI,EAAE,CAAC,CAAC;IACRqE,OAAO,EAAEF,0BAA0B,CAAC,CAAC;IACrC8B;EACJ,CAAC,CAAC;AACN;AACA,SAASC,4BAA4BA,CAACC,QAAQ,EAAE1H,KAAK,EAAE2H,QAAQ,EAAE;EAC7D,CAAC3J,SAAS,IACN6D,UAAU,CAAC,wEAAwE,CAAC;EACxF,MAAM+F,iBAAiB,GAAGjC,0BAA0B,CAAC;IAAE+B,QAAQ;IAAE1H;EAAM,CAAC,CAAC;EACzE,IAAI;IACA2H,QAAQ,CAAC,CAAC;EACd,CAAC,SACO;IACJhC,0BAA0B,CAACiC,iBAAiB,CAAC;EACjD;AACJ;AAEA,SAASC,sBAAsBA,CAACpE,KAAK,EAAE;EACnC,OAAOA,KAAK,IAAI,CAAC,CAACA,KAAK,CAACqE,UAAU;AACtC;AAEA,MAAMC,WAAW,gBAAGvI,sBAAsB,CAAC;EAAEwI,IAAI,EAAExI;AAAuB,CAAC,CAAC;AAC5E,MAAMyI,UAAU,gBAAGzI,sBAAsB,CAAC;EAAE0I,IAAI,EAAE1I;AAAuB,CAAC,CAAC;AAC3E,MAAM2I,WAAW,gBAAG3I,sBAAsB,CAAC;EAAE4I,KAAK,EAAE5I;AAAuB,CAAC,CAAC;AAC7E,MAAM6I,UAAU,gBAAG7I,sBAAsB,CAAC;EAAE8I,IAAI,EAAE9I;AAAuB,CAAC,CAAC;AAC3E,MAAM+I,cAAc,gBAAG/I,sBAAsB,CAAC;EAAEgJ,IAAI,EAAEhJ;AAAuB,CAAC,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiJ,aAAa,gBAAGjJ,sBAAsB,CAAC;EACzC+F,iBAAiB,EAAE/F;AACvB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkJ,SAAS,gBAAGlJ,sBAAsB,CAAC;EAAEmJ,aAAa,EAAEnJ;AAAuB,CAAC,CAAC;;AAEnF;AACA;AACA;AACA;AACA;AACA;AACA,SAASoJ,eAAeA,CAACnF,KAAK,EAAE;EAC5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACzB,OAAOA,KAAK;EAChB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,EAAE;EACb;EACA;EACA,OAAOoF,MAAM,CAACpF,KAAK,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqF,iBAAiBA,CAACrF,KAAK,EAAE;EAC9B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAOA,KAAK,CAACpD,IAAI,IAAIoD,KAAK,CAAChF,QAAQ,CAAC,CAAC;EACzC,IAAI,OAAOgF,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,CAAClC,IAAI,KAAK,UAAU,EAAE;IAChF,OAAOkC,KAAK,CAAClC,IAAI,CAAClB,IAAI,IAAIoD,KAAK,CAAClC,IAAI,CAAC9C,QAAQ,CAAC,CAAC;EACnD;EACA,OAAOmK,eAAe,CAACnF,KAAK,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsF,0BAA0BA,CAACxH,IAAI,EAAE;EACtC;EACA;EACA,IAAIyH,YAAY,GAAGzH,IAAI,CAACwG,WAAW,CAAC,IAAI,IAAI;EAC5C,IAAIiB,YAAY,KAAK,IAAI,IAAIA,YAAY,CAACC,SAAS,EAAE;IACjD,OAAOC,0BAA0B,CAACF,YAAY,CAACC,SAAS,CAAC;EAC7D;EACA,OAAOH,iBAAiB,CAACvH,IAAI,CAAC;AAClC;AACA;AACA;AACA,SAAS2H,0BAA0BA,CAACD,SAAS,EAAE;EAC3C,IAAI,CAACA,SAAS,CAACE,QAAQ,IAAI,CAACF,SAAS,CAACG,UAAU,EAAE;IAC9C,OAAOH,SAAS,CAACI,SAAS;EAC9B,CAAC,MACI;IACD,OAAO,GAAGJ,SAAS,CAACI,SAAS,QAAQJ,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACG,UAAU,GAAG;EACtF;AACJ;;AAEA;AACA,SAASE,0BAA0BA,CAACtJ,KAAK,EAAEuJ,IAAI,EAAE;EAC7C,MAAM,IAAIlM,YAAY,CAAC,CAAC,GAAG,CAAC,6CAA6CW,SAAS,GAC5E,0CAA0CgC,KAAK,GAAGuJ,IAAI,GAAG,sBAAsBA,IAAI,CAACnJ,IAAI,CAAC,KAAK,CAAC,MAAMJ,KAAK,EAAE,GAAG,EAAE,EAAE,GACnHA,KAAK,CAAC;AAChB;AACA,SAASwJ,4BAA4BA,CAAA,EAAG;EACpC,MAAM,IAAIlM,KAAK,CAAC,kDAAkD,CAAC;AACvE;AACA,SAASmM,yBAAyBA,CAACC,YAAY,EAAErF,SAAS,EAAE0C,QAAQ,EAAE;EAClE,IAAI2C,YAAY,IAAIrF,SAAS,EAAE;IAC3B,MAAMsF,cAAc,GAAGtF,SAAS,CAAClE,GAAG,CAAEyJ,CAAC,IAAMA,CAAC,IAAI7C,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG,GAAG,KAAM,CAAC;IAC3F,MAAM,IAAIzJ,KAAK,CAAC,sCAAsCyC,SAAS,CAAC2J,YAAY,CAAC,8DAA8DC,cAAc,CAACvJ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EAC5K,CAAC,MACI,IAAIyH,sBAAsB,CAACd,QAAQ,CAAC,EAAE;IACvC,IAAIA,QAAQ,CAAC8C,aAAa,EAAE;MACxB,MAAM,IAAIxM,YAAY,CAAC,GAAG,CAAC,kDAAkD,kJAAkJ,CAAC;IACpO,CAAC,MACI;MACD,MAAM,IAAIA,YAAY,CAAC,GAAG,CAAC,kDAAkD,wHAAwH,CAAC;IAC1M;EACJ,CAAC,MACI;IACD,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACvC;AACJ;AACA;AACA,SAASwM,0BAA0BA,CAAC9J,KAAK,EAAE+J,YAAY,EAAE;EACrD,MAAMhM,YAAY,GAAGC,SAAS,IAC1B,mBAAmB8K,iBAAiB,CAAC9I,KAAK,CAAC,SAAS+J,YAAY,GAAG,OAAOA,YAAY,EAAE,GAAG,EAAE,EAAE;EACnG,MAAM,IAAI1M,YAAY,CAAC,CAAC,GAAG,CAAC,2CAA2CU,YAAY,CAAC;AACxF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiM,qBAAqB;AACzB,SAASC,uBAAuBA,CAAA,EAAG;EAC/B,OAAOD,qBAAqB;AAChC;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAACC,IAAI,EAAE;EACnC,MAAMtE,QAAQ,GAAGmE,qBAAqB;EACtCA,qBAAqB,GAAGG,IAAI;EAC5B,OAAOtE,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuE,kBAAkBA,CAACpK,KAAK,EAAEqK,aAAa,EAAEhD,KAAK,EAAE;EACrD,MAAMiD,aAAa,GAAG/F,gBAAgB,CAACvE,KAAK,CAAC;EAC7C,IAAIsK,aAAa,IAAIA,aAAa,CAACvG,UAAU,IAAI,MAAM,EAAE;IACrD,OAAOuG,aAAa,CAAC7G,KAAK,KAAKQ,SAAS,GACjCqG,aAAa,CAAC7G,KAAK,GAAG6G,aAAa,CAACtG,OAAO,CAAC,CAAC,GAC9CsG,aAAa,CAAC7G,KAAK;EAC7B;EACA,IAAI4D,KAAK,GAAG,CAAC,CAAC,oCACV,OAAO,IAAI;EACf,IAAIgD,aAAa,KAAKpG,SAAS,EAC3B,OAAOoG,aAAa;EACxBP,0BAA0B,CAAC9J,KAAK,EAAE,UAAU,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,kCAAkCA,CAAC9I,EAAE,EAAE;EAC5CzD,SAAS,IACLuE,cAAc,CAACyH,qBAAqB,EAAEvI,EAAE,EAAE,iDAAiD,CAAC;AACpG;AAEA,MAAM+I,mBAAmB,GAAG,CAAC,CAAC;AAC9B,MAAMC,kBAAkB,GAAGD,mBAAmB;AAC9C;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,GAAG,gBAAgB;AAC1C;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBjD,QAAQ;EACRlK,WAAWA,CAACkK,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAkD,QAAQA,CAAC5K,KAAK,EAAEoE,OAAO,EAAE;IACrB,MAAMiD,KAAK,GAAGwD,iBAAiB,CAACzG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI;MACA,OAAO,IAAI,CAACsD,QAAQ,CAACoD,GAAG,CAAC9K,KAAK;MAC9B;MACCqH,KAAK,GAAG,CAAC,CAAC,qCAAqC,IAAI,GAAGoD,kBAAkB,EAAGpD,KAAK,CAAC;IACtF,CAAC,CACD,OAAO0D,CAAC,EAAE;MACN,IAAIvO,UAAU,CAACuO,CAAC,CAAC,EAAE;QACf,OAAOA,CAAC;MACZ;MACA,MAAMA,CAAC;IACX;EACJ;AACJ;AACA,MAAMC,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,aAAa,GAAG,aAAa;AACnC,MAAMC,QAAQ,GAAG,MAAM;AACvB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,MAAM,GAAG,UAAU;AACzB,SAASC,kBAAkBA,CAACrL,KAAK,EAAEqH,KAAK,GAAG,CAAC,CAAC,mCAAmC;EAC5E,MAAMiE,eAAe,GAAG7O,kBAAkB,CAAC,CAAC;EAC5C,IAAI6O,eAAe,KAAKrH,SAAS,EAAE;IAC/B,MAAM,IAAI5G,YAAY,CAAC,CAAC,GAAG,CAAC,kDAAkDW,SAAS,IACnF,SAAS+B,SAAS,CAACC,KAAK,CAAC,8MAA8M,CAAC;EAChP,CAAC,MACI,IAAIsL,eAAe,KAAK,IAAI,EAAE;IAC/B,OAAOlB,kBAAkB,CAACpK,KAAK,EAAEiE,SAAS,EAAEoD,KAAK,CAAC;EACtD,CAAC,MACI;IACD,MAAMjD,OAAO,GAAGmH,sBAAsB,CAAClE,KAAK,CAAC;IAC7C,MAAM5D,KAAK,GAAG6H,eAAe,CAACV,QAAQ,CAAC5K,KAAK,EAAEoE,OAAO,CAAC;IACtDpG,SAAS,IAAIoJ,eAAe,CAACpH,KAAK,EAAEyD,KAAK,EAAE4D,KAAK,CAAC;IACjD,IAAI7K,UAAU,CAACiH,KAAK,CAAC,EAAE;MACnB,IAAIW,OAAO,CAACoH,QAAQ,EAAE;QAClB,OAAO,IAAI;MACf;MACA,MAAM/H,KAAK;IACf;IACA,OAAOA,KAAK;EAChB;AACJ;AACA,SAASgI,QAAQA,CAACzL,KAAK,EAAEqH,KAAK,GAAG,CAAC,CAAC,mCAAmC;EAClE,OAAO,CAAC4C,uBAAuB,CAAC,CAAC,IAAIoB,kBAAkB,EAAE/J,iBAAiB,CAACtB,KAAK,CAAC,EAAEqH,KAAK,CAAC;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqE,mBAAmBA,CAACpI,KAAK,EAAE;EAChC,MAAM,IAAIjG,YAAY,CAAC,GAAG,CAAC,mDAAmDW,SAAS,IACnF,wGAAwGsF,KAAK;AACrH;AACA;AACA,2DAA2DA,KAAK,iGAAiG,CAAC;AAClK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqI,MAAMA,CAAC3L,KAAK,EAAEoE,OAAO,EAAE;EAC5B;EACA;EACA,OAAOqH,QAAQ,CAACzL,KAAK,EAAE6K,iBAAiB,CAACzG,OAAO,CAAC,CAAC;AACtD;AACA;AACA,SAASyG,iBAAiBA,CAACxD,KAAK,EAAE;EAC9B,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3D,OAAOA,KAAK;EAChB;EACA;EACA;EACA;EACA,OAAQ,CAAC,CAAC;EAAoC;EACzCA,KAAK,CAACmE,QAAQ,IAAI,CAAC,CAAC,mCAAmC,IACvDnE,KAAK,CAACuE,IAAI,IAAI,CAAC,CAAC,+BAA+B,IAC/CvE,KAAK,CAACwE,IAAI,IAAI,CAAC,CAAC,+BAA+B,IAC/CxE,KAAK,CAACyE,QAAQ,IAAI,CAAC,CAAC,mCAAmC;AAChE;AACA;AACA,SAASP,sBAAsBA,CAAClE,KAAK,EAAE;EACnC,OAAO;IACHmE,QAAQ,EAAE,CAAC,EAAEnE,KAAK,GAAG,CAAC,CAAC,mCAAmC;IAC1DuE,IAAI,EAAE,CAAC,EAAEvE,KAAK,GAAG,CAAC,CAAC,+BAA+B;IAClDwE,IAAI,EAAE,CAAC,EAAExE,KAAK,GAAG,CAAC,CAAC,+BAA+B;IAClDyE,QAAQ,EAAE,CAAC,EAAEzE,KAAK,GAAG,CAAC,CAAC;EAC3B,CAAC;AACL;AACA,SAAS0E,UAAUA,CAACC,KAAK,EAAE;EACvB,MAAMC,IAAI,GAAG,EAAE;EACf,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,KAAK,CAACzM,MAAM,EAAEkH,CAAC,EAAE,EAAE;IACnC,MAAMyF,GAAG,GAAG5K,iBAAiB,CAAC0K,KAAK,CAACvF,CAAC,CAAC,CAAC;IACvC,IAAIxG,KAAK,CAACC,OAAO,CAACgM,GAAG,CAAC,EAAE;MACpB,IAAIA,GAAG,CAAC3M,MAAM,KAAK,CAAC,EAAE;QAClB,MAAM,IAAIlC,YAAY,CAAC,GAAG,CAAC,6CAA6CW,SAAS,IAAI,sCAAsC,CAAC;MAChI;MACA,IAAIuD,IAAI,GAAG0C,SAAS;MACpB,IAAIoD,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAI8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAAC3M,MAAM,EAAE4M,CAAC,EAAE,EAAE;QACjC,MAAMC,IAAI,GAAGF,GAAG,CAACC,CAAC,CAAC;QACnB,MAAME,IAAI,GAAGC,aAAa,CAACF,IAAI,CAAC;QAChC,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;UAC1B;UACA,IAAIA,IAAI,KAAK,CAAC,CAAC,CAAC,6BAA6B;YACzC9K,IAAI,GAAG6K,IAAI,CAACpM,KAAK;UACrB,CAAC,MACI;YACDqH,KAAK,IAAIgF,IAAI;UACjB;QACJ,CAAC,MACI;UACD9K,IAAI,GAAG6K,IAAI;QACf;MACJ;MACAH,IAAI,CAAC1F,IAAI,CAACkF,QAAQ,CAAClK,IAAI,EAAE8F,KAAK,CAAC,CAAC;IACpC,CAAC,MACI;MACD4E,IAAI,CAAC1F,IAAI,CAACkF,QAAQ,CAACS,GAAG,CAAC,CAAC;IAC5B;EACJ;EACA,OAAOD,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAACC,SAAS,EAAEH,IAAI,EAAE;EACvCG,SAAS,CAAC9B,iBAAiB,CAAC,GAAG2B,IAAI;EACnCG,SAAS,CAACC,SAAS,CAAC/B,iBAAiB,CAAC,GAAG2B,IAAI;EAC7C,OAAOG,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,aAAaA,CAACtM,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAAC0K,iBAAiB,CAAC;AACnC;AACA,SAASgC,kBAAkBA,CAAC3B,CAAC,EAAE/K,KAAK,EAAE2M,iBAAiB,EAAE9M,MAAM,EAAE;EAC7D,MAAM+M,SAAS,GAAG7B,CAAC,CAACC,kBAAkB,CAAC;EACvC,IAAIhL,KAAK,CAACoL,MAAM,CAAC,EAAE;IACfwB,SAAS,CAACC,OAAO,CAAC7M,KAAK,CAACoL,MAAM,CAAC,CAAC;EACpC;EACAL,CAAC,CAACtN,OAAO,GAAGqP,WAAW,CAAC,IAAI,GAAG/B,CAAC,CAACtN,OAAO,EAAEmP,SAAS,EAAED,iBAAiB,EAAE9M,MAAM,CAAC;EAC/EkL,CAAC,CAACE,aAAa,CAAC,GAAG2B,SAAS;EAC5B7B,CAAC,CAACC,kBAAkB,CAAC,GAAG,IAAI;EAC5B,MAAMD,CAAC;AACX;AACA,SAAS+B,WAAWA,CAACC,IAAI,EAAEC,GAAG,EAAEL,iBAAiB,EAAE9M,MAAM,GAAG,IAAI,EAAE;EAC9DkN,IAAI,GAAGA,IAAI,IAAIA,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIF,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,IAAI9B,WAAW,GAAG4B,IAAI,CAACtM,KAAK,CAAC,CAAC,CAAC,GAAGsM,IAAI;EAC9F,IAAInH,OAAO,GAAG7F,SAAS,CAACiN,GAAG,CAAC;EAC5B,IAAI/M,KAAK,CAACC,OAAO,CAAC8M,GAAG,CAAC,EAAE;IACpBpH,OAAO,GAAGoH,GAAG,CAAC7M,GAAG,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,MAAM,CAAC;EAC7C,CAAC,MACI,IAAI,OAAO4M,GAAG,KAAK,QAAQ,EAAE;IAC9B,IAAIE,KAAK,GAAG,EAAE;IACd,KAAK,IAAIxN,GAAG,IAAIsN,GAAG,EAAE;MACjB,IAAIA,GAAG,CAAClN,cAAc,CAACJ,GAAG,CAAC,EAAE;QACzB,IAAI+D,KAAK,GAAGuJ,GAAG,CAACtN,GAAG,CAAC;QACpBwN,KAAK,CAAC3G,IAAI,CAAC7G,GAAG,GAAG,GAAG,IAAI,OAAO+D,KAAK,KAAK,QAAQ,GAAGE,IAAI,CAAC5D,SAAS,CAAC0D,KAAK,CAAC,GAAG1D,SAAS,CAAC0D,KAAK,CAAC,CAAC,CAAC;MAClG;IACJ;IACAmC,OAAO,GAAG,IAAIsH,KAAK,CAAC9M,IAAI,CAAC,IAAI,CAAC,GAAG;EACrC;EACA,OAAO,GAAGuM,iBAAiB,GAAG9M,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,GAAG,GAAG,EAAE,IAAI+F,OAAO,MAAMmH,IAAI,CAACI,OAAO,CAACjC,QAAQ,EAAE,MAAM,CAAC,EAAE;AACnH;AAEA,SAASkC,aAAaA,CAAC7L,IAAI,EAAE8L,aAAa,EAAE;EACxC,MAAMC,aAAa,GAAG/L,IAAI,CAACzB,cAAc,CAACyI,cAAc,CAAC;EACzD,IAAI,CAAC+E,aAAa,IAAID,aAAa,KAAK,IAAI,IAAIrP,SAAS,EAAE;IACvD,MAAM,IAAIV,KAAK,CAAC,QAAQyC,SAAS,CAACwB,IAAI,CAAC,iCAAiC,CAAC;EAC7E;EACA,OAAO+L,aAAa,GAAG/L,IAAI,CAACgH,cAAc,CAAC,GAAG,IAAI;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgF,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,gBAAgB,EAAE;EACzC,IAAIF,CAAC,CAACjO,MAAM,KAAKkO,CAAC,CAAClO,MAAM,EACrB,OAAO,KAAK;EAChB,KAAK,IAAIkH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,CAAC,CAACjO,MAAM,EAAEkH,CAAC,EAAE,EAAE;IAC/B,IAAIkH,MAAM,GAAGH,CAAC,CAAC/G,CAAC,CAAC;IACjB,IAAImH,MAAM,GAAGH,CAAC,CAAChH,CAAC,CAAC;IACjB,IAAIiH,gBAAgB,EAAE;MAClBC,MAAM,GAAGD,gBAAgB,CAACC,MAAM,CAAC;MACjCC,MAAM,GAAGF,gBAAgB,CAACE,MAAM,CAAC;IACrC;IACA,IAAIA,MAAM,KAAKD,MAAM,EAAE;MACnB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACC,IAAI,EAAE;EACnB,OAAOA,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,iBAAiB,CAAC;AAC9C;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE1M,EAAE,EAAE;EAC5B0M,KAAK,CAACC,OAAO,CAAE3K,KAAK,IAAMxD,KAAK,CAACC,OAAO,CAACuD,KAAK,CAAC,GAAGyK,WAAW,CAACzK,KAAK,EAAEhC,EAAE,CAAC,GAAGA,EAAE,CAACgC,KAAK,CAAE,CAAC;AACzF;AACA,SAAS4K,UAAUA,CAAChL,GAAG,EAAEC,KAAK,EAAEG,KAAK,EAAE;EACnC;EACA,IAAIH,KAAK,IAAID,GAAG,CAAC9D,MAAM,EAAE;IACrB8D,GAAG,CAACkD,IAAI,CAAC9C,KAAK,CAAC;EACnB,CAAC,MACI;IACDJ,GAAG,CAAC8C,MAAM,CAAC7C,KAAK,EAAE,CAAC,EAAEG,KAAK,CAAC;EAC/B;AACJ;AACA,SAAS6K,eAAeA,CAACjL,GAAG,EAAEC,KAAK,EAAE;EACjC;EACA,IAAIA,KAAK,IAAID,GAAG,CAAC9D,MAAM,GAAG,CAAC,EAAE;IACzB,OAAO8D,GAAG,CAACkL,GAAG,CAAC,CAAC;EACpB,CAAC,MACI;IACD,OAAOlL,GAAG,CAAC8C,MAAM,CAAC7C,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;AACJ;AACA,SAASkL,QAAQA,CAACC,IAAI,EAAEhL,KAAK,EAAE;EAC3B,MAAMqK,IAAI,GAAG,EAAE;EACf,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,IAAI,EAAEhI,CAAC,EAAE,EAAE;IAC3BqH,IAAI,CAACvH,IAAI,CAAC9C,KAAK,CAAC;EACpB;EACA,OAAOqK,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,WAAWA,CAACC,KAAK,EAAErL,KAAK,EAAEsL,KAAK,EAAE;EACtC,MAAMrP,MAAM,GAAGoP,KAAK,CAACpP,MAAM,GAAGqP,KAAK;EACnC,OAAOtL,KAAK,GAAG/D,MAAM,EAAE;IACnBoP,KAAK,CAACrL,KAAK,CAAC,GAAGqL,KAAK,CAACrL,KAAK,GAAGsL,KAAK,CAAC;IACnCtL,KAAK,EAAE;EACX;EACA,OAAOsL,KAAK,EAAE,EAAE;IACZD,KAAK,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAACF,KAAK,EAAErL,KAAK,EAAEwL,MAAM,EAAEC,MAAM,EAAE;EAChD/Q,SAAS,IAAIiE,qBAAqB,CAACqB,KAAK,EAAEqL,KAAK,CAACpP,MAAM,EAAE,8BAA8B,CAAC;EACvF,IAAIyP,GAAG,GAAGL,KAAK,CAACpP,MAAM;EACtB,IAAIyP,GAAG,IAAI1L,KAAK,EAAE;IACd;IACAqL,KAAK,CAACpI,IAAI,CAACuI,MAAM,EAAEC,MAAM,CAAC;EAC9B,CAAC,MACI,IAAIC,GAAG,KAAK,CAAC,EAAE;IAChB;IACAL,KAAK,CAACpI,IAAI,CAACwI,MAAM,EAAEJ,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5BA,KAAK,CAAC,CAAC,CAAC,GAAGG,MAAM;EACrB,CAAC,MACI;IACDE,GAAG,EAAE;IACLL,KAAK,CAACpI,IAAI,CAACoI,KAAK,CAACK,GAAG,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACK,GAAG,CAAC,CAAC;IACtC,OAAOA,GAAG,GAAG1L,KAAK,EAAE;MAChB,MAAM2L,WAAW,GAAGD,GAAG,GAAG,CAAC;MAC3BL,KAAK,CAACK,GAAG,CAAC,GAAGL,KAAK,CAACM,WAAW,CAAC;MAC/BD,GAAG,EAAE;IACT;IACAL,KAAK,CAACrL,KAAK,CAAC,GAAGwL,MAAM;IACrBH,KAAK,CAACrL,KAAK,GAAG,CAAC,CAAC,GAAGyL,MAAM;EAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAACC,aAAa,EAAEzP,GAAG,EAAE+D,KAAK,EAAE;EACjD,IAAIH,KAAK,GAAG8L,oBAAoB,CAACD,aAAa,EAAEzP,GAAG,CAAC;EACpD,IAAI4D,KAAK,IAAI,CAAC,EAAE;IACZ;IACA6L,aAAa,CAAC7L,KAAK,GAAG,CAAC,CAAC,GAAGG,KAAK;EACpC,CAAC,MACI;IACDH,KAAK,GAAG,CAACA,KAAK;IACduL,YAAY,CAACM,aAAa,EAAE7L,KAAK,EAAE5D,GAAG,EAAE+D,KAAK,CAAC;EAClD;EACA,OAAOH,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+L,gBAAgBA,CAACF,aAAa,EAAEzP,GAAG,EAAE;EAC1C,MAAM4D,KAAK,GAAG8L,oBAAoB,CAACD,aAAa,EAAEzP,GAAG,CAAC;EACtD,IAAI4D,KAAK,IAAI,CAAC,EAAE;IACZ;IACA,OAAO6L,aAAa,CAAC7L,KAAK,GAAG,CAAC,CAAC;EACnC;EACA,OAAOW,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmL,oBAAoBA,CAACD,aAAa,EAAEzP,GAAG,EAAE;EAC9C,OAAO4P,mBAAmB,CAACH,aAAa,EAAEzP,GAAG,EAAE,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4P,mBAAmBA,CAACX,KAAK,EAAElL,KAAK,EAAE8L,KAAK,EAAE;EAC9CvR,SAAS,IAAIqE,WAAW,CAACpC,KAAK,CAACC,OAAO,CAACyO,KAAK,CAAC,EAAE,IAAI,EAAE,oBAAoB,CAAC;EAC1E,IAAIa,KAAK,GAAG,CAAC;EACb,IAAIR,GAAG,GAAGL,KAAK,CAACpP,MAAM,IAAIgQ,KAAK;EAC/B,OAAOP,GAAG,KAAKQ,KAAK,EAAE;IAClB,MAAMC,MAAM,GAAGD,KAAK,IAAKR,GAAG,GAAGQ,KAAK,IAAK,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAME,OAAO,GAAGf,KAAK,CAACc,MAAM,IAAIF,KAAK,CAAC;IACtC,IAAI9L,KAAK,KAAKiM,OAAO,EAAE;MACnB,OAAOD,MAAM,IAAIF,KAAK;IAC1B,CAAC,MACI,IAAIG,OAAO,GAAGjM,KAAK,EAAE;MACtBuL,GAAG,GAAGS,MAAM;IAChB,CAAC,MACI;MACDD,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxB;EACJ;EACA,OAAO,EAAET,GAAG,IAAIO,KAAK,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,SAAS,GAAG,CAAC,CAAC;AACpB,MAAMC,WAAW,GAAG,EAAE;AACtB;AACA,IAAI,CAAC,OAAO5R,SAAS,KAAK,WAAW,IAAIA,SAAS,kBAAKqB,aAAa,CAAC,CAAC,EAAE;EACpE;EACA;EACA;EACA,aAAAF,MAAM,CAAC0Q,MAAM,CAACF,SAAS,CAAC;EACxB;EACA,aAAAxQ,MAAM,CAAC0Q,MAAM,CAACD,WAAW,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,gBAAG,IAAI1K,cAAc,CAACpH,SAAS,GAAG,yBAAyB,GAAG,EAAE,CAAC;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+R,UAAU,gBAAG,IAAI3K,cAAc,CAACpH,SAAS,GAAG,UAAU,GAAG,EAAE;AACjE;AACA;AACA,CAAC,CAAC,CAAC,8BAA8B,CAAC;AAElC,MAAMgS,kBAAkB,gBAAG,IAAI5K,cAAc,CAACpH,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;AAEpF,MAAMiS,YAAY,CAAC;EACfnF,GAAGA,CAAC9K,KAAK,EAAEqK,aAAa,GAAGI,kBAAkB,EAAE;IAC3C,IAAIJ,aAAa,KAAKI,kBAAkB,EAAE;MACtC,MAAMyF,KAAK,GAAG,IAAIlT,aAAa,CAAC,sCAAsC+C,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC;MAC1F,MAAMkQ,KAAK;IACf;IACA,OAAO7F,aAAa;EACxB;AACJ;AAEA,SAAS8F,cAAcA,CAAC5O,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAAC8G,UAAU,CAAC,IAAI,IAAI;AACnC;AACA,SAAS+H,qBAAqBA,CAAC7O,IAAI,EAAE;EACjC,MAAM8O,WAAW,GAAGF,cAAc,CAAC5O,IAAI,CAAC;EACxC,IAAI,CAAC8O,WAAW,EAAE;IACd,MAAM,IAAIhT,YAAY,CAAC,GAAG,CAAC,qDAAqD,CAAC,OAAOW,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,QAAQ+B,SAAS,CAACwB,IAAI,CAAC,iCAAiC,CAAC;EACjE;EACA,OAAO8O,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAC/O,IAAI,EAAE;EAC3B,OAAOA,IAAI,CAACwG,WAAW,CAAC,IAAI,IAAI;AACpC;AACA,SAASwI,sBAAsBA,CAAChP,IAAI,EAAE;EAClC,MAAMsD,GAAG,GAAG2L,eAAe,CAACjP,IAAI,CAAC;EACjC,IAAI,CAACsD,GAAG,EAAE;IACN,MAAM,IAAIxH,YAAY,CAAC,GAAG,CAAC,qDAAqD,CAAC,OAAOW,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,QAAQ+B,SAAS,CAACwB,IAAI,CAAC,iCAAiC,CAAC;EACjE;EACA,OAAOsD,GAAG;AACd;AACA,SAAS2L,eAAeA,CAACjP,IAAI,EAAE;EAC3B,OAAOA,IAAI,CAAC0G,UAAU,CAAC,IAAI,IAAI;AACnC;AACA,SAASwI,UAAUA,CAAClP,IAAI,EAAE;EACtB,OAAOA,IAAI,CAAC4G,WAAW,CAAC,IAAI,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuI,YAAYA,CAACnP,IAAI,EAAE;EACxB,MAAMsD,GAAG,GAAGyL,eAAe,CAAC/O,IAAI,CAAC,IAAIiP,eAAe,CAACjP,IAAI,CAAC,IAAIkP,UAAU,CAAClP,IAAI,CAAC;EAC9E,OAAOsD,GAAG,KAAK,IAAI,IAAIA,GAAG,CAAC8L,UAAU;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACvM,SAAS,EAAE;EACzC,OAAO;IACHyD,UAAU,EAAEzD;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwM,6BAA6BA,CAACC,aAAa,EAAE;EAClD,OAAOF,wBAAwB,CAAC,CAC5B;IACI9J,OAAO,EAAEgJ,uBAAuB;IAChCtK,KAAK,EAAE,IAAI;IACXuL,QAAQ,EAAED;EACd,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAAC,GAAGC,OAAO,EAAE;EACrC,OAAO;IACHnJ,UAAU,EAAEoJ,2BAA2B,CAAC,IAAI,EAAED,OAAO,CAAC;IACtDpH,aAAa,EAAE;EACnB,CAAC;AACL;AACA,SAASqH,2BAA2BA,CAACC,qBAAqB,EAAE,GAAGF,OAAO,EAAE;EACpE,MAAMG,YAAY,GAAG,EAAE;EACvB,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIC,0BAA0B;EAC9B,MAAMC,gBAAgB,GAAIzK,QAAQ,IAAK;IACnCqK,YAAY,CAAC7K,IAAI,CAACQ,QAAQ,CAAC;EAC/B,CAAC;EACDmH,WAAW,CAAC+C,OAAO,EAAGpR,MAAM,IAAK;IAC7B,IAAI,CAAC,OAAO7B,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKmT,qBAAqB,EAAE;MAC1E,MAAMM,MAAM,GAAGnB,eAAe,CAACzQ,MAAM,CAAC;MACtC,IAAI4R,MAAM,EAAEd,UAAU,EAAE;QACpB,MAAM,IAAItT,YAAY,CAAC,GAAG,CAAC,yDAAyD,gGAAgGyL,iBAAiB,CAACjJ,MAAM,CAAC,GAAG,CAAC;MACrN;IACJ;IACA;IACA,MAAM6R,cAAc,GAAG7R,MAAM;IAC7B,IAAI8R,gBAAgB,CAACD,cAAc,EAAEF,gBAAgB,EAAE,EAAE,EAAEH,KAAK,CAAC,EAAE;MAC/DE,0BAA0B,KAAK,EAAE;MACjCA,0BAA0B,CAAChL,IAAI,CAACmL,cAAc,CAAC;IACnD;EACJ,CAAC,CAAC;EACF;EACA,IAAIH,0BAA0B,KAAKtN,SAAS,EAAE;IAC1C2N,iCAAiC,CAACL,0BAA0B,EAAEC,gBAAgB,CAAC;EACnF;EACA,OAAOJ,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA,SAASQ,iCAAiCA,CAACC,kBAAkB,EAAEC,OAAO,EAAE;EACpE,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,kBAAkB,CAACtS,MAAM,EAAEkH,CAAC,EAAE,EAAE;IAChD,MAAM;MAAEsL,QAAQ;MAAE1N;IAAU,CAAC,GAAGwN,kBAAkB,CAACpL,CAAC,CAAC;IACrDuL,mBAAmB,CAAC3N,SAAS,EAAG0C,QAAQ,IAAK;MACzC/I,SAAS,IAAIiU,gBAAgB,CAAClL,QAAQ,EAAE1C,SAAS,IAAIuL,WAAW,EAAEmC,QAAQ,CAAC;MAC3ED,OAAO,CAAC/K,QAAQ,EAAEgL,QAAQ,CAAC;IAC/B,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,gBAAgBA,CAACO,SAAS,EAAEJ,OAAO,EAAEK,OAAO,EAAEd,KAAK,EAAE;EAC1Da,SAAS,GAAG5Q,iBAAiB,CAAC4Q,SAAS,CAAC;EACxC,IAAI,CAACA,SAAS,EACV,OAAO,KAAK;EAChB;EACA;EACA,IAAIE,OAAO,GAAG,IAAI;EAClB,IAAIC,MAAM,GAAGrN,cAAc,CAACkN,SAAS,CAAC;EACtC,MAAMT,MAAM,GAAG,CAACY,MAAM,IAAI/B,eAAe,CAAC4B,SAAS,CAAC;EACpD,IAAI,CAACG,MAAM,IAAI,CAACZ,MAAM,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA,MAAMM,QAAQ,GAAGG,SAAS,CACrBH,QAAQ;IACbM,MAAM,GAAGrN,cAAc,CAAC+M,QAAQ,CAAC;IACjC,IAAIM,MAAM,EAAE;MACRD,OAAO,GAAGL,QAAQ;IACtB,CAAC,MACI;MACD;MACA,OAAO,KAAK;IAChB;EACJ,CAAC,MACI,IAAIN,MAAM,IAAI,CAACA,MAAM,CAACd,UAAU,EAAE;IACnC,OAAO,KAAK;EAChB,CAAC,MACI;IACDyB,OAAO,GAAGF,SAAS;EACvB;EACA;EACA,IAAIlU,SAAS,IAAImU,OAAO,CAACjT,OAAO,CAACkT,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9C,MAAME,OAAO,GAAGvS,SAAS,CAACqS,OAAO,CAAC;IAClC,MAAM7I,IAAI,GAAG4I,OAAO,CAAChS,GAAG,CAACJ,SAAS,CAAC;IACnCuJ,0BAA0B,CAACgJ,OAAO,EAAE/I,IAAI,CAAC;EAC7C;EACA;EACA,MAAMgJ,WAAW,GAAGlB,KAAK,CAACmB,GAAG,CAACJ,OAAO,CAAC;EACtC,IAAIX,MAAM,EAAE;IACR,IAAIc,WAAW,EAAE;MACb;MACA,OAAO,KAAK;IAChB;IACAlB,KAAK,CAACoB,GAAG,CAACL,OAAO,CAAC;IAClB,IAAIX,MAAM,CAACiB,YAAY,EAAE;MACrB,MAAMC,IAAI,GAAG,OAAOlB,MAAM,CAACiB,YAAY,KAAK,UAAU,GAAGjB,MAAM,CAACiB,YAAY,CAAC,CAAC,GAAGjB,MAAM,CAACiB,YAAY;MACpG,KAAK,MAAME,GAAG,IAAID,IAAI,EAAE;QACpBhB,gBAAgB,CAACiB,GAAG,EAAEd,OAAO,EAAEK,OAAO,EAAEd,KAAK,CAAC;MAClD;IACJ;EACJ,CAAC,MACI,IAAIgB,MAAM,EAAE;IACb;IACA,IAAIA,MAAM,CAAC/N,OAAO,IAAI,IAAI,IAAI,CAACiO,WAAW,EAAE;MACxC;MACA;MACAvU,SAAS,IAAImU,OAAO,CAAC5L,IAAI,CAAC6L,OAAO,CAAC;MAClC;MACAf,KAAK,CAACoB,GAAG,CAACL,OAAO,CAAC;MAClB,IAAIS,wBAAwB;MAC5B,IAAI;QACA3E,WAAW,CAACmE,MAAM,CAAC/N,OAAO,EAAGwO,QAAQ,IAAK;UACtC,IAAInB,gBAAgB,CAACmB,QAAQ,EAAEhB,OAAO,EAAEK,OAAO,EAAEd,KAAK,CAAC,EAAE;YACrDwB,wBAAwB,KAAK,EAAE;YAC/B;YACA;YACAA,wBAAwB,CAACtM,IAAI,CAACuM,QAAQ,CAAC;UAC3C;QACJ,CAAC,CAAC;MACN,CAAC,SACO;QACJ;QACA9U,SAAS,IAAImU,OAAO,CAAC5D,GAAG,CAAC,CAAC;MAC9B;MACA;MACA;MACA;MACA,IAAIsE,wBAAwB,KAAK5O,SAAS,EAAE;QACxC2N,iCAAiC,CAACiB,wBAAwB,EAAEf,OAAO,CAAC;MACxE;IACJ;IACA,IAAI,CAACS,WAAW,EAAE;MACd;MACA;MACA,MAAMvO,OAAO,GAAGoJ,aAAa,CAACgF,OAAO,CAAC,KAAK,MAAM,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC/D;MACA;MACA;MACA;MACAN,OAAO,CAAC;QAAEhL,OAAO,EAAEsL,OAAO;QAAEW,UAAU,EAAE/O,OAAO;QAAE2O,IAAI,EAAE/C;MAAY,CAAC,EAAEwC,OAAO,CAAC;MAC9E;MACAN,OAAO,CAAC;QAAEhL,OAAO,EAAEkJ,kBAAkB;QAAEe,QAAQ,EAAEqB,OAAO;QAAE5M,KAAK,EAAE;MAAK,CAAC,EAAE4M,OAAO,CAAC;MACjF;MACAN,OAAO,CAAC;QAAEhL,OAAO,EAAEgJ,uBAAuB;QAAEiB,QAAQ,EAAEA,CAAA,KAAMtF,QAAQ,CAAC2G,OAAO,CAAC;QAAE5M,KAAK,EAAE;MAAK,CAAC,EAAE4M,OAAO,CAAC;IAC1G;IACA;IACA,MAAMY,YAAY,GAAGX,MAAM,CAAChO,SAAS;IACrC,IAAI2O,YAAY,IAAI,IAAI,IAAI,CAACT,WAAW,EAAE;MACtC,MAAMU,YAAY,GAAGf,SAAS;MAC9BF,mBAAmB,CAACgB,YAAY,EAAGjM,QAAQ,IAAK;QAC5C/I,SAAS,IAAIiU,gBAAgB,CAAClL,QAAQ,EAAEiM,YAAY,EAAEC,YAAY,CAAC;QACnEnB,OAAO,CAAC/K,QAAQ,EAAEkM,YAAY,CAAC;MACnC,CAAC,CAAC;IACN;EACJ,CAAC,MACI;IACD;IACA,OAAO,KAAK;EAChB;EACA,OAAQb,OAAO,KAAKF,SAAS,IAAIA,SAAS,CAAC7N,SAAS,KAAKJ,SAAS;AACtE;AACA,SAASgO,gBAAgBA,CAAClL,QAAQ,EAAE1C,SAAS,EAAE6O,aAAa,EAAE;EAC1D,IAAIC,cAAc,CAACpM,QAAQ,CAAC,IACxBqM,eAAe,CAACrM,QAAQ,CAAC,IACzBsM,iBAAiB,CAACtM,QAAQ,CAAC,IAC3BuM,kBAAkB,CAACvM,QAAQ,CAAC,EAAE;IAC9B;EACJ;EACA;EACA,MAAMwM,QAAQ,GAAGjS,iBAAiB,CAACyF,QAAQ,KAAKA,QAAQ,CAACyM,QAAQ,IAAIzM,QAAQ,CAACD,OAAO,CAAC,CAAC;EACvF,IAAI,CAACyM,QAAQ,EAAE;IACX9J,yBAAyB,CAACyJ,aAAa,EAAE7O,SAAS,EAAE0C,QAAQ,CAAC;EACjE;AACJ;AACA,SAASiL,mBAAmBA,CAAC3N,SAAS,EAAE5C,EAAE,EAAE;EACxC,KAAK,IAAIsF,QAAQ,IAAI1C,SAAS,EAAE;IAC5B,IAAIwD,sBAAsB,CAACd,QAAQ,CAAC,EAAE;MAClCA,QAAQ,GAAGA,QAAQ,CAACe,UAAU;IAClC;IACA,IAAI7H,KAAK,CAACC,OAAO,CAAC6G,QAAQ,CAAC,EAAE;MACzBiL,mBAAmB,CAACjL,QAAQ,EAAEtF,EAAE,CAAC;IACrC,CAAC,MACI;MACDA,EAAE,CAACsF,QAAQ,CAAC;IAChB;EACJ;AACJ;AACA,MAAM0M,SAAS,gBAAGjU,sBAAsB,CAAC;EACrCsH,OAAO,EAAE+B,MAAM;EACfkI,QAAQ,EAAEvR;AACd,CAAC,CAAC;AACF,SAAS4T,eAAeA,CAAC3P,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIgQ,SAAS,IAAIhQ,KAAK;AAC3E;AACA,SAAS6P,kBAAkBA,CAAC7P,KAAK,EAAE;EAC/B,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACiQ,WAAW,CAAC;AACzC;AACA,SAASL,iBAAiBA,CAAC5P,KAAK,EAAE;EAC9B,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACsP,UAAU,CAAC;AACxC;AACA,SAASI,cAAcA,CAAC1P,KAAK,EAAE;EAC3B,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA,SAASkQ,eAAeA,CAAClQ,KAAK,EAAE;EAC5B,OAAO,CAAC,CAACA,KAAK,CAAC+P,QAAQ;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,cAAc,gBAAG,IAAIxO,cAAc,CAACpH,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;;AAEjF;AACA;AACA;AACA,MAAM6V,OAAO,GAAG,CAAC,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;AACnB;AACA;AACA;AACA,IAAIC,aAAa,GAAG9P,SAAS;AAC7B,SAAS+P,eAAeA,CAAA,EAAG;EACvB,IAAID,aAAa,KAAK9P,SAAS,EAAE;IAC7B8P,aAAa,GAAG,IAAI9D,YAAY,CAAC,CAAC;EACtC;EACA,OAAO8D,aAAa;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,mBAAmB,CAAC;AAE1B,MAAMC,UAAU,SAASD,mBAAmB,CAAC;EACzCE,MAAM;EACNtU,MAAM;EACNuU,MAAM;EACN;AACJ;AACA;AACA;AACA;EACIC,OAAO,gBAAG,IAAIC,GAAG,CAAC,CAAC;EACnB;AACJ;AACA;EACIC,iBAAiB,gBAAG,IAAIjD,GAAG,CAAC,CAAC;EAC7BkD,eAAe,GAAG,EAAE;EACpB;AACJ;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACAA,UAAU,GAAG,KAAK;EAClBC,gBAAgB;EAChBnX,WAAWA,CAAC6G,SAAS,EAAE8P,MAAM,EAAEtU,MAAM,EAAEuU,MAAM,EAAE;IAC3C,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACtU,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuU,MAAM,GAAGA,MAAM;IACpB;IACAQ,qBAAqB,CAACvQ,SAAS,EAAG0C,QAAQ,IAAK,IAAI,CAAC8N,eAAe,CAAC9N,QAAQ,CAAC,CAAC;IAC9E;IACA,IAAI,CAACsN,OAAO,CAACS,GAAG,CAAC/E,UAAU,EAAEgF,UAAU,CAAC9Q,SAAS,EAAE,IAAI,CAAC,CAAC;IACzD;IACA,IAAImQ,MAAM,CAAC5B,GAAG,CAAC,aAAa,CAAC,EAAE;MAC3B,IAAI,CAAC6B,OAAO,CAACS,GAAG,CAACb,mBAAmB,EAAEc,UAAU,CAAC9Q,SAAS,EAAE,IAAI,CAAC,CAAC;IACtE;IACA;IACA;IACA,MAAM+Q,MAAM,GAAG,IAAI,CAACX,OAAO,CAACvJ,GAAG,CAAC8I,cAAc,CAAC;IAC/C,IAAIoB,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACvR,KAAK,KAAK,QAAQ,EAAE;MACpD,IAAI,CAAC2Q,MAAM,CAAC3B,GAAG,CAACuC,MAAM,CAACvR,KAAK,CAAC;IACjC;IACA,IAAI,CAACkR,gBAAgB,GAAG,IAAIrD,GAAG,CAAC,IAAI,CAACxG,GAAG,CAACkF,kBAAkB,EAAEJ,WAAW,EAAE;MAAE/D,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;EAC9F;EACAjB,QAAQA,CAAC5K,KAAK,EAAEoE,OAAO,EAAE;IACrB,MAAMiD,KAAK,GAAGwD,iBAAiB,CAACzG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI;MACA,OAAO,IAAI,CAAC0G,GAAG,CAAC9K,KAAK;MACrB;MACAyK,kBAAkB,EAAEpD,KAAK,CAAC;IAC9B,CAAC,CACD,OAAO0D,CAAC,EAAE;MACN,IAAI9N,YAAY,CAAC8N,CAAC,CAAC,EAAE;QACjB,OAAOA,CAAC;MACZ;MACA,MAAMA,CAAC;IACX;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkK,OAAOA,CAAA,EAAG;IACNC,kBAAkB,CAAC,IAAI,CAAC;IACxB;IACA,IAAI,CAACR,UAAU,GAAG,IAAI;IACtB,MAAMS,YAAY,GAAGjY,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI;MACA;MACA,KAAK,MAAMoK,OAAO,IAAI,IAAI,CAACiN,iBAAiB,EAAE;QAC1CjN,OAAO,CAAC8N,WAAW,CAAC,CAAC;MACzB;MACA,MAAMC,cAAc,GAAG,IAAI,CAACb,eAAe;MAC3C;MACA;MACA,IAAI,CAACA,eAAe,GAAG,EAAE;MACzB,KAAK,MAAMc,IAAI,IAAID,cAAc,EAAE;QAC/BC,IAAI,CAAC,CAAC;MACV;IACJ,CAAC,SACO;MACJ;MACA,IAAI,CAACjB,OAAO,CAACkB,KAAK,CAAC,CAAC;MACpB,IAAI,CAAChB,iBAAiB,CAACgB,KAAK,CAAC,CAAC;MAC9B,IAAI,CAACZ,gBAAgB,CAACY,KAAK,CAAC,CAAC;MAC7BrY,iBAAiB,CAACiY,YAAY,CAAC;IACnC;EACJ;EACAK,SAASA,CAAC7N,QAAQ,EAAE;IAChBuN,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI,CAACV,eAAe,CAACjO,IAAI,CAACoB,QAAQ,CAAC;IACnC,OAAO,MAAM,IAAI,CAAC8N,eAAe,CAAC9N,QAAQ,CAAC;EAC/C;EACA+N,YAAYA,CAACjU,EAAE,EAAE;IACbyT,kBAAkB,CAAC,IAAI,CAAC;IACxB,MAAMS,gBAAgB,GAAGjZ,kBAAkB,CAAC,IAAI,CAAC;IACjD,MAAMkZ,4BAA4B,GAAG1L,uBAAuB,CAACjG,SAAS,CAAC;IACvE,IAAI2D,iBAAiB;IACrB,IAAI5J,SAAS,EAAE;MACX4J,iBAAiB,GAAGjC,0BAA0B,CAAC;QAAE+B,QAAQ,EAAE,IAAI;QAAE1H,KAAK,EAAE;MAAK,CAAC,CAAC;IACnF;IACA,IAAI;MACA,OAAOyB,EAAE,CAAC,CAAC;IACf,CAAC,SACO;MACJ/E,kBAAkB,CAACiZ,gBAAgB,CAAC;MACpCzL,uBAAuB,CAAC0L,4BAA4B,CAAC;MACrD5X,SAAS,IAAI2H,0BAA0B,CAACiC,iBAAiB,CAAC;IAC9D;EACJ;EACAkD,GAAGA,CAAC9K,KAAK,EAAEqK,aAAa,GAAGI,kBAAkB,EAAErG,OAAO,EAAE;IACpD8Q,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAIlV,KAAK,CAACF,cAAc,CAAC4I,SAAS,CAAC,EAAE;MACjC,OAAO1I,KAAK,CAAC0I,SAAS,CAAC,CAAC,IAAI,CAAC;IACjC;IACA,MAAMrB,KAAK,GAAGwD,iBAAiB,CAACzG,OAAO,CAAC;IACxC;IACA,IAAIwD,iBAAiB;IACrB,IAAI5J,SAAS,EAAE;MACX4J,iBAAiB,GAAGjC,0BAA0B,CAAC;QAAE+B,QAAQ,EAAE,IAAI;QAAE1H,KAAK,EAAEA;MAAM,CAAC,CAAC;IACpF;IACA,MAAM2V,gBAAgB,GAAGjZ,kBAAkB,CAAC,IAAI,CAAC;IACjD,MAAMkZ,4BAA4B,GAAG1L,uBAAuB,CAACjG,SAAS,CAAC;IACvE,IAAI;MACA;MACA,IAAI,EAAEoD,KAAK,GAAG,CAAC,CAAC,mCAAmC,EAAE;QACjD;QACA,IAAI2N,MAAM,GAAG,IAAI,CAACX,OAAO,CAACvJ,GAAG,CAAC9K,KAAK,CAAC;QACpC,IAAIgV,MAAM,KAAK/Q,SAAS,EAAE;UACtB;UACA;UACA,MAAMY,GAAG,GAAGgR,qBAAqB,CAAC7V,KAAK,CAAC,IAAIuE,gBAAgB,CAACvE,KAAK,CAAC;UACnE,IAAI6E,GAAG,IAAI,IAAI,CAACiR,oBAAoB,CAACjR,GAAG,CAAC,EAAE;YACvC;YACA;YACA,IAAI7G,SAAS,EAAE;cACXyJ,4BAA4B,CAAC,IAAI,EAAEzH,KAAK,EAAE,MAAM;gBAC5C2G,2BAA2B,CAAC3G,KAAK,CAAC;cACtC,CAAC,CAAC;YACN;YACAgV,MAAM,GAAGD,UAAU,CAACgB,iCAAiC,CAAC/V,KAAK,CAAC,EAAE6T,OAAO,CAAC;UAC1E,CAAC,MACI;YACDmB,MAAM,GAAG,IAAI;UACjB;UACA,IAAI,CAACX,OAAO,CAACS,GAAG,CAAC9U,KAAK,EAAEgV,MAAM,CAAC;QACnC;QACA;QACA,IAAIA,MAAM,IAAI,IAAI,CAAC,6BAA6B;UAC5C,OAAO,IAAI,CAACgB,OAAO,CAAChW,KAAK,EAAEgV,MAAM,CAAC;QACtC;MACJ;MACA;MACA;MACA,MAAMiB,YAAY,GAAG,EAAE5O,KAAK,GAAG,CAAC,CAAC,+BAA+B,GAAG,IAAI,CAAC8M,MAAM,GAAGH,eAAe,CAAC,CAAC;MAClG;MACA;MACA3J,aAAa,GACThD,KAAK,GAAG,CAAC,CAAC,sCAAsCgD,aAAa,KAAKI,kBAAkB,GAC9E,IAAI,GACJJ,aAAa;MACvB,OAAO4L,YAAY,CAACnL,GAAG,CAAC9K,KAAK,EAAEqK,aAAa,CAAC;IACjD,CAAC,CACD,OAAOU,CAAC,EAAE;MACN,IAAI9N,YAAY,CAAC8N,CAAC,CAAC,EAAE;QACjB;QACA,MAAMxB,IAAI,GAAIwB,CAAC,CAACC,kBAAkB,CAAC,GAAGD,CAAC,CAACC,kBAAkB,CAAC,IAAI,EAAG;QAClEzB,IAAI,CAACsD,OAAO,CAAC9M,SAAS,CAACC,KAAK,CAAC,CAAC;QAC9B,IAAI2V,gBAAgB,EAAE;UAClB;UACA,MAAM5K,CAAC;QACX,CAAC,MACI;UACD;UACA,OAAO2B,kBAAkB,CAAC3B,CAAC,EAAE/K,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAACH,MAAM,CAAC;QACvE;MACJ,CAAC,MACI;QACD,MAAMkL,CAAC;MACX;IACJ,CAAC,SACO;MACJ;MACAb,uBAAuB,CAAC0L,4BAA4B,CAAC;MACrDlZ,kBAAkB,CAACiZ,gBAAgB,CAAC;MACpC3X,SAAS,IAAI2H,0BAA0B,CAACiC,iBAAiB,CAAC;IAC9D;EACJ;EACA;EACAsO,2BAA2BA,CAAA,EAAG;IAC1B,MAAMf,YAAY,GAAGjY,iBAAiB,CAAC,IAAI,CAAC;IAC5C,MAAMyY,gBAAgB,GAAGjZ,kBAAkB,CAAC,IAAI,CAAC;IACjD,MAAMkZ,4BAA4B,GAAG1L,uBAAuB,CAACjG,SAAS,CAAC;IACvE,IAAI2D,iBAAiB;IACrB,IAAI5J,SAAS,EAAE;MACX4J,iBAAiB,GAAGjC,0BAA0B,CAAC;QAAE+B,QAAQ,EAAE,IAAI;QAAE1H,KAAK,EAAE;MAAK,CAAC,CAAC;IACnF;IACA,IAAI;MACA,MAAMmW,YAAY,GAAG,IAAI,CAACrL,GAAG,CAACgF,uBAAuB,EAAEF,WAAW,EAAE;QAAE/D,IAAI,EAAE;MAAK,CAAC,CAAC;MACnF,IAAI7N,SAAS,IAAI,CAACiC,KAAK,CAACC,OAAO,CAACiW,YAAY,CAAC,EAAE;QAC3C,MAAM,IAAI9Y,YAAY,CAAC,CAAC,GAAG,CAAC,+CAA+C,+DAA+D,GACtI,+BAA+B,OAAO8Y,YAAY,KAAK,GACvD,2EAA2E,GAC3E,yBAAyB,CAAC;MAClC;MACA,KAAK,MAAMC,WAAW,IAAID,YAAY,EAAE;QACpCC,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC,SACO;MACJ1Z,kBAAkB,CAACiZ,gBAAgB,CAAC;MACpCzL,uBAAuB,CAAC0L,4BAA4B,CAAC;MACrD5X,SAAS,IAAI2H,0BAA0B,CAACiC,iBAAiB,CAAC;MAC1D1K,iBAAiB,CAACiY,YAAY,CAAC;IACnC;EACJ;EACA1W,QAAQA,CAAA,EAAG;IACP,MAAM4X,MAAM,GAAG,EAAE;IACjB,MAAMhC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,KAAK,MAAMrU,KAAK,IAAIqU,OAAO,CAAC/U,IAAI,CAAC,CAAC,EAAE;MAChC+W,MAAM,CAAC9P,IAAI,CAACxG,SAAS,CAACC,KAAK,CAAC,CAAC;IACjC;IACA,OAAO,cAAcqW,MAAM,CAACjW,IAAI,CAAC,IAAI,CAAC,GAAG;EAC7C;EACA;AACJ;AACA;EACIyU,eAAeA,CAAC9N,QAAQ,EAAE;IACtB;IACA;IACAA,QAAQ,GAAGzF,iBAAiB,CAACyF,QAAQ,CAAC;IACtC,IAAI/G,KAAK,GAAGmT,cAAc,CAACpM,QAAQ,CAAC,GAC9BA,QAAQ,GACRzF,iBAAiB,CAACyF,QAAQ,IAAIA,QAAQ,CAACD,OAAO,CAAC;IACrD;IACA,MAAMkO,MAAM,GAAGsB,gBAAgB,CAACvP,QAAQ,CAAC;IACzC,IAAI/I,SAAS,EAAE;MACXyJ,4BAA4B,CAAC,IAAI,EAAEzH,KAAK,EAAE,MAAM;QAC5C;QACA;QACA;QACA,IAAIoT,eAAe,CAACrM,QAAQ,CAAC,EAAE;UAC3BE,iCAAiC,CAACjH,KAAK,CAAC;UACxCkH,kCAAkC,CAACH,QAAQ,CAACgK,QAAQ,CAAC;QACzD;QACApK,2BAA2B,CAACI,QAAQ,CAAC;MACzC,CAAC,CAAC;IACN;IACA,IAAI,CAACoM,cAAc,CAACpM,QAAQ,CAAC,IAAIA,QAAQ,CAACvB,KAAK,KAAK,IAAI,EAAE;MACtD;MACA;MACA,IAAI+Q,WAAW,GAAG,IAAI,CAAClC,OAAO,CAACvJ,GAAG,CAAC9K,KAAK,CAAC;MACzC,IAAIuW,WAAW,EAAE;QACb;QACA,IAAIvY,SAAS,IAAIuY,WAAW,CAAC/Q,KAAK,KAAKvB,SAAS,EAAE;UAC9CuF,4BAA4B,CAAC,CAAC;QAClC;MACJ,CAAC,MACI;QACD+M,WAAW,GAAGxB,UAAU,CAAC9Q,SAAS,EAAE4P,OAAO,EAAE,IAAI,CAAC;QAClD0C,WAAW,CAACvS,OAAO,GAAG,MAAM+H,UAAU,CAACwK,WAAW,CAAC/Q,KAAK,CAAC;QACzD,IAAI,CAAC6O,OAAO,CAACS,GAAG,CAAC9U,KAAK,EAAEuW,WAAW,CAAC;MACxC;MACAvW,KAAK,GAAG+G,QAAQ;MAChBwP,WAAW,CAAC/Q,KAAK,CAACe,IAAI,CAACQ,QAAQ,CAAC;IACpC,CAAC,MACI;MACD,IAAI/I,SAAS,EAAE;QACX,MAAMwY,QAAQ,GAAG,IAAI,CAACnC,OAAO,CAACvJ,GAAG,CAAC9K,KAAK,CAAC;QACxC,IAAIwW,QAAQ,IAAIA,QAAQ,CAAChR,KAAK,KAAKvB,SAAS,EAAE;UAC1CuF,4BAA4B,CAAC,CAAC;QAClC;MACJ;IACJ;IACA,IAAI,CAAC6K,OAAO,CAACS,GAAG,CAAC9U,KAAK,EAAEgV,MAAM,CAAC;EACnC;EACAgB,OAAOA,CAAChW,KAAK,EAAEgV,MAAM,EAAE;IACnB,MAAMG,YAAY,GAAGjY,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI;MACA,IAAI8X,MAAM,CAACvR,KAAK,KAAKqQ,QAAQ,EAAE;QAC3BxK,0BAA0B,CAACvJ,SAAS,CAACC,KAAK,CAAC,CAAC;MAChD,CAAC,MACI,IAAIgV,MAAM,CAACvR,KAAK,KAAKoQ,OAAO,EAAE;QAC/BmB,MAAM,CAACvR,KAAK,GAAGqQ,QAAQ;QACvB,IAAI9V,SAAS,EAAE;UACXyJ,4BAA4B,CAAC,IAAI,EAAEzH,KAAK,EAAE,MAAM;YAC5CiH,iCAAiC,CAACjH,KAAK,CAAC;YACxCgV,MAAM,CAACvR,KAAK,GAAGuR,MAAM,CAAChR,OAAO,CAAC,CAAC;YAC/BkD,kCAAkC,CAAC8N,MAAM,CAACvR,KAAK,CAAC;UACpD,CAAC,CAAC;QACN,CAAC,MACI;UACDuR,MAAM,CAACvR,KAAK,GAAGuR,MAAM,CAAChR,OAAO,CAAC,CAAC;QACnC;MACJ;MACA,IAAI,OAAOgR,MAAM,CAACvR,KAAK,KAAK,QAAQ,IAAIuR,MAAM,CAACvR,KAAK,IAAIgT,YAAY,CAACzB,MAAM,CAACvR,KAAK,CAAC,EAAE;QAChF,IAAI,CAAC8Q,iBAAiB,CAAC9B,GAAG,CAACuC,MAAM,CAACvR,KAAK,CAAC;MAC5C;MACA,OAAOuR,MAAM,CAACvR,KAAK;IACvB,CAAC,SACO;MACJvG,iBAAiB,CAACiY,YAAY,CAAC;IACnC;EACJ;EACAW,oBAAoBA,CAACjR,GAAG,EAAE;IACtB,IAAI,CAACA,GAAG,CAACd,UAAU,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,MAAMA,UAAU,GAAGzC,iBAAiB,CAACuD,GAAG,CAACd,UAAU,CAAC;IACpD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MAChC,OAAOA,UAAU,KAAK,KAAK,IAAI,IAAI,CAACqQ,MAAM,CAAC5B,GAAG,CAACzO,UAAU,CAAC;IAC9D,CAAC,MACI;MACD,OAAO,IAAI,CAAC4Q,gBAAgB,CAACnC,GAAG,CAACzO,UAAU,CAAC;IAChD;EACJ;EACA0R,eAAeA,CAAC9N,QAAQ,EAAE;IACtB,MAAM+O,YAAY,GAAG,IAAI,CAAClC,eAAe,CAACtV,OAAO,CAACyI,QAAQ,CAAC;IAC3D,IAAI+O,YAAY,KAAK,CAAC,CAAC,EAAE;MACrB,IAAI,CAAClC,eAAe,CAACrO,MAAM,CAACuQ,YAAY,EAAE,CAAC,CAAC;IAChD;EACJ;AACJ;AACA,SAASX,iCAAiCA,CAAC/V,KAAK,EAAE;EAC9C;EACA,MAAMsK,aAAa,GAAG/F,gBAAgB,CAACvE,KAAK,CAAC;EAC7C,MAAMgE,OAAO,GAAGsG,aAAa,KAAK,IAAI,GAAGA,aAAa,CAACtG,OAAO,GAAGoJ,aAAa,CAACpN,KAAK,CAAC;EACrF,IAAIgE,OAAO,KAAK,IAAI,EAAE;IAClB,OAAOA,OAAO;EAClB;EACA;EACA;EACA,IAAIhE,KAAK,YAAYoF,cAAc,EAAE;IACjC,MAAM,IAAI/H,YAAY,CAAC,GAAG,CAAC,gDAAgDW,SAAS,IAAI,SAAS+B,SAAS,CAACC,KAAK,CAAC,iCAAiC,CAAC;EACvJ;EACA;EACA,IAAIA,KAAK,YAAY2W,QAAQ,EAAE;IAC3B,OAAOC,+BAA+B,CAAC5W,KAAK,CAAC;EACjD;EACA;EACA,MAAM,IAAI3C,YAAY,CAAC,GAAG,CAAC,gDAAgDW,SAAS,IAAI,aAAa,CAAC;AAC1G;AACA,SAAS4Y,+BAA+BA,CAAC5W,KAAK,EAAE;EAC5C;EACA,MAAM6W,WAAW,GAAG7W,KAAK,CAACT,MAAM;EAChC,IAAIsX,WAAW,GAAG,CAAC,EAAE;IACjB,MAAM,IAAIxZ,YAAY,CAAC,GAAG,CAAC,gDAAgDW,SAAS,IAChF,oCAAoC+B,SAAS,CAACC,KAAK,CAAC,MAAMwO,QAAQ,CAACqI,WAAW,EAAE,GAAG,CAAC,CAACzW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;EAC5G;EACA;EACA;EACA;EACA;EACA;EACA,MAAM0W,sBAAsB,GAAGlS,yBAAyB,CAAC5E,KAAK,CAAC;EAC/D,IAAI8W,sBAAsB,KAAK,IAAI,EAAE;IACjC,OAAO,MAAMA,sBAAsB,CAAC9S,OAAO,CAAChE,KAAK,CAAC;EACtD,CAAC,MACI;IACD,OAAO,MAAM,IAAIA,KAAK,CAAC,CAAC;EAC5B;AACJ;AACA,SAASsW,gBAAgBA,CAACvP,QAAQ,EAAE;EAChC,IAAIqM,eAAe,CAACrM,QAAQ,CAAC,EAAE;IAC3B,OAAOgO,UAAU,CAAC9Q,SAAS,EAAE8C,QAAQ,CAACgK,QAAQ,CAAC;EACnD,CAAC,MACI;IACD,MAAM/M,OAAO,GAAG+S,iBAAiB,CAAChQ,QAAQ,CAAC;IAC3C,OAAOgO,UAAU,CAAC/Q,OAAO,EAAE6P,OAAO,CAAC;EACvC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASkD,iBAAiBA,CAAChQ,QAAQ,EAAE2C,YAAY,EAAErF,SAAS,EAAE;EAC1D,IAAIL,OAAO,GAAGC,SAAS;EACvB,IAAIjG,SAAS,IAAI6J,sBAAsB,CAACd,QAAQ,CAAC,EAAE;IAC/C0C,yBAAyB,CAACxF,SAAS,EAAEI,SAAS,EAAE0C,QAAQ,CAAC;EAC7D;EACA,IAAIoM,cAAc,CAACpM,QAAQ,CAAC,EAAE;IAC1B,MAAMiQ,iBAAiB,GAAG1V,iBAAiB,CAACyF,QAAQ,CAAC;IACrD,OAAOqG,aAAa,CAAC4J,iBAAiB,CAAC,IAAIjB,iCAAiC,CAACiB,iBAAiB,CAAC;EACnG,CAAC,MACI;IACD,IAAI5D,eAAe,CAACrM,QAAQ,CAAC,EAAE;MAC3B/C,OAAO,GAAGA,CAAA,KAAM1C,iBAAiB,CAACyF,QAAQ,CAACgK,QAAQ,CAAC;IACxD,CAAC,MACI,IAAIsC,iBAAiB,CAACtM,QAAQ,CAAC,EAAE;MAClC/C,OAAO,GAAGA,CAAA,KAAM+C,QAAQ,CAACgM,UAAU,CAAC,GAAGhH,UAAU,CAAChF,QAAQ,CAAC4L,IAAI,IAAI,EAAE,CAAC,CAAC;IAC3E,CAAC,MACI,IAAIW,kBAAkB,CAACvM,QAAQ,CAAC,EAAE;MACnC/C,OAAO,GAAGA,CAAA,KAAMyH,QAAQ,CAACnK,iBAAiB,CAACyF,QAAQ,CAAC2M,WAAW,CAAC,CAAC;IACrE,CAAC,MACI;MACD,MAAMH,QAAQ,GAAGjS,iBAAiB,CAACyF,QAAQ,KACtCA,QAAQ,CAACyM,QAAQ,IAAIzM,QAAQ,CAACD,OAAO,CAAC,CAAC;MAC5C,IAAI9I,SAAS,IAAI,CAACuV,QAAQ,EAAE;QACxB9J,yBAAyB,CAACC,YAAY,EAAErF,SAAS,EAAE0C,QAAQ,CAAC;MAChE;MACA,IAAIkQ,OAAO,CAAClQ,QAAQ,CAAC,EAAE;QACnB/C,OAAO,GAAGA,CAAA,KAAM,IAAIuP,QAAQ,CAAC,GAAGxH,UAAU,CAAChF,QAAQ,CAAC4L,IAAI,CAAC,CAAC;MAC9D,CAAC,MACI;QACD,OAAOvF,aAAa,CAACmG,QAAQ,CAAC,IAAIwC,iCAAiC,CAACxC,QAAQ,CAAC;MACjF;IACJ;EACJ;EACA,OAAOvP,OAAO;AAClB;AACA,SAASkR,kBAAkBA,CAACxN,QAAQ,EAAE;EAClC,IAAIA,QAAQ,CAAC+M,SAAS,EAAE;IACpB,MAAM,IAAIpX,YAAY,CAAC,GAAG,CAAC,mDAAmDW,SAAS,IAAI,sCAAsC,CAAC;EACtI;AACJ;AACA,SAAS+W,UAAUA,CAAC/Q,OAAO,EAAEP,KAAK,EAAE+B,KAAK,GAAG,KAAK,EAAE;EAC/C,OAAO;IACHxB,OAAO,EAAEA,OAAO;IAChBP,KAAK,EAAEA,KAAK;IACZ+B,KAAK,EAAEA,KAAK,GAAG,EAAE,GAAGvB;EACxB,CAAC;AACL;AACA,SAASgT,OAAOA,CAACxT,KAAK,EAAE;EACpB,OAAO,CAAC,CAACA,KAAK,CAACkP,IAAI;AACvB;AACA,SAAS8D,YAAYA,CAAChT,KAAK,EAAE;EACzB,OAAQA,KAAK,KAAK,IAAI,IAClB,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,CAAC2R,WAAW,KAAK,UAAU;AAC/C;AACA,SAASS,qBAAqBA,CAACpS,KAAK,EAAE;EAClC,OAAQ,OAAOA,KAAK,KAAK,UAAU,IAC9B,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC6B,cAAc,KAAK,gBAAiB;AAChF;AACA,SAASsP,qBAAqBA,CAACvQ,SAAS,EAAE5C,EAAE,EAAE;EAC1C,KAAK,MAAMsF,QAAQ,IAAI1C,SAAS,EAAE;IAC9B,IAAIpE,KAAK,CAACC,OAAO,CAAC6G,QAAQ,CAAC,EAAE;MACzB6N,qBAAqB,CAAC7N,QAAQ,EAAEtF,EAAE,CAAC;IACvC,CAAC,MACI,IAAIsF,QAAQ,IAAIc,sBAAsB,CAACd,QAAQ,CAAC,EAAE;MACnD6N,qBAAqB,CAAC7N,QAAQ,CAACe,UAAU,EAAErG,EAAE,CAAC;IAClD,CAAC,MACI;MACDA,EAAE,CAACsF,QAAQ,CAAC;IAChB;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmQ,qBAAqBA,CAACxP,QAAQ,EAAEjG,EAAE,EAAE;EACzC,IAAI0V,gBAAgB;EACpB,IAAIzP,QAAQ,YAAYwM,UAAU,EAAE;IAChCgB,kBAAkB,CAACxN,QAAQ,CAAC;IAC5ByP,gBAAgB,GAAGzP,QAAQ;EAC/B,CAAC,MACI;IACDyP,gBAAgB,GAAG,IAAIxM,kBAAkB,CAACjD,QAAQ,CAAC;EACvD;EACA,IAAI0P,2BAA2B;EAC/B,IAAIpZ,SAAS,EAAE;IACXoZ,2BAA2B,GAAGzR,0BAA0B,CAAC;MAAE+B,QAAQ;MAAE1H,KAAK,EAAE;IAAK,CAAC,CAAC;EACvF;EACA,MAAMqX,YAAY,GAAG3a,kBAAkB,CAACya,gBAAgB,CAAC;EACzD,MAAMvB,4BAA4B,GAAG1L,uBAAuB,CAACjG,SAAS,CAAC;EACvE,IAAI;IACA,OAAOxC,EAAE,CAAC,CAAC;EACf,CAAC,SACO;IACJ/E,kBAAkB,CAAC2a,YAAY,CAAC;IAChCrZ,SAAS,IAAI2H,0BAA0B,CAACyR,2BAA2B,CAAC;IACpElN,uBAAuB,CAAC0L,4BAA4B,CAAC;EACzD;AACJ;AACA;AACA;AACA;AACA,SAAS0B,oBAAoBA,CAAA,EAAG;EAC5B,OAAOrN,uBAAuB,CAAC,CAAC,KAAKhG,SAAS,IAAIxH,kBAAkB,CAAC,CAAC,IAAI,IAAI;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8a,wBAAwBA,CAACC,OAAO,EAAE;EACvC;EACA;EACA,IAAI,CAACF,oBAAoB,CAAC,CAAC,EAAE;IACzB,MAAM,IAAIja,YAAY,CAAC,CAAC,GAAG,CAAC,kDAAkDW,SAAS,IACnFwZ,OAAO,CAACnX,IAAI,GACR,iKAAiK,CAAC;EAC9K;AACJ;;AAEA;AACA;AACA;AACA,MAAMoX,IAAI,GAAG,CAAC;AACd,MAAMC,KAAK,GAAG,CAAC;AACf;AACA,MAAMC,KAAK,GAAG,CAAC;AACf,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,IAAI,GAAG,CAAC;AACd,MAAMC,MAAM,GAAG,CAAC;AAChB;AACA,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAG,EAAE;AACrB;AACA,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,0BAA0B,GAAG,EAAE;AACrC,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,EAAE,GAAG,EAAE;AACb,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,0BAA0B,GAAG,EAAE;AACrC,MAAMC,6BAA6B,GAAG,EAAE;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,EAAE;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,WAAW,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,EAAE;;AAElC;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACjW,KAAK,EAAE;EACpB,OAAOxD,KAAK,CAACC,OAAO,CAACuD,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC2V,IAAI,CAAC,KAAK,QAAQ;AAClE;AACA;AACA;AACA;AACA;AACA,SAASO,YAAYA,CAAClW,KAAK,EAAE;EACzB,OAAOxD,KAAK,CAACC,OAAO,CAACuD,KAAK,CAAC,IAAIA,KAAK,CAAC2V,IAAI,CAAC,KAAK,IAAI;AACvD;AACA,SAASQ,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,OAAO,CAACA,KAAK,CAACxS,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC;AACnE;AACA,SAASyS,eAAeA,CAACD,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC;AACrC;AACA,SAASC,eAAeA,CAACH,KAAK,EAAE;EAC5B,OAAO,CAACA,KAAK,CAACxS,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC,CAAC;AACpE;AACA,SAAS4S,cAAcA,CAACpV,GAAG,EAAE;EACzB,OAAO,CAAC,CAACA,GAAG,CAACqV,QAAQ;AACzB;AACA,SAASC,UAAUA,CAACva,MAAM,EAAE;EACxB;EACA,OAAO,CAACA,MAAM,CAAC+X,KAAK,CAAC,GAAG,GAAG,CAAC,6BAA6B,CAAC;AAC9D;AACA,SAASyC,iBAAiBA,CAACP,KAAK,EAAE;EAC9B,OAAO,CAACA,KAAK,CAACtY,IAAI,GAAG,EAAE,CAAC,gCAAgC,EAAE,CAAC;AAC/D;AACA,SAAS8Y,OAAOA,CAACC,KAAK,EAAE;EACpB,OAAO,CAACA,KAAK,CAAC3C,KAAK,CAAC,GAAG,EAAE,CAAC,8BAA8B,EAAE,CAAC;AAC/D;AACA,SAAS4C,WAAWA,CAACD,KAAK,EAAE;EACxB;EACA,OAAO,CAACA,KAAK,CAAC3C,KAAK,CAAC,GAAG,GAAG,CAAC,gCAAgC,GAAG,CAAC;AACnE;;AAEA;AACA;AACA,SAAS6C,mBAAmBA,CAACX,KAAK,EAAES,KAAK,EAAE;EACvCG,mBAAmB,CAACZ,KAAK,EAAES,KAAK,CAAC5C,KAAK,CAAC,CAAC;AAC5C;AACA,SAAS+C,mBAAmBA,CAACZ,KAAK,EAAEa,KAAK,EAAE;EACvCC,WAAW,CAACd,KAAK,CAAC;EAClB,MAAMe,KAAK,GAAGF,KAAK,CAACG,IAAI;EACxB,KAAK,IAAIpU,CAAC,GAAG0S,aAAa,EAAE1S,CAAC,GAAGmU,KAAK,CAACrb,MAAM,EAAEkH,CAAC,EAAE,EAAE;IAC/C,IAAImU,KAAK,CAACnU,CAAC,CAAC,KAAKoT,KAAK,EAAE;MACpB;IACJ;EACJ;EACAhY,UAAU,CAAC,2CAA2C,CAAC;AAC3D;AACA,SAAS8Y,WAAWA,CAACd,KAAK,EAAE;EACxBhX,aAAa,CAACgX,KAAK,EAAE,uBAAuB,CAAC;EAC7C,IAAI,EAAEA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC/Z,cAAc,CAAC,sBAAsB,CAAC,CAAC,EAAE;IACvF+B,UAAU,CAAC,0BAA0B,GAAGgY,KAAK,CAAC;EAClD;AACJ;AACA,SAASiB,UAAUA,CAACC,IAAI,EAAE;EACtBlY,aAAa,CAACkY,IAAI,EAAE,6BAA6B,CAAC;EAClD,IAAI,EAAE,OAAOA,IAAI,CAACC,qBAAqB,KAAK,QAAQ,CAAC,EAAE;IACnDnZ,UAAU,CAAC,6BAA6B,CAAC;EAC7C;AACJ;AACA,SAASoZ,mBAAmBA,CAACtZ,MAAM,EAAEC,GAAG,GAAG,wEAAwE,EAAE;EACjH,IAAI,CAAC0O,eAAe,CAAC3O,MAAM,CAAC,EAAE;IAC1BE,UAAU,CAACD,GAAG,CAAC;EACnB;AACJ;AACA,SAASsZ,kBAAkBA,CAACvZ,MAAM,EAAEC,GAAG,GAAG,uEAAuE,EAAE;EAC/G,IAAI,CAACuO,cAAc,CAACxO,MAAM,CAAC,EAAE;IACzBE,UAAU,CAACD,GAAG,CAAC;EACnB;AACJ;AACA,SAASuZ,eAAeA,CAACtB,KAAK,EAAE;EAC5BhX,aAAa,CAACgX,KAAK,EAAE,4BAA4B,CAAC;EAClDhX,aAAa,CAACgX,KAAK,CAAC1F,MAAM,EAAE,mCAAmC,CAAC;AACpE;AACA,SAASiH,gBAAgBA,CAAC3X,KAAK,EAAE;EAC7BZ,aAAa,CAACY,KAAK,EAAE,4BAA4B,CAAC;EAClDpB,WAAW,CAACsX,YAAY,CAAClW,KAAK,CAAC,EAAE,IAAI,EAAE,sBAAsB,CAAC;AAClE;AACA,SAAS4X,sBAAsBA,CAAC5X,KAAK,EAAE;EACnCA,KAAK,IAAIpB,WAAW,CAACqX,OAAO,CAACjW,KAAK,CAAC,EAAE,IAAI,EAAE,sCAAsC,CAAC;AACtF;AACA,SAAS6X,WAAWA,CAAC7X,KAAK,EAAE;EACxBZ,aAAa,CAACY,KAAK,EAAE,uBAAuB,CAAC;EAC7CpB,WAAW,CAACqX,OAAO,CAACjW,KAAK,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC;AACxD;AACA,SAAS8X,qBAAqBA,CAACb,KAAK,EAAEc,UAAU,EAAE;EAC9CnZ,WAAW,CAACqY,KAAK,CAACe,eAAe,EAAE,IAAI,EAAED,UAAU,IAAI,6CAA6C,CAAC;AACzG;AACA,SAASE,qBAAqBA,CAAChB,KAAK,EAAEc,UAAU,EAAE;EAC9CnZ,WAAW,CAACqY,KAAK,CAACiB,eAAe,EAAE,IAAI,EAAE,6CAA6C,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAC5O,GAAG,EAAE;EAC7B,IAAIA,GAAG,CAACzL,IAAI,KAAK0C,SAAS,IAAI+I,GAAG,CAAC6O,SAAS,IAAI5X,SAAS,IAAI+I,GAAG,CAAC8O,MAAM,KAAK7X,SAAS,EAAE;IAClFpC,UAAU,CAAC,gGAAgG,CAAC;EAChH;AACJ;AACA,SAASka,sBAAsBA,CAACrB,KAAK,EAAEpX,KAAK,EAAE;EAC1C0Y,aAAa,CAAC7C,aAAa,EAAEuB,KAAK,CAACuB,iBAAiB,EAAE3Y,KAAK,CAAC;AAChE;AACA,SAAS4Y,yBAAyBA,CAAC5B,KAAK,EAAEhX,KAAK,EAAE;EAC7C,MAAMoX,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACtB0B,aAAa,CAACtB,KAAK,CAACyB,iBAAiB,EAAE7B,KAAK,CAAC/a,MAAM,EAAE+D,KAAK,CAAC;AAC/D;AACA,SAAS0Y,aAAaA,CAACI,KAAK,EAAEC,KAAK,EAAE/Y,KAAK,EAAE;EACxC,IAAI,EAAE8Y,KAAK,IAAI9Y,KAAK,IAAIA,KAAK,GAAG+Y,KAAK,CAAC,EAAE;IACpCxa,UAAU,CAAC,iCAAiCua,KAAK,OAAO9Y,KAAK,MAAM+Y,KAAK,GAAG,CAAC;EAChF;AACJ;AACA,SAASC,qBAAqBA,CAAChC,KAAK,EAAEkB,UAAU,EAAE;EAC9C3Y,aAAa,CAACyX,KAAK,CAAC9B,0BAA0B,CAAC,EAAE,+BAA+B,CAAC;EACjF3V,aAAa,CAACyX,KAAK,CAAC9B,0BAA0B,CAAC,CAACV,MAAM,CAAC,CAACyE,UAAU,EAAE,qFAAqF,CAAC;AAC9J;AACA,SAASC,gBAAgBA,CAAClC,KAAK,EAAEkB,UAAU,EAAE;EACzC3Y,aAAa,CAACyX,KAAK,EAAE,0EAA0E,CAAC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,kBAAkBA,CAACnC,KAAK,EAAEoC,aAAa,EAAE;EAC9CR,yBAAyB,CAAC5B,KAAK,EAAEoC,aAAa,CAAC;EAC/CR,yBAAyB,CAAC5B,KAAK,EAAEoC,aAAa,GAAG,CAAC,CAAC,+BAA+B,CAAC;EACnFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;EACtFhb,YAAY,CAAC4Y,KAAK,CAACoC,aAAa,GAAG,CAAC,CAAC,gCAAgC,EAAE,+CAA+C,CAAC;AAC3H;AAEA,MAAMC,aAAa,GAAG,KAAK;AAC3B,MAAMC,iBAAiB,GAAG,MAAM;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACpZ,KAAK,EAAE;EACxB,OAAOxD,KAAK,CAACC,OAAO,CAACuD,KAAK,CAAC,EAAE;IACzBA,KAAK,GAAGA,KAAK,CAACgU,IAAI,CAAC;EACvB;EACA,OAAOhU,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASqZ,WAAWA,CAACrZ,KAAK,EAAE;EACxB,OAAOxD,KAAK,CAACC,OAAO,CAACuD,KAAK,CAAC,EAAE;IACzB;IACA;IACA,IAAI,OAAOA,KAAK,CAAC2V,IAAI,CAAC,KAAK,QAAQ,EAC/B,OAAO3V,KAAK;IAChBA,KAAK,GAAGA,KAAK,CAACgU,IAAI,CAAC;EACvB;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,SAASsF,gBAAgBA,CAACzZ,KAAK,EAAEgX,KAAK,EAAE;EACpCtc,SAAS,IAAIoF,kBAAkB,CAACkX,KAAK,EAAEhX,KAAK,CAAC;EAC7CtF,SAAS,IAAIkE,wBAAwB,CAACoB,KAAK,EAAE6V,aAAa,EAAE,mCAAmC,CAAC;EAChG,OAAO0D,WAAW,CAACvC,KAAK,CAAChX,KAAK,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0Z,gBAAgBA,CAACnD,KAAK,EAAES,KAAK,EAAE;EACpCtc,SAAS,IAAIwc,mBAAmB,CAACX,KAAK,EAAES,KAAK,CAAC;EAC9Ctc,SAAS,IAAIoF,kBAAkB,CAACkX,KAAK,EAAET,KAAK,CAACvW,KAAK,CAAC;EACnD,MAAMN,IAAI,GAAG6Z,WAAW,CAACvC,KAAK,CAACT,KAAK,CAACvW,KAAK,CAAC,CAAC;EAC5C,OAAON,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASia,sBAAsBA,CAACpD,KAAK,EAAES,KAAK,EAAE;EAC1C,MAAMhX,KAAK,GAAGuW,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,KAAK,CAACvW,KAAK;EAC/C,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;IACdtF,SAAS,IAAIwc,mBAAmB,CAACX,KAAK,EAAES,KAAK,CAAC;IAC9C,MAAMtX,IAAI,GAAG6Z,WAAW,CAACvC,KAAK,CAAChX,KAAK,CAAC,CAAC;IACtC,OAAON,IAAI;EACf;EACA,OAAO,IAAI;AACf;AACA;AACA,SAASka,QAAQA,CAACxC,KAAK,EAAEpX,KAAK,EAAE;EAC5BtF,SAAS,IAAI2E,iBAAiB,CAACW,KAAK,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;EAClEtF,SAAS,IAAI0E,cAAc,CAACY,KAAK,EAAEoX,KAAK,CAACG,IAAI,CAACtb,MAAM,EAAE,uBAAuB,CAAC;EAC9E,MAAMsa,KAAK,GAAGa,KAAK,CAACG,IAAI,CAACvX,KAAK,CAAC;EAC/BtF,SAAS,IAAI6b,KAAK,KAAK,IAAI,IAAIc,WAAW,CAACd,KAAK,CAAC;EACjD,OAAOA,KAAK;AAChB;AACA;AACA,SAASsD,IAAIA,CAACC,IAAI,EAAE9Z,KAAK,EAAE;EACvBtF,SAAS,IAAIoF,kBAAkB,CAACga,IAAI,EAAE9Z,KAAK,CAAC;EAC5C,OAAO8Z,IAAI,CAAC9Z,KAAK,CAAC;AACtB;AACA;AACA,SAAS+Z,KAAKA,CAAC3C,KAAK,EAAEJ,KAAK,EAAEhX,KAAK,EAAEG,KAAK,EAAE;EACvC;EACA;EACA,IAAIH,KAAK,IAAIoX,KAAK,CAACG,IAAI,CAACtb,MAAM,EAAE;IAC5Bmb,KAAK,CAACG,IAAI,CAACvX,KAAK,CAAC,GAAG,IAAI;IACxBoX,KAAK,CAAC4C,SAAS,CAACha,KAAK,CAAC,GAAG,IAAI;EACjC;EACAgX,KAAK,CAAChX,KAAK,CAAC,GAAGG,KAAK;AACxB;AACA,SAAS8Z,wBAAwBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACnD;EACAzf,SAAS,IAAIoF,kBAAkB,CAACqa,QAAQ,EAAED,SAAS,CAAC;EACpD,MAAME,SAAS,GAAGD,QAAQ,CAACD,SAAS,CAAC;EACrC,MAAMlD,KAAK,GAAGZ,OAAO,CAACgE,SAAS,CAAC,GAAGA,SAAS,GAAGA,SAAS,CAACjG,IAAI,CAAC;EAC9D,OAAO6C,KAAK;AAChB;AACA;AACA,SAASqD,cAAcA,CAACP,IAAI,EAAE;EAC1B,OAAO,CAACA,IAAI,CAACzF,KAAK,CAAC,GAAG,CAAC,CAAC,mCAAmC,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiG,4BAA4BA,CAACR,IAAI,EAAE;EACxC,OAAO,CAACA,IAAI,CAACzF,KAAK,CAAC,GAAG,GAAG,CAAC,+BAA+B,GAAG,CAAC;AACjE;AACA;AACA,SAASkG,uBAAuBA,CAACT,IAAI,EAAE;EACnC,OAAOzD,YAAY,CAACyD,IAAI,CAACxF,MAAM,CAAC,CAAC;AACrC;AACA,SAASkG,WAAWA,CAACC,MAAM,EAAEza,KAAK,EAAE;EAChC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKW,SAAS,EACrC,OAAO,IAAI;EACfjG,SAAS,IAAIoF,kBAAkB,CAAC2a,MAAM,EAAEza,KAAK,CAAC;EAC9C,OAAOya,MAAM,CAACza,KAAK,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA,SAAS0a,sBAAsBA,CAAC1D,KAAK,EAAE;EACnCA,KAAK,CAAC5B,mBAAmB,CAAC,GAAG,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,SAASuF,kBAAkBA,CAAC3D,KAAK,EAAE;EAC/B,IAAIA,KAAK,CAAC3C,KAAK,CAAC,GAAG,IAAI,CAAC,8BAA8B;IAClD;EACJ;EACA2C,KAAK,CAAC3C,KAAK,CAAC,IAAI,IAAI,CAAC;EACrB,IAAIiG,4BAA4B,CAACtD,KAAK,CAAC,EAAE;IACrC4D,yBAAyB,CAAC5D,KAAK,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6D,WAAWA,CAACC,YAAY,EAAEC,WAAW,EAAE;EAC5C,OAAOD,YAAY,GAAG,CAAC,EAAE;IACrBpgB,SAAS,IACL6E,aAAa,CAACwb,WAAW,CAAC9F,gBAAgB,CAAC,EAAE,wEAAwE,CAAC;IAC1H8F,WAAW,GAAGA,WAAW,CAAC9F,gBAAgB,CAAC;IAC3C6F,YAAY,EAAE;EAClB;EACA,OAAOC,WAAW;AACtB;AACA,SAASC,0BAA0BA,CAAChE,KAAK,EAAE;EACvC,OAAO,CAAC,EAAEA,KAAK,CAAC3C,KAAK,CAAC,IAAI,IAAI,CAAC,+BAA+B,IAAI,CAAC,wCAAwC,IACvG2C,KAAK,CAACrB,0BAA0B,CAAC,EAAEsF,KAAK,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASC,oCAAoCA,CAAClE,KAAK,EAAE;EACjDA,KAAK,CAACnC,WAAW,CAAC,CAACsG,wBAAwB,EAAEC,MAAM,CAAC,CAAC,CAAC,qCAAqC,CAAC;EAC5F,IAAIpE,KAAK,CAAC3C,KAAK,CAAC,GAAG,EAAE,CAAC,wBAAwB;IAC1C2C,KAAK,CAAC3C,KAAK,CAAC,IAAI,IAAI,CAAC;EACzB;EACA,IAAI2G,0BAA0B,CAAChE,KAAK,CAAC,EAAE;IACnC4D,yBAAyB,CAAC5D,KAAK,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4D,yBAAyBA,CAAC5D,KAAK,EAAE;EACtCA,KAAK,CAACnC,WAAW,CAAC,CAACsG,wBAAwB,EAAEC,MAAM,CAAC,CAAC,CAAC,kDAAkD,CAAC;EACzG,IAAIvK,MAAM,GAAGwK,cAAc,CAACrE,KAAK,CAAC;EAClC,OAAOnG,MAAM,KAAK,IAAI,EAAE;IACpB;IACA;IACA,IAAIA,MAAM,CAACwD,KAAK,CAAC,GAAG,IAAI,CAAC,yCAAyC;MAC9D;IACJ;IACAxD,MAAM,CAACwD,KAAK,CAAC,IAAI,IAAI,CAAC;IACtB,IAAI,CAACiG,4BAA4B,CAACzJ,MAAM,CAAC,EAAE;MACvC;IACJ;IACAA,MAAM,GAAGwK,cAAc,CAACxK,MAAM,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA,SAASyK,mBAAmBA,CAACtE,KAAK,EAAEuE,iBAAiB,EAAE;EACnD,IAAItE,WAAW,CAACD,KAAK,CAAC,EAAE;IACpB,MAAM,IAAIjd,YAAY,CAAC,GAAG,CAAC,+CAA+CW,SAAS,IAAI,kCAAkC,CAAC;EAC9H;EACA,IAAIsc,KAAK,CAACxB,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAClCwB,KAAK,CAACxB,gBAAgB,CAAC,GAAG,EAAE;EAChC;EACAwB,KAAK,CAACxB,gBAAgB,CAAC,CAACvS,IAAI,CAACsY,iBAAiB,CAAC;AACnD;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACxE,KAAK,EAAEuE,iBAAiB,EAAE;EACpD,IAAIvE,KAAK,CAACxB,gBAAgB,CAAC,KAAK,IAAI,EAChC;EACJ,MAAMpC,YAAY,GAAG4D,KAAK,CAACxB,gBAAgB,CAAC,CAAC5Z,OAAO,CAAC2f,iBAAiB,CAAC;EACvE,IAAInI,YAAY,KAAK,CAAC,CAAC,EAAE;IACrB4D,KAAK,CAACxB,gBAAgB,CAAC,CAAC3S,MAAM,CAACuQ,YAAY,EAAE,CAAC,CAAC;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASiI,cAAcA,CAACrE,KAAK,EAAE;EAC3Btc,SAAS,IAAIsd,WAAW,CAAChB,KAAK,CAAC;EAC/B,MAAMnG,MAAM,GAAGmG,KAAK,CAAC1C,MAAM,CAAC;EAC5B,OAAO+B,YAAY,CAACxF,MAAM,CAAC,GAAGA,MAAM,CAACyD,MAAM,CAAC,GAAGzD,MAAM;AACzD;AACA,SAAS4K,uBAAuBA,CAAC3B,IAAI,EAAE;EACnC;EACA,OAAQA,IAAI,CAACpF,OAAO,CAAC,KAAK,EAAE;AAChC;AACA,SAASgH,uBAAuBA,CAACtE,KAAK,EAAE;EACpC,OAAQA,KAAK,CAACuE,OAAO,KAAK,EAAE;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACxE,KAAK,EAAEJ,KAAK,EAAE1U,OAAO,EAAEuZ,SAAS,EAAE;EAC/D,MAAMC,QAAQ,GAAGL,uBAAuB,CAACzE,KAAK,CAAC;EAC/C;EACA;EACA;EACA;EACAtc,SAAS,IACL6E,aAAa,CAAC+C,OAAO,EAAE,6EAA6E,CAAC;EACzGwZ,QAAQ,CAAC7Y,IAAI,CAACX,OAAO,CAAC;EACtB,IAAI8U,KAAK,CAACe,eAAe,EAAE;IACvBuD,uBAAuB,CAACtE,KAAK,CAAC,CAACnU,IAAI,CAAC4Y,SAAS,EAAEC,QAAQ,CAAC7f,MAAM,GAAG,CAAC,CAAC;EACvE,CAAC,MACI;IACD;IACA;IACA,IAAIvB,SAAS,EAAE;MACXmB,MAAM,CAAC0Q,MAAM,CAACmP,uBAAuB,CAACtE,KAAK,CAAC,CAAC;IACjD;EACJ;AACJ;AAEA,MAAM2E,gBAAgB,GAAG;EACrBC,MAAM,eAAEC,YAAY,CAAC,IAAI,CAAC;EAC1BC,eAAe,EAAE,IAAI;EACrBC,sBAAsB,EAAE;AAC5B,CAAC;AACD,IAAIC,kBAAkB,gBACrB,UAAUA,kBAAkB,EAAE;EAC3BA,kBAAkB,CAACA,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACzDA,kBAAkB,CAACA,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACvEA,kBAAkB,CAACA,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAAC,OAHzEA,kBAAkB;AAI7B,CAAC,CAAEA,kBAAkB,IAA0B,CAAC,CAAE,CAL5B;AAMtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,OAAOR,gBAAgB,CAACC,MAAM,CAACQ,iBAAiB;AACpD;AACA,SAASC,yBAAyBA,CAAA,EAAG;EACjCV,gBAAgB,CAACC,MAAM,CAACQ,iBAAiB,EAAE;AAC/C;AACA,SAASE,yBAAyBA,CAAA,EAAG;EACjCX,gBAAgB,CAACC,MAAM,CAACQ,iBAAiB,EAAE;AAC/C;AACA,SAASG,kBAAkBA,CAAA,EAAG;EAC1B,OAAOZ,gBAAgB,CAACG,eAAe;AAC3C;AACA;AACA;AACA;AACA;AACA,SAASU,sBAAsBA,CAAA,EAAG;EAC9B,OAAOb,gBAAgB,CAACI,sBAAsB,KAAK,IAAI;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,wBAAwBA,CAACtG,KAAK,EAAE;EACrC,OAAOwF,gBAAgB,CAACI,sBAAsB,KAAK5F,KAAK;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuG,gBAAgBA,CAAA,EAAG;EACxBf,gBAAgB,CAACG,eAAe,GAAG,IAAI;AAC3C;AACA;AACA;AACA;AACA;AACA,SAASa,uBAAuBA,CAACxG,KAAK,EAAE;EACpCwF,gBAAgB,CAACI,sBAAsB,GAAG5F,KAAK;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyG,iBAAiBA,CAAA,EAAG;EACzBjB,gBAAgB,CAACG,eAAe,GAAG,KAAK;AAC5C;AACA;AACA;AACA;AACA,SAASe,uBAAuBA,CAAA,EAAG;EAC/BlB,gBAAgB,CAACI,sBAAsB,GAAG,IAAI;AAClD;AACA;AACA;AACA;AACA,SAASe,QAAQA,CAAA,EAAG;EAChB,OAAOnB,gBAAgB,CAACC,MAAM,CAAChF,KAAK;AACxC;AACA;AACA;AACA;AACA,SAASmG,QAAQA,CAAA,EAAG;EAChB,OAAOpB,gBAAgB,CAACC,MAAM,CAAC5E,KAAK;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgG,aAAaA,CAACC,aAAa,EAAE;EAClCtB,gBAAgB,CAACC,MAAM,CAACsB,YAAY,GAAGD,aAAa;EACpD,OAAOA,aAAa,CAAC1I,OAAO,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4I,WAAWA,CAACpd,KAAK,EAAE;EACxB4b,gBAAgB,CAACC,MAAM,CAACsB,YAAY,GAAG,IAAI;EAC3C,OAAOnd,KAAK;AAChB;AACA,SAASqd,eAAeA,CAAA,EAAG;EACvB,IAAIC,YAAY,GAAGC,4BAA4B,CAAC,CAAC;EACjD,OAAOD,YAAY,KAAK,IAAI,IAAIA,YAAY,CAACxf,IAAI,KAAK,EAAE,CAAC,6BAA6B;IAClFwf,YAAY,GAAGA,YAAY,CAAC5M,MAAM;EACtC;EACA,OAAO4M,YAAY;AACvB;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,OAAO3B,gBAAgB,CAACC,MAAM,CAACyB,YAAY;AAC/C;AACA,SAASE,qBAAqBA,CAAA,EAAG;EAC7B,MAAM3B,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtC,MAAMyB,YAAY,GAAGzB,MAAM,CAACyB,YAAY;EACxC,OAAOzB,MAAM,CAAC4B,QAAQ,GAAGH,YAAY,GAAGA,YAAY,CAAC5M,MAAM;AAC/D;AACA,SAASgN,eAAeA,CAACtH,KAAK,EAAEqH,QAAQ,EAAE;EACtCljB,SAAS,IAAI6b,KAAK,IAAIY,mBAAmB,CAACZ,KAAK,EAAEwF,gBAAgB,CAACC,MAAM,CAAC5E,KAAK,CAAC;EAC/E,MAAM4E,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtCA,MAAM,CAACyB,YAAY,GAAGlH,KAAK;EAC3ByF,MAAM,CAAC4B,QAAQ,GAAGA,QAAQ;AAC9B;AACA,SAASE,oBAAoBA,CAAA,EAAG;EAC5B,OAAO/B,gBAAgB,CAACC,MAAM,CAAC4B,QAAQ;AAC3C;AACA,SAASG,0BAA0BA,CAAA,EAAG;EAClChC,gBAAgB,CAACC,MAAM,CAAC4B,QAAQ,GAAG,KAAK;AAC5C;AACA,SAASI,eAAeA,CAAA,EAAG;EACvB,MAAMV,YAAY,GAAGvB,gBAAgB,CAACC,MAAM,CAACsB,YAAY;EACzD5iB,SAAS,IAAI6E,aAAa,CAAC+d,YAAY,EAAE,+BAA+B,CAAC;EACzE,OAAOA,YAAY;AACvB;AACA,SAASW,sBAAsBA,CAAA,EAAG;EAC9B,CAACvjB,SAAS,IAAI6D,UAAU,CAAC,yCAAyC,CAAC;EACnE,OAAO8d,mBAAmB,KAAKD,kBAAkB,CAAC8B,GAAG;AACzD;AACA,SAASC,0BAA0BA,CAAA,EAAG;EAClC,CAACzjB,SAAS,IAAI6D,UAAU,CAAC,yCAAyC,CAAC;EACnE,OAAO8d,mBAAmB,KAAKD,kBAAkB,CAACgC,UAAU;AAChE;AACA,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EACrC,CAAC5jB,SAAS,IAAI6D,UAAU,CAAC,yCAAyC,CAAC;EACnE8d,mBAAmB,GAAGiC,IAAI;AAC9B;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOjC,kBAAkB;AAC7B;AACA,SAASkC,oBAAoBA,CAACF,IAAI,EAAE;EAChC,MAAMG,IAAI,GAAGnC,kBAAkB;EAC/BA,kBAAkB,GAAGgC,IAAI;EACzB,OAAOG,IAAI;AACf;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB,MAAM1C,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtC,IAAIhc,KAAK,GAAGgc,MAAM,CAAC2C,gBAAgB;EACnC,IAAI3e,KAAK,KAAK,CAAC,CAAC,EAAE;IACdA,KAAK,GAAGgc,MAAM,CAAC2C,gBAAgB,GAAG3C,MAAM,CAAC5E,KAAK,CAACuB,iBAAiB;EACpE;EACA,OAAO3Y,KAAK;AAChB;AACA,SAAS4e,eAAeA,CAAA,EAAG;EACvB,OAAO7C,gBAAgB,CAACC,MAAM,CAAC6C,YAAY;AAC/C;AACA,SAASC,eAAeA,CAAC3e,KAAK,EAAE;EAC5B,OAAQ4b,gBAAgB,CAACC,MAAM,CAAC6C,YAAY,GAAG1e,KAAK;AACxD;AACA,SAAS4e,gBAAgBA,CAAA,EAAG;EACxB,OAAOhD,gBAAgB,CAACC,MAAM,CAAC6C,YAAY,EAAE;AACjD;AACA,SAASG,qBAAqBA,CAAC1T,KAAK,EAAE;EAClC,MAAM0Q,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtC,MAAMhc,KAAK,GAAGgc,MAAM,CAAC6C,YAAY;EACjC7C,MAAM,CAAC6C,YAAY,GAAG7C,MAAM,CAAC6C,YAAY,GAAGvT,KAAK;EACjD,OAAOtL,KAAK;AAChB;AACA,SAASif,aAAaA,CAAA,EAAG;EACrB,OAAOlD,gBAAgB,CAACC,MAAM,CAACkD,MAAM;AACzC;AACA,SAASC,cAAcA,CAACF,aAAa,EAAE;EACnClD,gBAAgB,CAACC,MAAM,CAACkD,MAAM,GAAGD,aAAa;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,6BAA6BA,CAACT,gBAAgB,EAAEU,qBAAqB,EAAE;EAC5E,MAAMrD,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtCA,MAAM,CAAC6C,YAAY,GAAG7C,MAAM,CAAC2C,gBAAgB,GAAGA,gBAAgB;EAChEW,wBAAwB,CAACD,qBAAqB,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAAA,EAAG;EAChC,OAAOxD,gBAAgB,CAACC,MAAM,CAACqD,qBAAqB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACD,qBAAqB,EAAE;EACrDtD,gBAAgB,CAACC,MAAM,CAACqD,qBAAqB,GAAGA,qBAAqB;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,sBAAsBA,CAAClI,KAAK,EAAE;EACnC,MAAM+H,qBAAqB,GAAGtD,gBAAgB,CAACC,MAAM,CAACqD,qBAAqB;EAC3E,OAAOA,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG/H,KAAK,CAAC+H,qBAAqB,CAAC;AAC7E;AACA,SAASI,oBAAoBA,CAAA,EAAG;EAC5B,OAAO1D,gBAAgB,CAACC,MAAM,CAAC0D,iBAAiB;AACpD;AACA,SAASC,oBAAoBA,CAACxf,KAAK,EAAE;EACjC4b,gBAAgB,CAACC,MAAM,CAAC0D,iBAAiB,GAAGvf,KAAK;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,SAASyf,mBAAmBA,CAAC5I,KAAK,EAAE;EAChC,MAAMI,KAAK,GAAGJ,KAAK,CAAC5C,KAAK,CAAC;EAC1B;EACA,IAAIgD,KAAK,CAACnZ,IAAI,KAAK,CAAC,CAAC,0BAA0B;IAC3CvD,SAAS,IAAI6E,aAAa,CAAC6X,KAAK,CAACyI,SAAS,EAAE,kDAAkD,CAAC;IAC/F,OAAOzI,KAAK,CAACyI,SAAS;EAC1B;EACA;EACA;EACA;EACA,IAAIzI,KAAK,CAACnZ,IAAI,KAAK,CAAC,CAAC,2BAA2B;IAC5C,OAAO+Y,KAAK,CAACxC,MAAM,CAAC;EACxB;EACA;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsL,OAAOA,CAAC9I,KAAK,EAAET,KAAK,EAAExS,KAAK,EAAE;EAClCrJ,SAAS,IAAIqd,sBAAsB,CAACf,KAAK,CAAC;EAC1C,IAAIjT,KAAK,GAAG,CAAC,CAAC,oCAAoC;IAC9CrJ,SAAS,IAAIyc,mBAAmB,CAACZ,KAAK,EAAES,KAAK,CAAC5C,KAAK,CAAC,CAAC;IACrD,IAAI2L,WAAW,GAAGxJ,KAAK;IACvB,IAAIyJ,WAAW,GAAGhJ,KAAK;IACvB,OAAO,IAAI,EAAE;MACTtc,SAAS,IAAI6E,aAAa,CAACwgB,WAAW,EAAE,gCAAgC,CAAC;MACzEA,WAAW,GAAGA,WAAW,CAAClP,MAAM;MAChC,IAAIkP,WAAW,KAAK,IAAI,IAAI,EAAEhc,KAAK,GAAG,CAAC,CAAC,+BAA+B,EAAE;QACrEgc,WAAW,GAAGH,mBAAmB,CAACI,WAAW,CAAC;QAC9C,IAAID,WAAW,KAAK,IAAI,EACpB;QACJ;QACA;QACArlB,SAAS,IAAI6E,aAAa,CAACygB,WAAW,EAAE,gCAAgC,CAAC;QACzEA,WAAW,GAAGA,WAAW,CAAC/K,gBAAgB,CAAC;QAC3C;QACA;QACA;QACA,IAAI8K,WAAW,CAAC9hB,IAAI,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC,iCAAiC,EAAE;UACrF;QACJ;MACJ,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAI8hB,WAAW,KAAK,IAAI,EAAE;MACtB;MACA,OAAO,KAAK;IAChB,CAAC,MACI;MACDxJ,KAAK,GAAGwJ,WAAW;MACnB/I,KAAK,GAAGgJ,WAAW;IACvB;EACJ;EACAtlB,SAAS,IAAIwc,mBAAmB,CAACX,KAAK,EAAES,KAAK,CAAC;EAC9C,MAAMgF,MAAM,GAAID,gBAAgB,CAACC,MAAM,GAAGiE,WAAW,CAAC,CAAE;EACxDjE,MAAM,CAACyB,YAAY,GAAGlH,KAAK;EAC3ByF,MAAM,CAAChF,KAAK,GAAGA,KAAK;EACpB,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkJ,SAASA,CAACC,OAAO,EAAE;EACxBzlB,SAAS,IAAIuE,cAAc,CAACkhB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;EAC3DzlB,SAAS,IAAIqd,sBAAsB,CAACoI,OAAO,CAAC;EAC5C,MAAMC,SAAS,GAAGH,WAAW,CAAC,CAAC;EAC/B,IAAIvlB,SAAS,EAAE;IACXqE,WAAW,CAACqhB,SAAS,CAACxC,QAAQ,EAAE,IAAI,EAAE,uBAAuB,CAAC;IAC9D7e,WAAW,CAACqhB,SAAS,CAACpJ,KAAK,EAAE,IAAI,EAAE,uBAAuB,CAAC;IAC3DjY,WAAW,CAACqhB,SAAS,CAAChJ,KAAK,EAAE,IAAI,EAAE,uBAAuB,CAAC;IAC3DrY,WAAW,CAACqhB,SAAS,CAACC,aAAa,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;IACjEthB,WAAW,CAACqhB,SAAS,CAAC5D,iBAAiB,EAAE,CAAC,EAAE,uBAAuB,CAAC;IACpEzd,WAAW,CAACqhB,SAAS,CAACf,qBAAqB,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;IACzEtgB,WAAW,CAACqhB,SAAS,CAACE,gBAAgB,EAAE,IAAI,EAAE,uBAAuB,CAAC;IACtEvhB,WAAW,CAACqhB,SAAS,CAACzB,gBAAgB,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;IACpE5f,WAAW,CAACqhB,SAAS,CAACV,iBAAiB,EAAE,CAAC,EAAE,uBAAuB,CAAC;EACxE;EACA,MAAMtI,KAAK,GAAG+I,OAAO,CAAC/L,KAAK,CAAC;EAC5B2H,gBAAgB,CAACC,MAAM,GAAGoE,SAAS;EACnC1lB,SAAS,IAAI0c,KAAK,CAACmJ,UAAU,IAAIpJ,mBAAmB,CAACC,KAAK,CAACmJ,UAAU,EAAEnJ,KAAK,CAAC;EAC7EgJ,SAAS,CAAC3C,YAAY,GAAGrG,KAAK,CAACmJ,UAAU;EACzCH,SAAS,CAACpJ,KAAK,GAAGmJ,OAAO;EACzBC,SAAS,CAAChJ,KAAK,GAAGA,KAAK;EACvBgJ,SAAS,CAAC9C,YAAY,GAAG6C,OAAO;EAChCC,SAAS,CAACvB,YAAY,GAAGzH,KAAK,CAACuB,iBAAiB;EAChDyH,SAAS,CAAClB,MAAM,GAAG,KAAK;AAC5B;AACA;AACA;AACA;AACA,SAASe,WAAWA,CAAA,EAAG;EACnB,MAAMO,aAAa,GAAGzE,gBAAgB,CAACC,MAAM;EAC7C,MAAMyE,WAAW,GAAGD,aAAa,KAAK,IAAI,GAAG,IAAI,GAAGA,aAAa,CAACE,KAAK;EACvE,MAAMN,SAAS,GAAGK,WAAW,KAAK,IAAI,GAAGxE,YAAY,CAACuE,aAAa,CAAC,GAAGC,WAAW;EAClF,OAAOL,SAAS;AACpB;AACA,SAASnE,YAAYA,CAACpL,MAAM,EAAE;EAC1B,MAAMmL,MAAM,GAAG;IACXyB,YAAY,EAAE,IAAI;IAClBG,QAAQ,EAAE,IAAI;IACd5G,KAAK,EAAE,IAAI;IACXI,KAAK,EAAE,IAAI;IACXiJ,aAAa,EAAE,CAAC,CAAC;IACjB/C,YAAY,EAAE,IAAI;IAClBd,iBAAiB,EAAE,CAAC;IACpB8D,gBAAgB,EAAE,IAAI;IACtBjB,qBAAqB,EAAE,CAAC,CAAC;IACzBV,gBAAgB,EAAE,CAAC,CAAC;IACpBE,YAAY,EAAE,CAAC,CAAC;IAChBa,iBAAiB,EAAE,CAAC;IACpB7O,MAAM,EAAEA,MAAM;IACd6P,KAAK,EAAE,IAAI;IACXxB,MAAM,EAAE;EACZ,CAAC;EACDrO,MAAM,KAAK,IAAI,KAAKA,MAAM,CAAC6P,KAAK,GAAG1E,MAAM,CAAC,CAAC,CAAC;EAC5C,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2E,cAAcA,CAAA,EAAG;EACtB,MAAMC,SAAS,GAAG7E,gBAAgB,CAACC,MAAM;EACzCD,gBAAgB,CAACC,MAAM,GAAG4E,SAAS,CAAC/P,MAAM;EAC1C+P,SAAS,CAACnD,YAAY,GAAG,IAAI;EAC7BmD,SAAS,CAAC5J,KAAK,GAAG,IAAI;EACtB,OAAO4J,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAGF,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAAA,EAAG;EACjB,MAAMF,SAAS,GAAGD,cAAc,CAAC,CAAC;EAClCC,SAAS,CAAChD,QAAQ,GAAG,IAAI;EACzBgD,SAAS,CAACxJ,KAAK,GAAG,IAAI;EACtBwJ,SAAS,CAACP,aAAa,GAAG,CAAC,CAAC;EAC5BO,SAAS,CAACtD,YAAY,GAAG,IAAI;EAC7BsD,SAAS,CAACpE,iBAAiB,GAAG,CAAC;EAC/BoE,SAAS,CAACvB,qBAAqB,GAAG,CAAC,CAAC;EACpCuB,SAAS,CAACN,gBAAgB,GAAG,IAAI;EACjCM,SAAS,CAACjC,gBAAgB,GAAG,CAAC,CAAC;EAC/BiC,SAAS,CAAC/B,YAAY,GAAG,CAAC,CAAC;EAC3B+B,SAAS,CAAClB,iBAAiB,GAAG,CAAC;AACnC;AACA,SAASqB,eAAeA,CAACC,KAAK,EAAE;EAC5B,MAAM1D,YAAY,GAAIvB,gBAAgB,CAACC,MAAM,CAACsB,YAAY,GAAGzC,WAAW,CAACmG,KAAK,EAAEjF,gBAAgB,CAACC,MAAM,CAACsB,YAAY,CAAE;EACtH,OAAOA,YAAY,CAAC3I,OAAO,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsM,gBAAgBA,CAAA,EAAG;EACxB,OAAOlF,gBAAgB,CAACC,MAAM,CAACqE,aAAa;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,gBAAgBA,CAAClhB,KAAK,EAAE;EAC7BtF,SAAS,IACLsF,KAAK,KAAK,CAAC,CAAC,IACZpB,wBAAwB,CAACoB,KAAK,EAAE6V,aAAa,EAAE,2CAA2C,CAAC;EAC/Fnb,SAAS,IACL0E,cAAc,CAACY,KAAK,EAAE+b,gBAAgB,CAACC,MAAM,CAAChF,KAAK,CAAC/a,MAAM,EAAE,qCAAqC,CAAC;EACtG8f,gBAAgB,CAACC,MAAM,CAACqE,aAAa,GAAGrgB,KAAK;AACjD;AACA;AACA;AACA;AACA,SAASmhB,gBAAgBA,CAAA,EAAG;EACxB,MAAMnF,MAAM,GAAGD,gBAAgB,CAACC,MAAM;EACtC,OAAOpC,QAAQ,CAACoC,MAAM,CAAC5E,KAAK,EAAE4E,MAAM,CAACqE,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,cAAcA,CAAA,EAAG;EACtBrF,gBAAgB,CAACC,MAAM,CAACsE,gBAAgB,GAAGjH,aAAa;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,SAASgI,iBAAiBA,CAAA,EAAG;EACzBtF,gBAAgB,CAACC,MAAM,CAACsE,gBAAgB,GAAGhH,iBAAiB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgI,eAAeA,CAAA,EAAG;EACvBC,qBAAqB,CAAC,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqBA,CAAA,EAAG;EAC7BxF,gBAAgB,CAACC,MAAM,CAACsE,gBAAgB,GAAG,IAAI;AACnD;AACA,SAASkB,YAAYA,CAAA,EAAG;EACpB,OAAOzF,gBAAgB,CAACC,MAAM,CAACsE,gBAAgB;AACnD;AACA,IAAImB,mBAAmB,GAAG,IAAI;AAC9B;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,OAAOD,mBAAmB;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAAC5Y,IAAI,EAAE;EAC9B0Y,mBAAmB,GAAG1Y,IAAI;AAC9B;;AAEA;AACA;AACA;AACA,SAAS6Y,cAAcA,CAAC9S,OAAO,EAAE+B,MAAM,GAAG,IAAI,EAAEgR,mBAAmB,GAAG,IAAI,EAAE9kB,IAAI,EAAE;EAC9E,MAAMqH,QAAQ,GAAG0d,sCAAsC,CAAChT,OAAO,EAAE+B,MAAM,EAAEgR,mBAAmB,EAAE9kB,IAAI,CAAC;EACnGqH,QAAQ,CAACwO,2BAA2B,CAAC,CAAC;EACtC,OAAOxO,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0d,sCAAsCA,CAAChT,OAAO,EAAE+B,MAAM,GAAG,IAAI,EAAEgR,mBAAmB,GAAG,IAAI,EAAE9kB,IAAI,EAAE+T,MAAM,GAAG,IAAI9C,GAAG,CAAC,CAAC,EAAE;EAC1H,MAAMjN,SAAS,GAAG,CAAC8gB,mBAAmB,IAAIvV,WAAW,EAAEoB,mBAAmB,CAACoB,OAAO,CAAC,CAAC;EACpF/R,IAAI,GAAGA,IAAI,KAAK,OAAO+R,OAAO,KAAK,QAAQ,GAAGnO,SAAS,GAAGlE,SAAS,CAACqS,OAAO,CAAC,CAAC;EAC7E,OAAO,IAAI8B,UAAU,CAAC7P,SAAS,EAAE8P,MAAM,IAAIH,eAAe,CAAC,CAAC,EAAE3T,IAAI,IAAI,IAAI,EAAE+T,MAAM,CAAC;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiR,QAAQ,CAAC;EACX,OAAO5a,kBAAkB,GAAGA,kBAAkB;EAC9C,OAAO6a,IAAI,gBAAG,IAAIrV,YAAY,CAAC,CAAC;EAChC,OAAOsV,MAAMA,CAACnhB,OAAO,EAAE+P,MAAM,EAAE;IAC3B,IAAIlU,KAAK,CAACC,OAAO,CAACkE,OAAO,CAAC,EAAE;MACxB,OAAO8gB,cAAc,CAAC;QAAE7kB,IAAI,EAAE;MAAG,CAAC,EAAE8T,MAAM,EAAE/P,OAAO,EAAE,EAAE,CAAC;IAC5D,CAAC,MACI;MACD,MAAM/D,IAAI,GAAG+D,OAAO,CAAC/D,IAAI,IAAI,EAAE;MAC/B,OAAO6kB,cAAc,CAAC;QAAE7kB;MAAK,CAAC,EAAE+D,OAAO,CAAC+P,MAAM,EAAE/P,OAAO,CAACC,SAAS,EAAEhE,IAAI,CAAC;IAC5E;EACJ;EACA;EACA,OAAO6E,KAAK,GAAG,0BAA0B,eAAgBrB,kBAAkB,CAAC;IACxE7D,KAAK,EAAEqlB,QAAQ;IACfthB,UAAU,EAAE,KAAK;IACjBC,OAAO,EAAEA,CAAA,KAAMyH,QAAQ,CAACsE,UAAU;EACtC,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI,OAAOxK,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMigB,QAAQ,gBAAG,IAAIpgB,cAAc,CAACpH,SAAS,GAAG,eAAe,GAAG,EAAE,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,IAQMynB,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACb;AACJ;AACA;AACA;IACI,OAAOlgB,iBAAiB,GAAGmgB,gBAAgB;IAC3C;AACJ;AACA;AACA;IACI,OAAO/c,aAAa,GAAIjB,QAAQ,IAAKA,QAAQ;EACjD;EAAC,OAXK+d,UAAU;AAAA;AAYhB,MAAME,sBAAsB,SAASF,UAAU,CAAC;EAC5CG,MAAM;EACNpoB,WAAWA,CAACooB,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA,IAAInR,SAASA,CAAA,EAAG;IACZ,OAAO8F,WAAW,CAAC,IAAI,CAACqL,MAAM,CAAC;EACnC;EACApQ,SAASA,CAAC7N,QAAQ,EAAE;IAChB,MAAM2S,KAAK,GAAG,IAAI,CAACsL,MAAM;IACzBhH,mBAAmB,CAACtE,KAAK,EAAE3S,QAAQ,CAAC;IACpC,OAAO,MAAMmX,oBAAoB,CAACxE,KAAK,EAAE3S,QAAQ,CAAC;EACtD;AACJ;AACA,SAAS+d,gBAAgBA,CAAA,EAAG;EACxB,OAAO,IAAIC,sBAAsB,CAACnF,QAAQ,CAAC,CAAC,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqF,YAAY,CAAC;EACf;AACJ;AACA;EACIC,QAAQ,GAAGhhB,OAAO;EAClBihB,WAAWA,CAAC7V,KAAK,EAAE;IACf,IAAI,CAAC4V,QAAQ,CAAC5V,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EACvC;AACJ;AACA;AACA;AACA;AACA,MAAM8V,kCAAkC,gBAAG,IAAI5gB,cAAc,CAAC,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,wBAAwB,GAAG,EAAE,EAAE;EACzI+F,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX;IACA;IACA,MAAM0D,QAAQ,GAAGiE,MAAM,CAACsI,mBAAmB,CAAC;IAC5C,IAAIgS,gBAAgB;IACpB,OAAQlb,CAAC,IAAK;MACVkb,gBAAgB,KAAKve,QAAQ,CAACoD,GAAG,CAAC+a,YAAY,CAAC;MAC/CI,gBAAgB,CAACF,WAAW,CAAChb,CAAC,CAAC;IACnC,CAAC;EACL;AACJ,CAAC,CAAC;AACF,MAAMmb,kCAAkC,GAAG;EACvCpf,OAAO,EAAEgJ,uBAAuB;EAChCiB,QAAQ,EAAEA,CAAA,KAAM,KAAKpF,MAAM,CAACka,YAAY,CAAC;EACzCrgB,KAAK,EAAE;AACX,CAAC;AACD,MAAM2gB,oBAAoB,gBAAG,IAAI/gB,cAAc,CAACpH,SAAS,GAAG,sBAAsB,GAAG,EAAE,EAAE;EACrF+F,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,IAAI,OAAOoiB,YAAY,KAAK,WAAW,IAAIA,YAAY,EAAE;MACrD;IACJ;IACA,MAAMC,MAAM,GAAG1a,MAAM,CAAC6Z,QAAQ,CAAC,CAACc,WAAW;IAC3C,IAAI,CAACD,MAAM,EAAE;MACT;IACJ;IACA,MAAME,YAAY,GAAG5a,MAAM,CAACqa,kCAAkC,CAAC;IAC/D,MAAMQ,iBAAiB,GAAIzb,CAAC,IAAK;MAC7Bwb,YAAY,CAACxb,CAAC,CAAC0b,MAAM,CAAC;MACtB1b,CAAC,CAAC2b,cAAc,CAAC,CAAC;IACtB,CAAC;IACD,MAAMC,aAAa,GAAI5b,CAAC,IAAK;MACzBwb,YAAY,CAACxb,CAAC,CAACmF,KAAK,CAAC;MACrBnF,CAAC,CAAC2b,cAAc,CAAC,CAAC;IACtB,CAAC;IACD,MAAME,mBAAmB,GAAGA,CAAA,KAAM;MAC9BP,MAAM,CAACQ,gBAAgB,CAAC,oBAAoB,EAAEL,iBAAiB,CAAC;MAChEH,MAAM,CAACQ,gBAAgB,CAAC,OAAO,EAAEF,aAAa,CAAC;IACnD,CAAC;IACD;IACA;IACA,IAAI,OAAOG,IAAI,KAAK,WAAW,EAAE;MAC7BA,IAAI,CAACC,IAAI,CAACC,GAAG,CAACJ,mBAAmB,CAAC;IACtC,CAAC,MACI;MACDA,mBAAmB,CAAC,CAAC;IACzB;IACAjb,MAAM,CAAC8Z,UAAU,CAAC,CAACjQ,SAAS,CAAC,MAAM;MAC/B6Q,MAAM,CAACY,mBAAmB,CAAC,OAAO,EAAEN,aAAa,CAAC;MAClDN,MAAM,CAACY,mBAAmB,CAAC,oBAAoB,EAAET,iBAAiB,CAAC;IACvE,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,kCAAkCA,CAAA,EAAG;EAC1C,OAAOtW,wBAAwB,CAAC,CAC5BC,6BAA6B,CAAC,MAAM,KAAKlF,MAAM,CAACwa,oBAAoB,CAAC,CAAC,CACzE,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASgB,QAAQA,CAAC1jB,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAC7G,MAAM,CAAC,KAAKqH,SAAS;AACrE;;AAEA;AACA;AACA;AACA;AACA,SAASmjB,qBAAqBA,CAAC3jB,KAAK,EAAE;EAClC;EACA;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA,SAAS4jB,MAAMA,CAACC,YAAY,EAAEljB,OAAO,EAAE;EACnC,MAAM,CAAC0G,GAAG,EAAEgK,GAAG,EAAEyS,MAAM,CAAC,GAAG1qB,YAAY,CAACyqB,YAAY,EAAEljB,OAAO,EAAEojB,KAAK,CAAC;EACrE,MAAMC,QAAQ,GAAG3c,GAAG;EACpB,MAAM9H,IAAI,GAAGykB,QAAQ,CAAC7qB,MAAM,CAAC;EAC7B6qB,QAAQ,CAAC3S,GAAG,GAAGA,GAAG;EAClB2S,QAAQ,CAACF,MAAM,GAAGA,MAAM;EACxBE,QAAQ,CAACC,UAAU,GAAGC,kBAAkB,CAACC,IAAI,CAACH,QAAQ,CAAC;EACvD,IAAIzpB,SAAS,EAAE;IACXypB,QAAQ,CAAChpB,QAAQ,GAAG,MAAM,YAAYgpB,QAAQ,CAAC,CAAC,GAAG;IACnDzkB,IAAI,CAAC6kB,SAAS,GAAGzjB,OAAO,EAAEyjB,SAAS;EACvC;EACA,OAAOJ,QAAQ;AACnB;AACA,SAASE,kBAAkBA,CAAA,EAAG;EAC1B,MAAM3kB,IAAI,GAAG,IAAI,CAACpG,MAAM,CAAC;EACzB,IAAIoG,IAAI,CAAC8kB,UAAU,KAAK7jB,SAAS,EAAE;IAC/B,MAAM6jB,UAAU,GAAGA,CAAA,KAAM,IAAI,CAAC,CAAC;IAC/BA,UAAU,CAAClrB,MAAM,CAAC,GAAGoG,IAAI;IACzBA,IAAI,CAAC8kB,UAAU,GAAGA,UAAU;EAChC;EACA,OAAO9kB,IAAI,CAAC8kB,UAAU;AAC1B;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACtkB,KAAK,EAAE;EAC7B,OAAO0jB,QAAQ,CAAC1jB,KAAK,CAAC,IAAI,OAAOA,KAAK,CAACqR,GAAG,KAAK,UAAU;AAC7D;;AAEA;AACA;AACA;AACA,MAAMkT,wBAAwB,CAAC;AAE/B;AACA,MAAMC,gBAAgB,gBAAG,IAAI7iB,cAAc,CAAC,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAAE+F,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEA,CAAA,KAAM;AAAM,CAAC,CAAC;AAClK;AACA,MAAMkkB,iBAAiB,gBAAG,IAAI9iB,cAAc,CAAC,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,mBAAmB,GAAG,EAAE,EAAE;EAAE+F,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEA,CAAA,KAAM;AAAM,CAAC,CAAC;AACpK,MAAMmkB,2BAA2B,gBAAG,IAAI/iB,cAAc,CAAC,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;AACjI;AACA,MAAMoqB,qBAAqB,gBAAG,IAAIhjB,cAAc,CAAC,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,kCAAkC,GAAG,EAAE,CAAC;;AAEzI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqqB,0BAA0BA,CAAC7Q,OAAO,EAAE8Q,YAAY,EAAE;EACvD;EACA;EACA,IAAI3rB,iBAAiB,CAAC,CAAC,KAAK,IAAI,EAAE;IAC9B,MAAM,IAAIU,YAAY,CAAC,CAAC,GAAG,CAAC,8DAA8DW,SAAS,IAC/F,GAAGwZ,OAAO,CAACnX,IAAI,sDAAsDioB,YAAY,GAAG,IAAIA,YAAY,EAAE,GAAG,EAAE,EAAE,CAAC;EACtH;AACJ;AAAC,IAEKC,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdnL,IAAI;IACJpa,IAAI;IACJxF,WAAWA,CAAC4f,IAAI,EAAEpa,IAAI,EAAE;MACpB,IAAI,CAACoa,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACpa,IAAI,GAAGA,IAAI;IACpB;IACA;AACJ;AACA;AACA;IACI,OAAOuC,iBAAiB,GAAGijB,iBAAiB;EAChD;EAAC,OAZKD,WAAW;AAAA;AAajB,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAO,IAAID,WAAW,CAAC/H,QAAQ,CAAC,CAAC,EAAEM,eAAe,CAAC,CAAC,CAAC;AACzD;;AAEA;AACA;AACA;AAFA,IAGM2H,oBAAoB;EAA1B,MAAMA,oBAAoB,CAAC;IACvBC,MAAM,GAAG,CAAC;IACVC,YAAY,GAAG,IAAIrX,GAAG,CAAC,CAAC;IACxBmD,SAAS,GAAG,KAAK;IACjBmU,WAAW,GAAG,IAAI9rB,eAAe,CAAC,KAAK,CAAC;IACxC,IAAI+rB,eAAeA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAACpU,SAAS,GAAG,KAAK,GAAG,IAAI,CAACmU,WAAW,CAACnlB,KAAK;IAC1D;IACA;AACJ;AACA;AACA;IACI,IAAIqlB,yBAAyBA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAACrU,SAAS,EAAE;QAChB;QACA,OAAO,IAAI1X,UAAU,CAAEgsB,UAAU,IAAK;UAClCA,UAAU,CAACC,IAAI,CAAC,KAAK,CAAC;UACtBD,UAAU,CAACE,QAAQ,CAAC,CAAC;QACzB,CAAC,CAAC;MACN;MACA,OAAO,IAAI,CAACL,WAAW;IAC3B;IACAnW,GAAGA,CAAA,EAAG;MACF;MACA,IAAI,CAAC,IAAI,CAACoW,eAAe,IAAI,CAAC,IAAI,CAACpU,SAAS,EAAE;QAC1C,IAAI,CAACmU,WAAW,CAACI,IAAI,CAAC,IAAI,CAAC;MAC/B;MACA,MAAMN,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;MAC5B,IAAI,CAACC,YAAY,CAAClW,GAAG,CAACiW,MAAM,CAAC;MAC7B,OAAOA,MAAM;IACjB;IACAlW,GAAGA,CAACkW,MAAM,EAAE;MACR,OAAO,IAAI,CAACC,YAAY,CAACnW,GAAG,CAACkW,MAAM,CAAC;IACxC;IACAQ,MAAMA,CAACR,MAAM,EAAE;MACX,IAAI,CAACC,YAAY,CAACQ,MAAM,CAACT,MAAM,CAAC;MAChC,IAAI,IAAI,CAACC,YAAY,CAACla,IAAI,KAAK,CAAC,IAAI,IAAI,CAACoa,eAAe,EAAE;QACtD,IAAI,CAACD,WAAW,CAACI,IAAI,CAAC,KAAK,CAAC;MAChC;IACJ;IACA5T,WAAWA,CAAA,EAAG;MACV,IAAI,CAACuT,YAAY,CAACpT,KAAK,CAAC,CAAC;MACzB,IAAI,IAAI,CAACsT,eAAe,EAAE;QACtB,IAAI,CAACD,WAAW,CAACI,IAAI,CAAC,KAAK,CAAC;MAChC;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACvU,SAAS,GAAG,IAAI;MACrB,IAAI,CAACmU,WAAW,CAACQ,WAAW,CAAC,CAAC;IAClC;IACA;IACA,OAAOlkB,KAAK,GAAG,0BAA0B,eAAgBrB,kBAAkB,CAAC;MACxE7D,KAAK,EAAEyoB,oBAAoB;MAC3B1kB,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAEA,CAAA,KAAM,IAAIykB,oBAAoB,CAAC;IAC5C,CAAC,CAAC;EACN;EAAC,OA5DKA,oBAAoB;AAAA;AA6D1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,IAqBMY,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfC,oBAAoB,GAAG3d,MAAM,CAAC8c,oBAAoB,CAAC;IACnDc,SAAS,GAAG5d,MAAM,CAACqc,wBAAwB,CAAC;IAC5CzB,YAAY,GAAG5a,MAAM,CAACqa,kCAAkC,CAAC;IACzD;AACJ;AACA;AACA;IACIvT,GAAGA,CAAA,EAAG;MACF,MAAMiW,MAAM,GAAG,IAAI,CAACY,oBAAoB,CAAC7W,GAAG,CAAC,CAAC;MAC9C,OAAO,MAAM;QACT,IAAI,CAAC,IAAI,CAAC6W,oBAAoB,CAAC9W,GAAG,CAACkW,MAAM,CAAC,EAAE;UACxC;UACA;QACJ;QACA;QACA,IAAI,CAACa,SAAS,CAAC7K,MAAM,CAAC,EAAE,CAAC,2CAA2C,CAAC;QACrE,IAAI,CAAC4K,oBAAoB,CAACJ,MAAM,CAACR,MAAM,CAAC;MAC5C,CAAC;IACL;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI1B,GAAGA,CAACvlB,EAAE,EAAE;MACJ,MAAM+nB,UAAU,GAAG,IAAI,CAAC/W,GAAG,CAAC,CAAC;MAC7BhR,EAAE,CAAC,CAAC,CAACgoB,KAAK,CAAC,IAAI,CAAClD,YAAY,CAAC,CAACmD,OAAO,CAACF,UAAU,CAAC;IACrD;IACA;IACA,OAAOtkB,KAAK,GAAG,0BAA0B,eAAgBrB,kBAAkB,CAAC;MACxE7D,KAAK,EAAEqpB,YAAY;MACnBtlB,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAEA,CAAA,KAAM,IAAIqlB,YAAY,CAAC;IACpC,CAAC,CAAC;EACN;EAAC,OA3CKA,YAAY;AAAA;AA6ClB,SAASM,IAAIA,CAAC,GAAG1d,IAAI,EAAE;EACnB;AAAA;;AAGJ;AACA;AACA;AAFA,IAGM2d,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB;IACA,OAAO1kB,KAAK,GAAG,0BAA0B,eAAgBrB,kBAAkB,CAAC;MACxE7D,KAAK,EAAE4pB,eAAe;MACtB7lB,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAEA,CAAA,KAAM,IAAI6lB,wBAAwB,CAAC;IAChD,CAAC,CAAC;EACN;EAAC,OAPKD,eAAe;AAAA;AAQrB;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BC,gBAAgB,GAAG,CAAC;EACpBC,MAAM,gBAAG,IAAIzV,GAAG,CAAC,CAAC;EAClB7B,GAAGA,CAACuX,MAAM,EAAE;IACR,IAAI,CAACC,OAAO,CAACD,MAAM,CAAC;IACpB,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC;EACzB;EACAE,QAAQA,CAACF,MAAM,EAAE;IACb,IAAI,CAACA,MAAM,CAACzL,KAAK,EAAE;MACf;IACJ;IACA,IAAI,CAACuL,gBAAgB,EAAE;EAC3B;EACAZ,MAAMA,CAACc,MAAM,EAAE;IACX,MAAMG,IAAI,GAAGH,MAAM,CAACG,IAAI;IACxB,MAAMC,KAAK,GAAG,IAAI,CAACL,MAAM,CAACjf,GAAG,CAACqf,IAAI,CAAC;IACnC,IAAI,CAACC,KAAK,CAAC5X,GAAG,CAACwX,MAAM,CAAC,EAAE;MACpB;IACJ;IACAI,KAAK,CAACjB,MAAM,CAACa,MAAM,CAAC;IACpB,IAAIA,MAAM,CAACzL,KAAK,EAAE;MACd,IAAI,CAACuL,gBAAgB,EAAE;IAC3B;EACJ;EACAG,OAAOA,CAACD,MAAM,EAAE;IACZ,MAAMG,IAAI,GAAGH,MAAM,CAACG,IAAI;IACxB,IAAI,CAAC,IAAI,CAACJ,MAAM,CAACvX,GAAG,CAAC2X,IAAI,CAAC,EAAE;MACxB,IAAI,CAACJ,MAAM,CAACjV,GAAG,CAACqV,IAAI,EAAE,IAAI7Y,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,MAAM8Y,KAAK,GAAG,IAAI,CAACL,MAAM,CAACjf,GAAG,CAACqf,IAAI,CAAC;IACnC,IAAIC,KAAK,CAAC5X,GAAG,CAACwX,MAAM,CAAC,EAAE;MACnB;IACJ;IACAI,KAAK,CAAC3X,GAAG,CAACuX,MAAM,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACP,gBAAgB,GAAG,CAAC,EAAE;MAC9B,IAAIQ,YAAY,GAAG,KAAK;MACxB,KAAK,MAAM,CAACH,IAAI,EAAEC,KAAK,CAAC,IAAI,IAAI,CAACL,MAAM,EAAE;QACrC;QACA,IAAII,IAAI,KAAK,IAAI,EAAE;UACfG,YAAY,KAAK,IAAI,CAACC,UAAU,CAACH,KAAK,CAAC;QAC3C,CAAC,MACI;UACDE,YAAY,KAAKH,IAAI,CAACnD,GAAG,CAAC,MAAM,IAAI,CAACuD,UAAU,CAACH,KAAK,CAAC,CAAC;QAC3D;MACJ;MACA;MACA;MACA,IAAI,CAACE,YAAY,EAAE;QACf,IAAI,CAACR,gBAAgB,GAAG,CAAC;MAC7B;IACJ;EACJ;EACAS,UAAUA,CAACH,KAAK,EAAE;IACd,IAAIE,YAAY,GAAG,KAAK;IACxB,KAAK,MAAMN,MAAM,IAAII,KAAK,EAAE;MACxB,IAAI,CAACJ,MAAM,CAACzL,KAAK,EAAE;QACf;MACJ;MACA,IAAI,CAACuL,gBAAgB,EAAE;MACvBQ,YAAY,GAAG,IAAI;MACnB;MACAN,MAAM,CAAChD,GAAG,CAAC,CAAC;IAChB;IACA,OAAOsD,YAAY;EACvB;AACJ;AAEA,SAASpR,6BAA6B,EAAEb,UAAU,EAAEC,UAAU,EAAEN,OAAO,EAAEyB,uBAAuB,EAAExB,OAAO,EAAE+P,wBAAwB,EAAEtI,kBAAkB,EAAElH,0BAA0B,EAAEC,sBAAsB,EAAEF,gBAAgB,EAAEc,gBAAgB,EAAEmM,QAAQ,EAAEC,UAAU,EAAEzM,OAAO,EAAED,mBAAmB,EAAEF,sBAAsB,EAAEjJ,WAAW,EAAED,SAAS,EAAEwI,WAAW,EAAErI,uBAAuB,EAAE8Z,eAAe,EAAE3V,mBAAmB,EAAE4R,YAAY,EAAElO,KAAK,EAAEwB,aAAa,EAAE1B,IAAI,EAAEM,SAAS,EAAEa,EAAE,EAAE7I,UAAU,IAAImI,QAAQ,EAAEA,QAAQ,IAAInI,UAAU,EAAEC,kBAAkB,EAAE4D,cAAc,EAAEoS,kCAAkC,EAAE5gB,cAAc,EAAEigB,QAAQ,EAAEzI,iBAAiB,EAAEpD,WAAW,EAAEF,MAAM,EAAEzB,IAAI,EAAE9P,WAAW,EAAEE,UAAU,EAAEQ,aAAa,EAAEF,cAAc,EAAEtD,UAAU,EAAEoD,UAAU,EAAEF,WAAW,EAAE1D,WAAW,EAAEkhB,sBAAsB,EAAE1V,YAAY,EAAE6I,gBAAgB,EAAElB,MAAM,EAAEc,mBAAmB,EAAEwP,iBAAiB,EAAEmB,YAAY,EAAEZ,oBAAoB,EAAE9P,OAAO,EAAEzE,UAAU,EAAE+E,0BAA0B,EAAEb,QAAQ,EAAE/a,YAAY,EAAE+qB,qBAAqB,EAAEzL,aAAa,EAAEjF,KAAK,EAAEI,MAAM,EAAEyB,SAAS,EAAEgP,WAAW,EAAEnrB,gBAAgB,EAAE6qB,gBAAgB,EAAEE,2BAA2B,EAAE/pB,OAAO,EAAEiQ,UAAU,EAAEd,WAAW,EAAEsB,YAAY,EAAEH,WAAW,EAAEuM,mBAAmB,EAAEpY,aAAa,EAAE+Y,kBAAkB,EAAE7Y,aAAa,EAAEG,aAAa,EAAEb,WAAW,EAAEkZ,qBAAqB,EAAEG,qBAAqB,EAAEtZ,cAAc,EAAEO,iBAAiB,EAAET,wBAAwB,EAAEiZ,eAAe,EAAE5D,wBAAwB,EAAEwE,sBAAsB,EAAEG,yBAAyB,EAAE9Y,kBAAkB,EAAEmH,kCAAkC,EAAE6Q,gBAAgB,EAAEE,WAAW,EAAE5Y,cAAc,EAAEwY,kBAAkB,EAAEuB,kBAAkB,EAAE7Z,gBAAgB,EAAEL,cAAc,EAAE8lB,0BAA0B,EAAEzkB,iBAAiB,EAAEnB,aAAa,EAAEf,YAAY,EAAEI,mBAAmB,EAAE0B,WAAW,EAAEgZ,gBAAgB,EAAEF,qBAAqB,EAAE9Z,UAAU,EAAEL,YAAY,EAAE2Y,UAAU,EAAEH,WAAW,EAAEH,mBAAmB,EAAEC,mBAAmB,EAAElO,gBAAgB,EAAE7L,sBAAsB,EAAEmK,iBAAiB,EAAEqa,cAAc,EAAEE,sCAAsC,EAAErc,0BAA0B,EAAEiX,yBAAyB,EAAE9R,WAAW,EAAEhK,gBAAgB,EAAEqD,sBAAsB,EAAEH,eAAe,EAAEH,iCAAiC,EAAEC,kCAAkC,EAAEP,2BAA2B,EAAEyc,OAAO,EAAE/C,uBAAuB,EAAEmD,SAAS,EAAE0C,kCAAkC,EAAEvmB,cAAc,EAAEkO,OAAO,EAAEnQ,kBAAkB,EAAE0D,UAAU,EAAE8gB,eAAe,EAAEF,cAAc,EAAE/B,kBAAkB,EAAEzgB,sBAAsB,EAAE8Q,eAAe,EAAEiN,wBAAwB,EAAEO,WAAW,EAAEwD,eAAe,EAAEwB,sBAAsB,EAAED,wBAAwB,EAAE5B,qBAAqB,EAAE8B,oBAAoB,EAAEjC,eAAe,EAAEE,4BAA4B,EAAExQ,eAAe,EAAED,sBAAsB,EAAEsP,oBAAoB,EAAEzS,aAAa,EAAE7I,gBAAgB,EAAES,cAAc,EAAEwb,QAAQ,EAAE7B,cAAc,EAAEmG,YAAY,EAAE/H,gBAAgB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAE9M,cAAc,EAAEC,qBAAqB,EAAE4D,eAAe,EAAE+K,uBAAuB,EAAEC,uBAAuB,EAAEvO,UAAU,EAAE8T,gBAAgB,EAAEE,gBAAgB,EAAEvH,QAAQ,EAAEuD,QAAQ,EAAEpG,OAAO,EAAErJ,mBAAmB,EAAE+O,yBAAyB,EAAEuC,qBAAqB,EAAEjjB,aAAa,EAAEsM,MAAM,EAAEvB,kBAAkB,EAAE8G,2BAA2B,EAAEyC,eAAe,EAAEsG,cAAc,EAAEH,eAAe,EAAEF,kBAAkB,EAAE+D,cAAc,EAAEyD,oBAAoB,EAAE7G,WAAW,EAAEP,eAAe,EAAEnS,sBAAsB,EAAE4Z,0BAA0B,EAAEjgB,YAAY,EAAE+f,sBAAsB,EAAEgB,aAAa,EAAEjL,oBAAoB,EAAE4I,sBAAsB,EAAExb,YAAY,EAAEiV,YAAY,EAAED,OAAO,EAAEU,iBAAiB,EAAEyH,iBAAiB,EAAE1H,UAAU,EAAEgN,QAAQ,EAAEhH,wBAAwB,EAAEzP,YAAY,EAAEyC,cAAc,EAAE4U,gBAAgB,EAAE1Y,gBAAgB,EAAED,oBAAoB,EAAEF,gBAAgB,EAAE+V,kBAAkB,EAAEd,OAAO,EAAE5D,uBAAuB,EAAE6D,SAAS,EAAEjH,IAAI,EAAEvM,wBAAwB,EAAEsN,yBAAyB,EAAED,kBAAkB,EAAEzP,QAAQ,EAAE6T,gBAAgB,EAAEgC,eAAe,EAAEsF,IAAI,EAAEzC,kCAAkC,EAAErW,6BAA6B,EAAEkG,iBAAiB,EAAEzI,eAAe,EAAEwQ,oBAAoB,EAAElW,eAAe,EAAE0V,0BAA0B,EAAEN,sBAAsB,EAAE1c,iBAAiB,EAAE4V,qBAAqB,EAAEzP,4BAA4B,EAAE2a,eAAe,EAAEM,6BAA6B,EAAEE,wBAAwB,EAAEK,oBAAoB,EAAE9B,eAAe,EAAEE,0BAA0B,EAAEoB,cAAc,EAAEvY,uBAAuB,EAAE9D,mBAAmB,EAAET,0BAA0B,EAAEgc,yBAAyB,EAAEG,oBAAoB,EAAE0C,gBAAgB,EAAE6C,MAAM,EAAEM,kBAAkB,EAAEtK,KAAK,EAAE6B,uBAAuB,EAAEN,mBAAmB,EAAE7e,SAAS,EAAE+I,iBAAiB,EAAEQ,0BAA0B,EAAEzH,UAAU,EAAEiI,0BAA0B,EAAEjJ,cAAc,EAAEic,WAAW,EAAED,WAAW,EAAE2B,oCAAoC,EAAEZ,4BAA4B,EAAEC,uBAAuB,EAAElM,gBAAgB,EAAEwM,WAAW,EAAE6G,kBAAkB,EAAEoC,qBAAqB,EAAEvjB,kBAAkB,EAAEM,gBAAgB,EAAEmc,iBAAiB,EAAEF,gBAAgB,EAAE3U,QAAQ,EAAEC,mBAAmB,EAAEkZ,eAAe,EAAED,iBAAiB,EAAED,cAAc,EAAE7D,WAAW,EAAEH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}