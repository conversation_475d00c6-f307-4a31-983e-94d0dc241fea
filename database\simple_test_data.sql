-- 🏪 بيانات تجريبية مبسطة لسنتر مفروشات مصري
-- مع دعم اللغة العربية الكامل

-- 🗑️ مسح البيانات القديمة
DELETE FROM JournalEntryDetails;
DELETE FROM JournalEntries; 
DELETE FROM CashReceipts;
DELETE FROM CashPayments;
DELETE FROM FinancialTransactions;
DELETE FROM ActivityLog;
UPDATE Counters SET CurrentValue = 0;

-- 🏢 إنشاء الفروع
INSERT INTO Branches (Code, NameAr, NameEn, Address, Phone, Email, IsActive, IsMainBranch, CreatedAt) VALUES
('BR001', N'الفرع الرئيسي - القاهرة', 'Main Branch - Cairo', N'شارع التحرير، وسط البلد، القاهرة', '02-25555555', '<EMAIL>', 1, 1, GETDATE()),
('BR002', N'فرع الإسكندرية', 'Alexandria Branch', N'شارع الكورنيش، الإسكندرية', '03-4888888', '<EMAIL>', 1, 0, GETDATE()),
('BR003', N'فرع الجيزة', 'Giza Branch', N'شارع الهرم، الجيزة', '02-33777777', '<EMAIL>', 1, 0, GETDATE());

-- 👥 إنشاء المستخدمين
INSERT INTO Users (Username, Email, PasswordHash, FullName, IsActive, IsSystemAdmin, CreatedAt) VALUES
('admin', '<EMAIL>', 'hashed_password_123', N'أحمد محمد الإدارة', 1, 1, GETDATE()),
('sales1', '<EMAIL>', 'hashed_password_456', N'فاطمة علي المبيعات', 1, 0, GETDATE()),
('cashier1', '<EMAIL>', 'hashed_password_789', N'محمد سعد الكاشير', 1, 0, GETDATE()),
('manager1', '<EMAIL>', 'hashed_password_101', N'سارة أحمد المدير', 1, 0, GETDATE());

-- 🏷️ إنشاء فئات المنتجات
INSERT INTO Categories (NameAr, NameEn, ParentId, DisplayOrder, IsActive, CreatedAt) VALUES
(N'غرف النوم', 'Bedrooms', NULL, 1, 1, GETDATE()),
(N'غرف المعيشة', 'Living Rooms', NULL, 2, 1, GETDATE()),
(N'غرف الطعام', 'Dining Rooms', NULL, 3, 1, GETDATE()),
(N'المطابخ', 'Kitchens', NULL, 4, 1, GETDATE()),
(N'الأدوات الكهربائية', 'Electrical Appliances', NULL, 5, 1, GETDATE()),
(N'الأدوات المنزلية', 'Home Accessories', NULL, 6, 1, GETDATE()),
(N'الإضاءة', 'Lighting', NULL, 7, 1, GETDATE()),
(N'السجاد والموكيت', 'Carpets & Rugs', NULL, 8, 1, GETDATE());

-- 📏 إنشاء وحدات القياس
INSERT INTO Units (NameAr, NameEn, Symbol, IsActive, CreatedAt) VALUES
(N'قطعة', 'Piece', 'PCS', 1, GETDATE()),
(N'طقم', 'Set', 'SET', 1, GETDATE()),
(N'متر مربع', 'Square Meter', 'M2', 1, GETDATE()),
(N'كيلوجرام', 'Kilogram', 'KG', 1, GETDATE()),
(N'صندوق', 'Box', 'BOX', 1, GETDATE()),
(N'زوج', 'Pair', 'PAIR', 1, GETDATE());

-- 💰 إنشاء فئات الأسعار
INSERT INTO PriceCategories (NameAr, NameEn, DiscountPercentage, MarkupPercentage, IsActive, CreatedAt) VALUES
(N'سعر التجزئة', 'Retail Price', 0.00, 0.00, 1, GETDATE()),
(N'سعر الجملة', 'Wholesale Price', 15.00, 0.00, 1, GETDATE()),
(N'عملاء مميزين', 'VIP Customers', 25.00, 0.00, 1, GETDATE()),
(N'سعر الموظفين', 'Employee Price', 30.00, 0.00, 1, GETDATE());

-- 🏪 إنشاء أنواع العملاء
INSERT INTO CustomerTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'عميل تجزئة', 'Retail Customer', 1, 1, GETDATE()),
(N'عميل جملة', 'Wholesale Customer', 2, 1, GETDATE()),
(N'عميل مميز', 'VIP Customer', 3, 1, GETDATE()),
(N'عميل مؤسسي', 'Corporate Customer', 4, 1, GETDATE());

-- 🏭 إنشاء أنواع الموردين
INSERT INTO SupplierTypes (NameAr, NameEn, DisplayOrder, IsActive, CreatedAt) VALUES
(N'مورد محلي', 'Local Supplier', 1, 1, GETDATE()),
(N'مورد مستورد', 'Import Supplier', 2, 1, GETDATE()),
(N'مصنع', 'Factory', 3, 1, GETDATE()),
(N'وكيل تجاري', 'Commercial Agent', 4, 1, GETDATE());

-- 💳 إنشاء طرق الدفع
INSERT INTO PaymentMethods (NameAr, NameEn, IsActive, CreatedAt) VALUES
(N'نقدي', 'Cash', 1, GETDATE()),
(N'فيزا', 'Visa Card', 1, GETDATE()),
(N'ماستر كارد', 'Master Card', 1, GETDATE()),
(N'تقسيط', 'Installment', 1, GETDATE()),
(N'تحويل بنكي', 'Bank Transfer', 1, GETDATE());

-- 💳 إنشاء طرق الدفع
INSERT INTO PaymentMethods (NameAr, NameEn, IsActive, CreatedAt) VALUES
(N'نقدي', 'Cash', 1, GETDATE()),
(N'فيزا', 'Visa Card', 1, GETDATE()),
(N'ماستر كارد', 'Master Card', 1, GETDATE()),
(N'تقسيط', 'Installment', 1, GETDATE()),
(N'تحويل بنكي', 'Bank Transfer', 1, GETDATE());

PRINT N'تم إنشاء البيانات الأساسية بنجاح ✅';

-- 🏭 إنشاء الموردين
INSERT INTO Suppliers (SupplierCode, NameAr, NameEn, SupplierTypeId, Phone1, Email, Address, PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('SUP001', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 1, '02-********', '<EMAIL>', N'المنطقة الصناعية، القاهرة', 30, 100000.00, 0.00, 0.00, 1, GETDATE()),
('SUP002', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 3, '03-5678901', '<EMAIL>', N'برج العرب، الإسكندرية', 45, 150000.00, 0.00, 0.00, 1, GETDATE()),
('SUP003', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, '02-********', '<EMAIL>', N'مدينة نصر، القاهرة', 30, 80000.00, 0.00, 0.00, 1, GETDATE()),
('SUP004', N'مستوردو الإضاءة الأوروبية', 'European Lighting Importers', 2, '02-25678901', '<EMAIL>', N'مصر الجديدة، القاهرة', 60, 200000.00, 0.00, 0.00, 1, GETDATE()),
('SUP005', N'مصنع السجاد الشرقي', 'Oriental Carpet Factory', 3, '02-44567890', '<EMAIL>', N'حلوان، القاهرة', 30, 120000.00, 0.00, 0.00, 1, GETDATE());

-- 👥 إنشاء العملاء
INSERT INTO Customers (CustomerCode, NameAr, NameEn, CustomerTypeId, Phone1, Email, Address, PriceCategoryId, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('CUS001', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, '01012345678', '<EMAIL>', N'شارع الجمهورية، وسط البلد، القاهرة', 1, 0.00, 0.00, 1, GETDATE()),
('CUS002', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, '01098765432', '<EMAIL>', N'شارع الكورنيش، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE()),
('CUS003', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 4, '02-********', '<EMAIL>', N'شارع النيل، الجيزة', 2, 0.00, 0.00, 1, GETDATE()),
('CUS004', N'محمد سعد الدين', 'Mohamed Saad El Din', 3, '01076543210', '<EMAIL>', N'شارع التحرير، القاهرة', 3, 0.00, 0.00, 1, GETDATE()),
('CUS005', N'سارة علي محمود', 'Sara Ali Mahmoud', 1, '01055555555', '<EMAIL>', N'شارع سعد زغلول، الإسكندرية', 1, 0.00, 0.00, 1, GETDATE()),
('CUS006', N'شركة المقاولات الكبرى', 'Major Contracting Company', 4, '02-44567890', '<EMAIL>', N'مدينة نصر، القاهرة', 2, 0.00, 0.00, 1, GETDATE());

PRINT N'تم إنشاء الموردين والعملاء بنجاح ✅';
