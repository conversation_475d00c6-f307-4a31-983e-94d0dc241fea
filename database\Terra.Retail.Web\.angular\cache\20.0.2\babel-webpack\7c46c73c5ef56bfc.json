{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, DOCUMENT, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, signal, EventEmitter, NgModule } from '@angular/core';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from './portal.mjs';\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nexport { CdkPortal as ɵɵCdkPortal, PortalHostDirective as ɵɵPortalHostDirective, TemplatePortalDirective as ɵɵTemplatePortalDirective } from './portal.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-DHa4AVFz.mjs';\nimport { F as FocusMonitor } from './focus-monitor-DLjkiju1.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { s as createBlockScrollStrategy, O as OverlayContainer, c as createOverlayRef, i as OverlayConfig, f as createGlobalPositionStrategy, d as OverlayRef, t as OverlayModule } from './overlay-module-Bd2UplUU.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport '@angular/common';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass = '';\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose = false;\n  /** Function used to determine whether the dialog is allowed to close. */\n  closePredicate;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Dialog label applied via `aria-label` */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus = true;\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation = true;\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy = true;\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments = true;\n  /**\n   * Whether the built-in overlay animations should be disabled.\n   */\n  disableAnimations = false;\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers;\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container;\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext;\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nlet CdkDialogContainer = /*#__PURE__*/(() => {\n  class CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, {\n      optional: true\n    });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n      super();\n      // Callback is primarily for some internal tests\n      // that were instantiating the dialog container manually.\n      this._config = inject(DialogConfig, {\n        optional: true\n      }) || new DialogConfig();\n      if (this._config.ariaLabelledBy) {\n        this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n      }\n    }\n    _addAriaLabelledBy(id) {\n      this._ariaLabelledByQueue.push(id);\n      this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n      const index = this._ariaLabelledByQueue.indexOf(id);\n      if (index > -1) {\n        this._ariaLabelledByQueue.splice(index, 1);\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    _contentAttached() {\n      this._initializeFocusTrap();\n      this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n      this._trapFocus();\n    }\n    ngOnDestroy() {\n      this._isDestroyed = true;\n      this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachComponentPortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n      this._contentAttached();\n      return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n      if (!this._containsFocus()) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1;\n        // The tabindex attribute should be removed to avoid navigating to that element again\n        this._ngZone.runOutsideAngular(() => {\n          const callback = () => {\n            deregisterBlur();\n            deregisterMousedown();\n            element.removeAttribute('tabindex');\n          };\n          const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n          const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n        });\n      }\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n      if (this._isDestroyed) {\n        return;\n      }\n      // If were to attempt to focus immediately, then the content of the dialog would not yet be\n      // ready in instances where change detection has to run first. To deal with this, we simply\n      // wait until after the next render.\n      afterNextRender(() => {\n        const element = this._elementRef.nativeElement;\n        switch (this._config.autoFocus) {\n          case false:\n          case 'dialog':\n            // Ensure that focus is on the dialog container. It's possible that a different\n            // component tried to move focus while the open animation was running. See:\n            // https://github.com/angular/components/issues/16215. Note that we only want to do this\n            // if the focus isn't inside the dialog already, because it's possible that the consumer\n            // turned off `autoFocus` in order to move focus themselves.\n            if (!this._containsFocus()) {\n              element.focus(options);\n            }\n            break;\n          case true:\n          case 'first-tabbable':\n            const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n            // If we weren't able to find a focusable element in the dialog, then focus the dialog\n            // container instead.\n            if (!focusedSuccessfully) {\n              this._focusDialogContainer(options);\n            }\n            break;\n          case 'first-heading':\n            this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n            break;\n          default:\n            this._focusByCssSelector(this._config.autoFocus, options);\n            break;\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n      const focusConfig = this._config.restoreFocus;\n      let focusTargetElement = null;\n      if (typeof focusConfig === 'string') {\n        focusTargetElement = this._document.querySelector(focusConfig);\n      } else if (typeof focusConfig === 'boolean') {\n        focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n      } else if (focusConfig) {\n        focusTargetElement = focusConfig;\n      }\n      // We need the extra check, because IE can set the `activeElement` to null in some cases.\n      if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n        const activeElement = _getFocusedElementPierceShadowDom();\n        const element = this._elementRef.nativeElement;\n        // Make sure that focus is still inside the dialog or is on the body (usually because a\n        // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n        // the consumer moved it themselves before the animation was done, in which case we shouldn't\n        // do anything.\n        if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n          if (this._focusMonitor) {\n            this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n            this._closeInteractionType = null;\n          } else {\n            focusTargetElement.focus();\n          }\n        }\n      }\n      if (this._focusTrap) {\n        this._focusTrap.destroy();\n      }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n      // Note that there is no focus method when rendering on the server.\n      this._elementRef.nativeElement.focus?.(options);\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n      const element = this._elementRef.nativeElement;\n      const activeElement = _getFocusedElementPierceShadowDom();\n      return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n      if (this._platform.isBrowser) {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        // Save the previously focused element. This element will be re-focused\n        // when the dialog closes.\n        if (this._document) {\n          this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n        }\n      }\n    }\n    static ɵfac = function CdkDialogContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDialogContainer)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkDialogContainer,\n      selectors: [[\"cdk-dialog-container\"]],\n      viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n      hostVars: 6,\n      hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkPortalOutlet\", \"\"]],\n      template: function CdkDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return CdkDialogContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  overlayRef;\n  config;\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Instance of the container that is rendering out the dialog content. */\n  containerInstance;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Emits when the dialog has been closed. */\n  closed = /*#__PURE__*/new Subject();\n  /** Emits when the backdrop of the dialog is clicked. */\n  backdropClick;\n  /** Emits when on keyboard events within the dialog. */\n  keydownEvents;\n  /** Emits on pointer events that happen outside of the dialog. */\n  outsidePointerEvents;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subscription to external detachments of the dialog. */\n  _detachSubscription;\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose && this._canClose()) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      } else {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this.containerInstance._recaptureFocus?.();\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this._canClose(result)) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n  /** Whether the dialog is allowed to close. */\n  _canClose(result) {\n    const config = this.config;\n    return !!this.containerInstance && (!config.closePredicate || config.closePredicate(result, config, this.componentInstance));\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createBlockScrollStrategy(injector);\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = /*#__PURE__*/new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = /*#__PURE__*/new InjectionToken('DefaultDialogConfig');\nfunction getDirectionality(value) {\n  const valueSignal = signal(value);\n  const change = new EventEmitter();\n  return {\n    valueSignal,\n    get value() {\n      return valueSignal();\n    },\n    change,\n    ngOnDestroy() {\n      change.complete();\n    }\n  };\n}\nlet Dialog = /*#__PURE__*/(() => {\n  class Dialog {\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, {\n      optional: true\n    });\n    _parentDialog = inject(Dialog, {\n      optional: true,\n      skipSelf: true\n    });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() {}\n    open(componentOrTemplateRef, config) {\n      const defaults = this._defaultOptions || new DialogConfig();\n      config = {\n        ...defaults,\n        ...config\n      };\n      config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n      if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n      }\n      const overlayConfig = this._getOverlayConfig(config);\n      const overlayRef = createOverlayRef(this._injector, overlayConfig);\n      const dialogRef = new DialogRef(overlayRef, config);\n      const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n      dialogRef.containerInstance = dialogContainer;\n      this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n      // If this is the first dialog that we're opening, hide all the non-overlay content.\n      if (!this.openDialogs.length) {\n        this._hideNonDialogContentFromAssistiveTechnology();\n      }\n      this.openDialogs.push(dialogRef);\n      dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n      this.afterOpened.next(dialogRef);\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n      reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n      // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n      // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n      // determines when `aria-hidden` is removed from elements outside the dialog.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => {\n        // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n        if (dialog.config.closeOnDestroy === false) {\n          this._removeOpenDialog(dialog, false);\n        }\n      });\n      // Make a second pass and close the remaining dialogs. We do this second pass in order to\n      // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n      // that should be closed and dialogs that should not.\n      reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n      this._afterAllClosedAtThisLevel.complete();\n      this._afterOpenedAtThisLevel.complete();\n      this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n      const state = new OverlayConfig({\n        positionStrategy: config.positionStrategy || createGlobalPositionStrategy().centerHorizontally().centerVertically(),\n        scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n        panelClass: config.panelClass,\n        hasBackdrop: config.hasBackdrop,\n        direction: config.direction,\n        minWidth: config.minWidth,\n        minHeight: config.minHeight,\n        maxWidth: config.maxWidth,\n        maxHeight: config.maxHeight,\n        width: config.width,\n        height: config.height,\n        disposeOnNavigation: config.closeOnNavigation,\n        disableAnimations: config.disableAnimations\n      });\n      if (config.backdropClass) {\n        state.backdropClass = config.backdropClass;\n      }\n      return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DialogConfig,\n        useValue: config\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }, {\n        provide: OverlayRef,\n        useValue: overlay\n      }];\n      let containerType;\n      if (config.container) {\n        if (typeof config.container === 'function') {\n          containerType = config.container;\n        } else {\n          containerType = config.container.type;\n          providers.push(...config.container.providers(config));\n        }\n      } else {\n        containerType = CdkDialogContainer;\n      }\n      const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n        parent: userInjector || this._injector,\n        providers\n      }));\n      const containerRef = overlay.attach(containerPortal);\n      return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n      if (componentOrTemplateRef instanceof TemplateRef) {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n        let context = {\n          $implicit: config.data,\n          dialogRef\n        };\n        if (config.templateContext) {\n          context = {\n            ...context,\n            ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n          };\n        }\n        dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n      } else {\n        const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n        const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n        dialogRef.componentRef = contentRef;\n        dialogRef.componentInstance = contentRef.instance;\n      }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n      const userInjector = config.injector || config.viewContainerRef?.injector;\n      const providers = [{\n        provide: DIALOG_DATA,\n        useValue: config.data\n      }, {\n        provide: DialogRef,\n        useValue: dialogRef\n      }];\n      if (config.providers) {\n        if (typeof config.providers === 'function') {\n          providers.push(...config.providers(dialogRef, config, dialogContainer));\n        } else {\n          providers.push(...config.providers);\n        }\n      }\n      if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n        optional: true\n      }))) {\n        providers.push({\n          provide: Directionality,\n          useValue: getDirectionality(config.direction)\n        });\n      }\n      return Injector.create({\n        parent: userInjector || fallbackInjector,\n        providers\n      });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        // If all the dialogs were closed, remove/restore the `aria-hidden`\n        // to a the siblings and emit to the `afterAllClosed` stream.\n        if (!this.openDialogs.length) {\n          this._ariaHiddenElements.forEach((previousValue, element) => {\n            if (previousValue) {\n              element.setAttribute('aria-hidden', previousValue);\n            } else {\n              element.removeAttribute('aria-hidden');\n            }\n          });\n          this._ariaHiddenElements.clear();\n          if (emitEvent) {\n            this._getAfterAllClosed().next();\n          }\n        }\n      }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n      const overlayContainer = this._overlayContainer.getContainerElement();\n      // Ensure that the overlay container is attached to the DOM.\n      if (overlayContainer.parentElement) {\n        const siblings = overlayContainer.parentElement.children;\n        for (let i = siblings.length - 1; i > -1; i--) {\n          const sibling = siblings[i];\n          if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n            this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n            sibling.setAttribute('aria-hidden', 'true');\n          }\n        }\n      }\n    }\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dialog)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Dialog,\n      factory: Dialog.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return Dialog;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nlet DialogModule = /*#__PURE__*/(() => {\n  class DialogModule {\n    static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Dialog],\n      imports: [OverlayModule, PortalModule, A11yModule,\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule]\n    });\n  }\n  return DialogModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "NgZone", "Renderer2", "DOCUMENT", "ChangeDetectorRef", "Injector", "afterNextRender", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "Injectable", "signal", "EventEmitter", "NgModule", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "CdkDialogContainer_ng_template_0_Template", "rf", "ctx", "CdkPortal", "ɵɵCdkPortal", "PortalHostDirective", "ɵɵPortalHostDirective", "TemplatePortalDirective", "ɵɵTemplatePortalDirective", "F", "FocusTrapFactory", "I", "InteractivityChecker", "A", "A11yModule", "FocusMonitor", "P", "Platform", "c", "_getFocusedElementPierceShadowDom", "Subject", "defer", "g", "ESCAPE", "hasModifierKey", "startWith", "s", "createBlockScrollStrategy", "O", "OverlayContainer", "createOverlayRef", "i", "OverlayConfig", "f", "createGlobalPositionStrategy", "d", "OverlayRef", "t", "OverlayModule", "_", "_IdGenerator", "D", "Directionality", "DialogConfig", "viewContainerRef", "injector", "id", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "closePredicate", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "positionStrategy", "data", "direction", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "scrollStrategy", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "disableAnimations", "providers", "container", "templateContext", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_focusMonitor", "_renderer", "_platform", "_document", "optional", "_portalOutlet", "_focusTrap", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "_ariaLabelledByQueue", "_changeDetectorRef", "_injector", "_isDestroyed", "constructor", "push", "_addAriaLabelledBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_removeAriaLabelledBy", "index", "indexOf", "splice", "_contentAttached", "_initializeFocusTrap", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "portal", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "attachTemplatePortal", "attachDomPortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "deregisterBlur", "deregisterMousedown", "removeAttribute", "listen", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "focusedSuccessfully", "focusInitialElement", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "create", "ɵfac", "CdkDialogContainer_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "CdkDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkDialogContainer_HostBindings", "ɵɵattribute", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CdkDialogContainer_Template", "ɵɵtemplate", "dependencies", "styles", "encapsulation", "DialogRef", "overlayRef", "config", "componentInstance", "componentRef", "containerInstance", "closed", "backdropClick", "keydownEvents", "outsidePointerEvents", "_detachSubscription", "subscribe", "event", "keyCode", "preventDefault", "close", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "_canClose", "detachments", "closedSubject", "unsubscribe", "dispose", "next", "complete", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "getDirectionality", "value", "valueSignal", "change", "Dialog", "_defaultOptions", "_parentDialog", "skipSelf", "_overlayContainer", "_idGenerator", "_openDialogsAtThisLevel", "_afterAllClosedAtThisLevel", "_afterOpenedAtThisLevel", "_ariaHiddenElements", "Map", "_scrollStrategy", "openDialogs", "afterOpened", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "open", "componentOrTemplateRef", "defaults", "getId", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "centerHorizontally", "centerVertically", "disposeOnNavigation", "overlay", "userInjector", "provide", "useValue", "containerType", "containerPortal", "parent", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "contentRef", "fallbackInjector", "get", "emitEvent", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "Dialog_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "items", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "ɵɵCdkPortalOutlet"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, DOCUMENT, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, signal, EventEmitter, NgModule } from '@angular/core';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from './portal.mjs';\nexport { CdkPortal as ɵɵCdkPortal, PortalHostDirective as ɵɵPortalHostDirective, TemplatePortalDirective as ɵɵTemplatePortalDirective } from './portal.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-DHa4AVFz.mjs';\nimport { F as FocusMonitor } from './focus-monitor-DLjkiju1.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { s as createBlockScrollStrategy, O as OverlayContainer, c as createOverlayRef, i as OverlayConfig, f as createGlobalPositionStrategy, d as OverlayRef, t as OverlayModule } from './overlay-module-Bd2UplUU.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport '@angular/common';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    disableClose = false;\n    /** Function used to determine whether the dialog is allowed to close. */\n    closePredicate;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n    positionStrategy;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    restoreFocus = true;\n    /**\n     * Scroll strategy to be used for the dialog. This determines how\n     * the dialog responds to scrolling underneath the panel element.\n     */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    closeOnOverlayDetachments = true;\n    /**\n     * Whether the built-in overlay animations should be disabled.\n     */\n    disableAnimations = false;\n    /**\n     * Providers that will be exposed to the contents of the dialog. Can also\n     * be provided as a function in order to generate the providers lazily.\n     */\n    providers;\n    /**\n     * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n     * A configuration object can be passed in to customize the providers that will be exposed\n     * to the dialog container.\n     */\n    container;\n    /**\n     * Context that will be passed to template-based dialogs.\n     * A function can be passed in to resolve the context lazily.\n     */\n    templateContext;\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, { optional: true });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        // Callback is primarily for some internal tests\n        // that were instantiating the dialog container manually.\n        this._config = (inject(DialogConfig, { optional: true }) || new DialogConfig());\n        if (this._config.ariaLabelledBy) {\n            this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n        }\n    }\n    _addAriaLabelledBy(id) {\n        this._ariaLabelledByQueue.push(id);\n        this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n        const index = this._ariaLabelledByQueue.indexOf(id);\n        if (index > -1) {\n            this._ariaLabelledByQueue.splice(index, 1);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._isDestroyed = true;\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._contentAttached();\n        return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    deregisterBlur();\n                    deregisterMousedown();\n                    element.removeAttribute('tabindex');\n                };\n                const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n                const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n        if (this._isDestroyed) {\n            return;\n        }\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait until after the next render.\n        afterNextRender(() => {\n            const element = this._elementRef.nativeElement;\n            switch (this._config.autoFocus) {\n                case false:\n                case 'dialog':\n                    // Ensure that focus is on the dialog container. It's possible that a different\n                    // component tried to move focus while the open animation was running. See:\n                    // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                    // if the focus isn't inside the dialog already, because it's possible that the consumer\n                    // turned off `autoFocus` in order to move focus themselves.\n                    if (!this._containsFocus()) {\n                        element.focus(options);\n                    }\n                    break;\n                case true:\n                case 'first-tabbable':\n                    const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer(options);\n                    }\n                    break;\n                case 'first-heading':\n                    this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n                    break;\n                default:\n                    this._focusByCssSelector(this._config.autoFocus, options);\n                    break;\n            }\n        }, { injector: this._injector });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n        // Note that there is no focus method when rendering on the server.\n        this._elementRef.nativeElement.focus?.(options);\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            // Save the previously focused element. This element will be re-focused\n            // when the dialog closes.\n            if (this._document) {\n                this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkDialogContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkDialogContainer, isStandalone: true, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    overlayRef;\n    config;\n    /**\n     * Instance of component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Instance of the container that is rendering out the dialog content. */\n    containerInstance;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Emits when the dialog has been closed. */\n    closed = new Subject();\n    /** Emits when the backdrop of the dialog is clicked. */\n    backdropClick;\n    /** Emits when on keyboard events within the dialog. */\n    keydownEvents;\n    /** Emits on pointer events that happen outside of the dialog. */\n    outsidePointerEvents;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subscription to external detachments of the dialog. */\n    _detachSubscription;\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose && this._canClose()) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n            else {\n                // Clicking on the backdrop will move focus out of dialog.\n                // Recapture it if closing via the backdrop is disabled.\n                this.containerInstance._recaptureFocus?.();\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this._canClose(result)) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n    /** Whether the dialog is allowed to close. */\n    _canClose(result) {\n        const config = this.config;\n        return (!!this.containerInstance &&\n            (!config.closePredicate || config.closePredicate(result, config, this.componentInstance)));\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createBlockScrollStrategy(injector);\n    },\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n\nfunction getDirectionality(value) {\n    const valueSignal = signal(value);\n    const change = new EventEmitter();\n    return {\n        valueSignal,\n        get value() {\n            return valueSignal();\n        },\n        change,\n        ngOnDestroy() {\n            change.complete();\n        },\n    };\n}\nclass Dialog {\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, { optional: true });\n    _parentDialog = inject(Dialog, { optional: true, skipSelf: true });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() { }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = createOverlayRef(this._injector, overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                createGlobalPositionStrategy().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n            disableAnimations: config.disableAnimations,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }));\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: getDirectionality(config.direction),\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, providers: [Dialog], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    providers: [Dialog],\n                }]\n        }] });\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxQ,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAChH,SAASC,SAAS,IAAIC,WAAW,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,uBAAuB,IAAIC,yBAAyB,QAAQ,cAAc;AAC3J,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC9G,SAASL,CAAC,IAAIM,YAAY,QAAQ,8BAA8B;AAChE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEX,CAAC,IAAIY,gBAAgB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,aAAa,QAAQ,+BAA+B;AACxN,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,OAAO,6BAA6B;AACpC,OAAO,eAAe;AACtB,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,wBAAwB;AAC/B,OAAO,qCAAqC;AAC5C,OAAO,kCAAkC;AACzC,OAAO,iBAAiB;AACxB,OAAO,iCAAiC;AACxC,OAAO,gCAAgC;AACvC,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,YAAY;AACnB,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;;AAEnC;AACA,MAAMC,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;EACAC,EAAE;EACF;EACAC,IAAI,GAAG,QAAQ;EACf;EACAC,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,aAAa,GAAG,EAAE;EAClB;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,cAAc;EACd;EACAC,KAAK,GAAG,EAAE;EACV;EACAC,MAAM,GAAG,EAAE;EACX;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,gBAAgB;EAChB;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,SAAS;EACT;EACAC,eAAe,GAAG,IAAI;EACtB;EACAC,cAAc,GAAG,IAAI;EACrB;EACAC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,gBAAgB;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;AACA;AACA;EACIC,yBAAyB,GAAG,IAAI;EAChC;AACJ;AACA;EACIC,iBAAiB,GAAG,KAAK;EACzB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,eAAe;AACnB;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AAHA,IAIMC,kBAAkB;EAAxB,MAAMA,kBAAkB,SAASnF,gBAAgB,CAAC;IAC9CoF,WAAW,GAAGtG,MAAM,CAACC,UAAU,CAAC;IAChCsG,iBAAiB,GAAGvG,MAAM,CAACiC,gBAAgB,CAAC;IAC5CuE,OAAO;IACPC,qBAAqB,GAAGzG,MAAM,CAACmC,oBAAoB,CAAC;IACpDuE,OAAO,GAAG1G,MAAM,CAACE,MAAM,CAAC;IACxByG,aAAa,GAAG3G,MAAM,CAACsC,YAAY,CAAC;IACpCsE,SAAS,GAAG5G,MAAM,CAACG,SAAS,CAAC;IAC7B0G,SAAS,GAAG7G,MAAM,CAACwC,QAAQ,CAAC;IAC5BsE,SAAS,GAAG9G,MAAM,CAACI,QAAQ,EAAE;MAAE2G,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChD;IACAC,aAAa;IACb;IACAC,UAAU,GAAG,IAAI;IACjB;IACAC,oCAAoC,GAAG,IAAI;IAC3C;AACJ;AACA;AACA;AACA;IACIC,qBAAqB,GAAG,IAAI;IAC5B;AACJ;AACA;AACA;AACA;AACA;IACIC,oBAAoB,GAAG,EAAE;IACzBC,kBAAkB,GAAGrH,MAAM,CAACK,iBAAiB,CAAC;IAC9CiH,SAAS,GAAGtH,MAAM,CAACM,QAAQ,CAAC;IAC5BiH,YAAY,GAAG,KAAK;IACpBC,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP;MACA;MACA,IAAI,CAAChB,OAAO,GAAIxG,MAAM,CAACkE,YAAY,EAAE;QAAE6C,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI,IAAI7C,YAAY,CAAC,CAAE;MAC/E,IAAI,IAAI,CAACsC,OAAO,CAAClB,cAAc,EAAE;QAC7B,IAAI,CAAC8B,oBAAoB,CAACK,IAAI,CAAC,IAAI,CAACjB,OAAO,CAAClB,cAAc,CAAC;MAC/D;IACJ;IACAoC,kBAAkBA,CAACrD,EAAE,EAAE;MACnB,IAAI,CAAC+C,oBAAoB,CAACK,IAAI,CAACpD,EAAE,CAAC;MAClC,IAAI,CAACgD,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;IACAC,qBAAqBA,CAACvD,EAAE,EAAE;MACtB,MAAMwD,KAAK,GAAG,IAAI,CAACT,oBAAoB,CAACU,OAAO,CAACzD,EAAE,CAAC;MACnD,IAAIwD,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACT,oBAAoB,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC1C,IAAI,CAACR,kBAAkB,CAACM,YAAY,CAAC,CAAC;MAC1C;IACJ;IACAK,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC/B;IACA;AACJ;AACA;AACA;IACIA,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACb,YAAY,GAAG,IAAI;MACxB,IAAI,CAACc,aAAa,CAAC,CAAC;IACxB;IACA;AACJ;AACA;AACA;IACIC,qBAAqBA,CAACC,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFtC,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAACsB,qBAAqB,CAACC,MAAM,CAAC;MAC/D,IAAI,CAACP,gBAAgB,CAAC,CAAC;MACvB,OAAOU,MAAM;IACjB;IACA;AACJ;AACA;AACA;IACIC,oBAAoBA,CAACJ,MAAM,EAAE;MACzB,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFtC,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAAC2B,oBAAoB,CAACJ,MAAM,CAAC;MAC9D,IAAI,CAACP,gBAAgB,CAAC,CAAC;MACvB,OAAOU,MAAM;IACjB;IACA;AACJ;AACA;AACA;AACA;AACA;IACIE,eAAe,GAAIL,MAAM,IAAK;MAC1B,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFtC,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAAC4B,eAAe,CAACL,MAAM,CAAC;MACzD,IAAI,CAACP,gBAAgB,CAAC,CAAC;MACvB,OAAOU,MAAM;IACjB,CAAC;IACD;IACA;IACAG,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;QACxB,IAAI,CAACX,UAAU,CAAC,CAAC;MACrB;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIY,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACxC,qBAAqB,CAACyC,WAAW,CAACF,OAAO,CAAC,EAAE;QAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;QACrB;QACA,IAAI,CAACzC,OAAO,CAAC0C,iBAAiB,CAAC,MAAM;UACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;YACnBC,cAAc,CAAC,CAAC;YAChBC,mBAAmB,CAAC,CAAC;YACrBP,OAAO,CAACQ,eAAe,CAAC,UAAU,CAAC;UACvC,CAAC;UACD,MAAMF,cAAc,GAAG,IAAI,CAAC1C,SAAS,CAAC6C,MAAM,CAACT,OAAO,EAAE,MAAM,EAAEK,QAAQ,CAAC;UACvE,MAAME,mBAAmB,GAAG,IAAI,CAAC3C,SAAS,CAAC6C,MAAM,CAACT,OAAO,EAAE,WAAW,EAAEK,QAAQ,CAAC;QACrF,CAAC,CAAC;MACN;MACAL,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;IAC1B;IACA;AACJ;AACA;AACA;IACIU,mBAAmBA,CAACC,QAAQ,EAAEX,OAAO,EAAE;MACnC,IAAIY,cAAc,GAAG,IAAI,CAACvD,WAAW,CAACwD,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;MAC3E,IAAIC,cAAc,EAAE;QAChB,IAAI,CAACd,WAAW,CAACc,cAAc,EAAEZ,OAAO,CAAC;MAC7C;IACJ;IACA;AACJ;AACA;AACA;IACId,UAAUA,CAACc,OAAO,EAAE;MAChB,IAAI,IAAI,CAAC1B,YAAY,EAAE;QACnB;MACJ;MACA;MACA;MACA;MACAhH,eAAe,CAAC,MAAM;QAClB,MAAMyI,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;QAC9C,QAAQ,IAAI,CAACtD,OAAO,CAACf,SAAS;UAC1B,KAAK,KAAK;UACV,KAAK,QAAQ;YACT;YACA;YACA;YACA;YACA;YACA,IAAI,CAAC,IAAI,CAACqD,cAAc,CAAC,CAAC,EAAE;cACxBE,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;YAC1B;YACA;UACJ,KAAK,IAAI;UACT,KAAK,gBAAgB;YACjB,MAAMe,mBAAmB,GAAG,IAAI,CAAC/C,UAAU,EAAEgD,mBAAmB,CAAChB,OAAO,CAAC;YACzE;YACA;YACA,IAAI,CAACe,mBAAmB,EAAE;cACtB,IAAI,CAACE,qBAAqB,CAACjB,OAAO,CAAC;YACvC;YACA;UACJ,KAAK,eAAe;YAChB,IAAI,CAACU,mBAAmB,CAAC,0CAA0C,EAAEV,OAAO,CAAC;YAC7E;UACJ;YACI,IAAI,CAACU,mBAAmB,CAAC,IAAI,CAACnD,OAAO,CAACf,SAAS,EAAEwD,OAAO,CAAC;YACzD;QACR;MACJ,CAAC,EAAE;QAAE7E,QAAQ,EAAE,IAAI,CAACkD;MAAU,CAAC,CAAC;IACpC;IACA;IACAe,aAAaA,CAAA,EAAG;MACZ,MAAM8B,WAAW,GAAG,IAAI,CAAC3D,OAAO,CAACd,YAAY;MAC7C,IAAI0E,kBAAkB,GAAG,IAAI;MAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;QACjCC,kBAAkB,GAAG,IAAI,CAACtD,SAAS,CAACiD,aAAa,CAACI,WAAW,CAAC;MAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;QACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAACjD,oCAAoC,GAAG,IAAI;MACvF,CAAC,MACI,IAAIiD,WAAW,EAAE;QAClBC,kBAAkB,GAAGD,WAAW;MACpC;MACA;MACA,IAAI,IAAI,CAAC3D,OAAO,CAACd,YAAY,IACzB0E,kBAAkB,IAClB,OAAOA,kBAAkB,CAACV,KAAK,KAAK,UAAU,EAAE;QAChD,MAAMW,aAAa,GAAG3H,iCAAiC,CAAC,CAAC;QACzD,MAAMsG,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;QAC9C;QACA;QACA;QACA;QACA,IAAI,CAACO,aAAa,IACdA,aAAa,KAAK,IAAI,CAACvD,SAAS,CAACwD,IAAI,IACrCD,aAAa,KAAKrB,OAAO,IACzBA,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC,EAAE;UACjC,IAAI,IAAI,CAAC1D,aAAa,EAAE;YACpB,IAAI,CAACA,aAAa,CAAC6D,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAACjD,qBAAqB,CAAC;YAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;UACrC,CAAC,MACI;YACDiD,kBAAkB,CAACV,KAAK,CAAC,CAAC;UAC9B;QACJ;MACJ;MACA,IAAI,IAAI,CAACzC,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACwD,OAAO,CAAC,CAAC;MAC7B;IACJ;IACA;IACAP,qBAAqBA,CAACjB,OAAO,EAAE;MAC3B;MACA,IAAI,CAAC3C,WAAW,CAACwD,aAAa,CAACJ,KAAK,GAAGT,OAAO,CAAC;IACnD;IACA;IACAH,cAAcA,CAAA,EAAG;MACb,MAAME,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;MAC9C,MAAMO,aAAa,GAAG3H,iCAAiC,CAAC,CAAC;MACzD,OAAOsG,OAAO,KAAKqB,aAAa,IAAIrB,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC;IACvE;IACA;IACApC,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACpB,SAAS,CAAC6D,SAAS,EAAE;QAC1B,IAAI,CAACzD,UAAU,GAAG,IAAI,CAACV,iBAAiB,CAACoE,MAAM,CAAC,IAAI,CAACrE,WAAW,CAACwD,aAAa,CAAC;QAC/E;QACA;QACA,IAAI,IAAI,CAAChD,SAAS,EAAE;UAChB,IAAI,CAACI,oCAAoC,GAAGxE,iCAAiC,CAAC,CAAC;QACnF;MACJ;IACJ;IACA,OAAOkI,IAAI,YAAAC,2BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFzE,kBAAkB;IAAA;IACrH,OAAO0E,IAAI,kBAD8EhL,EAAE,CAAAiL,iBAAA;MAAAC,IAAA,EACJ5E,kBAAkB;MAAA6E,SAAA;MAAAC,SAAA,WAAAC,yBAAA5J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADhBzB,EAAE,CAAAsL,WAAA,CACygBlK,eAAe;QAAA;QAAA,IAAAK,EAAA;UAAA,IAAA8J,EAAA;UAD1hBvL,EAAE,CAAAwL,cAAA,CAAAD,EAAA,GAAFvL,EAAE,CAAAyL,WAAA,QAAA/J,GAAA,CAAAuF,aAAA,GAAAsE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,eACwG,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAArK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD9GzB,EAAE,CAAA+L,WAAA,OAAArK,GAAA,CAAA+E,OAAA,CAAAnC,EAAA,IACU,IAAI,UAAA5C,GAAA,CAAA+E,OAAA,CAAAlC,IAAA,gBAAA7C,GAAA,CAAA+E,OAAA,CAAAhB,SAAA,qBAAA/D,GAAA,CAAA+E,OAAA,CAAAjB,SAAA,GAAE,IAAI,GAAA9D,GAAA,CAAA2F,oBAAA,CAAwB,CAAC,iBAAA3F,GAAA,CAAA+E,OAAA,CAAAjB,SAAA,sBAAA9D,GAAA,CAAA+E,OAAA,CAAAnB,eAAA,IAAtB,IAAI;QAAA;MAAA;MAAA0G,QAAA,GAD7BhM,EAAE,CAAAiM,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAA7K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzB,EAAE,CAAAuM,UAAA,IAAA/K,yCAAA,wBAC4oB,CAAC;QAAA;MAAA;MAAAgL,YAAA,GAAgKpL,eAAe;MAAAqL,MAAA;MAAAC,aAAA;IAAA;EAC35B;EAAC,OA1PKpG,kBAAkB;AAAA;AA2PxB;EAAA,QAAAoC,SAAA,oBAAAA,SAAA;AAAA;;AAiBA;AACA;AACA;AACA,MAAMiE,SAAS,CAAC;EACZC,UAAU;EACVC,MAAM;EACN;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACAC,iBAAiB;EACjB;EACArI,YAAY;EACZ;EACAsI,MAAM,gBAAG,IAAIrK,OAAO,CAAC,CAAC;EACtB;EACAsK,aAAa;EACb;EACAC,aAAa;EACb;EACAC,oBAAoB;EACpB;EACA9I,EAAE;EACF;EACA+I,mBAAmB;EACnB5F,WAAWA,CAACmF,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClI,YAAY,GAAGkI,MAAM,CAAClI,YAAY;IACvC,IAAI,CAACuI,aAAa,GAAGN,UAAU,CAACM,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAGP,UAAU,CAACO,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGR,UAAU,CAACQ,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAAC9I,EAAE,GAAGuI,MAAM,CAACvI,EAAE,CAAC,CAAC;IACrB,IAAI,CAAC6I,aAAa,CAACG,SAAS,CAACC,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKzK,MAAM,IAAI,CAAC,IAAI,CAAC4B,YAAY,IAAI,CAAC3B,cAAc,CAACuK,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,aAAa,CAACI,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAAC3I,YAAY,IAAI,IAAI,CAACkJ,SAAS,CAAC,CAAC,EAAE;QACxC,IAAI,CAACH,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACZ,iBAAiB,CAAClE,eAAe,GAAG,CAAC;MAC9C;IACJ,CAAC,CAAC;IACF,IAAI,CAACuE,mBAAmB,GAAGT,UAAU,CAACkB,WAAW,CAAC,CAAC,CAACR,SAAS,CAAC,MAAM;MAChE;MACA,IAAIT,MAAM,CAAC9G,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAAC2H,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAAC/E,MAAM,EAAEO,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC2E,SAAS,CAAClF,MAAM,CAAC,EAAE;MACxB,MAAMoF,aAAa,GAAG,IAAI,CAACd,MAAM;MACjC,IAAI,CAACD,iBAAiB,CAAC5F,qBAAqB,GAAG8B,OAAO,EAAE0E,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACP,mBAAmB,CAACW,WAAW,CAAC,CAAC;MACtC,IAAI,CAACpB,UAAU,CAACqB,OAAO,CAAC,CAAC;MACzBF,aAAa,CAACG,IAAI,CAACvF,MAAM,CAAC;MAC1BoF,aAAa,CAACI,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACrB,iBAAiB,GAAG,IAAI,CAACE,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACxB,UAAU,CAACwB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAACxJ,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAAC8H,UAAU,CAACyB,UAAU,CAAC;MAAExJ,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACAwJ,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAAC3B,UAAU,CAAC0B,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAAC3B,UAAU,CAAC4B,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;EACA;EACAV,SAASA,CAAClF,MAAM,EAAE;IACd,MAAMkE,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,OAAQ,CAAC,CAAC,IAAI,CAACG,iBAAiB,KAC3B,CAACH,MAAM,CAACjI,cAAc,IAAIiI,MAAM,CAACjI,cAAc,CAAC+D,MAAM,EAAEkE,MAAM,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;EACjG;AACJ;;AAEA;AACA,MAAM2B,sBAAsB,gBAAG,IAAI5N,cAAc,CAAC,sBAAsB,EAAE;EACtE6N,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMtK,QAAQ,GAAGpE,MAAM,CAACM,QAAQ,CAAC;IACjC,OAAO,MAAM4C,yBAAyB,CAACkB,QAAQ,CAAC;EACpD;AACJ,CAAC,CAAC;AACF;AACA,MAAMuK,WAAW,gBAAG,IAAI/N,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAMgO,qBAAqB,gBAAG,IAAIhO,cAAc,CAAC,qBAAqB,CAAC;AAEvE,SAASiO,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,MAAMC,WAAW,GAAGhO,MAAM,CAAC+N,KAAK,CAAC;EACjC,MAAME,MAAM,GAAG,IAAIhO,YAAY,CAAC,CAAC;EACjC,OAAO;IACH+N,WAAW;IACX,IAAID,KAAKA,CAAA,EAAG;MACR,OAAOC,WAAW,CAAC,CAAC;IACxB,CAAC;IACDC,MAAM;IACN5G,WAAWA,CAAA,EAAG;MACV4G,MAAM,CAACd,QAAQ,CAAC,CAAC;IACrB;EACJ,CAAC;AACL;AAAC,IACKe,MAAM;EAAZ,MAAMA,MAAM,CAAC;IACT3H,SAAS,GAAGtH,MAAM,CAACM,QAAQ,CAAC;IAC5B4O,eAAe,GAAGlP,MAAM,CAAC4O,qBAAqB,EAAE;MAAE7H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACnEoI,aAAa,GAAGnP,MAAM,CAACiP,MAAM,EAAE;MAAElI,QAAQ,EAAE,IAAI;MAAEqI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClEC,iBAAiB,GAAGrP,MAAM,CAACoD,gBAAgB,CAAC;IAC5CkM,YAAY,GAAGtP,MAAM,CAAC+D,YAAY,CAAC;IACnCwL,uBAAuB,GAAG,EAAE;IAC5BC,0BAA0B,GAAG,IAAI7M,OAAO,CAAC,CAAC;IAC1C8M,uBAAuB,GAAG,IAAI9M,OAAO,CAAC,CAAC;IACvC+M,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/BC,eAAe,GAAG5P,MAAM,CAACwO,sBAAsB,CAAC;IAChD;IACA,IAAIqB,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACV,aAAa,GAAG,IAAI,CAACA,aAAa,CAACU,WAAW,GAAG,IAAI,CAACN,uBAAuB;IAC7F;IACA;IACA,IAAIO,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACX,aAAa,GAAG,IAAI,CAACA,aAAa,CAACW,WAAW,GAAG,IAAI,CAACL,uBAAuB;IAC7F;IACA;AACJ;AACA;AACA;IACIM,cAAc,GAAGnN,KAAK,CAAC,MAAM,IAAI,CAACiN,WAAW,CAACG,MAAM,GAC9C,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAAClN,SAAS,CAAC0K,SAAS,CAAC,CAAC,CAAC;IAC3DlG,WAAWA,CAAA,EAAG,CAAE;IAChB2I,IAAIA,CAACC,sBAAsB,EAAExD,MAAM,EAAE;MACjC,MAAMyD,QAAQ,GAAI,IAAI,CAACnB,eAAe,IAAI,IAAIhL,YAAY,CAAC,CAAE;MAC7D0I,MAAM,GAAG;QAAE,GAAGyD,QAAQ;QAAE,GAAGzD;MAAO,CAAC;MACnCA,MAAM,CAACvI,EAAE,GAAGuI,MAAM,CAACvI,EAAE,IAAI,IAAI,CAACiL,YAAY,CAACgB,KAAK,CAAC,aAAa,CAAC;MAC/D,IAAI1D,MAAM,CAACvI,EAAE,IACT,IAAI,CAACkM,aAAa,CAAC3D,MAAM,CAACvI,EAAE,CAAC,KAC5B,OAAOoE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMrC,KAAK,CAAC,mBAAmBwG,MAAM,CAACvI,EAAE,iDAAiD,CAAC;MAC9F;MACA,MAAMmM,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC7D,MAAM,CAAC;MACpD,MAAMD,UAAU,GAAGtJ,gBAAgB,CAAC,IAAI,CAACiE,SAAS,EAAEkJ,aAAa,CAAC;MAClE,MAAME,SAAS,GAAG,IAAIhE,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;MACnD,MAAM+D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACjE,UAAU,EAAE+D,SAAS,EAAE9D,MAAM,CAAC;MAC5E8D,SAAS,CAAC3D,iBAAiB,GAAG4D,eAAe;MAC7C,IAAI,CAACE,oBAAoB,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAE/D,MAAM,CAAC;MACrF;MACA,IAAI,CAAC,IAAI,CAACiD,WAAW,CAACG,MAAM,EAAE;QAC1B,IAAI,CAACc,4CAA4C,CAAC,CAAC;MACvD;MACA,IAAI,CAACjB,WAAW,CAACpI,IAAI,CAACiJ,SAAS,CAAC;MAChCA,SAAS,CAAC1D,MAAM,CAACK,SAAS,CAAC,MAAM,IAAI,CAAC0D,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC,CAAC;MACzE,IAAI,CAACZ,WAAW,CAAC7B,IAAI,CAACyC,SAAS,CAAC;MAChC,OAAOA,SAAS;IACpB;IACA;AACJ;AACA;IACIM,QAAQA,CAAA,EAAG;MACPC,cAAc,CAAC,IAAI,CAACpB,WAAW,EAAEqB,MAAM,IAAIA,MAAM,CAACzD,KAAK,CAAC,CAAC,CAAC;IAC9D;IACA;AACJ;AACA;AACA;IACI8C,aAAaA,CAAClM,EAAE,EAAE;MACd,OAAO,IAAI,CAACwL,WAAW,CAACsB,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAC7M,EAAE,KAAKA,EAAE,CAAC;IAC5D;IACA+D,WAAWA,CAAA,EAAG;MACV;MACA;MACA;MACA6I,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAI;QACnD;QACA,IAAIA,MAAM,CAACtE,MAAM,CAAC/G,cAAc,KAAK,KAAK,EAAE;UACxC,IAAI,CAACkL,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;QACzC;MACJ,CAAC,CAAC;MACF;MACA;MACA;MACAD,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAIA,MAAM,CAACzD,KAAK,CAAC,CAAC,CAAC;MACtE,IAAI,CAAC+B,0BAA0B,CAACtB,QAAQ,CAAC,CAAC;MAC1C,IAAI,CAACuB,uBAAuB,CAACvB,QAAQ,CAAC,CAAC;MACvC,IAAI,CAACqB,uBAAuB,GAAG,EAAE;IACrC;IACA;AACJ;AACA;AACA;AACA;IACIkB,iBAAiBA,CAAC7D,MAAM,EAAE;MACtB,MAAMwE,KAAK,GAAG,IAAI7N,aAAa,CAAC;QAC5B2B,gBAAgB,EAAE0H,MAAM,CAAC1H,gBAAgB,IACrCzB,4BAA4B,CAAC,CAAC,CAAC4N,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;QAC1E3L,cAAc,EAAEiH,MAAM,CAACjH,cAAc,IAAI,IAAI,CAACiK,eAAe,CAAC,CAAC;QAC/DrL,UAAU,EAAEqI,MAAM,CAACrI,UAAU;QAC7BC,WAAW,EAAEoI,MAAM,CAACpI,WAAW;QAC/BY,SAAS,EAAEwH,MAAM,CAACxH,SAAS;QAC3BN,QAAQ,EAAE8H,MAAM,CAAC9H,QAAQ;QACzBC,SAAS,EAAE6H,MAAM,CAAC7H,SAAS;QAC3BC,QAAQ,EAAE4H,MAAM,CAAC5H,QAAQ;QACzBC,SAAS,EAAE2H,MAAM,CAAC3H,SAAS;QAC3BL,KAAK,EAAEgI,MAAM,CAAChI,KAAK;QACnBC,MAAM,EAAE+H,MAAM,CAAC/H,MAAM;QACrB0M,mBAAmB,EAAE3E,MAAM,CAAChH,iBAAiB;QAC7CG,iBAAiB,EAAE6G,MAAM,CAAC7G;MAC9B,CAAC,CAAC;MACF,IAAI6G,MAAM,CAACnI,aAAa,EAAE;QACtB2M,KAAK,CAAC3M,aAAa,GAAGmI,MAAM,CAACnI,aAAa;MAC9C;MACA,OAAO2M,KAAK;IAChB;IACA;AACJ;AACA;AACA;AACA;AACA;IACIR,gBAAgBA,CAACY,OAAO,EAAEd,SAAS,EAAE9D,MAAM,EAAE;MACzC,MAAM6E,YAAY,GAAG7E,MAAM,CAACxI,QAAQ,IAAIwI,MAAM,CAACzI,gBAAgB,EAAEC,QAAQ;MACzE,MAAM4B,SAAS,GAAG,CACd;QAAE0L,OAAO,EAAExN,YAAY;QAAEyN,QAAQ,EAAE/E;MAAO,CAAC,EAC3C;QAAE8E,OAAO,EAAEhF,SAAS;QAAEiF,QAAQ,EAAEjB;MAAU,CAAC,EAC3C;QAAEgB,OAAO,EAAE/N,UAAU;QAAEgO,QAAQ,EAAEH;MAAQ,CAAC,CAC7C;MACD,IAAII,aAAa;MACjB,IAAIhF,MAAM,CAAC3G,SAAS,EAAE;QAClB,IAAI,OAAO2G,MAAM,CAAC3G,SAAS,KAAK,UAAU,EAAE;UACxC2L,aAAa,GAAGhF,MAAM,CAAC3G,SAAS;QACpC,CAAC,MACI;UACD2L,aAAa,GAAGhF,MAAM,CAAC3G,SAAS,CAACgF,IAAI;UACrCjF,SAAS,CAACyB,IAAI,CAAC,GAAGmF,MAAM,CAAC3G,SAAS,CAACD,SAAS,CAAC4G,MAAM,CAAC,CAAC;QACzD;MACJ,CAAC,MACI;QACDgF,aAAa,GAAGvL,kBAAkB;MACtC;MACA,MAAMwL,eAAe,GAAG,IAAIzQ,eAAe,CAACwQ,aAAa,EAAEhF,MAAM,CAACzI,gBAAgB,EAAE7D,QAAQ,CAACqK,MAAM,CAAC;QAAEmH,MAAM,EAAEL,YAAY,IAAI,IAAI,CAACnK,SAAS;QAAEtB;MAAU,CAAC,CAAC,CAAC;MAC3J,MAAM+L,YAAY,GAAGP,OAAO,CAACQ,MAAM,CAACH,eAAe,CAAC;MACpD,OAAOE,YAAY,CAACE,QAAQ;IAChC;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIpB,oBAAoBA,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAE/D,MAAM,EAAE;MAC7E,IAAIwD,sBAAsB,YAAYvP,WAAW,EAAE;QAC/C,MAAMuD,QAAQ,GAAG,IAAI,CAAC8N,eAAe,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAEjD,SAAS,CAAC;QACpF,IAAIyE,OAAO,GAAG;UAAEC,SAAS,EAAExF,MAAM,CAACzH,IAAI;UAAEuL;QAAU,CAAC;QACnD,IAAI9D,MAAM,CAAC1G,eAAe,EAAE;UACxBiM,OAAO,GAAG;YACN,GAAGA,OAAO;YACV,IAAI,OAAOvF,MAAM,CAAC1G,eAAe,KAAK,UAAU,GAC1C0G,MAAM,CAAC1G,eAAe,CAAC,CAAC,GACxB0G,MAAM,CAAC1G,eAAe;UAChC,CAAC;QACL;QACAyK,eAAe,CAAChI,oBAAoB,CAAC,IAAItH,cAAc,CAAC+O,sBAAsB,EAAE,IAAI,EAAE+B,OAAO,EAAE/N,QAAQ,CAAC,CAAC;MAC7G,CAAC,MACI;QACD,MAAMA,QAAQ,GAAG,IAAI,CAAC8N,eAAe,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAACrJ,SAAS,CAAC;QACzF,MAAM+K,UAAU,GAAG1B,eAAe,CAACrI,qBAAqB,CAAC,IAAIlH,eAAe,CAACgP,sBAAsB,EAAExD,MAAM,CAACzI,gBAAgB,EAAEC,QAAQ,CAAC,CAAC;QACxIsM,SAAS,CAAC5D,YAAY,GAAGuF,UAAU;QACnC3B,SAAS,CAAC7D,iBAAiB,GAAGwF,UAAU,CAACJ,QAAQ;MACrD;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,eAAeA,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAE2B,gBAAgB,EAAE;MAClE,MAAMb,YAAY,GAAG7E,MAAM,CAACxI,QAAQ,IAAIwI,MAAM,CAACzI,gBAAgB,EAAEC,QAAQ;MACzE,MAAM4B,SAAS,GAAG,CACd;QAAE0L,OAAO,EAAE/C,WAAW;QAAEgD,QAAQ,EAAE/E,MAAM,CAACzH;MAAK,CAAC,EAC/C;QAAEuM,OAAO,EAAEhF,SAAS;QAAEiF,QAAQ,EAAEjB;MAAU,CAAC,CAC9C;MACD,IAAI9D,MAAM,CAAC5G,SAAS,EAAE;QAClB,IAAI,OAAO4G,MAAM,CAAC5G,SAAS,KAAK,UAAU,EAAE;UACxCA,SAAS,CAACyB,IAAI,CAAC,GAAGmF,MAAM,CAAC5G,SAAS,CAAC0K,SAAS,EAAE9D,MAAM,EAAE+D,eAAe,CAAC,CAAC;QAC3E,CAAC,MACI;UACD3K,SAAS,CAACyB,IAAI,CAAC,GAAGmF,MAAM,CAAC5G,SAAS,CAAC;QACvC;MACJ;MACA,IAAI4G,MAAM,CAACxH,SAAS,KACf,CAACqM,YAAY,IACV,CAACA,YAAY,CAACc,GAAG,CAACtO,cAAc,EAAE,IAAI,EAAE;QAAE8C,QAAQ,EAAE;MAAK,CAAC,CAAC,CAAC,EAAE;QAClEf,SAAS,CAACyB,IAAI,CAAC;UACXiK,OAAO,EAAEzN,cAAc;UACvB0N,QAAQ,EAAE9C,iBAAiB,CAACjC,MAAM,CAACxH,SAAS;QAChD,CAAC,CAAC;MACN;MACA,OAAO9E,QAAQ,CAACqK,MAAM,CAAC;QAAEmH,MAAM,EAAEL,YAAY,IAAIa,gBAAgB;QAAEtM;MAAU,CAAC,CAAC;IACnF;IACA;AACJ;AACA;AACA;AACA;IACI+K,iBAAiBA,CAACL,SAAS,EAAE8B,SAAS,EAAE;MACpC,MAAM3K,KAAK,GAAG,IAAI,CAACgI,WAAW,CAAC/H,OAAO,CAAC4I,SAAS,CAAC;MACjD,IAAI7I,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACgI,WAAW,CAAC9H,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC;QACA;QACA,IAAI,CAAC,IAAI,CAACgI,WAAW,CAACG,MAAM,EAAE;UAC1B,IAAI,CAACN,mBAAmB,CAAC+C,OAAO,CAAC,CAACC,aAAa,EAAE1J,OAAO,KAAK;YACzD,IAAI0J,aAAa,EAAE;cACf1J,OAAO,CAAC2J,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;YACtD,CAAC,MACI;cACD1J,OAAO,CAACQ,eAAe,CAAC,aAAa,CAAC;YAC1C;UACJ,CAAC,CAAC;UACF,IAAI,CAACkG,mBAAmB,CAACkD,KAAK,CAAC,CAAC;UAChC,IAAIJ,SAAS,EAAE;YACX,IAAI,CAACvC,kBAAkB,CAAC,CAAC,CAAChC,IAAI,CAAC,CAAC;UACpC;QACJ;MACJ;IACJ;IACA;IACA6C,4CAA4CA,CAAA,EAAG;MAC3C,MAAM+B,gBAAgB,GAAG,IAAI,CAACxD,iBAAiB,CAACyD,mBAAmB,CAAC,CAAC;MACrE;MACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;QAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;QACxD,KAAK,IAAI3P,CAAC,GAAG0P,QAAQ,CAAChD,MAAM,GAAG,CAAC,EAAE1M,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,MAAM4P,OAAO,GAAGF,QAAQ,CAAC1P,CAAC,CAAC;UAC3B,IAAI4P,OAAO,KAAKL,gBAAgB,IAC5BK,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;YACpC,IAAI,CAAC1D,mBAAmB,CAAC2D,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;YAC1EJ,OAAO,CAACP,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;UAC/C;QACJ;MACJ;IACJ;IACA1C,kBAAkBA,CAAA,EAAG;MACjB,MAAM6B,MAAM,GAAG,IAAI,CAAC3C,aAAa;MACjC,OAAO2C,MAAM,GAAGA,MAAM,CAAC7B,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACT,0BAA0B;IACjF;IACA,OAAO5E,IAAI,YAAA2I,eAAAzI,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmE,MAAM;IAAA;IACzG,OAAOuE,KAAK,kBA5Z6EzT,EAAE,CAAA0T,kBAAA;MAAAC,KAAA,EA4ZYzE,MAAM;MAAAP,OAAA,EAANO,MAAM,CAAArE,IAAA;MAAA6D,UAAA,EAAc;IAAM;EACrI;EAAC,OA7PKQ,MAAM;AAAA;AA8PZ;EAAA,QAAAxG,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA,SAASwI,cAAcA,CAAC0C,KAAK,EAAEtK,QAAQ,EAAE;EACrC,IAAI/F,CAAC,GAAGqQ,KAAK,CAAC3D,MAAM;EACpB,OAAO1M,CAAC,EAAE,EAAE;IACR+F,QAAQ,CAACsK,KAAK,CAACrQ,CAAC,CAAC,CAAC;EACtB;AACJ;AAAC,IAEKsQ,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACf,OAAOhJ,IAAI,YAAAiJ,qBAAA/I,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8I,YAAY;IAAA;IAC/G,OAAOE,IAAI,kBA/a8E/T,EAAE,CAAAgU,gBAAA;MAAA9I,IAAA,EA+aS2I;IAAY;IAKhH,OAAOI,IAAI,kBApb8EjU,EAAE,CAAAkU,gBAAA;MAAAjO,SAAA,EAobkC,CAACiJ,MAAM,CAAC;MAAAiF,OAAA,GAAYrQ,aAAa,EAAEvC,YAAY,EAAEe,UAAU;MAChL;MACA;MACAf,YAAY;IAAA;EACxB;EAAC,OAXKsS,YAAY;AAAA;AAYlB;EAAA,QAAAnL,SAAA,oBAAAA,SAAA;AAAA;AAcA,SAASpC,kBAAkB,EAAEuI,qBAAqB,EAAED,WAAW,EAAEH,sBAAsB,EAAES,MAAM,EAAE/K,YAAY,EAAE0P,YAAY,EAAElH,SAAS,EAAEvG,sCAAsC,EAAEhF,eAAe,IAAIgT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}