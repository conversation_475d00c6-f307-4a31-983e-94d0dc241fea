using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Core.Entities;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    /// <summary>
    /// تحكم في الفروع
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class BranchesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<BranchesController> _logger;

        public BranchesController(TerraRetailDbContext context, ILogger<BranchesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الفروع
        /// </summary>
        /// <returns>قائمة الفروع</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetBranches()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.IsMainBranch ? 0 : 1)
                    .ThenBy(b => b.NameAr)
                    .Select(b => new
                    {
                        id = b.Id,
                        nameAr = b.NameAr,
                        nameEn = b.NameEn,
                        code = b.Code,
                        isMainBranch = b.IsMainBranch,
                        address = b.Address,
                        phone = b.Phone,
                        isActive = b.IsActive
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع {Count} فرع", branches.Count);
                return Ok(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفروع");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على فرع محدد
        /// </summary>
        /// <param name="id">معرف الفرع</param>
        /// <returns>بيانات الفرع</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Branch>> GetBranch(int id)
        {
            try
            {
                var branch = await _context.Branches.FindAsync(id);

                if (branch == null)
                {
                    _logger.LogWarning("الفرع غير موجود: {BranchId}", id);
                    return NotFound(new { message = "الفرع غير موجود" });
                }

                _logger.LogInformation("تم استرجاع الفرع: {BranchName}", branch.NameAr);
                return Ok(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفرع {BranchId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء فرع جديد
        /// </summary>
        /// <param name="branch">بيانات الفرع</param>
        /// <returns>الفرع المنشأ</returns>
        [HttpPost]
        public async Task<ActionResult<Branch>> CreateBranch(Branch branch)
        {
            try
            {
                // التحقق من وجود كود الفرع
                var existingBranch = await _context.Branches
                    .FirstOrDefaultAsync(b => b.Code == branch.Code);

                if (existingBranch != null)
                {
                    return BadRequest(new { message = "كود الفرع موجود بالفعل" });
                }

                branch.CreatedAt = DateTime.UtcNow;
                _context.Branches.Add(branch);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء فرع جديد: {BranchName} - {BranchCode}", 
                    branch.NameAr, branch.Code);

                return CreatedAtAction(nameof(GetBranch), new { id = branch.Id }, branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الفرع");
                return StatusCode(500, new { message = "حدث خطأ في إنشاء الفرع", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث بيانات فرع
        /// </summary>
        /// <param name="id">معرف الفرع</param>
        /// <param name="branch">البيانات المحدثة</param>
        /// <returns>نتيجة التحديث</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBranch(int id, Branch branch)
        {
            if (id != branch.Id)
            {
                return BadRequest(new { message = "معرف الفرع غير متطابق" });
            }

            try
            {
                var existingBranch = await _context.Branches.FindAsync(id);
                if (existingBranch == null)
                {
                    return NotFound(new { message = "الفرع غير موجود" });
                }

                // تحديث البيانات
                existingBranch.NameAr = branch.NameAr;
                existingBranch.NameEn = branch.NameEn;
                existingBranch.Code = branch.Code;
                existingBranch.Address = branch.Address;
                existingBranch.Phone = branch.Phone;
                existingBranch.Email = branch.Email;
                existingBranch.ManagerName = branch.ManagerName;
                existingBranch.WorkingHours = branch.WorkingHours;
                existingBranch.Longitude = branch.Longitude;
                existingBranch.Latitude = branch.Latitude;
                existingBranch.Area = branch.Area;
                existingBranch.TaxNumber = branch.TaxNumber;
                existingBranch.CommercialRegister = branch.CommercialRegister;
                existingBranch.IsActive = branch.IsActive;
                existingBranch.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الفرع: {BranchName}", existingBranch.NameAr);
                return Ok(new { message = "تم تحديث الفرع بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الفرع {BranchId}", id);
                return StatusCode(500, new { message = "حدث خطأ في تحديث الفرع", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف فرع
        /// </summary>
        /// <param name="id">معرف الفرع</param>
        /// <returns>نتيجة الحذف</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBranch(int id)
        {
            try
            {
                var branch = await _context.Branches.FindAsync(id);
                if (branch == null)
                {
                    return NotFound(new { message = "الفرع غير موجود" });
                }

                // التحقق من أن الفرع ليس رئيسياً
                if (branch.IsMainBranch)
                {
                    return BadRequest(new { message = "لا يمكن حذف الفرع الرئيسي" });
                }

                // التحقق من وجود بيانات مرتبطة بالفرع
                var hasCustomers = await _context.Customers.AnyAsync(c => c.BranchId == id);
                var hasSales = await _context.Sales.AnyAsync(s => s.BranchId == id);

                if (hasCustomers || hasSales)
                {
                    // إلغاء تفعيل بدلاً من الحذف
                    branch.IsActive = false;
                    branch.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم إلغاء تفعيل الفرع: {BranchName}", branch.NameAr);
                    return Ok(new { message = "تم إلغاء تفعيل الفرع بنجاح" });
                }
                else
                {
                    _context.Branches.Remove(branch);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم حذف الفرع: {BranchName}", branch.NameAr);
                    return Ok(new { message = "تم حذف الفرع بنجاح" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الفرع {BranchId}", id);
                return StatusCode(500, new { message = "حدث خطأ في حذف الفرع", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الفرع
        /// </summary>
        /// <param name="id">معرف الفرع</param>
        /// <returns>إحصائيات الفرع</returns>
        [HttpGet("{id}/statistics")]
        public async Task<ActionResult> GetBranchStatistics(int id)
        {
            try
            {
                var branch = await _context.Branches.FindAsync(id);
                if (branch == null)
                {
                    return NotFound(new { message = "الفرع غير موجود" });
                }

                var statistics = new
                {
                    BranchId = id,
                    BranchName = branch.NameAr,
                    CustomersCount = await _context.Customers.CountAsync(c => c.BranchId == id && c.IsActive),
                    ProductsCount = await _context.ProductStocks.CountAsync(ps => ps.BranchId == id),
                    SalesCount = await _context.Sales.CountAsync(s => s.BranchId == id),
                    TotalSalesAmount = await _context.Sales
                        .Where(s => s.BranchId == id && s.Status == SaleStatus.FullyPaid)
                        .SumAsync(s => s.TotalAmount),
                    LastSaleDate = await _context.Sales
                        .Where(s => s.BranchId == id)
                        .MaxAsync(s => (DateTime?)s.InvoiceDate)
                };

                _logger.LogInformation("تم استرجاع إحصائيات الفرع: {BranchName}", branch.NameAr);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع إحصائيات الفرع {BranchId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع الإحصائيات", error = ex.Message });
            }
        }
    }
}
