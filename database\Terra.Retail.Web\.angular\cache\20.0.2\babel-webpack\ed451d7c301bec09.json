{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/table\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/checkbox\";\nfunction AreasComponent_mat_card_19_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0627\\u0633\\u0645 \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0643\\u0648\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0643\\u0648\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0644\\u0627 \\u064A\\u0632\\u064A\\u062F \\u0639\\u0646 10 \\u0623\\u062D\\u0631\\u0641 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 16)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 17)(9, \"div\", 18)(10, \"mat-form-field\", 19)(11, \"mat-label\");\n    i0.ɵɵtext(12, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 20);\n    i0.ɵɵelementStart(14, \"mat-icon\", 21);\n    i0.ɵɵtext(15, \"location_city\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, AreasComponent_mat_card_19_mat_error_16_Template, 2, 0, \"mat-error\", 22)(17, AreasComponent_mat_card_19_mat_error_17_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-form-field\", 19)(19, \"mat-label\");\n    i0.ɵɵtext(20, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 23);\n    i0.ɵɵelementStart(22, \"mat-icon\", 21);\n    i0.ɵɵtext(23, \"location_city\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, AreasComponent_mat_card_19_mat_error_24_Template, 2, 0, \"mat-error\", 22)(25, AreasComponent_mat_card_19_mat_error_25_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 19)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 24);\n    i0.ɵɵelementStart(30, \"mat-icon\", 21);\n    i0.ɵɵtext(31, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, AreasComponent_mat_card_19_mat_error_32_Template, 2, 0, \"mat-error\", 22)(33, AreasComponent_mat_card_19_mat_error_33_Template, 2, 0, \"mat-error\", 22)(34, AreasComponent_mat_card_19_mat_error_34_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 25)(36, \"mat-checkbox\", 26);\n    i0.ɵɵtext(37, \" \\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0646\\u0634\\u0637\\u0629 \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(38, \"mat-card-actions\", 27)(39, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AreasComponent_mat_card_19_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelForm());\n    });\n    i0.ɵɵelementStart(40, \"mat-icon\");\n    i0.ɵɵtext(41, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43, \"\\u0625\\u0644\\u063A\\u0627\\u0621\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function AreasComponent_mat_card_19_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveArea());\n    });\n    i0.ɵɵelementStart(45, \"mat-icon\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.editingArea ? \"edit\" : \"add\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingArea ? \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.areaForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.areaForm.get(\"nameAr\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.areaForm.get(\"nameAr\")) == null ? null : tmp_5_0.hasError(\"minlength\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.areaForm.get(\"nameEn\")) == null ? null : tmp_6_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r1.areaForm.get(\"nameEn\")) == null ? null : tmp_7_0.hasError(\"minlength\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx_r1.areaForm.get(\"code\")) == null ? null : tmp_8_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r1.areaForm.get(\"code\")) == null ? null : tmp_9_0.hasError(\"minlength\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = ctx_r1.areaForm.get(\"code\")) == null ? null : tmp_10_0.hasError(\"maxlength\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingArea ? \"save\" : \"add\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.editingArea ? \"\\u062A\\u062D\\u062F\\u064A\\u062B\" : \"\\u0625\\u0636\\u0627\\u0641\\u0629\");\n  }\n}\nfunction AreasComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"mat-spinner\", 31);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AreasComponent_div_29_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 44);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_div_29_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(area_r3.NameAr);\n  }\n}\nfunction AreasComponent_div_29_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 44);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_div_29_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(area_r4.NameEn);\n  }\n}\nfunction AreasComponent_div_29_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 44);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0643\\u0648\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_div_29_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 45)(1, \"span\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(area_r5.Code);\n  }\n}\nfunction AreasComponent_div_29_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 44);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_div_29_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 45)(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", area_r6.IsActive ? \"active\" : \"inactive\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r6.IsActive ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n  }\n}\nfunction AreasComponent_div_29_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 44);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AreasComponent_div_29_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 45)(1, \"div\", 48)(2, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function AreasComponent_div_29_td_16_Template_button_click_2_listener() {\n      const area_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editArea(area_r8));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function AreasComponent_div_29_td_16_Template_button_click_5_listener() {\n      const area_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteArea(area_r8));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AreasComponent_div_29_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction AreasComponent_div_29_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 52);\n  }\n}\nfunction AreasComponent_div_29_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0623\\u064A \\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A \\u0628\\u0639\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function AreasComponent_div_29_div_19_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showAddArea());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AreasComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"table\", 33);\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, AreasComponent_div_29_th_3_Template, 2, 0, \"th\", 35)(4, AreasComponent_div_29_td_4_Template, 2, 1, \"td\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 37);\n    i0.ɵɵtemplate(6, AreasComponent_div_29_th_6_Template, 2, 0, \"th\", 35)(7, AreasComponent_div_29_td_7_Template, 2, 1, \"td\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 38);\n    i0.ɵɵtemplate(9, AreasComponent_div_29_th_9_Template, 2, 0, \"th\", 35)(10, AreasComponent_div_29_td_10_Template, 3, 1, \"td\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 39);\n    i0.ɵɵtemplate(12, AreasComponent_div_29_th_12_Template, 2, 0, \"th\", 35)(13, AreasComponent_div_29_td_13_Template, 3, 2, \"td\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 40);\n    i0.ɵɵtemplate(15, AreasComponent_div_29_th_15_Template, 2, 0, \"th\", 35)(16, AreasComponent_div_29_td_16_Template, 8, 0, \"td\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(17, AreasComponent_div_29_tr_17_Template, 1, 0, \"tr\", 41)(18, AreasComponent_div_29_tr_18_Template, 1, 0, \"tr\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, AreasComponent_div_29_div_19_Template, 12, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.areas);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.areas.length === 0);\n  }\n}\nfunction AreasComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"mat-spinner\", 56);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0645\\u0639\\u0627\\u0644\\u062C\\u0629...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AreasComponent = /*#__PURE__*/(() => {\n  class AreasComponent {\n    http;\n    router;\n    fb;\n    snackBar;\n    dialog;\n    // Component State\n    isLoading = false;\n    showAddForm = false;\n    editingArea = null;\n    // Data\n    areas = [];\n    // Form\n    areaForm;\n    // Table Configuration\n    displayedColumns = ['nameAr', 'nameEn', 'code', 'isActive', 'actions'];\n    // Subscriptions\n    subscriptions = [];\n    constructor(http, router, fb, snackBar, dialog) {\n      this.http = http;\n      this.router = router;\n      this.fb = fb;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.areaForm = this.createForm();\n    }\n    ngOnInit() {\n      this.loadAreas();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Create reactive form\n     */\n    createForm() {\n      return this.fb.group({\n        nameAr: ['', [Validators.required, Validators.minLength(2)]],\n        nameEn: ['', [Validators.required, Validators.minLength(2)]],\n        code: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(10)]],\n        isActive: [true]\n      });\n    }\n    /**\n     * Load areas\n     */\n    loadAreas() {\n      this.isLoading = true;\n      const sub = this.http.get('http://localhost:5127/api/simple/areas-db').subscribe({\n        next: response => {\n          console.log('Areas response:', response);\n          this.areas = response.areas || [];\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading areas:', error);\n          this.showError('خطأ في تحميل المحافظات');\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Show add form\n     */\n    showAddArea() {\n      this.showAddForm = true;\n      this.editingArea = null;\n      this.areaForm.reset();\n      this.areaForm.patchValue({\n        isActive: true\n      });\n    }\n    /**\n     * Edit area\n     */\n    editArea(area) {\n      this.showAddForm = true;\n      this.editingArea = area;\n      this.areaForm.patchValue({\n        nameAr: area.NameAr,\n        nameEn: area.NameEn,\n        code: area.Code,\n        isActive: area.IsActive\n      });\n    }\n    /**\n     * Save area\n     */\n    saveArea() {\n      if (this.areaForm.valid) {\n        this.isLoading = true;\n        const formValue = this.areaForm.value;\n        const request = {\n          nameAr: formValue.nameAr,\n          nameEn: formValue.nameEn,\n          code: formValue.code.toUpperCase(),\n          isActive: formValue.isActive\n        };\n        const apiCall = this.editingArea ? this.http.put(`http://localhost:5127/api/areas/${this.editingArea.Id}`, request) : this.http.post('http://localhost:5127/api/areas', request);\n        const sub = apiCall.subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess(this.editingArea ? 'تم تحديث المحافظة بنجاح' : 'تم إضافة المحافظة بنجاح');\n            this.showAddForm = false;\n            this.loadAreas();\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error saving area:', error);\n            this.showError('خطأ في حفظ المحافظة');\n          }\n        });\n        this.subscriptions.push(sub);\n      } else {\n        this.markFormGroupTouched();\n        this.showError('يرجى تصحيح الأخطاء في النموذج');\n      }\n    }\n    /**\n     * Delete area\n     */\n    deleteArea(area) {\n      if (confirm(`هل أنت متأكد من حذف المحافظة \"${area.NameAr}\"؟`)) {\n        this.isLoading = true;\n        const sub = this.http.delete(`http://localhost:5127/api/areas/${area.Id}`).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess('تم حذف المحافظة بنجاح');\n            this.loadAreas();\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error deleting area:', error);\n            this.showError('خطأ في حذف المحافظة');\n          }\n        });\n        this.subscriptions.push(sub);\n      }\n    }\n    /**\n     * Cancel form\n     */\n    cancelForm() {\n      this.showAddForm = false;\n      this.editingArea = null;\n      this.areaForm.reset();\n    }\n    /**\n     * Go back to suppliers\n     */\n    goBack() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Mark all form fields as touched\n     */\n    markFormGroupTouched() {\n      Object.keys(this.areaForm.controls).forEach(key => {\n        const control = this.areaForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    /**\n     * Show success message\n     */\n    showSuccess(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        panelClass: ['success-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    /**\n     * Show error message\n     */\n    showError(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 5000,\n        panelClass: ['error-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    static ɵfac = function AreasComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AreasComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AreasComponent,\n      selectors: [[\"app-areas\"]],\n      decls: 31,\n      vars: 4,\n      consts: [[1, \"areas-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-btn\", 3, \"click\"], [1, \"content\"], [\"class\", \"form-card\", 4, \"ngIf\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"form-card\"], [1, \"area-form\", 3, \"formGroup\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"nameAr\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\", \"required\", \"\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"nameEn\", \"placeholder\", \"Enter area name in English\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"CAI\", \"required\", \"\", \"maxlength\", \"10\", 2, \"text-transform\", \"uppercase\"], [1, \"checkbox-field\"], [\"formControlName\", \"isActive\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"areas-table\", 3, \"dataSource\"], [\"matColumnDef\", \"nameAr\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"nameEn\"], [\"matColumnDef\", \"code\"], [\"matColumnDef\", \"isActive\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"code-badge\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function AreasComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function AreasComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n          i0.ɵɵtext(9, \"\\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 7);\n          i0.ɵɵtext(11, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AreasComponent_Template_button_click_13_listener() {\n            return ctx.showAddArea();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\");\n          i0.ɵɵtext(17, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(18, \"div\", 10);\n          i0.ɵɵtemplate(19, AreasComponent_mat_card_19_Template, 49, 13, \"mat-card\", 11);\n          i0.ɵɵelementStart(20, \"mat-card\", 12)(21, \"mat-card-header\")(22, \"mat-card-title\")(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"mat-card-content\");\n          i0.ɵɵtemplate(28, AreasComponent_div_28_Template, 4, 0, \"div\", 13)(29, AreasComponent_div_29_Template, 20, 4, \"div\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(30, AreasComponent_div_30_Template, 4, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MaxLengthValidator, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatCardModule, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatFormFieldModule, i10.MatFormField, i10.MatLabel, i10.MatError, i10.MatSuffix, MatInputModule, i11.MatInput, MatTableModule, i12.MatTable, i12.MatHeaderCellDef, i12.MatHeaderRowDef, i12.MatColumnDef, i12.MatCellDef, i12.MatRowDef, i12.MatHeaderCell, i12.MatCell, i12.MatHeaderRow, i12.MatRow, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, i13.MatProgressSpinner, MatCheckboxModule, i14.MatCheckbox],\n      styles: [\"\\n\\n.areas-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--warning-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateX(-2px);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: var(--spacing-xs) 0 0;\\n  opacity: 0.9;\\n}\\n.page-header[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--border-color);\\n  overflow: hidden;\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--primary-700);\\n  margin: 0;\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl);\\n}\\n.form-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  background: var(--surface-50);\\n  border-top: 1px solid var(--border-color);\\n}\\n\\n\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-lg);\\n  align-items: start;\\n}\\n.form-grid[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.form-grid[_ngcontent-%COMP%]   .checkbox-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: var(--spacing-md) 0;\\n}\\n\\n\\n\\n.table-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--border-color);\\n  overflow: hidden;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--warning-50) 100%);\\n  padding: var(--spacing-lg) var(--spacing-xl);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--primary-700);\\n  margin: 0;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.areas-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: white;\\n}\\n.areas-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: var(--surface-100);\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  padding: var(--spacing-lg);\\n  border-bottom: 2px solid var(--border-color);\\n}\\n.areas-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg);\\n  border-bottom: 1px solid var(--border-light);\\n  vertical-align: middle;\\n}\\n.areas-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background: var(--surface-50);\\n}\\n\\n\\n\\n.code-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  background: var(--primary-100);\\n  color: var(--primary-700);\\n  border: 1px solid var(--primary-200);\\n  border-radius: var(--radius-md);\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  font-family: \\\"Courier New\\\", monospace;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-full);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.status-badge.active[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n  border: 1px solid var(--success-200);\\n}\\n.status-badge.inactive[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n  border: 1px solid var(--error-200);\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n  align-items: center;\\n}\\n\\n\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--spacing-4xl);\\n  color: var(--text-secondary);\\n}\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: var(--text-disabled);\\n  margin-bottom: var(--spacing-lg);\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0 0 var(--spacing-sm);\\n  color: var(--text-primary);\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0 0 var(--spacing-xl);\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4xl);\\n  color: var(--text-secondary);\\n}\\n.loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n.loading-overlay[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-primary);\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .areas-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .areas-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md);\\n    font-size: 0.875rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AreasComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatTableModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatCheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "AreasComponent_mat_card_19_mat_error_16_Template", "AreasComponent_mat_card_19_mat_error_17_Template", "AreasComponent_mat_card_19_mat_error_24_Template", "AreasComponent_mat_card_19_mat_error_25_Template", "AreasComponent_mat_card_19_mat_error_32_Template", "AreasComponent_mat_card_19_mat_error_33_Template", "AreasComponent_mat_card_19_mat_error_34_Template", "ɵɵlistener", "AreasComponent_mat_card_19_Template_button_click_39_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "cancelForm", "AreasComponent_mat_card_19_Template_button_click_44_listener", "saveArea", "ɵɵadvance", "ɵɵtextInterpolate", "editingArea", "ɵɵproperty", "areaForm", "tmp_4_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_10_0", "isLoading", "area_r3", "NameAr", "area_r4", "NameEn", "area_r5", "Code", "area_r6", "IsActive", "ɵɵtextInterpolate1", "AreasComponent_div_29_td_16_Template_button_click_2_listener", "area_r8", "_r7", "$implicit", "editArea", "AreasComponent_div_29_td_16_Template_button_click_5_listener", "deleteArea", "AreasComponent_div_29_div_19_Template_button_click_7_listener", "_r9", "showAddArea", "ɵɵelementContainerStart", "AreasComponent_div_29_th_3_Template", "AreasComponent_div_29_td_4_Template", "AreasComponent_div_29_th_6_Template", "AreasComponent_div_29_td_7_Template", "AreasComponent_div_29_th_9_Template", "AreasComponent_div_29_td_10_Template", "AreasComponent_div_29_th_12_Template", "AreasComponent_div_29_td_13_Template", "AreasComponent_div_29_th_15_Template", "AreasComponent_div_29_td_16_Template", "AreasComponent_div_29_tr_17_Template", "AreasComponent_div_29_tr_18_Template", "AreasComponent_div_29_div_19_Template", "areas", "displayedColumns", "length", "AreasComponent", "http", "router", "fb", "snackBar", "dialog", "showAddForm", "subscriptions", "constructor", "createForm", "ngOnInit", "loadAreas", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "group", "nameAr", "required", "<PERSON><PERSON><PERSON><PERSON>", "nameEn", "code", "max<PERSON><PERSON><PERSON>", "isActive", "subscribe", "next", "response", "console", "log", "error", "showError", "push", "reset", "patchValue", "area", "valid", "formValue", "value", "request", "toUpperCase", "apiCall", "put", "Id", "post", "showSuccess", "markFormGroupTouched", "confirm", "delete", "goBack", "navigate", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "message", "open", "duration", "panelClass", "horizontalPosition", "verticalPosition", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "Router", "i3", "FormBuilder", "i4", "MatSnackBar", "i5", "MatDialog", "selectors", "decls", "vars", "consts", "template", "AreasComponent_Template", "rf", "ctx", "AreasComponent_Template_button_click_4_listener", "AreasComponent_Template_button_click_13_listener", "AreasComponent_mat_card_19_Template", "AreasComponent_div_28_Template", "AreasComponent_div_29_Template", "AreasComponent_div_30_Template", "i6", "Ng<PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "FormGroupDirective", "FormControlName", "i7", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatInput", "i12", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i13", "MatProgressSpinner", "i14", "MatCheckbox", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\areas\\areas.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\areas\\areas.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\n\n// Interfaces\ninterface Area {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n  Code: string;\n  IsActive: boolean;\n  CreatedAt?: string;\n  UpdatedAt?: string;\n}\n\n@Component({\n  selector: 'app-areas',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatCheckboxModule\n  ],\n  templateUrl: './areas.component.html',\n  styleUrls: ['./areas.component.scss']\n})\nexport class AreasComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  showAddForm = false;\n  editingArea: Area | null = null;\n  \n  // Data\n  areas: Area[] = [];\n  \n  // Form\n  areaForm: FormGroup;\n  \n  // Table Configuration\n  displayedColumns: string[] = ['nameAr', 'nameEn', 'code', 'isActive', 'actions'];\n  \n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n    private fb: FormBuilder,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog\n  ) {\n    this.areaForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.loadAreas();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      nameAr: ['', [Validators.required, Validators.minLength(2)]],\n      nameEn: ['', [Validators.required, Validators.minLength(2)]],\n      code: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(10)]],\n      isActive: [true]\n    });\n  }\n\n  /**\n   * Load areas\n   */\n  loadAreas(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>('http://localhost:5127/api/simple/areas-db').subscribe({\n      next: (response) => {\n        console.log('Areas response:', response);\n        this.areas = response.areas || [];\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading areas:', error);\n        this.showError('خطأ في تحميل المحافظات');\n        this.isLoading = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Show add form\n   */\n  showAddArea(): void {\n    this.showAddForm = true;\n    this.editingArea = null;\n    this.areaForm.reset();\n    this.areaForm.patchValue({ isActive: true });\n  }\n\n  /**\n   * Edit area\n   */\n  editArea(area: Area): void {\n    this.showAddForm = true;\n    this.editingArea = area;\n    this.areaForm.patchValue({\n      nameAr: area.NameAr,\n      nameEn: area.NameEn,\n      code: area.Code,\n      isActive: area.IsActive\n    });\n  }\n\n  /**\n   * Save area\n   */\n  saveArea(): void {\n    if (this.areaForm.valid) {\n      this.isLoading = true;\n      \n      const formValue = this.areaForm.value;\n      const request = {\n        nameAr: formValue.nameAr,\n        nameEn: formValue.nameEn,\n        code: formValue.code.toUpperCase(),\n        isActive: formValue.isActive\n      };\n\n      const apiCall = this.editingArea\n        ? this.http.put<any>(`http://localhost:5127/api/areas/${this.editingArea.Id}`, request)\n        : this.http.post<any>('http://localhost:5127/api/areas', request);\n\n      const sub = apiCall.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.showSuccess(this.editingArea ? 'تم تحديث المحافظة بنجاح' : 'تم إضافة المحافظة بنجاح');\n          this.showAddForm = false;\n          this.loadAreas();\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error saving area:', error);\n          this.showError('خطأ في حفظ المحافظة');\n        }\n      });\n      this.subscriptions.push(sub);\n    } else {\n      this.markFormGroupTouched();\n      this.showError('يرجى تصحيح الأخطاء في النموذج');\n    }\n  }\n\n  /**\n   * Delete area\n   */\n  deleteArea(area: Area): void {\n    if (confirm(`هل أنت متأكد من حذف المحافظة \"${area.NameAr}\"؟`)) {\n      this.isLoading = true;\n\n      const sub = this.http.delete(`http://localhost:5127/api/areas/${area.Id}`).subscribe({\n        next: (response: any) => {\n          this.isLoading = false;\n          this.showSuccess('تم حذف المحافظة بنجاح');\n          this.loadAreas();\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error deleting area:', error);\n          this.showError('خطأ في حذف المحافظة');\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n  }\n\n  /**\n   * Cancel form\n   */\n  cancelForm(): void {\n    this.showAddForm = false;\n    this.editingArea = null;\n    this.areaForm.reset();\n  }\n\n  /**\n   * Go back to suppliers\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Mark all form fields as touched\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.areaForm.controls).forEach(key => {\n      const control = this.areaForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Show success message\n   */\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      panelClass: ['success-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Show error message\n   */\n  private showError(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 5000,\n      panelClass: ['error-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n}\n", "<!-- <PERSON> Retail ERP - Areas -->\n<div class=\"areas-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">المحافظات</h1>\n          <p class=\"page-subtitle\">إدارة المحافظات في النظام</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" class=\"add-btn\" (click)=\"showAddArea()\">\n          <mat-icon>add</mat-icon>\n          <span>إضافة محافظة</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content -->\n  <div class=\"content\">\n    \n    <!-- Add/Edit Form -->\n    <mat-card class=\"form-card\" *ngIf=\"showAddForm\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>{{ editingArea ? 'edit' : 'add' }}</mat-icon>\n          <span>{{ editingArea ? 'تعديل المحافظة' : 'إضافة محافظة جديدة' }}</span>\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <form [formGroup]=\"areaForm\" class=\"area-form\">\n          <div class=\"form-grid\">\n            \n            <!-- Arabic Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم المحافظة بالعربية *</mat-label>\n              <input matInput formControlName=\"nameAr\" placeholder=\"أدخل اسم المحافظة بالعربية\" required>\n              <mat-icon matSuffix>location_city</mat-icon>\n              <mat-error *ngIf=\"areaForm.get('nameAr')?.hasError('required')\">\n                اسم المحافظة بالعربية مطلوب\n              </mat-error>\n              <mat-error *ngIf=\"areaForm.get('nameAr')?.hasError('minlength')\">\n                الاسم يجب أن يكون حرفين على الأقل\n              </mat-error>\n            </mat-form-field>\n\n            <!-- English Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم المحافظة بالإنجليزية *</mat-label>\n              <input matInput formControlName=\"nameEn\" placeholder=\"Enter area name in English\" required>\n              <mat-icon matSuffix>location_city</mat-icon>\n              <mat-error *ngIf=\"areaForm.get('nameEn')?.hasError('required')\">\n                اسم المحافظة بالإنجليزية مطلوب\n              </mat-error>\n              <mat-error *ngIf=\"areaForm.get('nameEn')?.hasError('minlength')\">\n                الاسم يجب أن يكون حرفين على الأقل\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Code -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>كود المحافظة *</mat-label>\n              <input matInput formControlName=\"code\" placeholder=\"CAI\" required maxlength=\"10\" style=\"text-transform: uppercase;\">\n              <mat-icon matSuffix>code</mat-icon>\n              <mat-error *ngIf=\"areaForm.get('code')?.hasError('required')\">\n                كود المحافظة مطلوب\n              </mat-error>\n              <mat-error *ngIf=\"areaForm.get('code')?.hasError('minlength')\">\n                الكود يجب أن يكون حرفين على الأقل\n              </mat-error>\n              <mat-error *ngIf=\"areaForm.get('code')?.hasError('maxlength')\">\n                الكود يجب ألا يزيد عن 10 أحرف\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Is Active -->\n            <div class=\"checkbox-field\">\n              <mat-checkbox formControlName=\"isActive\">\n                محافظة نشطة\n              </mat-checkbox>\n            </div>\n\n          </div>\n        </form>\n      </mat-card-content>\n      <mat-card-actions align=\"end\">\n        <button mat-button (click)=\"cancelForm()\">\n          <mat-icon>cancel</mat-icon>\n          <span>إلغاء</span>\n        </button>\n        <button mat-raised-button color=\"primary\" (click)=\"saveArea()\" [disabled]=\"isLoading\">\n          <mat-icon>{{ editingArea ? 'save' : 'add' }}</mat-icon>\n          <span>{{ editingArea ? 'تحديث' : 'إضافة' }}</span>\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <!-- Areas Table -->\n    <mat-card class=\"table-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>list</mat-icon>\n          <span>قائمة المحافظات</span>\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        \n        <!-- Loading Spinner -->\n        <div class=\"loading-container\" *ngIf=\"isLoading\">\n          <mat-spinner diameter=\"40\"></mat-spinner>\n          <p>جاري تحميل البيانات...</p>\n        </div>\n\n        <!-- Table -->\n        <div class=\"table-container\" *ngIf=\"!isLoading\">\n          <table mat-table [dataSource]=\"areas\" class=\"areas-table\">\n\n            <!-- Arabic Name Column -->\n            <ng-container matColumnDef=\"nameAr\">\n              <th mat-header-cell *matHeaderCellDef>الاسم بالعربية</th>\n              <td mat-cell *matCellDef=\"let area\">{{ area.NameAr }}</td>\n            </ng-container>\n\n            <!-- English Name Column -->\n            <ng-container matColumnDef=\"nameEn\">\n              <th mat-header-cell *matHeaderCellDef>الاسم بالإنجليزية</th>\n              <td mat-cell *matCellDef=\"let area\">{{ area.NameEn }}</td>\n            </ng-container>\n\n            <!-- Code Column -->\n            <ng-container matColumnDef=\"code\">\n              <th mat-header-cell *matHeaderCellDef>الكود</th>\n              <td mat-cell *matCellDef=\"let area\">\n                <span class=\"code-badge\">{{ area.Code }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Status Column -->\n            <ng-container matColumnDef=\"isActive\">\n              <th mat-header-cell *matHeaderCellDef>الحالة</th>\n              <td mat-cell *matCellDef=\"let area\">\n                <span class=\"status-badge\" [ngClass]=\"area.IsActive ? 'active' : 'inactive'\">\n                  {{ area.IsActive ? 'نشط' : 'غير نشط' }}\n                </span>\n              </td>\n            </ng-container>\n\n            <!-- Actions Column -->\n            <ng-container matColumnDef=\"actions\">\n              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\n              <td mat-cell *matCellDef=\"let area\">\n                <div class=\"action-buttons\">\n                  <button mat-icon-button color=\"primary\" \n                          matTooltip=\"تعديل\"\n                          (click)=\"editArea(area)\">\n                    <mat-icon>edit</mat-icon>\n                  </button>\n                  <button mat-icon-button color=\"warn\" \n                          matTooltip=\"حذف\"\n                          (click)=\"deleteArea(area)\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                </div>\n              </td>\n            </ng-container>\n\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n\n          </table>\n\n          <!-- No Data Message -->\n          <div class=\"no-data\" *ngIf=\"areas.length === 0\">\n            <mat-icon>location_off</mat-icon>\n            <h3>لا توجد محافظات</h3>\n            <p>لم يتم إضافة أي محافظات بعد</p>\n            <button mat-raised-button color=\"primary\" (click)=\"showAddArea()\">\n              <mat-icon>add</mat-icon>\n              <span>إضافة محافظة جديدة</span>\n            </button>\n          </div>\n\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري المعالجة...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAKrG;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,iBAAiB,QAAqB,6BAA6B;AAC5E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;;;IC2BhDC,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,4JACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAE,MAAA,iLACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,8KACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAE,MAAA,iLACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAE,MAAA,iLACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAE,MAAA,+IACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IA/ChBH,EAHN,CAAAC,cAAA,mBAAgD,sBAC7B,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAErEF,EAFqE,CAAAG,YAAA,EAAO,EACzD,EACD;IAOVH,EANR,CAAAC,cAAA,uBAAkB,eAC+B,cACtB,0BAGgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,8HAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9CH,EAAA,CAAAI,SAAA,iBAA2F;IAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI5CH,EAHA,CAAAK,UAAA,KAAAC,gDAAA,wBAAgE,KAAAC,gDAAA,wBAGC;IAGnEP,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,gJAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjDH,EAAA,CAAAI,SAAA,iBAA2F;IAC3FJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI5CH,EAHA,CAAAK,UAAA,KAAAG,gDAAA,wBAAgE,KAAAC,gDAAA,wBAGC;IAGnET,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,6EAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAI,SAAA,iBAAoH;IACpHJ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAOnCH,EANA,CAAAK,UAAA,KAAAK,gDAAA,wBAA8D,KAAAC,gDAAA,wBAGC,KAAAC,gDAAA,wBAGA;IAGjEZ,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,eAA4B,wBACe;IACvCD,EAAA,CAAAE,MAAA,uEACF;IAKRF,EALQ,CAAAG,YAAA,EAAe,EACX,EAEF,EACD,EACU;IAEjBH,EADF,CAAAC,cAAA,4BAA8B,kBACc;IAAvBD,EAAA,CAAAa,UAAA,mBAAAC,6DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IACvCpB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IACbF,EADa,CAAAG,YAAA,EAAO,EACX;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA5CD,EAAA,CAAAa,UAAA,mBAAAQ,6DAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAK,QAAA,EAAU;IAAA,EAAC;IAC5DtB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvDH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAGjDF,EAHiD,CAAAG,YAAA,EAAO,EAC3C,EACQ,EACV;;;;;;;;;;;IAtEKH,EAAA,CAAAuB,SAAA,GAAkC;IAAlCvB,EAAA,CAAAwB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,kBAAkC;IACtCzB,EAAA,CAAAuB,SAAA,GAA2D;IAA3DvB,EAAA,CAAAwB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,4LAA2D;IAI7DzB,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAA0B,UAAA,cAAAT,MAAA,CAAAU,QAAA,CAAsB;IAQV3B,EAAA,CAAAuB,SAAA,GAAkD;IAAlDvB,EAAA,CAAA0B,UAAA,UAAAE,OAAA,GAAAX,MAAA,CAAAU,QAAA,CAAAE,GAAA,6BAAAD,OAAA,CAAAE,QAAA,aAAkD;IAGlD9B,EAAA,CAAAuB,SAAA,EAAmD;IAAnDvB,EAAA,CAAA0B,UAAA,UAAAK,OAAA,GAAAd,MAAA,CAAAU,QAAA,CAAAE,GAAA,6BAAAE,OAAA,CAAAD,QAAA,cAAmD;IAUnD9B,EAAA,CAAAuB,SAAA,GAAkD;IAAlDvB,EAAA,CAAA0B,UAAA,UAAAM,OAAA,GAAAf,MAAA,CAAAU,QAAA,CAAAE,GAAA,6BAAAG,OAAA,CAAAF,QAAA,aAAkD;IAGlD9B,EAAA,CAAAuB,SAAA,EAAmD;IAAnDvB,EAAA,CAAA0B,UAAA,UAAAO,OAAA,GAAAhB,MAAA,CAAAU,QAAA,CAAAE,GAAA,6BAAAI,OAAA,CAAAH,QAAA,cAAmD;IAUnD9B,EAAA,CAAAuB,SAAA,GAAgD;IAAhDvB,EAAA,CAAA0B,UAAA,UAAAQ,OAAA,GAAAjB,MAAA,CAAAU,QAAA,CAAAE,GAAA,2BAAAK,OAAA,CAAAJ,QAAA,aAAgD;IAGhD9B,EAAA,CAAAuB,SAAA,EAAiD;IAAjDvB,EAAA,CAAA0B,UAAA,UAAAS,OAAA,GAAAlB,MAAA,CAAAU,QAAA,CAAAE,GAAA,2BAAAM,OAAA,CAAAL,QAAA,cAAiD;IAGjD9B,EAAA,CAAAuB,SAAA,EAAiD;IAAjDvB,EAAA,CAAA0B,UAAA,UAAAU,QAAA,GAAAnB,MAAA,CAAAU,QAAA,CAAAE,GAAA,2BAAAO,QAAA,CAAAN,QAAA,cAAiD;IAoBJ9B,EAAA,CAAAuB,SAAA,IAAsB;IAAtBvB,EAAA,CAAA0B,UAAA,aAAAT,MAAA,CAAAoB,SAAA,CAAsB;IACzErC,EAAA,CAAAuB,SAAA,GAAkC;IAAlCvB,EAAA,CAAAwB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,kBAAkC;IACtCzB,EAAA,CAAAuB,SAAA,GAAqC;IAArCvB,EAAA,CAAAwB,iBAAA,CAAAP,MAAA,CAAAQ,WAAA,uEAAqC;;;;;IAgB7CzB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAI,SAAA,sBAAyC;IACzCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;;IAQAH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,sFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACzDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtBH,EAAA,CAAAuB,SAAA,EAAiB;IAAjBvB,EAAA,CAAAwB,iBAAA,CAAAc,OAAA,CAAAC,MAAA,CAAiB;;;;;IAKrDvC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,wGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC5DH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtBH,EAAA,CAAAuB,SAAA,EAAiB;IAAjBvB,EAAA,CAAAwB,iBAAA,CAAAgB,OAAA,CAAAC,MAAA,CAAiB;;;;;IAKrDzC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE9CH,EADF,CAAAC,cAAA,aAAoC,eACT;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC5C;;;;IADsBH,EAAA,CAAAuB,SAAA,GAAe;IAAfvB,EAAA,CAAAwB,iBAAA,CAAAkB,OAAA,CAAAC,IAAA,CAAe;;;;;IAM1C3C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAoC,eAC2C;IAC3ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAHwBH,EAAA,CAAAuB,SAAA,EAAiD;IAAjDvB,EAAA,CAAA0B,UAAA,YAAAkB,OAAA,CAAAC,QAAA,yBAAiD;IAC1E7C,EAAA,CAAAuB,SAAA,EACF;IADEvB,EAAA,CAAA8C,kBAAA,MAAAF,OAAA,CAAAC,QAAA,uEACF;;;;;IAMF7C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAoC,cACN,iBAGO;IAAzBD,EAAA,CAAAa,UAAA,mBAAAkC,6DAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAe,aAAA,CAAAkC,GAAA,EAAAC,SAAA;MAAA,MAAAjC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAkC,QAAA,CAAAH,OAAA,CAAc;IAAA,EAAC;IAC9BhD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAEmC;IAA3BD,EAAA,CAAAa,UAAA,mBAAAuC,6DAAA;MAAA,MAAAJ,OAAA,GAAAhD,EAAA,CAAAe,aAAA,CAAAkC,GAAA,EAAAC,SAAA;MAAA,MAAAjC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAoC,UAAA,CAAAL,OAAA,CAAgB;IAAA,EAAC;IAChChD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAGtBF,EAHsB,CAAAG,YAAA,EAAW,EACpB,EACL,EACH;;;;;IAGPH,EAAA,CAAAI,SAAA,aAA4D;;;;;IAC5DJ,EAAA,CAAAI,SAAA,aAAkE;;;;;;IAMlEJ,EADF,CAAAC,cAAA,cAAgD,eACpC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gJAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClCH,EAAA,CAAAC,cAAA,iBAAkE;IAAxBD,EAAA,CAAAa,UAAA,mBAAAyC,8DAAA;MAAAtD,EAAA,CAAAe,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAuC,WAAA,EAAa;IAAA,EAAC;IAC/DxD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,0GAAkB;IAE5BF,EAF4B,CAAAG,YAAA,EAAO,EACxB,EACL;;;;;IAjENH,EADF,CAAAC,cAAA,cAAgD,gBACY;IAGxDD,EAAA,CAAAyD,uBAAA,OAAoC;IAElCzD,EADA,CAAAK,UAAA,IAAAqD,mCAAA,iBAAsC,IAAAC,mCAAA,iBACF;;IAItC3D,EAAA,CAAAyD,uBAAA,OAAoC;IAElCzD,EADA,CAAAK,UAAA,IAAAuD,mCAAA,iBAAsC,IAAAC,mCAAA,iBACF;;IAItC7D,EAAA,CAAAyD,uBAAA,OAAkC;IAEhCzD,EADA,CAAAK,UAAA,IAAAyD,mCAAA,iBAAsC,KAAAC,oCAAA,iBACF;;IAMtC/D,EAAA,CAAAyD,uBAAA,QAAsC;IAEpCzD,EADA,CAAAK,UAAA,KAAA2D,oCAAA,iBAAsC,KAAAC,oCAAA,iBACF;;IAQtCjE,EAAA,CAAAyD,uBAAA,QAAqC;IAEnCzD,EADA,CAAAK,UAAA,KAAA6D,oCAAA,iBAAsC,KAAAC,oCAAA,iBACF;;IAiBtCnE,EADA,CAAAK,UAAA,KAAA+D,oCAAA,iBAAuD,KAAAC,oCAAA,iBACM;IAE/DrE,EAAA,CAAAG,YAAA,EAAQ;IAGRH,EAAA,CAAAK,UAAA,KAAAiE,qCAAA,mBAAgD;IAUlDtE,EAAA,CAAAG,YAAA,EAAM;;;;IAnEaH,EAAA,CAAAuB,SAAA,EAAoB;IAApBvB,EAAA,CAAA0B,UAAA,eAAAT,MAAA,CAAAsD,KAAA,CAAoB;IAmDfvE,EAAA,CAAAuB,SAAA,IAAiC;IAAjCvB,EAAA,CAAA0B,UAAA,oBAAAT,MAAA,CAAAuD,gBAAA,CAAiC;IACpBxE,EAAA,CAAAuB,SAAA,EAA0B;IAA1BvB,EAAA,CAAA0B,UAAA,qBAAAT,MAAA,CAAAuD,gBAAA,CAA0B;IAKvCxE,EAAA,CAAAuB,SAAA,EAAwB;IAAxBvB,EAAA,CAAA0B,UAAA,SAAAT,MAAA,CAAAsD,KAAA,CAAAE,MAAA,OAAwB;;;;;IAiBtDzE,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAI,SAAA,sBAAyC;IACzCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mFAAgB;IACrBF,EADqB,CAAAG,YAAA,EAAI,EACnB;;;ADnJR,WAAauE,cAAc;EAArB,MAAOA,cAAc;IAoBfC,IAAA;IACAC,MAAA;IACAC,EAAA;IACAC,QAAA;IACAC,MAAA;IAtBV;IACA1C,SAAS,GAAG,KAAK;IACjB2C,WAAW,GAAG,KAAK;IACnBvD,WAAW,GAAgB,IAAI;IAE/B;IACA8C,KAAK,GAAW,EAAE;IAElB;IACA5C,QAAQ;IAER;IACA6C,gBAAgB,GAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;IAEhF;IACQS,aAAa,GAAmB,EAAE;IAE1CC,YACUP,IAAgB,EAChBC,MAAc,EACdC,EAAe,EACfC,QAAqB,EACrBC,MAAiB;MAJjB,KAAAJ,IAAI,GAAJA,IAAI;MACJ,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,MAAM,GAANA,MAAM;MAEd,IAAI,CAACpD,QAAQ,GAAG,IAAI,CAACwD,UAAU,EAAE;IACnC;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,SAAS,EAAE;IAClB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQN,UAAUA,CAAA;MAChB,OAAO,IAAI,CAACN,EAAE,CAACa,KAAK,CAAC;QACnBC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACuG,QAAQ,EAAEvG,UAAU,CAACwG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACzG,UAAU,CAACuG,QAAQ,EAAEvG,UAAU,CAACwG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC1G,UAAU,CAACuG,QAAQ,EAAEvG,UAAU,CAACwG,SAAS,CAAC,CAAC,CAAC,EAAExG,UAAU,CAAC2G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QACpFC,QAAQ,EAAE,CAAC,IAAI;OAChB,CAAC;IACJ;IAEA;;;IAGAZ,SAASA,CAAA;MACP,IAAI,CAAChD,SAAS,GAAG,IAAI;MAErB,MAAMmD,GAAG,GAAG,IAAI,CAACb,IAAI,CAAC9C,GAAG,CAAM,2CAA2C,CAAC,CAACqE,SAAS,CAAC;QACpFC,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;UACxC,IAAI,CAAC7B,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,IAAI,EAAE;UACjC,IAAI,CAAClC,SAAS,GAAG,KAAK;QACxB,CAAC;QACDkE,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC;UACxC,IAAI,CAACnE,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MACF,IAAI,CAAC4C,aAAa,CAACwB,IAAI,CAACjB,GAAG,CAAC;IAC9B;IAEA;;;IAGAhC,WAAWA,CAAA;MACT,IAAI,CAACwB,WAAW,GAAG,IAAI;MACvB,IAAI,CAACvD,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,QAAQ,CAAC+E,KAAK,EAAE;MACrB,IAAI,CAAC/E,QAAQ,CAACgF,UAAU,CAAC;QAAEV,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC9C;IAEA;;;IAGA9C,QAAQA,CAACyD,IAAU;MACjB,IAAI,CAAC5B,WAAW,GAAG,IAAI;MACvB,IAAI,CAACvD,WAAW,GAAGmF,IAAI;MACvB,IAAI,CAACjF,QAAQ,CAACgF,UAAU,CAAC;QACvBhB,MAAM,EAAEiB,IAAI,CAACrE,MAAM;QACnBuD,MAAM,EAAEc,IAAI,CAACnE,MAAM;QACnBsD,IAAI,EAAEa,IAAI,CAACjE,IAAI;QACfsD,QAAQ,EAAEW,IAAI,CAAC/D;OAChB,CAAC;IACJ;IAEA;;;IAGAvB,QAAQA,CAAA;MACN,IAAI,IAAI,CAACK,QAAQ,CAACkF,KAAK,EAAE;QACvB,IAAI,CAACxE,SAAS,GAAG,IAAI;QAErB,MAAMyE,SAAS,GAAG,IAAI,CAACnF,QAAQ,CAACoF,KAAK;QACrC,MAAMC,OAAO,GAAG;UACdrB,MAAM,EAAEmB,SAAS,CAACnB,MAAM;UACxBG,MAAM,EAAEgB,SAAS,CAAChB,MAAM;UACxBC,IAAI,EAAEe,SAAS,CAACf,IAAI,CAACkB,WAAW,EAAE;UAClChB,QAAQ,EAAEa,SAAS,CAACb;SACrB;QAED,MAAMiB,OAAO,GAAG,IAAI,CAACzF,WAAW,GAC5B,IAAI,CAACkD,IAAI,CAACwC,GAAG,CAAM,mCAAmC,IAAI,CAAC1F,WAAW,CAAC2F,EAAE,EAAE,EAAEJ,OAAO,CAAC,GACrF,IAAI,CAACrC,IAAI,CAAC0C,IAAI,CAAM,iCAAiC,EAAEL,OAAO,CAAC;QAEnE,MAAMxB,GAAG,GAAG0B,OAAO,CAAChB,SAAS,CAAC;UAC5BC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAC/D,SAAS,GAAG,KAAK;YACtB,IAAI,CAACiF,WAAW,CAAC,IAAI,CAAC7F,WAAW,GAAG,yBAAyB,GAAG,yBAAyB,CAAC;YAC1F,IAAI,CAACuD,WAAW,GAAG,KAAK;YACxB,IAAI,CAACK,SAAS,EAAE;UAClB,CAAC;UACDkB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAClE,SAAS,GAAG,KAAK;YACtBgE,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;YAC1C,IAAI,CAACC,SAAS,CAAC,qBAAqB,CAAC;UACvC;SACD,CAAC;QACF,IAAI,CAACvB,aAAa,CAACwB,IAAI,CAACjB,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC+B,oBAAoB,EAAE;QAC3B,IAAI,CAACf,SAAS,CAAC,+BAA+B,CAAC;MACjD;IACF;IAEA;;;IAGAnD,UAAUA,CAACuD,IAAU;MACnB,IAAIY,OAAO,CAAC,iCAAiCZ,IAAI,CAACrE,MAAM,IAAI,CAAC,EAAE;QAC7D,IAAI,CAACF,SAAS,GAAG,IAAI;QAErB,MAAMmD,GAAG,GAAG,IAAI,CAACb,IAAI,CAAC8C,MAAM,CAAC,mCAAmCb,IAAI,CAACQ,EAAE,EAAE,CAAC,CAAClB,SAAS,CAAC;UACnFC,IAAI,EAAGC,QAAa,IAAI;YACtB,IAAI,CAAC/D,SAAS,GAAG,KAAK;YACtB,IAAI,CAACiF,WAAW,CAAC,uBAAuB,CAAC;YACzC,IAAI,CAACjC,SAAS,EAAE;UAClB,CAAC;UACDkB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAClE,SAAS,GAAG,KAAK;YACtBgE,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C,IAAI,CAACC,SAAS,CAAC,qBAAqB,CAAC;UACvC;SACD,CAAC;QACF,IAAI,CAACvB,aAAa,CAACwB,IAAI,CAACjB,GAAG,CAAC;MAC9B;IACF;IAEA;;;IAGApE,UAAUA,CAAA;MACR,IAAI,CAAC4D,WAAW,GAAG,KAAK;MACxB,IAAI,CAACvD,WAAW,GAAG,IAAI;MACvB,IAAI,CAACE,QAAQ,CAAC+E,KAAK,EAAE;IACvB;IAEA;;;IAGAgB,MAAMA,CAAA;MACJ,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGQJ,oBAAoBA,CAAA;MAC1BK,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClG,QAAQ,CAACmG,QAAQ,CAAC,CAACvC,OAAO,CAACwC,GAAG,IAAG;QAChD,MAAMC,OAAO,GAAG,IAAI,CAACrG,QAAQ,CAACE,GAAG,CAACkG,GAAG,CAAC;QACtCC,OAAO,EAAEC,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA;;;IAGQX,WAAWA,CAACY,OAAe;MACjC,IAAI,CAACpD,QAAQ,CAACqD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;;;IAGQ/B,SAASA,CAAC0B,OAAe;MAC/B,IAAI,CAACpD,QAAQ,CAACqD,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;;uCA5MW7D,cAAc,EAAA1E,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA5I,EAAA,CAAAwI,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAAwI,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAhJ,EAAA,CAAAwI,iBAAA,CAAAS,EAAA,CAAAC,SAAA;IAAA;;YAAdxE,cAAc;MAAAyE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CnBzJ,EANR,CAAAC,cAAA,aAA6B,aAGF,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAa,UAAA,mBAAA8I,gDAAA;YAAA,OAASD,GAAA,CAAAhC,MAAA,EAAQ;UAAA,EAAC;UACzD1H,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,6DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,+IAAyB;UAEtDF,EAFsD,CAAAG,YAAA,EAAI,EAClD,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBACwD;UAAxBD,EAAA,CAAAa,UAAA,mBAAA+I,iDAAA;YAAA,OAASF,GAAA,CAAAlG,WAAA,EAAa;UAAA,EAAC;UAC/ExD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAI1BF,EAJ0B,CAAAG,YAAA,EAAO,EAClB,EACL,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAAqB;UAGnBD,EAAA,CAAAK,UAAA,KAAAwJ,mCAAA,yBAAgD;UA+E1C7J,EAHN,CAAAC,cAAA,oBAA6B,uBACV,sBACC,gBACJ;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;UAClBH,EAAA,CAAAC,cAAA,wBAAkB;UAShBD,EANA,CAAAK,UAAA,KAAAyJ,8BAAA,kBAAiD,KAAAC,8BAAA,mBAMD;UAwEtD/J,EAHI,CAAAG,YAAA,EAAmB,EACV,EAEP;UAGNH,EAAA,CAAAK,UAAA,KAAA2J,8BAAA,kBAA+C;UAKjDhK,EAAA,CAAAG,YAAA,EAAM;;;UA5K2BH,EAAA,CAAAuB,SAAA,IAAiB;UAAjBvB,EAAA,CAAA0B,UAAA,SAAAgI,GAAA,CAAA1E,WAAA,CAAiB;UAsFVhF,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA0B,UAAA,SAAAgI,GAAA,CAAArH,SAAA,CAAe;UAMjBrC,EAAA,CAAAuB,SAAA,EAAgB;UAAhBvB,EAAA,CAAA0B,UAAA,UAAAgI,GAAA,CAAArH,SAAA,CAAgB;UA2EtBrC,EAAA,CAAAuB,SAAA,EAAe;UAAfvB,EAAA,CAAA0B,UAAA,SAAAgI,GAAA,CAAArH,SAAA,CAAe;;;qBDjK3CnD,YAAY,EAAA+K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhL,WAAW,EAAA0J,EAAA,CAAAuB,aAAA,EAAAvB,EAAA,CAAAwB,oBAAA,EAAAxB,EAAA,CAAAyB,eAAA,EAAAzB,EAAA,CAAA0B,oBAAA,EAAA1B,EAAA,CAAA2B,iBAAA,EAAA3B,EAAA,CAAA4B,kBAAA,EACXrL,mBAAmB,EAAAyJ,EAAA,CAAA6B,kBAAA,EAAA7B,EAAA,CAAA8B,eAAA,EACnBrL,aAAa,EAAAsL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,YAAA,EACb1L,eAAe,EAAA2L,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf5L,aAAa,EAAA6L,EAAA,CAAAC,OAAA,EACb7L,kBAAkB,EAAA8L,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,QAAA,EAAAH,GAAA,CAAAI,SAAA,EAClBjM,cAAc,EAAAkM,GAAA,CAAAC,QAAA,EACdlM,cAAc,EAAAmM,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACd5M,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EAAA2M,GAAA,CAAAC,kBAAA,EACxB3M,iBAAiB,EAAA4M,GAAA,CAAAC,WAAA;MAAAC,MAAA;IAAA;;SAKRnI,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}