/* Terra Retail ERP Styles */
:host {
  display: block;
  height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* App Container */
.app-container {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main";
  grid-template-rows: 60px 1fr;
  grid-template-columns: 250px 1fr;
  height: 100vh;
  transition: grid-template-columns 0.3s ease;

  &.sidebar-collapsed {
    grid-template-columns: 60px 1fr;
  }
}

/* Header */
.app-header {
  grid-area: header;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1000;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .sidebar-toggle {
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(255,255,255,0.1);
      }
    }

    .app-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        font-size: 24px;
      }
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .user-name {
        font-weight: 500;
      }

      .dropdown-toggle {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        padding: 5px;

        &:hover {
          color: #f8f9fa;
        }
      }
    }
  }
}

/* Sidebar */
.app-sidebar {
  grid-area: sidebar;
  background: #2c3e50;
  color: white;
  overflow-y: auto;
  transition: width 0.3s ease;

  .sidebar-nav {
    padding: 20px 0;

    .nav-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .nav-item {
        .nav-link {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          color: #bdc3c7;
          text-decoration: none;
          transition: all 0.2s;
          border-right: 3px solid transparent;

          i {
            font-size: 18px;
            width: 20px;
            margin-left: 12px;
          }

          .nav-text {
            font-weight: 500;
          }

          &:hover {
            background-color: #34495e;
            color: white;
            border-right-color: #3498db;
          }

          &.active {
            background-color: #3498db;
            color: white;
            border-right-color: #2980b9;
          }
        }
      }
    }
  }
}

/* Sidebar Collapsed State */
.sidebar-collapsed {
  .app-sidebar {
    .nav-link {
      justify-content: center;
      padding: 12px 10px;

      .nav-text {
        display: none;
      }

      i {
        margin-left: 0;
      }
    }
  }
}

/* Main Content */
.app-main {
  grid-area: main;
  background: #f8f9fa;
  overflow-y: auto;

  .main-content {
    padding: 20px;
    min-height: calc(100vh - 60px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    grid-template-areas: 
      "header"
      "main";
    grid-template-rows: 60px 1fr;
    grid-template-columns: 1fr;

    .app-sidebar {
      position: fixed;
      left: -250px;
      top: 60px;
      height: calc(100vh - 60px);
      width: 250px;
      z-index: 999;
      transition: left 0.3s ease;

      &.show {
        left: 0;
      }
    }
  }
}

/* Utility Classes */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;

  &.btn-link {
    background: none;
    color: inherit;
    text-decoration: none;

    &:hover {
      text-decoration: none;
    }
  }
}

.dropdown {
  position: relative;

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 200px;
    z-index: 1000;
    display: none;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      padding: 8px 16px;
      color: #212529;
      text-decoration: none;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
      }

      i {
        margin-left: 8px;
        width: 16px;
      }
    }

    .dropdown-divider {
      height: 0;
      margin: 8px 0;
      overflow: hidden;
      border-top: 1px solid #dee2e6;
    }
  }
}
