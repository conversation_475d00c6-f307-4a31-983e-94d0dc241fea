using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ReferralSourcesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public ReferralSourcesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetReferralSources()
        {
            try
            {
                var referralSources = await _context.ReferralSources
                    .Where(rs => rs.IsActive)
                    .OrderBy(rs => rs.NameAr)
                    .Select(rs => new
                    {
                        id = rs.Id,
                        nameAr = rs.NameAr,
                        nameEn = rs.NameEn,
                        code = rs.Code,
                        isActive = rs.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة مصادر التعرف بنجاح",
                    data = referralSources,
                    count = referralSources.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة مصادر التعرف",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetReferralSource(int id)
        {
            try
            {
                var referralSource = await _context.ReferralSources
                    .Where(rs => rs.Id == id && rs.IsActive)
                    .Select(rs => new
                    {
                        id = rs.Id,
                        nameAr = rs.NameAr,
                        nameEn = rs.NameEn,
                        code = rs.Code,
                        isActive = rs.IsActive,
                        createdAt = rs.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (referralSource == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "مصدر التعرف غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات مصدر التعرف بنجاح",
                    data = referralSource
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات مصدر التعرف",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreateReferralSource(CreateReferralSourceRequest request)
        {
            try
            {
                var referralSource = new ReferralSource
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.ReferralSources.Add(referralSource);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetReferralSource), new { id = referralSource.Id }, new
                {
                    success = true,
                    message = "تم إضافة مصدر التعرف بنجاح",
                    data = new
                    {
                        id = referralSource.Id,
                        nameAr = referralSource.NameAr,
                        nameEn = referralSource.NameEn,
                        code = referralSource.Code,
                        isActive = referralSource.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة مصدر التعرف",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdateReferralSource(int id, UpdateReferralSourceRequest request)
        {
            try
            {
                var referralSource = await _context.ReferralSources.FindAsync(id);
                if (referralSource == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "مصدر التعرف غير موجود"
                    });
                }

                referralSource.NameAr = request.NameAr;
                referralSource.NameEn = request.NameEn;
                referralSource.Code = request.Code;
                referralSource.IsActive = request.IsActive;
                referralSource.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات مصدر التعرف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث مصدر التعرف",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteReferralSource(int id)
        {
            try
            {
                var referralSource = await _context.ReferralSources.FindAsync(id);
                if (referralSource == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "مصدر التعرف غير موجود"
                    });
                }

                referralSource.IsActive = false;
                referralSource.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف مصدر التعرف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف مصدر التعرف",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreateReferralSourceRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
    }

    public class UpdateReferralSourceRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
