using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("🏭 Purchase Management")]
    public class PurchasesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public PurchasesController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PurchaseInvoice>>> GetPurchases(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? supplierId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.PurchaseInvoices
                    .Include(p => p.Supplier)
                    .Include(p => p.Branch)
                    .Include(p => p.User)
                    .Include(p => p.PurchaseInvoiceDetails)
                        .ThenInclude(pid => pid.Product)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.InvoiceNumber.Contains(search) || 
                                           p.SupplierInvoiceNumber!.Contains(search) ||
                                           p.Supplier.NameAr.Contains(search));
                }

                if (supplierId.HasValue)
                    query = query.Where(p => p.SupplierId == supplierId);

                if (branchId.HasValue)
                    query = query.Where(p => p.BranchId == branchId);

                if (status.HasValue)
                    query = query.Where(p => p.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(p => p.InvoiceDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(p => p.InvoiceDate <= toDate);

                var totalCount = await query.CountAsync();
                var purchases = await query
                    .OrderByDescending(p => p.InvoiceDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new
                    {
                        p.Id,
                        p.InvoiceNumber,
                        p.SupplierInvoiceNumber,
                        p.InvoiceDate,
                        p.DueDate,
                        p.SupplierId,
                        SupplierName = p.Supplier.NameAr,
                        p.BranchId,
                        BranchName = p.Branch.NameAr,
                        p.Status,
                        p.SubTotal,
                        p.DiscountAmount,
                        p.TaxAmount,
                        p.TotalAmount,
                        p.PaidAmount,
                        p.RemainingAmount,
                        ItemsCount = p.PurchaseInvoiceDetails.Count,
                        p.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = purchases,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<PurchaseInvoice>> GetPurchase(int id)
        {
            try
            {
                var purchase = await _context.PurchaseInvoices
                    .Include(p => p.Supplier)
                    .Include(p => p.Branch)
                    .Include(p => p.User)
                    .Include(p => p.PurchaseInvoiceDetails)
                        .ThenInclude(pid => pid.Product)
                            .ThenInclude(pr => pr.Unit)
                    .Include(p => p.PurchasePayments)
                        .ThenInclude(pp => pp.PaymentMethod)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (purchase == null)
                    return NotFound(new { message = "فاتورة المشتريات غير موجودة" });

                return Ok(purchase);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<PurchaseInvoice>> CreatePurchase(CreatePurchaseRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                // Generate invoice number
                var invoiceNumber = await GenerateInvoiceNumber(request.BranchId);

                // Calculate totals
                var subTotal = request.Items.Sum(i => i.OrderedQuantity * i.UnitCost);
                var discountAmount = subTotal * (request.DiscountPercentage / 100);
                var taxableAmount = subTotal - discountAmount;
                var taxAmount = taxableAmount * (request.TaxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount + request.ShippingCost + request.OtherCharges;

                var purchase = new PurchaseInvoice
                {
                    InvoiceNumber = invoiceNumber,
                    SupplierInvoiceNumber = request.SupplierInvoiceNumber,
                    SupplierId = request.SupplierId,
                    BranchId = request.BranchId,
                    UserId = userId.Value,
                    InvoiceDate = request.InvoiceDate ?? DateTime.Now,
                    DueDate = request.DueDate,
                    Status = 1, // Pending
                    SubTotal = subTotal,
                    DiscountPercentage = request.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TaxPercentage = request.TaxPercentage,
                    TaxAmount = taxAmount,
                    ShippingCost = request.ShippingCost,
                    OtherCharges = request.OtherCharges,
                    TotalAmount = totalAmount,
                    PaidAmount = 0,
                    RemainingAmount = totalAmount,
                    Notes = request.Notes,
                    DeliveryAddress = request.DeliveryAddress,
                    Currency = request.Currency ?? "EGP",
                    ExchangeRate = request.ExchangeRate ?? 1,
                    CreatedAt = DateTime.Now
                };

                _context.PurchaseInvoices.Add(purchase);
                await _context.SaveChangesAsync();

                // Add purchase items
                int lineNumber = 1;
                foreach (var item in request.Items)
                {
                    var lineTotal = item.OrderedQuantity * item.UnitCost;
                    var lineDiscountAmount = lineTotal * (item.DiscountPercentage / 100);
                    var netLineTotal = lineTotal - lineDiscountAmount;
                    var lineTaxAmount = netLineTotal * (item.TaxPercentage / 100);
                    var finalTotal = netLineTotal + lineTaxAmount;

                    var purchaseDetail = new PurchaseInvoiceDetail
                    {
                        PurchaseInvoiceId = purchase.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        OrderedQuantity = item.OrderedQuantity,
                        ReceivedQuantity = 0,
                        RemainingQuantity = item.OrderedQuantity,
                        UnitCost = item.UnitCost,
                        DiscountPercentage = item.DiscountPercentage,
                        DiscountAmount = lineDiscountAmount,
                        NetUnitCost = item.UnitCost - (lineDiscountAmount / item.OrderedQuantity),
                        LineTotal = lineTotal,
                        NetLineTotal = netLineTotal,
                        TaxPercentage = item.TaxPercentage,
                        TaxAmount = lineTaxAmount,
                        FinalTotal = finalTotal,
                        ItemNotes = item.ItemNotes,
                        BatchNumber = item.BatchNumber,
                        ExpiryDate = item.ExpiryDate,
                        ManufacturingDate = item.ManufacturingDate,
                        CreatedAt = DateTime.Now
                    };

                    _context.PurchaseInvoiceDetails.Add(purchaseDetail);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Load related data for response
                await _context.Entry(purchase)
                    .Reference(p => p.Supplier)
                    .LoadAsync();
                await _context.Entry(purchase)
                    .Reference(p => p.Branch)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetPurchase), new { id = purchase.Id }, purchase);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}/receive")]
        public async Task<IActionResult> ReceivePurchase(int id, ReceivePurchaseRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var purchase = await _context.PurchaseInvoices
                    .Include(p => p.PurchaseInvoiceDetails)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (purchase == null)
                    return NotFound(new { message = "فاتورة المشتريات غير موجودة" });

                if (purchase.Status != 1)
                    return BadRequest(new { message = "لا يمكن استلام هذه الفاتورة" });

                // Update received quantities
                foreach (var receivedItem in request.ReceivedItems)
                {
                    var detail = purchase.PurchaseInvoiceDetails
                        .FirstOrDefault(d => d.Id == receivedItem.DetailId);

                    if (detail != null)
                    {
                        detail.ReceivedQuantity = receivedItem.ReceivedQuantity;
                        detail.RemainingQuantity = detail.OrderedQuantity - receivedItem.ReceivedQuantity;

                        // Update product stock
                        await UpdateProductStock(detail.ProductId, purchase.BranchId, 
                            receivedItem.ReceivedQuantity, detail.UnitCost);

                        // Create stock movement
                        await CreateStockMovement(detail.ProductId, purchase.BranchId, 
                            receivedItem.ReceivedQuantity, detail.UnitCost, "Purchase", 
                            purchase.Id, purchase.InvoiceNumber, userId.Value);

                        // Create product batch if provided
                        if (!string.IsNullOrEmpty(receivedItem.BatchNumber))
                        {
                            var batch = new ProductBatch
                            {
                                ProductId = detail.ProductId,
                                BranchId = purchase.BranchId,
                                BatchNumber = receivedItem.BatchNumber,
                                Quantity = receivedItem.ReceivedQuantity,
                                RemainingQuantity = receivedItem.ReceivedQuantity,
                                CostPrice = detail.UnitCost,
                                ExpiryDate = receivedItem.ExpiryDate,
                                ManufacturingDate = receivedItem.ManufacturingDate,
                                PurchaseId = purchase.Id,
                                CreatedAt = DateTime.Now
                            };
                            _context.ProductBatches.Add(batch);
                        }
                    }
                }

                purchase.Status = 2; // Received
                purchase.ReceivedDate = DateTime.Now;
                purchase.ReceivedById = userId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { message = "تم استلام الفاتورة بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetPurchasesDashboard([FromQuery] int? branchId = null, [FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                var query = _context.PurchaseInvoices.AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(p => p.BranchId == branchId);

                // Today's purchases
                var todayPurchases = await query
                    .Where(p => p.InvoiceDate.Date == targetDate && p.Status != 3)
                    .GroupBy(p => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(p => p.TotalAmount),
                        Paid = g.Sum(p => p.PaidAmount),
                        Remaining = g.Sum(p => p.RemainingAmount)
                    })
                    .FirstOrDefaultAsync();

                // This month's purchases
                var monthStart = new DateTime(targetDate.Year, targetDate.Month, 1);
                var monthPurchases = await query
                    .Where(p => p.InvoiceDate >= monthStart && p.InvoiceDate < monthStart.AddMonths(1) && p.Status != 3)
                    .GroupBy(p => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(p => p.TotalAmount)
                    })
                    .FirstOrDefaultAsync();

                // Top suppliers
                var topSuppliers = await _context.PurchaseInvoices
                    .Include(p => p.Supplier)
                    .Where(p => p.InvoiceDate.Date == targetDate && p.Status != 3)
                    .GroupBy(p => new { p.SupplierId, p.Supplier.NameAr })
                    .Select(g => new
                    {
                        SupplierId = g.Key.SupplierId,
                        SupplierName = g.Key.NameAr,
                        Count = g.Count(),
                        Amount = g.Sum(p => p.TotalAmount)
                    })
                    .OrderByDescending(s => s.Amount)
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    today = todayPurchases ?? new { Count = 0, Total = 0m, Paid = 0m, Remaining = 0m },
                    month = monthPurchases ?? new { Count = 0, Total = 0m },
                    topSuppliers
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateInvoiceNumber(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.PurchaseInvoice && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.PurchaseInvoice,
                    Prefix = "PUR",
                    CurrentValue = 1,
                    NumberLength = 8,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task UpdateProductStock(int productId, int branchId, decimal quantity, decimal unitCost)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            if (stock != null)
            {
                stock.AvailableQuantity += quantity;
                stock.TotalInQuantity += quantity;
                
                // Update average cost
                var totalValue = (stock.AvailableQuantity - quantity) * stock.AverageCostPrice + quantity * unitCost;
                stock.AverageCostPrice = stock.AvailableQuantity > 0 ? totalValue / stock.AvailableQuantity : unitCost;
                stock.LastCostPrice = unitCost;
                stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;
                stock.UpdatedAt = DateTime.Now;
            }
        }

        private async Task CreateStockMovement(int productId, int branchId, decimal quantity, decimal unitCost, 
            string movementReason, int sourceId, string sourceReference, int userId)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            var movement = new StockMovement
            {
                ProductId = productId,
                BranchId = branchId,
                MovementNumber = $"MOV{DateTime.Now.Ticks}",
                MovementType = 1, // In
                MovementReason = movementReason,
                Quantity = quantity,
                UnitCost = unitCost,
                TotalCost = quantity * unitCost,
                BalanceBefore = (stock?.AvailableQuantity ?? 0) - quantity,
                BalanceAfter = stock?.AvailableQuantity ?? 0,
                MovementDate = DateTime.Now,
                SourceId = sourceId,
                SourceType = "Purchase",
                UserId = userId,
                Notes = $"Purchase Invoice: {sourceReference}",
                CreatedAt = DateTime.Now
            };

            _context.StockMovements.Add(movement);
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class CreatePurchaseRequest
    {
        public string? SupplierInvoiceNumber { get; set; }
        public int SupplierId { get; set; }
        public int BranchId { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public decimal ShippingCost { get; set; } = 0;
        public decimal OtherCharges { get; set; } = 0;
        public string? Notes { get; set; }
        public string? DeliveryAddress { get; set; }
        public string? Currency { get; set; }
        public decimal? ExchangeRate { get; set; }
        public List<CreatePurchaseItemRequest> Items { get; set; } = new();
    }

    public class CreatePurchaseItemRequest
    {
        public int ProductId { get; set; }
        public decimal OrderedQuantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public string? ItemNotes { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }
    }

    public class ReceivePurchaseRequest
    {
        public List<ReceivePurchaseItemRequest> ReceivedItems { get; set; } = new();
    }

    public class ReceivePurchaseItemRequest
    {
        public int DetailId { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }
    }
}
