/* Terra Retail ERP - Customers Page - Fixed Layout */

.customers-page {
  padding: 1rem;
  direction: rtl;
  background: #f8fafc;
  min-height: 100vh;
  font-family: '<PERSON><PERSON><PERSON>', 'IBM Plex Sans Arabic', sans-serif;
  
  * {
    box-sizing: border-box;
  }
}

/* إصلاح شامل لجميع Material Form Fields */
::ng-deep .customers-page,
::ng-deep {
  .mat-mdc-form-field {
    width: 100% !important;
    margin-bottom: 0 !important;
    
    .mat-mdc-form-field-wrapper {
      width: 100% !important;
      padding-bottom: 0 !important;
    }
    
    .mat-mdc-form-field-flex {
      width: 100% !important;
      align-items: center !important;
    }
    
    .mat-mdc-form-field-infix {
      width: 100% !important;
      padding: 0.75rem 1rem !important;
      border: none !important;
      min-height: 48px !important;
    }
    
    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
    }
    
    .mat-mdc-text-field-wrapper {
      width: 100% !important;
      border: 1px solid #e2e8f0 !important;
      border-radius: 8px !important;
      background: white !important;
      min-height: 48px !important;
      
      &:hover {
        border-color: #cbd5e1 !important;
      }
      
      &.mdc-text-field--focused {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
      }
    }
    
    .mat-mdc-select-trigger {
      width: 100% !important;
      min-height: 48px !important;
      display: flex !important;
      align-items: center !important;
    }
    
    .mat-mdc-select-value {
      width: 100% !important;
    }

    .mat-mdc-select-arrow-wrapper {
      transform: none !important;
    }

    .mat-mdc-select-arrow {
      color: #6b7280 !important;
    }
  }

  // إصلاح خاص للـ select panels
  .mat-mdc-select-panel {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e2e8f0 !important;

    .mat-mdc-option {
      padding: 0.75rem 1rem !important;
      font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif !important;

      &:hover {
        background-color: #f8fafc !important;
      }

      &.mdc-list-item--selected {
        background-color: #eff6ff !important;
        color: #1d4ed8 !important;
      }
    }
  }
}

/* رأس الصفحة */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-info h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.header-info p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* إحصائيات */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-content {
  padding: 1.5rem;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* قسم البحث والفلاتر */
.search-filters-wrapper {
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section,
.filters-section {
  width: 100%;
}

.search-card,
.filters-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  background: white;
  
  .mat-mdc-card-content {
    padding: 1.5rem !important;
  }
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.search-input {
  width: 100%;
  max-width: 600px;
}

.filters-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.clear-filters-btn {
  height: 48px !important;
  padding: 0 1.5rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  background: #3b82f6 !important;
  color: white !important;
  
  &:hover {
    background: #2563eb !important;
  }
}

/* جدول العملاء */
.customers-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.customers-table {
  width: 100%;
  
  .mat-mdc-header-row {
    background: #f8fafc;
    
    .mat-mdc-header-cell {
      font-weight: 600;
      color: #374151;
      border-bottom: 1px solid #e5e7eb;
      padding: 1rem;
    }
  }
  
  .mat-mdc-row {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #f8fafc;
    }
    
    .mat-mdc-cell {
      padding: 1rem;
      border-bottom: 1px solid #f3f4f6;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* رسائل التحميل والأخطاء */
.loading-container,
.no-data-container {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.loading-spinner {
  margin: 0 auto 1rem;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .customers-page {
    padding: 0.5rem;
  }
  
  .page-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-item {
    min-width: auto;
    width: 100%;
  }
  
  .clear-filters-btn {
    width: 100% !important;
  }
  
  .customers-table {
    font-size: 0.85rem;
    
    .mat-mdc-cell,
    .mat-mdc-header-cell {
      padding: 0.75rem 0.5rem;
    }
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .search-input {
    max-width: none;
  }
  
  .header-info h1 {
    font-size: 1.5rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
