import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

interface Customer {
  id: number;
  fullName: string;
  customerCode: string;
  phoneNumber: string;
  email: string;
  customerTypeId: number;
  areaId: number;
  isActive: boolean;
  balance?: number;
}

interface CustomerType {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface Area {
  id: number;
  nameAr: string;
  nameEn: string;
}

@Component({
  selector: 'app-customers',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatMenuModule,
    MatSnackBarModule
  ],
  templateUrl: './customers.html',
  styleUrls: ['./customers-simple.scss']
})
export class Customers implements OnInit {
  // Stats
  totalCustomers = 0;
  activeCustomers = 0;
  newCustomers = 0;
  vipCustomers = 0;

  // Data
  customers: Customer[] = [];
  filteredCustomers: Customer[] = [];
  customerTypes: CustomerType[] = [];
  areas: Area[] = [];

  // Filters
  searchTerm = '';
  selectedCustomerType = '';
  selectedArea = '';

  // Table
  displayedColumns: string[] = ['avatar', 'name', 'contact', 'type', 'area', 'status', 'actions'];
  isLoading = false;

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    console.log('Customers component initialized');
    this.loadData();
  }

  loadData() {
    this.isLoading = true;

    // Load customers
    this.loadCustomers();

    // Load customer types
    this.loadCustomerTypes();

    // Load areas
    this.loadAreas();
  }

  loadCustomers() {
    this.isLoading = true;
    this.http.get<any>('http://localhost:5127/api/customers').subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.customers = response.map((c: any) => ({
          id: c.id,
          fullName: c.nameAr || c.fullName,
          customerCode: c.customerCode,
          phoneNumber: c.phoneNumber,
          email: c.email,
          address: c.address,
          customerTypeId: c.customerTypeId || 1,
          areaId: c.areaId || 1,
          isActive: c.isActive
        }));
        this.filteredCustomers = [...this.customers];
        this.totalCustomers = this.customers.length;
        this.activeCustomers = this.customers.filter(c => c.isActive).length;
        this.isLoading = false;

        console.log('Loaded customers from database:', this.customers);

        if (this.customers.length === 0) {
          this.showMessage('لا توجد بيانات عملاء في قاعدة البيانات');
        }
      },
      error: (error) => {
        console.error('Error loading customers:', error);
        this.showMessage('خطأ في تحميل بيانات العملاء من قاعدة البيانات: ' + error.message);
        this.customers = [];
        this.filteredCustomers = [];
        this.totalCustomers = 0;
        this.activeCustomers = 0;
        this.isLoading = false;
      }
    });
  }

  loadCustomerTypes() {
    this.http.get<any>('http://localhost:5127/api/customertypes').subscribe({
      next: (response) => {
        console.log('Customer Types Response:', response);
        this.customerTypes = response || [];
      },
      error: (error) => {
        console.error('Error loading customer types:', error);
        // إضافة بيانات افتراضية في حالة الخطأ
        this.customerTypes = [
          { id: 1, nameAr: 'عميل عادي', nameEn: 'Regular Customer' },
          { id: 2, nameAr: 'عميل جملة', nameEn: 'Wholesale Customer' },
          { id: 3, nameAr: 'عميل VIP', nameEn: 'VIP Customer' }
        ];
      }
    });
  }

  loadAreas() {
    this.http.get<any>('http://localhost:5127/api/areas').subscribe({
      next: (response) => {
        console.log('Areas Response:', response);
        this.areas = response || [];
      },
      error: (error) => {
        console.error('Error loading areas:', error);
        // إضافة بيانات افتراضية في حالة الخطأ
        this.areas = [
          { id: 1, nameAr: 'القاهرة', nameEn: 'Cairo' },
          { id: 2, nameAr: 'الإسكندرية', nameEn: 'Alexandria' },
          { id: 3, nameAr: 'الجيزة', nameEn: 'Giza' }
        ];
      }
    });
  }



  onSearch() {
    this.applyFilters();
  }

  onFilterChange() {
    this.applyFilters();
  }

  applyFilters() {
    this.filteredCustomers = this.customers.filter(customer => {
      const matchesSearch = !this.searchTerm ||
        customer.fullName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        customer.phoneNumber.includes(this.searchTerm) ||
        customer.email.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesType = !this.selectedCustomerType ||
        customer.customerTypeId.toString() === this.selectedCustomerType;

      const matchesArea = !this.selectedArea ||
        customer.areaId.toString() === this.selectedArea;

      return matchesSearch && matchesType && matchesArea;
    });
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedCustomerType = '';
    this.selectedArea = '';
    this.filteredCustomers = [...this.customers];
  }

  getCustomerTypeName(typeId: number): string {
    const type = this.customerTypes.find(t => t.id === typeId);
    return type ? type.nameAr : 'عميل عادي';
  }

  getCustomerTypeClass(typeId: number): string {
    const classes = ['regular', 'wholesale', 'vip', 'merchant', 'corporate'];
    return classes[(typeId - 1) % classes.length];
  }

  getAreaName(areaId: number): string {
    const area = this.areas.find(a => a.id === areaId);
    return area ? area.nameAr : 'القاهرة';
  }

  addCustomer() {
    this.showMessage('إضافة عميل جديد - قيد التطوير');
  }

  viewCustomer(customer: Customer) {
    this.showMessage(`عرض تفاصيل العميل: ${customer.fullName}`);
  }

  editCustomer(customer: Customer) {
    this.showMessage(`تعديل العميل: ${customer.fullName} - قيد التطوير`);
  }

  deleteCustomer(customer: Customer) {
    this.showMessage(`حذف العميل: ${customer.fullName} - قيد التطوير`);
  }

  exportCustomers() {
    this.showMessage('تصدير بيانات العملاء - قيد التطوير');
  }

  importCustomers() {
    this.showMessage('استيراد عملاء - قيد التطوير');
  }

  printCustomers() {
    this.showMessage('طباعة قائمة العملاء - قيد التطوير');
  }

  getTotalBalance(): number {
    return this.customers.reduce((total, customer) => total + (customer.balance || 0), 0);
  }

  private showMessage(message: string) {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
