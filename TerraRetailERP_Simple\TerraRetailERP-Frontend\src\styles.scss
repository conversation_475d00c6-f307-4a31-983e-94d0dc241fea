/* You can add global styles to this file, and also import other style files */

// Bootstrap
@import '~bootstrap/scss/bootstrap';

// Arabic RTL Support
body {
  direction: rtl;
  text-align: right;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Custom ERP Styles
.navbar-brand {
  font-weight: bold;
  color: #2c3e50 !important;
}

.sidebar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 8px;
  margin: 2px 0;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(-5px);
}

.main-content {
  background: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

// Arabic number support
.arabic-numbers {
  font-family: 'Arial', sans-serif;
}

// Table styles
.table {
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-weight: 500;
}

// Form styles
.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
