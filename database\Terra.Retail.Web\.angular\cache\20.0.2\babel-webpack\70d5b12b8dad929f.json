{"ast": null, "code": "import { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction Login_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Login_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Login_mat_form_field_40_mat_option_4_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 42);\n    i0.ɵɵtext(1, \"\\u0631\\u0626\\u064A\\u0633\\u064A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Login_mat_form_field_40_mat_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39)(1, \"div\", 40)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Login_mat_form_field_40_mat_option_4_small_6_Template, 2, 0, \"small\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const branch_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r1.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(branch_r1.nameAr);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", branch_r1.isMainBranch);\n  }\n}\nfunction Login_mat_form_field_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 16)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0641\\u0631\\u0639\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-select\", 37);\n    i0.ɵɵtemplate(4, Login_mat_form_field_40_mat_option_4_Template, 7, 3, \"mat-option\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-icon\", 18);\n    i0.ɵɵtext(6, \"business\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.branches);\n  }\n}\nfunction Login_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nfunction Login_mat_spinner_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nfunction Login_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Login_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let Login = /*#__PURE__*/(() => {\n  class Login {\n    fb;\n    router;\n    http;\n    snackBar;\n    loginForm;\n    hidePassword = true;\n    isLoading = false;\n    errorMessage = '';\n    branches = [];\n    apiStatus = 'checking';\n    dbStatus = 'checking';\n    constructor(fb, router, http, snackBar) {\n      this.fb = fb;\n      this.router = router;\n      this.http = http;\n      this.snackBar = snackBar;\n      this.loginForm = this.fb.group({\n        username: ['', [Validators.required]],\n        password: ['', [Validators.required]],\n        branchId: [''],\n        rememberMe: [false]\n      });\n    }\n    ngOnInit() {\n      this.checkSystemStatus();\n      this.loadBranches();\n      this.setDemoCredentials();\n    }\n    setDemoCredentials() {\n      this.loginForm.patchValue({\n        username: 'admin',\n        password: 'admin123'\n      });\n    }\n    checkSystemStatus() {\n      this.http.get('http://localhost:5000/health').subscribe({\n        next: response => {\n          this.apiStatus = 'connected';\n          this.dbStatus = response.Database?.Connected ? 'connected' : 'disconnected';\n        },\n        error: () => {\n          this.apiStatus = 'disconnected';\n          this.dbStatus = 'disconnected';\n        }\n      });\n    }\n    loadBranches() {\n      this.http.get('http://localhost:5000/api/simple/branches').subscribe({\n        next: response => {\n          this.branches = response.branches || [];\n          console.log('Loaded branches:', this.branches);\n          // Auto-select main branch if available\n          const mainBranch = this.branches.find(b => b.isMainBranch);\n          if (mainBranch) {\n            this.loginForm.patchValue({\n              branchId: mainBranch.id\n            });\n          } else if (this.branches.length > 0) {\n            // If no main branch, select first branch\n            this.loginForm.patchValue({\n              branchId: this.branches[0].id\n            });\n          }\n        },\n        error: error => {\n          console.error('Error loading branches:', error);\n          // Fallback to demo data\n          this.branches = [{\n            id: 1,\n            nameAr: 'الفرع الرئيسي - القاهرة',\n            nameEn: 'Main Branch - Cairo',\n            code: 'MAIN',\n            isMainBranch: true,\n            isActive: true\n          }, {\n            id: 2,\n            nameAr: 'فرع الإسكندرية',\n            nameEn: 'Alexandria Branch',\n            code: 'ALX',\n            isMainBranch: false,\n            isActive: true\n          }, {\n            id: 3,\n            nameAr: 'فرع الجيزة',\n            nameEn: 'Giza Branch',\n            code: 'GIZ',\n            isMainBranch: false,\n            isActive: true\n          }, {\n            id: 4,\n            nameAr: 'فرع المنصورة',\n            nameEn: 'Mansoura Branch',\n            code: 'MAN',\n            isMainBranch: false,\n            isActive: true\n          }];\n          this.loginForm.patchValue({\n            branchId: 1\n          });\n          this.showMessage('تم تحميل الفروع التجريبية', false);\n        }\n      });\n    }\n    showMessage(message, isError = false) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        horizontalPosition: 'center',\n        verticalPosition: 'top',\n        panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\n      });\n    }\n    onLogin() {\n      if (this.loginForm.valid) {\n        this.isLoading = true;\n        this.errorMessage = '';\n        const loginData = this.loginForm.value;\n        setTimeout(() => {\n          if (loginData.username === 'admin' && loginData.password === 'admin123') {\n            const selectedBranch = this.branches.find(b => b.id === loginData.branchId);\n            localStorage.setItem('currentUser', JSON.stringify({\n              username: loginData.username,\n              branch: selectedBranch,\n              loginTime: new Date().toISOString()\n            }));\n            this.snackBar.open('تم تسجيل الدخول بنجاح!', 'إغلاق', {\n              duration: 3000,\n              horizontalPosition: 'center',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/dashboard']);\n          } else {\n            this.errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';\n            this.snackBar.open('فشل تسجيل الدخول', 'إغلاق', {\n              duration: 3000,\n              horizontalPosition: 'center',\n              verticalPosition: 'top'\n            });\n          }\n          this.isLoading = false;\n        }, 2000);\n      }\n    }\n    loginAsDemo() {\n      this.loginForm.patchValue({\n        username: 'admin',\n        password: 'admin123'\n      });\n      this.onLogin();\n    }\n    static ɵfac = function Login_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Login)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Login,\n      selectors: [[\"app-login\"]],\n      decls: 80,\n      vars: 19,\n      consts: [[1, \"login-container\"], [1, \"background-animation\"], [1, \"floating-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"shape\", \"shape-4\"], [1, \"shape\", \"shape-5\"], [1, \"login-card\"], [1, \"logo-section\"], [1, \"logo-container\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"logo-subtitle\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-header\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"], [\"matPrefix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [1, \"form-options\"], [\"formControlName\", \"rememberMe\", \"color\", \"primary\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"alternative-login\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"demo-button\", 3, \"click\"], [1, \"login-footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"system-status\"], [1, \"status-item\"], [\"formControlName\", \"branchId\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"branch-option\"], [\"class\", \"main-branch-badge\", 4, \"ngIf\"], [1, \"main-branch-badge\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n      template: function Login_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"mat-icon\", 11);\n          i0.ɵɵtext(12, \"store\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"h1\", 12);\n          i0.ɵɵtext(14, \"Terra Retail ERP\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"p\", 13);\n          i0.ɵɵtext(16, \"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u062A\\u0643\\u0627\\u0645\\u0644 \\u0644\\u0644\\u0645\\u062A\\u0627\\u062C\\u0631 \\u0648\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u064A\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function Login_Template_form_ngSubmit_17_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"h2\");\n          i0.ɵɵtext(20, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\\u060C \\u064A\\u0631\\u062C\\u0649 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0644\\u0644\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"mat-form-field\", 16)(24, \"mat-label\");\n          i0.ɵɵtext(25, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 17);\n          i0.ɵɵelementStart(27, \"mat-icon\", 18);\n          i0.ɵɵtext(28, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, Login_mat_error_29_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 16)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"mat-icon\", 18);\n          i0.ɵɵtext(35, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function Login_Template_button_click_36_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(37, \"mat-icon\");\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(39, Login_mat_error_39_Template, 2, 0, \"mat-error\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, Login_mat_form_field_40_Template, 7, 1, \"mat-form-field\", 22);\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"mat-checkbox\", 24);\n          i0.ɵɵtext(43, \" \\u062A\\u0630\\u0643\\u0631\\u0646\\u064A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"a\", 25);\n          i0.ɵɵtext(45, \"\\u0646\\u0633\\u064A\\u062A \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\\u061F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, Login_div_46_Template, 5, 1, \"div\", 26);\n          i0.ɵɵelementStart(47, \"button\", 27);\n          i0.ɵɵtemplate(48, Login_mat_spinner_48_Template, 1, 0, \"mat-spinner\", 28)(49, Login_span_49_Template, 2, 0, \"span\", 19)(50, Login_span_50_Template, 2, 0, \"span\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 29)(52, \"div\", 30)(53, \"span\");\n          i0.ɵɵtext(54, \"\\u0623\\u0648\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function Login_Template_button_click_55_listener() {\n            return ctx.loginAsDemo();\n          });\n          i0.ɵɵelementStart(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(58, \" \\u062F\\u062E\\u0648\\u0644 \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 32)(60, \"p\");\n          i0.ɵɵtext(61, \"\\u00A9 2024 Terra Retail ERP. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 33)(63, \"a\", 34);\n          i0.ɵɵtext(64, \"\\u0627\\u0644\\u062F\\u0639\\u0645 \\u0627\\u0644\\u0641\\u0646\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"a\", 34);\n          i0.ɵɵtext(66, \"\\u0633\\u064A\\u0627\\u0633\\u0629 \\u0627\\u0644\\u062E\\u0635\\u0648\\u0635\\u064A\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"a\", 34);\n          i0.ɵɵtext(68, \"\\u0634\\u0631\\u0648\\u0637 \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"div\", 35)(70, \"div\", 36)(71, \"mat-icon\");\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\");\n          i0.ɵɵtext(74);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 36)(76, \"mat-icon\");\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"span\");\n          i0.ɵɵtext(79);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.branches.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(21);\n          i0.ɵɵclassMap(ctx.apiStatus === \"connected\" ? \"status-online\" : \"status-offline\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.apiStatus === \"connected\" ? \"cloud_done\" : \"cloud_off\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"API: \", ctx.apiStatus === \"connected\" ? \"\\u0645\\u062A\\u0635\\u0644\" : \"\\u063A\\u064A\\u0631 \\u0645\\u062A\\u0635\\u0644\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.dbStatus === \"connected\" ? \"status-online\" : \"status-offline\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.dbStatus === \"connected\" ? \"storage\" : \"error\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\\u0642\\u0627\\u0639\\u062F\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A: \", ctx.dbStatus === \"connected\" ? \"\\u0645\\u062A\\u0635\\u0644\\u0629\" : \"\\u063A\\u064A\\u0631 \\u0645\\u062A\\u0635\\u0644\\u0629\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatPrefix, i6.MatSuffix, MatInputModule, i7.MatInput, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatCheckboxModule, i10.MatCheckbox, MatSelectModule, i11.MatSelect, i11.MatOption, MatProgressSpinnerModule, i12.MatProgressSpinner, MatSnackBarModule],\n      styles: [\"\\n\\n.login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  overflow: hidden;\\n  direction: rtl;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n\\n\\n.background-animation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.floating-shapes[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.shape[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n\\n.shape-1[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  top: 20%;\\n  left: 10%;\\n  animation-delay: 0s;\\n}\\n\\n.shape-2[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  top: 60%;\\n  left: 80%;\\n  animation-delay: 2s;\\n}\\n\\n.shape-3[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  top: 80%;\\n  left: 20%;\\n  animation-delay: 4s;\\n}\\n\\n.shape-4[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  top: 10%;\\n  left: 70%;\\n  animation-delay: 1s;\\n}\\n\\n.shape-5[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: 140px;\\n  top: 40%;\\n  left: 5%;\\n  animation-delay: 3s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n    opacity: 0.7;\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(180deg);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.login-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 20px;\\n  padding: 3rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  max-width: 450px;\\n  position: relative;\\n  z-index: 2;\\n  animation: _ngcontent-%COMP%_slideUp 0.8s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n.logo-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem !important;\\n  color: #667eea;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.logo-text[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin: 0;\\n}\\n\\n.logo-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin: 0;\\n  opacity: 0.8;\\n}\\n\\n\\n\\n.form-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.form-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.95rem;\\n  margin: 0;\\n}\\n\\n\\n\\n.login-form[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-focus-overlay[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n}\\n\\n.branch-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.branch-option[_ngcontent-%COMP%]   .main-branch-badge[_ngcontent-%COMP%] {\\n  background: #e74c3c;\\n  color: white;\\n  padding: 0.2rem 0.5rem;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  margin-right: auto;\\n}\\n\\n\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  transition: color 0.3s ease;\\n}\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  color: #764ba2;\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  background: #ffebee;\\n  color: #c62828;\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  font-size: 0.9rem;\\n}\\n.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-bottom: 1.5rem;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n}\\n.login-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\n\\n\\n\\n.alternative-login[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 1.5rem 0;\\n}\\n.divider[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: #e0e0e0;\\n}\\n.divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 0 1rem;\\n  color: #999;\\n  font-size: 0.9rem;\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 44px;\\n  border-radius: 12px;\\n  border: 2px solid #667eea;\\n  color: #667eea;\\n  font-weight: 500;\\n}\\n.demo-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n}\\n\\n\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 2rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 0.8rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 0.8rem;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.system-status[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 20px;\\n  right: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  z-index: 3;\\n}\\n\\n.status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.status-item[_ngcontent-%COMP%]   .status-online[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n}\\n.status-item[_ngcontent-%COMP%]   .status-offline[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    margin: 1rem;\\n    padding: 2rem;\\n    max-width: none;\\n  }\\n  .logo-text[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .system-status[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return Login;\n})();", "map": {"version": 3, "names": ["Validators", "ReactiveFormsModule", "CommonModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatCheckboxModule", "MatSelectModule", "MatProgressSpinnerModule", "MatSnackBarModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "Login_mat_form_field_40_mat_option_4_small_6_Template", "ɵɵproperty", "branch_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate", "nameAr", "isMainBranch", "Login_mat_form_field_40_mat_option_4_Template", "ctx_r1", "branches", "errorMessage", "ɵɵelement", "<PERSON><PERSON>", "fb", "router", "http", "snackBar", "loginForm", "hidePassword", "isLoading", "api<PERSON><PERSON>us", "db<PERSON><PERSON>us", "constructor", "group", "username", "required", "password", "branchId", "rememberMe", "ngOnInit", "checkSystemStatus", "loadBranches", "setDemoCredentials", "patchValue", "get", "subscribe", "next", "response", "Database", "Connected", "error", "console", "log", "mainBranch", "find", "b", "length", "nameEn", "code", "isActive", "showMessage", "message", "isError", "open", "duration", "horizontalPosition", "verticalPosition", "panelClass", "onLogin", "valid", "loginData", "value", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "setItem", "JSON", "stringify", "branch", "loginTime", "Date", "toISOString", "navigate", "loginAsDemo", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "HttpClient", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "Login_Template", "rf", "ctx", "ɵɵlistener", "Login_Template_form_ngSubmit_17_listener", "Login_mat_error_29_Template", "Login_Template_button_click_36_listener", "Login_mat_error_39_Template", "Login_mat_form_field_40_Template", "Login_div_46_Template", "Login_mat_spinner_48_Template", "Login_span_49_Template", "Login_span_50_Template", "Login_Template_button_click_55_listener", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_4_0", "invalid", "ɵɵclassMap", "ɵɵtextInterpolate1", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatPrefix", "MatSuffix", "i7", "MatInput", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatCheckbox", "i11", "MatSelect", "MatOption", "i12", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\auth\\login\\login.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\auth\\login\\login.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\r\n\r\ninterface Branch {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n  code: string;\r\n  isMainBranch: boolean;\r\n  isActive: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatCardModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatCheckboxModule,\r\n    MatSelectModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule\r\n  ],\r\n  templateUrl: './login.html',\r\n  styleUrls: ['./login.scss']\r\n})\r\nexport class Login implements OnInit {\r\n  loginForm: FormGroup;\r\n  hidePassword = true;\r\n  isLoading = false;\r\n  errorMessage = '';\r\n  branches: Branch[] = [];\r\n  apiStatus = 'checking';\r\n  dbStatus = 'checking';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      username: ['', [Validators.required]],\r\n      password: ['', [Validators.required]],\r\n      branchId: [''],\r\n      rememberMe: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.checkSystemStatus();\r\n    this.loadBranches();\r\n    this.setDemoCredentials();\r\n  }\r\n\r\n  setDemoCredentials() {\r\n    this.loginForm.patchValue({\r\n      username: 'admin',\r\n      password: 'admin123'\r\n    });\r\n  }\r\n\r\n  checkSystemStatus() {\r\n    this.http.get('http://localhost:5000/health').subscribe({\r\n      next: (response: any) => {\r\n        this.apiStatus = 'connected';\r\n        this.dbStatus = response.Database?.Connected ? 'connected' : 'disconnected';\r\n      },\r\n      error: () => {\r\n        this.apiStatus = 'disconnected';\r\n        this.dbStatus = 'disconnected';\r\n      }\r\n    });\r\n  }\r\n\r\n  loadBranches() {\r\n    this.http.get<any>('http://localhost:5000/api/simple/branches').subscribe({\r\n      next: (response) => {\r\n        this.branches = response.branches || [];\r\n        console.log('Loaded branches:', this.branches);\r\n\r\n        // Auto-select main branch if available\r\n        const mainBranch = this.branches.find(b => b.isMainBranch);\r\n        if (mainBranch) {\r\n          this.loginForm.patchValue({ branchId: mainBranch.id });\r\n        } else if (this.branches.length > 0) {\r\n          // If no main branch, select first branch\r\n          this.loginForm.patchValue({ branchId: this.branches[0].id });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading branches:', error);\r\n        // Fallback to demo data\r\n        this.branches = [\r\n          { id: 1, nameAr: 'الفرع الرئيسي - القاهرة', nameEn: 'Main Branch - Cairo', code: 'MAIN', isMainBranch: true, isActive: true },\r\n          { id: 2, nameAr: 'فرع الإسكندرية', nameEn: 'Alexandria Branch', code: 'ALX', isMainBranch: false, isActive: true },\r\n          { id: 3, nameAr: 'فرع الجيزة', nameEn: 'Giza Branch', code: 'GIZ', isMainBranch: false, isActive: true },\r\n          { id: 4, nameAr: 'فرع المنصورة', nameEn: 'Mansoura Branch', code: 'MAN', isMainBranch: false, isActive: true }\r\n        ];\r\n        this.loginForm.patchValue({ branchId: 1 });\r\n        this.showMessage('تم تحميل الفروع التجريبية', false);\r\n      }\r\n    });\r\n  }\r\n\r\n  private showMessage(message: string, isError = false) {\r\n    this.snackBar.open(message, 'إغلاق', {\r\n      duration: 3000,\r\n      horizontalPosition: 'center',\r\n      verticalPosition: 'top',\r\n      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\r\n    });\r\n  }\r\n\r\n  onLogin() {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading = true;\r\n      this.errorMessage = '';\r\n\r\n      const loginData = this.loginForm.value;\r\n\r\n      setTimeout(() => {\r\n        if (loginData.username === 'admin' && loginData.password === 'admin123') {\r\n          const selectedBranch = this.branches.find(b => b.id === loginData.branchId);\r\n\r\n          localStorage.setItem('currentUser', JSON.stringify({\r\n            username: loginData.username,\r\n            branch: selectedBranch,\r\n            loginTime: new Date().toISOString()\r\n          }));\r\n\r\n          this.snackBar.open('تم تسجيل الدخول بنجاح!', 'إغلاق', {\r\n            duration: 3000,\r\n            horizontalPosition: 'center',\r\n            verticalPosition: 'top'\r\n          });\r\n\r\n          this.router.navigate(['/dashboard']);\r\n        } else {\r\n          this.errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';\r\n          this.snackBar.open('فشل تسجيل الدخول', 'إغلاق', {\r\n            duration: 3000,\r\n            horizontalPosition: 'center',\r\n            verticalPosition: 'top'\r\n          });\r\n        }\r\n        this.isLoading = false;\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  loginAsDemo() {\r\n    this.loginForm.patchValue({\r\n      username: 'admin',\r\n      password: 'admin123'\r\n    });\r\n    this.onLogin();\r\n  }\r\n}\r\n", "<div class=\"login-container\">\r\n  <!-- Background Animation -->\r\n  <div class=\"background-animation\">\r\n    <div class=\"floating-shapes\">\r\n      <div class=\"shape shape-1\"></div>\r\n      <div class=\"shape shape-2\"></div>\r\n      <div class=\"shape shape-3\"></div>\r\n      <div class=\"shape shape-4\"></div>\r\n      <div class=\"shape shape-5\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Login Card -->\r\n  <div class=\"login-card\">\r\n    <!-- Logo Section -->\r\n    <div class=\"logo-section\">\r\n      <div class=\"logo-container\">\r\n        <mat-icon class=\"logo-icon\">store</mat-icon>\r\n        <h1 class=\"logo-text\">Terra Retail ERP</h1>\r\n      </div>\r\n      <p class=\"logo-subtitle\">نظام إدارة متكامل للمتاجر والشركات التجارية المصرية</p>\r\n    </div>\r\n\r\n    <!-- Login Form -->\r\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onLogin()\" class=\"login-form\">\r\n      <div class=\"form-header\">\r\n        <h2>تسجيل الدخول</h2>\r\n        <p>مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>\r\n      </div>\r\n\r\n      <!-- Username Field -->\r\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>اسم المستخدم</mat-label>\r\n        <input matInput formControlName=\"username\" placeholder=\"أدخل اسم المستخدم\">\r\n        <mat-icon matPrefix>person</mat-icon>\r\n        <mat-error *ngIf=\"loginForm.get('username')?.hasError('required')\">\r\n          اسم المستخدم مطلوب\r\n        </mat-error>\r\n      </mat-form-field>\r\n\r\n      <!-- Password Field -->\r\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n        <mat-label>كلمة المرور</mat-label>\r\n        <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" placeholder=\"أدخل كلمة المرور\">\r\n        <mat-icon matPrefix>lock</mat-icon>\r\n        <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\r\n          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n        </button>\r\n        <mat-error *ngIf=\"loginForm.get('password')?.hasError('required')\">\r\n          كلمة المرور مطلوبة\r\n        </mat-error>\r\n      </mat-form-field>\r\n\r\n      <!-- Branch Selection -->\r\n      <mat-form-field appearance=\"outline\" class=\"full-width\" *ngIf=\"branches.length > 0\">\r\n        <mat-label>اختر الفرع</mat-label>\r\n        <mat-select formControlName=\"branchId\">\r\n          <mat-option *ngFor=\"let branch of branches\" [value]=\"branch.id\">\r\n            <div class=\"branch-option\">\r\n              <mat-icon>business</mat-icon>\r\n              <span>{{ branch.nameAr }}</span>\r\n              <small *ngIf=\"branch.isMainBranch\" class=\"main-branch-badge\">رئيسي</small>\r\n            </div>\r\n          </mat-option>\r\n        </mat-select>\r\n        <mat-icon matPrefix>business</mat-icon>\r\n      </mat-form-field>\r\n\r\n      <!-- Remember Me -->\r\n      <div class=\"form-options\">\r\n        <mat-checkbox formControlName=\"rememberMe\" color=\"primary\">\r\n          تذكرني\r\n        </mat-checkbox>\r\n        <a href=\"#\" class=\"forgot-password\">نسيت كلمة المرور؟</a>\r\n      </div>\r\n\r\n      <!-- Error Message -->\r\n      <div class=\"error-message\" *ngIf=\"errorMessage\">\r\n        <mat-icon>error</mat-icon>\r\n        <span>{{ errorMessage }}</span>\r\n      </div>\r\n\r\n      <!-- Login Button -->\r\n      <button mat-raised-button color=\"primary\" type=\"submit\" class=\"login-button\" [disabled]=\"loginForm.invalid || isLoading\">\r\n        <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\r\n        <span *ngIf=\"!isLoading\">تسجيل الدخول</span>\r\n        <span *ngIf=\"isLoading\">جاري تسجيل الدخول...</span>\r\n      </button>\r\n\r\n      <!-- Alternative Login -->\r\n      <div class=\"alternative-login\">\r\n        <div class=\"divider\">\r\n          <span>أو</span>\r\n        </div>\r\n        <button mat-stroked-button type=\"button\" class=\"demo-button\" (click)=\"loginAsDemo()\">\r\n          <mat-icon>visibility</mat-icon>\r\n          دخول تجريبي\r\n        </button>\r\n      </div>\r\n    </form>\r\n\r\n    <!-- Footer -->\r\n    <div class=\"login-footer\">\r\n      <p>&copy; 2024 Terra Retail ERP. جميع الحقوق محفوظة</p>\r\n      <div class=\"footer-links\">\r\n        <a href=\"#\">الدعم الفني</a>\r\n        <a href=\"#\">سياسة الخصوصية</a>\r\n        <a href=\"#\">شروط الاستخدام</a>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- System Status -->\r\n  <div class=\"system-status\">\r\n    <div class=\"status-item\">\r\n      <mat-icon [class]=\"apiStatus === 'connected' ? 'status-online' : 'status-offline'\">\r\n        {{ apiStatus === 'connected' ? 'cloud_done' : 'cloud_off' }}\r\n      </mat-icon>\r\n      <span>API: {{ apiStatus === 'connected' ? 'متصل' : 'غير متصل' }}</span>\r\n    </div>\r\n    <div class=\"status-item\">\r\n      <mat-icon [class]=\"dbStatus === 'connected' ? 'status-online' : 'status-offline'\">\r\n        {{ dbStatus === 'connected' ? 'storage' : 'error' }}\r\n      </mat-icon>\r\n      <span>قاعدة البيانات: {{ dbStatus === 'connected' ? 'متصلة' : 'غير متصلة' }}</span>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AAGxF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;;;;;;;;;;;;;;;;ICsBpEC,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAAmE;IACjED,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWNH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF1EH,EAFJ,CAAAC,cAAA,qBAAgE,cACnC,eACf;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAI,UAAA,IAAAC,qDAAA,oBAA6D;IAEjEL,EADE,CAAAG,YAAA,EAAM,EACK;;;;IAN+BH,EAAA,CAAAM,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAmB;IAGrDR,EAAA,CAAAS,SAAA,GAAmB;IAAnBT,EAAA,CAAAU,iBAAA,CAAAH,SAAA,CAAAI,MAAA,CAAmB;IACjBX,EAAA,CAAAS,SAAA,EAAyB;IAAzBT,EAAA,CAAAM,UAAA,SAAAC,SAAA,CAAAK,YAAA,CAAyB;;;;;IANvCZ,EADF,CAAAC,cAAA,yBAAoF,gBACvE;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAC,cAAA,qBAAuC;IACrCD,EAAA,CAAAI,UAAA,IAAAS,6CAAA,yBAAgE;IAOlEb,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;;;;IATkBH,EAAA,CAAAS,SAAA,GAAW;IAAXT,EAAA,CAAAM,UAAA,YAAAQ,MAAA,CAAAC,QAAA,CAAW;;;;;IAqB5Cf,EADF,CAAAC,cAAA,cAAgD,eACpC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;;;;IADEH,EAAA,CAAAS,SAAA,GAAkB;IAAlBT,EAAA,CAAAU,iBAAA,CAAAI,MAAA,CAAAE,YAAA,CAAkB;;;;;IAKxBhB,EAAA,CAAAiB,SAAA,sBAA2D;;;;;IAC3DjB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,0EAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,sGAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD3C3D,WAAae,KAAK;EAAZ,MAAOA,KAAK;IAUNC,EAAA;IACAC,MAAA;IACAC,IAAA;IACAC,QAAA;IAZVC,SAAS;IACTC,YAAY,GAAG,IAAI;IACnBC,SAAS,GAAG,KAAK;IACjBT,YAAY,GAAG,EAAE;IACjBD,QAAQ,GAAa,EAAE;IACvBW,SAAS,GAAG,UAAU;IACtBC,QAAQ,GAAG,UAAU;IAErBC,YACUT,EAAe,EACfC,MAAc,EACdC,IAAgB,EAChBC,QAAqB;MAHrB,KAAAH,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,QAAQ,GAARA,QAAQ;MAEhB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACJ,EAAE,CAACU,KAAK,CAAC;QAC7BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAAC2C,QAAQ,CAAC,CAAC;QACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC2C,QAAQ,CAAC,CAAC;QACrCE,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC,KAAK;OACnB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEAA,kBAAkBA,CAAA;MAChB,IAAI,CAACf,SAAS,CAACgB,UAAU,CAAC;QACxBT,QAAQ,EAAE,OAAO;QACjBE,QAAQ,EAAE;OACX,CAAC;IACJ;IAEAI,iBAAiBA,CAAA;MACf,IAAI,CAACf,IAAI,CAACmB,GAAG,CAAC,8BAA8B,CAAC,CAACC,SAAS,CAAC;QACtDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACjB,SAAS,GAAG,WAAW;UAC5B,IAAI,CAACC,QAAQ,GAAGgB,QAAQ,CAACC,QAAQ,EAAEC,SAAS,GAAG,WAAW,GAAG,cAAc;QAC7E,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACpB,SAAS,GAAG,cAAc;UAC/B,IAAI,CAACC,QAAQ,GAAG,cAAc;QAChC;OACD,CAAC;IACJ;IAEAU,YAAYA,CAAA;MACV,IAAI,CAAChB,IAAI,CAACmB,GAAG,CAAM,2CAA2C,CAAC,CAACC,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC5B,QAAQ,GAAG4B,QAAQ,CAAC5B,QAAQ,IAAI,EAAE;UACvCgC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACjC,QAAQ,CAAC;UAE9C;UACA,MAAMkC,UAAU,GAAG,IAAI,CAAClC,QAAQ,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,YAAY,CAAC;UAC1D,IAAIqC,UAAU,EAAE;YACd,IAAI,CAAC1B,SAAS,CAACgB,UAAU,CAAC;cAAEN,QAAQ,EAAEgB,UAAU,CAACzC;YAAE,CAAE,CAAC;UACxD,CAAC,MAAM,IAAI,IAAI,CAACO,QAAQ,CAACqC,MAAM,GAAG,CAAC,EAAE;YACnC;YACA,IAAI,CAAC7B,SAAS,CAACgB,UAAU,CAAC;cAAEN,QAAQ,EAAE,IAAI,CAAClB,QAAQ,CAAC,CAAC,CAAC,CAACP;YAAE,CAAE,CAAC;UAC9D;QACF,CAAC;QACDsC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C;UACA,IAAI,CAAC/B,QAAQ,GAAG,CACd;YAAEP,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,yBAAyB;YAAE0C,MAAM,EAAE,qBAAqB;YAAEC,IAAI,EAAE,MAAM;YAAE1C,YAAY,EAAE,IAAI;YAAE2C,QAAQ,EAAE;UAAI,CAAE,EAC7H;YAAE/C,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,gBAAgB;YAAE0C,MAAM,EAAE,mBAAmB;YAAEC,IAAI,EAAE,KAAK;YAAE1C,YAAY,EAAE,KAAK;YAAE2C,QAAQ,EAAE;UAAI,CAAE,EAClH;YAAE/C,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,YAAY;YAAE0C,MAAM,EAAE,aAAa;YAAEC,IAAI,EAAE,KAAK;YAAE1C,YAAY,EAAE,KAAK;YAAE2C,QAAQ,EAAE;UAAI,CAAE,EACxG;YAAE/C,EAAE,EAAE,CAAC;YAAEG,MAAM,EAAE,cAAc;YAAE0C,MAAM,EAAE,iBAAiB;YAAEC,IAAI,EAAE,KAAK;YAAE1C,YAAY,EAAE,KAAK;YAAE2C,QAAQ,EAAE;UAAI,CAAE,CAC/G;UACD,IAAI,CAAChC,SAAS,CAACgB,UAAU,CAAC;YAAEN,QAAQ,EAAE;UAAC,CAAE,CAAC;UAC1C,IAAI,CAACuB,WAAW,CAAC,2BAA2B,EAAE,KAAK,CAAC;QACtD;OACD,CAAC;IACJ;IAEQA,WAAWA,CAACC,OAAe,EAAEC,OAAO,GAAG,KAAK;MAClD,IAAI,CAACpC,QAAQ,CAACqC,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;QACnCG,QAAQ,EAAE,IAAI;QACdC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE,KAAK;QACvBC,UAAU,EAAEL,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB;OAC/D,CAAC;IACJ;IAEAM,OAAOA,CAAA;MACL,IAAI,IAAI,CAACzC,SAAS,CAAC0C,KAAK,EAAE;QACxB,IAAI,CAACxC,SAAS,GAAG,IAAI;QACrB,IAAI,CAACT,YAAY,GAAG,EAAE;QAEtB,MAAMkD,SAAS,GAAG,IAAI,CAAC3C,SAAS,CAAC4C,KAAK;QAEtCC,UAAU,CAAC,MAAK;UACd,IAAIF,SAAS,CAACpC,QAAQ,KAAK,OAAO,IAAIoC,SAAS,CAAClC,QAAQ,KAAK,UAAU,EAAE;YACvE,MAAMqC,cAAc,GAAG,IAAI,CAACtD,QAAQ,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3C,EAAE,KAAK0D,SAAS,CAACjC,QAAQ,CAAC;YAE3EqC,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC;cACjD3C,QAAQ,EAAEoC,SAAS,CAACpC,QAAQ;cAC5B4C,MAAM,EAAEL,cAAc;cACtBM,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;aAClC,CAAC,CAAC;YAEH,IAAI,CAACvD,QAAQ,CAACqC,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;cACpDC,QAAQ,EAAE,IAAI;cACdC,kBAAkB,EAAE,QAAQ;cAC5BC,gBAAgB,EAAE;aACnB,CAAC;YAEF,IAAI,CAAC1C,MAAM,CAAC0D,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;UACtC,CAAC,MAAM;YACL,IAAI,CAAC9D,YAAY,GAAG,uCAAuC;YAC3D,IAAI,CAACM,QAAQ,CAACqC,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE;cAC9CC,QAAQ,EAAE,IAAI;cACdC,kBAAkB,EAAE,QAAQ;cAC5BC,gBAAgB,EAAE;aACnB,CAAC;UACJ;UACA,IAAI,CAACrC,SAAS,GAAG,KAAK;QACxB,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IAEAsD,WAAWA,CAAA;MACT,IAAI,CAACxD,SAAS,CAACgB,UAAU,CAAC;QACxBT,QAAQ,EAAE,OAAO;QACjBE,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAACgC,OAAO,EAAE;IAChB;;uCAnIW9C,KAAK,EAAAlB,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApF,EAAA,CAAAgF,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAtF,EAAA,CAAAgF,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;;YAALtE,KAAK;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,eAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCd/F,EAHJ,CAAAC,cAAA,aAA6B,aAEO,aACH;UAK3BD,EAJA,CAAAiB,SAAA,aAAiC,aACA,aACA,aACA,aACA;UAErCjB,EADE,CAAAG,YAAA,EAAM,EACF;UAOAH,EAJN,CAAAC,cAAA,aAAwB,aAEI,eACI,oBACE;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5CH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACxCF,EADwC,CAAAG,YAAA,EAAK,EACvC;UACNH,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,MAAA,4RAAmD;UAC9EF,EAD8E,CAAAG,YAAA,EAAI,EAC5E;UAGNH,EAAA,CAAAC,cAAA,gBAAwE;UAA1CD,EAAA,CAAAiG,UAAA,sBAAAC,yCAAA;YAAA,OAAYF,GAAA,CAAAhC,OAAA,EAAS;UAAA,EAAC;UAEhDhE,EADF,CAAAC,cAAA,eAAyB,UACnB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6MAAqC;UAC1CF,EAD0C,CAAAG,YAAA,EAAI,EACxC;UAIJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAiB,SAAA,iBAA2E;UAC3EjB,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAAI,UAAA,KAAA+F,2BAAA,wBAAmE;UAGrEnG,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAiB,SAAA,iBAAsH;UACtHjB,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnCH,EAAA,CAAAC,cAAA,kBAAuF;UAArDD,EAAA,CAAAiG,UAAA,mBAAAG,wCAAA;YAAA,OAAAJ,GAAA,CAAAxE,YAAA,IAAAwE,GAAA,CAAAxE,YAAA;UAAA,EAAsC;UACtExB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAkD;UAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;UACTH,EAAA,CAAAI,UAAA,KAAAiG,2BAAA,wBAAmE;UAGrErG,EAAA,CAAAG,YAAA,EAAiB;UAGjBH,EAAA,CAAAI,UAAA,KAAAkG,gCAAA,6BAAoF;UAgBlFtG,EADF,CAAAC,cAAA,eAA0B,wBACmC;UACzDD,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,oGAAiB;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;UAGNH,EAAA,CAAAI,UAAA,KAAAmG,qBAAA,kBAAgD;UAMhDvG,EAAA,CAAAC,cAAA,kBAAyH;UAGvHD,EAFA,CAAAI,UAAA,KAAAoG,6BAAA,0BAA6C,KAAAC,sBAAA,mBACpB,KAAAC,sBAAA,mBACD;UAC1B1G,EAAA,CAAAG,YAAA,EAAS;UAKLH,EAFJ,CAAAC,cAAA,eAA+B,eACR,YACb;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UACVF,EADU,CAAAG,YAAA,EAAO,EACX;UACNH,EAAA,CAAAC,cAAA,kBAAqF;UAAxBD,EAAA,CAAAiG,UAAA,mBAAAU,wCAAA;YAAA,OAASX,GAAA,CAAAjB,WAAA,EAAa;UAAA,EAAC;UAClF/E,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,uEACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACD;UAILH,EADF,CAAAC,cAAA,eAA0B,SACrB;UAAAD,EAAA,CAAAE,MAAA,wIAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAErDH,EADF,CAAAC,cAAA,eAA0B,aACZ;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3BH,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9BH,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAGhCF,EAHgC,CAAAG,YAAA,EAAI,EAC1B,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACA,gBAC4D;UACjFD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA0D;UAClEF,EADkE,CAAAG,YAAA,EAAO,EACnE;UAEJH,EADF,CAAAC,cAAA,eAAyB,gBAC2D;UAChFD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAsE;UAGlFF,EAHkF,CAAAG,YAAA,EAAO,EAC/E,EACF,EACF;;;;;UAvGIH,EAAA,CAAAS,SAAA,IAAuB;UAAvBT,EAAA,CAAAM,UAAA,cAAA0F,GAAA,CAAAzE,SAAA,CAAuB;UAWbvB,EAAA,CAAAS,SAAA,IAAqD;UAArDT,EAAA,CAAAM,UAAA,UAAAsG,OAAA,GAAAZ,GAAA,CAAAzE,SAAA,CAAAiB,GAAA,+BAAAoE,OAAA,CAAAC,QAAA,aAAqD;UAQjD7G,EAAA,CAAAS,SAAA,GAA2C;UAA3CT,EAAA,CAAAM,UAAA,SAAA0F,GAAA,CAAAxE,YAAA,uBAA2C;UAG/CxB,EAAA,CAAAS,SAAA,GAAkD;UAAlDT,EAAA,CAAAU,iBAAA,CAAAsF,GAAA,CAAAxE,YAAA,mCAAkD;UAElDxB,EAAA,CAAAS,SAAA,EAAqD;UAArDT,EAAA,CAAAM,UAAA,UAAAwG,OAAA,GAAAd,GAAA,CAAAzE,SAAA,CAAAiB,GAAA,+BAAAsE,OAAA,CAAAD,QAAA,aAAqD;UAMV7G,EAAA,CAAAS,SAAA,EAAyB;UAAzBT,EAAA,CAAAM,UAAA,SAAA0F,GAAA,CAAAjF,QAAA,CAAAqC,MAAA,KAAyB;UAuBtDpD,EAAA,CAAAS,SAAA,GAAkB;UAAlBT,EAAA,CAAAM,UAAA,SAAA0F,GAAA,CAAAhF,YAAA,CAAkB;UAM+BhB,EAAA,CAAAS,SAAA,EAA2C;UAA3CT,EAAA,CAAAM,UAAA,aAAA0F,GAAA,CAAAzE,SAAA,CAAAwF,OAAA,IAAAf,GAAA,CAAAvE,SAAA,CAA2C;UAC1FzB,EAAA,CAAAS,SAAA,EAAe;UAAfT,EAAA,CAAAM,UAAA,SAAA0F,GAAA,CAAAvE,SAAA,CAAe;UACpCzB,EAAA,CAAAS,SAAA,EAAgB;UAAhBT,EAAA,CAAAM,UAAA,UAAA0F,GAAA,CAAAvE,SAAA,CAAgB;UAChBzB,EAAA,CAAAS,SAAA,EAAe;UAAfT,EAAA,CAAAM,UAAA,SAAA0F,GAAA,CAAAvE,SAAA,CAAe;UA6BdzB,EAAA,CAAAS,SAAA,IAAwE;UAAxET,EAAA,CAAAgH,UAAA,CAAAhB,GAAA,CAAAtE,SAAA,sDAAwE;UAChF1B,EAAA,CAAAS,SAAA,EACF;UADET,EAAA,CAAAiH,kBAAA,MAAAjB,GAAA,CAAAtE,SAAA,mDACF;UACM1B,EAAA,CAAAS,SAAA,GAA0D;UAA1DT,EAAA,CAAAiH,kBAAA,UAAAjB,GAAA,CAAAtE,SAAA,8FAA0D;UAGtD1B,EAAA,CAAAS,SAAA,GAAuE;UAAvET,EAAA,CAAAgH,UAAA,CAAAhB,GAAA,CAAArE,QAAA,sDAAuE;UAC/E3B,EAAA,CAAAS,SAAA,EACF;UADET,EAAA,CAAAiH,kBAAA,MAAAjB,GAAA,CAAArE,QAAA,4CACF;UACM3B,EAAA,CAAAS,SAAA,GAAsE;UAAtET,EAAA,CAAAiH,kBAAA,sFAAAjB,GAAA,CAAArE,QAAA,0GAAsE;;;qBDhG9ErC,YAAY,EAAA4H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,mBAAmB,EAAA4F,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,kBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EACnBnI,aAAa,EACbC,kBAAkB,EAAAmI,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAAAJ,EAAA,CAAAK,SAAA,EAClBvI,cAAc,EAAAwI,EAAA,CAAAC,QAAA,EACdxI,eAAe,EAAAyI,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf1I,aAAa,EAAA2I,EAAA,CAAAC,OAAA,EACb3I,iBAAiB,EAAA4I,GAAA,CAAAC,WAAA,EACjB5I,eAAe,EAAA6I,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACf9I,wBAAwB,EAAA+I,GAAA,CAAAC,kBAAA,EACxB/I,iBAAiB;MAAAgJ,MAAA;IAAA;;SAKR7H,KAAK;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}