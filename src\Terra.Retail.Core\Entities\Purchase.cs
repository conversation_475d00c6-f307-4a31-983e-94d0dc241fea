using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// فواتير المشتريات
    /// </summary>
    public class Purchase : BaseEntity
    {
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم (المشتري)
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// رقم فاتورة المورد
        /// </summary>
        [MaxLength(50)]
        public string? SupplierInvoiceNumber { get; set; }

        /// <summary>
        /// تاريخ فاتورة المورد
        /// </summary>
        public DateTime? SupplierInvoiceDate { get; set; }

        /// <summary>
        /// إجمالي الفاتورة قبل الخصم
        /// </summary>
        public decimal SubTotal { get; set; } = 0;

        /// <summary>
        /// نسبة الخصم العام (%)
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الخصم العام
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// نسبة الضريبة (%)
        /// </summary>
        public decimal TaxPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; } = 0;

        /// <summary>
        /// مصاريف إضافية (شحن، تأمين، إلخ)
        /// </summary>
        public decimal AdditionalCharges { get; set; } = 0;

        /// <summary>
        /// الإجمالي النهائي
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; } = 0;

        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        public PurchaseStatus Status { get; set; } = PurchaseStatus.Draft;

        /// <summary>
        /// نوع الفاتورة
        /// </summary>
        public PurchaseType PurchaseType { get; set; } = PurchaseType.Cash;

        /// <summary>
        /// تاريخ الاستحقاق (للفواتير الآجلة)
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// ملاحظات الفاتورة
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية
        /// </summary>
        [MaxLength(1000)]
        public string? InternalNotes { get; set; }

        /// <summary>
        /// رقم مرجعي خارجي
        /// </summary>
        [MaxLength(50)]
        public string? ExternalReference { get; set; }

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// سعر الصرف
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// الإجمالي بالعملة الأساسية
        /// </summary>
        public decimal BaseCurrencyTotal { get; set; } = 0;

        /// <summary>
        /// هل الفاتورة مؤكدة
        /// </summary>
        public bool IsConfirmed { get; set; } = false;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد الفاتورة
        /// </summary>
        public int? ConfirmedById { get; set; }

        /// <summary>
        /// هل تم استلام البضاعة
        /// </summary>
        public bool IsReceived { get; set; } = false;

        /// <summary>
        /// تاريخ الاستلام
        /// </summary>
        public DateTime? ReceivedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي استلم البضاعة
        /// </summary>
        public int? ReceivedById { get; set; }

        // Navigation Properties
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        // public virtual User User { get; set; } = null!;
        // public virtual User? ConfirmedBy { get; set; }
        // public virtual User? ReceivedBy { get; set; }
        public virtual ICollection<PurchaseItem> Items { get; set; } = new List<PurchaseItem>();
        public virtual ICollection<PurchasePayment> Payments { get; set; } = new List<PurchasePayment>();
        public virtual ICollection<PurchaseReturn> Returns { get; set; } = new List<PurchaseReturn>();
    }

    /// <summary>
    /// حالة فاتورة المشتريات
    /// </summary>
    public enum PurchaseStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,

        /// <summary>
        /// مؤكدة
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// مستلمة جزئياً
        /// </summary>
        PartiallyReceived = 3,

        /// <summary>
        /// مستلمة بالكامل
        /// </summary>
        FullyReceived = 4,

        /// <summary>
        /// مدفوعة جزئياً
        /// </summary>
        PartiallyPaid = 5,

        /// <summary>
        /// مدفوعة بالكامل
        /// </summary>
        FullyPaid = 6,

        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 7,

        /// <summary>
        /// مرتجعة جزئياً
        /// </summary>
        PartiallyReturned = 8,

        /// <summary>
        /// مرتجعة بالكامل
        /// </summary>
        FullyReturned = 9
    }

    /// <summary>
    /// نوع فاتورة المشتريات
    /// </summary>
    public enum PurchaseType
    {
        /// <summary>
        /// نقدي
        /// </summary>
        Cash = 1,

        /// <summary>
        /// آجل
        /// </summary>
        Credit = 2,

        /// <summary>
        /// مختلط
        /// </summary>
        Mixed = 3,

        /// <summary>
        /// أمر شراء
        /// </summary>
        PurchaseOrder = 4
    }
}
