{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nlet MutationObserverFactory = /*#__PURE__*/(() => {\n  class MutationObserverFactory {\n    create(callback) {\n      return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MutationObserverFactory)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MutationObserverFactory;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nlet ContentObserver = /*#__PURE__*/(() => {\n  class ContentObserver {\n    _mutationObserverFactory = inject(MutationObserverFactory);\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _observedElements = new Map();\n    _ngZone = inject(NgZone);\n    constructor() {}\n    ngOnDestroy() {\n      this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n      const element = coerceElement(elementOrRef);\n      return new Observable(observer => {\n        const stream = this._observeElement(element);\n        const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n          this._ngZone.run(() => {\n            observer.next(records);\n          });\n        });\n        return () => {\n          subscription.unsubscribe();\n          this._unobserveElement(element);\n        };\n      });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n      return this._ngZone.runOutsideAngular(() => {\n        if (!this._observedElements.has(element)) {\n          const stream = new Subject();\n          const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n          if (observer) {\n            observer.observe(element, {\n              characterData: true,\n              childList: true,\n              subtree: true\n            });\n          }\n          this._observedElements.set(element, {\n            observer,\n            stream,\n            count: 1\n          });\n        } else {\n          this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n      });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n      if (this._observedElements.has(element)) {\n        this._observedElements.get(element).count--;\n        if (!this._observedElements.get(element).count) {\n          this._cleanupObserver(element);\n        }\n      }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n      if (this._observedElements.has(element)) {\n        const {\n          observer,\n          stream\n        } = this._observedElements.get(element);\n        if (observer) {\n          observer.disconnect();\n        }\n        stream.complete();\n        this._observedElements.delete(element);\n      }\n    }\n    static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentObserver)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ContentObserver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nlet CdkObserveContent = /*#__PURE__*/(() => {\n  class CdkObserveContent {\n    _contentObserver = inject(ContentObserver);\n    _elementRef = inject(ElementRef);\n    /** Event emitted for each change in the element's content. */\n    event = new EventEmitter();\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    _disabled = false;\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n      return this._debounce;\n    }\n    set debounce(value) {\n      this._debounce = coerceNumberProperty(value);\n      this._subscribe();\n    }\n    _debounce;\n    _currentSubscription = null;\n    constructor() {}\n    ngAfterContentInit() {\n      if (!this._currentSubscription && !this.disabled) {\n        this._subscribe();\n      }\n    }\n    ngOnDestroy() {\n      this._unsubscribe();\n    }\n    _subscribe() {\n      this._unsubscribe();\n      const stream = this._contentObserver.observe(this._elementRef);\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n      this._currentSubscription?.unsubscribe();\n    }\n    static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkObserveContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"]\n    });\n  }\n  return CdkObserveContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ObserversModule = /*#__PURE__*/(() => {\n  class ObserversModule {\n    static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ObserversModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n  return ObserversModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "NgZone", "ElementRef", "EventEmitter", "booleanAttribute", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "map", "filter", "debounceTime", "c", "coerceNumberProperty", "a", "coerceElement", "shouldIgnoreRecord", "record", "type", "target", "Comment", "i", "addedNodes", "length", "removedNodes", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "MutationObserverFactory_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ContentObserver", "_mutationObserverFactory", "_observedElements", "Map", "_ngZone", "constructor", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "pipe", "records", "subscribe", "run", "next", "unsubscribe", "_unobserveElement", "runOutsideAngular", "has", "mutations", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "ContentObserver_Factory", "CdkObserveContent", "_contentObserver", "_elementRef", "event", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_currentSubscription", "ngAfterContentInit", "CdkObserveContent_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "ObserversModule", "ObserversModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n    // Ignore changes to comment text.\n    if (record.type === 'characterData' && record.target instanceof Comment) {\n        return true;\n    }\n    // Ignore addition / removal of comments.\n    if (record.type === 'childList') {\n        for (let i = 0; i < record.addedNodes.length; i++) {\n            if (!(record.addedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        for (let i = 0; i < record.removedNodes.length; i++) {\n            if (!(record.removedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Observe everything else.\n    return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    _mutationObserverFactory = inject(MutationObserverFactory);\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _observedElements = new Map();\n    _ngZone = inject(NgZone);\n    constructor() { }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream\n                .pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length))\n                .subscribe(records => {\n                this._ngZone.run(() => {\n                    observer.next(records);\n                });\n            });\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._observedElements.has(element)) {\n                const stream = new Subject();\n                const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n                if (observer) {\n                    observer.observe(element, {\n                        characterData: true,\n                        childList: true,\n                        subtree: true,\n                    });\n                }\n                this._observedElements.set(element, { observer, stream, count: 1 });\n            }\n            else {\n                this._observedElements.get(element).count++;\n            }\n            return this._observedElements.get(element).stream;\n        });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    _contentObserver = inject(ContentObserver);\n    _elementRef = inject(ElementRef);\n    /** Event emitted for each change in the element's content. */\n    event = new EventEmitter();\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    _disabled = false;\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    _debounce;\n    _currentSubscription = null;\n    constructor() { }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkObserveContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkObserveContent, isStandalone: true, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\", booleanAttribute], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: () => [], propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkObserveContentDisabled', transform: booleanAttribute }]\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, imports: [CdkObserveContent], exports: [CdkObserveContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkObserveContent],\n                    exports: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1I,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;AAC1D,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;;AAEtF;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC;EACA,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,IAAID,MAAM,CAACE,MAAM,YAAYC,OAAO,EAAE;IACrE,OAAO,IAAI;EACf;EACA;EACA,IAAIH,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IAC7B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,EAAEJ,MAAM,CAACK,UAAU,CAACD,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC5C,OAAO,KAAK;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACO,YAAY,CAACD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,EAAEJ,MAAM,CAACO,YAAY,CAACH,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AAHA,IAIMK,uBAAuB;EAA7B,MAAMA,uBAAuB,CAAC;IAC1BC,MAAMA,CAACC,QAAQ,EAAE;MACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;IAC1F;IACA,OAAOE,IAAI,YAAAC,gCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFN,uBAAuB;IAAA;IAC1H,OAAOO,KAAK,kBAD6EpC,EAAE,CAAAqC,kBAAA;MAAAC,KAAA,EACYT,uBAAuB;MAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;MAAAO,UAAA,EAAc;IAAM;EACtJ;EAAC,OANKX,uBAAuB;AAAA;AAO7B;EAAA,QAAAY,SAAA,oBAAAA,SAAA;AAAA;AAIA;AAAA,IACMC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBC,wBAAwB,GAAGzC,MAAM,CAAC2B,uBAAuB,CAAC;IAC1D;IACAe,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7BC,OAAO,GAAG5C,MAAM,CAACC,MAAM,CAAC;IACxB4C,WAAWA,CAAA,EAAG,CAAE;IAChBC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACJ,iBAAiB,CAACK,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;IAClF;IACAE,OAAOA,CAACC,YAAY,EAAE;MAClB,MAAMH,OAAO,GAAGhC,aAAa,CAACmC,YAAY,CAAC;MAC3C,OAAO,IAAI3C,UAAU,CAAE4C,QAAQ,IAAK;QAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;QAC5C,MAAMO,YAAY,GAAGF,MAAM,CACtBG,IAAI,CAAC9C,GAAG,CAAC+C,OAAO,IAAIA,OAAO,CAAC9C,MAAM,CAACO,MAAM,IAAI,CAACD,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,MAAM,CAAC8C,OAAO,IAAI,CAAC,CAACA,OAAO,CAACjC,MAAM,CAAC,CAAC,CAChHkC,SAAS,CAACD,OAAO,IAAI;UACtB,IAAI,CAACd,OAAO,CAACgB,GAAG,CAAC,MAAM;YACnBP,QAAQ,CAACQ,IAAI,CAACH,OAAO,CAAC;UAC1B,CAAC,CAAC;QACN,CAAC,CAAC;QACF,OAAO,MAAM;UACTF,YAAY,CAACM,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACC,iBAAiB,CAACd,OAAO,CAAC;QACnC,CAAC;MACL,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIM,eAAeA,CAACN,OAAO,EAAE;MACrB,OAAO,IAAI,CAACL,OAAO,CAACoB,iBAAiB,CAAC,MAAM;QACxC,IAAI,CAAC,IAAI,CAACtB,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;UACtC,MAAMK,MAAM,GAAG,IAAI5C,OAAO,CAAC,CAAC;UAC5B,MAAM2C,QAAQ,GAAG,IAAI,CAACZ,wBAAwB,CAACb,MAAM,CAACsC,SAAS,IAAIZ,MAAM,CAACO,IAAI,CAACK,SAAS,CAAC,CAAC;UAC1F,IAAIb,QAAQ,EAAE;YACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;cACtBkB,aAAa,EAAE,IAAI;cACnBC,SAAS,EAAE,IAAI;cACfC,OAAO,EAAE;YACb,CAAC,CAAC;UACN;UACA,IAAI,CAAC3B,iBAAiB,CAAC4B,GAAG,CAACrB,OAAO,EAAE;YAAEI,QAAQ;YAAEC,MAAM;YAAEiB,KAAK,EAAE;UAAE,CAAC,CAAC;QACvE,CAAC,MACI;UACD,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;QAC/C;QACA,OAAO,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACK,MAAM;MACrD,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIS,iBAAiBA,CAACd,OAAO,EAAE;MACvB,IAAI,IAAI,CAACP,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;QACrC,IAAI,CAACP,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;QAC3C,IAAI,CAAC,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;UAC5C,IAAI,CAACrB,gBAAgB,CAACD,OAAO,CAAC;QAClC;MACJ;IACJ;IACA;IACAC,gBAAgBA,CAACD,OAAO,EAAE;MACtB,IAAI,IAAI,CAACP,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;QACrC,MAAM;UAAEI,QAAQ;UAAEC;QAAO,CAAC,GAAG,IAAI,CAACZ,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC;QAChE,IAAII,QAAQ,EAAE;UACVA,QAAQ,CAACoB,UAAU,CAAC,CAAC;QACzB;QACAnB,MAAM,CAACoB,QAAQ,CAAC,CAAC;QACjB,IAAI,CAAChC,iBAAiB,CAACiC,MAAM,CAAC1B,OAAO,CAAC;MAC1C;IACJ;IACA,OAAOlB,IAAI,YAAA6C,wBAAA3C,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,eAAe;IAAA;IAClH,OAAON,KAAK,kBAlF6EpC,EAAE,CAAAqC,kBAAA;MAAAC,KAAA,EAkFYI,eAAe;MAAAH,OAAA,EAAfG,eAAe,CAAAT,IAAA;MAAAO,UAAA,EAAc;IAAM;EAC9I;EAAC,OA3EKE,eAAe;AAAA;AA4ErB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AAHA,IAIMsC,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpBC,gBAAgB,GAAG9E,MAAM,CAACwC,eAAe,CAAC;IAC1CuC,WAAW,GAAG/E,MAAM,CAACE,UAAU,CAAC;IAChC;IACA8E,KAAK,GAAG,IAAI7E,YAAY,CAAC,CAAC;IAC1B;AACJ;AACA;AACA;IACI,IAAI8E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC5D;IACAH,SAAS,GAAG,KAAK;IACjB;IACA,IAAII,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACH,KAAK,EAAE;MAChB,IAAI,CAACI,SAAS,GAAGxE,oBAAoB,CAACoE,KAAK,CAAC;MAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;IACrB;IACAE,SAAS;IACTC,oBAAoB,GAAG,IAAI;IAC3B3C,WAAWA,CAAA,EAAG,CAAE;IAChB4C,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACD,oBAAoB,IAAI,CAAC,IAAI,CAACP,QAAQ,EAAE;QAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;MACrB;IACJ;IACAvC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACsC,YAAY,CAAC,CAAC;IACvB;IACAC,UAAUA,CAAA,EAAG;MACT,IAAI,CAACD,YAAY,CAAC,CAAC;MACnB,MAAM9B,MAAM,GAAG,IAAI,CAACwB,gBAAgB,CAAC3B,OAAO,CAAC,IAAI,CAAC4B,WAAW,CAAC;MAC9D,IAAI,CAACS,oBAAoB,GAAG,CAAC,IAAI,CAACF,QAAQ,GAAGhC,MAAM,CAACG,IAAI,CAAC5C,YAAY,CAAC,IAAI,CAACyE,QAAQ,CAAC,CAAC,GAAGhC,MAAM,EAAEK,SAAS,CAAC,IAAI,CAACqB,KAAK,CAAC;IACzH;IACAI,YAAYA,CAAA,EAAG;MACX,IAAI,CAACI,oBAAoB,EAAE1B,WAAW,CAAC,CAAC;IAC5C;IACA,OAAO/B,IAAI,YAAA2D,0BAAAzD,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4C,iBAAiB;IAAA;IACpH,OAAOc,IAAI,kBAzI8E7F,EAAE,CAAA8F,iBAAA;MAAAxE,IAAA,EAyIJyD,iBAAiB;MAAAgB,SAAA;MAAAC,MAAA;QAAAb,QAAA,+CAAqH7E,gBAAgB;QAAAkF,QAAA;MAAA;MAAAS,OAAA;QAAAf,KAAA;MAAA;MAAAgB,QAAA;IAAA;EACjP;EAAC,OA9CKnB,iBAAiB;AAAA;AA+CvB;EAAA,QAAAtC,SAAA,oBAAAA,SAAA;AAAA;AAcoB,IACd0D,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOlE,IAAI,YAAAmE,wBAAAjE,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgE,eAAe;IAAA;IAClH,OAAOE,IAAI,kBA5J8ErG,EAAE,CAAAsG,gBAAA;MAAAhF,IAAA,EA4JS6E;IAAe;IACnH,OAAOI,IAAI,kBA7J8EvG,EAAE,CAAAwG,gBAAA;MAAAC,SAAA,EA6JqC,CAAC5E,uBAAuB;IAAC;EAC7J;EAAC,OAJKsE,eAAe;AAAA;AAKrB;EAAA,QAAA1D,SAAA,oBAAAA,SAAA;AAAA;AASA,SAASsC,iBAAiB,EAAErC,eAAe,EAAEb,uBAAuB,EAAEsE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}