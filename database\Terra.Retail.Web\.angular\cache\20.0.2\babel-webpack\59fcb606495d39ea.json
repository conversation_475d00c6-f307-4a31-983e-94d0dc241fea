{"ast": null, "code": "import { importProvidersFrom, LOCALE_ID } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\n// Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { FormsModule } from '@angular/forms';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(), provideAnimations(), {\n    provide: LOCALE_ID,\n    useValue: 'ar'\n  }, importProvidersFrom(MatToolbarModule, MatSidenavModule, MatButtonModule, MatIconModule, MatListModule, MatCardModule, MatInputModule, MatSelectModule, MatTableModule, MatPaginatorModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatBadgeModule, MatMenuModule, MatCheckboxModule, MatStepperModule, MatAutocompleteModule, MatRadioModule, MatTooltipModule, MatFormFieldModule, MatSortModule, MatDialogModule, FormsModule)]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "LOCALE_ID", "provideRouter", "provideHttpClient", "provideAnimations", "MatToolbarModule", "MatSidenavModule", "MatButtonModule", "MatIconModule", "MatListModule", "MatCardModule", "MatInputModule", "MatSelectModule", "MatTableModule", "MatPaginatorModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatChipsModule", "MatBadgeModule", "MatMenuModule", "MatCheckboxModule", "MatStepperModule", "MatAutocompleteModule", "MatRadioModule", "MatTooltipModule", "MatFormFieldModule", "MatSortModule", "MatDialogModule", "FormsModule", "routes", "appConfig", "providers", "provide", "useValue"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom, LOCALE_ID } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideHttpClient } from '@angular/common/http';\r\nimport { provideAnimations } from '@angular/platform-browser/animations';\r\n\r\n// Material Modules\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatSidenavModule } from '@angular/material/sidenav';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatBadgeModule } from '@angular/material/badge';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatStepperModule } from '@angular/material/stepper';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatRadioModule } from '@angular/material/radio';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { routes } from './app.routes';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideHttpClient(),\r\n    provideAnimations(),\r\n    { provide: LOCALE_ID, useValue: 'ar' },\r\n    importProvidersFrom(\r\n      MatToolbarModule,\r\n      MatSidenavModule,\r\n      MatButtonModule,\r\n      MatIconModule,\r\n      MatListModule,\r\n      MatCardModule,\r\n      MatInputModule,\r\n      MatSelectModule,\r\n      MatTableModule,\r\n      MatPaginatorModule,\r\n      MatSnackBarModule,\r\n      MatProgressSpinnerModule,\r\n      MatChipsModule,\r\n      MatBadgeModule,\r\n      MatMenuModule,\r\n      MatCheckboxModule,\r\n      MatStepperModule,\r\n      MatAutocompleteModule,\r\n      MatRadioModule,\r\n      MatTooltipModule,\r\n      MatFormFieldModule,\r\n      MatSortModule,\r\n      MatDialogModule,\r\n      FormsModule\r\n    )\r\n  ]\r\n};\r\n"], "mappings": "AAAA,SAA4BA,mBAAmB,EAAEC,SAAS,QAAQ,eAAe;AACjF,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,iBAAiB,QAAQ,sCAAsC;AAExE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,MAAM,QAAQ,cAAc;AAErC,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACT7B,aAAa,CAAC2B,MAAM,CAAC,EACrB1B,iBAAiB,EAAE,EACnBC,iBAAiB,EAAE,EACnB;IAAE4B,OAAO,EAAE/B,SAAS;IAAEgC,QAAQ,EAAE;EAAI,CAAE,EACtCjC,mBAAmB,CACjBK,gBAAgB,EAChBC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,iBAAiB,EACjBC,gBAAgB,EAChBC,qBAAqB,EACrBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,WAAW,CACZ;CAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}