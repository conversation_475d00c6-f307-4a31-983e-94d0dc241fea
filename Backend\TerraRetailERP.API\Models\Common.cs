using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Areas")]
    public class Area
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        public int CountryId { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("CountryId")]
        public virtual Country Country { get; set; } = null!;

        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("Countries")]
    public class Country
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(10)]
        public string? Code { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<Area> Areas { get; set; } = new List<Area>();
    }

    [Table("UserSessions")]
    public class UserSession
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        [Required]
        [StringLength(500)]
        public string SessionToken { get; set; } = string.Empty;

        public int CurrentBranchId { get; set; }
        public DateTime LoginTime { get; set; } = DateTime.Now;
        public DateTime LastActivity { get; set; } = DateTime.Now;
        public DateTime? LogoutTime { get; set; }

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CurrentBranchId")]
        public virtual Branch CurrentBranch { get; set; } = null!;
    }

    [Table("AuditTrail")]
    public class AuditTrail
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string TableName { get; set; } = string.Empty;

        public int? RecordId { get; set; }

        [StringLength(MAX)]
        public string? OldValues { get; set; }

        [StringLength(MAX)]
        public string? NewValues { get; set; }

        public int UserId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        private const int MAX = int.MaxValue;
    }

    [Table("EmployeeDocuments")]
    public class EmployeeDocument
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        [Required]
        [StringLength(50)]
        public string DocumentType { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string DocumentName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? FileName { get; set; }

        [StringLength(1000)]
        public string? FilePath { get; set; }

        public long? FileSize { get; set; }

        [StringLength(100)]
        public string? MimeType { get; set; }

        public DateTime? ExpiryDate { get; set; }
        public bool IsRequired { get; set; } = false;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int UploadedBy { get; set; }
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("UploadedBy")]
        public virtual User UploadedByUser { get; set; } = null!;
    }

    [Table("WorkRegulations")]
    public class WorkRegulation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string RegulationType { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string RegulationCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string TitleAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? TitleEn { get; set; }

        [Required]
        public string DescriptionAr { get; set; } = string.Empty;

        public string? DescriptionEn { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? NumericValue { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? MinValue { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? MaxValue { get; set; }

        [StringLength(20)]
        public string? UnitType { get; set; }

        [Required]
        [StringLength(50)]
        public string AppliesTo { get; set; } = string.Empty;

        public int? TargetId { get; set; }
        public int? BranchId { get; set; }

        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        public bool IsActive { get; set; } = true;
        public int Priority { get; set; } = 1;

        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public int? LastModifiedBy { get; set; }
        public DateTime? LastModifiedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch? Branch { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        [ForeignKey("LastModifiedBy")]
        public virtual User? LastModifier { get; set; }
    }
}
