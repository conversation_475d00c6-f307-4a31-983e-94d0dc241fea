/* Terra Retail ERP - Edit Supplier Form Styles */

.edit-supplier-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--primary-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .back-btn {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      width: 48px !important;
      height: 48px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }
    }

    .header-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0 0 var(--spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .page-subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;

    .save-btn {
      background: var(--warning-500) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-xl) !important;
      font-weight: 600 !important;
      box-shadow: var(--shadow-lg) !important;

      &:hover:not(:disabled) {
        background: var(--warning-600) !important;
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
      }

      &:disabled {
        background: var(--gray-400) !important;
        color: var(--gray-600) !important;
      }
    }

    .cancel-btn {
      border-color: rgba(255, 255, 255, 0.5) !important;
      color: white !important;
      padding: var(--spacing-md) var(--spacing-lg) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: white !important;
      }
    }
  }
}

/* ===== FORM CONTENT ===== */
.form-content {
  padding: var(--spacing-2xl) 0;
}

.supplier-form {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

/* ===== FORM CARDS ===== */
.form-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  overflow: hidden !important;

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-xl) var(--spacing-2xl) !important;
    border-bottom: 1px solid var(--gray-200) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;

      mat-icon {
        color: var(--warning-600) !important;
        font-size: 1.5rem !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-2xl) !important;
  }
}

/* ===== FORM GRID ===== */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  align-items: start;

  .full-width {
    grid-column: 1 / -1;
  }
}

/* ===== FORM FIELDS ===== */
.mat-mdc-form-field {
  width: 100% !important;

  .mat-mdc-text-field-wrapper {
    background: white !important;
    border-radius: var(--radius-lg) !important;
    transition: all var(--transition-normal) !important;

    &:hover {
      box-shadow: var(--shadow-sm) !important;
    }

    &.mdc-text-field--focused {
      box-shadow: 0 0 0 3px var(--warning-100) !important;
    }
  }

  .mat-mdc-form-field-label {
    font-family: var(--font-family-primary) !important;
    font-weight: 500 !important;
  }

  .mat-mdc-input-element {
    font-family: var(--font-family-primary) !important;
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--gray-500) !important;
  }
}

/* ===== CHECKBOX FIELD ===== */
.checkbox-field {
  display: flex;
  align-items: center;
  height: 56px;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background: white;
  transition: all var(--transition-normal);

  &:hover {
    border-color: var(--warning-300);
    box-shadow: var(--shadow-sm);
  }

  .mat-mdc-checkbox {
    .mdc-checkbox {
      .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
        background-color: var(--warning-500) !important;
        border-color: var(--warning-500) !important;
      }
    }

    .mdc-form-field {
      font-family: var(--font-family-primary) !important;
      font-weight: 500 !important;
      color: var(--gray-800) !important;
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: var(--warning-500);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: var(--spacing-md);

      .header-text {
        .page-title {
          font-size: 2rem;
        }
      }
    }

    .header-actions {
      width: 100%;
      justify-content: center;
    }
  }

  .form-content {
    padding: var(--spacing-xl) 0;
  }

  .supplier-form {
    gap: var(--spacing-xl);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .form-card {
    .mat-mdc-card-header {
      padding: var(--spacing-lg) var(--spacing-xl) !important;

      .mat-mdc-card-title {
        font-size: 1.125rem !important;
      }
    }

    .mat-mdc-card-content {
      padding: var(--spacing-xl) !important;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-left {
      .header-text {
        .page-title {
          font-size: 1.75rem;
        }

        .page-subtitle {
          font-size: 1rem;
        }
      }
    }

    .header-actions {
      flex-direction: column;
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  .form-card {
    .mat-mdc-card-header {
      padding: var(--spacing-md) var(--spacing-lg) !important;
    }

    .mat-mdc-card-content {
      padding: var(--spacing-lg) !important;
    }
  }

  .form-grid {
    gap: var(--spacing-md);
  }
}
