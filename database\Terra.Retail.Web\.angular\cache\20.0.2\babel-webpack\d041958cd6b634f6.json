{"ast": null, "code": "/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nlet PlatformNavigation = /*#__PURE__*/(() => {\n  class PlatformNavigation {\n    static ɵfac = function PlatformNavigation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformNavigation)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformNavigation,\n      factory: () => (() => window.navigation)(),\n      providedIn: 'platform'\n    });\n  }\n  return PlatformNavigation;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { PlatformNavigation };", "map": {"version": 3, "names": ["i0", "Injectable", "PlatformNavigation", "ɵfac", "PlatformNavigation_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "window", "navigation", "providedIn", "ngDevMode"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/common/fesm2022/platform_navigation-B45Jeakb.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: PlatformNavigation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: PlatformNavigation, providedIn: 'platform', useFactory: () => window.navigation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.3\", ngImport: i0, type: PlatformNavigation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => window.navigation }]\n        }] });\n\nexport { PlatformNavigation };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA;AACA;AACA;AAHA,IAIMC,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAOC,IAAI,YAAAC,2BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,kBAAkB;IAAA;IACrH,OAAOI,KAAK,kBAD6EN,EAAE,CAAAO,kBAAA;MAAAC,KAAA,EACYN,kBAAkB;MAAAO,OAAA,EAAAA,CAAA,MAAsC,MAAMC,MAAM,CAACC,UAAU;MAAAC,UAAA,EAA/C;IAAU;EACrJ;EAAC,OAHKV,kBAAkB;AAAA;AAIxB;EAAA,QAAAW,SAAA,oBAAAA,SAAA;AAAA;AAKA,SAASX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}