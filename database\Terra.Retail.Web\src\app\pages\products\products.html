<!-- صفحة إدارة المنتجات -->
<div class="products-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <mat-icon>inventory</mat-icon>
          إدارة المنتجات
        </h1>
        <p>إدارة وتتبع جميع المنتجات في المتجر</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="/add-product">
          <mat-icon>add</mat-icon>
          إضافة منتج جديد
        </button>
        <button mat-stroked-button (click)="exportProducts()">
          <mat-icon>download</mat-icon>
          تصدير البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <mat-card class="stat-card total">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>inventory_2</mat-icon>
          </div>
          <div class="stat-details">
            <h3>{{ totalProducts }}</h3>
            <p>إجمالي المنتجات</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card active">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-details">
            <h3>{{ activeProducts }}</h3>
            <p>المنتجات النشطة</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card value">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>attach_money</mat-icon>
          </div>
          <div class="stat-details">
            <h3>{{ totalValue | currency:'EGP':'symbol':'1.2-2' }}</h3>
            <p>قيمة المخزون</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card warning">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon">
            <mat-icon>warning</mat-icon>
          </div>
          <div class="stat-details">
            <h3 [matBadge]="lowStockProducts" matBadgeColor="warn">{{ lowStockProducts }}</h3>
            <p>مخزون منخفض</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Filters Section -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-container">
        <div class="search-section">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>البحث في المنتجات</mat-label>
            <input matInput
                   [(ngModel)]="searchTerm"
                   (input)="onSearch()"
                   placeholder="اسم المنتج، الكود، أو الوصف">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="filter-section">
          <mat-form-field appearance="outline">
            <mat-label>الفئة</mat-label>
            <mat-select [(ngModel)]="selectedCategory" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الفئات</mat-option>
              <mat-option *ngFor="let category of categories" [value]="category.id">
                {{ category.nameAr }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>الوحدة</mat-label>
            <mat-select [(ngModel)]="selectedUnit" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الوحدات</mat-option>
              <mat-option *ngFor="let unit of units" [value]="unit.id">
                {{ unit.nameAr }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
            <mat-icon>clear</mat-icon>
            مسح الفلاتر
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Products Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <div class="table-header">
        <h2>قائمة المنتجات ({{ filteredProducts.length }})</h2>
      </div>

      <div class="table-container" *ngIf="!isLoading">
        <table mat-table [dataSource]="filteredProducts" class="products-table">
          <!-- Product Code Column -->
          <ng-container matColumnDef="productCode">
            <th mat-header-cell *matHeaderCellDef>كود المنتج</th>
            <td mat-cell *matCellDef="let product">
              <span class="product-code">{{ product.productCode }}</span>
            </td>
          </ng-container>

          <!-- Product Name Column -->
          <ng-container matColumnDef="nameAr">
            <th mat-header-cell *matHeaderCellDef>اسم المنتج</th>
            <td mat-cell *matCellDef="let product">
              <div class="product-name">
                <strong>{{ product.nameAr }}</strong>
                <small>{{ product.nameEn }}</small>
              </div>
            </td>
          </ng-container>

          <!-- Category Column -->
          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef>الفئة</th>
            <td mat-cell *matCellDef="let product">
              <mat-chip-set>
                <mat-chip>{{ product.categoryName }}</mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Unit Column -->
          <ng-container matColumnDef="unit">
            <th mat-header-cell *matHeaderCellDef>الوحدة</th>
            <td mat-cell *matCellDef="let product">{{ product.unitName }}</td>
          </ng-container>

          <!-- Price Column -->
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef>السعر</th>
            <td mat-cell *matCellDef="let product">
              <span class="price">{{ product.price | currency:'EGP':'symbol':'1.2-2' }}</span>
            </td>
          </ng-container>

          <!-- Stock Column -->
          <ng-container matColumnDef="stock">
            <th mat-header-cell *matHeaderCellDef>المخزون</th>
            <td mat-cell *matCellDef="let product">
              <span class="stock-badge" [ngClass]="getStockClass(product.stock)">
                {{ product.stock }} {{ product.unitName }}
              </span>
              <small class="stock-status">{{ getStockStatus(product.stock) }}</small>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>الحالة</th>
            <td mat-cell *matCellDef="let product">
              <mat-chip-set>
                <mat-chip [color]="product.isActive ? 'primary' : 'warn'" selected>
                  {{ product.isActive ? 'نشط' : 'غير نشط' }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let product">
              <div class="action-buttons">
                <button mat-icon-button color="primary" (click)="viewProduct(product)" matTooltip="عرض التفاصيل">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button color="accent" (click)="editProduct(product)" matTooltip="تعديل">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteProduct(product)" matTooltip="حذف">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="filteredProducts.length === 0" class="no-data">
          <mat-icon>inventory_2</mat-icon>
          <h3>لا توجد منتجات</h3>
          <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
          <button mat-raised-button color="primary" (click)="clearFilters()">
            مسح الفلاتر
          </button>
        </div>
      </div>

      <!-- Loading -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-icon class="loading-icon">hourglass_empty</mat-icon>
        <p>جاري تحميل المنتجات...</p>
      </div>
    </mat-card-content>
  </mat-card>
</div>
