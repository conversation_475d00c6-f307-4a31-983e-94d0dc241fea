TableName	ColumnName	DataType	MaxLength	IsNullable	ColumnOrder
__EFMigrationsHistory	MigrationId	nvarchar	300	0	1
__EFMigrationsHistory	ProductVersion	nvarchar	64	0	2
AccountBalances	Id	int	4	0	1
AccountBalances	AccountId	int	4	0	2
AccountBalances	DebitBalance	decimal	9	0	3
AccountBalances	CreditBalance	decimal	9	0	4
AccountBalances	NetBalance	decimal	9	0	5
AccountBalances	LastTransactionDate	datetime2	8	0	6
AccountBalances	CreatedAt	datetime2	8	0	7
AccountBalances	UpdatedAt	datetime2	8	1	8
ActivityLog	Id	bigint	8	0	1
ActivityLog	UserId	int	4	0	2
ActivityLog	Action	nvarchar	200	0	3
ActivityLog	Module	nvarchar	200	1	4
ActivityLog	Entity	nvarchar	200	1	5
ActivityLog	EntityId	int	4	1	6
ActivityLog	Description	nvarchar	2000	1	7
ActivityLog	Details	nvarchar	4000	1	8
ActivityLog	IPAddress	nvarchar	100	1	9
ActivityLog	UserAgent	nvarchar	1000	1	10
ActivityLog	CreatedAt	datetime2	8	0	11
ActivityLog	BranchId	int	4	1	12
AttendanceRecords	Id	int	4	0	1
AttendanceRecords	EmployeeId	int	4	0	2
AttendanceRecords	ShiftId	int	4	1	3
AttendanceRecords	ShiftDate	datetime2	8	0	4
AttendanceRecords	PlannedCheckInTime	datetime2	8	0	5
AttendanceRecords	PlannedCheckOutTime	datetime2	8	0	6
AttendanceRecords	ActualCheckInTime	datetime2	8	1	7
AttendanceRecords	ActualCheckOutTime	datetime2	8	1	8
AttendanceRecords	WorkingMinutes	int	4	0	9
AttendanceRecords	LateMinutes	int	4	0	10
AttendanceRecords	EarlyLeaveMinutes	int	4	0	11
AttendanceRecords	OvertimeMinutes	int	4	0	12
AttendanceRecords	AttendanceStatus	nvarchar	100	0	13
AttendanceRecords	IsComplete	bit	1	0	14
AttendanceRecords	IsManualEntry	bit	1	0	15
AttendanceRecords	Notes	nvarchar	2000	1	16
AttendanceRecords	CreatedAt	datetime2	8	0	17
AttendanceRecords	UpdatedAt	datetime2	8	1	18
AuditLog	Id	bigint	8	0	1
AuditLog	TableName	nvarchar	200	0	2
AuditLog	RecordId	int	4	0	3
AuditLog	Action	nvarchar	100	0	4
AuditLog	OldValues	nvarchar	4000	1	5
AuditLog	NewValues	nvarchar	4000	1	6
AuditLog	ChangedFields	nvarchar	4000	1	7
AuditLog	UserId	int	4	0	8
AuditLog	ActionDate	datetime2	8	0	9
AuditLog	IPAddress	nvarchar	100	1	10
AuditLog	UserAgent	nvarchar	1000	1	11
AuditLog	SessionId	nvarchar	200	1	12
AuditLog	Description	nvarchar	2000	1	13
BarcodeTypes	Id	int	4	0	1
BarcodeTypes	TypeCode	nvarchar	20	0	2
BarcodeTypes	NameAr	nvarchar	200	0	3
BarcodeTypes	NameEn	nvarchar	200	1	4
BarcodeTypes	Description	nvarchar	1000	1	5
BarcodeTypes	Format	nvarchar	100	1	6
BarcodeTypes	IsActive	bit	1	0	7
BarcodeTypes	CreatedAt	datetime2	8	0	8
BarcodeTypes	UpdatedAt	datetime2	8	1	9
Branches	Id	int	4	0	1
Branches	NameAr	nvarchar	400	0	2
Branches	NameEn	nvarchar	400	1	3
Branches	Code	nvarchar	40	0	4
Branches	Address	nvarchar	2000	1	5
Branches	Phone	nvarchar	80	1	6
Branches	Email	nvarchar	400	1	7
Branches	IsActive	bit	1	0	8
Branches	IsMainBranch	bit	1	0	9
Branches	CreatedAt	datetime2	8	0	10
BranchTransferDetails	Id	int	4	0	1
BranchTransferDetails	BranchTransferId	int	4	0	2
BranchTransferDetails	ProductId	int	4	0	3
BranchTransferDetails	LineNumber	int	4	0	4
BranchTransferDetails	RequestedQuantity	decimal	9	0	5
BranchTransferDetails	SentQuantity	decimal	9	0	6
BranchTransferDetails	ReceivedQuantity	decimal	9	0	7
BranchTransferDetails	UnitCost	decimal	9	0	8
BranchTransferDetails	TotalCost	decimal	9	0	9
BranchTransferDetails	BatchNumber	nvarchar	100	1	10
BranchTransferDetails	ExpiryDate	datetime2	8	1	11
BranchTransferDetails	ItemNotes	nvarchar	2000	1	12
BranchTransferDetails	CreatedAt	datetime2	8	0	13
BranchTransfers	Id	int	4	0	1
BranchTransfers	TransferNumber	nvarchar	80	0	2
BranchTransfers	TransferDate	datetime2	8	0	3
BranchTransfers	ReceivedDate	datetime2	8	1	4
BranchTransfers	FromBranchId	int	4	0	5
BranchTransfers	ToBranchId	int	4	0	6
BranchTransfers	Status	int	4	0	7
BranchTransfers	TotalValue	decimal	9	0	8
BranchTransfers	TotalItems	int	4	0	9
BranchTransfers	Notes	nvarchar	2000	1	10
BranchTransfers	RequestedBy	int	4	0	11
BranchTransfers	SentBy	int	4	1	12
BranchTransfers	ReceivedBy	int	4	1	13
BranchTransfers	SentAt	datetime2	8	1	14
BranchTransfers	CreatedAt	datetime2	8	0	15
BranchTransfers	RequestedByUserId	int	4	0	16
BranchTransfers	SentByUserId	int	4	1	17
BranchTransfers	ReceivedByUserId	int	4	1	18
CashPayments	Id	int	4	0	1
CashPayments	PaymentNumber	nvarchar	40	0	2
CashPayments	PaymentDate	datetime2	8	0	3
CashPayments	Amount	decimal	9	0	4
CashPayments	CashAccountId	int	4	0	5
CashPayments	ToAccountId	int	4	1	6
CashPayments	CustomerId	int	4	1	7
CashPayments	SupplierId	int	4	1	8
CashPayments	EmployeeId	int	4	1	9
CashPayments	Description	nvarchar	1000	1	10
CashPayments	Notes	nvarchar	2000	1	11
CashPayments	PaymentMethod	nvarchar	100	1	12
CashPayments	ReferenceNumber	nvarchar	100	1	13
CashPayments	JournalEntryId	int	4	1	14
CashPayments	BranchId	int	4	1	15
CashPayments	UserId	int	4	1	16
CashPayments	IsPosted	bit	1	1	17
CashPayments	PostedAt	datetime2	8	1	18
CashPayments	PostedBy	int	4	1	19
CashPayments	IsActive	bit	1	1	20
CashPayments	CreatedAt	datetime2	8	1	21
CashPayments	UpdatedAt	datetime2	8	1	22
CashReceipts	Id	int	4	0	1
CashReceipts	ReceiptNumber	nvarchar	40	0	2
CashReceipts	ReceiptDate	datetime2	8	0	3
CashReceipts	Amount	decimal	9	0	4
CashReceipts	CashAccountId	int	4	0	5
CashReceipts	FromAccountId	int	4	1	6
CashReceipts	CustomerId	int	4	1	7
CashReceipts	SupplierId	int	4	1	8
CashReceipts	EmployeeId	int	4	1	9
CashReceipts	Description	nvarchar	1000	1	10
CashReceipts	Notes	nvarchar	2000	1	11
CashReceipts	PaymentMethod	nvarchar	100	1	12
CashReceipts	ReferenceNumber	nvarchar	100	1	13
CashReceipts	JournalEntryId	int	4	1	14
CashReceipts	BranchId	int	4	1	15
CashReceipts	UserId	int	4	1	16
CashReceipts	IsPosted	bit	1	1	17
CashReceipts	PostedAt	datetime2	8	1	18
CashReceipts	PostedBy	int	4	1	19
CashReceipts	IsActive	bit	1	1	20
CashReceipts	CreatedAt	datetime2	8	1	21
CashReceipts	UpdatedAt	datetime2	8	1	22
Categories	Id	int	4	0	1
Categories	NameAr	nvarchar	400	0	2
Categories	NameEn	nvarchar	400	1	3
Categories	Description	nvarchar	2000	1	4
Categories	ParentId	int	4	1	5
Categories	DisplayOrder	int	4	0	6
Categories	IsActive	bit	1	0	7
Categories	CreatedAt	datetime2	8	0	8
ChartOfAccounts	Id	int	4	0	1
ChartOfAccounts	AccountCode	nvarchar	40	0	2
ChartOfAccounts	NameAr	nvarchar	400	0	3
ChartOfAccounts	NameEn	nvarchar	400	1	4
ChartOfAccounts	AccountType	int	4	0	5
ChartOfAccounts	ParentId	int	4	1	6
ChartOfAccounts	Level	int	4	0	7
ChartOfAccounts	IsParent	bit	1	0	8
ChartOfAccounts	AllowPosting	bit	1	0	9
ChartOfAccounts	Description	nvarchar	2000	1	10
ChartOfAccounts	IsActive	bit	1	0	11
ChartOfAccounts	CreatedAt	datetime2	8	0	12
ChartOfAccounts	UpdatedAt	datetime2	8	1	13
ChartOfAccounts	CreatedBy	int	4	1	14
ChartOfAccounts	UpdatedBy	int	4	1	15
ChartOfAccounts	AutoGenerateCode	bit	1	1	16
ChartOfAccounts	NextChildCode	int	4	1	17
ChartOfAccounts	CodePrefix	nvarchar	20	1	18
ChartOfAccounts	CodeLength	int	4	1	19
Cities	Id	int	4	0	1
Cities	NameAr	nvarchar	400	0	2
Cities	NameEn	nvarchar	400	1	3
Cities	CountryId	int	4	0	4
Cities	PostalCode	nvarchar	40	1	5
Cities	AreaCode	nvarchar	40	1	6
Cities	IsActive	bit	1	0	7
Cities	CreatedAt	datetime2	8	0	8
Cities	UpdatedAt	datetime2	8	1	9
CostCenters	Id	int	4	0	1
CostCenters	Code	nvarchar	40	0	2
CostCenters	NameAr	nvarchar	400	0	3
CostCenters	NameEn	nvarchar	400	1	4
CostCenters	Description	nvarchar	2000	1	5
CostCenters	IsActive	bit	1	0	6
CostCenters	CreatedAt	datetime2	8	0	7
Counters	Id	int	4	0	1
Counters	CounterName	nvarchar	100	0	2
Counters	Prefix	nvarchar	40	1	3
Counters	CurrentValue	int	4	0	4
Counters	NumberLength	int	4	0	5
Counters	BranchId	int	4	1	6
Counters	IsActive	bit	1	0	7
Counters	CreatedAt	datetime2	8	0	8
Counters	UpdatedAt	datetime2	8	1	9
Countries	Id	int	4	0	1
Countries	NameAr	nvarchar	400	0	2
Countries	NameEn	nvarchar	400	1	3
Countries	Code	nvarchar	20	1	4
Countries	PhoneCode	nvarchar	20	1	5
Countries	Currency	nvarchar	20	1	6
Countries	Flag	nvarchar	100	1	7
Countries	IsActive	bit	1	0	8
Countries	CreatedAt	datetime2	8	0	9
Countries	UpdatedAt	datetime2	8	1	10
Customers	Id	int	4	0	1
Customers	CustomerCode	nvarchar	80	0	2
Customers	NameAr	nvarchar	400	0	3
Customers	NameEn	nvarchar	400	1	4
Customers	Phone1	nvarchar	80	1	5
Customers	Email	nvarchar	400	1	6
Customers	Address	nvarchar	2000	1	7
Customers	CustomerTypeId	int	4	1	8
Customers	CountryId	int	4	1	9
Customers	CityId	int	4	1	10
Customers	PriceCategoryId	int	4	1	11
Customers	OpeningBalance	decimal	9	0	12
Customers	CurrentBalance	decimal	9	0	13
Customers	IsActive	bit	1	0	14
Customers	CreatedAt	datetime2	8	0	15
Customers	UpdatedAt	datetime2	8	1	16
Customers	Phone2	nvarchar	80	1	17
Customers	AccountCode	nvarchar	40	1	18
Customers	ChartOfAccountId	int	4	1	19
CustomerTypes	Id	int	4	0	1
CustomerTypes	NameAr	nvarchar	400	0	2
CustomerTypes	NameEn	nvarchar	400	1	3
CustomerTypes	Description	nvarchar	2000	1	4
CustomerTypes	DisplayOrder	int	4	0	5
CustomerTypes	Color	nvarchar	40	1	6
CustomerTypes	Icon	nvarchar	100	1	7
CustomerTypes	IsActive	bit	1	0	8
CustomerTypes	CreatedAt	datetime2	8	0	9
CustomerTypes	UpdatedAt	datetime2	8	1	10
Departments	Id	int	4	0	1
Departments	NameAr	nvarchar	400	0	2
Departments	NameEn	nvarchar	400	1	3
Departments	Description	nvarchar	2000	1	4
Departments	ManagerId	int	4	1	5
Departments	IsActive	bit	1	0	6
Departments	CreatedAt	datetime2	8	0	7
Discounts	Id	int	4	0	1
Discounts	NameAr	nvarchar	400	0	2
Discounts	NameEn	nvarchar	400	1	3
Discounts	Code	nvarchar	40	1	4
Discounts	DiscountType	int	4	0	5
Discounts	Value	decimal	9	0	6
Discounts	MinimumAmount	decimal	9	1	7
Discounts	MaximumDiscount	decimal	9	1	8
Discounts	StartDate	datetime2	8	1	9
Discounts	EndDate	datetime2	8	1	10
Discounts	MaxUsageCount	int	4	1	11
Discounts	CurrentUsageCount	int	4	0	12
Discounts	Description	nvarchar	2000	1	13
Discounts	IsActive	bit	1	0	14
Discounts	CreatedAt	datetime2	8	0	15
Discounts	UpdatedAt	datetime2	8	1	16
EmployeeLeaveBalances	Id	int	4	0	1
EmployeeLeaveBalances	EmployeeId	int	4	0	2
EmployeeLeaveBalances	LeaveTypeId	int	4	0	3
EmployeeLeaveBalances	Year	int	4	0	4
EmployeeLeaveBalances	EntitledDays	int	4	0	5
EmployeeLeaveBalances	UsedDays	int	4	0	6
EmployeeLeaveBalances	RemainingDays	int	4	0	7
EmployeeLeaveBalances	CarriedForwardDays	int	4	0	8
EmployeeLeaveBalances	CreatedAt	datetime2	8	0	9
EmployeeLeaveBalances	UpdatedAt	datetime2	8	1	10
EmployeeLeaves	Id	int	4	0	1
EmployeeLeaves	EmployeeId	int	4	0	2
EmployeeLeaves	LeaveTypeId	int	4	0	3
EmployeeLeaves	LeaveNumber	nvarchar	80	0	4
EmployeeLeaves	StartDate	datetime2	8	0	5
EmployeeLeaves	EndDate	datetime2	8	0	6
EmployeeLeaves	TotalDays	int	4	0	7
EmployeeLeaves	Reason	nvarchar	2000	1	8
EmployeeLeaves	Status	int	4	0	9
EmployeeLeaves	ApprovedBy	int	4	1	10
EmployeeLeaves	ApprovedAt	datetime2	8	1	11
EmployeeLeaves	ApprovalNotes	nvarchar	2000	1	12
EmployeeLeaves	CreatedAt	datetime2	8	0	13
Employees	Id	int	4	0	1
Employees	EmployeeCode	nvarchar	80	0	2
Employees	NameAr	nvarchar	400	0	3
Employees	NameEn	nvarchar	400	1	4
Employees	NationalId	nvarchar	40	1	5
Employees	Phone1	nvarchar	80	1	6
Employees	Phone2	nvarchar	80	1	7
Employees	Email	nvarchar	400	1	8
Employees	Address	nvarchar	2000	1	9
Employees	BirthDate	datetime2	8	1	10
Employees	HireDate	datetime2	8	1	11
Employees	DepartmentId	int	4	1	12
Employees	PositionId	int	4	1	13
Employees	BranchId	int	4	0	14
Employees	BasicSalary	decimal	9	0	15
Employees	BiometricId	nvarchar	100	1	16
Employees	EmergencyContact	nvarchar	200	1	17
Employees	EmergencyPhone	nvarchar	80	1	18
Employees	IsActive	bit	1	0	19
Employees	CreatedAt	datetime2	8	0	20
Employees	UpdatedAt	datetime2	8	1	21
EmployeeShifts	Id	int	4	0	1
EmployeeShifts	EmployeeId	int	4	0	2
EmployeeShifts	ShiftId	int	4	0	3
EmployeeShifts	EffectiveDate	datetime2	8	0	4
EmployeeShifts	EndDate	datetime2	8	1	5
EmployeeShifts	DaysOfWeek	nvarchar	40	1	6
EmployeeShifts	IsActive	bit	1	0	7
EmployeeShifts	CreatedAt	datetime2	8	0	8
FinancialTransactions	Id	bigint	8	0	1
FinancialTransactions	TransactionNumber	nvarchar	80	0	2
FinancialTransactions	TransactionDate	datetime2	8	0	3
FinancialTransactions	TransactionType	int	4	0	4
FinancialTransactions	SourceType	nvarchar	100	1	5
FinancialTransactions	SourceId	int	4	1	6
FinancialTransactions	SourceNumber	nvarchar	80	1	7
FinancialTransactions	CustomerId	int	4	1	8
FinancialTransactions	SupplierId	int	4	1	9
FinancialTransactions	BranchId	int	4	0	10
FinancialTransactions	UserId	int	4	0	11
FinancialTransactions	DebitAmount	decimal	9	0	12
FinancialTransactions	CreditAmount	decimal	9	0	13
FinancialTransactions	NetAmount	decimal	9	0	14
FinancialTransactions	Description	nvarchar	2000	0	15
FinancialTransactions	Reference	nvarchar	200	1	16
FinancialTransactions	Status	int	4	0	17
FinancialTransactions	PaymentMethodId	int	4	1	18
FinancialTransactions	JournalEntryId	int	4	1	19
FinancialTransactions	CheckNumber	nvarchar	200	1	20
FinancialTransactions	BankReference	nvarchar	200	1	21
FinancialTransactions	DueDate	datetime2	8	1	22
FinancialTransactions	ClearanceDate	datetime2	8	1	23
FinancialTransactions	CurrencyCode	nvarchar	40	1	24
FinancialTransactions	ExchangeRate	decimal	9	0	25
FinancialTransactions	TransactionFee	decimal	9	1	26
FinancialTransactions	Notes	nvarchar	2000	1	27
FinancialTransactions	IsReconciled	bit	1	0	28
FinancialTransactions	ReconciledAt	datetime2	8	1	29
FinancialTransactions	ReconciledBy	int	4	1	30
FinancialTransactions	CreatedAt	datetime2	8	0	31
FinancialTransactions	UpdatedAt	datetime2	8	1	32
FinancialTransactions	CancelledBy	int	4	1	33
FinancialTransactions	CancelledAt	datetime2	8	1	34
FinancialTransactions	CancellationReason	nvarchar	1000	1	35
FinancialTransactions	TransactionTypeEntityId	int	4	1	36
FinancialTransactions	MainAccountId	int	4	0	37
FinancialTransactions	SubAccountId	int	4	1	38
JournalEntries	Id	int	4	0	1
JournalEntries	EntryNumber	nvarchar	80	0	2
JournalEntries	EntryDate	datetime2	8	0	3
JournalEntries	Description	nvarchar	2000	0	4
JournalEntries	Reference	nvarchar	200	1	5
JournalEntries	TransactionType	int	4	0	6
JournalEntries	Status	int	4	0	7
JournalEntries	TotalDebit	decimal	9	0	8
JournalEntries	TotalCredit	decimal	9	0	9
JournalEntries	UserId	int	4	0	10
JournalEntries	PostedBy	int	4	1	11
JournalEntries	PostedAt	datetime2	8	1	12
JournalEntries	CreatedAt	datetime2	8	0	13
JournalEntries	PreliminaryPostedBy	int	4	1	14
JournalEntries	PreliminaryPostedAt	datetime2	8	1	15
JournalEntries	PreliminaryPostingNotes	nvarchar	1000	1	16
JournalEntries	FinalPostedBy	int	4	1	17
JournalEntries	FinalPostedAt	datetime2	8	1	18
JournalEntries	FinalPostingNotes	nvarchar	1000	1	19
JournalEntries	ReversedBy	int	4	1	20
JournalEntries	ReversedAt	datetime2	8	1	21
JournalEntries	ReversalReason	nvarchar	1000	1	22
JournalEntries	ReversalJournalEntryId	int	4	1	23
JournalEntryDetails	Id	int	4	0	1
JournalEntryDetails	JournalEntryId	int	4	0	2
JournalEntryDetails	AccountId	int	4	0	3
JournalEntryDetails	LineNumber	int	4	0	4
JournalEntryDetails	Description	nvarchar	2000	1	5
JournalEntryDetails	DebitAmount	decimal	9	0	6
JournalEntryDetails	CreditAmount	decimal	9	0	7
JournalEntryDetails	CostCenterId	int	4	1	8
JournalEntryDetails	Reference	nvarchar	200	1	9
JournalEntryDetails	CreatedAt	datetime2	8	0	10
LeaveTypes	Id	int	4	0	1
LeaveTypes	NameAr	nvarchar	400	0	2
LeaveTypes	NameEn	nvarchar	400	1	3
LeaveTypes	MaxDaysPerYear	int	4	0	4
LeaveTypes	IsPaid	bit	1	0	5
LeaveTypes	RequireApproval	bit	1	0	6
LeaveTypes	RequireDocument	bit	1	0	7
LeaveTypes	Description	nvarchar	2000	1	8
LeaveTypes	IsActive	bit	1	0	9
LeaveTypes	CreatedAt	datetime2	8	0	10
NotificationLog	Id	bigint	8	0	1
NotificationLog	NotificationType	nvarchar	100	0	2
NotificationLog	Recipient	nvarchar	400	0	3
NotificationLog	Subject	nvarchar	1000	0	4
NotificationLog	Message	nvarchar	4000	0	5
NotificationLog	Status	nvarchar	100	0	6
NotificationLog	ErrorMessage	nvarchar	2000	1	7
NotificationLog	UserId	int	4	1	8
NotificationLog	CreatedAt	datetime2	8	0	9
NotificationLog	SentAt	datetime2	8	1	10
NotificationLog	DeliveredAt	datetime2	8	1	11
NotificationLog	RetryCount	int	4	0	12
NotificationLog	NextRetryAt	datetime2	8	1	13
NotificationLog	ExternalId	nvarchar	200	1	14
NotificationLog	Metadata	nvarchar	4000	1	15
PaymentMethods	Id	int	4	0	1
PaymentMethods	NameAr	nvarchar	400	0	2
PaymentMethods	NameEn	nvarchar	400	1	3
PaymentMethods	Code	nvarchar	40	1	4
PaymentMethods	Description	nvarchar	2000	1	5
PaymentMethods	PaymentType	int	4	0	6
PaymentMethods	RequireReference	bit	1	0	7
PaymentMethods	RequireApproval	bit	1	0	8
PaymentMethods	Icon	nvarchar	100	1	9
PaymentMethods	Color	nvarchar	40	1	10
PaymentMethods	DisplayOrder	int	4	0	11
PaymentMethods	TransactionFeePercentage	decimal	5	1	12
PaymentMethods	FixedTransactionFee	decimal	9	1	13
PaymentMethods	ChartAccountId	int	4	1	14
PaymentMethods	IsActive	bit	1	0	15
PaymentMethods	IsDefault	bit	1	0	16
PaymentMethods	CreatedAt	datetime2	8	0	17
Payments	Id	int	4	0	1
Payments	PaymentNumber	nvarchar	80	0	2
Payments	PaymentDate	datetime2	8	0	3
Payments	SupplierId	int	4	1	4
Payments	BranchId	int	4	0	5
Payments	PaymentMethodId	int	4	0	6
Payments	Amount	decimal	9	0	7
Payments	Description	nvarchar	2000	0	8
Payments	ReferenceNumber	nvarchar	200	1	9
Payments	BankName	nvarchar	400	1	10
Payments	AccountNumber	nvarchar	200	1	11
Payments	Status	int	4	0	12
Payments	UserId	int	4	0	13
Payments	Currency	nvarchar	20	0	14
Payments	ExchangeRate	decimal	9	0	15
Payments	CreatedAt	datetime2	8	0	16
Permissions	Id	int	4	0	1
Permissions	NameAr	nvarchar	200	0	2
Permissions	NameEn	nvarchar	200	0	3
Permissions	Code	nvarchar	200	0	4
Permissions	Module	nvarchar	200	1	5
Permissions	Category	nvarchar	200	1	6
Permissions	Description	nvarchar	1000	1	7
Permissions	IsActive	bit	1	0	8
Permissions	SortOrder	int	4	0	9
Permissions	Icon	nvarchar	100	1	10
Permissions	CreatedAt	datetime2	8	0	11
Permissions	UpdatedAt	datetime2	8	1	12
Positions	Id	int	4	0	1
Positions	NameAr	nvarchar	400	0	2
Positions	NameEn	nvarchar	400	1	3
Positions	Description	nvarchar	2000	1	4
Positions	MinSalary	decimal	9	1	5
Positions	MaxSalary	decimal	9	1	6
Positions	IsActive	bit	1	0	7
Positions	CreatedAt	datetime2	8	0	8
PriceCategories	Id	int	4	0	1
PriceCategories	NameAr	nvarchar	400	0	2
PriceCategories	NameEn	nvarchar	400	1	3
PriceCategories	Description	nvarchar	2000	1	4
PriceCategories	DiscountPercentage	decimal	5	0	5
PriceCategories	MarkupPercentage	decimal	5	0	6
PriceCategories	DisplayOrder	int	4	0	7
PriceCategories	Color	nvarchar	40	1	8
PriceCategories	IsActive	bit	1	0	9
PriceCategories	CreatedAt	datetime2	8	0	10
PriceCategories	UpdatedAt	datetime2	8	1	11
ProductBatches	Id	int	4	0	1
ProductBatches	ProductId	int	4	0	2
ProductBatches	BranchId	int	4	0	3
ProductBatches	BatchNumber	nvarchar	100	0	4
ProductBatches	Quantity	decimal	9	0	5
ProductBatches	RemainingQuantity	decimal	9	0	6
ProductBatches	CostPrice	decimal	9	0	7
ProductBatches	ExpiryDate	datetime2	8	1	8
ProductBatches	ManufacturingDate	datetime2	8	1	9
ProductBatches	SupplierBatchNumber	nvarchar	200	1	10
ProductBatches	SupplierId	int	4	1	11
ProductBatches	PurchaseId	int	4	1	12
ProductBatches	IsActive	bit	1	0	13
ProductBatches	CreatedAt	datetime2	8	0	14
ProductPrices	Id	int	4	0	1
ProductPrices	ProductId	int	4	0	2
ProductPrices	PriceCategoryId	int	4	0	3
ProductPrices	BranchId	int	4	1	4
ProductPrices	Price	decimal	9	0	5
ProductPrices	DiscountPercentage	decimal	5	1	6
ProductPrices	EffectiveDate	datetime2	8	0	7
ProductPrices	ExpiryDate	datetime2	8	1	8
ProductPrices	IsActive	bit	1	0	9
ProductPrices	CreatedAt	datetime2	8	0	10
ProductPrices	UpdatedAt	datetime2	8	1	11
Products	Id	int	4	0	1
Products	ProductCode	nvarchar	80	0	2
Products	NameAr	nvarchar	400	0	3
Products	NameEn	nvarchar	400	1	4
Products	Description	nvarchar	2000	1	5
Products	CategoryId	int	4	0	6
Products	UnitId	int	4	0	7
Products	Barcode	nvarchar	100	1	8
Products	CostPrice	decimal	9	0	9
Products	BasePrice	decimal	9	0	10
Products	ProfitMargin	decimal	5	0	11
Products	MinimumStock	decimal	9	1	12
Products	MaximumStock	decimal	9	1	13
Products	ReorderPoint	decimal	9	1	14
Products	Weight	decimal	9	1	15
Products	IsActive	bit	1	0	16
Products	CreatedAt	datetime2	8	0	17
Products	UpdatedAt	datetime2	8	1	18
Products	TaxId	int	4	1	19
Products	BarcodeTypeId	int	4	1	20
ProductStocks	Id	int	4	0	1
ProductStocks	ProductId	int	4	0	2
ProductStocks	BranchId	int	4	0	3
ProductStocks	AvailableQuantity	decimal	9	0	4
ProductStocks	ReservedQuantity	decimal	9	0	5
ProductStocks	OnOrderQuantity	decimal	9	0	6
ProductStocks	TotalInQuantity	decimal	9	0	7
ProductStocks	TotalOutQuantity	decimal	9	0	8
ProductStocks	BranchMinStock	decimal	9	1	9
ProductStocks	BranchMaxStock	decimal	9	1	10
ProductStocks	BranchReorderPoint	decimal	9	1	11
ProductStocks	AverageCostPrice	decimal	9	0	12
ProductStocks	LastCostPrice	decimal	9	0	13
ProductStocks	StockValue	decimal	9	0	14
ProductStocks	StorageLocation	nvarchar	200	1	15
ProductStocks	IsAvailableForSale	bit	1	0	16
ProductStocks	LastMovementDate	datetime2	8	0	17
ProductStocks	CreatedAt	datetime2	8	0	18
ProductStocks	UpdatedAt	datetime2	8	1	19
PurchaseItems	Id	int	4	0	1
PurchaseItems	PurchaseId	int	4	0	2
PurchaseItems	ProductId	int	4	0	3
PurchaseItems	LineNumber	int	4	0	4
PurchaseItems	Quantity	decimal	9	0	5
PurchaseItems	UnitCost	decimal	9	0	6
PurchaseItems	DiscountPercentage	decimal	5	0	7
PurchaseItems	DiscountAmount	decimal	9	0	8
PurchaseItems	LineTotal	decimal	9	0	9
PurchaseItems	TaxPercentage	decimal	5	0	10
PurchaseItems	TaxAmount	decimal	9	0	11
PurchaseItems	FinalTotal	decimal	9	0	12
PurchaseItems	ItemNotes	nvarchar	2000	1	13
PurchaseItems	CreatedAt	datetime2	8	0	14
Purchases	Id	int	4	0	1
Purchases	InvoiceNumber	nvarchar	80	0	2
Purchases	SupplierInvoiceNumber	nvarchar	80	1	3
Purchases	InvoiceDate	datetime2	8	0	4
Purchases	SupplierId	int	4	0	5
Purchases	BranchId	int	4	0	6
Purchases	UserId	int	4	0	7
Purchases	Status	int	4	0	8
Purchases	PurchaseType	int	4	0	9
Purchases	SubTotal	decimal	9	0	10
Purchases	DiscountPercentage	decimal	5	0	11
Purchases	DiscountAmount	decimal	9	0	12
Purchases	TaxPercentage	decimal	5	0	13
Purchases	TaxAmount	decimal	9	0	14
Purchases	TotalAmount	decimal	9	0	15
Purchases	PaidAmount	decimal	9	0	16
Purchases	RemainingAmount	decimal	9	0	17
Purchases	Notes	nvarchar	2000	1	18
Purchases	CreatedAt	datetime2	8	0	19
Purchases	UpdatedAt	datetime2	8	1	20
Receipts	Id	int	4	0	1
Receipts	ReceiptNumber	nvarchar	80	0	2
Receipts	ReceiptDate	datetime2	8	0	3
Receipts	CustomerId	int	4	1	4
Receipts	BranchId	int	4	0	5
Receipts	PaymentMethodId	int	4	0	6
Receipts	Amount	decimal	9	0	7
Receipts	Description	nvarchar	2000	0	8
Receipts	ReferenceNumber	nvarchar	200	1	9
Receipts	BankName	nvarchar	400	1	10
Receipts	AccountNumber	nvarchar	200	1	11
Receipts	Status	int	4	0	12
Receipts	UserId	int	4	0	13
Receipts	Currency	nvarchar	20	0	14
Receipts	ExchangeRate	decimal	9	0	15
Receipts	CreatedAt	datetime2	8	0	16
RolePermissions	Id	int	4	0	1
RolePermissions	RoleId	int	4	0	2
RolePermissions	PermissionId	int	4	0	3
RolePermissions	CanView	bit	1	0	4
RolePermissions	CanAdd	bit	1	0	5
RolePermissions	CanEdit	bit	1	0	6
RolePermissions	CanDelete	bit	1	0	7
RolePermissions	CanApprove	bit	1	0	8
RolePermissions	CanReverse	bit	1	0	9
RolePermissions	CanExport	bit	1	0	10
RolePermissions	CanPrint	bit	1	0	11
RolePermissions	CreatedAt	datetime2	8	0	12
RolePermissions	CreatedBy	int	4	0	13
Roles	Id	int	4	0	1
Roles	NameAr	nvarchar	200	0	2
Roles	NameEn	nvarchar	200	0	3
Roles	Description	nvarchar	1000	1	4
Roles	IsActive	bit	1	0	5
Roles	IsSystemRole	bit	1	0	6
Roles	SortOrder	int	4	0	7
Roles	CreatedAt	datetime2	8	0	8
Roles	UpdatedAt	datetime2	8	1	9
Roles	CreatedBy	int	4	0	10
Roles	UpdatedBy	int	4	1	11
SaleItems	Id	int	4	0	1
SaleItems	SaleId	int	4	0	2
SaleItems	ProductId	int	4	0	3
SaleItems	LineNumber	int	4	0	4
SaleItems	Quantity	decimal	9	0	5
SaleItems	UnitPrice	decimal	9	0	6
SaleItems	UnitCostPrice	decimal	9	0	7
SaleItems	DiscountPercentage	decimal	5	0	8
SaleItems	DiscountAmount	decimal	9	0	9
SaleItems	LineTotal	decimal	9	0	10
SaleItems	TaxPercentage	decimal	5	0	11
SaleItems	TaxAmount	decimal	9	0	12
SaleItems	FinalTotal	decimal	9	0	13
SaleItems	ItemNotes	nvarchar	2000	1	14
SaleItems	CreatedAt	datetime2	8	0	15
Sales	Id	int	4	0	1
Sales	InvoiceNumber	nvarchar	80	0	2
Sales	InvoiceDate	datetime2	8	0	3
Sales	CustomerId	int	4	1	4
Sales	CustomerName	nvarchar	400	1	5
Sales	BranchId	int	4	0	6
Sales	UserId	int	4	0	7
Sales	Status	int	4	0	8
Sales	SaleType	int	4	0	9
Sales	SubTotal	decimal	9	0	10
Sales	DiscountPercentage	decimal	5	0	11
Sales	DiscountAmount	decimal	9	0	12
Sales	TaxPercentage	decimal	5	0	13
Sales	TaxAmount	decimal	9	0	14
Sales	TotalAmount	decimal	9	0	15
Sales	PaidAmount	decimal	9	0	16
Sales	RemainingAmount	decimal	9	0	17
Sales	Notes	nvarchar	2000	1	18
Sales	CreatedAt	datetime2	8	0	19
Sales	UpdatedAt	datetime2	8	1	20
Shifts	Id	int	4	0	1
Shifts	ShiftName	nvarchar	400	0	2
Shifts	StartTime	time	5	0	3
Shifts	EndTime	time	5	0	4
Shifts	BreakDuration	int	4	0	5
Shifts	GracePeriodMinutes	int	4	0	6
Shifts	MaxOvertimeHours	decimal	5	0	7
Shifts	IsFlexible	bit	1	0	8
Shifts	Description	nvarchar	2000	1	9
Shifts	IsActive	bit	1	0	10
Shifts	CreatedAt	datetime2	8	0	11
StockAdjustmentDetails	Id	int	4	0	1
StockAdjustmentDetails	StockAdjustmentId	int	4	0	2
StockAdjustmentDetails	ProductId	int	4	0	3
StockAdjustmentDetails	LineNumber	int	4	0	4
StockAdjustmentDetails	SystemQuantity	decimal	9	0	5
StockAdjustmentDetails	PhysicalQuantity	decimal	9	0	6
StockAdjustmentDetails	AdjustmentQuantity	decimal	9	0	7
StockAdjustmentDetails	UnitCost	decimal	9	0	8
StockAdjustmentDetails	AdjustmentValue	decimal	9	0	9
StockAdjustmentDetails	Reason	nvarchar	2000	1	10
StockAdjustmentDetails	Notes	nvarchar	2000	1	11
StockAdjustmentDetails	CreatedAt	datetime2	8	0	12
StockAdjustments	Id	int	4	0	1
StockAdjustments	AdjustmentNumber	nvarchar	80	0	2
StockAdjustments	AdjustmentDate	datetime2	8	0	3
StockAdjustments	AdjustmentReason	nvarchar	200	0	4
StockAdjustments	Description	nvarchar	2000	1	5
StockAdjustments	BranchId	int	4	0	6
StockAdjustments	Status	int	4	0	7
StockAdjustments	TotalAdjustmentValue	decimal	9	0	8
StockAdjustments	TotalItems	int	4	0	9
StockAdjustments	UserId	int	4	0	10
StockAdjustments	ApprovedBy	int	4	1	11
StockAdjustments	ApprovedAt	datetime2	8	1	12
StockAdjustments	CreatedAt	datetime2	8	0	13
StockMovements	Id	int	4	0	1
StockMovements	ProductId	int	4	0	2
StockMovements	BranchId	int	4	0	3
StockMovements	MovementNumber	nvarchar	80	0	4
StockMovements	MovementType	int	4	0	5
StockMovements	MovementReason	nvarchar	200	0	6
StockMovements	Quantity	decimal	9	0	7
StockMovements	UnitCost	decimal	9	0	8
StockMovements	TotalCost	decimal	9	0	9
StockMovements	BalanceBefore	decimal	9	0	10
StockMovements	BalanceAfter	decimal	9	0	11
StockMovements	BatchNumber	nvarchar	100	1	12
StockMovements	ExpiryDate	datetime2	8	1	13
StockMovements	MovementDate	datetime2	8	0	14
StockMovements	SourceType	nvarchar	100	1	15
StockMovements	SourceId	int	4	1	16
StockMovements	UserId	int	4	0	17
StockMovements	Notes	nvarchar	2000	1	18
StockMovements	CreatedAt	datetime2	8	0	19
Suppliers	Id	int	4	0	1
Suppliers	SupplierCode	nvarchar	80	0	2
Suppliers	NameAr	nvarchar	400	0	3
Suppliers	NameEn	nvarchar	400	1	4
Suppliers	Phone1	nvarchar	80	1	5
Suppliers	Phone2	nvarchar	80	1	6
Suppliers	Email	nvarchar	400	1	7
Suppliers	Address	nvarchar	2000	1	8
Suppliers	SupplierTypeId	int	4	1	9
Suppliers	CountryId	int	4	1	10
Suppliers	CityId	int	4	1	11
Suppliers	ContactPerson	nvarchar	200	1	12
Suppliers	TaxNumber	nvarchar	80	1	13
Suppliers	CommercialRegister	nvarchar	80	1	14
Suppliers	OpeningBalance	decimal	9	0	15
Suppliers	CurrentBalance	decimal	9	0	16
Suppliers	CreditLimit	decimal	9	1	17
Suppliers	PaymentTerms	int	4	0	18
Suppliers	IsActive	bit	1	0	19
Suppliers	CreatedAt	datetime2	8	0	20
Suppliers	UpdatedAt	datetime2	8	1	21
Suppliers	ContactPersonPhone	nvarchar	80	1	22
Suppliers	AccountCode	nvarchar	40	1	23
Suppliers	ChartOfAccountId	int	4	1	24
SupplierTypes	Id	int	4	0	1
SupplierTypes	NameAr	nvarchar	400	0	2
SupplierTypes	NameEn	nvarchar	400	1	3
SupplierTypes	Description	nvarchar	2000	1	4
SupplierTypes	DisplayOrder	int	4	0	5
SupplierTypes	Color	nvarchar	40	1	6
SupplierTypes	Icon	nvarchar	100	1	7
SupplierTypes	IsActive	bit	1	0	8
SupplierTypes	CreatedAt	datetime2	8	0	9
SupplierTypes	UpdatedAt	datetime2	8	1	10
SystemLogs	Id	bigint	8	0	1
SystemLogs	LogLevel	nvarchar	100	0	2
SystemLogs	Category	nvarchar	200	0	3
SystemLogs	Message	nvarchar	1000	0	4
SystemLogs	Details	nvarchar	4000	1	5
SystemLogs	StackTrace	nvarchar	4000	1	6
SystemLogs	UserId	int	4	1	7
SystemLogs	IPAddress	nvarchar	100	1	8
SystemLogs	UserAgent	nvarchar	1000	1	9
SystemLogs	RequestPath	nvarchar	200	1	10
SystemLogs	HttpMethod	nvarchar	40	1	11
SystemLogs	ResponseStatusCode	int	4	1	12
SystemLogs	ResponseTime	bigint	8	1	13
SystemLogs	CreatedAt	datetime2	8	0	14
Taxes	Id	int	4	0	1
Taxes	NameAr	nvarchar	400	0	2
Taxes	NameEn	nvarchar	400	1	3
Taxes	Code	nvarchar	40	1	4
Taxes	Percentage	decimal	5	0	5
Taxes	TaxType	int	4	0	6
Taxes	Description	nvarchar	2000	1	7
Taxes	IsDefault	bit	1	0	8
Taxes	IsActive	bit	1	0	9
Taxes	CreatedAt	datetime2	8	0	10
Taxes	UpdatedAt	datetime2	8	1	11
TransactionTracking	Id	bigint	8	0	1
TransactionTracking	FinancialTransactionId	bigint	8	0	2
TransactionTracking	Action	nvarchar	100	0	3
TransactionTracking	OldStatus	nvarchar	100	1	4
TransactionTracking	NewStatus	nvarchar	100	1	5
TransactionTracking	OldAmount	decimal	9	1	6
TransactionTracking	NewAmount	decimal	9	1	7
TransactionTracking	ChangeDetails	nvarchar	4000	1	8
TransactionTracking	Reason	nvarchar	2000	1	9
TransactionTracking	UserId	int	4	0	10
TransactionTracking	ActionDate	datetime2	8	0	11
TransactionTracking	IPAddress	nvarchar	100	1	12
TransactionTracking	UserAgent	nvarchar	1000	1	13
TransactionTypes	Id	int	4	0	1
TransactionTypes	NameAr	nvarchar	200	0	2
TransactionTypes	NameEn	nvarchar	200	0	3
TransactionTypes	Code	nvarchar	100	0	4
TransactionTypes	Category	nvarchar	100	1	5
TransactionTypes	Description	nvarchar	1000	1	6
TransactionTypes	IsActive	bit	1	0	7
TransactionTypes	SortOrder	int	4	0	8
TransactionTypes	Icon	nvarchar	100	1	9
TransactionTypes	Color	nvarchar	100	1	10
TransactionTypes	RequiresApproval	bit	1	0	11
TransactionTypes	AllowReversal	bit	1	0	12
TransactionTypes	CreatedAt	datetime2	8	0	13
TransactionTypes	UpdatedAt	datetime2	8	1	14
Units	Id	int	4	0	1
Units	NameAr	nvarchar	400	0	2
Units	NameEn	nvarchar	400	1	3
Units	Symbol	nvarchar	20	0	4
Units	DisplayOrder	int	4	0	5
Units	IsActive	bit	1	0	6
Units	CreatedAt	datetime2	8	0	7
UserBranchPermissions	Id	int	4	0	1
UserBranchPermissions	UserId	int	4	0	2
UserBranchPermissions	BranchId	int	4	1	3
UserBranchPermissions	IsDefaultBranch	bit	1	0	4
UserBranchPermissions	CanAccessAllBranches	bit	1	0	5
UserBranchPermissions	CanViewReports	bit	1	0	6
UserBranchPermissions	CanViewFinancials	bit	1	0	7
UserBranchPermissions	CanTransferBetweenBranches	bit	1	0	8
UserBranchPermissions	CreatedAt	datetime2	8	0	9
UserBranchPermissions	UpdatedAt	datetime2	8	1	10
UserBranchPermissions	CreatedBy	int	4	0	11
UserBranchPermissions	UpdatedBy	int	4	1	12
UserPermissions	Id	int	4	0	1
UserPermissions	UserId	int	4	0	2
UserPermissions	PermissionId	int	4	0	3
UserPermissions	CanView	bit	1	0	4
UserPermissions	CanAdd	bit	1	0	5
UserPermissions	CanEdit	bit	1	0	6
UserPermissions	CanDelete	bit	1	0	7
UserPermissions	CanApprove	bit	1	0	8
UserPermissions	CanReverse	bit	1	0	9
UserPermissions	CanExport	bit	1	0	10
UserPermissions	CanPrint	bit	1	0	11
UserPermissions	BranchId	int	4	1	12
UserPermissions	CreatedAt	datetime2	8	0	13
UserPermissions	UpdatedAt	datetime2	8	1	14
UserPermissions	CreatedBy	int	4	0	15
UserPermissions	UpdatedBy	int	4	1	16
UserRoles	Id	int	4	0	1
UserRoles	UserId	int	4	0	2
UserRoles	RoleId	int	4	0	3
UserRoles	CreatedAt	datetime2	8	0	4
UserRoles	CreatedBy	int	4	0	5
Users	Id	int	4	0	1
Users	Username	nvarchar	200	0	2
Users	Email	nvarchar	400	0	3
Users	PasswordHash	nvarchar	-1	0	4
Users	FullName	nvarchar	400	0	5
Users	IsActive	bit	1	0	6
Users	IsSystemAdmin	bit	1	0	7
Users	CreatedAt	datetime2	8	0	8
Users	LastLoginAt	datetime2	8	1	9
UserSessions	Id	int	4	0	1
UserSessions	UserId	int	4	0	2
UserSessions	SessionId	nvarchar	200	0	3
UserSessions	IPAddress	nvarchar	100	1	4
UserSessions	UserAgent	nvarchar	1000	1	5
UserSessions	LoginTime	datetime2	8	0	6
UserSessions	LogoutTime	datetime2	8	1	7
UserSessions	IsActive	bit	1	0	8
UserSessions	DefaultBranchId	int	4	1	9
UserSessions	LastActivity	nvarchar	2000	1	10
UserSessions	LastActivityTime	datetime2	8	0	11
WorkRegulations	Id	int	4	0	1
WorkRegulations	NameAr	nvarchar	400	0	2
WorkRegulations	NameEn	nvarchar	400	1	3
WorkRegulations	Description	nvarchar	2000	1	4
WorkRegulations	RegulationType	int	4	0	5
WorkRegulations	RegulationText	nvarchar	4000	1	6
WorkRegulations	DisplayOrder	int	4	0	7
WorkRegulations	IsActive	bit	1	0	8
WorkRegulations	CreatedAt	datetime2	8	0	9
WorkRegulations	UpdatedAt	datetime2	8	1	10