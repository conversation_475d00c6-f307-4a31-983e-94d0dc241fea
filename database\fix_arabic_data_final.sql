-- إصلاح نهائي للبيانات العربية في قاعدة البيانات
-- Final Fix for Arabic Data in Database

USE TerraRetailERP;
GO

PRINT N'بدء الإصلاح النهائي للبيانات العربية...';

-- إصلاح أنواع العملاء
UPDATE CustomerTypes SET NameAr = N'عميل عادي' WHERE Id = 1;
UPDATE CustomerTypes SET NameAr = N'عميل جملة' WHERE Id = 2;
UPDATE CustomerTypes SET NameAr = N'عميل VIP' WHERE Id = 3;
UPDATE CustomerTypes SET NameAr = N'عميل مؤسسي' WHERE Id = 4;
UPDATE CustomerTypes SET NameAr = N'عميل حكومي' WHERE Id = 5;

-- إصلاح وحدات القياس
UPDATE Units SET NameAr = N'قطعة' WHERE Id = 1;
UPDATE Units SET NameAr = N'كيلوجرام' WHERE Id = 2;
UPDATE Units SET NameAr = N'جرام' WHERE Id = 3;
UPDATE Units SET NameAr = N'متر' WHERE Id = 4;
UPDATE Units SET NameAr = N'سنتيمتر' WHERE Id = 5;
UPDATE Units SET NameAr = N'لتر' WHERE Id = 6;
UPDATE Units SET NameAr = N'مليلتر' WHERE Id = 7;
UPDATE Units SET NameAr = N'علبة' WHERE Id = 8;
UPDATE Units SET NameAr = N'كرتون' WHERE Id = 9;
UPDATE Units SET NameAr = N'دزينة' WHERE Id = 10;

-- إصلاح فئات الأسعار
UPDATE PriceCategories SET NameAr = N'سعر التجزئة' WHERE Id = 1;
UPDATE PriceCategories SET NameAr = N'سعر الجملة' WHERE Id = 2;
UPDATE PriceCategories SET NameAr = N'سعر VIP' WHERE Id = 3;
UPDATE PriceCategories SET NameAr = N'سعر المؤسسات' WHERE Id = 4;
UPDATE PriceCategories SET NameAr = N'سعر الموظفين' WHERE Id = 5;

-- إصلاح فئات المنتجات
UPDATE Categories SET NameAr = N'عام' WHERE Id = 1;
UPDATE Categories SET NameAr = N'أغذية ومشروبات' WHERE Id = 2;
UPDATE Categories SET NameAr = N'إلكترونيات' WHERE Id = 3;
UPDATE Categories SET NameAr = N'ملابس وأزياء' WHERE Id = 4;
UPDATE Categories SET NameAr = N'منزل وحديقة' WHERE Id = 5;
UPDATE Categories SET NameAr = N'صحة وجمال' WHERE Id = 6;
UPDATE Categories SET NameAr = N'رياضة وترفيه' WHERE Id = 7;
UPDATE Categories SET NameAr = N'كتب وقرطاسية' WHERE Id = 8;
UPDATE Categories SET NameAr = N'ألعاب وهدايا' WHERE Id = 9;
UPDATE Categories SET NameAr = N'سيارات وقطع غيار' WHERE Id = 10;

-- إصلاح الفروع
UPDATE Branches SET NameAr = N'الفرع الرئيسي' WHERE Code = 'MAIN';

-- إضافة فروع إضافية للاختبار
IF NOT EXISTS (SELECT * FROM Branches WHERE Code = 'BR001')
BEGIN
    INSERT INTO Branches (NameAr, NameEn, Code, IsActive, IsMainBranch, Address, Phone, CreatedAt) VALUES
    (N'فرع الرياض', N'Riyadh Branch', 'BR001', 1, 0, N'شارع الملك فهد، الرياض', '011-1234567', GETUTCDATE()),
    (N'فرع جدة', N'Jeddah Branch', 'BR002', 1, 0, N'شارع التحلية، جدة', '012-1234567', GETUTCDATE()),
    (N'فرع الدمام', N'Dammam Branch', 'BR003', 1, 0, N'شارع الملك عبدالعزيز، الدمام', '013-1234567', GETUTCDATE()),
    (N'فرع مكة', N'Makkah Branch', 'BR004', 1, 0, N'شارع إبراهيم الخليل، مكة', '012-7654321', GETUTCDATE());
END

-- إصلاح طرق الدفع
UPDATE PaymentMethods SET NameAr = N'نقدي' WHERE Code = 'CASH';
UPDATE PaymentMethods SET NameAr = N'بطاقة ائتمان' WHERE Code = 'CREDIT';
UPDATE PaymentMethods SET NameAr = N'آجل' WHERE Code = 'TERM';

-- إضافة طرق دفع إضافية
IF NOT EXISTS (SELECT * FROM PaymentMethods WHERE Code = 'DEBIT')
BEGIN
    INSERT INTO PaymentMethods (NameAr, NameEn, Code, PaymentType, IsActive, DisplayOrder, CreatedAt) VALUES
    (N'بطاقة مدى', N'Debit Card', 'DEBIT', 2, 1, 4, GETUTCDATE()),
    (N'تحويل بنكي', N'Bank Transfer', 'TRANSFER', 3, 1, 5, GETUTCDATE()),
    (N'شيك', N'Check', 'CHECK', 5, 1, 6, GETUTCDATE()),
    (N'محفظة إلكترونية', N'E-Wallet', 'EWALLET', 2, 1, 7, GETUTCDATE());
END

-- إصلاح أنواع الموردين
UPDATE SupplierTypes SET NameAr = N'مورد محلي' WHERE Id = 1;
UPDATE SupplierTypes SET NameAr = N'مورد دولي' WHERE Id = 2;
UPDATE SupplierTypes SET NameAr = N'مورد حكومي' WHERE Id = 3;

-- إضافة مناطق جغرافية
IF NOT EXISTS (SELECT * FROM Areas WHERE Id = 1)
BEGIN
    INSERT INTO Areas (NameAr, NameEn, Code, IsActive, DisplayOrder, CreatedAt) VALUES
    (N'الرياض', N'Riyadh', 'RYD', 1, 1, GETUTCDATE()),
    (N'جدة', N'Jeddah', 'JED', 1, 2, GETUTCDATE()),
    (N'الدمام', N'Dammam', 'DMM', 1, 3, GETUTCDATE()),
    (N'مكة المكرمة', N'Makkah', 'MKK', 1, 4, GETUTCDATE()),
    (N'المدينة المنورة', N'Madinah', 'MDN', 1, 5, GETUTCDATE()),
    (N'الطائف', N'Taif', 'TAF', 1, 6, GETUTCDATE()),
    (N'أبها', N'Abha', 'ABH', 1, 7, GETUTCDATE()),
    (N'تبوك', N'Tabuk', 'TBK', 1, 8, GETUTCDATE()),
    (N'حائل', N'Hail', 'HAL', 1, 9, GETUTCDATE()),
    (N'الجوف', N'Al Jouf', 'JOF', 1, 10, GETUTCDATE());
END

PRINT N'تم الانتهاء من إصلاح جميع البيانات العربية!';

-- عرض النتائج
SELECT N'أنواع العملاء' as TableName, COUNT(*) as RecordCount FROM CustomerTypes
UNION ALL
SELECT N'وحدات القياس', COUNT(*) FROM Units
UNION ALL
SELECT N'فئات الأسعار', COUNT(*) FROM PriceCategories
UNION ALL
SELECT N'فئات المنتجات', COUNT(*) FROM Categories
UNION ALL
SELECT N'الفروع', COUNT(*) FROM Branches
UNION ALL
SELECT N'طرق الدفع', COUNT(*) FROM PaymentMethods
UNION ALL
SELECT N'أنواع الموردين', COUNT(*) FROM SupplierTypes
UNION ALL
SELECT N'المناطق', COUNT(*) FROM Areas;
