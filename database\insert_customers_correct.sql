USE TerraRetailERP;
GO

-- حذ<PERSON> العملاء الموجودين
DELETE FROM Customers;
DBCC CHECKIDENT ('Customers', RESEED, 0);

-- إضافة عملاء تجريبيين مصريين بترميز صحيح
INSERT INTO Customers (
    NameAr, CustomerCode, Phone1, Email, Address, 
    CustomerTypeId, AreaId, BranchId, PriceCategoryId, 
    IsActive, CreatedAt
) VALUES
-- عملاء القاهرة
(N'أحمد محمد علي', 'C001', '01012345678', '<EMAIL>', N'شارع التحرير، وسط البلد، القاهرة', 1, 1, 1, 1, 1, GETUTCDATE()),
(N'فاطمة حسن إبراهيم', 'C002', '01123456789', '<EMAIL>', N'مدينة نصر، القاهرة', 2, 1, 1, 2, 1, GETUTCDATE()),
(N'محمود عبدالله أحمد', 'C003', '01234567890', '<EMAIL>', N'مصر الجديدة، القاهرة', 3, 1, 1, 3, 1, GETUTCDATE()),
(N'نورا سامي محمد', 'C004', '01098765432', '<EMAIL>', N'المعادي، القاهرة', 1, 1, 1, 1, 1, GETUTCDATE()),
(N'عمر خالد حسن', 'C005', '01187654321', '<EMAIL>', N'الزمالك، القاهرة', 4, 1, 1, 4, 1, GETUTCDATE()),

-- عملاء الجيزة
(N'سارة أحمد محمود', 'C006', '01276543210', '<EMAIL>', N'الدقي، الجيزة', 2, 2, 21, 2, 1, GETUTCDATE()),
(N'يوسف إبراهيم علي', 'C007', '01065432109', '<EMAIL>', N'المهندسين، الجيزة', 1, 2, 21, 1, 1, GETUTCDATE()),
(N'مريم عبدالرحمن', 'C008', '01154321098', '<EMAIL>', N'فيصل، الجيزة', 3, 2, 21, 3, 1, GETUTCDATE()),

-- عملاء الإسكندرية
(N'حسام الدين محمد', 'C009', '01043210987', '<EMAIL>', N'سيدي جابر، الإسكندرية', 1, 3, 20, 1, 1, GETUTCDATE()),
(N'دينا أحمد فتحي', 'C010', '01132109876', '<EMAIL>', N'الكورنيش، الإسكندرية', 2, 3, 20, 2, 1, GETUTCDATE()),
(N'كريم محمود سعد', 'C011', '01221098765', '<EMAIL>', N'محطة الرمل، الإسكندرية', 1, 3, 20, 1, 1, GETUTCDATE()),

-- عملاء الدقهلية
(N'إيمان حسن محمد', 'C012', '01010987654', '<EMAIL>', N'شارع الجمهورية، المنصورة', 1, 4, 22, 1, 1, GETUTCDATE()),
(N'طارق عبدالله', 'C013', '01119876543', '<EMAIL>', N'ميت غمر، الدقهلية', 2, 4, 22, 2, 1, GETUTCDATE()),

-- عملاء الشرقية
(N'ريهام محمد أحمد', 'C014', '01208765432', '<EMAIL>', N'شارع الجامعة، الزقازيق', 1, 5, 25, 1, 1, GETUTCDATE()),
(N'أمير حسام الدين', 'C015', '01097654321', '<EMAIL>', N'بلبيس، الشرقية', 3, 5, 25, 3, 1, GETUTCDATE()),

-- عملاء القليوبية
(N'هبة الله سامي', 'C016', '01186543210', '<EMAIL>', N'بنها، القليوبية', 1, 6, 1, 1, 1, GETUTCDATE()),
(N'محمد عادل فتحي', 'C017', '01275432109', '<EMAIL>', N'شبرا الخيمة، القليوبية', 2, 6, 1, 2, 1, GETUTCDATE()),

-- عملاء كفر الشيخ
(N'نادية محمود علي', 'C018', '01064321098', '<EMAIL>', N'كفر الشيخ', 1, 7, 1, 1, 1, GETUTCDATE()),

-- عملاء الغربية
(N'وليد أحمد حسن', 'C019', '01153210987', '<EMAIL>', N'شارع الجلاء، طنطا', 1, 8, 24, 1, 1, GETUTCDATE()),
(N'شيماء عبدالرحمن', 'C020', '01242109876', '<EMAIL>', N'المحلة الكبرى، الغربية', 2, 8, 24, 2, 1, GETUTCDATE()),

-- عملاء أسيوط
(N'عبدالرحمن محمد', 'C021', '01031098765', '<EMAIL>', N'شارع الثورة، أسيوط', 1, 19, 23, 1, 1, GETUTCDATE()),
(N'آية سامي أحمد', 'C022', '01120987654', '<EMAIL>', N'أسيوط الجديدة', 3, 19, 23, 3, 1, GETUTCDATE()),

-- عملاء أسوان
(N'مصطفى علي حسن', 'C023', '01209876543', '<EMAIL>', N'شارع النيل، أسوان', 1, 23, 26, 1, 1, GETUTCDATE()),
(N'رانيا محمود', 'C024', '01098765432', '<EMAIL>', N'كوم أمبو، أسوان', 2, 23, 26, 2, 1, GETUTCDATE()),

-- عميل غير نشط للاختبار
(N'عميل غير نشط', 'C025', '01087654321', '<EMAIL>', N'عنوان تجريبي', 1, 1, 1, 1, 0, GETUTCDATE());

-- تحديث العداد
UPDATE Counters SET CurrentValue = 25 WHERE CounterName = 'CUSTOMER';

SELECT N'تم إضافة العملاء المصريين بنجاح' as Result, COUNT(*) as TotalCustomers, 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveCustomers
FROM Customers;
