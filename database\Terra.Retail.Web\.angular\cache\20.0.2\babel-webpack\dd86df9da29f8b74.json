{"ast": null, "code": "import { _Id<PERSON><PERSON><PERSON>, FocusMonitor } from '@angular/cdk/a11y';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, inject, ChangeDetectorRef, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, ElementRef, NgZone, Renderer2, Injector, HostAttributeToken, numberAttribute, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Change event object emitted by radio button and radio group. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"formField\"];\nconst _c2 = [\"*\"];\nclass MatRadioChange {\n  source;\n  value;\n  constructor(/** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = /*#__PURE__*/new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    disabledInteractive: false\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nlet MatRadioGroup = /*#__PURE__*/(() => {\n  class MatRadioGroup {\n    _changeDetector = inject(ChangeDetectorRef);\n    /** Selected value for the radio group. */\n    _value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    _name = inject(_IdGenerator).getId('mat-radio-group-');\n    /** The currently selected radio button. Should match value. */\n    _selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    _labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    _disabled = false;\n    /** Whether the radio group is required. */\n    _required = false;\n    /** Subscription to changes in amount of radio buttons. */\n    _buttonChanges;\n    /** The method to be called in order to update ngModel */\n    _controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** Child radio buttons. */\n    _radios;\n    /**\n     * Theme color of the radio buttons in the group. This API is supported in M2\n     * themes only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n      return this._name;\n    }\n    set name(value) {\n      this._name = value;\n      this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition;\n    }\n    set labelPosition(v) {\n      this._labelPosition = v === 'before' ? 'before' : 'after';\n      this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      if (this._value !== newValue) {\n        // Set this before proceeding to ensure no circular loop occurs with selection.\n        this._value = newValue;\n        this._updateSelectedRadioFromValue();\n        this._checkSelectedRadioButton();\n      }\n    }\n    _checkSelectedRadioButton() {\n      if (this._selected && !this._selected.checked) {\n        this._selected.checked = true;\n      }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n      return this._selected;\n    }\n    set selected(selected) {\n      this._selected = selected;\n      this.value = selected ? selected.value : null;\n      this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n      return this._required;\n    }\n    set required(value) {\n      this._required = value;\n      this._markRadiosForCheck();\n    }\n    /** Whether buttons in the group should be interactive while they're disabled. */\n    get disabledInteractive() {\n      return this._disabledInteractive;\n    }\n    set disabledInteractive(value) {\n      this._disabledInteractive = value;\n      this._markRadiosForCheck();\n    }\n    _disabledInteractive = false;\n    constructor() {}\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n      // Mark this component as initialized in AfterContentInit because the initial value can\n      // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n      // NgModel occurs *after* the OnInit of the MatRadioGroup.\n      this._isInitialized = true;\n      // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n      // buttons depends on it. Note that we don't clear the `value`, because the radio button\n      // may be swapped out with a similar one and there are some internal apps that depend on\n      // that behavior.\n      this._buttonChanges = this._radios.changes.subscribe(() => {\n        if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n          this._selected = null;\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n      if (this.onTouched) {\n        this.onTouched();\n      }\n    }\n    _updateRadioButtonNames() {\n      if (this._radios) {\n        this._radios.forEach(radio => {\n          radio.name = this.name;\n          radio._markForCheck();\n        });\n      }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n      // If the value already matches the selected radio, do nothing.\n      const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n      if (this._radios && !isAlreadySelected) {\n        this._selected = null;\n        this._radios.forEach(radio => {\n          radio.checked = this.value === radio.value;\n          if (radio.checked) {\n            this._selected = radio;\n          }\n        });\n      }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n      if (this._isInitialized) {\n        this.change.emit(new MatRadioChange(this._selected, this._value));\n      }\n    }\n    _markRadiosForCheck() {\n      if (this._radios) {\n        this._radios.forEach(radio => radio._markForCheck());\n      }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n      this.value = value;\n      this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n      this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetector.markForCheck();\n    }\n    static ɵfac = function MatRadioGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioGroup)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioGroup\"],\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }])]\n    });\n  }\n  return MatRadioGroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioButton = /*#__PURE__*/(() => {\n  class MatRadioButton {\n    _elementRef = inject(ElementRef);\n    _changeDetector = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _radioDispatcher = inject(UniqueSelectionDispatcher);\n    _defaultOptions = inject(MAT_RADIO_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _uniqueId = inject(_IdGenerator).getId('mat-radio-');\n    _cleanupClick;\n    /** The unique ID for the radio button. */\n    id = this._uniqueId;\n    /** Analog to HTML 'name' attribute used to group radios for unique selection. */\n    name;\n    /** Used to set the 'aria-label' attribute on the underlying input element. */\n    ariaLabel;\n    /** The 'aria-labelledby' attribute takes precedence as the element's text alternative. */\n    ariaLabelledby;\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    ariaDescribedby;\n    /** Whether ripples are disabled inside the radio button */\n    disableRipple = false;\n    /** Tabindex of the radio button. */\n    tabIndex = 0;\n    /** Whether this radio button is checked. */\n    get checked() {\n      return this._checked;\n    }\n    set checked(value) {\n      if (this._checked !== value) {\n        this._checked = value;\n        if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n          this.radioGroup.selected = this;\n        } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n          // When unchecking the selected radio button, update the selected radio\n          // property on the group.\n          this.radioGroup.selected = null;\n        }\n        if (value) {\n          // Notify all radio buttons with the same name to un-check.\n          this._radioDispatcher.notify(this.id, this.name);\n        }\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** The value of this radio button. */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      if (this._value !== value) {\n        this._value = value;\n        if (this.radioGroup !== null) {\n          if (!this.checked) {\n            // Update checked when the value changed to match the radio group's value\n            this.checked = this.radioGroup.value === value;\n          }\n          if (this.checked) {\n            this.radioGroup.selected = this;\n          }\n        }\n      }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n      return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n    }\n    set labelPosition(value) {\n      this._labelPosition = value;\n    }\n    _labelPosition;\n    /** Whether the radio button is disabled. */\n    get disabled() {\n      return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n    }\n    set disabled(value) {\n      this._setDisabled(value);\n    }\n    /** Whether the radio button is required. */\n    get required() {\n      return this._required || this.radioGroup && this.radioGroup.required;\n    }\n    set required(value) {\n      if (value !== this._required) {\n        this._changeDetector.markForCheck();\n      }\n      this._required = value;\n    }\n    /**\n     * Theme color of the radio button. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      // As per M2 design specifications the selection control radio should use the accent color\n      // palette by default. https://m2.material.io/components/radio-buttons#specs\n      return this._color || this.radioGroup && this.radioGroup.color || this._defaultOptions && this._defaultOptions.color || 'accent';\n    }\n    set color(newValue) {\n      this._color = newValue;\n    }\n    _color;\n    /** Whether the radio button should remain interactive when it is disabled. */\n    get disabledInteractive() {\n      return this._disabledInteractive || this.radioGroup !== null && this.radioGroup.disabledInteractive;\n    }\n    set disabledInteractive(value) {\n      this._disabledInteractive = value;\n    }\n    _disabledInteractive;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** The parent radio group. May or may not be present. */\n    radioGroup;\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether this radio is checked. */\n    _checked = false;\n    /** Whether this radio is disabled. */\n    _disabled;\n    /** Whether this radio is required. */\n    _required;\n    /** Value assigned to this radio. */\n    _value = null;\n    /** Unregister function for _radioDispatcher */\n    _removeUniqueSelectionListener = () => {};\n    /** Previous value of the input's tabindex. */\n    _previousTabIndex;\n    /** The native `<input type=radio>` element */\n    _inputElement;\n    /** Trigger elements for the ripple events. */\n    _rippleTrigger;\n    /** Whether animations are disabled. */\n    _noopAnimations = _animationsDisabled();\n    _injector = inject(Injector);\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const radioGroup = inject(MAT_RADIO_GROUP, {\n        optional: true\n      });\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      // Assertions. Ideally these should be stripped out by the compiler.\n      // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n      this.radioGroup = radioGroup;\n      this._disabledInteractive = this._defaultOptions?.disabledInteractive ?? false;\n      if (tabIndex) {\n        this.tabIndex = numberAttribute(tabIndex, 0);\n      }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._inputElement, origin, options);\n      } else {\n        this._inputElement.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n      // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n      // update radio button's status\n      this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n      if (this.radioGroup) {\n        // If the radio is inside a radio group, determine if it should be checked\n        this.checked = this.radioGroup.value === this._value;\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n        // Copy name from parent radio group\n        this.name = this.radioGroup.name;\n      }\n      this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n        if (id !== this.id && name === this.name) {\n          this.checked = false;\n        }\n      });\n    }\n    ngDoCheck() {\n      this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n      this._updateTabIndex();\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n        if (!focusOrigin && this.radioGroup) {\n          this.radioGroup._touch();\n        }\n      });\n      // We bind this outside of the zone, because:\n      // 1. Its logic is completely DOM-related so we can avoid some change detections.\n      // 2. There appear to be some internal tests that break when this triggers a change detection.\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupClick = this._renderer.listen(this._inputElement.nativeElement, 'click', this._onInputClick);\n      });\n    }\n    ngOnDestroy() {\n      this._cleanupClick?.();\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n      this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n      return this.disableRipple || this.disabled;\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n      // We always have to stop propagation on the change event.\n      // Otherwise the change event, from the input element, will bubble up and\n      // emit its event object to the `change` output.\n      event.stopPropagation();\n      if (!this.checked && !this.disabled) {\n        const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n        this.checked = true;\n        this._emitChangeEvent();\n        if (this.radioGroup) {\n          this.radioGroup._controlValueAccessorChangeFn(this.value);\n          if (groupValueChanged) {\n            this.radioGroup._emitChangeEvent();\n          }\n        }\n      }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n      this._onInputInteraction(event);\n      if (!this.disabled || this.disabledInteractive) {\n        // Normally the input should be focused already, but if the click\n        // comes from the touch target, then we might have to focus it ourselves.\n        this._inputElement?.nativeElement.focus();\n      }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n      if (this._disabled !== value) {\n        this._disabled = value;\n        this._changeDetector.markForCheck();\n      }\n    }\n    /** Called when the input is clicked. */\n    _onInputClick = event => {\n      // If the input is disabled while interactive, we need to prevent the\n      // selection from happening in this event handler. Note that even though\n      // this happens on `click` events, the logic applies when the user is\n      // navigating with the keyboard as well. An alternative way of doing\n      // this is by resetting the `checked` state in the `change` callback but\n      // it isn't optimal, because it can allow a pre-checked disabled button\n      // to be un-checked. This approach seems to cover everything.\n      if (this.disabled && this.disabledInteractive) {\n        event.preventDefault();\n      }\n    };\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n      const group = this.radioGroup;\n      let value;\n      // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n      // necessary, because the browser handles the tab order for inputs inside a group automatically,\n      // but we need an explicitly higher tabindex for the selected button in order for things like\n      // the focus trap to pick it up correctly.\n      if (!group || !group.selected || this.disabled) {\n        value = this.tabIndex;\n      } else {\n        value = group.selected === this ? this.tabIndex : -1;\n      }\n      if (value !== this._previousTabIndex) {\n        // We have to set the tabindex directly on the DOM node, because it depends on\n        // the selected state which is prone to \"changed after checked errors\".\n        const input = this._inputElement?.nativeElement;\n        if (input) {\n          input.setAttribute('tabindex', value + '');\n          this._previousTabIndex = value;\n          // Wait for any pending tabindex changes to be applied\n          afterNextRender(() => {\n            queueMicrotask(() => {\n              // The radio group uses a \"selection follows focus\" pattern for tab management, so if this\n              // radio button is currently focused and another radio button in the group becomes\n              // selected, we should move focus to the newly selected radio button to maintain\n              // consistency between the focused and selected states.\n              if (group && group.selected && group.selected !== this && document.activeElement === input) {\n                group.selected?._inputElement.nativeElement.focus();\n                // If this radio button still has focus, the selected one must be disabled. In this\n                // case the radio group as a whole should lose focus.\n                if (document.activeElement === input) {\n                  this._inputElement.nativeElement.blur();\n                }\n              }\n            });\n          }, {\n            injector: this._injector\n          });\n        }\n      }\n    }\n    static ɵfac = function MatRadioButton_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioButton)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      viewQuery: function MatRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rippleTrigger = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 19,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"mat-mdc-radio-disabled\", ctx.disabled)(\"mat-mdc-radio-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [2, \"checked\", \"checked\", booleanAttribute],\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        color: \"color\",\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioButton\"],\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 17,\n      consts: [[\"formField\", \"\"], [\"input\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", \"aria-invalid\", \"false\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 5, 1);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputInteraction($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._rippleTrigger.nativeElement)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled])~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-hover-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-pressed-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-pressed-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-sys-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple>.mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio>.mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display, block)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatRadioButton;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatRadioModule = /*#__PURE__*/(() => {\n  class MatRadioModule {\n    static ɵfac = function MatRadioModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule, MatRadioButton, MatCommonModule]\n    });\n  }\n  return MatRadioModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };", "map": {"version": 3, "names": ["_IdGenerator", "FocusMonitor", "UniqueSelectionDispatcher", "i0", "forwardRef", "InjectionToken", "inject", "ChangeDetectorRef", "EventEmitter", "booleanAttribute", "Directive", "Output", "ContentChildren", "Input", "ElementRef", "NgZone", "Renderer2", "Injector", "HostAttributeToken", "numberAttribute", "afterNextRender", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "_CdkPrivateStyleLoader", "_", "_animationsDisabled", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "_MatInternalFormField", "MatCommonModule", "MatRippleModule", "_c0", "_c1", "_c2", "MatRadioChange", "source", "value", "constructor", "MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatRadioGroup", "multi", "MAT_RADIO_GROUP", "MAT_RADIO_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_RADIO_DEFAULT_OPTIONS_FACTORY", "color", "disabledInteractive", "_changeDetector", "_value", "_name", "getId", "_selected", "_isInitialized", "_labelPosition", "_disabled", "_required", "_buttonChanges", "_controlValueAccessorChangeFn", "onTouched", "change", "_radios", "name", "_updateRadioButtonNames", "labelPosition", "v", "_markRadiosForCheck", "newValue", "_updateSelectedRadioFromValue", "_checkSelectedRadioButton", "checked", "selected", "disabled", "required", "_disabledInteractive", "ngAfterContentInit", "changes", "subscribe", "find", "radio", "ngOnDestroy", "unsubscribe", "_touch", "for<PERSON>ach", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "isAlreadySelected", "_emitChangeEvent", "emit", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ɵfac", "MatRadioGroup_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "contentQueries", "MatRadioGroup_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "MatRadioButton", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "ngDevMode", "_elementRef", "_focusMonitor", "_radioDispatcher", "_defaultOptions", "optional", "_ngZone", "_renderer", "_uniqueId", "_cleanupClick", "id", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON>", "tabIndex", "_checked", "radioGroup", "notify", "_setDisabled", "_color", "inputId", "_removeUniqueSelectionListener", "_previousTabIndex", "_inputElement", "_rippleTrigger", "_noopAnimations", "_injector", "load", "focus", "options", "origin", "focusVia", "nativeElement", "ngOnInit", "listen", "ngDoCheck", "_updateTabIndex", "ngAfterViewInit", "monitor", "<PERSON><PERSON><PERSON><PERSON>", "runOutsideAngular", "_onInputClick", "stopMonitoring", "_isRippleDisabled", "_onInputInteraction", "event", "stopPropagation", "groupValueChanged", "_onTouchTargetClick", "preventDefault", "group", "input", "setAttribute", "queueMicrotask", "document", "activeElement", "blur", "injector", "MatRadioButton_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatRadioButton_Query", "ɵɵviewQuery", "first", "hostVars", "hostBindings", "MatRadioButton_HostBindings", "ɵɵlistener", "MatRadioButton_focus_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "ngContentSelectors", "decls", "vars", "consts", "template", "MatRadioButton_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "MatRadio<PERSON><PERSON><PERSON>_Template_div_click_3_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "ɵɵelementEnd", "MatRadio<PERSON><PERSON>on_Template_input_change_4_listener", "ɵɵelement", "ɵɵprojection", "ɵɵproperty", "ɵɵadvance", "dependencies", "styles", "encapsulation", "changeDetection", "MatRadioModule", "MatRadioModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/radio.mjs"], "sourcesContent": ["import { _Id<PERSON><PERSON><PERSON>, FocusMonitor } from '@angular/cdk/a11y';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, inject, ChangeDetectorRef, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, ElementRef, NgZone, Renderer2, Injector, HostAttributeToken, numberAttribute, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n    source;\n    value;\n    constructor(\n    /** The radio button that emits the change event. */\n    source, \n    /** The value of the radio button. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatRadioGroup),\n    multi: true,\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n    providedIn: 'root',\n    factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        disabledInteractive: false,\n    };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup {\n    _changeDetector = inject(ChangeDetectorRef);\n    /** Selected value for the radio group. */\n    _value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    _name = inject(_IdGenerator).getId('mat-radio-group-');\n    /** The currently selected radio button. Should match value. */\n    _selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    _labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    _disabled = false;\n    /** Whether the radio group is required. */\n    _required = false;\n    /** Subscription to changes in amount of radio buttons. */\n    _buttonChanges;\n    /** The method to be called in order to update ngModel */\n    _controlValueAccessorChangeFn = () => { };\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    onTouched = () => { };\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** Child radio buttons. */\n    _radios;\n    /**\n     * Theme color of the radio buttons in the group. This API is supported in M2\n     * themes only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n        return this._name;\n    }\n    set name(value) {\n        this._name = value;\n        this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition;\n    }\n    set labelPosition(v) {\n        this._labelPosition = v === 'before' ? 'before' : 'after';\n        this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        if (this._value !== newValue) {\n            // Set this before proceeding to ensure no circular loop occurs with selection.\n            this._value = newValue;\n            this._updateSelectedRadioFromValue();\n            this._checkSelectedRadioButton();\n        }\n    }\n    _checkSelectedRadioButton() {\n        if (this._selected && !this._selected.checked) {\n            this._selected.checked = true;\n        }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(selected) {\n        this._selected = selected;\n        this.value = selected ? selected.value : null;\n        this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n        return this._required;\n    }\n    set required(value) {\n        this._required = value;\n        this._markRadiosForCheck();\n    }\n    /** Whether buttons in the group should be interactive while they're disabled. */\n    get disabledInteractive() {\n        return this._disabledInteractive;\n    }\n    set disabledInteractive(value) {\n        this._disabledInteractive = value;\n        this._markRadiosForCheck();\n    }\n    _disabledInteractive = false;\n    constructor() { }\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n        // Mark this component as initialized in AfterContentInit because the initial value can\n        // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n        // NgModel occurs *after* the OnInit of the MatRadioGroup.\n        this._isInitialized = true;\n        // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n        // buttons depends on it. Note that we don't clear the `value`, because the radio button\n        // may be swapped out with a similar one and there are some internal apps that depend on\n        // that behavior.\n        this._buttonChanges = this._radios.changes.subscribe(() => {\n            if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n                this._selected = null;\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n        if (this.onTouched) {\n            this.onTouched();\n        }\n    }\n    _updateRadioButtonNames() {\n        if (this._radios) {\n            this._radios.forEach(radio => {\n                radio.name = this.name;\n                radio._markForCheck();\n            });\n        }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n        // If the value already matches the selected radio, do nothing.\n        const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n        if (this._radios && !isAlreadySelected) {\n            this._selected = null;\n            this._radios.forEach(radio => {\n                radio.checked = this.value === radio.value;\n                if (radio.checked) {\n                    this._selected = radio;\n                }\n            });\n        }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n        if (this._isInitialized) {\n            this.change.emit(new MatRadioChange(this._selected, this._value));\n        }\n    }\n    _markRadiosForCheck() {\n        if (this._radios) {\n            this._radios.forEach(radio => radio._markForCheck());\n        }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n        this.value = value;\n        this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetector.markForCheck();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatRadioGroup, isStandalone: true, selector: \"mat-radio-group\", inputs: { color: \"color\", name: \"name\", labelPosition: \"labelPosition\", value: \"value\", selected: \"selected\", disabled: [\"disabled\", \"disabled\", booleanAttribute], required: [\"required\", \"required\", booleanAttribute], disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, outputs: { change: \"change\" }, host: { attributes: { \"role\": \"radiogroup\" }, classAttribute: \"mat-mdc-radio-group\" }, providers: [\n            MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n            { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n        ], queries: [{ propertyName: \"_radios\", predicate: i0.forwardRef(() => MatRadioButton), descendants: true }], exportAs: [\"matRadioGroup\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-radio-group',\n                    exportAs: 'matRadioGroup',\n                    providers: [\n                        MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n                        { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n                    ],\n                    host: {\n                        'role': 'radiogroup',\n                        'class': 'mat-mdc-radio-group',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { change: [{\n                type: Output\n            }], _radios: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatRadioButton), { descendants: true }]\n            }], color: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass MatRadioButton {\n    _elementRef = inject(ElementRef);\n    _changeDetector = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _radioDispatcher = inject(UniqueSelectionDispatcher);\n    _defaultOptions = inject(MAT_RADIO_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _uniqueId = inject(_IdGenerator).getId('mat-radio-');\n    _cleanupClick;\n    /** The unique ID for the radio button. */\n    id = this._uniqueId;\n    /** Analog to HTML 'name' attribute used to group radios for unique selection. */\n    name;\n    /** Used to set the 'aria-label' attribute on the underlying input element. */\n    ariaLabel;\n    /** The 'aria-labelledby' attribute takes precedence as the element's text alternative. */\n    ariaLabelledby;\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    ariaDescribedby;\n    /** Whether ripples are disabled inside the radio button */\n    disableRipple = false;\n    /** Tabindex of the radio button. */\n    tabIndex = 0;\n    /** Whether this radio button is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (this._checked !== value) {\n            this._checked = value;\n            if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n                this.radioGroup.selected = this;\n            }\n            else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n                // When unchecking the selected radio button, update the selected radio\n                // property on the group.\n                this.radioGroup.selected = null;\n            }\n            if (value) {\n                // Notify all radio buttons with the same name to un-check.\n                this._radioDispatcher.notify(this.id, this.name);\n            }\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** The value of this radio button. */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        if (this._value !== value) {\n            this._value = value;\n            if (this.radioGroup !== null) {\n                if (!this.checked) {\n                    // Update checked when the value changed to match the radio group's value\n                    this.checked = this.radioGroup.value === value;\n                }\n                if (this.checked) {\n                    this.radioGroup.selected = this;\n                }\n            }\n        }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition || (this.radioGroup && this.radioGroup.labelPosition) || 'after';\n    }\n    set labelPosition(value) {\n        this._labelPosition = value;\n    }\n    _labelPosition;\n    /** Whether the radio button is disabled. */\n    get disabled() {\n        return this._disabled || (this.radioGroup !== null && this.radioGroup.disabled);\n    }\n    set disabled(value) {\n        this._setDisabled(value);\n    }\n    /** Whether the radio button is required. */\n    get required() {\n        return this._required || (this.radioGroup && this.radioGroup.required);\n    }\n    set required(value) {\n        if (value !== this._required) {\n            this._changeDetector.markForCheck();\n        }\n        this._required = value;\n    }\n    /**\n     * Theme color of the radio button. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/radio/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        // As per M2 design specifications the selection control radio should use the accent color\n        // palette by default. https://m2.material.io/components/radio-buttons#specs\n        return (this._color ||\n            (this.radioGroup && this.radioGroup.color) ||\n            (this._defaultOptions && this._defaultOptions.color) ||\n            'accent');\n    }\n    set color(newValue) {\n        this._color = newValue;\n    }\n    _color;\n    /** Whether the radio button should remain interactive when it is disabled. */\n    get disabledInteractive() {\n        return (this._disabledInteractive || (this.radioGroup !== null && this.radioGroup.disabledInteractive));\n    }\n    set disabledInteractive(value) {\n        this._disabledInteractive = value;\n    }\n    _disabledInteractive;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    change = new EventEmitter();\n    /** The parent radio group. May or may not be present. */\n    radioGroup;\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether this radio is checked. */\n    _checked = false;\n    /** Whether this radio is disabled. */\n    _disabled;\n    /** Whether this radio is required. */\n    _required;\n    /** Value assigned to this radio. */\n    _value = null;\n    /** Unregister function for _radioDispatcher */\n    _removeUniqueSelectionListener = () => { };\n    /** Previous value of the input's tabindex. */\n    _previousTabIndex;\n    /** The native `<input type=radio>` element */\n    _inputElement;\n    /** Trigger elements for the ripple events. */\n    _rippleTrigger;\n    /** Whether animations are disabled. */\n    _noopAnimations = _animationsDisabled();\n    _injector = inject(Injector);\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const radioGroup = inject(MAT_RADIO_GROUP, { optional: true });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        // Assertions. Ideally these should be stripped out by the compiler.\n        // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n        this.radioGroup = radioGroup;\n        this._disabledInteractive = this._defaultOptions?.disabledInteractive ?? false;\n        if (tabIndex) {\n            this.tabIndex = numberAttribute(tabIndex, 0);\n        }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._inputElement, origin, options);\n        }\n        else {\n            this._inputElement.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n        // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n        // update radio button's status\n        this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n        if (this.radioGroup) {\n            // If the radio is inside a radio group, determine if it should be checked\n            this.checked = this.radioGroup.value === this._value;\n            if (this.checked) {\n                this.radioGroup.selected = this;\n            }\n            // Copy name from parent radio group\n            this.name = this.radioGroup.name;\n        }\n        this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n            if (id !== this.id && name === this.name) {\n                this.checked = false;\n            }\n        });\n    }\n    ngDoCheck() {\n        this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n        this._updateTabIndex();\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n            if (!focusOrigin && this.radioGroup) {\n                this.radioGroup._touch();\n            }\n        });\n        // We bind this outside of the zone, because:\n        // 1. Its logic is completely DOM-related so we can avoid some change detections.\n        // 2. There appear to be some internal tests that break when this triggers a change detection.\n        this._ngZone.runOutsideAngular(() => {\n            this._cleanupClick = this._renderer.listen(this._inputElement.nativeElement, 'click', this._onInputClick);\n        });\n    }\n    ngOnDestroy() {\n        this._cleanupClick?.();\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n        this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n        if (!this.checked && !this.disabled) {\n            const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n            this.checked = true;\n            this._emitChangeEvent();\n            if (this.radioGroup) {\n                this.radioGroup._controlValueAccessorChangeFn(this.value);\n                if (groupValueChanged) {\n                    this.radioGroup._emitChangeEvent();\n                }\n            }\n        }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n        this._onInputInteraction(event);\n        if (!this.disabled || this.disabledInteractive) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement?.nativeElement.focus();\n        }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n        if (this._disabled !== value) {\n            this._disabled = value;\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** Called when the input is clicked. */\n    _onInputClick = (event) => {\n        // If the input is disabled while interactive, we need to prevent the\n        // selection from happening in this event handler. Note that even though\n        // this happens on `click` events, the logic applies when the user is\n        // navigating with the keyboard as well. An alternative way of doing\n        // this is by resetting the `checked` state in the `change` callback but\n        // it isn't optimal, because it can allow a pre-checked disabled button\n        // to be un-checked. This approach seems to cover everything.\n        if (this.disabled && this.disabledInteractive) {\n            event.preventDefault();\n        }\n    };\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n        const group = this.radioGroup;\n        let value;\n        // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n        // necessary, because the browser handles the tab order for inputs inside a group automatically,\n        // but we need an explicitly higher tabindex for the selected button in order for things like\n        // the focus trap to pick it up correctly.\n        if (!group || !group.selected || this.disabled) {\n            value = this.tabIndex;\n        }\n        else {\n            value = group.selected === this ? this.tabIndex : -1;\n        }\n        if (value !== this._previousTabIndex) {\n            // We have to set the tabindex directly on the DOM node, because it depends on\n            // the selected state which is prone to \"changed after checked errors\".\n            const input = this._inputElement?.nativeElement;\n            if (input) {\n                input.setAttribute('tabindex', value + '');\n                this._previousTabIndex = value;\n                // Wait for any pending tabindex changes to be applied\n                afterNextRender(() => {\n                    queueMicrotask(() => {\n                        // The radio group uses a \"selection follows focus\" pattern for tab management, so if this\n                        // radio button is currently focused and another radio button in the group becomes\n                        // selected, we should move focus to the newly selected radio button to maintain\n                        // consistency between the focused and selected states.\n                        if (group &&\n                            group.selected &&\n                            group.selected !== this &&\n                            document.activeElement === input) {\n                            group.selected?._inputElement.nativeElement.focus();\n                            // If this radio button still has focus, the selected one must be disabled. In this\n                            // case the radio group as a whole should lose focus.\n                            if (document.activeElement === input) {\n                                this._inputElement.nativeElement.blur();\n                            }\n                        }\n                    });\n                }, { injector: this._injector });\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioButton, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatRadioButton, isStandalone: true, selector: \"mat-radio-button\", inputs: { id: \"id\", name: \"name\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], checked: [\"checked\", \"checked\", booleanAttribute], value: \"value\", labelPosition: \"labelPosition\", disabled: [\"disabled\", \"disabled\", booleanAttribute], required: [\"required\", \"required\", booleanAttribute], color: \"color\", disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, outputs: { change: \"change\" }, host: { listeners: { \"focus\": \"_inputElement.nativeElement.focus()\" }, properties: { \"attr.id\": \"id\", \"class.mat-primary\": \"color === \\\"primary\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.mat-mdc-radio-checked\": \"checked\", \"class.mat-mdc-radio-disabled\": \"disabled\", \"class.mat-mdc-radio-disabled-interactive\": \"disabledInteractive\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" }, classAttribute: \"mat-mdc-radio-button\" }, viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_rippleTrigger\", first: true, predicate: [\"formField\"], descendants: true, read: ElementRef, static: true }], exportAs: [\"matRadioButton\"], ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <!--\\n      Note that we set `aria-invalid=\\\"false\\\"` on the input, because otherwise some screen readers\\n      will read out \\\"required, invalid data\\\" for each radio button that hasn't been checked.\\n      An alternate approach is to use `aria-required` instead of `required`, however we have an\\n      internal check which enforces that elements marked as `aria-required` also have the `required`\\n      attribute which ends up re-introducing the issue for us.\\n    -->\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           aria-invalid=\\\"false\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled])~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-hover-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-pressed-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-pressed-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-sys-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple>.mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio>.mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display, block)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioButton, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-radio-button', host: {\n                        'class': 'mat-mdc-radio-button',\n                        '[attr.id]': 'id',\n                        '[class.mat-primary]': 'color === \"primary\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.mat-mdc-radio-checked]': 'checked',\n                        '[class.mat-mdc-radio-disabled]': 'disabled',\n                        '[class.mat-mdc-radio-disabled-interactive]': 'disabledInteractive',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        // Needs to be removed since it causes some a11y issues (see #21266).\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                        // Note: under normal conditions focus shouldn't land on this element, however it may be\n                        // programmatically set, for example inside of a focus trap, in this case we want to forward\n                        // the focus to the native element.\n                        '(focus)': '_inputElement.nativeElement.focus()',\n                    }, exportAs: 'matRadioButton', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <!--\\n      Note that we set `aria-invalid=\\\"false\\\"` on the input, because otherwise some screen readers\\n      will read out \\\"required, invalid data\\\" for each radio button that hasn't been checked.\\n      An alternate approach is to use `aria-required` instead of `required`, however we have an\\n      internal check which enforces that elements marked as `aria-required` also have the `required`\\n      attribute which ends up re-introducing the issue for us.\\n    -->\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           aria-invalid=\\\"false\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:not([disabled])~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-hover-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-pressed-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active>.mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-pressed-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-sys-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple>.mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio>.mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display, block)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { id: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _rippleTrigger: [{\n                type: ViewChild,\n                args: ['formField', { read: ElementRef, static: true }]\n            }] } });\n\nclass MatRadioModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioModule, imports: [MatCommonModule, MatRippleModule, MatRadioGroup, MatRadioButton], exports: [MatCommonModule, MatRadioGroup, MatRadioButton] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioModule, imports: [MatCommonModule, MatRippleModule, MatRadioButton, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRadioModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n                    exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n                }]\n        }] });\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC3U,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASD,CAAC,IAAIE,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASJ,CAAC,IAAIK,qBAAqB,QAAQ,oCAAoC;AAC/E,SAASF,CAAC,IAAIG,eAAe,QAAQ,8BAA8B;AACnE,SAASH,CAAC,IAAII,eAAe,QAAQ,sBAAsB;AAC3D,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,mBAAmB;;AAE1B;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,cAAc,CAAC;EACjBC,MAAM;EACNC,KAAK;EACLC,WAAWA,CACX;EACAF,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,sCAAsC,GAAG;EAC3CC,OAAO,EAAElB,iBAAiB;EAC1BmB,WAAW,eAAEzC,UAAU,CAAC,MAAM0C,aAAa,CAAC;EAC5CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,gBAAG,IAAI3C,cAAc,CAAC,eAAe,CAAC;AAC3D,MAAM4C,yBAAyB,gBAAG,IAAI5C,cAAc,CAAC,2BAA2B,EAAE;EAC9E6C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,iCAAiCA,CAAA,EAAG;EACzC,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,mBAAmB,EAAE;EACzB,CAAC;AACL;AACA;AACA;AACA;AAFA,IAGMR,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBS,eAAe,GAAGjD,MAAM,CAACC,iBAAiB,CAAC;IAC3C;IACAiD,MAAM,GAAG,IAAI;IACb;IACAC,KAAK,GAAGnD,MAAM,CAACN,YAAY,CAAC,CAAC0D,KAAK,CAAC,kBAAkB,CAAC;IACtD;IACAC,SAAS,GAAG,IAAI;IAChB;IACAC,cAAc,GAAG,KAAK;IACtB;IACAC,cAAc,GAAG,OAAO;IACxB;IACAC,SAAS,GAAG,KAAK;IACjB;IACAC,SAAS,GAAG,KAAK;IACjB;IACAC,cAAc;IACd;IACAC,6BAA6B,GAAGA,CAAA,KAAM,CAAE,CAAC;IACzC;AACJ;AACA;AACA;IACIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;IACrB;AACJ;AACA;AACA;AACA;IACIC,MAAM,GAAG,IAAI3D,YAAY,CAAC,CAAC;IAC3B;IACA4D,OAAO;IACP;AACJ;AACA;AACA;AACA;AACA;AACA;IACIf,KAAK;IACL;IACA,IAAIgB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACZ,KAAK;IACrB;IACA,IAAIY,IAAIA,CAAC5B,KAAK,EAAE;MACZ,IAAI,CAACgB,KAAK,GAAGhB,KAAK;MAClB,IAAI,CAAC6B,uBAAuB,CAAC,CAAC;IAClC;IACA;IACA,IAAIC,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACV,cAAc;IAC9B;IACA,IAAIU,aAAaA,CAACC,CAAC,EAAE;MACjB,IAAI,CAACX,cAAc,GAAGW,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACzD,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIhC,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACe,MAAM;IACtB;IACA,IAAIf,KAAKA,CAACiC,QAAQ,EAAE;MAChB,IAAI,IAAI,CAAClB,MAAM,KAAKkB,QAAQ,EAAE;QAC1B;QACA,IAAI,CAAClB,MAAM,GAAGkB,QAAQ;QACtB,IAAI,CAACC,6BAA6B,CAAC,CAAC;QACpC,IAAI,CAACC,yBAAyB,CAAC,CAAC;MACpC;IACJ;IACAA,yBAAyBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACjB,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACkB,OAAO,EAAE;QAC3C,IAAI,CAAClB,SAAS,CAACkB,OAAO,GAAG,IAAI;MACjC;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACnB,SAAS;IACzB;IACA,IAAImB,QAAQA,CAACA,QAAQ,EAAE;MACnB,IAAI,CAACnB,SAAS,GAAGmB,QAAQ;MACzB,IAAI,CAACrC,KAAK,GAAGqC,QAAQ,GAAGA,QAAQ,CAACrC,KAAK,GAAG,IAAI;MAC7C,IAAI,CAACmC,yBAAyB,CAAC,CAAC;IACpC;IACA;IACA,IAAIG,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACjB,SAAS;IACzB;IACA,IAAIiB,QAAQA,CAACtC,KAAK,EAAE;MAChB,IAAI,CAACqB,SAAS,GAAGrB,KAAK;MACtB,IAAI,CAACgC,mBAAmB,CAAC,CAAC;IAC9B;IACA;IACA,IAAIO,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACjB,SAAS;IACzB;IACA,IAAIiB,QAAQA,CAACvC,KAAK,EAAE;MAChB,IAAI,CAACsB,SAAS,GAAGtB,KAAK;MACtB,IAAI,CAACgC,mBAAmB,CAAC,CAAC;IAC9B;IACA;IACA,IAAInB,mBAAmBA,CAAA,EAAG;MACtB,OAAO,IAAI,CAAC2B,oBAAoB;IACpC;IACA,IAAI3B,mBAAmBA,CAACb,KAAK,EAAE;MAC3B,IAAI,CAACwC,oBAAoB,GAAGxC,KAAK;MACjC,IAAI,CAACgC,mBAAmB,CAAC,CAAC;IAC9B;IACAQ,oBAAoB,GAAG,KAAK;IAC5BvC,WAAWA,CAAA,EAAG,CAAE;IAChB;AACJ;AACA;AACA;IACIwC,kBAAkBA,CAAA,EAAG;MACjB;MACA;MACA;MACA,IAAI,CAACtB,cAAc,GAAG,IAAI;MAC1B;MACA;MACA;MACA;MACA,IAAI,CAACI,cAAc,GAAG,IAAI,CAACI,OAAO,CAACe,OAAO,CAACC,SAAS,CAAC,MAAM;QACvD,IAAI,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAACV,OAAO,CAACiB,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAACR,QAAQ,CAAC,EAAE;UACvE,IAAI,CAACnB,SAAS,GAAG,IAAI;QACzB;MACJ,CAAC,CAAC;IACN;IACA4B,WAAWA,CAAA,EAAG;MACV,IAAI,CAACvB,cAAc,EAAEwB,WAAW,CAAC,CAAC;IACtC;IACA;AACJ;AACA;AACA;IACIC,MAAMA,CAAA,EAAG;MACL,IAAI,IAAI,CAACvB,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC,CAAC;MACpB;IACJ;IACAI,uBAAuBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACF,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACsB,OAAO,CAACJ,KAAK,IAAI;UAC1BA,KAAK,CAACjB,IAAI,GAAG,IAAI,CAACA,IAAI;UACtBiB,KAAK,CAACK,aAAa,CAAC,CAAC;QACzB,CAAC,CAAC;MACN;IACJ;IACA;IACAhB,6BAA6BA,CAAA,EAAG;MAC5B;MACA,MAAMiB,iBAAiB,GAAG,IAAI,CAACjC,SAAS,KAAK,IAAI,IAAI,IAAI,CAACA,SAAS,CAAClB,KAAK,KAAK,IAAI,CAACe,MAAM;MACzF,IAAI,IAAI,CAACY,OAAO,IAAI,CAACwB,iBAAiB,EAAE;QACpC,IAAI,CAACjC,SAAS,GAAG,IAAI;QACrB,IAAI,CAACS,OAAO,CAACsB,OAAO,CAACJ,KAAK,IAAI;UAC1BA,KAAK,CAACT,OAAO,GAAG,IAAI,CAACpC,KAAK,KAAK6C,KAAK,CAAC7C,KAAK;UAC1C,IAAI6C,KAAK,CAACT,OAAO,EAAE;YACf,IAAI,CAAClB,SAAS,GAAG2B,KAAK;UAC1B;QACJ,CAAC,CAAC;MACN;IACJ;IACA;IACAO,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACjC,cAAc,EAAE;QACrB,IAAI,CAACO,MAAM,CAAC2B,IAAI,CAAC,IAAIvD,cAAc,CAAC,IAAI,CAACoB,SAAS,EAAE,IAAI,CAACH,MAAM,CAAC,CAAC;MACrE;IACJ;IACAiB,mBAAmBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACL,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACsB,OAAO,CAACJ,KAAK,IAAIA,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC;MACxD;IACJ;IACA;AACJ;AACA;AACA;IACII,UAAUA,CAACtD,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACc,eAAe,CAACyC,YAAY,CAAC,CAAC;IACvC;IACA;AACJ;AACA;AACA;AACA;IACIC,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACjC,6BAA6B,GAAGiC,EAAE;IAC3C;IACA;AACJ;AACA;AACA;AACA;IACIC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAAChC,SAAS,GAAGgC,EAAE;IACvB;IACA;AACJ;AACA;AACA;IACIE,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAACtB,QAAQ,GAAGsB,UAAU;MAC1B,IAAI,CAAC9C,eAAe,CAACyC,YAAY,CAAC,CAAC;IACvC;IACA,OAAOM,IAAI,YAAAC,sBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwF1D,aAAa;IAAA;IAChH,OAAO2D,IAAI,kBAD8EtG,EAAE,CAAAuG,iBAAA;MAAAC,IAAA,EACJ7D,aAAa;MAAA8D,SAAA;MAAAC,cAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UADX5G,EAAE,CAAA+G,cAAA,CAAAD,QAAA,EAIhBE,cAAc;QAAA;QAAA,IAAAJ,EAAA;UAAA,IAAAK,EAAA;UAJAjH,EAAE,CAAAkH,cAAA,CAAAD,EAAA,GAAFjH,EAAE,CAAAmH,WAAA,QAAAN,GAAA,CAAA5C,OAAA,GAAAgD,EAAA;QAAA;MAAA;MAAAG,SAAA,WAC4a,YAAY;MAAAC,MAAA;QAAAnE,KAAA;QAAAgB,IAAA;QAAAE,aAAA;QAAA9B,KAAA;QAAAqC,QAAA;QAAAC,QAAA,8BAA3OtE,gBAAgB;QAAAuE,QAAA,8BAAsCvE,gBAAgB;QAAA6C,mBAAA,oDAAuE7C,gBAAgB;MAAA;MAAAgH,OAAA;QAAAtD,MAAA;MAAA;MAAAuD,QAAA;MAAAC,QAAA,GAD5WxH,EAAE,CAAAyH,kBAAA,CACgf,CACnkBjF,sCAAsC,EACtC;QAAEC,OAAO,EAAEI,eAAe;QAAEH,WAAW,EAAEC;MAAc,CAAC,CAC3D;IAAA;EACT;EAAC,OA1NKA,aAAa;AAAA;AA2NnB;EAAA,QAAA+E,SAAA,oBAAAA,SAAA;AAAA;AAsCoB,IACdV,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBW,WAAW,GAAGxH,MAAM,CAACQ,UAAU,CAAC;IAChCyC,eAAe,GAAGjD,MAAM,CAACC,iBAAiB,CAAC;IAC3CwH,aAAa,GAAGzH,MAAM,CAACL,YAAY,CAAC;IACpC+H,gBAAgB,GAAG1H,MAAM,CAACJ,yBAAyB,CAAC;IACpD+H,eAAe,GAAG3H,MAAM,CAAC2C,yBAAyB,EAAE;MAChDiF,QAAQ,EAAE;IACd,CAAC,CAAC;IACFC,OAAO,GAAG7H,MAAM,CAACS,MAAM,CAAC;IACxBqH,SAAS,GAAG9H,MAAM,CAACU,SAAS,CAAC;IAC7BqH,SAAS,GAAG/H,MAAM,CAACN,YAAY,CAAC,CAAC0D,KAAK,CAAC,YAAY,CAAC;IACpD4E,aAAa;IACb;IACAC,EAAE,GAAG,IAAI,CAACF,SAAS;IACnB;IACAhE,IAAI;IACJ;IACAmE,SAAS;IACT;IACAC,cAAc;IACd;IACAC,eAAe;IACf;IACAC,aAAa,GAAG,KAAK;IACrB;IACAC,QAAQ,GAAG,CAAC;IACZ;IACA,IAAI/D,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACgE,QAAQ;IACxB;IACA,IAAIhE,OAAOA,CAACpC,KAAK,EAAE;MACf,IAAI,IAAI,CAACoG,QAAQ,KAAKpG,KAAK,EAAE;QACzB,IAAI,CAACoG,QAAQ,GAAGpG,KAAK;QACrB,IAAIA,KAAK,IAAI,IAAI,CAACqG,UAAU,IAAI,IAAI,CAACA,UAAU,CAACrG,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;UAClE,IAAI,CAACqG,UAAU,CAAChE,QAAQ,GAAG,IAAI;QACnC,CAAC,MACI,IAAI,CAACrC,KAAK,IAAI,IAAI,CAACqG,UAAU,IAAI,IAAI,CAACA,UAAU,CAACrG,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;UACxE;UACA;UACA,IAAI,CAACqG,UAAU,CAAChE,QAAQ,GAAG,IAAI;QACnC;QACA,IAAIrC,KAAK,EAAE;UACP;UACA,IAAI,CAACuF,gBAAgB,CAACe,MAAM,CAAC,IAAI,CAACR,EAAE,EAAE,IAAI,CAAClE,IAAI,CAAC;QACpD;QACA,IAAI,CAACd,eAAe,CAACyC,YAAY,CAAC,CAAC;MACvC;IACJ;IACA;IACA,IAAIvD,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACe,MAAM;IACtB;IACA,IAAIf,KAAKA,CAACA,KAAK,EAAE;MACb,IAAI,IAAI,CAACe,MAAM,KAAKf,KAAK,EAAE;QACvB,IAAI,CAACe,MAAM,GAAGf,KAAK;QACnB,IAAI,IAAI,CAACqG,UAAU,KAAK,IAAI,EAAE;UAC1B,IAAI,CAAC,IAAI,CAACjE,OAAO,EAAE;YACf;YACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACiE,UAAU,CAACrG,KAAK,KAAKA,KAAK;UAClD;UACA,IAAI,IAAI,CAACoC,OAAO,EAAE;YACd,IAAI,CAACiE,UAAU,CAAChE,QAAQ,GAAG,IAAI;UACnC;QACJ;MACJ;IACJ;IACA;IACA,IAAIP,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACV,cAAc,IAAK,IAAI,CAACiF,UAAU,IAAI,IAAI,CAACA,UAAU,CAACvE,aAAc,IAAI,OAAO;IAC/F;IACA,IAAIA,aAAaA,CAAC9B,KAAK,EAAE;MACrB,IAAI,CAACoB,cAAc,GAAGpB,KAAK;IAC/B;IACAoB,cAAc;IACd;IACA,IAAIkB,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACjB,SAAS,IAAK,IAAI,CAACgF,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,CAAC/D,QAAS;IACnF;IACA,IAAIA,QAAQA,CAACtC,KAAK,EAAE;MAChB,IAAI,CAACuG,YAAY,CAACvG,KAAK,CAAC;IAC5B;IACA;IACA,IAAIuC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACjB,SAAS,IAAK,IAAI,CAAC+E,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC9D,QAAS;IAC1E;IACA,IAAIA,QAAQA,CAACvC,KAAK,EAAE;MAChB,IAAIA,KAAK,KAAK,IAAI,CAACsB,SAAS,EAAE;QAC1B,IAAI,CAACR,eAAe,CAACyC,YAAY,CAAC,CAAC;MACvC;MACA,IAAI,CAACjC,SAAS,GAAGtB,KAAK;IAC1B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIY,KAAKA,CAAA,EAAG;MACR;MACA;MACA,OAAQ,IAAI,CAAC4F,MAAM,IACd,IAAI,CAACH,UAAU,IAAI,IAAI,CAACA,UAAU,CAACzF,KAAM,IACzC,IAAI,CAAC4E,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC5E,KAAM,IACpD,QAAQ;IAChB;IACA,IAAIA,KAAKA,CAACqB,QAAQ,EAAE;MAChB,IAAI,CAACuE,MAAM,GAAGvE,QAAQ;IAC1B;IACAuE,MAAM;IACN;IACA,IAAI3F,mBAAmBA,CAAA,EAAG;MACtB,OAAQ,IAAI,CAAC2B,oBAAoB,IAAK,IAAI,CAAC6D,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,CAACxF,mBAAoB;IAC1G;IACA,IAAIA,mBAAmBA,CAACb,KAAK,EAAE;MAC3B,IAAI,CAACwC,oBAAoB,GAAGxC,KAAK;IACrC;IACAwC,oBAAoB;IACpB;AACJ;AACA;AACA;AACA;IACId,MAAM,GAAG,IAAI3D,YAAY,CAAC,CAAC;IAC3B;IACAsI,UAAU;IACV;IACA,IAAII,OAAOA,CAAA,EAAG;MACV,OAAO,GAAG,IAAI,CAACX,EAAE,IAAI,IAAI,CAACF,SAAS,QAAQ;IAC/C;IACA;IACAQ,QAAQ,GAAG,KAAK;IAChB;IACA/E,SAAS;IACT;IACAC,SAAS;IACT;IACAP,MAAM,GAAG,IAAI;IACb;IACA2F,8BAA8B,GAAGA,CAAA,KAAM,CAAE,CAAC;IAC1C;IACAC,iBAAiB;IACjB;IACAC,aAAa;IACb;IACAC,cAAc;IACd;IACAC,eAAe,GAAG1H,mBAAmB,CAAC,CAAC;IACvC2H,SAAS,GAAGlJ,MAAM,CAACW,QAAQ,CAAC;IAC5ByB,WAAWA,CAAA,EAAG;MACVpC,MAAM,CAACqB,sBAAsB,CAAC,CAAC8H,IAAI,CAAC3H,uBAAuB,CAAC;MAC5D,MAAMgH,UAAU,GAAGxI,MAAM,CAAC0C,eAAe,EAAE;QAAEkF,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC9D,MAAMU,QAAQ,GAAGtI,MAAM,CAAC,IAAIY,kBAAkB,CAAC,UAAU,CAAC,EAAE;QAAEgH,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC/E;MACA;MACA,IAAI,CAACY,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAAC7D,oBAAoB,GAAG,IAAI,CAACgD,eAAe,EAAE3E,mBAAmB,IAAI,KAAK;MAC9E,IAAIsF,QAAQ,EAAE;QACV,IAAI,CAACA,QAAQ,GAAGzH,eAAe,CAACyH,QAAQ,EAAE,CAAC,CAAC;MAChD;IACJ;IACA;IACAc,KAAKA,CAACC,OAAO,EAAEC,MAAM,EAAE;MACnB,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC7B,aAAa,CAAC8B,QAAQ,CAAC,IAAI,CAACR,aAAa,EAAEO,MAAM,EAAED,OAAO,CAAC;MACpE,CAAC,MACI;QACD,IAAI,CAACN,aAAa,CAACS,aAAa,CAACJ,KAAK,CAACC,OAAO,CAAC;MACnD;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIhE,aAAaA,CAAA,EAAG;MACZ;MACA;MACA,IAAI,CAACpC,eAAe,CAACyC,YAAY,CAAC,CAAC;IACvC;IACA+D,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAACjB,UAAU,EAAE;QACjB;QACA,IAAI,CAACjE,OAAO,GAAG,IAAI,CAACiE,UAAU,CAACrG,KAAK,KAAK,IAAI,CAACe,MAAM;QACpD,IAAI,IAAI,CAACqB,OAAO,EAAE;UACd,IAAI,CAACiE,UAAU,CAAChE,QAAQ,GAAG,IAAI;QACnC;QACA;QACA,IAAI,CAACT,IAAI,GAAG,IAAI,CAACyE,UAAU,CAACzE,IAAI;MACpC;MACA,IAAI,CAAC8E,8BAA8B,GAAG,IAAI,CAACnB,gBAAgB,CAACgC,MAAM,CAAC,CAACzB,EAAE,EAAElE,IAAI,KAAK;QAC7E,IAAIkE,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIlE,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;UACtC,IAAI,CAACQ,OAAO,GAAG,KAAK;QACxB;MACJ,CAAC,CAAC;IACN;IACAoF,SAASA,CAAA,EAAG;MACR,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,CAACD,eAAe,CAAC,CAAC;MACtB,IAAI,CAACnC,aAAa,CAACqC,OAAO,CAAC,IAAI,CAACtC,WAAW,EAAE,IAAI,CAAC,CAAC1C,SAAS,CAACiF,WAAW,IAAI;QACxE,IAAI,CAACA,WAAW,IAAI,IAAI,CAACvB,UAAU,EAAE;UACjC,IAAI,CAACA,UAAU,CAACrD,MAAM,CAAC,CAAC;QAC5B;MACJ,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAI,CAAC0C,OAAO,CAACmC,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACF,SAAS,CAAC4B,MAAM,CAAC,IAAI,CAACX,aAAa,CAACS,aAAa,EAAE,OAAO,EAAE,IAAI,CAACS,aAAa,CAAC;MAC7G,CAAC,CAAC;IACN;IACAhF,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC+C,aAAa,GAAG,CAAC;MACtB,IAAI,CAACP,aAAa,CAACyC,cAAc,CAAC,IAAI,CAAC1C,WAAW,CAAC;MACnD,IAAI,CAACqB,8BAA8B,CAAC,CAAC;IACzC;IACA;IACAtD,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAAC1B,MAAM,CAAC2B,IAAI,CAAC,IAAIvD,cAAc,CAAC,IAAI,EAAE,IAAI,CAACiB,MAAM,CAAC,CAAC;IAC3D;IACAiH,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAAC9B,aAAa,IAAI,IAAI,CAAC5D,QAAQ;IAC9C;IACA;IACA2F,mBAAmBA,CAACC,KAAK,EAAE;MACvB;MACA;MACA;MACAA,KAAK,CAACC,eAAe,CAAC,CAAC;MACvB,IAAI,CAAC,IAAI,CAAC/F,OAAO,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;QACjC,MAAM8F,iBAAiB,GAAG,IAAI,CAAC/B,UAAU,IAAI,IAAI,CAACrG,KAAK,KAAK,IAAI,CAACqG,UAAU,CAACrG,KAAK;QACjF,IAAI,CAACoC,OAAO,GAAG,IAAI;QACnB,IAAI,CAACgB,gBAAgB,CAAC,CAAC;QACvB,IAAI,IAAI,CAACiD,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,CAAC7E,6BAA6B,CAAC,IAAI,CAACxB,KAAK,CAAC;UACzD,IAAIoI,iBAAiB,EAAE;YACnB,IAAI,CAAC/B,UAAU,CAACjD,gBAAgB,CAAC,CAAC;UACtC;QACJ;MACJ;IACJ;IACA;IACAiF,mBAAmBA,CAACH,KAAK,EAAE;MACvB,IAAI,CAACD,mBAAmB,CAACC,KAAK,CAAC;MAC/B,IAAI,CAAC,IAAI,CAAC5F,QAAQ,IAAI,IAAI,CAACzB,mBAAmB,EAAE;QAC5C;QACA;QACA,IAAI,CAAC+F,aAAa,EAAES,aAAa,CAACJ,KAAK,CAAC,CAAC;MAC7C;IACJ;IACA;IACAV,YAAYA,CAACvG,KAAK,EAAE;MAChB,IAAI,IAAI,CAACqB,SAAS,KAAKrB,KAAK,EAAE;QAC1B,IAAI,CAACqB,SAAS,GAAGrB,KAAK;QACtB,IAAI,CAACc,eAAe,CAACyC,YAAY,CAAC,CAAC;MACvC;IACJ;IACA;IACAuE,aAAa,GAAII,KAAK,IAAK;MACvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC5F,QAAQ,IAAI,IAAI,CAACzB,mBAAmB,EAAE;QAC3CqH,KAAK,CAACI,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACAb,eAAeA,CAAA,EAAG;MACd,MAAMc,KAAK,GAAG,IAAI,CAAClC,UAAU;MAC7B,IAAIrG,KAAK;MACT;MACA;MACA;MACA;MACA,IAAI,CAACuI,KAAK,IAAI,CAACA,KAAK,CAAClG,QAAQ,IAAI,IAAI,CAACC,QAAQ,EAAE;QAC5CtC,KAAK,GAAG,IAAI,CAACmG,QAAQ;MACzB,CAAC,MACI;QACDnG,KAAK,GAAGuI,KAAK,CAAClG,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC8D,QAAQ,GAAG,CAAC,CAAC;MACxD;MACA,IAAInG,KAAK,KAAK,IAAI,CAAC2G,iBAAiB,EAAE;QAClC;QACA;QACA,MAAM6B,KAAK,GAAG,IAAI,CAAC5B,aAAa,EAAES,aAAa;QAC/C,IAAImB,KAAK,EAAE;UACPA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAEzI,KAAK,GAAG,EAAE,CAAC;UAC1C,IAAI,CAAC2G,iBAAiB,GAAG3G,KAAK;UAC9B;UACArB,eAAe,CAAC,MAAM;YAClB+J,cAAc,CAAC,MAAM;cACjB;cACA;cACA;cACA;cACA,IAAIH,KAAK,IACLA,KAAK,CAAClG,QAAQ,IACdkG,KAAK,CAAClG,QAAQ,KAAK,IAAI,IACvBsG,QAAQ,CAACC,aAAa,KAAKJ,KAAK,EAAE;gBAClCD,KAAK,CAAClG,QAAQ,EAAEuE,aAAa,CAACS,aAAa,CAACJ,KAAK,CAAC,CAAC;gBACnD;gBACA;gBACA,IAAI0B,QAAQ,CAACC,aAAa,KAAKJ,KAAK,EAAE;kBAClC,IAAI,CAAC5B,aAAa,CAACS,aAAa,CAACwB,IAAI,CAAC,CAAC;gBAC3C;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,EAAE;YAAEC,QAAQ,EAAE,IAAI,CAAC/B;UAAU,CAAC,CAAC;QACpC;MACJ;IACJ;IACA,OAAOlD,IAAI,YAAAkF,uBAAAhF,iBAAA;MAAA,YAAAA,iBAAA,IAAwFW,cAAc;IAAA;IACjH,OAAOsE,IAAI,kBA1W8EtL,EAAE,CAAAuL,iBAAA;MAAA/E,IAAA,EA0WJQ,cAAc;MAAAP,SAAA;MAAA+E,SAAA,WAAAC,qBAAA7E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1WZ5G,EAAE,CAAA0L,WAAA,CAAAzJ,GAAA;UAAFjC,EAAE,CAAA0L,WAAA,CAAAxJ,GAAA,KA0WihDvB,UAAU;QAAA;QAAA,IAAAiG,EAAA;UAAA,IAAAK,EAAA;UA1W7hDjH,EAAE,CAAAkH,cAAA,CAAAD,EAAA,GAAFjH,EAAE,CAAAmH,WAAA,QAAAN,GAAA,CAAAqC,aAAA,GAAAjC,EAAA,CAAA0E,KAAA;UAAF3L,EAAE,CAAAkH,cAAA,CAAAD,EAAA,GAAFjH,EAAE,CAAAmH,WAAA,QAAAN,GAAA,CAAAsC,cAAA,GAAAlC,EAAA,CAAA0E,KAAA;QAAA;MAAA;MAAAvE,SAAA;MAAAwE,QAAA;MAAAC,YAAA,WAAAC,4BAAAlF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5G,EAAE,CAAA+L,UAAA,mBAAAC,wCAAA;YAAA,OA0WJnF,GAAA,CAAAqC,aAAA,CAAAS,aAAA,CAAAJ,KAAA,CAAkC,CAAC;UAAA,CAAtB,CAAC;QAAA;QAAA,IAAA3C,EAAA;UA1WZ5G,EAAE,CAAAiM,WAAA,OAAApF,GAAA,CAAAuB,EAAA,cA0WJ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;UA1WFpI,EAAE,CAAAkM,WAAA,gBAAArF,GAAA,CAAA3D,KAAA,KA0WM,SAAG,CAAC,eAAA2D,GAAA,CAAA3D,KAAA,KAAJ,QAAG,CAAC,aAAA2D,GAAA,CAAA3D,KAAA,KAAJ,MAAG,CAAC,0BAAA2D,GAAA,CAAAnC,OAAD,CAAC,2BAAAmC,GAAA,CAAAjC,QAAD,CAAC,uCAAAiC,GAAA,CAAA1D,mBAAD,CAAC,4BAAA0D,GAAA,CAAAuC,eAAD,CAAC;QAAA;MAAA;MAAA/B,MAAA;QAAAe,EAAA;QAAAlE,IAAA;QAAAmE,SAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAC,aAAA,wCAAiSlI,gBAAgB;QAAAmI,QAAA,8BAAuCnG,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGtB,eAAe,CAACsB,KAAK,CAAE;QAAAoC,OAAA,4BAAmCpE,gBAAgB;QAAAgC,KAAA;QAAA8B,aAAA;QAAAQ,QAAA,8BAAsFtE,gBAAgB;QAAAuE,QAAA,8BAAsCvE,gBAAgB;QAAA4C,KAAA;QAAAC,mBAAA,oDAAuF7C,gBAAgB;MAAA;MAAAgH,OAAA;QAAAtD,MAAA;MAAA;MAAAuD,QAAA;MAAA4E,kBAAA,EAAAhK,GAAA;MAAAiK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAA5F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA6F,GAAA,GA1WhtBzM,EAAE,CAAA0M,gBAAA;UAAF1M,EAAE,CAAA2M,eAAA;UAAF3M,EAAE,CAAA4M,cAAA,eA0W8qD,CAAC,YAAqE,CAAC,YAAuJ,CAAC;UA1W/4D5M,EAAE,CAAA+L,UAAA,mBAAAc,6CAAAC,MAAA;YAAF9M,EAAE,CAAA+M,aAAA,CAAAN,GAAA;YAAA,OAAFzM,EAAE,CAAAgN,WAAA,CA0W+2DnG,GAAA,CAAA8D,mBAAA,CAAAmC,MAA0B,CAAC;UAAA,CAAC,CAAC;UA1W94D9M,EAAE,CAAAiN,YAAA,CA0Wk5D,CAAC;UA1Wr5DjN,EAAE,CAAA4M,cAAA,iBA0Wk+F,CAAC;UA1Wr+F5M,EAAE,CAAA+L,UAAA,oBAAAmB,gDAAAJ,MAAA;YAAF9M,EAAE,CAAA+M,aAAA,CAAAN,GAAA;YAAA,OAAFzM,EAAE,CAAAgN,WAAA,CA0Wq8FnG,GAAA,CAAA0D,mBAAA,CAAAuC,MAA0B,CAAC;UAAA,CAAC,CAAC;UA1Wp+F9M,EAAE,CAAAiN,YAAA,CA0Wk+F,CAAC;UA1Wr+FjN,EAAE,CAAA4M,cAAA,YA0W6gG,CAAC;UA1WhhG5M,EAAE,CAAAmN,SAAA,YA0WkkG,CAAC,YAAoD,CAAC;UA1W1nGnN,EAAE,CAAAiN,YAAA,CA0WmoG,CAAC;UA1WtoGjN,EAAE,CAAA4M,cAAA,YA0Wm2G,CAAC;UA1Wt2G5M,EAAE,CAAAmN,SAAA,cA0W+6G,CAAC;UA1Wl7GnN,EAAE,CAAAiN,YAAA,CA0W27G,CAAC,CAAS,CAAC;UA1Wx8GjN,EAAE,CAAA4M,cAAA,gBA0Ws/G,CAAC;UA1Wz/G5M,EAAE,CAAAoN,YAAA,GA0WqhH,CAAC;UA1WxhHpN,EAAE,CAAAiN,YAAA,CA0WiiH,CAAC,CAAO,CAAC;QAAA;QAAA,IAAArG,EAAA;UA1W5iH5G,EAAE,CAAAqN,UAAA,kBAAAxG,GAAA,CAAAzC,aA0WkqD,CAAC;UA1WrqDpE,EAAE,CAAAsN,SAAA,EA0WmvD,CAAC;UA1WtvDtN,EAAE,CAAAkM,WAAA,wBAAArF,GAAA,CAAAjC,QA0WmvD,CAAC;UA1WtvD5E,EAAE,CAAAsN,SAAA,EA0Wo9E,CAAC;UA1Wv9EtN,EAAE,CAAAqN,UAAA,OAAAxG,GAAA,CAAAkC,OA0Wo9E,CAAC,YAAAlC,GAAA,CAAAnC,OAAiC,CAAC,aAAAmC,GAAA,CAAAjC,QAAA,KAAAiC,GAAA,CAAA1D,mBAA2D,CAAC,aAAA0D,GAAA,CAAAhC,QAAuG,CAAC;UA1W7pF7E,EAAE,CAAAiM,WAAA,SAAApF,GAAA,CAAA3C,IAAA,WAAA2C,GAAA,CAAAvE,KAAA,gBAAAuE,GAAA,CAAAwB,SAAA,qBAAAxB,GAAA,CAAAyB,cAAA,sBAAAzB,GAAA,CAAA0B,eAAA,mBAAA1B,GAAA,CAAAjC,QAAA,IAAAiC,GAAA,CAAA1D,mBAAA;UAAFnD,EAAE,CAAAsN,SAAA,EA0WqwG,CAAC;UA1WxwGtN,EAAE,CAAAqN,UAAA,qBAAAxG,GAAA,CAAAsC,cAAA,CAAAQ,aA0WqwG,CAAC,sBAAA9C,GAAA,CAAAyD,iBAAA,EAAqD,CAAC,0BAAsC,CAAC;UA1Wr2GtK,EAAE,CAAAsN,SAAA,EA0Wq/G,CAAC;UA1Wx/GtN,EAAE,CAAAqN,UAAA,QAAAxG,GAAA,CAAAkC,OA0Wq/G,CAAC;QAAA;MAAA;MAAAwE,YAAA,GAA+uT1L,SAAS,EAAwPC,qBAAqB;MAAA0L,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC1lb;EAAC,OA9TK1G,cAAc;AAAA;AA+TpB;EAAA,QAAAU,SAAA,oBAAAA,SAAA;AAAA;AAqEoB,IAEdiG,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB,OAAOxH,IAAI,YAAAyH,uBAAAvH,iBAAA;MAAA,YAAAA,iBAAA,IAAwFsH,cAAc;IAAA;IACjH,OAAOE,IAAI,kBArb8E7N,EAAE,CAAA8N,gBAAA;MAAAtH,IAAA,EAqbSmH;IAAc;IAClH,OAAOI,IAAI,kBAtb8E/N,EAAE,CAAAgO,gBAAA;MAAAC,OAAA,GAsbmClM,eAAe,EAAEC,eAAe,EAAEgF,cAAc,EAAEjF,eAAe;IAAA;EACnM;EAAC,OAJK4L,cAAc;AAAA;AAKpB;EAAA,QAAAjG,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAAS5E,yBAAyB,EAAEG,iCAAiC,EAAEJ,eAAe,EAAEL,sCAAsC,EAAEwE,cAAc,EAAE5E,cAAc,EAAEO,aAAa,EAAEgL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}