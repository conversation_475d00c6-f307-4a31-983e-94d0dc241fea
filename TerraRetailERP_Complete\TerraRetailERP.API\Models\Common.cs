using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Countries")]
    public class Country
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(10)]
        public string? Code { get; set; }

        [StringLength(10)]
        public string? PhoneCode { get; set; }

        [StringLength(6)]
        public string? CurrencyCode { get; set; }

        [StringLength(100)]
        public string? CurrencyNameAr { get; set; }

        [StringLength(100)]
        public string? CurrencyNameEn { get; set; }

        public bool IsActive { get; set; } = true;

        public int DisplayOrder { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<Area> Areas { get; set; } = new List<Area>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("Areas")]
    public class Area
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        public int CountryId { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        public bool IsActive { get; set; } = true;

        public int DisplayOrder { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("CountryId")]
        public virtual Country Country { get; set; } = null!;

        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    [Table("PaymentMethods")]
    public class PaymentMethod
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        [StringLength(400)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public bool RequireReference { get; set; } = false;

        public bool RequireApproval { get; set; } = false;

        public bool IsDefault { get; set; } = false;

        public int PaymentType { get; set; } = 1; // 1=Cash, 2=Card, 3=Bank, 4=Check, 5=Other

        [StringLength(100)]
        public string? Icon { get; set; }

        [StringLength(14)]
        public string? Color { get; set; }

        public int DisplayOrder { get; set; } = 1;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? TransactionFeePercentage { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? FixedTransactionFee { get; set; }

        public int? ChartOfAccountId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount? ChartAccount { get; set; }

        public virtual ICollection<SalePayment> SalePayments { get; set; } = new List<SalePayment>();
        public virtual ICollection<PurchasePayment> PurchasePayments { get; set; } = new List<PurchasePayment>();
        public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
    }

    [Table("UserBranches")]
    public class UserBranch
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }
        public int BranchId { get; set; }

        public bool IsDefault { get; set; } = false;

        // Permissions
        public bool CanView { get; set; } = true;
        public bool CanCreate { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
        public bool CanApprove { get; set; } = false;
        public bool CanViewReports { get; set; } = false;
        public bool CanManageInventory { get; set; } = false;
        public bool CanManageFinance { get; set; } = false;
        public bool CanManageHR { get; set; } = false;
        public bool CanManageSettings { get; set; } = false;

        // Time Restrictions
        public TimeSpan? AccessStartTime { get; set; }
        public TimeSpan? AccessEndTime { get; set; }

        [StringLength(20)]
        public string? AccessDays { get; set; } // "1234567" for Sun-Sat

        // Validity Period
        public DateTime EffectiveDate { get; set; } = DateTime.Today;
        public DateTime? ExpiryDate { get; set; }

        public bool IsActive { get; set; } = true;

        public int AssignedBy { get; set; }
        public DateTime AssignedAt { get; set; } = DateTime.Now;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("AssignedBy")]
        public virtual User AssignedByUser { get; set; } = null!;
    }

    [Table("UserSessions")]
    public class UserSession
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        [Required]
        [StringLength(1000)]
        public string SessionToken { get; set; } = string.Empty;

        public int CurrentBranchId { get; set; }

        public DateTime LoginTime { get; set; } = DateTime.Now;
        public DateTime LastActivity { get; set; } = DateTime.Now;
        public DateTime? LogoutTime { get; set; }

        [StringLength(90)]
        public string? IPAddress { get; set; }

        [StringLength(1000)]
        public string? UserAgent { get; set; }

        [StringLength(200)]
        public string? DeviceInfo { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsExpired { get; set; } = false;

        public DateTime? ExpiryTime { get; set; }

        [StringLength(1000)]
        public string? LogoutReason { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CurrentBranchId")]
        public virtual Branch CurrentBranch { get; set; } = null!;
    }

    [Table("UserRoles")]
    public class UserRole
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }
        public int RoleId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime AssignedAt { get; set; } = DateTime.Now;
        public int AssignedBy { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; } = null!;

        [ForeignKey("AssignedBy")]
        public virtual User AssignedByUser { get; set; } = null!;
    }

    [Table("Roles")]
    public class Role
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsSystemRole { get; set; } = false;

        public int DisplayOrder { get; set; } = 1;

        [StringLength(14)]
        public string? Color { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }

    [Table("RolePermissions")]
    public class RolePermission
    {
        [Key]
        public int Id { get; set; }

        public int RoleId { get; set; }

        [Required]
        [StringLength(100)]
        public string PermissionKey { get; set; } = string.Empty;

        public bool IsGranted { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; } = null!;
    }

    [Table("AuditTrails")]
    public class AuditTrail
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string TableName { get; set; } = string.Empty;

        public int? RecordId { get; set; }

        public string? OldValues { get; set; }
        public string? NewValues { get; set; }

        public int UserId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [StringLength(90)]
        public string? IPAddress { get; set; }

        [StringLength(1000)]
        public string? UserAgent { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int? BranchId { get; set; }

        [StringLength(100)]
        public string? ModuleName { get; set; }

        [StringLength(100)]
        public string? FeatureName { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch? Branch { get; set; }
    }
}
