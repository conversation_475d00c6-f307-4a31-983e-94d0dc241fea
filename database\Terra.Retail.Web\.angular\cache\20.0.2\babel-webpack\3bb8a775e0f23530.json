{"ast": null, "code": "import { CommonModule } from '@angular/common';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"@angular/material/progress-bar\";\nimport * as i9 from \"@angular/material/chips\";\nfunction SupplierStatsComponent_div_23_div_151_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"mat-icon\", 60);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61)(4, \"h4\");\n    i0.ɵɵtext(5, \"\\u062A\\u062D\\u0630\\u064A\\u0631: \\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0639\\u0627\\u0644\\u064A \\u0644\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"\\u062A\\u0645 \\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0623\\u0643\\u062B\\u0631 \\u0645\\u0646 90% \\u0645\\u0646 \\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646. \\u064A\\u064F\\u0646\\u0635\\u062D \\u0628\\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SupplierStatsComponent_div_23_div_151_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"mat-icon\", 62);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61)(4, \"h4\");\n    i0.ɵɵtext(5, \"\\u062A\\u0646\\u0628\\u064A\\u0647: \\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0645\\u062A\\u0642\\u062F\\u0645 \\u0644\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"\\u062A\\u0645 \\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0623\\u0643\\u062B\\u0631 \\u0645\\u0646 80% \\u0645\\u0646 \\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646. \\u064A\\u064F\\u0646\\u0635\\u062D \\u0628\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SupplierStatsComponent_div_23_div_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-card\", 56)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0648\\u062A\\u0648\\u0635\\u064A\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 57);\n    i0.ɵɵtemplate(10, SupplierStatsComponent_div_23_div_151_div_10_Template, 8, 0, \"div\", 58)(11, SupplierStatsComponent_div_23_div_151_div_11_Template, 8, 0, \"div\", 58);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getUsagePercentage() > 90);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getUsagePercentage() > 80 && ctx_r0.getUsagePercentage() <= 90);\n  }\n}\nfunction SupplierStatsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h2\", 15);\n    i0.ɵɵtext(3, \"\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"mat-card\", 17)(6, \"mat-card-content\")(7, \"div\", 18)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"people\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 19)(11, \"h3\");\n    i0.ɵɵtext(12, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 21);\n    i0.ɵɵtext(16, \"\\u0645\\u0648\\u0631\\u062F \\u0645\\u0633\\u062C\\u0644\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"mat-card\", 22)(18, \"mat-card-content\")(19, \"div\", 18)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 19)(23, \"h3\");\n    i0.ɵɵtext(24, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 20);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 21);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"mat-card\", 23)(30, \"mat-card-content\")(31, \"div\", 18)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"cancel\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 19)(35, \"h3\");\n    i0.ɵɵtext(36, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u063A\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\", 20);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 21);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(41, \"mat-card\", 24)(42, \"mat-card-content\")(43, \"div\", 18)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"account_balance_wallet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"h3\");\n    i0.ɵɵtext(48, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"p\", 25);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 21);\n    i0.ɵɵtext(52, \"\\u0631\\u0635\\u064A\\u062F \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(53, \"div\", 26)(54, \"h2\", 15);\n    i0.ɵɵtext(55, \"\\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 27)(57, \"mat-card\", 28)(58, \"mat-card-header\")(59, \"mat-card-title\")(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"span\");\n    i0.ɵɵtext(63, \"\\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629 \\u0627\\u0644\\u0645\\u0648\\u062C\\u0628\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 29);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"p\", 30);\n    i0.ɵɵtext(68, \"\\u0627\\u0644\\u0645\\u0628\\u0627\\u0644\\u063A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062D\\u0642\\u0629 \\u0644\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(69, \"mat-card\", 31)(70, \"mat-card-header\")(71, \"mat-card-title\")(72, \"mat-icon\");\n    i0.ɵɵtext(73, \"trending_down\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"span\");\n    i0.ɵɵtext(75, \"\\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629 \\u0627\\u0644\\u0633\\u0627\\u0644\\u0628\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"mat-card-content\")(77, \"div\", 32);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"p\", 30);\n    i0.ɵɵtext(80, \"\\u0627\\u0644\\u0645\\u0628\\u0627\\u0644\\u063A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062D\\u0642\\u0629 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(81, \"div\", 33)(82, \"h2\", 15);\n    i0.ɵɵtext(83, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 34)(85, \"mat-card\", 35)(86, \"mat-card-header\")(87, \"mat-card-title\")(88, \"mat-icon\");\n    i0.ɵɵtext(89, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"span\");\n    i0.ɵɵtext(91, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(92, \"mat-card-content\")(93, \"div\", 36)(94, \"div\", 37)(95, \"span\", 38);\n    i0.ɵɵtext(96, \"\\u0622\\u062E\\u0631 \\u0643\\u0648\\u062F \\u0645\\u0633\\u062A\\u062E\\u062F\\u0645:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(97, \"span\", 39);\n    i0.ɵɵtext(98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(99, \"div\", 37)(100, \"span\", 38);\n    i0.ɵɵtext(101, \"\\u0627\\u0644\\u0643\\u0648\\u062F \\u0627\\u0644\\u062A\\u0627\\u0644\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"span\", 40);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(104, \"div\", 37)(105, \"span\", 38);\n    i0.ɵɵtext(106, \"\\u062A\\u0646\\u0633\\u064A\\u0642 \\u0627\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"span\", 39);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(109, \"mat-card\", 41)(110, \"mat-card-header\")(111, \"mat-card-title\")(112, \"mat-icon\");\n    i0.ɵɵtext(113, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"span\");\n    i0.ɵɵtext(115, \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(116, \"mat-card-content\")(117, \"div\", 42)(118, \"div\", 43)(119, \"span\", 44);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"span\", 45);\n    i0.ɵɵtext(122, \"\\u0645\\u0646 \\u0627\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u0629\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(123, \"mat-progress-bar\", 46);\n    i0.ɵɵelementStart(124, \"div\", 47)(125, \"mat-chip\", 48);\n    i0.ɵɵtext(126);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(127, \"mat-card\", 49)(128, \"mat-card-header\")(129, \"mat-card-title\")(130, \"mat-icon\");\n    i0.ɵɵtext(131, \"storage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(132, \"span\");\n    i0.ɵɵtext(133, \"\\u0627\\u0644\\u0633\\u0639\\u0629 \\u0648\\u0627\\u0644\\u062D\\u062F\\u0648\\u062F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(134, \"mat-card-content\")(135, \"div\", 50)(136, \"div\", 51)(137, \"span\", 38);\n    i0.ɵɵtext(138, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0623\\u0642\\u0635\\u0649 \\u0644\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"span\", 39);\n    i0.ɵɵtext(140);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(141, \"div\", 51)(142, \"span\", 38);\n    i0.ɵɵtext(143, \"\\u0627\\u0644\\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0645\\u062A\\u0628\\u0642\\u064A\\u0629:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(144, \"span\", 52);\n    i0.ɵɵtext(145);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 51)(147, \"span\", 38);\n    i0.ɵɵtext(148, \"\\u064A\\u0645\\u0643\\u0646 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F\\u064A\\u0646:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(149, \"mat-chip\", 53);\n    i0.ɵɵtext(150);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵtemplate(151, SupplierStatsComponent_div_23_div_151_Template, 12, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(ctx_r0.formatNumber(ctx_r0.statistics.totalSuppliers));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatNumber(ctx_r0.statistics.activeSuppliers));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getActivePercentage().toFixed(1), \"% \\u0645\\u0646 \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r0.formatNumber(ctx_r0.statistics.inactiveSuppliers));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (100 - ctx_r0.getActivePercentage()).toFixed(1), \"% \\u0645\\u0646 \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getBalanceClass(ctx_r0.statistics.totalBalance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatCurrency(ctx_r0.statistics.totalBalance), \" \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.statistics.positiveBalance));\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.statistics.negativeBalance));\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate(ctx_r0.codeInfo.lastSupplierNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.codeInfo.nextSupplierCode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.codeInfo.codeFormat);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getUsagePercentage().toFixed(2), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r0.getUsagePercentage())(\"ngClass\", ctx_r0.getUsageStatus());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getUsageStatus());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getUsageStatusText(), \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(ctx_r0.formatNumber(ctx_r0.codeInfo.maxPossibleCodes));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.formatNumber(ctx_r0.codeInfo.remainingCodes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"color\", ctx_r0.codeInfo.canAddMore ? \"primary\" : \"warn\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.codeInfo.canAddMore ? \"\\u0646\\u0639\\u0645\" : \"\\u0644\\u0627\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getUsagePercentage() > 80);\n  }\n}\nfunction SupplierStatsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"mat-spinner\", 64);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SupplierStatsComponent = /*#__PURE__*/(() => {\n  class SupplierStatsComponent {\n    router;\n    http;\n    // Component State\n    isLoading = false;\n    // Data\n    statistics = null;\n    codeInfo = null;\n    // Subscriptions\n    subscriptions = [];\n    constructor(router, http) {\n      this.router = router;\n      this.http = http;\n    }\n    ngOnInit() {\n      this.loadSupplierStats();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Load supplier statistics\n     */\n    loadSupplierStats() {\n      this.isLoading = true;\n      const sub = this.http.get('http://localhost:5127/api/simple/supplier-stats').subscribe({\n        next: response => {\n          console.log('Stats Response:', response);\n          this.statistics = response.statistics;\n          this.codeInfo = response.codeInfo;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading supplier stats:', error);\n          this.statistics = this.getMockStatistics();\n          this.codeInfo = this.getMockCodeInfo();\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Navigate to suppliers list\n     */\n    goToSuppliers() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Navigate to add supplier\n     */\n    addSupplier() {\n      this.router.navigate(['/suppliers/add']);\n    }\n    /**\n     * Go back\n     */\n    goBack() {\n      this.router.navigate(['/suppliers/management']);\n    }\n    /**\n     * Format currency\n     */\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('ar-EG', {\n        style: 'currency',\n        currency: 'EGP'\n      }).format(amount);\n    }\n    /**\n     * Format number with commas\n     */\n    formatNumber(num) {\n      return new Intl.NumberFormat('ar-EG').format(num);\n    }\n    /**\n     * Get balance class for styling\n     */\n    getBalanceClass(balance) {\n      if (balance > 0) return 'positive';\n      if (balance < 0) return 'negative';\n      return 'zero';\n    }\n    /**\n     * Get usage percentage\n     */\n    getUsagePercentage() {\n      if (!this.codeInfo) return 0;\n      return this.codeInfo.lastSupplierNumber / this.codeInfo.maxPossibleCodes * 100;\n    }\n    /**\n     * Get usage status\n     */\n    getUsageStatus() {\n      const percentage = this.getUsagePercentage();\n      if (percentage < 50) return 'low';\n      if (percentage < 80) return 'medium';\n      if (percentage < 95) return 'high';\n      return 'critical';\n    }\n    /**\n     * Get usage status text\n     */\n    getUsageStatusText() {\n      const status = this.getUsageStatus();\n      switch (status) {\n        case 'low':\n          return 'استخدام منخفض';\n        case 'medium':\n          return 'استخدام متوسط';\n        case 'high':\n          return 'استخدام عالي';\n        case 'critical':\n          return 'استخدام حرج';\n        default:\n          return 'غير محدد';\n      }\n    }\n    /**\n     * Get active percentage\n     */\n    getActivePercentage() {\n      if (!this.statistics || this.statistics.totalSuppliers === 0) return 0;\n      return this.statistics.activeSuppliers / this.statistics.totalSuppliers * 100;\n    }\n    /**\n     * Get mock statistics\n     */\n    getMockStatistics() {\n      return {\n        totalSuppliers: 10,\n        activeSuppliers: 8,\n        inactiveSuppliers: 2,\n        totalBalance: -50000,\n        positiveBalance: 200000,\n        negativeBalance: -250000\n      };\n    }\n    /**\n     * Get mock code info\n     */\n    getMockCodeInfo() {\n      return {\n        lastSupplierNumber: 10,\n        nextSupplierCode: 'SUP011',\n        maxPossibleCodes: 999999,\n        remainingCodes: 999989,\n        codeFormat: 'SUP001 إلى SUP999999',\n        canAddMore: true\n      };\n    }\n    static ɵfac = function SupplierStatsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SupplierStatsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierStatsComponent,\n      selectors: [[\"app-supplier-stats\"]],\n      decls: 25,\n      vars: 2,\n      consts: [[1, \"supplier-stats-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"content\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"content\"], [1, \"stats-section\"], [1, \"section-title\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-card\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-card\", \"active-card\"], [1, \"stat-card\", \"inactive-card\"], [1, \"stat-card\", \"balance-card\"], [1, \"stat-number\", 3, \"ngClass\"], [1, \"financial-section\"], [1, \"financial-grid\"], [1, \"financial-card\", \"positive-card\"], [1, \"amount\", \"positive\"], [1, \"description\"], [1, \"financial-card\", \"negative-card\"], [1, \"amount\", \"negative\"], [1, \"code-section\"], [1, \"code-grid\"], [1, \"code-card\", \"status-card\"], [1, \"code-info\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [1, \"value\", \"highlight\"], [1, \"code-card\", \"usage-card\"], [1, \"usage-info\"], [1, \"usage-percentage\"], [1, \"percentage-value\"], [1, \"percentage-label\"], [3, \"value\", \"ngClass\"], [1, \"usage-status\"], [3, \"ngClass\"], [1, \"code-card\", \"capacity-card\"], [1, \"capacity-info\"], [1, \"capacity-row\"], [1, \"value\", \"remaining\"], [3, \"color\"], [\"class\", \"recommendations-section\", 4, \"ngIf\"], [1, \"recommendations-section\"], [1, \"recommendations-card\"], [1, \"recommendations-list\"], [\"class\", \"recommendation-item\", 4, \"ngIf\"], [1, \"recommendation-item\"], [1, \"warning-icon\"], [1, \"recommendation-text\"], [1, \"info-icon\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function SupplierStatsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SupplierStatsComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n          i0.ɵɵtext(9, \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 7);\n          i0.ɵɵtext(11, \"\\u062A\\u0642\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644 \\u0639\\u0646 \\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0648\\u0623\\u0643\\u0648\\u0627\\u062F \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SupplierStatsComponent_Template_button_click_13_listener() {\n            return ctx.goToSuppliers();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\");\n          i0.ɵɵtext(17, \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function SupplierStatsComponent_Template_button_click_18_listener() {\n            return ctx.addSupplier();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(23, SupplierStatsComponent_div_23_Template, 152, 22, \"div\", 11)(24, SupplierStatsComponent_div_24_Template, 4, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.statistics && ctx.codeInfo);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgIf, MatCardModule, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardTitle, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatProgressSpinnerModule, i7.MatProgressSpinner, MatProgressBarModule, i8.MatProgressBar, MatChipsModule, i9.MatChip],\n      styles: [\"\\n\\n.supplier-stats-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--info-600) 0%, var(--primary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2) !important;\\n  color: white !important;\\n  width: 48px !important;\\n  height: 48px !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md) var(--spacing-lg) !important;\\n  font-weight: 600 !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[mat-raised-button][_ngcontent-%COMP%] {\\n  background: var(--warning-500) !important;\\n  color: white !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[mat-raised-button][_ngcontent-%COMP%]:hover {\\n  background: var(--warning-600) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[mat-stroked-button][_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5) !important;\\n  color: white !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[mat-stroked-button][_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-color: white !important;\\n}\\n\\n\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) 0;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0 0 var(--spacing-xl) 0;\\n  text-align: center;\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-3xl);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-lg) !important;\\n  padding: var(--spacing-xl) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: var(--radius-full);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  color: white;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--gray-600);\\n  margin: 0 0 var(--spacing-xs) 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-xs) 0;\\n  color: var(--gray-900);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n  font-weight: 500;\\n}\\n.stat-card.total-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.stat-card.active-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.stat-card.inactive-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.stat-card.balance-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--info-500), var(--info-600));\\n}\\n\\n\\n\\n.financial-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-3xl);\\n}\\n\\n.financial-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.financial-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.25rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n  text-align: center;\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin-bottom: var(--spacing-md);\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .amount.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .amount.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--gray-600);\\n  margin: 0;\\n}\\n.financial-card.positive-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--success-500) !important;\\n}\\n.financial-card.positive-card[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--success-600) !important;\\n}\\n.financial-card.negative-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--error-500) !important;\\n}\\n.financial-card.negative-card[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--error-600) !important;\\n}\\n\\n\\n\\n.code-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-3xl);\\n}\\n\\n.code-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.code-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.code-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n}\\n.code-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.25rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.code-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600) !important;\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.code-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n}\\n\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%], .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md) 0;\\n  border-bottom: 1px solid var(--gray-100);\\n}\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child, .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]:last-child, .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child, .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-700);\\n}\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: var(--gray-900);\\n  font-weight: 500;\\n}\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value.highlight[_ngcontent-%COMP%], .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value.highlight[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value.highlight[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value.highlight[_ngcontent-%COMP%] {\\n  background: var(--primary-100);\\n  color: var(--primary-700);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-md);\\n  font-weight: 700;\\n}\\n.code-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value.remaining[_ngcontent-%COMP%], .code-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value.remaining[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value.remaining[_ngcontent-%COMP%], .capacity-info[_ngcontent-%COMP%]   .capacity-row[_ngcontent-%COMP%]   .value.remaining[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n  font-weight: 700;\\n}\\n\\n.usage-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-percentage[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-percentage[_ngcontent-%COMP%]   .percentage-value[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  color: var(--primary-600);\\n  display: block;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-percentage[_ngcontent-%COMP%]   .percentage-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n}\\n.usage-info[_ngcontent-%COMP%]   .mat-mdc-progress-bar[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-lg);\\n  height: 12px !important;\\n  border-radius: var(--radius-full) !important;\\n}\\n.usage-info[_ngcontent-%COMP%]   .mat-mdc-progress-bar.low[_ngcontent-%COMP%] {\\n  --mdc-linear-progress-active-indicator-color: var(--success-500);\\n}\\n.usage-info[_ngcontent-%COMP%]   .mat-mdc-progress-bar.medium[_ngcontent-%COMP%] {\\n  --mdc-linear-progress-active-indicator-color: var(--warning-500);\\n}\\n.usage-info[_ngcontent-%COMP%]   .mat-mdc-progress-bar.high[_ngcontent-%COMP%] {\\n  --mdc-linear-progress-active-indicator-color: var(--error-500);\\n}\\n.usage-info[_ngcontent-%COMP%]   .mat-mdc-progress-bar.critical[_ngcontent-%COMP%] {\\n  --mdc-linear-progress-active-indicator-color: var(--error-700);\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-status[_ngcontent-%COMP%]   .mat-mdc-chip[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-status[_ngcontent-%COMP%]   .mat-mdc-chip.low[_ngcontent-%COMP%] {\\n  background: var(--success-100) !important;\\n  color: var(--success-700) !important;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-status[_ngcontent-%COMP%]   .mat-mdc-chip.medium[_ngcontent-%COMP%] {\\n  background: var(--warning-100) !important;\\n  color: var(--warning-700) !important;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-status[_ngcontent-%COMP%]   .mat-mdc-chip.high[_ngcontent-%COMP%] {\\n  background: var(--error-100) !important;\\n  color: var(--error-700) !important;\\n}\\n.usage-info[_ngcontent-%COMP%]   .usage-status[_ngcontent-%COMP%]   .mat-mdc-chip.critical[_ngcontent-%COMP%] {\\n  background: var(--error-200) !important;\\n  color: var(--error-800) !important;\\n}\\n\\n\\n\\n.recommendations-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.recommendations-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--warning-200) !important;\\n  background: var(--warning-25) !important;\\n}\\n.recommendations-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--warning-50) !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n}\\n.recommendations-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.25rem !important;\\n  font-weight: 700 !important;\\n  color: var(--warning-800) !important;\\n  margin: 0 !important;\\n}\\n.recommendations-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--warning-600) !important;\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.recommendations-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n}\\n\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-lg);\\n  background: white;\\n  border-radius: var(--radius-lg);\\n  margin-bottom: var(--spacing-md);\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  flex-shrink: 0;\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%] {\\n  color: var(--info-600);\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  flex-shrink: 0;\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]   .recommendation-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]   .recommendation-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0 0 var(--spacing-xs) 0;\\n}\\n.recommendations-list[_ngcontent-%COMP%]   .recommendation-item[_ngcontent-%COMP%]   .recommendation-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  font-size: 1.125rem;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   .mat-mdc-progress-spinner[_ngcontent-%COMP%] {\\n  --mdc-circular-progress-active-indicator-color: var(--info-500);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .code-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .stats-grid[_ngcontent-%COMP%], .financial-grid[_ngcontent-%COMP%], .code-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) 0;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    text-align: center !important;\\n    gap: var(--spacing-md) !important;\\n  }\\n  .financial-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .usage-info[_ngcontent-%COMP%]   .usage-percentage[_ngcontent-%COMP%]   .percentage-value[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SupplierStatsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatProgressBarModule", "MatChipsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "SupplierStatsComponent_div_23_div_151_div_10_Template", "SupplierStatsComponent_div_23_div_151_div_11_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "getUsagePercentage", "ɵɵelement", "SupplierStatsComponent_div_23_div_151_Template", "ɵɵtextInterpolate", "formatNumber", "statistics", "totalSuppliers", "activeSuppliers", "ɵɵtextInterpolate1", "getActivePercentage", "toFixed", "inactiveSuppliers", "getBalanceClass", "totalBalance", "formatCurrency", "positiveBalance", "negativeBalance", "codeInfo", "lastSupplierNumber", "nextSupplierCode", "codeFormat", "getUsageStatus", "getUsageStatusText", "maxPossibleCodes", "remainingCodes", "canAddMore", "SupplierStatsComponent", "router", "http", "isLoading", "subscriptions", "constructor", "ngOnInit", "loadSupplierStats", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "get", "subscribe", "next", "response", "console", "log", "error", "getMockStatistics", "getMockCodeInfo", "push", "goToSuppliers", "navigate", "addSupplier", "goBack", "amount", "Intl", "NumberFormat", "style", "currency", "format", "num", "balance", "percentage", "status", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "SupplierStatsComponent_Template", "rf", "ctx", "ɵɵlistener", "SupplierStatsComponent_Template_button_click_4_listener", "SupplierStatsComponent_Template_button_click_13_listener", "SupplierStatsComponent_Template_button_click_18_listener", "SupplierStatsComponent_div_23_Template", "SupplierStatsComponent_div_24_Template", "i3", "Ng<PERSON><PERSON>", "NgIf", "i4", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatProgressSpinner", "i8", "MatProgressBar", "i9", "MatChip", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-stats\\supplier-stats.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-stats\\supplier-stats.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\n\ninterface SupplierStats {\n  totalSuppliers: number;\n  activeSuppliers: number;\n  inactiveSuppliers: number;\n  totalBalance: number;\n  positiveBalance: number;\n  negativeBalance: number;\n}\n\ninterface CodeInfo {\n  lastSupplierNumber: number;\n  nextSupplierCode: string;\n  maxPossibleCodes: number;\n  remainingCodes: number;\n  codeFormat: string;\n  canAddMore: boolean;\n}\n\n@Component({\n  selector: 'app-supplier-stats',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatProgressBarModule,\n    MatChipsModule\n  ],\n  templateUrl: './supplier-stats.component.html',\n  styleUrls: ['./supplier-stats.component.scss']\n})\nexport class SupplierStatsComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  \n  // Data\n  statistics: SupplierStats | null = null;\n  codeInfo: CodeInfo | null = null;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    this.loadSupplierStats();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Load supplier statistics\n   */\n  private loadSupplierStats(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-stats').subscribe({\n      next: (response) => {\n        console.log('Stats Response:', response);\n        this.statistics = response.statistics;\n        this.codeInfo = response.codeInfo;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading supplier stats:', error);\n        this.statistics = this.getMockStatistics();\n        this.codeInfo = this.getMockCodeInfo();\n        this.isLoading = false;\n      }\n    });\n    \n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Navigate to suppliers list\n   */\n  goToSuppliers(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Navigate to add supplier\n   */\n  addSupplier(): void {\n    this.router.navigate(['/suppliers/add']);\n  }\n\n  /**\n   * Go back\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers/management']);\n  }\n\n  /**\n   * Format currency\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('ar-EG', {\n      style: 'currency',\n      currency: 'EGP'\n    }).format(amount);\n  }\n\n  /**\n   * Format number with commas\n   */\n  formatNumber(num: number): string {\n    return new Intl.NumberFormat('ar-EG').format(num);\n  }\n\n  /**\n   * Get balance class for styling\n   */\n  getBalanceClass(balance: number): string {\n    if (balance > 0) return 'positive';\n    if (balance < 0) return 'negative';\n    return 'zero';\n  }\n\n  /**\n   * Get usage percentage\n   */\n  getUsagePercentage(): number {\n    if (!this.codeInfo) return 0;\n    return (this.codeInfo.lastSupplierNumber / this.codeInfo.maxPossibleCodes) * 100;\n  }\n\n  /**\n   * Get usage status\n   */\n  getUsageStatus(): string {\n    const percentage = this.getUsagePercentage();\n    if (percentage < 50) return 'low';\n    if (percentage < 80) return 'medium';\n    if (percentage < 95) return 'high';\n    return 'critical';\n  }\n\n  /**\n   * Get usage status text\n   */\n  getUsageStatusText(): string {\n    const status = this.getUsageStatus();\n    switch (status) {\n      case 'low': return 'استخدام منخفض';\n      case 'medium': return 'استخدام متوسط';\n      case 'high': return 'استخدام عالي';\n      case 'critical': return 'استخدام حرج';\n      default: return 'غير محدد';\n    }\n  }\n\n  /**\n   * Get active percentage\n   */\n  getActivePercentage(): number {\n    if (!this.statistics || this.statistics.totalSuppliers === 0) return 0;\n    return (this.statistics.activeSuppliers / this.statistics.totalSuppliers) * 100;\n  }\n\n  /**\n   * Get mock statistics\n   */\n  private getMockStatistics(): SupplierStats {\n    return {\n      totalSuppliers: 10,\n      activeSuppliers: 8,\n      inactiveSuppliers: 2,\n      totalBalance: -50000,\n      positiveBalance: 200000,\n      negativeBalance: -250000\n    };\n  }\n\n  /**\n   * Get mock code info\n   */\n  private getMockCodeInfo(): CodeInfo {\n    return {\n      lastSupplierNumber: 10,\n      nextSupplierCode: 'SUP011',\n      maxPossibleCodes: 999999,\n      remainingCodes: 999989,\n      codeFormat: 'SUP001 إلى SUP999999',\n      canAddMore: true\n    };\n  }\n}\n", "<!-- Terra Retail ERP - Supplier Statistics -->\n<div class=\"supplier-stats-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">إحصائيات الموردين</h1>\n          <p class=\"page-subtitle\">تقرير شامل عن حالة الموردين وأكواد النظام</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-stroked-button (click)=\"goToSuppliers()\">\n          <mat-icon>list</mat-icon>\n          <span>عرض الموردين</span>\n        </button>\n        <button mat-raised-button color=\"primary\" (click)=\"addSupplier()\">\n          <mat-icon>add</mat-icon>\n          <span>إضافة مورد</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content -->\n  <div class=\"content\" *ngIf=\"!isLoading && statistics && codeInfo\">\n    \n    <!-- General Statistics -->\n    <div class=\"stats-section\">\n      <h2 class=\"section-title\">الإحصائيات العامة</h2>\n      \n      <div class=\"stats-grid\">\n        \n        <!-- Total Suppliers -->\n        <mat-card class=\"stat-card total-card\">\n          <mat-card-content>\n            <div class=\"stat-icon\">\n              <mat-icon>people</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>إجمالي الموردين</h3>\n              <p class=\"stat-number\">{{ formatNumber(statistics.totalSuppliers) }}</p>\n              <span class=\"stat-label\">مورد مسجل</span>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Active Suppliers -->\n        <mat-card class=\"stat-card active-card\">\n          <mat-card-content>\n            <div class=\"stat-icon\">\n              <mat-icon>check_circle</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>الموردين النشطين</h3>\n              <p class=\"stat-number\">{{ formatNumber(statistics.activeSuppliers) }}</p>\n              <span class=\"stat-label\">{{ getActivePercentage().toFixed(1) }}% من الإجمالي</span>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Inactive Suppliers -->\n        <mat-card class=\"stat-card inactive-card\">\n          <mat-card-content>\n            <div class=\"stat-icon\">\n              <mat-icon>cancel</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>الموردين غير النشطين</h3>\n              <p class=\"stat-number\">{{ formatNumber(statistics.inactiveSuppliers) }}</p>\n              <span class=\"stat-label\">{{ (100 - getActivePercentage()).toFixed(1) }}% من الإجمالي</span>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Total Balance -->\n        <mat-card class=\"stat-card balance-card\">\n          <mat-card-content>\n            <div class=\"stat-icon\">\n              <mat-icon>account_balance_wallet</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>إجمالي الأرصدة</h3>\n              <p class=\"stat-number\" [ngClass]=\"getBalanceClass(statistics.totalBalance)\">\n                {{ formatCurrency(statistics.totalBalance) }}\n              </p>\n              <span class=\"stat-label\">رصيد إجمالي</span>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n      </div>\n    </div>\n\n    <!-- Financial Details -->\n    <div class=\"financial-section\">\n      <h2 class=\"section-title\">التفاصيل المالية</h2>\n      \n      <div class=\"financial-grid\">\n        \n        <!-- Positive Balance -->\n        <mat-card class=\"financial-card positive-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>trending_up</mat-icon>\n              <span>الأرصدة الموجبة</span>\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"amount positive\">{{ formatCurrency(statistics.positiveBalance) }}</div>\n            <p class=\"description\">المبالغ المستحقة للموردين</p>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Negative Balance -->\n        <mat-card class=\"financial-card negative-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>trending_down</mat-icon>\n              <span>الأرصدة السالبة</span>\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"amount negative\">{{ formatCurrency(statistics.negativeBalance) }}</div>\n            <p class=\"description\">المبالغ المستحقة من الموردين</p>\n          </mat-card-content>\n        </mat-card>\n\n      </div>\n    </div>\n\n    <!-- Code Information -->\n    <div class=\"code-section\">\n      <h2 class=\"section-title\">معلومات أكواد الموردين</h2>\n      \n      <div class=\"code-grid\">\n        \n        <!-- Current Status -->\n        <mat-card class=\"code-card status-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>info</mat-icon>\n              <span>الحالة الحالية</span>\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"code-info\">\n              <div class=\"info-row\">\n                <span class=\"label\">آخر كود مستخدم:</span>\n                <span class=\"value\">{{ codeInfo.lastSupplierNumber }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">الكود التالي:</span>\n                <span class=\"value highlight\">{{ codeInfo.nextSupplierCode }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">تنسيق الأكواد:</span>\n                <span class=\"value\">{{ codeInfo.codeFormat }}</span>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Usage Statistics -->\n        <mat-card class=\"code-card usage-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>analytics</mat-icon>\n              <span>إحصائيات الاستخدام</span>\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"usage-info\">\n              <div class=\"usage-percentage\">\n                <span class=\"percentage-value\">{{ getUsagePercentage().toFixed(2) }}%</span>\n                <span class=\"percentage-label\">من الأكواد المستخدمة</span>\n              </div>\n              <mat-progress-bar \n                [value]=\"getUsagePercentage()\" \n                [ngClass]=\"getUsageStatus()\">\n              </mat-progress-bar>\n              <div class=\"usage-status\">\n                <mat-chip [ngClass]=\"getUsageStatus()\">\n                  {{ getUsageStatusText() }}\n                </mat-chip>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Capacity Information -->\n        <mat-card class=\"code-card capacity-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>storage</mat-icon>\n              <span>السعة والحدود</span>\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"capacity-info\">\n              <div class=\"capacity-row\">\n                <span class=\"label\">الحد الأقصى للأكواد:</span>\n                <span class=\"value\">{{ formatNumber(codeInfo.maxPossibleCodes) }}</span>\n              </div>\n              <div class=\"capacity-row\">\n                <span class=\"label\">الأكواد المتبقية:</span>\n                <span class=\"value remaining\">{{ formatNumber(codeInfo.remainingCodes) }}</span>\n              </div>\n              <div class=\"capacity-row\">\n                <span class=\"label\">يمكن إضافة موردين:</span>\n                <mat-chip [color]=\"codeInfo.canAddMore ? 'primary' : 'warn'\">\n                  {{ codeInfo.canAddMore ? 'نعم' : 'لا' }}\n                </mat-chip>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n      </div>\n    </div>\n\n    <!-- Recommendations -->\n    <div class=\"recommendations-section\" *ngIf=\"getUsagePercentage() > 80\">\n      <mat-card class=\"recommendations-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>warning</mat-icon>\n            <span>تنبيهات وتوصيات</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"recommendations-list\">\n            <div class=\"recommendation-item\" *ngIf=\"getUsagePercentage() > 90\">\n              <mat-icon class=\"warning-icon\">error</mat-icon>\n              <div class=\"recommendation-text\">\n                <h4>تحذير: استخدام عالي للأكواد</h4>\n                <p>تم استخدام أكثر من 90% من أكواد الموردين. يُنصح بمراجعة النظام قريباً.</p>\n              </div>\n            </div>\n            <div class=\"recommendation-item\" *ngIf=\"getUsagePercentage() > 80 && getUsagePercentage() <= 90\">\n              <mat-icon class=\"info-icon\">info</mat-icon>\n              <div class=\"recommendation-text\">\n                <h4>تنبيه: استخدام متقدم للأكواد</h4>\n                <p>تم استخدام أكثر من 80% من أكواد الموردين. يُنصح بمتابعة الاستخدام.</p>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل إحصائيات الموردين...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAK9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;;ICiO1CC,EADF,CAAAC,cAAA,cAAmE,mBAClC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7CH,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAE,MAAA,qJAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2VAAsE;IAE7EF,EAF6E,CAAAG,YAAA,EAAI,EACzE,EACF;;;;;IAEJH,EADF,CAAAC,cAAA,cAAiG,mBACnE;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEzCH,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAE,MAAA,2JAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wUAAkE;IAEzEF,EAFyE,CAAAG,YAAA,EAAI,EACrE,EACF;;;;;IAnBNH,EAJR,CAAAC,cAAA,cAAuE,mBAC9B,sBACpB,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,cACkB;IAQhCD,EAPA,CAAAI,UAAA,KAAAC,qDAAA,kBAAmE,KAAAC,qDAAA,kBAO8B;IAUzGN,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;;;;IAjBoCH,EAAA,CAAAO,SAAA,IAA+B;IAA/BP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,kBAAA,QAA+B;IAO/BV,EAAA,CAAAO,SAAA,EAA6D;IAA7DP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,kBAAA,WAAAD,MAAA,CAAAC,kBAAA,SAA6D;;;;;IAlNrGV,EAJJ,CAAAC,cAAA,cAAkE,cAGrC,aACC;IAAAD,EAAA,CAAAE,MAAA,wGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAQxCH,EANR,CAAAC,cAAA,cAAwB,mBAGiB,uBACnB,cACO,eACX;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,yDAAS;IAGxCF,EAHwC,CAAAG,YAAA,EAAO,EACrC,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAwC,wBACpB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAmD;IAGlFF,EAHkF,CAAAG,YAAA,EAAO,EAC/E,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA0C,wBACtB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,sHAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC3EH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA2D;IAG1FF,EAH0F,CAAAG,YAAA,EAAO,EACvF,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAyC,wBACrB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,aAA4E;IAC1ED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IAM9CF,EAN8C,CAAAG,YAAA,EAAO,EACvC,EACW,EACV,EAEP,EACF;IAIJH,EADF,CAAAC,cAAA,eAA+B,cACH;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAQvCH,EANR,CAAAC,cAAA,eAA4B,oBAGqB,uBAC5B,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACa;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnFH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,oJAAyB;IAEpDF,EAFoD,CAAAG,YAAA,EAAI,EACnC,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA+C,uBAC5B,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACa;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnFH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,iKAA4B;IAK3DF,EAL2D,CAAAG,YAAA,EAAI,EACtC,EACV,EAEP,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,kIAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAQ7CH,EANR,CAAAC,cAAA,eAAuB,oBAGmB,uBACrB,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAExBF,EAFwB,CAAAG,YAAA,EAAO,EACZ,EACD;IAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACO,eACC,gBACA;IAAAD,EAAA,CAAAE,MAAA,mFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACA;IAAAD,EAAA,CAAAE,MAAA,6EAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,iBAA8B;IAAAD,EAAA,CAAAE,MAAA,KAA+B;IAC/DF,EAD+D,CAAAG,YAAA,EAAO,EAChE;IAEJH,EADF,CAAAC,cAAA,gBAAsB,iBACA;IAAAD,EAAA,CAAAE,MAAA,mFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,KAAyB;IAIrDF,EAJqD,CAAAG,YAAA,EAAO,EAChD,EACF,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAAuC,wBACpB,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,gHAAkB;IAE5BF,EAF4B,CAAAG,YAAA,EAAO,EAChB,EACD;IAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACQ,gBACQ,iBACG;IAAAD,EAAA,CAAAE,MAAA,KAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5EH,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAE,MAAA,uHAAoB;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;IACNH,EAAA,CAAAW,SAAA,6BAGmB;IAEjBX,EADF,CAAAC,cAAA,gBAA0B,qBACe;IACrCD,EAAA,CAAAE,MAAA,KACF;IAIRF,EAJQ,CAAAG,YAAA,EAAW,EACP,EACF,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAA0C,wBACvB,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,gBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,kFAAa;IAEvBF,EAFuB,CAAAG,YAAA,EAAO,EACX,EACD;IAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACW,gBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,kHAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,KAA6C;IACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACJ;IAAAD,EAAA,CAAAE,MAAA,qGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,iBAA8B;IAAAD,EAAA,CAAAE,MAAA,KAA2C;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC5E;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACJ;IAAAD,EAAA,CAAAE,MAAA,sGAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,qBAA6D;IAC3DD,EAAA,CAAAE,MAAA,KACF;IAOZF,EAPY,CAAAG,YAAA,EAAW,EACP,EACF,EACW,EACV,EAEP,EACF;IAGNH,EAAA,CAAAI,UAAA,MAAAQ,8CAAA,mBAAuE;IA6BzEZ,EAAA,CAAAG,YAAA,EAAM;;;;IAlN6BH,EAAA,CAAAO,SAAA,IAA6C;IAA7CP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAAM,UAAA,CAAAC,cAAA,EAA6C;IAc7ChB,EAAA,CAAAO,SAAA,IAA8C;IAA9CP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAAM,UAAA,CAAAE,eAAA,EAA8C;IAC5CjB,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAkB,kBAAA,KAAAT,MAAA,CAAAU,mBAAA,GAAAC,OAAA,uEAAmD;IAarDpB,EAAA,CAAAO,SAAA,IAAgD;IAAhDP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAAM,UAAA,CAAAM,iBAAA,EAAgD;IAC9CrB,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAkB,kBAAA,YAAAT,MAAA,CAAAU,mBAAA,IAAAC,OAAA,uEAA2D;IAa7DpB,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,UAAA,YAAAC,MAAA,CAAAa,eAAA,CAAAb,MAAA,CAAAM,UAAA,CAAAQ,YAAA,EAAoD;IACzEvB,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAAM,UAAA,CAAAQ,YAAA,OACF;IAwB2BvB,EAAA,CAAAO,SAAA,IAAgD;IAAhDP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAAM,UAAA,CAAAU,eAAA,EAAgD;IAchDzB,EAAA,CAAAO,SAAA,IAAgD;IAAhDP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAAM,UAAA,CAAAW,eAAA,EAAgD;IA0BrD1B,EAAA,CAAAO,SAAA,IAAiC;IAAjCP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAkB,QAAA,CAAAC,kBAAA,CAAiC;IAIvB5B,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAkB,QAAA,CAAAE,gBAAA,CAA+B;IAIzC7B,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAkB,QAAA,CAAAG,UAAA,CAAyB;IAiBd9B,EAAA,CAAAO,SAAA,IAAsC;IAAtCP,EAAA,CAAAkB,kBAAA,KAAAT,MAAA,CAAAC,kBAAA,GAAAU,OAAA,SAAsC;IAIrEpB,EAAA,CAAAO,SAAA,GAA8B;IAC9BP,EADA,CAAAQ,UAAA,UAAAC,MAAA,CAAAC,kBAAA,GAA8B,YAAAD,MAAA,CAAAsB,cAAA,GACF;IAGlB/B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,UAAA,YAAAC,MAAA,CAAAsB,cAAA,GAA4B;IACpC/B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAuB,kBAAA,QACF;IAkBoBhC,EAAA,CAAAO,SAAA,IAA6C;IAA7CP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAAkB,QAAA,CAAAM,gBAAA,EAA6C;IAInCjC,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAa,iBAAA,CAAAJ,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAAkB,QAAA,CAAAO,cAAA,EAA2C;IAI/DlC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAQ,UAAA,UAAAC,MAAA,CAAAkB,QAAA,CAAAQ,UAAA,sBAAkD;IAC1DnC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAkB,kBAAA,MAAAT,MAAA,CAAAkB,QAAA,CAAAQ,UAAA,8CACF;IAU0BnC,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,kBAAA,QAA+B;;;;;IAgCvEV,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAW,SAAA,sBAAyC;IACzCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mKAA+B;IACpCF,EADoC,CAAAG,YAAA,EAAI,EAClC;;;ADtNR,WAAaiC,sBAAsB;EAA7B,MAAOA,sBAAsB;IAavBC,MAAA;IACAC,IAAA;IAZV;IACAC,SAAS,GAAG,KAAK;IAEjB;IACAxB,UAAU,GAAyB,IAAI;IACvCY,QAAQ,GAAoB,IAAI;IAEhC;IACQa,aAAa,GAAmB,EAAE;IAE1CC,YACUJ,MAAc,EACdC,IAAgB;MADhB,KAAAD,MAAM,GAANA,MAAM;MACN,KAAAC,IAAI,GAAJA,IAAI;IACX;IAEHI,QAAQA,CAAA;MACN,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQJ,iBAAiBA,CAAA;MACvB,IAAI,CAACJ,SAAS,GAAG,IAAI;MAErB,MAAMO,GAAG,GAAG,IAAI,CAACR,IAAI,CAACU,GAAG,CAAM,iDAAiD,CAAC,CAACC,SAAS,CAAC;QAC1FC,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;UACxC,IAAI,CAACpC,UAAU,GAAGoC,QAAQ,CAACpC,UAAU;UACrC,IAAI,CAACY,QAAQ,GAAGwB,QAAQ,CAACxB,QAAQ;UACjC,IAAI,CAACY,SAAS,GAAG,KAAK;QACxB,CAAC;QACDe,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAI,CAACvC,UAAU,GAAG,IAAI,CAACwC,iBAAiB,EAAE;UAC1C,IAAI,CAAC5B,QAAQ,GAAG,IAAI,CAAC6B,eAAe,EAAE;UACtC,IAAI,CAACjB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MAEF,IAAI,CAACC,aAAa,CAACiB,IAAI,CAACX,GAAG,CAAC;IAC9B;IAEA;;;IAGAY,aAAaA,CAAA;MACX,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGAC,WAAWA,CAAA;MACT,IAAI,CAACvB,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC1C;IAEA;;;IAGAE,MAAMA,CAAA;MACJ,IAAI,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;IACjD;IAEA;;;IAGAnC,cAAcA,CAACsC,MAAc;MAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE;OACX,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;IACnB;IAEA;;;IAGAhD,YAAYA,CAACsD,GAAW;MACtB,OAAO,IAAIL,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACG,MAAM,CAACC,GAAG,CAAC;IACnD;IAEA;;;IAGA9C,eAAeA,CAAC+C,OAAe;MAC7B,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,OAAO,MAAM;IACf;IAEA;;;IAGA3D,kBAAkBA,CAAA;MAChB,IAAI,CAAC,IAAI,CAACiB,QAAQ,EAAE,OAAO,CAAC;MAC5B,OAAQ,IAAI,CAACA,QAAQ,CAACC,kBAAkB,GAAG,IAAI,CAACD,QAAQ,CAACM,gBAAgB,GAAI,GAAG;IAClF;IAEA;;;IAGAF,cAAcA,CAAA;MACZ,MAAMuC,UAAU,GAAG,IAAI,CAAC5D,kBAAkB,EAAE;MAC5C,IAAI4D,UAAU,GAAG,EAAE,EAAE,OAAO,KAAK;MACjC,IAAIA,UAAU,GAAG,EAAE,EAAE,OAAO,QAAQ;MACpC,IAAIA,UAAU,GAAG,EAAE,EAAE,OAAO,MAAM;MAClC,OAAO,UAAU;IACnB;IAEA;;;IAGAtC,kBAAkBA,CAAA;MAChB,MAAMuC,MAAM,GAAG,IAAI,CAACxC,cAAc,EAAE;MACpC,QAAQwC,MAAM;QACZ,KAAK,KAAK;UAAE,OAAO,eAAe;QAClC,KAAK,QAAQ;UAAE,OAAO,eAAe;QACrC,KAAK,MAAM;UAAE,OAAO,cAAc;QAClC,KAAK,UAAU;UAAE,OAAO,aAAa;QACrC;UAAS,OAAO,UAAU;MAC5B;IACF;IAEA;;;IAGApD,mBAAmBA,CAAA;MACjB,IAAI,CAAC,IAAI,CAACJ,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,cAAc,KAAK,CAAC,EAAE,OAAO,CAAC;MACtE,OAAQ,IAAI,CAACD,UAAU,CAACE,eAAe,GAAG,IAAI,CAACF,UAAU,CAACC,cAAc,GAAI,GAAG;IACjF;IAEA;;;IAGQuC,iBAAiBA,CAAA;MACvB,OAAO;QACLvC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,CAAC;QAClBI,iBAAiB,EAAE,CAAC;QACpBE,YAAY,EAAE,CAAC,KAAK;QACpBE,eAAe,EAAE,MAAM;QACvBC,eAAe,EAAE,CAAC;OACnB;IACH;IAEA;;;IAGQ8B,eAAeA,CAAA;MACrB,OAAO;QACL5B,kBAAkB,EAAE,EAAE;QACtBC,gBAAgB,EAAE,QAAQ;QAC1BI,gBAAgB,EAAE,MAAM;QACxBC,cAAc,EAAE,MAAM;QACtBJ,UAAU,EAAE,sBAAsB;QAClCK,UAAU,EAAE;OACb;IACH;;uCAnKWC,sBAAsB,EAAApC,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;;YAAtBxC,sBAAsB;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxC3BnF,EANR,CAAAC,cAAA,aAAsC,aAGX,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAqF,UAAA,mBAAAC,wDAAA;YAAA,OAASF,GAAA,CAAAvB,MAAA,EAAQ;UAAA,EAAC;UACzD7D,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,wGAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,gOAAyC;UAEtEF,EAFsE,CAAAG,YAAA,EAAI,EAClE,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBAC2B;UAA1BD,EAAA,CAAAqF,UAAA,mBAAAE,yDAAA;YAAA,OAASH,GAAA,CAAA1B,aAAA,EAAe;UAAA,EAAC;UAClD1D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UACpBF,EADoB,CAAAG,YAAA,EAAO,EAClB;UACTH,EAAA,CAAAC,cAAA,kBAAkE;UAAxBD,EAAA,CAAAqF,UAAA,mBAAAG,yDAAA;YAAA,OAASJ,GAAA,CAAAxB,WAAA,EAAa;UAAA,EAAC;UAC/D5D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAIxBF,EAJwB,CAAAG,YAAA,EAAO,EAChB,EACL,EACF,EACF;UAwONH,EArOA,CAAAI,UAAA,KAAAqF,sCAAA,qBAAkE,KAAAC,sCAAA,kBAqOnB;UAKjD1F,EAAA,CAAAG,YAAA,EAAM;;;UA1OkBH,EAAA,CAAAO,SAAA,IAA0C;UAA1CP,EAAA,CAAAQ,UAAA,UAAA4E,GAAA,CAAA7C,SAAA,IAAA6C,GAAA,CAAArE,UAAA,IAAAqE,GAAA,CAAAzD,QAAA,CAA0C;UAqOlC3B,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAA4E,GAAA,CAAA7C,SAAA,CAAe;;;qBD9N3C9C,YAAY,EAAAkG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZnG,aAAa,EAAAoG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbvG,eAAe,EAAAwG,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfzG,aAAa,EAAA0G,EAAA,CAAAC,OAAA,EACb1G,wBAAwB,EAAA2G,EAAA,CAAAC,kBAAA,EACxB3G,oBAAoB,EAAA4G,EAAA,CAAAC,cAAA,EACpB5G,cAAc,EAAA6G,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;;SAKL1E,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}