using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("PurchaseInvoices")]
    public class PurchaseInvoice
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [StringLength(100)]
        public string? SupplierInvoiceNumber { get; set; }

        public int SupplierId { get; set; }
        public int BranchId { get; set; }
        public int UserId { get; set; }

        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public DateTime? DueDate { get; set; }
        public DateTime? ReceivedDate { get; set; }

        public int Status { get; set; } = 1; // 1=Pending, 2=Received, 3=Cancelled, 4=Returned

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ShippingCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherCharges { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(2000)]
        public string? Notes { get; set; }

        [StringLength(2000)]
        public string? InternalNotes { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        public int? ReceivedById { get; set; }

        public int? CancelledById { get; set; }
        public DateTime? CancelledAt { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        [StringLength(200)]
        public string? DeliveryAddress { get; set; }

        [StringLength(100)]
        public string? DeliveryContactPerson { get; set; }

        [StringLength(40)]
        public string? DeliveryContactPhone { get; set; }

        [StringLength(100)]
        public string? ShippingCompany { get; set; }

        [StringLength(100)]
        public string? TrackingNumber { get; set; }

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ReceivedById")]
        public virtual User? ReceivedBy { get; set; }

        [ForeignKey("CancelledById")]
        public virtual User? CancelledBy { get; set; }

        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
        public virtual ICollection<PurchasePayment> PurchasePayments { get; set; } = new List<PurchasePayment>();
        public virtual ICollection<ProductBatch> ProductBatches { get; set; } = new List<ProductBatch>();
    }

    [Table("PurchaseInvoiceDetails")]
    public class PurchaseInvoiceDetail
    {
        [Key]
        public int Id { get; set; }

        public int PurchaseInvoiceId { get; set; }
        public int ProductId { get; set; }

        public int LineNumber { get; set; } = 1;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OrderedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ReceivedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal RemainingQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetUnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetLineTotal { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal FinalTotal { get; set; } = 0;

        [StringLength(1000)]
        public string? ItemNotes { get; set; }

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }

        [StringLength(200)]
        public string? SerialNumber { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ActualWeight { get; set; }

        [StringLength(400)]
        public string? DiscountReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }

    [Table("PurchasePayments")]
    public class PurchasePayment
    {
        [Key]
        public int Id { get; set; }

        public int PurchaseInvoiceId { get; set; }

        [Required]
        [StringLength(40)]
        public string PaymentNumber { get; set; } = string.Empty;

        public int PaymentMethodId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public int Status { get; set; } = 1; // 1=Completed, 2=Pending, 3=Failed

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        [StringLength(100)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int UserId { get; set; }

        public int? ConfirmedById { get; set; }
        public DateTime? ConfirmedAt { get; set; }

        [StringLength(40)]
        public string? VoucherNumber { get; set; }

        [StringLength(200)]
        public string? ExternalTransactionId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TransactionFee { get; set; } = 0;

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public int? CashBoxId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ConfirmedById")]
        public virtual User? ConfirmedBy { get; set; }
    }
}
