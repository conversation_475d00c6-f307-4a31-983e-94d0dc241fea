using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("📦 Inventory Management")]
    public class InventoryController : ControllerBase
    {
        private readonly AppDbContext _context;

        public InventoryController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("stock")]
        public async Task<ActionResult> GetStock(
            [FromQuery] int? branchId = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] string? search = null)
        {
            try
            {
                var query = _context.ProductStocks
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Category)
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Unit)
                    .Include(ps => ps.Branch)
                    .AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(ps => ps.BranchId == branchId);

                if (categoryId.HasValue)
                    query = query.Where(ps => ps.Product.CategoryId == categoryId);

                if (!string.IsNullOrEmpty(search))
                    query = query.Where(ps => ps.Product.NameAr.Contains(search) || 
                                            ps.Product.ProductCode.Contains(search));

                var stocks = await query
                    .Select(ps => new
                    {
                        ps.Id,
                        ps.ProductId,
                        ProductCode = ps.Product.ProductCode,
                        ProductName = ps.Product.NameAr,
                        CategoryName = ps.Product.Category.NameAr,
                        UnitName = ps.Product.Unit.NameAr,
                        BranchName = ps.Branch.NameAr,
                        ps.AvailableQuantity,
                        ps.ReservedQuantity,
                        ps.OnOrderQuantity,
                        ps.AverageCostPrice,
                        ps.StockValue,
                        ps.BranchMinStock,
                        ps.BranchMaxStock,
                        ps.BranchReorderPoint,
                        IsLowStock = ps.AvailableQuantity <= ps.BranchReorderPoint,
                        ps.LastMovementDate
                    })
                    .OrderBy(ps => ps.ProductName)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات المخزون بنجاح",
                    data = stocks,
                    count = stocks.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("movements")]
        public async Task<ActionResult> GetStockMovements(
            [FromQuery] int? productId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.StockMovements
                    .Include(sm => sm.Product)
                    .Include(sm => sm.Branch)
                    .Include(sm => sm.User)
                    .Where(sm => sm.MovementDate >= startDate && sm.MovementDate <= endDate);

                if (productId.HasValue)
                    query = query.Where(sm => sm.ProductId == productId);

                if (branchId.HasValue)
                    query = query.Where(sm => sm.BranchId == branchId);

                var movements = await query
                    .OrderByDescending(sm => sm.MovementDate)
                    .Select(sm => new
                    {
                        sm.Id,
                        sm.MovementNumber,
                        sm.MovementDate,
                        ProductName = sm.Product.NameAr,
                        BranchName = sm.Branch.NameAr,
                        sm.MovementType,
                        MovementTypeName = sm.MovementType == 1 ? "وارد" : 
                                          sm.MovementType == 2 ? "صادر" : 
                                          sm.MovementType == 3 ? "تحويل" : "تسوية",
                        sm.MovementReason,
                        sm.Quantity,
                        sm.UnitCost,
                        sm.TotalCost,
                        sm.BalanceBefore,
                        sm.BalanceAfter,
                        UserName = sm.User.FullName,
                        sm.Notes
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب حركات المخزون بنجاح",
                    data = movements,
                    count = movements.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("transfers")]
        public async Task<ActionResult> CreateBranchTransfer(CreateBranchTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate transfer number
                var transferNumber = await GetNextTransferNumber();

                var transfer = new BranchTransfer
                {
                    TransferNumber = transferNumber,
                    TransferDate = request.TransferDate ?? DateTime.Now,
                    FromBranchId = request.FromBranchId,
                    ToBranchId = request.ToBranchId,
                    Status = 1, // Pending
                    Notes = request.Notes,
                    RequestedBy = request.UserId,
                    CreatedAt = DateTime.Now
                };

                _context.BranchTransfers.Add(transfer);
                await _context.SaveChangesAsync();

                // Add transfer details
                decimal totalValue = 0;
                int lineNumber = 1;

                foreach (var item in request.Items)
                {
                    var product = await _context.Products.FindAsync(item.ProductId);
                    var stock = await _context.ProductStocks
                        .FirstOrDefaultAsync(ps => ps.ProductId == item.ProductId && ps.BranchId == request.FromBranchId);

                    if (stock == null || stock.AvailableQuantity < item.RequestedQuantity)
                    {
                        throw new Exception($"الكمية المطلوبة غير متوفرة للمنتج {product?.NameAr}");
                    }

                    var transferDetail = new BranchTransferDetail
                    {
                        BranchTransferId = transfer.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        RequestedQuantity = item.RequestedQuantity,
                        UnitCost = stock.AverageCostPrice,
                        TotalCost = item.RequestedQuantity * stock.AverageCostPrice,
                        ItemNotes = item.ItemNotes,
                        CreatedAt = DateTime.Now
                    };

                    _context.BranchTransferDetails.Add(transferDetail);
                    totalValue += transferDetail.TotalCost;
                }

                transfer.TotalValue = totalValue;
                transfer.TotalItems = request.Items.Count;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetBranchTransfers), new { id = transfer.Id }, new
                {
                    success = true,
                    message = "تم إنشاء أذن التحويل بنجاح",
                    data = transfer
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("transfers")]
        public async Task<ActionResult> GetBranchTransfers(
            [FromQuery] int? fromBranchId = null,
            [FromQuery] int? toBranchId = null,
            [FromQuery] int? status = null)
        {
            try
            {
                var query = _context.BranchTransfers
                    .Include(bt => bt.FromBranch)
                    .Include(bt => bt.ToBranch)
                    .Include(bt => bt.RequestedByUser)
                    .Include(bt => bt.BranchTransferDetails)
                        .ThenInclude(btd => btd.Product)
                    .AsQueryable();

                if (fromBranchId.HasValue)
                    query = query.Where(bt => bt.FromBranchId == fromBranchId);

                if (toBranchId.HasValue)
                    query = query.Where(bt => bt.ToBranchId == toBranchId);

                if (status.HasValue)
                    query = query.Where(bt => bt.Status == status);

                var transfers = await query
                    .OrderByDescending(bt => bt.TransferDate)
                    .Select(bt => new
                    {
                        bt.Id,
                        bt.TransferNumber,
                        bt.TransferDate,
                        bt.ReceivedDate,
                        FromBranchName = bt.FromBranch.NameAr,
                        ToBranchName = bt.ToBranch.NameAr,
                        bt.Status,
                        StatusName = bt.Status == 1 ? "في الانتظار" :
                                    bt.Status == 2 ? "تم الإرسال" :
                                    bt.Status == 3 ? "تم الاستلام" : "ملغي",
                        bt.TotalValue,
                        bt.TotalItems,
                        RequestedByName = bt.RequestedByUser.FullName,
                        bt.Notes
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أذون التحويل بنجاح",
                    data = transfers,
                    count = transfers.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("transfers/{id}/send")]
        public async Task<ActionResult> SendTransfer(int id, [FromBody] SendTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var transfer = await _context.BranchTransfers
                    .Include(bt => bt.BranchTransferDetails)
                    .FirstOrDefaultAsync(bt => bt.Id == id);

                if (transfer == null)
                    return NotFound(new { success = false, message = "أذن التحويل غير موجود" });

                if (transfer.Status != 1)
                    return BadRequest(new { success = false, message = "لا يمكن إرسال هذا التحويل" });

                // Update stock for each item
                foreach (var detail in transfer.BranchTransferDetails)
                {
                    // Reduce stock from source branch
                    await UpdateProductStock(detail.ProductId, transfer.FromBranchId, -detail.RequestedQuantity, 
                        detail.UnitCost, "Transfer Out", transfer.TransferNumber, request.UserId);

                    detail.SentQuantity = detail.RequestedQuantity;
                }

                transfer.Status = 2; // Sent
                transfer.SentBy = request.UserId;
                transfer.SentAt = DateTime.Now;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { success = true, message = "تم إرسال التحويل بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("transfers/{id}/receive")]
        public async Task<ActionResult> ReceiveTransfer(int id, [FromBody] ReceiveTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var transfer = await _context.BranchTransfers
                    .Include(bt => bt.BranchTransferDetails)
                    .FirstOrDefaultAsync(bt => bt.Id == id);

                if (transfer == null)
                    return NotFound(new { success = false, message = "أذن التحويل غير موجود" });

                if (transfer.Status != 2)
                    return BadRequest(new { success = false, message = "لا يمكن استلام هذا التحويل" });

                // Update stock for each item
                foreach (var detail in transfer.BranchTransferDetails)
                {
                    // Add stock to destination branch
                    await UpdateProductStock(detail.ProductId, transfer.ToBranchId, detail.SentQuantity, 
                        detail.UnitCost, "Transfer In", transfer.TransferNumber, request.UserId);

                    detail.ReceivedQuantity = detail.SentQuantity;
                }

                transfer.Status = 3; // Received
                transfer.ReceivedBy = request.UserId;
                transfer.ReceivedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { success = true, message = "تم استلام التحويل بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        private async Task<string> GetNextTransferNumber()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.BranchTransfer.ToString());

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.BranchTransfer.ToString(),
                    Prefix = "TRF",
                    CurrentValue = 1,
                    NumberLength = 6,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();
            return $"{counter.Prefix}{DateTime.Now.Year}{counter.CurrentValue:D6}";
        }

        private async Task UpdateProductStock(int productId, int branchId, decimal quantity, decimal unitCost, 
            string reason, string reference, int userId)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            if (stock == null)
            {
                stock = new ProductStock
                {
                    ProductId = productId,
                    BranchId = branchId,
                    AvailableQuantity = 0,
                    AverageCostPrice = unitCost,
                    CreatedAt = DateTime.Now
                };
                _context.ProductStocks.Add(stock);
            }

            var balanceBefore = stock.AvailableQuantity;
            stock.AvailableQuantity += quantity;
            stock.LastMovementDate = DateTime.Now;
            stock.UpdatedAt = DateTime.Now;

            if (quantity > 0)
            {
                // Calculate new average cost for incoming stock
                var totalValue = (stock.AvailableQuantity - quantity) * stock.AverageCostPrice + quantity * unitCost;
                stock.AverageCostPrice = stock.AvailableQuantity > 0 ? totalValue / stock.AvailableQuantity : unitCost;
                stock.LastCostPrice = unitCost;
                stock.TotalInQuantity += quantity;
            }
            else
            {
                stock.TotalOutQuantity += Math.Abs(quantity);
            }

            stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;

            // Create stock movement record
            var movement = new StockMovement
            {
                ProductId = productId,
                BranchId = branchId,
                MovementNumber = reference,
                MovementType = quantity > 0 ? 1 : 2, // 1=In, 2=Out
                MovementReason = reason,
                Quantity = Math.Abs(quantity),
                UnitCost = unitCost,
                TotalCost = Math.Abs(quantity) * unitCost,
                BalanceBefore = balanceBefore,
                BalanceAfter = stock.AvailableQuantity,
                MovementDate = DateTime.Now,
                SourceType = "Transfer",
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            _context.StockMovements.Add(movement);
        }

        [HttpGet("adjustments")]
        public async Task<ActionResult> GetStockAdjustments([FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.StockAdjustments
                    .Include(sa => sa.Branch)
                    .Include(sa => sa.User)
                    .Include(sa => sa.StockAdjustmentDetails)
                        .ThenInclude(sad => sad.Product)
                    .AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(sa => sa.BranchId == branchId);

                var adjustments = await query
                    .OrderByDescending(sa => sa.AdjustmentDate)
                    .Select(sa => new
                    {
                        sa.Id,
                        sa.AdjustmentNumber,
                        sa.AdjustmentDate,
                        sa.AdjustmentReason,
                        BranchName = sa.Branch.NameAr,
                        sa.Status,
                        StatusName = sa.Status == 1 ? "مسودة" : sa.Status == 2 ? "معتمد" : "مرحل",
                        sa.TotalAdjustmentValue,
                        sa.TotalItems,
                        UserName = sa.User.FullName
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب تسويات المخزون بنجاح",
                    data = adjustments
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost("adjustments")]
        public async Task<ActionResult> CreateStockAdjustment(CreateStockAdjustmentRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate adjustment number
                var adjustmentNumber = await GetNextAdjustmentNumber();

                var adjustment = new StockAdjustment
                {
                    AdjustmentNumber = adjustmentNumber,
                    AdjustmentDate = request.AdjustmentDate ?? DateTime.Now,
                    AdjustmentReason = request.AdjustmentReason,
                    Description = request.Description,
                    BranchId = request.BranchId,
                    Status = 1, // Draft
                    UserId = request.UserId,
                    CreatedAt = DateTime.Now
                };

                _context.StockAdjustments.Add(adjustment);
                await _context.SaveChangesAsync();

                // Add adjustment details
                decimal totalValue = 0;
                int lineNumber = 1;

                foreach (var item in request.Items)
                {
                    var stock = await _context.ProductStocks
                        .FirstOrDefaultAsync(ps => ps.ProductId == item.ProductId && ps.BranchId == request.BranchId);

                    var systemQuantity = stock?.AvailableQuantity ?? 0;
                    var adjustmentQuantity = item.PhysicalQuantity - systemQuantity;
                    var adjustmentValue = adjustmentQuantity * item.UnitCost;

                    var adjustmentDetail = new StockAdjustmentDetail
                    {
                        StockAdjustmentId = adjustment.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        SystemQuantity = systemQuantity,
                        PhysicalQuantity = item.PhysicalQuantity,
                        AdjustmentQuantity = adjustmentQuantity,
                        UnitCost = item.UnitCost,
                        AdjustmentValue = adjustmentValue,
                        Reason = item.Reason,
                        Notes = item.Notes,
                        CreatedAt = DateTime.Now
                    };

                    _context.StockAdjustmentDetails.Add(adjustmentDetail);
                    totalValue += Math.Abs(adjustmentValue);
                }

                adjustment.TotalAdjustmentValue = totalValue;
                adjustment.TotalItems = request.Items.Count;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetStockAdjustments), new { id = adjustment.Id }, new
                {
                    success = true,
                    message = "تم إنشاء تسوية المخزون بنجاح",
                    data = adjustment
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        private async Task<string> GetNextAdjustmentNumber()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.StockAdjustment.ToString());

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.StockAdjustment.ToString(),
                    Prefix = "ADJ",
                    CurrentValue = 1,
                    NumberLength = 6,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();
            return $"{counter.Prefix}{DateTime.Now.Year}{counter.CurrentValue:D6}";
        }
    }

    // DTOs
    public class CreateBranchTransferRequest
    {
        public DateTime? TransferDate { get; set; }
        public int FromBranchId { get; set; }
        public int ToBranchId { get; set; }
        public string? Notes { get; set; }
        public int UserId { get; set; } = 1;
        public List<CreateBranchTransferItemRequest> Items { get; set; } = new();
    }

    public class CreateBranchTransferItemRequest
    {
        public int ProductId { get; set; }
        public decimal RequestedQuantity { get; set; }
        public string? ItemNotes { get; set; }
    }

    public class SendTransferRequest
    {
        public int UserId { get; set; } = 1;
    }

    public class ReceiveTransferRequest
    {
        public int UserId { get; set; } = 1;
    }

    public class CreateStockAdjustmentRequest
    {
        public DateTime? AdjustmentDate { get; set; }
        public string AdjustmentReason { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int BranchId { get; set; } = 1;
        public int UserId { get; set; } = 1;
        public List<CreateStockAdjustmentItemRequest> Items { get; set; } = new();
    }

    public class CreateStockAdjustmentItemRequest
    {
        public int ProductId { get; set; }
        public decimal PhysicalQuantity { get; set; }
        public decimal UnitCost { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
    }
}
