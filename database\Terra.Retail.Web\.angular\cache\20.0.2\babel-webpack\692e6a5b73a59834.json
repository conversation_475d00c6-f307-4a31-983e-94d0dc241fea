{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction EditSupplierComponent_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.supplier.nameAr, \" - \", ctx_r0.supplier.supplierCode);\n  }\n}\nfunction EditSupplierComponent_div_22_mat_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r2.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.NameAr, \" \");\n  }\n}\nfunction EditSupplierComponent_div_22_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSupplierComponent_div_22_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSupplierComponent_div_22_mat_error_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSupplierComponent_div_22_mat_option_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", area_r3.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r3.NameAr, \" \");\n  }\n}\nfunction EditSupplierComponent_div_22_mat_error_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSupplierComponent_div_22_mat_error_147_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u062A\\u0643\\u0648\\u0646 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSupplierComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"form\", 15)(2, \"mat-card\", 16)(3, \"mat-card-header\")(4, \"mat-card-title\")(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 17)(11, \"mat-form-field\", 18)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 19);\n    i0.ɵɵelementStart(15, \"mat-icon\", 20);\n    i0.ɵɵtext(16, \"tag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 18)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"mat-select\", 21)(21, \"mat-option\", 22);\n    i0.ɵɵtext(22, \"\\u0627\\u062E\\u062A\\u0631 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, EditSupplierComponent_div_22_mat_option_23_Template, 2, 2, \"mat-option\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-icon\", 20);\n    i0.ɵɵtext(25, \"category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 24)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 25);\n    i0.ɵɵelementStart(30, \"mat-icon\", 20);\n    i0.ɵɵtext(31, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, EditSupplierComponent_div_22_mat_error_32_Template, 2, 0, \"mat-error\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 24)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 27);\n    i0.ɵɵelementStart(37, \"mat-icon\", 20);\n    i0.ɵɵtext(38, \"business\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(39, \"mat-card\", 16)(40, \"mat-card-header\")(41, \"mat-card-title\")(42, \"mat-icon\");\n    i0.ɵɵtext(43, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"mat-card-content\")(47, \"div\", 17)(48, \"mat-form-field\", 18)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u0623\\u0648\\u0644 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 28);\n    i0.ɵɵelementStart(52, \"mat-icon\", 20);\n    i0.ɵɵtext(53, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, EditSupplierComponent_div_22_mat_error_54_Template, 2, 0, \"mat-error\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"mat-form-field\", 18)(56, \"mat-label\");\n    i0.ɵɵtext(57, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"input\", 29);\n    i0.ɵɵelementStart(59, \"mat-icon\", 20);\n    i0.ɵɵtext(60, \"phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"mat-form-field\", 18)(62, \"mat-label\");\n    i0.ɵɵtext(63, \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"input\", 30);\n    i0.ɵɵelementStart(65, \"mat-icon\", 20);\n    i0.ɵɵtext(66, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(67, EditSupplierComponent_div_22_mat_error_67_Template, 2, 0, \"mat-error\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"mat-form-field\", 18)(69, \"mat-label\");\n    i0.ɵɵtext(70, \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"input\", 31);\n    i0.ɵɵelementStart(72, \"mat-icon\", 20);\n    i0.ɵɵtext(73, \"language\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(74, \"mat-card\", 16)(75, \"mat-card-header\")(76, \"mat-card-title\")(77, \"mat-icon\");\n    i0.ɵɵtext(78, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"span\");\n    i0.ɵɵtext(80, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(81, \"mat-card-content\")(82, \"div\", 17)(83, \"mat-form-field\", 18)(84, \"mat-label\");\n    i0.ɵɵtext(85, \"\\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"mat-select\", 32)(87, \"mat-option\", 22);\n    i0.ɵɵtext(88, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(89, EditSupplierComponent_div_22_mat_option_89_Template, 2, 2, \"mat-option\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-icon\", 20);\n    i0.ɵɵtext(91, \"location_city\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(92, \"mat-form-field\", 24)(93, \"mat-label\");\n    i0.ɵɵtext(94, \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(95, \"textarea\", 33);\n    i0.ɵɵelementStart(96, \"mat-icon\", 20);\n    i0.ɵɵtext(97, \"home\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(98, \"mat-card\", 16)(99, \"mat-card-header\")(100, \"mat-card-title\")(101, \"mat-icon\");\n    i0.ɵɵtext(102, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"span\");\n    i0.ɵɵtext(104, \"\\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(105, \"mat-card-content\")(106, \"div\", 17)(107, \"mat-form-field\", 18)(108, \"mat-label\");\n    i0.ɵɵtext(109, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(110, \"input\", 34);\n    i0.ɵɵelementStart(111, \"mat-icon\", 20);\n    i0.ɵɵtext(112, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(113, \"mat-form-field\", 18)(114, \"mat-label\");\n    i0.ɵɵtext(115, \"\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(116, \"input\", 35);\n    i0.ɵɵelementStart(117, \"mat-icon\", 20);\n    i0.ɵɵtext(118, \"phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(119, \"mat-form-field\", 18)(120, \"mat-label\");\n    i0.ɵɵtext(121, \"\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(122, \"input\", 36);\n    i0.ɵɵelementStart(123, \"mat-icon\", 20);\n    i0.ɵɵtext(124, \"email\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(125, \"mat-card\", 16)(126, \"mat-card-header\")(127, \"mat-card-title\")(128, \"mat-icon\");\n    i0.ɵɵtext(129, \"account_balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"span\");\n    i0.ɵɵtext(131, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(132, \"mat-card-content\")(133, \"div\", 17)(134, \"mat-form-field\", 18)(135, \"mat-label\");\n    i0.ɵɵtext(136, \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0633\\u062F\\u0627\\u062F (\\u0628\\u0627\\u0644\\u0623\\u064A\\u0627\\u0645)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(137, \"input\", 37);\n    i0.ɵɵelementStart(138, \"mat-icon\", 20);\n    i0.ɵɵtext(139, \"schedule\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(140, \"mat-form-field\", 18)(141, \"mat-label\");\n    i0.ɵɵtext(142, \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F (\\u0628\\u0627\\u0644\\u0623\\u064A\\u0627\\u0645) *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(143, \"input\", 38);\n    i0.ɵɵelementStart(144, \"mat-icon\", 20);\n    i0.ɵɵtext(145, \"local_shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(146, EditSupplierComponent_div_22_mat_error_146_Template, 2, 0, \"mat-error\", 26)(147, EditSupplierComponent_div_22_mat_error_147_Template, 2, 0, \"mat-error\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(148, \"mat-form-field\", 18)(149, \"mat-label\");\n    i0.ɵɵtext(150, \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(151, \"input\", 39);\n    i0.ɵɵelementStart(152, \"mat-icon\", 20);\n    i0.ɵɵtext(153, \"credit_card\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(154, \"mat-form-field\", 18)(155, \"mat-label\");\n    i0.ɵɵtext(156, \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(157, \"input\", 40);\n    i0.ɵɵelementStart(158, \"mat-icon\", 20);\n    i0.ɵɵtext(159, \"receipt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(160, \"mat-form-field\", 18)(161, \"mat-label\");\n    i0.ɵɵtext(162, \"\\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(163, \"input\", 41);\n    i0.ɵɵelementStart(164, \"mat-icon\", 20);\n    i0.ɵɵtext(165, \"business_center\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(166, \"mat-card\", 16)(167, \"mat-card-header\")(168, \"mat-card-title\")(169, \"mat-icon\");\n    i0.ɵɵtext(170, \"account_balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(171, \"span\");\n    i0.ɵɵtext(172, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0628\\u0646\\u0643\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(173, \"mat-card-content\")(174, \"div\", 17)(175, \"mat-form-field\", 18)(176, \"mat-label\");\n    i0.ɵɵtext(177, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(178, \"input\", 42);\n    i0.ɵɵelementStart(179, \"mat-icon\", 20);\n    i0.ɵɵtext(180, \"account_balance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(181, \"mat-form-field\", 18)(182, \"mat-label\");\n    i0.ɵɵtext(183, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628 \\u0627\\u0644\\u0628\\u0646\\u0643\\u064A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(184, \"input\", 43);\n    i0.ɵɵelementStart(185, \"mat-icon\", 20);\n    i0.ɵɵtext(186, \"credit_card\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(187, \"mat-card\", 16)(188, \"mat-card-header\")(189, \"mat-card-title\")(190, \"mat-icon\");\n    i0.ɵɵtext(191, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(192, \"span\");\n    i0.ɵɵtext(193, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(194, \"mat-card-content\")(195, \"div\", 17)(196, \"mat-form-field\", 18)(197, \"mat-label\");\n    i0.ɵɵtext(198, \"\\u0627\\u0644\\u062A\\u0642\\u064A\\u064A\\u0645\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(199, \"mat-select\", 44)(200, \"mat-option\", 22);\n    i0.ɵɵtext(201, \"\\u0628\\u062F\\u0648\\u0646 \\u062A\\u0642\\u064A\\u064A\\u0645\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(202, \"mat-option\", 45);\n    i0.ɵɵtext(203, \"\\u2B50 (1/5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(204, \"mat-option\", 46);\n    i0.ɵɵtext(205, \"\\u2B50\\u2B50 (2/5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(206, \"mat-option\", 47);\n    i0.ɵɵtext(207, \"\\u2B50\\u2B50\\u2B50 (3/5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(208, \"mat-option\", 48);\n    i0.ɵɵtext(209, \"\\u2B50\\u2B50\\u2B50\\u2B50 (4/5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(210, \"mat-option\", 49);\n    i0.ɵɵtext(211, \"\\u2B50\\u2B50\\u2B50\\u2B50\\u2B50 (5/5)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(212, \"mat-icon\", 20);\n    i0.ɵɵtext(213, \"star\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(214, \"div\", 50)(215, \"mat-checkbox\", 51);\n    i0.ɵɵtext(216, \" \\u0645\\u0648\\u0631\\u062F \\u0646\\u0634\\u0637 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(217, \"mat-form-field\", 24)(218, \"mat-label\");\n    i0.ɵɵtext(219, \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(220, \"textarea\", 52);\n    i0.ɵɵelementStart(221, \"mat-icon\", 20);\n    i0.ɵɵtext(222, \"note\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.supplierForm);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.supplierTypes);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.supplierForm.get(\"nameAr\")) == null ? null : tmp_3_0.hasError(\"required\"));\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r0.supplierForm.get(\"phone1\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r0.supplierForm.get(\"email\")) == null ? null : tmp_5_0.hasError(\"email\"));\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.areas);\n    i0.ɵɵadvance(57);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_7_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_8_0.hasError(\"min\"));\n  }\n}\nfunction EditSupplierComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"mat-spinner\", 55);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let EditSupplierComponent = /*#__PURE__*/(() => {\n  class EditSupplierComponent {\n    fb;\n    router;\n    route;\n    http;\n    snackBar;\n    // Component State\n    isLoading = false;\n    supplierForm;\n    supplierId = 0;\n    // Data\n    supplier = null;\n    supplierTypes = [];\n    areas = [];\n    // Subscriptions\n    subscriptions = [];\n    constructor(fb, router, route, http, snackBar) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.http = http;\n      this.snackBar = snackBar;\n      this.supplierForm = this.createForm();\n    }\n    ngOnInit() {\n      this.supplierId = Number(this.route.snapshot.paramMap.get('id'));\n      if (this.supplierId) {\n        this.loadSupplier();\n        this.loadInitialData();\n      } else {\n        this.router.navigate(['/suppliers']);\n      }\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Create reactive form\n     */\n    createForm() {\n      return this.fb.group({\n        supplierCode: ['', [Validators.required]],\n        nameAr: ['', [Validators.required, Validators.minLength(2)]],\n        nameEn: [''],\n        supplierTypeId: [''],\n        phone1: ['', [Validators.required]],\n        phone2: [''],\n        email: ['', [Validators.email]],\n        website: [''],\n        address: [''],\n        areaId: [''],\n        contactPersonName: [''],\n        contactPersonPhone: [''],\n        contactPersonEmail: ['', [Validators.email]],\n        paymentTerms: [30, [Validators.required, Validators.min(0)]],\n        deliveryDays: [7, [Validators.required, Validators.min(1)]],\n        creditLimit: [0, [Validators.required, Validators.min(0)]],\n        taxNumber: [''],\n        commercialRegister: [''],\n        bankName: [''],\n        bankAccountNumber: [''],\n        rating: [''],\n        notes: [''],\n        isActive: [true]\n      });\n    }\n    /**\n     * Load supplier data\n     */\n    loadSupplier() {\n      this.isLoading = true;\n      const sub = this.http.get(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({\n        next: response => {\n          console.log('API Response:', response);\n          console.log('Supplier Data:', response.supplier);\n          this.supplier = response.supplier;\n          this.populateForm();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading supplier:', error);\n          this.showError('خطأ في تحميل بيانات المورد');\n          this.isLoading = false;\n          this.router.navigate(['/suppliers']);\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Populate form with supplier data\n     */\n    populateForm() {\n      if (this.supplier) {\n        const data = this.supplier; // Use any to avoid TypeScript issues\n        this.supplierForm.patchValue({\n          supplierCode: data.SupplierCode || data.supplierCode || '',\n          nameAr: data.NameAr || data.nameAr || '',\n          nameEn: data.NameEn || data.nameEn || '',\n          supplierTypeId: data.SupplierTypeId || data.supplierTypeId || '',\n          phone1: data.Phone1 || data.phone1 || '',\n          phone2: data.Phone2 || data.phone2 || '',\n          email: data.Email || data.email || '',\n          website: data.Website || data.website || '',\n          address: data.Address || data.address || '',\n          areaId: data.AreaId || data.areaId || '',\n          contactPersonName: data.ContactPersonName || data.contactPersonName || '',\n          contactPersonPhone: data.ContactPersonPhone || data.contactPersonPhone || '',\n          contactPersonEmail: data.ContactPersonEmail || data.contactPersonEmail || '',\n          paymentTerms: data.PaymentTerms || data.paymentTerms || 30,\n          deliveryDays: data.DeliveryDays || data.deliveryDays || 7,\n          creditLimit: data.CreditLimit || data.creditLimit || 0,\n          taxNumber: data.TaxNumber || data.taxNumber || '',\n          commercialRegister: data.CommercialRegister || data.commercialRegister || '',\n          bankName: data.BankName || data.bankName || '',\n          bankAccountNumber: data.BankAccountNumber || data.bankAccountNumber || '',\n          rating: data.Rating || data.rating || '',\n          notes: data.Notes || data.notes || '',\n          isActive: data.IsActive !== undefined ? data.IsActive : data.isActive !== false\n        });\n      }\n    }\n    /**\n     * Load initial data\n     */\n    loadInitialData() {\n      Promise.all([this.loadSupplierTypes(), this.loadAreas()]).catch(error => {\n        console.error('Error loading initial data:', error);\n      });\n    }\n    /**\n     * Load supplier types\n     */\n    loadSupplierTypes() {\n      return new Promise(resolve => {\n        const sub = this.http.get('http://localhost:5127/api/simple/supplier-types').subscribe({\n          next: response => {\n            this.supplierTypes = response.supplierTypes || [];\n            resolve();\n          },\n          error: () => {\n            this.supplierTypes = this.getMockSupplierTypes();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load areas\n     */\n    loadAreas() {\n      return new Promise(resolve => {\n        const sub = this.http.get('http://localhost:5127/api/simple/areas-db').subscribe({\n          next: response => {\n            this.areas = response.areas || [];\n            resolve();\n          },\n          error: () => {\n            this.areas = this.getMockAreas();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Update supplier\n     */\n    updateSupplier() {\n      if (this.supplierForm.valid) {\n        this.isLoading = true;\n        const formValue = this.supplierForm.value;\n        const request = {\n          nameAr: formValue.nameAr,\n          nameEn: formValue.nameEn || null,\n          supplierTypeId: formValue.supplierTypeId || null,\n          phone1: formValue.phone1,\n          phone2: formValue.phone2 || null,\n          email: formValue.email || null,\n          website: formValue.website || null,\n          address: formValue.address || null,\n          areaId: formValue.areaId || null,\n          contactPersonName: formValue.contactPersonName || null,\n          contactPersonPhone: formValue.contactPersonPhone || null,\n          contactPersonEmail: formValue.contactPersonEmail || null,\n          paymentTerms: formValue.paymentTerms,\n          deliveryDays: formValue.deliveryDays,\n          creditLimit: formValue.creditLimit,\n          taxNumber: formValue.taxNumber || null,\n          commercialRegister: formValue.commercialRegister || null,\n          bankName: formValue.bankName || null,\n          bankAccountNumber: formValue.bankAccountNumber || null,\n          rating: formValue.rating || null,\n          notes: formValue.notes || null,\n          isActive: formValue.isActive\n        };\n        const sub = this.http.put(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`, request).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess('تم تحديث بيانات المورد بنجاح');\n            this.router.navigate(['/suppliers']);\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error updating supplier:', error);\n            this.showError('خطأ في تحديث بيانات المورد');\n          }\n        });\n        this.subscriptions.push(sub);\n      } else {\n        this.markFormGroupTouched();\n        this.showError('يرجى تصحيح الأخطاء في النموذج');\n      }\n    }\n    /**\n     * Mark all form fields as touched\n     */\n    markFormGroupTouched() {\n      Object.keys(this.supplierForm.controls).forEach(key => {\n        const control = this.supplierForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    /**\n     * Go back to suppliers list\n     */\n    goBack() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Cancel and go back\n     */\n    cancel() {\n      this.goBack();\n    }\n    /**\n     * Show success message\n     */\n    showSuccess(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        panelClass: ['success-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    /**\n     * Show error message\n     */\n    showError(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 5000,\n        panelClass: ['error-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    /**\n     * Get mock supplier types\n     */\n    getMockSupplierTypes() {\n      return [{\n        Id: 1,\n        NameAr: 'مورد محلي',\n        NameEn: 'Local Supplier'\n      }, {\n        Id: 2,\n        NameAr: 'مورد دولي',\n        NameEn: 'International Supplier'\n      }, {\n        Id: 3,\n        NameAr: 'مورد حكومي',\n        NameEn: 'Government Supplier'\n      }];\n    }\n    /**\n     * Get mock areas\n     */\n    getMockAreas() {\n      return [{\n        Id: 1,\n        NameAr: 'القاهرة',\n        NameEn: 'Cairo',\n        Code: 'CAI'\n      }, {\n        Id: 2,\n        NameAr: 'الجيزة',\n        NameEn: 'Giza',\n        Code: 'GIZ'\n      }, {\n        Id: 3,\n        NameAr: 'الإسكندرية',\n        NameEn: 'Alexandria',\n        Code: 'ALX'\n      }, {\n        Id: 4,\n        NameAr: 'الدقهلية',\n        NameEn: 'Dakahlia',\n        Code: 'DKH'\n      }, {\n        Id: 5,\n        NameAr: 'الشرقية',\n        NameEn: 'Sharqia',\n        Code: 'SHR'\n      }, {\n        Id: 6,\n        NameAr: 'القليوبية',\n        NameEn: 'Qalyubia',\n        Code: 'QLY'\n      }];\n    }\n    static ɵfac = function EditSupplierComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditSupplierComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditSupplierComponent,\n      selectors: [[\"app-edit-supplier\"]],\n      decls: 24,\n      vars: 4,\n      consts: [[1, \"edit-supplier-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"header-text\"], [1, \"page-title\"], [\"class\", \"page-subtitle\", 4, \"ngIf\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 1, \"cancel-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-btn\", 3, \"click\", \"disabled\"], [\"class\", \"form-content\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"page-subtitle\"], [1, \"form-content\"], [1, \"supplier-form\", 3, \"formGroup\"], [1, \"form-card\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"supplierCode\", \"readonly\", \"\"], [\"matSuffix\", \"\"], [\"formControlName\", \"supplierTypeId\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"nameAr\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"nameEn\", \"placeholder\", \"Enter supplier name in English\"], [\"matInput\", \"\", \"formControlName\", \"phone1\", \"placeholder\", \"+201234567890\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"phone2\", \"placeholder\", \"+201234567890\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"www.supplier.com\"], [\"formControlName\", \"areaId\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"3\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A \\u0644\\u0644\\u0645\\u0648\\u0631\\u062F\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonPhone\", \"placeholder\", \"+201234567890\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonEmail\", \"type\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"paymentTerms\", \"type\", \"number\", \"placeholder\", \"30\"], [\"matInput\", \"\", \"formControlName\", \"deliveryDays\", \"type\", \"number\", \"placeholder\", \"7\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"creditLimit\", \"type\", \"number\", \"placeholder\", \"0.00\"], [\"matInput\", \"\", \"formControlName\", \"taxNumber\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"], [\"matInput\", \"\", \"formControlName\", \"commercialRegister\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\"], [\"matInput\", \"\", \"formControlName\", \"bankName\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643\"], [\"matInput\", \"\", \"formControlName\", \"bankAccountNumber\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"], [\"formControlName\", \"rating\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [1, \"checkbox-field\"], [\"formControlName\", \"isActive\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"4\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0623\\u064A \\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629 \\u0639\\u0646 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"], [3, \"value\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function EditSupplierComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function EditSupplierComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n          i0.ɵɵtext(9, \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, EditSupplierComponent_p_10_Template, 2, 2, \"p\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function EditSupplierComponent_Template_button_click_12_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelementStart(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"\\u0625\\u0644\\u063A\\u0627\\u0621\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function EditSupplierComponent_Template_button_click_17_listener() {\n            return ctx.updateSupplier();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u0639\\u062F\\u064A\\u0644\\u0627\\u062A\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(22, EditSupplierComponent_div_22_Template, 223, 8, \"div\", 11)(23, EditSupplierComponent_div_23_Template, 4, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.supplier);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", !ctx.supplierForm.valid || ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, MatButtonModule, i7.MatButton, i7.MatIconButton, MatIconModule, i8.MatIcon, MatFormFieldModule, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, MatInputModule, i10.MatInput, MatSelectModule, i11.MatSelect, i11.MatOption, MatCheckboxModule, i12.MatCheckbox, MatProgressSpinnerModule, i13.MatProgressSpinner, MatSnackBarModule],\n      styles: [\"\\n\\n.edit-supplier-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-600) 0%, var(--primary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2) !important;\\n  color: white !important;\\n  width: 48px !important;\\n  height: 48px !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  align-items: center;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n  background: var(--warning-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: var(--warning-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:disabled {\\n  background: var(--gray-400) !important;\\n  color: var(--gray-600) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-color: white !important;\\n}\\n\\n\\n\\n.form-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) 0;\\n}\\n\\n.supplier-form[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-2xl);\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  overflow: hidden !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-xl) var(--spacing-2xl) !important;\\n  border-bottom: 1px solid var(--gray-200) !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.25rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--warning-600) !important;\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) !important;\\n}\\n\\n\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-xl);\\n  align-items: start;\\n}\\n.form-grid[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n\\n\\n\\n.mat-mdc-form-field[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: white !important;\\n  border-radius: var(--radius-lg) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-sm) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 3px var(--warning-100) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n  font-weight: 500 !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-input-element[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-icon-suffix[_ngcontent-%COMP%] {\\n  color: var(--gray-500) !important;\\n}\\n\\n\\n\\n.checkbox-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 56px;\\n  padding: var(--spacing-md);\\n  border: 2px solid var(--gray-200);\\n  border-radius: var(--radius-lg);\\n  background: white;\\n  transition: all var(--transition-normal);\\n}\\n.checkbox-field[_ngcontent-%COMP%]:hover {\\n  border-color: var(--warning-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n.checkbox-field[_ngcontent-%COMP%]   .mat-mdc-checkbox[_ngcontent-%COMP%]   .mdc-checkbox[_ngcontent-%COMP%]   .mdc-checkbox__native-control[_ngcontent-%COMP%]:enabled:checked    ~ .mdc-checkbox__background[_ngcontent-%COMP%] {\\n  background-color: var(--warning-500) !important;\\n  border-color: var(--warning-500) !important;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   .mat-mdc-checkbox[_ngcontent-%COMP%]   .mdc-form-field[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n  font-weight: 500 !important;\\n  color: var(--gray-800) !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  font-size: 1.125rem;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   .mat-mdc-progress-spinner[_ngcontent-%COMP%] {\\n  --mdc-circular-progress-active-indicator-color: var(--warning-500);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .form-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) 0;\\n  }\\n  .supplier-form[_ngcontent-%COMP%] {\\n    gap: var(--spacing-xl);\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n    font-size: 1.125rem !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) var(--spacing-lg) !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) !important;\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    gap: var(--spacing-md);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return EditSupplierComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatSnackBarModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "supplier", "nameAr", "supplierCode", "ɵɵproperty", "type_r2", "Id", "ɵɵtextInterpolate1", "NameAr", "area_r3", "ɵɵelement", "ɵɵtemplate", "EditSupplierComponent_div_22_mat_option_23_Template", "EditSupplierComponent_div_22_mat_error_32_Template", "EditSupplierComponent_div_22_mat_error_54_Template", "EditSupplierComponent_div_22_mat_error_67_Template", "EditSupplierComponent_div_22_mat_option_89_Template", "EditSupplierComponent_div_22_mat_error_146_Template", "EditSupplierComponent_div_22_mat_error_147_Template", "supplierForm", "supplierTypes", "tmp_3_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_4_0", "tmp_5_0", "areas", "tmp_7_0", "tmp_8_0", "EditSupplierComponent", "fb", "router", "route", "http", "snackBar", "isLoading", "supplierId", "subscriptions", "constructor", "createForm", "ngOnInit", "Number", "snapshot", "paramMap", "loadSupplier", "loadInitialData", "navigate", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "nameEn", "supplierTypeId", "phone1", "phone2", "email", "website", "address", "areaId", "contactPersonName", "contactPersonPhone", "contactPersonEmail", "paymentTerms", "min", "deliveryDays", "creditLimit", "taxNumber", "commercialRegister", "bankName", "bankAccountNumber", "rating", "notes", "isActive", "subscribe", "next", "response", "console", "log", "populateForm", "error", "showError", "push", "data", "patchValue", "SupplierCode", "NameEn", "SupplierTypeId", "Phone1", "Phone2", "Email", "Website", "Address", "AreaId", "ContactPersonName", "ContactPersonPhone", "ContactPersonEmail", "PaymentTerms", "DeliveryDays", "CreditLimit", "TaxNumber", "CommercialRegister", "BankName", "BankAccountNumber", "Rating", "Notes", "IsActive", "undefined", "Promise", "all", "loadSupplierTypes", "loadAreas", "catch", "resolve", "getMockSupplierTypes", "getMockAreas", "updateSupplier", "valid", "formValue", "value", "request", "put", "showSuccess", "markFormGroupTouched", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "goBack", "cancel", "message", "open", "duration", "panelClass", "horizontalPosition", "verticalPosition", "Code", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "HttpClient", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "EditSupplierComponent_Template", "rf", "ctx", "ɵɵlistener", "EditSupplierComponent_Template_button_click_4_listener", "EditSupplierComponent_p_10_Template", "EditSupplierComponent_Template_button_click_12_listener", "EditSupplierComponent_Template_button_click_17_listener", "EditSupplierComponent_div_22_Template", "EditSupplierComponent_div_23_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i6", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i7", "MatButton", "MatIconButton", "i8", "MatIcon", "i9", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i10", "MatInput", "i11", "MatSelect", "MatOption", "i12", "MatCheckbox", "i13", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\edit-supplier\\edit-supplier.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\edit-supplier\\edit-supplier.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\n\n// Interfaces\ninterface Supplier {\n  id: number;\n  supplierCode: string;\n  nameAr: string;\n  nameEn?: string;\n  supplierTypeId?: number;\n  phone1: string;\n  phone2?: string;\n  email?: string;\n  website?: string;\n  address?: string;\n  areaId?: number;\n  contactPersonName?: string;\n  contactPersonPhone?: string;\n  contactPersonEmail?: string;\n  paymentTerms: number;\n  deliveryDays: number;\n  creditLimit: number;\n  openingBalance: number;\n  currentBalance: number;\n  taxNumber?: string;\n  commercialRegister?: string;\n  bankName?: string;\n  bankAccountNumber?: string;\n  rating?: number;\n  notes?: string;\n  isActive: boolean;\n}\n\ninterface SupplierType {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n}\n\ninterface Area {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n  Code: string;\n}\n\n@Component({\n  selector: 'app-edit-supplier',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule\n  ],\n  templateUrl: './edit-supplier.component.html',\n  styleUrls: ['./edit-supplier.component.scss']\n})\nexport class EditSupplierComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  supplierForm: FormGroup;\n  supplierId: number = 0;\n  \n  // Data\n  supplier: Supplier | null = null;\n  supplierTypes: SupplierType[] = [];\n  areas: Area[] = [];\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private fb: FormBuilder,\n    private router: Router,\n    private route: ActivatedRoute,\n    private http: HttpClient,\n    private snackBar: MatSnackBar\n  ) {\n    this.supplierForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.supplierId = Number(this.route.snapshot.paramMap.get('id'));\n    if (this.supplierId) {\n      this.loadSupplier();\n      this.loadInitialData();\n    } else {\n      this.router.navigate(['/suppliers']);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      supplierCode: ['', [Validators.required]],\n      nameAr: ['', [Validators.required, Validators.minLength(2)]],\n      nameEn: [''],\n      supplierTypeId: [''],\n      phone1: ['', [Validators.required]],\n      phone2: [''],\n      email: ['', [Validators.email]],\n      website: [''],\n      address: [''],\n      areaId: [''],\n      contactPersonName: [''],\n      contactPersonPhone: [''],\n      contactPersonEmail: ['', [Validators.email]],\n      paymentTerms: [30, [Validators.required, Validators.min(0)]],\n      deliveryDays: [7, [Validators.required, Validators.min(1)]],\n      creditLimit: [0, [Validators.required, Validators.min(0)]],\n      taxNumber: [''],\n      commercialRegister: [''],\n      bankName: [''],\n      bankAccountNumber: [''],\n      rating: [''],\n      notes: [''],\n      isActive: [true]\n    });\n  }\n\n  /**\n   * Load supplier data\n   */\n  private loadSupplier(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`).subscribe({\n      next: (response) => {\n        console.log('API Response:', response);\n        console.log('Supplier Data:', response.supplier);\n        this.supplier = response.supplier;\n        this.populateForm();\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading supplier:', error);\n        this.showError('خطأ في تحميل بيانات المورد');\n        this.isLoading = false;\n        this.router.navigate(['/suppliers']);\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Populate form with supplier data\n   */\n  private populateForm(): void {\n    if (this.supplier) {\n      const data = this.supplier as any; // Use any to avoid TypeScript issues\n      this.supplierForm.patchValue({\n        supplierCode: data.SupplierCode || data.supplierCode || '',\n        nameAr: data.NameAr || data.nameAr || '',\n        nameEn: data.NameEn || data.nameEn || '',\n        supplierTypeId: data.SupplierTypeId || data.supplierTypeId || '',\n        phone1: data.Phone1 || data.phone1 || '',\n        phone2: data.Phone2 || data.phone2 || '',\n        email: data.Email || data.email || '',\n        website: data.Website || data.website || '',\n        address: data.Address || data.address || '',\n        areaId: data.AreaId || data.areaId || '',\n        contactPersonName: data.ContactPersonName || data.contactPersonName || '',\n        contactPersonPhone: data.ContactPersonPhone || data.contactPersonPhone || '',\n        contactPersonEmail: data.ContactPersonEmail || data.contactPersonEmail || '',\n        paymentTerms: data.PaymentTerms || data.paymentTerms || 30,\n        deliveryDays: data.DeliveryDays || data.deliveryDays || 7,\n        creditLimit: data.CreditLimit || data.creditLimit || 0,\n        taxNumber: data.TaxNumber || data.taxNumber || '',\n        commercialRegister: data.CommercialRegister || data.commercialRegister || '',\n        bankName: data.BankName || data.bankName || '',\n        bankAccountNumber: data.BankAccountNumber || data.bankAccountNumber || '',\n        rating: data.Rating || data.rating || '',\n        notes: data.Notes || data.notes || '',\n        isActive: data.IsActive !== undefined ? data.IsActive : (data.isActive !== false)\n      });\n    }\n  }\n\n  /**\n   * Load initial data\n   */\n  private loadInitialData(): void {\n    Promise.all([\n      this.loadSupplierTypes(),\n      this.loadAreas()\n    ]).catch(error => {\n      console.error('Error loading initial data:', error);\n    });\n  }\n\n  /**\n   * Load supplier types\n   */\n  private loadSupplierTypes(): Promise<void> {\n    return new Promise((resolve) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({\n        next: (response) => {\n          this.supplierTypes = response.supplierTypes || [];\n          resolve();\n        },\n        error: () => {\n          this.supplierTypes = this.getMockSupplierTypes();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load areas\n   */\n  private loadAreas(): Promise<void> {\n    return new Promise((resolve) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/areas-db').subscribe({\n        next: (response) => {\n          this.areas = response.areas || [];\n          resolve();\n        },\n        error: () => {\n          this.areas = this.getMockAreas();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Update supplier\n   */\n  updateSupplier(): void {\n    if (this.supplierForm.valid) {\n      this.isLoading = true;\n      \n      const formValue = this.supplierForm.value;\n      const request = {\n        nameAr: formValue.nameAr,\n        nameEn: formValue.nameEn || null,\n        supplierTypeId: formValue.supplierTypeId || null,\n        phone1: formValue.phone1,\n        phone2: formValue.phone2 || null,\n        email: formValue.email || null,\n        website: formValue.website || null,\n        address: formValue.address || null,\n        areaId: formValue.areaId || null,\n        contactPersonName: formValue.contactPersonName || null,\n        contactPersonPhone: formValue.contactPersonPhone || null,\n        contactPersonEmail: formValue.contactPersonEmail || null,\n        paymentTerms: formValue.paymentTerms,\n        deliveryDays: formValue.deliveryDays,\n        creditLimit: formValue.creditLimit,\n        taxNumber: formValue.taxNumber || null,\n        commercialRegister: formValue.commercialRegister || null,\n        bankName: formValue.bankName || null,\n        bankAccountNumber: formValue.bankAccountNumber || null,\n        rating: formValue.rating || null,\n        notes: formValue.notes || null,\n        isActive: formValue.isActive\n      };\n\n      const sub = this.http.put<any>(`http://localhost:5127/api/simple/suppliers/${this.supplierId}`, request).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.showSuccess('تم تحديث بيانات المورد بنجاح');\n          this.router.navigate(['/suppliers']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error updating supplier:', error);\n          this.showError('خطأ في تحديث بيانات المورد');\n        }\n      });\n      this.subscriptions.push(sub);\n    } else {\n      this.markFormGroupTouched();\n      this.showError('يرجى تصحيح الأخطاء في النموذج');\n    }\n  }\n\n  /**\n   * Mark all form fields as touched\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.supplierForm.controls).forEach(key => {\n      const control = this.supplierForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Go back to suppliers list\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Cancel and go back\n   */\n  cancel(): void {\n    this.goBack();\n  }\n\n  /**\n   * Show success message\n   */\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      panelClass: ['success-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Show error message\n   */\n  private showError(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 5000,\n      panelClass: ['error-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Get mock supplier types\n   */\n  private getMockSupplierTypes(): SupplierType[] {\n    return [\n      { Id: 1, NameAr: 'مورد محلي', NameEn: 'Local Supplier' },\n      { Id: 2, NameAr: 'مورد دولي', NameEn: 'International Supplier' },\n      { Id: 3, NameAr: 'مورد حكومي', NameEn: 'Government Supplier' }\n    ];\n  }\n\n  /**\n   * Get mock areas\n   */\n  private getMockAreas(): Area[] {\n    return [\n      { Id: 1, NameAr: 'القاهرة', NameEn: 'Cairo', Code: 'CAI' },\n      { Id: 2, NameAr: 'الجيزة', NameEn: 'Giza', Code: 'GIZ' },\n      { Id: 3, NameAr: 'الإسكندرية', NameEn: 'Alexandria', Code: 'ALX' },\n      { Id: 4, NameAr: 'الدقهلية', NameEn: 'Dakahlia', Code: 'DKH' },\n      { Id: 5, NameAr: 'الشرقية', NameEn: 'Sharqia', Code: 'SHR' },\n      { Id: 6, NameAr: 'القليوبية', NameEn: 'Qalyubia', Code: 'QLY' }\n    ];\n  }\n}\n", "<!-- Terra Retail ERP - Edit Supplier Form -->\n<div class=\"edit-supplier-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">تعديل بيانات المورد</h1>\n          <p class=\"page-subtitle\" *ngIf=\"supplier\">{{ supplier.nameAr }} - {{ supplier.supplierCode }}</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-stroked-button class=\"cancel-btn\" (click)=\"cancel()\">\n          <mat-icon>close</mat-icon>\n          <span>إلغاء</span>\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"save-btn\" \n                [disabled]=\"!supplierForm.valid || isLoading\"\n                (click)=\"updateSupplier()\">\n          <mat-icon>save</mat-icon>\n          <span>حفظ التعديلات</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Form Content -->\n  <div class=\"form-content\" *ngIf=\"!isLoading\">\n    <form [formGroup]=\"supplierForm\" class=\"supplier-form\">\n      \n      <!-- Basic Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>business</mat-icon>\n            <span>المعلومات الأساسية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Supplier Code -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>كود المورد</mat-label>\n              <input matInput formControlName=\"supplierCode\" readonly>\n              <mat-icon matSuffix>tag</mat-icon>\n            </mat-form-field>\n\n            <!-- Supplier Type -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>نوع المورد</mat-label>\n              <mat-select formControlName=\"supplierTypeId\">\n                <mat-option value=\"\">اختر نوع المورد</mat-option>\n                <mat-option *ngFor=\"let type of supplierTypes\" [value]=\"type.Id\">\n                  {{ type.NameAr }}\n                </mat-option>\n              </mat-select>\n              <mat-icon matSuffix>category</mat-icon>\n            </mat-form-field>\n\n            <!-- Arabic Name -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>اسم المورد بالعربية *</mat-label>\n              <input matInput formControlName=\"nameAr\" placeholder=\"أدخل اسم المورد بالعربية\" required>\n              <mat-icon matSuffix>business</mat-icon>\n              <mat-error *ngIf=\"supplierForm.get('nameAr')?.hasError('required')\">\n                اسم المورد مطلوب\n              </mat-error>\n            </mat-form-field>\n\n            <!-- English Name -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>اسم المورد بالإنجليزية</mat-label>\n              <input matInput formControlName=\"nameEn\" placeholder=\"Enter supplier name in English\">\n              <mat-icon matSuffix>business</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Contact Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>contact_phone</mat-icon>\n            <span>معلومات الاتصال</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Phone 1 -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الهاتف الأول *</mat-label>\n              <input matInput formControlName=\"phone1\" placeholder=\"+201234567890\" required>\n              <mat-icon matSuffix>phone</mat-icon>\n              <mat-error *ngIf=\"supplierForm.get('phone1')?.hasError('required')\">\n                رقم الهاتف مطلوب\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Phone 2 -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الهاتف الثاني</mat-label>\n              <input matInput formControlName=\"phone2\" placeholder=\"+201234567890\">\n              <mat-icon matSuffix>phone</mat-icon>\n            </mat-form-field>\n\n            <!-- Email -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>البريد الإلكتروني</mat-label>\n              <input matInput formControlName=\"email\" type=\"email\" placeholder=\"<EMAIL>\">\n              <mat-icon matSuffix>email</mat-icon>\n              <mat-error *ngIf=\"supplierForm.get('email')?.hasError('email')\">\n                البريد الإلكتروني غير صحيح\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Website -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الموقع الإلكتروني</mat-label>\n              <input matInput formControlName=\"website\" placeholder=\"www.supplier.com\">\n              <mat-icon matSuffix>language</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Address Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>location_on</mat-icon>\n            <span>معلومات العنوان</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Area -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>المحافظة</mat-label>\n              <mat-select formControlName=\"areaId\">\n                <mat-option value=\"\">اختر المحافظة</mat-option>\n                <mat-option *ngFor=\"let area of areas\" [value]=\"area.Id\">\n                  {{ area.NameAr }}\n                </mat-option>\n              </mat-select>\n              <mat-icon matSuffix>location_city</mat-icon>\n            </mat-form-field>\n\n            <!-- Address -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>العنوان التفصيلي</mat-label>\n              <textarea matInput formControlName=\"address\" rows=\"3\" \n                        placeholder=\"أدخل العنوان التفصيلي للمورد\"></textarea>\n              <mat-icon matSuffix>home</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Contact Person Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>person</mat-icon>\n            <span>الشخص المسؤول</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Contact Person Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم الشخص المسؤول</mat-label>\n              <input matInput formControlName=\"contactPersonName\" placeholder=\"أدخل اسم الشخص المسؤول\">\n              <mat-icon matSuffix>person</mat-icon>\n            </mat-form-field>\n\n            <!-- Contact Person Phone -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>هاتف الشخص المسؤول</mat-label>\n              <input matInput formControlName=\"contactPersonPhone\" placeholder=\"+201234567890\">\n              <mat-icon matSuffix>phone</mat-icon>\n            </mat-form-field>\n\n            <!-- Contact Person Email -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>بريد الشخص المسؤول</mat-label>\n              <input matInput formControlName=\"contactPersonEmail\" type=\"email\" placeholder=\"<EMAIL>\">\n              <mat-icon matSuffix>email</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Financial Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>account_balance</mat-icon>\n            <span>المعلومات المالية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Payment Terms -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>مدة السداد (بالأيام)</mat-label>\n              <input matInput formControlName=\"paymentTerms\" type=\"number\" placeholder=\"30\">\n              <mat-icon matSuffix>schedule</mat-icon>\n            </mat-form-field>\n\n            <!-- Delivery Days -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>مدة التوريد (بالأيام) *</mat-label>\n              <input matInput formControlName=\"deliveryDays\" type=\"number\" placeholder=\"7\" required>\n              <mat-icon matSuffix>local_shipping</mat-icon>\n              <mat-error *ngIf=\"supplierForm.get('deliveryDays')?.hasError('required')\">\n                مدة التوريد مطلوبة\n              </mat-error>\n              <mat-error *ngIf=\"supplierForm.get('deliveryDays')?.hasError('min')\">\n                مدة التوريد يجب أن تكون يوم واحد على الأقل\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Credit Limit -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الحد الائتماني</mat-label>\n              <input matInput formControlName=\"creditLimit\" type=\"number\" placeholder=\"0.00\">\n              <mat-icon matSuffix>credit_card</mat-icon>\n            </mat-form-field>\n\n            <!-- Tax Number -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الرقم الضريبي</mat-label>\n              <input matInput formControlName=\"taxNumber\" placeholder=\"أدخل الرقم الضريبي\">\n              <mat-icon matSuffix>receipt</mat-icon>\n            </mat-form-field>\n\n            <!-- Commercial Register -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>السجل التجاري</mat-label>\n              <input matInput formControlName=\"commercialRegister\" placeholder=\"أدخل رقم السجل التجاري\">\n              <mat-icon matSuffix>business_center</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Bank Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>account_balance</mat-icon>\n            <span>المعلومات البنكية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Bank Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم البنك</mat-label>\n              <input matInput formControlName=\"bankName\" placeholder=\"أدخل اسم البنك\">\n              <mat-icon matSuffix>account_balance</mat-icon>\n            </mat-form-field>\n\n            <!-- Bank Account Number -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>رقم الحساب البنكي</mat-label>\n              <input matInput formControlName=\"bankAccountNumber\" placeholder=\"أدخل رقم الحساب\">\n              <mat-icon matSuffix>credit_card</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Additional Information Card -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>info</mat-icon>\n            <span>معلومات إضافية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n            \n            <!-- Rating -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>التقييم</mat-label>\n              <mat-select formControlName=\"rating\">\n                <mat-option value=\"\">بدون تقييم</mat-option>\n                <mat-option value=\"1\">⭐ (1/5)</mat-option>\n                <mat-option value=\"2\">⭐⭐ (2/5)</mat-option>\n                <mat-option value=\"3\">⭐⭐⭐ (3/5)</mat-option>\n                <mat-option value=\"4\">⭐⭐⭐⭐ (4/5)</mat-option>\n                <mat-option value=\"5\">⭐⭐⭐⭐⭐ (5/5)</mat-option>\n              </mat-select>\n              <mat-icon matSuffix>star</mat-icon>\n            </mat-form-field>\n\n            <!-- Is Active -->\n            <div class=\"checkbox-field\">\n              <mat-checkbox formControlName=\"isActive\">\n                مورد نشط\n              </mat-checkbox>\n            </div>\n\n            <!-- Notes -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>ملاحظات</mat-label>\n              <textarea matInput formControlName=\"notes\" rows=\"4\" \n                        placeholder=\"أدخل أي ملاحظات إضافية عن المورد\"></textarea>\n              <mat-icon matSuffix>note</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n    </form>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل بيانات المورد...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAKxF;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;ICJlEC,EAAA,CAAAC,cAAA,YAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAvDH,EAAA,CAAAI,SAAA,EAAmD;IAAnDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,SAAAF,MAAA,CAAAC,QAAA,CAAAE,YAAA,CAAmD;;;;;IA6CvFT,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAU,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAC9DZ,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAF,OAAA,CAAAG,MAAA,MACF;;;;;IAUFd,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA8BZH,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAeZH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,sJACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA8BVH,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAU,UAAA,UAAAK,OAAA,CAAAH,EAAA,CAAiB;IACtDZ,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAE,OAAA,CAAAD,MAAA,MACF;;;;;IA4EFd,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAqE;IACnED,EAAA,CAAAE,MAAA,6NACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAnMdH,EAPV,CAAAC,cAAA,cAA6C,eACY,mBAGzB,sBACT,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,8GAAkB;IAE5BF,EAF4B,CAAAG,YAAA,EAAO,EAChB,EACD;IAMZH,EALN,CAAAC,cAAA,uBAAkB,eACO,0BAGgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAgB,SAAA,iBAAwD;IACxDhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IACzBF,EADyB,CAAAG,YAAA,EAAW,EACnB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE/BH,EADF,CAAAC,cAAA,sBAA6C,sBACtB;IAAAD,EAAA,CAAAE,MAAA,wFAAe;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACjDH,EAAA,CAAAiB,UAAA,KAAAC,mDAAA,yBAAiE;IAGnElB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;IAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,kHAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5CH,EAAA,CAAAgB,SAAA,iBAAyF;IACzFhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvCH,EAAA,CAAAiB,UAAA,KAAAE,kDAAA,wBAAoE;IAGtEnB,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,kIAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC7CH,EAAA,CAAAgB,SAAA,iBAAsF;IACtFhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAKpCF,EALoC,CAAAG,YAAA,EAAW,EACxB,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAMZH,EALN,CAAAC,cAAA,wBAAkB,eACO,0BAGgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,6EAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAgB,SAAA,iBAA8E;IAC9EhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAiB,UAAA,KAAAG,kDAAA,wBAAoE;IAGtEpB,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,iFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAgB,SAAA,iBAAqE;IACrEhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EACrB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,yGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAgB,SAAA,iBAAwF;IACxFhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAiB,UAAA,KAAAI,kDAAA,wBAAgE;IAGlErB,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,yGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAgB,SAAA,iBAAyE;IACzEhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAKpCF,EALoC,CAAAG,YAAA,EAAW,EACxB,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IAEzBF,EAFyB,CAAAG,YAAA,EAAO,EACb,EACD;IAMZH,EALN,CAAAC,cAAA,wBAAkB,eACO,0BAGgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,wDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE7BH,EADF,CAAAC,cAAA,sBAAqC,sBACd;IAAAD,EAAA,CAAAE,MAAA,iFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAiB,UAAA,KAAAK,mDAAA,yBAAyD;IAG3DtB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACnCF,EADmC,CAAAG,YAAA,EAAW,EAC7B;IAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAgB,SAAA,oBACgE;IAChEhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAKhCF,EALgC,CAAAG,YAAA,EAAW,EACpB,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,kFAAa;IAEvBF,EAFuB,CAAAG,YAAA,EAAO,EACX,EACD;IAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;IAAAD,EAAA,CAAAE,MAAA,qGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAgB,SAAA,kBAAyF;IACzFhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,2GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAgB,SAAA,kBAAiF;IACjFhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EACrB;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,2GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAgB,SAAA,kBAAqG;IACrGhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAKjCF,EALiC,CAAAG,YAAA,EAAW,EACrB,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,0GAAiB;IAE3BF,EAF2B,CAAAG,YAAA,EAAO,EACf,EACD;IAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;IAAAD,EAAA,CAAAE,MAAA,6GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAgB,SAAA,kBAA8E;IAC9EhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,qHAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9CH,EAAA,CAAAgB,SAAA,kBAAsF;IACtFhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,uBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAI7CH,EAHA,CAAAiB,UAAA,MAAAM,mDAAA,wBAA0E,MAAAC,mDAAA,wBAGL;IAGvExB,EAAA,CAAAG,YAAA,EAAiB;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,wFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAgB,SAAA,kBAA+E;IAC/EhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IACjCF,EADiC,CAAAG,YAAA,EAAW,EAC3B;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,kFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAgB,SAAA,kBAA6E;IAC7EhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAO;IAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACvB;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,kFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAgB,SAAA,kBAA0F;IAC1FhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAK3CF,EAL2C,CAAAG,YAAA,EAAW,EAC/B,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,0GAAiB;IAE3BF,EAF2B,CAAAG,YAAA,EAAO,EACf,EACD;IAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;IAAAD,EAAA,CAAAE,MAAA,0DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAgB,SAAA,kBAAwE;IACxEhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IACrCF,EADqC,CAAAG,YAAA,EAAW,EAC/B;IAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;IAAAD,EAAA,CAAAE,MAAA,qGAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAgB,SAAA,kBAAkF;IAClFhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAKvCF,EALuC,CAAAG,YAAA,EAAW,EAC3B,EAEb,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,wFAAc;IAExBF,EAFwB,CAAAG,YAAA,EAAO,EACZ,EACD;IAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;IAAAD,EAAA,CAAAE,MAAA,mDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE5BH,EADF,CAAAC,cAAA,uBAAqC,uBACd;IAAAD,EAAA,CAAAE,MAAA,gEAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC5CH,EAAA,CAAAC,cAAA,uBAAsB;IAAAD,EAAA,CAAAE,MAAA,qBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC1CH,EAAA,CAAAC,cAAA,uBAAsB;IAAAD,EAAA,CAAAE,MAAA,2BAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC3CH,EAAA,CAAAC,cAAA,uBAAsB;IAAAD,EAAA,CAAAE,MAAA,iCAAS;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC5CH,EAAA,CAAAC,cAAA,uBAAsB;IAAAD,EAAA,CAAAE,MAAA,uCAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC7CH,EAAA,CAAAC,cAAA,uBAAsB;IAAAD,EAAA,CAAAE,MAAA,6CAAW;IACnCF,EADmC,CAAAG,YAAA,EAAa,EACnC;IACbH,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EACpB;IAIfH,EADF,CAAAC,cAAA,gBAA4B,yBACe;IACvCD,EAAA,CAAAE,MAAA,sDACF;IACFF,EADE,CAAAG,YAAA,EAAe,EACX;IAIJH,EADF,CAAAC,cAAA,2BAAwD,kBAC3C;IAAAD,EAAA,CAAAE,MAAA,mDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAgB,SAAA,qBACoE;IACpEhB,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAQpCF,EARoC,CAAAG,YAAA,EAAW,EACpB,EAEb,EACW,EACV,EAEN,EACH;;;;;;;;;IA/SEH,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAU,UAAA,cAAAJ,MAAA,CAAAmB,YAAA,CAA0B;IAyBSzB,EAAA,CAAAI,SAAA,IAAgB;IAAhBJ,EAAA,CAAAU,UAAA,YAAAJ,MAAA,CAAAoB,aAAA,CAAgB;IAYnC1B,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAiB,OAAA,GAAArB,MAAA,CAAAmB,YAAA,CAAAG,GAAA,6BAAAD,OAAA,CAAAE,QAAA,aAAsD;IAgCtD7B,EAAA,CAAAI,SAAA,IAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAoB,OAAA,GAAAxB,MAAA,CAAAmB,YAAA,CAAAG,GAAA,6BAAAE,OAAA,CAAAD,QAAA,aAAsD;IAiBtD7B,EAAA,CAAAI,SAAA,IAAkD;IAAlDJ,EAAA,CAAAU,UAAA,UAAAqB,OAAA,GAAAzB,MAAA,CAAAmB,YAAA,CAAAG,GAAA,4BAAAG,OAAA,CAAAF,QAAA,UAAkD;IAgC/B7B,EAAA,CAAAI,SAAA,IAAQ;IAARJ,EAAA,CAAAU,UAAA,YAAAJ,MAAA,CAAA0B,KAAA,CAAQ;IA8E3BhC,EAAA,CAAAI,SAAA,IAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAuB,OAAA,GAAA3B,MAAA,CAAAmB,YAAA,CAAAG,GAAA,mCAAAK,OAAA,CAAAJ,QAAA,aAA4D;IAG5D7B,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAwB,OAAA,GAAA5B,MAAA,CAAAmB,YAAA,CAAAG,GAAA,mCAAAM,OAAA,CAAAL,QAAA,QAAuD;;;;;IA2G/E7B,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAgB,SAAA,sBAAyC;IACzChB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2IAA2B;IAChCF,EADgC,CAAAG,YAAA,EAAI,EAC9B;;;ADrQR,WAAagC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAgBtBC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,IAAA;IACAC,QAAA;IAlBV;IACAC,SAAS,GAAG,KAAK;IACjBhB,YAAY;IACZiB,UAAU,GAAW,CAAC;IAEtB;IACAnC,QAAQ,GAAoB,IAAI;IAChCmB,aAAa,GAAmB,EAAE;IAClCM,KAAK,GAAW,EAAE;IAElB;IACQW,aAAa,GAAmB,EAAE;IAE1CC,YACUR,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,IAAgB,EAChBC,QAAqB;MAJrB,KAAAJ,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,QAAQ,GAARA,QAAQ;MAEhB,IAAI,CAACf,YAAY,GAAG,IAAI,CAACoB,UAAU,EAAE;IACvC;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACJ,UAAU,GAAGK,MAAM,CAAC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACrB,GAAG,CAAC,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACc,UAAU,EAAE;QACnB,IAAI,CAACQ,YAAY,EAAE;QACnB,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACtC;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACV,aAAa,CAACW,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQX,UAAUA,CAAA;MAChB,OAAO,IAAI,CAACT,EAAE,CAACqB,KAAK,CAAC;QACnBhD,YAAY,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACoE,QAAQ,CAAC,CAAC;QACzClD,MAAM,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACoE,QAAQ,EAAEpE,UAAU,CAACqE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,cAAc,EAAE,CAAC,EAAE,CAAC;QACpBC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACxE,UAAU,CAACoE,QAAQ,CAAC,CAAC;QACnCK,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAAC0E,KAAK,CAAC,CAAC;QAC/BC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACvBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QACxBC,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAChF,UAAU,CAAC0E,KAAK,CAAC,CAAC;QAC5CO,YAAY,EAAE,CAAC,EAAE,EAAE,CAACjF,UAAU,CAACoE,QAAQ,EAAEpE,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DC,YAAY,EAAE,CAAC,CAAC,EAAE,CAACnF,UAAU,CAACoE,QAAQ,EAAEpE,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3DE,WAAW,EAAE,CAAC,CAAC,EAAE,CAACpF,UAAU,CAACoE,QAAQ,EAAEpE,UAAU,CAACkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1DG,SAAS,EAAE,CAAC,EAAE,CAAC;QACfC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QACxBC,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdC,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACvBC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC,IAAI;OAChB,CAAC;IACJ;IAEA;;;IAGQ/B,YAAYA,CAAA;MAClB,IAAI,CAACT,SAAS,GAAG,IAAI;MAErB,MAAMc,GAAG,GAAG,IAAI,CAAChB,IAAI,CAACX,GAAG,CAAM,8CAA8C,IAAI,CAACc,UAAU,EAAE,CAAC,CAACwC,SAAS,CAAC;QACxGC,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,QAAQ,CAAC;UACtCC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,QAAQ,CAAC7E,QAAQ,CAAC;UAChD,IAAI,CAACA,QAAQ,GAAG6E,QAAQ,CAAC7E,QAAQ;UACjC,IAAI,CAACgF,YAAY,EAAE;UACnB,IAAI,CAAC9C,SAAS,GAAG,KAAK;QACxB,CAAC;QACD+C,KAAK,EAAGA,KAAK,IAAI;UACfH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACC,SAAS,CAAC,4BAA4B,CAAC;UAC5C,IAAI,CAAChD,SAAS,GAAG,KAAK;UACtB,IAAI,CAACJ,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC;OACD,CAAC;MACF,IAAI,CAACT,aAAa,CAAC+C,IAAI,CAACnC,GAAG,CAAC;IAC9B;IAEA;;;IAGQgC,YAAYA,CAAA;MAClB,IAAI,IAAI,CAAChF,QAAQ,EAAE;QACjB,MAAMoF,IAAI,GAAG,IAAI,CAACpF,QAAe,CAAC,CAAC;QACnC,IAAI,CAACkB,YAAY,CAACmE,UAAU,CAAC;UAC3BnF,YAAY,EAAEkF,IAAI,CAACE,YAAY,IAAIF,IAAI,CAAClF,YAAY,IAAI,EAAE;UAC1DD,MAAM,EAAEmF,IAAI,CAAC7E,MAAM,IAAI6E,IAAI,CAACnF,MAAM,IAAI,EAAE;UACxCoD,MAAM,EAAE+B,IAAI,CAACG,MAAM,IAAIH,IAAI,CAAC/B,MAAM,IAAI,EAAE;UACxCC,cAAc,EAAE8B,IAAI,CAACI,cAAc,IAAIJ,IAAI,CAAC9B,cAAc,IAAI,EAAE;UAChEC,MAAM,EAAE6B,IAAI,CAACK,MAAM,IAAIL,IAAI,CAAC7B,MAAM,IAAI,EAAE;UACxCC,MAAM,EAAE4B,IAAI,CAACM,MAAM,IAAIN,IAAI,CAAC5B,MAAM,IAAI,EAAE;UACxCC,KAAK,EAAE2B,IAAI,CAACO,KAAK,IAAIP,IAAI,CAAC3B,KAAK,IAAI,EAAE;UACrCC,OAAO,EAAE0B,IAAI,CAACQ,OAAO,IAAIR,IAAI,CAAC1B,OAAO,IAAI,EAAE;UAC3CC,OAAO,EAAEyB,IAAI,CAACS,OAAO,IAAIT,IAAI,CAACzB,OAAO,IAAI,EAAE;UAC3CC,MAAM,EAAEwB,IAAI,CAACU,MAAM,IAAIV,IAAI,CAACxB,MAAM,IAAI,EAAE;UACxCC,iBAAiB,EAAEuB,IAAI,CAACW,iBAAiB,IAAIX,IAAI,CAACvB,iBAAiB,IAAI,EAAE;UACzEC,kBAAkB,EAAEsB,IAAI,CAACY,kBAAkB,IAAIZ,IAAI,CAACtB,kBAAkB,IAAI,EAAE;UAC5EC,kBAAkB,EAAEqB,IAAI,CAACa,kBAAkB,IAAIb,IAAI,CAACrB,kBAAkB,IAAI,EAAE;UAC5EC,YAAY,EAAEoB,IAAI,CAACc,YAAY,IAAId,IAAI,CAACpB,YAAY,IAAI,EAAE;UAC1DE,YAAY,EAAEkB,IAAI,CAACe,YAAY,IAAIf,IAAI,CAAClB,YAAY,IAAI,CAAC;UACzDC,WAAW,EAAEiB,IAAI,CAACgB,WAAW,IAAIhB,IAAI,CAACjB,WAAW,IAAI,CAAC;UACtDC,SAAS,EAAEgB,IAAI,CAACiB,SAAS,IAAIjB,IAAI,CAAChB,SAAS,IAAI,EAAE;UACjDC,kBAAkB,EAAEe,IAAI,CAACkB,kBAAkB,IAAIlB,IAAI,CAACf,kBAAkB,IAAI,EAAE;UAC5EC,QAAQ,EAAEc,IAAI,CAACmB,QAAQ,IAAInB,IAAI,CAACd,QAAQ,IAAI,EAAE;UAC9CC,iBAAiB,EAAEa,IAAI,CAACoB,iBAAiB,IAAIpB,IAAI,CAACb,iBAAiB,IAAI,EAAE;UACzEC,MAAM,EAAEY,IAAI,CAACqB,MAAM,IAAIrB,IAAI,CAACZ,MAAM,IAAI,EAAE;UACxCC,KAAK,EAAEW,IAAI,CAACsB,KAAK,IAAItB,IAAI,CAACX,KAAK,IAAI,EAAE;UACrCC,QAAQ,EAAEU,IAAI,CAACuB,QAAQ,KAAKC,SAAS,GAAGxB,IAAI,CAACuB,QAAQ,GAAIvB,IAAI,CAACV,QAAQ,KAAK;SAC5E,CAAC;MACJ;IACF;IAEA;;;IAGQ9B,eAAeA,CAAA;MACrBiE,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,iBAAiB,EAAE,EACxB,IAAI,CAACC,SAAS,EAAE,CACjB,CAAC,CAACC,KAAK,CAAChC,KAAK,IAAG;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CAAC;IACJ;IAEA;;;IAGQ8B,iBAAiBA,CAAA;MACvB,OAAO,IAAIF,OAAO,CAAEK,OAAO,IAAI;QAC7B,MAAMlE,GAAG,GAAG,IAAI,CAAChB,IAAI,CAACX,GAAG,CAAM,iDAAiD,CAAC,CAACsD,SAAS,CAAC;UAC1FC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAC1D,aAAa,GAAG0D,QAAQ,CAAC1D,aAAa,IAAI,EAAE;YACjD+F,OAAO,EAAE;UACX,CAAC;UACDjC,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAAC9D,aAAa,GAAG,IAAI,CAACgG,oBAAoB,EAAE;YAChDD,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAAC9E,aAAa,CAAC+C,IAAI,CAACnC,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQgE,SAASA,CAAA;MACf,OAAO,IAAIH,OAAO,CAAEK,OAAO,IAAI;QAC7B,MAAMlE,GAAG,GAAG,IAAI,CAAChB,IAAI,CAACX,GAAG,CAAM,2CAA2C,CAAC,CAACsD,SAAS,CAAC;UACpFC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACpD,KAAK,GAAGoD,QAAQ,CAACpD,KAAK,IAAI,EAAE;YACjCyF,OAAO,EAAE;UACX,CAAC;UACDjC,KAAK,EAAEA,CAAA,KAAK;YACV,IAAI,CAACxD,KAAK,GAAG,IAAI,CAAC2F,YAAY,EAAE;YAChCF,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAAC9E,aAAa,CAAC+C,IAAI,CAACnC,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGAqE,cAAcA,CAAA;MACZ,IAAI,IAAI,CAACnG,YAAY,CAACoG,KAAK,EAAE;QAC3B,IAAI,CAACpF,SAAS,GAAG,IAAI;QAErB,MAAMqF,SAAS,GAAG,IAAI,CAACrG,YAAY,CAACsG,KAAK;QACzC,MAAMC,OAAO,GAAG;UACdxH,MAAM,EAAEsH,SAAS,CAACtH,MAAM;UACxBoD,MAAM,EAAEkE,SAAS,CAAClE,MAAM,IAAI,IAAI;UAChCC,cAAc,EAAEiE,SAAS,CAACjE,cAAc,IAAI,IAAI;UAChDC,MAAM,EAAEgE,SAAS,CAAChE,MAAM;UACxBC,MAAM,EAAE+D,SAAS,CAAC/D,MAAM,IAAI,IAAI;UAChCC,KAAK,EAAE8D,SAAS,CAAC9D,KAAK,IAAI,IAAI;UAC9BC,OAAO,EAAE6D,SAAS,CAAC7D,OAAO,IAAI,IAAI;UAClCC,OAAO,EAAE4D,SAAS,CAAC5D,OAAO,IAAI,IAAI;UAClCC,MAAM,EAAE2D,SAAS,CAAC3D,MAAM,IAAI,IAAI;UAChCC,iBAAiB,EAAE0D,SAAS,CAAC1D,iBAAiB,IAAI,IAAI;UACtDC,kBAAkB,EAAEyD,SAAS,CAACzD,kBAAkB,IAAI,IAAI;UACxDC,kBAAkB,EAAEwD,SAAS,CAACxD,kBAAkB,IAAI,IAAI;UACxDC,YAAY,EAAEuD,SAAS,CAACvD,YAAY;UACpCE,YAAY,EAAEqD,SAAS,CAACrD,YAAY;UACpCC,WAAW,EAAEoD,SAAS,CAACpD,WAAW;UAClCC,SAAS,EAAEmD,SAAS,CAACnD,SAAS,IAAI,IAAI;UACtCC,kBAAkB,EAAEkD,SAAS,CAAClD,kBAAkB,IAAI,IAAI;UACxDC,QAAQ,EAAEiD,SAAS,CAACjD,QAAQ,IAAI,IAAI;UACpCC,iBAAiB,EAAEgD,SAAS,CAAChD,iBAAiB,IAAI,IAAI;UACtDC,MAAM,EAAE+C,SAAS,CAAC/C,MAAM,IAAI,IAAI;UAChCC,KAAK,EAAE8C,SAAS,CAAC9C,KAAK,IAAI,IAAI;UAC9BC,QAAQ,EAAE6C,SAAS,CAAC7C;SACrB;QAED,MAAM1B,GAAG,GAAG,IAAI,CAAChB,IAAI,CAAC0F,GAAG,CAAM,8CAA8C,IAAI,CAACvF,UAAU,EAAE,EAAEsF,OAAO,CAAC,CAAC9C,SAAS,CAAC;UACjHC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAC3C,SAAS,GAAG,KAAK;YACtB,IAAI,CAACyF,WAAW,CAAC,8BAA8B,CAAC;YAChD,IAAI,CAAC7F,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;UACtC,CAAC;UACDoC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC/C,SAAS,GAAG,KAAK;YACtB4C,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChD,IAAI,CAACC,SAAS,CAAC,4BAA4B,CAAC;UAC9C;SACD,CAAC;QACF,IAAI,CAAC9C,aAAa,CAAC+C,IAAI,CAACnC,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC4E,oBAAoB,EAAE;QAC3B,IAAI,CAAC1C,SAAS,CAAC,+BAA+B,CAAC;MACjD;IACF;IAEA;;;IAGQ0C,oBAAoBA,CAAA;MAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5G,YAAY,CAAC6G,QAAQ,CAAC,CAAChF,OAAO,CAACiF,GAAG,IAAG;QACpD,MAAMC,OAAO,GAAG,IAAI,CAAC/G,YAAY,CAACG,GAAG,CAAC2G,GAAG,CAAC;QAC1CC,OAAO,EAAEC,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA;;;IAGAC,MAAMA,CAAA;MACJ,IAAI,CAACrG,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGAuF,MAAMA,CAAA;MACJ,IAAI,CAACD,MAAM,EAAE;IACf;IAEA;;;IAGQR,WAAWA,CAACU,OAAe;MACjC,IAAI,CAACpG,QAAQ,CAACqG,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;;;IAGQxD,SAASA,CAACmD,OAAe;MAC/B,IAAI,CAACpG,QAAQ,CAACqG,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;;;IAGQvB,oBAAoBA,CAAA;MAC1B,OAAO,CACL;QAAE9G,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,WAAW;QAAEgF,MAAM,EAAE;MAAgB,CAAE,EACxD;QAAElF,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,WAAW;QAAEgF,MAAM,EAAE;MAAwB,CAAE,EAChE;QAAElF,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,YAAY;QAAEgF,MAAM,EAAE;MAAqB,CAAE,CAC/D;IACH;IAEA;;;IAGQ6B,YAAYA,CAAA;MAClB,OAAO,CACL;QAAE/G,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,SAAS;QAAEgF,MAAM,EAAE,OAAO;QAAEoD,IAAI,EAAE;MAAK,CAAE,EAC1D;QAAEtI,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,QAAQ;QAAEgF,MAAM,EAAE,MAAM;QAAEoD,IAAI,EAAE;MAAK,CAAE,EACxD;QAAEtI,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,YAAY;QAAEgF,MAAM,EAAE,YAAY;QAAEoD,IAAI,EAAE;MAAK,CAAE,EAClE;QAAEtI,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,UAAU;QAAEgF,MAAM,EAAE,UAAU;QAAEoD,IAAI,EAAE;MAAK,CAAE,EAC9D;QAAEtI,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,SAAS;QAAEgF,MAAM,EAAE,SAAS;QAAEoD,IAAI,EAAE;MAAK,CAAE,EAC5D;QAAEtI,EAAE,EAAE,CAAC;QAAEE,MAAM,EAAE,WAAW;QAAEgF,MAAM,EAAE,UAAU;QAAEoD,IAAI,EAAE;MAAK,CAAE,CAChE;IACH;;uCA7SW/G,qBAAqB,EAAAnC,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAvJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAxJ,EAAA,CAAAmJ,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAA1J,EAAA,CAAAmJ,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;;YAArBzH,qBAAqB;MAAA0H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzE1BnK,EANR,CAAAC,cAAA,aAAqC,aAGV,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAqK,UAAA,mBAAAC,uDAAA;YAAA,OAASF,GAAA,CAAA1B,MAAA,EAAQ;UAAA,EAAC;UACzD1I,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,+GAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAiB,UAAA,KAAAsJ,mCAAA,eAA0C;UAE9CvK,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBACuC;UAAnBD,EAAA,CAAAqK,UAAA,mBAAAG,wDAAA;YAAA,OAASJ,GAAA,CAAAzB,MAAA,EAAQ;UAAA,EAAC;UAC9D3I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UACbF,EADa,CAAAG,YAAA,EAAO,EACX;UACTH,EAAA,CAAAC,cAAA,kBAEmC;UAA3BD,EAAA,CAAAqK,UAAA,mBAAAI,wDAAA;YAAA,OAASL,GAAA,CAAAxC,cAAA,EAAgB;UAAA,EAAC;UAChC5H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,iFAAa;UAI3BF,EAJ2B,CAAAG,YAAA,EAAO,EACnB,EACL,EACF,EACF;UAsTNH,EAnTA,CAAAiB,UAAA,KAAAyJ,qCAAA,oBAA6C,KAAAC,qCAAA,kBAmTE;UAKjD3K,EAAA,CAAAG,YAAA,EAAM;;;UA3U8BH,EAAA,CAAAI,SAAA,IAAc;UAAdJ,EAAA,CAAAU,UAAA,SAAA0J,GAAA,CAAA7J,QAAA,CAAc;UASlCP,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAU,UAAA,cAAA0J,GAAA,CAAA3I,YAAA,CAAAoG,KAAA,IAAAuC,GAAA,CAAA3H,SAAA,CAA6C;UAUhCzC,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAU,UAAA,UAAA0J,GAAA,CAAA3H,SAAA,CAAgB;UAmTbzC,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAA0J,GAAA,CAAA3H,SAAA,CAAe;;;qBDjR3CrD,YAAY,EAAAwL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZzL,mBAAmB,EAAA+J,EAAA,CAAA2B,aAAA,EAAA3B,EAAA,CAAA4B,oBAAA,EAAA5B,EAAA,CAAA6B,mBAAA,EAAA7B,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,oBAAA,EAAA/B,EAAA,CAAAgC,iBAAA,EAAAhC,EAAA,CAAAiC,kBAAA,EAAAjC,EAAA,CAAAkC,eAAA,EACnB/L,aAAa,EAAAgM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbnM,eAAe,EAAAoM,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfrM,aAAa,EAAAsM,EAAA,CAAAC,OAAA,EACbtM,kBAAkB,EAAAuM,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB1M,cAAc,EAAA2M,GAAA,CAAAC,QAAA,EACd3M,eAAe,EAAA4M,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACf7M,iBAAiB,EAAA8M,GAAA,CAAAC,WAAA,EACjB9M,wBAAwB,EAAA+M,GAAA,CAAAC,kBAAA,EACxB/M,iBAAiB;MAAAgN,MAAA;IAAA;;SAKR5K,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}