-- إنشاء جميع جداول نظام Terra Retail ERP
-- Create All Tables for Terra Retail ERP System

USE TerraRetailERP;
GO

PRINT N'بدء إنشاء جميع الجداول المطلوبة...';

-- جد<PERSON><PERSON> المنتجات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE Products (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProductCode nvarchar(20) NOT NULL UNIQUE,
        NameAr nvarchar(200) NOT NULL,
        NameEn nvarchar(200) NULL,
        Description nvarchar(1000) NULL,
        CategoryId int NOT NULL,
        UnitId int NOT NULL,
        Barcode nvarchar(50) NULL,
        CostPrice decimal(18,2) NOT NULL DEFAULT 0,
        BasePrice decimal(18,2) NOT NULL DEFAULT 0,
        ProfitMargin decimal(5,2) NOT NULL DEFAULT 0,
        MinimumStock decimal(18,3) NULL,
        MaximumStock decimal(18,3) NULL,
        ReorderPoint decimal(18,3) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        Weight decimal(18,3) NULL,
        Length decimal(18,3) NULL,
        Width decimal(18,3) NULL,
        Height decimal(18,3) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (CategoryId) REFERENCES Categories(Id),
        FOREIGN KEY (UnitId) REFERENCES Units(Id)
    );
    PRINT N'تم إنشاء جدول Products';
END

-- جدول صور المنتجات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductImages' AND xtype='U')
BEGIN
    CREATE TABLE ProductImages (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProductId int NOT NULL,
        ImagePath nvarchar(500) NOT NULL,
        ImageName nvarchar(200) NULL,
        IsMain bit NOT NULL DEFAULT 0,
        DisplayOrder int NOT NULL DEFAULT 0,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
    );
    PRINT N'تم إنشاء جدول ProductImages';
END

-- جدول الأكواد البديلة للمنتجات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductAlternativeCodes' AND xtype='U')
BEGIN
    CREATE TABLE ProductAlternativeCodes (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProductId int NOT NULL,
        AlternativeCode nvarchar(50) NOT NULL,
        CodeType nvarchar(20) NULL,
        Description nvarchar(200) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
    );
    PRINT N'تم إنشاء جدول ProductAlternativeCodes';
END

-- جدول أسعار المنتجات حسب الفرع وفئة السعر
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductBranchPrices' AND xtype='U')
BEGIN
    CREATE TABLE ProductBranchPrices (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProductId int NOT NULL,
        BranchId int NOT NULL,
        PriceCategoryId int NOT NULL,
        Price decimal(18,2) NOT NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (ProductId) REFERENCES Products(Id),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (PriceCategoryId) REFERENCES PriceCategories(Id)
    );
    PRINT N'تم إنشاء جدول ProductBranchPrices';
END

-- جدول مخزون المنتجات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductStocks' AND xtype='U')
BEGIN
    CREATE TABLE ProductStocks (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ProductId int NOT NULL,
        BranchId int NOT NULL,
        AvailableQuantity decimal(18,3) NOT NULL DEFAULT 0,
        ReservedQuantity decimal(18,3) NOT NULL DEFAULT 0,
        OnOrderQuantity decimal(18,3) NOT NULL DEFAULT 0,
        OpeningQuantity decimal(18,3) NOT NULL DEFAULT 0,
        TotalInQuantity decimal(18,3) NOT NULL DEFAULT 0,
        TotalOutQuantity decimal(18,3) NOT NULL DEFAULT 0,
        AverageCostPrice decimal(18,2) NOT NULL DEFAULT 0,
        LastCostPrice decimal(18,2) NOT NULL DEFAULT 0,
        StockValue decimal(18,2) NOT NULL DEFAULT 0,
        BranchMinStock decimal(18,3) NULL,
        BranchMaxStock decimal(18,3) NULL,
        BranchReorderPoint decimal(18,3) NULL,
        StorageLocation nvarchar(100) NULL,
        ShelfNumber nvarchar(20) NULL,
        StockNotes nvarchar(500) NULL,
        IsAvailableForSale bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (ProductId) REFERENCES Products(Id),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    
    CREATE UNIQUE INDEX IX_ProductStocks_ProductId_BranchId ON ProductStocks(ProductId, BranchId);
    PRINT N'تم إنشاء جدول ProductStocks';
END

-- جدول حركات المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockMovements' AND xtype='U')
BEGIN
    CREATE TABLE StockMovements (
        Id int IDENTITY(1,1) PRIMARY KEY,
        MovementNumber nvarchar(20) NOT NULL UNIQUE,
        ProductId int NOT NULL,
        BranchId int NOT NULL,
        MovementType int NOT NULL, -- 1=In, 2=Out, 3=Transfer, 4=Adjustment
        Quantity decimal(18,3) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL DEFAULT 0,
        TotalValue decimal(18,2) NOT NULL DEFAULT 0,
        BalanceBefore decimal(18,3) NOT NULL,
        BalanceAfter decimal(18,3) NOT NULL,
        MovementDate datetime2 NOT NULL DEFAULT GETUTCDATE(),
        ReferenceType nvarchar(50) NULL,
        ReferenceId int NULL,
        Reference nvarchar(50) NULL,
        Description nvarchar(500) NULL,
        BatchNumber nvarchar(50) NULL,
        FromBranchId int NULL,
        ToBranchId int NULL,
        UserId int NULL,
        ConfirmedById int NULL,
        ConfirmedAt datetime2 NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (ProductId) REFERENCES Products(Id),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (FromBranchId) REFERENCES Branches(Id),
        FOREIGN KEY (ToBranchId) REFERENCES Branches(Id)
    );
    PRINT N'تم إنشاء جدول StockMovements';
END

-- جدول أنواع الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SupplierTypes' AND xtype='U')
BEGIN
    CREATE TABLE SupplierTypes (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NameAr nvarchar(50) NOT NULL,
        NameEn nvarchar(50) NULL,
        Description nvarchar(200) NULL,
        DefaultPaymentTerms int NOT NULL DEFAULT 30,
        IsActive bit NOT NULL DEFAULT 1,
        Color nvarchar(7) NULL,
        DisplayOrder int NOT NULL DEFAULT 0,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL
    );
    PRINT N'تم إنشاء جدول SupplierTypes';
END

-- جدول الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        Id int IDENTITY(1,1) PRIMARY KEY,
        SupplierCode nvarchar(20) NOT NULL UNIQUE,
        NameAr nvarchar(100) NOT NULL,
        NameEn nvarchar(100) NULL,
        SupplierTypeId int NOT NULL,
        Phone1 nvarchar(20) NULL,
        Phone2 nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        Website nvarchar(200) NULL,
        Address nvarchar(500) NULL,
        AreaId int NULL,
        ContactPersonName nvarchar(100) NULL,
        ContactPersonPhone nvarchar(20) NULL,
        ContactPersonEmail nvarchar(100) NULL,
        PaymentTerms int NOT NULL DEFAULT 30,
        CreditLimit decimal(18,2) NOT NULL DEFAULT 0,
        OpeningBalance decimal(18,2) NOT NULL DEFAULT 0,
        CurrentBalance decimal(18,2) NOT NULL DEFAULT 0,
        TaxNumber nvarchar(50) NULL,
        CommercialRegister nvarchar(50) NULL,
        BankName nvarchar(100) NULL,
        BankAccountNumber nvarchar(50) NULL,
        IBAN nvarchar(50) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        Rating int NULL,
        Notes nvarchar(1000) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (SupplierTypeId) REFERENCES SupplierTypes(Id),
        FOREIGN KEY (AreaId) REFERENCES Areas(Id)
    );
    PRINT N'تم إنشاء جدول Suppliers';
END

-- جدول المبيعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sales' AND xtype='U')
BEGIN
    CREATE TABLE Sales (
        Id int IDENTITY(1,1) PRIMARY KEY,
        InvoiceNumber nvarchar(20) NOT NULL UNIQUE,
        CustomerId int NULL,
        CustomerName nvarchar(100) NULL,
        BranchId int NOT NULL,
        UserId int NOT NULL,
        InvoiceDate datetime2 NOT NULL DEFAULT GETUTCDATE(),
        DueDate datetime2 NULL,
        Status int NOT NULL DEFAULT 1, -- 1=Draft, 2=Confirmed, 3=PartiallyPaid, 4=FullyPaid, 5=Cancelled
        SaleType int NOT NULL DEFAULT 1, -- 1=Cash, 2=Credit, 3=Mixed
        SubTotal decimal(18,2) NOT NULL DEFAULT 0,
        DiscountPercentage decimal(5,2) NOT NULL DEFAULT 0,
        DiscountAmount decimal(18,2) NOT NULL DEFAULT 0,
        TaxPercentage decimal(5,2) NOT NULL DEFAULT 0,
        TaxAmount decimal(18,2) NOT NULL DEFAULT 0,
        TotalAmount decimal(18,2) NOT NULL DEFAULT 0,
        PaidAmount decimal(18,2) NOT NULL DEFAULT 0,
        RemainingAmount decimal(18,2) NOT NULL DEFAULT 0,
        Notes nvarchar(1000) NULL,
        InternalNotes nvarchar(1000) NULL,
        ExternalReference nvarchar(50) NULL,
        CancelledById int NULL,
        CancelledAt datetime2 NULL,
        CancellationReason nvarchar(500) NULL,
        TableNumber nvarchar(20) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    PRINT N'تم إنشاء جدول Sales';
END

-- جدول عناصر المبيعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SaleItems' AND xtype='U')
BEGIN
    CREATE TABLE SaleItems (
        Id int IDENTITY(1,1) PRIMARY KEY,
        SaleId int NOT NULL,
        ProductId int NOT NULL,
        LineNumber int NOT NULL,
        Quantity decimal(18,3) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL,
        UnitCostPrice decimal(18,2) NOT NULL DEFAULT 0,
        DiscountPercentage decimal(5,2) NOT NULL DEFAULT 0,
        DiscountAmount decimal(18,2) NOT NULL DEFAULT 0,
        NetUnitPrice decimal(18,2) NOT NULL,
        LineTotal decimal(18,2) NOT NULL,
        NetLineTotal decimal(18,2) NOT NULL,
        TaxPercentage decimal(5,2) NOT NULL DEFAULT 0,
        TaxAmount decimal(18,2) NOT NULL DEFAULT 0,
        FinalTotal decimal(18,2) NOT NULL,
        PriceCategoryId int NULL,
        ItemNotes nvarchar(500) NULL,
        BatchNumber nvarchar(50) NULL,
        SerialNumber nvarchar(100) NULL,
        ActualWeight decimal(18,3) NULL,
        ReturnedQuantity decimal(18,3) NOT NULL DEFAULT 0,
        DiscountReason nvarchar(200) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
        FOREIGN KEY (ProductId) REFERENCES Products(Id),
        FOREIGN KEY (PriceCategoryId) REFERENCES PriceCategories(Id)
    );
    PRINT N'تم إنشاء جدول SaleItems';
END

-- جدول مدفوعات المبيعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalePayments' AND xtype='U')
BEGIN
    CREATE TABLE SalePayments (
        Id int IDENTITY(1,1) PRIMARY KEY,
        SaleId int NOT NULL,
        PaymentNumber nvarchar(20) NOT NULL UNIQUE,
        PaymentMethodId int NOT NULL,
        Amount decimal(18,2) NOT NULL,
        ReceivedAmount decimal(18,2) NOT NULL,
        ChangeAmount decimal(18,2) NOT NULL DEFAULT 0,
        PaymentDate datetime2 NOT NULL DEFAULT GETUTCDATE(),
        Status int NOT NULL DEFAULT 1, -- 1=Pending, 2=Confirmed, 3=Cancelled
        ReferenceNumber nvarchar(100) NULL,
        BankName nvarchar(100) NULL,
        AccountNumber nvarchar(50) NULL,
        Notes nvarchar(500) NULL,
        UserId int NOT NULL,
        ConfirmedById int NULL,
        ConfirmedAt datetime2 NULL,
        ReceiptNumber nvarchar(20) NULL,
        ExternalTransactionId nvarchar(100) NULL,
        TransactionFee decimal(18,2) NOT NULL DEFAULT 0,
        Currency nvarchar(3) NOT NULL DEFAULT 'SAR',
        ExchangeRate decimal(18,6) NOT NULL DEFAULT 1,
        CashBoxId int NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
        FOREIGN KEY (PaymentMethodId) REFERENCES PaymentMethods(Id)
    );
    PRINT N'تم إنشاء جدول SalePayments';
END

PRINT N'تم الانتهاء من إنشاء الجداول الأساسية بنجاح!';
PRINT N'Finished creating basic tables successfully!';
