using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CountriesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public CountriesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetCountries()
        {
            try
            {
                var countries = await _context.Countries
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        id = c.Id,
                        nameAr = c.NameAr,
                        nameEn = c.NameEn,
                        code = c.Code,
                        phoneCode = c.PhoneCode,
                        currency = c.Currency,
                        flag = c.Flag,
                        isActive = c.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة البلدان بنجاح",
                    data = countries,
                    count = countries.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة البلدان",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetCountry(int id)
        {
            try
            {
                var country = await _context.Countries
                    .Where(c => c.Id == id && c.IsActive)
                    .Select(c => new
                    {
                        id = c.Id,
                        nameAr = c.NameAr,
                        nameEn = c.NameEn,
                        code = c.Code,
                        phoneCode = c.PhoneCode,
                        currency = c.Currency,
                        flag = c.Flag,
                        isActive = c.IsActive,
                        createdAt = c.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (country == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "البلد غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات البلد بنجاح",
                    data = country
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات البلد",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreateCountry(CreateCountryRequest request)
        {
            try
            {
                var country = new Country
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    PhoneCode = request.PhoneCode,
                    Currency = request.Currency,
                    Flag = request.Flag,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Countries.Add(country);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCountry), new { id = country.Id }, new
                {
                    success = true,
                    message = "تم إضافة البلد بنجاح",
                    data = new
                    {
                        id = country.Id,
                        nameAr = country.NameAr,
                        nameEn = country.NameEn,
                        code = country.Code,
                        phoneCode = country.PhoneCode,
                        currency = country.Currency,
                        flag = country.Flag,
                        isActive = country.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة البلد",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdateCountry(int id, UpdateCountryRequest request)
        {
            try
            {
                var country = await _context.Countries.FindAsync(id);
                if (country == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "البلد غير موجود"
                    });
                }

                country.NameAr = request.NameAr;
                country.NameEn = request.NameEn;
                country.Code = request.Code;
                country.PhoneCode = request.PhoneCode;
                country.Currency = request.Currency;
                country.Flag = request.Flag;
                country.IsActive = request.IsActive;
                country.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات البلد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث البلد",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteCountry(int id)
        {
            try
            {
                var country = await _context.Countries.FindAsync(id);
                if (country == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "البلد غير موجود"
                    });
                }

                country.IsActive = false;
                country.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف البلد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف البلد",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreateCountryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
        public string? PhoneCode { get; set; }
        public string? Currency { get; set; }
        public string? Flag { get; set; }
    }

    public class UpdateCountryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
        public string? PhoneCode { get; set; }
        public string? Currency { get; set; }
        public string? Flag { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
