using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("ChartOfAccounts")]
    public class ChartOfAccount
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string AccountNameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? AccountNameEn { get; set; }

        public int AccountType { get; set; } = 1; // 1=Asset, 2=Liability, 3=Equity, 4=Revenue, 5=Expense

        public int? ParentAccountId { get; set; }

        public int Level { get; set; } = 1;

        [StringLength(200)]
        public string? Path { get; set; }

        public bool IsMainAccount { get; set; } = false;
        public bool IsActive { get; set; } = true;
        public bool AllowPosting { get; set; } = true;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int? BranchId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ParentAccountId")]
        public virtual ChartOfAccount? ParentAccount { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch? Branch { get; set; }

        public virtual ICollection<ChartOfAccount> SubAccounts { get; set; } = new List<ChartOfAccount>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("JournalEntries")]
    public class JournalEntry
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string JournalNumber { get; set; } = string.Empty;

        public DateTime JournalDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? Notes { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; } = 0;

        public int Status { get; set; } = 1; // 1=Draft, 2=Posted, 3=Final

        public int TransactionType { get; set; } = 1; // 1=Manual, 2=Sale, 3=Purchase, 4=Payment, 5=Receipt, etc.

        public int? SourceId { get; set; } // Reference to source transaction

        [StringLength(100)]
        public string? SourceType { get; set; } // Sale, Purchase, Payment, etc.

        public int BranchId { get; set; }
        public int UserId { get; set; }

        public int? PostedById { get; set; }
        public DateTime? PostedAt { get; set; }

        public int? FinalizedById { get; set; }
        public DateTime? FinalizedAt { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("PostedById")]
        public virtual User? PostedBy { get; set; }

        [ForeignKey("FinalizedById")]
        public virtual User? FinalizedBy { get; set; }

        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    [Table("JournalEntryDetails")]
    public class JournalEntryDetail
    {
        [Key]
        public int Id { get; set; }

        public int JournalEntryId { get; set; }
        public int ChartAccountId { get; set; }

        public int LineNumber { get; set; } = 1;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        [StringLength(100)]
        public string? ReferenceNumber { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int? CostCenterId { get; set; }

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ForeignDebitAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ForeignCreditAmount { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; } = null!;

        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount ChartAccount { get; set; } = null!;

        [ForeignKey("CostCenterId")]
        public virtual CostCenter? CostCenter { get; set; }
    }

    [Table("CostCenters")]
    public class CostCenter
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int? ParentCostCenterId { get; set; }

        public int Level { get; set; } = 1;

        [StringLength(200)]
        public string? Path { get; set; }

        public bool IsActive { get; set; } = true;

        public int? BranchId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ParentCostCenterId")]
        public virtual CostCenter? ParentCostCenter { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch? Branch { get; set; }

        public virtual ICollection<CostCenter> SubCostCenters { get; set; } = new List<CostCenter>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    [Table("FinancialTransactions")]
    public class FinancialTransaction
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string TransactionNumber { get; set; } = string.Empty;

        public int TransactionType { get; set; } = 1; // 1=Receipt, 2=Payment

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(2000)]
        public string? Notes { get; set; }

        public int? CustomerId { get; set; }
        public int? SupplierId { get; set; }

        public int PaymentMethodId { get; set; }

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        public int BranchId { get; set; }
        public int UserId { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled

        public int? JournalEntryId { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }
    }
}
