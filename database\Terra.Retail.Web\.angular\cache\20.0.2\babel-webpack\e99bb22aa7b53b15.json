{"ast": null, "code": "/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\nexport { coerceCssPixelValue as c };", "map": {"version": 3, "names": ["coerceCssPixelValue", "value", "c"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs"], "sourcesContent": ["/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\nexport { coerceCssPixelValue as c };\n"], "mappings": "AAAA;AACA,SAASA,mBAAmBA,CAACC,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAO,EAAE;EACb;EACA,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,GAAGA,KAAK,IAAI;AAC3D;AAEA,SAASD,mBAAmB,IAAIE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}