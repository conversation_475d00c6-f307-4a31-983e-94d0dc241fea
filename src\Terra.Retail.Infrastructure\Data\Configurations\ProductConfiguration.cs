using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class ProductConfiguration : IEntityTypeConfiguration<Product>
    {
        public void Configure(EntityTypeBuilder<Product> builder)
        {
            builder.ToTable("Products");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.ProductCode)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(p => p.NameAr)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(p => p.NameEn)
                .HasMaxLength(200);

            builder.Property(p => p.Description)
                .HasMaxLength(1000);

            builder.Property(p => p.Barcode)
                .HasMaxLength(50);

            builder.Property(p => p.CostPrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(p => p.BasePrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(p => p.ProfitMargin)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(p => p.MinimumStock)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(p => p.MaximumStock)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(p => p.ReorderPoint)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(p => p.MainImage)
                .HasMaxLength(500);

            builder.Property(p => p.Keywords)
                .HasMaxLength(500);

            builder.Property(p => p.Weight)
                .HasColumnType("decimal(18,3)");

            builder.Property(p => p.Length)
                .HasColumnType("decimal(18,2)");

            builder.Property(p => p.Width)
                .HasColumnType("decimal(18,2)");

            builder.Property(p => p.Height)
                .HasColumnType("decimal(18,2)");

            builder.Property(p => p.StorageTemperature)
                .HasMaxLength(50);

            builder.Property(p => p.SpecialNotes)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(p => p.Unit)
                .WithMany(u => u.Products)
                .HasForeignKey(p => p.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(p => p.ProductCode)
                .IsUnique()
                .HasDatabaseName("IX_Products_ProductCode");

            builder.HasIndex(p => p.Barcode)
                .HasDatabaseName("IX_Products_Barcode");

            builder.HasIndex(p => new { p.NameAr, p.CategoryId })
                .HasDatabaseName("IX_Products_NameAr_CategoryId");

            builder.HasIndex(p => p.IsActive)
                .HasDatabaseName("IX_Products_IsActive");
        }
    }

    public class CategoryConfiguration : IEntityTypeConfiguration<Category>
    {
        public void Configure(EntityTypeBuilder<Category> builder)
        {
            builder.ToTable("Categories");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.NameAr)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.NameEn)
                .HasMaxLength(100);

            builder.Property(c => c.Code)
                .HasMaxLength(20);

            builder.Property(c => c.Path)
                .HasMaxLength(100);

            builder.Property(c => c.Description)
                .HasMaxLength(500);

            builder.Property(c => c.Image)
                .HasMaxLength(500);

            builder.Property(c => c.Icon)
                .HasMaxLength(50);

            builder.Property(c => c.Color)
                .HasMaxLength(7);

            builder.Property(c => c.Keywords)
                .HasMaxLength(500);

            builder.Property(c => c.SeoTitle)
                .HasMaxLength(200);

            builder.Property(c => c.SeoDescription)
                .HasMaxLength(500);

            builder.Property(c => c.SeoKeywords)
                .HasMaxLength(500);

            // Self-referencing relationship
            builder.HasOne(c => c.ParentCategory)
                .WithMany(c => c.SubCategories)
                .HasForeignKey(c => c.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(c => c.Code)
                .HasDatabaseName("IX_Categories_Code");

            builder.HasIndex(c => new { c.ParentCategoryId, c.DisplayOrder })
                .HasDatabaseName("IX_Categories_ParentCategoryId_DisplayOrder");

            builder.HasIndex(c => c.Level)
                .HasDatabaseName("IX_Categories_Level");
        }
    }

    public class UnitConfiguration : IEntityTypeConfiguration<Unit>
    {
        public void Configure(EntityTypeBuilder<Unit> builder)
        {
            builder.ToTable("Units");

            builder.HasKey(u => u.Id);

            builder.Property(u => u.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(u => u.NameEn)
                .HasMaxLength(50);

            builder.Property(u => u.Symbol)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(u => u.Description)
                .HasMaxLength(200);

            builder.Property(u => u.ConversionFactor)
                .HasColumnType("decimal(18,6)")
                .HasDefaultValue(1);

            // Self-referencing relationship for base unit
            builder.HasOne(u => u.BaseUnit)
                .WithMany(u => u.DerivedUnits)
                .HasForeignKey(u => u.BaseUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(u => u.Symbol)
                .IsUnique()
                .HasDatabaseName("IX_Units_Symbol");

            builder.HasIndex(u => u.UnitType)
                .HasDatabaseName("IX_Units_UnitType");
        }
    }

    public class ProductImageConfiguration : IEntityTypeConfiguration<ProductImage>
    {
        public void Configure(EntityTypeBuilder<ProductImage> builder)
        {
            builder.ToTable("ProductImages");

            builder.HasKey(pi => pi.Id);

            builder.Property(pi => pi.ImagePath)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(pi => pi.OriginalFileName)
                .HasMaxLength(255);

            builder.Property(pi => pi.ContentType)
                .HasMaxLength(50);

            builder.Property(pi => pi.AltText)
                .HasMaxLength(200);

            builder.Property(pi => pi.Title)
                .HasMaxLength(200);

            builder.Property(pi => pi.Description)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(pi => pi.Product)
                .WithMany(p => p.Images)
                .HasForeignKey(pi => pi.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(pi => new { pi.ProductId, pi.IsMain })
                .HasDatabaseName("IX_ProductImages_ProductId_IsMain");

            builder.HasIndex(pi => new { pi.ProductId, pi.DisplayOrder })
                .HasDatabaseName("IX_ProductImages_ProductId_DisplayOrder");
        }
    }

    public class ProductCodeConfiguration : IEntityTypeConfiguration<ProductCode>
    {
        public void Configure(EntityTypeBuilder<ProductCode> builder)
        {
            builder.ToTable("ProductCodes");

            builder.HasKey(pc => pc.Id);

            builder.Property(pc => pc.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(pc => pc.Description)
                .HasMaxLength(200);

            // Relationships
            builder.HasOne(pc => pc.Product)
                .WithMany(p => p.AlternativeCodes)
                .HasForeignKey(pc => pc.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(pc => pc.Supplier)
                .WithMany(s => s.ProductCodes)
                .HasForeignKey(pc => pc.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(pc => pc.Code)
                .HasDatabaseName("IX_ProductCodes_Code");

            builder.HasIndex(pc => new { pc.ProductId, pc.CodeType })
                .HasDatabaseName("IX_ProductCodes_ProductId_CodeType");
        }
    }

    public class ProductBranchPriceConfiguration : IEntityTypeConfiguration<ProductBranchPrice>
    {
        public void Configure(EntityTypeBuilder<ProductBranchPrice> builder)
        {
            builder.ToTable("ProductBranchPrices");

            builder.HasKey(pbp => pbp.Id);

            builder.Property(pbp => pbp.Price)
                .HasColumnType("decimal(18,2)");

            builder.Property(pbp => pbp.CostPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(pbp => pbp.ProfitMargin)
                .HasColumnType("decimal(5,2)");

            builder.Property(pbp => pbp.MinPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(pbp => pbp.MaxPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(pbp => pbp.DiscountPercentage)
                .HasColumnType("decimal(5,2)");

            builder.Property(pbp => pbp.DiscountAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(pbp => pbp.MinQuantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(pbp => pbp.MaxQuantity)
                .HasColumnType("decimal(18,3)");

            // Relationships
            builder.HasOne(pbp => pbp.Product)
                .WithMany(p => p.BranchPrices)
                .HasForeignKey(pbp => pbp.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(pbp => pbp.Branch)
                .WithMany()
                .HasForeignKey(pbp => pbp.BranchId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(pbp => pbp.PriceCategory)
                .WithMany(pc => pc.ProductBranchPrices)
                .HasForeignKey(pbp => pbp.PriceCategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(pbp => new { pbp.ProductId, pbp.BranchId, pbp.PriceCategoryId })
                .IsUnique()
                .HasDatabaseName("IX_ProductBranchPrices_ProductId_BranchId_PriceCategoryId");

            builder.HasIndex(pbp => new { pbp.BranchId, pbp.IsActive })
                .HasDatabaseName("IX_ProductBranchPrices_BranchId_IsActive");
        }
    }
}
