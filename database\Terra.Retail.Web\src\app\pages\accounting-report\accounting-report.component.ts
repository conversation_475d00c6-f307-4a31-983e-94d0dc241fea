import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from '../../../environments/environment';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Interfaces
interface ChartOfAccount {
  id: number;
  accountCode: string;
  accountNameAr: string;
  accountType: string;
  accountCategory: string;
  debitBalance: number;
  creditBalance: number;
  netBalance: number;
  lastTransactionDate?: string;
}

interface Transaction {
  transactionId: number;
  voucherId: string;
  transactionDate: string;
  transactionType: string;
  accountCode: string;
  accountName: string;
  debit: number;
  credit: number;
  notes: string;
  status: string;
}

interface TransactionType {
  transactionTypeId: number;
  nameAr: string;
  nameEn: string;
  transactionCount: number;
  totalDebit: number;
  totalCredit: number;
}

interface AccountSummary {
  accountType: string;
  accountCount: number;
  totalDebit: number;
  totalCredit: number;
  netBalance: number;
}

@Component({
  selector: 'app-accounting-report',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './accounting-report.component.html',
  styleUrls: ['./accounting-report.component.scss']
})
export class AccountingReportComponent implements OnInit, OnDestroy {

  // Loading states
  isLoading = false;
  
  // Data
  chartOfAccounts: ChartOfAccount[] = [];
  transactions: Transaction[] = [];
  transactionTypes: TransactionType[] = [];
  accountSummary: AccountSummary[] = [];

  // Table columns
  accountColumns: string[] = ['accountCode', 'accountNameAr', 'accountType', 'debitBalance', 'creditBalance', 'netBalance'];
  transactionColumns: string[] = ['voucherId', 'transactionDate', 'transactionType', 'accountName', 'debit', 'credit', 'notes'];
  typeColumns: string[] = ['nameAr', 'transactionCount', 'totalDebit', 'totalCredit'];
  summaryColumns: string[] = ['accountType', 'accountCount', 'totalDebit', 'totalCredit', 'netBalance'];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAccountingData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load all accounting data
   */
  loadAccountingData(): void {
    this.isLoading = true;
    
    Promise.all([
      this.loadChartOfAccounts(),
      this.loadTransactions(),
      this.loadTransactionTypes(),
      this.loadAccountSummary()
    ]).then(() => {
      this.isLoading = false;
      this.showSuccess('تم تحميل البيانات المحاسبية بنجاح');
    }).catch(error => {
      this.isLoading = false;
      console.error('Error loading accounting data:', error);
      this.showError('خطأ في تحميل البيانات المحاسبية');
    });
  }

  /**
   * Load chart of accounts with balances
   */
  private loadChartOfAccounts(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${environment.apiUrl}/accounting/chart-of-accounts-with-balances`).subscribe({
        next: (response) => {
          this.chartOfAccounts = response.accounts || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading chart of accounts:', error);
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load transactions
   */
  private loadTransactions(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${environment.apiUrl}/accounting/transactions-detailed`).subscribe({
        next: (response) => {
          this.transactions = response.transactions || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading transactions:', error);
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load transaction types with usage
   */
  private loadTransactionTypes(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${environment.apiUrl}/accounting/transaction-types-usage`).subscribe({
        next: (response) => {
          this.transactionTypes = response.transactionTypes || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading transaction types:', error);
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load account summary
   */
  private loadAccountSummary(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>(`${environment.apiUrl}/accounting/account-summary`).subscribe({
        next: (response) => {
          this.accountSummary = response.summary || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading account summary:', error);
          reject(error);
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Format currency
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  }

  /**
   * Get account type color
   */
  getAccountTypeColor(accountType: string): string {
    switch (accountType) {
      case 'ASSET': return 'primary';
      case 'LIABILITY': return 'warn';
      case 'EQUITY': return 'accent';
      case 'REVENUE': return 'primary';
      case 'EXPENSE': return 'warn';
      default: return '';
    }
  }

  /**
   * Get balance color
   */
  getBalanceColor(balance: number): string {
    if (balance > 0) return 'color: green;';
    if (balance < 0) return 'color: red;';
    return '';
  }

  /**
   * Refresh data
   */
  refreshData(): void {
    this.loadAccountingData();
  }

  /**
   * Export to Excel (placeholder)
   */
  exportToExcel(): void {
    this.showInfo('سيتم إضافة ميزة التصدير قريباً');
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show info message
   */
  private showInfo(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['info-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
