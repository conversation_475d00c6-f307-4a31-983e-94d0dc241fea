2025-06-17 18:38:43.589 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.661 +03:00 [WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.670 +03:00 [WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.679 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.689 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.695 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.704 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.709 +03:00 [WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.715 +03:00 [WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.726 +03:00 [WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.730 +03:00 [WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.738 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.745 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.754 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.760 +03:00 [WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.765 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.774 +03:00 [WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.779 +03:00 [WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.789 +03:00 [WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.794 +03:00 [WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.799 +03:00 [WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.807 +03:00 [WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.812 +03:00 [WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.821 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.827 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.831 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.839 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.844 +03:00 [WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.852 +03:00 [WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.859 +03:00 [WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.864 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.882 +03:00 [WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.908 +03:00 [WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.914 +03:00 [WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.922 +03:00 [WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.927 +03:00 [WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.932 +03:00 [WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.940 +03:00 [WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.945 +03:00 [WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.953 +03:00 [WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.958 +03:00 [WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.963 +03:00 [WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.972 +03:00 [WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.977 +03:00 [WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.981 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.989 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.994 +03:00 [WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:43.999 +03:00 [WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.008 +03:00 [WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.013 +03:00 [WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.022 +03:00 [WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.027 +03:00 [WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.036 +03:00 [WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.042 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.048 +03:00 [WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.056 +03:00 [WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.061 +03:00 [WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.068 +03:00 [WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.073 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.078 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.082 +03:00 [WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.089 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.094 +03:00 [WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.098 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.106 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.110 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.115 +03:00 [WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.148 +03:00 [WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.163 +03:00 [WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.173 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.180 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.190 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.196 +03:00 [WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.204 +03:00 [WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.209 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.214 +03:00 [WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.222 +03:00 [WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.229 +03:00 [WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.238 +03:00 [WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.244 +03:00 [WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.253 +03:00 [WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.258 +03:00 [WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.263 +03:00 [WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.270 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.275 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.280 +03:00 [WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.287 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.293 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.298 +03:00 [WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.306 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.311 +03:00 [WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.316 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.325 +03:00 [WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.331 +03:00 [WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.340 +03:00 [WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.345 +03:00 [WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.352 +03:00 [WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.357 +03:00 [WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.361 +03:00 [WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.365 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.372 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.377 +03:00 [WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.381 +03:00 [WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.403 +03:00 [WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.409 +03:00 [WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.414 +03:00 [WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.421 +03:00 [WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:44.428 +03:00 [WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-17 18:38:46.176 +03:00 [INF] تم إنشاء قاعدة البيانات بنجاح
2025-06-17 18:38:46.318 +03:00 [INF] تم بدء تشغيل Terra Retail ERP API
2025-06-17 18:39:11.058 +03:00 [WRN] Failed to determine the https port for redirect.
[2025-06-17 18:41:19.303 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.382 +03:00 WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.414 +03:00 WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.420 +03:00 WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.432 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.439 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.450 +03:00 WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.455 +03:00 WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.469 +03:00 WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.481 +03:00 WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.487 +03:00 WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.500 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.505 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.513 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.546 +03:00 WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.568 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.581 +03:00 WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.586 +03:00 WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.594 +03:00 WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.601 +03:00 WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.606 +03:00 WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.633 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.640 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.651 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.668 +03:00 WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.682 +03:00 WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.693 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.701 +03:00 WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.710 +03:00 WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.718 +03:00 WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.723 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.730 +03:00 WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.735 +03:00 WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.740 +03:00 WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.749 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.754 +03:00 WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.764 +03:00 WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.774 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.788 +03:00 WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.797 +03:00 WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.803 +03:00 WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.819 +03:00 WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.836 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.854 +03:00 WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.864 +03:00 WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.868 +03:00 WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.872 +03:00 WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.884 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.893 +03:00 WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.900 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.905 +03:00 WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.915 +03:00 WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.920 +03:00 WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.930 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.936 +03:00 WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.945 +03:00 WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.950 +03:00 WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.955 +03:00 WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.963 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.970 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.979 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.985 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:19.993 +03:00 WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.031 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.040 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.053 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.166 +03:00 WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.334 +03:00 WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.341 +03:00 WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:20.370 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.436 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.921 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.935 +03:00 WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.940 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.950 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.956 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.964 +03:00 WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.971 +03:00 WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.978 +03:00 WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.986 +03:00 WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:21.992 +03:00 WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.001 +03:00 WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.009 +03:00 WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.025 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.059 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.126 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.374 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.423 +03:00 WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.434 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.440 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.449 +03:00 WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.454 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.460 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.499 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.504 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.508 +03:00 WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.519 +03:00 WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.523 +03:00 WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.530 +03:00 WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.537 +03:00 WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.584 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.629 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.639 +03:00 WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.670 +03:00 WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.675 +03:00 WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.685 +03:00 WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.691 +03:00 WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:22.701 +03:00 WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 18:41:23.535 +03:00 INF] تم إنشاء قاعدة البيانات بنجاح {}
[2025-06-17 18:41:23.640 +03:00 INF] تم بدء تشغيل Terra Retail ERP API {}
[2025-06-17 18:41:55.124 +03:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNDDMLUCBT8J:00000001","RequestPath":"/api/simple/suppliers","ConnectionId":"0HNDDMLUCBT8J"}
