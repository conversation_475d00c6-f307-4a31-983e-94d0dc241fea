using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("⚙️ System Settings")]
    public class SettingsController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public SettingsController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet("branches")]
        public async Task<ActionResult<IEnumerable<Branch>>> GetBranches()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.NameAr)
                    .ToListAsync();

                return Ok(branches);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("countries")]
        public async Task<ActionResult<IEnumerable<Country>>> GetCountries()
        {
            try
            {
                var countries = await _context.Countries
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.NameAr)
                    .ToListAsync();

                return Ok(countries);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("areas")]
        public async Task<ActionResult<IEnumerable<Area>>> GetAreas([FromQuery] int? countryId = null)
        {
            try
            {
                var query = _context.Areas
                    .Include(a => a.Country)
                    .Where(a => a.IsActive);

                if (countryId.HasValue)
                    query = query.Where(a => a.CountryId == countryId);

                var areas = await query
                    .OrderBy(a => a.DisplayOrder)
                    .ThenBy(a => a.NameAr)
                    .ToListAsync();

                return Ok(areas);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("payment-methods")]
        public async Task<ActionResult<IEnumerable<PaymentMethod>>> GetPaymentMethods()
        {
            try
            {
                var paymentMethods = await _context.PaymentMethods
                    .Where(pm => pm.IsActive)
                    .OrderBy(pm => pm.DisplayOrder)
                    .ThenBy(pm => pm.NameAr)
                    .ToListAsync();

                return Ok(paymentMethods);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("price-categories")]
        public async Task<ActionResult<IEnumerable<PriceCategory>>> GetPriceCategories()
        {
            try
            {
                var priceCategories = await _context.PriceCategories
                    .Where(pc => pc.IsActive)
                    .OrderBy(pc => pc.DisplayOrder)
                    .ThenBy(pc => pc.NameAr)
                    .ToListAsync();

                return Ok(priceCategories);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("leave-types")]
        public async Task<ActionResult<IEnumerable<LeaveType>>> GetLeaveTypes()
        {
            try
            {
                var leaveTypes = await _context.LeaveTypes
                    .Where(lt => lt.IsActive)
                    .OrderBy(lt => lt.NameAr)
                    .ToListAsync();

                return Ok(leaveTypes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("roles")]
        public async Task<ActionResult<IEnumerable<Role>>> GetRoles()
        {
            try
            {
                var roles = await _context.Roles
                    .Where(r => r.IsActive)
                    .OrderBy(r => r.DisplayOrder)
                    .ThenBy(r => r.NameAr)
                    .ToListAsync();

                return Ok(roles);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("counters")]
        public async Task<ActionResult<IEnumerable<Counter>>> GetCounters([FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.Counters
                    .Include(c => c.Branch)
                    .Where(c => c.IsActive);

                if (branchId.HasValue)
                    query = query.Where(c => c.BranchId == branchId);

                var counters = await query
                    .OrderBy(c => c.CounterName)
                    .ToListAsync();

                return Ok(counters);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("counters")]
        public async Task<ActionResult<Counter>> CreateCounter(CreateCounterRequest request)
        {
            try
            {
                var counter = new Counter
                {
                    CounterName = request.CounterName,
                    Prefix = request.Prefix,
                    CurrentValue = request.CurrentValue,
                    NumberLength = request.NumberLength,
                    Description = request.Description,
                    BranchId = request.BranchId,
                    CodeFormat = request.CodeFormat,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Counters.Add(counter);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCounters), new { id = counter.Id }, counter);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("counters/{id}")]
        public async Task<IActionResult> UpdateCounter(int id, UpdateCounterRequest request)
        {
            try
            {
                var counter = await _context.Counters.FindAsync(id);
                if (counter == null)
                    return NotFound(new { message = "العداد غير موجود" });

                counter.CounterName = request.CounterName;
                counter.Prefix = request.Prefix;
                counter.CurrentValue = request.CurrentValue;
                counter.NumberLength = request.NumberLength;
                counter.Description = request.Description;
                counter.CodeFormat = request.CodeFormat;
                counter.IsActive = request.IsActive;
                counter.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث العداد بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("branches")]
        public async Task<ActionResult<Branch>> CreateBranch(CreateBranchRequest request)
        {
            try
            {
                var branch = new Branch
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    Address = request.Address,
                    Phone = request.Phone,
                    Email = request.Email,
                    ManagerName = request.ManagerName,
                    OpeningDate = request.OpeningDate,
                    WorkingHours = request.WorkingHours,
                    Longitude = request.Longitude,
                    Latitude = request.Latitude,
                    Area = request.Area,
                    EmployeeCount = request.EmployeeCount,
                    TaxNumber = request.TaxNumber,
                    CommercialRegister = request.CommercialRegister,
                    IsActive = true,
                    IsMainBranch = false,
                    CreatedAt = DateTime.Now
                };

                _context.Branches.Add(branch);
                await _context.SaveChangesAsync();

                // Create default counters for the new branch
                await CreateDefaultCounters(branch.Id);

                return CreatedAtAction(nameof(GetBranches), new { id = branch.Id }, branch);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("payment-methods")]
        public async Task<ActionResult<PaymentMethod>> CreatePaymentMethod(CreatePaymentMethodRequest request)
        {
            try
            {
                var paymentMethod = new PaymentMethod
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    Description = request.Description,
                    PaymentType = request.PaymentType,
                    RequireReference = request.RequireReference,
                    RequireApproval = request.RequireApproval,
                    Icon = request.Icon,
                    Color = request.Color,
                    DisplayOrder = request.DisplayOrder,
                    TransactionFeePercentage = request.TransactionFeePercentage,
                    FixedTransactionFee = request.FixedTransactionFee,
                    ChartOfAccountId = request.ChartOfAccountId,
                    IsActive = true,
                    IsDefault = false,
                    CreatedAt = DateTime.Now
                };

                _context.PaymentMethods.Add(paymentMethod);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetPaymentMethods), new { id = paymentMethod.Id }, paymentMethod);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("price-categories")]
        public async Task<ActionResult<PriceCategory>> CreatePriceCategory(CreatePriceCategoryRequest request)
        {
            try
            {
                var priceCategory = new PriceCategory
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    Description = request.Description,
                    PriceAdjustmentPercentage = request.PriceAdjustmentPercentage,
                    Color = request.Color,
                    DisplayOrder = request.DisplayOrder,
                    MinimumQuantity = request.MinimumQuantity,
                    MaximumQuantity = request.MaximumQuantity,
                    IsActive = true,
                    IsDefault = false,
                    CreatedAt = DateTime.Now
                };

                _context.PriceCategories.Add(priceCategory);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetPriceCategories), new { id = priceCategory.Id }, priceCategory);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("system-info")]
        public async Task<ActionResult> GetSystemInfo()
        {
            try
            {
                var totalUsers = await _context.Users.CountAsync();
                var activeUsers = await _context.Users.Where(u => u.IsActive).CountAsync();
                var totalBranches = await _context.Branches.CountAsync();
                var activeBranches = await _context.Branches.Where(b => b.IsActive).CountAsync();
                var totalProducts = await _context.Products.CountAsync();
                var activeProducts = await _context.Products.Where(p => p.IsActive).CountAsync();
                var totalCustomers = await _context.Customers.CountAsync();
                var activeCustomers = await _context.Customers.Where(c => c.IsActive).CountAsync();
                var totalSuppliers = await _context.Suppliers.Where(s => !s.IsDeleted).CountAsync();
                var activeSuppliers = await _context.Suppliers.Where(s => s.IsActive && !s.IsDeleted).CountAsync();
                var totalEmployees = await _context.Employees.CountAsync();
                var activeEmployees = await _context.Employees.Where(e => e.IsActive).CountAsync();

                var dbSize = await _context.Database.SqlQueryRaw<decimal>(
                    "SELECT SUM(size) * 8.0 / 1024 FROM sys.master_files WHERE database_id = DB_ID()")
                    .FirstOrDefaultAsync();

                return Ok(new
                {
                    systemName = "Terra Retail ERP",
                    version = "1.0.0",
                    databaseSize = $"{dbSize:F2} MB",
                    statistics = new
                    {
                        users = new { total = totalUsers, active = activeUsers },
                        branches = new { total = totalBranches, active = activeBranches },
                        products = new { total = totalProducts, active = activeProducts },
                        customers = new { total = totalCustomers, active = activeCustomers },
                        suppliers = new { total = totalSuppliers, active = activeSuppliers },
                        employees = new { total = totalEmployees, active = activeEmployees }
                    },
                    serverTime = DateTime.Now,
                    uptime = DateTime.Now - System.Diagnostics.Process.GetCurrentProcess().StartTime
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task CreateDefaultCounters(int branchId)
        {
            var defaultCounters = new[]
            {
                new Counter { CounterName = CounterTypes.Customer, Prefix = "CUS", CurrentValue = 0, NumberLength = 6, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.Supplier, Prefix = "SUP", CurrentValue = 0, NumberLength = 6, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.SaleInvoice, Prefix = "SAL", CurrentValue = 0, NumberLength = 8, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.PurchaseInvoice, Prefix = "PUR", CurrentValue = 0, NumberLength = 8, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.Receipt, Prefix = "REC", CurrentValue = 0, NumberLength = 8, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.Payment, Prefix = "PAY", CurrentValue = 0, NumberLength = 8, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.BranchTransfer, Prefix = "TRF", CurrentValue = 0, NumberLength = 8, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now },
                new Counter { CounterName = CounterTypes.Employee, Prefix = "EMP", CurrentValue = 0, NumberLength = 6, BranchId = branchId, IsActive = true, CreatedAt = DateTime.Now }
            };

            _context.Counters.AddRange(defaultCounters);
            await _context.SaveChangesAsync();
        }
    }

    // DTOs
    public class CreateCounterRequest
    {
        public string CounterName { get; set; } = string.Empty;
        public string? Prefix { get; set; }
        public long CurrentValue { get; set; } = 0;
        public int NumberLength { get; set; } = 6;
        public string? Description { get; set; }
        public int? BranchId { get; set; }
        public string? CodeFormat { get; set; }
    }

    public class UpdateCounterRequest
    {
        public string CounterName { get; set; } = string.Empty;
        public string? Prefix { get; set; }
        public long CurrentValue { get; set; } = 0;
        public int NumberLength { get; set; } = 6;
        public string? Description { get; set; }
        public string? CodeFormat { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class CreateBranchRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string Code { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? ManagerName { get; set; }
        public DateTime? OpeningDate { get; set; }
        public string? WorkingHours { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Area { get; set; }
        public int? EmployeeCount { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
    }

    public class CreatePaymentMethodRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
        public string? Description { get; set; }
        public int PaymentType { get; set; } = 1;
        public bool RequireReference { get; set; } = false;
        public bool RequireApproval { get; set; } = false;
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public int DisplayOrder { get; set; } = 1;
        public decimal? TransactionFeePercentage { get; set; }
        public decimal? FixedTransactionFee { get; set; }
        public int? ChartOfAccountId { get; set; }
    }

    public class CreatePriceCategoryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Code { get; set; }
        public string? Description { get; set; }
        public decimal PriceAdjustmentPercentage { get; set; } = 0;
        public string? Color { get; set; }
        public int DisplayOrder { get; set; } = 1;
        public decimal? MinimumQuantity { get; set; }
        public decimal? MaximumQuantity { get; set; }
    }
}
