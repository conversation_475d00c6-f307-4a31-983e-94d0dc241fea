using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("📋 Lookup Tables")]
    public class LookupController : ControllerBase
    {
        private readonly AppDbContext _context;

        public LookupController(AppDbContext context)
        {
            _context = context;
        }

        // Customer Types
        [HttpGet("customer-types")]
        public async Task<ActionResult> GetCustomerTypes()
        {
            try
            {
                var customerTypes = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.DisplayOrder)
                    .ThenBy(ct => ct.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أنواع العملاء بنجاح",
                    data = customerTypes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("customer-types")]
        public async Task<ActionResult> CreateCustomerType(CreateCustomerTypeRequest request)
        {
            try
            {
                var customerType = new CustomerType
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    DisplayOrder = request.DisplayOrder,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.CustomerTypes.Add(customerType);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCustomerTypes), new { id = customerType.Id }, new
                {
                    success = true,
                    message = "تم إضافة نوع العميل بنجاح",
                    data = customerType
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // Supplier Types
        [HttpGet("supplier-types")]
        public async Task<ActionResult> GetSupplierTypes()
        {
            try
            {
                var supplierTypes = await _context.SupplierTypes
                    .Where(st => st.IsActive)
                    .OrderBy(st => st.DisplayOrder)
                    .ThenBy(st => st.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أنواع الموردين بنجاح",
                    data = supplierTypes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("supplier-types")]
        public async Task<ActionResult> CreateSupplierType(CreateSupplierTypeRequest request)
        {
            try
            {
                var supplierType = new SupplierType
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    DisplayOrder = request.DisplayOrder,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.SupplierTypes.Add(supplierType);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetSupplierTypes), new { id = supplierType.Id }, new
                {
                    success = true,
                    message = "تم إضافة نوع المورد بنجاح",
                    data = supplierType
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // Countries
        [HttpGet("countries")]
        public async Task<ActionResult> GetCountries()
        {
            try
            {
                var countries = await _context.Countries
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة البلدان بنجاح",
                    data = countries
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("countries")]
        public async Task<ActionResult> CreateCountry(CreateCountryRequest request)
        {
            try
            {
                var country = new Country
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = request.Code,
                    PhoneCode = request.PhoneCode,
                    Currency = request.Currency,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Countries.Add(country);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCountries), new { id = country.Id }, new
                {
                    success = true,
                    message = "تم إضافة البلد بنجاح",
                    data = country
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // Cities
        [HttpGet("cities")]
        public async Task<ActionResult> GetCities([FromQuery] int? countryId = null)
        {
            try
            {
                var query = _context.Cities
                    .Include(c => c.Country)
                    .Where(c => c.IsActive);

                if (countryId.HasValue)
                    query = query.Where(c => c.CountryId == countryId);

                var cities = await query
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        c.Id,
                        c.NameAr,
                        c.NameEn,
                        c.CountryId,
                        CountryName = c.Country.NameAr,
                        c.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المدن بنجاح",
                    data = cities
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("cities")]
        public async Task<ActionResult> CreateCity(CreateCityRequest request)
        {
            try
            {
                var city = new City
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    CountryId = request.CountryId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Cities.Add(city);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCities), new { id = city.Id }, new
                {
                    success = true,
                    message = "تم إضافة المدينة بنجاح",
                    data = city
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // Categories Tree
        [HttpGet("categories-tree")]
        public async Task<ActionResult> GetCategoriesTree()
        {
            try
            {
                var categories = await _context.Categories
                    .Include(c => c.Parent)
                    .Include(c => c.Children)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.NameAr)
                    .ToListAsync();

                // Build tree structure
                var rootCategories = categories.Where(c => c.ParentId == null).ToList();
                var tree = BuildCategoryTree(rootCategories, categories);

                return Ok(new
                {
                    success = true,
                    message = "تم جلب شجرة التصنيفات بنجاح",
                    data = tree
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // Chart of Accounts Tree
        [HttpGet("accounts-tree")]
        public async Task<ActionResult> GetAccountsTree()
        {
            try
            {
                var accounts = await _context.ChartOfAccounts
                    .Include(a => a.Parent)
                    .Include(a => a.Children)
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.AccountCode)
                    .ToListAsync();

                // Build tree structure
                var rootAccounts = accounts.Where(a => a.ParentId == null).ToList();
                var tree = BuildAccountTree(rootAccounts, accounts);

                return Ok(new
                {
                    success = true,
                    message = "تم جلب شجرة الحسابات بنجاح",
                    data = tree
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        private List<object> BuildCategoryTree(List<Category> categories, List<Category> allCategories)
        {
            return categories.Select(c => new
            {
                c.Id,
                c.NameAr,
                c.NameEn,
                c.Description,
                c.ParentId,
                c.DisplayOrder,
                c.IsActive,
                Children = BuildCategoryTree(
                    allCategories.Where(child => child.ParentId == c.Id).ToList(),
                    allCategories
                )
            }).ToList<object>();
        }

        private List<object> BuildAccountTree(List<ChartOfAccount> accounts, List<ChartOfAccount> allAccounts)
        {
            return accounts.Select(a => new
            {
                a.Id,
                a.AccountCode,
                a.NameAr,
                a.NameEn,
                a.AccountType,
                a.ParentId,
                a.Level,
                a.IsParent,
                a.AllowPosting,
                a.IsActive,
                Children = BuildAccountTree(
                    allAccounts.Where(child => child.ParentId == a.Id).ToList(),
                    allAccounts
                )
            }).ToList<object>();
        }
    }

    // DTOs


    public class CreateSupplierTypeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int DisplayOrder { get; set; } = 1;
    }




}
