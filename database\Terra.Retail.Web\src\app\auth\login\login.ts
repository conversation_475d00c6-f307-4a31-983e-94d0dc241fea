import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

interface Branch {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
  isMainBranch: boolean;
  isActive: boolean;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './login.html',
  styleUrls: ['./login.scss']
})
export class Login implements OnInit {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;
  errorMessage = '';
  branches: Branch[] = [];
  apiStatus = 'checking';
  dbStatus = 'checking';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      branchId: [''],
      rememberMe: [false]
    });
  }

  ngOnInit() {
    this.checkSystemStatus();
    this.loadBranches();
    this.setDemoCredentials();
  }

  setDemoCredentials() {
    this.loginForm.patchValue({
      username: 'admin',
      password: 'admin123'
    });
  }

  checkSystemStatus() {
    this.http.get('http://localhost:5000/health').subscribe({
      next: (response: any) => {
        this.apiStatus = 'connected';
        this.dbStatus = response.Database?.Connected ? 'connected' : 'disconnected';
      },
      error: () => {
        this.apiStatus = 'disconnected';
        this.dbStatus = 'disconnected';
      }
    });
  }

  loadBranches() {
    this.http.get<any>('http://localhost:5000/api/simple/branches').subscribe({
      next: (response) => {
        this.branches = response.branches || [];
        console.log('Loaded branches:', this.branches);

        // Auto-select main branch if available
        const mainBranch = this.branches.find(b => b.isMainBranch);
        if (mainBranch) {
          this.loginForm.patchValue({ branchId: mainBranch.id });
        } else if (this.branches.length > 0) {
          // If no main branch, select first branch
          this.loginForm.patchValue({ branchId: this.branches[0].id });
        }
      },
      error: (error) => {
        console.error('Error loading branches:', error);
        // Fallback to demo data
        this.branches = [
          { id: 1, nameAr: 'الفرع الرئيسي - القاهرة', nameEn: 'Main Branch - Cairo', code: 'MAIN', isMainBranch: true, isActive: true },
          { id: 2, nameAr: 'فرع الإسكندرية', nameEn: 'Alexandria Branch', code: 'ALX', isMainBranch: false, isActive: true },
          { id: 3, nameAr: 'فرع الجيزة', nameEn: 'Giza Branch', code: 'GIZ', isMainBranch: false, isActive: true },
          { id: 4, nameAr: 'فرع المنصورة', nameEn: 'Mansoura Branch', code: 'MAN', isMainBranch: false, isActive: true }
        ];
        this.loginForm.patchValue({ branchId: 1 });
        this.showMessage('تم تحميل الفروع التجريبية', false);
      }
    });
  }

  private showMessage(message: string, isError = false) {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top',
      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']
    });
  }

  onLogin() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      const loginData = this.loginForm.value;

      setTimeout(() => {
        if (loginData.username === 'admin' && loginData.password === 'admin123') {
          const selectedBranch = this.branches.find(b => b.id === loginData.branchId);

          localStorage.setItem('currentUser', JSON.stringify({
            username: loginData.username,
            branch: selectedBranch,
            loginTime: new Date().toISOString()
          }));

          this.snackBar.open('تم تسجيل الدخول بنجاح!', 'إغلاق', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top'
          });

          this.router.navigate(['/dashboard']);
        } else {
          this.errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
          this.snackBar.open('فشل تسجيل الدخول', 'إغلاق', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top'
          });
        }
        this.isLoading = false;
      }, 2000);
    }
  }

  loginAsDemo() {
    this.loginForm.patchValue({
      username: 'admin',
      password: 'admin123'
    });
    this.onLogin();
  }
}
