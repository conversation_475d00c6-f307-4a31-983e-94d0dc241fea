/* Terra Retail ERP - Professional Login Page */

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  direction: rtl;
  font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Background Animation */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 70%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 40%;
  left: 5%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo Section */
.logo-section {
  text-align: center;
  margin-bottom: 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 3rem !important;
  color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.logo-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.8;
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-header p {
  color: #7f8c8d;
  font-size: 0.95rem;
  margin: 0;
}

/* Form Styles */
.login-form {
  width: 100%;
}

.full-width {
  width: 100%;
  margin-bottom: 1rem;
}

.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 12px;
  }

  .mat-mdc-form-field-focus-overlay {
    border-radius: 12px;
  }
}

.branch-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .main-branch-badge {
    background: #e74c3c;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-right: auto;
  }
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;

  &:hover {
    color: #764ba2;
    text-decoration: underline;
  }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #ffebee;
  color: #c62828;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;

  mat-icon {
    font-size: 1.2rem;
  }
}

/* Login Button */
.login-button {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);

  &:disabled {
    opacity: 0.7;
  }

  mat-spinner {
    margin-left: 0.5rem;
  }
}

/* Alternative Login */
.alternative-login {
  text-align: center;
}

.divider {
  position: relative;
  margin: 1.5rem 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
  }

  span {
    background: white;
    padding: 0 1rem;
    color: #999;
    font-size: 0.9rem;
  }
}

.demo-button {
  width: 100%;
  height: 44px;
  border-radius: 12px;
  border: 2px solid #667eea;
  color: #667eea;
  font-weight: 500;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
}

/* Footer */
.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.login-footer p {
  color: #999;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 1rem;

  a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.8rem;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* System Status */
.system-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 3;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .status-online {
    color: #27ae60;
  }

  .status-offline {
    color: #e74c3c;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-card {
    margin: 1rem;
    padding: 2rem;
    max-width: none;
  }

  .logo-text {
    font-size: 1.5rem;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  .system-status {
    bottom: 10px;
    right: 10px;
  }

  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}