
/* Terra Retail ERP - Professional Arabic Theme */
@use '@angular/material' as mat;
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');

// Define professional color palette
$terra-primary: (
  50: #e8f4fd,
  100: #c5e4fa,
  200: #9fd2f7,
  300: #79c0f4,
  400: #5cb3f2,
  500: #3fa6f0,
  600: #399eee,
  700: #3195ec,
  800: #298bea,
  900: #1b7be6,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
  )
);

$terra-accent: (
  50: #f3e5f5,
  100: #e1bee7,
  200: #ce93d8,
  300: #ba68c8,
  400: #ab47bc,
  500: #9c27b0,
  600: #8e24aa,
  700: #7b1fa2,
  800: #6a1b9a,
  900: #4a148c,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: white,
    400: white,
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
  )
);

// Apply professional theme
html {
  @include mat.theme((
    color: (
      theme-type: light,
      primary: $terra-primary,
      tertiary: $terra-accent,
    ),
    typography: (
      brand-family: 'Tajawal, IBM Plex Sans Arabic, Roboto, "Helvetica Neue", Arial, sans-serif',
      plain-family: 'Tajawal, IBM Plex Sans Arabic, Roboto, "Helvetica Neue", Arial, sans-serif',
    ),
    density: 0,
  ));
}

/* الأنماط العامة الاحترافية */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Tajawal', 'IBM Plex Sans Arabic', 'Roboto', "Helvetica Neue", Arial, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #1e293b;
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  font-weight: 400;
  line-height: 1.6;
}

/* دعم اللغة العربية */
[dir="rtl"] {
  text-align: right;
  font-family: 'Tajawal', 'IBM Plex Sans Arabic', 'Roboto', "Helvetica Neue", Arial, sans-serif;
}

/* تحسين النصوص العربية */
.arabic-text {
  font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif;
  font-weight: 400;
  letter-spacing: 0.02em;
}

.arabic-heading {
  font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif;
  font-weight: 600;
  letter-spacing: 0.01em;
}

/* إصلاح شامل للعناصر المتداخلة */
* {
  box-sizing: border-box;
}

/* إصلاح شامل لـ Material Form Fields */
.mat-mdc-form-field {
  width: 100% !important;
  margin-bottom: 0 !important;

  .mat-mdc-form-field-wrapper {
    width: 100% !important;
    padding-bottom: 0 !important;
  }

  .mat-mdc-form-field-flex {
    width: 100% !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-infix {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    border: none !important;
    min-height: 48px !important;
  }

  .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }

  .mat-mdc-text-field-wrapper {
    width: 100% !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    background: white !important;
    min-height: 48px !important;

    &:hover {
      border-color: #cbd5e1 !important;
    }

    &.mdc-text-field--focused {
      border-color: #3b82f6 !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  .mat-mdc-select-trigger {
    width: 100% !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-select-value {
    width: 100% !important;
  }
}

/* إصلاح الأزرار */
.mat-mdc-raised-button,
.mat-mdc-outlined-button {
  min-height: 48px !important;
  padding: 0 1.5rem !important;
  border-radius: 8px !important;
  font-family: 'Tajawal', 'IBM Plex Sans Arabic', sans-serif !important;
  font-weight: 500 !important;

  .mat-icon {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
}

/* إصلاح Material Cards */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e2e8f0 !important;

  .mat-mdc-card-content {
    padding: 1.5rem !important;
  }
}

/* إصلاح Material Tables */
.mat-mdc-table {
  width: 100% !important;

  .mat-mdc-header-row {
    background: #f8fafc !important;

    .mat-mdc-header-cell {
      font-weight: 600 !important;
      color: #374151 !important;
      border-bottom: 1px solid #e5e7eb !important;
      padding: 1rem !important;
    }
  }

  .mat-mdc-row {
    transition: background-color 0.2s ease !important;

    &:hover {
      background-color: #f8fafc !important;
    }

    .mat-mdc-cell {
      padding: 1rem !important;
      border-bottom: 1px solid #f3f4f6 !important;
    }
  }
}

/* Professional Global Components */
.professional-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }
}

.professional-button {
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
  padding: 8px 16px;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #3fa6f0 0%, #1b7be6 100%);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #298bea 0%, #1565c0 100%);
      transform: translateY(-1px);
    }
  }

  &.secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #e2e8f0;
      border-color: #cbd5e1;
    }
  }
}

.professional-input {
  .mat-mdc-form-field {
    width: 100%;

    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
      background-color: #f8fafc;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        border-color: #cbd5e1;
      }

      &.mdc-text-field--focused {
        border-color: #3fa6f0;
        background-color: white;
      }
    }
  }
}

.professional-table {
  .mat-mdc-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .mat-mdc-header-row {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

      .mat-mdc-header-cell {
        font-weight: 600;
        color: #374151;
        border-bottom: 1px solid #e5e7eb;
      }
    }

    .mat-mdc-row {
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #f8fafc;
      }

      .mat-mdc-cell {
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    text-align: center;

    .mat-mdc-progress-spinner {
      margin: 0 auto 16px;
    }

    .loading-text {
      color: #64748b;
      font-weight: 500;
    }
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-slide-up {
  animation: slideInUp 0.5s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-card {
    margin: 8px;
    border-radius: 8px;
  }

  .professional-button {
    width: 100%;
    margin-bottom: 8px;
  }
}
