import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

export interface Customer {
  id: number;
  customerCode: string;
  fullName: string;
  nameAr: string;
  nameEn?: string;
  customerTypeId: number;
  customerTypeName: string;
  phoneNumber: string;
  phone1: string;
  phone2?: string;
  email?: string;
  address?: string;
  areaId?: number;
  areaName?: string;
  branchId?: number;
  branchName?: string;
  priceCategoryId?: number;
  priceCategoryName?: string;
  creditLimit: number;
  openingBalance: number;
  currentBalance: number;
  isActive: boolean;
  createdAt: Date;
}

export interface CustomerType {
  id: number;
  nameAr: string;
  nameEn?: string;
  defaultDiscountPercentage: number;
  defaultCreditLimit: number;
}

export interface Area {
  id: number;
  nameAr: string;
  nameEn?: string;
  code: string;
}

export interface Branch {
  id: number;
  code: string;
  nameAr: string;
  nameEn?: string;
  address?: string;
  phone?: string;
  isMainBranch: boolean;
}

@Component({
  selector: 'app-customers',
  templateUrl: './customers.component.html',
  styleUrls: ['./customers.component.scss'],
  standalone: false
})
export class CustomersComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  // Table Configuration
  displayedColumns: string[] = [
    'customerCode',
    'fullName', 
    'customerTypeName',
    'phoneNumber',
    'areaName',
    'currentBalance',
    'isActive',
    'actions'
  ];
  
  dataSource = new MatTableDataSource<Customer>([]);
  
  // Data Arrays
  customers: Customer[] = [];
  customerTypes: CustomerType[] = [];
  areas: Area[] = [];
  branches: Branch[] = [];
  
  // Filter Properties
  searchTerm: string = '';
  selectedCustomerType: string = '';
  selectedArea: string = '';
  selectedBranch: string = '';
  
  // Statistics
  totalCustomers: number = 0;
  activeCustomers: number = 0;
  totalBalance: number = 0;
  totalCreditLimit: number = 0;
  
  // Loading State
  isLoading: boolean = false;
  
  private apiUrl = environment.apiUrl;

  constructor(
    private http: HttpClient,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    
    // Custom filter predicate
    this.dataSource.filterPredicate = (data: Customer, filter: string): boolean => {
      const searchStr = filter.toLowerCase();
      return data.fullName.toLowerCase().includes(searchStr) ||
             data.customerCode.toLowerCase().includes(searchStr) ||
             data.phoneNumber.toLowerCase().includes(searchStr) ||
             (data.email ? data.email.toLowerCase().includes(searchStr) : false);
    };
  }

  async loadInitialData(): Promise<void> {
    this.isLoading = true;
    
    try {
      // Load all required data in parallel
      await Promise.all([
        this.loadCustomers(),
        this.loadCustomerTypes(),
        this.loadAreas(),
        this.loadBranches()
      ]);
      
      this.calculateStatistics();
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.showMessage('حدث خطأ في تحميل البيانات', 'error');
    } finally {
      this.isLoading = false;
    }
  }

  async loadCustomers(): Promise<void> {
    try {
      const response = await this.http.get<any>(`${this.apiUrl}/customers`).toPromise();
      
      if (response && Array.isArray(response)) {
        this.customers = response.map(customer => ({
          ...customer,
          fullName: customer.nameAr || customer.fullName,
          phoneNumber: customer.phone1 || customer.phoneNumber,
          createdAt: new Date(customer.createdAt)
        }));
      } else {
        // Fallback data if API fails
        this.customers = this.getFallbackCustomers();
      }
      
      this.dataSource.data = this.customers;
      this.totalCustomers = this.customers.length;
      
    } catch (error) {
      console.error('Error loading customers:', error);
      this.customers = this.getFallbackCustomers();
      this.dataSource.data = this.customers;
      this.showMessage('تم تحميل بيانات تجريبية للعملاء', 'warning');
    }
  }

  async loadCustomerTypes(): Promise<void> {
    try {
      const response = await this.http.get<any>(`${this.apiUrl}/simple/customer-types`).toPromise();
      
      if (response && response.customerTypes) {
        this.customerTypes = response.customerTypes;
      } else {
        this.customerTypes = this.getFallbackCustomerTypes();
      }
    } catch (error) {
      console.error('Error loading customer types:', error);
      this.customerTypes = this.getFallbackCustomerTypes();
    }
  }

  async loadAreas(): Promise<void> {
    try {
      const response = await this.http.get<any>(`${this.apiUrl}/simple/areas-db`).toPromise();
      
      if (response && Array.isArray(response)) {
        this.areas = response;
      } else {
        this.areas = this.getFallbackAreas();
      }
    } catch (error) {
      console.error('Error loading areas:', error);
      this.areas = this.getFallbackAreas();
    }
  }

  async loadBranches(): Promise<void> {
    try {
      const response = await this.http.get<any>(`${this.apiUrl}/simple/branches-db`).toPromise();
      
      if (response && response.branches) {
        this.branches = response.branches;
      } else {
        this.branches = this.getFallbackBranches();
      }
    } catch (error) {
      console.error('Error loading branches:', error);
      this.branches = this.getFallbackBranches();
    }
  }

  calculateStatistics(): void {
    this.totalCustomers = this.customers.length;
    this.activeCustomers = this.customers.filter(c => c.isActive).length;
    this.totalBalance = this.customers.reduce((sum, c) => sum + (c.currentBalance || 0), 0);
    this.totalCreditLimit = this.customers.reduce((sum, c) => sum + (c.creditLimit || 0), 0);
  }

  onSearch(): void {
    this.dataSource.filter = this.searchTerm.trim().toLowerCase();
  }

  onFilterChange(): void {
    let filteredData = this.customers;

    if (this.selectedCustomerType) {
      filteredData = filteredData.filter(c => c.customerTypeId.toString() === this.selectedCustomerType);
    }

    if (this.selectedArea) {
      filteredData = filteredData.filter(c => c.areaId?.toString() === this.selectedArea);
    }

    if (this.selectedBranch) {
      filteredData = filteredData.filter(c => c.branchId?.toString() === this.selectedBranch);
    }

    this.dataSource.data = filteredData;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedCustomerType = '';
    this.selectedArea = '';
    this.selectedBranch = '';
    this.dataSource.filter = '';
    this.dataSource.data = this.customers;
  }

  refreshCustomers(): void {
    this.loadCustomers();
    this.showMessage('تم تحديث بيانات العملاء', 'success');
  }

  openAddCustomerDialog(): void {
    // TODO: Implement add customer dialog
    this.showMessage('سيتم إضافة نافذة إضافة عميل قريباً', 'info');
  }

  viewCustomer(customer: Customer): void {
    // TODO: Implement view customer details
    this.showMessage(`عرض تفاصيل العميل: ${customer.fullName}`, 'info');
  }

  editCustomer(customer: Customer): void {
    // TODO: Implement edit customer dialog
    this.showMessage(`تعديل العميل: ${customer.fullName}`, 'info');
  }

  viewFinancials(customer: Customer): void {
    // TODO: Implement financial details view
    this.showMessage(`عرض الحساب المالي للعميل: ${customer.fullName}`, 'info');
  }

  toggleCustomerStatus(customer: Customer): void {
    customer.isActive = !customer.isActive;
    this.showMessage(`تم ${customer.isActive ? 'تفعيل' : 'إلغاء تفعيل'} العميل: ${customer.fullName}`, 'success');
    this.calculateStatistics();
  }

  deleteCustomer(customer: Customer): void {
    if (confirm(`هل أنت متأكد من حذف العميل: ${customer.fullName}؟`)) {
      const index = this.customers.findIndex(c => c.id === customer.id);
      if (index > -1) {
        this.customers.splice(index, 1);
        this.dataSource.data = this.customers;
        this.calculateStatistics();
        this.showMessage(`تم حذف العميل: ${customer.fullName}`, 'success');
      }
    }
  }

  exportCustomers(): void {
    // TODO: Implement export functionality
    this.showMessage('سيتم إضافة وظيفة التصدير قريباً', 'info');
  }

  private showMessage(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: [`snackbar-${type}`],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  // Fallback data methods
  private getFallbackCustomers(): Customer[] {
    return [
      {
        id: 1,
        customerCode: 'CUS001',
        fullName: 'شركة الأهرام للتجارة',
        nameAr: 'شركة الأهرام للتجارة',
        nameEn: 'Ahram Trading Company',
        customerTypeId: 3,
        customerTypeName: 'عميل مؤسسي',
        phoneNumber: '+201111111111',
        phone1: '+201111111111',
        phone2: '+201111111112',
        email: '<EMAIL>',
        address: 'شارع التحرير، وسط البلد',
        areaId: 1,
        areaName: 'القاهرة',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        priceCategoryId: 3,
        priceCategoryName: 'سعر كبار العملاء',
        creditLimit: 100000,
        openingBalance: 25000,
        currentBalance: 25000,
        isActive: true,
        createdAt: new Date()
      }
      // Add more fallback customers as needed
    ];
  }

  private getFallbackCustomerTypes(): CustomerType[] {
    return [
      { id: 1, nameAr: 'عميل تجزئة', nameEn: 'Retail Customer', defaultDiscountPercentage: 0, defaultCreditLimit: 5000 },
      { id: 2, nameAr: 'عميل جملة', nameEn: 'Wholesale Customer', defaultDiscountPercentage: 10, defaultCreditLimit: 50000 },
      { id: 3, nameAr: 'عميل مؤسسي', nameEn: 'Corporate Customer', defaultDiscountPercentage: 15, defaultCreditLimit: 100000 }
    ];
  }

  private getFallbackAreas(): Area[] {
    return [
      { id: 1, nameAr: 'القاهرة', nameEn: 'Cairo', code: 'CAI' },
      { id: 2, nameAr: 'الجيزة', nameEn: 'Giza', code: 'GIZ' },
      { id: 3, nameAr: 'الإسكندرية', nameEn: 'Alexandria', code: 'ALX' }
    ];
  }

  private getFallbackBranches(): Branch[] {
    return [
      { id: 1, code: 'BR001', nameAr: 'الفرع الرئيسي', nameEn: 'Main Branch', address: 'القاهرة', phone: '+201234567890', isMainBranch: true },
      { id: 2, code: 'BR002', nameAr: 'فرع الإسكندرية', nameEn: 'Alexandria Branch', address: 'الإسكندرية', phone: '+203456789012', isMainBranch: false }
    ];
  }
}
