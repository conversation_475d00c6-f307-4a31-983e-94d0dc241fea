using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("ChartOfAccounts")]
    public class ChartOfAccount
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int AccountType { get; set; } // 1=Assets, 2=Liabilities, 3=Equity, 4=Revenue, 5=Expenses

        public int? ParentId { get; set; }

        public int Level { get; set; } = 1;

        public bool IsParent { get; set; } = false;

        public bool AllowPosting { get; set; } = true;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ChartOfAccount? Parent { get; set; }
        public virtual ICollection<ChartOfAccount> Children { get; set; } = new List<ChartOfAccount>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    [Table("JournalEntries")]
    public class JournalEntry
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string EntryNumber { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Reference { get; set; }

        public int TransactionType { get; set; } = 1; // 1=Normal, 2=Opening, 3=Adjustment, 4=Closing

        public JournalEntryStatus Status { get; set; } = JournalEntryStatus.Draft;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; } = 0;

        public int UserId { get; set; }

        public int? PostedBy { get; set; }

        public DateTime? PostedAt { get; set; }

        // معلومات الترحيل المبدئي
        public int? PreliminaryPostedBy { get; set; }
        public DateTime? PreliminaryPostedAt { get; set; }
        [StringLength(500)]
        public string? PreliminaryPostingNotes { get; set; }

        // معلومات الترحيل النهائي
        public int? FinalPostedBy { get; set; }
        public DateTime? FinalPostedAt { get; set; }
        [StringLength(500)]
        public string? FinalPostingNotes { get; set; }

        // معلومات العكس
        public int? ReversedBy { get; set; }
        public DateTime? ReversedAt { get; set; }
        [StringLength(500)]
        public string? ReversalReason { get; set; }
        public int? ReversalJournalEntryId { get; set; } // القيد المعكوس

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual User? PostedByUser { get; set; }
        public virtual User? PreliminaryPostedByUser { get; set; }
        public virtual User? FinalPostedByUser { get; set; }
        public virtual User? ReversedByUser { get; set; }
        public virtual JournalEntry? ReversalJournalEntry { get; set; }
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    [Table("JournalEntryDetails")]
    public class JournalEntryDetail
    {
        [Key]
        public int Id { get; set; }

        public int JournalEntryId { get; set; }

        public int AccountId { get; set; }

        public int LineNumber { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        public int? CostCenterId { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual JournalEntry JournalEntry { get; set; } = null!;
        public virtual ChartOfAccount Account { get; set; } = null!;
        public virtual CostCenter? CostCenter { get; set; }
    }

    [Table("CostCenters")]
    public class CostCenter
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    [Table("Receipts")]
    public class Receipt
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string ReceiptNumber { get; set; } = string.Empty;

        public DateTime ReceiptDate { get; set; } = DateTime.Now;

        public int? CustomerId { get; set; }

        public int BranchId { get; set; }

        public int PaymentMethodId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        public int Status { get; set; } = 1; // 1=Completed, 2=Cancelled

        public int UserId { get; set; }

        [StringLength(10)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Customer? Customer { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    [Table("Payments")]
    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string PaymentNumber { get; set; } = string.Empty;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public int? SupplierId { get; set; }

        public int BranchId { get; set; }

        public int PaymentMethodId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        public int Status { get; set; } = 1; // 1=Completed, 2=Cancelled

        public int UserId { get; set; }

        [StringLength(10)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Supplier? Supplier { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    [Table("PaymentMethods")]
    public class PaymentMethod
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? Code { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int PaymentType { get; set; } = 1; // 1=Cash, 2=Bank, 3=Credit

        public bool RequireReference { get; set; } = false;

        public bool RequireApproval { get; set; } = false;

        [StringLength(50)]
        public string? Icon { get; set; }

        [StringLength(20)]
        public string? Color { get; set; }

        public int DisplayOrder { get; set; } = 1;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? TransactionFeePercentage { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? FixedTransactionFee { get; set; }

        public int? ChartOfAccountId { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsDefault { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ChartOfAccount? ChartAccount { get; set; }
        public virtual ICollection<Receipt> Receipts { get; set; } = new List<Receipt>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }

    [Table("AccountBalances")]
    public class AccountBalance
    {
        [Key]
        public int Id { get; set; }

        public int AccountId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetBalance { get; set; } = 0;

        public DateTime LastTransactionDate { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ChartOfAccount Account { get; set; } = null!;
    }
}
