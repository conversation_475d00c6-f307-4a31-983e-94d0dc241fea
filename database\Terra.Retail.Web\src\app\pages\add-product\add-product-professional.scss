/* Professional Add Product Styles */
.professional-add-product {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  .professional-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 24px 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      
      .header-info {
        .header-title {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0;
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          
          .header-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;
            color: #3fa6f0;
          }
        }
        
        .header-subtitle {
          margin: 8px 0 0 0;
          color: #64748b;
          font-size: 16px;
          font-weight: 400;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
    
    .progress-steps {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      
      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        
        .step-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #e2e8f0;
          color: #64748b;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          transition: all 0.3s ease;
          
          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }
        
        .step-label {
          font-size: 12px;
          color: #64748b;
          font-weight: 500;
          text-align: center;
          max-width: 100px;
        }
        
        &.active {
          .step-circle {
            background: linear-gradient(135deg, #3fa6f0 0%, #1b7be6 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(63, 166, 240, 0.3);
          }
          
          .step-label {
            color: #3fa6f0;
            font-weight: 600;
          }
        }
        
        &.completed {
          .step-circle {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
          }
          
          .step-label {
            color: #10b981;
            font-weight: 600;
          }
        }
      }
      
      .step-line {
        width: 60px;
        height: 2px;
        background: #e2e8f0;
        transition: all 0.3s ease;
        
        &.completed {
          background: linear-gradient(90deg, #10b981 0%, #059669 100%);
        }
      }
    }
  }
  
  .professional-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px;
    
    .professional-form {
      .form-step {
        .step-content {
          .step-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0 0 24px 0;
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            
            mat-icon {
              font-size: 28px;
              width: 28px;
              height: 28px;
              color: #3fa6f0;
            }
          }
          
          .professional-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            margin-bottom: 24px;
            overflow: hidden;
            transition: all 0.3s ease;
            
            &:hover {
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
              transform: translateY(-2px);
            }
            
            .card-header {
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              padding: 20px 24px;
              border-bottom: 1px solid #e2e8f0;
              
              h3 {
                margin: 0 0 4px 0;
                font-size: 18px;
                font-weight: 600;
                color: #1e293b;
              }
              
              p {
                margin: 0;
                font-size: 14px;
                color: #64748b;
              }
            }
            
            .card-content {
              padding: 24px;
              
              .form-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
              
              .form-group {
                &.full-width {
                  grid-column: 1 / -1;
                }
                
                .professional-input {
                  width: 100%;
                  
                  .mat-mdc-form-field {
                    width: 100%;
                    
                    .mat-mdc-text-field-wrapper {
                      border-radius: 8px;
                      background-color: #f8fafc;
                      border: 1px solid #e2e8f0;
                      transition: all 0.3s ease;
                      
                      &:hover {
                        border-color: #cbd5e1;
                        background-color: #f1f5f9;
                      }
                      
                      &.mdc-text-field--focused {
                        border-color: #3fa6f0;
                        background-color: white;
                        box-shadow: 0 0 0 3px rgba(63, 166, 240, 0.1);
                      }
                    }
                  }
                }
                
                .option-content {
                  display: flex;
                  flex-direction: column;
                  gap: 2px;
                  
                  strong {
                    font-weight: 600;
                    color: #1e293b;
                  }
                  
                  small {
                    color: #64748b;
                    font-size: 12px;
                  }
                }
              }
              
              .profit-margin {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                border: 1px solid #a7f3d0;
                border-radius: 8px;
                color: #065f46;
                font-weight: 500;
                margin-top: 16px;
                
                mat-icon {
                  color: #10b981;
                }
              }
              
              .settings-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 16px;
                
                .professional-checkbox {
                  display: flex;
                  flex-direction: column;
                  gap: 4px;
                  padding: 16px;
                  background: #f8fafc;
                  border: 1px solid #e2e8f0;
                  border-radius: 8px;
                  transition: all 0.3s ease;
                  
                  &:hover {
                    background: #f1f5f9;
                    border-color: #cbd5e1;
                  }
                  
                  .checkbox-label {
                    font-weight: 500;
                    color: #1e293b;
                  }
                  
                  small {
                    color: #64748b;
                    font-size: 12px;
                  }
                }
              }
              
              .review-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 16px;
                
                .review-item {
                  display: flex;
                  flex-direction: column;
                  gap: 4px;
                  padding: 16px;
                  background: #f8fafc;
                  border: 1px solid #e2e8f0;
                  border-radius: 8px;
                  
                  label {
                    font-weight: 500;
                    color: #64748b;
                    font-size: 12px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                  }
                  
                  span {
                    font-weight: 600;
                    color: #1e293b;
                    font-size: 16px;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .navigation-buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 32px;
      padding: 24px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }
  }
}

/* Animations */
@keyframes slideAnimation {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-add-product {
    .professional-header {
      padding: 16px 20px;
      
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 24px;
        
        .header-actions {
          width: 100%;
          
          .professional-button {
            flex: 1;
          }
        }
      }
      
      .progress-steps {
        flex-direction: column;
        gap: 12px;
        
        .step {
          flex-direction: row;
          gap: 12px;
          
          .step-label {
            max-width: none;
          }
        }
        
        .step-line {
          display: none;
        }
      }
    }
    
    .professional-content {
      padding: 16px 20px;
      
      .professional-form {
        .form-step {
          .step-content {
            .professional-card {
              .card-content {
                .form-grid {
                  grid-template-columns: 1fr;
                  gap: 16px;
                }
                
                .settings-grid {
                  grid-template-columns: 1fr;
                }
                
                .review-grid {
                  grid-template-columns: 1fr;
                }
              }
            }
          }
        }
      }
      
      .navigation-buttons {
        flex-direction: column;
        gap: 12px;
        
        .professional-button {
          width: 100%;
        }
      }
    }
  }
}
