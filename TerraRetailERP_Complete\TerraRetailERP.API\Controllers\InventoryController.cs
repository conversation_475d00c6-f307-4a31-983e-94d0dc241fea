using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("📦 Inventory Management")]
    public class InventoryController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public InventoryController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet("stock")]
        public async Task<ActionResult> GetStock(
            [FromQuery] int? branchId = null,
            [FromQuery] int? productId = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] bool? lowStock = null,
            [FromQuery] bool? outOfStock = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.ProductStocks
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Category)
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Unit)
                    .Include(ps => ps.Branch)
                    .AsQueryable();

                // Apply filters
                if (branchId.HasValue)
                    query = query.Where(ps => ps.BranchId == branchId);

                if (productId.HasValue)
                    query = query.Where(ps => ps.ProductId == productId);

                if (categoryId.HasValue)
                    query = query.Where(ps => ps.Product.CategoryId == categoryId);

                if (lowStock == true)
                    query = query.Where(ps => ps.AvailableQuantity <= ps.BranchMinStock && ps.BranchMinStock > 0);

                if (outOfStock == true)
                    query = query.Where(ps => ps.AvailableQuantity <= 0);

                var totalCount = await query.CountAsync();
                var stocks = await query
                    .OrderBy(ps => ps.Product.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(ps => new
                    {
                        ps.Id,
                        ps.ProductId,
                        ProductCode = ps.Product.ProductCode,
                        ProductNameAr = ps.Product.NameAr,
                        ProductNameEn = ps.Product.NameEn,
                        CategoryName = ps.Product.Category.NameAr,
                        UnitName = ps.Product.Unit.NameAr,
                        UnitSymbol = ps.Product.Unit.Symbol,
                        ps.BranchId,
                        BranchName = ps.Branch.NameAr,
                        ps.AvailableQuantity,
                        ps.ReservedQuantity,
                        ps.OnOrderQuantity,
                        ps.BranchMinStock,
                        ps.BranchMaxStock,
                        ps.BranchReorderPoint,
                        ps.AverageCostPrice,
                        ps.LastCostPrice,
                        ps.StockValue,
                        ps.StorageLocation,
                        ps.IsAvailableForSale,
                        StockStatus = ps.AvailableQuantity <= 0 ? "Out of Stock" :
                                    ps.AvailableQuantity <= ps.BranchMinStock ? "Low Stock" :
                                    ps.AvailableQuantity > ps.BranchMaxStock ? "Over Stock" : "Normal",
                        ps.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = stocks,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("movements")]
        public async Task<ActionResult> GetStockMovements(
            [FromQuery] int? branchId = null,
            [FromQuery] int? productId = null,
            [FromQuery] int? movementType = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.StockMovements
                    .Include(sm => sm.Product)
                    .Include(sm => sm.Branch)
                    .Include(sm => sm.User)
                    .AsQueryable();

                // Apply filters
                if (branchId.HasValue)
                    query = query.Where(sm => sm.BranchId == branchId);

                if (productId.HasValue)
                    query = query.Where(sm => sm.ProductId == productId);

                if (movementType.HasValue)
                    query = query.Where(sm => sm.MovementType == movementType);

                if (fromDate.HasValue)
                    query = query.Where(sm => sm.MovementDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(sm => sm.MovementDate <= toDate);

                var totalCount = await query.CountAsync();
                var movements = await query
                    .OrderByDescending(sm => sm.MovementDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(sm => new
                    {
                        sm.Id,
                        sm.MovementNumber,
                        sm.MovementDate,
                        sm.ProductId,
                        ProductCode = sm.Product.ProductCode,
                        ProductName = sm.Product.NameAr,
                        sm.BranchId,
                        BranchName = sm.Branch.NameAr,
                        sm.MovementType,
                        MovementTypeName = sm.MovementType == 1 ? "وارد" : 
                                         sm.MovementType == 2 ? "صادر" :
                                         sm.MovementType == 3 ? "تحويل" : "تسوية",
                        sm.MovementReason,
                        sm.Quantity,
                        sm.UnitCost,
                        sm.TotalCost,
                        sm.BalanceBefore,
                        sm.BalanceAfter,
                        sm.BatchNumber,
                        sm.SourceType,
                        sm.SourceId,
                        UserName = sm.User.FullName,
                        sm.Notes,
                        sm.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = movements,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("transfers")]
        public async Task<ActionResult> GetBranchTransfers(
            [FromQuery] int? fromBranchId = null,
            [FromQuery] int? toBranchId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.BranchTransfers
                    .Include(bt => bt.FromBranch)
                    .Include(bt => bt.ToBranch)
                    .Include(bt => bt.RequestedByUser)
                    .Include(bt => bt.BranchTransferDetails)
                        .ThenInclude(btd => btd.Product)
                    .AsQueryable();

                // Apply filters
                if (fromBranchId.HasValue)
                    query = query.Where(bt => bt.FromBranchId == fromBranchId);

                if (toBranchId.HasValue)
                    query = query.Where(bt => bt.ToBranchId == toBranchId);

                if (status.HasValue)
                    query = query.Where(bt => bt.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(bt => bt.TransferDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(bt => bt.TransferDate <= toDate);

                var totalCount = await query.CountAsync();
                var transfers = await query
                    .OrderByDescending(bt => bt.TransferDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(bt => new
                    {
                        bt.Id,
                        bt.TransferNumber,
                        bt.TransferDate,
                        bt.ReceivedDate,
                        bt.FromBranchId,
                        FromBranchName = bt.FromBranch.NameAr,
                        bt.ToBranchId,
                        ToBranchName = bt.ToBranch.NameAr,
                        bt.Status,
                        StatusName = bt.Status == 1 ? "معلق" :
                                   bt.Status == 2 ? "مرسل" :
                                   bt.Status == 3 ? "مستلم" : "ملغي",
                        bt.TotalValue,
                        bt.TotalItems,
                        RequestedByName = bt.RequestedByUser.FullName,
                        ItemsCount = bt.BranchTransferDetails.Count,
                        bt.Notes,
                        bt.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = transfers,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("transfers")]
        public async Task<ActionResult> CreateBranchTransfer(CreateBranchTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                // Generate transfer number
                var transferNumber = await GenerateTransferNumber(request.FromBranchId);

                var transfer = new BranchTransfer
                {
                    TransferNumber = transferNumber,
                    FromBranchId = request.FromBranchId,
                    ToBranchId = request.ToBranchId,
                    TransferDate = request.TransferDate ?? DateTime.Now,
                    Status = 1, // Pending
                    Notes = request.Notes,
                    RequestedBy = userId.Value,
                    TotalItems = request.Items.Count,
                    TotalValue = 0,
                    CreatedAt = DateTime.Now
                };

                _context.BranchTransfers.Add(transfer);
                await _context.SaveChangesAsync();

                // Add transfer details
                decimal totalValue = 0;
                int lineNumber = 1;

                foreach (var item in request.Items)
                {
                    // Get current stock and cost
                    var stock = await _context.ProductStocks
                        .FirstOrDefaultAsync(ps => ps.ProductId == item.ProductId && ps.BranchId == request.FromBranchId);

                    if (stock == null || stock.AvailableQuantity < item.RequestedQuantity)
                    {
                        await transaction.RollbackAsync();
                        return BadRequest(new { message = $"الكمية المطلوبة غير متوفرة للمنتج {item.ProductId}" });
                    }

                    var unitCost = stock.AverageCostPrice;
                    var lineCost = item.RequestedQuantity * unitCost;
                    totalValue += lineCost;

                    var transferDetail = new BranchTransferDetail
                    {
                        BranchTransferId = transfer.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        RequestedQuantity = item.RequestedQuantity,
                        SentQuantity = 0,
                        ReceivedQuantity = 0,
                        UnitCost = unitCost,
                        TotalCost = lineCost,
                        BatchNumber = item.BatchNumber,
                        ExpiryDate = item.ExpiryDate,
                        ItemNotes = item.ItemNotes,
                        CreatedAt = DateTime.Now
                    };

                    _context.BranchTransferDetails.Add(transferDetail);
                }

                transfer.TotalValue = totalValue;
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetBranchTransfers), new { id = transfer.Id }, new
                {
                    message = "تم إنشاء طلب التحويل بنجاح",
                    transferId = transfer.Id,
                    transferNumber = transfer.TransferNumber
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("transfers/{id}/send")]
        public async Task<IActionResult> SendBranchTransfer(int id, SendBranchTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var transfer = await _context.BranchTransfers
                    .Include(bt => bt.BranchTransferDetails)
                    .FirstOrDefaultAsync(bt => bt.Id == id);

                if (transfer == null)
                    return NotFound(new { message = "طلب التحويل غير موجود" });

                if (transfer.Status != 1)
                    return BadRequest(new { message = "لا يمكن إرسال هذا التحويل" });

                // Update sent quantities and reduce stock
                foreach (var sentItem in request.SentItems)
                {
                    var detail = transfer.BranchTransferDetails
                        .FirstOrDefault(d => d.Id == sentItem.DetailId);

                    if (detail != null)
                    {
                        detail.SentQuantity = sentItem.SentQuantity;

                        // Reduce stock from source branch
                        await UpdateProductStock(detail.ProductId, transfer.FromBranchId, 
                            -sentItem.SentQuantity, detail.UnitCost);

                        // Create stock movement for source branch
                        await CreateStockMovement(detail.ProductId, transfer.FromBranchId, 
                            -sentItem.SentQuantity, detail.UnitCost, "Branch Transfer Out", 
                            transfer.Id, transfer.TransferNumber, userId.Value);
                    }
                }

                transfer.Status = 2; // Sent
                transfer.SentBy = userId;
                transfer.SentAt = DateTime.Now;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { message = "تم إرسال التحويل بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("transfers/{id}/receive")]
        public async Task<IActionResult> ReceiveBranchTransfer(int id, ReceiveBranchTransferRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var transfer = await _context.BranchTransfers
                    .Include(bt => bt.BranchTransferDetails)
                    .FirstOrDefaultAsync(bt => bt.Id == id);

                if (transfer == null)
                    return NotFound(new { message = "طلب التحويل غير موجود" });

                if (transfer.Status != 2)
                    return BadRequest(new { message = "لا يمكن استلام هذا التحويل" });

                // Update received quantities and add stock
                foreach (var receivedItem in request.ReceivedItems)
                {
                    var detail = transfer.BranchTransferDetails
                        .FirstOrDefault(d => d.Id == receivedItem.DetailId);

                    if (detail != null)
                    {
                        detail.ReceivedQuantity = receivedItem.ReceivedQuantity;

                        // Add stock to destination branch
                        await UpdateProductStock(detail.ProductId, transfer.ToBranchId, 
                            receivedItem.ReceivedQuantity, detail.UnitCost);

                        // Create stock movement for destination branch
                        await CreateStockMovement(detail.ProductId, transfer.ToBranchId, 
                            receivedItem.ReceivedQuantity, detail.UnitCost, "Branch Transfer In", 
                            transfer.Id, transfer.TransferNumber, userId.Value);
                    }
                }

                transfer.Status = 3; // Received
                transfer.ReceivedBy = userId;
                transfer.ReceivedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { message = "تم استلام التحويل بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("adjustments")]
        public async Task<ActionResult> GetStockAdjustments(
            [FromQuery] int? branchId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.StockAdjustments
                    .Include(sa => sa.Branch)
                    .Include(sa => sa.User)
                    .Include(sa => sa.StockAdjustmentDetails)
                        .ThenInclude(sad => sad.Product)
                    .AsQueryable();

                // Apply filters
                if (branchId.HasValue)
                    query = query.Where(sa => sa.BranchId == branchId);

                if (status.HasValue)
                    query = query.Where(sa => sa.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(sa => sa.AdjustmentDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(sa => sa.AdjustmentDate <= toDate);

                var totalCount = await query.CountAsync();
                var adjustments = await query
                    .OrderByDescending(sa => sa.AdjustmentDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(sa => new
                    {
                        sa.Id,
                        sa.AdjustmentNumber,
                        sa.AdjustmentDate,
                        sa.AdjustmentReason,
                        sa.Description,
                        sa.BranchId,
                        BranchName = sa.Branch.NameAr,
                        sa.Status,
                        StatusName = sa.Status == 1 ? "مسودة" :
                                   sa.Status == 2 ? "معتمد" : "مرحل",
                        sa.TotalAdjustmentValue,
                        sa.TotalItems,
                        UserName = sa.User.FullName,
                        ItemsCount = sa.StockAdjustmentDetails.Count,
                        sa.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = adjustments,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateTransferNumber(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.BranchTransfer && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.BranchTransfer,
                    Prefix = "TRF",
                    CurrentValue = 1,
                    NumberLength = 8,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task UpdateProductStock(int productId, int branchId, decimal quantity, decimal unitCost)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            if (stock != null)
            {
                stock.AvailableQuantity += quantity;
                
                if (quantity > 0) // Incoming stock
                {
                    stock.TotalInQuantity += quantity;
                    // Update average cost
                    var totalValue = (stock.AvailableQuantity - quantity) * stock.AverageCostPrice + quantity * unitCost;
                    stock.AverageCostPrice = stock.AvailableQuantity > 0 ? totalValue / stock.AvailableQuantity : unitCost;
                    stock.LastCostPrice = unitCost;
                }
                else // Outgoing stock
                {
                    stock.TotalOutQuantity += Math.Abs(quantity);
                }

                stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;
                stock.UpdatedAt = DateTime.Now;
            }
        }

        private async Task CreateStockMovement(int productId, int branchId, decimal quantity, decimal unitCost, 
            string movementReason, int sourceId, string sourceReference, int userId)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            var movement = new StockMovement
            {
                ProductId = productId,
                BranchId = branchId,
                MovementNumber = $"MOV{DateTime.Now.Ticks}",
                MovementType = quantity > 0 ? 1 : 2, // 1=In, 2=Out
                MovementReason = movementReason,
                Quantity = Math.Abs(quantity),
                UnitCost = unitCost,
                TotalCost = Math.Abs(quantity) * unitCost,
                BalanceBefore = (stock?.AvailableQuantity ?? 0) - quantity,
                BalanceAfter = stock?.AvailableQuantity ?? 0,
                MovementDate = DateTime.Now,
                SourceId = sourceId,
                SourceType = "Transfer",
                UserId = userId,
                Notes = $"Transfer: {sourceReference}",
                CreatedAt = DateTime.Now
            };

            _context.StockMovements.Add(movement);
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class CreateBranchTransferRequest
    {
        public int FromBranchId { get; set; }
        public int ToBranchId { get; set; }
        public DateTime? TransferDate { get; set; }
        public string? Notes { get; set; }
        public List<CreateBranchTransferItemRequest> Items { get; set; } = new();
    }

    public class CreateBranchTransferItemRequest
    {
        public int ProductId { get; set; }
        public decimal RequestedQuantity { get; set; }
        public string? BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? ItemNotes { get; set; }
    }

    public class SendBranchTransferRequest
    {
        public List<SendBranchTransferItemRequest> SentItems { get; set; } = new();
    }

    public class SendBranchTransferItemRequest
    {
        public int DetailId { get; set; }
        public decimal SentQuantity { get; set; }
    }

    public class ReceiveBranchTransferRequest
    {
        public List<ReceiveBranchTransferItemRequest> ReceivedItems { get; set; } = new();
    }

    public class ReceiveBranchTransferItemRequest
    {
        public int DetailId { get; set; }
        public decimal ReceivedQuantity { get; set; }
    }
}
