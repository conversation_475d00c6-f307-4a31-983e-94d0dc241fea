{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/table\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"@angular/material/sort\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/tooltip\";\nconst _c0 = () => [10, 25, 50, 100];\nfunction CustomersNewComponent_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r1.name, \" \");\n  }\n}\nfunction CustomersNewComponent_mat_option_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gov_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", gov_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", gov_r2.name, \" \");\n  }\n}\nfunction CustomersNewComponent_th_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r3.code);\n  }\n}\nfunction CustomersNewComponent_th_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"div\", 63)(2, \"div\", 64);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 65);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const customer_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(customer_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r4.customerTypeName);\n  }\n}\nfunction CustomersNewComponent_th_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_127_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(customer_r5.phone);\n  }\n}\nfunction CustomersNewComponent_td_127_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(customer_r5.email);\n  }\n}\nfunction CustomersNewComponent_td_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"div\", 67);\n    i0.ɵɵtemplate(2, CustomersNewComponent_td_127_div_2_Template, 5, 1, \"div\", 68)(3, CustomersNewComponent_td_127_div_3_Template, 5, 1, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", customer_r5.phone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", customer_r5.email);\n  }\n}\nfunction CustomersNewComponent_th_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_130_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(customer_r6.address);\n  }\n}\nfunction CustomersNewComponent_td_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"div\", 72)(2, \"div\", 73);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersNewComponent_td_130_div_4_Template, 2, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(customer_r6.governorateName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", customer_r6.address);\n  }\n}\nfunction CustomersNewComponent_th_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"div\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r7 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r7.getBalanceClass(customer_r7.balance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 3, customer_r7.balance, \"1.2-2\"), \" \\u062C\\u0646\\u064A\\u0647 \");\n  }\n}\nfunction CustomersNewComponent_th_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(customer_r9.isActive ? \"active\" : \"inactive\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", customer_r9.isActive ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n  }\n}\nfunction CustomersNewComponent_th_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_td_139_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 61)(1, \"div\", 78)(2, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function CustomersNewComponent_td_139_Template_button_click_2_listener() {\n      const customer_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.viewCustomer(customer_r11));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function CustomersNewComponent_td_139_Template_button_click_5_listener() {\n      const customer_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.editCustomer(customer_r11));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function CustomersNewComponent_td_139_Template_button_click_8_listener() {\n      const customer_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.deleteCustomer(customer_r11));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction CustomersNewComponent_tr_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 82);\n  }\n}\nfunction CustomersNewComponent_tr_141_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 83);\n    i0.ɵɵlistener(\"click\", function CustomersNewComponent_tr_141_Template_tr_click_0_listener() {\n      const row_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.viewCustomer(row_r13));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersNewComponent_div_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵelement(1, \"mat-spinner\", 85);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CustomersNewComponent = /*#__PURE__*/(() => {\n  class CustomersNewComponent {\n    http;\n    // Component State\n    isLoading = true;\n    // Data\n    customers = [];\n    filteredCustomers = [];\n    paginatedCustomers = [];\n    customerTypes = [];\n    governorates = [];\n    // Statistics\n    statistics = {\n      totalCustomers: 0,\n      activeCustomers: 0,\n      newCustomersThisMonth: 0,\n      vipCustomers: 0\n    };\n    // Filters\n    searchTerm = '';\n    selectedCustomerType = '';\n    selectedGovernorate = '';\n    selectedStatus = '';\n    // Table Configuration\n    displayedColumns = ['code', 'name', 'contact', 'address', 'balance', 'status', 'actions'];\n    pageSize = 25;\n    currentPage = 0;\n    // Subscriptions\n    subscriptions = [];\n    constructor(http) {\n      this.http = http;\n    }\n    ngOnInit() {\n      this.loadInitialData();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Load initial data\n     */\n    loadInitialData() {\n      this.isLoading = true;\n      // Load all data in parallel\n      Promise.all([this.loadCustomers(), this.loadCustomerTypes(), this.loadGovernorates()]).then(() => {\n        this.calculateStatistics();\n        this.applyFilters();\n        this.isLoading = false;\n      }).catch(error => {\n        console.error('Error loading data:', error);\n        this.isLoading = false;\n      });\n    }\n    /**\n     * Load customers from API\n     */\n    loadCustomers() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/customers').subscribe({\n          next: response => {\n            this.customers = response.customers || [];\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading customers:', error);\n            // Use mock data if API fails\n            this.customers = this.getMockCustomers();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load customer types from API\n     */\n    loadCustomerTypes() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/customer-types').subscribe({\n          next: response => {\n            this.customerTypes = response.customerTypes || [];\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading customer types:', error);\n            // Use mock data if API fails\n            this.customerTypes = this.getMockCustomerTypes();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load governorates from API\n     */\n    loadGovernorates() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/governorates').subscribe({\n          next: response => {\n            this.governorates = response.governorates || [];\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading governorates:', error);\n            // Use mock data if API fails\n            this.governorates = this.getMockGovernorates();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Calculate statistics\n     */\n    calculateStatistics() {\n      this.statistics.totalCustomers = this.customers.length;\n      this.statistics.activeCustomers = this.customers.filter(c => c.isActive).length;\n      const currentMonth = new Date().getMonth();\n      const currentYear = new Date().getFullYear();\n      this.statistics.newCustomersThisMonth = this.customers.filter(c => {\n        const createdDate = new Date(c.createdAt);\n        return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;\n      }).length;\n      // VIP customers (customers with balance > 10000)\n      this.statistics.vipCustomers = this.customers.filter(c => c.balance > 10000).length;\n    }\n    /**\n     * Apply filters\n     */\n    applyFilters() {\n      let filtered = [...this.customers];\n      // Search filter\n      if (this.searchTerm.trim()) {\n        const term = this.searchTerm.toLowerCase().trim();\n        filtered = filtered.filter(customer => customer.name.toLowerCase().includes(term) || customer.code.toLowerCase().includes(term) || customer.phone && customer.phone.includes(term) || customer.email && customer.email.toLowerCase().includes(term));\n      }\n      // Customer type filter\n      if (this.selectedCustomerType) {\n        filtered = filtered.filter(customer => customer.customerTypeId.toString() === this.selectedCustomerType);\n      }\n      // Governorate filter\n      if (this.selectedGovernorate) {\n        filtered = filtered.filter(customer => customer.governorateId.toString() === this.selectedGovernorate);\n      }\n      // Status filter\n      if (this.selectedStatus) {\n        const isActive = this.selectedStatus === 'active';\n        filtered = filtered.filter(customer => customer.isActive === isActive);\n      }\n      this.filteredCustomers = filtered;\n      this.currentPage = 0;\n      this.updatePaginatedData();\n    }\n    /**\n     * Update paginated data\n     */\n    updatePaginatedData() {\n      const startIndex = this.currentPage * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);\n    }\n    /**\n     * Handle search\n     */\n    onSearch() {\n      this.applyFilters();\n    }\n    /**\n     * Handle filter change\n     */\n    onFilterChange() {\n      this.applyFilters();\n    }\n    /**\n     * Clear all filters\n     */\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedCustomerType = '';\n      this.selectedGovernorate = '';\n      this.selectedStatus = '';\n      this.applyFilters();\n    }\n    /**\n     * Handle page change\n     */\n    onPageChange(event) {\n      this.currentPage = event.pageIndex;\n      this.pageSize = event.pageSize;\n      this.updatePaginatedData();\n    }\n    /**\n     * Handle page size change\n     */\n    onPageSizeChange() {\n      this.currentPage = 0;\n      this.updatePaginatedData();\n    }\n    /**\n     * Get balance class for styling\n     */\n    getBalanceClass(balance) {\n      if (balance > 0) return 'positive';\n      if (balance < 0) return 'negative';\n      return 'zero';\n    }\n    /**\n     * Open add customer dialog\n     */\n    openAddCustomer() {\n      console.log('Open add customer dialog');\n      // Implement add customer functionality\n    }\n    /**\n     * View customer details\n     */\n    viewCustomer(customer) {\n      console.log('View customer:', customer);\n      // Implement view customer functionality\n    }\n    /**\n     * Edit customer\n     */\n    editCustomer(customer) {\n      console.log('Edit customer:', customer);\n      // Implement edit customer functionality\n    }\n    /**\n     * Delete customer\n     */\n    deleteCustomer(customer) {\n      console.log('Delete customer:', customer);\n      // Implement delete customer functionality\n    }\n    /**\n     * Export customers\n     */\n    exportCustomers() {\n      console.log('Export customers');\n      // Implement export functionality\n    }\n    /**\n     * Get mock customers data\n     */\n    getMockCustomers() {\n      return [{\n        id: 1,\n        code: 'C001',\n        name: 'أحمد محمد علي',\n        phone: '01234567890',\n        email: '<EMAIL>',\n        address: 'شارع النيل، المعادي',\n        customerTypeId: 1,\n        customerTypeName: 'عميل عادي',\n        governorateId: 1,\n        governorateName: 'القاهرة',\n        balance: 5000,\n        isActive: true,\n        createdAt: new Date('2024-01-15')\n      }, {\n        id: 2,\n        code: 'C002',\n        name: 'فاطمة أحمد حسن',\n        phone: '01098765432',\n        email: '<EMAIL>',\n        address: 'شارع الهرم، الجيزة',\n        customerTypeId: 2,\n        customerTypeName: 'عميل VIP',\n        governorateId: 2,\n        governorateName: 'الجيزة',\n        balance: 15000,\n        isActive: true,\n        createdAt: new Date('2024-02-10')\n      }\n      // Add more mock data as needed\n      ];\n    }\n    /**\n     * Get mock customer types\n     */\n    getMockCustomerTypes() {\n      return [{\n        id: 1,\n        name: 'عميل عادي'\n      }, {\n        id: 2,\n        name: 'عميل VIP'\n      }, {\n        id: 3,\n        name: 'عميل جملة'\n      }, {\n        id: 4,\n        name: 'عميل تجزئة'\n      }];\n    }\n    /**\n     * Get mock governorates\n     */\n    getMockGovernorates() {\n      return [{\n        id: 1,\n        name: 'القاهرة'\n      }, {\n        id: 2,\n        name: 'الجيزة'\n      }, {\n        id: 3,\n        name: 'الإسكندرية'\n      }, {\n        id: 4,\n        name: 'الشرقية'\n      }, {\n        id: 5,\n        name: 'البحيرة'\n      }];\n    }\n    static ɵfac = function CustomersNewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomersNewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomersNewComponent,\n      selectors: [[\"app-customers-new\"]],\n      decls: 144,\n      vars: 20,\n      consts: [[1, \"customers-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"export-btn\", 3, \"click\"], [1, \"filters-section\"], [1, \"filters-card\"], [1, \"filters-grid\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0627\\u0644\\u0627\\u0633\\u0645\\u060C \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\\u060C \\u0623\\u0648 \\u0627\\u0644\\u0631\\u0642\\u0645...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"active\"], [\"value\", \"inactive\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"stats-section\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-customers\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-card\", \"active-customers\"], [1, \"stat-card\", \"new-customers\"], [1, \"stat-card\", \"vip-customers\"], [1, \"table-section\"], [1, \"table-card\"], [1, \"table-header\"], [1, \"table-title\"], [1, \"results-count\"], [1, \"table-actions\"], [\"appearance\", \"outline\", 1, \"page-size-field\"], [\"value\", \"10\"], [\"value\", \"25\"], [\"value\", \"50\"], [\"value\", \"100\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"customers-table\", 3, \"dataSource\"], [\"matColumnDef\", \"code\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"name\"], [\"matColumnDef\", \"contact\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"address\"], [\"matColumnDef\", \"balance\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"table-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [3, \"value\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"customer-code\"], [1, \"customer-info\"], [1, \"customer-name\"], [1, \"customer-type\"], [\"mat-header-cell\", \"\"], [1, \"contact-info\"], [\"class\", \"phone\", 4, \"ngIf\"], [\"class\", \"email\", 4, \"ngIf\"], [1, \"phone\"], [1, \"email\"], [1, \"address-info\"], [1, \"governorate\"], [\"class\", \"address\", 4, \"ngIf\"], [1, \"address\"], [1, \"balance\"], [1, \"status-badge\"], [1, \"actions-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"table-row\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function CustomersNewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0644\\u062C\\u0645\\u064A\\u0639 \\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0645\\u062A\\u062C\\u0631\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function CustomersNewComponent_Template_button_click_9_listener() {\n            return ctx.openAddCustomer();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function CustomersNewComponent_Template_button_click_14_listener() {\n            return ctx.exportCustomers();\n          });\n          i0.ɵɵelementStart(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"file_download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"\\u062A\\u0635\\u062F\\u064A\\u0631\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"mat-card\", 10)(21, \"mat-card-content\")(22, \"div\", 11)(23, \"mat-form-field\", 12)(24, \"mat-label\");\n          i0.ɵɵtext(25, \"\\u0627\\u0644\\u0628\\u062D\\u062B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersNewComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function CustomersNewComponent_Template_input_input_26_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-icon\", 14);\n          i0.ɵɵtext(28, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"mat-form-field\", 15)(30, \"mat-label\");\n          i0.ɵɵtext(31, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersNewComponent_Template_mat_select_ngModelChange_32_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCustomerType, $event) || (ctx.selectedCustomerType = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersNewComponent_Template_mat_select_selectionChange_32_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(33, \"mat-option\", 17);\n          i0.ɵɵtext(34, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, CustomersNewComponent_mat_option_35_Template, 2, 2, \"mat-option\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"mat-form-field\", 15)(37, \"mat-label\");\n          i0.ɵɵtext(38, \"\\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersNewComponent_Template_mat_select_ngModelChange_39_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedGovernorate, $event) || (ctx.selectedGovernorate = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersNewComponent_Template_mat_select_selectionChange_39_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(40, \"mat-option\", 17);\n          i0.ɵɵtext(41, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, CustomersNewComponent_mat_option_42_Template, 2, 2, \"mat-option\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 15)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersNewComponent_Template_mat_select_ngModelChange_46_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersNewComponent_Template_mat_select_selectionChange_46_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(47, \"mat-option\", 17);\n          i0.ɵɵtext(48, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0627\\u0644\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"mat-option\", 19);\n          i0.ɵɵtext(50, \"\\u0646\\u0634\\u0637\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-option\", 20);\n          i0.ɵɵtext(52, \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function CustomersNewComponent_Template_button_click_53_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\");\n          i0.ɵɵtext(57, \"\\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0644\\u0627\\u062A\\u0631\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(58, \"div\", 22)(59, \"div\", 23)(60, \"div\", 24)(61, \"div\", 25)(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"people\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 26)(65, \"div\", 27);\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 28);\n          i0.ɵɵtext(68, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"div\", 29)(70, \"div\", 25)(71, \"mat-icon\");\n          i0.ɵɵtext(72, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 26)(74, \"div\", 27);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 28);\n          i0.ɵɵtext(77, \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"div\", 30)(79, \"div\", 25)(80, \"mat-icon\");\n          i0.ɵɵtext(81, \"person_add\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 26)(83, \"div\", 27);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"div\", 28);\n          i0.ɵɵtext(86, \"\\u0639\\u0645\\u0644\\u0627\\u0621 \\u062C\\u062F\\u062F \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0634\\u0647\\u0631\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"div\", 31)(88, \"div\", 25)(89, \"mat-icon\");\n          i0.ɵɵtext(90, \"star\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 26)(92, \"div\", 27);\n          i0.ɵɵtext(93);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"div\", 28);\n          i0.ɵɵtext(95, \"\\u0639\\u0645\\u0644\\u0627\\u0621 VIP\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(96, \"div\", 32)(97, \"mat-card\", 33)(98, \"div\", 34)(99, \"div\", 35)(100, \"h3\");\n          i0.ɵɵtext(101, \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"span\", 36);\n          i0.ɵɵtext(103);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"div\", 37)(105, \"mat-form-field\", 38)(106, \"mat-label\");\n          i0.ɵɵtext(107, \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"mat-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersNewComponent_Template_mat_select_ngModelChange_108_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersNewComponent_Template_mat_select_selectionChange_108_listener() {\n            return ctx.onPageSizeChange();\n          });\n          i0.ɵɵelementStart(109, \"mat-option\", 39);\n          i0.ɵɵtext(110, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"mat-option\", 40);\n          i0.ɵɵtext(112, \"25\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"mat-option\", 41);\n          i0.ɵɵtext(114, \"50\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"mat-option\", 42);\n          i0.ɵɵtext(116, \"100\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(117, \"div\", 43)(118, \"table\", 44);\n          i0.ɵɵelementContainerStart(119, 45);\n          i0.ɵɵtemplate(120, CustomersNewComponent_th_120_Template, 2, 0, \"th\", 46)(121, CustomersNewComponent_td_121_Template, 3, 1, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(122, 48);\n          i0.ɵɵtemplate(123, CustomersNewComponent_th_123_Template, 2, 0, \"th\", 46)(124, CustomersNewComponent_td_124_Template, 6, 2, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(125, 49);\n          i0.ɵɵtemplate(126, CustomersNewComponent_th_126_Template, 2, 0, \"th\", 50)(127, CustomersNewComponent_td_127_Template, 4, 2, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(128, 51);\n          i0.ɵɵtemplate(129, CustomersNewComponent_th_129_Template, 2, 0, \"th\", 50)(130, CustomersNewComponent_td_130_Template, 5, 2, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(131, 52);\n          i0.ɵɵtemplate(132, CustomersNewComponent_th_132_Template, 2, 0, \"th\", 46)(133, CustomersNewComponent_td_133_Template, 4, 6, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(134, 53);\n          i0.ɵɵtemplate(135, CustomersNewComponent_th_135_Template, 2, 0, \"th\", 50)(136, CustomersNewComponent_td_136_Template, 3, 3, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(137, 54);\n          i0.ɵɵtemplate(138, CustomersNewComponent_th_138_Template, 2, 0, \"th\", 50)(139, CustomersNewComponent_td_139_Template, 11, 0, \"td\", 47);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(140, CustomersNewComponent_tr_140_Template, 1, 0, \"tr\", 55)(141, CustomersNewComponent_tr_141_Template, 1, 0, \"tr\", 56);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(142, \"mat-paginator\", 57);\n          i0.ɵɵlistener(\"page\", function CustomersNewComponent_Template_mat_paginator_page_142_listener($event) {\n            return ctx.onPageChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(143, CustomersNewComponent_div_143_Template, 4, 0, \"div\", 58);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCustomerType);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customerTypes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedGovernorate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.governorates);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedStatus);\n          i0.ɵɵadvance(20);\n          i0.ɵɵtextInterpolate(ctx.statistics.totalCustomers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.activeCustomers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.newCustomersThisMonth);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.vipCustomers);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"(\", ctx.filteredCustomers.length, \" \\u0639\\u0645\\u064A\\u0644)\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.pageSize);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"dataSource\", ctx.paginatedCustomers);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.filteredCustomers.length)(\"pageSize\", ctx.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(19, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, MatCardModule, i4.MatCard, i4.MatCardContent, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatSuffix, MatInputModule, i8.MatInput, MatSelectModule, i9.MatSelect, i9.MatOption, MatTableModule, i10.MatTable, i10.MatHeaderCellDef, i10.MatHeaderRowDef, i10.MatColumnDef, i10.MatCellDef, i10.MatRowDef, i10.MatHeaderCell, i10.MatCell, i10.MatHeaderRow, i10.MatRow, MatPaginatorModule, i11.MatPaginator, MatSortModule, i12.MatSort, i12.MatSortHeader, MatProgressSpinnerModule, i13.MatProgressSpinner, MatTooltipModule, i14.MatTooltip],\n      styles: [\"\\n\\n.customers-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  align-items: center;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%] {\\n  background: var(--success-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--success-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-color: white !important;\\n}\\n\\n\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.filters-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) !important;\\n}\\n\\n.filters-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr auto;\\n  gap: var(--spacing-xl);\\n  align-items: end;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  padding: 0 var(--spacing-lg) !important;\\n  color: var(--gray-600) !important;\\n  border-color: var(--gray-300) !important;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--gray-50) !important;\\n  color: var(--gray-800) !important;\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  padding: var(--spacing-xl);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--gray-200);\\n  position: relative;\\n  overflow: hidden;\\n  transition: all var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n  min-height: 100px;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--shadow-2xl);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: var(--radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 800;\\n  color: var(--gray-900);\\n  line-height: 1;\\n  margin-bottom: var(--spacing-xs);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n.stat-card.total-customers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.stat-card.active-customers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.stat-card.new-customers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.stat-card.vip-customers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));\\n}\\n\\n\\n\\n.table-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.table-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  overflow: hidden !important;\\n}\\n.table-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-xl) var(--spacing-2xl);\\n  border-bottom: 1px solid var(--gray-200);\\n  background: var(--gray-50);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%]   .results-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n  background: var(--gray-200);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-md);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .page-size-field[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-height: 600px;\\n}\\n\\n.customers-table[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  background: white !important;\\n}\\n.customers-table[_ngcontent-%COMP%]   .mat-mdc-header-row[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n}\\n.customers-table[_ngcontent-%COMP%]   .mat-mdc-header-row[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 700 !important;\\n  color: var(--gray-800) !important;\\n  border-bottom: 2px solid var(--gray-200) !important;\\n  padding: var(--spacing-lg) !important;\\n  font-family: var(--font-family-primary) !important;\\n  font-size: 0.875rem !important;\\n}\\n.customers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast) !important;\\n  cursor: pointer !important;\\n}\\n.customers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-50) !important;\\n}\\n.customers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) !important;\\n  border-bottom: 1px solid var(--gray-100) !important;\\n  font-family: var(--font-family-primary) !important;\\n  vertical-align: middle !important;\\n}\\n\\n\\n\\n.customer-code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 600;\\n  color: var(--primary-600);\\n  background: var(--primary-50);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-sm);\\n  font-size: 0.875rem;\\n}\\n\\n.customer-info[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.customer-info[_ngcontent-%COMP%]   .customer-type[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  background: var(--gray-100);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-sm);\\n  display: inline-block;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-xs);\\n}\\n.contact-info[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  font-size: 0.875rem;\\n}\\n.contact-info[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: var(--gray-500);\\n}\\n\\n.address-info[_ngcontent-%COMP%]   .governorate[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-800);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.address-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n}\\n\\n.balance[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 0.875rem;\\n}\\n.balance.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.balance.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.balance.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-500);\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xs) var(--spacing-md);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.status-badge.active[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n}\\n.status-badge.inactive[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n}\\n\\n.actions-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n}\\n.actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px !important;\\n  height: 36px !important;\\n  min-width: 36px !important;\\n}\\n.actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.125rem !important;\\n  width: 1.125rem !important;\\n  height: 1.125rem !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .filters-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr 1fr;\\n  }\\n  .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n    grid-column: span 3;\\n    justify-self: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .filters-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .table-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n    align-items: stretch;\\n  }\\n  .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%] {\\n    align-self: center;\\n  }\\n  .customers-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%], \\n   .customers-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) !important;\\n    font-size: 0.875rem !important;\\n  }\\n  .actions-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-xs);\\n  }\\n  .actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 32px !important;\\n    height: 32px !important;\\n    min-width: 32px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jdXN0b21lcnMvY3VzdG9tZXJzLW5ldy5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uL0VycCUyMDIvZGF0YWJhc2UvVGVycmEuUmV0YWlsLldlYi9zcmMvYXBwL21vZHVsZXMvY3VzdG9tZXJzL2N1c3RvbWVycy1uZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsNERBQUE7QUFFQTtFQUNFLFVBQUE7RUFDQSx1QkFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0FDQUY7O0FER0EsNEJBQUE7QUFDQTtFQUNFLHFGQUFBO0VBQ0EsWUFBQTtFQUNBLGlFQUFBO0VBQ0Esc0ZBQUE7RUFDQSxzREFBQTtFQUNBLDRCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0FDQUY7QURFRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSx3VkFBQTtFQUNBLFlBQUE7QUNBSjtBREdFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7QUNESjtBRElFO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLCtCQUFBO0VBQ0EseUNBQUE7QUNGSjtBREtFO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0FDSEo7QURNRTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FDSko7QURNSTtFQUNFLHlDQUFBO0VBQ0EsdUJBQUE7RUFDQSx1REFBQTtFQUNBLDJCQUFBO0VBQ0EsdUNBQUE7QUNKTjtBRE1NO0VBQ0UseUNBQUE7RUFDQSxzQ0FBQTtFQUNBLHVDQUFBO0FDSlI7QURRSTtFQUNFLGlEQUFBO0VBQ0EsdUJBQUE7RUFDQSx1REFBQTtBQ05OO0FEUU07RUFDRSwrQ0FBQTtFQUNBLDhCQUFBO0FDTlI7O0FEWUEsZ0NBQUE7QUFDQTtFQUNFLGlDQUFBO0FDVEY7O0FEWUE7RUFDRSwwQ0FBQTtFQUNBLHVDQUFBO0VBQ0EsNENBQUE7QUNURjtBRFdFO0VBQ0Usc0NBQUE7QUNUSjs7QURhQTtFQUNFLGFBQUE7RUFDQSwyQ0FBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUNWRjtBRGFJO0VBQ0UscUNBQUE7QUNYTjtBRGVFO0VBQ0UsWUFBQTtFQUNBLHVDQUFBO0VBQ0EsaUNBQUE7RUFDQSx3Q0FBQTtBQ2JKO0FEZUk7RUFDRSxxQ0FBQTtFQUNBLGlDQUFBO0FDYk47O0FEa0JBLG1DQUFBO0FBQ0E7RUFDRSxpQ0FBQTtBQ2ZGOztBRGtCQTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLHNCQUFBO0FDZkY7O0FEa0JBO0VBQ0UsaUJBQUE7RUFDQSwrQkFBQTtFQUNBLDBCQUFBO0VBQ0EsNEJBQUE7RUFDQSxpQ0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3Q0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7QUNmRjtBRGlCRTtFQUNFLDJCQUFBO0VBQ0EsNkJBQUE7QUNmSjtBRGtCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsK0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGNBQUE7QUNoQko7QURrQkk7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0FDaEJOO0FEb0JFO0VBQ0UsT0FBQTtBQ2xCSjtBRG9CSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsY0FBQTtFQUNBLGdDQUFBO0FDbEJOO0FEcUJJO0VBQ0UsbUJBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0FDbkJOO0FEdUJFO0VBQ0UsMkVBQUE7QUNyQko7QUR3QkU7RUFDRSwyRUFBQTtBQ3RCSjtBRHlCRTtFQUNFLDJFQUFBO0FDdkJKO0FEMEJFO0VBQ0UsK0VBQUE7QUN4Qko7O0FENEJBLDhCQUFBO0FBQ0E7RUFDRSxpQ0FBQTtBQ3pCRjs7QUQ0QkE7RUFDRSwwQ0FBQTtFQUNBLHVDQUFBO0VBQ0EsNENBQUE7RUFDQSwyQkFBQTtBQ3pCRjtBRDJCRTtFQUNFLHFCQUFBO0FDekJKOztBRDZCQTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsNkNBQUE7RUFDQSx3Q0FBQTtFQUNBLDBCQUFBO0FDMUJGO0FENEJFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esc0JBQUE7QUMxQko7QUQ0Qkk7RUFDRSxTQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0FDMUJOO0FENkJJO0VBQ0UsbUJBQUE7RUFDQSxzQkFBQTtFQUNBLDJCQUFBO0VBQ0EsNENBQUE7RUFDQSwrQkFBQTtBQzNCTjtBRGdDSTtFQUNFLFlBQUE7QUM5Qk47O0FEbUNBO0VBQ0UsZ0JBQUE7RUFDQSxpQkFBQTtBQ2hDRjs7QURtQ0E7RUFDRSxzQkFBQTtFQUNBLDRCQUFBO0FDaENGO0FEa0NFO0VBQ0UscUNBQUE7QUNoQ0o7QURrQ0k7RUFDRSwyQkFBQTtFQUNBLGlDQUFBO0VBQ0EsbURBQUE7RUFDQSxxQ0FBQTtFQUNBLGtEQUFBO0VBQ0EsOEJBQUE7QUNoQ047QURvQ0U7RUFDRSxpREFBQTtFQUNBLDBCQUFBO0FDbENKO0FEb0NJO0VBQ0Usd0NBQUE7QUNsQ047QURxQ0k7RUFDRSxxQ0FBQTtFQUNBLG1EQUFBO0VBQ0Esa0RBQUE7RUFDQSxpQ0FBQTtBQ25DTjs7QUR3Q0Esa0NBQUE7QUFDQTtFQUNFLHFDQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0EsNENBQUE7RUFDQSwrQkFBQTtFQUNBLG1CQUFBO0FDckNGOztBRHlDRTtFQUNFLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQ0FBQTtBQ3RDSjtBRHlDRTtFQUNFLGtCQUFBO0VBQ0Esc0JBQUE7RUFDQSwyQkFBQTtFQUNBLDRDQUFBO0VBQ0EsK0JBQUE7RUFDQSxxQkFBQTtBQ3ZDSjs7QUQyQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxzQkFBQTtBQ3hDRjtBRDBDRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUN4Q0o7QUQwQ0k7RUFDRSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxzQkFBQTtBQ3hDTjs7QUQ4Q0U7RUFDRSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0NBQUE7QUMzQ0o7QUQ4Q0U7RUFDRSxtQkFBQTtFQUNBLHNCQUFBO0FDNUNKOztBRGdEQTtFQUNFLGdCQUFBO0VBQ0EsbUJBQUE7QUM3Q0Y7QUQrQ0U7RUFDRSx5QkFBQTtBQzdDSjtBRGdERTtFQUNFLHVCQUFBO0FDOUNKO0FEaURFO0VBQ0Usc0JBQUE7QUMvQ0o7O0FEbURBO0VBQ0UsNENBQUE7RUFDQSwrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQ2hERjtBRGtERTtFQUNFLDhCQUFBO0VBQ0EseUJBQUE7QUNoREo7QURtREU7RUFDRSw0QkFBQTtFQUNBLHVCQUFBO0FDakRKOztBRHFEQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtBQ2xERjtBRG9ERTtFQUNFLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSwwQkFBQTtBQ2xESjtBRG9ESTtFQUNFLDhCQUFBO0VBQ0EsMEJBQUE7RUFDQSwyQkFBQTtBQ2xETjs7QUR1REEsZ0NBQUE7QUFDQTtFQUNFLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0Esb0NBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtBQ3BERjtBRHNERTtFQUNFLDZCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQ3BESjs7QUR3REEsa0NBQUE7QUFDQTtFQUNFO0lBQ0Usa0NBQUE7RUNyREY7RUR1REU7SUFDRSxtQkFBQTtJQUNBLG9CQUFBO0VDckRKO0FBQ0Y7QUR5REE7RUFDRTtJQUNFLDBCQUFBO0lBQ0EscUZBQUE7RUN2REY7RUR5REU7SUFDRSxzQkFBQTtJQUNBLHNCQUFBO0lBQ0Esa0JBQUE7RUN2REo7RUQwREU7SUFDRSxlQUFBO0VDeERKO0VEMkRFO0lBQ0UsV0FBQTtJQUNBLHVCQUFBO0VDekRKO0VENkRBO0lBQ0UsMEJBQUE7SUFDQSxzQkFBQTtFQzNERjtFRDZERTtJQUNFLG1CQUFBO0VDM0RKO0VEK0RBO0lBQ0UsMEJBQUE7SUFDQSxzQkFBQTtFQzdERjtFRGdFQTtJQUNFLHNCQUFBO0lBQ0Esc0JBQUE7SUFDQSxvQkFBQTtFQzlERjtFRGdFRTtJQUNFLGtCQUFBO0VDOURKO0VEbUVFOztJQUVFLHFDQUFBO0lBQ0EsOEJBQUE7RUNqRUo7RURxRUE7SUFDRSxzQkFBQTtJQUNBLHNCQUFBO0VDbkVGO0VEcUVFO0lBQ0Usc0JBQUE7SUFDQSx1QkFBQTtJQUNBLDBCQUFBO0VDbkVKO0FBQ0Y7QURDQSxnbGxDQUFnbGxDIiwic291cmNlc0NvbnRlbnQiOlsiLyogVGVycmEgUmV0YWlsIEVSUCAtIFByb2Zlc3Npb25hbCBDdXN0b21lcnMgTW9kdWxlIFN0eWxlcyAqL1xuXG4uY3VzdG9tZXJzLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDA7XG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgd2lkdGg6IDEwMCU7XG4gIG1heC13aWR0aDogMTAwJTtcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcbiAgb3ZlcmZsb3cteDogaGlkZGVuO1xufVxuXG4vKiA9PT09PSBQQUdFIEhFQURFUiA9PT09PSAqL1xuLnBhZ2UtaGVhZGVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tcHJpbWFyeS02MDApIDAlLCB2YXIoLS1zZWNvbmRhcnktNjAwKSAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLTJ4bCkgdmFyKC0tc3BhY2luZy0yeGwpIHZhcigtLXNwYWNpbmctM3hsKTtcbiAgbWFyZ2luOiBjYWxjKC0xICogdmFyKC0tc3BhY2luZy0yeGwpKSBjYWxjKC0xICogdmFyKC0tc3BhY2luZy0yeGwpKSB2YXIoLS1zcGFjaW5nLTJ4bCk7XG4gIGJvcmRlci1yYWRpdXM6IDAgMCB2YXIoLS1yYWRpdXMtMnhsKSB2YXIoLS1yYWRpdXMtMnhsKTtcbiAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LXhsKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xuXG4gICY6OmJlZm9yZSB7XG4gICAgY29udGVudDogJyc7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogMDtcbiAgICBsZWZ0OiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGJvdHRvbTogMDtcbiAgICBiYWNrZ3JvdW5kOiB1cmwoJ2RhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDEwMCAxMDBcIj48ZGVmcz48cGF0dGVybiBpZD1cImdyaWRcIiB3aWR0aD1cIjEwXCIgaGVpZ2h0PVwiMTBcIiBwYXR0ZXJuVW5pdHM9XCJ1c2VyU3BhY2VPblVzZVwiPjxwYXRoIGQ9XCJNIDEwIDAgTCAwIDAgMCAxMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwicmdiYSgyNTUsMjU1LDI1NSwwLjEpXCIgc3Ryb2tlLXdpZHRoPVwiMC41XCIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3Qgd2lkdGg9XCIxMDBcIiBoZWlnaHQ9XCIxMDBcIiBmaWxsPVwidXJsKCUyM2dyaWQpXCIvPjwvc3ZnPicpO1xuICAgIG9wYWNpdHk6IDAuMztcbiAgfVxuXG4gIC5oZWFkZXItY29udGVudCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgei1pbmRleDogMTtcbiAgfVxuXG4gIC5wYWdlLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDIuNXJlbTtcbiAgICBmb250LXdlaWdodDogODAwO1xuICAgIG1hcmdpbjogMCAwIHZhcigtLXNwYWNpbmctc20pIDA7XG4gICAgdGV4dC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIH1cblxuICAucGFnZS1zdWJ0aXRsZSB7XG4gICAgZm9udC1zaXplOiAxLjEyNXJlbTtcbiAgICBvcGFjaXR5OiAwLjk7XG4gICAgbWFyZ2luOiAwO1xuICAgIGZvbnQtd2VpZ2h0OiA0MDA7XG4gIH1cblxuICAuaGVhZGVyLWFjdGlvbnMge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLW1kKTtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuXG4gICAgLmFkZC1idG4ge1xuICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VjY2Vzcy01MDApICFpbXBvcnRhbnQ7XG4gICAgICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbWQpIHZhcigtLXNwYWNpbmcteGwpICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7XG4gICAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctbGcpICFpbXBvcnRhbnQ7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdWNjZXNzLTYwMCkgIWltcG9ydGFudDtcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpICFpbXBvcnRhbnQ7XG4gICAgICAgIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy14bCkgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAuZXhwb3J0LWJ0biB7XG4gICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSAhaW1wb3J0YW50O1xuICAgICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLW1kKSB2YXIoLS1zcGFjaW5nLXhsKSAhaW1wb3J0YW50O1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7XG4gICAgICAgIGJvcmRlci1jb2xvcjogd2hpdGUgIWltcG9ydGFudDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyogPT09PT0gRklMVEVSUyBTRUNUSU9OID09PT09ICovXG4uZmlsdGVycy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2luZy0yeGwpO1xufVxuXG4uZmlsdGVycy1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLXhsKSAhaW1wb3J0YW50O1xuICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctbGcpICFpbXBvcnRhbnQ7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWdyYXktMjAwKSAhaW1wb3J0YW50O1xuXG4gIC5tYXQtbWRjLWNhcmQtY29udGVudCB7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2luZy0yeGwpICFpbXBvcnRhbnQ7XG4gIH1cbn1cblxuLmZpbHRlcnMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMmZyIDFmciAxZnIgMWZyIGF1dG87XG4gIGdhcDogdmFyKC0tc3BhY2luZy14bCk7XG4gIGFsaWduLWl0ZW1zOiBlbmQ7XG5cbiAgLnNlYXJjaC1maWVsZCB7XG4gICAgLm1hdC1tZGMtdGV4dC1maWVsZC13cmFwcGVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLWdyYXktNTApICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG5cbiAgLmNsZWFyLWZpbHRlcnMtYnRuIHtcbiAgICBoZWlnaHQ6IDU2cHg7XG4gICAgcGFkZGluZzogMCB2YXIoLS1zcGFjaW5nLWxnKSAhaW1wb3J0YW50O1xuICAgIGNvbG9yOiB2YXIoLS1ncmF5LTYwMCkgIWltcG9ydGFudDtcbiAgICBib3JkZXItY29sb3I6IHZhcigtLWdyYXktMzAwKSAhaW1wb3J0YW50O1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1ncmF5LTUwKSAhaW1wb3J0YW50O1xuICAgICAgY29sb3I6IHZhcigtLWdyYXktODAwKSAhaW1wb3J0YW50O1xuICAgIH1cbiAgfVxufVxuXG4vKiA9PT09PSBTVEFUSVNUSUNTIFNFQ1RJT04gPT09PT0gKi9cbi5zdGF0cy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2luZy0yeGwpO1xufVxuXG4uc3RhdHMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpO1xuICBnYXA6IHZhcigtLXNwYWNpbmcteGwpO1xufVxuXG4uc3RhdC1jYXJkIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy14bCk7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteGwpO1xuICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3ctbGcpO1xuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ncmF5LTIwMCk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgdHJhbnNpdGlvbjogYWxsIHZhcigtLXRyYW5zaXRpb24tbm9ybWFsKTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiB2YXIoLS1zcGFjaW5nLWxnKTtcbiAgbWluLWhlaWdodDogMTAwcHg7XG5cbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICAgIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy0yeGwpO1xuICB9XG5cbiAgLnN0YXQtaWNvbiB7XG4gICAgd2lkdGg6IDYwcHg7XG4gICAgaGVpZ2h0OiA2MHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy14bCk7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGZsZXgtc2hyaW5rOiAwO1xuXG4gICAgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgd2lkdGg6IDJyZW07XG4gICAgICBoZWlnaHQ6IDJyZW07XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuICB9XG5cbiAgLnN0YXQtY29udGVudCB7XG4gICAgZmxleDogMTtcblxuICAgIC5zdGF0LXZhbHVlIHtcbiAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA4MDA7XG4gICAgICBjb2xvcjogdmFyKC0tZ3JheS05MDApO1xuICAgICAgbGluZS1oZWlnaHQ6IDE7XG4gICAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLXhzKTtcbiAgICB9XG5cbiAgICAuc3RhdC1sYWJlbCB7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgICAgY29sb3I6IHZhcigtLWdyYXktNjAwKTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuICB9XG5cbiAgJi50b3RhbC1jdXN0b21lcnMgLnN0YXQtaWNvbiB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tcHJpbWFyeS01MDApLCB2YXIoLS1wcmltYXJ5LTYwMCkpO1xuICB9XG5cbiAgJi5hY3RpdmUtY3VzdG9tZXJzIC5zdGF0LWljb24ge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXN1Y2Nlc3MtNTAwKSwgdmFyKC0tc3VjY2Vzcy02MDApKTtcbiAgfVxuXG4gICYubmV3LWN1c3RvbWVycyAuc3RhdC1pY29uIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS13YXJuaW5nLTUwMCksIHZhcigtLXdhcm5pbmctNjAwKSk7XG4gIH1cblxuICAmLnZpcC1jdXN0b21lcnMgLnN0YXQtaWNvbiB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tc2Vjb25kYXJ5LTUwMCksIHZhcigtLXNlY29uZGFyeS02MDApKTtcbiAgfVxufVxuXG4vKiA9PT09PSBUQUJMRSBTRUNUSU9OID09PT09ICovXG4udGFibGUtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmctMnhsKTtcbn1cblxuLnRhYmxlLWNhcmQge1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMteGwpICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1sZykgIWltcG9ydGFudDtcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tZ3JheS0yMDApICFpbXBvcnRhbnQ7XG4gIG92ZXJmbG93OiBoaWRkZW4gIWltcG9ydGFudDtcblxuICAubWF0LW1kYy1jYXJkLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDAgIWltcG9ydGFudDtcbiAgfVxufVxuXG4udGFibGUtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhsKSB2YXIoLS1zcGFjaW5nLTJ4bCk7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1ncmF5LTIwMCk7XG4gIGJhY2tncm91bmQ6IHZhcigtLWdyYXktNTApO1xuXG4gIC50YWJsZS10aXRsZSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogdmFyKC0tc3BhY2luZy1tZCk7XG5cbiAgICBoMyB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XG4gICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgY29sb3I6IHZhcigtLWdyYXktOTAwKTtcbiAgICB9XG5cbiAgICAucmVzdWx0cy1jb3VudCB7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgICAgY29sb3I6IHZhcigtLWdyYXktNTAwKTtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLWdyYXktMjAwKTtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteHMpIHZhcigtLXNwYWNpbmctc20pO1xuICAgICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLW1kKTtcbiAgICB9XG4gIH1cblxuICAudGFibGUtYWN0aW9ucyB7XG4gICAgLnBhZ2Utc2l6ZS1maWVsZCB7XG4gICAgICB3aWR0aDogMTIwcHg7XG4gICAgfVxuICB9XG59XG5cbi50YWJsZS1jb250YWluZXIge1xuICBvdmVyZmxvdy14OiBhdXRvO1xuICBtYXgtaGVpZ2h0OiA2MDBweDtcbn1cblxuLmN1c3RvbWVycy10YWJsZSB7XG4gIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7XG4gIGJhY2tncm91bmQ6IHdoaXRlICFpbXBvcnRhbnQ7XG5cbiAgLm1hdC1tZGMtaGVhZGVyLXJvdyB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tZ3JheS01MCkgIWltcG9ydGFudDtcblxuICAgIC5tYXQtbWRjLWhlYWRlci1jZWxsIHtcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDAgIWltcG9ydGFudDtcbiAgICAgIGNvbG9yOiB2YXIoLS1ncmF5LTgwMCkgIWltcG9ydGFudDtcbiAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCB2YXIoLS1ncmF5LTIwMCkgIWltcG9ydGFudDtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbGcpICFpbXBvcnRhbnQ7XG4gICAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1mYW1pbHktcHJpbWFyeSkgIWltcG9ydGFudDtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW0gIWltcG9ydGFudDtcbiAgICB9XG4gIH1cblxuICAubWF0LW1kYy1yb3cge1xuICAgIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpICFpbXBvcnRhbnQ7XG4gICAgY3Vyc29yOiBwb2ludGVyICFpbXBvcnRhbnQ7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktNTApICFpbXBvcnRhbnQ7XG4gICAgfVxuXG4gICAgLm1hdC1tZGMtY2VsbCB7XG4gICAgICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLWxnKSAhaW1wb3J0YW50O1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWdyYXktMTAwKSAhaW1wb3J0YW50O1xuICAgICAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtZmFtaWx5LXByaW1hcnkpICFpbXBvcnRhbnQ7XG4gICAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG59XG5cbi8qID09PT09IFRBQkxFIENFTEwgU1RZTEVTID09PT09ICovXG4uY3VzdG9tZXItY29kZSB7XG4gIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5LTYwMCk7XG4gIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktNTApO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhzKSB2YXIoLS1zcGFjaW5nLXNtKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLXNtKTtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbn1cblxuLmN1c3RvbWVyLWluZm8ge1xuICAuY3VzdG9tZXItbmFtZSB7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogdmFyKC0tZ3JheS05MDApO1xuICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmcteHMpO1xuICB9XG5cbiAgLmN1c3RvbWVyLXR5cGUge1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICBjb2xvcjogdmFyKC0tZ3JheS01MDApO1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWdyYXktMTAwKTtcbiAgICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhzKSB2YXIoLS1zcGFjaW5nLXNtKTtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtc20pO1xuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgfVxufVxuXG4uY29udGFjdC1pbmZvIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiB2YXIoLS1zcGFjaW5nLXhzKTtcblxuICAucGhvbmUsIC5lbWFpbCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogdmFyKC0tc3BhY2luZy14cyk7XG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcblxuICAgIG1hdC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgIHdpZHRoOiAxcmVtO1xuICAgICAgaGVpZ2h0OiAxcmVtO1xuICAgICAgY29sb3I6IHZhcigtLWdyYXktNTAwKTtcbiAgICB9XG4gIH1cbn1cblxuLmFkZHJlc3MtaW5mbyB7XG4gIC5nb3Zlcm5vcmF0ZSB7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogdmFyKC0tZ3JheS04MDApO1xuICAgIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmcteHMpO1xuICB9XG5cbiAgLmFkZHJlc3Mge1xuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgY29sb3I6IHZhcigtLWdyYXktNjAwKTtcbiAgfVxufVxuXG4uYmFsYW5jZSB7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG5cbiAgJi5wb3NpdGl2ZSB7XG4gICAgY29sb3I6IHZhcigtLXN1Y2Nlc3MtNjAwKTtcbiAgfVxuXG4gICYubmVnYXRpdmUge1xuICAgIGNvbG9yOiB2YXIoLS1lcnJvci02MDApO1xuICB9XG5cbiAgJi56ZXJvIHtcbiAgICBjb2xvcjogdmFyKC0tZ3JheS01MDApO1xuICB9XG59XG5cbi5zdGF0dXMtYmFkZ2Uge1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhzKSB2YXIoLS1zcGFjaW5nLW1kKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLW1kKTtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuXG4gICYuYWN0aXZlIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdWNjZXNzLTEwMCk7XG4gICAgY29sb3I6IHZhcigtLXN1Y2Nlc3MtNzAwKTtcbiAgfVxuXG4gICYuaW5hY3RpdmUge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWVycm9yLTEwMCk7XG4gICAgY29sb3I6IHZhcigtLWVycm9yLTcwMCk7XG4gIH1cbn1cblxuLmFjdGlvbnMtYnV0dG9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogdmFyKC0tc3BhY2luZy14cyk7XG5cbiAgYnV0dG9uIHtcbiAgICB3aWR0aDogMzZweCAhaW1wb3J0YW50O1xuICAgIGhlaWdodDogMzZweCAhaW1wb3J0YW50O1xuICAgIG1pbi13aWR0aDogMzZweCAhaW1wb3J0YW50O1xuXG4gICAgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAxLjEyNXJlbSAhaW1wb3J0YW50O1xuICAgICAgd2lkdGg6IDEuMTI1cmVtICFpbXBvcnRhbnQ7XG4gICAgICBoZWlnaHQ6IDEuMTI1cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG59XG5cbi8qID09PT09IExPQURJTkcgT1ZFUkxBWSA9PT09PSAqL1xuLmxvYWRpbmctb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBmaXhlZDtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICB6LWluZGV4OiA5OTk5O1xuXG4gIHAge1xuICAgIG1hcmdpbi10b3A6IHZhcigtLXNwYWNpbmctbGcpO1xuICAgIGNvbG9yOiB2YXIoLS1ncmF5LTYwMCk7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgfVxufVxuXG4vKiA9PT09PSBSRVNQT05TSVZFIERFU0lHTiA9PT09PSAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkge1xuICAuZmlsdGVycy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyO1xuICAgIFxuICAgIC5jbGVhci1maWx0ZXJzLWJ0biB7XG4gICAgICBncmlkLWNvbHVtbjogc3BhbiAzO1xuICAgICAganVzdGlmeS1zZWxmOiBjZW50ZXI7XG4gICAgfVxuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAucGFnZS1oZWFkZXIge1xuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteGwpO1xuICAgIG1hcmdpbjogY2FsYygtMSAqIHZhcigtLXNwYWNpbmctMnhsKSkgY2FsYygtMSAqIHZhcigtLXNwYWNpbmctMnhsKSkgdmFyKC0tc3BhY2luZy14bCk7XG5cbiAgICAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIGdhcDogdmFyKC0tc3BhY2luZy1sZyk7XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgfVxuXG4gICAgLnBhZ2UtdGl0bGUge1xuICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgIH1cblxuICAgIC5oZWFkZXItYWN0aW9ucyB7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIH1cbiAgfVxuXG4gIC5maWx0ZXJzLWdyaWQge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIGdhcDogdmFyKC0tc3BhY2luZy1sZyk7XG5cbiAgICAuY2xlYXItZmlsdGVycy1idG4ge1xuICAgICAgZ3JpZC1jb2x1bW46IHNwYW4gMTtcbiAgICB9XG4gIH1cblxuICAuc3RhdHMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLWxnKTtcbiAgfVxuXG4gIC50YWJsZS1oZWFkZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLW1kKTtcbiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcblxuICAgIC50YWJsZS1hY3Rpb25zIHtcbiAgICAgIGFsaWduLXNlbGY6IGNlbnRlcjtcbiAgICB9XG4gIH1cblxuICAuY3VzdG9tZXJzLXRhYmxlIHtcbiAgICAubWF0LW1kYy1oZWFkZXItY2VsbCxcbiAgICAubWF0LW1kYy1jZWxsIHtcbiAgICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbWQpICFpbXBvcnRhbnQ7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG5cbiAgLmFjdGlvbnMtYnV0dG9ucyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IHZhcigtLXNwYWNpbmcteHMpO1xuXG4gICAgYnV0dG9uIHtcbiAgICAgIHdpZHRoOiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICBoZWlnaHQ6IDMycHggIWltcG9ydGFudDtcbiAgICAgIG1pbi13aWR0aDogMzJweCAhaW1wb3J0YW50O1xuICAgIH1cbiAgfVxufVxuIiwiLyogVGVycmEgUmV0YWlsIEVSUCAtIFByb2Zlc3Npb25hbCBDdXN0b21lcnMgTW9kdWxlIFN0eWxlcyAqL1xuLmN1c3RvbWVycy1jb250YWluZXIge1xuICBwYWRkaW5nOiAwO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIHdpZHRoOiAxMDAlO1xuICBtYXgtd2lkdGg6IDEwMCU7XG4gIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcbn1cblxuLyogPT09PT0gUEFHRSBIRUFERVIgPT09PT0gKi9cbi5wYWdlLWhlYWRlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXByaW1hcnktNjAwKSAwJSwgdmFyKC0tc2Vjb25kYXJ5LTYwMCkgMTAwJSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2luZy0yeGwpIHZhcigtLXNwYWNpbmctMnhsKSB2YXIoLS1zcGFjaW5nLTN4bCk7XG4gIG1hcmdpbjogY2FsYygtMSAqIHZhcigtLXNwYWNpbmctMnhsKSkgY2FsYygtMSAqIHZhcigtLXNwYWNpbmctMnhsKSkgdmFyKC0tc3BhY2luZy0yeGwpO1xuICBib3JkZXItcmFkaXVzOiAwIDAgdmFyKC0tcmFkaXVzLTJ4bCkgdmFyKC0tcmFkaXVzLTJ4bCk7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy14bCk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcbn1cbi5wYWdlLWhlYWRlcjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIHZpZXdCb3g9XCIwIDAgMTAwIDEwMFwiPjxkZWZzPjxwYXR0ZXJuIGlkPVwiZ3JpZFwiIHdpZHRoPVwiMTBcIiBoZWlnaHQ9XCIxMFwiIHBhdHRlcm5Vbml0cz1cInVzZXJTcGFjZU9uVXNlXCI+PHBhdGggZD1cIk0gMTAgMCBMIDAgMCAwIDEwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJyZ2JhKDI1NSwyNTUsMjU1LDAuMSlcIiBzdHJva2Utd2lkdGg9XCIwLjVcIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD1cIjEwMFwiIGhlaWdodD1cIjEwMFwiIGZpbGw9XCJ1cmwoJTIzZ3JpZClcIi8+PC9zdmc+Jyk7XG4gIG9wYWNpdHk6IDAuMztcbn1cbi5wYWdlLWhlYWRlciAuaGVhZGVyLWNvbnRlbnQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbn1cbi5wYWdlLWhlYWRlciAucGFnZS10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMi41cmVtO1xuICBmb250LXdlaWdodDogODAwO1xuICBtYXJnaW46IDAgMCB2YXIoLS1zcGFjaW5nLXNtKSAwO1xuICB0ZXh0LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4yKTtcbn1cbi5wYWdlLWhlYWRlciAucGFnZS1zdWJ0aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4xMjVyZW07XG4gIG9wYWNpdHk6IDAuOTtcbiAgbWFyZ2luOiAwO1xuICBmb250LXdlaWdodDogNDAwO1xufVxuLnBhZ2UtaGVhZGVyIC5oZWFkZXItYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogdmFyKC0tc3BhY2luZy1tZCk7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG4ucGFnZS1oZWFkZXIgLmhlYWRlci1hY3Rpb25zIC5hZGQtYnRuIHtcbiAgYmFja2dyb3VuZDogdmFyKC0tc3VjY2Vzcy01MDApICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLW1kKSB2YXIoLS1zcGFjaW5nLXhsKSAhaW1wb3J0YW50O1xuICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1sZykgIWltcG9ydGFudDtcbn1cbi5wYWdlLWhlYWRlciAuaGVhZGVyLWFjdGlvbnMgLmFkZC1idG46aG92ZXIge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1zdWNjZXNzLTYwMCkgIWltcG9ydGFudDtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy14bCkgIWltcG9ydGFudDtcbn1cbi5wYWdlLWhlYWRlciAuaGVhZGVyLWFjdGlvbnMgLmV4cG9ydC1idG4ge1xuICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSAhaW1wb3J0YW50O1xuICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2luZy1tZCkgdmFyKC0tc3BhY2luZy14bCkgIWltcG9ydGFudDtcbn1cbi5wYWdlLWhlYWRlciAuaGVhZGVyLWFjdGlvbnMgLmV4cG9ydC1idG46aG92ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDtcbiAgYm9yZGVyLWNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xufVxuXG4vKiA9PT09PSBGSUxURVJTIFNFQ1RJT04gPT09PT0gKi9cbi5maWx0ZXJzLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLTJ4bCk7XG59XG5cbi5maWx0ZXJzLWNhcmQge1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMteGwpICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1sZykgIWltcG9ydGFudDtcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tZ3JheS0yMDApICFpbXBvcnRhbnQ7XG59XG4uZmlsdGVycy1jYXJkIC5tYXQtbWRjLWNhcmQtY29udGVudCB7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctMnhsKSAhaW1wb3J0YW50O1xufVxuXG4uZmlsdGVycy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyZnIgMWZyIDFmciAxZnIgYXV0bztcbiAgZ2FwOiB2YXIoLS1zcGFjaW5nLXhsKTtcbiAgYWxpZ24taXRlbXM6IGVuZDtcbn1cbi5maWx0ZXJzLWdyaWQgLnNlYXJjaC1maWVsZCAubWF0LW1kYy10ZXh0LWZpZWxkLXdyYXBwZXIge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1ncmF5LTUwKSAhaW1wb3J0YW50O1xufVxuLmZpbHRlcnMtZ3JpZCAuY2xlYXItZmlsdGVycy1idG4ge1xuICBoZWlnaHQ6IDU2cHg7XG4gIHBhZGRpbmc6IDAgdmFyKC0tc3BhY2luZy1sZykgIWltcG9ydGFudDtcbiAgY29sb3I6IHZhcigtLWdyYXktNjAwKSAhaW1wb3J0YW50O1xuICBib3JkZXItY29sb3I6IHZhcigtLWdyYXktMzAwKSAhaW1wb3J0YW50O1xufVxuLmZpbHRlcnMtZ3JpZCAuY2xlYXItZmlsdGVycy1idG46aG92ZXIge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1ncmF5LTUwKSAhaW1wb3J0YW50O1xuICBjb2xvcjogdmFyKC0tZ3JheS04MDApICFpbXBvcnRhbnQ7XG59XG5cbi8qID09PT09IFNUQVRJU1RJQ1MgU0VDVElPTiA9PT09PSAqL1xuLnN0YXRzLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLTJ4bCk7XG59XG5cbi5zdGF0cy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XG4gIGdhcDogdmFyKC0tc3BhY2luZy14bCk7XG59XG5cbi5zdGF0LWNhcmQge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLXhsKTtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2luZy14bCk7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1sZyk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWdyYXktMjAwKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IHZhcigtLXNwYWNpbmctbGcpO1xuICBtaW4taGVpZ2h0OiAxMDBweDtcbn1cbi5zdGF0LWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy0yeGwpO1xufVxuLnN0YXQtY2FyZCAuc3RhdC1pY29uIHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLXhsKTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGZsZXgtc2hyaW5rOiAwO1xufVxuLnN0YXQtY2FyZCAuc3RhdC1pY29uIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAycmVtO1xuICB3aWR0aDogMnJlbTtcbiAgaGVpZ2h0OiAycmVtO1xuICBjb2xvcjogd2hpdGU7XG59XG4uc3RhdC1jYXJkIC5zdGF0LWNvbnRlbnQge1xuICBmbGV4OiAxO1xufVxuLnN0YXQtY2FyZCAuc3RhdC1jb250ZW50IC5zdGF0LXZhbHVlIHtcbiAgZm9udC1zaXplOiAycmVtO1xuICBmb250LXdlaWdodDogODAwO1xuICBjb2xvcjogdmFyKC0tZ3JheS05MDApO1xuICBsaW5lLWhlaWdodDogMTtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2luZy14cyk7XG59XG4uc3RhdC1jYXJkIC5zdGF0LWNvbnRlbnQgLnN0YXQtbGFiZWwge1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBjb2xvcjogdmFyKC0tZ3JheS02MDApO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLnN0YXQtY2FyZC50b3RhbC1jdXN0b21lcnMgLnN0YXQtaWNvbiB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXByaW1hcnktNTAwKSwgdmFyKC0tcHJpbWFyeS02MDApKTtcbn1cbi5zdGF0LWNhcmQuYWN0aXZlLWN1c3RvbWVycyAuc3RhdC1pY29uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tc3VjY2Vzcy01MDApLCB2YXIoLS1zdWNjZXNzLTYwMCkpO1xufVxuLnN0YXQtY2FyZC5uZXctY3VzdG9tZXJzIC5zdGF0LWljb24ge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS13YXJuaW5nLTUwMCksIHZhcigtLXdhcm5pbmctNjAwKSk7XG59XG4uc3RhdC1jYXJkLnZpcC1jdXN0b21lcnMgLnN0YXQtaWNvbiB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXNlY29uZGFyeS01MDApLCB2YXIoLS1zZWNvbmRhcnktNjAwKSk7XG59XG5cbi8qID09PT09IFRBQkxFIFNFQ1RJT04gPT09PT0gKi9cbi50YWJsZS1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2luZy0yeGwpO1xufVxuXG4udGFibGUtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy14bCkgIWltcG9ydGFudDtcbiAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LWxnKSAhaW1wb3J0YW50O1xuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ncmF5LTIwMCkgIWltcG9ydGFudDtcbiAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xufVxuLnRhYmxlLWNhcmQgLm1hdC1tZGMtY2FyZC1jb250ZW50IHtcbiAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xufVxuXG4udGFibGUtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhsKSB2YXIoLS1zcGFjaW5nLTJ4bCk7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1ncmF5LTIwMCk7XG4gIGJhY2tncm91bmQ6IHZhcigtLWdyYXktNTApO1xufVxuLnRhYmxlLWhlYWRlciAudGFibGUtdGl0bGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IHZhcigtLXNwYWNpbmctbWQpO1xufVxuLnRhYmxlLWhlYWRlciAudGFibGUtdGl0bGUgaDMge1xuICBtYXJnaW46IDA7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6IHZhcigtLWdyYXktOTAwKTtcbn1cbi50YWJsZS1oZWFkZXIgLnRhYmxlLXRpdGxlIC5yZXN1bHRzLWNvdW50IHtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgY29sb3I6IHZhcigtLWdyYXktNTAwKTtcbiAgYmFja2dyb3VuZDogdmFyKC0tZ3JheS0yMDApO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhzKSB2YXIoLS1zcGFjaW5nLXNtKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLW1kKTtcbn1cbi50YWJsZS1oZWFkZXIgLnRhYmxlLWFjdGlvbnMgLnBhZ2Utc2l6ZS1maWVsZCB7XG4gIHdpZHRoOiAxMjBweDtcbn1cblxuLnRhYmxlLWNvbnRhaW5lciB7XG4gIG92ZXJmbG93LXg6IGF1dG87XG4gIG1heC1oZWlnaHQ6IDYwMHB4O1xufVxuXG4uY3VzdG9tZXJzLXRhYmxlIHtcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcbiAgYmFja2dyb3VuZDogd2hpdGUgIWltcG9ydGFudDtcbn1cbi5jdXN0b21lcnMtdGFibGUgLm1hdC1tZGMtaGVhZGVyLXJvdyB7XG4gIGJhY2tncm91bmQ6IHZhcigtLWdyYXktNTApICFpbXBvcnRhbnQ7XG59XG4uY3VzdG9tZXJzLXRhYmxlIC5tYXQtbWRjLWhlYWRlci1yb3cgLm1hdC1tZGMtaGVhZGVyLWNlbGwge1xuICBmb250LXdlaWdodDogNzAwICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiB2YXIoLS1ncmF5LTgwMCkgIWltcG9ydGFudDtcbiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkIHZhcigtLWdyYXktMjAwKSAhaW1wb3J0YW50O1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLWxnKSAhaW1wb3J0YW50O1xuICBmb250LWZhbWlseTogdmFyKC0tZm9udC1mYW1pbHktcHJpbWFyeSkgIWltcG9ydGFudDtcbiAgZm9udC1zaXplOiAwLjg3NXJlbSAhaW1wb3J0YW50O1xufVxuLmN1c3RvbWVycy10YWJsZSAubWF0LW1kYy1yb3cge1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KSAhaW1wb3J0YW50O1xuICBjdXJzb3I6IHBvaW50ZXIgIWltcG9ydGFudDtcbn1cbi5jdXN0b21lcnMtdGFibGUgLm1hdC1tZGMtcm93OmhvdmVyIHtcbiAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS01MCkgIWltcG9ydGFudDtcbn1cbi5jdXN0b21lcnMtdGFibGUgLm1hdC1tZGMtcm93IC5tYXQtbWRjLWNlbGwge1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLWxnKSAhaW1wb3J0YW50O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tZ3JheS0xMDApICFpbXBvcnRhbnQ7XG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWZhbWlseS1wcmltYXJ5KSAhaW1wb3J0YW50O1xuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlICFpbXBvcnRhbnQ7XG59XG5cbi8qID09PT09IFRBQkxFIENFTEwgU1RZTEVTID09PT09ICovXG4uY3VzdG9tZXItY29kZSB7XG4gIGZvbnQtZmFtaWx5OiBcIkNvdXJpZXIgTmV3XCIsIG1vbm9zcGFjZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktNjAwKTtcbiAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS01MCk7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteHMpIHZhcigtLXNwYWNpbmctc20pO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtc20pO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuXG4uY3VzdG9tZXItaW5mbyAuY3VzdG9tZXItbmFtZSB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiB2YXIoLS1ncmF5LTkwMCk7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmcteHMpO1xufVxuLmN1c3RvbWVyLWluZm8gLmN1c3RvbWVyLXR5cGUge1xuICBmb250LXNpemU6IDAuNzVyZW07XG4gIGNvbG9yOiB2YXIoLS1ncmF5LTUwMCk7XG4gIGJhY2tncm91bmQ6IHZhcigtLWdyYXktMTAwKTtcbiAgcGFkZGluZzogdmFyKC0tc3BhY2luZy14cykgdmFyKC0tc3BhY2luZy1zbSk7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy1zbSk7XG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbn1cblxuLmNvbnRhY3QtaW5mbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogdmFyKC0tc3BhY2luZy14cyk7XG59XG4uY29udGFjdC1pbmZvIC5waG9uZSwgLmNvbnRhY3QtaW5mbyAuZW1haWwge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IHZhcigtLXNwYWNpbmcteHMpO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuLmNvbnRhY3QtaW5mbyAucGhvbmUgbWF0LWljb24sIC5jb250YWN0LWluZm8gLmVtYWlsIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAxcmVtO1xuICB3aWR0aDogMXJlbTtcbiAgaGVpZ2h0OiAxcmVtO1xuICBjb2xvcjogdmFyKC0tZ3JheS01MDApO1xufVxuXG4uYWRkcmVzcy1pbmZvIC5nb3Zlcm5vcmF0ZSB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiB2YXIoLS1ncmF5LTgwMCk7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmcteHMpO1xufVxuLmFkZHJlc3MtaW5mbyAuYWRkcmVzcyB7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gIGNvbG9yOiB2YXIoLS1ncmF5LTYwMCk7XG59XG5cbi5iYWxhbmNlIHtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbn1cbi5iYWxhbmNlLnBvc2l0aXZlIHtcbiAgY29sb3I6IHZhcigtLXN1Y2Nlc3MtNjAwKTtcbn1cbi5iYWxhbmNlLm5lZ2F0aXZlIHtcbiAgY29sb3I6IHZhcigtLWVycm9yLTYwMCk7XG59XG4uYmFsYW5jZS56ZXJvIHtcbiAgY29sb3I6IHZhcigtLWdyYXktNTAwKTtcbn1cblxuLnN0YXR1cy1iYWRnZSB7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteHMpIHZhcigtLXNwYWNpbmctbWQpO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtbWQpO1xuICBmb250LXNpemU6IDAuNzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG59XG4uc3RhdHVzLWJhZGdlLmFjdGl2ZSB7XG4gIGJhY2tncm91bmQ6IHZhcigtLXN1Y2Nlc3MtMTAwKTtcbiAgY29sb3I6IHZhcigtLXN1Y2Nlc3MtNzAwKTtcbn1cbi5zdGF0dXMtYmFkZ2UuaW5hY3RpdmUge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1lcnJvci0xMDApO1xuICBjb2xvcjogdmFyKC0tZXJyb3ItNzAwKTtcbn1cblxuLmFjdGlvbnMtYnV0dG9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogdmFyKC0tc3BhY2luZy14cyk7XG59XG4uYWN0aW9ucy1idXR0b25zIGJ1dHRvbiB7XG4gIHdpZHRoOiAzNnB4ICFpbXBvcnRhbnQ7XG4gIGhlaWdodDogMzZweCAhaW1wb3J0YW50O1xuICBtaW4td2lkdGg6IDM2cHggIWltcG9ydGFudDtcbn1cbi5hY3Rpb25zLWJ1dHRvbnMgYnV0dG9uIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAxLjEyNXJlbSAhaW1wb3J0YW50O1xuICB3aWR0aDogMS4xMjVyZW0gIWltcG9ydGFudDtcbiAgaGVpZ2h0OiAxLjEyNXJlbSAhaW1wb3J0YW50O1xufVxuXG4vKiA9PT09PSBMT0FESU5HIE9WRVJMQVkgPT09PT0gKi9cbi5sb2FkaW5nLW92ZXJsYXkge1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJvdHRvbTogMDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgei1pbmRleDogOTk5OTtcbn1cbi5sb2FkaW5nLW92ZXJsYXkgcCB7XG4gIG1hcmdpbi10b3A6IHZhcigtLXNwYWNpbmctbGcpO1xuICBjb2xvcjogdmFyKC0tZ3JheS02MDApO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4vKiA9PT09PSBSRVNQT05TSVZFIERFU0lHTiA9PT09PSAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkge1xuICAuZmlsdGVycy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyO1xuICB9XG4gIC5maWx0ZXJzLWdyaWQgLmNsZWFyLWZpbHRlcnMtYnRuIHtcbiAgICBncmlkLWNvbHVtbjogc3BhbiAzO1xuICAgIGp1c3RpZnktc2VsZjogY2VudGVyO1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnBhZ2UtaGVhZGVyIHtcbiAgICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLXhsKTtcbiAgICBtYXJnaW46IGNhbGMoLTEgKiB2YXIoLS1zcGFjaW5nLTJ4bCkpIGNhbGMoLTEgKiB2YXIoLS1zcGFjaW5nLTJ4bCkpIHZhcigtLXNwYWNpbmcteGwpO1xuICB9XG4gIC5wYWdlLWhlYWRlciAuaGVhZGVyLWNvbnRlbnQge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLWxnKTtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIH1cbiAgLnBhZ2UtaGVhZGVyIC5wYWdlLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gIH1cbiAgLnBhZ2UtaGVhZGVyIC5oZWFkZXItYWN0aW9ucyB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIH1cbiAgLmZpbHRlcnMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLWxnKTtcbiAgfVxuICAuZmlsdGVycy1ncmlkIC5jbGVhci1maWx0ZXJzLWJ0biB7XG4gICAgZ3JpZC1jb2x1bW46IHNwYW4gMTtcbiAgfVxuICAuc3RhdHMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgZ2FwOiB2YXIoLS1zcGFjaW5nLWxnKTtcbiAgfVxuICAudGFibGUtaGVhZGVyIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogdmFyKC0tc3BhY2luZy1tZCk7XG4gICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XG4gIH1cbiAgLnRhYmxlLWhlYWRlciAudGFibGUtYWN0aW9ucyB7XG4gICAgYWxpZ24tc2VsZjogY2VudGVyO1xuICB9XG4gIC5jdXN0b21lcnMtdGFibGUgLm1hdC1tZGMtaGVhZGVyLWNlbGwsXG4gIC5jdXN0b21lcnMtdGFibGUgLm1hdC1tZGMtY2VsbCB7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2luZy1tZCkgIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuODc1cmVtICFpbXBvcnRhbnQ7XG4gIH1cbiAgLmFjdGlvbnMtYnV0dG9ucyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IHZhcigtLXNwYWNpbmcteHMpO1xuICB9XG4gIC5hY3Rpb25zLWJ1dHRvbnMgYnV0dG9uIHtcbiAgICB3aWR0aDogMzJweCAhaW1wb3J0YW50O1xuICAgIGhlaWdodDogMzJweCAhaW1wb3J0YW50O1xuICAgIG1pbi13aWR0aDogMzJweCAhaW1wb3J0YW50O1xuICB9XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return CustomersNewComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatProgressSpinnerModule", "MatTooltipModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "gov_r2", "ɵɵtextInterpolate", "customer_r3", "code", "customer_r4", "customerTypeName", "customer_r5", "phone", "email", "ɵɵtemplate", "CustomersNewComponent_td_127_div_2_Template", "CustomersNewComponent_td_127_div_3_Template", "customer_r6", "address", "CustomersNewComponent_td_130_div_4_Template", "governorateName", "ɵɵclassMap", "ctx_r7", "getBalanceClass", "customer_r7", "balance", "ɵɵpipeBind2", "customer_r9", "isActive", "ɵɵlistener", "CustomersNewComponent_td_139_Template_button_click_2_listener", "customer_r11", "ɵɵrestoreView", "_r10", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewCustomer", "CustomersNewComponent_td_139_Template_button_click_5_listener", "editCustomer", "CustomersNewComponent_td_139_Template_button_click_8_listener", "deleteCustomer", "ɵɵelement", "CustomersNewComponent_tr_141_Template_tr_click_0_listener", "row_r13", "_r12", "CustomersNewComponent", "http", "isLoading", "customers", "filteredCustomers", "paginatedCustomers", "customerTypes", "governorates", "statistics", "totalCustomers", "activeCustomers", "newCustomersThisMonth", "vipCustomers", "searchTerm", "selectedCustomerType", "selectedGovernorate", "selectedStatus", "displayedColumns", "pageSize", "currentPage", "subscriptions", "constructor", "ngOnInit", "loadInitialData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "Promise", "all", "loadCustomers", "loadCustomerTypes", "loadGovernorates", "then", "calculateStatistics", "applyFilters", "catch", "error", "console", "resolve", "reject", "get", "subscribe", "next", "response", "getMockCustomers", "push", "getMockCustomerTypes", "getMockGovernorates", "length", "filter", "c", "currentMonth", "Date", "getMonth", "currentYear", "getFullYear", "createdDate", "createdAt", "filtered", "trim", "term", "toLowerCase", "customer", "includes", "customerTypeId", "toString", "governorateId", "updatePaginatedData", "startIndex", "endIndex", "slice", "onSearch", "onFilterChange", "clearFilters", "onPageChange", "event", "pageIndex", "onPageSizeChange", "openAddCustomer", "log", "exportCustomers", "ɵɵdirectiveInject", "i1", "HttpClient", "selectors", "decls", "vars", "consts", "template", "CustomersNewComponent_Template", "rf", "ctx", "CustomersNewComponent_Template_button_click_9_listener", "CustomersNewComponent_Template_button_click_14_listener", "ɵɵtwoWayListener", "CustomersNewComponent_Template_input_ngModelChange_26_listener", "$event", "ɵɵtwoWayBindingSet", "CustomersNewComponent_Template_input_input_26_listener", "CustomersNewComponent_Template_mat_select_ngModelChange_32_listener", "CustomersNewComponent_Template_mat_select_selectionChange_32_listener", "CustomersNewComponent_mat_option_35_Template", "CustomersNewComponent_Template_mat_select_ngModelChange_39_listener", "CustomersNewComponent_Template_mat_select_selectionChange_39_listener", "CustomersNewComponent_mat_option_42_Template", "CustomersNewComponent_Template_mat_select_ngModelChange_46_listener", "CustomersNewComponent_Template_mat_select_selectionChange_46_listener", "CustomersNewComponent_Template_button_click_53_listener", "CustomersNewComponent_Template_mat_select_ngModelChange_108_listener", "CustomersNewComponent_Template_mat_select_selectionChange_108_listener", "ɵɵelementContainerStart", "CustomersNewComponent_th_120_Template", "CustomersNewComponent_td_121_Template", "CustomersNewComponent_th_123_Template", "CustomersNewComponent_td_124_Template", "CustomersNewComponent_th_126_Template", "CustomersNewComponent_td_127_Template", "CustomersNewComponent_th_129_Template", "CustomersNewComponent_td_130_Template", "CustomersNewComponent_th_132_Template", "CustomersNewComponent_td_133_Template", "CustomersNewComponent_th_135_Template", "CustomersNewComponent_td_136_Template", "CustomersNewComponent_th_138_Template", "CustomersNewComponent_td_139_Template", "CustomersNewComponent_tr_140_Template", "CustomersNewComponent_tr_141_Template", "CustomersNewComponent_Template_mat_paginator_page_142_listener", "CustomersNewComponent_div_143_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i8", "MatInput", "i9", "MatSelect", "MatOption", "i10", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i11", "MatPaginator", "i12", "MatSort", "Mat<PERSON>ort<PERSON><PERSON>er", "i13", "MatProgressSpinner", "i14", "MatTooltip", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\customers\\customers-new.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\customers\\customers-new.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule, PageEvent } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Interfaces\ninterface Customer {\n  id: number;\n  code: string;\n  name: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  customerTypeId: number;\n  customerTypeName: string;\n  governorateId: number;\n  governorateName: string;\n  balance: number;\n  isActive: boolean;\n  createdAt: Date;\n}\n\ninterface CustomerType {\n  id: number;\n  name: string;\n}\n\ninterface Governorate {\n  id: number;\n  name: string;\n}\n\ninterface Statistics {\n  totalCustomers: number;\n  activeCustomers: number;\n  newCustomersThisMonth: number;\n  vipCustomers: number;\n}\n\n@Component({\n  selector: 'app-customers-new',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule\n  ],\n  templateUrl: './customers-new.component.html',\n  styleUrls: ['./customers-new.component.scss']\n})\nexport class CustomersNewComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = true;\n  \n  // Data\n  customers: Customer[] = [];\n  filteredCustomers: Customer[] = [];\n  paginatedCustomers: Customer[] = [];\n  customerTypes: CustomerType[] = [];\n  governorates: Governorate[] = [];\n  \n  // Statistics\n  statistics: Statistics = {\n    totalCustomers: 0,\n    activeCustomers: 0,\n    newCustomersThisMonth: 0,\n    vipCustomers: 0\n  };\n\n  // Filters\n  searchTerm = '';\n  selectedCustomerType = '';\n  selectedGovernorate = '';\n  selectedStatus = '';\n\n  // Table Configuration\n  displayedColumns: string[] = ['code', 'name', 'contact', 'address', 'balance', 'status', 'actions'];\n  pageSize = 25;\n  currentPage = 0;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(private http: HttpClient) {}\n\n  ngOnInit(): void {\n    this.loadInitialData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Load initial data\n   */\n  private loadInitialData(): void {\n    this.isLoading = true;\n    \n    // Load all data in parallel\n    Promise.all([\n      this.loadCustomers(),\n      this.loadCustomerTypes(),\n      this.loadGovernorates()\n    ]).then(() => {\n      this.calculateStatistics();\n      this.applyFilters();\n      this.isLoading = false;\n    }).catch(error => {\n      console.error('Error loading data:', error);\n      this.isLoading = false;\n    });\n  }\n\n  /**\n   * Load customers from API\n   */\n  private loadCustomers(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/customers').subscribe({\n        next: (response) => {\n          this.customers = response.customers || [];\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading customers:', error);\n          // Use mock data if API fails\n          this.customers = this.getMockCustomers();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load customer types from API\n   */\n  private loadCustomerTypes(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/customer-types').subscribe({\n        next: (response) => {\n          this.customerTypes = response.customerTypes || [];\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading customer types:', error);\n          // Use mock data if API fails\n          this.customerTypes = this.getMockCustomerTypes();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load governorates from API\n   */\n  private loadGovernorates(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/governorates').subscribe({\n        next: (response) => {\n          this.governorates = response.governorates || [];\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading governorates:', error);\n          // Use mock data if API fails\n          this.governorates = this.getMockGovernorates();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Calculate statistics\n   */\n  private calculateStatistics(): void {\n    this.statistics.totalCustomers = this.customers.length;\n    this.statistics.activeCustomers = this.customers.filter(c => c.isActive).length;\n    \n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    this.statistics.newCustomersThisMonth = this.customers.filter(c => {\n      const createdDate = new Date(c.createdAt);\n      return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;\n    }).length;\n    \n    // VIP customers (customers with balance > 10000)\n    this.statistics.vipCustomers = this.customers.filter(c => c.balance > 10000).length;\n  }\n\n  /**\n   * Apply filters\n   */\n  applyFilters(): void {\n    let filtered = [...this.customers];\n\n    // Search filter\n    if (this.searchTerm.trim()) {\n      const term = this.searchTerm.toLowerCase().trim();\n      filtered = filtered.filter(customer => \n        customer.name.toLowerCase().includes(term) ||\n        customer.code.toLowerCase().includes(term) ||\n        (customer.phone && customer.phone.includes(term)) ||\n        (customer.email && customer.email.toLowerCase().includes(term))\n      );\n    }\n\n    // Customer type filter\n    if (this.selectedCustomerType) {\n      filtered = filtered.filter(customer => \n        customer.customerTypeId.toString() === this.selectedCustomerType\n      );\n    }\n\n    // Governorate filter\n    if (this.selectedGovernorate) {\n      filtered = filtered.filter(customer => \n        customer.governorateId.toString() === this.selectedGovernorate\n      );\n    }\n\n    // Status filter\n    if (this.selectedStatus) {\n      const isActive = this.selectedStatus === 'active';\n      filtered = filtered.filter(customer => customer.isActive === isActive);\n    }\n\n    this.filteredCustomers = filtered;\n    this.currentPage = 0;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Update paginated data\n   */\n  private updatePaginatedData(): void {\n    const startIndex = this.currentPage * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);\n  }\n\n  /**\n   * Handle search\n   */\n  onSearch(): void {\n    this.applyFilters();\n  }\n\n  /**\n   * Handle filter change\n   */\n  onFilterChange(): void {\n    this.applyFilters();\n  }\n\n  /**\n   * Clear all filters\n   */\n  clearFilters(): void {\n    this.searchTerm = '';\n    this.selectedCustomerType = '';\n    this.selectedGovernorate = '';\n    this.selectedStatus = '';\n    this.applyFilters();\n  }\n\n  /**\n   * Handle page change\n   */\n  onPageChange(event: PageEvent): void {\n    this.currentPage = event.pageIndex;\n    this.pageSize = event.pageSize;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Handle page size change\n   */\n  onPageSizeChange(): void {\n    this.currentPage = 0;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Get balance class for styling\n   */\n  getBalanceClass(balance: number): string {\n    if (balance > 0) return 'positive';\n    if (balance < 0) return 'negative';\n    return 'zero';\n  }\n\n  /**\n   * Open add customer dialog\n   */\n  openAddCustomer(): void {\n    console.log('Open add customer dialog');\n    // Implement add customer functionality\n  }\n\n  /**\n   * View customer details\n   */\n  viewCustomer(customer: Customer): void {\n    console.log('View customer:', customer);\n    // Implement view customer functionality\n  }\n\n  /**\n   * Edit customer\n   */\n  editCustomer(customer: Customer): void {\n    console.log('Edit customer:', customer);\n    // Implement edit customer functionality\n  }\n\n  /**\n   * Delete customer\n   */\n  deleteCustomer(customer: Customer): void {\n    console.log('Delete customer:', customer);\n    // Implement delete customer functionality\n  }\n\n  /**\n   * Export customers\n   */\n  exportCustomers(): void {\n    console.log('Export customers');\n    // Implement export functionality\n  }\n\n  /**\n   * Get mock customers data\n   */\n  private getMockCustomers(): Customer[] {\n    return [\n      {\n        id: 1,\n        code: 'C001',\n        name: 'أحمد محمد علي',\n        phone: '01234567890',\n        email: '<EMAIL>',\n        address: 'شارع النيل، المعادي',\n        customerTypeId: 1,\n        customerTypeName: 'عميل عادي',\n        governorateId: 1,\n        governorateName: 'القاهرة',\n        balance: 5000,\n        isActive: true,\n        createdAt: new Date('2024-01-15')\n      },\n      {\n        id: 2,\n        code: 'C002',\n        name: 'فاطمة أحمد حسن',\n        phone: '01098765432',\n        email: '<EMAIL>',\n        address: 'شارع الهرم، الجيزة',\n        customerTypeId: 2,\n        customerTypeName: 'عميل VIP',\n        governorateId: 2,\n        governorateName: 'الجيزة',\n        balance: 15000,\n        isActive: true,\n        createdAt: new Date('2024-02-10')\n      }\n      // Add more mock data as needed\n    ];\n  }\n\n  /**\n   * Get mock customer types\n   */\n  private getMockCustomerTypes(): CustomerType[] {\n    return [\n      { id: 1, name: 'عميل عادي' },\n      { id: 2, name: 'عميل VIP' },\n      { id: 3, name: 'عميل جملة' },\n      { id: 4, name: 'عميل تجزئة' }\n    ];\n  }\n\n  /**\n   * Get mock governorates\n   */\n  private getMockGovernorates(): Governorate[] {\n    return [\n      { id: 1, name: 'القاهرة' },\n      { id: 2, name: 'الجيزة' },\n      { id: 3, name: 'الإسكندرية' },\n      { id: 4, name: 'الشرقية' },\n      { id: 5, name: 'البحيرة' }\n    ];\n  }\n}\n", "<!-- Terra Retail ERP - Professional Customers Module -->\n<div class=\"customers-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <h1 class=\"page-title\">إدارة العملاء</h1>\n        <p class=\"page-subtitle\">إدارة شاملة لجميع عملاء المتجر</p>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" class=\"add-btn\" (click)=\"openAddCustomer()\">\n          <mat-icon>person_add</mat-icon>\n          <span>إضافة عميل جديد</span>\n        </button>\n        <button mat-stroked-button class=\"export-btn\" (click)=\"exportCustomers()\">\n          <mat-icon>file_download</mat-icon>\n          <span>تصدير</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Filters Section -->\n  <div class=\"filters-section\">\n    <mat-card class=\"filters-card\">\n      <mat-card-content>\n        <div class=\"filters-grid\">\n          \n          <!-- Search -->\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>البحث</mat-label>\n            <input matInput placeholder=\"ابحث بالاسم، الهاتف، أو الرقم...\" \n                   [(ngModel)]=\"searchTerm\" (input)=\"onSearch()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <!-- Customer Type Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>نوع العميل</mat-label>\n            <mat-select [(ngModel)]=\"selectedCustomerType\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الأنواع</mat-option>\n              <mat-option *ngFor=\"let type of customerTypes\" [value]=\"type.id\">\n                {{ type.name }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Governorate Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>المحافظة</mat-label>\n            <mat-select [(ngModel)]=\"selectedGovernorate\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع المحافظات</mat-option>\n              <mat-option *ngFor=\"let gov of governorates\" [value]=\"gov.id\">\n                {{ gov.name }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Status Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>الحالة</mat-label>\n            <mat-select [(ngModel)]=\"selectedStatus\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الحالات</mat-option>\n              <mat-option value=\"active\">نشط</mat-option>\n              <mat-option value=\"inactive\">غير نشط</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Clear Filters -->\n          <button mat-stroked-button class=\"clear-filters-btn\" (click)=\"clearFilters()\">\n            <mat-icon>clear</mat-icon>\n            <span>مسح الفلاتر</span>\n          </button>\n\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Statistics Cards -->\n  <div class=\"stats-section\">\n    <div class=\"stats-grid\">\n      \n      <div class=\"stat-card total-customers\">\n        <div class=\"stat-icon\">\n          <mat-icon>people</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.totalCustomers }}</div>\n          <div class=\"stat-label\">إجمالي العملاء</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card active-customers\">\n        <div class=\"stat-icon\">\n          <mat-icon>person</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.activeCustomers }}</div>\n          <div class=\"stat-label\">العملاء النشطين</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card new-customers\">\n        <div class=\"stat-icon\">\n          <mat-icon>person_add</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.newCustomersThisMonth }}</div>\n          <div class=\"stat-label\">عملاء جدد هذا الشهر</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card vip-customers\">\n        <div class=\"stat-icon\">\n          <mat-icon>star</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.vipCustomers }}</div>\n          <div class=\"stat-label\">عملاء VIP</div>\n        </div>\n      </div>\n\n    </div>\n  </div>\n\n  <!-- Customers Table -->\n  <div class=\"table-section\">\n    <mat-card class=\"table-card\">\n      \n      <!-- Table Header -->\n      <div class=\"table-header\">\n        <div class=\"table-title\">\n          <h3>قائمة العملاء</h3>\n          <span class=\"results-count\">({{ filteredCustomers.length }} عميل)</span>\n        </div>\n        <div class=\"table-actions\">\n          <mat-form-field appearance=\"outline\" class=\"page-size-field\">\n            <mat-label>عدد الصفوف</mat-label>\n            <mat-select [(ngModel)]=\"pageSize\" (selectionChange)=\"onPageSizeChange()\">\n              <mat-option value=\"10\">10</mat-option>\n              <mat-option value=\"25\">25</mat-option>\n              <mat-option value=\"50\">50</mat-option>\n              <mat-option value=\"100\">100</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Table Content -->\n      <div class=\"table-container\">\n        <table mat-table [dataSource]=\"paginatedCustomers\" class=\"customers-table\" matSort>\n\n          <!-- Customer Code Column -->\n          <ng-container matColumnDef=\"code\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>كود العميل</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <span class=\"customer-code\">{{ customer.code }}</span>\n            </td>\n          </ng-container>\n\n          <!-- Customer Name Column -->\n          <ng-container matColumnDef=\"name\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم العميل</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <div class=\"customer-info\">\n                <div class=\"customer-name\">{{ customer.name }}</div>\n                <div class=\"customer-type\">{{ customer.customerTypeName }}</div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Contact Info Column -->\n          <ng-container matColumnDef=\"contact\">\n            <th mat-header-cell *matHeaderCellDef>معلومات الاتصال</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <div class=\"contact-info\">\n                <div class=\"phone\" *ngIf=\"customer.phone\">\n                  <mat-icon>phone</mat-icon>\n                  <span>{{ customer.phone }}</span>\n                </div>\n                <div class=\"email\" *ngIf=\"customer.email\">\n                  <mat-icon>email</mat-icon>\n                  <span>{{ customer.email }}</span>\n                </div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Address Column -->\n          <ng-container matColumnDef=\"address\">\n            <th mat-header-cell *matHeaderCellDef>العنوان</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <div class=\"address-info\">\n                <div class=\"governorate\">{{ customer.governorateName }}</div>\n                <div class=\"address\" *ngIf=\"customer.address\">{{ customer.address }}</div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Balance Column -->\n          <ng-container matColumnDef=\"balance\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <div class=\"balance\" [class]=\"getBalanceClass(customer.balance)\">\n                {{ customer.balance | number:'1.2-2' }} جنيه\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Status Column -->\n          <ng-container matColumnDef=\"status\">\n            <th mat-header-cell *matHeaderCellDef>الحالة</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <span class=\"status-badge\" [class]=\"customer.isActive ? 'active' : 'inactive'\">\n                {{ customer.isActive ? 'نشط' : 'غير نشط' }}\n              </span>\n            </td>\n          </ng-container>\n\n          <!-- Actions Column -->\n          <ng-container matColumnDef=\"actions\">\n            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\n            <td mat-cell *matCellDef=\"let customer\">\n              <div class=\"actions-buttons\">\n                <button mat-icon-button color=\"primary\" \n                        matTooltip=\"عرض التفاصيل\" \n                        (click)=\"viewCustomer(customer)\">\n                  <mat-icon>visibility</mat-icon>\n                </button>\n                <button mat-icon-button color=\"accent\" \n                        matTooltip=\"تعديل\" \n                        (click)=\"editCustomer(customer)\">\n                  <mat-icon>edit</mat-icon>\n                </button>\n                <button mat-icon-button color=\"warn\" \n                        matTooltip=\"حذف\" \n                        (click)=\"deleteCustomer(customer)\">\n                  <mat-icon>delete</mat-icon>\n                </button>\n              </div>\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" \n              class=\"table-row\" (click)=\"viewCustomer(row)\"></tr>\n\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <mat-paginator \n        [length]=\"filteredCustomers.length\"\n        [pageSize]=\"pageSize\"\n        [pageSizeOptions]=\"[10, 25, 50, 100]\"\n        (page)=\"onPageChange($event)\"\n        showFirstLastButtons>\n      </mat-paginator>\n\n    </mat-card>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل البيانات...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAmB,6BAA6B;AAC3E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;;ICyB9CC,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,IAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,qBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAM,MAAA,CAAAJ,EAAA,CAAgB;IAC3DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,MAAA,CAAAD,IAAA,MACF;;;;;IAqGFT,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnEH,EADF,CAAAC,cAAA,aAAwC,eACV;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;;;;IADyBH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAC,WAAA,CAAAC,IAAA,CAAmB;;;;;IAMjDb,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAGjEH,EAFJ,CAAAC,cAAA,aAAwC,cACX,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC5D,EACH;;;;IAH0BH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAG,WAAA,CAAAL,IAAA,CAAmB;IACnBT,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAW,iBAAA,CAAAG,WAAA,CAAAC,gBAAA,CAA+B;;;;;IAO9Df,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAIpDH,EADF,CAAAC,cAAA,cAA0C,eAC9B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;IADEH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAK,WAAA,CAAAC,KAAA,CAAoB;;;;;IAG1BjB,EADF,CAAAC,cAAA,cAA0C,eAC9B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;IADEH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAK,WAAA,CAAAE,KAAA,CAAoB;;;;;IAP9BlB,EADF,CAAAC,cAAA,aAAwC,cACZ;IAKxBD,EAJA,CAAAmB,UAAA,IAAAC,2CAAA,kBAA0C,IAAAC,2CAAA,kBAIA;IAK9CrB,EADE,CAAAG,YAAA,EAAM,EACH;;;;IATmBH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAY,WAAA,CAAAC,KAAA,CAAoB;IAIpBjB,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAY,WAAA,CAAAE,KAAA,CAAoB;;;;;IAU5ClB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAI9CH,EAAA,CAAAC,cAAA,cAA8C;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA5BH,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAW,WAAA,CAAAC,OAAA,CAAsB;;;;;IADpEvB,EAFJ,CAAAC,cAAA,aAAwC,cACZ,cACC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAmB,UAAA,IAAAK,2CAAA,kBAA8C;IAElDxB,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAHwBH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAW,iBAAA,CAAAW,WAAA,CAAAG,eAAA,CAA8B;IACjCzB,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAkB,WAAA,CAAAC,OAAA,CAAsB;;;;;IAOhDvB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/DH,EADF,CAAAC,cAAA,aAAwC,cAC2B;IAC/DD,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;;;;;IAHkBH,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAA0B,UAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAC,WAAA,CAAAC,OAAA,EAA2C;IAC9D9B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA+B,WAAA,OAAAF,WAAA,CAAAC,OAAA,yCACF;;;;;IAMF9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAwC,eACyC;IAC7ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAHwBH,EAAA,CAAAO,SAAA,EAAmD;IAAnDP,EAAA,CAAA0B,UAAA,CAAAM,WAAA,CAAAC,QAAA,yBAAmD;IAC5EjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAwB,WAAA,CAAAC,QAAA,uEACF;;;;;IAMFjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAwC,cACT,iBAGc;IAAjCD,EAAA,CAAAkC,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAA3B,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASd,MAAA,CAAAe,YAAA,CAAAN,YAAA,CAAsB;IAAA,EAAC;IACtCpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAEyC;IAAjCD,EAAA,CAAAkC,UAAA,mBAAAS,8DAAA;MAAA,MAAAP,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAA3B,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASd,MAAA,CAAAiB,YAAA,CAAAR,YAAA,CAAsB;IAAA,EAAC;IACtCpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAE2C;IAAnCD,EAAA,CAAAkC,UAAA,mBAAAW,8DAAA;MAAA,MAAAT,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAA3B,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASd,MAAA,CAAAmB,cAAA,CAAAV,YAAA,CAAwB;IAAA,EAAC;IACxCpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGtBF,EAHsB,CAAAG,YAAA,EAAW,EACpB,EACL,EACH;;;;;IAGPH,EAAA,CAAA+C,SAAA,aAA4D;;;;;;IAC5D/C,EAAA,CAAAC,cAAA,aACkD;IAA5BD,EAAA,CAAAkC,UAAA,mBAAAc,0DAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAqC,aAAA,CAAAa,IAAA,EAAAX,SAAA;MAAA,MAAAZ,MAAA,GAAA3B,EAAA,CAAAwC,aAAA;MAAA,OAAAxC,EAAA,CAAAyC,WAAA,CAASd,MAAA,CAAAe,YAAA,CAAAO,OAAA,CAAiB;IAAA,EAAC;IAACjD,EAAA,CAAAG,YAAA,EAAK;;;;;IAkB/DH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA+C,SAAA,sBAAyC;IACzC/C,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;ADlMR,WAAagD,qBAAqB;EAA5B,MAAOA,qBAAqB;IAkCZC,IAAA;IAhCpB;IACAC,SAAS,GAAG,IAAI;IAEhB;IACAC,SAAS,GAAe,EAAE;IAC1BC,iBAAiB,GAAe,EAAE;IAClCC,kBAAkB,GAAe,EAAE;IACnCC,aAAa,GAAmB,EAAE;IAClCC,YAAY,GAAkB,EAAE;IAEhC;IACAC,UAAU,GAAe;MACvBC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,qBAAqB,EAAE,CAAC;MACxBC,YAAY,EAAE;KACf;IAED;IACAC,UAAU,GAAG,EAAE;IACfC,oBAAoB,GAAG,EAAE;IACzBC,mBAAmB,GAAG,EAAE;IACxBC,cAAc,GAAG,EAAE;IAEnB;IACAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;IACnGC,QAAQ,GAAG,EAAE;IACbC,WAAW,GAAG,CAAC;IAEf;IACQC,aAAa,GAAmB,EAAE;IAE1CC,YAAoBpB,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAe;IAEvCqB,QAAQA,CAAA;MACN,IAAI,CAACC,eAAe,EAAE;IACxB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQJ,eAAeA,CAAA;MACrB,IAAI,CAACrB,SAAS,GAAG,IAAI;MAErB;MACA0B,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,aAAa,EAAE,EACpB,IAAI,CAACC,iBAAiB,EAAE,EACxB,IAAI,CAACC,gBAAgB,EAAE,CACxB,CAAC,CAACC,IAAI,CAAC,MAAK;QACX,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACjC,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC,CAACkC,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACnC,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;IAEA;;;IAGQ4B,aAAaA,CAAA;MACnB,OAAO,IAAIF,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAACzB,IAAI,CAACwC,GAAG,CAAM,4CAA4C,CAAC,CAACC,SAAS,CAAC;UACrFC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACzC,SAAS,GAAGyC,QAAQ,CAACzC,SAAS,IAAI,EAAE;YACzCoC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChD;YACA,IAAI,CAAClC,SAAS,GAAG,IAAI,CAAC0C,gBAAgB,EAAE;YACxCN,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC0B,IAAI,CAACpB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQK,iBAAiBA,CAAA;MACvB,OAAO,IAAIH,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAACzB,IAAI,CAACwC,GAAG,CAAM,iDAAiD,CAAC,CAACC,SAAS,CAAC;UAC1FC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACtC,aAAa,GAAGsC,QAAQ,CAACtC,aAAa,IAAI,EAAE;YACjDiC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD;YACA,IAAI,CAAC/B,aAAa,GAAG,IAAI,CAACyC,oBAAoB,EAAE;YAChDR,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC0B,IAAI,CAACpB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQM,gBAAgBA,CAAA;MACtB,OAAO,IAAIJ,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAACzB,IAAI,CAACwC,GAAG,CAAM,+CAA+C,CAAC,CAACC,SAAS,CAAC;UACxFC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACrC,YAAY,GAAGqC,QAAQ,CAACrC,YAAY,IAAI,EAAE;YAC/CgC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;YACnD;YACA,IAAI,CAAC9B,YAAY,GAAG,IAAI,CAACyC,mBAAmB,EAAE;YAC9CT,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC0B,IAAI,CAACpB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQQ,mBAAmBA,CAAA;MACzB,IAAI,CAAC1B,UAAU,CAACC,cAAc,GAAG,IAAI,CAACN,SAAS,CAAC8C,MAAM;MACtD,IAAI,CAACzC,UAAU,CAACE,eAAe,GAAG,IAAI,CAACP,SAAS,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrE,QAAQ,CAAC,CAACmE,MAAM;MAE/E,MAAMG,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;MAC1C,MAAMC,WAAW,GAAG,IAAIF,IAAI,EAAE,CAACG,WAAW,EAAE;MAC5C,IAAI,CAAChD,UAAU,CAACG,qBAAqB,GAAG,IAAI,CAACR,SAAS,CAAC+C,MAAM,CAACC,CAAC,IAAG;QAChE,MAAMM,WAAW,GAAG,IAAIJ,IAAI,CAACF,CAAC,CAACO,SAAS,CAAC;QACzC,OAAOD,WAAW,CAACH,QAAQ,EAAE,KAAKF,YAAY,IAAIK,WAAW,CAACD,WAAW,EAAE,KAAKD,WAAW;MAC7F,CAAC,CAAC,CAACN,MAAM;MAET;MACA,IAAI,CAACzC,UAAU,CAACI,YAAY,GAAG,IAAI,CAACT,SAAS,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxE,OAAO,GAAG,KAAK,CAAC,CAACsE,MAAM;IACrF;IAEA;;;IAGAd,YAAYA,CAAA;MACV,IAAIwB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACxD,SAAS,CAAC;MAElC;MACA,IAAI,IAAI,CAACU,UAAU,CAAC+C,IAAI,EAAE,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAAChD,UAAU,CAACiD,WAAW,EAAE,CAACF,IAAI,EAAE;QACjDD,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAACa,QAAQ,IACjCA,QAAQ,CAACzG,IAAI,CAACwG,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC1CE,QAAQ,CAACrG,IAAI,CAACoG,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IACzCE,QAAQ,CAACjG,KAAK,IAAIiG,QAAQ,CAACjG,KAAK,CAACkG,QAAQ,CAACH,IAAI,CAAE,IAChDE,QAAQ,CAAChG,KAAK,IAAIgG,QAAQ,CAAChG,KAAK,CAAC+F,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAE,CAChE;MACH;MAEA;MACA,IAAI,IAAI,CAAC/C,oBAAoB,EAAE;QAC7B6C,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAACa,QAAQ,IACjCA,QAAQ,CAACE,cAAc,CAACC,QAAQ,EAAE,KAAK,IAAI,CAACpD,oBAAoB,CACjE;MACH;MAEA;MACA,IAAI,IAAI,CAACC,mBAAmB,EAAE;QAC5B4C,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAACa,QAAQ,IACjCA,QAAQ,CAACI,aAAa,CAACD,QAAQ,EAAE,KAAK,IAAI,CAACnD,mBAAmB,CAC/D;MACH;MAEA;MACA,IAAI,IAAI,CAACC,cAAc,EAAE;QACvB,MAAMlC,QAAQ,GAAG,IAAI,CAACkC,cAAc,KAAK,QAAQ;QACjD2C,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAACa,QAAQ,IAAIA,QAAQ,CAACjF,QAAQ,KAAKA,QAAQ,CAAC;MACxE;MAEA,IAAI,CAACsB,iBAAiB,GAAGuD,QAAQ;MACjC,IAAI,CAACxC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACiD,mBAAmB,EAAE;IAC5B;IAEA;;;IAGQA,mBAAmBA,CAAA;MACzB,MAAMC,UAAU,GAAG,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACD,QAAQ;MACnD,MAAMoD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACnD,QAAQ;MAC3C,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACD,iBAAiB,CAACmE,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IAC9E;IAEA;;;IAGAE,QAAQA,CAAA;MACN,IAAI,CAACrC,YAAY,EAAE;IACrB;IAEA;;;IAGAsC,cAAcA,CAAA;MACZ,IAAI,CAACtC,YAAY,EAAE;IACrB;IAEA;;;IAGAuC,YAAYA,CAAA;MACV,IAAI,CAAC7D,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACmB,YAAY,EAAE;IACrB;IAEA;;;IAGAwC,YAAYA,CAACC,KAAgB;MAC3B,IAAI,CAACzD,WAAW,GAAGyD,KAAK,CAACC,SAAS;MAClC,IAAI,CAAC3D,QAAQ,GAAG0D,KAAK,CAAC1D,QAAQ;MAC9B,IAAI,CAACkD,mBAAmB,EAAE;IAC5B;IAEA;;;IAGAU,gBAAgBA,CAAA;MACd,IAAI,CAAC3D,WAAW,GAAG,CAAC;MACpB,IAAI,CAACiD,mBAAmB,EAAE;IAC5B;IAEA;;;IAGA3F,eAAeA,CAACE,OAAe;MAC7B,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,OAAO,MAAM;IACf;IAEA;;;IAGAoG,eAAeA,CAAA;MACbzC,OAAO,CAAC0C,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;;;IAGAzF,YAAYA,CAACwE,QAAkB;MAC7BzB,OAAO,CAAC0C,GAAG,CAAC,gBAAgB,EAAEjB,QAAQ,CAAC;MACvC;IACF;IAEA;;;IAGAtE,YAAYA,CAACsE,QAAkB;MAC7BzB,OAAO,CAAC0C,GAAG,CAAC,gBAAgB,EAAEjB,QAAQ,CAAC;MACvC;IACF;IAEA;;;IAGApE,cAAcA,CAACoE,QAAkB;MAC/BzB,OAAO,CAAC0C,GAAG,CAAC,kBAAkB,EAAEjB,QAAQ,CAAC;MACzC;IACF;IAEA;;;IAGAkB,eAAeA,CAAA;MACb3C,OAAO,CAAC0C,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEA;;;IAGQnC,gBAAgBA,CAAA;MACtB,OAAO,CACL;QACE1F,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,MAAM;QACZJ,IAAI,EAAE,eAAe;QACrBQ,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,mBAAmB;QAC1BK,OAAO,EAAE,qBAAqB;QAC9B6F,cAAc,EAAE,CAAC;QACjBrG,gBAAgB,EAAE,WAAW;QAC7BuG,aAAa,EAAE,CAAC;QAChB7F,eAAe,EAAE,SAAS;QAC1BK,OAAO,EAAE,IAAI;QACbG,QAAQ,EAAE,IAAI;QACd4E,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;OACjC,EACD;QACElG,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,MAAM;QACZJ,IAAI,EAAE,gBAAgB;QACtBQ,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,mBAAmB;QAC1BK,OAAO,EAAE,oBAAoB;QAC7B6F,cAAc,EAAE,CAAC;QACjBrG,gBAAgB,EAAE,UAAU;QAC5BuG,aAAa,EAAE,CAAC;QAChB7F,eAAe,EAAE,QAAQ;QACzBK,OAAO,EAAE,KAAK;QACdG,QAAQ,EAAE,IAAI;QACd4E,SAAS,EAAE,IAAIL,IAAI,CAAC,YAAY;;MAElC;MAAA,CACD;IACH;IAEA;;;IAGQN,oBAAoBA,CAAA;MAC1B,OAAO,CACL;QAAE5F,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAW,CAAE,EAC5B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAU,CAAE,EAC3B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAW,CAAE,EAC5B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAY,CAAE,CAC9B;IACH;IAEA;;;IAGQ0F,mBAAmBA,CAAA;MACzB,OAAO,CACL;QAAE7F,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAS,CAAE,EAC1B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAQ,CAAE,EACzB;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAY,CAAE,EAC7B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAS,CAAE,EAC1B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAS,CAAE,CAC3B;IACH;;uCA9VW0C,qBAAqB,EAAAnD,EAAA,CAAAqI,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;YAArBpF,qBAAqB;MAAAqF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnE1B9I,EANR,CAAAC,cAAA,aAAiC,aAGN,aACK,aACD,YACA;UAAAD,EAAA,CAAAE,MAAA,gFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,uKAA8B;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACvD;UAEJH,EADF,CAAAC,cAAA,aAA4B,gBAC4D;UAA5BD,EAAA,CAAAkC,UAAA,mBAAA8G,uDAAA;YAAA,OAASD,GAAA,CAAAb,eAAA,EAAiB;UAAA,EAAC;UACnFlI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wFAAe;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACrB;UACTH,EAAA,CAAAC,cAAA,iBAA0E;UAA5BD,EAAA,CAAAkC,UAAA,mBAAA+G,wDAAA;YAAA,OAASF,GAAA,CAAAX,eAAA,EAAiB;UAAA,EAAC;UACvEpI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAInBF,EAJmB,CAAAG,YAAA,EAAO,EACX,EACL,EACF,EACF;UAUIH,EAPV,CAAAC,cAAA,cAA6B,oBACI,wBACX,eACU,0BAGkC,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,iBACqD;UAA9CD,EAAA,CAAAkJ,gBAAA,2BAAAC,+DAAAC,MAAA;YAAApJ,EAAA,CAAAqJ,kBAAA,CAAAN,GAAA,CAAA/E,UAAA,EAAAoF,MAAA,MAAAL,GAAA,CAAA/E,UAAA,GAAAoF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACpJ,EAAA,CAAAkC,UAAA,mBAAAoH,uDAAA;YAAA,OAASP,GAAA,CAAApB,QAAA,EAAU;UAAA,EAAC;UADpD3H,EAAA,CAAAG,YAAA,EACqD;UACrDH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,sBAAoF;UAAxED,EAAA,CAAAkJ,gBAAA,2BAAAK,oEAAAH,MAAA;YAAApJ,EAAA,CAAAqJ,kBAAA,CAAAN,GAAA,CAAA9E,oBAAA,EAAAmF,MAAA,MAAAL,GAAA,CAAA9E,oBAAA,GAAAmF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAACpJ,EAAA,CAAAkC,UAAA,6BAAAsH,sEAAA;YAAA,OAAmBT,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UACjF5H,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAmB,UAAA,KAAAsI,4CAAA,yBAAiE;UAIrEzJ,EADE,CAAAG,YAAA,EAAa,EACE;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,sBAAmF;UAAvED,EAAA,CAAAkJ,gBAAA,2BAAAQ,oEAAAN,MAAA;YAAApJ,EAAA,CAAAqJ,kBAAA,CAAAN,GAAA,CAAA7E,mBAAA,EAAAkF,MAAA,MAAAL,GAAA,CAAA7E,mBAAA,GAAAkF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAACpJ,EAAA,CAAAkC,UAAA,6BAAAyH,sEAAA;YAAA,OAAmBZ,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAChF5H,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAChDH,EAAA,CAAAmB,UAAA,KAAAyI,4CAAA,yBAA8D;UAIlE5J,EADE,CAAAG,YAAA,EAAa,EACE;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,sBAA8E;UAAlED,EAAA,CAAAkJ,gBAAA,2BAAAW,oEAAAT,MAAA;YAAApJ,EAAA,CAAAqJ,kBAAA,CAAAN,GAAA,CAAA5E,cAAA,EAAAiF,MAAA,MAAAL,GAAA,CAAA5E,cAAA,GAAAiF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAACpJ,EAAA,CAAAkC,UAAA,6BAAA4H,sEAAA;YAAA,OAAmBf,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAC3E5H,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC3CH,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAExCF,EAFwC,CAAAG,YAAA,EAAa,EACtC,EACE;UAGjBH,EAAA,CAAAC,cAAA,kBAA8E;UAAzBD,EAAA,CAAAkC,UAAA,mBAAA6H,wDAAA;YAAA,OAAShB,GAAA,CAAAlB,YAAA,EAAc;UAAA,EAAC;UAC3E7H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAM3BF,EAN2B,CAAAG,YAAA,EAAO,EACjB,EAEL,EACW,EACV,EACP;UAQEH,EALR,CAAAC,cAAA,eAA2B,eACD,eAEiB,eACd,gBACX;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAE1CF,EAF0C,CAAAG,YAAA,EAAM,EACxC,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACf,gBACX;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC9DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAE3CF,EAF2C,CAAAG,YAAA,EAAM,EACzC,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAqC,eACZ,gBACX;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EAC3B;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpEH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,2GAAmB;UAE/CF,EAF+C,CAAAG,YAAA,EAAM,EAC7C,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAqC,eACZ,gBACX;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EACrB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,0CAAS;UAKzCF,EALyC,CAAAG,YAAA,EAAM,EACnC,EACF,EAEF,EACF;UASEH,EANR,CAAAC,cAAA,eAA2B,oBACI,eAGD,eACC,WACnB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAE,MAAA,KAAqC;UACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;UAGFH,EAFJ,CAAAC,cAAA,gBAA2B,2BACoC,kBAChD;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,uBAA0E;UAA9DD,EAAA,CAAAkJ,gBAAA,2BAAAc,qEAAAZ,MAAA;YAAApJ,EAAA,CAAAqJ,kBAAA,CAAAN,GAAA,CAAA1E,QAAA,EAAA+E,MAAA,MAAAL,GAAA,CAAA1E,QAAA,GAAA+E,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAACpJ,EAAA,CAAAkC,UAAA,6BAAA+H,uEAAA;YAAA,OAAmBlB,GAAA,CAAAd,gBAAA,EAAkB;UAAA,EAAC;UACvEjI,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAwB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAInCF,EAJmC,CAAAG,YAAA,EAAa,EAC7B,EACE,EACb,EACF;UAIJH,EADF,CAAAC,cAAA,gBAA6B,kBACwD;UAGjFD,EAAA,CAAAkK,uBAAA,SAAkC;UAEhClK,EADA,CAAAmB,UAAA,MAAAgJ,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAM1CpK,EAAA,CAAAkK,uBAAA,SAAkC;UAEhClK,EADA,CAAAmB,UAAA,MAAAkJ,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAS1CtK,EAAA,CAAAkK,uBAAA,SAAqC;UAEnClK,EADA,CAAAmB,UAAA,MAAAoJ,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAe1CxK,EAAA,CAAAkK,uBAAA,SAAqC;UAEnClK,EADA,CAAAmB,UAAA,MAAAsJ,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAS1C1K,EAAA,CAAAkK,uBAAA,SAAqC;UAEnClK,EADA,CAAAmB,UAAA,MAAAwJ,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAQ1C5K,EAAA,CAAAkK,uBAAA,SAAoC;UAElClK,EADA,CAAAmB,UAAA,MAAA0J,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAQ1C9K,EAAA,CAAAkK,uBAAA,SAAqC;UAEnClK,EADA,CAAAmB,UAAA,MAAA4J,qCAAA,iBAAsC,MAAAC,qCAAA,kBACE;;UAsB1ChL,EADA,CAAAmB,UAAA,MAAA8J,qCAAA,iBAAuD,MAAAC,qCAAA,iBAEL;UAGtDlL,EADE,CAAAG,YAAA,EAAQ,EACJ;UAGNH,EAAA,CAAAC,cAAA,0BAKuB;UADrBD,EAAA,CAAAkC,UAAA,kBAAAiJ,+DAAA/B,MAAA;YAAA,OAAQL,GAAA,CAAAjB,YAAA,CAAAsB,MAAA,CAAoB;UAAA,EAAC;UAKnCpJ,EAHI,CAAAG,YAAA,EAAgB,EAEP,EACP;UAGNH,EAAA,CAAAmB,UAAA,MAAAiK,sCAAA,kBAA+C;UAKjDpL,EAAA,CAAAG,YAAA,EAAM;;;UA7OaH,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAAqL,gBAAA,YAAAtC,GAAA,CAAA/E,UAAA,CAAwB;UAOnBhE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAqL,gBAAA,YAAAtC,GAAA,CAAA9E,oBAAA,CAAkC;UAEfjE,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA2I,GAAA,CAAAtF,aAAA,CAAgB;UASnCzD,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAqL,gBAAA,YAAAtC,GAAA,CAAA7E,mBAAA,CAAiC;UAEflE,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,YAAA2I,GAAA,CAAArF,YAAA,CAAe;UASjC1D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAqL,gBAAA,YAAAtC,GAAA,CAAA5E,cAAA,CAA4B;UA2BlBnE,EAAA,CAAAO,SAAA,IAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAoI,GAAA,CAAApF,UAAA,CAAAC,cAAA,CAA+B;UAU/B5D,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAW,iBAAA,CAAAoI,GAAA,CAAApF,UAAA,CAAAE,eAAA,CAAgC;UAUhC7D,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAW,iBAAA,CAAAoI,GAAA,CAAApF,UAAA,CAAAG,qBAAA,CAAsC;UAUtC9D,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAW,iBAAA,CAAAoI,GAAA,CAAApF,UAAA,CAAAI,YAAA,CAA6B;UAgBzB/D,EAAA,CAAAO,SAAA,IAAqC;UAArCP,EAAA,CAAAQ,kBAAA,MAAAuI,GAAA,CAAAxF,iBAAA,CAAA6C,MAAA,+BAAqC;UAKnDpG,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAqL,gBAAA,YAAAtC,GAAA,CAAA1E,QAAA,CAAsB;UAYrBrE,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,eAAA2I,GAAA,CAAAvF,kBAAA,CAAiC;UA6F5BxD,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,oBAAA2I,GAAA,CAAA3E,gBAAA,CAAiC;UACpBpE,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,qBAAA2I,GAAA,CAAA3E,gBAAA,CAA0B;UAQ7DpE,EAAA,CAAAO,SAAA,EAAmC;UAEnCP,EAFA,CAAAI,UAAA,WAAA2I,GAAA,CAAAxF,iBAAA,CAAA6C,MAAA,CAAmC,aAAA2C,GAAA,CAAA1E,QAAA,CACd,oBAAArE,EAAA,CAAAsL,eAAA,KAAAC,GAAA,EACgB;UASbvL,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA2I,GAAA,CAAA1F,SAAA,CAAe;;;qBDhN3ClE,YAAY,EAAAqM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZvM,WAAW,EAAAwM,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX1M,aAAa,EAAA2M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EACb5M,eAAe,EAAA6M,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf9M,aAAa,EAAA+M,EAAA,CAAAC,OAAA,EACb/M,kBAAkB,EAAAgN,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAClBlN,cAAc,EAAAmN,EAAA,CAAAC,QAAA,EACdnN,eAAe,EAAAoN,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfrN,cAAc,EAAAsN,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACd/N,kBAAkB,EAAAgO,GAAA,CAAAC,YAAA,EAClBhO,aAAa,EAAAiO,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,aAAA,EACblO,wBAAwB,EAAAmO,GAAA,CAAAC,kBAAA,EACxBnO,gBAAgB,EAAAoO,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;;SAKPlL,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}