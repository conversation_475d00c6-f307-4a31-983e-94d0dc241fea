import pyodbc
import datetime

# إعداد الاتصال بقاعدة البيانات
def get_connection():
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=localhost;"
        "DATABASE=TerraRetailERP;"
        "UID=sa;"
        "PWD=@a123admin4;"
        "Trusted_Connection=no;"
    )
    return pyodbc.connect(conn_str)

def create_accounting_system():
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        print("🏗️ إنشاء النظام المحاسبي الكامل...")
        
        # 1. حذف الجداول القديمة وإعادة إنشائها
        print("📋 إنشاء جداول النظام المحاسبي...")
        
        # حذف الجداول القديمة
        cursor.execute("DROP TABLE IF EXISTS Transactions")
        cursor.execute("DROP TABLE IF EXISTS TransactionTypes")
        cursor.execute("DROP TABLE IF EXISTS AccountBalances")
        
        # إنشاء جدول أنواع المعاملات
        cursor.execute("""
        CREATE TABLE TransactionTypes (
            TransactionTypeId INT IDENTITY(1,1) PRIMARY KEY,
            NameAr NVARCHAR(100) NOT NULL,
            NameEn NVARCHAR(100) NULL,
            IsActive BIT DEFAULT 1,
            CreatedAt DATETIME2 DEFAULT GETDATE(),
            CreatedBy NVARCHAR(100) DEFAULT 'System'
        )
        """)
        
        # إدخال أنواع المعاملات المحاسبية الاحترافية
        transaction_types = [
            # معاملات المبيعات والعملاء
            ('فاتورة مبيعات', 'Sales Invoice'),
            ('مردود مبيعات', 'Sales Return'),
            ('خصم مبيعات', 'Sales Discount'),
            ('عمولة مبيعات', 'Sales Commission'),
            ('مقبوضات من العملاء', 'Customer Receipts'),
            ('تسوية حساب عميل', 'Customer Account Adjustment'),

            # معاملات المشتريات والموردين
            ('فاتورة مشتريات', 'Purchase Invoice'),
            ('مردود مشتريات', 'Purchase Return'),
            ('خصم مشتريات', 'Purchase Discount'),
            ('مدفوعات للموردين', 'Supplier Payments'),
            ('تسوية حساب مورد', 'Supplier Account Adjustment'),
            ('مصروفات شحن ونقل', 'Shipping & Transportation'),

            # معاملات الخزينة والبنوك
            ('سند قبض نقدي', 'Cash Receipt Voucher'),
            ('سند صرف نقدي', 'Cash Payment Voucher'),
            ('إيداع بنكي', 'Bank Deposit'),
            ('سحب بنكي', 'Bank Withdrawal'),
            ('تحويل بنكي', 'Bank Transfer'),
            ('عمولة بنكية', 'Bank Commission'),
            ('فوائد بنكية دائنة', 'Bank Interest Credit'),
            ('فوائد بنكية مدينة', 'Bank Interest Debit'),

            # معاملات المخزون
            ('استلام بضاعة', 'Goods Receipt'),
            ('صرف بضاعة', 'Goods Issue'),
            ('تسوية مخزون', 'Inventory Adjustment'),
            ('تحويل بين المخازن', 'Inter-Warehouse Transfer'),
            ('تحويل بين الفروع', 'Inter-Branch Transfer'),
            ('جرد مخزون', 'Inventory Count'),
            ('تالف وفاقد', 'Damaged & Lost Items'),

            # معاملات الموظفين والرواتب
            ('راتب موظف', 'Employee Salary'),
            ('بدلات ومكافآت', 'Allowances & Bonuses'),
            ('خصومات رواتب', 'Salary Deductions'),
            ('تأمينات اجتماعية', 'Social Insurance'),
            ('ضرائب رواتب', 'Payroll Taxes'),
            ('سلف موظفين', 'Employee Advances'),

            # معاملات الأصول الثابتة
            ('شراء أصل ثابت', 'Fixed Asset Purchase'),
            ('بيع أصل ثابت', 'Fixed Asset Sale'),
            ('استهلاك أصول', 'Asset Depreciation'),
            ('صيانة أصول', 'Asset Maintenance'),
            ('تحسينات أصول', 'Asset Improvements'),

            # معاملات المصروفات التشغيلية
            ('مصروفات إدارية', 'Administrative Expenses'),
            ('مصروفات تسويقية', 'Marketing Expenses'),
            ('مصروفات إيجار', 'Rent Expenses'),
            ('مصروفات كهرباء ومياه', 'Utilities Expenses'),
            ('مصروفات اتصالات', 'Communication Expenses'),
            ('مصروفات صيانة', 'Maintenance Expenses'),
            ('مصروفات تأمين', 'Insurance Expenses'),
            ('مصروفات قانونية', 'Legal Expenses'),

            # معاملات الضرائب والرسوم
            ('ضريبة القيمة المضافة', 'VAT Tax'),
            ('ضريبة الدخل', 'Income Tax'),
            ('رسوم حكومية', 'Government Fees'),
            ('غرامات وجزاءات', 'Penalties & Fines'),

            # معاملات رأس المال والاستثمار
            ('رأس مال مدفوع', 'Paid Capital'),
            ('احتياطيات', 'Reserves'),
            ('أرباح محتجزة', 'Retained Earnings'),
            ('توزيعات أرباح', 'Dividend Distribution'),
            ('استثمارات مالية', 'Financial Investments'),

            # معاملات خاصة
            ('أرصدة افتتاحية', 'Opening Balances'),
            ('قيود تسوية', 'Adjustment Entries'),
            ('قيود إقفال', 'Closing Entries'),
            ('قيود تصحيحية', 'Corrective Entries'),
            ('مخصصات', 'Provisions'),
            ('احتياطيات', 'Reserves'),
            ('إعادة تقييم', 'Revaluation'),
            ('تحويل عملة', 'Currency Exchange'),
            ('فروق تقييم', 'Valuation Differences')
        ]
        
        for name_ar, name_en in transaction_types:
            cursor.execute("""
            INSERT INTO TransactionTypes (NameAr, NameEn) 
            VALUES (?, ?)
            """, name_ar, name_en)
        
        print(f"✅ تم إنشاء {len(transaction_types)} نوع معاملة")
        
        # 2. إنشاء جدول المعاملات المالية
        cursor.execute("""
        CREATE TABLE Transactions (
            TransactionId BIGINT IDENTITY(1,1) PRIMARY KEY,
            TransactionDate DATE NOT NULL,
            VoucherId NVARCHAR(20) NULL,
            Type INT NOT NULL,
            LineNumber INT NOT NULL,
            MainAccountId INT NOT NULL,
            SubAccountId INT NULL,
            Credit DECIMAL(18,2) DEFAULT 0,
            Debit DECIMAL(18,2) DEFAULT 0,
            BranchId INT NULL,
            YearId INT NULL,
            Status NVARCHAR(20) DEFAULT 'POSTED',
            CollectionName NVARCHAR(100) NULL,
            Notes NVARCHAR(500) NULL,
            ReferenceType NVARCHAR(50) NULL,
            ReferenceId BIGINT NULL,
            CustomerId INT NULL,
            SupplierId INT NULL,
            AddUser NVARCHAR(100) NULL,
            UpdateUser NVARCHAR(100) NULL,
            DeleteUser NVARCHAR(100) NULL,
            AddDate DATETIME2 DEFAULT GETDATE(),
            UpdateDate DATETIME2 NULL,
            DeleteDate DATETIME2 NULL,
            IsDeleted BIT DEFAULT 0,
            FOREIGN KEY (Type) REFERENCES TransactionTypes(TransactionTypeId),
            FOREIGN KEY (MainAccountId) REFERENCES ChartOfAccounts(Id),
            FOREIGN KEY (BranchId) REFERENCES Branches(Id),
            FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
            FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
        )
        """)
        
        # 3. إنشاء جدول أرصدة الحسابات
        cursor.execute("""
        CREATE TABLE AccountBalances (
            Id INT IDENTITY(1,1) PRIMARY KEY,
            AccountId INT NOT NULL UNIQUE,
            DebitBalance DECIMAL(18,2) DEFAULT 0,
            CreditBalance DECIMAL(18,2) DEFAULT 0,
            NetBalance DECIMAL(18,2) DEFAULT 0,
            LastTransactionDate DATETIME2 NULL,
            LastTransactionId BIGINT NULL,
            UpdatedAt DATETIME2 DEFAULT GETDATE(),
            UpdatedBy NVARCHAR(100) NULL,
            FOREIGN KEY (AccountId) REFERENCES ChartOfAccounts(Id),
            FOREIGN KEY (LastTransactionId) REFERENCES Transactions(TransactionId)
        )
        """)
        
        print("✅ تم إنشاء جداول النظام المحاسبي")
        
        # 4. تحديث شجرة الحسابات بالأكواد الرقمية
        print("🌳 تحديث شجرة الحسابات...")
        
        # إضافة عمود الكود الرقمي إذا لم يكن موجوداً
        try:
            cursor.execute("ALTER TABLE ChartOfAccounts ADD AccountCodeNumeric NVARCHAR(20) NULL")
        except:
            pass  # العمود موجود بالفعل
        
        # تحديث الأكواد الرقمية للحسابات الرئيسية
        main_accounts = [
            ('CASH', '1000'),
            ('BANK', '1100'),
            ('INVENTORY', '1200'),
            ('EQUIPMENT', '1300'),
            ('CUSTOMERS', '1400'),
            ('ACCOUNTS_PAYABLE', '2000'),
            ('SUPPLIERS', '2100'),
            ('CAPITAL', '3000'),
            ('SALES', '4000'),
            ('COGS', '5000'),
            ('EXPENSES', '6000')
        ]
        
        for account_code, numeric_code in main_accounts:
            cursor.execute("""
            UPDATE ChartOfAccounts 
            SET AccountCodeNumeric = ? 
            WHERE AccountCode = ?
            """, numeric_code, account_code)
        
        print("✅ تم تحديث الأكواد الرقمية للحسابات الرئيسية")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النظام المحاسبي: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    create_accounting_system()
