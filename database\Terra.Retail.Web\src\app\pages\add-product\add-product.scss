.add-product-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .title-section {
        h1 {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 8px 0;
          color: #1976d2;
          font-size: 2rem;
          font-weight: 600;

          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }

        p {
          margin: 0;
          color: #666;
          font-size: 1rem;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .form-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .form-section {
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    mat-card-header {
      margin-bottom: 16px;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #1976d2;
        font-size: 1.2rem;
        font-weight: 600;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    .flex-1 {
      flex: 1;
    }

    .flex-2 {
      flex: 2;
    }

    .full-width {
      width: 100%;
    }
  }

  .generate-code-btn {
    height: 56px;
    min-width: 140px;
    margin-top: 0;
    align-self: flex-end;

    .spinning {
      animation: spin 1s linear infinite;
    }
  }

  .product-type-option {
    display: flex;
    flex-direction: column;
    gap: 4px;

    strong {
      font-size: 1rem;
      color: #333;
    }

    small {
      font-size: 0.8rem;
      color: #666;
      line-height: 1.2;
    }
  }

  .additional-suppliers {
    margin-top: 16px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 1rem;
      font-weight: 500;
    }

    .suppliers-chips {
      margin-bottom: 16px;

      mat-chip-set {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      mat-chip {
        background-color: #e3f2fd;
        color: #1976d2;

        mat-icon {
          color: #1976d2;
        }
      }
    }
  }

  .settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    mat-checkbox {
      font-size: 0.9rem;

      ::ng-deep .mat-mdc-checkbox-label {
        color: #333;
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .loading-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #1976d2;
      margin-bottom: 16px;
      animation: spin 2s linear infinite;
    }

    p {
      margin: 0;
      font-size: 1.1rem;
      color: #333;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error styles
::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

::ng-deep .success-snackbar {
  background-color: #4CAF50 !important;
  color: white !important;
}

// Form field improvements
mat-form-field {
  width: 100%;

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .add-product-container {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .form-row {
      flex-direction: column;
      gap: 12px;

      .flex-1,
      .flex-2 {
        flex: none;
        width: 100%;
      }
    }

    .generate-code-btn {
      align-self: stretch;
      margin-top: 8px;
    }

    .settings-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 480px) {
  .add-product-container {
    padding: 12px;

    .page-header .title-section h1 {
      font-size: 1.5rem;
    }

    .form-section mat-card-header mat-card-title {
      font-size: 1rem;
    }
  }
}
