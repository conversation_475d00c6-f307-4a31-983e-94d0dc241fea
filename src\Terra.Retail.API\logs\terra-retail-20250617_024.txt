2025-06-17 04:35:48.495 +03:00 [ERR] خطأ في إنشاء قاعدة البيانات
System.InvalidOperationException: Unable to determine the relationship represented by navigation 'Purchase.ConfirmedBy' of type 'User'. Either manually configure the relationship, or ignore this property using the '[NotMapped]' attribute or by using 'EntityTypeBuilder.Ignore' in 'OnModelCreating'.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidatePropertyMapping>g__Validate|7_0(IConventionTypeBase typeBase, <>c__DisplayClass7_0&)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidatePropertyMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in X:\barmaga\Angler\Erp 2\src\Terra.Retail.API\Program.cs:line 53
2025-06-17 04:35:48.736 +03:00 [INF] تم بدء تشغيل Terra Retail ERP API
2025-06-17 04:36:15.402 +03:00 [WRN] Failed to determine the https port for redirect.
2025-06-17 04:36:16.539 +03:00 [ERR] خطأ في استرجاع العملاء
System.InvalidOperationException: Unable to determine the relationship represented by navigation 'Purchase.ConfirmedBy' of type 'User'. Either manually configure the relationship, or ignore this property using the '[NotMapped]' attribute or by using 'EntityTypeBuilder.Ignore' in 'OnModelCreating'.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidatePropertyMapping>g__Validate|7_0(IConventionTypeBase typeBase, <>c__DisplayClass7_0&)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidatePropertyMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_Model()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityType()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.CheckState()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityQueryable()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.System.Linq.IQueryable.get_Provider()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.Include[TEntity,TProperty](IQueryable`1 source, Expression`1 navigationPropertyPath)
   at Terra.Retail.API.Controllers.CustomersController.GetCustomers() in X:\barmaga\Angler\Erp 2\src\Terra.Retail.API\Controllers\CustomersController.cs:line 34
[2025-06-17 04:37:50.150 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.217 +03:00 WRN] No store type was specified for the decimal property 'MaxBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.223 +03:00 WRN] No store type was specified for the decimal property 'MinBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.231 +03:00 WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.236 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.240 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'Account'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.248 +03:00 WRN] No store type was specified for the decimal property 'AllocatedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.254 +03:00 WRN] No store type was specified for the decimal property 'RemainingBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.259 +03:00 WRN] No store type was specified for the decimal property 'UsedBudget' on entity type 'CostCenter'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.267 +03:00 WRN] No store type was specified for the decimal property 'AnnualBudget' on entity type 'Department'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.272 +03:00 WRN] No store type was specified for the decimal property 'AnnualLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.276 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.284 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.291 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.299 +03:00 WRN] No store type was specified for the decimal property 'SickLeaveBalance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.304 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'Employee'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.315 +03:00 WRN] No store type was specified for the decimal property 'SalaryDeduction' on entity type 'EmployeeLeave'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.331 +03:00 WRN] No store type was specified for the decimal property 'AllocatedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.340 +03:00 WRN] No store type was specified for the decimal property 'CarriedForwardDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.353 +03:00 WRN] No store type was specified for the decimal property 'RemainingDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.363 +03:00 WRN] No store type was specified for the decimal property 'UsedDays' on entity type 'EmployeeLeaveBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.371 +03:00 WRN] No store type was specified for the decimal property 'TotalCredit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.384 +03:00 WRN] No store type was specified for the decimal property 'TotalDebit' on entity type 'JournalEntry'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.391 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.400 +03:00 WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.407 +03:00 WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.426 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'JournalEntryItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.441 +03:00 WRN] No store type was specified for the decimal property 'SalaryImpactPercentage' on entity type 'LeaveType'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.451 +03:00 WRN] No store type was specified for the decimal property 'NetSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.456 +03:00 WRN] No store type was specified for the decimal property 'TotalAllowances' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.465 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.472 +03:00 WRN] No store type was specified for the decimal property 'TotalOvertime' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.480 +03:00 WRN] No store type was specified for the decimal property 'TotalSalaries' on entity type 'Payroll'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.486 +03:00 WRN] No store type was specified for the decimal property 'AbsenceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.492 +03:00 WRN] No store type was specified for the decimal property 'BasicSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.501 +03:00 WRN] No store type was specified for the decimal property 'Bonuses' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.507 +03:00 WRN] No store type was specified for the decimal property 'Commissions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.515 +03:00 WRN] No store type was specified for the decimal property 'HousingAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.565 +03:00 WRN] No store type was specified for the decimal property 'IncomeTaxDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.576 +03:00 WRN] No store type was specified for the decimal property 'LateDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.590 +03:00 WRN] No store type was specified for the decimal property 'LateHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.598 +03:00 WRN] No store type was specified for the decimal property 'NetSalary' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.604 +03:00 WRN] No store type was specified for the decimal property 'OtherAllowances' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.610 +03:00 WRN] No store type was specified for the decimal property 'OtherDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.617 +03:00 WRN] No store type was specified for the decimal property 'OvertimeAmount' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.623 +03:00 WRN] No store type was specified for the decimal property 'OvertimeHours' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.631 +03:00 WRN] No store type was specified for the decimal property 'SocialInsuranceDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.637 +03:00 WRN] No store type was specified for the decimal property 'TotalDeductions' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.643 +03:00 WRN] No store type was specified for the decimal property 'TotalEarnings' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.653 +03:00 WRN] No store type was specified for the decimal property 'TransportationAllowance' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.659 +03:00 WRN] No store type was specified for the decimal property 'UnpaidLeaveDeduction' on entity type 'PayrollItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.666 +03:00 WRN] No store type was specified for the decimal property 'MaxSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.672 +03:00 WRN] No store type was specified for the decimal property 'MinSalary' on entity type 'Position'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.680 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.686 +03:00 WRN] No store type was specified for the decimal property 'LastPurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.693 +03:00 WRN] No store type was specified for the decimal property 'MinOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.701 +03:00 WRN] No store type was specified for the decimal property 'PreferredOrderQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.706 +03:00 WRN] No store type was specified for the decimal property 'PurchasePrice' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.716 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.723 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchaseValue' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.734 +03:00 WRN] No store type was specified for the decimal property 'TotalPurchasedQuantity' on entity type 'ProductSupplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.740 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCharges' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.748 +03:00 WRN] No store type was specified for the decimal property 'BaseCurrencyTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.754 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.760 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.768 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.774 +03:00 WRN] No store type was specified for the decimal property 'PaidAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.782 +03:00 WRN] No store type was specified for the decimal property 'RemainingAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.788 +03:00 WRN] No store type was specified for the decimal property 'SubTotal' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.798 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.804 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.810 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'Purchase'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.818 +03:00 WRN] No store type was specified for the decimal property 'ActualWeight' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.824 +03:00 WRN] No store type was specified for the decimal property 'AdditionalCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.834 +03:00 WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.840 +03:00 WRN] No store type was specified for the decimal property 'DiscountPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.848 +03:00 WRN] No store type was specified for the decimal property 'FinalTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.853 +03:00 WRN] No store type was specified for the decimal property 'FinalUnitCost' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.861 +03:00 WRN] No store type was specified for the decimal property 'LineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.869 +03:00 WRN] No store type was specified for the decimal property 'NetLineTotal' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.875 +03:00 WRN] No store type was specified for the decimal property 'NetUnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.884 +03:00 WRN] No store type was specified for the decimal property 'OrderedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.890 +03:00 WRN] No store type was specified for the decimal property 'ReceivedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.898 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.904 +03:00 WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.909 +03:00 WRN] No store type was specified for the decimal property 'TaxPercentage' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.918 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.924 +03:00 WRN] No store type was specified for the decimal property 'Amount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.937 +03:00 WRN] No store type was specified for the decimal property 'BaseAmount' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.943 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'PurchasePayment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.952 +03:00 WRN] No store type was specified for the decimal property 'RefundedAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.958 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturn'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.967 +03:00 WRN] No store type was specified for the decimal property 'ReturnedQuantity' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.973 +03:00 WRN] No store type was specified for the decimal property 'TotalAmount' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.987 +03:00 WRN] No store type was specified for the decimal property 'UnitPrice' on entity type 'PurchaseReturnItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:50.993 +03:00 WRN] No store type was specified for the decimal property 'Duration' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.001 +03:00 WRN] No store type was specified for the decimal property 'MaxWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.007 +03:00 WRN] No store type was specified for the decimal property 'MinWorkHours' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.016 +03:00 WRN] No store type was specified for the decimal property 'OvertimeMultiplier' on entity type 'Shift'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.021 +03:00 WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.027 +03:00 WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.036 +03:00 WRN] No store type was specified for the decimal property 'Rating' on entity type 'Supplier'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.042 +03:00 WRN] No store type was specified for the decimal property 'CustomerServiceRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.050 +03:00 WRN] No store type was specified for the decimal property 'DeliveryTimeRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.056 +03:00 WRN] No store type was specified for the decimal property 'FlexibilityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.064 +03:00 WRN] No store type was specified for the decimal property 'OverallRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.070 +03:00 WRN] No store type was specified for the decimal property 'PricingRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:51.076 +03:00 WRN] No store type was specified for the decimal property 'ProductQualityRating' on entity type 'SupplierEvaluation'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'. {"EventId":{"Id":30000,"Name":"Microsoft.EntityFrameworkCore.Model.Validation.DecimalTypeDefaultWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Model.Validation"}
[2025-06-17 04:37:52.628 +03:00 INF] تم إنشاء قاعدة البيانات بنجاح {}
[2025-06-17 04:37:52.723 +03:00 INF] تم بدء تشغيل Terra Retail ERP API {}
[2025-06-17 04:38:12.711 +03:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNDD7UFN84OC:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OC"}
[2025-06-17 04:38:13.098 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OC:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OC"}
[2025-06-17 04:47:05.782 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000004","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:47:23.507 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:47:41.927 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:0000000A","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:47:41.941 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84OK:0000000A","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84OK"}
[2025-06-17 04:48:00.826 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84OQ:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84OQ"}
[2025-06-17 04:48:00.840 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000010","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:48:15.594 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000015","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:48:15.607 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000016","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:48:25.398 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OR:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OR"}
[2025-06-17 04:50:00.041 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OQ:00000007","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OQ"}
[2025-06-17 04:50:00.043 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84OK:0000000D","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84OK"}
[2025-06-17 04:50:26.029 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OS:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OS"}
[2025-06-17 04:50:46.244 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84OK:00000012","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84OK"}
[2025-06-17 04:50:46.250 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84OJ:00000019","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84OJ"}
[2025-06-17 04:58:24.762 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:0000000A","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:58:24.808 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:0000000B","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:58:34.584 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P5:0000000F","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P5"}
[2025-06-17 04:58:34.586 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000012","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:58:51.726 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000016","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:58:51.751 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000017","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:59:11.142 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:00000017","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 04:59:11.147 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000020","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:59:26.696 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000024","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:59:26.696 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000001A","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 04:59:44.553 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000027","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 04:59:44.562 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P5:00000022","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P5"}
[2025-06-17 05:00:27.387 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:0000002E","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 05:00:27.397 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000001D","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:00:46.144 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000030","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 05:00:46.155 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:00000023","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:01:02.369 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000002A","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:01:02.387 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000002B","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:01:18.001 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000002F","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:01:18.024 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:00000030","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:01:35.518 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000039","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 05:01:35.518 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P5:0000002F","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P5"}
[2025-06-17 05:01:54.196 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:0000003C","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 05:01:54.202 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P3:0000003A","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P3"}
[2025-06-17 05:02:12.223 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84P4:00000040","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84P4"}
[2025-06-17 05:02:12.223 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84P5:00000035","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84P5"}
[2025-06-17 05:47:42.318 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PG:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PG"}
[2025-06-17 05:47:42.326 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PF:00000003","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PF"}
[2025-06-17 06:37:50.635 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PK:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PK"}
[2025-06-17 06:37:50.640 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PI:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PI"}
[2025-06-17 06:39:17.319 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PN:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PN"}
[2025-06-17 06:39:17.321 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PL:00000001","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PL"}
[2025-06-17 06:39:19.623 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PL:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PL"}
[2025-06-17 06:39:19.623 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PM:00000002","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PM"}
[2025-06-17 06:41:18.023 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:00000003","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:41:18.026 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PS:00000002","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PS"}
[2025-06-17 06:41:38.999 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:00000009","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:41:39.101 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PO:00000009","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PO"}
[2025-06-17 06:41:58.219 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:0000000B","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:41:58.225 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PS:00000005","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PS"}
[2025-06-17 06:42:18.873 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PS:0000000B","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PS"}
[2025-06-17 06:42:18.896 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PS:0000000C","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PS"}
[2025-06-17 06:42:31.609 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PM:00000011","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PM"}
[2025-06-17 06:42:31.609 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PT:00000001","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PT"}
[2025-06-17 06:42:32.711 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PM:00000012","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PM"}
[2025-06-17 06:42:32.713 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PL:0000000E","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PL"}
[2025-06-17 06:42:35.535 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:00000014","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:42:35.575 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:00000015","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:42:56.782 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:0000001C","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:42:56.813 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PR:0000001E","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PR"}
[2025-06-17 06:43:14.930 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PO:00000015","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PO"}
[2025-06-17 06:43:14.950 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PO:00000017","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PO"}
[2025-06-17 06:43:30.301 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PS:00000018","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PS"}
[2025-06-17 06:43:30.301 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PO:00000019","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PO"}
[2025-06-17 06:45:31.927 +03:00 INF] تم استرجاع 26 محافظة {"SourceContext":"Terra.Retail.API.Controllers.AreasController","ActionId":"10c10847-9ba4-4b66-aa91-624b30a64671","ActionName":"Terra.Retail.API.Controllers.AreasController.GetAreas (Terra.Retail.API)","RequestId":"0HNDD7UFN84PM:0000001B","RequestPath":"/api/areas","ConnectionId":"0HNDD7UFN84PM"}
[2025-06-17 06:45:31.931 +03:00 INF] تم استرجاع 24 عميل {"SourceContext":"Terra.Retail.API.Controllers.CustomersController","ActionId":"32661cd7-de54-4d25-a68a-f11b3dc10ad5","ActionName":"Terra.Retail.API.Controllers.CustomersController.GetCustomers (Terra.Retail.API)","RequestId":"0HNDD7UFN84PT:0000000C","RequestPath":"/api/customers","ConnectionId":"0HNDD7UFN84PT"}
