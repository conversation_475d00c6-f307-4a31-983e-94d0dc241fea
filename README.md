# 🏪 Terra Retail ERP System

نظام إدارة متكامل للمتاجر والشركات التجارية

## 📋 نظرة عامة

Terra Retail ERP هو نظام إدارة متكامل مصمم خصيصاً للمتاجر والشركات التجارية في المنطقة العربية. يوفر النظام جميع الأدوات اللازمة لإدارة العمليات التجارية بكفاءة عالية.

## 🚀 المميزات الرئيسية

### 📊 الوحدات الأساسية
- **إدارة العملاء** - نظام شامل لإدارة بيانات العملاء والحسابات
- **إدارة المنتجات** - كتالوج منتجات متقدم مع نظام فئات هرمي
- **نقطة البيع (POS)** - واجهة بيع سريعة ومرنة
- **إدارة المخزون** - تتبع دقيق للمخزون مع تنبيهات ذكية
- **المبيعات والمشتريات** - نظام فواتير متكامل
- **إدارة الموظفين** - نظام HR شامل مع الحضور والانصراف
- **النظام المالي** - محاسبة متكاملة مع القوائم المالية
- **التقارير** - تقارير شاملة وقابلة للتخصيص

### 🛠️ المميزات التقنية
- **واجهة مستجيبة** - تعمل على جميع الأجهزة
- **دعم متعدد اللغات** - العربية والإنجليزية
- **نظام صلاحيات متقدم** - تحكم دقيق في الوصول
- **أمان عالي** - حماية شاملة للبيانات
- **أداء عالي** - استجابة سريعة وموثوقة

## 🏗️ التقنيات المستخدمة

### Backend
- **ASP.NET Core 8.0** - Web API
- **Entity Framework Core** - ORM
- **SQL Server** - قاعدة البيانات
- **JWT Authentication** - المصادقة
- **AutoMapper** - Object Mapping

### Frontend
- **Angular 17+** - Frontend Framework
- **Angular Material** - UI Components
- **RxJS** - Reactive Programming
- **Chart.js** - الرسوم البيانية
- **Bootstrap** - CSS Framework

## 📁 هيكل المشروع

```
Terra.Retail.ERP/
├── src/
│   ├── Terra.Retail.API/          # Web API
│   ├── Terra.Retail.Core/         # Business Logic
│   ├── Terra.Retail.Infrastructure/ # Data Access
│   ├── Terra.Retail.Shared/       # Shared Models
│   └── Terra.Retail.Web/          # Angular Frontend
├── docs/                          # Documentation
├── scripts/                       # Database Scripts
└── tests/                         # Unit Tests
```

## 🚀 البدء السريع

### متطلبات النظام
- .NET 8.0 SDK
- Node.js 18+
- SQL Server 2019+
- Visual Studio 2022 أو VS Code

### إعداد قاعدة البيانات
```bash
# تحديث قاعدة البيانات
dotnet ef database update --project Terra.Retail.Infrastructure
```

### تشغيل Backend
```bash
cd src/Terra.Retail.API
dotnet run
```

### تشغيل Frontend
```bash
cd src/Terra.Retail.Web
npm install
ng serve
```

## 📖 الوثائق

- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api-docs.md)
- [Database Schema](docs/database-schema.md)

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام. يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://terraretail.com
- **الدعم الفني**: https://support.terraretail.com

---

**تم التطوير بواسطة فريق Terra Retail** 🚀
