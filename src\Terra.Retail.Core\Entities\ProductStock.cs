using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مخزون المنتجات حسب الفرع
    /// </summary>
    public class ProductStock : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// الكمية المتاحة
        /// </summary>
        public decimal AvailableQuantity { get; set; } = 0;

        /// <summary>
        /// الكمية المحجوزة
        /// </summary>
        public decimal ReservedQuantity { get; set; } = 0;

        /// <summary>
        /// الكمية المطلوبة
        /// </summary>
        public decimal OnOrderQuantity { get; set; } = 0;

        /// <summary>
        /// الكمية الافتتاحية
        /// </summary>
        public decimal OpeningQuantity { get; set; } = 0;

        /// <summary>
        /// إجمالي الكمية الداخلة
        /// </summary>
        public decimal TotalInQuantity { get; set; } = 0;

        /// <summary>
        /// إجمالي الكمية الخارجة
        /// </summary>
        public decimal TotalOutQuantity { get; set; } = 0;

        /// <summary>
        /// متوسط سعر التكلفة
        /// </summary>
        public decimal AverageCostPrice { get; set; } = 0;

        /// <summary>
        /// آخر سعر تكلفة
        /// </summary>
        public decimal LastCostPrice { get; set; } = 0;

        /// <summary>
        /// قيمة المخزون (بسعر التكلفة)
        /// </summary>
        public decimal StockValue { get; set; } = 0;

        /// <summary>
        /// الحد الأدنى للمخزون (خاص بالفرع)
        /// </summary>
        public decimal? BranchMinStock { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون (خاص بالفرع)
        /// </summary>
        public decimal? BranchMaxStock { get; set; }

        /// <summary>
        /// نقطة إعادة الطلب (خاصة بالفرع)
        /// </summary>
        public decimal? BranchReorderPoint { get; set; }

        /// <summary>
        /// تاريخ آخر حركة مخزون
        /// </summary>
        public DateTime? LastMovementDate { get; set; }

        /// <summary>
        /// تاريخ آخر جرد
        /// </summary>
        public DateTime? LastStockTakingDate { get; set; }

        /// <summary>
        /// موقع التخزين في المستودع
        /// </summary>
        [MaxLength(100)]
        public string? StorageLocation { get; set; }

        /// <summary>
        /// رقم الرف
        /// </summary>
        [MaxLength(20)]
        public string? ShelfNumber { get; set; }

        /// <summary>
        /// هل المنتج متاح للبيع في هذا الفرع
        /// </summary>
        public bool IsAvailableForSale { get; set; } = true;

        /// <summary>
        /// ملاحظات خاصة بالمخزون
        /// </summary>
        [MaxLength(500)]
        public string? StockNotes { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<ProductBatch> Batches { get; set; } = new List<ProductBatch>();
    }
}
