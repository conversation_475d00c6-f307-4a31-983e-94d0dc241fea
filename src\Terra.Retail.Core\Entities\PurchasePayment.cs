using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مدفوعات فواتير المشتريات
    /// </summary>
    public class PurchasePayment : BaseEntity
    {
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int PurchaseId { get; set; }

        /// <summary>
        /// معرف طريقة الدفع
        /// </summary>
        public int PaymentMethodId { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string PaymentNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// رقم مرجعي (رقم الشيك، رقم التحويل، إلخ)
        /// </summary>
        [MaxLength(100)]
        public string? ReferenceNumber { get; set; }

        /// <summary>
        /// تاريخ الاستحقاق (للشيكات)
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// اسم البنك (للشيكات والتحويلات)
        /// </summary>
        [MaxLength(100)]
        public string? BankName { get; set; }

        /// <summary>
        /// رقم الحساب
        /// </summary>
        [MaxLength(50)]
        public string? AccountNumber { get; set; }

        /// <summary>
        /// حالة الدفع
        /// </summary>
        public PaymentStatus Status { get; set; } = PaymentStatus.Completed;

        /// <summary>
        /// ملاحظات الدفع
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// معرف المستخدم الذي دفع
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// معرف الخزينة (للنقدي)
        /// </summary>
        public int? CashBoxId { get; set; }

        /// <summary>
        /// رقم الإيصال
        /// </summary>
        [MaxLength(20)]
        public string? ReceiptNumber { get; set; }

        /// <summary>
        /// هل الدفع مؤكد
        /// </summary>
        public bool IsConfirmed { get; set; } = true;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد الدفع
        /// </summary>
        public int? ConfirmedById { get; set; }

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// سعر الصرف
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// المبلغ بالعملة الأساسية
        /// </summary>
        public decimal BaseAmount { get; set; }

        // Navigation Properties
        public virtual Purchase Purchase { get; set; } = null!;
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
        public virtual CashBox? CashBox { get; set; }
    }
}
