import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Customer {
  id: number;
  customerCode: string;
  nameAr: string;
  nameEn: string;
  phone1: string;
  phone2?: string;
  email: string;
  address: string;
  customerTypeId: number;
  priceCategoryId: number;
  currentBalance: number;
  isActive: boolean;
  customerTypeName?: string;
  priceCategoryName?: string;
}

interface CustomerType {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface PriceCategory {
  id: number;
  nameAr: string;
  nameEn: string;
}

@Component({
  selector: 'app-customers-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './customers-list.component.html',
  styleUrls: ['./customers-list.component.scss']
})
export class CustomersListComponent implements OnInit {
  customers: Customer[] = [];
  filteredCustomers: Customer[] = [];
  customerTypes: CustomerType[] = [];
  priceCategories: PriceCategory[] = [];
  
  // Search and Filter
  searchByCode: string = '';
  searchByOther: string = '';
  selectedCustomerType: string = '';
  selectedStatus: string = '';
  
  // Statistics
  totalCustomers: number = 0;
  activeCustomers: number = 0;
  totalBalance: number = 0;
  
  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 0;
  
  // Loading
  isLoading: boolean = false;
  
  private apiUrl = 'http://localhost:5233/api';

  constructor(private http: HttpClient, private router: Router) {}

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    this.isLoading = true;
    
    // Load customers
    this.loadCustomers();
    
    // Load customer types
    this.loadCustomerTypes();
    
    // Load price categories
    this.loadPriceCategories();
  }

  loadCustomers() {
    this.http.get<any>(`${this.apiUrl}/customers`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customers = response.data;
          this.filteredCustomers = [...this.customers];
          this.calculateStatistics();
          this.updatePagination();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading customers:', error);
        this.isLoading = false;
        // Load demo data if API fails
        this.loadDemoData();
      }
    });
  }

  loadCustomerTypes() {
    this.http.get<any>(`${this.apiUrl}/customer-types`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.customerTypes = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading customer types:', error);
        // Fallback data
        this.customerTypes = [
          { id: 1, nameAr: 'عميل تجزئة', nameEn: 'Retail Customer' },
          { id: 2, nameAr: 'عميل جملة', nameEn: 'Wholesale Customer' },
          { id: 3, nameAr: 'عميل مؤسسي', nameEn: 'Corporate Customer' },
          { id: 4, nameAr: 'عميل VIP', nameEn: 'VIP Customer' }
        ];
      }
    });
  }

  loadPriceCategories() {
    this.http.get<any>(`${this.apiUrl}/price-categories`).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.priceCategories = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading price categories:', error);
        // Fallback data
        this.priceCategories = [
          { id: 1, nameAr: 'سعر التجزئة', nameEn: 'Retail Price' },
          { id: 2, nameAr: 'سعر الجملة', nameEn: 'Wholesale Price' },
          { id: 3, nameAr: 'سعر المؤسسات', nameEn: 'Corporate Price' },
          { id: 4, nameAr: 'سعر VIP', nameEn: 'VIP Price' }
        ];
      }
    });
  }

  loadDemoData() {
    // Demo data for testing
    this.customers = [
      {
        id: 1,
        customerCode: 'CUS000001',
        nameAr: 'أحمد محمد علي',
        nameEn: 'Ahmed Mohamed Ali',
        phone1: '01012345678',
        phone2: '02-25555555',
        email: '<EMAIL>',
        address: 'شارع النيل، المعادي، القاهرة',
        customerTypeId: 1,
        priceCategoryId: 1,
        currentBalance: 0,
        isActive: true,
        customerTypeName: 'عميل تجزئة',
        priceCategoryName: 'سعر التجزئة'
      },
      {
        id: 2,
        customerCode: 'CUS000002',
        nameAr: 'فاطمة أحمد حسن',
        nameEn: 'Fatma Ahmed Hassan',
        phone1: '01098765432',
        phone2: '02-25444444',
        email: '<EMAIL>',
        address: 'شارع التحرير، وسط البلد، القاهرة',
        customerTypeId: 2,
        priceCategoryId: 2,
        currentBalance: 1500,
        isActive: true,
        customerTypeName: 'عميل جملة',
        priceCategoryName: 'سعر الجملة'
      },
      {
        id: 3,
        customerCode: 'CUS000003',
        nameAr: 'محمد علي أحمد',
        nameEn: 'Mohamed Ali Ahmed',
        phone1: '01055555555',
        email: '<EMAIL>',
        address: 'شارع الهرم، الجيزة',
        customerTypeId: 1,
        priceCategoryId: 1,
        currentBalance: -500,
        isActive: true,
        customerTypeName: 'عميل تجزئة',
        priceCategoryName: 'سعر التجزئة'
      }
    ];
    
    this.filteredCustomers = [...this.customers];
    this.calculateStatistics();
    this.updatePagination();
  }

  calculateStatistics() {
    this.totalCustomers = this.customers.length;
    this.activeCustomers = this.customers.filter(c => c.isActive).length;
    this.totalBalance = this.customers.reduce((sum, c) => sum + c.currentBalance, 0);
  }

  onSearch() {
    this.applyFilters();
  }

  onFilterChange() {
    this.applyFilters();
  }

  applyFilters() {
    this.filteredCustomers = this.customers.filter(customer => {
      // البحث بالكود منفصل
      const matchesCode = !this.searchByCode ||
        customer.customerCode.toLowerCase().includes(this.searchByCode.toLowerCase());

      // البحث بالباقي منفصل
      const searchOtherLower = this.searchByOther.toLowerCase();
      const matchesOther = !this.searchByOther ||
        customer.nameAr.toLowerCase().includes(searchOtherLower) ||
        customer.nameEn.toLowerCase().includes(searchOtherLower) ||
        customer.phone1.includes(this.searchByOther) ||
        (customer.phone2 && customer.phone2.includes(this.searchByOther)) ||
        (customer.email && customer.email.toLowerCase().includes(searchOtherLower));

      const matchesType = !this.selectedCustomerType ||
        customer.customerTypeId.toString() === this.selectedCustomerType;

      const matchesStatus = !this.selectedStatus ||
        customer.isActive.toString() === this.selectedStatus;

      return matchesCode && matchesOther && matchesType && matchesStatus;
    });

    this.currentPage = 1;
    this.updatePagination();
  }

  updatePagination() {
    this.totalPages = Math.ceil(this.filteredCustomers.length / this.pageSize);
  }

  getPageNumbers(): number[] {
    const pages = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  trackByCustomerId(index: number, customer: Customer): number {
    return customer.id;
  }

  // Actions
  openAddCustomerModal() {
    this.router.navigate(['/customers/new']);
  }

  viewCustomer(customer: Customer) {
    console.log('View customer:', customer);
    // TODO: Navigate to customer details
  }

  editCustomer(customer: Customer) {
    this.router.navigate(['/customers/edit', customer.id]);
  }

  deleteCustomer(customer: Customer) {
    if (confirm(`هل أنت متأكد من حذف العميل ${customer.nameAr}؟`)) {
      console.log('Delete customer:', customer);
      // TODO: Implement delete customer
    }
  }

  viewCustomerAccount(customer: Customer) {
    console.log('View customer account:', customer);
    // TODO: Navigate to customer account statement
  }

  exportToExcel() {
    console.log('Export to Excel');
    // TODO: Implement Excel export
  }

  // Utility
  Math = Math;
}
