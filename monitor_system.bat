@echo off
title Terra Retail ERP - System Monitor
color 0B
chcp 65001 >nul

:MONITOR_LOOP
cls
echo.
echo ========================================
echo    🔍 Terra Retail ERP - System Monitor
echo    مراقب نظام Terra Retail ERP
echo ========================================
echo.
echo 📊 System Status at %date% %time%
echo حالة النظام في %date% %time%
echo.

echo 🔍 Checking API Server...
echo فحص خادم API...
curl -s http://localhost:5000/health >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ API Server: RUNNING on http://localhost:5000
    echo ✅ خادم API: يعمل على http://localhost:5000
) else (
    echo ❌ API Server: NOT RESPONDING
    echo ❌ خادم API: لا يستجيب
)

echo.
echo 🔍 Checking Angular App...
echo فحص تطبيق Angular...
curl -s http://localhost:4200 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Angular App: RUNNING on http://localhost:4200
    echo ✅ تطبيق Angular: يعمل على http://localhost:4200
) else (
    echo ❌ Angular App: NOT RESPONDING
    echo ❌ تطبيق Angular: لا يستجيب
)

echo.
echo 🔍 Checking Database...
echo فحص قاعدة البيانات...
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "SELECT COUNT(*) as Tables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h -1 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Database: CONNECTED
    echo ✅ قاعدة البيانات: متصلة
) else (
    echo ❌ Database: CONNECTION FAILED
    echo ❌ قاعدة البيانات: فشل الاتصال
)

echo.
echo 📈 Quick Stats:
echo إحصائيات سريعة:
echo.

for /f "tokens=*" %%i in ('curl -s http://localhost:5000/api/system/statistics 2^>nul ^| findstr "Active"') do (
    echo %%i
)

echo.
echo ========================================
echo 🎮 Controls | التحكم:
echo ========================================
echo.
echo [R] Refresh | تحديث
echo [A] Open Angular App | فتح تطبيق Angular  
echo [S] Open Swagger | فتح Swagger
echo [H] Open Health Check | فتح فحص الصحة
echo [Q] Quit | خروج
echo.

choice /c RASHQ /n /m "Select option | اختر خيار: "

if errorlevel 5 goto END
if errorlevel 4 (
    start http://localhost:5000/health
    goto MONITOR_LOOP
)
if errorlevel 3 (
    start http://localhost:5000/swagger
    goto MONITOR_LOOP
)
if errorlevel 2 (
    start http://localhost:4200
    goto MONITOR_LOOP
)
if errorlevel 1 goto MONITOR_LOOP

:END
echo.
echo 👋 Goodbye! | وداعاً!
echo.
pause
exit
