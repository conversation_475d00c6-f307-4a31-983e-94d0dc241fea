{"ast": null, "code": "import { Login } from './auth/login/login';\nimport { DashboardNewComponent } from './dashboard/dashboard-new.component';\nimport { CustomersNewComponent } from './modules/customers/customers-new.component';\nimport { CustomersComponent } from './modules/customers/customers.component';\nimport { SuppliersNewComponent } from './modules/suppliers/suppliers-new.component';\nimport { AddSupplierComponent } from './modules/suppliers/add-supplier/add-supplier.component';\nimport { EditSupplierComponent } from './modules/suppliers/edit-supplier/edit-supplier.component';\nimport { SupplierDetailsComponent } from './modules/suppliers/supplier-details/supplier-details.component';\nimport { SupplierManagementComponent } from './modules/suppliers/supplier-management/supplier-management.component';\nimport { SupplierStatsComponent } from './modules/suppliers/supplier-stats/supplier-stats.component';\nimport { SupplierTypesComponent } from './modules/suppliers/supplier-types/supplier-types.component';\nimport { AreasComponent } from './modules/suppliers/areas/areas.component';\nimport { CountriesComponent } from './modules/suppliers/countries/countries.component';\nimport { Products } from './pages/products/products';\nimport { AddProduct } from './pages/add-product/add-product';\nimport { Pos } from './pages/pos/pos';\nexport const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: Login\n}, {\n  path: 'dashboard',\n  component: DashboardNewComponent\n}, {\n  path: 'pos',\n  component: Pos\n}, {\n  path: 'customers',\n  component: CustomersComponent\n}, {\n  path: 'customers-old',\n  component: CustomersNewComponent\n}, {\n  path: 'suppliers',\n  component: SuppliersNewComponent\n}, {\n  path: 'suppliers/add',\n  component: AddSupplierComponent\n}, {\n  path: 'suppliers/edit/:id',\n  component: EditSupplierComponent\n}, {\n  path: 'suppliers/details/:id',\n  component: SupplierDetailsComponent\n}, {\n  path: 'suppliers/management',\n  component: SupplierManagementComponent\n}, {\n  path: 'suppliers/stats',\n  component: SupplierStatsComponent\n}, {\n  path: 'suppliers/types',\n  component: SupplierTypesComponent\n}, {\n  path: 'suppliers/areas',\n  component: AreasComponent\n}, {\n  path: 'suppliers/countries',\n  component: CountriesComponent\n}, {\n  path: 'products',\n  component: Products\n}, {\n  path: 'add-product',\n  component: AddProduct\n}, {\n  path: 'sales',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: 'inventory',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: 'suppliers',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: 'purchases',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: 'reports',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: 'settings',\n  component: DashboardNewComponent\n},\n// Temporary\n{\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON>", "DashboardNewComponent", "CustomersNewComponent", "CustomersComponent", "SuppliersNewComponent", "AddSupplierComponent", "EditSupplierComponent", "SupplierDetailsComponent", "SupplierManagementComponent", "SupplierStatsComponent", "SupplierTypesComponent", "AreasComponent", "CountriesComponent", "Products", "AddProduct", "Pos", "routes", "path", "redirectTo", "pathMatch", "component"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { Login } from './auth/login/login';\r\nimport { DashboardNewComponent } from './dashboard/dashboard-new.component';\r\nimport { CustomersNewComponent } from './modules/customers/customers-new.component';\r\nimport { CustomersComponent } from './modules/customers/customers.component';\r\nimport { SuppliersNewComponent } from './modules/suppliers/suppliers-new.component';\r\nimport { AddSupplierComponent } from './modules/suppliers/add-supplier/add-supplier.component';\r\nimport { EditSupplierComponent } from './modules/suppliers/edit-supplier/edit-supplier.component';\r\nimport { SupplierDetailsComponent } from './modules/suppliers/supplier-details/supplier-details.component';\r\nimport { SupplierManagementComponent } from './modules/suppliers/supplier-management/supplier-management.component';\r\nimport { SupplierStatsComponent } from './modules/suppliers/supplier-stats/supplier-stats.component';\r\nimport { SupplierTypesComponent } from './modules/suppliers/supplier-types/supplier-types.component';\r\nimport { AreasComponent } from './modules/suppliers/areas/areas.component';\r\nimport { CountriesComponent } from './modules/suppliers/countries/countries.component';\r\nimport { Products } from './pages/products/products';\r\nimport { AddProduct } from './pages/add-product/add-product';\r\nimport { Pos } from './pages/pos/pos';\r\n\r\nexport const routes: Routes = [\r\n  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\r\n  { path: 'login', component: Login },\r\n  { path: 'dashboard', component: DashboardNewComponent },\r\n  { path: 'pos', component: Pos },\r\n  { path: 'customers', component: CustomersComponent },\r\n  { path: 'customers-old', component: CustomersNewComponent },\r\n  { path: 'suppliers', component: SuppliersNewComponent },\r\n  { path: 'suppliers/add', component: AddSupplierComponent },\r\n  { path: 'suppliers/edit/:id', component: EditSupplierComponent },\r\n  { path: 'suppliers/details/:id', component: SupplierDetailsComponent },\r\n  { path: 'suppliers/management', component: SupplierManagementComponent },\r\n  { path: 'suppliers/stats', component: SupplierStatsComponent },\r\n  { path: 'suppliers/types', component: SupplierTypesComponent },\r\n  { path: 'suppliers/areas', component: AreasComponent },\r\n  { path: 'suppliers/countries', component: CountriesComponent },\r\n  { path: 'products', component: Products },\r\n  { path: 'add-product', component: AddProduct },\r\n  { path: 'sales', component: DashboardNewComponent }, // Temporary\r\n  { path: 'inventory', component: DashboardNewComponent }, // Temporary\r\n  { path: 'suppliers', component: DashboardNewComponent }, // Temporary\r\n  { path: 'purchases', component: DashboardNewComponent }, // Temporary\r\n  { path: 'reports', component: DashboardNewComponent }, // Temporary\r\n  { path: 'settings', component: DashboardNewComponent }, // Temporary\r\n  { path: '**', redirectTo: '/dashboard' }\r\n];\r\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,oBAAoB,QAAQ,yDAAyD;AAC9F,SAASC,qBAAqB,QAAQ,2DAA2D;AACjG,SAASC,wBAAwB,QAAQ,iEAAiE;AAC1G,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,sBAAsB,QAAQ,6DAA6D;AACpG,SAASC,sBAAsB,QAAQ,6DAA6D;AACpG,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,GAAG,QAAQ,iBAAiB;AAErC,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,EACzD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEpB;AAAK,CAAE,EACnC;EAAEiB,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEnB;AAAqB,CAAE,EACvD;EAAEgB,IAAI,EAAE,KAAK;EAAEG,SAAS,EAAEL;AAAG,CAAE,EAC/B;EAAEE,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEjB;AAAkB,CAAE,EACpD;EAAEc,IAAI,EAAE,eAAe;EAAEG,SAAS,EAAElB;AAAqB,CAAE,EAC3D;EAAEe,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEhB;AAAqB,CAAE,EACvD;EAAEa,IAAI,EAAE,eAAe;EAAEG,SAAS,EAAEf;AAAoB,CAAE,EAC1D;EAAEY,IAAI,EAAE,oBAAoB;EAAEG,SAAS,EAAEd;AAAqB,CAAE,EAChE;EAAEW,IAAI,EAAE,uBAAuB;EAAEG,SAAS,EAAEb;AAAwB,CAAE,EACtE;EAAEU,IAAI,EAAE,sBAAsB;EAAEG,SAAS,EAAEZ;AAA2B,CAAE,EACxE;EAAES,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEX;AAAsB,CAAE,EAC9D;EAAEQ,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEV;AAAsB,CAAE,EAC9D;EAAEO,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAET;AAAc,CAAE,EACtD;EAAEM,IAAI,EAAE,qBAAqB;EAAEG,SAAS,EAAER;AAAkB,CAAE,EAC9D;EAAEK,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEP;AAAQ,CAAE,EACzC;EAAEI,IAAI,EAAE,aAAa;EAAEG,SAAS,EAAEN;AAAU,CAAE,EAC9C;EAAEG,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACrD;EAAEgB,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACzD;EAAEgB,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACzD;EAAEgB,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACzD;EAAEgB,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACvD;EAAEgB,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEnB;AAAqB,CAAE;AAAE;AACxD;EAAEgB,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAY,CAAE,CACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}