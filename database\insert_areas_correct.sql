USE TerraRetailERP;
GO

INSERT INTO Areas (NameAr, NameEn, Code, IsActive, DisplayOrder, CreatedAt) VALUES
(N'القاهرة', 'Cairo', 'CAI', 1, 1, GETUTCDATE()),
(N'الجيزة', '<PERSON><PERSON>', 'GIZ', 1, 2, GETUTCDATE()),
(N'الإسكندرية', 'Alexandria', 'ALX', 1, 3, GETUTCDATE()),
(N'الدقهلية', 'Dakahlia', 'DAK', 1, 4, GETUTCDATE()),
(N'الشرقية', 'Sharqia', 'SHR', 1, 5, GETUTCDATE()),
(N'القليوبية', 'Qalyubia', 'QLY', 1, 6, GETUTCDATE()),
(N'كفر الشيخ', 'Ka<PERSON>r El Sheikh', 'KFS', 1, 7, GETUTCDATE()),
(N'الغربية', 'Gharbia', 'GHR', 1, 8, GETUTCDATE()),
(N'المنوفية', 'Monufia', 'MNF', 1, 9, GETUTCDATE()),
(N'البحيرة', 'Beheira', 'BHR', 1, 10, GETUTCDATE()),
(N'الإسماعيلية', 'Ismailia', 'ISM', 1, 11, GETUTCDATE()),
(N'بورسعيد', 'Port Said', 'PTS', 1, 12, GETUTCDATE()),
(N'السويس', 'Suez', 'SUZ', 1, 13, GETUTCDATE()),
(N'شمال سيناء', 'North Sinai', 'NSI', 1, 14, GETUTCDATE()),
(N'جنوب سيناء', 'South Sinai', 'SSI', 1, 15, GETUTCDATE()),
(N'الفيوم', 'Fayoum', 'FYM', 1, 16, GETUTCDATE()),
(N'بني سويف', 'Beni Suef', 'BSF', 1, 17, GETUTCDATE()),
(N'المنيا', 'Minya', 'MNY', 1, 18, GETUTCDATE()),
(N'أسيوط', 'Assiut', 'AST', 1, 19, GETUTCDATE()),
(N'سوهاج', 'Sohag', 'SOH', 1, 20, GETUTCDATE()),
(N'قنا', 'Qena', 'QNA', 1, 21, GETUTCDATE()),
(N'الأقصر', 'Luxor', 'LXR', 1, 22, GETUTCDATE()),
(N'أسوان', 'Aswan', 'ASN', 1, 23, GETUTCDATE()),
(N'البحر الأحمر', 'Red Sea', 'RDS', 1, 24, GETUTCDATE()),
(N'الوادي الجديد', 'New Valley', 'NVL', 1, 25, GETUTCDATE()),
(N'مطروح', 'Matrouh', 'MTR', 1, 26, GETUTCDATE());

SELECT N'تم إنشاء المحافظات المصرية بنجاح' as Result, COUNT(*) as Count FROM Areas;
