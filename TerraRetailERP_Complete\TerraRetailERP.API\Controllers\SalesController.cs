using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("💰 Sales Management")]
    public class SalesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public SalesController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Sale>>> GetSales(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? customerId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Sales
                    .Include(s => s.Customer)
                    .Include(s => s.Branch)
                    .Include(s => s.User)
                    .Include(s => s.SaleItems)
                        .ThenInclude(si => si.Product)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(s => s.InvoiceNumber.Contains(search) || 
                                           s.CustomerName!.Contains(search) ||
                                           s.Customer!.NameAr.Contains(search));
                }

                if (customerId.HasValue)
                    query = query.Where(s => s.CustomerId == customerId);

                if (branchId.HasValue)
                    query = query.Where(s => s.BranchId == branchId);

                if (status.HasValue)
                    query = query.Where(s => s.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate);

                var totalCount = await query.CountAsync();
                var sales = await query
                    .OrderByDescending(s => s.InvoiceDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(s => new
                    {
                        s.Id,
                        s.InvoiceNumber,
                        s.InvoiceDate,
                        s.CustomerId,
                        CustomerName = s.Customer != null ? s.Customer.NameAr : s.CustomerName,
                        s.BranchId,
                        BranchName = s.Branch.NameAr,
                        s.Status,
                        s.SaleType,
                        s.SubTotal,
                        s.DiscountAmount,
                        s.TaxAmount,
                        s.TotalAmount,
                        s.PaidAmount,
                        s.RemainingAmount,
                        ItemsCount = s.SaleItems.Count,
                        s.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = sales,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Sale>> GetSale(int id)
        {
            try
            {
                var sale = await _context.Sales
                    .Include(s => s.Customer)
                    .Include(s => s.Branch)
                    .Include(s => s.User)
                    .Include(s => s.SaleItems)
                        .ThenInclude(si => si.Product)
                            .ThenInclude(p => p.Unit)
                    .Include(s => s.SalePayments)
                        .ThenInclude(sp => sp.PaymentMethod)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (sale == null)
                    return NotFound(new { message = "فاتورة المبيعات غير موجودة" });

                return Ok(sale);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Sale>> CreateSale(CreateSaleRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                // Generate invoice number
                var invoiceNumber = await GenerateInvoiceNumber(request.BranchId);

                // Calculate totals
                var subTotal = request.Items.Sum(i => i.Quantity * i.UnitPrice);
                var discountAmount = subTotal * (request.DiscountPercentage / 100);
                var taxableAmount = subTotal - discountAmount;
                var taxAmount = taxableAmount * (request.TaxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount;

                var sale = new Sale
                {
                    InvoiceNumber = invoiceNumber,
                    CustomerId = request.CustomerId,
                    CustomerName = request.CustomerName,
                    BranchId = request.BranchId,
                    UserId = userId.Value,
                    InvoiceDate = request.InvoiceDate ?? DateTime.Now,
                    DueDate = request.DueDate,
                    Status = 1, // Active
                    SaleType = request.SaleType,
                    SubTotal = subTotal,
                    DiscountPercentage = request.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TaxPercentage = request.TaxPercentage,
                    TaxAmount = taxAmount,
                    TotalAmount = totalAmount,
                    PaidAmount = 0,
                    RemainingAmount = totalAmount,
                    Notes = request.Notes,
                    TableNumber = request.TableNumber,
                    CreatedAt = DateTime.Now
                };

                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Add sale items
                int lineNumber = 1;
                foreach (var item in request.Items)
                {
                    var lineTotal = item.Quantity * item.UnitPrice;
                    var lineDiscountAmount = lineTotal * (item.DiscountPercentage / 100);
                    var netLineTotal = lineTotal - lineDiscountAmount;
                    var lineTaxAmount = netLineTotal * (item.TaxPercentage / 100);
                    var finalTotal = netLineTotal + lineTaxAmount;

                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        UnitCostPrice = item.UnitCostPrice,
                        DiscountPercentage = item.DiscountPercentage,
                        DiscountAmount = lineDiscountAmount,
                        NetUnitPrice = item.UnitPrice - (lineDiscountAmount / item.Quantity),
                        LineTotal = lineTotal,
                        NetLineTotal = netLineTotal,
                        TaxPercentage = item.TaxPercentage,
                        TaxAmount = lineTaxAmount,
                        FinalTotal = finalTotal,
                        PriceCategoryId = item.PriceCategoryId,
                        ItemNotes = item.ItemNotes,
                        BatchNumber = item.BatchNumber,
                        SerialNumber = item.SerialNumber,
                        ActualWeight = item.ActualWeight,
                        CreatedAt = DateTime.Now
                    };

                    _context.SaleItems.Add(saleItem);

                    // Update product stock
                    await UpdateProductStock(item.ProductId, request.BranchId, -item.Quantity, item.UnitCostPrice);

                    // Create stock movement
                    await CreateStockMovement(item.ProductId, request.BranchId, -item.Quantity, 
                        item.UnitCostPrice, "Sale", sale.Id, sale.InvoiceNumber, userId.Value);
                }

                // Add payments if provided
                if (request.Payments != null && request.Payments.Any())
                {
                    decimal totalPaid = 0;
                    foreach (var payment in request.Payments)
                    {
                        var paymentNumber = await GeneratePaymentNumber(request.BranchId);
                        
                        var salePayment = new SalePayment
                        {
                            SaleId = sale.Id,
                            PaymentNumber = paymentNumber,
                            PaymentMethodId = payment.PaymentMethodId,
                            Amount = payment.Amount,
                            ReceivedAmount = payment.ReceivedAmount,
                            ChangeAmount = payment.ChangeAmount,
                            PaymentDate = DateTime.Now,
                            Status = 1, // Completed
                            ReferenceNumber = payment.ReferenceNumber,
                            BankName = payment.BankName,
                            AccountNumber = payment.AccountNumber,
                            Notes = payment.Notes,
                            UserId = userId.Value,
                            Currency = "EGP",
                            ExchangeRate = 1,
                            CreatedAt = DateTime.Now
                        };

                        _context.SalePayments.Add(salePayment);
                        totalPaid += payment.Amount;
                    }

                    sale.PaidAmount = totalPaid;
                    sale.RemainingAmount = sale.TotalAmount - totalPaid;
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Load related data for response
                await _context.Entry(sale)
                    .Reference(s => s.Customer)
                    .LoadAsync();
                await _context.Entry(sale)
                    .Reference(s => s.Branch)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetSale), new { id = sale.Id }, sale);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}/cancel")]
        public async Task<IActionResult> CancelSale(int id, [FromBody] CancelSaleRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var sale = await _context.Sales
                    .Include(s => s.SaleItems)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (sale == null)
                    return NotFound(new { message = "فاتورة المبيعات غير موجودة" });

                if (sale.Status == 2)
                    return BadRequest(new { message = "الفاتورة ملغية مسبقاً" });

                // Reverse stock movements
                foreach (var item in sale.SaleItems)
                {
                    await UpdateProductStock(item.ProductId, sale.BranchId, item.Quantity, item.UnitCostPrice);
                    
                    await CreateStockMovement(item.ProductId, sale.BranchId, item.Quantity, 
                        item.UnitCostPrice, "Sale Cancellation", sale.Id, sale.InvoiceNumber, userId.Value);
                }

                sale.Status = 2; // Cancelled
                sale.CancelledById = userId;
                sale.CancelledAt = DateTime.Now;
                sale.CancellationReason = request.Reason;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new { message = "تم إلغاء الفاتورة بنجاح" });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetSalesDashboard([FromQuery] int? branchId = null, [FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                var query = _context.Sales.AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(s => s.BranchId == branchId);

                // Today's sales
                var todaySales = await query
                    .Where(s => s.InvoiceDate.Date == targetDate && s.Status == 1)
                    .GroupBy(s => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(s => s.TotalAmount),
                        Paid = g.Sum(s => s.PaidAmount),
                        Remaining = g.Sum(s => s.RemainingAmount)
                    })
                    .FirstOrDefaultAsync();

                // This month's sales
                var monthStart = new DateTime(targetDate.Year, targetDate.Month, 1);
                var monthSales = await query
                    .Where(s => s.InvoiceDate >= monthStart && s.InvoiceDate < monthStart.AddMonths(1) && s.Status == 1)
                    .GroupBy(s => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Total = g.Sum(s => s.TotalAmount)
                    })
                    .FirstOrDefaultAsync();

                // Top selling products today
                var topProducts = await _context.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate.Date == targetDate && si.Sale.Status == 1)
                    .GroupBy(si => new { si.ProductId, si.Product.NameAr })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.NameAr,
                        Quantity = g.Sum(si => si.Quantity),
                        Amount = g.Sum(si => si.FinalTotal)
                    })
                    .OrderByDescending(p => p.Quantity)
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    today = todaySales ?? new { Count = 0, Total = 0m, Paid = 0m, Remaining = 0m },
                    month = monthSales ?? new { Count = 0, Total = 0m },
                    topProducts
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateInvoiceNumber(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.SaleInvoice && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.SaleInvoice,
                    Prefix = "SAL",
                    CurrentValue = 1,
                    NumberLength = 8,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task<string> GeneratePaymentNumber(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == "SALE_PAYMENT" && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = "SALE_PAYMENT",
                    Prefix = "PAY",
                    CurrentValue = 1,
                    NumberLength = 8,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task UpdateProductStock(int productId, int branchId, decimal quantity, decimal unitCost)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            if (stock != null)
            {
                stock.AvailableQuantity += quantity;
                
                if (quantity > 0) // Incoming stock
                {
                    stock.TotalInQuantity += quantity;
                    // Update average cost
                    var totalValue = (stock.AvailableQuantity - quantity) * stock.AverageCostPrice + quantity * unitCost;
                    stock.AverageCostPrice = stock.AvailableQuantity > 0 ? totalValue / stock.AvailableQuantity : unitCost;
                    stock.LastCostPrice = unitCost;
                }
                else // Outgoing stock
                {
                    stock.TotalOutQuantity += Math.Abs(quantity);
                }

                stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;
                stock.UpdatedAt = DateTime.Now;
            }
        }

        private async Task CreateStockMovement(int productId, int branchId, decimal quantity, decimal unitCost, 
            string movementReason, int sourceId, string sourceReference, int userId)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            var movement = new StockMovement
            {
                ProductId = productId,
                BranchId = branchId,
                MovementNumber = $"MOV{DateTime.Now.Ticks}",
                MovementType = quantity > 0 ? 1 : 2, // 1=In, 2=Out
                MovementReason = movementReason,
                Quantity = Math.Abs(quantity),
                UnitCost = unitCost,
                TotalCost = Math.Abs(quantity) * unitCost,
                BalanceBefore = (stock?.AvailableQuantity ?? 0) - quantity,
                BalanceAfter = stock?.AvailableQuantity ?? 0,
                MovementDate = DateTime.Now,
                SourceId = sourceId,
                SourceType = "Sale",
                UserId = userId,
                Notes = $"Sale Invoice: {sourceReference}",
                CreatedAt = DateTime.Now
            };

            _context.StockMovements.Add(movement);
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class CreateSaleRequest
    {
        public int? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public int BranchId { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public int SaleType { get; set; } = 1; // 1=Cash, 2=Credit, 3=Mixed
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public string? Notes { get; set; }
        public string? TableNumber { get; set; }
        public List<CreateSaleItemRequest> Items { get; set; } = new();
        public List<CreateSalePaymentRequest>? Payments { get; set; }
    }

    public class CreateSaleItemRequest
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal UnitCostPrice { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public int? PriceCategoryId { get; set; }
        public string? ItemNotes { get; set; }
        public string? BatchNumber { get; set; }
        public string? SerialNumber { get; set; }
        public decimal? ActualWeight { get; set; }
    }

    public class CreateSalePaymentRequest
    {
        public int PaymentMethodId { get; set; }
        public decimal Amount { get; set; }
        public decimal ReceivedAmount { get; set; }
        public decimal ChangeAmount { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? BankName { get; set; }
        public string? AccountNumber { get; set; }
        public string? Notes { get; set; }
    }

    public class CancelSaleRequest
    {
        public string Reason { get; set; } = string.Empty;
    }
}
