{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n// Angular Material Imports\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDividerModule } from '@angular/material/divider';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/list\";\nimport * as i7 from \"@angular/material/menu\";\nimport * as i8 from \"@angular/material/badge\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nfunction LayoutNewComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"Terra Retail ERP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"span\", 52);\n    i0.ɵɵtext(2, \"\\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4, \"\\u0645\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LayoutNewComponent_h3_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 54);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"sales\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 57)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 58)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"receipt_long\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"a\", 59)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"assignment_return\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u0645\\u0631\\u062A\\u062C\\u0639\\u0627\\u062A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"inventory\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 60)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 61)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u0627\\u0644\\u062A\\u0635\\u0646\\u064A\\u0641\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"a\", 62)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"swap_horiz\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u062D\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_span_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"purchases\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 63)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 64)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"list_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u0623\\u0648\\u0627\\u0645\\u0631 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"finance\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 65)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"account_balance_wallet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 66)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"book\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u0627\\u0644\\u0642\\u064A\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A\\u0629\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"a\", 67)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"savings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0648\\u0627\\u0631\\u062F \\u0627\\u0644\\u0628\\u0634\\u0631\\u064A\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"hr\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 68)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 69)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u0643\\u0634\\u0648\\u0641 \\u0627\\u0644\\u0645\\u0631\\u062A\\u0628\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"a\", 70)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u0627\\u0644\\u062D\\u0636\\u0648\\u0631 \\u0648\\u0627\\u0644\\u0627\\u0646\\u0635\\u0631\\u0627\\u0641\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_mat_icon_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.expandedGroups.has(\"reports\") ? \"expand_less\" : \"expand_more\", \" \");\n  }\n}\nfunction LayoutNewComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"a\", 71)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 72)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"a\", 73)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LayoutNewComponent_span_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LayoutNewComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"p\", 76);\n    i0.ɵɵtext(3, \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 2.0.1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 77);\n    i0.ɵɵtext(5, \"\\u00A9 2024 Terra Retail\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let LayoutNewComponent = /*#__PURE__*/(() => {\n  class LayoutNewComponent {\n    // Layout State\n    sidebarCollapsed = false;\n    isMobile = false;\n    sidebarVisible = true;\n    // Navigation State\n    expandedGroups = new Set();\n    // Search\n    globalSearchTerm = '';\n    // User Info\n    currentUser = {\n      name: 'أحمد محمد',\n      role: 'مدير النظام',\n      avatar: 'assets/images/user-avatar.svg'\n    };\n    constructor() {\n      this.checkScreenSize();\n    }\n    ngOnInit() {\n      // Initialize expanded groups based on current route\n      this.initializeExpandedGroups();\n      // Load user preferences\n      this.loadUserPreferences();\n    }\n    onResize(event) {\n      this.checkScreenSize();\n    }\n    /**\n     * Check screen size and adjust layout accordingly\n     */\n    checkScreenSize() {\n      this.isMobile = window.innerWidth <= 1024;\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n        this.sidebarVisible = false;\n      } else {\n        this.sidebarVisible = true;\n      }\n    }\n    /**\n     * Toggle sidebar collapsed state\n     */\n    toggleSidebar() {\n      if (this.isMobile) {\n        this.sidebarVisible = !this.sidebarVisible;\n      } else {\n        this.sidebarCollapsed = !this.sidebarCollapsed;\n        // Collapse all groups when sidebar is collapsed\n        if (this.sidebarCollapsed) {\n          this.expandedGroups.clear();\n        }\n      }\n      // Save preference\n      this.saveUserPreferences();\n    }\n    /**\n     * Toggle navigation group expansion\n     */\n    toggleNavGroup(groupName) {\n      if (this.sidebarCollapsed) {\n        return; // Don't expand groups when sidebar is collapsed\n      }\n      if (this.expandedGroups.has(groupName)) {\n        this.expandedGroups.delete(groupName);\n      } else {\n        this.expandedGroups.add(groupName);\n      }\n      // Save preference\n      this.saveUserPreferences();\n    }\n    /**\n     * Initialize expanded groups based on current route\n     */\n    initializeExpandedGroups() {\n      const currentUrl = window.location.pathname;\n      // Auto-expand groups based on current route\n      if (currentUrl.includes('/pos') || currentUrl.includes('/sales')) {\n        this.expandedGroups.add('sales');\n      }\n      if (currentUrl.includes('/products') || currentUrl.includes('/categories') || currentUrl.includes('/inventory')) {\n        this.expandedGroups.add('inventory');\n      }\n      if (currentUrl.includes('/purchases') || currentUrl.includes('/purchase-orders')) {\n        this.expandedGroups.add('purchases');\n      }\n      if (currentUrl.includes('/accounts') || currentUrl.includes('/journal') || currentUrl.includes('/treasury')) {\n        this.expandedGroups.add('finance');\n      }\n      if (currentUrl.includes('/employees') || currentUrl.includes('/payroll') || currentUrl.includes('/attendance')) {\n        this.expandedGroups.add('hr');\n      }\n      if (currentUrl.includes('/reports')) {\n        this.expandedGroups.add('reports');\n      }\n    }\n    /**\n     * Load user preferences from localStorage\n     */\n    loadUserPreferences() {\n      try {\n        const preferences = localStorage.getItem('terra-layout-preferences');\n        if (preferences) {\n          const parsed = JSON.parse(preferences);\n          if (!this.isMobile) {\n            this.sidebarCollapsed = parsed.sidebarCollapsed || false;\n          }\n          if (parsed.expandedGroups && Array.isArray(parsed.expandedGroups)) {\n            this.expandedGroups = new Set(parsed.expandedGroups);\n          }\n        }\n      } catch (error) {\n        console.warn('Failed to load user preferences:', error);\n      }\n    }\n    /**\n     * Save user preferences to localStorage\n     */\n    saveUserPreferences() {\n      try {\n        const preferences = {\n          sidebarCollapsed: this.sidebarCollapsed,\n          expandedGroups: Array.from(this.expandedGroups)\n        };\n        localStorage.setItem('terra-layout-preferences', JSON.stringify(preferences));\n      } catch (error) {\n        console.warn('Failed to save user preferences:', error);\n      }\n    }\n    /**\n     * Handle global search\n     */\n    onGlobalSearch() {\n      if (this.globalSearchTerm.trim()) {\n        // Implement global search functionality\n        console.log('Searching for:', this.globalSearchTerm);\n        // You can implement search across different modules here\n        // For example, search in products, customers, suppliers, etc.\n      }\n    }\n    /**\n     * Handle user logout\n     */\n    logout() {\n      // Clear user preferences\n      localStorage.removeItem('terra-layout-preferences');\n      // Implement logout logic\n      console.log('User logged out');\n      // Redirect to login page\n      // this.router.navigate(['/login']);\n    }\n    /**\n     * Navigate to user profile\n     */\n    navigateToProfile() {\n      // Implement navigation to user profile\n      console.log('Navigate to user profile');\n    }\n    /**\n     * Navigate to settings\n     */\n    navigateToSettings() {\n      // Implement navigation to settings\n      console.log('Navigate to settings');\n    }\n    /**\n     * Handle notification click\n     */\n    onNotificationClick(notification) {\n      // Implement notification handling\n      console.log('Notification clicked:', notification);\n    }\n    /**\n     * Mark all notifications as read\n     */\n    markAllNotificationsAsRead() {\n      // Implement mark all as read functionality\n      console.log('Mark all notifications as read');\n    }\n    /**\n     * Get notification count\n     */\n    getNotificationCount() {\n      // Return actual notification count from service\n      return 3; // Mock data\n    }\n    /**\n     * Check if current route is active\n     */\n    isRouteActive(route) {\n      return window.location.pathname.includes(route);\n    }\n    /**\n     * Get current page title based on route\n     */\n    getCurrentPageTitle() {\n      const currentUrl = window.location.pathname;\n      const routeTitles = {\n        '/dashboard': 'لوحة التحكم',\n        '/customers': 'إدارة العملاء',\n        '/products': 'إدارة المنتجات',\n        '/suppliers': 'إدارة الموردين',\n        '/pos': 'نقطة البيع',\n        '/sales': 'المبيعات',\n        '/purchases': 'المشتريات',\n        '/inventory': 'المخزون',\n        '/finance': 'المالية',\n        '/hr': 'الموارد البشرية',\n        '/reports': 'التقارير',\n        '/settings': 'الإعدادات'\n      };\n      for (const route in routeTitles) {\n        if (currentUrl.includes(route)) {\n          return routeTitles[route];\n        }\n      }\n      return 'Terra Retail ERP';\n    }\n    static ɵfac = function LayoutNewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LayoutNewComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutNewComponent,\n      selectors: [[\"app-layout-new\"]],\n      hostBindings: function LayoutNewComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function LayoutNewComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 137,\n      vars: 46,\n      consts: [[\"notificationsMenu\", \"matMenu\"], [\"userMenu\", \"matMenu\"], [1, \"app-layout\"], [1, \"top-navbar\"], [1, \"navbar-content\"], [1, \"navbar-left\"], [\"mat-icon-button\", \"\", 1, \"sidebar-toggle\", 3, \"click\"], [1, \"logo-section\"], [\"src\", \"assets/images/logo.svg\", \"alt\", \"Terra Retail\", 1, \"logo\"], [\"class\", \"logo-text\", 4, \"ngIf\"], [1, \"navbar-center\"], [1, \"global-search\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matPrefix\", \"\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"navbar-right\"], [\"mat-icon-button\", \"\", 1, \"notification-btn\", 3, \"matMenuTriggerFor\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\"], [\"mat-button\", \"\", 1, \"user-profile\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [\"src\", \"assets/images/user-avatar.svg\", \"alt\", \"User\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"settings-btn\"], [1, \"sidebar\"], [1, \"sidebar-nav\"], [1, \"nav-section\"], [\"class\", \"nav-section-title\", 4, \"ngIf\"], [\"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [4, \"ngIf\"], [1, \"nav-group\"], [1, \"nav-item\", \"expandable\", 3, \"click\"], [\"class\", \"expand-icon\", 4, \"ngIf\"], [\"class\", \"nav-submenu\", 4, \"ngIf\"], [\"routerLink\", \"/customers\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [\"routerLink\", \"/suppliers\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [\"routerLink\", \"/settings\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [\"class\", \"sidebar-footer\", 4, \"ngIf\"], [1, \"main-content\"], [1, \"content-wrapper\"], [1, \"notifications-menu\"], [1, \"menu-header\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-menu-item\", \"\", 1, \"notification-item\"], [\"color\", \"primary\"], [1, \"notification-content\"], [1, \"notification-title\"], [1, \"notification-time\"], [\"color\", \"warn\"], [1, \"user-menu\"], [\"mat-menu-item\", \"\"], [1, \"logo-text\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-role\"], [1, \"nav-section-title\"], [1, \"expand-icon\"], [1, \"nav-submenu\"], [\"routerLink\", \"/pos\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/sales\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/sales-returns\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/products\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/categories\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/inventory-movements\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/purchases\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/purchase-orders\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/accounts\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/journal-entries\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/treasury\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/employees\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/payroll\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/attendance\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/sales-reports\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/inventory-reports\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [\"routerLink\", \"/financial-reports\", \"routerLinkActive\", \"active\", 1, \"nav-subitem\"], [1, \"sidebar-footer\"], [1, \"system-info\"], [1, \"version\"], [1, \"copyright\"]],\n      template: function LayoutNewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSidebar());\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵtemplate(9, LayoutNewComponent_span_9_Template, 2, 0, \"span\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"mat-form-field\", 12)(13, \"mat-icon\", 13);\n          i0.ɵɵtext(14, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LayoutNewComponent_Template_input_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 15)(17, \"button\", 16)(18, \"mat-icon\", 17);\n          i0.ɵɵtext(19, \"notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"button\", 18)(21, \"div\", 19);\n          i0.ɵɵelement(22, \"img\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, LayoutNewComponent_div_23_Template, 5, 0, \"div\", 21);\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"button\", 22)(27, \"mat-icon\");\n          i0.ɵɵtext(28, \"settings\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"aside\", 23)(30, \"nav\", 24)(31, \"div\", 25);\n          i0.ɵɵtemplate(32, LayoutNewComponent_h3_32_Template, 2, 0, \"h3\", 26);\n          i0.ɵɵelementStart(33, \"a\", 27)(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, LayoutNewComponent_span_36_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 29)(38, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"sales\"));\n          });\n          i0.ɵɵelementStart(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"point_of_sale\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, LayoutNewComponent_span_41_Template, 2, 0, \"span\", 28)(42, LayoutNewComponent_mat_icon_42_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, LayoutNewComponent_div_43_Template, 16, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 29)(45, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"inventory\"));\n          });\n          i0.ɵɵelementStart(46, \"mat-icon\");\n          i0.ɵɵtext(47, \"inventory_2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, LayoutNewComponent_span_48_Template, 2, 0, \"span\", 28)(49, LayoutNewComponent_mat_icon_49_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, LayoutNewComponent_div_50_Template, 16, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"a\", 33)(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(54, LayoutNewComponent_span_54_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"a\", 34)(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"local_shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(58, LayoutNewComponent_span_58_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 29)(60, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_60_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"purchases\"));\n          });\n          i0.ɵɵelementStart(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"shopping_bag\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(63, LayoutNewComponent_span_63_Template, 2, 0, \"span\", 28)(64, LayoutNewComponent_mat_icon_64_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, LayoutNewComponent_div_65_Template, 11, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 29)(67, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_67_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"finance\"));\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(70, LayoutNewComponent_span_70_Template, 2, 0, \"span\", 28)(71, LayoutNewComponent_mat_icon_71_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(72, LayoutNewComponent_div_72_Template, 16, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 29)(74, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_74_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"hr\"));\n          });\n          i0.ɵɵelementStart(75, \"mat-icon\");\n          i0.ɵɵtext(76, \"badge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(77, LayoutNewComponent_span_77_Template, 2, 0, \"span\", 28)(78, LayoutNewComponent_mat_icon_78_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, LayoutNewComponent_div_79_Template, 16, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 29)(81, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LayoutNewComponent_Template_button_click_81_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleNavGroup(\"reports\"));\n          });\n          i0.ɵɵelementStart(82, \"mat-icon\");\n          i0.ɵɵtext(83, \"assessment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(84, LayoutNewComponent_span_84_Template, 2, 0, \"span\", 28)(85, LayoutNewComponent_mat_icon_85_Template, 2, 1, \"mat-icon\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(86, LayoutNewComponent_div_86_Template, 16, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"a\", 35)(88, \"mat-icon\");\n          i0.ɵɵtext(89, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(90, LayoutNewComponent_span_90_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(91, LayoutNewComponent_div_91_Template, 6, 0, \"div\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"main\", 37)(93, \"div\", 38);\n          i0.ɵɵelement(94, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"mat-menu\", 39, 0)(97, \"div\", 40)(98, \"h3\");\n          i0.ɵɵtext(99, \"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"button\", 41);\n          i0.ɵɵtext(101, \"\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0627\\u0644\\u0643\\u0644\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(102, \"mat-divider\");\n          i0.ɵɵelementStart(103, \"button\", 42)(104, \"mat-icon\", 43);\n          i0.ɵɵtext(105, \"shopping_cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"div\", 44)(107, \"p\", 45);\n          i0.ɵɵtext(108, \"\\u0637\\u0644\\u0628 \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"p\", 46);\n          i0.ɵɵtext(110, \"\\u0645\\u0646\\u0630 5 \\u062F\\u0642\\u0627\\u0626\\u0642\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(111, \"button\", 42)(112, \"mat-icon\", 47);\n          i0.ɵɵtext(113, \"inventory\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"div\", 44)(115, \"p\", 45);\n          i0.ɵɵtext(116, \"\\u0646\\u0641\\u0627\\u062F \\u0645\\u062E\\u0632\\u0648\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"p\", 46);\n          i0.ɵɵtext(118, \"\\u0645\\u0646\\u0630 15 \\u062F\\u0642\\u064A\\u0642\\u0629\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(119, \"mat-menu\", 48, 1)(121, \"button\", 49)(122, \"mat-icon\");\n          i0.ɵɵtext(123, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"span\");\n          i0.ɵɵtext(125, \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"button\", 49)(127, \"mat-icon\");\n          i0.ɵɵtext(128, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"span\");\n          i0.ɵɵtext(130, \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(131, \"mat-divider\");\n          i0.ɵɵelementStart(132, \"button\", 49)(133, \"mat-icon\");\n          i0.ɵɵtext(134, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"span\");\n          i0.ɵɵtext(136, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const notificationsMenu_r3 = i0.ɵɵreference(96);\n          const userMenu_r4 = i0.ɵɵreference(120);\n          i0.ɵɵclassProp(\"sidebar-collapsed\", ctx.sidebarCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.sidebarCollapsed ? \"menu\" : \"menu_open\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", notificationsMenu_r3);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r4);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"collapsed\", ctx.sidebarCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"sales\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"sales\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"inventory\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"inventory\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"purchases\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"purchases\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"finance\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"finance\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"hr\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"hr\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.expandedGroups.has(\"reports\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.expandedGroups.has(\"reports\") && !ctx.sidebarCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.sidebarCollapsed);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, RouterModule, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, MatToolbarModule, MatSidenavModule, MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, MatListModule, i6.MatDivider, MatMenuModule, i7.MatMenu, i7.MatMenuItem, i7.MatMenuTrigger, MatBadgeModule, i8.MatBadge, MatFormFieldModule, i9.MatFormField, i9.MatPrefix, MatInputModule, i10.MatInput, MatDividerModule],\n      styles: [\"\\n\\n.app-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  overflow: hidden;\\n  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);\\n}\\n.app-layout.sidebar-collapsed[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  width: 70px;\\n}\\n.app-layout.sidebar-collapsed[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  margin-right: 70px;\\n}\\n\\n\\n\\n.top-navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 70px;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-bottom: 1px solid var(--gray-200);\\n  box-shadow: var(--shadow-sm);\\n  z-index: 1000;\\n}\\n\\n.navbar-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 100%;\\n  padding: 0 var(--spacing-xl);\\n  max-width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.navbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n.sidebar-toggle[_ngcontent-%COMP%] {\\n  color: var(--gray-600) !important;\\n  transition: all var(--transition-fast) !important;\\n}\\n.sidebar-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-600) !important;\\n  background: var(--primary-50) !important;\\n}\\n\\n.logo-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n.logo-section[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: var(--radius-lg);\\n}\\n.logo-section[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: var(--primary-700);\\n  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.navbar-center[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 500px;\\n  margin: 0 var(--spacing-xl);\\n}\\n\\n.global-search[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.global-search[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.8) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.global-search[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  background: white !important;\\n  border-color: var(--primary-300) !important;\\n}\\n\\n.navbar-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.notification-btn[_ngcontent-%COMP%] {\\n  color: var(--gray-600) !important;\\n  position: relative;\\n}\\n.notification-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-600) !important;\\n  background: var(--primary-50) !important;\\n}\\n\\n.user-profile[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-sm) !important;\\n  padding: var(--spacing-sm) var(--spacing-md) !important;\\n  border-radius: var(--radius-lg) !important;\\n  transition: all var(--transition-fast) !important;\\n}\\n.user-profile[_ngcontent-%COMP%]:hover {\\n  background: var(--gray-50) !important;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 2px solid var(--primary-200);\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n  line-height: 1.2;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  line-height: 1.2;\\n}\\n\\n.settings-btn[_ngcontent-%COMP%] {\\n  color: var(--gray-600) !important;\\n}\\n.settings-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-600) !important;\\n  background: var(--primary-50) !important;\\n}\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 70px;\\n  right: 0;\\n  width: 280px;\\n  height: calc(100vh - 70px);\\n  background: linear-gradient(180deg, var(--primary-900) 0%, var(--primary-800) 50%, var(--secondary-800) 100%);\\n  color: white;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  transition: all var(--transition-normal);\\n  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.1);\\n  z-index: 999;\\n}\\n.sidebar.collapsed[_ngcontent-%COMP%] {\\n  width: 70px;\\n}\\n.sidebar.collapsed[_ngcontent-%COMP%]   .nav-section-title[_ngcontent-%COMP%], \\n.sidebar.collapsed[_ngcontent-%COMP%]   .nav-submenu[_ngcontent-%COMP%], \\n.sidebar.collapsed[_ngcontent-%COMP%]   .sidebar-footer[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.sidebar.collapsed[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  padding: var(--spacing-md);\\n}\\n.sidebar.collapsed[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.sidebar.collapsed[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) 0;\\n  flex: 1;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n\\n.nav-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-xl);\\n}\\n\\n.nav-section-title[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  color: rgba(255, 255, 255, 0.7);\\n  padding: 0 var(--spacing-lg);\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md) var(--spacing-lg);\\n  color: rgba(255, 255, 255, 0.9);\\n  text-decoration: none;\\n  transition: all var(--transition-fast);\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  cursor: pointer;\\n  font-family: var(--font-family-primary);\\n  font-size: 0.95rem;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  transform: translateX(-4px);\\n}\\n.nav-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));\\n  color: white;\\n  box-shadow: var(--shadow-md);\\n  border-radius: var(--radius-xl) 0 0 var(--radius-xl);\\n  margin-left: var(--spacing-md);\\n  position: relative;\\n}\\n.nav-item.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background: var(--warning-400);\\n}\\n.nav-item.expandable[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\n.nav-item.expandable[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  transition: transform var(--transition-fast);\\n}\\n.nav-item.expandable.expanded[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: right;\\n}\\n\\n.nav-group[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-sm);\\n}\\n\\n.nav-submenu[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.2);\\n  margin: var(--spacing-xs) 0;\\n  border-radius: var(--radius-lg) 0 0 var(--radius-lg);\\n  overflow: hidden;\\n}\\n\\n.nav-subitem[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-lg);\\n  color: rgba(255, 255, 255, 0.8);\\n  text-decoration: none;\\n  transition: all var(--transition-fast);\\n  font-size: 0.875rem;\\n}\\n.nav-subitem[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  transform: translateX(-2px);\\n}\\n.nav-subitem.active[_ngcontent-%COMP%] {\\n  background: var(--primary-600);\\n  color: white;\\n}\\n.nav-subitem[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  margin-top: auto;\\n}\\n\\n.system-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.system-info[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.system-info[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: rgba(255, 255, 255, 0.5);\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  margin-top: 70px;\\n  margin-right: 280px;\\n  min-height: calc(100vh - 70px);\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);\\n  transition: margin-right var(--transition-normal);\\n  box-sizing: border-box;\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl);\\n  min-height: calc(100vh - 70px);\\n  max-width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.notifications-menu[_ngcontent-%COMP%]   .mat-mdc-menu-content[_ngcontent-%COMP%], \\n.user-menu[_ngcontent-%COMP%]   .mat-mdc-menu-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n  border-radius: var(--radius-lg) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n\\n.menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md) var(--spacing-lg);\\n  background: var(--gray-50);\\n}\\n.menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n\\n.notification-item[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  padding: var(--spacing-md) var(--spacing-lg) !important;\\n}\\n.notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--gray-800);\\n  margin: 0;\\n}\\n.notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .navbar-center[_ngcontent-%COMP%] {\\n    max-width: 300px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n  }\\n  .sidebar[_ngcontent-%COMP%] {\\n    transform: translateX(100%);\\n    z-index: 1001;\\n  }\\n  .sidebar.show[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n  .navbar-center[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .navbar-content[_ngcontent-%COMP%] {\\n    padding: 0 var(--spacing-lg);\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n  .navbar-left[_ngcontent-%COMP%] {\\n    gap: var(--spacing-sm);\\n  }\\n  .navbar-right[_ngcontent-%COMP%] {\\n    gap: var(--spacing-xs);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .navbar-content[_ngcontent-%COMP%] {\\n    padding: 0 var(--spacing-md);\\n  }\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md);\\n  }\\n  .logo-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  from {\\n    transform: translateX(-100%);\\n  }\\n  to {\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.3s ease-out;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.4s ease-out;\\n  animation-fill-mode: both;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0.1s;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.3s;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:nth-child(4) {\\n  animation-delay: 0.4s;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:nth-child(5) {\\n  animation-delay: 0.5s;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LayoutNewComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "MatToolbarModule", "MatSidenavModule", "MatButtonModule", "MatIconModule", "MatListModule", "MatMenuModule", "MatBadgeModule", "MatFormFieldModule", "MatInputModule", "MatDividerModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "expandedGroups", "has", "LayoutNewComponent", "sidebarCollapsed", "isMobile", "sidebarVisible", "Set", "globalSearchTerm", "currentUser", "name", "role", "avatar", "constructor", "checkScreenSize", "ngOnInit", "initializeExpandedGroups", "loadUserPreferences", "onResize", "event", "window", "innerWidth", "toggleSidebar", "clear", "saveUserPreferences", "toggleNavGroup", "groupName", "delete", "add", "currentUrl", "location", "pathname", "includes", "preferences", "localStorage", "getItem", "parsed", "JSON", "parse", "Array", "isArray", "error", "console", "warn", "from", "setItem", "stringify", "onGlobalSearch", "trim", "log", "logout", "removeItem", "navigateToProfile", "navigateToSettings", "onNotificationClick", "notification", "markAllNotificationsAsRead", "getNotificationCount", "isRouteActive", "route", "getCurrentPageTitle", "routeTitles", "selectors", "hostBindings", "LayoutNewComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "LayoutNewComponent_resize_HostBindingHandler", "$event", "ɵɵresolveWindow", "LayoutNewComponent_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "ɵɵtemplate", "LayoutNewComponent_span_9_Template", "ɵɵtwoWayListener", "LayoutNewComponent_Template_input_ngModelChange_15_listener", "ɵɵtwoWayBindingSet", "LayoutNewComponent_div_23_Template", "LayoutNewComponent_h3_32_Template", "LayoutNewComponent_span_36_Template", "LayoutNewComponent_Template_button_click_38_listener", "LayoutNewComponent_span_41_Template", "LayoutNewComponent_mat_icon_42_Template", "LayoutNewComponent_div_43_Template", "LayoutNewComponent_Template_button_click_45_listener", "LayoutNewComponent_span_48_Template", "LayoutNewComponent_mat_icon_49_Template", "LayoutNewComponent_div_50_Template", "LayoutNewComponent_span_54_Template", "LayoutNewComponent_span_58_Template", "LayoutNewComponent_Template_button_click_60_listener", "LayoutNewComponent_span_63_Template", "LayoutNewComponent_mat_icon_64_Template", "LayoutNewComponent_div_65_Template", "LayoutNewComponent_Template_button_click_67_listener", "LayoutNewComponent_span_70_Template", "LayoutNewComponent_mat_icon_71_Template", "LayoutNewComponent_div_72_Template", "LayoutNewComponent_Template_button_click_74_listener", "LayoutNewComponent_span_77_Template", "LayoutNewComponent_mat_icon_78_Template", "LayoutNewComponent_div_79_Template", "LayoutNewComponent_Template_button_click_81_listener", "LayoutNewComponent_span_84_Template", "LayoutNewComponent_mat_icon_85_Template", "LayoutNewComponent_div_86_Template", "LayoutNewComponent_span_90_Template", "LayoutNewComponent_div_91_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵtwoWayProperty", "notificationsMenu_r3", "userMenu_r4", "i1", "NgIf", "i2", "RouterOutlet", "RouterLink", "RouterLinkActive", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "i6", "<PERSON><PERSON><PERSON><PERSON>", "i7", "MatMenu", "MatMenuItem", "MatMenuTrigger", "i8", "MatBadge", "i9", "MatFormField", "MatPrefix", "i10", "MatInput", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\layout\\layout-new.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\layout\\layout-new.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\n// Angular Material Imports\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDividerModule } from '@angular/material/divider';\n\n@Component({\n  selector: 'app-layout-new',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatButtonModule,\n    MatIconModule,\n    MatListModule,\n    MatMenuModule,\n    MatBadgeModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDividerModule\n  ],\n  templateUrl: './layout-new.component.html',\n  styleUrls: ['./layout-new.component.scss']\n})\nexport class LayoutNewComponent implements OnInit {\n  \n  // Layout State\n  sidebarCollapsed = false;\n  isMobile = false;\n  sidebarVisible = true;\n  \n  // Navigation State\n  expandedGroups = new Set<string>();\n  \n  // Search\n  globalSearchTerm = '';\n  \n  // User Info\n  currentUser = {\n    name: 'أحمد محمد',\n    role: 'مدير النظام',\n    avatar: 'assets/images/user-avatar.svg'\n  };\n\n  constructor() {\n    this.checkScreenSize();\n  }\n\n  ngOnInit(): void {\n    // Initialize expanded groups based on current route\n    this.initializeExpandedGroups();\n    \n    // Load user preferences\n    this.loadUserPreferences();\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any): void {\n    this.checkScreenSize();\n  }\n\n  /**\n   * Check screen size and adjust layout accordingly\n   */\n  private checkScreenSize(): void {\n    this.isMobile = window.innerWidth <= 1024;\n    \n    if (this.isMobile) {\n      this.sidebarCollapsed = true;\n      this.sidebarVisible = false;\n    } else {\n      this.sidebarVisible = true;\n    }\n  }\n\n  /**\n   * Toggle sidebar collapsed state\n   */\n  toggleSidebar(): void {\n    if (this.isMobile) {\n      this.sidebarVisible = !this.sidebarVisible;\n    } else {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      \n      // Collapse all groups when sidebar is collapsed\n      if (this.sidebarCollapsed) {\n        this.expandedGroups.clear();\n      }\n    }\n    \n    // Save preference\n    this.saveUserPreferences();\n  }\n\n  /**\n   * Toggle navigation group expansion\n   */\n  toggleNavGroup(groupName: string): void {\n    if (this.sidebarCollapsed) {\n      return; // Don't expand groups when sidebar is collapsed\n    }\n\n    if (this.expandedGroups.has(groupName)) {\n      this.expandedGroups.delete(groupName);\n    } else {\n      this.expandedGroups.add(groupName);\n    }\n    \n    // Save preference\n    this.saveUserPreferences();\n  }\n\n  /**\n   * Initialize expanded groups based on current route\n   */\n  private initializeExpandedGroups(): void {\n    const currentUrl = window.location.pathname;\n    \n    // Auto-expand groups based on current route\n    if (currentUrl.includes('/pos') || currentUrl.includes('/sales')) {\n      this.expandedGroups.add('sales');\n    }\n    \n    if (currentUrl.includes('/products') || currentUrl.includes('/categories') || currentUrl.includes('/inventory')) {\n      this.expandedGroups.add('inventory');\n    }\n    \n    if (currentUrl.includes('/purchases') || currentUrl.includes('/purchase-orders')) {\n      this.expandedGroups.add('purchases');\n    }\n    \n    if (currentUrl.includes('/accounts') || currentUrl.includes('/journal') || currentUrl.includes('/treasury')) {\n      this.expandedGroups.add('finance');\n    }\n    \n    if (currentUrl.includes('/employees') || currentUrl.includes('/payroll') || currentUrl.includes('/attendance')) {\n      this.expandedGroups.add('hr');\n    }\n    \n    if (currentUrl.includes('/reports')) {\n      this.expandedGroups.add('reports');\n    }\n  }\n\n  /**\n   * Load user preferences from localStorage\n   */\n  private loadUserPreferences(): void {\n    try {\n      const preferences = localStorage.getItem('terra-layout-preferences');\n      if (preferences) {\n        const parsed = JSON.parse(preferences);\n        \n        if (!this.isMobile) {\n          this.sidebarCollapsed = parsed.sidebarCollapsed || false;\n        }\n        \n        if (parsed.expandedGroups && Array.isArray(parsed.expandedGroups)) {\n          this.expandedGroups = new Set(parsed.expandedGroups);\n        }\n      }\n    } catch (error) {\n      console.warn('Failed to load user preferences:', error);\n    }\n  }\n\n  /**\n   * Save user preferences to localStorage\n   */\n  private saveUserPreferences(): void {\n    try {\n      const preferences = {\n        sidebarCollapsed: this.sidebarCollapsed,\n        expandedGroups: Array.from(this.expandedGroups)\n      };\n      \n      localStorage.setItem('terra-layout-preferences', JSON.stringify(preferences));\n    } catch (error) {\n      console.warn('Failed to save user preferences:', error);\n    }\n  }\n\n  /**\n   * Handle global search\n   */\n  onGlobalSearch(): void {\n    if (this.globalSearchTerm.trim()) {\n      // Implement global search functionality\n      console.log('Searching for:', this.globalSearchTerm);\n      \n      // You can implement search across different modules here\n      // For example, search in products, customers, suppliers, etc.\n    }\n  }\n\n  /**\n   * Handle user logout\n   */\n  logout(): void {\n    // Clear user preferences\n    localStorage.removeItem('terra-layout-preferences');\n    \n    // Implement logout logic\n    console.log('User logged out');\n    \n    // Redirect to login page\n    // this.router.navigate(['/login']);\n  }\n\n  /**\n   * Navigate to user profile\n   */\n  navigateToProfile(): void {\n    // Implement navigation to user profile\n    console.log('Navigate to user profile');\n  }\n\n  /**\n   * Navigate to settings\n   */\n  navigateToSettings(): void {\n    // Implement navigation to settings\n    console.log('Navigate to settings');\n  }\n\n  /**\n   * Handle notification click\n   */\n  onNotificationClick(notification: any): void {\n    // Implement notification handling\n    console.log('Notification clicked:', notification);\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  markAllNotificationsAsRead(): void {\n    // Implement mark all as read functionality\n    console.log('Mark all notifications as read');\n  }\n\n  /**\n   * Get notification count\n   */\n  getNotificationCount(): number {\n    // Return actual notification count from service\n    return 3; // Mock data\n  }\n\n  /**\n   * Check if current route is active\n   */\n  isRouteActive(route: string): boolean {\n    return window.location.pathname.includes(route);\n  }\n\n  /**\n   * Get current page title based on route\n   */\n  getCurrentPageTitle(): string {\n    const currentUrl = window.location.pathname;\n    \n    const routeTitles: { [key: string]: string } = {\n      '/dashboard': 'لوحة التحكم',\n      '/customers': 'إدارة العملاء',\n      '/products': 'إدارة المنتجات',\n      '/suppliers': 'إدارة الموردين',\n      '/pos': 'نقطة البيع',\n      '/sales': 'المبيعات',\n      '/purchases': 'المشتريات',\n      '/inventory': 'المخزون',\n      '/finance': 'المالية',\n      '/hr': 'الموارد البشرية',\n      '/reports': 'التقارير',\n      '/settings': 'الإعدادات'\n    };\n    \n    for (const route in routeTitles) {\n      if (currentUrl.includes(route)) {\n        return routeTitles[route];\n      }\n    }\n    \n    return 'Terra Retail ERP';\n  }\n}\n", "<!-- Terra Retail ERP - Professional Layout -->\n<div class=\"app-layout\" [class.sidebar-collapsed]=\"sidebarCollapsed\">\n  \n  <!-- Top Navigation Bar -->\n  <header class=\"top-navbar\">\n    <div class=\"navbar-content\">\n      <!-- Left Section -->\n      <div class=\"navbar-left\">\n        <button mat-icon-button (click)=\"toggleSidebar()\" class=\"sidebar-toggle\">\n          <mat-icon>{{ sidebarCollapsed ? 'menu' : 'menu_open' }}</mat-icon>\n        </button>\n        \n        <div class=\"logo-section\">\n          <img src=\"assets/images/logo.svg\" alt=\"Terra Retail\" class=\"logo\" />\n          <span class=\"logo-text\" *ngIf=\"!sidebarCollapsed\">Terra Retail ERP</span>\n        </div>\n      </div>\n\n      <!-- Center Section - Search -->\n      <div class=\"navbar-center\">\n        <div class=\"global-search\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-icon matPrefix>search</mat-icon>\n            <input matInput placeholder=\"البحث في النظام...\" [(ngModel)]=\"globalSearchTerm\">\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Right Section -->\n      <div class=\"navbar-right\">\n        <!-- Notifications -->\n        <button mat-icon-button [matMenuTriggerFor]=\"notificationsMenu\" class=\"notification-btn\">\n          <mat-icon matBadge=\"3\" matBadgeColor=\"warn\">notifications</mat-icon>\n        </button>\n        \n        <!-- User Profile -->\n        <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-profile\">\n          <div class=\"user-avatar\">\n            <img src=\"assets/images/user-avatar.svg\" alt=\"User\" />\n          </div>\n          <div class=\"user-info\" *ngIf=\"!sidebarCollapsed\">\n            <span class=\"user-name\">أحمد محمد</span>\n            <span class=\"user-role\">مدير النظام</span>\n          </div>\n          <mat-icon>keyboard_arrow_down</mat-icon>\n        </button>\n\n        <!-- Settings -->\n        <button mat-icon-button class=\"settings-btn\">\n          <mat-icon>settings</mat-icon>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Sidebar Navigation -->\n  <aside class=\"sidebar\" [class.collapsed]=\"sidebarCollapsed\">\n    <nav class=\"sidebar-nav\">\n      \n      <!-- Main Navigation -->\n      <div class=\"nav-section\">\n        <h3 class=\"nav-section-title\" *ngIf=\"!sidebarCollapsed\">القائمة الرئيسية</h3>\n        \n        <a routerLink=\"/dashboard\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>dashboard</mat-icon>\n          <span *ngIf=\"!sidebarCollapsed\">لوحة التحكم</span>\n        </a>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('sales')\" \n                  [class.expanded]=\"expandedGroups.has('sales')\">\n            <mat-icon>point_of_sale</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">المبيعات</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('sales') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('sales') && !sidebarCollapsed\">\n            <a routerLink=\"/pos\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>shopping_cart</mat-icon>\n              <span>نقطة البيع</span>\n            </a>\n            <a routerLink=\"/sales\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>receipt_long</mat-icon>\n              <span>فواتير المبيعات</span>\n            </a>\n            <a routerLink=\"/sales-returns\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>assignment_return</mat-icon>\n              <span>مرتجعات المبيعات</span>\n            </a>\n          </div>\n        </div>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('inventory')\" \n                  [class.expanded]=\"expandedGroups.has('inventory')\">\n            <mat-icon>inventory_2</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">المخزون</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('inventory') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('inventory') && !sidebarCollapsed\">\n            <a routerLink=\"/products\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>category</mat-icon>\n              <span>المنتجات</span>\n            </a>\n            <a routerLink=\"/categories\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>label</mat-icon>\n              <span>التصنيفات</span>\n            </a>\n            <a routerLink=\"/inventory-movements\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>swap_horiz</mat-icon>\n              <span>حركات المخزون</span>\n            </a>\n          </div>\n        </div>\n\n        <a routerLink=\"/customers\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>people</mat-icon>\n          <span *ngIf=\"!sidebarCollapsed\">العملاء</span>\n        </a>\n\n        <a routerLink=\"/suppliers\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>local_shipping</mat-icon>\n          <span *ngIf=\"!sidebarCollapsed\">الموردين</span>\n        </a>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('purchases')\" \n                  [class.expanded]=\"expandedGroups.has('purchases')\">\n            <mat-icon>shopping_bag</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">المشتريات</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('purchases') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('purchases') && !sidebarCollapsed\">\n            <a routerLink=\"/purchases\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>receipt</mat-icon>\n              <span>فواتير المشتريات</span>\n            </a>\n            <a routerLink=\"/purchase-orders\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>list_alt</mat-icon>\n              <span>أوامر الشراء</span>\n            </a>\n          </div>\n        </div>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('finance')\" \n                  [class.expanded]=\"expandedGroups.has('finance')\">\n            <mat-icon>account_balance</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">المالية</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('finance') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('finance') && !sidebarCollapsed\">\n            <a routerLink=\"/accounts\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>account_balance_wallet</mat-icon>\n              <span>الحسابات</span>\n            </a>\n            <a routerLink=\"/journal-entries\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>book</mat-icon>\n              <span>القيود المحاسبية</span>\n            </a>\n            <a routerLink=\"/treasury\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>savings</mat-icon>\n              <span>الخزينة</span>\n            </a>\n          </div>\n        </div>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('hr')\" \n                  [class.expanded]=\"expandedGroups.has('hr')\">\n            <mat-icon>badge</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">الموارد البشرية</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('hr') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('hr') && !sidebarCollapsed\">\n            <a routerLink=\"/employees\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>person</mat-icon>\n              <span>الموظفين</span>\n            </a>\n            <a routerLink=\"/payroll\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>payment</mat-icon>\n              <span>كشوف المرتبات</span>\n            </a>\n            <a routerLink=\"/attendance\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>schedule</mat-icon>\n              <span>الحضور والانصراف</span>\n            </a>\n          </div>\n        </div>\n\n        <div class=\"nav-group\">\n          <button class=\"nav-item expandable\" (click)=\"toggleNavGroup('reports')\" \n                  [class.expanded]=\"expandedGroups.has('reports')\">\n            <mat-icon>assessment</mat-icon>\n            <span *ngIf=\"!sidebarCollapsed\">التقارير</span>\n            <mat-icon class=\"expand-icon\" *ngIf=\"!sidebarCollapsed\">\n              {{ expandedGroups.has('reports') ? 'expand_less' : 'expand_more' }}\n            </mat-icon>\n          </button>\n          \n          <div class=\"nav-submenu\" *ngIf=\"expandedGroups.has('reports') && !sidebarCollapsed\">\n            <a routerLink=\"/sales-reports\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>trending_up</mat-icon>\n              <span>تقارير المبيعات</span>\n            </a>\n            <a routerLink=\"/inventory-reports\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>inventory</mat-icon>\n              <span>تقارير المخزون</span>\n            </a>\n            <a routerLink=\"/financial-reports\" routerLinkActive=\"active\" class=\"nav-subitem\">\n              <mat-icon>pie_chart</mat-icon>\n              <span>التقارير المالية</span>\n            </a>\n          </div>\n        </div>\n\n        <a routerLink=\"/settings\" routerLinkActive=\"active\" class=\"nav-item\">\n          <mat-icon>settings</mat-icon>\n          <span *ngIf=\"!sidebarCollapsed\">الإعدادات</span>\n        </a>\n      </div>\n    </nav>\n\n    <!-- Sidebar Footer -->\n    <div class=\"sidebar-footer\" *ngIf=\"!sidebarCollapsed\">\n      <div class=\"system-info\">\n        <p class=\"version\">الإصدار 2.0.1</p>\n        <p class=\"copyright\">© 2024 Terra Retail</p>\n      </div>\n    </div>\n  </aside>\n\n  <!-- Main Content Area -->\n  <main class=\"main-content\">\n    <div class=\"content-wrapper\">\n      <router-outlet></router-outlet>\n    </div>\n  </main>\n\n  <!-- Notifications Menu -->\n  <mat-menu #notificationsMenu=\"matMenu\" class=\"notifications-menu\">\n    <div class=\"menu-header\">\n      <h3>الإشعارات</h3>\n      <button mat-button color=\"primary\">مشاهدة الكل</button>\n    </div>\n    <mat-divider></mat-divider>\n    \n    <button mat-menu-item class=\"notification-item\">\n      <mat-icon color=\"primary\">shopping_cart</mat-icon>\n      <div class=\"notification-content\">\n        <p class=\"notification-title\">طلب جديد</p>\n        <p class=\"notification-time\">منذ 5 دقائق</p>\n      </div>\n    </button>\n    \n    <button mat-menu-item class=\"notification-item\">\n      <mat-icon color=\"warn\">inventory</mat-icon>\n      <div class=\"notification-content\">\n        <p class=\"notification-title\">نفاد مخزون</p>\n        <p class=\"notification-time\">منذ 15 دقيقة</p>\n      </div>\n    </button>\n  </mat-menu>\n\n  <!-- User Menu -->\n  <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\n    <button mat-menu-item>\n      <mat-icon>person</mat-icon>\n      <span>الملف الشخصي</span>\n    </button>\n    <button mat-menu-item>\n      <mat-icon>settings</mat-icon>\n      <span>الإعدادات</span>\n    </button>\n    <mat-divider></mat-divider>\n    <button mat-menu-item>\n      <mat-icon>logout</mat-icon>\n      <span>تسجيل الخروج</span>\n    </button>\n  </mat-menu>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;ICDlDC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA2BvEH,EADF,CAAAC,cAAA,cAAiD,eACvB;IAAAD,EAAA,CAAAE,MAAA,wDAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,oEAAW;IACrCF,EADqC,CAAAG,YAAA,EAAO,EACtC;;;;;IAkBRH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,kGAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAI3EH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,oEAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOhDH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/CH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,+CACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAAkF,YACb,eACvD;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EACrB;IAEFH,EADF,CAAAC,cAAA,YAAqE,eACzD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAe;IACvBF,EADuB,CAAAG,YAAA,EAAO,EAC1B;IAEFH,EADF,CAAAC,cAAA,aAA6E,gBACjE;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAE1BF,EAF0B,CAAAG,YAAA,EAAO,EAC3B,EACA;;;;;IAOJH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,mDACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAAsF,YACZ,eAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACnB;IAEFH,EADF,CAAAC,cAAA,YAA0E,eAC9D;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,8DAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACpB;IAEFH,EADF,CAAAC,cAAA,aAAmF,gBACvE;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,iFAAa;IAEvBF,EAFuB,CAAAG,YAAA,EAAO,EACxB,EACA;;;;;IAKNH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAK9CH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAO7CH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,mDACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAAsF,YACX,eAC7D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kGAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EAC3B;IAEFH,EADF,CAAAC,cAAA,YAA+E,eACnE;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,2EAAY;IAEtBF,EAFsB,CAAAG,YAAA,EAAO,EACvB,EACA;;;;;IAOJH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,iDACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAAoF,YACV,eAC5D;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACnB;IAEFH,EADF,CAAAC,cAAA,YAA+E,eACnE;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EAC3B;IAEFH,EADF,CAAAC,cAAA,aAAwE,gBAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kDAAO;IAEjBF,EAFiB,CAAAG,YAAA,EAAO,EAClB,EACA;;;;;IAOJH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtDH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,4CACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAA+E,YACJ,eAC7D;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACnB;IAEFH,EADF,CAAAC,cAAA,YAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iFAAa;IACrBF,EADqB,CAAAG,YAAA,EAAO,EACxB;IAEFH,EADF,CAAAC,cAAA,aAA0E,gBAC9D;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAE1BF,EAF0B,CAAAG,YAAA,EAAO,EAC3B,EACA;;;;;IAOJH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/CH,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,iDACF;;;;;IAKER,EAFJ,CAAAC,cAAA,cAAoF,YACL,eACjE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IACvBF,EADuB,CAAAG,YAAA,EAAO,EAC1B;IAEFH,EADF,CAAAC,cAAA,YAAiF,eACrE;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IACtBF,EADsB,CAAAG,YAAA,EAAO,EACzB;IAEFH,EADF,CAAAC,cAAA,aAAiF,gBACrE;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAE1BF,EAF0B,CAAAG,YAAA,EAAO,EAC3B,EACA;;;;;IAKNH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQlDH,EAFJ,CAAAC,cAAA,cAAsD,cAC3B,YACJ;IAAAD,EAAA,CAAAE,MAAA,uDAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpCH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,+BAAmB;IAE5CF,EAF4C,CAAAG,YAAA,EAAI,EACxC,EACF;;;AD7MV,WAAaM,kBAAkB;EAAzB,MAAOA,kBAAkB;IAE7B;IACAC,gBAAgB,GAAG,KAAK;IACxBC,QAAQ,GAAG,KAAK;IAChBC,cAAc,GAAG,IAAI;IAErB;IACAL,cAAc,GAAG,IAAIM,GAAG,EAAU;IAElC;IACAC,gBAAgB,GAAG,EAAE;IAErB;IACAC,WAAW,GAAG;MACZC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;KACT;IAEDC,YAAA;MACE,IAAI,CAACC,eAAe,EAAE;IACxB;IAEAC,QAAQA,CAAA;MACN;MACA,IAAI,CAACC,wBAAwB,EAAE;MAE/B;MACA,IAAI,CAACC,mBAAmB,EAAE;IAC5B;IAGAC,QAAQA,CAACC,KAAU;MACjB,IAAI,CAACL,eAAe,EAAE;IACxB;IAEA;;;IAGQA,eAAeA,CAAA;MACrB,IAAI,CAACT,QAAQ,GAAGe,MAAM,CAACC,UAAU,IAAI,IAAI;MAEzC,IAAI,IAAI,CAAChB,QAAQ,EAAE;QACjB,IAAI,CAACD,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACE,cAAc,GAAG,KAAK;MAC7B,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,IAAI;MAC5B;IACF;IAEA;;;IAGAgB,aAAaA,CAAA;MACX,IAAI,IAAI,CAACjB,QAAQ,EAAE;QACjB,IAAI,CAACC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;MAC5C,CAAC,MAAM;QACL,IAAI,CAACF,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;QAE9C;QACA,IAAI,IAAI,CAACA,gBAAgB,EAAE;UACzB,IAAI,CAACH,cAAc,CAACsB,KAAK,EAAE;QAC7B;MACF;MAEA;MACA,IAAI,CAACC,mBAAmB,EAAE;IAC5B;IAEA;;;IAGAC,cAAcA,CAACC,SAAiB;MAC9B,IAAI,IAAI,CAACtB,gBAAgB,EAAE;QACzB,OAAO,CAAC;MACV;MAEA,IAAI,IAAI,CAACH,cAAc,CAACC,GAAG,CAACwB,SAAS,CAAC,EAAE;QACtC,IAAI,CAACzB,cAAc,CAAC0B,MAAM,CAACD,SAAS,CAAC;MACvC,CAAC,MAAM;QACL,IAAI,CAACzB,cAAc,CAAC2B,GAAG,CAACF,SAAS,CAAC;MACpC;MAEA;MACA,IAAI,CAACF,mBAAmB,EAAE;IAC5B;IAEA;;;IAGQR,wBAAwBA,CAAA;MAC9B,MAAMa,UAAU,GAAGT,MAAM,CAACU,QAAQ,CAACC,QAAQ;MAE3C;MACA,IAAIF,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAChE,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,OAAO,CAAC;MAClC;MAEA,IAAIC,UAAU,CAACG,QAAQ,CAAC,WAAW,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,aAAa,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC/G,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,WAAW,CAAC;MACtC;MAEA,IAAIC,UAAU,CAACG,QAAQ,CAAC,YAAY,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAChF,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,WAAW,CAAC;MACtC;MAEA,IAAIC,UAAU,CAACG,QAAQ,CAAC,WAAW,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC3G,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,SAAS,CAAC;MACpC;MAEA,IAAIC,UAAU,CAACG,QAAQ,CAAC,YAAY,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC9G,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,IAAI,CAAC;MAC/B;MAEA,IAAIC,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnC,IAAI,CAAC/B,cAAc,CAAC2B,GAAG,CAAC,SAAS,CAAC;MACpC;IACF;IAEA;;;IAGQX,mBAAmBA,CAAA;MACzB,IAAI;QACF,MAAMgB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;QACpE,IAAIF,WAAW,EAAE;UACf,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;UAEtC,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE;YAClB,IAAI,CAACD,gBAAgB,GAAGgC,MAAM,CAAChC,gBAAgB,IAAI,KAAK;UAC1D;UAEA,IAAIgC,MAAM,CAACnC,cAAc,IAAIsC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACnC,cAAc,CAAC,EAAE;YACjE,IAAI,CAACA,cAAc,GAAG,IAAIM,GAAG,CAAC6B,MAAM,CAACnC,cAAc,CAAC;UACtD;QACF;MACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;MACzD;IACF;IAEA;;;IAGQjB,mBAAmBA,CAAA;MACzB,IAAI;QACF,MAAMS,WAAW,GAAG;UAClB7B,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCH,cAAc,EAAEsC,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC3C,cAAc;SAC/C;QAEDiC,YAAY,CAACW,OAAO,CAAC,0BAA0B,EAAER,IAAI,CAACS,SAAS,CAACb,WAAW,CAAC,CAAC;MAC/E,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;MACzD;IACF;IAEA;;;IAGAM,cAAcA,CAAA;MACZ,IAAI,IAAI,CAACvC,gBAAgB,CAACwC,IAAI,EAAE,EAAE;QAChC;QACAN,OAAO,CAACO,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzC,gBAAgB,CAAC;QAEpD;QACA;MACF;IACF;IAEA;;;IAGA0C,MAAMA,CAAA;MACJ;MACAhB,YAAY,CAACiB,UAAU,CAAC,0BAA0B,CAAC;MAEnD;MACAT,OAAO,CAACO,GAAG,CAAC,iBAAiB,CAAC;MAE9B;MACA;IACF;IAEA;;;IAGAG,iBAAiBA,CAAA;MACf;MACAV,OAAO,CAACO,GAAG,CAAC,0BAA0B,CAAC;IACzC;IAEA;;;IAGAI,kBAAkBA,CAAA;MAChB;MACAX,OAAO,CAACO,GAAG,CAAC,sBAAsB,CAAC;IACrC;IAEA;;;IAGAK,mBAAmBA,CAACC,YAAiB;MACnC;MACAb,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAEM,YAAY,CAAC;IACpD;IAEA;;;IAGAC,0BAA0BA,CAAA;MACxB;MACAd,OAAO,CAACO,GAAG,CAAC,gCAAgC,CAAC;IAC/C;IAEA;;;IAGAQ,oBAAoBA,CAAA;MAClB;MACA,OAAO,CAAC,CAAC,CAAC;IACZ;IAEA;;;IAGAC,aAAaA,CAACC,KAAa;MACzB,OAAOvC,MAAM,CAACU,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC2B,KAAK,CAAC;IACjD;IAEA;;;IAGAC,mBAAmBA,CAAA;MACjB,MAAM/B,UAAU,GAAGT,MAAM,CAACU,QAAQ,CAACC,QAAQ;MAE3C,MAAM8B,WAAW,GAA8B;QAC7C,YAAY,EAAE,aAAa;QAC3B,YAAY,EAAE,eAAe;QAC7B,WAAW,EAAE,gBAAgB;QAC7B,YAAY,EAAE,gBAAgB;QAC9B,MAAM,EAAE,YAAY;QACpB,QAAQ,EAAE,UAAU;QACpB,YAAY,EAAE,WAAW;QACzB,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,SAAS;QACrB,KAAK,EAAE,iBAAiB;QACxB,UAAU,EAAE,UAAU;QACtB,WAAW,EAAE;OACd;MAED,KAAK,MAAMF,KAAK,IAAIE,WAAW,EAAE;QAC/B,IAAIhC,UAAU,CAACG,QAAQ,CAAC2B,KAAK,CAAC,EAAE;UAC9B,OAAOE,WAAW,CAACF,KAAK,CAAC;QAC3B;MACF;MAEA,OAAO,kBAAkB;IAC3B;;uCApQWxD,kBAAkB;IAAA;;YAAlBA,kBAAkB;MAAA2D,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAlBvE,EAAA,CAAAyE,UAAA,oBAAAC,6CAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAhD,QAAA,CAAAmD,MAAA,CAAgB;UAAA,GAAA3E,EAAA,CAAA4E,eAAA,CAAE;;;;;;;;;UC9BvB5E,EAPR,CAAAC,cAAA,aAAqE,gBAGxC,aACG,aAED,gBACkD;UAAjDD,EAAA,CAAAyE,UAAA,mBAAAI,oDAAA;YAAA7E,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAA5C,aAAA,EAAe;UAAA,EAAC;UAC/C5B,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,GAA6C;UACzDF,EADyD,CAAAG,YAAA,EAAW,EAC3D;UAETH,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAiF,SAAA,aAAoE;UACpEjF,EAAA,CAAAkF,UAAA,IAAAC,kCAAA,kBAAkD;UAEtDnF,EADE,CAAAG,YAAA,EAAM,EACF;UAMAH,EAHN,CAAAC,cAAA,eAA2B,eACE,0BACiC,oBACpC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAAC,cAAA,iBAAgF;UAA/BD,EAAA,CAAAoF,gBAAA,2BAAAC,4DAAAV,MAAA;YAAA3E,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA/E,EAAA,CAAAsF,kBAAA,CAAAd,GAAA,CAAA1D,gBAAA,EAAA6D,MAAA,MAAAH,GAAA,CAAA1D,gBAAA,GAAA6D,MAAA;YAAA,OAAA3E,EAAA,CAAAgF,WAAA,CAAAL,MAAA;UAAA,EAA8B;UAGrF3E,EAHM,CAAAG,YAAA,EAAgF,EACjE,EACb,EACF;UAMFH,EAHJ,CAAAC,cAAA,eAA0B,kBAEiE,oBAC3C;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAC3DF,EAD2D,CAAAG,YAAA,EAAW,EAC7D;UAIPH,EADF,CAAAC,cAAA,kBAAuE,eAC5C;UACvBD,EAAA,CAAAiF,SAAA,eAAsD;UACxDjF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAkF,UAAA,KAAAK,kCAAA,kBAAiD;UAIjDvF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAC/BF,EAD+B,CAAAG,YAAA,EAAW,EACjC;UAIPH,EADF,CAAAC,cAAA,kBAA6C,gBACjC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAI1BF,EAJ0B,CAAAG,YAAA,EAAW,EACtB,EACL,EACF,EACC;UAOLH,EAJJ,CAAAC,cAAA,iBAA4D,eACjC,eAGE;UACvBD,EAAA,CAAAkF,UAAA,KAAAM,iCAAA,iBAAwD;UAGtDxF,EADF,CAAAC,cAAA,aAAsE,gBAC1D;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAkF,UAAA,KAAAO,mCAAA,mBAAgC;UAClCzF,EAAA,CAAAG,YAAA,EAAI;UAGFH,EADF,CAAAC,cAAA,eAAuB,kBAEkC;UADnBD,EAAA,CAAAyE,UAAA,mBAAAiB,qDAAA;YAAA1F,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,OAAO,CAAC;UAAA,EAAC;UAEnE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAElCH,EADA,CAAAkF,UAAA,KAAAS,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1D5F,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAAW,kCAAA,mBAAkF;UAcpF7F,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAuB,kBAEsC;UADvBD,EAAA,CAAAyE,UAAA,mBAAAqB,qDAAA;YAAA9F,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,WAAW,CAAC;UAAA,EAAC;UAEvE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhCH,EADA,CAAAkF,UAAA,KAAAa,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1DhG,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAAe,kCAAA,mBAAsF;UAcxFjG,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAsE,gBAC1D;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAkF,UAAA,KAAAgB,mCAAA,mBAAgC;UAClClG,EAAA,CAAAG,YAAA,EAAI;UAGFH,EADF,CAAAC,cAAA,aAAsE,gBAC1D;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnCH,EAAA,CAAAkF,UAAA,KAAAiB,mCAAA,mBAAgC;UAClCnG,EAAA,CAAAG,YAAA,EAAI;UAGFH,EADF,CAAAC,cAAA,eAAuB,kBAEsC;UADvBD,EAAA,CAAAyE,UAAA,mBAAA2B,qDAAA;YAAApG,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,WAAW,CAAC;UAAA,EAAC;UAEvE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEjCH,EADA,CAAAkF,UAAA,KAAAmB,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1DtG,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAAqB,kCAAA,mBAAsF;UAUxFvG,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAuB,kBAEoC;UADrBD,EAAA,CAAAyE,UAAA,mBAAA+B,qDAAA;YAAAxG,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,SAAS,CAAC;UAAA,EAAC;UAErE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEpCH,EADA,CAAAkF,UAAA,KAAAuB,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1D1G,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAAyB,kCAAA,mBAAoF;UActF3G,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAuB,kBAE+B;UADhBD,EAAA,CAAAyE,UAAA,mBAAAmC,qDAAA;YAAA5G,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,IAAI,CAAC;UAAA,EAAC;UAEhE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE1BH,EADA,CAAAkF,UAAA,KAAA2B,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1D9G,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAA6B,kCAAA,mBAA+E;UAcjF/G,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAuB,kBAEoC;UADrBD,EAAA,CAAAyE,UAAA,mBAAAuC,qDAAA;YAAAhH,EAAA,CAAA8E,aAAA,CAAAC,GAAA;YAAA,OAAA/E,EAAA,CAAAgF,WAAA,CAASR,GAAA,CAAAzC,cAAA,CAAe,SAAS,CAAC;UAAA,EAAC;UAErE/B,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/BH,EADA,CAAAkF,UAAA,KAAA+B,mCAAA,mBAAgC,KAAAC,uCAAA,uBACwB;UAG1DlH,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAkF,UAAA,KAAAiC,kCAAA,mBAAoF;UActFnH,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAqE,gBACzD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAkF,UAAA,KAAAkC,mCAAA,mBAAgC;UAGtCpH,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;UAGNH,EAAA,CAAAkF,UAAA,KAAAmC,kCAAA,kBAAsD;UAMxDrH,EAAA,CAAAG,YAAA,EAAQ;UAINH,EADF,CAAAC,cAAA,gBAA2B,eACI;UAC3BD,EAAA,CAAAiF,SAAA,qBAA+B;UAEnCjF,EADE,CAAAG,YAAA,EAAM,EACD;UAKHH,EAFJ,CAAAC,cAAA,uBAAkE,eACvC,UACnB;UAAAD,EAAA,CAAAE,MAAA,8DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,mBAAmC;UAAAD,EAAA,CAAAE,MAAA,sEAAW;UAChDF,EADgD,CAAAG,YAAA,EAAS,EACnD;UACNH,EAAA,CAAAiF,SAAA,oBAA2B;UAGzBjF,EADF,CAAAC,cAAA,mBAAgD,qBACpB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEhDH,EADF,CAAAC,cAAA,gBAAkC,cACF;UAAAD,EAAA,CAAAE,MAAA,oDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1CH,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAE,MAAA,4DAAW;UAE5CF,EAF4C,CAAAG,YAAA,EAAI,EACxC,EACC;UAGPH,EADF,CAAAC,cAAA,mBAAgD,qBACvB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEzCH,EADF,CAAAC,cAAA,gBAAkC,cACF;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5CH,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAE,MAAA,6DAAY;UAG/CF,EAH+C,CAAAG,YAAA,EAAI,EACzC,EACC,EACA;UAKPH,EAFJ,CAAAC,cAAA,wBAAgD,mBACxB,iBACV;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,4EAAY;UACpBF,EADoB,CAAAG,YAAA,EAAO,EAClB;UAEPH,EADF,CAAAC,cAAA,mBAAsB,iBACV;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,+DAAS;UACjBF,EADiB,CAAAG,YAAA,EAAO,EACf;UACTH,EAAA,CAAAiF,SAAA,oBAA2B;UAEzBjF,EADF,CAAAC,cAAA,mBAAsB,iBACV;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,4EAAY;UAIxBF,EAJwB,CAAAG,YAAA,EAAO,EAClB,EACA,EAEP;;;;;UAtSkBH,EAAA,CAAAsH,WAAA,sBAAA9C,GAAA,CAAA9D,gBAAA,CAA4C;UAQhDV,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAuH,iBAAA,CAAA/C,GAAA,CAAA9D,gBAAA,wBAA6C;UAK9BV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UASGV,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAyH,gBAAA,YAAAjD,GAAA,CAAA1D,gBAAA,CAA8B;UAQ3Dd,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAwH,UAAA,sBAAAE,oBAAA,CAAuC;UAK5C1H,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAwH,UAAA,sBAAAG,WAAA,CAA8B;UAIvB3H,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAgBhCV,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAsH,WAAA,cAAA9C,GAAA,CAAA9D,gBAAA,CAAoC;UAKtBV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAI7CV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAKtBV,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,UAA8C;UAE7CR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAAsD;UAAtDJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,cAAAgE,GAAA,CAAA9D,gBAAA,CAAsD;UAkBxEV,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,cAAkD;UAEjDR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAA0D;UAA1DJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,kBAAAgE,GAAA,CAAA9D,gBAAA,CAA0D;UAkB7EV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAKvBV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAKtBV,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,cAAkD;UAEjDR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAA0D;UAA1DJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,kBAAAgE,GAAA,CAAA9D,gBAAA,CAA0D;UAc5EV,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,YAAgD;UAE/CR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAAwD;UAAxDJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,gBAAAgE,GAAA,CAAA9D,gBAAA,CAAwD;UAkB1EV,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,OAA2C;UAE1CR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAAmD;UAAnDJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,WAAAgE,GAAA,CAAA9D,gBAAA,CAAmD;UAkBrEV,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAsH,WAAA,aAAA9C,GAAA,CAAAjE,cAAA,CAAAC,GAAA,YAAgD;UAE/CR,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UACCV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAK9BV,EAAA,CAAAI,SAAA,EAAwD;UAAxDJ,EAAA,CAAAwH,UAAA,SAAAhD,GAAA,CAAAjE,cAAA,CAAAC,GAAA,gBAAAgE,GAAA,CAAA9D,gBAAA,CAAwD;UAkB3EV,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;UAMPV,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAwH,UAAA,UAAAhD,GAAA,CAAA9D,gBAAA,CAAuB;;;qBDzNpDvB,YAAY,EAAAyI,EAAA,CAAAC,IAAA,EACZzI,YAAY,EAAA0I,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,UAAA,EAAAF,EAAA,CAAAG,gBAAA,EACZ5I,WAAW,EAAA6I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX/I,gBAAgB,EAChBC,gBAAgB,EAChBC,eAAe,EAAA8I,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf/I,aAAa,EAAAgJ,EAAA,CAAAC,OAAA,EACbhJ,aAAa,EAAAiJ,EAAA,CAAAC,UAAA,EACbjJ,aAAa,EAAAkJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,cAAA,EACbpJ,cAAc,EAAAqJ,EAAA,CAAAC,QAAA,EACdrJ,kBAAkB,EAAAsJ,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,SAAA,EAClBvJ,cAAc,EAAAwJ,GAAA,CAAAC,QAAA,EACdxJ,gBAAgB;MAAAyJ,MAAA;IAAA;;SAKP/I,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}