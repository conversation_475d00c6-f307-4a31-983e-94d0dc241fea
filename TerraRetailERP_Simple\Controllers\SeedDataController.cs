using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("🌱 Seed Data")]
    public class SeedDataController : ControllerBase
    {
        private readonly AppDbContext _context;

        public SeedDataController(AppDbContext context)
        {
            _context = context;
        }

        [HttpPost("chart-of-accounts")]
        public async Task<ActionResult> SeedChartOfAccounts()
        {
            try
            {
                // حذف البيانات الموجودة
                _context.ChartOfAccounts.RemoveRange(_context.ChartOfAccounts);
                await _context.SaveChangesAsync();

                var accounts = new List<ChartOfAccount>();

                // ===== المستوى الأول - الحسابات الرئيسية =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "1", NameAr = "الأصول", NameEn = "Assets", AccountType = 1, ParentId = null, Level = 1, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "2", NameAr = "الخصوم", NameEn = "Liabilities", AccountType = 2, ParentId = null, Level = 1, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "3", NameAr = "حقوق الملكية", NameEn = "Equity", AccountType = 3, ParentId = null, Level = 1, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "4", NameAr = "الإيرادات", NameEn = "Revenue", AccountType = 4, ParentId = null, Level = 1, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "5", NameAr = "المصروفات", NameEn = "Expenses", AccountType = 5, ParentId = null, Level = 1, IsParent = true, AllowPosting = false, IsActive = true }
                });

                // ===== المستوى الثاني - الأصول =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11", NameAr = "الأصول المتداولة", NameEn = "Current Assets", AccountType = 1, ParentId = 1, Level = 2, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "12", NameAr = "الأصول الثابتة", NameEn = "Fixed Assets", AccountType = 1, ParentId = 1, Level = 2, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "13", NameAr = "الأصول الأخرى", NameEn = "Other Assets", AccountType = 1, ParentId = 1, Level = 2, IsParent = true, AllowPosting = false, IsActive = true }
                });

                // ===== المستوى الثالث - الأصول المتداولة =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "111", NameAr = "النقدية والبنوك", NameEn = "Cash & Banks", AccountType = 1, ParentId = 6, Level = 3, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "112", NameAr = "العملاء", NameEn = "Accounts Receivable", AccountType = 1, ParentId = 6, Level = 3, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "113", NameAr = "المخزون", NameEn = "Inventory", AccountType = 1, ParentId = 6, Level = 3, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "114", NameAr = "مصروفات مدفوعة مقدماً", NameEn = "Prepaid Expenses", AccountType = 1, ParentId = 6, Level = 3, IsParent = true, AllowPosting = false, IsActive = true },
                    new ChartOfAccount { AccountCode = "115", NameAr = "أرصدة مدينة أخرى", NameEn = "Other Debit Balances", AccountType = 1, ParentId = 6, Level = 3, IsParent = true, AllowPosting = false, IsActive = true }
                });

                // ===== المستوى الرابع - النقدية والبنوك =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11101", NameAr = "الصندوق الرئيسي", NameEn = "Main Cash", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11102", NameAr = "صندوق الفرع الأول", NameEn = "Branch 1 Cash", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11103", NameAr = "صندوق الفرع الثاني", NameEn = "Branch 2 Cash", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11104", NameAr = "صندوق العملة الأجنبية", NameEn = "Foreign Currency Cash", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11105", NameAr = "البنك الأهلي المصري - ج.م", NameEn = "National Bank of Egypt - EGP", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11106", NameAr = "بنك مصر - ج.م", NameEn = "Banque Misr - EGP", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11107", NameAr = "البنك التجاري الدولي - ج.م", NameEn = "CIB - EGP", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11108", NameAr = "بنك القاهرة - ج.م", NameEn = "Banque du Caire - EGP", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11109", NameAr = "البنك الأهلي المصري - دولار", NameEn = "National Bank of Egypt - USD", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11110", NameAr = "بنك مصر - دولار", NameEn = "Banque Misr - USD", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11111", NameAr = "حسابات بنكية أخرى", NameEn = "Other Bank Accounts", AccountType = 1, ParentId = 9, Level = 4, IsParent = false, AllowPosting = true, IsActive = true }
                });

                // ===== العملاء =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11201", NameAr = "عملاء محليين", NameEn = "Local Customers", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11202", NameAr = "عملاء أجانب", NameEn = "Foreign Customers", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11203", NameAr = "أوراق قبض", NameEn = "Notes Receivable", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11204", NameAr = "عملاء شيكات تحت التحصيل", NameEn = "Customers Checks Under Collection", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11205", NameAr = "مخصص ديون مشكوك فيها", NameEn = "Allowance for Doubtful Debts", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11206", NameAr = "عملاء بطاقات ائتمان", NameEn = "Credit Card Customers", AccountType = 1, ParentId = 10, Level = 4, IsParent = false, AllowPosting = true, IsActive = true }
                });

                // ===== المخزون =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11301", NameAr = "مخزون البضاعة", NameEn = "Merchandise Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11302", NameAr = "مخزون المواد الخام", NameEn = "Raw Materials Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11303", NameAr = "مخزون الإنتاج تحت التشغيل", NameEn = "Work in Process Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11304", NameAr = "مخزون البضاعة التامة", NameEn = "Finished Goods Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11305", NameAr = "مخزون قطع الغيار", NameEn = "Spare Parts Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11306", NameAr = "مخزون المستلزمات", NameEn = "Supplies Inventory", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11307", NameAr = "مخزون البضاعة في الطريق", NameEn = "Goods in Transit", AccountType = 1, ParentId = 11, Level = 4, IsParent = false, AllowPosting = true, IsActive = true }
                });

                // ===== مصروفات مدفوعة مقدماً =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11401", NameAr = "إيجار مدفوع مقدماً", NameEn = "Prepaid Rent", AccountType = 1, ParentId = 12, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11402", NameAr = "تأمين مدفوع مقدماً", NameEn = "Prepaid Insurance", AccountType = 1, ParentId = 12, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11403", NameAr = "مصروفات دعاية مدفوعة مقدماً", NameEn = "Prepaid Advertising", AccountType = 1, ParentId = 12, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11404", NameAr = "رسوم ترخيص مدفوعة مقدماً", NameEn = "Prepaid License Fees", AccountType = 1, ParentId = 12, Level = 4, IsParent = false, AllowPosting = true, IsActive = true }
                });

                // ===== أرصدة مدينة أخرى =====
                accounts.AddRange(new[]
                {
                    new ChartOfAccount { AccountCode = "11501", NameAr = "سلف الموظفين", NameEn = "Employee Advances", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11502", NameAr = "عهد نقدية", NameEn = "Cash Custody", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11503", NameAr = "عهد عينية", NameEn = "Material Custody", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11504", NameAr = "أمانات مدفوعة", NameEn = "Deposits Paid", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11505", NameAr = "ضرائب مدفوعة مقدماً", NameEn = "Prepaid Taxes", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true },
                    new ChartOfAccount { AccountCode = "11506", NameAr = "مدينون متنوعون", NameEn = "Sundry Debtors", AccountType = 1, ParentId = 13, Level = 4, IsParent = false, AllowPosting = true, IsActive = true }
                });

                _context.ChartOfAccounts.AddRange(accounts);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم إنشاء شجرة الحسابات بنجاح",
                    count = accounts.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpPost("transaction-types")]
        public async Task<ActionResult> SeedTransactionTypes()
        {
            try
            {
                // حذف البيانات الموجودة
                _context.TransactionTypeEntities.RemoveRange(_context.TransactionTypeEntities);
                await _context.SaveChangesAsync();

                var transactionTypes = new List<TransactionTypeEntity>
                {
                    // مبيعات (Sales) - 1-10
                    new TransactionTypeEntity { Id = 1, NameAr = "مبيعات", NameEn = "Sale", Code = "SALE", Category = "مبيعات", Description = "فاتورة مبيعات عادية", IsActive = true, SortOrder = 1, Icon = "shopping-cart", Color = "#28a745", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 2, NameAr = "مرتجع مبيعات", NameEn = "Sale Return", Code = "SALE_RETURN", Category = "مبيعات", Description = "مرتجع فاتورة مبيعات", IsActive = true, SortOrder = 2, Icon = "undo", Color = "#dc3545", RequiresApproval = true, AllowReversal = false },
                    new TransactionTypeEntity { Id = 3, NameAr = "خصم مبيعات", NameEn = "Sale Discount", Code = "SALE_DISCOUNT", Category = "مبيعات", Description = "خصم على فاتورة مبيعات", IsActive = true, SortOrder = 3, Icon = "percent", Color = "#ffc107", RequiresApproval = true, AllowReversal = true },
                    new TransactionTypeEntity { Id = 4, NameAr = "دفعة من عميل", NameEn = "Sale Payment", Code = "SALE_PAYMENT", Category = "مبيعات", Description = "تحصيل من عميل", IsActive = true, SortOrder = 4, Icon = "money-bill", Color = "#17a2b8", RequiresApproval = false, AllowReversal = true },

                    // مشتريات (Purchases) - 11-20
                    new TransactionTypeEntity { Id = 11, NameAr = "مشتريات", NameEn = "Purchase", Code = "PURCHASE", Category = "مشتريات", Description = "فاتورة مشتريات عادية", IsActive = true, SortOrder = 11, Icon = "shopping-bag", Color = "#6f42c1", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 12, NameAr = "مرتجع مشتريات", NameEn = "Purchase Return", Code = "PURCHASE_RETURN", Category = "مشتريات", Description = "مرتجع فاتورة مشتريات", IsActive = true, SortOrder = 12, Icon = "undo", Color = "#e83e8c", RequiresApproval = true, AllowReversal = false },
                    new TransactionTypeEntity { Id = 13, NameAr = "خصم مشتريات", NameEn = "Purchase Discount", Code = "PURCHASE_DISCOUNT", Category = "مشتريات", Description = "خصم على فاتورة مشتريات", IsActive = true, SortOrder = 13, Icon = "percent", Color = "#fd7e14", RequiresApproval = true, AllowReversal = true },
                    new TransactionTypeEntity { Id = 14, NameAr = "دفعة لمورد", NameEn = "Purchase Payment", Code = "PURCHASE_PAYMENT", Category = "مشتريات", Description = "دفع لمورد", IsActive = true, SortOrder = 14, Icon = "credit-card", Color = "#20c997", RequiresApproval = false, AllowReversal = true },

                    // نقدية وبنوك (Cash & Banks) - 21-30
                    new TransactionTypeEntity { Id = 21, NameAr = "سند قبض نقدي", NameEn = "Cash Receipt", Code = "CASH_RECEIPT", Category = "نقدية وبنوك", Description = "سند قبض نقدي", IsActive = true, SortOrder = 21, Icon = "hand-holding-usd", Color = "#28a745", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 22, NameAr = "سند دفع نقدي", NameEn = "Cash Payment", Code = "CASH_PAYMENT", Category = "نقدية وبنوك", Description = "سند دفع نقدي", IsActive = true, SortOrder = 22, Icon = "money-bill-wave", Color = "#dc3545", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 23, NameAr = "إيداع بنكي", NameEn = "Bank Deposit", Code = "BANK_DEPOSIT", Category = "نقدية وبنوك", Description = "إيداع في البنك", IsActive = true, SortOrder = 23, Icon = "university", Color = "#007bff", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 24, NameAr = "سحب بنكي", NameEn = "Bank Withdrawal", Code = "BANK_WITHDRAWAL", Category = "نقدية وبنوك", Description = "سحب من البنك", IsActive = true, SortOrder = 24, Icon = "university", Color = "#6c757d", RequiresApproval = false, AllowReversal = true },

                    // مخزون (Inventory) - 31-40
                    new TransactionTypeEntity { Id = 31, NameAr = "تسوية مخزون", NameEn = "Stock Adjustment", Code = "STOCK_ADJUSTMENT", Category = "مخزون", Description = "تسوية كميات المخزون", IsActive = true, SortOrder = 31, Icon = "boxes", Color = "#6f42c1", RequiresApproval = true, AllowReversal = true },
                    new TransactionTypeEntity { Id = 32, NameAr = "تحويل مخزون", NameEn = "Stock Transfer", Code = "STOCK_TRANSFER", Category = "مخزون", Description = "تحويل بين المخازن", IsActive = true, SortOrder = 32, Icon = "truck", Color = "#fd7e14", RequiresApproval = false, AllowReversal = true },

                    // موظفين (Employees) - 41-50
                    new TransactionTypeEntity { Id = 41, NameAr = "دفع راتب", NameEn = "Salary Payment", Code = "SALARY_PAYMENT", Category = "موظفين", Description = "دفع راتب موظف", IsActive = true, SortOrder = 41, Icon = "user-tie", Color = "#007bff", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 42, NameAr = "سلفة راتب", NameEn = "Salary Advance", Code = "SALARY_ADVANCE", Category = "موظفين", Description = "سلفة على الراتب", IsActive = true, SortOrder = 42, Icon = "hand-holding-usd", Color = "#ffc107", RequiresApproval = true, AllowReversal = true },

                    // مصروفات (Expenses) - 51-60
                    new TransactionTypeEntity { Id = 51, NameAr = "مصروف تشغيلي", NameEn = "Operating Expense", Code = "OPERATING_EXPENSE", Category = "مصروفات", Description = "مصروف تشغيل عام", IsActive = true, SortOrder = 51, Icon = "receipt", Color = "#6c757d", RequiresApproval = false, AllowReversal = true },
                    new TransactionTypeEntity { Id = 52, NameAr = "مصروف خدمات", NameEn = "Utility Expense", Code = "UTILITY_EXPENSE", Category = "مصروفات", Description = "فواتير الخدمات", IsActive = true, SortOrder = 52, Icon = "bolt", Color = "#ffc107", RequiresApproval = false, AllowReversal = true }
                };

                _context.TransactionTypeEntities.AddRange(transactionTypes);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم إنشاء أنواع المعاملات بنجاح",
                    count = transactionTypes.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }
    }
}
