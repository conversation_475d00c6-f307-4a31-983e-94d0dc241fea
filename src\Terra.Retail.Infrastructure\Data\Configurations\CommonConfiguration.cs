using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class BranchConfiguration : IEntityTypeConfiguration<Branch>
    {
        public void Configure(EntityTypeBuilder<Branch> builder)
        {
            builder.ToTable("Branches");

            builder.HasKey(b => b.Id);

            builder.Property(b => b.NameAr)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(b => b.NameEn)
                .HasMaxLength(100);

            builder.Property(b => b.Code)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(b => b.Address)
                .HasMaxLength(500);

            builder.Property(b => b.Phone)
                .HasMaxLength(20);

            builder.Property(b => b.Email)
                .HasMaxLength(100);

            builder.Property(b => b.ManagerName)
                .HasMaxLength(100);

            builder.Property(b => b.WorkingHours)
                .HasMaxLength(100);

            builder.Property(b => b.Longitude)
                .HasColumnType("decimal(18,6)");

            builder.Property(b => b.Latitude)
                .HasColumnType("decimal(18,6)");

            builder.Property(b => b.Area)
                .HasColumnType("decimal(18,2)");

            builder.Property(b => b.TaxNumber)
                .HasMaxLength(50);

            builder.Property(b => b.CommercialRegister)
                .HasMaxLength(50);

            // Indexes
            builder.HasIndex(b => b.Code)
                .IsUnique()
                .HasDatabaseName("IX_Branches_Code");

            builder.HasIndex(b => b.NameAr)
                .HasDatabaseName("IX_Branches_NameAr");

            builder.HasIndex(b => b.IsActive)
                .HasDatabaseName("IX_Branches_IsActive");
        }
    }

    public class AreaConfiguration : IEntityTypeConfiguration<Area>
    {
        public void Configure(EntityTypeBuilder<Area> builder)
        {
            builder.ToTable("Areas");

            builder.HasKey(a => a.Id);

            builder.Property(a => a.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(a => a.NameEn)
                .HasMaxLength(50);

            builder.Property(a => a.Code)
                .HasMaxLength(10);

            builder.Property(a => a.Description)
                .HasMaxLength(200);

            builder.Property(a => a.Longitude)
                .HasColumnType("decimal(18,6)");

            builder.Property(a => a.Latitude)
                .HasColumnType("decimal(18,6)");

            builder.Property(a => a.CoverageRadius)
                .HasColumnType("decimal(18,2)");

            // Self-referencing relationship
            builder.HasOne(a => a.ParentArea)
                .WithMany(a => a.SubAreas)
                .HasForeignKey(a => a.ParentAreaId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(a => a.Code)
                .HasDatabaseName("IX_Areas_Code");

            builder.HasIndex(a => new { a.ParentAreaId, a.DisplayOrder })
                .HasDatabaseName("IX_Areas_ParentAreaId_DisplayOrder");
        }
    }

    public class PriceCategoryConfiguration : IEntityTypeConfiguration<PriceCategory>
    {
        public void Configure(EntityTypeBuilder<PriceCategory> builder)
        {
            builder.ToTable("PriceCategories");

            builder.HasKey(pc => pc.Id);

            builder.Property(pc => pc.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(pc => pc.NameEn)
                .HasMaxLength(50);

            builder.Property(pc => pc.Code)
                .HasMaxLength(10);

            builder.Property(pc => pc.Description)
                .HasMaxLength(200);

            builder.Property(pc => pc.PriceAdjustmentPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(pc => pc.Color)
                .HasMaxLength(7);

            builder.Property(pc => pc.MinimumQuantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(pc => pc.MaximumQuantity)
                .HasColumnType("decimal(18,3)");

            // Indexes
            builder.HasIndex(pc => pc.Code)
                .HasDatabaseName("IX_PriceCategories_Code");

            builder.HasIndex(pc => pc.IsDefault)
                .HasDatabaseName("IX_PriceCategories_IsDefault");
        }
    }

    public class CounterConfiguration : IEntityTypeConfiguration<Counter>
    {
        public void Configure(EntityTypeBuilder<Counter> builder)
        {
            builder.ToTable("Counters");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.CounterName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(c => c.Prefix)
                .HasMaxLength(10);

            builder.Property(c => c.Description)
                .HasMaxLength(200);

            builder.Property(c => c.CodeFormat)
                .HasMaxLength(100);

            // Relationships
            builder.HasOne(c => c.Branch)
                .WithMany(b => b.Counters)
                .HasForeignKey(c => c.BranchId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(c => new { c.CounterName, c.BranchId })
                .IsUnique()
                .HasDatabaseName("IX_Counters_CounterName_BranchId");

            builder.HasIndex(c => c.IsActive)
                .HasDatabaseName("IX_Counters_IsActive");
        }
    }

    public class PaymentMethodConfiguration : IEntityTypeConfiguration<PaymentMethod>
    {
        public void Configure(EntityTypeBuilder<PaymentMethod> builder)
        {
            builder.ToTable("PaymentMethods");

            builder.HasKey(pm => pm.Id);

            builder.Property(pm => pm.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(pm => pm.NameEn)
                .HasMaxLength(50);

            builder.Property(pm => pm.Code)
                .HasMaxLength(10);

            builder.Property(pm => pm.Description)
                .HasMaxLength(200);

            builder.Property(pm => pm.Icon)
                .HasMaxLength(50);

            builder.Property(pm => pm.Color)
                .HasMaxLength(7);

            builder.Property(pm => pm.MinAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(pm => pm.MaxAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(pm => pm.CommissionPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(pm => pm.CommissionAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(pm => pm.Settings)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(pm => pm.Account)
                .WithMany(a => a.PaymentMethods)
                .HasForeignKey(pm => pm.AccountId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(pm => pm.Code)
                .HasDatabaseName("IX_PaymentMethods_Code");

            builder.HasIndex(pm => pm.PaymentType)
                .HasDatabaseName("IX_PaymentMethods_PaymentType");

            builder.HasIndex(pm => pm.IsActive)
                .HasDatabaseName("IX_PaymentMethods_IsActive");
        }
    }

    public class CashBoxConfiguration : IEntityTypeConfiguration<CashBox>
    {
        public void Configure(EntityTypeBuilder<CashBox> builder)
        {
            builder.ToTable("CashBoxes");

            builder.HasKey(cb => cb.Id);

            builder.Property(cb => cb.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cb => cb.Code)
                .HasMaxLength(20);

            builder.Property(cb => cb.CurrentBalance)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(cb => cb.OpeningBalance)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(cb => cb.MinBalance)
                .HasColumnType("decimal(18,2)");

            builder.Property(cb => cb.MaxBalance)
                .HasColumnType("decimal(18,2)");

            builder.Property(cb => cb.Currency)
                .HasMaxLength(3)
                .HasDefaultValue("SAR");

            builder.Property(cb => cb.Location)
                .HasMaxLength(200);

            builder.Property(cb => cb.Description)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(cb => cb.Branch)
                .WithMany(b => b.CashBoxes)
                .HasForeignKey(cb => cb.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(cb => cb.ResponsibleUser)
                .WithMany()
                .HasForeignKey(cb => cb.ResponsibleUserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(cb => cb.Account)
                .WithMany(a => a.CashBoxes)
                .HasForeignKey(cb => cb.AccountId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(cb => cb.Code)
                .HasDatabaseName("IX_CashBoxes_Code");

            builder.HasIndex(cb => new { cb.BranchId, cb.IsActive })
                .HasDatabaseName("IX_CashBoxes_BranchId_IsActive");
        }
    }

    public class CashTransactionConfiguration : IEntityTypeConfiguration<CashTransaction>
    {
        public void Configure(EntityTypeBuilder<CashTransaction> builder)
        {
            builder.ToTable("CashTransactions");

            builder.HasKey(ct => ct.Id);

            builder.Property(ct => ct.TransactionNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(ct => ct.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.BalanceBefore)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.BalanceAfter)
                .HasColumnType("decimal(18,2)");

            builder.Property(ct => ct.Description)
                .HasMaxLength(500);

            builder.Property(ct => ct.Reference)
                .HasMaxLength(50);

            builder.Property(ct => ct.CounterParty)
                .HasMaxLength(200);

            builder.Property(ct => ct.ReceiptNumber)
                .HasMaxLength(20);

            builder.Property(ct => ct.Currency)
                .HasMaxLength(3)
                .HasDefaultValue("SAR");

            builder.Property(ct => ct.ExchangeRate)
                .HasColumnType("decimal(18,6)")
                .HasDefaultValue(1);

            builder.Property(ct => ct.BaseAmount)
                .HasColumnType("decimal(18,2)");

            // Relationships
            builder.HasOne(ct => ct.CashBox)
                .WithMany(cb => cb.Transactions)
                .HasForeignKey(ct => ct.CashBoxId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ct => ct.PaymentMethod)
                .WithMany(pm => pm.CashTransactions)
                .HasForeignKey(ct => ct.PaymentMethodId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(ct => ct.User)
                .WithMany()
                .HasForeignKey(ct => ct.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ct => ct.ConfirmedBy)
                .WithMany()
                .HasForeignKey(ct => ct.ConfirmedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ct => ct.TransactionNumber)
                .IsUnique()
                .HasDatabaseName("IX_CashTransactions_TransactionNumber");

            builder.HasIndex(ct => new { ct.CashBoxId, ct.TransactionDate })
                .HasDatabaseName("IX_CashTransactions_CashBoxId_TransactionDate");

            builder.HasIndex(ct => ct.TransactionType)
                .HasDatabaseName("IX_CashTransactions_TransactionType");
        }
    }
}
