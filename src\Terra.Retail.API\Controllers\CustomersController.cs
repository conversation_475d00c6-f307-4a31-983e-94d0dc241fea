using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Core.Entities;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    /// <summary>
    /// تحكم في العملاء
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class CustomersController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<CustomersController> _logger;

        public CustomersController(TerraRetailDbContext context, ILogger<CustomersController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        /// <returns>قائمة العملاء</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetCustomers()
        {
            try
            {
                var customers = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Area)
                    .Include(c => c.Branch)
                    .Include(c => c.PriceCategory)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        id = c.Id,
                        fullName = c.NameAr,
                        customerCode = c.CustomerCode,
                        phoneNumber = c.Phone1,
                        email = c.Email,
                        address = c.Address,
                        customerTypeId = c.CustomerTypeId,
                        customerTypeName = c.CustomerType != null ? c.CustomerType.NameAr : "غير محدد",
                        areaId = c.AreaId,
                        areaName = c.Area != null ? c.Area.NameAr : "غير محدد",
                        isActive = c.IsActive,
                        createdAt = c.CreatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع {Count} عميل", customers.Count);
                return Ok(customers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع العملاء");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على عميل محدد
        /// </summary>
        /// <param name="id">معرف العميل</param>
        /// <returns>بيانات العميل</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Customer>> GetCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Area)
                    .Include(c => c.Branch)
                    .Include(c => c.PriceCategory)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (customer == null)
                {
                    _logger.LogWarning("العميل غير موجود: {CustomerId}", id);
                    return NotFound(new { message = "العميل غير موجود" });
                }

                _logger.LogInformation("تم استرجاع العميل: {CustomerName}", customer.NameAr);
                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع العميل {CustomerId}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء عميل جديد
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>العميل المنشأ</returns>
        [HttpPost]
        public async Task<ActionResult<Customer>> CreateCustomer(Customer customer)
        {
            try
            {
                // التحقق من وجود كود العميل
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.CustomerCode == customer.CustomerCode);

                if (existingCustomer != null)
                {
                    return BadRequest(new { message = "كود العميل موجود بالفعل" });
                }

                // إنشاء كود تلقائي إذا لم يتم توفيره
                if (string.IsNullOrEmpty(customer.CustomerCode))
                {
                    customer.CustomerCode = await GenerateCustomerCodeAsync();
                }

                customer.CreatedAt = DateTime.UtcNow;
                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء عميل جديد: {CustomerName} - {CustomerCode}", 
                    customer.NameAr, customer.CustomerCode);

                return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء العميل");
                return StatusCode(500, new { message = "حدث خطأ في إنشاء العميل", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث بيانات عميل
        /// </summary>
        /// <param name="id">معرف العميل</param>
        /// <param name="customer">البيانات المحدثة</param>
        /// <returns>نتيجة التحديث</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomer(int id, Customer customer)
        {
            if (id != customer.Id)
            {
                return BadRequest(new { message = "معرف العميل غير متطابق" });
            }

            try
            {
                var existingCustomer = await _context.Customers.FindAsync(id);
                if (existingCustomer == null)
                {
                    return NotFound(new { message = "العميل غير موجود" });
                }

                // تحديث البيانات
                existingCustomer.NameAr = customer.NameAr;
                existingCustomer.NameEn = customer.NameEn;
                existingCustomer.Phone1 = customer.Phone1;
                existingCustomer.Phone2 = customer.Phone2;
                existingCustomer.Email = customer.Email;
                existingCustomer.Address = customer.Address;
                existingCustomer.CustomerTypeId = customer.CustomerTypeId;
                existingCustomer.AreaId = customer.AreaId;
                existingCustomer.PriceCategoryId = customer.PriceCategoryId;
                existingCustomer.DiscountPercentage = customer.DiscountPercentage;
                existingCustomer.CreditLimit = customer.CreditLimit;
                existingCustomer.IdentityNumber = customer.IdentityNumber;
                existingCustomer.TaxNumber = customer.TaxNumber;
                existingCustomer.IsActive = customer.IsActive;
                existingCustomer.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث العميل: {CustomerName}", existingCustomer.NameAr);
                return Ok(new { message = "تم تحديث العميل بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث العميل {CustomerId}", id);
                return StatusCode(500, new { message = "حدث خطأ في تحديث العميل", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        /// <param name="id">معرف العميل</param>
        /// <returns>نتيجة الحذف</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                {
                    return NotFound(new { message = "العميل غير موجود" });
                }

                // التحقق من وجود معاملات للعميل
                var hasTransactions = await _context.Sales.AnyAsync(s => s.CustomerId == id);
                if (hasTransactions)
                {
                    // إلغاء تفعيل بدلاً من الحذف
                    customer.IsActive = false;
                    customer.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم إلغاء تفعيل العميل: {CustomerName}", customer.NameAr);
                    return Ok(new { message = "تم إلغاء تفعيل العميل بنجاح" });
                }
                else
                {
                    _context.Customers.Remove(customer);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("تم حذف العميل: {CustomerName}", customer.NameAr);
                    return Ok(new { message = "تم حذف العميل بنجاح" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف العميل {CustomerId}", id);
                return StatusCode(500, new { message = "حدث خطأ في حذف العميل", error = ex.Message });
            }
        }

        /// <summary>
        /// البحث في العملاء
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <returns>نتائج البحث</returns>
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Customer>>> SearchCustomers([FromQuery] string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return BadRequest(new { message = "يرجى إدخال كلمة البحث" });
                }

                var customers = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Area)
                    .Include(c => c.Branch)
                    .Where(c => c.IsActive &&
                        (c.NameAr.Contains(searchTerm) ||
                         (c.NameEn != null && c.NameEn.Contains(searchTerm)) ||
                         c.CustomerCode.Contains(searchTerm) ||
                         (c.Phone1 != null && c.Phone1.Contains(searchTerm)) ||
                         (c.Email != null && c.Email.Contains(searchTerm))))
                    .OrderBy(c => c.NameAr)
                    .Take(50) // تحديد عدد النتائج
                    .ToListAsync();

                _logger.LogInformation("تم البحث عن '{SearchTerm}' ووجد {Count} نتيجة", searchTerm, customers.Count);
                return Ok(customers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن العملاء");
                return StatusCode(500, new { message = "حدث خطأ في البحث", error = ex.Message });
            }
        }

        /// <summary>
        /// توليد كود عميل تلقائي
        /// </summary>
        /// <returns>كود العميل الجديد</returns>
        private async Task<string> GenerateCustomerCodeAsync()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == "CUSTOMER");

            if (counter == null)
            {
                // إنشاء عداد جديد إذا لم يكن موجوداً
                counter = new Counter
                {
                    CounterName = "CUSTOMER",
                    Prefix = "CUS",
                    CurrentValue = 1,
                    NumberLength = 6,
                    CreatedAt = DateTime.UtcNow
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }
    }
}
