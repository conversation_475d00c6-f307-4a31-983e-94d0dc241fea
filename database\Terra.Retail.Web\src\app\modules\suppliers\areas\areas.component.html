<!-- <PERSON> Retail ERP - Areas -->
<div class="areas-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">المحافظات</h1>
          <p class="page-subtitle">إدارة المحافظات في النظام</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" class="add-btn" (click)="showAddArea()">
          <mat-icon>add</mat-icon>
          <span>إضافة محافظة</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content">
    
    <!-- Add/Edit Form -->
    <mat-card class="form-card" *ngIf="showAddForm">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>{{ editingArea ? 'edit' : 'add' }}</mat-icon>
          <span>{{ editingArea ? 'تعديل المحافظة' : 'إضافة محافظة جديدة' }}</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="areaForm" class="area-form">
          <div class="form-grid">
            
            <!-- Arabic Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم المحافظة بالعربية *</mat-label>
              <input matInput formControlName="nameAr" placeholder="أدخل اسم المحافظة بالعربية" required>
              <mat-icon matSuffix>location_city</mat-icon>
              <mat-error *ngIf="areaForm.get('nameAr')?.hasError('required')">
                اسم المحافظة بالعربية مطلوب
              </mat-error>
              <mat-error *ngIf="areaForm.get('nameAr')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- English Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم المحافظة بالإنجليزية *</mat-label>
              <input matInput formControlName="nameEn" placeholder="Enter area name in English" required>
              <mat-icon matSuffix>location_city</mat-icon>
              <mat-error *ngIf="areaForm.get('nameEn')?.hasError('required')">
                اسم المحافظة بالإنجليزية مطلوب
              </mat-error>
              <mat-error *ngIf="areaForm.get('nameEn')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- Code -->
            <mat-form-field appearance="outline">
              <mat-label>كود المحافظة *</mat-label>
              <input matInput formControlName="code" placeholder="CAI" required maxlength="10" style="text-transform: uppercase;">
              <mat-icon matSuffix>code</mat-icon>
              <mat-error *ngIf="areaForm.get('code')?.hasError('required')">
                كود المحافظة مطلوب
              </mat-error>
              <mat-error *ngIf="areaForm.get('code')?.hasError('minlength')">
                الكود يجب أن يكون حرفين على الأقل
              </mat-error>
              <mat-error *ngIf="areaForm.get('code')?.hasError('maxlength')">
                الكود يجب ألا يزيد عن 10 أحرف
              </mat-error>
            </mat-form-field>

            <!-- Is Active -->
            <div class="checkbox-field">
              <mat-checkbox formControlName="isActive">
                محافظة نشطة
              </mat-checkbox>
            </div>

          </div>
        </form>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button (click)="cancelForm()">
          <mat-icon>cancel</mat-icon>
          <span>إلغاء</span>
        </button>
        <button mat-raised-button color="primary" (click)="saveArea()" [disabled]="isLoading">
          <mat-icon>{{ editingArea ? 'save' : 'add' }}</mat-icon>
          <span>{{ editingArea ? 'تحديث' : 'إضافة' }}</span>
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Areas Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>list</mat-icon>
          <span>قائمة المحافظات</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        
        <!-- Loading Spinner -->
        <div class="loading-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>جاري تحميل البيانات...</p>
        </div>

        <!-- Table -->
        <div class="table-container" *ngIf="!isLoading">
          <table mat-table [dataSource]="areas" class="areas-table">

            <!-- Arabic Name Column -->
            <ng-container matColumnDef="nameAr">
              <th mat-header-cell *matHeaderCellDef>الاسم بالعربية</th>
              <td mat-cell *matCellDef="let area">{{ area.NameAr }}</td>
            </ng-container>

            <!-- English Name Column -->
            <ng-container matColumnDef="nameEn">
              <th mat-header-cell *matHeaderCellDef>الاسم بالإنجليزية</th>
              <td mat-cell *matCellDef="let area">{{ area.NameEn }}</td>
            </ng-container>

            <!-- Code Column -->
            <ng-container matColumnDef="code">
              <th mat-header-cell *matHeaderCellDef>الكود</th>
              <td mat-cell *matCellDef="let area">
                <span class="code-badge">{{ area.Code }}</span>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="isActive">
              <th mat-header-cell *matHeaderCellDef>الحالة</th>
              <td mat-cell *matCellDef="let area">
                <span class="status-badge" [ngClass]="area.IsActive ? 'active' : 'inactive'">
                  {{ area.IsActive ? 'نشط' : 'غير نشط' }}
                </span>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
              <td mat-cell *matCellDef="let area">
                <div class="action-buttons">
                  <button mat-icon-button color="primary" 
                          matTooltip="تعديل"
                          (click)="editArea(area)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" 
                          matTooltip="حذف"
                          (click)="deleteArea(area)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          </table>

          <!-- No Data Message -->
          <div class="no-data" *ngIf="areas.length === 0">
            <mat-icon>location_off</mat-icon>
            <h3>لا توجد محافظات</h3>
            <p>لم يتم إضافة أي محافظات بعد</p>
            <button mat-raised-button color="primary" (click)="showAddArea()">
              <mat-icon>add</mat-icon>
              <span>إضافة محافظة جديدة</span>
            </button>
          </div>

        </div>
      </mat-card-content>
    </mat-card>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري المعالجة...</p>
  </div>

</div>
