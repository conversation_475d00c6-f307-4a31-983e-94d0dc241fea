#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات
Test Database Connection
"""

import pyodbc
import sys

print("🚀 بدء اختبار الاتصال...")

# إعدادات الاتصال
SERVER = 'localhost'
DATABASE = 'TerraRetailERP'
USERNAME = 'sa'
PASSWORD = '@a123admin4'

try:
    # محاولة الاتصال
    CONNECTION_STRING = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};'
    print(f"🔗 محاولة الاتصال بـ: {SERVER}")
    
    conn = pyodbc.connect(CONNECTION_STRING)
    print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
    
    # اختبار استعلام بسيط
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM Suppliers")
    count = cursor.fetchone()[0]
    print(f"📊 عدد الموردين الحالي: {count}")
    
    # اختبار عرض أسماء الموردين
    cursor.execute("SELECT TOP 3 SupplierCode, NameAr FROM Suppliers")
    print("\n📋 عينة من الموردين:")
    for row in cursor.fetchall():
        code, name = row
        print(f"  {code}: {name}")
    
    conn.close()
    print("\n✅ تم إغلاق الاتصال بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    sys.exit(1)

print("🎉 انتهى الاختبار بنجاح!")
