using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Receipts")]
    public class Receipt
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string ReceiptNumber { get; set; } = string.Empty;

        public int CustomerId { get; set; }
        public int BranchId { get; set; }
        public int UserId { get; set; }

        public DateTime ReceiptDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int PaymentMethodId { get; set; }

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        [StringLength(100)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled

        public int? JournalEntryId { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public int? CancelledBy { get; set; }
        public DateTime? CancelledAt { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CancelledBy")]
        public virtual User? CancelledByUser { get; set; }
    }

    [Table("Payments")]
    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string PaymentNumber { get; set; } = string.Empty;

        public int SupplierId { get; set; }
        public int BranchId { get; set; }
        public int UserId { get; set; }

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int PaymentMethodId { get; set; }

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? AccountNumber { get; set; }

        [StringLength(100)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled

        public int? JournalEntryId { get; set; }

        [StringLength(100)]
        public string? ExternalReference { get; set; }

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public int? CancelledBy { get; set; }
        public DateTime? CancelledAt { get; set; }

        [StringLength(1000)]
        public string? CancellationReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; } = null!;

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        [ForeignKey("CancelledBy")]
        public virtual User? CancelledByUser { get; set; }
    }

    [Table("CashBoxes")]
    public class CashBox
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(40)]
        public string Code { get; set; } = string.Empty;

        public int BranchId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaxLimit { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;

        public int? ResponsibleUserId { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public int? ChartAccountId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("ResponsibleUserId")]
        public virtual User? ResponsibleUser { get; set; }

        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount? ChartAccount { get; set; }

        public virtual ICollection<CashBoxTransaction> CashBoxTransactions { get; set; } = new List<CashBoxTransaction>();
    }

    [Table("CashBoxTransactions")]
    public class CashBoxTransaction
    {
        [Key]
        public int Id { get; set; }

        public int CashBoxId { get; set; }

        [Required]
        [StringLength(40)]
        public string TransactionNumber { get; set; } = string.Empty;

        public int TransactionType { get; set; } = 1; // 1=In, 2=Out, 3=Transfer

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceBefore { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int? SourceId { get; set; } // Reference to source transaction

        [StringLength(100)]
        public string? SourceType { get; set; } // Sale, Purchase, Receipt, Payment, etc.

        public int UserId { get; set; }

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("CashBoxId")]
        public virtual CashBox CashBox { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    [Table("BankAccounts")]
    public class BankAccount
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string BankName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? IBAN { get; set; }

        [StringLength(100)]
        public string? SwiftCode { get; set; }

        public int BranchId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        public int? ChartAccountId { get; set; }

        [StringLength(200)]
        public string? ContactPerson { get; set; }

        [StringLength(40)]
        public string? ContactPhone { get; set; }

        [StringLength(200)]
        public string? ContactEmail { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount? ChartAccount { get; set; }

        public virtual ICollection<BankTransaction> BankTransactions { get; set; } = new List<BankTransaction>();
    }

    [Table("BankTransactions")]
    public class BankTransaction
    {
        [Key]
        public int Id { get; set; }

        public int BankAccountId { get; set; }

        [Required]
        [StringLength(40)]
        public string TransactionNumber { get; set; } = string.Empty;

        public int TransactionType { get; set; } = 1; // 1=Deposit, 2=Withdrawal, 3=Transfer

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceBefore { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(200)]
        public string? ReferenceNumber { get; set; }

        public int? SourceId { get; set; } // Reference to source transaction

        [StringLength(100)]
        public string? SourceType { get; set; } // Receipt, Payment, etc.

        public int UserId { get; set; }

        public int Status { get; set; } = 1; // 1=Active, 2=Cancelled

        [Required]
        [StringLength(6)]
        public string Currency { get; set; } = "EGP";

        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("BankAccountId")]
        public virtual BankAccount BankAccount { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
