using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// فواتير المبيعات
    /// </summary>
    public class Sale : BaseEntity
    {
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// اسم العميل (للعملاء غير المسجلين)
        /// </summary>
        [MaxLength(100)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم (البائع)
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// إجمالي الفاتورة قبل الخصم
        /// </summary>
        public decimal SubTotal { get; set; } = 0;

        /// <summary>
        /// نسبة الخصم العام (%)
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الخصم العام
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// نسبة الضريبة (%)
        /// </summary>
        public decimal TaxPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; } = 0;

        /// <summary>
        /// الإجمالي النهائي
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; } = 0;

        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        public SaleStatus Status { get; set; } = SaleStatus.Draft;

        /// <summary>
        /// نوع الفاتورة
        /// </summary>
        public SaleType SaleType { get; set; } = SaleType.Cash;

        /// <summary>
        /// تاريخ الاستحقاق (للفواتير الآجلة)
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// ملاحظات الفاتورة
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية
        /// </summary>
        [MaxLength(1000)]
        public string? InternalNotes { get; set; }

        /// <summary>
        /// رقم مرجعي خارجي
        /// </summary>
        [MaxLength(50)]
        public string? ExternalReference { get; set; }

        /// <summary>
        /// هل الفاتورة مطبوعة
        /// </summary>
        public bool IsPrinted { get; set; } = false;

        /// <summary>
        /// عدد مرات الطباعة
        /// </summary>
        public int PrintCount { get; set; } = 0;

        /// <summary>
        /// هل الفاتورة مرسلة بالإيميل
        /// </summary>
        public bool IsEmailSent { get; set; } = false;

        /// <summary>
        /// هل الفاتورة مرسلة بـ SMS
        /// </summary>
        public bool IsSMSSent { get; set; } = false;

        /// <summary>
        /// تاريخ الإلغاء
        /// </summary>
        public DateTime? CancelledAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي ألغى الفاتورة
        /// </summary>
        public int? CancelledById { get; set; }

        /// <summary>
        /// سبب الإلغاء
        /// </summary>
        [MaxLength(500)]
        public string? CancellationReason { get; set; }

        /// <summary>
        /// معرف نقطة البيع
        /// </summary>
        public int? POSTerminalId { get; set; }

        /// <summary>
        /// رقم الطاولة (للمطاعم)
        /// </summary>
        [MaxLength(20)]
        public string? TableNumber { get; set; }

        // Navigation Properties
        public virtual Customer? Customer { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? CancelledBy { get; set; }
        public virtual ICollection<SaleItem> Items { get; set; } = new List<SaleItem>();
        public virtual ICollection<SalePayment> Payments { get; set; } = new List<SalePayment>();
        public virtual ICollection<SaleReturn> Returns { get; set; } = new List<SaleReturn>();
    }

    /// <summary>
    /// حالة فاتورة المبيعات
    /// </summary>
    public enum SaleStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,

        /// <summary>
        /// مؤكدة
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// مدفوعة جزئياً
        /// </summary>
        PartiallyPaid = 3,

        /// <summary>
        /// مدفوعة بالكامل
        /// </summary>
        FullyPaid = 4,

        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 5,

        /// <summary>
        /// مرتجعة جزئياً
        /// </summary>
        PartiallyReturned = 6,

        /// <summary>
        /// مرتجعة بالكامل
        /// </summary>
        FullyReturned = 7
    }

    /// <summary>
    /// نوع فاتورة المبيعات
    /// </summary>
    public enum SaleType
    {
        /// <summary>
        /// نقدي
        /// </summary>
        Cash = 1,

        /// <summary>
        /// آجل
        /// </summary>
        Credit = 2,

        /// <summary>
        /// مختلط (نقدي + آجل)
        /// </summary>
        Mixed = 3,

        /// <summary>
        /// عرض أسعار
        /// </summary>
        Quotation = 4,

        /// <summary>
        /// أمر بيع
        /// </summary>
        SalesOrder = 5
    }
}
