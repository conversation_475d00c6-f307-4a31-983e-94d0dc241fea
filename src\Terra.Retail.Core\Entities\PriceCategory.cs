using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// فئات الأسعار
    /// </summary>
    public class PriceCategory : BaseEntity
    {
        /// <summary>
        /// اسم فئة السعر بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم فئة السعر بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود فئة السعر
        /// </summary>
        [MaxLength(10)]
        public string? Code { get; set; }

        /// <summary>
        /// وصف فئة السعر
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// نسبة الزيادة/النقص على السعر الأساسي (%)
        /// </summary>
        public decimal PriceAdjustmentPercentage { get; set; } = 0;

        /// <summary>
        /// هل الفئة افتراضية
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// هل الفئة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// لون مميز للفئة (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// الحد الأدنى للكمية لتطبيق هذه الفئة
        /// </summary>
        public decimal? MinimumQuantity { get; set; }

        /// <summary>
        /// الحد الأقصى للكمية لتطبيق هذه الفئة
        /// </summary>
        public decimal? MaximumQuantity { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<ProductBranchPrice> ProductBranchPrices { get; set; } = new List<ProductBranchPrice>();
    }
}
