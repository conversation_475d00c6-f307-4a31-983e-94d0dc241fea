-- 🏪 بيانات تجريبية كاملة لسنتر مفروشات وأدوات منزلية وكهربائية - مصر
-- تم إنشاؤها بعناية مع دعم اللغة العربية الكامل
-- UTF-8 Encoding Support

-- 🗑️ مسح البيانات القديمة أولاً
DELETE FROM JournalEntryDetails;
DELETE FROM JournalEntries; 
DELETE FROM CashReceipts;
DELETE FROM CashPayments;
DELETE FROM FinancialTransactions;
DELETE FROM ActivityLog;
UPDATE Counters SET CurrentValue = 0;

-- 🏢 إنشاء الفروع في مصر
INSERT INTO Branches (Code, NameAr, NameEn, Address, Phone, Email, IsActive, IsMainBranch, CreatedAt) VALUES
('BR001', N'الفرع الرئيسي - القاهرة', 'Main Branch - Cairo', N'شارع التحرير، وسط البلد، القاهرة', '02-25555555', '<EMAIL>', 1, 1, GETDATE()),
('BR002', N'فرع الإسكندرية', 'Alexandria Branch', N'شارع الكورنيش، الإسكندرية', '03-4888888', '<EMAIL>', 1, 0, GETDATE()),
('BR003', N'فرع الجيزة', 'Giza Branch', N'شارع الهرم، الجيزة', '02-33777777', '<EMAIL>', 1, 0, GETDATE());

-- 👥 إنشاء المستخدمين
INSERT INTO Users (Username, Email, PasswordHash, FullName, IsActive, CreatedAt) VALUES
('admin', '<EMAIL>', 'hashed_password_123', N'أحمد محمد الإدارة', 1, GETDATE()),
('sales1', '<EMAIL>', 'hashed_password_456', N'فاطمة علي المبيعات', 1, GETDATE()),
('cashier1', '<EMAIL>', 'hashed_password_789', N'محمد سعد الكاشير', 1, GETDATE()),
('manager1', '<EMAIL>', 'hashed_password_101', N'سارة أحمد المدير', 1, GETDATE());

-- 🏷️ إنشاء فئات المنتجات
INSERT INTO Categories (NameAr, NameEn, ParentId, IsActive, CreatedAt) VALUES
(N'غرف النوم', 'Bedrooms', NULL, 1, GETDATE()),
(N'غرف المعيشة', 'Living Rooms', NULL, 1, GETDATE()),
(N'غرف الطعام', 'Dining Rooms', NULL, 1, GETDATE()),
(N'المطابخ', 'Kitchens', NULL, 1, GETDATE()),
(N'الأدوات الكهربائية', 'Electrical Appliances', NULL, 1, GETDATE()),
(N'الأدوات المنزلية', 'Home Accessories', NULL, 1, GETDATE()),
(N'الإضاءة', 'Lighting', NULL, 1, GETDATE()),
(N'السجاد والموكيت', 'Carpets & Rugs', NULL, 1, GETDATE());

-- 📏 إنشاء وحدات القياس
INSERT INTO Units (UnitCode, NameAr, NameEn, IsActive, CreatedAt) VALUES
('PCS', N'قطعة', 'Piece', 1, GETDATE()),
('SET', N'طقم', 'Set', 1, GETDATE()),
('M2', N'متر مربع', 'Square Meter', 1, GETDATE()),
('KG', N'كيلوجرام', 'Kilogram', 1, GETDATE()),
('BOX', N'صندوق', 'Box', 1, GETDATE()),
('PAIR', N'زوج', 'Pair', 1, GETDATE());

-- 💰 إنشاء فئات الأسعار
INSERT INTO PriceCategories (NameAr, NameEn, DiscountPercentage, IsActive, CreatedAt) VALUES
(N'سعر التجزئة', 'Retail Price', 0.00, 1, GETDATE()),
(N'سعر الجملة', 'Wholesale Price', 15.00, 1, GETDATE()),
(N'عملاء مميزين', 'VIP Customers', 25.00, 1, GETDATE()),
(N'سعر الموظفين', 'Employee Price', 30.00, 1, GETDATE());

-- 🏪 إنشاء أنواع العملاء
INSERT INTO CustomerTypes (TypeCode, NameAr, NameEn, IsActive, CreatedAt) VALUES
('RETAIL', N'عميل تجزئة', 'Retail Customer', 1, GETDATE()),
('WHOLESALE', N'عميل جملة', 'Wholesale Customer', 1, GETDATE()),
('VIP', N'عميل مميز', 'VIP Customer', 1, GETDATE()),
('CORPORATE', N'عميل مؤسسي', 'Corporate Customer', 1, GETDATE());

-- 🏭 إنشاء أنواع الموردين
INSERT INTO SupplierTypes (TypeCode, NameAr, NameEn, IsActive, CreatedAt) VALUES
('LOCAL', N'مورد محلي', 'Local Supplier', 1, GETDATE()),
('IMPORT', N'مورد مستورد', 'Import Supplier', 1, GETDATE()),
('FACTORY', N'مصنع', 'Factory', 1, GETDATE()),
('AGENT', N'وكيل تجاري', 'Commercial Agent', 1, GETDATE());

-- 🌍 إنشاء المناطق
INSERT INTO Areas (AreaCode, NameAr, NameEn, IsActive, CreatedAt) VALUES
('CAIRO', N'القاهرة', 'Cairo', 1, GETDATE()),
('ALEX', N'الإسكندرية', 'Alexandria', 1, GETDATE()),
('GIZA', N'الجيزة', 'Giza', 1, GETDATE()),
('SHARM', N'شرم الشيخ', 'Sharm El Sheikh', 1, GETDATE()),
('HURGHADA', N'الغردقة', 'Hurghada', 1, GETDATE());

-- 💳 إنشاء طرق الدفع
INSERT INTO PaymentMethods (MethodCode, NameAr, NameEn, IsActive, CreatedAt) VALUES
('CASH', N'نقدي', 'Cash', 1, GETDATE()),
('VISA', N'فيزا', 'Visa Card', 1, GETDATE()),
('MASTER', N'ماستر كارد', 'Master Card', 1, GETDATE()),
('INSTALLMENT', N'تقسيط', 'Installment', 1, GETDATE()),
('BANK_TRANSFER', N'تحويل بنكي', 'Bank Transfer', 1, GETDATE());

-- 🏭 إنشاء الموردين
INSERT INTO Suppliers (SupplierCode, NameAr, NameEn, SupplierTypeId, Phone1, Phone2, Email, Website, Address, AreaId, ContactPersonName, ContactPersonPhone, PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance, IsActive, CreatedAt) VALUES
('SUP001', N'شركة الأثاث المصري', 'Egyptian Furniture Co.', 1, '02-********', '02-********', '<EMAIL>', 'www.egyptfurniture.com', N'المنطقة الصناعية، القاهرة', 1, N'محمد أحمد', '***********', 30, 100000.00, 0.00, 0.00, 1, GETDATE()),
('SUP002', N'مصنع الكهربائيات الحديثة', 'Modern Electronics Factory', 3, '03-5678901', '03-5678902', '<EMAIL>', 'www.modernelec.com', N'برج العرب، الإسكندرية', 2, N'أحمد علي', '***********', 45, 150000.00, 0.00, 0.00, 1, GETDATE()),
('SUP003', N'شركة الأدوات المنزلية المتحدة', 'United Home Tools Co.', 2, '02-********', NULL, '<EMAIL>', NULL, N'مدينة نصر، القاهرة', 1, N'فاطمة محمد', '01076543210', 30, 80000.00, 0.00, 0.00, 1, GETDATE()),
('SUP004', N'مستوردو الإضاءة الأوروبية', 'European Lighting Importers', 2, '02-25678901', '02-25678902', '<EMAIL>', 'www.eurolighting.com', N'مصر الجديدة، القاهرة', 1, N'سارة أحمد', '01055555555', 60, 200000.00, 0.00, 0.00, 1, GETDATE()),
('SUP005', N'مصنع السجاد الشرقي', 'Oriental Carpet Factory', 3, '02-44567890', NULL, '<EMAIL>', NULL, N'حلوان، القاهرة', 1, N'عبدالله محمد', '01087654321', 30, 120000.00, 0.00, 0.00, 1, GETDATE());

-- 👥 إنشاء العملاء
INSERT INTO Customers (CustomerCode, NameAr, NameEn, CustomerTypeId, Phone1, Phone2, Email, Address, AreaId, BranchId, PriceCategoryId, DiscountPercentage, OpeningBalance, CurrentBalance, CreditLimit, IsActive, CreatedAt) VALUES
('CUS001', N'أحمد محمد علي', 'Ahmed Mohamed Ali', 1, '***********', '02-25555555', '<EMAIL>', N'شارع الجمهورية، وسط البلد، القاهرة', 1, 1, 1, 0.00, 0.00, 0.00, 5000.00, 1, GETDATE()),
('CUS002', N'فاطمة أحمد حسن', 'Fatima Ahmed Hassan', 1, '***********', NULL, '<EMAIL>', N'شارع الكورنيش، الإسكندرية', 2, 2, 1, 0.00, 0.00, 0.00, 3000.00, 1, GETDATE()),
('CUS003', N'شركة الفنادق الذهبية', 'Golden Hotels Company', 4, '02-********', '02-33456790', '<EMAIL>', N'شارع النيل، الجيزة', 3, 3, 2, 15.00, 0.00, 0.00, 50000.00, 1, GETDATE()),
('CUS004', N'محمد سعد الدين', 'Mohamed Saad El Din', 3, '01076543210', NULL, '<EMAIL>', N'شارع التحرير، القاهرة', 1, 1, 3, 25.00, 0.00, 0.00, 10000.00, 1, GETDATE()),
('CUS005', N'سارة علي محمود', 'Sara Ali Mahmoud', 1, '01055555555', '03-4888888', '<EMAIL>', N'شارع سعد زغلول، الإسكندرية', 2, 2, 1, 0.00, 0.00, 0.00, 4000.00, 1, GETDATE()),
('CUS006', N'شركة المقاولات الكبرى', 'Major Contracting Company', 4, '02-44567890', '02-44567891', '<EMAIL>', N'مدينة نصر، القاهرة', 1, 1, 2, 15.00, 0.00, 0.00, 100000.00, 1, GETDATE());

-- 🛏️ إنشاء المنتجات - غرف النوم
INSERT INTO Products (ProductCode, NameAr, NameEn, Description, CategoryId, UnitId, Barcode, CostPrice, BasePrice, ProfitMargin, MinimumStock, MaximumStock, IsActive, CreatedAt) VALUES
('PRD001', N'سرير خشب زان مقاس 180×200', 'Beech Wood Bed 180x200', N'سرير من خشب الزان الطبيعي مع كومودينو', 1, 1, '1234567890123', 2500.00, 3500.00, 40.00, 5, 50, 1, GETDATE()),
('PRD002', N'دولاب ملابس 6 أبواب', '6-Door Wardrobe', N'دولاب ملابس من الخشب المضغوط مع مرآة', 1, 1, '1234567890124', 3000.00, 4200.00, 40.00, 3, 30, 1, GETDATE()),
('PRD003', N'تسريحة مع مرآة ومقعد', 'Dressing Table with Mirror', N'تسريحة خشبية مع مرآة كبيرة ومقعد مبطن', 1, 1, '1234567890125', 1200.00, 1680.00, 40.00, 5, 40, 1, GETDATE()),

-- 🛋️ إنشاء المنتجات - غرف المعيشة
('PRD004', N'طقم صالون جلد طبيعي 3+2+1', 'Leather Sofa Set 3+2+1', N'طقم صالون من الجلد الطبيعي مع إطار خشبي', 2, 2, '1234567890126', 8000.00, 12000.00, 50.00, 2, 20, 1, GETDATE()),
('PRD005', N'طاولة وسط زجاجية', 'Glass Coffee Table', N'طاولة وسط من الزجاج المقسى مع قاعدة معدنية', 2, 1, '1234567890127', 800.00, 1200.00, 50.00, 5, 30, 1, GETDATE()),
('PRD006', N'مكتبة خشبية 5 أرفف', '5-Shelf Wooden Bookcase', N'مكتبة من الخشب الطبيعي بـ 5 أرفف', 2, 1, '1234567890128', 1500.00, 2100.00, 40.00, 3, 25, 1, GETDATE()),

-- 🍽️ إنشاء المنتجات - غرف الطعام
('PRD007', N'طاولة طعام خشبية 8 أشخاص', 'Wooden Dining Table 8 Seats', N'طاولة طعام من الخشب الطبيعي تتسع لـ 8 أشخاص', 3, 1, '1234567890129', 2000.00, 2800.00, 40.00, 2, 15, 1, GETDATE()),
('PRD008', N'كراسي طعام مبطنة - 6 قطع', 'Upholstered Dining Chairs - 6pcs', N'مجموعة من 6 كراسي طعام مبطنة بالقماش', 3, 2, '1234567890130', 1800.00, 2520.00, 40.00, 3, 20, 1, GETDATE()),
('PRD009', N'بوفيه مع مرآة', 'Buffet with Mirror', N'بوفيه خشبي مع مرآة وأدراج للتخزين', 3, 1, '1234567890131', 2500.00, 3500.00, 40.00, 2, 12, 1, GETDATE()),

-- 🔌 إنشاء المنتجات - الأدوات الكهربائية
('PRD010', N'ثلاجة سامسونج 18 قدم', 'Samsung Refrigerator 18ft', N'ثلاجة سامسونج نوفروست 18 قدم مع فريزر', 5, 1, '1234567890132', 8000.00, 11200.00, 40.00, 5, 25, 1, GETDATE()),
('PRD011', N'غسالة أتوماتيك LG 7 كيلو', 'LG Automatic Washer 7kg', N'غسالة أتوماتيك من LG سعة 7 كيلو', 5, 1, '1234567890133', 4500.00, 6300.00, 40.00, 3, 20, 1, GETDATE()),
('PRD012', N'مكيف شارب 1.5 حصان', 'Sharp AC 1.5 HP', N'مكيف هواء شارب 1.5 حصان بارد فقط', 5, 1, '1234567890134', 3000.00, 4200.00, 40.00, 5, 30, 1, GETDATE()),

-- 🏠 إنشاء المنتجات - الأدوات المنزلية
('PRD013', N'طقم أواني طبخ تيفال 12 قطعة', 'Tefal Cookware Set 12pcs', N'طقم أواني طبخ تيفال مع طلاء تيفلون', 6, 2, '1234567890135', 800.00, 1120.00, 40.00, 10, 50, 1, GETDATE()),
('PRD014', N'مجموعة أطباق بورسلين 24 قطعة', 'Porcelain Dinnerware Set 24pcs', N'مجموعة أطباق من البورسلين الفاخر', 6, 2, '1234567890136', 600.00, 840.00, 40.00, 8, 40, 1, GETDATE()),
('PRD015', N'مكنسة كهربائية توشيبا', 'Toshiba Vacuum Cleaner', N'مكنسة كهربائية توشيبا بقوة شفط عالية', 6, 1, '1234567890137', 1200.00, 1680.00, 40.00, 5, 25, 1, GETDATE()),

-- 💡 إنشاء المنتجات - الإضاءة
('PRD016', N'نجفة كريستال كلاسيكية', 'Classic Crystal Chandelier', N'نجفة من الكريستال الطبيعي تصميم كلاسيكي', 7, 1, '1234567890138', 2000.00, 2800.00, 40.00, 3, 15, 1, GETDATE()),
('PRD017', N'أباجورة طاولة LED', 'LED Table Lamp', N'أباجورة طاولة بإضاءة LED قابلة للتحكم', 7, 1, '1234567890139', 300.00, 420.00, 40.00, 10, 50, 1, GETDATE()),
('PRD018', N'إضاءة سقف دائرية LED', 'Round LED Ceiling Light', N'إضاءة سقف دائرية بتقنية LED موفرة للطاقة', 7, 1, '1234567890140', 500.00, 700.00, 40.00, 8, 40, 1, GETDATE()),

-- 🏺 إنشاء المنتجات - السجاد والموكيت
('PRD019', N'سجادة فارسية 3×4 متر', 'Persian Carpet 3x4m', N'سجادة فارسية أصلية مصنوعة يدوياً', 8, 1, '1234567890141', 3000.00, 4200.00, 40.00, 2, 10, 1, GETDATE()),
('PRD020', N'موكيت تركي مقاس 2×3 متر', 'Turkish Carpet 2x3m', N'موكيت تركي عالي الجودة بألوان متنوعة', 8, 1, '1234567890142', 1500.00, 2100.00, 40.00, 5, 25, 1, GETDATE());

PRINT N'تم إنشاء المنتجات بنجاح ✅';
