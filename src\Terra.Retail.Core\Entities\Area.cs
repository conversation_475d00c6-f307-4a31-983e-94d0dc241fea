using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// المناطق الجغرافية
    /// </summary>
    public class Area : BaseEntity
    {
        /// <summary>
        /// اسم المنطقة بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المنطقة بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود المنطقة
        /// </summary>
        [MaxLength(10)]
        public string? Code { get; set; }

        /// <summary>
        /// المنطقة الأب (للمناطق الفرعية)
        /// </summary>
        public int? ParentAreaId { get; set; }

        /// <summary>
        /// وصف المنطقة
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// هل المنطقة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// إحداثيات المنطقة (خط الطول)
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// إحداثيات المنطقة (خط العرض)
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// نطاق التغطية بالكيلومتر
        /// </summary>
        public decimal? CoverageRadius { get; set; }

        // Navigation Properties
        public virtual Area? ParentArea { get; set; }
        public virtual ICollection<Area> SubAreas { get; set; } = new List<Area>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }
}
