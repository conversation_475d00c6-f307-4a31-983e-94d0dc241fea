using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Shifts")]
    public class Shift
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string ShiftName { get; set; } = string.Empty;

        [Required]
        [StringLength(40)]
        public string ShiftCode { get; set; } = string.Empty;

        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }

        public bool IsOvernightShift { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal WorkingHours { get; set; }

        public int BreakDuration { get; set; } = 0; // in minutes
        public int GracePeriodMinutes { get; set; } = 15;
        public int OvertimeAfterMinutes { get; set; } = 0;

        public int BranchId { get; set; }
        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
    }

    [Table("EmployeeShifts")]
    public class EmployeeShift
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsTemporary { get; set; } = false;
        public int AssignedBy { get; set; }
        public DateTime AssignedAt { get; set; } = DateTime.Now;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; } = null!;

        [ForeignKey("AssignedBy")]
        public virtual User AssignedByUser { get; set; } = null!;
    }

    [Table("AttendanceRecords")]
    public class AttendanceRecord
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime ShiftDate { get; set; }

        // Actual Times
        public DateTime? ActualCheckInTime { get; set; }
        public DateTime? ActualCheckOutTime { get; set; }

        // Planned Times
        public DateTime PlannedCheckInTime { get; set; }
        public DateTime PlannedCheckOutTime { get; set; }

        // Calculations
        public int WorkingMinutes { get; set; } = 0;
        public int LateMinutes { get; set; } = 0;
        public int EarlyLeaveMinutes { get; set; } = 0;
        public int OvertimeMinutes { get; set; } = 0;
        public int BreakMinutes { get; set; } = 0;

        // Status
        [Required]
        [StringLength(40)]
        public string AttendanceStatus { get; set; } = "Absent"; // Present, Absent, Late, etc.

        public bool IsComplete { get; set; } = false;
        public bool IsOvernightShift { get; set; } = false;

        // Biometric Info
        public int? CheckInDeviceId { get; set; }
        public int? CheckOutDeviceId { get; set; }
        public bool IsManualEntry { get; set; } = false;

        [StringLength(400)]
        public string? ManualEntryReason { get; set; }

        public int? ApprovedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModified { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }
    }

    [Table("LeaveTypes")]
    public class LeaveType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int MaxDaysPerYear { get; set; }
        public bool IsPaid { get; set; } = true;
        public bool RequireApproval { get; set; } = true;
        public int MinAdvanceNoticeDays { get; set; } = 0;
        public bool CanCarryForward { get; set; } = false;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = new List<EmployeeLeave>();
        public virtual ICollection<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; } = new List<EmployeeLeaveBalance>();
    }

    [Table("EmployeeLeaves")]
    public class EmployeeLeave
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int LeaveTypeId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalDays { get; set; }

        [Required]
        [StringLength(1000)]
        public string Reason { get; set; } = string.Empty;

        [StringLength(40)]
        public string LeaveStatus { get; set; } = "Pending"; // Pending, Approved, Rejected, Cancelled

        public int? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }

        [StringLength(1000)]
        public string? ApprovalNotes { get; set; }

        public DateTime RequestDate { get; set; } = DateTime.Now;
        public int RequestedBy { get; set; }

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("LeaveTypeId")]
        public virtual LeaveType LeaveType { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        [ForeignKey("RequestedBy")]
        public virtual User RequestedByUser { get; set; } = null!;
    }

    [Table("EmployeeLeaveBalances")]
    public class EmployeeLeaveBalance
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int LeaveTypeId { get; set; }
        public int Year { get; set; }
        public int EntitledDays { get; set; }
        public int UsedDays { get; set; } = 0;
        public int CarriedForwardDays { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Computed Property
        public int RemainingDays => EntitledDays + CarriedForwardDays - UsedDays;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("LeaveTypeId")]
        public virtual LeaveType LeaveType { get; set; } = null!;
    }

    [Table("Payrolls")]
    public class Payroll
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime PayrollDate { get; set; }

        // Earnings
        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Allowances { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OvertimeAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Bonuses { get; set; } = 0;

        // Deductions
        [Column(TypeName = "decimal(18,2)")]
        public decimal SocialInsurance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal IncomeTax { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Penalties { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Advances { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherDeductions { get; set; } = 0;

        // Computed Properties
        public decimal GrossAmount => BasicSalary + Allowances + OvertimeAmount + Bonuses;
        public decimal TotalDeductions => SocialInsurance + IncomeTax + Penalties + Advances + OtherDeductions;
        public decimal NetAmount => GrossAmount - TotalDeductions;

        [StringLength(40)]
        public string PayrollStatus { get; set; } = "Calculated"; // Calculated, Approved, Paid

        public bool IsPaid { get; set; } = false;
        public DateTime? PaidDate { get; set; }
        public int? PaidBy { get; set; }

        [StringLength(40)]
        public string? PaymentMethod { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("PaidBy")]
        public virtual User? PaidByUser { get; set; }
    }
}
