

# Preferences
- User prefers ASP.NET with Angular and C# for ERP development.
- User prefers autonomous completion of applications without frequent check-backs.
- User prefers professional global interface with login page and branch selection functionality.
- User prefers the old Angular interface design over new updates - the old design is what's working for them.
- User prefers attractive global design for Angular app with better sidebar, missing icons to be added, and all features connected to database instead of demo data.
- User requires fixing Arabic text display in database for a high-quality Angular theme and proper Arabic text encoding in database.
- User prefers Egyptian localization instead of Saudi data, and requires beautiful frontend with main and sub navigation pages in the sidebar.
- User prefers professional global-standard Angular design that is well-organized and cohesive, specifically wants the add product screen to be made more professional.
- User prefers professional, beautiful Angular interfaces with high-quality themes and designs to be rebuilt from scratch.
- User prefers to remove fallback/demo data completely from the ERP system - wants only real database data to be displayed.
- User prefers clean, well-organized page layouts without overlapping elements.
- User prefers to rebuild interfaces from scratch with new designs while keeping existing files as backup for potential future reference.
- User prefers simple solutions - when fixing dropdown display issues, they only wanted a white background for dropdown data, not complex z-index and overlay modifications.
- User prefers to organize pages systematically and work on completing one section at a time rather than working on multiple sections simultaneously.
- User prefers to complete modules entirely with full CRUD operations (add, edit, delete) rather than partial implementations.
- User prefers Python code for database operations with Arabic language support over direct SQL files.
- User prefers required fields to be displayed in red color as visual indicators and grouped together within their respective sections for better visual organization.
- User wants organized form layouts with proper field positioning.
- User expects + buttons next to dropdowns to open dedicated pages for adding new items (like supplier types).
- User prefers form fields grouped by related data categories, wants only the border/outline of required fields to be red (not background boxes), and wants improved styling for form field containers while ensuring all components are connected to the database.
- User wants auto-translation from Arabic to English for supplier names, using a pattern like 'الصعيدي للادوات المنزلية' to 'Alsaydy Lladwat Almnzlyh'.
- User prefers to always add full CRUD operations (create, read, update, delete) when implementing any feature.
- User wants 'محافظة' changed to 'منطقة' and 'البلد' to 'المحافظة' in Angular.
- User wants better test data in database.
- User prefers to keep previous themes as backup when making UI changes so they can revert if needed.
- User prefers not to have separate customer/supplier account tables - wants to read financial data from the general chart of accounts instead of dedicated tables.
- User prefers transaction-based accounting system with codes rather than separate columns for each account to keep database structure scalable and manageable.
- User prefers supplier accounts to have a known main account number in one column and sub-account details in another column, rather than creating separate tables for each account type.
- User prefers comprehensive accounting systems with full chart of accounts tree structure and complete financial transaction implementation, wants to expand on counter examples provided to create a complete professional system.
- User prefers professional, comprehensive implementations over basic examples when developing transaction types and database content.
- User prefers transaction type 58 to be used for closing entries (إقفال) rather than opening balances (رصيد افتتاحي) in the accounting system.

# Development Environment
- Uses SQL Server with localhost/sa/@a123admin4.
- Requires continuous terminal monitoring of running processes.
- User prefers SQL creation code to be executed in the terminal rather than opening external applications.
- User prefers to execute Python scripts and SQL code directly in the current terminal environment rather than opening them in external applications or different execution environments.
- User prefers to execute code directly in the terminal rather than writing scripts to separate files.

# ERP Requirements
- Requires comprehensive ERP with sales, products, suppliers, inventory, invoices, discount coupons, cash discounts, promotions, employees, complete professional financial system, audit trails for all changes, counters system, bank accounts, cash receipts/payments, trial balance, statistics, and comprehensive test data.
- Requires comprehensive retail ERP with modules for customers, products, POS, sales, purchases, suppliers, employees, inventory, finance, treasury, reports, and settings.
- User provided complete Terra Retail ERP specifications including 12 modules (Customers, Products, POS, Sales, Purchases, Suppliers, Employees, Inventory, Finance, Treasury, Reports, Settings) with detailed features, database tables, APIs, and technical requirements for ASP.NET/Angular/C# development with SQL Server localhost/sa/@a123admin4.
- Requires automatic product code generation with specific patterns: local products start from *************, international products use manual codes, weighted products use automatic codes for scale integration.
- Products need primary supplier plus multiple suppliers.
- UI needs improvement with better spacing and tables.
- Must rely completely on database with no dummy data.
- Wants suppliers module to have additional sub-pages with functionality and prefers different page design/layout than current implementation.
- Requires suppliers module with sub-pages: supplier list, supplier accounts, supplier payments, statistics, and wants functional + buttons for supplier types, areas, and countries with full CRUD operations connected to database tables via API and Angular.
- Supplier types and areas to be loaded from database tables instead of hardcoded values.
- Requires SQL scripts for proper Arabic language database setup.
- Arabic text in database is displaying as garbled characters (encoding issue) - need to ensure proper UTF-8 encoding for Arabic text in database operations.
- User requires automatic supplier code generation that increments from the last used code and uses atomic counter operations to handle concurrent supplier creation safely.
- User expects supplier codes to be assigned sequentially by the counter system and maintain the same code that was generated during creation (e.g., code 42 should remain 42).
- User requires that all previously existing required data fields are displayed, the application must not save without essential required fields, and wants complete CRUD operations (create, read, update, delete) connected to the database.
- Requires fixing empty countries/areas data in the suppliers module.
- Requires enabling the supplier statistics page.
- Requires creating initial product categories linked to suppliers instead of dummy data.
- User's database name is TerraRetailERP.
- User wants all supplier features fully functional.
- Requires financial integration where supplier financial data becomes opening balances in the accounting module linked to supplier accounts.
- User wants professional Angular themes and UI updates.
- Requires comprehensive financial accounting system with opening balances, journal entries, adjustments, payments, receipts, and full accounting capabilities integrated with supplier accounts.
- User requires that every type/entity in Angular should have a corresponding database table.
- User requires multi-tier pricing per product linked to branches and customers.
- User requires products sourced from multiple suppliers.
- User requires inter-branch transfers functionality.
- Requires customer records with Arabic/English names, dual phone numbers, email, address, customer types (as separate table), price categories linked to branches with default settings, and financial integration where opening balances automatically connect to accounting system.

# Database Operations & Security
- User requires all database operations to include: counter for auto-incrementing IDs, IsDeleted flag for soft deletes, and tracking which user created each record.
- Requires isDeleted flag for soft deletes.
- User expects database records to track who created them (audit trail functionality).
- User wants comprehensive audit tracking for all create/update/delete operations plus permissions system for all database tables and functionality.