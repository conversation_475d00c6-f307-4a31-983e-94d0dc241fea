using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// ربط المستخدمين بالفروع
    /// </summary>
    public class UserBranch : BaseEntity
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// هل المستخدم له صلاحية الوصول للفرع
        /// </summary>
        public bool HasAccess { get; set; } = true;

        /// <summary>
        /// هل الفرع افتراضي للمستخدم
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// تاريخ منح الوصول
        /// </summary>
        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي منح الوصول
        /// </summary>
        public int? GrantedById { get; set; }

        /// <summary>
        /// تاريخ انتهاء الوصول
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// ملاحظات حول الوصول
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual User? GrantedBy { get; set; }
    }
}
