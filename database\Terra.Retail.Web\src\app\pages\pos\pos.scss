.pos-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  position: relative;

  .pos-header {
    background: white;
    padding: 16px 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e0e0e0;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        h1 {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 4px 0;
          color: #1976d2;
          font-size: 1.8rem;
          font-weight: 600;

          .pos-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }

        p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .pos-main {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow: hidden;

    .products-panel {
      flex: 2;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .search-section {
        .search-field {
          width: 100%;
        }
      }

      .categories-section {
        .category-chips {
          mat-chip-set {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            mat-chip {
              background: #e3f2fd;
              color: #1976d2;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: #bbdefb;
              }

              &[selected], &.selected {
                background: #1976d2 !important;
                color: white !important;
              }
            }
          }
        }
      }

      .products-grid {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
        overflow-y: auto;
        padding: 8px;

        .product-card {
          background: white;
          border-radius: 12px;
          padding: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            border-color: #1976d2;
          }

          &.out-of-stock {
            opacity: 0.6;
            cursor: not-allowed;

            &:hover {
              transform: none;
              border-color: transparent;
            }
          }

          .product-image {
            text-align: center;
            margin-bottom: 12px;

            mat-icon {
              font-size: 3rem;
              width: 3rem;
              height: 3rem;
              color: #1976d2;
            }
          }

          .product-info {
            text-align: center;

            h4 {
              margin: 0 0 8px 0;
              font-size: 1rem;
              font-weight: 600;
              color: #333;
              line-height: 1.3;
            }

            .product-code {
              margin: 0 0 8px 0;
              font-size: 0.8rem;
              color: #666;
            }

            .product-price {
              font-size: 1.2rem;
              font-weight: 700;
              color: #4caf50;
              margin-bottom: 8px;
            }

            .product-stock {
              font-size: 0.8rem;
              color: #666;

              &.low-stock {
                color: #ff9800;
                font-weight: 600;
              }
            }
          }
        }
      }
    }

    .cart-panel {
      flex: 1;
      min-width: 400px;
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      gap: 20px;

      .customer-section {
        .customer-field {
          width: 100%;
        }
      }

      .cart-section {
        flex: 1;
        display: flex;
        flex-direction: column;

        .cart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h3 {
            margin: 0;
            color: #333;
            font-size: 1.2rem;
          }

          .items-count {
            background: #1976d2;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.8rem;
            font-weight: 600;
          }
        }

        .cart-items {
          flex: 1;
          overflow-y: auto;
          max-height: 300px;

          .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 8px;
            background: #fafafa;

            .item-info {
              flex: 1;

              h4 {
                margin: 0 0 4px 0;
                font-size: 0.9rem;
                color: #333;
              }

              p {
                margin: 0;
                font-size: 0.8rem;
                color: #666;
              }
            }

            .item-controls {
              display: flex;
              align-items: center;
              gap: 12px;

              .quantity-controls {
                display: flex;
                align-items: center;
                gap: 8px;
                background: white;
                border-radius: 20px;
                padding: 4px;

                .quantity {
                  min-width: 30px;
                  text-align: center;
                  font-weight: 600;
                }
              }

              .item-price {
                font-weight: 600;
                color: #4caf50;
                min-width: 80px;
                text-align: right;
              }
            }
          }
        }

        .empty-cart {
          text-align: center;
          padding: 40px 20px;
          color: #666;

          mat-icon {
            font-size: 4rem;
            width: 4rem;
            height: 4rem;
            color: #ccc;
            margin-bottom: 16px;
          }

          p {
            margin: 8px 0;
          }
        }
      }

      .totals-section {
        border-top: 1px solid #e0e0e0;
        padding-top: 16px;

        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.9rem;

          &.discount {
            color: #ff9800;
          }

          &.final {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1976d2;
            border-top: 1px solid #e0e0e0;
            padding-top: 8px;
            margin-top: 8px;
          }
        }
      }

      .payment-section {
        .payment-methods {
          display: flex;
          gap: 8px;
          margin-bottom: 16px;

          .payment-btn {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 12px 8px;
            font-size: 0.8rem;
          }
        }

        .payment-amount {
          margin-bottom: 16px;

          .amount-field {
            width: 100%;
          }

          .change-amount {
            margin-top: 8px;
            padding: 8px;
            background: #e8f5e8;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
            color: #4caf50;
          }
        }

        .action-buttons {
          display: flex;
          gap: 12px;

          .complete-btn {
            flex: 2;
            padding: 12px;
            font-size: 1rem;
            font-weight: 600;
          }

          .clear-btn {
            flex: 1;
            padding: 12px;
          }
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 1.1rem;
      color: #333;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .pos-container .pos-main {
    flex-direction: column;
    .cart-panel { min-width: auto; }
  }
}