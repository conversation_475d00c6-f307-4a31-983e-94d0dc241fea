@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 Terra Retail ERP - API Server
echo ========================================
echo.

echo 🔧 بدء تشغيل API Server...
echo Starting API Server...
echo.

cd src\Terra.Retail.API

echo 📦 استعادة الحزم...
echo Restoring packages...
dotnet restore

echo 🏗️ بناء المشروع...
echo Building project...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء المشروع بنجاح
    echo ✅ Project built successfully
    echo.
    echo 🌐 تشغيل API Server على: http://localhost:5000
    echo 🌐 Starting API Server on: http://localhost:5000
    echo.
    echo 📖 Swagger Documentation: http://localhost:5000
    echo.
    echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
    echo ⏹️ Press Ctrl+C to stop the server
    echo.
    
    dotnet run
) else (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Failed to build project
    echo.
    echo 🔍 تحقق من الأخطاء أعلاه
    echo 🔍 Check the errors above
    echo.
)

pause
