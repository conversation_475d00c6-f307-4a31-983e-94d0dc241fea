import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Supplier {
  id?: number;
  supplierCode?: string;
  nameAr: string;
  nameEn?: string;
  supplierTypeId?: number;
  phone1?: string;
  phone2?: string;
  email?: string;
  website?: string;
  address?: string;
  areaId?: number;
  countryId?: number;
  contactPersonName?: string;
  contactPersonPhone?: string;
  paymentTerms?: number;
  deliveryDays?: number;
  creditLimit?: number;
  openingBalance?: number;
  taxNumber?: string;
  commercialRegister?: string;
  bankName?: string;
  bankAccountNumber?: string;
  rating?: number;
  isActive?: boolean;
  createdAt?: Date;
  createdBy?: string;
  updatedAt?: Date;
  updatedBy?: string;
  isDeleted?: boolean;
}

export interface SupplierType {
  id: number;
  nameAr: string;
  nameEn?: string;
  description?: string;
  isActive: boolean;
}

export interface Area {
  id: number;
  nameAr: string;
  nameEn?: string;
  code?: string;
  isActive: boolean;
}

export interface Country {
  id: number;
  nameAr: string;
  nameEn?: string;
  code?: string;
  phoneCode?: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private apiUrl = `${environment.apiUrl}/simple`;
  private suppliersSubject = new BehaviorSubject<Supplier[]>([]);
  public suppliers$ = this.suppliersSubject.asObservable();

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    })
  };

  constructor(private http: HttpClient) {
    this.loadSuppliers();
  }

  // تحميل الموردين
  loadSuppliers(): void {
    this.getSuppliers().subscribe({
      next: (suppliers) => this.suppliersSubject.next(suppliers),
      error: (error) => console.error('خطأ في تحميل الموردين:', error)
    });
  }

  // الحصول على جميع الموردين
  getSuppliers(): Observable<Supplier[]> {
    return this.http.get<Supplier[]>(`${this.apiUrl}/suppliers`, this.httpOptions);
  }

  // الحصول على مورد بالـ ID
  getSupplier(id: number): Observable<Supplier> {
    return this.http.get<Supplier>(`${this.apiUrl}/suppliers/${id}`, this.httpOptions);
  }

  // إضافة مورد جديد
  addSupplier(supplier: Supplier): Observable<Supplier> {
    return this.http.post<Supplier>(`${this.apiUrl}/suppliers`, supplier, this.httpOptions);
  }

  // تحديث مورد
  updateSupplier(id: number, supplier: Supplier): Observable<Supplier> {
    return this.http.put<Supplier>(`${this.apiUrl}/suppliers/${id}`, supplier, this.httpOptions);
  }

  // حذف مورد
  deleteSupplier(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/suppliers/${id}`, this.httpOptions);
  }

  // الحصول على أنواع الموردين
  getSupplierTypes(): Observable<SupplierType[]> {
    return this.http.get<SupplierType[]>(`${this.apiUrl}/supplier-types`, this.httpOptions);
  }

  // الحصول على المناطق
  getAreas(): Observable<Area[]> {
    return this.http.get<Area[]>(`${this.apiUrl}/areas-db`, this.httpOptions);
  }

  // الحصول على البلدان
  getCountries(): Observable<Country[]> {
    return this.http.get<Country[]>(`${this.apiUrl}/countries`, this.httpOptions);
  }

  // الحصول على كود المورد التالي
  getNextSupplierCode(): Observable<{nextCode: string}> {
    return this.http.get<{nextCode: string}>(`${this.apiUrl}/next-supplier-code`, this.httpOptions);
  }

  // الحصول على إحصائيات الموردين
  getSupplierStats(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/supplier-stats`, this.httpOptions);
  }
}
