using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("👥 Customer Management")]
    public class CustomersController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public CustomersController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Customer>>> GetCustomers(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? customerTypeId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Area)
                    .Include(c => c.Branch)
                    .Include(c => c.Price<PERSON>ategory)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(c => c.NameAr.Contains(search) || 
                                           c.NameEn!.Contains(search) || 
                                           c.CustomerCode.Contains(search) ||
                                           c.Phone1!.Contains(search) ||
                                           c.Email!.Contains(search));
                }

                if (customerTypeId.HasValue)
                    query = query.Where(c => c.CustomerTypeId == customerTypeId);

                if (branchId.HasValue)
                    query = query.Where(c => c.BranchId == branchId);

                if (isActive.HasValue)
                    query = query.Where(c => c.IsActive == isActive);

                var totalCount = await query.CountAsync();
                var customers = await query
                    .OrderBy(c => c.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return Ok(new
                {
                    data = customers,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Customer>> GetCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Area)
                    .Include(c => c.Branch)
                    .Include(c => c.PriceCategory)
                    .Include(c => c.ChartAccount)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (customer == null)
                    return NotFound(new { message = "العميل غير موجود" });

                return Ok(customer);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Customer>> CreateCustomer(CreateCustomerRequest request)
        {
            try
            {
                // Generate customer code
                var customerCode = await GenerateCustomerCode(request.BranchId);

                var customer = new Customer
                {
                    CustomerCode = customerCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    CustomerTypeId = request.CustomerTypeId,
                    Phone1 = request.Phone1,
                    Phone2 = request.Phone2,
                    Email = request.Email,
                    Address = request.Address,
                    AreaId = request.AreaId,
                    BranchId = request.BranchId,
                    PriceCategoryId = request.PriceCategoryId,
                    DiscountPercentage = request.DiscountPercentage,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    CreditLimit = request.CreditLimit,
                    IdentityNumber = request.IdentityNumber,
                    TaxNumber = request.TaxNumber,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                // Load related data for response
                await _context.Entry(customer)
                    .Reference(c => c.CustomerType)
                    .LoadAsync();
                await _context.Entry(customer)
                    .Reference(c => c.Area)
                    .LoadAsync();
                await _context.Entry(customer)
                    .Reference(c => c.Branch)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomer(int id, UpdateCustomerRequest request)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new { message = "العميل غير موجود" });

                customer.NameAr = request.NameAr;
                customer.NameEn = request.NameEn;
                customer.CustomerTypeId = request.CustomerTypeId;
                customer.Phone1 = request.Phone1;
                customer.Phone2 = request.Phone2;
                customer.Email = request.Email;
                customer.Address = request.Address;
                customer.AreaId = request.AreaId;
                customer.PriceCategoryId = request.PriceCategoryId;
                customer.DiscountPercentage = request.DiscountPercentage;
                customer.CreditLimit = request.CreditLimit;
                customer.IdentityNumber = request.IdentityNumber;
                customer.TaxNumber = request.TaxNumber;
                customer.IsActive = request.IsActive;
                customer.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث بيانات العميل بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new { message = "العميل غير موجود" });

                // Check if customer has transactions
                var hasTransactions = await _context.Sales.AnyAsync(s => s.CustomerId == id);
                if (hasTransactions)
                {
                    return BadRequest(new { message = "لا يمكن حذف العميل لوجود معاملات مرتبطة به" });
                }

                _context.Customers.Remove(customer);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف العميل بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("types")]
        public async Task<ActionResult<IEnumerable<CustomerType>>> GetCustomerTypes()
        {
            try
            {
                var types = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.DisplayOrder)
                    .ToListAsync();

                return Ok(types);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("balance/{id}")]
        public async Task<ActionResult> GetCustomerBalance(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new { message = "العميل غير موجود" });

                // Calculate current balance from transactions
                var salesTotal = await _context.Sales
                    .Where(s => s.CustomerId == id && s.Status == 1)
                    .SumAsync(s => s.RemainingAmount);

                var receiptsTotal = await _context.Receipts
                    .Where(r => r.CustomerId == id && r.Status == 1)
                    .SumAsync(r => r.Amount);

                var currentBalance = customer.OpeningBalance + salesTotal - receiptsTotal;

                return Ok(new
                {
                    customerId = id,
                    customerName = customer.NameAr,
                    openingBalance = customer.OpeningBalance,
                    salesTotal,
                    receiptsTotal,
                    currentBalance,
                    creditLimit = customer.CreditLimit,
                    availableCredit = customer.CreditLimit - currentBalance
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateCustomerCode(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.Customer && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.Customer,
                    Prefix = "CUS",
                    CurrentValue = 1,
                    NumberLength = 6,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }
    }

    // DTOs
    public class CreateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int CustomerTypeId { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public int BranchId { get; set; }
        public int? PriceCategoryId { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CreditLimit { get; set; } = 0;
        public string? IdentityNumber { get; set; }
        public string? TaxNumber { get; set; }
    }

    public class UpdateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int CustomerTypeId { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public int? PriceCategoryId { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal CreditLimit { get; set; } = 0;
        public string? IdentityNumber { get; set; }
        public string? TaxNumber { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
