{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=TerraRetailERP;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "Jwt": {"Key": "TerraRetailERP_SecretKey_2024_VeryLongAndSecureKey_ForJWTTokenGeneration", "Issuer": "TerraRetailERP", "Audience": "TerraRetailERP_Users", "ExpiryInHours": 24}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "AppSettings": {"CompanyName": "Terra Retail ERP", "CompanyNameAr": "نظام تخطيط موارد المؤسسات التجارية", "Version": "1.0.0", "SupportEmail": "<EMAIL>", "DefaultLanguage": "ar", "DefaultCurrency": "EGP", "DefaultTimeZone": "Africa/Cairo", "MaxFileUploadSize": 10485760, "AllowedFileExtensions": ".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx", "EnableAuditTrail": true, "EnableEmailNotifications": true, "EnableSMSNotifications": false, "SessionTimeoutMinutes": 60, "PasswordMinLength": 8, "PasswordRequireUppercase": true, "PasswordRequireLowercase": true, "PasswordRequireDigit": true, "PasswordRequireSpecialChar": true, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 30}}