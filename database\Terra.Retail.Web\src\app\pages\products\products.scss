.products-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .title-section {
        h1 {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 8px 0;
          color: #1976d2;
          font-size: 2rem;
          font-weight: 600;

          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }

        p {
          margin: 0;
          color: #666;
          font-size: 1rem;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;

    .stat-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
            color: white;
          }
        }

        .stat-details {
          h3 {
            margin: 0 0 4px 0;
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
          }

          p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
          }
        }
      }

      &.total .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.active .stat-icon {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      }

      &.value .stat-icon {
        background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
      }

      &.warning .stat-icon {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      }
    }
  }

  .filters-card {
    margin-bottom: 24px;

    .filters-container {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .search-section {
        .search-field {
          width: 100%;
          max-width: 400px;
        }
      }

      .filter-section {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        align-items: center;

        mat-form-field {
          min-width: 200px;
        }

        .clear-filters-btn {
          height: 56px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h2 {
        margin: 0;
        color: #333;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }

    .table-container {
      overflow-x: auto;

      .products-table {
        width: 100%;
        background: white;

        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          border-bottom: 2px solid #e0e0e0;
        }

        td {
          border-bottom: 1px solid #e0e0e0;
        }

        .product-code {
          font-family: 'Courier New', monospace;
          background-color: #e3f2fd;
          padding: 4px 8px;
          border-radius: 4px;
          font-weight: 600;
          color: #1976d2;
        }

        .product-name {
          strong {
            display: block;
            color: #333;
            font-size: 1rem;
          }

          small {
            color: #666;
            font-size: 0.85rem;
          }
        }

        .price {
          font-weight: 600;
          color: #4CAF50;
          font-size: 1.1rem;
        }

        .stock-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-weight: 600;
          font-size: 0.9rem;

          &.good-stock {
            background-color: #e8f5e8;
            color: #2e7d32;
          }

          &.medium-stock {
            background-color: #fff3e0;
            color: #f57c00;
          }

          &.low-stock {
            background-color: #ffebee;
            color: #d32f2f;
          }

          &.out-of-stock {
            background-color: #fce4ec;
            color: #c2185b;
          }
        }

        .stock-status {
          display: block;
          margin-top: 4px;
          font-size: 0.75rem;
          color: #666;
        }

        .action-buttons {
          display: flex;
          gap: 4px;

          button {
            width: 36px;
            height: 36px;

            mat-icon {
              font-size: 1.2rem;
              width: 1.2rem;
              height: 1.2rem;
            }
          }
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        color: #ccc;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        font-size: 1.5rem;
        color: #333;
      }

      p {
        margin: 0 0 24px 0;
        font-size: 1rem;
      }
    }

    .loading-container {
      text-align: center;
      padding: 60px 20px;
      color: #666;

      .loading-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: #1976d2;
        margin-bottom: 16px;
        animation: spin 2s linear infinite;
      }

      p {
        margin: 0;
        font-size: 1.1rem;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .products-container {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .filters-card .filters-container .filter-section {
      flex-direction: column;
      align-items: stretch;

      mat-form-field {
        min-width: auto;
        width: 100%;
      }
    }
  }
}