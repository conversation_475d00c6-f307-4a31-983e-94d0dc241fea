using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("Employees")]
    public class Employee
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string EmployeeCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        [StringLength(40)]
        public string? Phone1 { get; set; }

        [StringLength(40)]
        public string? Phone2 { get; set; }

        [StringLength(200)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(1000)]
        public string? Address { get; set; }

        public DateTime? BirthDate { get; set; }

        public DateTime? HireDate { get; set; }

        public int? DepartmentId { get; set; }

        public int? PositionId { get; set; }

        public int BranchId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } = 0;

        [StringLength(50)]
        public string? BiometricId { get; set; }

        [StringLength(100)]
        public string? EmergencyContact { get; set; }

        [StringLength(40)]
        public string? EmergencyPhone { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Department? Department { get; set; }
        public virtual Position? Position { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
    }

    [Table("Departments")]
    public class Department
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public int? ManagerId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Employee? Manager { get; set; }
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    [Table("Positions")]
    public class Position
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinSalary { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxSalary { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    [Table("Shifts")]
    public class Shift
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string ShiftName { get; set; } = string.Empty;

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public int BreakDuration { get; set; } = 0; // minutes

        public int GracePeriodMinutes { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal MaxOvertimeHours { get; set; } = 0;

        public bool IsFlexible { get; set; } = false;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
    }

    [Table("EmployeeShifts")]
    public class EmployeeShift
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        public int ShiftId { get; set; }

        public DateTime EffectiveDate { get; set; } = DateTime.Now;

        public DateTime? EndDate { get; set; }

        [StringLength(20)]
        public string? DaysOfWeek { get; set; } // "1,2,3,4,5" for Mon-Fri

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }

    [Table("AttendanceRecords")]
    public class AttendanceRecord
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        public int? ShiftId { get; set; }

        public DateTime ShiftDate { get; set; }

        public DateTime PlannedCheckInTime { get; set; }

        public DateTime PlannedCheckOutTime { get; set; }

        public DateTime? ActualCheckInTime { get; set; }

        public DateTime? ActualCheckOutTime { get; set; }

        public int WorkingMinutes { get; set; } = 0;

        public int LateMinutes { get; set; } = 0;

        public int EarlyLeaveMinutes { get; set; } = 0;

        public int OvertimeMinutes { get; set; } = 0;

        [StringLength(50)]
        public string AttendanceStatus { get; set; } = "Absent"; // Present, Absent, Late, EarlyLeave

        public bool IsComplete { get; set; } = false;

        public bool IsManualEntry { get; set; } = false;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual Shift? Shift { get; set; }
    }

    [Table("LeaveTypes")]
    public class LeaveType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int MaxDaysPerYear { get; set; } = 0;

        public bool IsPaid { get; set; } = true;

        public bool RequireApproval { get; set; } = true;

        public bool RequireDocument { get; set; } = false;

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = new List<EmployeeLeave>();
    }

    [Table("EmployeeLeaves")]
    public class EmployeeLeave
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        public int LeaveTypeId { get; set; }

        [Required]
        [StringLength(40)]
        public string LeaveNumber { get; set; } = string.Empty;

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public int TotalDays { get; set; }

        [StringLength(1000)]
        public string? Reason { get; set; }

        public int Status { get; set; } = 1; // 1=Pending, 2=Approved, 3=Rejected, 4=Cancelled

        public int? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        [StringLength(1000)]
        public string? ApprovalNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
        public virtual User? ApprovedByUser { get; set; }
    }

    [Table("EmployeeLeaveBalances")]
    public class EmployeeLeaveBalance
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        public int LeaveTypeId { get; set; }

        public int Year { get; set; }

        public int EntitledDays { get; set; } = 0;

        public int UsedDays { get; set; } = 0;

        public int RemainingDays { get; set; } = 0;

        public int CarriedForwardDays { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
    }
}
