<div class="dashboard-container">
  <!-- Header -->
  <header class="dashboard-header">
    <div class="header-content">
      <div class="logo-section">
        <mat-icon class="logo-icon">store</mat-icon>
        <div class="logo-text">
          <h1>Terra Retail ERP</h1>
          <p>نظام إدارة متكامل للمتاجر</p>
        </div>
      </div>

      <div class="user-section">
        <div class="user-info">
          <div class="user-details">
            <span class="user-name">{{ currentUser?.username || 'مدير النظام' }}</span>
            <span class="user-branch">{{ currentUser?.branch?.nameAr || 'الفرع الرئيسي' }}</span>
          </div>
          <mat-icon class="user-avatar">account_circle</mat-icon>
        </div>

        <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-btn">
          <mat-icon>more_vert</mat-icon>
        </button>

        <mat-menu #userMenu="matMenu">
          <button mat-menu-item>
            <mat-icon>person</mat-icon>
            <span>الملف الشخصي</span>
          </button>
          <button mat-menu-item>
            <mat-icon>settings</mat-icon>
            <span>الإعدادات</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>تسجيل الخروج</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="dashboard-main">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h2>مرحباً بك، {{ currentUser?.username || 'مدير النظام' }}</h2>
        <p>إليك نظرة سريعة على أداء نشاطك التجاري اليوم</p>
      </div>
      <div class="welcome-actions">
        <button mat-raised-button color="primary" (click)="openPOS()">
          <mat-icon>point_of_sale</mat-icon>
          نقطة البيع
        </button>
        <button mat-stroked-button color="primary" (click)="openReports()">
          <mat-icon>assessment</mat-icon>
          التقارير
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card sales-card">
        <mat-card-content>
          <div class="stat-header">
            <mat-icon class="stat-icon">trending_up</mat-icon>
            <div class="stat-info">
              <h3>المبيعات اليوم</h3>
              <p class="stat-number">{{ stats.todaySales | currency:'EGP':'symbol':'1.0-0' }}</p>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-change positive">+12.5%</span>
            <span class="stat-period">مقارنة بالأمس</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card customers-card">
        <mat-card-content>
          <div class="stat-header">
            <mat-icon class="stat-icon">people</mat-icon>
            <div class="stat-info">
              <h3>العملاء</h3>
              <p class="stat-number">{{ stats.customers }}</p>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-change positive">+5</span>
            <span class="stat-period">عملاء جدد هذا الأسبوع</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card products-card">
        <mat-card-content>
          <div class="stat-header">
            <mat-icon class="stat-icon">inventory</mat-icon>
            <div class="stat-info">
              <h3>المنتجات</h3>
              <p class="stat-number">{{ stats.products }}</p>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-change neutral">{{ stats.lowStock }}</span>
            <span class="stat-period">منتجات قليلة المخزون</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card orders-card">
        <mat-card-content>
          <div class="stat-header">
            <mat-icon class="stat-icon">shopping_cart</mat-icon>
            <div class="stat-info">
              <h3>الطلبات اليوم</h3>
              <p class="stat-number">{{ stats.todayOrders }}</p>
            </div>
          </div>
          <div class="stat-footer">
            <span class="stat-change positive">+8.3%</span>
            <span class="stat-period">مقارنة بالأمس</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3>الإجراءات السريعة</h3>
      <div class="actions-grid">
        <mat-card class="action-card" (click)="openModule('pos')">
          <mat-card-content>
            <mat-icon class="action-icon">point_of_sale</mat-icon>
            <h4>نقطة البيع</h4>
            <p>إجراء عمليات البيع والدفع</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="openModule('customers')">
          <mat-card-content>
            <mat-icon class="action-icon">people</mat-icon>
            <h4>إدارة العملاء</h4>
            <p>إضافة وإدارة بيانات العملاء</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="openModule('products')">
          <mat-card-content>
            <mat-icon class="action-icon">inventory</mat-icon>
            <h4>إدارة المنتجات</h4>
            <p>إضافة وتعديل المنتجات</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="openModule('inventory')">
          <mat-card-content>
            <mat-icon class="action-icon">warehouse</mat-icon>
            <h4>إدارة المخزون</h4>
            <p>متابعة وإدارة المخزون</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="openModule('reports')">
          <mat-card-content>
            <mat-icon class="action-icon">assessment</mat-icon>
            <h4>التقارير</h4>
            <p>تقارير المبيعات والأرباح</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="openModule('settings')">
          <mat-card-content>
            <mat-icon class="action-icon">settings</mat-icon>
            <h4>الإعدادات</h4>
            <p>إعدادات النظام والمستخدمين</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- System Status -->
    <div class="system-status">
      <h3>حالة النظام</h3>
      <div class="status-grid">
        <div class="status-item">
          <mat-icon [class]="apiStatus === 'connected' ? 'status-online' : 'status-offline'">
            {{ apiStatus === 'connected' ? 'cloud_done' : 'cloud_off' }}
          </mat-icon>
          <div class="status-info">
            <span class="status-label">خادم API</span>
            <span class="status-value">{{ apiStatus === 'connected' ? 'متصل' : 'غير متصل' }}</span>
          </div>
        </div>

        <div class="status-item">
          <mat-icon [class]="dbStatus === 'connected' ? 'status-online' : 'status-offline'">
            {{ dbStatus === 'connected' ? 'storage' : 'error' }}
          </mat-icon>
          <div class="status-info">
            <span class="status-label">قاعدة البيانات</span>
            <span class="status-value">{{ dbStatus === 'connected' ? 'متصلة' : 'غير متصلة' }}</span>
          </div>
        </div>

        <div class="status-item">
          <mat-icon class="status-online">schedule</mat-icon>
          <div class="status-info">
            <span class="status-label">آخر تحديث</span>
            <span class="status-value">{{ lastUpdate }}</span>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="dashboard-footer">
    <p>&copy; 2024 Terra Retail ERP - جميع الحقوق محفوظة</p>
    <p>الإصدار 1.0.0 | نظام إدارة متكامل للمستقبل 🚀</p>
  </footer>
</div>
