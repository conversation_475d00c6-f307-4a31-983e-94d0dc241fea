import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { RouterModule } from '@angular/router';
import { Sidebar } from '../sidebar/sidebar';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    MatBadgeModule,
    MatSnackBarModule,
    RouterModule,
    Sidebar
  ],
  templateUrl: './main-layout.html',
  styleUrls: ['./main-layout.scss']
})
export class MainLayout implements OnInit {
  isSidebarCollapsed = false;
  currentUser: any = null;
  currentPageTitle = 'لوحة التحكم';

  private pageTitle: { [key: string]: string } = {
    '/dashboard': 'لوحة التحكم',
    '/pos': 'نقطة البيع',
    '/customers': 'إدارة العملاء',
    '/products': 'إدارة المنتجات',
    '/sales': 'المبيعات',
    '/inventory': 'إدارة المخزون',
    '/suppliers': 'الموردين',
    '/purchases': 'المشتريات',
    '/reports': 'التقارير',
    '/settings': 'الإعدادات'
  };

  constructor(
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadCurrentUser();
    this.setupRouterEvents();
  }

  loadCurrentUser() {
    if (typeof localStorage !== 'undefined') {
      const userData = localStorage.getItem('currentUser');
      if (userData) {
        this.currentUser = JSON.parse(userData);
      } else {
        this.router.navigate(['/login']);
      }
    } else {
      // Fallback for server-side rendering
      this.router.navigate(['/login']);
    }
  }

  setupRouterEvents() {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentPageTitle = this.pageTitle[event.url] || 'Terra Retail ERP';
      });
  }

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  onSidebarToggle(collapsed: boolean) {
    this.isSidebarCollapsed = collapsed;
  }

  logout() {
    localStorage.removeItem('currentUser');
    this.snackBar.open('تم تسجيل الخروج بنجاح', 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
    this.router.navigate(['/login']);
  }
}
