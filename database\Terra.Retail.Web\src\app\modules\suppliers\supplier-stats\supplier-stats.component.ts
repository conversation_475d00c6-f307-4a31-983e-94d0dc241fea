import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';

interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  inactiveSuppliers: number;
  totalBalance: number;
  positiveBalance: number;
  negativeBalance: number;
}

interface CodeInfo {
  lastSupplierNumber: number;
  nextSupplierCode: string;
  maxPossibleCodes: number;
  remainingCodes: number;
  codeFormat: string;
  canAddMore: boolean;
}

@Component({
  selector: 'app-supplier-stats',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatChipsModule
  ],
  templateUrl: './supplier-stats.component.html',
  styleUrls: ['./supplier-stats.component.scss']
})
export class SupplierStatsComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  
  // Data
  statistics: SupplierStats | null = null;
  codeInfo: CodeInfo | null = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loadSupplierStats();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load supplier statistics
   */
  private loadSupplierStats(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-stats').subscribe({
      next: (response) => {
        console.log('Stats Response:', response);
        this.statistics = response.statistics;
        this.codeInfo = response.codeInfo;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading supplier stats:', error);
        this.statistics = this.getMockStatistics();
        this.codeInfo = this.getMockCodeInfo();
        this.isLoading = false;
      }
    });
    
    this.subscriptions.push(sub);
  }

  /**
   * Navigate to suppliers list
   */
  goToSuppliers(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Navigate to add supplier
   */
  addSupplier(): void {
    this.router.navigate(['/suppliers/add']);
  }

  /**
   * Go back
   */
  goBack(): void {
    this.router.navigate(['/suppliers/management']);
  }

  /**
   * Format currency
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP'
    }).format(amount);
  }

  /**
   * Format number with commas
   */
  formatNumber(num: number): string {
    return new Intl.NumberFormat('ar-EG').format(num);
  }

  /**
   * Get balance class for styling
   */
  getBalanceClass(balance: number): string {
    if (balance > 0) return 'positive';
    if (balance < 0) return 'negative';
    return 'zero';
  }

  /**
   * Get usage percentage
   */
  getUsagePercentage(): number {
    if (!this.codeInfo) return 0;
    return (this.codeInfo.lastSupplierNumber / this.codeInfo.maxPossibleCodes) * 100;
  }

  /**
   * Get usage status
   */
  getUsageStatus(): string {
    const percentage = this.getUsagePercentage();
    if (percentage < 50) return 'low';
    if (percentage < 80) return 'medium';
    if (percentage < 95) return 'high';
    return 'critical';
  }

  /**
   * Get usage status text
   */
  getUsageStatusText(): string {
    const status = this.getUsageStatus();
    switch (status) {
      case 'low': return 'استخدام منخفض';
      case 'medium': return 'استخدام متوسط';
      case 'high': return 'استخدام عالي';
      case 'critical': return 'استخدام حرج';
      default: return 'غير محدد';
    }
  }

  /**
   * Get active percentage
   */
  getActivePercentage(): number {
    if (!this.statistics || this.statistics.totalSuppliers === 0) return 0;
    return (this.statistics.activeSuppliers / this.statistics.totalSuppliers) * 100;
  }

  /**
   * Get mock statistics
   */
  private getMockStatistics(): SupplierStats {
    return {
      totalSuppliers: 10,
      activeSuppliers: 8,
      inactiveSuppliers: 2,
      totalBalance: -50000,
      positiveBalance: 200000,
      negativeBalance: -250000
    };
  }

  /**
   * Get mock code info
   */
  private getMockCodeInfo(): CodeInfo {
    return {
      lastSupplierNumber: 10,
      nextSupplierCode: 'SUP011',
      maxPossibleCodes: 999999,
      remainingCodes: 999989,
      codeFormat: 'SUP001 إلى SUP999999',
      canAddMore: true
    };
  }
}
