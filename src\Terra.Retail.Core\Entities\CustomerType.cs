using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// أنواع العملاء
    /// </summary>
    public class CustomerType : BaseEntity
    {
        /// <summary>
        /// اسم نوع العميل بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع العميل بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// وصف نوع العميل
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// نسبة خصم افتراضية لهذا النوع
        /// </summary>
        public decimal DefaultDiscountPercentage { get; set; } = 0;

        /// <summary>
        /// الحد الائتماني الافتراضي
        /// </summary>
        public decimal DefaultCreditLimit { get; set; } = 0;

        /// <summary>
        /// هل النوع نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// لون مميز للنوع (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        // Navigation Properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }
}
