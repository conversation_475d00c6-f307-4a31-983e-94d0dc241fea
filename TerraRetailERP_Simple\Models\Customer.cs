using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("Customers")]
    public class Customer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(40)]
        public string? Phone1 { get; set; }

        [StringLength(40)]
        public string? Phone2 { get; set; }

        [StringLength(200)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(1000)]
        public string? Address { get; set; }

        public int? CustomerTypeId { get; set; }

        public int? CountryId { get; set; }

        public int? CityId { get; set; }

        public int? PriceCategoryId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;

        public int? BranchId { get; set; }

        public int? ReferralSourceId { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual CustomerType? CustomerType { get; set; }
        public virtual Country? Country { get; set; }
        public virtual City? City { get; set; }
        public virtual PriceCategory? PriceCategory { get; set; }
        public virtual Branch? Branch { get; set; }
        public virtual ReferralSource? ReferralSource { get; set; }
    }
}
