import pyodbc
import datetime

def get_connection():
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=localhost;"
        "DATABASE=TerraRetailERP;"
        "UID=sa;"
        "PWD=@a123admin4;"
        "Trusted_Connection=no;"
    )
    return pyodbc.connect(conn_str)

def setup_sub_accounts_and_transactions():
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        print("🔧 إعداد الحسابات الفرعية والمعاملات...")
        
        # 1. تحديث أكواد العملاء (1400 + رقم العميل)
        cursor.execute("""
        UPDATE coa 
        SET AccountCodeNumeric = '1400' + RIGHT('000' + CAST(c.Id AS VARCHAR), 3)
        FROM ChartOfAccounts coa
        INNER JOIN Customers c ON coa.AccountCode = 'CUS' + RIGHT('000' + CAST(c.Id AS VARCHAR), 3)
        """)
        
        customers_updated = cursor.rowcount
        print(f"✅ تم تحديث {customers_updated} حساب عميل")
        
        # 2. تحديث أكواد الموردين (2100 + رقم المورد)
        cursor.execute("""
        UPDATE coa 
        SET AccountCodeNumeric = '2100' + RIGHT('000' + CAST(s.Id AS VARCHAR), 3)
        FROM ChartOfAccounts coa
        INNER JOIN Suppliers s ON coa.AccountCode = 'SUP' + RIGHT('000' + CAST(s.Id AS VARCHAR), 3)
        WHERE s.IsDeleted = 0
        """)
        
        suppliers_updated = cursor.rowcount
        print(f"✅ تم تحديث {suppliers_updated} حساب مورد")
        
        # 3. إنشاء أرصدة افتتاحية للحسابات
        cursor.execute("""
        INSERT INTO AccountBalances (AccountId, DebitBalance, CreditBalance, NetBalance, UpdatedBy)
        SELECT 
            Id,
            0,
            0,
            0,
            'System'
        FROM ChartOfAccounts 
        WHERE Id NOT IN (SELECT AccountId FROM AccountBalances)
        """)
        
        balances_created = cursor.rowcount
        print(f"✅ تم إنشاء {balances_created} رصيد حساب")
        
        # 4. إنشاء معاملات الأرصدة الافتتاحية للعملاء
        print("💰 إنشاء معاملات الأرصدة الافتتاحية للعملاء...")
        
        cursor.execute("""
        SELECT c.Id, c.ChartAccountId, c.OpeningBalance, c.NameAr, coa.Id as MainAccountId
        FROM Customers c
        INNER JOIN ChartOfAccounts coa ON c.ChartAccountId = coa.Id
        INNER JOIN ChartOfAccounts main_coa ON main_coa.AccountCode = 'CUSTOMERS'
        WHERE c.OpeningBalance != 0
        """)
        
        customers = cursor.fetchall()
        
        for customer in customers:
            customer_id, chart_account_id, opening_balance, name_ar, main_account_id = customer
            
            # الحصول على MainAccountId للعملاء
            cursor.execute("SELECT Id FROM ChartOfAccounts WHERE AccountCode = 'CUSTOMERS'")
            main_account_result = cursor.fetchone()
            if main_account_result:
                main_account_id = main_account_result[0]
                
                # إنشاء معاملة الرصيد الافتتاحي
                cursor.execute("""
                INSERT INTO Transactions (
                    TransactionDate, VoucherId, Type, LineNumber, 
                    MainAccountId, SubAccountId, Credit, Debit, 
                    BranchId, Status, Notes, ReferenceType, 
                    CustomerId, AddUser
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, 
                datetime.date.today(),  # TransactionDate
                f'OB-CUS-{customer_id:03d}',  # VoucherId
                19,  # Type (رصيد افتتاحي)
                1,   # LineNumber
                main_account_id,  # MainAccountId
                customer_id,  # SubAccountId
                max(0, -opening_balance),  # Credit (إذا كان الرصيد سالب)
                max(0, opening_balance),   # Debit (إذا كان الرصيد موجب)
                1,   # BranchId
                'POSTED',  # Status
                f'رصيد افتتاحي للعميل: {name_ar}',  # Notes
                'OPENING_BALANCE',  # ReferenceType
                customer_id,  # CustomerId
                'System'  # AddUser
                )
        
        print(f"✅ تم إنشاء {len(customers)} معاملة رصيد افتتاحي للعملاء")
        
        # 5. إنشاء معاملات الأرصدة الافتتاحية للموردين
        print("💰 إنشاء معاملات الأرصدة الافتتاحية للموردين...")
        
        cursor.execute("""
        SELECT s.Id, s.ChartAccountId, s.OpeningBalance, s.NameAr
        FROM Suppliers s
        INNER JOIN ChartOfAccounts coa ON s.ChartAccountId = coa.Id
        WHERE s.OpeningBalance != 0 AND s.IsDeleted = 0
        """)
        
        suppliers = cursor.fetchall()
        
        for supplier in suppliers:
            supplier_id, chart_account_id, opening_balance, name_ar = supplier
            
            # الحصول على MainAccountId للموردين
            cursor.execute("SELECT Id FROM ChartOfAccounts WHERE AccountCode = 'SUPPLIERS'")
            main_account_result = cursor.fetchone()
            if main_account_result:
                main_account_id = main_account_result[0]
                
                # إنشاء معاملة الرصيد الافتتاحي
                cursor.execute("""
                INSERT INTO Transactions (
                    TransactionDate, VoucherId, Type, LineNumber, 
                    MainAccountId, SubAccountId, Credit, Debit, 
                    BranchId, Status, Notes, ReferenceType, 
                    SupplierId, AddUser
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, 
                datetime.date.today(),  # TransactionDate
                f'OB-SUP-{supplier_id:03d}',  # VoucherId
                19,  # Type (رصيد افتتاحي)
                1,   # LineNumber
                main_account_id,  # MainAccountId
                supplier_id,  # SubAccountId
                max(0, opening_balance),   # Credit (الموردين دائماً دائنين)
                max(0, -opening_balance),  # Debit (إذا كان الرصيد سالب)
                1,   # BranchId
                'POSTED',  # Status
                f'رصيد افتتاحي للمورد: {name_ar}',  # Notes
                'OPENING_BALANCE',  # ReferenceType
                supplier_id,  # SupplierId
                'System'  # AddUser
                )
        
        print(f"✅ تم إنشاء {len(suppliers)} معاملة رصيد افتتاحي للموردين")
        
        # 6. تحديث أرصدة الحسابات
        print("📊 تحديث أرصدة الحسابات...")
        
        cursor.execute("""
        UPDATE ab
        SET 
            DebitBalance = ISNULL(t.TotalDebit, 0),
            CreditBalance = ISNULL(t.TotalCredit, 0),
            NetBalance = ISNULL(t.TotalDebit, 0) - ISNULL(t.TotalCredit, 0),
            LastTransactionDate = t.LastTransactionDate,
            UpdatedAt = GETDATE(),
            UpdatedBy = 'System'
        FROM AccountBalances ab
        INNER JOIN (
            SELECT 
                MainAccountId,
                SUM(Debit) as TotalDebit,
                SUM(Credit) as TotalCredit,
                MAX(TransactionDate) as LastTransactionDate
            FROM Transactions 
            WHERE IsDeleted = 0
            GROUP BY MainAccountId
        ) t ON ab.AccountId = t.MainAccountId
        """)
        
        balances_updated = cursor.rowcount
        print(f"✅ تم تحديث {balances_updated} رصيد حساب")
        
        # 7. إضافة عداد المعاملات إذا لم يكن موجوداً
        cursor.execute("""
        IF NOT EXISTS (SELECT 1 FROM Counters WHERE CounterName = 'Transaction')
        BEGIN
            INSERT INTO Counters (CounterName, CurrentValue, Prefix) 
            VALUES ('Transaction', 1000, 'TXN')
        END
        """)
        
        print("✅ تم إعداد عداد المعاملات")
        
        conn.commit()
        print("🎉 تم إعداد النظام المحاسبي بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد النظام: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    setup_sub_accounts_and_transactions()
