USE TerraRetailERP;
GO

-- <PERSON>ذ<PERSON> المنتجات الموجودة
DELETE FROM Products;
DBCC CHECKIDENT ('Products', RESEED, 0);

-- إضافة منتجات تجريبية مصرية
INSERT INTO Products (
    NameAr, NameEn, ProductCode, CategoryId, UnitId, 
    PurchasePrice, SalePrice, MinStockLevel, MaxStockLevel,
    IsActive, CreatedAt
) VALUES
-- مواد غذائية ومشروبات
(N'أرز مصري أبيض', 'Egyptian White Rice', 'P001', 2, 2, 25.00, 30.00, 10, 100, 1, GETUTCDATE()),
(N'سكر أبيض مصري', 'Egyptian White Sugar', 'P002', 2, 2, 18.00, 22.00, 5, 50, 1, GETUTCDATE()),
(N'شاي أحمد تي', 'Ahmad Tea', 'P003', 2, 1, 45.00, 55.00, 20, 200, 1, GETUTCDATE()),
(N'زيت عباد الشمس', 'Sunflower Oil', 'P004', 2, 5, 35.00, 42.00, 10, 80, 1, GETUTCDATE()),
(N'مكرونة مصرية', 'Egyptian Pasta', 'P005', 2, 1, 8.00, 12.00, 50, 500, 1, GETUTCDATE()),

-- أجهزة كهربائية
(N'مروحة سقف مصرية', 'Egyptian Ceiling Fan', 'P006', 3, 1, 450.00, 550.00, 5, 30, 1, GETUTCDATE()),
(N'مكواة بخار', 'Steam Iron', 'P007', 3, 1, 280.00, 350.00, 10, 50, 1, GETUTCDATE()),
(N'خلاط كهربائي', 'Electric Blender', 'P008', 3, 1, 320.00, 400.00, 8, 40, 1, GETUTCDATE()),

-- ملابس وأحذية
(N'جلابية رجالي قطن', 'Men Cotton Galabeya', 'P009', 4, 1, 85.00, 120.00, 20, 100, 1, GETUTCDATE()),
(N'شبشب جلد طبيعي', 'Natural Leather Slippers', 'P010', 4, 1, 65.00, 95.00, 15, 80, 1, GETUTCDATE()),
(N'قميص قطن مصري', 'Egyptian Cotton Shirt', 'P011', 4, 1, 120.00, 180.00, 25, 150, 1, GETUTCDATE()),

-- أدوات منزلية
(N'طقم أكواب زجاج', 'Glass Cups Set', 'P012', 5, 1, 45.00, 65.00, 10, 60, 1, GETUTCDATE()),
(N'مفرش سفرة قطن', 'Cotton Table Cloth', 'P013', 5, 1, 35.00, 50.00, 15, 80, 1, GETUTCDATE()),
(N'طاسة ألومنيوم', 'Aluminum Pot', 'P014', 5, 1, 25.00, 38.00, 20, 100, 1, GETUTCDATE()),

-- منتجات عامة
(N'صابون غسيل', 'Washing Soap', 'P015', 1, 1, 12.00, 18.00, 30, 200, 1, GETUTCDATE()),
(N'منظف أرضيات', 'Floor Cleaner', 'P016', 1, 5, 22.00, 32.00, 20, 120, 1, GETUTCDATE()),
(N'مناديل ورقية', 'Tissue Paper', 'P017', 1, 1, 15.00, 22.00, 40, 300, 1, GETUTCDATE()),
(N'كيس قمامة', 'Garbage Bags', 'P018', 1, 1, 8.00, 12.00, 50, 400, 1, GETUTCDATE()),
(N'فوطة قطن مصري', 'Egyptian Cotton Towel', 'P019', 1, 1, 55.00, 80.00, 15, 100, 1, GETUTCDATE()),
(N'شمع إضاءة', 'Candles', 'P020', 1, 1, 5.00, 8.00, 100, 1000, 1, GETUTCDATE());

-- تحديث العداد
UPDATE Counters SET CurrentValue = 20 WHERE CounterName = 'PRODUCT';

SELECT N'تم إضافة المنتجات المصرية بنجاح' as Result, 
       COUNT(*) as TotalProducts,
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveProducts
FROM Products;

-- عرض المنتجات حسب الفئة
SELECT 
    c.NameAr as CategoryName,
    COUNT(p.Id) as ProductCount
FROM Categories c
LEFT JOIN Products p ON c.Id = p.CategoryId AND p.IsActive = 1
WHERE c.IsActive = 1
GROUP BY c.Id, c.NameAr
ORDER BY ProductCount DESC;
