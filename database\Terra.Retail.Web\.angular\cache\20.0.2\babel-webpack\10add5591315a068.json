{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, ElementRef, Ng<PERSON><PERSON>, ChangeDetectorRef, Renderer2, EventEmitter, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nfunction MatProgressBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = /*#__PURE__*/new InjectionToken('mat-progress-bar-location', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_BAR_LOCATION_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\nlet MatProgressBar = /*#__PURE__*/(() => {\n  class MatProgressBar {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    constructor() {\n      const defaults = inject(MAT_PROGRESS_BAR_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      if (defaults) {\n        if (defaults.color) {\n          this.color = this._defaultColor = defaults.color;\n        }\n        this.mode = defaults.mode || this.mode;\n      }\n    }\n    /** Flag that indicates whether NoopAnimations mode is set to true. */\n    _isNoopAnimation = _animationsDisabled();\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the progress bar. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-bar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      return this._color || this._defaultColor;\n    }\n    set color(value) {\n      this._color = value;\n    }\n    _color;\n    _defaultColor = 'primary';\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n      return this._value;\n    }\n    set value(v) {\n      this._value = clamp(v || 0);\n      this._changeDetectorRef.markForCheck();\n    }\n    _value = 0;\n    /** Buffer value of the progress bar. Defaults to zero. */\n    get bufferValue() {\n      return this._bufferValue || 0;\n    }\n    set bufferValue(v) {\n      this._bufferValue = clamp(v || 0);\n      this._changeDetectorRef.markForCheck();\n    }\n    _bufferValue = 0;\n    /**\n     * Event emitted when animation of the primary progress bar completes. This event will not\n     * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n     * animations (indeterminate and query).\n     */\n    animationEnd = new EventEmitter();\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    get mode() {\n      return this._mode;\n    }\n    set mode(value) {\n      // Note that we don't technically need a getter and a setter here,\n      // but we use it to match the behavior of the existing mat-progress-bar.\n      this._mode = value;\n      this._changeDetectorRef.markForCheck();\n    }\n    _mode = 'determinate';\n    ngAfterViewInit() {\n      // Run outside angular so change detection didn't get triggered on every transition end\n      // instead only on the animation that we care about (primary value bar's transitionend)\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupTransitionEnd = this._renderer.listen(this._elementRef.nativeElement, 'transitionend', this._transitionendHandler);\n      });\n    }\n    ngOnDestroy() {\n      this._cleanupTransitionEnd?.();\n    }\n    /** Gets the transform style that should be applied to the primary bar. */\n    _getPrimaryBarTransform() {\n      return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n    }\n    /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n    _getBufferBarFlexBasis() {\n      return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n    }\n    /** Returns whether the progress bar is indeterminate. */\n    _isIndeterminate() {\n      return this.mode === 'indeterminate' || this.mode === 'query';\n    }\n    /** Event handler for `transitionend` events. */\n    _transitionendHandler = event => {\n      if (this.animationEnd.observers.length === 0 || !event.target || !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n        return;\n      }\n      if (this.mode === 'determinate' || this.mode === 'buffer') {\n        this._ngZone.run(() => this.animationEnd.next({\n          value: this.value\n        }));\n      }\n    };\n    static ɵfac = function MatProgressBar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatProgressBar)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatProgressBar,\n      selectors: [[\"mat-progress-bar\"]],\n      hostAttrs: [\"role\", \"progressbar\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-bar\", \"mdc-linear-progress\"],\n      hostVars: 10,\n      hostBindings: function MatProgressBar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuenow\", ctx._isIndeterminate() ? null : ctx.value)(\"mode\", ctx.mode);\n          i0.ɵɵclassMap(\"mat-\" + ctx.color);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._isNoopAnimation)(\"mdc-linear-progress--animation-ready\", !ctx._isNoopAnimation)(\"mdc-linear-progress--indeterminate\", ctx._isIndeterminate());\n        }\n      },\n      inputs: {\n        color: \"color\",\n        value: [2, \"value\", \"value\", numberAttribute],\n        bufferValue: [2, \"bufferValue\", \"bufferValue\", numberAttribute],\n        mode: \"mode\"\n      },\n      outputs: {\n        animationEnd: \"animationEnd\"\n      },\n      exportAs: [\"matProgressBar\"],\n      decls: 7,\n      vars: 5,\n      consts: [[\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__buffer\"], [1, \"mdc-linear-progress__buffer-bar\"], [1, \"mdc-linear-progress__buffer-dots\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__primary-bar\"], [1, \"mdc-linear-progress__bar-inner\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__secondary-bar\"]],\n      template: function MatProgressBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵconditionalCreate(2, MatProgressBar_Conditional_2_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"flex-basis\", ctx._getBufferBarFlexBasis());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.mode === \"buffer\" ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"transform\", ctx._getPrimaryBarTransform());\n        }\n      },\n      styles: [\".mat-mdc-progress-bar{display:block;text-align:start}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:max(var(--mat-progress-bar-track-height, 4px),var(--mat-progress-bar-active-indicator-height, 4px))}@media(forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}[dir=rtl] .mdc-linear-progress__bar{right:0;transform-origin:center right}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid;border-color:var(--mat-progress-bar-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden;height:var(--mat-progress-bar-track-height, 4px);border-radius:var(--mat-progress-bar-track-shape, var(--mat-sys-corner-none))}.mdc-linear-progress__buffer-dots{-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");background-repeat:repeat-x;flex:auto;transform:rotate(180deg);animation:mdc-linear-progress-buffering 250ms infinite linear;background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}[dir=rtl] .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mat-progress-bar-track-height, 4px) * -2.5))}}@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(83.67142%)}100%{transform:translateX(200.611057%)}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(84.386165%)}100%{transform:translateX(160.277782%)}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(-83.67142%)}100%{transform:translateX(-200.611057%)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(-37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(-84.386165%)}100%{transform:translateX(-160.277782%)}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatProgressBar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n  return Math.max(min, Math.min(max, v));\n}\nlet MatProgressBarModule = /*#__PURE__*/(() => {\n  class MatProgressBarModule {\n    static ɵfac = function MatProgressBarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatProgressBarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatProgressBarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n  return MatProgressBarModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "DOCUMENT", "ElementRef", "NgZone", "ChangeDetectorRef", "Renderer2", "EventEmitter", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "_", "_animationsDisabled", "M", "MatCommonModule", "MatProgressBar_Conditional_2_Template", "rf", "ctx", "ɵɵelement", "MAT_PROGRESS_BAR_DEFAULT_OPTIONS", "MAT_PROGRESS_BAR_LOCATION", "providedIn", "factory", "MAT_PROGRESS_BAR_LOCATION_FACTORY", "_document", "_location", "location", "getPathname", "pathname", "search", "MatProgressBar", "_elementRef", "_ngZone", "_changeDetectorRef", "_renderer", "_cleanupTransitionEnd", "constructor", "defaults", "optional", "color", "_defaultColor", "mode", "_isNoopAnimation", "_color", "value", "_value", "v", "clamp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufferValue", "_bufferValue", "animationEnd", "_mode", "ngAfterViewInit", "runOutsideAngular", "listen", "nativeElement", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "_getPrimaryBarTransform", "_isIndeterminate", "_getBufferBarFlexBasis", "event", "observers", "length", "target", "classList", "contains", "run", "next", "ɵfac", "MatProgressBar_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatProgressBar_HostBindings", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "outputs", "exportAs", "decls", "vars", "consts", "template", "MatProgressBar_Template", "ɵɵelementStart", "ɵɵconditionalCreate", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ɵɵconditional", "styles", "encapsulation", "changeDetection", "ngDevMode", "min", "max", "Math", "MatProgressBarModule", "MatProgressBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/progress-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, ElementRef, <PERSON><PERSON><PERSON>, ChangeDetectorRef, Renderer2, EventEmitter, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = new InjectionToken('mat-progress-bar-location', { providedIn: 'root', factory: MAT_PROGRESS_BAR_LOCATION_FACTORY });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\nclass MatProgressBar {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    constructor() {\n        const defaults = inject(MAT_PROGRESS_BAR_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            this.mode = defaults.mode || this.mode;\n        }\n    }\n    /** Flag that indicates whether NoopAnimations mode is set to true. */\n    _isNoopAnimation = _animationsDisabled();\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the progress bar. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-bar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    _color;\n    _defaultColor = 'primary';\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n        return this._value;\n    }\n    set value(v) {\n        this._value = clamp(v || 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    _value = 0;\n    /** Buffer value of the progress bar. Defaults to zero. */\n    get bufferValue() {\n        return this._bufferValue || 0;\n    }\n    set bufferValue(v) {\n        this._bufferValue = clamp(v || 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    _bufferValue = 0;\n    /**\n     * Event emitted when animation of the primary progress bar completes. This event will not\n     * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n     * animations (indeterminate and query).\n     */\n    animationEnd = new EventEmitter();\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        // Note that we don't technically need a getter and a setter here,\n        // but we use it to match the behavior of the existing mat-progress-bar.\n        this._mode = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    _mode = 'determinate';\n    ngAfterViewInit() {\n        // Run outside angular so change detection didn't get triggered on every transition end\n        // instead only on the animation that we care about (primary value bar's transitionend)\n        this._ngZone.runOutsideAngular(() => {\n            this._cleanupTransitionEnd = this._renderer.listen(this._elementRef.nativeElement, 'transitionend', this._transitionendHandler);\n        });\n    }\n    ngOnDestroy() {\n        this._cleanupTransitionEnd?.();\n    }\n    /** Gets the transform style that should be applied to the primary bar. */\n    _getPrimaryBarTransform() {\n        return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n    }\n    /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n    _getBufferBarFlexBasis() {\n        return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n    }\n    /** Returns whether the progress bar is indeterminate. */\n    _isIndeterminate() {\n        return this.mode === 'indeterminate' || this.mode === 'query';\n    }\n    /** Event handler for `transitionend` events. */\n    _transitionendHandler = (event) => {\n        if (this.animationEnd.observers.length === 0 ||\n            !event.target ||\n            !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n            return;\n        }\n        if (this.mode === 'determinate' || this.mode === 'buffer') {\n            this._ngZone.run(() => this.animationEnd.next({ value: this.value }));\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatProgressBar, isStandalone: true, selector: \"mat-progress-bar\", inputs: { color: \"color\", value: [\"value\", \"value\", numberAttribute], bufferValue: [\"bufferValue\", \"bufferValue\", numberAttribute], mode: \"mode\" }, outputs: { animationEnd: \"animationEnd\" }, host: { attributes: { \"role\": \"progressbar\", \"aria-valuemin\": \"0\", \"aria-valuemax\": \"100\", \"tabindex\": \"-1\" }, properties: { \"attr.aria-valuenow\": \"_isIndeterminate() ? null : value\", \"attr.mode\": \"mode\", \"class\": \"\\\"mat-\\\" + color\", \"class._mat-animation-noopable\": \"_isNoopAnimation\", \"class.mdc-linear-progress--animation-ready\": \"!_isNoopAnimation\", \"class.mdc-linear-progress--indeterminate\": \"_isIndeterminate()\" }, classAttribute: \"mat-mdc-progress-bar mdc-linear-progress\" }, exportAs: [\"matProgressBar\"], ngImport: i0, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\\n  @if (mode === 'buffer') {\\n    <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n  }\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\".mat-mdc-progress-bar{display:block;text-align:start}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:max(var(--mat-progress-bar-track-height, 4px),var(--mat-progress-bar-active-indicator-height, 4px))}@media(forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}[dir=rtl] .mdc-linear-progress__bar{right:0;transform-origin:center right}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid;border-color:var(--mat-progress-bar-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden;height:var(--mat-progress-bar-track-height, 4px);border-radius:var(--mat-progress-bar-track-shape, var(--mat-sys-corner-none))}.mdc-linear-progress__buffer-dots{-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");background-repeat:repeat-x;flex:auto;transform:rotate(180deg);animation:mdc-linear-progress-buffering 250ms infinite linear;background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}[dir=rtl] .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mat-progress-bar-track-height, 4px) * -2.5))}}@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(83.67142%)}100%{transform:translateX(200.611057%)}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(84.386165%)}100%{transform:translateX(160.277782%)}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(-83.67142%)}100%{transform:translateX(-200.611057%)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(-37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(-84.386165%)}100%{transform:translateX(-160.277782%)}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-bar', exportAs: 'matProgressBar', host: {\n                        'role': 'progressbar',\n                        'aria-valuemin': '0',\n                        'aria-valuemax': '100',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n                        '[attr.mode]': 'mode',\n                        'class': 'mat-mdc-progress-bar mdc-linear-progress',\n                        '[class]': '\"mat-\" + color',\n                        '[class._mat-animation-noopable]': '_isNoopAnimation',\n                        '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n                        '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\\n  @if (mode === 'buffer') {\\n    <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n  }\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\".mat-mdc-progress-bar{display:block;text-align:start}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:max(var(--mat-progress-bar-track-height, 4px),var(--mat-progress-bar-active-indicator-height, 4px))}@media(forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}[dir=rtl] .mdc-linear-progress__bar{right:0;transform-origin:center right}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid;border-color:var(--mat-progress-bar-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden;height:var(--mat-progress-bar-track-height, 4px);border-radius:var(--mat-progress-bar-track-shape, var(--mat-sys-corner-none))}.mdc-linear-progress__buffer-dots{-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");background-repeat:repeat-x;flex:auto;transform:rotate(180deg);animation:mdc-linear-progress-buffering 250ms infinite linear;background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}[dir=rtl] .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mat-progress-bar-track-height, 4px) * -2.5))}}@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(83.67142%)}100%{transform:translateX(200.611057%)}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(84.386165%)}100%{transform:translateX(160.277782%)}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(-83.67142%)}100%{transform:translateX(-200.611057%)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(-37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(-84.386165%)}100%{transform:translateX(-160.277782%)}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], value: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], bufferValue: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], animationEnd: [{\n                type: Output\n            }], mode: [{\n                type: Input\n            }] } });\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n    return Math.max(min, Math.min(max, v));\n}\n\nclass MatProgressBarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBarModule, imports: [MatProgressBar], exports: [MatProgressBar, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBarModule, imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatProgressBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatProgressBar],\n                    exports: [MatProgressBar, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjO,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAmI6FrB,EAAE,CAAAuB,SAAA,YACozC,CAAC;EAAA;AAAA;AAnIp5C,MAAMC,gCAAgC,gBAAG,IAAIvB,cAAc,CAAC,kCAAkC,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA,MAAMwB,yBAAyB,gBAAG,IAAIxB,cAAc,CAAC,2BAA2B,EAAE;EAAEyB,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEC;AAAkC,CAAC,CAAC;AACrJ;AACA;AACA;AACA;AACA;AACA,SAASA,iCAAiCA,CAAA,EAAG;EACzC,MAAMC,SAAS,GAAG3B,MAAM,CAACC,QAAQ,CAAC;EAClC,MAAM2B,SAAS,GAAGD,SAAS,GAAGA,SAAS,CAACE,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAEA,CAAA,KAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AAAC,IACKC,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBC,WAAW,GAAGlC,MAAM,CAACE,UAAU,CAAC;IAChCiC,OAAO,GAAGnC,MAAM,CAACG,MAAM,CAAC;IACxBiC,kBAAkB,GAAGpC,MAAM,CAACI,iBAAiB,CAAC;IAC9CiC,SAAS,GAAGrC,MAAM,CAACK,SAAS,CAAC;IAC7BiC,qBAAqB;IACrBC,WAAWA,CAAA,EAAG;MACV,MAAMC,QAAQ,GAAGxC,MAAM,CAACsB,gCAAgC,EAAE;QACtDmB,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAID,QAAQ,EAAE;QACV,IAAIA,QAAQ,CAACE,KAAK,EAAE;UAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,aAAa,GAAGH,QAAQ,CAACE,KAAK;QACpD;QACA,IAAI,CAACE,IAAI,GAAGJ,QAAQ,CAACI,IAAI,IAAI,IAAI,CAACA,IAAI;MAC1C;IACJ;IACA;IACAC,gBAAgB,GAAG9B,mBAAmB,CAAC,CAAC;IACxC;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI2B,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACI,MAAM,IAAI,IAAI,CAACH,aAAa;IAC5C;IACA,IAAID,KAAKA,CAACK,KAAK,EAAE;MACb,IAAI,CAACD,MAAM,GAAGC,KAAK;IACvB;IACAD,MAAM;IACNH,aAAa,GAAG,SAAS;IACzB;IACA,IAAII,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM;IACtB;IACA,IAAID,KAAKA,CAACE,CAAC,EAAE;MACT,IAAI,CAACD,MAAM,GAAGE,KAAK,CAACD,CAAC,IAAI,CAAC,CAAC;MAC3B,IAAI,CAACb,kBAAkB,CAACe,YAAY,CAAC,CAAC;IAC1C;IACAH,MAAM,GAAG,CAAC;IACV;IACA,IAAII,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,YAAY,IAAI,CAAC;IACjC;IACA,IAAID,WAAWA,CAACH,CAAC,EAAE;MACf,IAAI,CAACI,YAAY,GAAGH,KAAK,CAACD,CAAC,IAAI,CAAC,CAAC;MACjC,IAAI,CAACb,kBAAkB,CAACe,YAAY,CAAC,CAAC;IAC1C;IACAE,YAAY,GAAG,CAAC;IAChB;AACJ;AACA;AACA;AACA;IACIC,YAAY,GAAG,IAAIhD,YAAY,CAAC,CAAC;IACjC;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIsC,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACW,KAAK;IACrB;IACA,IAAIX,IAAIA,CAACG,KAAK,EAAE;MACZ;MACA;MACA,IAAI,CAACQ,KAAK,GAAGR,KAAK;MAClB,IAAI,CAACX,kBAAkB,CAACe,YAAY,CAAC,CAAC;IAC1C;IACAI,KAAK,GAAG,aAAa;IACrBC,eAAeA,CAAA,EAAG;MACd;MACA;MACA,IAAI,CAACrB,OAAO,CAACsB,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACnB,qBAAqB,GAAG,IAAI,CAACD,SAAS,CAACqB,MAAM,CAAC,IAAI,CAACxB,WAAW,CAACyB,aAAa,EAAE,eAAe,EAAE,IAAI,CAACC,qBAAqB,CAAC;MACnI,CAAC,CAAC;IACN;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACvB,qBAAqB,GAAG,CAAC;IAClC;IACA;IACAwB,uBAAuBA,CAAA,EAAG;MACtB,OAAO,UAAU,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAChB,KAAK,GAAG,GAAG,GAAG;IACtE;IACA;IACAiB,sBAAsBA,CAAA,EAAG;MACrB,OAAO,GAAG,IAAI,CAACpB,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACQ,WAAW,GAAG,GAAG,GAAG;IAChE;IACA;IACAW,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAACnB,IAAI,KAAK,eAAe,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO;IACjE;IACA;IACAgB,qBAAqB,GAAIK,KAAK,IAAK;MAC/B,IAAI,IAAI,CAACX,YAAY,CAACY,SAAS,CAACC,MAAM,KAAK,CAAC,IACxC,CAACF,KAAK,CAACG,MAAM,IACb,CAACH,KAAK,CAACG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,kCAAkC,CAAC,EAAE;QACtE;MACJ;MACA,IAAI,IAAI,CAAC1B,IAAI,KAAK,aAAa,IAAI,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;QACvD,IAAI,CAACT,OAAO,CAACoC,GAAG,CAAC,MAAM,IAAI,CAACjB,YAAY,CAACkB,IAAI,CAAC;UAAEzB,KAAK,EAAE,IAAI,CAACA;QAAM,CAAC,CAAC,CAAC;MACzE;IACJ,CAAC;IACD,OAAO0B,IAAI,YAAAC,uBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwF1C,cAAc;IAAA;IACjH,OAAO2C,IAAI,kBAD8E9E,EAAE,CAAA+E,iBAAA;MAAAC,IAAA,EACJ7C,cAAc;MAAA8C,SAAA;MAAAC,SAAA,WAAiR,aAAa,mBAAmB,GAAG,mBAAmB,KAAK,cAAc,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAAhE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD1WrB,EAAE,CAAAsF,WAAA,kBACJhE,GAAA,CAAA2C,gBAAA,CAAiB,CAAC,GAAG,IAAI,GAAA3C,GAAA,CAAA2B,KAAA,UAAA3B,GAAA,CAAAwB,IAAA;UADvB9C,EAAE,CAAAuF,UAAA,CACJ,MAAM,GAAAjE,GAAA,CAAAsB,KAAO,CAAC;UADZ5C,EAAE,CAAAwF,WAAA,4BAAAlE,GAAA,CAAAyB,gBACS,CAAC,0CAAAzB,GAAA,CAAAyB,gBAAD,CAAC,uCAAdzB,GAAA,CAAA2C,gBAAA,CAAiB,CAAJ,CAAC;QAAA;MAAA;MAAAwB,MAAA;QAAA7C,KAAA;QAAAK,KAAA,wBAAwGxC,eAAe;QAAA6C,WAAA,oCAA+C7C,eAAe;QAAAqC,IAAA;MAAA;MAAA4C,OAAA;QAAAlC,YAAA;MAAA;MAAAmC,QAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAA3E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADjMrB,EAAE,CAAAiG,cAAA,YACwgC,CAAC;UAD3gCjG,EAAE,CAAAuB,SAAA,YAC2nC,CAAC;UAD9nCvB,EAAE,CAAAkG,mBAAA,IAAA9E,qCAAA,gBACwvC,CAAC;UAD3vCpB,EAAE,CAAAmG,YAAA,CACi0C,CAAC;UADp0CnG,EAAE,CAAAiG,cAAA,YAC09C,CAAC;UAD79CjG,EAAE,CAAAuB,SAAA,aACohD,CAAC;UADvhDvB,EAAE,CAAAmG,YAAA,CAC4hD,CAAC;UAD/hDnG,EAAE,CAAAiG,cAAA,YAC8nD,CAAC;UADjoDjG,EAAE,CAAAuB,SAAA,aACwrD,CAAC;UAD3rDvB,EAAE,CAAAmG,YAAA,CACgsD,CAAC;QAAA;QAAA,IAAA9E,EAAA;UADnsDrB,EAAE,CAAAoG,SAAA,CAConC,CAAC;UADvnCpG,EAAE,CAAAqG,WAAA,eAAA/E,GAAA,CAAA4C,sBAAA,EAConC,CAAC;UADvnClE,EAAE,CAAAoG,SAAA,CACyzC,CAAC;UAD5zCpG,EAAE,CAAAsG,aAAA,CAAAhF,GAAA,CAAAwB,IAAA,sBACyzC,CAAC;UAD5zC9C,EAAE,CAAAoG,SAAA,CACy9C,CAAC;UAD59CpG,EAAE,CAAAqG,WAAA,cAAA/E,GAAA,CAAA0C,uBAAA,EACy9C,CAAC;QAAA;MAAA;MAAAuC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACzjD;EAAC,OA/GKtE,cAAc;AAAA;AAgHpB;EAAA,QAAAuE,SAAA,oBAAAA,SAAA;AAAA;AA8BA;AACA,SAAStD,KAAKA,CAACD,CAAC,EAAEwD,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,GAAG,EAAE;EAClC,OAAOC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEzD,CAAC,CAAC,CAAC;AAC1C;AAAC,IAEK2D,oBAAoB;EAA1B,MAAMA,oBAAoB,CAAC;IACvB,OAAOnC,IAAI,YAAAoC,6BAAAlC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiC,oBAAoB;IAAA;IACvH,OAAOE,IAAI,kBAxC8EhH,EAAE,CAAAiH,gBAAA;MAAAjC,IAAA,EAwCS8B;IAAoB;IACxH,OAAOI,IAAI,kBAzC8ElH,EAAE,CAAAmH,gBAAA;MAAAC,OAAA,GAyCyCjG,eAAe;IAAA;EACvJ;EAAC,OAJK2F,oBAAoB;AAAA;AAK1B;EAAA,QAAAJ,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAASlF,gCAAgC,EAAEC,yBAAyB,EAAEG,iCAAiC,EAAEO,cAAc,EAAE2E,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}