import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

// Interfaces
interface Customer {
  id: number;
  code: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  customerTypeId: number;
  customerTypeName: string;
  governorateId: number;
  governorateName: string;
  balance: number;
  isActive: boolean;
  createdAt: Date;
}

interface CustomerType {
  id: number;
  name: string;
}

interface Governorate {
  id: number;
  name: string;
}

interface Statistics {
  totalCustomers: number;
  activeCustomers: number;
  newCustomersThisMonth: number;
  vipCustomers: number;
}

@Component({
  selector: 'app-customers-new',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  templateUrl: './customers-new.component.html',
  styleUrls: ['./customers-new.component.scss']
})
export class CustomersNewComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = true;
  
  // Data
  customers: Customer[] = [];
  filteredCustomers: Customer[] = [];
  paginatedCustomers: Customer[] = [];
  customerTypes: CustomerType[] = [];
  governorates: Governorate[] = [];
  
  // Statistics
  statistics: Statistics = {
    totalCustomers: 0,
    activeCustomers: 0,
    newCustomersThisMonth: 0,
    vipCustomers: 0
  };

  // Filters
  searchTerm = '';
  selectedCustomerType = '';
  selectedGovernorate = '';
  selectedStatus = '';

  // Table Configuration
  displayedColumns: string[] = ['code', 'name', 'contact', 'address', 'balance', 'status', 'actions'];
  pageSize = 25;
  currentPage = 0;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load initial data
   */
  private loadInitialData(): void {
    this.isLoading = true;
    
    // Load all data in parallel
    Promise.all([
      this.loadCustomers(),
      this.loadCustomerTypes(),
      this.loadGovernorates()
    ]).then(() => {
      this.calculateStatistics();
      this.applyFilters();
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }

  /**
   * Load customers from API
   */
  private loadCustomers(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/customers').subscribe({
        next: (response) => {
          this.customers = response.customers || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading customers:', error);
          // Use mock data if API fails
          this.customers = this.getMockCustomers();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load customer types from API
   */
  private loadCustomerTypes(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/customer-types').subscribe({
        next: (response) => {
          this.customerTypes = response.customerTypes || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading customer types:', error);
          // Use mock data if API fails
          this.customerTypes = this.getMockCustomerTypes();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load governorates from API
   */
  private loadGovernorates(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/governorates').subscribe({
        next: (response) => {
          this.governorates = response.governorates || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading governorates:', error);
          // Use mock data if API fails
          this.governorates = this.getMockGovernorates();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Calculate statistics
   */
  private calculateStatistics(): void {
    this.statistics.totalCustomers = this.customers.length;
    this.statistics.activeCustomers = this.customers.filter(c => c.isActive).length;
    
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    this.statistics.newCustomersThisMonth = this.customers.filter(c => {
      const createdDate = new Date(c.createdAt);
      return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
    }).length;
    
    // VIP customers (customers with balance > 10000)
    this.statistics.vipCustomers = this.customers.filter(c => c.balance > 10000).length;
  }

  /**
   * Apply filters
   */
  applyFilters(): void {
    let filtered = [...this.customers];

    // Search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(customer => 
        customer.name.toLowerCase().includes(term) ||
        customer.code.toLowerCase().includes(term) ||
        (customer.phone && customer.phone.includes(term)) ||
        (customer.email && customer.email.toLowerCase().includes(term))
      );
    }

    // Customer type filter
    if (this.selectedCustomerType) {
      filtered = filtered.filter(customer => 
        customer.customerTypeId.toString() === this.selectedCustomerType
      );
    }

    // Governorate filter
    if (this.selectedGovernorate) {
      filtered = filtered.filter(customer => 
        customer.governorateId.toString() === this.selectedGovernorate
      );
    }

    // Status filter
    if (this.selectedStatus) {
      const isActive = this.selectedStatus === 'active';
      filtered = filtered.filter(customer => customer.isActive === isActive);
    }

    this.filteredCustomers = filtered;
    this.currentPage = 0;
    this.updatePaginatedData();
  }

  /**
   * Update paginated data
   */
  private updatePaginatedData(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);
  }

  /**
   * Handle search
   */
  onSearch(): void {
    this.applyFilters();
  }

  /**
   * Handle filter change
   */
  onFilterChange(): void {
    this.applyFilters();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.searchTerm = '';
    this.selectedCustomerType = '';
    this.selectedGovernorate = '';
    this.selectedStatus = '';
    this.applyFilters();
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedData();
  }

  /**
   * Handle page size change
   */
  onPageSizeChange(): void {
    this.currentPage = 0;
    this.updatePaginatedData();
  }

  /**
   * Get balance class for styling
   */
  getBalanceClass(balance: number): string {
    if (balance > 0) return 'positive';
    if (balance < 0) return 'negative';
    return 'zero';
  }

  /**
   * Open add customer dialog
   */
  openAddCustomer(): void {
    console.log('Open add customer dialog');
    // Implement add customer functionality
  }

  /**
   * View customer details
   */
  viewCustomer(customer: Customer): void {
    console.log('View customer:', customer);
    // Implement view customer functionality
  }

  /**
   * Edit customer
   */
  editCustomer(customer: Customer): void {
    console.log('Edit customer:', customer);
    // Implement edit customer functionality
  }

  /**
   * Delete customer
   */
  deleteCustomer(customer: Customer): void {
    console.log('Delete customer:', customer);
    // Implement delete customer functionality
  }

  /**
   * Export customers
   */
  exportCustomers(): void {
    console.log('Export customers');
    // Implement export functionality
  }

  /**
   * Get mock customers data
   */
  private getMockCustomers(): Customer[] {
    return [
      {
        id: 1,
        code: 'C001',
        name: 'أحمد محمد علي',
        phone: '01234567890',
        email: '<EMAIL>',
        address: 'شارع النيل، المعادي',
        customerTypeId: 1,
        customerTypeName: 'عميل عادي',
        governorateId: 1,
        governorateName: 'القاهرة',
        balance: 5000,
        isActive: true,
        createdAt: new Date('2024-01-15')
      },
      {
        id: 2,
        code: 'C002',
        name: 'فاطمة أحمد حسن',
        phone: '01098765432',
        email: '<EMAIL>',
        address: 'شارع الهرم، الجيزة',
        customerTypeId: 2,
        customerTypeName: 'عميل VIP',
        governorateId: 2,
        governorateName: 'الجيزة',
        balance: 15000,
        isActive: true,
        createdAt: new Date('2024-02-10')
      }
      // Add more mock data as needed
    ];
  }

  /**
   * Get mock customer types
   */
  private getMockCustomerTypes(): CustomerType[] {
    return [
      { id: 1, name: 'عميل عادي' },
      { id: 2, name: 'عميل VIP' },
      { id: 3, name: 'عميل جملة' },
      { id: 4, name: 'عميل تجزئة' }
    ];
  }

  /**
   * Get mock governorates
   */
  private getMockGovernorates(): Governorate[] {
    return [
      { id: 1, name: 'القاهرة' },
      { id: 2, name: 'الجيزة' },
      { id: 3, name: 'الإسكندرية' },
      { id: 4, name: 'الشرقية' },
      { id: 5, name: 'البحيرة' }
    ];
  }
}
