using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("🛍️ Purchase Management")]
    public class PurchasesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public PurchasesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult> GetPurchases(
            [FromQuery] string? search = null,
            [FromQuery] int? supplierId = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Purchases
                    .Include(p => p.Supplier)
                    .Include(p => p.Branch)
                    .Include(p => p.User)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(pi => pi.Product)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.InvoiceNumber.Contains(search) || 
                                           p.SupplierInvoiceNumber!.Contains(search) ||
                                           (p.Supplier != null && p.Supplier.NameAr.Contains(search)));
                }

                if (supplierId.HasValue)
                    query = query.Where(p => p.SupplierId == supplierId);

                if (status.HasValue)
                    query = query.Where(p => p.Status == status);

                if (fromDate.HasValue)
                    query = query.Where(p => p.InvoiceDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(p => p.InvoiceDate <= toDate);

                var purchases = await query
                    .OrderByDescending(p => p.InvoiceDate)
                    .Select(p => new
                    {
                        p.Id,
                        p.InvoiceNumber,
                        p.SupplierInvoiceNumber,
                        p.InvoiceDate,
                        p.SupplierId,
                        SupplierName = p.Supplier != null ? p.Supplier.NameAr : "غير محدد",
                        BranchName = p.Branch.NameAr,
                        p.Status,
                        StatusName = p.Status == 1 ? "نشط" : "ملغي",
                        p.PurchaseType,
                        PurchaseTypeName = p.PurchaseType == 1 ? "نقدي" : "آجل",
                        p.SubTotal,
                        p.DiscountAmount,
                        p.TaxAmount,
                        p.TotalAmount,
                        p.PaidAmount,
                        p.RemainingAmount,
                        ItemsCount = p.PurchaseItems.Count,
                        p.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المشتريات بنجاح",
                    data = purchases,
                    count = purchases.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult> GetPurchase(int id)
        {
            try
            {
                var purchase = await _context.Purchases
                    .Include(p => p.Supplier)
                    .Include(p => p.Branch)
                    .Include(p => p.User)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(pi => pi.Product)
                            .ThenInclude(pr => pr.Unit)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (purchase == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "فاتورة المشتريات غير موجودة" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات الفاتورة بنجاح",
                    data = purchase
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult> CreatePurchase(CreatePurchaseRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate invoice number
                var lastPurchase = await _context.Purchases
                    .OrderByDescending(p => p.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastPurchase?.Id ?? 0) + 1;
                var invoiceNumber = $"PUR{nextId:D8}";

                // Calculate totals
                var subTotal = request.Items.Sum(i => i.Quantity * i.UnitCost);
                var discountAmount = subTotal * (request.DiscountPercentage / 100);
                var taxableAmount = subTotal - discountAmount;
                var taxAmount = taxableAmount * (request.TaxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount;

                var purchase = new Purchase
                {
                    InvoiceNumber = invoiceNumber,
                    SupplierInvoiceNumber = request.SupplierInvoiceNumber,
                    SupplierId = request.SupplierId,
                    BranchId = request.BranchId,
                    UserId = request.UserId,
                    InvoiceDate = request.InvoiceDate ?? DateTime.Now,
                    Status = 1, // Active
                    PurchaseType = request.PurchaseType,
                    SubTotal = subTotal,
                    DiscountPercentage = request.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TaxPercentage = request.TaxPercentage,
                    TaxAmount = taxAmount,
                    TotalAmount = totalAmount,
                    PaidAmount = request.PaidAmount,
                    RemainingAmount = totalAmount - request.PaidAmount,
                    Notes = request.Notes,
                    CreatedAt = DateTime.Now
                };

                _context.Purchases.Add(purchase);
                await _context.SaveChangesAsync();

                // Add purchase items and update stock
                int lineNumber = 1;
                foreach (var item in request.Items)
                {
                    var lineTotal = item.Quantity * item.UnitCost;
                    var lineDiscountAmount = lineTotal * (item.DiscountPercentage / 100);
                    var netLineTotal = lineTotal - lineDiscountAmount;
                    var lineTaxAmount = netLineTotal * (item.TaxPercentage / 100);
                    var finalTotal = netLineTotal + lineTaxAmount;

                    var purchaseItem = new PurchaseItem
                    {
                        PurchaseId = purchase.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        Quantity = item.Quantity,
                        UnitCost = item.UnitCost,
                        DiscountPercentage = item.DiscountPercentage,
                        DiscountAmount = lineDiscountAmount,
                        LineTotal = lineTotal,
                        TaxPercentage = item.TaxPercentage,
                        TaxAmount = lineTaxAmount,
                        FinalTotal = finalTotal,
                        ItemNotes = item.ItemNotes,
                        CreatedAt = DateTime.Now
                    };

                    _context.PurchaseItems.Add(purchaseItem);

                    // Update product stock
                    await UpdateProductStock(item.ProductId, request.BranchId, item.Quantity, 
                        item.UnitCost, "Purchase", invoiceNumber, request.UserId);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetPurchase), new { id = purchase.Id }, new
                {
                    success = true,
                    message = "تم إنشاء فاتورة المشتريات بنجاح",
                    data = purchase
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}/cancel")]
        public async Task<IActionResult> CancelPurchase(int id, [FromBody] CancelPurchaseRequest request)
        {
            try
            {
                var purchase = await _context.Purchases.FindAsync(id);
                if (purchase == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "فاتورة المشتريات غير موجودة" 
                    });

                if (purchase.Status == 2)
                    return BadRequest(new 
                    { 
                        success = false,
                        message = "الفاتورة ملغية مسبقاً" 
                    });

                purchase.Status = 2; // Cancelled
                purchase.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم إلغاء الفاتورة بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        private async Task UpdateProductStock(int productId, int branchId, decimal quantity, decimal unitCost, 
            string reason, string reference, int userId)
        {
            var stock = await _context.ProductStocks
                .FirstOrDefaultAsync(ps => ps.ProductId == productId && ps.BranchId == branchId);

            if (stock == null)
            {
                stock = new ProductStock
                {
                    ProductId = productId,
                    BranchId = branchId,
                    AvailableQuantity = 0,
                    AverageCostPrice = unitCost,
                    CreatedAt = DateTime.Now
                };
                _context.ProductStocks.Add(stock);
            }

            var balanceBefore = stock.AvailableQuantity;
            stock.AvailableQuantity += quantity;
            stock.LastMovementDate = DateTime.Now;
            stock.UpdatedAt = DateTime.Now;

            // Calculate new average cost
            var totalValue = (stock.AvailableQuantity - quantity) * stock.AverageCostPrice + quantity * unitCost;
            stock.AverageCostPrice = stock.AvailableQuantity > 0 ? totalValue / stock.AvailableQuantity : unitCost;
            stock.LastCostPrice = unitCost;
            stock.TotalInQuantity += quantity;
            stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;

            // Create stock movement record
            var movement = new StockMovement
            {
                ProductId = productId,
                BranchId = branchId,
                MovementNumber = reference,
                MovementType = 1, // In
                MovementReason = reason,
                Quantity = quantity,
                UnitCost = unitCost,
                TotalCost = quantity * unitCost,
                BalanceBefore = balanceBefore,
                BalanceAfter = stock.AvailableQuantity,
                MovementDate = DateTime.Now,
                SourceType = "Purchase",
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            _context.StockMovements.Add(movement);
        }
    }

    // DTOs
    public class CreatePurchaseRequest
    {
        public string? SupplierInvoiceNumber { get; set; }
        public int SupplierId { get; set; }
        public int BranchId { get; set; } = 1;
        public int UserId { get; set; } = 1;
        public DateTime? InvoiceDate { get; set; }
        public int PurchaseType { get; set; } = 1; // 1=Cash, 2=Credit
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public string? Notes { get; set; }
        public List<CreatePurchaseItemRequest> Items { get; set; } = new();
    }

    public class CreatePurchaseItemRequest
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public string? ItemNotes { get; set; }
    }

    public class CancelPurchaseRequest
    {
        public string Reason { get; set; } = string.Empty;
    }
}
