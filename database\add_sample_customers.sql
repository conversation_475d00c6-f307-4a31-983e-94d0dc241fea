-- إضافة عملاء تجريبيين مصريين
-- Add Sample Egyptian Customers

USE TerraRetailERP;
GO

PRINT N'إضافة عملاء تجريبيين مصريين...';

-- حذف العملاء الموجودين إن وجدوا
DELETE FROM Customers;

-- إعادة تعيين العداد
DBCC CHECKIDENT ('Customers', RESEED, 0);

-- إضافة عملاء تجريبيين مصريين
INSERT INTO Customers (
    NameAr, CustomerCode, Phone1, Email, Address,
    CustomerTypeId, AreaId, BranchId, PriceCategoryId,
    IsActive, CreatedAt
) VALUES
-- عملاء القاهرة (Area: 37, Branch: 1)
(N'أحمد محمد علي', 'C001', '01012345678', '<EMAIL>', N'شارع التحرير، وسط البلد، القاهرة', 1, 37, 1, 1, 1, GETUTCDATE()),
(N'فاطمة حسن إبراهيم', 'C002', '01123456789', '<EMAIL>', N'مدينة نصر، القاهرة', 2, 37, 1, 2, 1, GETUTCDATE()),
(N'محمود عبدالله أحمد', 'C003', '01234567890', '<EMAIL>', N'مصر الجديدة، القاهرة', 3, 37, 1, 3, 1, GETUTCDATE()),
(N'نورا سامي محمد', 'C004', '01098765432', '<EMAIL>', N'المعادي، القاهرة', 1, 37, 1, 1, 1, GETUTCDATE()),
(N'عمر خالد حسن', 'C005', '01187654321', '<EMAIL>', N'الزمالك، القاهرة', 4, 37, 1, 4, 1, GETUTCDATE()),

-- عملاء الجيزة (Area: 38, Branch: 14)
(N'سارة أحمد محمود', 'C006', '01276543210', '<EMAIL>', N'الدقي، الجيزة', 2, 38, 14, 2, 1, GETUTCDATE()),
(N'يوسف إبراهيم علي', 'C007', '01065432109', '<EMAIL>', N'المهندسين، الجيزة', 1, 38, 14, 1, 1, GETUTCDATE()),
(N'مريم عبدالرحمن', 'C008', '01154321098', '<EMAIL>', N'فيصل، الجيزة', 3, 38, 14, 3, 1, GETUTCDATE()),

-- عملاء الإسكندرية (Area: 39, Branch: 13)
(N'حسام الدين محمد', 'C009', '01043210987', '<EMAIL>', N'سيدي جابر، الإسكندرية', 1, 39, 13, 1, 1, GETUTCDATE()),
(N'دينا أحمد فتحي', 'C010', '01132109876', '<EMAIL>', N'الكورنيش، الإسكندرية', 2, 39, 13, 2, 1, GETUTCDATE()),
(N'كريم محمود سعد', 'C011', '01221098765', '<EMAIL>', N'محطة الرمل، الإسكندرية', 1, 39, 13, 1, 1, GETUTCDATE()),

-- عملاء الدقهلية (Area: 40, Branch: 15)
(N'إيمان حسن محمد', 'C012', '01010987654', '<EMAIL>', N'شارع الجمهورية، المنصورة', 1, 40, 15, 1, 1, GETUTCDATE()),
(N'طارق عبدالله', 'C013', '01119876543', '<EMAIL>', N'ميت غمر، الدقهلية', 2, 40, 15, 2, 1, GETUTCDATE()),

-- عملاء الشرقية (Area: 41, Branch: 18)
(N'ريهام محمد أحمد', 'C014', '01208765432', '<EMAIL>', N'شارع الجامعة، الزقازيق', 1, 41, 18, 1, 1, GETUTCDATE()),
(N'أمير حسام الدين', 'C015', '01097654321', '<EMAIL>', N'بلبيس، الشرقية', 3, 41, 18, 3, 1, GETUTCDATE()),

-- عملاء القليوبية (Area: 42, Branch: 1)
(N'هبة الله سامي', 'C016', '01186543210', '<EMAIL>', N'بنها، القليوبية', 1, 42, 1, 1, 1, GETUTCDATE()),
(N'محمد عادل فتحي', 'C017', '01275432109', '<EMAIL>', N'شبرا الخيمة، القليوبية', 2, 42, 1, 2, 1, GETUTCDATE()),

-- عملاء كفر الشيخ (Area: 43, Branch: 1)
(N'نادية محمود علي', 'C018', '01064321098', '<EMAIL>', N'كفر الشيخ', 1, 43, 1, 1, 1, GETUTCDATE()),

-- عملاء الغربية (Area: 44, Branch: 17)
(N'وليد أحمد حسن', 'C019', '01153210987', '<EMAIL>', N'شارع الجلاء، طنطا', 1, 44, 17, 1, 1, GETUTCDATE()),
(N'شيماء عبدالرحمن', 'C020', '01242109876', '<EMAIL>', N'المحلة الكبرى، الغربية', 2, 44, 17, 2, 1, GETUTCDATE()),

-- عملاء أسيوط (Area: 55, Branch: 16)
(N'عبدالرحمن محمد', 'C021', '01031098765', '<EMAIL>', N'شارع الثورة، أسيوط', 1, 55, 16, 1, 1, GETUTCDATE()),
(N'آية سامي أحمد', 'C022', '01120987654', '<EMAIL>', N'أسيوط الجديدة', 3, 55, 16, 3, 1, GETUTCDATE()),

-- عملاء أسوان (Area: 59, Branch: 19)
(N'مصطفى علي حسن', 'C023', '01209876543', '<EMAIL>', N'شارع النيل، أسوان', 1, 59, 19, 1, 1, GETUTCDATE()),
(N'رانيا محمود', 'C024', '01098765432', '<EMAIL>', N'كوم أمبو، أسوان', 2, 59, 19, 2, 1, GETUTCDATE()),

-- عميل غير نشط للاختبار
(N'عميل غير نشط', 'C025', '01087654321', '<EMAIL>', N'عنوان تجريبي', 1, 37, 1, 1, 0, GETUTCDATE());

-- تحديث العداد
UPDATE Counters SET CurrentValue = 25 WHERE CounterName = 'CUSTOMER';

PRINT N'تم إضافة 25 عميل تجريبي مصري بنجاح!';

-- عرض الإحصائيات
SELECT 
    N'إجمالي العملاء' as Description,
    COUNT(*) as Count
FROM Customers
UNION ALL
SELECT 
    N'العملاء النشطون',
    COUNT(*)
FROM Customers WHERE IsActive = 1
UNION ALL
SELECT 
    N'العملاء غير النشطين',
    COUNT(*)
FROM Customers WHERE IsActive = 0;

-- عرض توزيع العملاء حسب المحافظات
SELECT
    a.NameAr as Governorate,
    COUNT(c.Id) as CustomerCount
FROM Areas a
LEFT JOIN Customers c ON a.Id = c.AreaId AND c.IsActive = 1
WHERE a.IsActive = 1
GROUP BY a.Id, a.NameAr, a.DisplayOrder
ORDER BY a.DisplayOrder;

-- عرض توزيع العملاء حسب النوع
SELECT
    ct.NameAr as CustomerType,
    COUNT(c.Id) as CustomerCount
FROM CustomerTypes ct
LEFT JOIN Customers c ON ct.Id = c.CustomerTypeId AND c.IsActive = 1
WHERE ct.IsActive = 1
GROUP BY ct.Id, ct.NameAr
ORDER BY ct.Id;
