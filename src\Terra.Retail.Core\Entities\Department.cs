using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الأقسام
    /// </summary>
    public class Department : BaseEntity
    {
        /// <summary>
        /// اسم القسم بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم القسم بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود القسم
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// القسم الأب
        /// </summary>
        public int? ParentDepartmentId { get; set; }

        /// <summary>
        /// وصف القسم
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// مدير القسم
        /// </summary>
        public int? ManagerId { get; set; }

        /// <summary>
        /// هل القسم نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// لون القسم (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة القسم
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// الميزانية السنوية للقسم
        /// </summary>
        public decimal? AnnualBudget { get; set; }

        /// <summary>
        /// عدد الموظفين المستهدف
        /// </summary>
        public int? TargetEmployeeCount { get; set; }

        /// <summary>
        /// عدد الموظفين الحالي
        /// </summary>
        public int CurrentEmployeeCount { get; set; } = 0;

        /// <summary>
        /// موقع القسم
        /// </summary>
        [MaxLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// رقم الهاتف الداخلي
        /// </summary>
        [MaxLength(20)]
        public string? Extension { get; set; }

        /// <summary>
        /// البريد الإلكتروني للقسم
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        // Navigation Properties
        public virtual Department? ParentDepartment { get; set; }
        public virtual ICollection<Department> SubDepartments { get; set; } = new List<Department>();
        // public virtual Employee? Manager { get; set; }
        // public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        // public virtual ICollection<Position> Positions { get; set; } = new List<Position>();
    }
}
