// Customer Models
export interface Customer {
  id: number;
  customerCode: string;
  nameAr: string;
  nameEn: string;
  phone1: string;
  phone2?: string;
  email: string;
  address: string;
  customerTypeId: number;
  customerTypeName?: string;
  countryId?: number;
  countryName?: string;
  cityId?: number;
  cityName?: string;
  priceCategoryId: number;
  priceCategoryName?: string;
  currentBalance: number;
  isActive: boolean;
  createdAt: Date;
  createdBy?: number;
}

// Supplier Models
export interface Supplier {
  id: number;
  supplierCode: string;
  nameAr: string;
  nameEn: string;
  phone: string;
  email: string;
  address: string;
  supplierTypeId: number;
  supplierTypeName?: string;
  countryId?: number;
  countryName?: string;
  cityId?: number;
  cityName?: string;
  contactPersonName?: string;
  contactPersonPhone?: string;
  currentBalance: number;
  isActive: boolean;
  createdAt: Date;
  createdBy?: number;
}

// Product Models
export interface Product {
  id: number;
  productCode: string;
  nameAr: string;
  nameEn: string;
  description?: string;
  categoryId: number;
  categoryName?: string;
  unitId: number;
  unitName?: string;
  barcodeTypeId: number;
  barcodeTypeName?: string;
  barcode?: string;
  costPrice: number;
  sellingPrice: number;
  minStockLevel: number;
  maxStockLevel: number;
  currentStock: number;
  isActive: boolean;
  createdAt: Date;
  createdBy?: number;
}

// Lookup Models
export interface CustomerType {
  id: number;
  nameAr: string;
  nameEn: string;
  description?: string;
  isActive: boolean;
}

export interface SupplierType {
  id: number;
  nameAr: string;
  nameEn: string;
  description?: string;
  isActive: boolean;
}

export interface Category {
  id: number;
  nameAr: string;
  nameEn: string;
  description?: string;
  isActive: boolean;
}

export interface Unit {
  id: number;
  nameAr: string;
  nameEn: string;
  symbol: string;
  isActive: boolean;
}

export interface PriceCategory {
  id: number;
  nameAr: string;
  nameEn: string;
  discountPercentage: number;
  isActive: boolean;
}

export interface PaymentMethod {
  id: number;
  nameAr: string;
  nameEn: string;
  isActive: boolean;
}

export interface BarcodeType {
  id: number;
  typeCode: string;
  nameAr: string;
  nameEn: string;
  isActive: boolean;
}

// Branch Models
export interface Branch {
  id: number;
  code: string;
  nameAr: string;
  nameEn: string;
  address: string;
  phone: string;
  email?: string;
  managerId?: number;
  managerName?: string;
  isActive: boolean;
  createdAt: Date;
  createdBy?: number;
}

// User Models
export interface User {
  id: number;
  username: string;
  fullName: string;
  email: string;
  phone?: string;
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
}

// Navigation Models
export interface MenuItem {
  id: string;
  titleAr: string;
  titleEn: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
  isActive: boolean;
  permission?: string;
}

// Common Models
export interface LookupData {
  customerTypes: CustomerType[];
  supplierTypes: SupplierType[];
  categories: Category[];
  units: Unit[];
  priceCategories: PriceCategory[];
  paymentMethods: PaymentMethod[];
  barcodeTypes: BarcodeType[];
  countries: any[];
  cities: any[];
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  count?: number;
}
