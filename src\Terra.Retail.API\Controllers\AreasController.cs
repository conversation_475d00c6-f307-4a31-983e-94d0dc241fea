using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Infrastructure.Data;
using Terra.Retail.Core.Entities;
using System.Data.SqlClient;
using Dapper;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api")]
    public class AreasController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<AreasController> _logger;
        private readonly string _connectionString;

        public AreasController(TerraRetailDbContext context, ILogger<AreasController> logger, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? "";
        }

        /// <summary>
        /// الحصول على جميع المحافظات المصرية
        /// </summary>
        /// <returns>قائمة المحافظات</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetAreas()
        {
            try
            {
                var areas = await _context.Areas
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.DisplayOrder)
                    .ThenBy(a => a.NameAr)
                    .Select(a => new
                    {
                        id = a.Id,
                        nameAr = a.NameAr,
                        nameEn = a.NameEn,
                        code = a.Code,
                        displayOrder = a.DisplayOrder,
                        isActive = a.IsActive
                    })
                    .ToListAsync();

                _logger.LogInformation("تم استرجاع {Count} محافظة", areas.Count);
                return Ok(areas);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع المحافظات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على محافظة محددة
        /// </summary>
        /// <param name="id">معرف المحافظة</param>
        /// <returns>المحافظة</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetArea(int id)
        {
            try
            {
                var area = await _context.Areas
                    .Where(a => a.Id == id && a.IsActive)
                    .Select(a => new
                    {
                        id = a.Id,
                        nameAr = a.NameAr,
                        nameEn = a.NameEn,
                        code = a.Code,
                        displayOrder = a.DisplayOrder,
                        isActive = a.IsActive
                    })
                    .FirstOrDefaultAsync();

                if (area == null)
                {
                    return NotFound(new { message = "المحافظة غير موجودة" });
                }

                return Ok(area);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع المحافظة {Id}", id);
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// إضافة محافظة جديدة
        /// </summary>
        [HttpPost("areas")]
        public async Task<ActionResult> CreateArea([FromBody] dynamic request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    INSERT INTO Areas (NameAr, NameEn, Code, IsActive, CreatedAt)
                    VALUES (@NameAr, @NameEn, @Code, @IsActive, GETDATE());
                    SELECT SCOPE_IDENTITY();";

                var newId = await connection.QuerySingleAsync<int>(query, new {
                    NameAr = (string)request.nameAr,
                    NameEn = (string)request.nameEn,
                    Code = (string)request.code,
                    IsActive = (bool)request.isActive
                });

                return Ok(new {
                    id = newId,
                    message = "تم إضافة المحافظة بنجاح"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المحافظة");
                return BadRequest(new {
                    message = "خطأ في إضافة المحافظة",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// تحديث محافظة
        /// </summary>
        [HttpPut("areas/{id}")]
        public async Task<ActionResult> UpdateArea(int id, [FromBody] dynamic request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    UPDATE Areas
                    SET NameAr = @NameAr, NameEn = @NameEn, Code = @Code,
                        IsActive = @IsActive, UpdatedAt = GETDATE()
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(query, new {
                    Id = id,
                    NameAr = (string)request.nameAr,
                    NameEn = (string)request.nameEn,
                    Code = (string)request.code,
                    IsActive = (bool)request.isActive
                });

                if (rowsAffected > 0)
                {
                    return Ok(new { message = "تم تحديث المحافظة بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "المحافظة غير موجودة" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المحافظة {Id}", id);
                return BadRequest(new {
                    message = "خطأ في تحديث المحافظة",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// حذف محافظة
        /// </summary>
        [HttpDelete("areas/{id}")]
        public async Task<ActionResult> DeleteArea(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    UPDATE Areas
                    SET IsActive = 0, UpdatedAt = GETDATE()
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(query, new { Id = id });

                if (rowsAffected > 0)
                {
                    return Ok(new { message = "تم حذف المحافظة بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "المحافظة غير موجودة" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المحافظة {Id}", id);
                return BadRequest(new {
                    message = "خطأ في حذف المحافظة",
                    error = ex.Message
                });
            }
        }
    }
}
