using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("🔐 Authentication & Authorization")]
    public class AuthController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly IConfiguration _configuration;

        public AuthController(TerraRetailDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.DefaultBranch)
                    .Include(u => u.Employee)
                    .Include(u => u.UserBranches)
                        .ThenInclude(ub => ub.Branch)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Username == request.Username || u.Email == request.Username);

                if (user == null || !VerifyPassword(request.Password, user.PasswordHash))
                {
                    return BadRequest(new { message = "اسم المستخدم أو كلمة المرور غير صحيحة" });
                }

                if (!user.IsActive)
                {
                    return BadRequest(new { message = "حساب المستخدم غير مفعل" });
                }

                // Check if account is locked
                if (user.LockedUntil.HasValue && user.LockedUntil > DateTime.Now)
                {
                    return BadRequest(new { message = "الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً" });
                }

                // Reset login attempts on successful login
                user.LoginAttempts = 0;
                user.LockedUntil = null;
                user.LastLoginAt = DateTime.Now;
                user.LastLoginIP = GetClientIP();

                // Generate JWT token
                var token = GenerateJwtToken(user);

                // Create user session
                var session = new UserSession
                {
                    UserId = user.Id,
                    SessionToken = token,
                    CurrentBranchId = user.DefaultBranchId ?? user.UserBranches.FirstOrDefault()?.BranchId ?? 1,
                    LoginTime = DateTime.Now,
                    LastActivity = DateTime.Now,
                    IPAddress = GetClientIP(),
                    UserAgent = Request.Headers["User-Agent"].ToString(),
                    IsActive = true,
                    ExpiryTime = DateTime.Now.AddHours(Convert.ToDouble(_configuration["Jwt:ExpiryInHours"]))
                };

                _context.UserSessions.Add(session);
                await _context.SaveChangesAsync();

                var response = new LoginResponse
                {
                    Token = token,
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        FullName = user.FullName,
                        DefaultBranchId = user.DefaultBranchId,
                        DefaultBranchName = user.DefaultBranch?.NameAr,
                        PreferredLanguage = user.PreferredLanguage,
                        ProfileImage = user.ProfileImage
                    },
                    Branches = user.UserBranches.Where(ub => ub.IsActive).Select(ub => new BranchInfo
                    {
                        Id = ub.BranchId,
                        Name = ub.Branch.NameAr,
                        Code = ub.Branch.Code,
                        IsDefault = ub.IsDefault,
                        Permissions = new BranchPermissions
                        {
                            CanView = ub.CanView,
                            CanCreate = ub.CanCreate,
                            CanEdit = ub.CanEdit,
                            CanDelete = ub.CanDelete,
                            CanApprove = ub.CanApprove,
                            CanViewReports = ub.CanViewReports,
                            CanManageInventory = ub.CanManageInventory,
                            CanManageFinance = ub.CanManageFinance,
                            CanManageHR = ub.CanManageHR,
                            CanManageSettings = ub.CanManageSettings
                        }
                    }).ToList(),
                    Roles = user.UserRoles.Where(ur => ur.IsActive).Select(ur => ur.Role.NameAr).ToList(),
                    ExpiresAt = DateTime.Now.AddHours(Convert.ToDouble(_configuration["Jwt:ExpiryInHours"]))
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                
                var session = await _context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionToken == token && s.IsActive);

                if (session != null)
                {
                    session.IsActive = false;
                    session.LogoutTime = DateTime.Now;
                    session.LogoutReason = "User logout";
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = "تم تسجيل الخروج بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken()
        {
            try
            {
                var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                
                var session = await _context.UserSessions
                    .Include(s => s.User)
                        .ThenInclude(u => u.DefaultBranch)
                    .Include(s => s.User)
                        .ThenInclude(u => u.UserBranches)
                            .ThenInclude(ub => ub.Branch)
                    .FirstOrDefaultAsync(s => s.SessionToken == token && s.IsActive);

                if (session == null || session.ExpiryTime < DateTime.Now)
                {
                    return Unauthorized(new { message = "الجلسة منتهية الصلاحية" });
                }

                // Generate new token
                var newToken = GenerateJwtToken(session.User);
                
                // Update session
                session.SessionToken = newToken;
                session.LastActivity = DateTime.Now;
                session.ExpiryTime = DateTime.Now.AddHours(Convert.ToDouble(_configuration["Jwt:ExpiryInHours"]));

                await _context.SaveChangesAsync();

                return Ok(new { 
                    token = newToken,
                    expiresAt = session.ExpiryTime
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var user = await _context.Users
                    .Include(u => u.DefaultBranch)
                    .Include(u => u.Employee)
                    .Include(u => u.UserBranches)
                        .ThenInclude(ub => ub.Branch)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                    return NotFound();

                var profile = new UserProfile
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FullName = user.FullName,
                    PhoneNumber = user.PhoneNumber,
                    ProfileImage = user.ProfileImage,
                    DefaultBranchId = user.DefaultBranchId,
                    DefaultBranchName = user.DefaultBranch?.NameAr,
                    EmployeeId = user.EmployeeId,
                    EmployeeName = user.Employee?.NameAr,
                    PreferredLanguage = user.PreferredLanguage,
                    TimeZone = user.TimeZone,
                    LastLoginAt = user.LastLoginAt,
                    CreatedAt = user.CreatedAt,
                    Branches = user.UserBranches.Where(ub => ub.IsActive).Select(ub => new BranchInfo
                    {
                        Id = ub.BranchId,
                        Name = ub.Branch.NameAr,
                        Code = ub.Branch.Code,
                        IsDefault = ub.IsDefault
                    }).ToList(),
                    Roles = user.UserRoles.Where(ur => ur.IsActive).Select(ur => ur.Role.NameAr).ToList()
                };

                return Ok(profile);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"]!);
            
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim("FullName", user.FullName),
                new Claim("DefaultBranchId", user.DefaultBranchId?.ToString() ?? ""),
                new Claim("PreferredLanguage", user.PreferredLanguage)
            };

            // Add role claims
            foreach (var userRole in user.UserRoles.Where(ur => ur.IsActive))
            {
                claims.Add(new Claim(ClaimTypes.Role, userRole.Role.NameEn ?? userRole.Role.NameAr));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(Convert.ToDouble(_configuration["Jwt:ExpiryInHours"])),
                Issuer = _configuration["Jwt:Issuer"],
                Audience = _configuration["Jwt:Audience"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private bool VerifyPassword(string password, string hash)
        {
            // Simple password verification - in production, use proper hashing like BCrypt
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }

        private string GetClientIP()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class LoginRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class LoginResponse
    {
        public string Token { get; set; } = string.Empty;
        public UserInfo User { get; set; } = new();
        public List<BranchInfo> Branches { get; set; } = new();
        public List<string> Roles { get; set; } = new();
        public DateTime ExpiresAt { get; set; }
    }

    public class UserInfo
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public int? DefaultBranchId { get; set; }
        public string? DefaultBranchName { get; set; }
        public string PreferredLanguage { get; set; } = "ar";
        public string? ProfileImage { get; set; }
    }

    public class BranchInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
        public BranchPermissions? Permissions { get; set; }
    }

    public class BranchPermissions
    {
        public bool CanView { get; set; }
        public bool CanCreate { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanApprove { get; set; }
        public bool CanViewReports { get; set; }
        public bool CanManageInventory { get; set; }
        public bool CanManageFinance { get; set; }
        public bool CanManageHR { get; set; }
        public bool CanManageSettings { get; set; }
    }

    public class UserProfile
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? ProfileImage { get; set; }
        public int? DefaultBranchId { get; set; }
        public string? DefaultBranchName { get; set; }
        public int? EmployeeId { get; set; }
        public string? EmployeeName { get; set; }
        public string PreferredLanguage { get; set; } = "ar";
        public string TimeZone { get; set; } = "Africa/Cairo";
        public DateTime? LastLoginAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<BranchInfo> Branches { get; set; } = new();
        public List<string> Roles { get; set; } = new();
    }
}
