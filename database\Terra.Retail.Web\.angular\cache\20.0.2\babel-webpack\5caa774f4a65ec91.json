{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function (...args) {\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function (...args) {\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n  return function (...args) {\n    const subject = new AsyncSubject();\n    let uninitialized = true;\n    return new Observable(subscriber => {\n      const subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        let isAsync = false;\n        let isComplete = false;\n        callbackFunc.apply(this, [...args, (...results) => {\n          if (isNodeStyle) {\n            const err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete = true;\n          if (isAsync) {\n            subject.complete();\n          }\n        }]);\n        if (isComplete) {\n          subject.complete();\n        }\n        isAsync = true;\n      }\n      return subs;\n    });\n  };\n}", "map": {"version": 3, "names": ["isScheduler", "Observable", "subscribeOn", "mapOneOrManyArgs", "observeOn", "AsyncSubject", "bindCallbackInternals", "isNodeStyle", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "apply", "pipe", "subject", "uninitialized", "subscriber", "subs", "subscribe", "isAsync", "isComplete", "results", "err", "shift", "error", "next", "length", "complete"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/rxjs/dist/esm/internal/observable/bindCallbackInternals.js"], "sourcesContent": ["import { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return function (...args) {\n                return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler)\n                    .apply(this, args)\n                    .pipe(mapOneOrManyArgs(resultSelector));\n            };\n        }\n    }\n    if (scheduler) {\n        return function (...args) {\n            return bindCallbackInternals(isNodeStyle, callbackFunc)\n                .apply(this, args)\n                .pipe(subscribeOn(scheduler), observeOn(scheduler));\n        };\n    }\n    return function (...args) {\n        const subject = new AsyncSubject();\n        let uninitialized = true;\n        return new Observable((subscriber) => {\n            const subs = subject.subscribe(subscriber);\n            if (uninitialized) {\n                uninitialized = false;\n                let isAsync = false;\n                let isComplete = false;\n                callbackFunc.apply(this, [\n                    ...args,\n                    (...results) => {\n                        if (isNodeStyle) {\n                            const err = results.shift();\n                            if (err != null) {\n                                subject.error(err);\n                                return;\n                            }\n                        }\n                        subject.next(1 < results.length ? results : results[0]);\n                        isComplete = true;\n                        if (isAsync) {\n                            subject.complete();\n                        }\n                    },\n                ]);\n                if (isComplete) {\n                    subject.complete();\n                }\n                isAsync = true;\n            }\n            return subs;\n        });\n    };\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,qBAAqBA,CAACC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACxF,IAAID,cAAc,EAAE;IAChB,IAAIT,WAAW,CAACS,cAAc,CAAC,EAAE;MAC7BC,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI;MACD,OAAO,UAAU,GAAGE,IAAI,EAAE;QACtB,OAAOL,qBAAqB,CAACC,WAAW,EAAEC,YAAY,EAAEE,SAAS,CAAC,CAC7DE,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC,CACjBE,IAAI,CAACV,gBAAgB,CAACM,cAAc,CAAC,CAAC;MAC/C,CAAC;IACL;EACJ;EACA,IAAIC,SAAS,EAAE;IACX,OAAO,UAAU,GAAGC,IAAI,EAAE;MACtB,OAAOL,qBAAqB,CAACC,WAAW,EAAEC,YAAY,CAAC,CAClDI,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC,CACjBE,IAAI,CAACX,WAAW,CAACQ,SAAS,CAAC,EAAEN,SAAS,CAACM,SAAS,CAAC,CAAC;IAC3D,CAAC;EACL;EACA,OAAO,UAAU,GAAGC,IAAI,EAAE;IACtB,MAAMG,OAAO,GAAG,IAAIT,YAAY,CAAC,CAAC;IAClC,IAAIU,aAAa,GAAG,IAAI;IACxB,OAAO,IAAId,UAAU,CAAEe,UAAU,IAAK;MAClC,MAAMC,IAAI,GAAGH,OAAO,CAACI,SAAS,CAACF,UAAU,CAAC;MAC1C,IAAID,aAAa,EAAE;QACfA,aAAa,GAAG,KAAK;QACrB,IAAII,OAAO,GAAG,KAAK;QACnB,IAAIC,UAAU,GAAG,KAAK;QACtBZ,YAAY,CAACI,KAAK,CAAC,IAAI,EAAE,CACrB,GAAGD,IAAI,EACP,CAAC,GAAGU,OAAO,KAAK;UACZ,IAAId,WAAW,EAAE;YACb,MAAMe,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC;YAC3B,IAAID,GAAG,IAAI,IAAI,EAAE;cACbR,OAAO,CAACU,KAAK,CAACF,GAAG,CAAC;cAClB;YACJ;UACJ;UACAR,OAAO,CAACW,IAAI,CAAC,CAAC,GAAGJ,OAAO,CAACK,MAAM,GAAGL,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;UACvDD,UAAU,GAAG,IAAI;UACjB,IAAID,OAAO,EAAE;YACTL,OAAO,CAACa,QAAQ,CAAC,CAAC;UACtB;QACJ,CAAC,CACJ,CAAC;QACF,IAAIP,UAAU,EAAE;UACZN,OAAO,CAACa,QAAQ,CAAC,CAAC;QACtB;QACAR,OAAO,GAAG,IAAI;MAClB;MACA,OAAOF,IAAI;IACf,CAAC,CAAC;EACN,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}