using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Products")]
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string ProductCode { get; set; } = string.Empty;

        [Required]
        [StringLength(400)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(400)]
        public string? NameEn { get; set; }

        [StringLength(2000)]
        public string? Description { get; set; }

        public int CategoryId { get; set; }
        public int UnitId { get; set; }

        [StringLength(100)]
        public string? Barcode { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal ProfitMargin { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MinimumStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaximumStock { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ReorderPoint { get; set; }

        public bool IsActive { get; set; } = true;

        [Column(TypeName = "decimal(18,3)")]
        public decimal? Weight { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? Length { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? Width { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? Height { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; } = null!;

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; } = null!;

        public virtual ICollection<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
        public virtual ICollection<ProductBatch> ProductBatches { get; set; } = new List<ProductBatch>();
        public virtual ICollection<ProductImage> ProductImages { get; set; } = new List<ProductImage>();
        public virtual ICollection<ProductAlternativeCode> ProductAlternativeCodes { get; set; } = new List<ProductAlternativeCode>();
        public virtual ICollection<ProductBranchPrice> ProductBranchPrices { get; set; } = new List<ProductBranchPrice>();
        public virtual ICollection<ProductSupplier> ProductSuppliers { get; set; } = new List<ProductSupplier>();
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<BranchTransferDetail> BranchTransferDetails { get; set; } = new List<BranchTransferDetail>();
    }

    [Table("Categories")]
    public class Category
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [StringLength(40)]
        public string? Code { get; set; }

        public int? ParentCategoryId { get; set; }

        public int Level { get; set; } = 1;

        [StringLength(200)]
        public string? Path { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        [StringLength(14)]
        public string? Color { get; set; }

        public int DisplayOrder { get; set; } = 1;

        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; } = false;

        public int ProductCount { get; set; } = 0;

        [StringLength(1000)]
        public string? Keywords { get; set; }

        [StringLength(400)]
        public string? SeoTitle { get; set; }

        [StringLength(1000)]
        public string? SeoDescription { get; set; }

        [StringLength(1000)]
        public string? SeoKeywords { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ParentCategoryId")]
        public virtual Category? ParentCategory { get; set; }

        public virtual ICollection<Category> SubCategories { get; set; } = new List<Category>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    [Table("Units")]
    public class Unit
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(20)]
        public string Symbol { get; set; } = string.Empty;

        public int UnitType { get; set; } = 1; // 1=Piece, 2=Weight, 3=Length, etc.

        public int? BaseUnitId { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        public decimal ConversionFactor { get; set; } = 1;

        [StringLength(400)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;

        public int DisplayOrder { get; set; } = 1;
        public int DecimalPlaces { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("BaseUnitId")]
        public virtual Unit? BaseUnit { get; set; }

        public virtual ICollection<Unit> DerivedUnits { get; set; } = new List<Unit>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
