using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("👥 Customer Management")]
    public class CustomersController : ControllerBase
    {
        private readonly AppDbContext _context;

        public CustomersController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Customer>>> GetCustomers()
        {
            try
            {
                var customers = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Country)
                    .Include(c => c.City)
                    .Include(c => c.PriceCategory)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        c.Id,
                        c.CustomerCode,
                        c.NameAr,
                        c.<PERSON>n,
                        c.Phone1,
                        c.Email,
                        c.Address,
                        CustomerTypeName = c.CustomerType != null ? c.CustomerType.NameAr : null,
                        CountryName = c.Country != null ? c.Country.NameAr : null,
                        CityName = c.City != null ? c.City.NameAr : null,
                        PriceCategoryName = c.PriceCategory != null ? c.PriceCategory.NameAr : null,
                        c.CurrentBalance,
                        c.IsActive,
                        c.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة العملاء بنجاح",
                    data = customers,
                    count = customers.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("next-code")]
        public async Task<ActionResult<object>> GetNextCustomerCode()
        {
            try
            {
                // البحث عن كونتر العملاء
                var counter = await _context.Counters
                    .FirstOrDefaultAsync(c => c.CounterName == "Customer");

                if (counter == null)
                {
                    // إنشاء كونتر جديد إذا لم يكن موجود
                    counter = new Counter
                    {
                        CounterName = "Customer",
                        Prefix = "CUS",
                        CurrentValue = 1,
                        NumberLength = 6,
                        BranchId = 1,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };
                    _context.Counters.Add(counter);
                    await _context.SaveChangesAsync();
                }

                // حجز الرقم التالي
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                // تكوين الكود
                var customerCode = $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";

                return Ok(new
                {
                    success = true,
                    message = "تم حجز كود العميل بنجاح",
                    data = new
                    {
                        customerCode = customerCode,
                        counterValue = counter.CurrentValue
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حجز كود العميل",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Customer>> GetCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);

                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات العميل بنجاح",
                    data = customer
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }



        [HttpPost]
        public async Task<ActionResult<Customer>> CreateCustomer(CreateCustomerRequest request)
        {
            try
            {
                // Generate customer code
                var lastCustomer = await _context.Customers
                    .OrderByDescending(c => c.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastCustomer?.Id ?? 0) + 1;
                var customerCode = $"CUS{nextId:D6}";

                var customer = new Customer
                {
                    CustomerCode = customerCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Phone1 = request.Phone1,
                    Email = request.Email,
                    Address = request.Address,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                // إنشاء حساب العميل في شجرة الحسابات
                await CreateCustomerAccount(customer);

                // إنشاء قيد يومية للرصيد الافتتاحي إذا كان موجود
                if (customer.OpeningBalance != 0)
                {
                    await CreateOpeningBalanceJournalEntry(customer);
                }

                return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, new
                {
                    success = true,
                    message = "تم إضافة العميل بنجاح",
                    data = customer
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomer(int id, UpdateCustomerRequest request)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                customer.NameAr = request.NameAr;
                customer.NameEn = request.NameEn;
                customer.Phone1 = request.Phone1;
                customer.Email = request.Email;
                customer.Address = request.Address;
                customer.IsActive = request.IsActive;

                await _context.SaveChangesAsync();

                // تحديث اسم العميل في شجرة الحسابات
                await UpdateCustomerAccountName(customer);

                return Ok(new 
                { 
                    success = true,
                    message = "تم تحديث بيانات العميل بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                _context.Customers.Remove(customer);
                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم حذف العميل بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        // إنشاء حساب العميل في شجرة الحسابات
        private async Task CreateCustomerAccount(Customer customer)
        {
            try
            {
                // البحث عن حساب العملاء المحليين
                var parentAccount = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == "11201"); // عملاء محليون

                if (parentAccount == null)
                {
                    // إنشاء حساب العملاء المحليين إذا لم يكن موجود
                    var customersParent = await _context.ChartOfAccounts
                        .FirstOrDefaultAsync(a => a.AccountCode == "112"); // العملاء

                    if (customersParent != null)
                    {
                        parentAccount = new ChartOfAccount
                        {
                            AccountCode = "11201",
                            NameAr = "عملاء محليين",
                            NameEn = "Local Customers",
                            AccountType = 1, // Assets
                            ParentId = customersParent.Id,
                            Level = 4,
                            IsParent = false,
                            AllowPosting = true,
                            IsActive = true,
                            CreatedAt = DateTime.Now
                        };
                        _context.ChartOfAccounts.Add(parentAccount);
                        await _context.SaveChangesAsync();
                    }
                }

                // إنشاء حساب العميل
                var customerAccount = new ChartOfAccount
                {
                    AccountCode = customer.CustomerCode,
                    NameAr = customer.NameAr,
                    NameEn = customer.NameEn,
                    AccountType = 1, // Assets
                    ParentId = parentAccount?.Id,
                    Level = 5,
                    IsParent = false,
                    AllowPosting = true,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.ChartOfAccounts.Add(customerAccount);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't fail customer creation
                Console.WriteLine($"Error creating customer account: {ex.Message}");
            }
        }

        // إنشاء قيد يومية للرصيد الافتتاحي
        private async Task CreateOpeningBalanceJournalEntry(Customer customer)
        {
            try
            {
                var journalEntry = new JournalEntry
                {
                    EntryNumber = await GenerateJournalEntryNumber(),
                    Description = $"رصيد افتتاحي للعميل {customer.CustomerCode} - {customer.NameAr}",
                    EntryDate = DateTime.Now,
                    Status = JournalEntryStatus.Draft,
                    TransactionType = 2, // Opening Balance
                    TotalDebit = customer.OpeningBalance > 0 ? customer.OpeningBalance : 0,
                    TotalCredit = customer.OpeningBalance > 0 ? customer.OpeningBalance : Math.Abs(customer.OpeningBalance),
                    UserId = 1, // Default user
                    CreatedAt = DateTime.Now
                };

                _context.JournalEntries.Add(journalEntry);
                await _context.SaveChangesAsync();

                // البحث عن حسابات العميل ورأس المال
                var customerAccount = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == customer.CustomerCode);
                var capitalAccount = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == "3001");

                if (customerAccount == null || capitalAccount == null)
                {
                    Console.WriteLine("Customer account or capital account not found");
                    return;
                }

                // تفاصيل القيد
                var details = new List<JournalEntryDetail>();

                if (customer.OpeningBalance > 0) // العميل له رصيد (مدين)
                {
                    // مدين: حساب العميل
                    details.Add(new JournalEntryDetail
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = customerAccount.Id,
                        LineNumber = 1,
                        Description = $"رصيد افتتاحي للعميل {customer.NameAr}",
                        DebitAmount = customer.OpeningBalance,
                        CreditAmount = 0
                    });

                    // دائن: حساب رأس المال أو الأرباح المحتجزة
                    details.Add(new JournalEntryDetail
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = capitalAccount.Id,
                        LineNumber = 2,
                        Description = $"رصيد افتتاحي للعميل {customer.NameAr}",
                        DebitAmount = 0,
                        CreditAmount = customer.OpeningBalance
                    });
                }
                else if (customer.OpeningBalance < 0) // العميل عليه رصيد (دائن)
                {
                    var amount = Math.Abs(customer.OpeningBalance);

                    // دائن: حساب العميل
                    details.Add(new JournalEntryDetail
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = customerAccount.Id,
                        LineNumber = 1,
                        Description = $"رصيد افتتاحي للعميل {customer.NameAr}",
                        DebitAmount = 0,
                        CreditAmount = amount
                    });

                    // مدين: حساب رأس المال أو الأرباح المحتجزة
                    details.Add(new JournalEntryDetail
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = capitalAccount.Id,
                        LineNumber = 2,
                        Description = $"رصيد افتتاحي للعميل {customer.NameAr}",
                        DebitAmount = amount,
                        CreditAmount = 0
                    });
                }

                _context.JournalEntryDetails.AddRange(details);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't fail customer creation
                Console.WriteLine($"Error creating opening balance journal entry: {ex.Message}");
            }
        }

        // تحديث اسم العميل في شجرة الحسابات
        private async Task UpdateCustomerAccountName(Customer customer)
        {
            try
            {
                var customerAccount = await _context.ChartOfAccounts
                    .FirstOrDefaultAsync(a => a.AccountCode == customer.CustomerCode);

                if (customerAccount != null)
                {
                    customerAccount.NameAr = customer.NameAr;
                    customerAccount.NameEn = customer.NameEn;
                    customerAccount.UpdatedAt = DateTime.Now;

                    await _context.SaveChangesAsync();
                    Console.WriteLine($"تم تحديث اسم العميل في شجرة الحسابات: {customer.CustomerCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating customer account name: {ex.Message}");
            }
        }

        // توليد رقم قيد يومية
        private async Task<string> GenerateJournalEntryNumber()
        {
            try
            {
                var count = await _context.JournalEntries.CountAsync();
                var nextId = count + 1;
                return $"JE{nextId:D6}";
            }
            catch
            {
                // Fallback if there's an issue
                return $"JE{DateTime.Now:yyyyMMddHHmmss}";
            }
        }
    }

    // DTOs
    public class CreateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public decimal OpeningBalance { get; set; } = 0;
    }

    public class UpdateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
