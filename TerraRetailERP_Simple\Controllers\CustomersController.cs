using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("👥 Customer Management")]
    public class CustomersController : ControllerBase
    {
        private readonly AppDbContext _context;

        public CustomersController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Customer>>> GetCustomers()
        {
            try
            {
                var customers = await _context.Customers
                    .Include(c => c.CustomerType)
                    .Include(c => c.Country)
                    .Include(c => c.City)
                    .Include(c => c.PriceCategory)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.NameAr)
                    .Select(c => new
                    {
                        c.Id,
                        c.CustomerCode,
                        c.NameAr,
                        c.<PERSON>n,
                        c.Phone1,
                        c.Email,
                        c.Address,
                        CustomerTypeName = c.CustomerType != null ? c.CustomerType.NameAr : null,
                        CountryName = c.Country != null ? c.Country.NameAr : null,
                        CityName = c.City != null ? c.City.NameAr : null,
                        PriceCategoryName = c.PriceCategory != null ? c.PriceCategory.NameAr : null,
                        c.CurrentBalance,
                        c.IsActive,
                        c.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة العملاء بنجاح",
                    data = customers,
                    count = customers.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Customer>> GetCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);

                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات العميل بنجاح",
                    data = customer
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("next-code")]
        public async Task<IActionResult> GetNextCustomerCode()
        {
            try
            {
                // الحصول على الكونتر
                var counter = await _context.Counters
                    .FirstOrDefaultAsync(c => c.CounterName == "Customer");

                if (counter == null)
                {
                    return BadRequest(new { success = false, message = "Customer counter not found" });
                }

                // إنشاء الكود التالي (مبسط)
                var nextNumber = counter.CurrentValue + 1;
                string nextCode;

                if (counter.NumberLength == 1)
                {
                    // كود مبسط: CUS1, CUS2, إلخ
                    nextCode = counter.Prefix + nextNumber.ToString();
                }
                else
                {
                    // كود مع padding: CUS000001, CUS000002, إلخ
                    nextCode = counter.Prefix + nextNumber.ToString().PadLeft(counter.NumberLength, '0');
                }

                return Ok(new {
                    success = true,
                    data = new {
                        nextCode = nextCode,
                        currentValue = counter.CurrentValue,
                        nextValue = nextNumber,
                        isSimpleFormat = counter.NumberLength == 1
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Customer>> CreateCustomer(CreateCustomerRequest request)
        {
            try
            {
                // Generate customer code
                var lastCustomer = await _context.Customers
                    .OrderByDescending(c => c.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastCustomer?.Id ?? 0) + 1;
                var customerCode = $"CUS{nextId:D6}";

                var customer = new Customer
                {
                    CustomerCode = customerCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Phone1 = request.Phone1,
                    Email = request.Email,
                    Address = request.Address,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, new
                {
                    success = true,
                    message = "تم إضافة العميل بنجاح",
                    data = customer
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomer(int id, UpdateCustomerRequest request)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                customer.NameAr = request.NameAr;
                customer.NameEn = request.NameEn;
                customer.Phone1 = request.Phone1;
                customer.Email = request.Email;
                customer.Address = request.Address;
                customer.IsActive = request.IsActive;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم تحديث بيانات العميل بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return NotFound(new 
                    { 
                        success = false,
                        message = "العميل غير موجود" 
                    });

                _context.Customers.Remove(customer);
                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    success = true,
                    message = "تم حذف العميل بنجاح" 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs
    public class CreateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public decimal OpeningBalance { get; set; } = 0;
    }

    public class UpdateCustomerRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Phone1 { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
