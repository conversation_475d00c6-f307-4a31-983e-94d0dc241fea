using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("💰 Financial Transactions")]
    public class TransactionController : ControllerBase
    {
        private readonly AppDbContext _context;

        public TransactionController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("financial")]
        public async Task<ActionResult> GetFinancialTransactions(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] TransactionType? transactionType = null,
            [FromQuery] int? branchId = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today.AddDays(1);

                var query = _context.FinancialTransactions
                    .Include(ft => ft.Customer)
                    .Include(ft => ft.Supplier)
                    .Include(ft => ft.Branch)
                    .Include(ft => ft.User)
                    .Include(ft => ft.PaymentMethod)
                    .Where(ft => ft.TransactionDate >= startDate && ft.TransactionDate < endDate);

                if (transactionType.HasValue)
                    query = query.Where(ft => ft.TransactionType == transactionType);

                if (branchId.HasValue)
                    query = query.Where(ft => ft.BranchId == branchId);

                var totalCount = await query.CountAsync();
                
                var transactions = await query
                    .OrderByDescending(ft => ft.TransactionDate)
                    .ThenByDescending(ft => ft.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(ft => new
                    {
                        ft.Id,
                        ft.TransactionNumber,
                        ft.TransactionDate,
                        ft.TransactionType,
                        TransactionTypeName = TransactionTypeHelper.GetTransactionTypeName(ft.TransactionType),
                        ft.SourceType,
                        ft.SourceNumber,
                        CustomerName = ft.Customer != null ? ft.Customer.NameAr : null,
                        SupplierName = ft.Supplier != null ? ft.Supplier.NameAr : null,
                        BranchName = ft.Branch.NameAr,
                        ft.DebitAmount,
                        ft.CreditAmount,
                        ft.NetAmount,
                        ft.Description,
                        ft.Status,
                        StatusName = TransactionTypeHelper.GetTransactionStatusName(ft.Status),
                        PaymentMethodName = ft.PaymentMethod != null ? ft.PaymentMethod.NameAr : null,
                        ft.IsReconciled,
                        UserName = ft.User.FullName
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب المعاملات المالية بنجاح",
                    data = transactions,
                    pagination = new
                    {
                        page,
                        pageSize,
                        totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("financial/{id}")]
        public async Task<ActionResult> GetFinancialTransaction(long id)
        {
            try
            {
                var transaction = await _context.FinancialTransactions
                    .Include(ft => ft.Customer)
                    .Include(ft => ft.Supplier)
                    .Include(ft => ft.Branch)
                    .Include(ft => ft.User)
                    .Include(ft => ft.PaymentMethod)
                    .Include(ft => ft.JournalEntry)
                    .FirstOrDefaultAsync(ft => ft.Id == id);

                if (transaction == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "المعاملة المالية غير موجودة"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب تفاصيل المعاملة المالية بنجاح",
                    data = transaction
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("tracking/{transactionId}")]
        public async Task<ActionResult> GetTransactionTracking(long transactionId)
        {
            try
            {
                var tracking = await _context.TransactionTrackings
                    .Include(tt => tt.User)
                    .Where(tt => tt.FinancialTransactionId == transactionId)
                    .OrderByDescending(tt => tt.ActionDate)
                    .Select(tt => new
                    {
                        tt.Id,
                        tt.Action,
                        tt.OldStatus,
                        tt.NewStatus,
                        tt.OldAmount,
                        tt.NewAmount,
                        tt.ChangeDetails,
                        tt.Reason,
                        tt.ActionDate,
                        UserName = tt.User.FullName,
                        tt.IPAddress
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب سجل تتبع المعاملة بنجاح",
                    data = tracking
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }



        [HttpPost("financial/{id}/reconcile")]
        public async Task<ActionResult> ReconcileTransaction(long id, ReconcileTransactionRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var financialTransaction = await _context.FinancialTransactions
                    .FirstOrDefaultAsync(ft => ft.Id == id);

                if (financialTransaction == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "المعاملة المالية غير موجودة"
                    });
                }

                if (financialTransaction.IsReconciled)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "المعاملة مطابقة مسبقاً"
                    });
                }

                // Update transaction
                financialTransaction.IsReconciled = true;
                financialTransaction.ReconciledAt = DateTime.Now;
                financialTransaction.ReconciledBy = request.UserId;
                financialTransaction.UpdatedAt = DateTime.Now;

                // Add tracking record
                var tracking = new TransactionTracking
                {
                    FinancialTransactionId = id,
                    Action = "Reconciled",
                    OldStatus = "Not Reconciled",
                    NewStatus = "Reconciled",
                    Reason = request.Reason,
                    UserId = request.UserId,
                    ActionDate = DateTime.Now,
                    IPAddress = GetClientIP()
                };

                _context.TransactionTrackings.Add(tracking);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تطابق المعاملة بنجاح"
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("audit-log")]
        public async Task<ActionResult> GetAuditLog(
            [FromQuery] string? tableName = null,
            [FromQuery] int? recordId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddDays(-7);
                var endDate = toDate ?? DateTime.Today.AddDays(1);

                var query = _context.AuditLogs
                    .Include(al => al.User)
                    .Where(al => al.ActionDate >= startDate && al.ActionDate < endDate);

                if (!string.IsNullOrEmpty(tableName))
                    query = query.Where(al => al.TableName == tableName);

                if (recordId.HasValue)
                    query = query.Where(al => al.RecordId == recordId);

                var totalCount = await query.CountAsync();
                
                var auditLogs = await query
                    .OrderByDescending(al => al.ActionDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(al => new
                    {
                        al.Id,
                        al.TableName,
                        al.RecordId,
                        al.Action,
                        al.OldValues,
                        al.NewValues,
                        al.ChangedFields,
                        al.ActionDate,
                        UserName = al.User.FullName,
                        al.IPAddress,
                        al.Description
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب سجل التدقيق بنجاح",
                    data = auditLogs,
                    pagination = new
                    {
                        page,
                        pageSize,
                        totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("transaction-types")]
        public ActionResult GetTransactionTypes()
        {
            try
            {
                var transactionTypes = TransactionTypeHelper.GetTransactionTypesList();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب أنواع المعاملات بنجاح",
                    data = transactionTypes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }

        [HttpGet("transaction-statuses")]
        public ActionResult GetTransactionStatuses()
        {
            try
            {
                var statuses = Enum.GetValues<TransactionStatus>()
                    .Select(s => new
                    {
                        Value = (int)s,
                        Name = TransactionTypeHelper.GetTransactionStatusName(s),
                        EnglishName = s.ToString()
                    })
                    .ToList();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب حالات المعاملات بنجاح",
                    data = statuses
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ في النظام",
                    error = ex.Message
                });
            }
        }



        private async Task<string> GetNextTransactionNumber()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.FinancialTransaction.ToString());

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.FinancialTransaction.ToString(),
                    Prefix = "FT",
                    CurrentValue = 1,
                    NumberLength = 8,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();
            return $"{counter.Prefix}{DateTime.Now.Year}{counter.CurrentValue:D8}";
        }

        private string GetClientIP()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
    }

    // DTOs
    public class CreateFinancialTransactionRequest
    {
        public DateTime? TransactionDate { get; set; }
        public TransactionType TransactionType { get; set; } = TransactionType.Sale;
        public string? SourceType { get; set; }
        public int? SourceId { get; set; }
        public string? SourceNumber { get; set; }
        public int? CustomerId { get; set; }
        public int? SupplierId { get; set; }
        public int BranchId { get; set; } = 1;
        public int UserId { get; set; } = 1;
        public decimal DebitAmount { get; set; } = 0;
        public decimal CreditAmount { get; set; } = 0;
        public string Description { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public int? PaymentMethodId { get; set; }
        public string? CheckNumber { get; set; }
        public string? BankReference { get; set; }
        public DateTime? DueDate { get; set; }
        public string? CurrencyCode { get; set; } = "EGP";
        public decimal? ExchangeRate { get; set; } = 1;
        public decimal? TransactionFee { get; set; }
        public string? Notes { get; set; }
    }

    public class ReconcileTransactionRequest
    {
        public int UserId { get; set; } = 1;
        public string? Reason { get; set; }
    }
}
