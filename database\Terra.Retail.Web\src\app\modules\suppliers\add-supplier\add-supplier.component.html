<!-- Terra Retail ERP - Add Supplier Form -->
<div class="add-supplier-container">

  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">إضافة مورد جديد</h1>
          <p class="page-subtitle">إدخال بيانات مورد جديد للنظام</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-stroked-button class="cancel-btn" (click)="cancel()">
          <mat-icon>close</mat-icon>
          <span>إلغاء</span>
        </button>
        <button mat-raised-button color="primary" class="save-btn"
                [disabled]="!supplierForm.valid || isLoading"
                (click)="saveSupplier()">
          <mat-icon>save</mat-icon>
          <span>حفظ المورد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Form Content -->
  <div class="form-content">
    <form [formGroup]="supplierForm" class="supplier-form">

      <!-- Validation Summary -->
      <div class="validation-summary" *ngIf="!supplierForm.valid && supplierForm.touched">
        <mat-card class="error-card">
          <mat-card-content>
            <div class="error-header">
              <mat-icon>error</mat-icon>
              <span>يرجى تصحيح الأخطاء التالية:</span>
            </div>
            <ul class="error-list">
              <li *ngIf="supplierForm.get('supplierCode')?.hasError('required') && supplierForm.get('supplierCode')?.touched">
                كود المورد مطلوب
              </li>
              <li *ngIf="supplierForm.get('nameAr')?.hasError('required') && supplierForm.get('nameAr')?.touched">
                اسم المورد بالعربية مطلوب
              </li>
              <li *ngIf="supplierForm.get('nameAr')?.hasError('minlength') && supplierForm.get('nameAr')?.touched">
                اسم المورد يجب أن يكون حرفين على الأقل
              </li>
              <li *ngIf="supplierForm.get('supplierTypeId')?.hasError('required') && supplierForm.get('supplierTypeId')?.touched">
                نوع المورد مطلوب
              </li>
              <li *ngIf="supplierForm.get('phone1')?.hasError('required') && supplierForm.get('phone1')?.touched">
                الهاتف الأول مطلوب
              </li>
              <li *ngIf="supplierForm.get('phone1')?.hasError('pattern') && supplierForm.get('phone1')?.touched">
                رقم الهاتف غير صحيح
              </li>
              <li *ngIf="supplierForm.get('deliveryDays')?.hasError('required') && supplierForm.get('deliveryDays')?.touched">
                مدة التوريد مطلوبة
              </li>
              <li *ngIf="supplierForm.get('deliveryDays')?.hasError('min') && supplierForm.get('deliveryDays')?.touched">
                مدة التوريد يجب أن تكون يوم واحد على الأقل
              </li>
              <li *ngIf="supplierForm.get('email')?.hasError('email') && supplierForm.get('email')?.touched">
                البريد الإلكتروني غير صحيح
              </li>
            </ul>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- البيانات الأساسية المطلوبة -->
      <mat-card class="form-card required-section">
        <mat-card-header>
          <mat-card-title class="required-title">
            <mat-icon>business</mat-icon>
            <span>البيانات الأساسية المطلوبة</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Supplier Code -->
            <div class="required-field">
              <mat-form-field appearance="outline">
                <mat-label>كود المورد *</mat-label>
                <input matInput formControlName="supplierCode" placeholder="سيتم إنشاؤه تلقائياً" readonly>
                <mat-icon matSuffix>tag</mat-icon>
              </mat-form-field>
            </div>

            <!-- Supplier Type -->
            <div class="field-with-button required-field">
              <mat-form-field appearance="outline" class="flex-field">
                <mat-label>نوع المورد *</mat-label>
                <mat-select formControlName="supplierTypeId" required>
                  <mat-option value="">اختر نوع المورد</mat-option>
                  <mat-option *ngFor="let type of supplierTypes" [value]="type.Id">
                    {{ type.NameAr }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>category</mat-icon>
                <mat-error *ngIf="supplierForm.get('supplierTypeId')?.hasError('required') && supplierForm.get('supplierTypeId')?.touched">
                  نوع المورد مطلوب
                </mat-error>
              </mat-form-field>
              <button mat-icon-button type="button" class="add-button"
                      matTooltip="إضافة نوع مورد جديد"
                      (click)="openAddSupplierTypeDialog()">
                <mat-icon>add</mat-icon>
              </button>
            </div>

            <!-- Arabic Name -->
            <div class="required-field">
              <mat-form-field appearance="outline">
                <mat-label>اسم المورد بالعربية *</mat-label>
                <input matInput formControlName="nameAr" placeholder="أدخل اسم المورد بالعربية" required
                       (input)="onArabicNameChange($event)">
                <mat-icon matSuffix>business</mat-icon>
                <mat-error *ngIf="supplierForm.get('nameAr')?.hasError('required') && supplierForm.get('nameAr')?.touched">
                  اسم المورد مطلوب
                </mat-error>
                <mat-error *ngIf="supplierForm.get('nameAr')?.hasError('minlength') && supplierForm.get('nameAr')?.touched">
                  اسم المورد يجب أن يكون حرفين على الأقل
                </mat-error>
              </mat-form-field>
            </div>

            <!-- English Name -->
            <div class="required-field">
              <mat-form-field appearance="outline">
                <mat-label>اسم المورد بالإنجليزية *</mat-label>
                <input matInput formControlName="nameEn" placeholder="Enter supplier name in English" required>
                <mat-icon matSuffix>business</mat-icon>
                <mat-error *ngIf="supplierForm.get('nameEn')?.hasError('required') && supplierForm.get('nameEn')?.touched">
                  اسم المورد بالإنجليزية مطلوب
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Phone 1 -->
            <div class="required-field">
              <mat-form-field appearance="outline">
                <mat-label>الهاتف الأول *</mat-label>
                <input matInput formControlName="phone1" placeholder="+201234567890" required>
                <mat-icon matSuffix>phone</mat-icon>
                <mat-error *ngIf="supplierForm.get('phone1')?.hasError('required') && supplierForm.get('phone1')?.touched">
                  رقم الهاتف مطلوب
                </mat-error>
                <mat-error *ngIf="supplierForm.get('phone1')?.hasError('pattern') && supplierForm.get('phone1')?.touched">
                  رقم الهاتف غير صحيح
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Delivery Days -->
            <div class="required-field">
              <mat-form-field appearance="outline">
                <mat-label>مدة التوريد (بالأيام) *</mat-label>
                <input matInput formControlName="deliveryDays" type="number" placeholder="7" required>
                <mat-icon matSuffix>local_shipping</mat-icon>
                <mat-error *ngIf="supplierForm.get('deliveryDays')?.hasError('required') && supplierForm.get('deliveryDays')?.touched">
                  مدة التوريد مطلوبة
                </mat-error>
                <mat-error *ngIf="supplierForm.get('deliveryDays')?.hasError('min') && supplierForm.get('deliveryDays')?.touched">
                  مدة التوريد يجب أن تكون يوم واحد على الأقل
                </mat-error>
              </mat-form-field>
            </div>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- معلومات الاتصال والتواصل -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>contact_phone</mat-icon>
            <span>معلومات الاتصال والتواصل</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Phone 2 -->
            <mat-form-field appearance="outline">
              <mat-label>الهاتف الثاني</mat-label>
              <input matInput formControlName="phone2" placeholder="+201234567890">
              <mat-icon matSuffix>phone</mat-icon>
            </mat-form-field>

            <!-- Email -->
            <mat-form-field appearance="outline">
              <mat-label>البريد الإلكتروني</mat-label>
              <input matInput formControlName="email" type="email" placeholder="<EMAIL>">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="supplierForm.get('email')?.hasError('email')">
                البريد الإلكتروني غير صحيح
              </mat-error>
            </mat-form-field>

            <!-- Website -->
            <mat-form-field appearance="outline">
              <mat-label>الموقع الإلكتروني</mat-label>
              <input matInput formControlName="website" placeholder="www.supplier.com">
              <mat-icon matSuffix>language</mat-icon>
            </mat-form-field>

            <!-- Contact Person Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم الشخص المسؤول</mat-label>
              <input matInput formControlName="contactPersonName" placeholder="أدخل اسم الشخص المسؤول">
              <mat-icon matSuffix>person</mat-icon>
            </mat-form-field>



          </div>
        </mat-card-content>
      </mat-card>

      <!-- معلومات الموقع والعنوان -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>location_on</mat-icon>
            <span>معلومات الموقع والعنوان</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Country -->
            <div class="field-with-button">
              <mat-form-field appearance="outline" class="flex-field">
                <mat-label>المحافظة</mat-label>
                <mat-select formControlName="countryId">
                  <mat-option value="">اختر المحافظة</mat-option>
                  <mat-option *ngFor="let country of countries" [value]="country.Id">
                    {{ country.NameAr }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>public</mat-icon>
              </mat-form-field>
              <button mat-icon-button type="button" class="add-button"
                      matTooltip="إضافة محافظة جديدة"
                      (click)="openAddCountryDialog()">
                <mat-icon>add</mat-icon>
              </button>
            </div>

            <!-- Area -->
            <div class="field-with-button">
              <mat-form-field appearance="outline" class="flex-field">
                <mat-label>المنطقة</mat-label>
                <mat-select formControlName="areaId">
                  <mat-option value="">اختر المنطقة</mat-option>
                  <mat-option *ngFor="let area of areas" [value]="area.Id">
                    {{ area.NameAr }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>location_city</mat-icon>
              </mat-form-field>
              <button mat-icon-button type="button" class="add-button"
                      matTooltip="إضافة منطقة جديدة"
                      (click)="openAddAreaDialog()">
                <mat-icon>add</mat-icon>
              </button>
            </div>

            <!-- Address -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>العنوان التفصيلي</mat-label>
              <textarea matInput formControlName="address" rows="2"
                        placeholder="أدخل العنوان التفصيلي للمورد"></textarea>
              <mat-icon matSuffix>home</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- المعلومات المالية -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_balance</mat-icon>
            <span>المعلومات المالية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Payment Terms -->
            <mat-form-field appearance="outline">
              <mat-label>شروط الدفع (بالأيام)</mat-label>
              <input matInput formControlName="paymentTerms" type="number" placeholder="30">
              <mat-icon matSuffix>schedule</mat-icon>
            </mat-form-field>

            <!-- Credit Limit -->
            <mat-form-field appearance="outline">
              <mat-label>حد الائتمان</mat-label>
              <input matInput formControlName="creditLimit" type="number" placeholder="0">
              <mat-icon matSuffix>credit_card</mat-icon>
            </mat-form-field>

            <!-- Opening Balance -->
            <mat-form-field appearance="outline">
              <mat-label>الرصيد الافتتاحي</mat-label>
              <input matInput formControlName="openingBalance" type="number" placeholder="0">
              <mat-icon matSuffix>account_balance_wallet</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- المعلومات القانونية -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>gavel</mat-icon>
            <span>المعلومات القانونية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Tax Number -->
            <mat-form-field appearance="outline">
              <mat-label>الرقم الضريبي</mat-label>
              <input matInput formControlName="taxNumber" placeholder="أدخل الرقم الضريبي">
              <mat-icon matSuffix>receipt</mat-icon>
            </mat-form-field>

            <!-- Commercial Register -->
            <mat-form-field appearance="outline">
              <mat-label>السجل التجاري</mat-label>
              <input matInput formControlName="commercialRegister" placeholder="أدخل رقم السجل التجاري">
              <mat-icon matSuffix>business_center</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- المعلومات المصرفية -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_balance</mat-icon>
            <span>المعلومات المصرفية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Bank Name -->
            <mat-form-field appearance="outline">
              <mat-label>اسم البنك</mat-label>
              <input matInput formControlName="bankName" placeholder="أدخل اسم البنك">
              <mat-icon matSuffix>account_balance</mat-icon>
            </mat-form-field>

            <!-- Bank Account Number -->
            <mat-form-field appearance="outline">
              <mat-label>رقم الحساب المصرفي</mat-label>
              <input matInput formControlName="bankAccountNumber" placeholder="أدخل رقم الحساب">
              <mat-icon matSuffix>credit_card</mat-icon>
            </mat-form-field>

            <!-- IBAN -->
            <mat-form-field appearance="outline">
              <mat-label>رقم الآيبان (IBAN)</mat-label>
              <input matInput formControlName="iban" placeholder="أدخل رقم الآيبان">
              <mat-icon matSuffix>account_balance</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- معلومات إضافية -->
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>info</mat-icon>
            <span>معلومات إضافية</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-grid">

            <!-- Rating -->
            <mat-form-field appearance="outline">
              <mat-label>التقييم</mat-label>
              <input matInput formControlName="rating" type="number" min="1" max="5" placeholder="من 1 إلى 5">
              <mat-icon matSuffix>star</mat-icon>
            </mat-form-field>

            <!-- Is Active -->
            <div class="checkbox-field">
              <mat-checkbox formControlName="isActive">
                مورد نشط
              </mat-checkbox>
            </div>

            <!-- Notes -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>ملاحظات</mat-label>
              <textarea matInput formControlName="notes" rows="3"
                        placeholder="أدخل أي ملاحظات إضافية عن المورد"></textarea>
              <mat-icon matSuffix>note</mat-icon>
            </mat-form-field>

          </div>
        </mat-card-content>
      </mat-card>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button mat-button type="button" class="cancel-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
          <span>العودة</span>
        </button>
        <button mat-raised-button color="primary" type="submit" class="save-btn"
                [disabled]="!supplierForm.valid || isLoading" (click)="saveSupplier()">
          <mat-icon>save</mat-icon>
          <span>حفظ المورد</span>
        </button>
      </div>

    </form>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري حفظ بيانات المورد...</p>
  </div>

</div>
