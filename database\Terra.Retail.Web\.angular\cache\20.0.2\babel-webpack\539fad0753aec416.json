{"ast": null, "code": "import _asyncToGenerator from \"X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/table\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"@angular/material/sort\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/form-field\";\nimport * as i15 from \"@angular/material/select\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nimport * as i17 from \"@angular/material/menu\";\nimport * as i18 from \"@angular/material/tooltip\";\nconst _c0 = () => [10, 25, 50, 100];\nfunction CustomersComponent_mat_option_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r1.nameAr, \" \");\n  }\n}\nfunction CustomersComponent_mat_option_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", area_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r2.nameAr, \" \");\n  }\n}\nfunction CustomersComponent_mat_option_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", branch_r3.nameAr, \" \");\n  }\n}\nfunction CustomersComponent_th_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 53);\n    i0.ɵɵtext(1, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r4.customerCode);\n  }\n}\nfunction CustomersComponent_th_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 53);\n    i0.ɵɵtext(1, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_121_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(customer_r5.nameEn);\n  }\n}\nfunction CustomersComponent_td_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"div\", 56)(2, \"span\", 57);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersComponent_td_121_span_4_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(customer_r5.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", customer_r5.nameEn);\n  }\n}\nfunction CustomersComponent_th_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"type-\" + customer_r6.customerTypeId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", customer_r6.customerTypeName, \" \");\n  }\n}\nfunction CustomersComponent_th_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r7.phoneNumber);\n  }\n}\nfunction CustomersComponent_th_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0646\\u0637\\u0642\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r8.areaName);\n  }\n}\nfunction CustomersComponent_th_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 53);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"negative\", customer_r9.currentBalance < 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(3, 3, customer_r9.currentBalance, \"EGP\", \"symbol\", \"1.2-2\"), \" \");\n  }\n}\nfunction CustomersComponent_th_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"span\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", customer_r10.isActive)(\"inactive\", !customer_r10.isActive);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", customer_r10.isActive ? \"\\u0646\\u0634\\u0637\" : \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\", \" \");\n  }\n}\nfunction CustomersComponent_th_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 60);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_td_139_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 54)(1, \"div\", 66)(2, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_td_139_Template_button_click_2_listener() {\n      const customer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.viewCustomer(customer_r12));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_td_139_Template_button_click_5_listener() {\n      const customer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.editCustomer(customer_r12));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_td_139_Template_button_click_8_listener() {\n      const customer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.viewFinancials(customer_r12));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"account_balance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 70)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-menu\", null, 0)(16, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_td_139_Template_button_click_16_listener() {\n      const customer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleCustomerStatus(customer_r12));\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_td_139_Template_button_click_20_listener() {\n      const customer_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deleteCustomer(customer_r12));\n    });\n    i0.ɵɵelementStart(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u062D\\u0630\\u0641 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const customer_r12 = ctx.$implicit;\n    const actionMenu_r14 = i0.ɵɵreference(15);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", actionMenu_r14);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(customer_r12.isActive ? \"block\" : \"check_circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", customer_r12.isActive ? \"\\u0625\\u0644\\u063A\\u0627\\u0621 \\u0627\\u0644\\u062A\\u0641\\u0639\\u064A\\u0644\" : \"\\u062A\\u0641\\u0639\\u064A\\u0644\", \" \");\n  }\n}\nfunction CustomersComponent_tr_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 72);\n  }\n}\nfunction CustomersComponent_tr_141_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 73);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_tr_141_Template_tr_click_0_listener() {\n      const row_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.viewCustomer(row_r16));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_142_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"people_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0639\\u0645\\u0644\\u0627\\u0621\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u0639\\u0645\\u0644\\u0627\\u0621. \\u0642\\u0645 \\u0628\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F \\u0644\\u0644\\u0628\\u062F\\u0621.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_142_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.openAddCustomerDialog());\n    });\n    i0.ɵɵtext(8, \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomersComponent_div_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"mat-spinner\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CustomersComponent = /*#__PURE__*/(() => {\n  class CustomersComponent {\n    http;\n    dialog;\n    snackBar;\n    paginator;\n    sort;\n    // Table Configuration\n    displayedColumns = ['customerCode', 'fullName', 'customerTypeName', 'phoneNumber', 'areaName', 'currentBalance', 'isActive', 'actions'];\n    dataSource = new MatTableDataSource([]);\n    // Data Arrays\n    customers = [];\n    customerTypes = [];\n    areas = [];\n    branches = [];\n    // Filter Properties\n    searchTerm = '';\n    selectedCustomerType = '';\n    selectedArea = '';\n    selectedBranch = '';\n    // Statistics\n    totalCustomers = 0;\n    activeCustomers = 0;\n    totalBalance = 0;\n    totalCreditLimit = 0;\n    // Loading State\n    isLoading = false;\n    apiUrl = environment.apiUrl;\n    constructor(http, dialog, snackBar) {\n      this.http = http;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n    }\n    ngOnInit() {\n      this.loadInitialData();\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n      // Custom filter predicate\n      this.dataSource.filterPredicate = (data, filter) => {\n        const searchStr = filter.toLowerCase();\n        return data.fullName.toLowerCase().includes(searchStr) || data.customerCode.toLowerCase().includes(searchStr) || data.phoneNumber.toLowerCase().includes(searchStr) || (data.email ? data.email.toLowerCase().includes(searchStr) : false);\n      };\n    }\n    loadInitialData() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isLoading = true;\n        try {\n          // Load all required data in parallel\n          yield Promise.all([_this.loadCustomers(), _this.loadCustomerTypes(), _this.loadAreas(), _this.loadBranches()]);\n          _this.calculateStatistics();\n        } catch (error) {\n          console.error('Error loading initial data:', error);\n          _this.showMessage('حدث خطأ في تحميل البيانات', 'error');\n        } finally {\n          _this.isLoading = false;\n        }\n      })();\n    }\n    loadCustomers() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this2.http.get(`${_this2.apiUrl}/customers`).toPromise();\n          if (response && Array.isArray(response)) {\n            _this2.customers = response.map(customer => ({\n              ...customer,\n              fullName: customer.nameAr || customer.fullName,\n              phoneNumber: customer.phone1 || customer.phoneNumber,\n              createdAt: new Date(customer.createdAt)\n            }));\n          } else {\n            // Fallback data if API fails\n            _this2.customers = _this2.getFallbackCustomers();\n          }\n          _this2.dataSource.data = _this2.customers;\n          _this2.totalCustomers = _this2.customers.length;\n        } catch (error) {\n          console.error('Error loading customers:', error);\n          _this2.customers = _this2.getFallbackCustomers();\n          _this2.dataSource.data = _this2.customers;\n          _this2.showMessage('تم تحميل بيانات تجريبية للعملاء', 'warning');\n        }\n      })();\n    }\n    loadCustomerTypes() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this3.http.get(`${_this3.apiUrl}/simple/customer-types`).toPromise();\n          if (response && response.customerTypes) {\n            _this3.customerTypes = response.customerTypes;\n          } else {\n            _this3.customerTypes = _this3.getFallbackCustomerTypes();\n          }\n        } catch (error) {\n          console.error('Error loading customer types:', error);\n          _this3.customerTypes = _this3.getFallbackCustomerTypes();\n        }\n      })();\n    }\n    loadAreas() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this4.http.get(`${_this4.apiUrl}/simple/areas-db`).toPromise();\n          if (response && Array.isArray(response)) {\n            _this4.areas = response;\n          } else {\n            _this4.areas = _this4.getFallbackAreas();\n          }\n        } catch (error) {\n          console.error('Error loading areas:', error);\n          _this4.areas = _this4.getFallbackAreas();\n        }\n      })();\n    }\n    loadBranches() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this5.http.get(`${_this5.apiUrl}/simple/branches-db`).toPromise();\n          if (response && response.branches) {\n            _this5.branches = response.branches;\n          } else {\n            _this5.branches = _this5.getFallbackBranches();\n          }\n        } catch (error) {\n          console.error('Error loading branches:', error);\n          _this5.branches = _this5.getFallbackBranches();\n        }\n      })();\n    }\n    calculateStatistics() {\n      this.totalCustomers = this.customers.length;\n      this.activeCustomers = this.customers.filter(c => c.isActive).length;\n      this.totalBalance = this.customers.reduce((sum, c) => sum + (c.currentBalance || 0), 0);\n      this.totalCreditLimit = this.customers.reduce((sum, c) => sum + (c.creditLimit || 0), 0);\n    }\n    onSearch() {\n      this.dataSource.filter = this.searchTerm.trim().toLowerCase();\n    }\n    onFilterChange() {\n      let filteredData = this.customers;\n      if (this.selectedCustomerType) {\n        filteredData = filteredData.filter(c => c.customerTypeId.toString() === this.selectedCustomerType);\n      }\n      if (this.selectedArea) {\n        filteredData = filteredData.filter(c => c.areaId?.toString() === this.selectedArea);\n      }\n      if (this.selectedBranch) {\n        filteredData = filteredData.filter(c => c.branchId?.toString() === this.selectedBranch);\n      }\n      this.dataSource.data = filteredData;\n    }\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedCustomerType = '';\n      this.selectedArea = '';\n      this.selectedBranch = '';\n      this.dataSource.filter = '';\n      this.dataSource.data = this.customers;\n    }\n    refreshCustomers() {\n      this.loadCustomers();\n      this.showMessage('تم تحديث بيانات العملاء', 'success');\n    }\n    openAddCustomerDialog() {\n      // TODO: Implement add customer dialog\n      this.showMessage('سيتم إضافة نافذة إضافة عميل قريباً', 'info');\n    }\n    viewCustomer(customer) {\n      // TODO: Implement view customer details\n      this.showMessage(`عرض تفاصيل العميل: ${customer.fullName}`, 'info');\n    }\n    editCustomer(customer) {\n      // TODO: Implement edit customer dialog\n      this.showMessage(`تعديل العميل: ${customer.fullName}`, 'info');\n    }\n    viewFinancials(customer) {\n      // TODO: Implement financial details view\n      this.showMessage(`عرض الحساب المالي للعميل: ${customer.fullName}`, 'info');\n    }\n    toggleCustomerStatus(customer) {\n      customer.isActive = !customer.isActive;\n      this.showMessage(`تم ${customer.isActive ? 'تفعيل' : 'إلغاء تفعيل'} العميل: ${customer.fullName}`, 'success');\n      this.calculateStatistics();\n    }\n    deleteCustomer(customer) {\n      if (confirm(`هل أنت متأكد من حذف العميل: ${customer.fullName}؟`)) {\n        const index = this.customers.findIndex(c => c.id === customer.id);\n        if (index > -1) {\n          this.customers.splice(index, 1);\n          this.dataSource.data = this.customers;\n          this.calculateStatistics();\n          this.showMessage(`تم حذف العميل: ${customer.fullName}`, 'success');\n        }\n      }\n    }\n    exportCustomers() {\n      // TODO: Implement export functionality\n      this.showMessage('سيتم إضافة وظيفة التصدير قريباً', 'info');\n    }\n    showMessage(message, type = 'info') {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        panelClass: [`snackbar-${type}`],\n        horizontalPosition: 'end',\n        verticalPosition: 'top'\n      });\n    }\n    // Fallback data methods\n    getFallbackCustomers() {\n      return [{\n        id: 1,\n        customerCode: 'CUS001',\n        fullName: 'شركة الأهرام للتجارة',\n        nameAr: 'شركة الأهرام للتجارة',\n        nameEn: 'Ahram Trading Company',\n        customerTypeId: 3,\n        customerTypeName: 'عميل مؤسسي',\n        phoneNumber: '+201111111111',\n        phone1: '+201111111111',\n        phone2: '+201111111112',\n        email: '<EMAIL>',\n        address: 'شارع التحرير، وسط البلد',\n        areaId: 1,\n        areaName: 'القاهرة',\n        branchId: 1,\n        branchName: 'الفرع الرئيسي',\n        priceCategoryId: 3,\n        priceCategoryName: 'سعر كبار العملاء',\n        creditLimit: 100000,\n        openingBalance: 25000,\n        currentBalance: 25000,\n        isActive: true,\n        createdAt: new Date()\n      }\n      // Add more fallback customers as needed\n      ];\n    }\n    getFallbackCustomerTypes() {\n      return [{\n        id: 1,\n        nameAr: 'عميل تجزئة',\n        nameEn: 'Retail Customer',\n        defaultDiscountPercentage: 0,\n        defaultCreditLimit: 5000\n      }, {\n        id: 2,\n        nameAr: 'عميل جملة',\n        nameEn: 'Wholesale Customer',\n        defaultDiscountPercentage: 10,\n        defaultCreditLimit: 50000\n      }, {\n        id: 3,\n        nameAr: 'عميل مؤسسي',\n        nameEn: 'Corporate Customer',\n        defaultDiscountPercentage: 15,\n        defaultCreditLimit: 100000\n      }];\n    }\n    getFallbackAreas() {\n      return [{\n        id: 1,\n        nameAr: 'القاهرة',\n        nameEn: 'Cairo',\n        code: 'CAI'\n      }, {\n        id: 2,\n        nameAr: 'الجيزة',\n        nameEn: 'Giza',\n        code: 'GIZ'\n      }, {\n        id: 3,\n        nameAr: 'الإسكندرية',\n        nameEn: 'Alexandria',\n        code: 'ALX'\n      }];\n    }\n    getFallbackBranches() {\n      return [{\n        id: 1,\n        code: 'BR001',\n        nameAr: 'الفرع الرئيسي',\n        nameEn: 'Main Branch',\n        address: 'القاهرة',\n        phone: '+201234567890',\n        isMainBranch: true\n      }, {\n        id: 2,\n        code: 'BR002',\n        nameAr: 'فرع الإسكندرية',\n        nameEn: 'Alexandria Branch',\n        address: 'الإسكندرية',\n        phone: '+203456789012',\n        isMainBranch: false\n      }];\n    }\n    static ɵfac = function CustomersComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomersComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomersComponent,\n      selectors: [[\"app-customers\"]],\n      viewQuery: function CustomersComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      standalone: false,\n      decls: 145,\n      vars: 28,\n      consts: [[\"actionMenu\", \"matMenu\"], [1, \"customers-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", \"routerLink\", \"/dashboard\", 1, \"back-btn\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", 1, \"add-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"export-btn\", 3, \"click\"], [1, \"content-area\"], [1, \"stats-grid\"], [1, \"stat-card\", \"customers-card\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-card\", \"active-card\"], [1, \"stat-card\", \"balance-card\"], [1, \"stat-card\", \"credit-card\"], [1, \"filters-card\"], [1, \"filters-row\"], [1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\\u060C \\u0627\\u0644\\u0643\\u0648\\u062F\\u060C \\u0623\\u0648 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [1, \"filter-field\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"table-card\"], [1, \"table-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\", 3, \"click\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"customers-table\", 3, \"dataSource\"], [\"matColumnDef\", \"customerCode\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"fullName\"], [\"matColumnDef\", \"customerTypeName\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"phoneNumber\"], [\"matColumnDef\", \"areaName\"], [\"matColumnDef\", \"currentBalance\"], [\"matColumnDef\", \"isActive\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"clickable-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"pageSizeOptions\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [3, \"value\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"customer-code\"], [1, \"customer-info\"], [1, \"customer-name-ar\"], [\"class\", \"customer-name-en\", 4, \"ngIf\"], [1, \"customer-name-en\"], [\"mat-header-cell\", \"\"], [1, \"customer-type-badge\"], [1, \"phone-number\"], [1, \"area-name\"], [1, \"balance\"], [1, \"status-badge\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u0627\\u0644\\u062D\\u0633\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u0627\\u0644\\u0645\\u0632\\u064A\\u062F\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"clickable-row\", 3, \"click\"], [1, \"no-data\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function CustomersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"button\", 5)(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"h1\", 7);\n          i0.ɵɵtext(9, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 8);\n          i0.ɵɵtext(11, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0648\\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_13_listener() {\n            return ctx.openAddCustomerDialog();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_17_listener() {\n            return ctx.exportCustomers();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20, \" \\u062A\\u0635\\u062F\\u064A\\u0631 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 12)(22, \"div\", 13)(23, \"mat-card\", 14)(24, \"mat-card-content\")(25, \"div\", 15)(26, \"div\", 16)(27, \"mat-icon\");\n          i0.ɵɵtext(28, \"people\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 17)(30, \"h3\");\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\");\n          i0.ɵɵtext(33, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"mat-card\", 18)(35, \"mat-card-content\")(36, \"div\", 15)(37, \"div\", 16)(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"person_check\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 17)(41, \"h3\");\n          i0.ɵɵtext(42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(45, \"mat-card\", 19)(46, \"mat-card-content\")(47, \"div\", 15)(48, \"div\", 16)(49, \"mat-icon\");\n          i0.ɵɵtext(50, \"account_balance_wallet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 17)(52, \"h3\");\n          i0.ɵɵtext(53);\n          i0.ɵɵpipe(54, \"currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(57, \"mat-card\", 20)(58, \"mat-card-content\")(59, \"div\", 15)(60, \"div\", 16)(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"credit_card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 17)(64, \"h3\");\n          i0.ɵɵtext(65);\n          i0.ɵɵpipe(66, \"currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"p\");\n          i0.ɵɵtext(68, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u062D\\u062F\\u0648\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(69, \"mat-card\", 21)(70, \"mat-card-content\")(71, \"div\", 22)(72, \"mat-form-field\", 23)(73, \"mat-label\");\n          i0.ɵɵtext(74, \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"input\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersComponent_Template_input_ngModelChange_75_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function CustomersComponent_Template_input_input_75_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"mat-icon\", 25);\n          i0.ɵɵtext(77, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"mat-form-field\", 26)(79, \"mat-label\");\n          i0.ɵɵtext(80, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"mat-select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersComponent_Template_mat_select_ngModelChange_81_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCustomerType, $event) || (ctx.selectedCustomerType = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersComponent_Template_mat_select_selectionChange_81_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(82, \"mat-option\", 28);\n          i0.ɵɵtext(83, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(84, CustomersComponent_mat_option_84_Template, 2, 2, \"mat-option\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 26)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"\\u0627\\u0644\\u0645\\u0646\\u0637\\u0642\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersComponent_Template_mat_select_ngModelChange_88_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedArea, $event) || (ctx.selectedArea = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersComponent_Template_mat_select_selectionChange_88_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(89, \"mat-option\", 28);\n          i0.ɵɵtext(90, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u0627\\u0637\\u0642\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(91, CustomersComponent_mat_option_91_Template, 2, 2, \"mat-option\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"mat-form-field\", 26)(93, \"mat-label\");\n          i0.ɵɵtext(94, \"\\u0627\\u0644\\u0641\\u0631\\u0639\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomersComponent_Template_mat_select_ngModelChange_95_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBranch, $event) || (ctx.selectedBranch = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function CustomersComponent_Template_mat_select_selectionChange_95_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(96, \"mat-option\", 28);\n          i0.ɵɵtext(97, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0648\\u0639\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(98, CustomersComponent_mat_option_98_Template, 2, 2, \"mat-option\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(99, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_99_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(100, \"mat-icon\");\n          i0.ɵɵtext(101, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(102, \" \\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0644\\u0627\\u062A\\u0631 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(103, \"mat-card\", 31)(104, \"mat-card-header\")(105, \"mat-card-title\")(106, \"mat-icon\");\n          i0.ɵɵtext(107, \"people\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(108, \" \\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 32)(110, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_110_listener() {\n            return ctx.refreshCustomers();\n          });\n          i0.ɵɵelementStart(111, \"mat-icon\");\n          i0.ɵɵtext(112, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(113, \"mat-card-content\")(114, \"div\", 34)(115, \"table\", 35);\n          i0.ɵɵelementContainerStart(116, 36);\n          i0.ɵɵtemplate(117, CustomersComponent_th_117_Template, 2, 0, \"th\", 37)(118, CustomersComponent_td_118_Template, 3, 1, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(119, 39);\n          i0.ɵɵtemplate(120, CustomersComponent_th_120_Template, 2, 0, \"th\", 37)(121, CustomersComponent_td_121_Template, 5, 2, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(122, 40);\n          i0.ɵɵtemplate(123, CustomersComponent_th_123_Template, 2, 0, \"th\", 41)(124, CustomersComponent_td_124_Template, 3, 3, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(125, 42);\n          i0.ɵɵtemplate(126, CustomersComponent_th_126_Template, 2, 0, \"th\", 41)(127, CustomersComponent_td_127_Template, 3, 1, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(128, 43);\n          i0.ɵɵtemplate(129, CustomersComponent_th_129_Template, 2, 0, \"th\", 41)(130, CustomersComponent_td_130_Template, 3, 1, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(131, 44);\n          i0.ɵɵtemplate(132, CustomersComponent_th_132_Template, 2, 0, \"th\", 37)(133, CustomersComponent_td_133_Template, 4, 8, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(134, 45);\n          i0.ɵɵtemplate(135, CustomersComponent_th_135_Template, 2, 0, \"th\", 41)(136, CustomersComponent_td_136_Template, 3, 5, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(137, 46);\n          i0.ɵɵtemplate(138, CustomersComponent_th_138_Template, 2, 0, \"th\", 41)(139, CustomersComponent_td_139_Template, 24, 3, \"td\", 38);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(140, CustomersComponent_tr_140_Template, 1, 0, \"tr\", 47)(141, CustomersComponent_tr_141_Template, 1, 0, \"tr\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(142, CustomersComponent_div_142_Template, 9, 0, \"div\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(143, \"mat-paginator\", 50);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(144, CustomersComponent_div_144_Template, 4, 0, \"div\", 51);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(31);\n          i0.ɵɵtextInterpolate(ctx.totalCustomers);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.activeCustomers);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(54, 17, ctx.totalBalance, \"EGP\", \"symbol\", \"1.2-2\"));\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(66, 22, ctx.totalCreditLimit, \"EGP\", \"symbol\", \"1.2-2\"));\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCustomerType);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customerTypes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedArea);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.areas);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBranch);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.branches);\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dataSource.data.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(27, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.RouterLink, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatTable, i10.MatHeaderCellDef, i10.MatHeaderRowDef, i10.MatColumnDef, i10.MatCellDef, i10.MatRowDef, i10.MatHeaderCell, i10.MatCell, i10.MatHeaderRow, i10.MatRow, i11.MatPaginator, i12.MatSort, i12.MatSortHeader, i13.MatInput, i14.MatFormField, i14.MatLabel, i14.MatSuffix, i15.MatSelect, i15.MatOption, i16.MatProgressSpinner, i17.MatMenu, i17.MatMenuItem, i17.MatMenuTrigger, i18.MatTooltip, i4.CurrencyPipe],\n      styles: [\"\\n\\n.customers-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n  position: relative;\\n}\\n.customers-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);\\n  color: white;\\n  padding: 40px 40px 60px;\\n  margin: -40px -40px 40px;\\n  border-radius: 0 0 30px 30px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"dots\\\" width=\\\"20\\\" height=\\\"20\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"10\\\" cy=\\\"10\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23dots)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateX(-5px);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  margin: 0 0 8px 0;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\\n  background: linear-gradient(45deg, #ffffff, #e3f2fd);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 300;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  align-items: center;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #4CAF50, #45a049);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  border-radius: 25px;\\n  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  transition: all 0.3s ease;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-2px);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.content-area[_ngcontent-%COMP%] {\\n  padding: 0 40px 40px;\\n  position: relative;\\n  z-index: 1;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.stat-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.stat-card.customers-card[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, #2196F3, #21CBF3);\\n}\\n.stat-card.active-card[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, #4CAF50, #45a049);\\n}\\n.stat-card.balance-card[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, #FF9800, #F57C00);\\n}\\n.stat-card.credit-card[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, #9C27B0, #673AB7);\\n}\\n.stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 25px !important;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n  color: #667eea;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0 0 5px 0;\\n  color: #2c3e50;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #7f8c8d;\\n  margin: 0;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 30px;\\n}\\n.filters-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 25px !important;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr auto;\\n  gap: 20px;\\n  align-items: end;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  min-width: 180px;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  border-radius: 12px;\\n  border: 1px solid #e0e0e0;\\n  color: #666;\\n  transition: all 0.3s ease;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5f5f5;\\n  border-color: #ccc;\\n}\\n.filters-card[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.table-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa, #e9ecef);\\n  padding: 20px 25px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  margin: 0;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.table-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-height: 600px;\\n}\\n\\n.customers-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: white;\\n}\\n.customers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  padding: 16px 12px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n.customers-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 16px 12px;\\n  border-bottom: 1px solid #f0f0f0;\\n  font-size: 0.9rem;\\n}\\n.customers-table[_ngcontent-%COMP%]   .clickable-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.customers-table[_ngcontent-%COMP%]   .clickable-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 600;\\n  color: #667eea;\\n  background: rgba(102, 126, 234, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 6px;\\n  font-size: 0.85rem;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-info[_ngcontent-%COMP%]   .customer-name-ar[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  margin-bottom: 2px;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-info[_ngcontent-%COMP%]   .customer-name-en[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.8rem;\\n  color: #7f8c8d;\\n  font-style: italic;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-align: center;\\n  display: inline-block;\\n  min-width: 80px;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge.type-1[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge.type-2[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  color: #7b1fa2;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge.type-3[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #388e3c;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge.type-4[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #f57c00;\\n}\\n.customers-table[_ngcontent-%COMP%]   .customer-type-badge.type-5[_ngcontent-%COMP%] {\\n  background: #fce4ec;\\n  color: #c2185b;\\n}\\n.customers-table[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  color: #2c3e50;\\n  direction: ltr;\\n  text-align: left;\\n}\\n.customers-table[_ngcontent-%COMP%]   .area-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 500;\\n}\\n.customers-table[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4caf50;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.customers-table[_ngcontent-%COMP%]   .balance.negative[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.customers-table[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-align: center;\\n  display: inline-block;\\n  min-width: 60px;\\n}\\n.customers-table[_ngcontent-%COMP%]   .status-badge.active[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #388e3c;\\n}\\n.customers-table[_ngcontent-%COMP%]   .status-badge.inactive[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  color: #d32f2f;\\n}\\n.customers-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  align-items: center;\\n}\\n.customers-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.customers-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f5f5f5;\\n  transform: scale(1.1);\\n}\\n.customers-table[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n\\n\\n.no-data[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #7f8c8d;\\n}\\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  color: #bdc3c7;\\n  margin-bottom: 20px;\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin: 0 0 10px 0;\\n  color: #2c3e50;\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0 0 30px 0;\\n  max-width: 400px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.no-data[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 25px;\\n  padding: 12px 30px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n  color: white;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .content-area[_ngcontent-%COMP%] {\\n    padding: 0 20px 20px;\\n  }\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: 30px 20px 40px;\\n    margin: -30px -20px 30px;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem !important;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 20px;\\n  }\\n  .filters-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n  .filters-row[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%], \\n   .filters-row[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .customers-container[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: 20px 15px 30px;\\n    margin: -20px -15px 20px;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem !important;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%], \\n   .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .customers-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .customers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .customers-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 12px 8px;\\n  }\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 2px;\\n  }\\n  .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return CustomersComponent;\n})();", "map": {"version": 3, "names": ["MatTableDataSource", "MatPaginator", "MatSort", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "nameAr", "area_r2", "branch_r3", "ɵɵtextInterpolate", "customer_r4", "customerCode", "customer_r5", "nameEn", "ɵɵtemplate", "CustomersComponent_td_121_span_4_Template", "fullName", "ɵɵclassMap", "customer_r6", "customerTypeId", "customerTypeName", "customer_r7", "phoneNumber", "customer_r8", "areaName", "ɵɵclassProp", "customer_r9", "currentBalance", "ɵɵpipeBind4", "customer_r10", "isActive", "ɵɵlistener", "CustomersComponent_td_139_Template_button_click_2_listener", "customer_r12", "ɵɵrestoreView", "_r11", "$implicit", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "viewCustomer", "CustomersComponent_td_139_Template_button_click_5_listener", "editCustomer", "CustomersComponent_td_139_Template_button_click_8_listener", "viewFinancials", "CustomersComponent_td_139_Template_button_click_16_listener", "toggleCustomerStatus", "CustomersComponent_td_139_Template_button_click_20_listener", "deleteCustomer", "actionMenu_r14", "ɵɵelement", "CustomersComponent_tr_141_Template_tr_click_0_listener", "row_r16", "_r15", "CustomersComponent_div_142_Template_button_click_7_listener", "_r17", "openAddCustomerDialog", "CustomersComponent", "http", "dialog", "snackBar", "paginator", "sort", "displayedColumns", "dataSource", "customers", "customerTypes", "areas", "branches", "searchTerm", "selectedCustomerType", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "totalCustomers", "activeCustomers", "totalBalance", "totalCreditLimit", "isLoading", "apiUrl", "constructor", "ngOnInit", "loadInitialData", "ngAfterViewInit", "filterPredicate", "data", "filter", "searchStr", "toLowerCase", "includes", "email", "_this", "_asyncToGenerator", "Promise", "all", "loadCustomers", "loadCustomerTypes", "loadAreas", "loadBranches", "calculateStatistics", "error", "console", "showMessage", "_this2", "response", "get", "to<PERSON>romise", "Array", "isArray", "map", "customer", "phone1", "createdAt", "Date", "getFallbackCustomers", "length", "_this3", "getFallbackCustomerTypes", "_this4", "getFallback<PERSON><PERSON>s", "_this5", "getFallbackBranches", "c", "reduce", "sum", "creditLimit", "onSearch", "trim", "onFilterChange", "filteredData", "toString", "areaId", "branchId", "clearFilters", "refreshCustomers", "confirm", "index", "findIndex", "splice", "exportCustomers", "message", "type", "open", "duration", "panelClass", "horizontalPosition", "verticalPosition", "phone2", "address", "branchName", "priceCategoryId", "priceCategoryName", "openingBalance", "defaultDiscountPercentage", "defaultCreditLimit", "code", "phone", "isMainBranch", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "MatDialog", "i3", "MatSnackBar", "selectors", "viewQuery", "CustomersComponent_Query", "rf", "ctx", "CustomersComponent_Template_button_click_13_listener", "CustomersComponent_Template_button_click_17_listener", "ɵɵtwoWayListener", "CustomersComponent_Template_input_ngModelChange_75_listener", "$event", "ɵɵtwoWayBindingSet", "CustomersComponent_Template_input_input_75_listener", "CustomersComponent_Template_mat_select_ngModelChange_81_listener", "CustomersComponent_Template_mat_select_selectionC<PERSON>e_81_listener", "CustomersComponent_mat_option_84_Template", "CustomersComponent_Template_mat_select_ngModelChange_88_listener", "CustomersComponent_Template_mat_select_selectionChange_88_listener", "CustomersComponent_mat_option_91_Template", "CustomersComponent_Template_mat_select_ngModelChange_95_listener", "CustomersComponent_Template_mat_select_selectionChange_95_listener", "CustomersComponent_mat_option_98_Template", "CustomersComponent_Template_button_click_99_listener", "CustomersComponent_Template_button_click_110_listener", "ɵɵelementContainerStart", "CustomersComponent_th_117_Template", "CustomersComponent_td_118_Template", "CustomersComponent_th_120_Template", "CustomersComponent_td_121_Template", "CustomersComponent_th_123_Template", "CustomersComponent_td_124_Template", "CustomersComponent_th_126_Template", "CustomersComponent_td_127_Template", "CustomersComponent_th_129_Template", "CustomersComponent_td_130_Template", "CustomersComponent_th_132_Template", "CustomersComponent_td_133_Template", "CustomersComponent_th_135_Template", "CustomersComponent_td_136_Template", "CustomersComponent_th_138_Template", "CustomersComponent_td_139_Template", "CustomersComponent_tr_140_Template", "CustomersComponent_tr_141_Template", "CustomersComponent_div_142_Template", "CustomersComponent_div_144_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\customers\\customers.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\customers\\customers.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\n\nexport interface Customer {\n  id: number;\n  customerCode: string;\n  fullName: string;\n  nameAr: string;\n  nameEn?: string;\n  customerTypeId: number;\n  customerTypeName: string;\n  phoneNumber: string;\n  phone1: string;\n  phone2?: string;\n  email?: string;\n  address?: string;\n  areaId?: number;\n  areaName?: string;\n  branchId?: number;\n  branchName?: string;\n  priceCategoryId?: number;\n  priceCategoryName?: string;\n  creditLimit: number;\n  openingBalance: number;\n  currentBalance: number;\n  isActive: boolean;\n  createdAt: Date;\n}\n\nexport interface CustomerType {\n  id: number;\n  nameAr: string;\n  nameEn?: string;\n  defaultDiscountPercentage: number;\n  defaultCreditLimit: number;\n}\n\nexport interface Area {\n  id: number;\n  nameAr: string;\n  nameEn?: string;\n  code: string;\n}\n\nexport interface Branch {\n  id: number;\n  code: string;\n  nameAr: string;\n  nameEn?: string;\n  address?: string;\n  phone?: string;\n  isMainBranch: boolean;\n}\n\n@Component({\n  selector: 'app-customers',\n  templateUrl: './customers.component.html',\n  styleUrls: ['./customers.component.scss'],\n  standalone: false\n})\nexport class CustomersComponent implements OnInit {\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  // Table Configuration\n  displayedColumns: string[] = [\n    'customerCode',\n    'fullName', \n    'customerTypeName',\n    'phoneNumber',\n    'areaName',\n    'currentBalance',\n    'isActive',\n    'actions'\n  ];\n  \n  dataSource = new MatTableDataSource<Customer>([]);\n  \n  // Data Arrays\n  customers: Customer[] = [];\n  customerTypes: CustomerType[] = [];\n  areas: Area[] = [];\n  branches: Branch[] = [];\n  \n  // Filter Properties\n  searchTerm: string = '';\n  selectedCustomerType: string = '';\n  selectedArea: string = '';\n  selectedBranch: string = '';\n  \n  // Statistics\n  totalCustomers: number = 0;\n  activeCustomers: number = 0;\n  totalBalance: number = 0;\n  totalCreditLimit: number = 0;\n  \n  // Loading State\n  isLoading: boolean = false;\n  \n  private apiUrl = environment.apiUrl;\n\n  constructor(\n    private http: HttpClient,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.loadInitialData();\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n    \n    // Custom filter predicate\n    this.dataSource.filterPredicate = (data: Customer, filter: string): boolean => {\n      const searchStr = filter.toLowerCase();\n      return data.fullName.toLowerCase().includes(searchStr) ||\n             data.customerCode.toLowerCase().includes(searchStr) ||\n             data.phoneNumber.toLowerCase().includes(searchStr) ||\n             (data.email ? data.email.toLowerCase().includes(searchStr) : false);\n    };\n  }\n\n  async loadInitialData(): Promise<void> {\n    this.isLoading = true;\n    \n    try {\n      // Load all required data in parallel\n      await Promise.all([\n        this.loadCustomers(),\n        this.loadCustomerTypes(),\n        this.loadAreas(),\n        this.loadBranches()\n      ]);\n      \n      this.calculateStatistics();\n    } catch (error) {\n      console.error('Error loading initial data:', error);\n      this.showMessage('حدث خطأ في تحميل البيانات', 'error');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  async loadCustomers(): Promise<void> {\n    try {\n      const response = await this.http.get<any>(`${this.apiUrl}/customers`).toPromise();\n      \n      if (response && Array.isArray(response)) {\n        this.customers = response.map(customer => ({\n          ...customer,\n          fullName: customer.nameAr || customer.fullName,\n          phoneNumber: customer.phone1 || customer.phoneNumber,\n          createdAt: new Date(customer.createdAt)\n        }));\n      } else {\n        // Fallback data if API fails\n        this.customers = this.getFallbackCustomers();\n      }\n      \n      this.dataSource.data = this.customers;\n      this.totalCustomers = this.customers.length;\n      \n    } catch (error) {\n      console.error('Error loading customers:', error);\n      this.customers = this.getFallbackCustomers();\n      this.dataSource.data = this.customers;\n      this.showMessage('تم تحميل بيانات تجريبية للعملاء', 'warning');\n    }\n  }\n\n  async loadCustomerTypes(): Promise<void> {\n    try {\n      const response = await this.http.get<any>(`${this.apiUrl}/simple/customer-types`).toPromise();\n      \n      if (response && response.customerTypes) {\n        this.customerTypes = response.customerTypes;\n      } else {\n        this.customerTypes = this.getFallbackCustomerTypes();\n      }\n    } catch (error) {\n      console.error('Error loading customer types:', error);\n      this.customerTypes = this.getFallbackCustomerTypes();\n    }\n  }\n\n  async loadAreas(): Promise<void> {\n    try {\n      const response = await this.http.get<any>(`${this.apiUrl}/simple/areas-db`).toPromise();\n      \n      if (response && Array.isArray(response)) {\n        this.areas = response;\n      } else {\n        this.areas = this.getFallbackAreas();\n      }\n    } catch (error) {\n      console.error('Error loading areas:', error);\n      this.areas = this.getFallbackAreas();\n    }\n  }\n\n  async loadBranches(): Promise<void> {\n    try {\n      const response = await this.http.get<any>(`${this.apiUrl}/simple/branches-db`).toPromise();\n      \n      if (response && response.branches) {\n        this.branches = response.branches;\n      } else {\n        this.branches = this.getFallbackBranches();\n      }\n    } catch (error) {\n      console.error('Error loading branches:', error);\n      this.branches = this.getFallbackBranches();\n    }\n  }\n\n  calculateStatistics(): void {\n    this.totalCustomers = this.customers.length;\n    this.activeCustomers = this.customers.filter(c => c.isActive).length;\n    this.totalBalance = this.customers.reduce((sum, c) => sum + (c.currentBalance || 0), 0);\n    this.totalCreditLimit = this.customers.reduce((sum, c) => sum + (c.creditLimit || 0), 0);\n  }\n\n  onSearch(): void {\n    this.dataSource.filter = this.searchTerm.trim().toLowerCase();\n  }\n\n  onFilterChange(): void {\n    let filteredData = this.customers;\n\n    if (this.selectedCustomerType) {\n      filteredData = filteredData.filter(c => c.customerTypeId.toString() === this.selectedCustomerType);\n    }\n\n    if (this.selectedArea) {\n      filteredData = filteredData.filter(c => c.areaId?.toString() === this.selectedArea);\n    }\n\n    if (this.selectedBranch) {\n      filteredData = filteredData.filter(c => c.branchId?.toString() === this.selectedBranch);\n    }\n\n    this.dataSource.data = filteredData;\n  }\n\n  clearFilters(): void {\n    this.searchTerm = '';\n    this.selectedCustomerType = '';\n    this.selectedArea = '';\n    this.selectedBranch = '';\n    this.dataSource.filter = '';\n    this.dataSource.data = this.customers;\n  }\n\n  refreshCustomers(): void {\n    this.loadCustomers();\n    this.showMessage('تم تحديث بيانات العملاء', 'success');\n  }\n\n  openAddCustomerDialog(): void {\n    // TODO: Implement add customer dialog\n    this.showMessage('سيتم إضافة نافذة إضافة عميل قريباً', 'info');\n  }\n\n  viewCustomer(customer: Customer): void {\n    // TODO: Implement view customer details\n    this.showMessage(`عرض تفاصيل العميل: ${customer.fullName}`, 'info');\n  }\n\n  editCustomer(customer: Customer): void {\n    // TODO: Implement edit customer dialog\n    this.showMessage(`تعديل العميل: ${customer.fullName}`, 'info');\n  }\n\n  viewFinancials(customer: Customer): void {\n    // TODO: Implement financial details view\n    this.showMessage(`عرض الحساب المالي للعميل: ${customer.fullName}`, 'info');\n  }\n\n  toggleCustomerStatus(customer: Customer): void {\n    customer.isActive = !customer.isActive;\n    this.showMessage(`تم ${customer.isActive ? 'تفعيل' : 'إلغاء تفعيل'} العميل: ${customer.fullName}`, 'success');\n    this.calculateStatistics();\n  }\n\n  deleteCustomer(customer: Customer): void {\n    if (confirm(`هل أنت متأكد من حذف العميل: ${customer.fullName}؟`)) {\n      const index = this.customers.findIndex(c => c.id === customer.id);\n      if (index > -1) {\n        this.customers.splice(index, 1);\n        this.dataSource.data = this.customers;\n        this.calculateStatistics();\n        this.showMessage(`تم حذف العميل: ${customer.fullName}`, 'success');\n      }\n    }\n  }\n\n  exportCustomers(): void {\n    // TODO: Implement export functionality\n    this.showMessage('سيتم إضافة وظيفة التصدير قريباً', 'info');\n  }\n\n  private showMessage(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      panelClass: [`snackbar-${type}`],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n\n  // Fallback data methods\n  private getFallbackCustomers(): Customer[] {\n    return [\n      {\n        id: 1,\n        customerCode: 'CUS001',\n        fullName: 'شركة الأهرام للتجارة',\n        nameAr: 'شركة الأهرام للتجارة',\n        nameEn: 'Ahram Trading Company',\n        customerTypeId: 3,\n        customerTypeName: 'عميل مؤسسي',\n        phoneNumber: '+201111111111',\n        phone1: '+201111111111',\n        phone2: '+201111111112',\n        email: '<EMAIL>',\n        address: 'شارع التحرير، وسط البلد',\n        areaId: 1,\n        areaName: 'القاهرة',\n        branchId: 1,\n        branchName: 'الفرع الرئيسي',\n        priceCategoryId: 3,\n        priceCategoryName: 'سعر كبار العملاء',\n        creditLimit: 100000,\n        openingBalance: 25000,\n        currentBalance: 25000,\n        isActive: true,\n        createdAt: new Date()\n      }\n      // Add more fallback customers as needed\n    ];\n  }\n\n  private getFallbackCustomerTypes(): CustomerType[] {\n    return [\n      { id: 1, nameAr: 'عميل تجزئة', nameEn: 'Retail Customer', defaultDiscountPercentage: 0, defaultCreditLimit: 5000 },\n      { id: 2, nameAr: 'عميل جملة', nameEn: 'Wholesale Customer', defaultDiscountPercentage: 10, defaultCreditLimit: 50000 },\n      { id: 3, nameAr: 'عميل مؤسسي', nameEn: 'Corporate Customer', defaultDiscountPercentage: 15, defaultCreditLimit: 100000 }\n    ];\n  }\n\n  private getFallbackAreas(): Area[] {\n    return [\n      { id: 1, nameAr: 'القاهرة', nameEn: 'Cairo', code: 'CAI' },\n      { id: 2, nameAr: 'الجيزة', nameEn: 'Giza', code: 'GIZ' },\n      { id: 3, nameAr: 'الإسكندرية', nameEn: 'Alexandria', code: 'ALX' }\n    ];\n  }\n\n  private getFallbackBranches(): Branch[] {\n    return [\n      { id: 1, code: 'BR001', nameAr: 'الفرع الرئيسي', nameEn: 'Main Branch', address: 'القاهرة', phone: '+201234567890', isMainBranch: true },\n      { id: 2, code: 'BR002', nameAr: 'فرع الإسكندرية', nameEn: 'Alexandria Branch', address: 'الإسكندرية', phone: '+203456789012', isMainBranch: false }\n    ];\n  }\n}\n", "<!-- Terra Retail ERP - Customers Page -->\n<div class=\"customers-container\">\n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" routerLink=\"/dashboard\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">إدارة العملاء</h1>\n          <p class=\"page-subtitle\">إدارة بيانات العملاء والحسابات المالية</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button class=\"add-btn\" (click)=\"openAddCustomerDialog()\">\n          <mat-icon>add</mat-icon>\n          إضافة عميل جديد\n        </button>\n        <button mat-stroked-button class=\"export-btn\" (click)=\"exportCustomers()\">\n          <mat-icon>download</mat-icon>\n          تصدير\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content Area -->\n  <div class=\"content-area\">\n    <!-- Statistics Cards -->\n    <div class=\"stats-grid\">\n      <mat-card class=\"stat-card customers-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\">\n              <mat-icon>people</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ totalCustomers }}</h3>\n              <p>إجمالي العملاء</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card active-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\">\n              <mat-icon>person_check</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ activeCustomers }}</h3>\n              <p>العملاء النشطين</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card balance-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\">\n              <mat-icon>account_balance_wallet</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ totalBalance | currency:'EGP':'symbol':'1.2-2' }}</h3>\n              <p>إجمالي الأرصدة</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card credit-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\">\n              <mat-icon>credit_card</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ totalCreditLimit | currency:'EGP':'symbol':'1.2-2' }}</h3>\n              <p>إجمالي حدود الائتمان</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Filters and Search -->\n    <mat-card class=\"filters-card\">\n      <mat-card-content>\n        <div class=\"filters-row\">\n          <mat-form-field class=\"search-field\">\n            <mat-label>البحث في العملاء</mat-label>\n            <input matInput [(ngModel)]=\"searchTerm\" (input)=\"onSearch()\" placeholder=\"اسم العميل، الكود، أو رقم الهاتف\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <mat-form-field class=\"filter-field\">\n            <mat-label>نوع العميل</mat-label>\n            <mat-select [(ngModel)]=\"selectedCustomerType\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الأنواع</mat-option>\n              <mat-option *ngFor=\"let type of customerTypes\" [value]=\"type.id\">\n                {{ type.nameAr }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field class=\"filter-field\">\n            <mat-label>المنطقة</mat-label>\n            <mat-select [(ngModel)]=\"selectedArea\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع المناطق</mat-option>\n              <mat-option *ngFor=\"let area of areas\" [value]=\"area.id\">\n                {{ area.nameAr }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field class=\"filter-field\">\n            <mat-label>الفرع</mat-label>\n            <mat-select [(ngModel)]=\"selectedBranch\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الفروع</mat-option>\n              <mat-option *ngFor=\"let branch of branches\" [value]=\"branch.id\">\n                {{ branch.nameAr }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <button mat-stroked-button class=\"clear-filters-btn\" (click)=\"clearFilters()\">\n            <mat-icon>clear</mat-icon>\n            مسح الفلاتر\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Customers Table -->\n    <mat-card class=\"table-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>people</mat-icon>\n          قائمة العملاء\n        </mat-card-title>\n        <div class=\"table-actions\">\n          <button mat-icon-button (click)=\"refreshCustomers()\" matTooltip=\"تحديث البيانات\">\n            <mat-icon>refresh</mat-icon>\n          </button>\n        </div>\n      </mat-card-header>\n\n      <mat-card-content>\n        <div class=\"table-container\">\n          <table mat-table [dataSource]=\"dataSource\" class=\"customers-table\" matSort>\n            <!-- Customer Code Column -->\n            <ng-container matColumnDef=\"customerCode\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>كود العميل</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"customer-code\">{{ customer.customerCode }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Customer Name Column -->\n            <ng-container matColumnDef=\"fullName\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم العميل</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <div class=\"customer-info\">\n                  <span class=\"customer-name-ar\">{{ customer.fullName }}</span>\n                  <span class=\"customer-name-en\" *ngIf=\"customer.nameEn\">{{ customer.nameEn }}</span>\n                </div>\n              </td>\n            </ng-container>\n\n            <!-- Customer Type Column -->\n            <ng-container matColumnDef=\"customerTypeName\">\n              <th mat-header-cell *matHeaderCellDef>نوع العميل</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"customer-type-badge\" [class]=\"'type-' + customer.customerTypeId\">\n                  {{ customer.customerTypeName }}\n                </span>\n              </td>\n            </ng-container>\n\n            <!-- Phone Column -->\n            <ng-container matColumnDef=\"phoneNumber\">\n              <th mat-header-cell *matHeaderCellDef>رقم الهاتف</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"phone-number\">{{ customer.phoneNumber }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Area Column -->\n            <ng-container matColumnDef=\"areaName\">\n              <th mat-header-cell *matHeaderCellDef>المنطقة</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"area-name\">{{ customer.areaName }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Balance Column -->\n            <ng-container matColumnDef=\"currentBalance\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد الحالي</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"balance\" [class.negative]=\"customer.currentBalance < 0\">\n                  {{ customer.currentBalance | currency:'EGP':'symbol':'1.2-2' }}\n                </span>\n              </td>\n            </ng-container>\n\n            <!-- Status Column -->\n            <ng-container matColumnDef=\"isActive\">\n              <th mat-header-cell *matHeaderCellDef>الحالة</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <span class=\"status-badge\" [class.active]=\"customer.isActive\" [class.inactive]=\"!customer.isActive\">\n                  {{ customer.isActive ? 'نشط' : 'غير نشط' }}\n                </span>\n              </td>\n            </ng-container>\n\n            <!-- Actions Column -->\n            <ng-container matColumnDef=\"actions\">\n              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\n              <td mat-cell *matCellDef=\"let customer\">\n                <div class=\"action-buttons\">\n                  <button mat-icon-button (click)=\"viewCustomer(customer)\" matTooltip=\"عرض التفاصيل\">\n                    <mat-icon>visibility</mat-icon>\n                  </button>\n                  <button mat-icon-button (click)=\"editCustomer(customer)\" matTooltip=\"تعديل\">\n                    <mat-icon>edit</mat-icon>\n                  </button>\n                  <button mat-icon-button (click)=\"viewFinancials(customer)\" matTooltip=\"الحساب المالي\">\n                    <mat-icon>account_balance</mat-icon>\n                  </button>\n                  <button mat-icon-button [matMenuTriggerFor]=\"actionMenu\" matTooltip=\"المزيد\">\n                    <mat-icon>more_vert</mat-icon>\n                  </button>\n                  <mat-menu #actionMenu=\"matMenu\">\n                    <button mat-menu-item (click)=\"toggleCustomerStatus(customer)\">\n                      <mat-icon>{{ customer.isActive ? 'block' : 'check_circle' }}</mat-icon>\n                      {{ customer.isActive ? 'إلغاء التفعيل' : 'تفعيل' }}\n                    </button>\n                    <button mat-menu-item (click)=\"deleteCustomer(customer)\">\n                      <mat-icon>delete</mat-icon>\n                      حذف\n                    </button>\n                  </mat-menu>\n                </div>\n              </td>\n            </ng-container>\n\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" (click)=\"viewCustomer(row)\" class=\"clickable-row\"></tr>\n          </table>\n\n          <!-- No Data Message -->\n          <div *ngIf=\"dataSource.data.length === 0\" class=\"no-data\">\n            <mat-icon>people_outline</mat-icon>\n            <h3>لا توجد عملاء</h3>\n            <p>لم يتم العثور على أي عملاء. قم بإضافة عميل جديد للبدء.</p>\n            <button mat-raised-button color=\"primary\" (click)=\"openAddCustomerDialog()\">\n              إضافة عميل جديد\n            </button>\n          </div>\n        </div>\n\n        <!-- Pagination -->\n        <mat-paginator [pageSizeOptions]=\"[10, 25, 50, 100]\" showFirstLastButtons></mat-paginator>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div *ngIf=\"isLoading\" class=\"loading-overlay\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل بيانات العملاء...</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAIhD,SAASC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;IC+FjDC,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,MAAA,MACF;;;;;IAQAT,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,EAAA,CAAiB;IACtDN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAD,MAAA,MACF;;;;;IAQAT,EAAA,CAAAC,cAAA,qBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+BH,EAAA,CAAAI,UAAA,UAAAO,SAAA,CAAAL,EAAA,CAAmB;IAC7DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAG,SAAA,CAAAF,MAAA,MACF;;;;;IA+BAT,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnEH,EADF,CAAAC,cAAA,aAAwC,eACV;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC3D;;;;IADyBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAY,iBAAA,CAAAC,WAAA,CAAAC,YAAA,CAA2B;;;;;IAMzDd,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAIjEH,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAY,iBAAA,CAAAG,WAAA,CAAAC,MAAA,CAAqB;;;;;IAD5EhB,EAFJ,CAAAC,cAAA,aAAwC,cACX,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAiB,UAAA,IAAAC,yCAAA,mBAAuD;IAE3DlB,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAH8BH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAY,iBAAA,CAAAG,WAAA,CAAAI,QAAA,CAAuB;IACtBnB,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAI,UAAA,SAAAW,WAAA,CAAAC,MAAA,CAAqB;;;;;IAOzDhB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnDH,EADF,CAAAC,cAAA,aAAwC,eACwC;IAC5ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAH+BH,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAoB,UAAA,WAAAC,WAAA,CAAAC,cAAA,CAA2C;IAC3EtB,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAa,WAAA,CAAAE,gBAAA,MACF;;;;;IAMFvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnDH,EADF,CAAAC,cAAA,aAAwC,eACX;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;;;;IADwBH,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAY,iBAAA,CAAAY,WAAA,CAAAC,WAAA,CAA0B;;;;;IAMvDzB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAwC,eACd;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;;;;IADqBH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAY,iBAAA,CAAAc,WAAA,CAAAC,QAAA,CAAuB;;;;;IAMjD3B,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,gFAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEtEH,EADF,CAAAC,cAAA,aAAwC,eAC+B;IACnED,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAHmBH,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAA4B,WAAA,aAAAC,WAAA,CAAAC,cAAA,KAA8C;IAClE9B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA+B,WAAA,OAAAF,WAAA,CAAAC,cAAA,iCACF;;;;;IAMF9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAwC,eAC8D;IAClGD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;IAHwBH,EAAA,CAAAO,SAAA,EAAkC;IAACP,EAAnC,CAAA4B,WAAA,WAAAI,YAAA,CAAAC,QAAA,CAAkC,cAAAD,YAAA,CAAAC,QAAA,CAAsC;IACjGjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAwB,YAAA,CAAAC,QAAA,uEACF;;;;;IAMFjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAwC,cACV,iBACyD;IAA3DD,EAAA,CAAAkC,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAG,YAAA,CAAAP,YAAA,CAAsB;IAAA,EAAC;IACtDpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAA4E;IAApDD,EAAA,CAAAkC,UAAA,mBAAAU,2DAAA;MAAA,MAAAR,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAK,YAAA,CAAAT,YAAA,CAAsB;IAAA,EAAC;IACtDpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAAsF;IAA9DD,EAAA,CAAAkC,UAAA,mBAAAY,2DAAA;MAAA,MAAAV,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAO,cAAA,CAAAX,YAAA,CAAwB;IAAA,EAAC;IACxDpC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;IAEPH,EADF,CAAAC,cAAA,kBAA6E,gBACjE;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IACrBF,EADqB,CAAAG,YAAA,EAAW,EACvB;IAEPH,EADF,CAAAC,cAAA,yBAAgC,kBACiC;IAAzCD,EAAA,CAAAkC,UAAA,mBAAAc,4DAAA;MAAA,MAAAZ,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAS,oBAAA,CAAAb,YAAA,CAA8B;IAAA,EAAC;IAC5DpC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAyD;IAAnCD,EAAA,CAAAkC,UAAA,mBAAAgB,4DAAA;MAAA,MAAAd,YAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAW,cAAA,CAAAf,YAAA,CAAwB;IAAA,EAAC;IACtDpC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,4BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACA,EACP,EACH;;;;;IAduBH,EAAA,CAAAO,SAAA,IAAgC;IAAhCP,EAAA,CAAAI,UAAA,sBAAAgD,cAAA,CAAgC;IAK1CpD,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAY,iBAAA,CAAAwB,YAAA,CAAAH,QAAA,4BAAkD;IAC5DjC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA4B,YAAA,CAAAH,QAAA,uHACF;;;;;IAURjC,EAAA,CAAAqD,SAAA,aAA4D;;;;;;IAC5DrD,EAAA,CAAAC,cAAA,aAA+G;IAAlDD,EAAA,CAAAkC,UAAA,mBAAAoB,uDAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAqC,aAAA,CAAAmB,IAAA,EAAAjB,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAG,YAAA,CAAAY,OAAA,CAAiB;IAAA,EAAC;IAAuBvD,EAAA,CAAAG,YAAA,EAAK;;;;;;IAKpHH,EADF,CAAAC,cAAA,cAA0D,eAC9C;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,2EAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+QAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7DH,EAAA,CAAAC,cAAA,iBAA4E;IAAlCD,EAAA,CAAAkC,UAAA,mBAAAuB,4DAAA;MAAAzD,EAAA,CAAAqC,aAAA,CAAAqB,IAAA;MAAA,MAAAlB,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAAzC,EAAA,CAAA0C,WAAA,CAASF,OAAA,CAAAmB,qBAAA,EAAuB;IAAA,EAAC;IACzE3D,EAAA,CAAAE,MAAA,yFACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAUdH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAqD,SAAA,sBAAyC;IACzCrD,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iJAA4B;IACjCF,EADiC,CAAAG,YAAA,EAAI,EAC/B;;;ADhNR,WAAayD,kBAAkB;EAAzB,MAAOA,kBAAkB;IA0CnBC,IAAA;IACAC,MAAA;IACAC,QAAA;IA3CeC,SAAS;IACdC,IAAI;IAExB;IACAC,gBAAgB,GAAa,CAC3B,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,SAAS,CACV;IAEDC,UAAU,GAAG,IAAIvE,kBAAkB,CAAW,EAAE,CAAC;IAEjD;IACAwE,SAAS,GAAe,EAAE;IAC1BC,aAAa,GAAmB,EAAE;IAClCC,KAAK,GAAW,EAAE;IAClBC,QAAQ,GAAa,EAAE;IAEvB;IACAC,UAAU,GAAW,EAAE;IACvBC,oBAAoB,GAAW,EAAE;IACjCC,YAAY,GAAW,EAAE;IACzBC,cAAc,GAAW,EAAE;IAE3B;IACAC,cAAc,GAAW,CAAC;IAC1BC,eAAe,GAAW,CAAC;IAC3BC,YAAY,GAAW,CAAC;IACxBC,gBAAgB,GAAW,CAAC;IAE5B;IACAC,SAAS,GAAY,KAAK;IAElBC,MAAM,GAAGlF,WAAW,CAACkF,MAAM;IAEnCC,YACUrB,IAAgB,EAChBC,MAAiB,EACjBC,QAAqB;MAFrB,KAAAF,IAAI,GAAJA,IAAI;MACJ,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,QAAQ,GAARA,QAAQ;IACf;IAEHoB,QAAQA,CAAA;MACN,IAAI,CAACC,eAAe,EAAE;IACxB;IAEAC,eAAeA,CAAA;MACb,IAAI,CAAClB,UAAU,CAACH,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1C,IAAI,CAACG,UAAU,CAACF,IAAI,GAAG,IAAI,CAACA,IAAI;MAEhC;MACA,IAAI,CAACE,UAAU,CAACmB,eAAe,GAAG,CAACC,IAAc,EAAEC,MAAc,KAAa;QAC5E,MAAMC,SAAS,GAAGD,MAAM,CAACE,WAAW,EAAE;QACtC,OAAOH,IAAI,CAACpE,QAAQ,CAACuE,WAAW,EAAE,CAACC,QAAQ,CAACF,SAAS,CAAC,IAC/CF,IAAI,CAACzE,YAAY,CAAC4E,WAAW,EAAE,CAACC,QAAQ,CAACF,SAAS,CAAC,IACnDF,IAAI,CAAC9D,WAAW,CAACiE,WAAW,EAAE,CAACC,QAAQ,CAACF,SAAS,CAAC,KACjDF,IAAI,CAACK,KAAK,GAAGL,IAAI,CAACK,KAAK,CAACF,WAAW,EAAE,CAACC,QAAQ,CAACF,SAAS,CAAC,GAAG,KAAK,CAAC;MAC5E,CAAC;IACH;IAEML,eAAeA,CAAA;MAAA,IAAAS,KAAA;MAAA,OAAAC,iBAAA;QACnBD,KAAI,CAACb,SAAS,GAAG,IAAI;QAErB,IAAI;UACF;UACA,MAAMe,OAAO,CAACC,GAAG,CAAC,CAChBH,KAAI,CAACI,aAAa,EAAE,EACpBJ,KAAI,CAACK,iBAAiB,EAAE,EACxBL,KAAI,CAACM,SAAS,EAAE,EAChBN,KAAI,CAACO,YAAY,EAAE,CACpB,CAAC;UAEFP,KAAI,CAACQ,mBAAmB,EAAE;QAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDT,KAAI,CAACW,WAAW,CAAC,2BAA2B,EAAE,OAAO,CAAC;QACxD,CAAC,SAAS;UACRX,KAAI,CAACb,SAAS,GAAG,KAAK;QACxB;MAAC;IACH;IAEMiB,aAAaA,CAAA;MAAA,IAAAQ,MAAA;MAAA,OAAAX,iBAAA;QACjB,IAAI;UACF,MAAMY,QAAQ,SAASD,MAAI,CAAC5C,IAAI,CAAC8C,GAAG,CAAM,GAAGF,MAAI,CAACxB,MAAM,YAAY,CAAC,CAAC2B,SAAS,EAAE;UAEjF,IAAIF,QAAQ,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;YACvCD,MAAI,CAACrC,SAAS,GAAGsC,QAAQ,CAACK,GAAG,CAACC,QAAQ,KAAK;cACzC,GAAGA,QAAQ;cACX7F,QAAQ,EAAE6F,QAAQ,CAACvG,MAAM,IAAIuG,QAAQ,CAAC7F,QAAQ;cAC9CM,WAAW,EAAEuF,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACvF,WAAW;cACpDyF,SAAS,EAAE,IAAIC,IAAI,CAACH,QAAQ,CAACE,SAAS;aACvC,CAAC,CAAC;UACL,CAAC,MAAM;YACL;YACAT,MAAI,CAACrC,SAAS,GAAGqC,MAAI,CAACW,oBAAoB,EAAE;UAC9C;UAEAX,MAAI,CAACtC,UAAU,CAACoB,IAAI,GAAGkB,MAAI,CAACrC,SAAS;UACrCqC,MAAI,CAAC7B,cAAc,GAAG6B,MAAI,CAACrC,SAAS,CAACiD,MAAM;QAE7C,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDG,MAAI,CAACrC,SAAS,GAAGqC,MAAI,CAACW,oBAAoB,EAAE;UAC5CX,MAAI,CAACtC,UAAU,CAACoB,IAAI,GAAGkB,MAAI,CAACrC,SAAS;UACrCqC,MAAI,CAACD,WAAW,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAChE;MAAC;IACH;IAEMN,iBAAiBA,CAAA;MAAA,IAAAoB,MAAA;MAAA,OAAAxB,iBAAA;QACrB,IAAI;UACF,MAAMY,QAAQ,SAASY,MAAI,CAACzD,IAAI,CAAC8C,GAAG,CAAM,GAAGW,MAAI,CAACrC,MAAM,wBAAwB,CAAC,CAAC2B,SAAS,EAAE;UAE7F,IAAIF,QAAQ,IAAIA,QAAQ,CAACrC,aAAa,EAAE;YACtCiD,MAAI,CAACjD,aAAa,GAAGqC,QAAQ,CAACrC,aAAa;UAC7C,CAAC,MAAM;YACLiD,MAAI,CAACjD,aAAa,GAAGiD,MAAI,CAACC,wBAAwB,EAAE;UACtD;QACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrDgB,MAAI,CAACjD,aAAa,GAAGiD,MAAI,CAACC,wBAAwB,EAAE;QACtD;MAAC;IACH;IAEMpB,SAASA,CAAA;MAAA,IAAAqB,MAAA;MAAA,OAAA1B,iBAAA;QACb,IAAI;UACF,MAAMY,QAAQ,SAASc,MAAI,CAAC3D,IAAI,CAAC8C,GAAG,CAAM,GAAGa,MAAI,CAACvC,MAAM,kBAAkB,CAAC,CAAC2B,SAAS,EAAE;UAEvF,IAAIF,QAAQ,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;YACvCc,MAAI,CAAClD,KAAK,GAAGoC,QAAQ;UACvB,CAAC,MAAM;YACLc,MAAI,CAAClD,KAAK,GAAGkD,MAAI,CAACC,gBAAgB,EAAE;UACtC;QACF,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CkB,MAAI,CAAClD,KAAK,GAAGkD,MAAI,CAACC,gBAAgB,EAAE;QACtC;MAAC;IACH;IAEMrB,YAAYA,CAAA;MAAA,IAAAsB,MAAA;MAAA,OAAA5B,iBAAA;QAChB,IAAI;UACF,MAAMY,QAAQ,SAASgB,MAAI,CAAC7D,IAAI,CAAC8C,GAAG,CAAM,GAAGe,MAAI,CAACzC,MAAM,qBAAqB,CAAC,CAAC2B,SAAS,EAAE;UAE1F,IAAIF,QAAQ,IAAIA,QAAQ,CAACnC,QAAQ,EAAE;YACjCmD,MAAI,CAACnD,QAAQ,GAAGmC,QAAQ,CAACnC,QAAQ;UACnC,CAAC,MAAM;YACLmD,MAAI,CAACnD,QAAQ,GAAGmD,MAAI,CAACC,mBAAmB,EAAE;UAC5C;QACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/CoB,MAAI,CAACnD,QAAQ,GAAGmD,MAAI,CAACC,mBAAmB,EAAE;QAC5C;MAAC;IACH;IAEAtB,mBAAmBA,CAAA;MACjB,IAAI,CAACzB,cAAc,GAAG,IAAI,CAACR,SAAS,CAACiD,MAAM;MAC3C,IAAI,CAACxC,eAAe,GAAG,IAAI,CAACT,SAAS,CAACoB,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAAC3F,QAAQ,CAAC,CAACoF,MAAM;MACpE,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACV,SAAS,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,IAAIF,CAAC,CAAC9F,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACvF,IAAI,CAACiD,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,IAAIF,CAAC,CAACG,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1F;IAEAC,QAAQA,CAAA;MACN,IAAI,CAAC7D,UAAU,CAACqB,MAAM,GAAG,IAAI,CAAChB,UAAU,CAACyD,IAAI,EAAE,CAACvC,WAAW,EAAE;IAC/D;IAEAwC,cAAcA,CAAA;MACZ,IAAIC,YAAY,GAAG,IAAI,CAAC/D,SAAS;MAEjC,IAAI,IAAI,CAACK,oBAAoB,EAAE;QAC7B0D,YAAY,GAAGA,YAAY,CAAC3C,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAACtG,cAAc,CAAC8G,QAAQ,EAAE,KAAK,IAAI,CAAC3D,oBAAoB,CAAC;MACpG;MAEA,IAAI,IAAI,CAACC,YAAY,EAAE;QACrByD,YAAY,GAAGA,YAAY,CAAC3C,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAACS,MAAM,EAAED,QAAQ,EAAE,KAAK,IAAI,CAAC1D,YAAY,CAAC;MACrF;MAEA,IAAI,IAAI,CAACC,cAAc,EAAE;QACvBwD,YAAY,GAAGA,YAAY,CAAC3C,MAAM,CAACoC,CAAC,IAAIA,CAAC,CAACU,QAAQ,EAAEF,QAAQ,EAAE,KAAK,IAAI,CAACzD,cAAc,CAAC;MACzF;MAEA,IAAI,CAACR,UAAU,CAACoB,IAAI,GAAG4C,YAAY;IACrC;IAEAI,YAAYA,CAAA;MACV,IAAI,CAAC/D,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACR,UAAU,CAACqB,MAAM,GAAG,EAAE;MAC3B,IAAI,CAACrB,UAAU,CAACoB,IAAI,GAAG,IAAI,CAACnB,SAAS;IACvC;IAEAoE,gBAAgBA,CAAA;MACd,IAAI,CAACvC,aAAa,EAAE;MACpB,IAAI,CAACO,WAAW,CAAC,yBAAyB,EAAE,SAAS,CAAC;IACxD;IAEA7C,qBAAqBA,CAAA;MACnB;MACA,IAAI,CAAC6C,WAAW,CAAC,oCAAoC,EAAE,MAAM,CAAC;IAChE;IAEA7D,YAAYA,CAACqE,QAAkB;MAC7B;MACA,IAAI,CAACR,WAAW,CAAC,sBAAsBQ,QAAQ,CAAC7F,QAAQ,EAAE,EAAE,MAAM,CAAC;IACrE;IAEA0B,YAAYA,CAACmE,QAAkB;MAC7B;MACA,IAAI,CAACR,WAAW,CAAC,iBAAiBQ,QAAQ,CAAC7F,QAAQ,EAAE,EAAE,MAAM,CAAC;IAChE;IAEA4B,cAAcA,CAACiE,QAAkB;MAC/B;MACA,IAAI,CAACR,WAAW,CAAC,6BAA6BQ,QAAQ,CAAC7F,QAAQ,EAAE,EAAE,MAAM,CAAC;IAC5E;IAEA8B,oBAAoBA,CAAC+D,QAAkB;MACrCA,QAAQ,CAAC/E,QAAQ,GAAG,CAAC+E,QAAQ,CAAC/E,QAAQ;MACtC,IAAI,CAACuE,WAAW,CAAC,MAAMQ,QAAQ,CAAC/E,QAAQ,GAAG,OAAO,GAAG,aAAa,YAAY+E,QAAQ,CAAC7F,QAAQ,EAAE,EAAE,SAAS,CAAC;MAC7G,IAAI,CAACkF,mBAAmB,EAAE;IAC5B;IAEAlD,cAAcA,CAAC6D,QAAkB;MAC/B,IAAIyB,OAAO,CAAC,+BAA+BzB,QAAQ,CAAC7F,QAAQ,GAAG,CAAC,EAAE;QAChE,MAAMuH,KAAK,GAAG,IAAI,CAACtE,SAAS,CAACuE,SAAS,CAACf,CAAC,IAAIA,CAAC,CAACtH,EAAE,KAAK0G,QAAQ,CAAC1G,EAAE,CAAC;QACjE,IAAIoI,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,IAAI,CAACtE,SAAS,CAACwE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC/B,IAAI,CAACvE,UAAU,CAACoB,IAAI,GAAG,IAAI,CAACnB,SAAS;UACrC,IAAI,CAACiC,mBAAmB,EAAE;UAC1B,IAAI,CAACG,WAAW,CAAC,kBAAkBQ,QAAQ,CAAC7F,QAAQ,EAAE,EAAE,SAAS,CAAC;QACpE;MACF;IACF;IAEA0H,eAAeA,CAAA;MACb;MACA,IAAI,CAACrC,WAAW,CAAC,iCAAiC,EAAE,MAAM,CAAC;IAC7D;IAEQA,WAAWA,CAACsC,OAAe,EAAEC,IAAA,GAAiD,MAAM;MAC1F,IAAI,CAAChF,QAAQ,CAACiF,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;QACnCG,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,YAAYH,IAAI,EAAE,CAAC;QAChCI,kBAAkB,EAAE,KAAK;QACzBC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;IACQhC,oBAAoBA,CAAA;MAC1B,OAAO,CACL;QACE9G,EAAE,EAAE,CAAC;QACLQ,YAAY,EAAE,QAAQ;QACtBK,QAAQ,EAAE,sBAAsB;QAChCV,MAAM,EAAE,sBAAsB;QAC9BO,MAAM,EAAE,uBAAuB;QAC/BM,cAAc,EAAE,CAAC;QACjBC,gBAAgB,EAAE,YAAY;QAC9BE,WAAW,EAAE,eAAe;QAC5BwF,MAAM,EAAE,eAAe;QACvBoC,MAAM,EAAE,eAAe;QACvBzD,KAAK,EAAE,uBAAuB;QAC9B0D,OAAO,EAAE,yBAAyB;QAClCjB,MAAM,EAAE,CAAC;QACT1G,QAAQ,EAAE,SAAS;QACnB2G,QAAQ,EAAE,CAAC;QACXiB,UAAU,EAAE,eAAe;QAC3BC,eAAe,EAAE,CAAC;QAClBC,iBAAiB,EAAE,kBAAkB;QACrC1B,WAAW,EAAE,MAAM;QACnB2B,cAAc,EAAE,KAAK;QACrB5H,cAAc,EAAE,KAAK;QACrBG,QAAQ,EAAE,IAAI;QACdiF,SAAS,EAAE,IAAIC,IAAI;;MAErB;MAAA,CACD;IACH;IAEQI,wBAAwBA,CAAA;MAC9B,OAAO,CACL;QAAEjH,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,YAAY;QAAEO,MAAM,EAAE,iBAAiB;QAAE2I,yBAAyB,EAAE,CAAC;QAAEC,kBAAkB,EAAE;MAAI,CAAE,EAClH;QAAEtJ,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,WAAW;QAAEO,MAAM,EAAE,oBAAoB;QAAE2I,yBAAyB,EAAE,EAAE;QAAEC,kBAAkB,EAAE;MAAK,CAAE,EACtH;QAAEtJ,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,YAAY;QAAEO,MAAM,EAAE,oBAAoB;QAAE2I,yBAAyB,EAAE,EAAE;QAAEC,kBAAkB,EAAE;MAAM,CAAE,CACzH;IACH;IAEQnC,gBAAgBA,CAAA;MACtB,OAAO,CACL;QAAEnH,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,SAAS;QAAEO,MAAM,EAAE,OAAO;QAAE6I,IAAI,EAAE;MAAK,CAAE,EAC1D;QAAEvJ,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,QAAQ;QAAEO,MAAM,EAAE,MAAM;QAAE6I,IAAI,EAAE;MAAK,CAAE,EACxD;QAAEvJ,EAAE,EAAE,CAAC;QAAEG,MAAM,EAAE,YAAY;QAAEO,MAAM,EAAE,YAAY;QAAE6I,IAAI,EAAE;MAAK,CAAE,CACnE;IACH;IAEQlC,mBAAmBA,CAAA;MACzB,OAAO,CACL;QAAErH,EAAE,EAAE,CAAC;QAAEuJ,IAAI,EAAE,OAAO;QAAEpJ,MAAM,EAAE,eAAe;QAAEO,MAAM,EAAE,aAAa;QAAEsI,OAAO,EAAE,SAAS;QAAEQ,KAAK,EAAE,eAAe;QAAEC,YAAY,EAAE;MAAI,CAAE,EACxI;QAAEzJ,EAAE,EAAE,CAAC;QAAEuJ,IAAI,EAAE,OAAO;QAAEpJ,MAAM,EAAE,gBAAgB;QAAEO,MAAM,EAAE,mBAAmB;QAAEsI,OAAO,EAAE,YAAY;QAAEQ,KAAK,EAAE,eAAe;QAAEC,YAAY,EAAE;MAAK,CAAE,CACpJ;IACH;;uCAlTWnG,kBAAkB,EAAA5D,EAAA,CAAAgK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAlK,EAAA,CAAAgK,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAApK,EAAA,CAAAgK,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;;YAAlB1G,kBAAkB;MAAA2G,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAClB7K,YAAY;yBACZC,OAAO;;;;;;;;;;;;;;UC7DVE,EANV,CAAAC,cAAA,aAAiC,aAEN,aACK,aACD,gBAC0C,eACrD;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,gFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,wNAAsC;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,kBACkD;UAAlCD,EAAA,CAAAkC,UAAA,mBAAA0I,qDAAA;YAAA,OAASD,GAAA,CAAAhH,qBAAA,EAAuB;UAAA,EAAC;UACzE3D,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,0FACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA0E;UAA5BD,EAAA,CAAAkC,UAAA,mBAAA2I,qDAAA;YAAA,OAASF,GAAA,CAAA9B,eAAA,EAAiB;UAAA,EAAC;UACvE7I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,wCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAUMH,EAPZ,CAAAC,cAAA,eAA0B,eAEA,oBACqB,wBACvB,eACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,IAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAIzBF,EAJyB,CAAAG,YAAA,EAAI,EACjB,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAwC,wBACpB,eACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAI1BF,EAJ0B,CAAAG,YAAA,EAAI,EAClB,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAyC,wBACrB,eACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,IAAoD;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAIzBF,EAJyB,CAAAG,YAAA,EAAI,EACjB,EACF,EACW,EACV;UAMHH,EAJR,CAAAC,cAAA,oBAAwC,wBACpB,eACU,eACD,gBACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,IAAwD;;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjEH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sHAAoB;UAKjCF,EALiC,CAAAG,YAAA,EAAI,EACvB,EACF,EACW,EACV,EACP;UAOEH,EAJR,CAAAC,cAAA,oBAA+B,wBACX,eACS,0BACc,iBACxB;UAAAD,EAAA,CAAAE,MAAA,8FAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,iBAA6G;UAA7FD,EAAA,CAAA8K,gBAAA,2BAAAC,4DAAAC,MAAA;YAAAhL,EAAA,CAAAiL,kBAAA,CAAAN,GAAA,CAAAnG,UAAA,EAAAwG,MAAA,MAAAL,GAAA,CAAAnG,UAAA,GAAAwG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAAChL,EAAA,CAAAkC,UAAA,mBAAAgJ,oDAAA;YAAA,OAASP,GAAA,CAAA3C,QAAA,EAAU;UAAA,EAAC;UAA7DhI,EAAA,CAAAG,YAAA,EAA6G;UAC7GH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAGfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,sBAAoF;UAAxED,EAAA,CAAA8K,gBAAA,2BAAAK,iEAAAH,MAAA;YAAAhL,EAAA,CAAAiL,kBAAA,CAAAN,GAAA,CAAAlG,oBAAA,EAAAuG,MAAA,MAAAL,GAAA,CAAAlG,oBAAA,GAAAuG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAAChL,EAAA,CAAAkC,UAAA,6BAAAkJ,mEAAA;YAAA,OAAmBT,GAAA,CAAAzC,cAAA,EAAgB;UAAA,EAAC;UACjFlI,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAiB,UAAA,KAAAoK,yCAAA,yBAAiE;UAIrErL,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAC,cAAA,sBAA4E;UAAhED,EAAA,CAAA8K,gBAAA,2BAAAQ,iEAAAN,MAAA;YAAAhL,EAAA,CAAAiL,kBAAA,CAAAN,GAAA,CAAAjG,YAAA,EAAAsG,MAAA,MAAAL,GAAA,CAAAjG,YAAA,GAAAsG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAAChL,EAAA,CAAAkC,UAAA,6BAAAqJ,mEAAA;YAAA,OAAmBZ,GAAA,CAAAzC,cAAA,EAAgB;UAAA,EAAC;UACzElI,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAiB,UAAA,KAAAuK,yCAAA,yBAAyD;UAI7DxL,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,sBAA8E;UAAlED,EAAA,CAAA8K,gBAAA,2BAAAW,iEAAAT,MAAA;YAAAhL,EAAA,CAAAiL,kBAAA,CAAAN,GAAA,CAAAhG,cAAA,EAAAqG,MAAA,MAAAL,GAAA,CAAAhG,cAAA,GAAAqG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAAChL,EAAA,CAAAkC,UAAA,6BAAAwJ,mEAAA;YAAA,OAAmBf,GAAA,CAAAzC,cAAA,EAAgB;UAAA,EAAC;UAC3ElI,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC7CH,EAAA,CAAAiB,UAAA,KAAA0K,yCAAA,yBAAgE;UAIpE3L,EADE,CAAAG,YAAA,EAAa,EACE;UAEjBH,EAAA,CAAAC,cAAA,kBAA8E;UAAzBD,EAAA,CAAAkC,UAAA,mBAAA0J,qDAAA;YAAA,OAASjB,GAAA,CAAApC,YAAA,EAAc;UAAA,EAAC;UAC3EvI,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,wEACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA6B,wBACV,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAE,MAAA,oFACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,gBAA2B,mBACwD;UAAzDD,EAAA,CAAAkC,UAAA,mBAAA2J,sDAAA;YAAA,OAASlB,GAAA,CAAAnC,gBAAA,EAAkB;UAAA,EAAC;UAClDxI,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAGvBF,EAHuB,CAAAG,YAAA,EAAW,EACrB,EACL,EACU;UAIdH,EAFJ,CAAAC,cAAA,yBAAkB,gBACa,kBACgD;UAEzED,EAAA,CAAA8L,uBAAA,SAA0C;UAExC9L,EADA,CAAAiB,UAAA,MAAA8K,kCAAA,iBAAsD,MAAAC,kCAAA,iBACd;;UAM1ChM,EAAA,CAAA8L,uBAAA,SAAsC;UAEpC9L,EADA,CAAAiB,UAAA,MAAAgL,kCAAA,iBAAsD,MAAAC,kCAAA,iBACd;;UAS1ClM,EAAA,CAAA8L,uBAAA,SAA8C;UAE5C9L,EADA,CAAAiB,UAAA,MAAAkL,kCAAA,iBAAsC,MAAAC,kCAAA,iBACE;;UAQ1CpM,EAAA,CAAA8L,uBAAA,SAAyC;UAEvC9L,EADA,CAAAiB,UAAA,MAAAoL,kCAAA,iBAAsC,MAAAC,kCAAA,iBACE;;UAM1CtM,EAAA,CAAA8L,uBAAA,SAAsC;UAEpC9L,EADA,CAAAiB,UAAA,MAAAsL,kCAAA,iBAAsC,MAAAC,kCAAA,iBACE;;UAM1CxM,EAAA,CAAA8L,uBAAA,SAA4C;UAE1C9L,EADA,CAAAiB,UAAA,MAAAwL,kCAAA,iBAAsD,MAAAC,kCAAA,iBACd;;UAQ1C1M,EAAA,CAAA8L,uBAAA,SAAsC;UAEpC9L,EADA,CAAAiB,UAAA,MAAA0L,kCAAA,iBAAsC,MAAAC,kCAAA,iBACE;;UAQ1C5M,EAAA,CAAA8L,uBAAA,SAAqC;UAEnC9L,EADA,CAAAiB,UAAA,MAAA4L,kCAAA,iBAAsC,MAAAC,kCAAA,kBACE;;UA6B1C9M,EADA,CAAAiB,UAAA,MAAA8L,kCAAA,iBAAuD,MAAAC,kCAAA,iBACwD;UACjHhN,EAAA,CAAAG,YAAA,EAAQ;UAGRH,EAAA,CAAAiB,UAAA,MAAAgM,mCAAA,kBAA0D;UAQ5DjN,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAqD,SAAA,0BAA0F;UAGhGrD,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;UAGNH,EAAA,CAAAiB,UAAA,MAAAiM,mCAAA,kBAA+C;UAIjDlN,EAAA,CAAAG,YAAA,EAAM;;;UA7OYH,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAY,iBAAA,CAAA+J,GAAA,CAAA/F,cAAA,CAAoB;UAcpB5E,EAAA,CAAAO,SAAA,IAAqB;UAArBP,EAAA,CAAAY,iBAAA,CAAA+J,GAAA,CAAA9F,eAAA,CAAqB;UAcrB7E,EAAA,CAAAO,SAAA,IAAoD;UAApDP,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA+B,WAAA,SAAA4I,GAAA,CAAA7F,YAAA,4BAAoD;UAcpD9E,EAAA,CAAAO,SAAA,IAAwD;UAAxDP,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA+B,WAAA,SAAA4I,GAAA,CAAA5F,gBAAA,4BAAwD;UAc9C/E,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAAmN,gBAAA,YAAAxC,GAAA,CAAAnG,UAAA,CAAwB;UAM5BxE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAmN,gBAAA,YAAAxC,GAAA,CAAAlG,oBAAA,CAAkC;UAEfzE,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAAtG,aAAA,CAAgB;UAQnCrE,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAmN,gBAAA,YAAAxC,GAAA,CAAAjG,YAAA,CAA0B;UAEP1E,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAArG,KAAA,CAAQ;UAQ3BtE,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAmN,gBAAA,YAAAxC,GAAA,CAAAhG,cAAA,CAA4B;UAEP3E,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAAuK,GAAA,CAAApG,QAAA,CAAW;UA8B7BvE,EAAA,CAAAO,SAAA,IAAyB;UAAzBP,EAAA,CAAAI,UAAA,eAAAuK,GAAA,CAAAxG,UAAA,CAAyB;UAiGpBnE,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,oBAAAuK,GAAA,CAAAzG,gBAAA,CAAiC;UACpBlE,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,qBAAAuK,GAAA,CAAAzG,gBAAA,CAA0B;UAIvDlE,EAAA,CAAAO,SAAA,EAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAAxG,UAAA,CAAAoB,IAAA,CAAA8B,MAAA,OAAkC;UAW3BrH,EAAA,CAAAO,SAAA,EAAqC;UAArCP,EAAA,CAAAI,UAAA,oBAAAJ,EAAA,CAAAoN,eAAA,KAAAC,GAAA,EAAqC;UAMpDrN,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAuK,GAAA,CAAA3F,SAAA,CAAe;;;;;;;SD7MVpB,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}