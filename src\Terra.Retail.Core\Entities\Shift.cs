using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الشيفتات/الورديات
    /// </summary>
    public class Shift : BaseEntity
    {
        /// <summary>
        /// اسم الشيفت
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// كود الشيفت
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// وقت بداية الشيفت
        /// </summary>
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// وقت نهاية الشيفت
        /// </summary>
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// مدة الشيفت (بالساعات)
        /// </summary>
        public decimal Duration { get; set; }

        /// <summary>
        /// فترة السماح للتأخير (بالدقائق)
        /// </summary>
        public int LateGracePeriod { get; set; } = 0;

        /// <summary>
        /// فترة السماح للانصراف المبكر (بالدقائق)
        /// </summary>
        public int EarlyLeaveGracePeriod { get; set; } = 0;

        /// <summary>
        /// وقت بداية استراحة الغداء
        /// </summary>
        public TimeSpan? BreakStartTime { get; set; }

        /// <summary>
        /// وقت نهاية استراحة الغداء
        /// </summary>
        public TimeSpan? BreakEndTime { get; set; }

        /// <summary>
        /// مدة الاستراحة (بالدقائق)
        /// </summary>
        public int? BreakDuration { get; set; }

        /// <summary>
        /// هل الشيفت نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// أيام العمل (JSON array)
        /// </summary>
        [MaxLength(50)]
        public string WorkDays { get; set; } = "[1,2,3,4,5]"; // الأحد إلى الخميس

        /// <summary>
        /// نوع الشيفت
        /// </summary>
        public ShiftType ShiftType { get; set; } = ShiftType.Regular;

        /// <summary>
        /// معامل الإضافي
        /// </summary>
        public decimal OvertimeMultiplier { get; set; } = 1.5m;

        /// <summary>
        /// الحد الأدنى لساعات العمل
        /// </summary>
        public decimal MinWorkHours { get; set; } = 8;

        /// <summary>
        /// الحد الأقصى لساعات العمل
        /// </summary>
        public decimal MaxWorkHours { get; set; } = 12;

        /// <summary>
        /// لون الشيفت (للعرض في التقويم)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// وصف الشيفت
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        // Navigation Properties
        public virtual ICollection<Attendance> AttendanceRecords { get; set; } = new List<Attendance>();
        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
    }

    /// <summary>
    /// ربط الموظفين بالشيفتات
    /// </summary>
    public class EmployeeShift : BaseEntity
    {
        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// معرف الشيفت
        /// </summary>
        public int ShiftId { get; set; }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.Today;

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// هل التعيين نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ملاحظات
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual Shift Shift { get; set; } = null!;
    }

    /// <summary>
    /// أنواع الشيفتات
    /// </summary>
    public enum ShiftType
    {
        /// <summary>
        /// عادي
        /// </summary>
        Regular = 1,

        /// <summary>
        /// ليلي
        /// </summary>
        Night = 2,

        /// <summary>
        /// نهاية الأسبوع
        /// </summary>
        Weekend = 3,

        /// <summary>
        /// عطلة
        /// </summary>
        Holiday = 4,

        /// <summary>
        /// مرن
        /// </summary>
        Flexible = 5
    }
}
