using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Terra.Retail.Infrastructure.Data;

namespace Terra.Retail.API.Controllers
{
    /// <summary>
    /// تحكم في النظام العام
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class SystemController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;
        private readonly ILogger<SystemController> _logger;

        public SystemController(TerraRetailDbContext context, ILogger<SystemController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// فحص حالة النظام
        /// </summary>
        /// <returns>حالة النظام</returns>
        [HttpGet("health")]
        public async Task<ActionResult> GetSystemHealth()
        {
            try
            {
                // فحص الاتصال بقاعدة البيانات
                var canConnect = await _context.Database.CanConnectAsync();
                
                var health = new
                {
                    Status = canConnect ? "Healthy" : "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Version = "1.0.0",
                    System = "Terra Retail ERP",
                    Database = new
                    {
                        Connected = canConnect,
                        Provider = _context.Database.ProviderName,
                        ConnectionString = _context.Database.GetConnectionString()?.Split(';')[0] // إخفاء كلمة المرور
                    },
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"
                };

                if (canConnect)
                {
                    _logger.LogInformation("فحص حالة النظام: سليم");
                    return Ok(health);
                }
                else
                {
                    _logger.LogWarning("فحص حالة النظام: غير سليم - مشكلة في قاعدة البيانات");
                    return StatusCode(503, health);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص حالة النظام");
                return StatusCode(500, new { 
                    Status = "Error", 
                    Message = "حدث خطأ في فحص حالة النظام",
                    Error = ex.Message 
                });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات النظام العامة
        /// </summary>
        /// <returns>إحصائيات النظام</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult> GetSystemStatistics()
        {
            try
            {
                var statistics = new
                {
                    GeneratedAt = DateTime.UtcNow,
                    Branches = new
                    {
                        Total = await _context.Branches.CountAsync(),
                        Active = await _context.Branches.CountAsync(b => b.IsActive)
                    },
                    Customers = new
                    {
                        Total = await _context.Customers.CountAsync(),
                        Active = await _context.Customers.CountAsync(c => c.IsActive)
                    },
                    Products = new
                    {
                        Total = await _context.Products.CountAsync(),
                        Active = await _context.Products.CountAsync(p => p.IsActive)
                    },
                    Sales = new
                    {
                        Total = await _context.Sales.CountAsync(),
                        Today = await _context.Sales.CountAsync(s => s.InvoiceDate.Date == DateTime.Today),
                        ThisMonth = await _context.Sales.CountAsync(s =>
                            s.InvoiceDate.Year == DateTime.Now.Year &&
                            s.InvoiceDate.Month == DateTime.Now.Month)
                    },
                    Revenue = new
                    {
                        Today = 0, // await _context.Sales.Where(s => s.InvoiceDate.Date == DateTime.Today).SumAsync(s => (decimal?)s.TotalAmount) ?? 0,
                        ThisMonth = 0 // await _context.Sales.Where(s => s.InvoiceDate.Year == DateTime.Now.Year && s.InvoiceDate.Month == DateTime.Now.Month).SumAsync(s => (decimal?)s.TotalAmount) ?? 0
                    }
                };

                _logger.LogInformation("تم استرجاع إحصائيات النظام");
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع إحصائيات النظام");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع الإحصائيات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        /// <returns>معلومات قاعدة البيانات</returns>
        [HttpGet("database-info")]
        public async Task<ActionResult> GetDatabaseInfo()
        {
            try
            {
                var tables = await _context.Database.SqlQueryRaw<string>(
                    "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME")
                    .ToListAsync();

                var info = new
                {
                    DatabaseName = _context.Database.GetDbConnection().Database,
                    Provider = _context.Database.ProviderName,
                    TablesCount = tables.Count,
                    Tables = tables,
                    LastBackup = "غير متوفر", // يمكن إضافة استعلام للحصول على تاريخ آخر نسخة احتياطية
                    DatabaseSize = "غير متوفر" // يمكن إضافة استعلام للحصول على حجم قاعدة البيانات
                };

                _logger.LogInformation("تم استرجاع معلومات قاعدة البيانات");
                return Ok(info);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع معلومات قاعدة البيانات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع معلومات قاعدة البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على إعدادات النظام
        /// </summary>
        /// <returns>إعدادات النظام</returns>
        [HttpGet("settings")]
        public ActionResult GetSystemSettings()
        {
            try
            {
                var settings = new
                {
                    SystemName = "Terra Retail ERP",
                    Version = "1.0.0",
                    DefaultLanguage = "ar",
                    DefaultCurrency = "SAR",
                    DefaultTimeZone = "Asia/Riyadh",
                    SupportedLanguages = new[] { "ar", "en" },
                    SupportedCurrencies = new[] { "SAR", "USD", "EUR" },
                    Features = new
                    {
                        MultiLanguage = true,
                        MultiCurrency = true,
                        MultiBranch = true,
                        POS = true,
                        Inventory = true,
                        Accounting = true,
                        HRM = true,
                        Reports = true
                    },
                    Modules = new[]
                    {
                        new { Name = "المبيعات", NameEn = "Sales", Enabled = true },
                        new { Name = "المشتريات", NameEn = "Purchases", Enabled = true },
                        new { Name = "المخزون", NameEn = "Inventory", Enabled = true },
                        new { Name = "العملاء", NameEn = "Customers", Enabled = true },
                        new { Name = "الموردين", NameEn = "Suppliers", Enabled = true },
                        new { Name = "الموظفين", NameEn = "Employees", Enabled = true },
                        new { Name = "المحاسبة", NameEn = "Accounting", Enabled = true },
                        new { Name = "التقارير", NameEn = "Reports", Enabled = true }
                    }
                };

                _logger.LogInformation("تم استرجاع إعدادات النظام");
                return Ok(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع إعدادات النظام");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع الإعدادات", error = ex.Message });
            }
        }

        /// <summary>
        /// تهيئة النظام بالبيانات الأولية
        /// </summary>
        /// <returns>نتيجة التهيئة</returns>
        [HttpPost("initialize")]
        public async Task<ActionResult> InitializeSystem()
        {
            try
            {
                var initialized = false;
                var messages = new List<string>();

                // التحقق من وجود فرع رئيسي
                var mainBranch = await _context.Branches.FirstOrDefaultAsync(b => b.IsMainBranch);
                if (mainBranch == null)
                {
                    var branch = new Core.Entities.Branch
                    {
                        NameAr = "الفرع الرئيسي",
                        NameEn = "Main Branch",
                        Code = "MAIN",
                        IsActive = true,
                        IsMainBranch = true,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.Branches.Add(branch);
                    messages.Add("تم إنشاء الفرع الرئيسي");
                    initialized = true;
                }

                // التحقق من وجود أنواع العملاء
                var customerTypesCount = await _context.CustomerTypes.CountAsync();
                if (customerTypesCount == 0)
                {
                    var customerTypes = new[]
                    {
                        new Core.Entities.CustomerType { NameAr = "عميل عادي", NameEn = "Regular Customer", DefaultDiscountPercentage = 0, IsActive = true, CreatedAt = DateTime.UtcNow },
                        new Core.Entities.CustomerType { NameAr = "عميل جملة", NameEn = "Wholesale Customer", DefaultDiscountPercentage = 5, IsActive = true, CreatedAt = DateTime.UtcNow },
                        new Core.Entities.CustomerType { NameAr = "عميل VIP", NameEn = "VIP Customer", DefaultDiscountPercentage = 10, IsActive = true, CreatedAt = DateTime.UtcNow }
                    };
                    _context.CustomerTypes.AddRange(customerTypes);
                    messages.Add("تم إنشاء أنواع العملاء");
                    initialized = true;
                }

                // التحقق من وجود فئات الأسعار
                var priceCategoriesCount = await _context.PriceCategories.CountAsync();
                if (priceCategoriesCount == 0)
                {
                    var priceCategories = new[]
                    {
                        new Core.Entities.PriceCategory { NameAr = "سعر التجزئة", NameEn = "Retail Price", Code = "RETAIL", IsDefault = true, IsActive = true, CreatedAt = DateTime.UtcNow },
                        new Core.Entities.PriceCategory { NameAr = "سعر الجملة", NameEn = "Wholesale Price", Code = "WHOLESALE", PriceAdjustmentPercentage = -10, IsActive = true, CreatedAt = DateTime.UtcNow },
                        new Core.Entities.PriceCategory { NameAr = "سعر VIP", NameEn = "VIP Price", Code = "VIP", PriceAdjustmentPercentage = -15, IsActive = true, CreatedAt = DateTime.UtcNow }
                    };
                    _context.PriceCategories.AddRange(priceCategories);
                    messages.Add("تم إنشاء فئات الأسعار");
                    initialized = true;
                }

                if (initialized)
                {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("تم تهيئة النظام بالبيانات الأولية");
                    return Ok(new { message = "تم تهيئة النظام بنجاح", details = messages });
                }
                else
                {
                    return Ok(new { message = "النظام مهيأ بالفعل" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة النظام");
                return StatusCode(500, new { message = "حدث خطأ في تهيئة النظام", error = ex.Message });
            }
        }
    }
}
