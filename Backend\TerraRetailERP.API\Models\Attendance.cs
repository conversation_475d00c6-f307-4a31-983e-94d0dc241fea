using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("BiometricDevices")]
    public class BiometricDevice
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string DeviceName { get; set; } = string.Empty;

        [Required]
        [StringLength(15)]
        public string IPAddress { get; set; } = string.Empty;

        public int Port { get; set; } = 4370;

        [Required]
        [StringLength(50)]
        public string DeviceType { get; set; } = string.Empty;

        [StringLength(100)]
        public string? SerialNumber { get; set; }

        public int BranchId { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime? LastSync { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<AttendanceRecord> CheckInRecords { get; set; } = new List<AttendanceRecord>();
        public virtual ICollection<AttendanceRecord> CheckOutRecords { get; set; } = new List<AttendanceRecord>();
    }

    [Table("AttendanceRecords")]
    public class AttendanceRecord
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime ShiftDate { get; set; }

        // Actual Times
        public DateTime? ActualCheckInTime { get; set; }
        public DateTime? ActualCheckOutTime { get; set; }

        // Planned Times
        public DateTime PlannedCheckInTime { get; set; }
        public DateTime PlannedCheckOutTime { get; set; }

        // Calculations
        public int WorkingMinutes { get; set; } = 0;
        public int LateMinutes { get; set; } = 0;
        public int EarlyLeaveMinutes { get; set; } = 0;
        public int OvertimeMinutes { get; set; } = 0;
        public int BreakMinutes { get; set; } = 0;

        // Status
        [Required]
        [StringLength(20)]
        public string AttendanceStatus { get; set; } = "Absent"; // Present, Absent, Late, etc.

        public bool IsComplete { get; set; } = false;
        public bool IsOvernightShift { get; set; } = false;

        // Biometric Info
        public int? CheckInDeviceId { get; set; }
        public int? CheckOutDeviceId { get; set; }
        public bool IsManualEntry { get; set; } = false;

        [StringLength(200)]
        public string? ManualEntryReason { get; set; }

        public int? ApprovedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModified { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; } = null!;

        [ForeignKey("CheckInDeviceId")]
        public virtual BiometricDevice? CheckInDevice { get; set; }

        [ForeignKey("CheckOutDeviceId")]
        public virtual BiometricDevice? CheckOutDevice { get; set; }

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }
    }
}
