using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("⏰ Attendance Management")]
    public class AttendanceController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public AttendanceController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult> GetAttendanceRecords(
            [FromQuery] int? employeeId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? attendanceStatus = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddDays(-30);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.AttendanceRecords
                    .Include(ar => ar.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(ar => ar.Employee)
                        .ThenInclude(e => e.Branch)
                    .Include(ar => ar.Shift)
                    .Where(ar => ar.ShiftDate >= startDate && ar.ShiftDate <= endDate);

                // Apply filters
                if (employeeId.HasValue)
                    query = query.Where(ar => ar.EmployeeId == employeeId);

                if (branchId.HasValue)
                    query = query.Where(ar => ar.Employee.BranchId == branchId);

                if (departmentId.HasValue)
                    query = query.Where(ar => ar.Employee.DepartmentId == departmentId);

                if (!string.IsNullOrEmpty(attendanceStatus))
                    query = query.Where(ar => ar.AttendanceStatus == attendanceStatus);

                var totalCount = await query.CountAsync();
                var records = await query
                    .OrderByDescending(ar => ar.ShiftDate)
                    .ThenBy(ar => ar.Employee.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(ar => new
                    {
                        ar.Id,
                        ar.EmployeeId,
                        EmployeeName = ar.Employee.NameAr,
                        EmployeeCode = ar.Employee.EmployeeCode,
                        DepartmentName = ar.Employee.Department != null ? ar.Employee.Department.NameAr : "غير محدد",
                        BranchName = ar.Employee.Branch.NameAr,
                        ar.ShiftDate,
                        ShiftName = ar.Shift != null ? ar.Shift.ShiftName : "غير محدد",
                        ar.PlannedCheckInTime,
                        ar.PlannedCheckOutTime,
                        ar.ActualCheckInTime,
                        ar.ActualCheckOutTime,
                        ar.WorkingMinutes,
                        ar.LateMinutes,
                        ar.EarlyLeaveMinutes,
                        ar.OvertimeMinutes,
                        ar.AttendanceStatus,
                        ar.IsComplete,
                        ar.IsManualEntry,
                        ar.Notes,
                        ar.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = records,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("check-in")]
        public async Task<ActionResult> CheckIn(CheckInRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var employee = await _context.Employees
                    .Include(e => e.EmployeeShifts)
                        .ThenInclude(es => es.Shift)
                    .FirstOrDefaultAsync(e => e.BiometricId == request.BiometricId || e.Id == request.EmployeeId);

                if (employee == null)
                    return NotFound(new { message = "الموظف غير موجود" });

                var today = DateTime.Today;
                var checkInTime = request.CheckInTime ?? DateTime.Now;

                // Check if already checked in today
                var existingRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.EmployeeId == employee.Id && ar.ShiftDate.Date == today);

                if (existingRecord != null && existingRecord.ActualCheckInTime.HasValue)
                    return BadRequest(new { message = "تم تسجيل الحضور مسبقاً لهذا اليوم" });

                // Get employee's shift for today
                var dayOfWeek = (int)today.DayOfWeek;
                var employeeShift = employee.EmployeeShifts
                    .FirstOrDefault(es => es.IsActive && 
                                        (es.DaysOfWeek == null || es.DaysOfWeek.Contains(dayOfWeek.ToString())));

                if (employeeShift == null)
                    return BadRequest(new { message = "لا توجد وردية محددة للموظف في هذا اليوم" });

                var shift = employeeShift.Shift;
                var plannedCheckIn = today.Add(shift.StartTime);
                var plannedCheckOut = today.Add(shift.EndTime);

                // If shift ends next day
                if (shift.EndTime < shift.StartTime)
                    plannedCheckOut = plannedCheckOut.AddDays(1);

                // Calculate late minutes
                var lateMinutes = checkInTime > plannedCheckIn ? 
                    (int)(checkInTime - plannedCheckIn).TotalMinutes : 0;

                if (existingRecord == null)
                {
                    // Create new attendance record
                    var attendanceRecord = new AttendanceRecord
                    {
                        EmployeeId = employee.Id,
                        ShiftId = shift.Id,
                        ShiftDate = today,
                        PlannedCheckInTime = plannedCheckIn,
                        PlannedCheckOutTime = plannedCheckOut,
                        ActualCheckInTime = checkInTime,
                        LateMinutes = lateMinutes,
                        AttendanceStatus = "Present",
                        IsComplete = false,
                        IsManualEntry = request.IsManualEntry,
                        Notes = request.Notes,
                        CreatedAt = DateTime.Now
                    };

                    _context.AttendanceRecords.Add(attendanceRecord);
                }
                else
                {
                    // Update existing record
                    existingRecord.ActualCheckInTime = checkInTime;
                    existingRecord.LateMinutes = lateMinutes;
                    existingRecord.AttendanceStatus = "Present";
                    existingRecord.IsManualEntry = request.IsManualEntry;
                    existingRecord.Notes = request.Notes;
                    existingRecord.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    message = "تم تسجيل الحضور بنجاح",
                    employeeName = employee.NameAr,
                    checkInTime = checkInTime,
                    lateMinutes = lateMinutes
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("check-out")]
        public async Task<ActionResult> CheckOut(CheckOutRequest request)
        {
            try
            {
                var employee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.BiometricId == request.BiometricId || e.Id == request.EmployeeId);

                if (employee == null)
                    return NotFound(new { message = "الموظف غير موجود" });

                var today = DateTime.Today;
                var checkOutTime = request.CheckOutTime ?? DateTime.Now;

                // Find today's attendance record
                var attendanceRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.EmployeeId == employee.Id && ar.ShiftDate.Date == today);

                if (attendanceRecord == null || !attendanceRecord.ActualCheckInTime.HasValue)
                    return BadRequest(new { message = "لم يتم تسجيل الحضور لهذا اليوم" });

                if (attendanceRecord.ActualCheckOutTime.HasValue)
                    return BadRequest(new { message = "تم تسجيل الانصراف مسبقاً لهذا اليوم" });

                // Calculate working time and overtime
                var workingMinutes = (int)(checkOutTime - attendanceRecord.ActualCheckInTime.Value).TotalMinutes;
                var plannedWorkingMinutes = (int)(attendanceRecord.PlannedCheckOutTime - attendanceRecord.PlannedCheckInTime).TotalMinutes;
                
                var earlyLeaveMinutes = checkOutTime < attendanceRecord.PlannedCheckOutTime ? 
                    (int)(attendanceRecord.PlannedCheckOutTime - checkOutTime).TotalMinutes : 0;
                
                var overtimeMinutes = checkOutTime > attendanceRecord.PlannedCheckOutTime ? 
                    (int)(checkOutTime - attendanceRecord.PlannedCheckOutTime).TotalMinutes : 0;

                // Update attendance record
                attendanceRecord.ActualCheckOutTime = checkOutTime;
                attendanceRecord.WorkingMinutes = workingMinutes;
                attendanceRecord.EarlyLeaveMinutes = earlyLeaveMinutes;
                attendanceRecord.OvertimeMinutes = overtimeMinutes;
                attendanceRecord.IsComplete = true;
                attendanceRecord.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new 
                { 
                    message = "تم تسجيل الانصراف بنجاح",
                    employeeName = employee.NameAr,
                    checkOutTime = checkOutTime,
                    workingHours = Math.Round(workingMinutes / 60.0, 2),
                    overtimeHours = Math.Round(overtimeMinutes / 60.0, 2)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("shifts")]
        public async Task<ActionResult> GetShifts([FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Shifts.AsQueryable();

                if (isActive.HasValue)
                    query = query.Where(s => s.IsActive == isActive);

                var shifts = await query
                    .OrderBy(s => s.StartTime)
                    .ToListAsync();

                return Ok(shifts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("shifts")]
        public async Task<ActionResult> CreateShift(CreateShiftRequest request)
        {
            try
            {
                var shift = new Shift
                {
                    ShiftName = request.ShiftName,
                    StartTime = request.StartTime,
                    EndTime = request.EndTime,
                    BreakDuration = request.BreakDuration,
                    GracePeriodMinutes = request.GracePeriodMinutes,
                    MaxOvertimeHours = request.MaxOvertimeHours,
                    IsFlexible = request.IsFlexible,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Shifts.Add(shift);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetShifts), new { id = shift.Id }, shift);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetAttendanceDashboard(
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                var query = _context.AttendanceRecords
                    .Include(ar => ar.Employee)
                    .Where(ar => ar.ShiftDate.Date == targetDate);

                if (branchId.HasValue)
                    query = query.Where(ar => ar.Employee.BranchId == branchId);

                var attendanceData = await query.ToListAsync();

                var summary = new
                {
                    TotalEmployees = attendanceData.Count,
                    PresentEmployees = attendanceData.Count(ar => ar.AttendanceStatus == "Present"),
                    AbsentEmployees = attendanceData.Count(ar => ar.AttendanceStatus == "Absent"),
                    LateEmployees = attendanceData.Count(ar => ar.LateMinutes > 0),
                    OnTimeEmployees = attendanceData.Count(ar => ar.AttendanceStatus == "Present" && ar.LateMinutes == 0),
                    TotalWorkingHours = attendanceData.Sum(ar => ar.WorkingMinutes) / 60.0,
                    TotalOvertimeHours = attendanceData.Sum(ar => ar.OvertimeMinutes) / 60.0,
                    AttendancePercentage = attendanceData.Count > 0 ? 
                        (attendanceData.Count(ar => ar.AttendanceStatus == "Present") * 100.0 / attendanceData.Count) : 0
                };

                // Department wise summary
                var departmentSummary = await query
                    .GroupBy(ar => new { ar.Employee.DepartmentId, ar.Employee.Department!.NameAr })
                    .Select(g => new
                    {
                        DepartmentId = g.Key.DepartmentId,
                        DepartmentName = g.Key.NameAr ?? "غير محدد",
                        TotalEmployees = g.Count(),
                        PresentEmployees = g.Count(ar => ar.AttendanceStatus == "Present"),
                        AttendancePercentage = g.Count() > 0 ? 
                            (g.Count(ar => ar.AttendanceStatus == "Present") * 100.0 / g.Count()) : 0
                    })
                    .ToListAsync();

                return Ok(new
                {
                    summary,
                    departmentSummary,
                    date = targetDate
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class CheckInRequest
    {
        public int? EmployeeId { get; set; }
        public string? BiometricId { get; set; }
        public DateTime? CheckInTime { get; set; }
        public bool IsManualEntry { get; set; } = false;
        public string? Notes { get; set; }
    }

    public class CheckOutRequest
    {
        public int? EmployeeId { get; set; }
        public string? BiometricId { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateShiftRequest
    {
        public string ShiftName { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public int BreakDuration { get; set; } = 0;
        public int GracePeriodMinutes { get; set; } = 0;
        public decimal MaxOvertimeHours { get; set; } = 0;
        public bool IsFlexible { get; set; } = false;
    }
}
