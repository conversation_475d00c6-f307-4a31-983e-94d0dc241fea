using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable("Users");

            builder.HasKey(u => u.Id);

            builder.Property(u => u.Username)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(u => u.Email)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(u => u.PasswordHash)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(u => u.FullName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(u => u.PhoneNumber)
                .HasMaxLength(20);

            builder.Property(u => u.ProfileImage)
                .HasMaxLength(500);

            builder.Property(u => u.LastLoginIP)
                .HasMaxLength(45);

            builder.Property(u => u.PasswordResetToken)
                .HasMaxLength(255);

            builder.Property(u => u.EmailConfirmationToken)
                .HasMaxLength(255);

            builder.Property(u => u.PreferredLanguage)
                .HasMaxLength(5)
                .HasDefaultValue("ar");

            builder.Property(u => u.TimeZone)
                .HasMaxLength(50)
                .HasDefaultValue("Asia/Riyadh");

            builder.Property(u => u.Settings)
                .HasMaxLength(2000);

            // Relationships
            // builder.HasOne(u => u.Employee)
            //     .WithOne(e => e.User)
            //     .HasForeignKey<User>(u => u.EmployeeId)
            //     .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(u => u.DefaultBranch)
                .WithMany(b => b.Users)
                .HasForeignKey(u => u.DefaultBranchId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(u => u.Username)
                .IsUnique()
                .HasDatabaseName("IX_Users_Username");

            builder.HasIndex(u => u.Email)
                .IsUnique()
                .HasDatabaseName("IX_Users_Email");

            builder.HasIndex(u => u.IsActive)
                .HasDatabaseName("IX_Users_IsActive");

            builder.HasIndex(u => u.EmployeeId)
                .HasDatabaseName("IX_Users_EmployeeId");
        }
    }

    public class RoleConfiguration : IEntityTypeConfiguration<Role>
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.ToTable("Roles");

            builder.HasKey(r => r.Id);

            builder.Property(r => r.NameAr)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.NameEn)
                .HasMaxLength(50);

            builder.Property(r => r.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.Description)
                .HasMaxLength(500);

            builder.Property(r => r.Color)
                .HasMaxLength(7);

            builder.Property(r => r.Icon)
                .HasMaxLength(50);

            // Indexes
            builder.HasIndex(r => r.Code)
                .IsUnique()
                .HasDatabaseName("IX_Roles_Code");

            builder.HasIndex(r => r.IsActive)
                .HasDatabaseName("IX_Roles_IsActive");

            builder.HasIndex(r => r.Level)
                .HasDatabaseName("IX_Roles_Level");
        }
    }

    public class PermissionConfiguration : IEntityTypeConfiguration<Permission>
    {
        public void Configure(EntityTypeBuilder<Permission> builder)
        {
            builder.ToTable("Permissions");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.NameAr)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.NameEn)
                .HasMaxLength(100);

            builder.Property(p => p.Code)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.Description)
                .HasMaxLength(500);

            builder.Property(p => p.Group)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(p => p.Icon)
                .HasMaxLength(50);

            builder.Property(p => p.Color)
                .HasMaxLength(7);

            builder.Property(p => p.Path)
                .HasMaxLength(200);

            builder.Property(p => p.HttpMethod)
                .HasMaxLength(10);

            // Self-referencing relationship
            builder.HasOne(p => p.ParentPermission)
                .WithMany(p => p.SubPermissions)
                .HasForeignKey(p => p.ParentPermissionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(p => p.Code)
                .IsUnique()
                .HasDatabaseName("IX_Permissions_Code");

            builder.HasIndex(p => p.Group)
                .HasDatabaseName("IX_Permissions_Group");

            builder.HasIndex(p => p.PermissionType)
                .HasDatabaseName("IX_Permissions_PermissionType");

            builder.HasIndex(p => new { p.ParentPermissionId, p.DisplayOrder })
                .HasDatabaseName("IX_Permissions_ParentPermissionId_DisplayOrder");
        }
    }

    public class UserRoleConfiguration : IEntityTypeConfiguration<UserRole>
    {
        public void Configure(EntityTypeBuilder<UserRole> builder)
        {
            builder.ToTable("UserRoles");

            builder.HasKey(ur => ur.Id);

            builder.Property(ur => ur.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ur => ur.GrantedBy)
                .WithMany()
                .HasForeignKey(ur => ur.GrantedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ur => new { ur.UserId, ur.RoleId })
                .IsUnique()
                .HasDatabaseName("IX_UserRoles_UserId_RoleId");

            builder.HasIndex(ur => ur.IsActive)
                .HasDatabaseName("IX_UserRoles_IsActive");
        }
    }

    public class RolePermissionConfiguration : IEntityTypeConfiguration<RolePermission>
    {
        public void Configure(EntityTypeBuilder<RolePermission> builder)
        {
            builder.ToTable("RolePermissions");

            builder.HasKey(rp => rp.Id);

            builder.Property(rp => rp.Constraints)
                .HasMaxLength(1000);

            builder.Property(rp => rp.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(rp => rp.GrantedBy)
                .WithMany()
                .HasForeignKey(rp => rp.GrantedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(rp => new { rp.RoleId, rp.PermissionId })
                .IsUnique()
                .HasDatabaseName("IX_RolePermissions_RoleId_PermissionId");

            builder.HasIndex(rp => rp.IsGranted)
                .HasDatabaseName("IX_RolePermissions_IsGranted");
        }
    }

    public class UserBranchConfiguration : IEntityTypeConfiguration<UserBranch>
    {
        public void Configure(EntityTypeBuilder<UserBranch> builder)
        {
            builder.ToTable("UserBranches");

            builder.HasKey(ub => ub.Id);

            builder.Property(ub => ub.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(ub => ub.User)
                .WithMany(u => u.UserBranches)
                .HasForeignKey(ub => ub.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ub => ub.Branch)
                .WithMany()
                .HasForeignKey(ub => ub.BranchId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ub => ub.GrantedBy)
                .WithMany()
                .HasForeignKey(ub => ub.GrantedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ub => new { ub.UserId, ub.BranchId })
                .IsUnique()
                .HasDatabaseName("IX_UserBranches_UserId_BranchId");

            builder.HasIndex(ub => ub.HasAccess)
                .HasDatabaseName("IX_UserBranches_HasAccess");
        }
    }

    public class UserSessionConfiguration : IEntityTypeConfiguration<UserSession>
    {
        public void Configure(EntityTypeBuilder<UserSession> builder)
        {
            builder.ToTable("UserSessions");

            builder.HasKey(us => us.Id);

            builder.Property(us => us.SessionToken)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(us => us.RefreshToken)
                .HasMaxLength(255);

            builder.Property(us => us.IPAddress)
                .HasMaxLength(45);

            builder.Property(us => us.UserAgent)
                .HasMaxLength(500);

            builder.Property(us => us.DeviceType)
                .HasMaxLength(50);

            builder.Property(us => us.OperatingSystem)
                .HasMaxLength(100);

            builder.Property(us => us.Browser)
                .HasMaxLength(100);

            builder.Property(us => us.Location)
                .HasMaxLength(200);

            // Relationships
            builder.HasOne(us => us.User)
                .WithMany(u => u.Sessions)
                .HasForeignKey(us => us.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(us => us.Branch)
                .WithMany()
                .HasForeignKey(us => us.BranchId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(us => us.SessionToken)
                .IsUnique()
                .HasDatabaseName("IX_UserSessions_SessionToken");

            builder.HasIndex(us => new { us.UserId, us.IsActive })
                .HasDatabaseName("IX_UserSessions_UserId_IsActive");

            builder.HasIndex(us => us.ExpiryTime)
                .HasDatabaseName("IX_UserSessions_ExpiryTime");
        }
    }

    public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
    {
        public void Configure(EntityTypeBuilder<AuditLog> builder)
        {
            builder.ToTable("AuditLogs");

            builder.HasKey(al => al.Id);

            builder.Property(al => al.Username)
                .HasMaxLength(100);

            builder.Property(al => al.TableName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(al => al.ChangedFields)
                .HasMaxLength(1000);

            builder.Property(al => al.Description)
                .HasMaxLength(500);

            builder.Property(al => al.IPAddress)
                .HasMaxLength(45);

            builder.Property(al => al.UserAgent)
                .HasMaxLength(500);

            builder.Property(al => al.SessionId)
                .HasMaxLength(255);

            builder.Property(al => al.ErrorMessage)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(al => al.User)
                .WithMany()
                .HasForeignKey(al => al.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(al => al.Branch)
                .WithMany()
                .HasForeignKey(al => al.BranchId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(al => new { al.TableName, al.RecordId })
                .HasDatabaseName("IX_AuditLogs_TableName_RecordId");

            builder.HasIndex(al => al.Timestamp)
                .HasDatabaseName("IX_AuditLogs_Timestamp");

            builder.HasIndex(al => al.Action)
                .HasDatabaseName("IX_AuditLogs_Action");

            builder.HasIndex(al => new { al.UserId, al.Timestamp })
                .HasDatabaseName("IX_AuditLogs_UserId_Timestamp");
        }
    }
}
