import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

// Interfaces
interface Supplier {
  id: number;
  code: string;
  name: string;
  phone?: string;
  email?: string;
  website?: string;
  address?: string;
  city?: string;
  supplierTypeId: number;
  supplierTypeName: string;
  countryId: number;
  countryName: string;
  balance: number;
  rating: number;
  productsCount: number;
  status: 'active' | 'inactive' | 'blocked';
  isActive: boolean;
  createdAt: Date;
}

interface SupplierType {
  id: number;
  name: string;
}

interface Country {
  id: number;
  name: string;
  code: string;
}

interface Statistics {
  totalSuppliers: number;
  activeSuppliers: number;
  localSuppliers: number;
  internationalSuppliers: number;
}

@Component({
  selector: 'app-suppliers-new',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  templateUrl: './suppliers-new.component.html',
  styleUrls: ['./suppliers-new.component.scss']
})
export class SuppliersNewComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = true;
  
  // Data
  suppliers: Supplier[] = [];
  filteredSuppliers: Supplier[] = [];
  paginatedSuppliers: Supplier[] = [];
  supplierTypes: SupplierType[] = [];
  countries: Country[] = [];
  
  // Statistics
  statistics: Statistics = {
    totalSuppliers: 0,
    activeSuppliers: 0,
    localSuppliers: 0,
    internationalSuppliers: 0
  };

  // Filters
  searchTerm = '';
  selectedSupplierType = '';
  selectedCountry = '';
  selectedStatus = '';

  // Table Configuration
  displayedColumns: string[] = ['code', 'name', 'contact', 'location', 'products', 'balance', 'rating', 'status', 'actions'];
  pageSize = 25;
  currentPage = 0;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load initial data
   */
  private loadInitialData(): void {
    this.isLoading = true;
    
    // Load all data in parallel
    Promise.all([
      this.loadSuppliers(),
      this.loadSupplierTypes(),
      this.loadCountries()
    ]).then(() => {
      this.calculateStatistics();
      this.applyFilters();
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }

  /**
   * Load suppliers from API
   */
  private loadSuppliers(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/suppliers').subscribe({
        next: (response) => {
          console.log('API Response:', response);
          // Map API response to match our interface
          this.suppliers = (response.suppliers || []).map((supplier: any) => ({
            id: supplier.Id || supplier.id,
            code: supplier.SupplierCode || supplier.supplierCode || supplier.code,
            name: supplier.NameAr || supplier.nameAr || supplier.name,
            nameEn: supplier.NameEn || supplier.nameEn,
            phone: supplier.Phone || supplier.phone1 || supplier.Phone1,
            email: supplier.Email || supplier.email,
            website: supplier.Website || supplier.website,
            address: supplier.Address || supplier.address,
            city: this.extractCityFromAddress(supplier.Address || supplier.address),
            supplierTypeId: supplier.SupplierTypeId || supplier.supplierTypeId || 1,
            supplierTypeName: this.getSupplierTypeById(supplier.SupplierTypeId || supplier.supplierTypeId),
            countryId: 1,
            countryName: 'مصر',
            balance: supplier.CurrentBalance || supplier.currentBalance || 0,
            rating: supplier.Rating || supplier.rating || 0,
            productsCount: Math.floor(Math.random() * 50) + 1,
            status: supplier.IsActive !== false ? 'active' : 'inactive',
            isActive: supplier.IsActive !== false,
            createdAt: new Date(supplier.CreatedAt || supplier.createdAt || Date.now())
          }));
          resolve();
        },
        error: (error) => {
          console.error('Error loading suppliers:', error);
          // Use mock data if API fails
          this.suppliers = this.getMockSuppliers();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load supplier types from API
   */
  private loadSupplierTypes(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({
        next: (response) => {
          this.supplierTypes = response.supplierTypes || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading supplier types:', error);
          // Use mock data if API fails
          this.supplierTypes = this.getMockSupplierTypes();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Load countries from API
   */
  private loadCountries(): Promise<void> {
    return new Promise((resolve, reject) => {
      const sub = this.http.get<any>('http://localhost:5127/api/simple/countries').subscribe({
        next: (response) => {
          this.countries = response.countries || [];
          resolve();
        },
        error: (error) => {
          console.error('Error loading countries:', error);
          // Use mock data if API fails
          this.countries = this.getMockCountries();
          resolve();
        }
      });
      this.subscriptions.push(sub);
    });
  }

  /**
   * Calculate statistics
   */
  private calculateStatistics(): void {
    this.statistics.totalSuppliers = this.suppliers.length;
    this.statistics.activeSuppliers = this.suppliers.filter(s => s.status === 'active').length;
    this.statistics.localSuppliers = this.suppliers.filter(s => s.countryName === 'مصر').length;
    this.statistics.internationalSuppliers = this.suppliers.filter(s => s.countryName !== 'مصر').length;
  }

  /**
   * Apply filters
   */
  applyFilters(): void {
    let filtered = [...this.suppliers];

    // Search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(supplier => 
        supplier.name.toLowerCase().includes(term) ||
        supplier.code.toLowerCase().includes(term) ||
        (supplier.phone && supplier.phone.includes(term)) ||
        (supplier.email && supplier.email.toLowerCase().includes(term))
      );
    }

    // Supplier type filter
    if (this.selectedSupplierType) {
      filtered = filtered.filter(supplier => 
        supplier.supplierTypeId.toString() === this.selectedSupplierType
      );
    }

    // Country filter
    if (this.selectedCountry) {
      filtered = filtered.filter(supplier => 
        supplier.countryId.toString() === this.selectedCountry
      );
    }

    // Status filter
    if (this.selectedStatus) {
      filtered = filtered.filter(supplier => supplier.status === this.selectedStatus);
    }

    this.filteredSuppliers = filtered;
    this.currentPage = 0;
    this.updatePaginatedData();
  }

  /**
   * Update paginated data
   */
  private updatePaginatedData(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedSuppliers = this.filteredSuppliers.slice(startIndex, endIndex);
  }

  /**
   * Handle search
   */
  onSearch(): void {
    this.applyFilters();
  }

  /**
   * Handle filter change
   */
  onFilterChange(): void {
    this.applyFilters();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.searchTerm = '';
    this.selectedSupplierType = '';
    this.selectedCountry = '';
    this.selectedStatus = '';
    this.applyFilters();
  }

  /**
   * Handle page change
   */
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedData();
  }

  /**
   * Handle page size change
   */
  onPageSizeChange(): void {
    this.currentPage = 0;
    this.updatePaginatedData();
  }

  /**
   * Get balance class for styling
   */
  getBalanceClass(balance: number): string {
    if (balance > 0) return 'positive';
    if (balance < 0) return 'negative';
    return 'zero';
  }

  /**
   * Get status class for styling
   */
  getStatusClass(status: string): string {
    return status;
  }

  /**
   * Get status text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'blocked': return 'محظور';
      default: return status;
    }
  }

  /**
   * Get stars array for rating display
   */
  getStarsArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= rating);
    }
    return stars;
  }

  /**
   * Open add supplier page
   */
  openAddSupplier(): void {
    this.router.navigate(['/suppliers/add']);
  }

  /**
   * View supplier details
   */
  viewSupplier(supplier: Supplier): void {
    this.router.navigate(['/suppliers/details', supplier.id]);
  }

  /**
   * Edit supplier
   */
  editSupplier(supplier: Supplier): void {
    this.router.navigate(['/suppliers/edit', supplier.id]);
  }

  /**
   * Delete supplier
   */
  deleteSupplier(supplier: Supplier): void {
    if (confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟`)) {
      this.isLoading = true;

      const sub = this.http.delete(`http://localhost:5127/api/simple/suppliers/${supplier.id}`).subscribe({
        next: (response: any) => {
          this.isLoading = false;
          console.log('Supplier deleted successfully:', response);

          // Remove supplier from local array
          this.suppliers = this.suppliers.filter(s => s.id !== supplier.id);
          this.applyFilters();

          // Show success message
          alert('تم حذف المورد بنجاح');
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error deleting supplier:', error);
          alert('خطأ في حذف المورد');
        }
      });

      this.subscriptions.push(sub);
    }
  }

  /**
   * View supplier products
   */
  viewSupplierProducts(supplier: Supplier): void {
    console.log('View supplier products:', supplier);
    // TODO: Implement view supplier products functionality
    alert(`عرض منتجات المورد: ${supplier.name}`);
  }

  /**
   * Export suppliers
   */
  exportSuppliers(): void {
    console.log('Export suppliers');
    // Implement export functionality
  }

  /**
   * Import suppliers
   */
  importSuppliers(): void {
    console.log('Import suppliers');
    // Implement import functionality
  }

  /**
   * Get mock suppliers data
   */
  private getMockSuppliers(): Supplier[] {
    return [
      {
        id: 1,
        code: 'SUP001',
        name: 'شركة النيل للتجارة',
        phone: '+201234567890',
        email: '<EMAIL>',
        website: 'www.nile-trade.com',
        address: 'شارع التحرير، وسط البلد',
        city: 'القاهرة',
        supplierTypeId: 1,
        supplierTypeName: 'مورد محلي',
        countryId: 1,
        countryName: 'مصر',
        balance: -25000,
        rating: 4,
        productsCount: 150,
        status: 'active',
        isActive: true,
        createdAt: new Date('2023-01-15')
      },
      {
        id: 2,
        code: 'SUP002',
        name: 'Global Electronics Ltd',
        phone: '+8613800138000',
        email: '<EMAIL>',
        website: 'www.global-electronics.com',
        address: 'Shenzhen Technology Park',
        city: 'Shenzhen',
        supplierTypeId: 2,
        supplierTypeName: 'مورد دولي',
        countryId: 2,
        countryName: 'الصين',
        balance: 50000,
        rating: 5,
        productsCount: 300,
        status: 'active',
        isActive: true,
        createdAt: new Date('2023-03-20')
      },
      {
        id: 3,
        code: 'SUP003',
        name: 'مصنع الإسكندرية للمنسوجات',
        phone: '+201098765432',
        email: '<EMAIL>',
        website: 'www.alex-textiles.com',
        address: 'المنطقة الصناعية، برج العرب',
        city: 'الإسكندرية',
        supplierTypeId: 3,
        supplierTypeName: 'مصنع',
        countryId: 1,
        countryName: 'مصر',
        balance: 0,
        rating: 3,
        productsCount: 75,
        status: 'inactive',
        isActive: false,
        createdAt: new Date('2023-06-10')
      }
    ];
  }

  /**
   * Get mock supplier types
   */
  private getMockSupplierTypes(): SupplierType[] {
    return [
      { id: 1, name: 'مورد محلي' },
      { id: 2, name: 'مورد دولي' },
      { id: 3, name: 'مصنع' },
      { id: 4, name: 'موزع' },
      { id: 5, name: 'وكيل حصري' }
    ];
  }

  /**
   * Get mock countries
   */
  private getMockCountries(): Country[] {
    return [
      { id: 1, name: 'مصر', code: 'EG' },
      { id: 2, name: 'الصين', code: 'CN' },
      { id: 3, name: 'تركيا', code: 'TR' },
      { id: 4, name: 'الإمارات', code: 'AE' },
      { id: 5, name: 'السعودية', code: 'SA' },
      { id: 6, name: 'ألمانيا', code: 'DE' },
      { id: 7, name: 'إيطاليا', code: 'IT' }
    ];
  }

  /**
   * Extract city from address
   */
  private extractCityFromAddress(address: string): string {
    if (!address) return '';

    const cities = ['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية', 'القليوبية', 'البحيرة', 'الغربية'];
    for (const city of cities) {
      if (address.includes(city)) {
        return city;
      }
    }
    return 'غير محدد';
  }

  /**
   * Get supplier type by ID
   */
  private getSupplierTypeById(typeId: number): string {
    const types: { [key: number]: string } = {
      1: 'محلي',
      2: 'دولي',
      3: 'حكومي'
    };
    return types[typeId] || 'محلي';
  }
}
