/* Terra Retail ERP - Professional Layout Styles */

.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);

  &.sidebar-collapsed {
    .sidebar {
      width: 70px;
    }

    .main-content {
      margin-right: 70px;
    }
  }
}

/* ===== TOP NAVBAR ===== */
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-xl);
  max-width: 100%;
  box-sizing: border-box;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.sidebar-toggle {
  color: var(--gray-600) !important;
  transition: all var(--transition-fast) !important;

  &:hover {
    color: var(--primary-600) !important;
    background: var(--primary-50) !important;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  .logo {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
  }

  .logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-700);
    background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.navbar-center {
  flex: 1;
  max-width: 500px;
  margin: 0 var(--spacing-xl);
}

.global-search {
  .search-field {
    width: 100%;
    
    .mat-mdc-text-field-wrapper {
      background: rgba(255, 255, 255, 0.8) !important;
      border: 1px solid var(--gray-200) !important;
      
      &:hover {
        background: white !important;
        border-color: var(--primary-300) !important;
      }
    }
  }
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notification-btn {
  color: var(--gray-600) !important;
  position: relative;

  &:hover {
    color: var(--primary-600) !important;
    background: var(--primary-50) !important;
  }
}

.user-profile {
  display: flex !important;
  align-items: center !important;
  gap: var(--spacing-sm) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-lg) !important;
  transition: all var(--transition-fast) !important;

  &:hover {
    background: var(--gray-50) !important;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--primary-200);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .user-name {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-800);
      line-height: 1.2;
    }

    .user-role {
      font-size: 0.75rem;
      color: var(--gray-500);
      line-height: 1.2;
    }
  }
}

.settings-btn {
  color: var(--gray-600) !important;

  &:hover {
    color: var(--primary-600) !important;
    background: var(--primary-50) !important;
  }
}

/* ===== SIDEBAR ===== */
.sidebar {
  position: fixed;
  top: 70px;
  right: 0;
  width: 280px;
  height: calc(100vh - 70px);
  background: linear-gradient(180deg, var(--primary-900) 0%, var(--primary-800) 50%, var(--secondary-800) 100%);
  color: white;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all var(--transition-normal);
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 999;

  &.collapsed {
    width: 70px;

    .nav-section-title,
    .nav-submenu,
    .sidebar-footer {
      display: none;
    }

    .nav-item {
      justify-content: center;
      padding: var(--spacing-md);

      span {
        display: none;
      }

      .expand-icon {
        display: none;
      }
    }
  }
}

.sidebar-nav {
  padding: var(--spacing-xl) 0;
  flex: 1;
  height: 100%;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--spacing-xl);
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: rgba(255, 255, 255, 0.7);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all var(--transition-fast);
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
  font-family: var(--font-family-primary);
  font-size: 0.95rem;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-4px);
  }

  &.active {
    background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    color: white;
    box-shadow: var(--shadow-md);
    border-radius: var(--radius-xl) 0 0 var(--radius-xl);
    margin-left: var(--spacing-md);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: var(--warning-400);
    }
  }

  &.expandable {
    justify-content: space-between;

    .expand-icon {
      transition: transform var(--transition-fast);
    }

    &.expanded .expand-icon {
      transform: rotate(180deg);
    }
  }

  mat-icon {
    font-size: 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }

  span {
    flex: 1;
    text-align: right;
  }
}

.nav-group {
  margin-bottom: var(--spacing-sm);
}

.nav-submenu {
  background: rgba(0, 0, 0, 0.2);
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  overflow: hidden;
}

.nav-subitem {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-lg);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-size: 0.875rem;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-2px);
  }

  &.active {
    background: var(--primary-600);
    color: white;
  }

  mat-icon {
    font-size: 1rem;
    width: 1rem;
    height: 1rem;
  }
}

.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.system-info {
  text-align: center;

  .version {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: var(--spacing-xs);
  }

  .copyright {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.5);
  }
}

/* ===== MAIN CONTENT ===== */
.main-content {
  margin-top: 70px;
  margin-right: 280px;
  min-height: calc(100vh - 70px);
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
  transition: margin-right var(--transition-normal);
  box-sizing: border-box;
}

.content-wrapper {
  padding: var(--spacing-2xl);
  min-height: calc(100vh - 70px);
  max-width: 100%;
  box-sizing: border-box;
}

/* ===== MENUS ===== */
.notifications-menu,
.user-menu {
  .mat-mdc-menu-content {
    padding: 0 !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-xl) !important;
    border: 1px solid var(--gray-200) !important;
  }
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--gray-50);

  h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
  }
}

.notification-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--spacing-md) !important;
  padding: var(--spacing-md) var(--spacing-lg) !important;

  .notification-content {
    flex: 1;

    .notification-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--gray-800);
      margin: 0;
    }

    .notification-time {
      font-size: 0.75rem;
      color: var(--gray-500);
      margin: 0;
    }
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .navbar-center {
    max-width: 300px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    margin-right: 0;
  }

  .sidebar {
    transform: translateX(100%);
    z-index: 1001;

    &.show {
      transform: translateX(0);
    }
  }

  .navbar-center {
    display: none;
  }

  .content-wrapper {
    padding: var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .navbar-content {
    padding: 0 var(--spacing-lg);
  }

  .user-info {
    display: none !important;
  }

  .content-wrapper {
    padding: var(--spacing-lg);
  }

  .navbar-left {
    gap: var(--spacing-sm);
  }

  .navbar-right {
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .navbar-content {
    padding: 0 var(--spacing-md);
  }

  .content-wrapper {
    padding: var(--spacing-md);
  }

  .logo-text {
    display: none;
  }
}



/* ===== ANIMATIONS ===== */
@keyframes slideInFromLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sidebar {
  animation: slideInFromLeft 0.3s ease-out;
}

.nav-item {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }
