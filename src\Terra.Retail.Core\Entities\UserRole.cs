using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// ربط المستخدمين بالأدوار
    /// </summary>
    public class UserRole : BaseEntity
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// معرف الدور
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// تاريخ منح الدور
        /// </summary>
        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي منح الدور
        /// </summary>
        public int? GrantedById { get; set; }

        /// <summary>
        /// تاريخ انتهاء الدور
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// هل الدور نشط للمستخدم
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ملاحظات حول منح الدور
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Role Role { get; set; } = null!;
        public virtual User? GrantedBy { get; set; }
    }
}
