-- إدخال شجرة الحسابات الكاملة
-- Chart of Accounts Complete Tree

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);

-- 1. الأصول (Assets) - 1xxxx
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, Level, IsParent, AllowPosting, IsActive, CreatedAt) VALUES
('1', 'الأصول', 'Assets', 1, NULL, 1, GETDATE()),

-- 1.1 الأصول المتداولة (Current Assets) - 11xxx
('11', 'الأصول المتداولة', 'Current Assets', 1, 1, 1, GETDATE()),

-- 1.1.1 النقدية والبنوك (Cash & Banks) - 111xx
('111', 'النقدية والبنوك', 'Cash & Banks', 1, 2, 1, GETDATE()),
('11101', 'الصندوق الرئيسي', 'Main Cash', 1, 3, 1, GETDATE()),
('11102', 'صندوق الفرع الأول', 'Branch 1 Cash', 1, 3, 1, GETDATE()),
('11103', 'صندوق الفرع الثاني', 'Branch 2 Cash', 1, 3, 1, GETDATE()),
('11104', 'صندوق العملة الأجنبية', 'Foreign Currency Cash', 1, 3, 1, GETDATE()),
('11105', 'البنك الأهلي المصري - ج.م', 'National Bank of Egypt - EGP', 1, 3, 1, GETDATE()),
('11106', 'بنك مصر - ج.م', 'Banque Misr - EGP', 1, 3, 1, GETDATE()),
('11107', 'البنك التجاري الدولي - ج.م', 'CIB - EGP', 1, 3, 1, GETDATE()),
('11108', 'بنك القاهرة - ج.م', 'Banque du Caire - EGP', 1, 3, 1, GETDATE()),
('11109', 'البنك الأهلي المصري - دولار', 'National Bank of Egypt - USD', 1, 3, 1, GETDATE()),
('11110', 'بنك مصر - دولار', 'Banque Misr - USD', 1, 3, 1, GETDATE()),

-- 1.1.2 العملاء (Accounts Receivable) - 112xx
('112', 'العملاء', 'Accounts Receivable', 1, 2, 1, GETDATE()),
('11201', 'عملاء محليين', 'Local Customers', 1, 12, 1, GETDATE()),
('11202', 'عملاء أجانب', 'Foreign Customers', 1, 12, 1, GETDATE()),
('11203', 'أوراق قبض', 'Notes Receivable', 1, 12, 1, GETDATE()),
('11204', 'عملاء شيكات تحت التحصيل', 'Customers Checks Under Collection', 1, 12, 1, GETDATE()),
('11205', 'مخصص ديون مشكوك فيها', 'Allowance for Doubtful Debts', 1, 12, 1, GETDATE()),

-- 1.1.3 المخزون (Inventory) - 113xx
('113', 'المخزون', 'Inventory', 1, 2, 1, GETDATE()),
('11301', 'مخزون البضاعة', 'Merchandise Inventory', 1, 17, 1, GETDATE()),
('11302', 'مخزون المواد الخام', 'Raw Materials Inventory', 1, 17, 1, GETDATE()),
('11303', 'مخزون الإنتاج تحت التشغيل', 'Work in Process Inventory', 1, 17, 1, GETDATE()),
('11304', 'مخزون البضاعة التامة', 'Finished Goods Inventory', 1, 17, 1, GETDATE()),
('11305', 'مخزون قطع الغيار', 'Spare Parts Inventory', 1, 17, 1, GETDATE()),
('11306', 'مخزون المستلزمات', 'Supplies Inventory', 1, 17, 1, GETDATE()),

-- 1.1.4 المصروفات المدفوعة مقدماً (Prepaid Expenses) - 114xx
('114', 'المصروفات المدفوعة مقدماً', 'Prepaid Expenses', 1, 2, 1, GETDATE()),
('11401', 'إيجار مدفوع مقدماً', 'Prepaid Rent', 1, 23, 1, GETDATE()),
('11402', 'تأمين مدفوع مقدماً', 'Prepaid Insurance', 1, 23, 1, GETDATE()),
('11403', 'مصروفات دعاية مدفوعة مقدماً', 'Prepaid Advertising', 1, 23, 1, GETDATE()),

-- 1.1.5 أرصدة مدينة أخرى (Other Debit Balances) - 115xx
('115', 'أرصدة مدينة أخرى', 'Other Debit Balances', 1, 2, 1, GETDATE()),
('11501', 'سلف الموظفين', 'Employee Advances', 1, 27, 1, GETDATE()),
('11502', 'عهد نقدية', 'Cash Custody', 1, 27, 1, GETDATE()),
('11503', 'عهد عينية', 'Material Custody', 1, 27, 1, GETDATE()),
('11504', 'أمانات مدفوعة', 'Deposits Paid', 1, 27, 1, GETDATE()),
('11505', 'ضرائب مدفوعة مقدماً', 'Prepaid Taxes', 1, 27, 1, GETDATE()),

-- 1.2 الأصول الثابتة (Fixed Assets) - 12xxx
('12', 'الأصول الثابتة', 'Fixed Assets', 1, 1, 1, GETDATE()),

-- 1.2.1 الأراضي والمباني (Land & Buildings) - 121xx
('121', 'الأراضي والمباني', 'Land & Buildings', 1, 32, 1, GETDATE()),
('12101', 'الأراضي', 'Land', 1, 33, 1, GETDATE()),
('12102', 'المباني', 'Buildings', 1, 33, 1, GETDATE()),
('12103', 'مجمع إهلاك المباني', 'Accumulated Depreciation - Buildings', 1, 33, 1, GETDATE()),

-- 1.2.2 الآلات والمعدات (Machinery & Equipment) - 122xx
('122', 'الآلات والمعدات', 'Machinery & Equipment', 1, 32, 1, GETDATE()),
('12201', 'آلات ومعدات', 'Machinery & Equipment', 1, 37, 1, GETDATE()),
('12202', 'مجمع إهلاك الآلات والمعدات', 'Accumulated Depreciation - Machinery', 1, 37, 1, GETDATE()),

-- 1.2.3 الأثاث والتجهيزات (Furniture & Fixtures) - 123xx
('123', 'الأثاث والتجهيزات', 'Furniture & Fixtures', 1, 32, 1, GETDATE()),
('12301', 'أثاث ومفروشات', 'Furniture & Furnishings', 1, 40, 1, GETDATE()),
('12302', 'مجمع إهلاك الأثاث', 'Accumulated Depreciation - Furniture', 1, 40, 1, GETDATE()),

-- 1.2.4 وسائل النقل (Vehicles) - 124xx
('124', 'وسائل النقل', 'Vehicles', 1, 32, 1, GETDATE()),
('12401', 'سيارات', 'Cars', 1, 43, 1, GETDATE()),
('12402', 'شاحنات', 'Trucks', 1, 43, 1, GETDATE()),
('12403', 'مجمع إهلاك وسائل النقل', 'Accumulated Depreciation - Vehicles', 1, 43, 1, GETDATE()),

-- 1.2.5 أصول أخرى (Other Assets) - 125xx
('125', 'أصول أخرى', 'Other Assets', 1, 32, 1, GETDATE()),
('12501', 'أصول غير ملموسة', 'Intangible Assets', 1, 47, 1, GETDATE()),
('12502', 'استثمارات طويلة الأجل', 'Long-term Investments', 1, 47, 1, GETDATE());

-- 2. الخصوم (Liabilities) - 2xxxx
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, IsActive, CreatedAt) VALUES
('2', 'الخصوم', 'Liabilities', 2, NULL, 1, GETDATE()),

-- 2.1 الخصوم المتداولة (Current Liabilities) - 21xxx
('21', 'الخصوم المتداولة', 'Current Liabilities', 2, 50, 1, GETDATE()),

-- 2.1.1 الموردين (Accounts Payable) - 211xx
('211', 'الموردين', 'Accounts Payable', 2, 51, 1, GETDATE()),
('21101', 'موردين محليين', 'Local Suppliers', 2, 52, 1, GETDATE()),
('21102', 'موردين أجانب', 'Foreign Suppliers', 2, 52, 1, GETDATE()),
('21103', 'أوراق دفع', 'Notes Payable', 2, 52, 1, GETDATE()),

-- 2.1.2 مصروفات مستحقة (Accrued Expenses) - 212xx
('212', 'مصروفات مستحقة', 'Accrued Expenses', 2, 51, 1, GETDATE()),
('21201', 'رواتب مستحقة', 'Accrued Salaries', 2, 56, 1, GETDATE()),
('21202', 'إيجار مستحق', 'Accrued Rent', 2, 56, 1, GETDATE()),
('21203', 'فوائد مستحقة', 'Accrued Interest', 2, 56, 1, GETDATE()),

-- 2.1.3 ضرائب مستحقة (Accrued Taxes) - 213xx
('213', 'ضرائب مستحقة', 'Accrued Taxes', 2, 51, 1, GETDATE()),
('21301', 'ضريبة الدخل المستحقة', 'Accrued Income Tax', 2, 60, 1, GETDATE()),
('21302', 'ضريبة القيمة المضافة', 'VAT Payable', 2, 60, 1, GETDATE()),
('21303', 'ضرائب أخرى مستحقة', 'Other Accrued Taxes', 2, 60, 1, GETDATE()),

-- 2.2 الخصوم طويلة الأجل (Long-term Liabilities) - 22xxx
('22', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 2, 50, 1, GETDATE()),
('22101', 'قروض طويلة الأجل', 'Long-term Loans', 2, 64, 1, GETDATE()),
('22102', 'سندات مستحقة الدفع', 'Bonds Payable', 2, 64, 1, GETDATE());

-- 3. حقوق الملكية (Equity) - 3xxxx
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, IsActive, CreatedAt) VALUES
('3', 'حقوق الملكية', 'Equity', 3, NULL, 1, GETDATE()),
('31', 'رأس المال', 'Capital', 3, 67, 1, GETDATE()),
('31101', 'رأس المال المدفوع', 'Paid-in Capital', 3, 68, 1, GETDATE()),
('31102', 'احتياطي قانوني', 'Legal Reserve', 3, 68, 1, GETDATE()),
('31103', 'احتياطي اختياري', 'Optional Reserve', 3, 68, 1, GETDATE()),
('31104', 'أرباح محتجزة', 'Retained Earnings', 3, 68, 1, GETDATE()),
('31105', 'أرباح العام الحالي', 'Current Year Profit', 3, 68, 1, GETDATE()),
('31106', 'مسحوبات شخصية', 'Owner Drawings', 3, 68, 1, GETDATE());

-- 4. الإيرادات (Revenue) - 4xxxx
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, IsActive, CreatedAt) VALUES
('4', 'الإيرادات', 'Revenue', 4, NULL, 1, GETDATE()),

-- 4.1 إيرادات المبيعات (Sales Revenue) - 41xxx
('41', 'إيرادات المبيعات', 'Sales Revenue', 4, 74, 1, GETDATE()),
('41101', 'مبيعات محلية', 'Local Sales', 4, 75, 1, GETDATE()),
('41102', 'مبيعات تصدير', 'Export Sales', 4, 75, 1, GETDATE()),
('41103', 'مبيعات خدمات', 'Service Revenue', 4, 75, 1, GETDATE()),
('41104', 'خصومات مسموحة', 'Sales Discounts', 4, 75, 1, GETDATE()),
('41105', 'مردودات ومسموحات مبيعات', 'Sales Returns & Allowances', 4, 75, 1, GETDATE()),

-- 4.2 إيرادات أخرى (Other Revenue) - 42xxx
('42', 'إيرادات أخرى', 'Other Revenue', 4, 74, 1, GETDATE()),
('42101', 'إيرادات فوائد', 'Interest Income', 4, 81, 1, GETDATE()),
('42102', 'إيرادات إيجار', 'Rental Income', 4, 81, 1, GETDATE()),
('42103', 'أرباح بيع أصول', 'Gain on Sale of Assets', 4, 81, 1, GETDATE()),
('42104', 'إيرادات متنوعة', 'Miscellaneous Income', 4, 81, 1, GETDATE());

-- 5. المصروفات (Expenses) - 5xxxx
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentId, IsActive, CreatedAt) VALUES
('5', 'المصروفات', 'Expenses', 5, NULL, 1, GETDATE()),

-- 5.1 تكلفة البضاعة المباعة (Cost of Goods Sold) - 51xxx
('51', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 6, 86, 1, GETDATE()),
('51101', 'تكلفة المبيعات', 'Cost of Sales', 6, 87, 1, GETDATE()),
('51102', 'مشتريات', 'Purchases', 6, 87, 1, GETDATE()),
('51103', 'مصروفات شراء', 'Purchase Expenses', 6, 87, 1, GETDATE()),
('51104', 'مردودات ومسموحات مشتريات', 'Purchase Returns & Allowances', 6, 87, 1, GETDATE()),

-- 5.2 مصروفات التشغيل (Operating Expenses) - 52xxx
('52', 'مصروفات التشغيل', 'Operating Expenses', 5, 86, 1, GETDATE()),
('52101', 'رواتب وأجور', 'Salaries & Wages', 5, 92, 1, GETDATE()),
('52102', 'إيجار', 'Rent Expense', 5, 92, 1, GETDATE()),
('52103', 'كهرباء ومياه', 'Utilities', 5, 92, 1, GETDATE()),
('52104', 'هاتف وإنترنت', 'Telephone & Internet', 5, 92, 1, GETDATE()),
('52105', 'صيانة وإصلاح', 'Maintenance & Repairs', 5, 92, 1, GETDATE()),
('52106', 'وقود ومواصلات', 'Fuel & Transportation', 5, 92, 1, GETDATE()),
('52107', 'دعاية وإعلان', 'Advertising & Marketing', 5, 92, 1, GETDATE()),
('52108', 'مصروفات إدارية', 'Administrative Expenses', 5, 92, 1, GETDATE()),
('52109', 'تأمينات', 'Insurance', 5, 92, 1, GETDATE()),
('52110', 'إهلاك', 'Depreciation', 5, 92, 1, GETDATE()),
('52111', 'مصروفات بنكية', 'Bank Charges', 5, 92, 1, GETDATE()),
('52112', 'مصروفات قانونية ومهنية', 'Legal & Professional Fees', 5, 92, 1, GETDATE()),
('52113', 'مصروفات متنوعة', 'Miscellaneous Expenses', 5, 92, 1, GETDATE());
