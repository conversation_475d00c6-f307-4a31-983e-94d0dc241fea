using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("📦 Product Management")]
    public class ProductsController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public ProductsController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Product>>> GetProducts(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? unitId = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.NameAr.Contains(search) || 
                                           p.NameEn!.Contains(search) || 
                                           p.ProductCode.Contains(search) ||
                                           p.Barcode!.Contains(search));
                }

                if (categoryId.HasValue)
                    query = query.Where(p => p.CategoryId == categoryId);

                if (unitId.HasValue)
                    query = query.Where(p => p.UnitId == unitId);

                if (isActive.HasValue)
                    query = query.Where(p => p.IsActive == isActive);

                var totalCount = await query.CountAsync();
                var products = await query
                    .OrderBy(p => p.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new
                    {
                        p.Id,
                        p.ProductCode,
                        p.NameAr,
                        p.NameEn,
                        p.Description,
                        p.Barcode,
                        p.CostPrice,
                        p.BasePrice,
                        p.ProfitMargin,
                        p.MinimumStock,
                        p.MaximumStock,
                        p.ReorderPoint,
                        p.IsActive,
                        p.Weight,
                        p.CreatedAt,
                        Category = new { p.Category.Id, p.Category.NameAr, p.Category.NameEn },
                        Unit = new { p.Unit.Id, p.Unit.NameAr, p.Unit.Symbol },
                        // Get stock for specific branch if provided
                        Stock = branchId.HasValue ? 
                            p.ProductStocks.Where(ps => ps.BranchId == branchId).FirstOrDefault() : null
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = products,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetProduct(int id)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .Include(p => p.ProductStocks)
                        .ThenInclude(ps => ps.Branch)
                    .Include(p => p.ProductImages)
                    .Include(p => p.ProductAlternativeCodes)
                    .Include(p => p.ProductSuppliers)
                        .ThenInclude(ps => ps.Supplier)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (product == null)
                    return NotFound(new { message = "المنتج غير موجود" });

                return Ok(product);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Product>> CreateProduct(CreateProductRequest request)
        {
            try
            {
                // Generate product code based on type
                var productCode = await GenerateProductCode(request.IsLocal, request.IsWeighted);

                var product = new Product
                {
                    ProductCode = productCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    CategoryId = request.CategoryId,
                    UnitId = request.UnitId,
                    Barcode = request.Barcode,
                    CostPrice = request.CostPrice,
                    BasePrice = request.BasePrice,
                    ProfitMargin = request.ProfitMargin,
                    MinimumStock = request.MinimumStock,
                    MaximumStock = request.MaximumStock,
                    ReorderPoint = request.ReorderPoint,
                    Weight = request.Weight,
                    Length = request.Length,
                    Width = request.Width,
                    Height = request.Height,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                // Create initial stock records for all branches
                var branches = await _context.Branches.Where(b => b.IsActive).ToListAsync();
                foreach (var branch in branches)
                {
                    var stock = new ProductStock
                    {
                        ProductId = product.Id,
                        BranchId = branch.Id,
                        AvailableQuantity = 0,
                        ReservedQuantity = 0,
                        OnOrderQuantity = 0,
                        OpeningQuantity = 0,
                        TotalInQuantity = 0,
                        TotalOutQuantity = 0,
                        AverageCostPrice = request.CostPrice,
                        LastCostPrice = request.CostPrice,
                        StockValue = 0,
                        BranchMinStock = request.MinimumStock,
                        BranchMaxStock = request.MaximumStock,
                        BranchReorderPoint = request.ReorderPoint,
                        IsAvailableForSale = true,
                        CreatedAt = DateTime.Now
                    };
                    _context.ProductStocks.Add(stock);
                }

                await _context.SaveChangesAsync();

                // Load related data for response
                await _context.Entry(product)
                    .Reference(p => p.Category)
                    .LoadAsync();
                await _context.Entry(product)
                    .Reference(p => p.Unit)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProduct(int id, UpdateProductRequest request)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                    return NotFound(new { message = "المنتج غير موجود" });

                product.NameAr = request.NameAr;
                product.NameEn = request.NameEn;
                product.Description = request.Description;
                product.CategoryId = request.CategoryId;
                product.UnitId = request.UnitId;
                product.Barcode = request.Barcode;
                product.CostPrice = request.CostPrice;
                product.BasePrice = request.BasePrice;
                product.ProfitMargin = request.ProfitMargin;
                product.MinimumStock = request.MinimumStock;
                product.MaximumStock = request.MaximumStock;
                product.ReorderPoint = request.ReorderPoint;
                product.Weight = request.Weight;
                product.Length = request.Length;
                product.Width = request.Width;
                product.Height = request.Height;
                product.IsActive = request.IsActive;
                product.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث بيانات المنتج بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                    return NotFound(new { message = "المنتج غير موجود" });

                // Check if product has transactions
                var hasTransactions = await _context.SaleItems.AnyAsync(si => si.ProductId == id) ||
                                    await _context.PurchaseInvoiceDetails.AnyAsync(pid => pid.ProductId == id);

                if (hasTransactions)
                {
                    return BadRequest(new { message = "لا يمكن حذف المنتج لوجود معاملات مرتبطة به" });
                }

                // Check if product has stock
                var hasStock = await _context.ProductStocks.AnyAsync(ps => ps.ProductId == id && ps.AvailableQuantity > 0);
                if (hasStock)
                {
                    return BadRequest(new { message = "لا يمكن حذف المنتج لوجود كمية في المخزن" });
                }

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المنتج بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("categories")]
        public async Task<ActionResult<IEnumerable<Category>>> GetCategories()
        {
            try
            {
                var categories = await _context.Categories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.NameAr)
                    .ToListAsync();

                return Ok(categories);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("units")]
        public async Task<ActionResult<IEnumerable<Unit>>> GetUnits()
        {
            try
            {
                var units = await _context.Units
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.DisplayOrder)
                    .ThenBy(u => u.NameAr)
                    .ToListAsync();

                return Ok(units);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}/stock")]
        public async Task<ActionResult> GetProductStock(int id, [FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.ProductStocks
                    .Include(ps => ps.Branch)
                    .Where(ps => ps.ProductId == id);

                if (branchId.HasValue)
                    query = query.Where(ps => ps.BranchId == branchId);

                var stocks = await query.ToListAsync();

                return Ok(stocks);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("barcode/{barcode}")]
        public async Task<ActionResult> GetProductByBarcode(string barcode)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .FirstOrDefaultAsync(p => p.Barcode == barcode && p.IsActive);

                if (product == null)
                    return NotFound(new { message = "المنتج غير موجود" });

                return Ok(product);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateProductCode(bool isLocal, bool isWeighted)
        {
            string counterType;
            string prefix;

            if (isWeighted)
            {
                counterType = CounterTypes.ProductWeight;
                prefix = "WGT";
            }
            else if (isLocal)
            {
                counterType = CounterTypes.ProductLocal;
                prefix = "2"; // Local products start from 2000000000001
            }
            else
            {
                // International products use manual codes
                return "INT" + DateTime.Now.Ticks.ToString().Substring(0, 10);
            }

            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == counterType);

            if (counter == null)
            {
                long startValue = isLocal ? 2000000000001 : 1;
                counter = new Counter
                {
                    CounterName = counterType,
                    Prefix = prefix,
                    CurrentValue = startValue,
                    NumberLength = isLocal ? 13 : 10,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            if (isLocal)
            {
                return counter.CurrentValue.ToString();
            }
            else
            {
                return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
            }
        }
    }

    // DTOs
    public class CreateProductRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int CategoryId { get; set; }
        public int UnitId { get; set; }
        public string? Barcode { get; set; }
        public decimal CostPrice { get; set; } = 0;
        public decimal BasePrice { get; set; } = 0;
        public decimal ProfitMargin { get; set; } = 0;
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public bool IsLocal { get; set; } = true;
        public bool IsWeighted { get; set; } = false;
    }

    public class UpdateProductRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int CategoryId { get; set; }
        public int UnitId { get; set; }
        public string? Barcode { get; set; }
        public decimal CostPrice { get; set; } = 0;
        public decimal BasePrice { get; set; } = 0;
        public decimal ProfitMargin { get; set; } = 0;
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderPoint { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
