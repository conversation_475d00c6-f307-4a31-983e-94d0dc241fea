<!-- صفحة قائمة العملاء -->
<div class="customers-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-users"></i>
        إدارة العملاء
      </h1>
      <p class="page-subtitle">إدارة وعرض جميع بيانات العملاء</p>
    </div>
    <div class="header-actions">
      <button class="btn btn-primary" (click)="openAddCustomerModal()">
        <i class="fas fa-plus"></i>
        إضافة عميل جديد
      </button>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="search-filters">
    <!-- خانات البحث المنفصلة -->
    <div class="search-boxes">
      <div class="search-box">
        <i class="fas fa-barcode"></i>
        <input
          type="text"
          class="form-control"
          placeholder="البحث بكود العميل (CUS1, CUS2, إلخ)..."
          [(ngModel)]="searchByCode"
          (input)="onSearch()"
        >
      </div>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          class="form-control"
          placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني..."
          [(ngModel)]="searchByOther"
          (input)="onSearch()"
        >
      </div>
    </div>
    <div class="filters">
      <select class="form-control" [(ngModel)]="selectedCustomerType" (change)="onFilterChange()">
        <option value="">جميع أنواع العملاء</option>
        <option *ngFor="let type of customerTypes" [value]="type.id">{{ type.nameAr }}</option>
      </select>
      <select class="form-control" [(ngModel)]="selectedStatus" (change)="onFilterChange()">
        <option value="">جميع الحالات</option>
        <option value="true">نشط</option>
        <option value="false">غير نشط</option>
      </select>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-row">
    <div class="stat-card total">
      <div class="stat-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-content">
        <h3>{{ totalCustomers }}</h3>
        <p>إجمالي العملاء</p>
      </div>
    </div>
    <div class="stat-card active">
      <div class="stat-icon">
        <i class="fas fa-user-check"></i>
      </div>
      <div class="stat-content">
        <h3>{{ activeCustomers }}</h3>
        <p>العملاء النشطين</p>
      </div>
    </div>
    <div class="stat-card balance">
      <div class="stat-icon">
        <i class="fas fa-money-bill-wave"></i>
      </div>
      <div class="stat-content">
        <h3>{{ totalBalance | currency:'EGP':'symbol':'1.0-0' }}</h3>
        <p>إجمالي الأرصدة</p>
      </div>
    </div>
  </div>

  <!-- Customers Table -->
  <div class="table-container">
    <div class="table-header">
      <h3>قائمة العملاء</h3>
      <div class="table-actions">
        <button class="btn btn-secondary" (click)="exportToExcel()">
          <i class="fas fa-file-excel"></i>
          تصدير Excel
        </button>
      </div>
    </div>

    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th>كود العميل</th>
            <th>الاسم العربي</th>
            <th>الاسم الإنجليزي</th>
            <th>الهاتف</th>
            <th>البريد الإلكتروني</th>
            <th>نوع العميل</th>
            <th>فئة السعر</th>
            <th>الرصيد الحالي</th>
            <th>الحالة</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let customer of filteredCustomers; trackBy: trackByCustomerId">
            <td>
              <span class="customer-code">{{ customer.customerCode }}</span>
            </td>
            <td>
              <div class="customer-name">
                <strong>{{ customer.nameAr }}</strong>
              </div>
            </td>
            <td>{{ customer.nameEn }}</td>
            <td>
              <div class="phone-numbers">
                <span class="phone-primary">{{ customer.phone1 }}</span>
                <span class="phone-secondary" *ngIf="customer.phone2">{{ customer.phone2 }}</span>
              </div>
            </td>
            <td>
              <a [href]="'mailto:' + customer.email" class="email-link">{{ customer.email }}</a>
            </td>
            <td>
              <span class="customer-type">{{ customer.customerTypeName }}</span>
            </td>
            <td>
              <span class="price-category">{{ customer.priceCategoryName }}</span>
            </td>
            <td>
              <span class="balance" [class.negative]="customer.currentBalance < 0">
                {{ customer.currentBalance | currency:'EGP':'symbol':'1.2-2' }}
              </span>
            </td>
            <td>
              <span class="status-badge" [class.active]="customer.isActive" [class.inactive]="!customer.isActive">
                {{ customer.isActive ? 'نشط' : 'غير نشط' }}
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="btn btn-sm btn-info" (click)="viewCustomer(customer)" title="عرض">
                  <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-warning" (click)="editCustomer(customer)" title="تعديل">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteCustomer(customer)" title="حذف">
                  <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-sm btn-success" (click)="viewCustomerAccount(customer)" title="كشف حساب">
                  <i class="fas fa-file-invoice"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination-container" *ngIf="totalPages > 1">
      <nav>
        <ul class="pagination">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <button class="page-link" (click)="goToPage(1)">الأولى</button>
          </li>
          <li class="page-item" [class.disabled]="currentPage === 1">
            <button class="page-link" (click)="goToPage(currentPage - 1)">السابقة</button>
          </li>
          <li class="page-item" *ngFor="let page of getPageNumbers()" [class.active]="page === currentPage">
            <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <button class="page-link" (click)="goToPage(currentPage + 1)">التالية</button>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <button class="page-link" (click)="goToPage(totalPages)">الأخيرة</button>
          </li>
        </ul>
      </nav>
      <div class="pagination-info">
        عرض {{ (currentPage - 1) * pageSize + 1 }} إلى {{ Math.min(currentPage * pageSize, totalCustomers) }} من {{ totalCustomers }} عميل
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>جاري تحميل البيانات...</p>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading && filteredCustomers.length === 0">
    <i class="fas fa-users"></i>
    <h3>لا توجد عملاء</h3>
    <p>لم يتم العثور على أي عملاء مطابقين للبحث</p>
    <button class="btn btn-primary" (click)="openAddCustomerModal()">
      <i class="fas fa-plus"></i>
      إضافة أول عميل
    </button>
  </div>
</div>
