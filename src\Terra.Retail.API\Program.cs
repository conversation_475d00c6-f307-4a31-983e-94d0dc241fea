using Microsoft.EntityFrameworkCore;
using Terra.Retail.Infrastructure.Data;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// إعداد Serilog للتسجيل
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/terra-retail-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// إضافة خدمات قاعدة البيانات
builder.Services.AddDbContext<TerraRetailDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// إضافة خدمات API
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "Terra Retail ERP API",
        Version = "v1",
        Description = "نظام إدارة متكامل للمتاجر والشركات التجارية"
    });
});

// إضافة CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularApp", policy =>
    {
        policy.WithOrigins("http://localhost:4200")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// إنشاء قاعدة البيانات إذا لم تكن موجودة
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<TerraRetailDbContext>();
    try
    {
        context.Database.EnsureCreated();
        Log.Information("تم إنشاء قاعدة البيانات بنجاح");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "خطأ في إنشاء قاعدة البيانات");
    }
}

// إعداد pipeline للطلبات
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Terra Retail ERP API v1");
        c.RoutePrefix = string.Empty; // لجعل Swagger الصفحة الرئيسية
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAngularApp");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// إضافة endpoint للتحقق من حالة النظام
app.MapGet("/health", () => new {
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Version = "1.0.0",
    System = "Terra Retail ERP"
})
.WithName("HealthCheck")
.WithOpenApi();

Log.Information("تم بدء تشغيل Terra Retail ERP API");

app.Run();
