<div class="login-container">
  <!-- Background Animation -->
  <div class="background-animation">
    <div class="floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
      <div class="shape shape-5"></div>
    </div>
  </div>

  <!-- Login Card -->
  <div class="login-card">
    <!-- Logo Section -->
    <div class="logo-section">
      <div class="logo-container">
        <mat-icon class="logo-icon">store</mat-icon>
        <h1 class="logo-text">Terra Retail ERP</h1>
      </div>
      <p class="logo-subtitle">نظام إدارة متكامل للمتاجر والشركات التجارية المصرية</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
      <div class="form-header">
        <h2>تسجيل الدخول</h2>
        <p>مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
      </div>

      <!-- Username Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>اسم المستخدم</mat-label>
        <input matInput formControlName="username" placeholder="أدخل اسم المستخدم">
        <mat-icon matPrefix>person</mat-icon>
        <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
          اسم المستخدم مطلوب
        </mat-error>
      </mat-form-field>

      <!-- Password Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>كلمة المرور</mat-label>
        <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="أدخل كلمة المرور">
        <mat-icon matPrefix>lock</mat-icon>
        <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
          كلمة المرور مطلوبة
        </mat-error>
      </mat-form-field>

      <!-- Branch Selection -->
      <mat-form-field appearance="outline" class="full-width" *ngIf="branches.length > 0">
        <mat-label>اختر الفرع</mat-label>
        <mat-select formControlName="branchId">
          <mat-option *ngFor="let branch of branches" [value]="branch.id">
            <div class="branch-option">
              <mat-icon>business</mat-icon>
              <span>{{ branch.nameAr }}</span>
              <small *ngIf="branch.isMainBranch" class="main-branch-badge">رئيسي</small>
            </div>
          </mat-option>
        </mat-select>
        <mat-icon matPrefix>business</mat-icon>
      </mat-form-field>

      <!-- Remember Me -->
      <div class="form-options">
        <mat-checkbox formControlName="rememberMe" color="primary">
          تذكرني
        </mat-checkbox>
        <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
      </div>

      <!-- Error Message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon>
        <span>{{ errorMessage }}</span>
      </div>

      <!-- Login Button -->
      <button mat-raised-button color="primary" type="submit" class="login-button" [disabled]="loginForm.invalid || isLoading">
        <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
        <span *ngIf="!isLoading">تسجيل الدخول</span>
        <span *ngIf="isLoading">جاري تسجيل الدخول...</span>
      </button>

      <!-- Alternative Login -->
      <div class="alternative-login">
        <div class="divider">
          <span>أو</span>
        </div>
        <button mat-stroked-button type="button" class="demo-button" (click)="loginAsDemo()">
          <mat-icon>visibility</mat-icon>
          دخول تجريبي
        </button>
      </div>
    </form>

    <!-- Footer -->
    <div class="login-footer">
      <p>&copy; 2024 Terra Retail ERP. جميع الحقوق محفوظة</p>
      <div class="footer-links">
        <a href="#">الدعم الفني</a>
        <a href="#">سياسة الخصوصية</a>
        <a href="#">شروط الاستخدام</a>
      </div>
    </div>
  </div>

  <!-- System Status -->
  <div class="system-status">
    <div class="status-item">
      <mat-icon [class]="apiStatus === 'connected' ? 'status-online' : 'status-offline'">
        {{ apiStatus === 'connected' ? 'cloud_done' : 'cloud_off' }}
      </mat-icon>
      <span>API: {{ apiStatus === 'connected' ? 'متصل' : 'غير متصل' }}</span>
    </div>
    <div class="status-item">
      <mat-icon [class]="dbStatus === 'connected' ? 'status-online' : 'status-offline'">
        {{ dbStatus === 'connected' ? 'storage' : 'error' }}
      </mat-icon>
      <span>قاعدة البيانات: {{ dbStatus === 'connected' ? 'متصلة' : 'غير متصلة' }}</span>
    </div>
  </div>
</div>
