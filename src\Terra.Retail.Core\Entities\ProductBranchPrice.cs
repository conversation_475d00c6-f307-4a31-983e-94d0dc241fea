using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// أسعار المنتجات حسب الفرع وفئة السعر
    /// </summary>
    public class ProductBranchPrice : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف فئة السعر
        /// </summary>
        public int? PriceCategoryId { get; set; }

        /// <summary>
        /// السعر
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// سعر التكلفة للفرع
        /// </summary>
        public decimal? CostPrice { get; set; }

        /// <summary>
        /// هامش الربح (%)
        /// </summary>
        public decimal? ProfitMargin { get; set; }

        /// <summary>
        /// الحد الأدنى للسعر
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// الحد الأقصى للسعر
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// تاريخ بداية السعر
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء السعر
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// هل السعر نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل السعر افتراضي للفرع
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// نوع السعر (عادي، عرض، تخفيض)
        /// </summary>
        public PriceType PriceType { get; set; } = PriceType.Regular;

        /// <summary>
        /// نسبة الخصم المطبقة
        /// </summary>
        public decimal? DiscountPercentage { get; set; }

        /// <summary>
        /// مبلغ الخصم الثابت
        /// </summary>
        public decimal? DiscountAmount { get; set; }

        /// <summary>
        /// الحد الأدنى للكمية لتطبيق السعر
        /// </summary>
        public decimal? MinQuantity { get; set; }

        /// <summary>
        /// الحد الأقصى للكمية لتطبيق السعر
        /// </summary>
        public decimal? MaxQuantity { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual PriceCategory? PriceCategory { get; set; }
    }

    /// <summary>
    /// أنواع الأسعار
    /// </summary>
    public enum PriceType
    {
        /// <summary>
        /// سعر عادي
        /// </summary>
        Regular = 1,

        /// <summary>
        /// سعر عرض
        /// </summary>
        Promotion = 2,

        /// <summary>
        /// سعر تخفيض
        /// </summary>
        Discount = 3,

        /// <summary>
        /// سعر جملة
        /// </summary>
        Wholesale = 4,

        /// <summary>
        /// سعر تجزئة
        /// </summary>
        Retail = 5,

        /// <summary>
        /// سعر خاص
        /// </summary>
        Special = 6
    }
}
