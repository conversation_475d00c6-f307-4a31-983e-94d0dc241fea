using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP_Simple.Models
{
    [Table("TransactionTypes")]
    public class TransactionTypeEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string NameEn { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;

        [StringLength(50)]
        public string? Category { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int SortOrder { get; set; } = 0;

        [StringLength(50)]
        public string? Icon { get; set; }

        [StringLength(50)]
        public string? Color { get; set; }

        public bool RequiresApproval { get; set; } = false;

        public bool AllowReversal { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
    }

    [Table("Permissions")]
    public class Permission
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string NameEn { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Module { get; set; }

        [StringLength(100)]
        public string? Category { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int SortOrder { get; set; } = 0;

        [StringLength(50)]
        public string? Icon { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }

    [Table("UserPermissions")]
    public class UserPermission
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        public int PermissionId { get; set; }

        public bool CanView { get; set; } = false;

        public bool CanAdd { get; set; } = false;

        public bool CanEdit { get; set; } = false;

        public bool CanDelete { get; set; } = false;

        public bool CanApprove { get; set; } = false;

        public bool CanReverse { get; set; } = false;

        public bool CanExport { get; set; } = false;

        public bool CanPrint { get; set; } = false;

        public int? BranchId { get; set; } // null = all branches

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        public int CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual Branch? Branch { get; set; }
        public virtual User CreatedByUser { get; set; } = null!;
        public virtual User? UpdatedByUser { get; set; }
    }

    [Table("UserBranchPermissions")]
    public class UserBranchPermission
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        public int? BranchId { get; set; } // null = all branches

        public bool IsDefaultBranch { get; set; } = false;

        public bool CanAccessAllBranches { get; set; } = false;

        public bool CanViewReports { get; set; } = true;

        public bool CanViewFinancials { get; set; } = true;

        public bool CanTransferBetweenBranches { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        public int CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Branch? Branch { get; set; }
        public virtual User CreatedByUser { get; set; } = null!;
        public virtual User? UpdatedByUser { get; set; }
    }

    [Table("Roles")]
    public class Role
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsSystemRole { get; set; } = false;

        public int SortOrder { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        public int CreatedBy { get; set; }

        public int? UpdatedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
        public virtual User CreatedByUser { get; set; } = null!;
        public virtual User? UpdatedByUser { get; set; }
    }

    [Table("UserRoles")]
    public class UserRole
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        public int RoleId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public int CreatedBy { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Role Role { get; set; } = null!;
        public virtual User CreatedByUser { get; set; } = null!;
    }

    [Table("RolePermissions")]
    public class RolePermission
    {
        [Key]
        public int Id { get; set; }

        public int RoleId { get; set; }

        public int PermissionId { get; set; }

        public bool CanView { get; set; } = false;

        public bool CanAdd { get; set; } = false;

        public bool CanEdit { get; set; } = false;

        public bool CanDelete { get; set; } = false;

        public bool CanApprove { get; set; } = false;

        public bool CanReverse { get; set; } = false;

        public bool CanExport { get; set; } = false;

        public bool CanPrint { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public int CreatedBy { get; set; }

        // Navigation Properties
        public virtual Role Role { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual User CreatedByUser { get; set; } = null!;
    }

    [Table("UserSessions")]
    public class UserSession
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string SessionId { get; set; } = string.Empty;

        [StringLength(50)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        public DateTime LoginTime { get; set; } = DateTime.Now;

        public DateTime? LogoutTime { get; set; }

        public bool IsActive { get; set; } = true;

        public int? DefaultBranchId { get; set; }

        [StringLength(1000)]
        public string? LastActivity { get; set; }

        public DateTime LastActivityTime { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Branch? DefaultBranch { get; set; }
    }

    [Table("ActivityLog")]
    public class ActivityLog
    {
        [Key]
        public long Id { get; set; }

        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Module { get; set; }

        [StringLength(100)]
        public string? Entity { get; set; }

        public int? EntityId { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(2000)]
        public string? Details { get; set; } // JSON

        [StringLength(50)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public int? BranchId { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Branch? Branch { get; set; }
    }
}
