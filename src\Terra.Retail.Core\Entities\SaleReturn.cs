using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مرتجعات المبيعات
    /// </summary>
    public class SaleReturn : BaseEntity
    {
        /// <summary>
        /// رقم المرتجع
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ReturnNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ المرتجع
        /// </summary>
        public DateTime ReturnDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف فاتورة المبيعات الأصلية
        /// </summary>
        public int SaleId { get; set; }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي استلم المرتجع
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// سبب الإرجاع
        /// </summary>
        public ReturnReason ReturnReason { get; set; }

        /// <summary>
        /// وصف سبب الإرجاع
        /// </summary>
        [MaxLength(500)]
        public string? ReturnReasonDescription { get; set; }

        /// <summary>
        /// إجمالي قيمة المرتجع
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المسترد
        /// </summary>
        public decimal RefundedAmount { get; set; } = 0;

        /// <summary>
        /// حالة المرتجع
        /// </summary>
        public ReturnStatus Status { get; set; } = ReturnStatus.Pending;

        /// <summary>
        /// نوع الاسترداد
        /// </summary>
        public RefundType RefundType { get; set; } = RefundType.Cash;

        /// <summary>
        /// ملاحظات المرتجع
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل المرتجع مؤكد
        /// </summary>
        public bool IsConfirmed { get; set; } = false;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد المرتجع
        /// </summary>
        public int? ConfirmedById { get; set; }

        /// <summary>
        /// هل تم استرداد المبلغ
        /// </summary>
        public bool IsRefunded { get; set; } = false;

        /// <summary>
        /// تاريخ الاسترداد
        /// </summary>
        public DateTime? RefundedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بالاسترداد
        /// </summary>
        public int? RefundedById { get; set; }

        // Navigation Properties
        public virtual Sale Sale { get; set; } = null!;
        public virtual Customer? Customer { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
        public virtual User? RefundedBy { get; set; }
        public virtual ICollection<SaleReturnItem> Items { get; set; } = new List<SaleReturnItem>();
    }

    /// <summary>
    /// أصناف مرتجعات المبيعات
    /// </summary>
    public class SaleReturnItem : BaseEntity
    {
        /// <summary>
        /// معرف المرتجع
        /// </summary>
        public int SaleReturnId { get; set; }

        /// <summary>
        /// معرف صنف الفاتورة الأصلي
        /// </summary>
        public int SaleItemId { get; set; }

        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// الكمية المرتجعة
        /// </summary>
        public decimal ReturnedQuantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// إجمالي قيمة الصنف المرتجع
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// سبب إرجاع الصنف
        /// </summary>
        [MaxLength(500)]
        public string? ItemReturnReason { get; set; }

        /// <summary>
        /// حالة الصنف المرتجع
        /// </summary>
        public ReturnedItemCondition ItemCondition { get; set; } = ReturnedItemCondition.Good;

        /// <summary>
        /// رقم التشغيل/الدفعة
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// الرقم التسلسلي
        /// </summary>
        [MaxLength(100)]
        public string? SerialNumber { get; set; }

        // Navigation Properties
        public virtual SaleReturn SaleReturn { get; set; } = null!;
        public virtual SaleItem SaleItem { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    /// <summary>
    /// أسباب الإرجاع
    /// </summary>
    public enum ReturnReason
    {
        /// <summary>
        /// عيب في المنتج
        /// </summary>
        ProductDefect = 1,

        /// <summary>
        /// منتج خاطئ
        /// </summary>
        WrongProduct = 2,

        /// <summary>
        /// تغيير رأي العميل
        /// </summary>
        CustomerChanged = 3,

        /// <summary>
        /// منتهي الصلاحية
        /// </summary>
        Expired = 4,

        /// <summary>
        /// تالف
        /// </summary>
        Damaged = 5,

        /// <summary>
        /// غير مطابق للمواصفات
        /// </summary>
        NotAsSpecified = 6,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 7
    }

    /// <summary>
    /// حالة المرتجع
    /// </summary>
    public enum ReturnStatus
    {
        /// <summary>
        /// معلق
        /// </summary>
        Pending = 1,

        /// <summary>
        /// مؤكد
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// مسترد
        /// </summary>
        Refunded = 3,

        /// <summary>
        /// مرفوض
        /// </summary>
        Rejected = 4,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 5
    }

    /// <summary>
    /// نوع الاسترداد
    /// </summary>
    public enum RefundType
    {
        /// <summary>
        /// نقدي
        /// </summary>
        Cash = 1,

        /// <summary>
        /// رصيد للعميل
        /// </summary>
        Credit = 2,

        /// <summary>
        /// استبدال
        /// </summary>
        Exchange = 3,

        /// <summary>
        /// قسيمة شرائية
        /// </summary>
        Voucher = 4
    }

    /// <summary>
    /// حالة الصنف المرتجع
    /// </summary>
    public enum ReturnedItemCondition
    {
        /// <summary>
        /// جيد
        /// </summary>
        Good = 1,

        /// <summary>
        /// تالف
        /// </summary>
        Damaged = 2,

        /// <summary>
        /// منتهي الصلاحية
        /// </summary>
        Expired = 3,

        /// <summary>
        /// مستعمل
        /// </summary>
        Used = 4
    }
}
