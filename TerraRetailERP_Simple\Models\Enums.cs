namespace TerraRetailERP_Simple.Models
{
    public enum TransactionType
    {
        // Sales Transactions (1-10)
        Sale = 1,                    // مبيعات
        SaleReturn = 2,              // مرتجع مبيعات
        SaleDiscount = 3,            // خصم مبيعات
        SalePayment = 4,             // دفعة من عميل

        // Purchase Transactions (11-20)
        Purchase = 11,               // مشتريات
        PurchaseReturn = 12,         // مرتجع مشتريات
        PurchaseDiscount = 13,       // خصم مشتريات
        PurchasePayment = 14,        // دفعة لمورد

        // Cash Transactions (21-30)
        CashReceipt = 21,            // سند قبض نقدي
        CashPayment = 22,            // سند دفع نقدي
        BankDeposit = 23,            // إيداع بنكي
        BankWithdrawal = 24,         // سحب بنكي
        BankTransfer = 25,           // تحويل بنكي
        CashTransfer = 26,           // تحويل نقدي

        // Inventory Transactions (31-40)
        StockAdjustment = 31,        // تسوية مخزون
        StockTransfer = 32,          // تحويل مخزون
        StockDamage = 33,            // تلف مخزون
        StockCount = 34,             // جرد مخزون
        ProductionIn = 35,           // إنتاج داخل
        ProductionOut = 36,          // إنتاج خارج

        // Employee Transactions (41-50)
        SalaryPayment = 41,          // دفع راتب
        SalaryAdvance = 42,          // سلفة راتب
        Bonus = 43,                  // مكافأة
        Deduction = 44,              // خصم
        Commission = 45,             // عمولة
        Overtime = 46,               // إضافي

        // Expense Transactions (51-60)
        OperatingExpense = 51,       // مصروف تشغيلي
        UtilityExpense = 52,         // مصروف خدمات
        RentExpense = 53,            // مصروف إيجار
        MaintenanceExpense = 54,     // مصروف صيانة
        MarketingExpense = 55,       // مصروف تسويق
        TravelExpense = 56,          // مصروف سفر

        // Tax Transactions (61-70)
        TaxPayment = 61,             // دفع ضريبة
        TaxRefund = 62,              // استرداد ضريبة
        VATCollection = 63,          // تحصيل ضريبة قيمة مضافة
        VATPayment = 64,             // دفع ضريبة قيمة مضافة

        // Asset Transactions (71-80)
        AssetPurchase = 71,          // شراء أصل
        AssetSale = 72,              // بيع أصل
        AssetDepreciation = 73,      // استهلاك أصل
        AssetMaintenance = 74,       // صيانة أصل

        // Loan Transactions (81-90)
        LoanReceived = 81,           // قرض مستلم
        LoanPayment = 82,            // دفعة قرض
        LoanInterest = 83,           // فوائد قرض
        LoanGiven = 84,              // قرض معطى

        // Investment Transactions (91-100)
        Investment = 91,             // استثمار
        InvestmentReturn = 92,       // عائد استثمار
        InvestmentSale = 93,         // بيع استثمار

        // Adjustment Transactions (101-110)
        GeneralAdjustment = 101,     // تسوية عامة
        CurrencyAdjustment = 102,    // تسوية عملة
        YearEndAdjustment = 103,     // تسوية نهاية السنة
        ErrorCorrection = 104,       // تصحيح خطأ
        WriteOff = 105,              // إعدام

        // Other Transactions (111-120)
        Opening = 111,               // رصيد افتتاحي
        Closing = 112,               // رصيد ختامي
        Transfer = 113,              // تحويل
        Reversal = 114,              // عكس قيد
        Cancellation = 115           // إلغاء
    }

    public enum TransactionStatus
    {
        Draft = 1,                   // مسودة - يمكن التعديل والحذف
        Pending = 2,                 // معلق
        Approved = 3,                // معتمد
        PreliminaryPosted = 4,       // مرحل مبدئي - يمكن العكس فقط
        FinalPosted = 5,             // مرحل نهائي - لا يمكن التعديل أو العكس
        Cancelled = 6,               // ملغي
        Reversed = 7,                // معكوس
        Reconciled = 8,              // مطابق
        Rejected = 9                 // مرفوض
    }

    public enum JournalEntryStatus
    {
        Draft = 1,                   // مسودة - يمكن التعديل والحذف
        UnderReview = 2,             // قيد المراجعة
        Approved = 3,                // معتمد
        PreliminaryPosted = 4,       // مرحل مبدئي - يمكن العكس فقط
        FinalPosted = 5,             // مرحل نهائي - لا يمكن التعديل أو العكس
        Cancelled = 6,               // ملغي
        Reversed = 7,                // معكوس
        Rejected = 8                 // مرفوض
    }

    public enum PaymentType
    {
        Cash = 1,                    // نقدي
        Bank = 2,                    // بنكي
        CreditCard = 3,              // بطاقة ائتمان
        DebitCard = 4,               // بطاقة خصم
        Check = 5,                   // شيك
        BankTransfer = 6,            // تحويل بنكي
        OnlinePayment = 7,           // دفع إلكتروني
        MobileWallet = 8,            // محفظة إلكترونية
        Cryptocurrency = 9,          // عملة رقمية
        Other = 10                   // أخرى
    }

    public enum AccountType
    {
        Asset = 1,                   // أصول
        Liability = 2,               // خصوم
        Equity = 3,                  // حقوق ملكية
        Revenue = 4,                 // إيرادات
        Expense = 5,                 // مصروفات
        CostOfSales = 6             // تكلفة المبيعات
    }

    public enum InventoryMovementType
    {
        In = 1,                      // داخل
        Out = 2,                     // خارج
        Transfer = 3,                // تحويل
        Adjustment = 4,              // تسوية
        Return = 5,                  // مرتجع
        Damage = 6,                  // تلف
        Loss = 7,                    // فقد
        Found = 8                    // موجود
    }

    public enum EmployeeStatus
    {
        Active = 1,                  // نشط
        Inactive = 2,                // غير نشط
        OnLeave = 3,                 // في إجازة
        Suspended = 4,               // موقوف
        Terminated = 5               // منتهي الخدمة
    }

    public enum LeaveStatus
    {
        Pending = 1,                 // معلق
        Approved = 2,                // معتمد
        Rejected = 3,                // مرفوض
        Cancelled = 4                // ملغي
    }

    public enum CounterTypes
    {
        Customer = 1,
        Supplier = 2,
        Product = 3,
        SaleInvoice = 4,
        PurchaseInvoice = 5,
        Receipt = 6,
        Payment = 7,
        JournalEntry = 8,
        StockAdjustment = 9,
        BranchTransfer = 10,
        Employee = 11,
        FinancialTransaction = 12
    }

    public static class TransactionTypeHelper
    {
        public static string GetTransactionTypeName(TransactionType type)
        {
            return type switch
            {
                TransactionType.Sale => "مبيعات",
                TransactionType.SaleReturn => "مرتجع مبيعات",
                TransactionType.SaleDiscount => "خصم مبيعات",
                TransactionType.SalePayment => "دفعة من عميل",
                
                TransactionType.Purchase => "مشتريات",
                TransactionType.PurchaseReturn => "مرتجع مشتريات",
                TransactionType.PurchaseDiscount => "خصم مشتريات",
                TransactionType.PurchasePayment => "دفعة لمورد",
                
                TransactionType.CashReceipt => "سند قبض نقدي",
                TransactionType.CashPayment => "سند دفع نقدي",
                TransactionType.BankDeposit => "إيداع بنكي",
                TransactionType.BankWithdrawal => "سحب بنكي",
                TransactionType.BankTransfer => "تحويل بنكي",
                TransactionType.CashTransfer => "تحويل نقدي",
                
                TransactionType.StockAdjustment => "تسوية مخزون",
                TransactionType.StockTransfer => "تحويل مخزون",
                TransactionType.StockDamage => "تلف مخزون",
                TransactionType.StockCount => "جرد مخزون",
                
                TransactionType.SalaryPayment => "دفع راتب",
                TransactionType.SalaryAdvance => "سلفة راتب",
                TransactionType.Bonus => "مكافأة",
                TransactionType.Deduction => "خصم",
                TransactionType.Commission => "عمولة",
                
                TransactionType.OperatingExpense => "مصروف تشغيلي",
                TransactionType.UtilityExpense => "مصروف خدمات",
                TransactionType.RentExpense => "مصروف إيجار",
                TransactionType.MaintenanceExpense => "مصروف صيانة",
                
                TransactionType.TaxPayment => "دفع ضريبة",
                TransactionType.TaxRefund => "استرداد ضريبة",
                TransactionType.VATCollection => "تحصيل ضريبة قيمة مضافة",
                TransactionType.VATPayment => "دفع ضريبة قيمة مضافة",
                
                TransactionType.AssetPurchase => "شراء أصل",
                TransactionType.AssetSale => "بيع أصل",
                TransactionType.AssetDepreciation => "استهلاك أصل",
                
                TransactionType.LoanReceived => "قرض مستلم",
                TransactionType.LoanPayment => "دفعة قرض",
                TransactionType.LoanInterest => "فوائد قرض",
                
                TransactionType.Investment => "استثمار",
                TransactionType.InvestmentReturn => "عائد استثمار",
                
                TransactionType.GeneralAdjustment => "تسوية عامة",
                TransactionType.CurrencyAdjustment => "تسوية عملة",
                TransactionType.YearEndAdjustment => "تسوية نهاية السنة",
                TransactionType.ErrorCorrection => "تصحيح خطأ",
                
                TransactionType.Opening => "رصيد افتتاحي",
                TransactionType.Closing => "رصيد ختامي",
                TransactionType.Transfer => "تحويل",
                TransactionType.Reversal => "عكس قيد",
                TransactionType.Cancellation => "إلغاء",
                
                _ => "غير محدد"
            };
        }

        public static string GetTransactionStatusName(TransactionStatus status)
        {
            return status switch
            {
                TransactionStatus.Draft => "مسودة",
                TransactionStatus.Pending => "معلق",
                TransactionStatus.Approved => "معتمد",
                TransactionStatus.PreliminaryPosted => "مرحل مبدئي",
                TransactionStatus.FinalPosted => "مرحل نهائي",
                TransactionStatus.Cancelled => "ملغي",
                TransactionStatus.Reversed => "معكوس",
                TransactionStatus.Reconciled => "مطابق",
                TransactionStatus.Rejected => "مرفوض",
                _ => "غير محدد"
            };
        }

        public static string GetJournalEntryStatusName(JournalEntryStatus status)
        {
            return status switch
            {
                JournalEntryStatus.Draft => "مسودة",
                JournalEntryStatus.UnderReview => "قيد المراجعة",
                JournalEntryStatus.Approved => "معتمد",
                JournalEntryStatus.PreliminaryPosted => "مرحل مبدئي",
                JournalEntryStatus.FinalPosted => "مرحل نهائي",
                JournalEntryStatus.Cancelled => "ملغي",
                JournalEntryStatus.Reversed => "معكوس",
                JournalEntryStatus.Rejected => "مرفوض",
                _ => "غير محدد"
            };
        }

        public static List<object> GetJournalEntryStatusList()
        {
            return Enum.GetValues<JournalEntryStatus>()
                .Select(s => new
                {
                    Value = (int)s,
                    Name = GetJournalEntryStatusName(s),
                    EnglishName = s.ToString(),
                    CanEdit = s == JournalEntryStatus.Draft,
                    CanReverse = s == JournalEntryStatus.PreliminaryPosted,
                    CanFinalPost = s == JournalEntryStatus.PreliminaryPosted
                })
                .ToList<object>();
        }

        public static List<object> GetTransactionTypesList()
        {
            return Enum.GetValues<TransactionType>()
                .Select(t => new
                {
                    Value = (int)t,
                    Name = GetTransactionTypeName(t),
                    EnglishName = t.ToString(),
                    Category = GetTransactionCategory(t)
                })
                .ToList<object>();
        }

        private static string GetTransactionCategory(TransactionType type)
        {
            return ((int)type) switch
            {
                >= 1 and <= 10 => "مبيعات",
                >= 11 and <= 20 => "مشتريات",
                >= 21 and <= 30 => "نقدية وبنوك",
                >= 31 and <= 40 => "مخزون",
                >= 41 and <= 50 => "موظفين",
                >= 51 and <= 60 => "مصروفات",
                >= 61 and <= 70 => "ضرائب",
                >= 71 and <= 80 => "أصول",
                >= 81 and <= 90 => "قروض",
                >= 91 and <= 100 => "استثمارات",
                >= 101 and <= 110 => "تسويات",
                >= 111 and <= 120 => "أخرى",
                _ => "عام"
            };
        }
    }
}
