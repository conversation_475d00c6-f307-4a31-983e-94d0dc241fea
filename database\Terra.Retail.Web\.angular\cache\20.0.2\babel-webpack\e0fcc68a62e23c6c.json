{"ast": null, "code": "import { FocusMonitor, _Id<PERSON>enerator, FocusKeyManager } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, booleanAttribute, numberAttribute, Directive, Input, ChangeDetectorRef, HOST_TAG_NAME, NgZone, DOCUMENT, EventEmitter, Injector, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, Output, ContentChild, ViewChild, afterNextRender, QueryList, forwardRef, NgModule } from '@angular/core';\nimport { Subject, merge } from 'rxjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BYgV4oZC.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nimport { takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR, NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { k as MatFormFieldControl, h as MAT_FORM_FIELD } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\n\n/** Injection token to be used to override the default options for the chips module. */\nconst _c0 = [\"*\", [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c1 = [\"*\", \"mat-chip-avatar, [matChipAvatar]\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChip_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChip_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipOption_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 9);\n    i0.ɵɵelement(4, \"path\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MatChipOption_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\";\nconst _c3 = [[[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"\", \"matChipEditInput\", \"\"]], \"*\", [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c4 = [\"mat-chip-avatar, [matChipAvatar]\", \"[matChipEditInput]\", \"*\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChipRow_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n}\nfunction MatChipRow_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n}\nfunction MatChipRow_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatChipRow_Conditional_4_Conditional_0_Template, 1, 0)(1, MatChipRow_Conditional_4_Conditional_1_Template, 1, 0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.contentEditInput ? 0 : 1);\n  }\n}\nfunction MatChipRow_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatChipRow_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c5 = [\"*\"];\nconst _c6 = \".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\";\nconst MAT_CHIPS_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-chips-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    separatorKeyCodes: [ENTER]\n  })\n});\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = /*#__PURE__*/new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = /*#__PURE__*/new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = /*#__PURE__*/new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = /*#__PURE__*/new InjectionToken('MatChip');\n\n/**\n * Section within a chip.\n * @docs-private\n */\nlet MatChipAction = /*#__PURE__*/(() => {\n  class MatChipAction {\n    _elementRef = inject(ElementRef);\n    _parentChip = inject(MAT_CHIP);\n    /** Whether the action is interactive. */\n    isInteractive = true;\n    /** Whether this is the primary action in the chip. */\n    _isPrimary = true;\n    /** Whether the action is disabled. */\n    get disabled() {\n      return this._disabled || this._parentChip?.disabled || false;\n    }\n    set disabled(value) {\n      this._disabled = value;\n    }\n    _disabled = false;\n    /** Tab index of the action. */\n    tabIndex = -1;\n    /**\n     * Private API to allow focusing this chip when it is disabled.\n     */\n    _allowFocusWhenDisabled = false;\n    /**\n     * Determine the value of the disabled attribute for this chip action.\n     */\n    _getDisabledAttribute() {\n      // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n      // string to indicate that disabled attribute should be included.\n      return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n    }\n    /**\n     * Determine the value of the tabindex attribute for this chip action.\n     */\n    _getTabindex() {\n      return this.disabled && !this._allowFocusWhenDisabled || !this.isInteractive ? null : this.tabIndex.toString();\n    }\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n        this._elementRef.nativeElement.setAttribute('type', 'button');\n      }\n    }\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    _handleClick(event) {\n      if (!this.disabled && this.isInteractive && this._isPrimary) {\n        event.preventDefault();\n        this._parentChip._handlePrimaryActionInteraction();\n      }\n    }\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled && this.isInteractive && this._isPrimary && !this._parentChip._isEditing) {\n        event.preventDefault();\n        this._parentChip._handlePrimaryActionInteraction();\n      }\n    }\n    static ɵfac = function MatChipAction_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipAction)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipAction,\n      selectors: [[\"\", \"matChipAction\", \"\"]],\n      hostAttrs: [1, \"mdc-evolution-chip__action\", \"mat-mdc-chip-action\"],\n      hostVars: 9,\n      hostBindings: function MatChipAction_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatChipAction_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"keydown\", function MatChipAction_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx._getTabindex())(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵclassProp(\"mdc-evolution-chip__action--primary\", ctx._isPrimary)(\"mdc-evolution-chip__action--presentational\", !ctx.isInteractive)(\"mdc-evolution-chip__action--trailing\", !ctx._isPrimary);\n        }\n      },\n      inputs: {\n        isInteractive: \"isInteractive\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? -1 : numberAttribute(value)],\n        _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\"\n      }\n    });\n  }\n  return MatChipAction;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Avatar image within a chip. */\nlet MatChipAvatar = /*#__PURE__*/(() => {\n  class MatChipAvatar {\n    static ɵfac = function MatChipAvatar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipAvatar)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipAvatar,\n      selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n      hostAttrs: [\"role\", \"img\", 1, \"mat-mdc-chip-avatar\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--primary\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }])]\n    });\n  }\n  return MatChipAvatar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Non-interactive trailing icon in a chip. */\nlet MatChipTrailingIcon = /*#__PURE__*/(() => {\n  class MatChipTrailingIcon extends MatChipAction {\n    /**\n     * MDC considers all trailing actions as a remove icon,\n     * but we support non-interactive trailing icons.\n     */\n    isInteractive = false;\n    _isPrimary = false;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatChipTrailingIcon_BaseFactory;\n      return function MatChipTrailingIcon_Factory(__ngFactoryType__) {\n        return (ɵMatChipTrailingIcon_BaseFactory || (ɵMatChipTrailingIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipTrailingIcon)))(__ngFactoryType__ || MatChipTrailingIcon);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipTrailingIcon,\n      selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n      hostAttrs: [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-trailing-icon\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatChipTrailingIcon;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nlet MatChipRemove = /*#__PURE__*/(() => {\n  class MatChipRemove extends MatChipAction {\n    _isPrimary = false;\n    _handleClick(event) {\n      if (!this.disabled) {\n        event.stopPropagation();\n        event.preventDefault();\n        this._parentChip.remove();\n      }\n    }\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n        event.stopPropagation();\n        event.preventDefault();\n        this._parentChip.remove();\n      }\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatChipRemove_BaseFactory;\n      return function MatChipRemove_Factory(__ngFactoryType__) {\n        return (ɵMatChipRemove_BaseFactory || (ɵMatChipRemove_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipRemove)))(__ngFactoryType__ || MatChipRemove);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipRemove,\n      selectors: [[\"\", \"matChipRemove\", \"\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-mdc-chip-remove\", \"mat-mdc-chip-trailing-icon\", \"mat-focus-indicator\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n      hostVars: 1,\n      hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-hidden\", null);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatChipRemove;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nlet MatChip = /*#__PURE__*/(() => {\n  class MatChip {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _tagName = inject(HOST_TAG_NAME);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    _document = inject(DOCUMENT);\n    /** Emits when the chip is focused. */\n    _onFocus = new Subject();\n    /** Emits when the chip is blurred. */\n    _onBlur = new Subject();\n    /** Whether this chip is a basic (unstyled) chip. */\n    _isBasicChip;\n    /** Role for the root of the chip. */\n    role = null;\n    /** Whether the chip has focus. */\n    _hasFocusInternal = false;\n    /** Whether moving focus into the chip is pending. */\n    _pendingFocus;\n    /** Subscription to changes in the chip's actions. */\n    _actionChanges;\n    /** Whether animations for the chip are enabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All avatars present in the chip. */\n    _allLeadingIcons;\n    /** All trailing icons present in the chip. */\n    _allTrailingIcons;\n    /** All remove icons present in the chip. */\n    _allRemoveIcons;\n    _hasFocus() {\n      return this._hasFocusInternal;\n    }\n    /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-');\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA label for the content of the chip. */\n    ariaLabel = null;\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA description for the content of the chip. */\n    ariaDescription = null;\n    /** Id of a span that contains this chip's aria description. */\n    _ariaDescriptionId = `${this.id}-aria-description`;\n    /** Whether the chip list is disabled. */\n    _chipListDisabled = false;\n    _textElement;\n    /**\n     * The value of the chip. Defaults to the content inside\n     * the `mat-mdc-chip-action-label` element.\n     */\n    get value() {\n      return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n    }\n    set value(value) {\n      this._value = value;\n    }\n    _value;\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the chip. This API is supported in M2 themes only, it has no\n     * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    removable = true;\n    /**\n     * Colors the chip for emphasis as if it were selected.\n     */\n    highlighted = false;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    /** Whether the chip is disabled. */\n    get disabled() {\n      return this._disabled || this._chipListDisabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n    }\n    _disabled = false;\n    /** Emitted when a chip is to be removed. */\n    removed = new EventEmitter();\n    /** Emitted when the chip is destroyed. */\n    destroyed = new EventEmitter();\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip';\n    /** The chip's leading icon. */\n    leadingIcon;\n    /** The chip's trailing icon. */\n    trailingIcon;\n    /** The chip's trailing remove icon. */\n    removeIcon;\n    /** Action receiving the primary set of user interactions. */\n    primaryAction;\n    /**\n     * Handles the lazy creation of the MatChip ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    _injector = inject(Injector);\n    constructor() {\n      const styleLoader = inject(_CdkPrivateStyleLoader);\n      styleLoader.load(_StructuralStylesLoader);\n      styleLoader.load(_VisuallyHiddenLoader);\n      this._monitorFocus();\n      this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n        className: 'mat-mdc-chip-ripple',\n        disabled: this._isRippleDisabled()\n      });\n    }\n    ngOnInit() {\n      // This check needs to happen in `ngOnInit` so the overridden value of\n      // `basicChipAttrName` coming from base classes can be picked up.\n      this._isBasicChip = this._elementRef.nativeElement.hasAttribute(this.basicChipAttrName) || this._tagName.toLowerCase() === this.basicChipAttrName;\n    }\n    ngAfterViewInit() {\n      this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n      if (this._pendingFocus) {\n        this._pendingFocus = false;\n        this.focus();\n      }\n    }\n    ngAfterContentInit() {\n      // Since the styling depends on the presence of some\n      // actions, we have to mark for check on changes.\n      this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    ngDoCheck() {\n      this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n      this._actionChanges?.unsubscribe();\n      this.destroyed.emit({\n        chip: this\n      });\n      this.destroyed.complete();\n    }\n    /**\n     * Allows for programmatic removal of the chip.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n      if (this.removable) {\n        this.removed.emit({\n          chip: this\n        });\n      }\n    }\n    /** Whether or not the ripple should be disabled. */\n    _isRippleDisabled() {\n      return this.disabled || this.disableRipple || this._animationsDisabled || this._isBasicChip || !!this._globalRippleOptions?.disabled;\n    }\n    /** Returns whether the chip has a trailing icon. */\n    _hasTrailingIcon() {\n      return !!(this.trailingIcon || this.removeIcon);\n    }\n    /** Handles keyboard events on the chip. */\n    _handleKeydown(event) {\n      // Ignore backspace events where the user is holding down the key\n      // so that we don't accidentally remove too many chips.\n      if (event.keyCode === BACKSPACE && !event.repeat || event.keyCode === DELETE) {\n        event.preventDefault();\n        this.remove();\n      }\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n      if (!this.disabled) {\n        // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n        // This can happen if the consumer tries to focus a chip immediately after it is added.\n        // Queue the method to be called again on init.\n        if (this.primaryAction) {\n          this.primaryAction.focus();\n        } else {\n          this._pendingFocus = true;\n        }\n      }\n    }\n    /** Gets the action that contains a specific target node. */\n    _getSourceAction(target) {\n      return this._getActions().find(action => {\n        const element = action._elementRef.nativeElement;\n        return element === target || element.contains(target);\n      });\n    }\n    /** Gets all of the actions within the chip. */\n    _getActions() {\n      const result = [];\n      if (this.primaryAction) {\n        result.push(this.primaryAction);\n      }\n      if (this.removeIcon) {\n        result.push(this.removeIcon);\n      }\n      if (this.trailingIcon) {\n        result.push(this.trailingIcon);\n      }\n      return result;\n    }\n    /** Handles interactions with the primary action of the chip. */\n    _handlePrimaryActionInteraction() {\n      // Empty here, but is overwritten in child classes.\n    }\n    /** Starts the focus monitoring process on the chip. */\n    _monitorFocus() {\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n        const hasFocus = origin !== null;\n        if (hasFocus !== this._hasFocusInternal) {\n          this._hasFocusInternal = hasFocus;\n          if (hasFocus) {\n            this._onFocus.next({\n              chip: this\n            });\n          } else {\n            // When animations are enabled, Angular may end up removing the chip from the DOM a little\n            // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n            // that moves focus to the next item. To work around the issue, we defer marking the chip\n            // as not focused until after the next render.\n            this._changeDetectorRef.markForCheck();\n            setTimeout(() => this._ngZone.run(() => this._onBlur.next({\n              chip: this\n            })));\n          }\n        }\n      });\n    }\n    static ɵfac = function MatChip_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChip)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChip,\n      selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n      contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leadingIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allLeadingIcons = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTrailingIcons = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allRemoveIcons = _t);\n        }\n      },\n      viewQuery: function MatChip_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatChipAction, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.primaryAction = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip\"],\n      hostVars: 31,\n      hostBindings: function MatChip_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"role\", ctx.role)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n          i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-basic-chip\", ctx._isBasicChip)(\"mat-mdc-standard-chip\", !ctx._isBasicChip)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon())(\"_mat-animation-noopable\", ctx._animationsDisabled);\n        }\n      },\n      inputs: {\n        role: \"role\",\n        id: \"id\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaDescription: [0, \"aria-description\", \"ariaDescription\"],\n        value: \"value\",\n        color: \"color\",\n        removable: [2, \"removable\", \"removable\", booleanAttribute],\n        highlighted: [2, \"highlighted\", \"highlighted\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        removed: \"removed\",\n        destroyed: \"destroyed\"\n      },\n      exportAs: [\"matChip\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_CHIP,\n        useExisting: MatChip\n      }])],\n      ngContentSelectors: _c1,\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", 3, \"isInteractive\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n      template: function MatChip_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelement(0, \"span\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1)(2, \"span\", 2);\n          i0.ɵɵconditionalCreate(3, MatChip_Conditional_3_Template, 2, 0, \"span\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵconditionalCreate(7, MatChip_Conditional_7_Template, 2, 0, \"span\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"isInteractive\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.leadingIcon ? 3 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n        }\n      },\n      dependencies: [MatChipAction],\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChip;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n  source;\n  selected;\n  isUserInput;\n  constructor(/** Reference to the chip that emitted the event. */\n  source, /** Whether the chip that emitted the event is selected. */\n  selected, /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nlet MatChipOption = /*#__PURE__*/(() => {\n  class MatChipOption extends MatChip {\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    /** Whether the chip list is selectable. */\n    chipListSelectable = true;\n    /** Whether the chip list is in multi-selection mode. */\n    _chipListMultiple = false;\n    /** Whether the chip list hides single-selection indicator. */\n    _chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /**\n     * Whether or not the chip is selectable.\n     *\n     * When a chip is not selectable, changes to its selected state are always\n     * ignored. By default an option chip is selectable, and it becomes\n     * non-selectable if its parent chip list is not selectable.\n     */\n    get selectable() {\n      return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n      this._selectable = value;\n      this._changeDetectorRef.markForCheck();\n    }\n    _selectable = true;\n    /** Whether the chip is selected. */\n    get selected() {\n      return this._selected;\n    }\n    set selected(value) {\n      this._setSelectedState(value, false, true);\n    }\n    _selected = false;\n    /**\n     * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n     * interaction patterns.\n     *\n     * From [WAI ARIA Listbox authoring practices guide](\n     * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n     *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n     *  set to true. All options that are selectable but not selected have either aria-selected or\n     *  aria-checked set to false.\"\n     *\n     * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n     * VoiceOver reading every option as \"selected\" (#25736).\n     */\n    get ariaSelected() {\n      return this.selectable ? this.selected.toString() : null;\n    }\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip-option';\n    /** Emitted when the chip is selected or deselected. */\n    selectionChange = new EventEmitter();\n    ngOnInit() {\n      super.ngOnInit();\n      this.role = 'presentation';\n    }\n    /** Selects the chip. */\n    select() {\n      this._setSelectedState(true, false, true);\n    }\n    /** Deselects the chip. */\n    deselect() {\n      this._setSelectedState(false, false, true);\n    }\n    /** Selects this chip and emits userInputSelection event */\n    selectViaInteraction() {\n      this._setSelectedState(true, true, true);\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n      this._setSelectedState(!this.selected, isUserInput, true);\n      return this.selected;\n    }\n    _handlePrimaryActionInteraction() {\n      if (!this.disabled) {\n        // Interacting with the primary action implies that the chip already has focus, however\n        // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n        // We work around it by explicitly focusing the primary action of the current chip.\n        this.focus();\n        if (this.selectable) {\n          this.toggleSelected(true);\n        }\n      }\n    }\n    _hasLeadingGraphic() {\n      if (this.leadingIcon) {\n        return true;\n      }\n      // The checkmark graphic communicates selected state for both single-select and multi-select.\n      // Include checkmark in single-select to fix a11y issue where selected state is communicated\n      // visually only using color (#25886).\n      return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n    }\n    _setSelectedState(isSelected, isUserInput, emitEvent) {\n      if (isSelected !== this.selected) {\n        this._selected = isSelected;\n        if (emitEvent) {\n          this.selectionChange.emit({\n            source: this,\n            isUserInput,\n            selected: this.selected\n          });\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatChipOption_BaseFactory;\n      return function MatChipOption_Factory(__ngFactoryType__) {\n        return (ɵMatChipOption_BaseFactory || (ɵMatChipOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipOption)))(__ngFactoryType__ || MatChipOption);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipOption,\n      selectors: [[\"mat-basic-chip-option\"], [\"\", \"mat-basic-chip-option\", \"\"], [\"mat-chip-option\"], [\"\", \"mat-chip-option\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-option\"],\n      hostVars: 37,\n      hostBindings: function MatChipOption_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n          i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--filter\", !ctx._isBasicChip)(\"mdc-evolution-chip--selectable\", !ctx._isBasicChip)(\"mat-mdc-chip-selected\", ctx.selected)(\"mat-mdc-chip-multiple\", ctx._chipListMultiple)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--selected\", ctx.selected)(\"mdc-evolution-chip--selecting\", !ctx._animationsDisabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-graphic\", ctx._hasLeadingGraphic())(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n        }\n      },\n      inputs: {\n        selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n        selected: [2, \"selected\", \"selected\", booleanAttribute]\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatChip,\n        useExisting: MatChipOption\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipOption\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 10,\n      vars: 8,\n      consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", \"role\", \"option\", 3, \"_allowFocusWhenDisabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [1, \"mdc-evolution-chip__checkmark\"], [\"viewBox\", \"-2 -3 30 30\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mdc-evolution-chip__checkmark-svg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-evolution-chip__checkmark-path\"]],\n      template: function MatChipOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelement(0, \"span\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1)(2, \"button\", 2);\n          i0.ɵɵconditionalCreate(3, MatChipOption_Conditional_3_Template, 5, 0, \"span\", 3);\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵprojection(5);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵconditionalCreate(7, MatChipOption_Conditional_7_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"_allowFocusWhenDisabled\", true);\n          i0.ɵɵattribute(\"aria-selected\", ctx.ariaSelected)(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasLeadingGraphic() ? 3 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.ariaDescription);\n        }\n      },\n      dependencies: [MatChipAction],\n      styles: [_c2],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipOption;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nlet MatChipEditInput = /*#__PURE__*/(() => {\n  class MatChipEditInput {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    constructor() {}\n    initialize(initialValue) {\n      this.getNativeElement().focus();\n      this.setValue(initialValue);\n    }\n    getNativeElement() {\n      return this._elementRef.nativeElement;\n    }\n    setValue(value) {\n      this.getNativeElement().textContent = value;\n      this._moveCursorToEndOfInput();\n    }\n    getValue() {\n      return this.getNativeElement().textContent || '';\n    }\n    _moveCursorToEndOfInput() {\n      const range = this._document.createRange();\n      range.selectNodeContents(this.getNativeElement());\n      range.collapse(false);\n      const sel = window.getSelection();\n      sel.removeAllRanges();\n      sel.addRange(range);\n    }\n    static ɵfac = function MatChipEditInput_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipEditInput)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipEditInput,\n      selectors: [[\"span\", \"matChipEditInput\", \"\"]],\n      hostAttrs: [\"role\", \"textbox\", \"tabindex\", \"-1\", \"contenteditable\", \"true\", 1, \"mat-chip-edit-input\"]\n    });\n  }\n  return MatChipEditInput;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nlet MatChipRow = /*#__PURE__*/(() => {\n  class MatChipRow extends MatChip {\n    basicChipAttrName = 'mat-basic-chip-row';\n    /**\n     * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n     * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n     * while the editing action is being initialized.\n     */\n    _editStartPending = false;\n    editable = false;\n    /** Emitted when the chip is edited. */\n    edited = new EventEmitter();\n    /** The default chip edit input that is used if none is projected into this chip row. */\n    defaultEditInput;\n    /** The projected chip edit input. */\n    contentEditInput;\n    _isEditing = false;\n    constructor() {\n      super();\n      this.role = 'row';\n      this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n        if (this._isEditing && !this._editStartPending) {\n          this._onEditFinish();\n        }\n      });\n    }\n    _hasTrailingIcon() {\n      // The trailing icon is hidden while editing.\n      return !this._isEditing && super._hasTrailingIcon();\n    }\n    /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n    _handleFocus() {\n      if (!this._isEditing && !this.disabled) {\n        this.focus();\n      }\n    }\n    _handleKeydown(event) {\n      if (event.keyCode === ENTER && !this.disabled) {\n        if (this._isEditing) {\n          event.preventDefault();\n          this._onEditFinish();\n        } else if (this.editable) {\n          this._startEditing(event);\n        }\n      } else if (this._isEditing) {\n        // Stop the event from reaching the chip set in order to avoid navigating.\n        event.stopPropagation();\n      } else {\n        super._handleKeydown(event);\n      }\n    }\n    _handleDoubleclick(event) {\n      if (!this.disabled && this.editable) {\n        this._startEditing(event);\n      }\n    }\n    _startEditing(event) {\n      if (!this.primaryAction || this.removeIcon && this._getSourceAction(event.target) === this.removeIcon) {\n        return;\n      }\n      // The value depends on the DOM so we need to extract it before we flip the flag.\n      const value = this.value;\n      this._isEditing = this._editStartPending = true;\n      // Defer initializing the input until after it has been added to the DOM.\n      afterNextRender(() => {\n        this._getEditInput().initialize(value);\n        this._editStartPending = false;\n      }, {\n        injector: this._injector\n      });\n    }\n    _onEditFinish() {\n      this._isEditing = this._editStartPending = false;\n      this.edited.emit({\n        chip: this,\n        value: this._getEditInput().getValue()\n      });\n      // If the edit input is still focused or focus was returned to the body after it was destroyed,\n      // return focus to the chip contents.\n      if (this._document.activeElement === this._getEditInput().getNativeElement() || this._document.activeElement === this._document.body) {\n        this.primaryAction.focus();\n      }\n    }\n    _isRippleDisabled() {\n      return super._isRippleDisabled() || this._isEditing;\n    }\n    /**\n     * Gets the projected chip edit input, or the default input if none is projected in. One of these\n     * two values is guaranteed to be defined.\n     */\n    _getEditInput() {\n      return this.contentEditInput || this.defaultEditInput;\n    }\n    static ɵfac = function MatChipRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipRow)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipRow,\n      selectors: [[\"mat-chip-row\"], [\"\", \"mat-chip-row\", \"\"], [\"mat-basic-chip-row\"], [\"\", \"mat-basic-chip-row\", \"\"]],\n      contentQueries: function MatChipRow_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipEditInput, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentEditInput = _t.first);\n        }\n      },\n      viewQuery: function MatChipRow_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatChipEditInput, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultEditInput = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-row\", \"mdc-evolution-chip\"],\n      hostVars: 27,\n      hostBindings: function MatChipRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipRow_focus_HostBindingHandler() {\n            return ctx._handleFocus();\n          })(\"dblclick\", function MatChipRow_dblclick_HostBindingHandler($event) {\n            return ctx._handleDoubleclick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : -1)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n          i0.ɵɵclassProp(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-editing\", ctx._isEditing)(\"mat-mdc-chip-editable\", ctx.editable)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n        }\n      },\n      inputs: {\n        editable: \"editable\"\n      },\n      outputs: {\n        edited: \"edited\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatChip,\n        useExisting: MatChipRow\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c4,\n      decls: 10,\n      vars: 9,\n      consts: [[1, \"mat-mdc-chip-focus-overlay\"], [\"role\", \"gridcell\", \"matChipAction\", \"\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\", 3, \"disabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [\"role\", \"gridcell\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [\"matChipEditInput\", \"\"]],\n      template: function MatChipRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵconditionalCreate(0, MatChipRow_Conditional_0_Template, 1, 0, \"span\", 0);\n          i0.ɵɵelementStart(1, \"span\", 1);\n          i0.ɵɵconditionalCreate(2, MatChipRow_Conditional_2_Template, 2, 0, \"span\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵconditionalCreate(4, MatChipRow_Conditional_4_Template, 2, 1)(5, MatChipRow_Conditional_5_Template, 1, 0);\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵconditionalCreate(7, MatChipRow_Conditional_7_Template, 2, 0, \"span\", 5);\n          i0.ɵɵelementStart(8, \"span\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(!ctx._isEditing ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.leadingIcon ? 2 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._isEditing ? 4 : 5);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.ariaDescription);\n        }\n      },\n      dependencies: [MatChipAction, MatChipEditInput],\n      styles: [_c2],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nlet MatChipSet = /*#__PURE__*/(() => {\n  class MatChipSet {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    /** Index of the last destroyed chip that had focus. */\n    _lastDestroyedFocusedChipIndex = null;\n    /** Used to manage focus within the chip list. */\n    _keyManager;\n    /** Subject that emits when the component has been destroyed. */\n    _destroyed = new Subject();\n    /** Role to use if it hasn't been overwritten by the user. */\n    _defaultRole = 'presentation';\n    /** Combined stream of all of the child chips' focus events. */\n    get chipFocusChanges() {\n      return this._getChipStream(chip => chip._onFocus);\n    }\n    /** Combined stream of all of the child chips' destroy events. */\n    get chipDestroyedChanges() {\n      return this._getChipStream(chip => chip.destroyed);\n    }\n    /** Combined stream of all of the child chips' remove events. */\n    get chipRemovedChanges() {\n      return this._getChipStream(chip => chip.removed);\n    }\n    /** Whether the chip set is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._syncChipsState();\n    }\n    _disabled = false;\n    /** Whether the chip list contains chips or not. */\n    get empty() {\n      return !this._chips || this._chips.length === 0;\n    }\n    /** The ARIA role applied to the chip set. */\n    get role() {\n      if (this._explicitRole) {\n        return this._explicitRole;\n      }\n      return this.empty ? null : this._defaultRole;\n    }\n    /** Tabindex of the chip set. */\n    tabIndex = 0;\n    set role(value) {\n      this._explicitRole = value;\n    }\n    _explicitRole = null;\n    /** Whether any of the chips inside of this chip-set has focus. */\n    get focused() {\n      return this._hasFocusedChip();\n    }\n    /** The chips that are part of this chip set. */\n    _chips;\n    /** Flat list of all the actions contained within the chips. */\n    _chipActions = new QueryList();\n    constructor() {}\n    ngAfterViewInit() {\n      this._setUpFocusManagement();\n      this._trackChipSetChanges();\n      this._trackDestroyedFocusedChip();\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._chipActions.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n      return this._chips && this._chips.some(chip => chip._hasFocus());\n    }\n    /** Syncs the chip-set's state with the individual chips. */\n    _syncChipsState() {\n      this._chips?.forEach(chip => {\n        chip._chipListDisabled = this._disabled;\n        chip._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n    focus() {}\n    /** Handles keyboard events on the chip set. */\n    _handleKeydown(event) {\n      if (this._originatesFromChip(event)) {\n        this._keyManager.onKeydown(event);\n      }\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n      return index >= 0 && index < this._chips.length;\n    }\n    /**\n     * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the set from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n      const previous = this._elementRef.nativeElement.tabIndex;\n      if (previous !== -1) {\n        // Set the tabindex directly on the element, instead of going through\n        // the data binding, because we aren't guaranteed that change detection\n        // will run quickly enough to allow focus to escape.\n        this._elementRef.nativeElement.tabIndex = -1;\n        // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n        // doesn't allow enough time for the focus to escape.\n        setTimeout(() => this._elementRef.nativeElement.tabIndex = previous);\n      }\n    }\n    /**\n     * Gets a stream of events from all the chips within the set.\n     * The stream will automatically incorporate any newly-added chips.\n     */\n    _getChipStream(mappingFunction) {\n      return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n      let currentElement = event.target;\n      while (currentElement && currentElement !== this._elementRef.nativeElement) {\n        if (currentElement.classList.contains('mat-mdc-chip')) {\n          return true;\n        }\n        currentElement = currentElement.parentElement;\n      }\n      return false;\n    }\n    /** Sets up the chip set's focus management logic. */\n    _setUpFocusManagement() {\n      // Create a flat `QueryList` containing the actions of all of the chips.\n      // This allows us to navigate both within the chip and move to the next/previous\n      // one using the existing `ListKeyManager`.\n      this._chips.changes.pipe(startWith(this._chips)).subscribe(chips => {\n        const actions = [];\n        chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n        this._chipActions.reset(actions);\n        this._chipActions.notifyOnChanges();\n      });\n      this._keyManager = new FocusKeyManager(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr').withHomeAndEnd().skipPredicate(action => this._skipPredicate(action));\n      // Keep the manager active index in sync so that navigation picks\n      // up from the current chip if the user clicks into the list directly.\n      this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({\n        chip\n      }) => {\n        const action = chip._getSourceAction(document.activeElement);\n        if (action) {\n          this._keyManager.updateActiveItem(action);\n        }\n      });\n      this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive and disabled actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n      // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n      // chips.\n      return !action.isInteractive || action.disabled;\n    }\n    /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n    _trackChipSetChanges() {\n      this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        if (this.disabled) {\n          // Since this happens after the content has been\n          // checked, we need to defer it to the next tick.\n          Promise.resolve().then(() => this._syncChipsState());\n        }\n        this._redirectDestroyedChipFocus();\n      });\n    }\n    /** Starts tracking the destroyed chips in order to capture the focused one. */\n    _trackDestroyedFocusedChip() {\n      this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n        const chipArray = this._chips.toArray();\n        const chipIndex = chipArray.indexOf(event.chip);\n        // If the focused chip is destroyed, save its index so that we can move focus to the next\n        // chip. We only save the index here, rather than move the focus immediately, because we want\n        // to wait until the chip is removed from the chip list before focusing the next one. This\n        // allows us to keep focus on the same index if the chip gets swapped out.\n        if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n          this._lastDestroyedFocusedChipIndex = chipIndex;\n        }\n      });\n    }\n    /**\n     * Finds the next appropriate chip to move focus to,\n     * if the currently-focused chip is destroyed.\n     */\n    _redirectDestroyedChipFocus() {\n      if (this._lastDestroyedFocusedChipIndex == null) {\n        return;\n      }\n      if (this._chips.length) {\n        const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n        const chipToFocus = this._chips.toArray()[newIndex];\n        if (chipToFocus.disabled) {\n          // If we're down to one disabled chip, move focus back to the set.\n          if (this._chips.length === 1) {\n            this.focus();\n          } else {\n            this._keyManager.setPreviousItemActive();\n          }\n        } else {\n          chipToFocus.focus();\n        }\n      } else {\n        this.focus();\n      }\n      this._lastDestroyedFocusedChipIndex = null;\n    }\n    static ɵfac = function MatChipSet_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipSet)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipSet,\n      selectors: [[\"mat-chip-set\"]],\n      contentQueries: function MatChipSet_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip-set\", \"mdc-evolution-chip-set\"],\n      hostVars: 1,\n      hostBindings: function MatChipSet_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChipSet_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role);\n        }\n      },\n      inputs: {\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        role: \"role\",\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipSet_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipSet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n  source;\n  value;\n  constructor(/** Chip listbox that emitted the event. */\n  source, /** Value of the chip listbox when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatChipListbox),\n  multi: true\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nlet MatChipListbox = /*#__PURE__*/(() => {\n  class MatChipListbox extends MatChipSet {\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => {};\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => {};\n    // TODO: MDC uses `grid` here\n    _defaultRole = 'listbox';\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      this._multiple = value;\n      this._syncListboxProperties();\n    }\n    _multiple = false;\n    /** The array of selected chips inside the chip listbox. */\n    get selected() {\n      const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n      return this.multiple ? selectedChips : selectedChips[0];\n    }\n    /** Orientation of the chip list. */\n    ariaOrientation = 'horizontal';\n    /**\n     * Whether or not this chip listbox is selectable.\n     *\n     * When a chip listbox is not selectable, the selected states for all\n     * the chips inside the chip listbox are always ignored.\n     */\n    get selectable() {\n      return this._selectable;\n    }\n    set selectable(value) {\n      this._selectable = value;\n      this._syncListboxProperties();\n    }\n    _selectable = true;\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    compareWith = (o1, o2) => o1 === o2;\n    /** Whether this chip listbox is required. */\n    required = false;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = value;\n      this._syncListboxProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n      return this._getChipStream(chip => chip.selectionChange);\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n      return this._getChipStream(chip => chip._onBlur);\n    }\n    /** The value of the listbox, which is the combined value of the selected chips. */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      if (this._chips && this._chips.length) {\n        this._setSelectionByValue(value, false);\n      }\n      this._value = value;\n    }\n    _value;\n    /** Event emitted when the selected chip listbox value has been changed by the user. */\n    change = new EventEmitter();\n    _chips = undefined;\n    ngAfterContentInit() {\n      this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        if (this.value !== undefined) {\n          Promise.resolve().then(() => {\n            this._setSelectionByValue(this.value, false);\n          });\n        }\n        // Update listbox selectable/multiple properties on chips\n        this._syncListboxProperties();\n      });\n      this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n      this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n        if (!this.multiple) {\n          this._chips.forEach(chip => {\n            if (chip !== event.source) {\n              chip._setSelectedState(false, false, false);\n            }\n          });\n        }\n        if (event.isUserInput) {\n          this._propagateChanges();\n        }\n      });\n    }\n    /**\n     * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n     * are no selected chips.\n     */\n    focus() {\n      if (this.disabled) {\n        return;\n      }\n      const firstSelectedChip = this._getFirstSelectedChip();\n      if (firstSelectedChip && !firstSelectedChip.disabled) {\n        firstSelectedChip.focus();\n      } else if (this._chips.length > 0) {\n        this._keyManager.setFirstItemActive();\n      } else {\n        this._elementRef.nativeElement.focus();\n      }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n      if (value != null) {\n        this.value = value;\n      } else {\n        this.value = undefined;\n      }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    /** Selects all chips with value. */\n    _setSelectionByValue(value, isUserInput = true) {\n      this._clearSelection();\n      if (Array.isArray(value)) {\n        value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n      } else {\n        this._selectValue(value, isUserInput);\n      }\n    }\n    /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n    _blur() {\n      if (!this.disabled) {\n        // Wait to see if focus moves to an individual chip.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._markAsTouched();\n          }\n        });\n      }\n    }\n    _keydown(event) {\n      if (event.keyCode === TAB) {\n        super._allowFocusEscape();\n      }\n    }\n    /** Marks the field as touched */\n    _markAsTouched() {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n      let valueToEmit = null;\n      if (Array.isArray(this.selected)) {\n        valueToEmit = this.selected.map(chip => chip.value);\n      } else {\n        valueToEmit = this.selected ? this.selected.value : undefined;\n      }\n      this._value = valueToEmit;\n      this.change.emit(new MatChipListboxChange(this, valueToEmit));\n      this._onChange(valueToEmit);\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Deselects every chip in the listbox.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n      this._chips.forEach(chip => {\n        if (chip !== skip) {\n          chip.deselect();\n        }\n      });\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput) {\n      const correspondingChip = this._chips.find(chip => {\n        return chip.value != null && this.compareWith(chip.value, value);\n      });\n      if (correspondingChip) {\n        isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n      }\n      return correspondingChip;\n    }\n    /** Syncs the chip-listbox selection state with the individual chips. */\n    _syncListboxProperties() {\n      if (this._chips) {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n          this._chips.forEach(chip => {\n            chip._chipListMultiple = this.multiple;\n            chip.chipListSelectable = this._selectable;\n            chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n            chip._changeDetectorRef.markForCheck();\n          });\n        });\n      }\n    }\n    /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n    _getFirstSelectedChip() {\n      if (Array.isArray(this.selected)) {\n        return this.selected.length ? this.selected[0] : undefined;\n      } else {\n        return this.selected;\n      }\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n      // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n      // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n      // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n      // exceptions for compound widgets.\n      //\n      // From [Developing a Keyboard Interface](\n      // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n      //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n      //   Listbox...\"\n      return !action.isInteractive;\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatChipListbox_BaseFactory;\n      return function MatChipListbox_Factory(__ngFactoryType__) {\n        return (ɵMatChipListbox_BaseFactory || (ɵMatChipListbox_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipListbox)))(__ngFactoryType__ || MatChipListbox);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipListbox,\n      selectors: [[\"mat-chip-listbox\"]],\n      contentQueries: function MatChipListbox_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipOption, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [1, \"mdc-evolution-chip-set\", \"mat-mdc-chip-listbox\"],\n      hostVars: 10,\n      hostBindings: function MatChipListbox_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipListbox_focus_HostBindingHandler() {\n            return ctx.focus();\n          })(\"blur\", function MatChipListbox_blur_HostBindingHandler() {\n            return ctx._blur();\n          })(\"keydown\", function MatChipListbox_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"tabIndex\", ctx.disabled || ctx.empty ? -1 : ctx.tabIndex);\n          i0.ɵɵattribute(\"role\", ctx.role)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-multiselectable\", ctx.multiple)(\"aria-orientation\", ctx.ariaOrientation);\n          i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-required\", ctx.required);\n        }\n      },\n      inputs: {\n        multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n        ariaOrientation: [0, \"aria-orientation\", \"ariaOrientation\"],\n        selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n        compareWith: \"compareWith\",\n        required: [2, \"required\", \"required\", booleanAttribute],\n        hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        value: \"value\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      features: [i0.ɵɵProvidersFeature([MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipListbox_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipListbox;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n  source;\n  value;\n  constructor(/** Chip grid that emitted the event. */\n  source, /** Value of the chip grid when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nlet MatChipGrid = /*#__PURE__*/(() => {\n  class MatChipGrid extends MatChipSet {\n    ngControl = inject(NgControl, {\n      optional: true,\n      self: true\n    });\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-chip-grid';\n    /** The chip input to add more chips */\n    _chipInput;\n    _defaultRole = 'grid';\n    _errorStateTracker;\n    /**\n     * List of element ids to propagate to the chipInput's aria-describedby attribute.\n     */\n    _ariaDescribedbyIds = [];\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => {};\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => {};\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n      return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._syncChipsState();\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n      return this._chipInput.id;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n      return (!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n      return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n      this._placeholder = value;\n      this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n    get focused() {\n      return this._chipInput.focused || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = value;\n      this.stateChanges.next();\n    }\n    _required;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n      return this._value;\n    }\n    set value(value) {\n      this._value = value;\n    }\n    _value = [];\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n      return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n      this._errorStateTracker.matcher = value;\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n      return this._getChipStream(chip => chip._onBlur);\n    }\n    /** Emits when the chip grid value has been changed by the user. */\n    change = new EventEmitter();\n    /**\n     * Emits whenever the raw value of the chip-grid changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    _chips = undefined;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /** Whether the chip grid is in an error state. */\n    get errorState() {\n      return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n      this._errorStateTracker.errorState = value;\n    }\n    constructor() {\n      super();\n      const parentForm = inject(NgForm, {\n        optional: true\n      });\n      const parentFormGroup = inject(FormGroupDirective, {\n        optional: true\n      });\n      const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n      if (this.ngControl) {\n        this.ngControl.valueAccessor = this;\n      }\n      this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    }\n    ngAfterContentInit() {\n      this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._blur();\n        this.stateChanges.next();\n      });\n      merge(this.chipFocusChanges, this._chips.changes).pipe(takeUntil(this._destroyed)).subscribe(() => this.stateChanges.next());\n    }\n    ngAfterViewInit() {\n      super.ngAfterViewInit();\n      if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n      }\n    }\n    ngDoCheck() {\n      if (this.ngControl) {\n        // We need to re-evaluate this on every change detection cycle, because there are some\n        // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n        // that whatever logic is in here has to be super lean or we risk destroying the performance.\n        this.updateErrorState();\n      }\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this.stateChanges.complete();\n    }\n    /** Associates an HTML input element with this chip grid. */\n    registerInput(inputElement) {\n      this._chipInput = inputElement;\n      this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n      if (!this.disabled && !this._originatesFromChip(event)) {\n        this.focus();\n      }\n    }\n    /**\n     * Focuses the first chip in this chip grid, or the associated input when there\n     * are no eligible chips.\n     */\n    focus() {\n      if (this.disabled || this._chipInput.focused) {\n        return;\n      }\n      if (!this._chips.length || this._chips.first.disabled) {\n        // Delay until the next tick, because this can cause a \"changed after checked\"\n        // error if the input does something on focus (e.g. opens an autocomplete).\n        Promise.resolve().then(() => this._chipInput.focus());\n      } else {\n        const activeItem = this._keyManager.activeItem;\n        if (activeItem) {\n          activeItem.focus();\n        } else {\n          this._keyManager.setFirstItemActive();\n        }\n      }\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get describedByIds() {\n      return this._chipInput?.describedByIds || [];\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      // We must keep this up to date to handle the case where ids are set\n      // before the chip input is registered.\n      this._ariaDescribedbyIds = ids;\n      this._chipInput?.setDescribedByIds(ids);\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n      // The user is responsible for creating the child chips, so we just store the value.\n      this._value = value;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this.stateChanges.next();\n    }\n    /** Refreshes the error state of the chip grid. */\n    updateErrorState() {\n      this._errorStateTracker.updateErrorState();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n    _blur() {\n      if (!this.disabled) {\n        // Check whether the focus moved to chip input.\n        // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n        // to chip input, do nothing.\n        // Timeout is needed to wait for the focus() event trigger on chip input.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._propagateChanges();\n            this._markAsTouched();\n          }\n        });\n      }\n    }\n    /**\n     * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the grid from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n      if (!this._chipInput.focused) {\n        super._allowFocusEscape();\n      }\n    }\n    /** Handles custom keyboard events. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      const activeItem = this._keyManager.activeItem;\n      if (keyCode === TAB) {\n        if (this._chipInput.focused && hasModifierKey(event, 'shiftKey') && this._chips.length && !this._chips.last.disabled) {\n          event.preventDefault();\n          if (activeItem) {\n            this._keyManager.setActiveItem(activeItem);\n          } else {\n            this._focusLastChip();\n          }\n        } else {\n          // Use the super method here since it doesn't check for the input\n          // focused state. This allows focus to escape if there's only one\n          // disabled chip left in the list.\n          super._allowFocusEscape();\n        }\n      } else if (!this._chipInput.focused) {\n        // The up and down arrows are supposed to navigate between the individual rows in the grid.\n        // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n        // flag as the active action and moving focus between them ourseles instead of delegating\n        // to the key manager. For more information, see #29359 and:\n        // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n        if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n          const eligibleActions = this._chipActions.filter(action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action));\n          const currentIndex = eligibleActions.indexOf(activeItem);\n          const delta = event.keyCode === UP_ARROW ? -1 : 1;\n          event.preventDefault();\n          if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n            this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n          }\n        } else {\n          super._handleKeydown(event);\n        }\n      }\n      this.stateChanges.next();\n    }\n    _focusLastChip() {\n      if (this._chips.length) {\n        this._chips.last.focus();\n      }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n      const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n      this._value = valueToEmit;\n      this.change.emit(new MatChipGridChange(this, valueToEmit));\n      this.valueChange.emit(valueToEmit);\n      this._onChange(valueToEmit);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n    static ɵfac = function MatChipGrid_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipGrid)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatChipGrid,\n      selectors: [[\"mat-chip-grid\"]],\n      contentQueries: function MatChipGrid_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatChipRow, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-chip-set\", \"mat-mdc-chip-grid\", \"mdc-evolution-chip-set\"],\n      hostVars: 10,\n      hostBindings: function MatChipGrid_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatChipGrid_focus_HostBindingHandler() {\n            return ctx.focus();\n          })(\"blur\", function MatChipGrid_blur_HostBindingHandler() {\n            return ctx._blur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx.disabled || ctx._chips && ctx._chips.length === 0 ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState);\n          i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-invalid\", ctx.errorState)(\"mat-mdc-chip-list-required\", ctx.required);\n        }\n      },\n      inputs: {\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        placeholder: \"placeholder\",\n        required: [2, \"required\", \"required\", booleanAttribute],\n        value: \"value\",\n        errorStateMatcher: \"errorStateMatcher\"\n      },\n      outputs: {\n        change: \"change\",\n        valueChange: \"valueChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatChipGrid\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c5,\n      decls: 2,\n      vars: 0,\n      consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n      template: function MatChipGrid_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c6],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatChipGrid;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nlet MatChipInput = /*#__PURE__*/(() => {\n  class MatChipInput {\n    _elementRef = inject(ElementRef);\n    /** Whether the control is focused. */\n    focused = false;\n    /** Register input for chip list */\n    get chipGrid() {\n      return this._chipGrid;\n    }\n    set chipGrid(value) {\n      if (value) {\n        this._chipGrid = value;\n        this._chipGrid.registerInput(this);\n      }\n    }\n    _chipGrid;\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    addOnBlur = false;\n    /**\n     * The list of key codes that will trigger a chipEnd event.\n     *\n     * Defaults to `[ENTER]`.\n     */\n    separatorKeyCodes;\n    /** Emitted when a chip is to be added. */\n    chipEnd = new EventEmitter();\n    /** The input's placeholder text. */\n    placeholder = '';\n    /** Unique id for the input. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n    /** Whether the input is disabled. */\n    get disabled() {\n      return this._disabled || this._chipGrid && this._chipGrid.disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n    }\n    _disabled = false;\n    /** Whether the input is readonly. */\n    readonly = false;\n    /** Whether the input should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** Whether the input is empty. */\n    get empty() {\n      return !this.inputElement.value;\n    }\n    /** The native input element to which this directive is attached. */\n    inputElement;\n    constructor() {\n      const defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS);\n      const formField = inject(MAT_FORM_FIELD, {\n        optional: true\n      });\n      this.inputElement = this._elementRef.nativeElement;\n      this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n      this.disabledInteractive = defaultOptions.inputDisabledInteractive ?? false;\n      if (formField) {\n        this.inputElement.classList.add('mat-mdc-form-field-input-control');\n      }\n    }\n    ngOnChanges() {\n      this._chipGrid.stateChanges.next();\n    }\n    ngOnDestroy() {\n      this.chipEnd.complete();\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n      if (this.empty && event.keyCode === BACKSPACE) {\n        // Ignore events where the user is holding down backspace\n        // so that we don't accidentally remove too many chips.\n        if (!event.repeat) {\n          this._chipGrid._focusLastChip();\n        }\n        event.preventDefault();\n      } else {\n        this._emitChipEnd(event);\n      }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n      if (this.addOnBlur) {\n        this._emitChipEnd();\n      }\n      this.focused = false;\n      // Blur the chip list if it is not focused\n      if (!this._chipGrid.focused) {\n        this._chipGrid._blur();\n      }\n      this._chipGrid.stateChanges.next();\n    }\n    _focus() {\n      this.focused = true;\n      this._chipGrid.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n      if (!event || this._isSeparatorKey(event) && !event.repeat) {\n        this.chipEnd.emit({\n          input: this.inputElement,\n          value: this.inputElement.value,\n          chipInput: this\n        });\n        event?.preventDefault();\n      }\n    }\n    _onInput() {\n      // Let chip list know whenever the value changes.\n      this._chipGrid.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus() {\n      this.inputElement.focus();\n    }\n    /** Clears the input */\n    clear() {\n      this.inputElement.value = '';\n    }\n    /**\n     * Implemented as part of MatChipTextControl.\n     * @docs-private\n     */\n    get describedByIds() {\n      const element = this._elementRef.nativeElement;\n      const existingDescribedBy = element.getAttribute('aria-describedby');\n      return existingDescribedBy?.split(' ') || [];\n    }\n    setDescribedByIds(ids) {\n      const element = this._elementRef.nativeElement;\n      // Set the value directly in the DOM since this binding\n      // is prone to \"changed after checked\" errors.\n      if (ids.length) {\n        element.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        element.removeAttribute('aria-describedby');\n      }\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n      return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n    /** Gets the value to set on the `readonly` attribute. */\n    _getReadonlyAttribute() {\n      return this.readonly || this.disabled && this.disabledInteractive ? 'true' : null;\n    }\n    static ɵfac = function MatChipInput_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipInput)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatChipInput,\n      selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-chip-input\", \"mat-mdc-input-element\", \"mdc-text-field__input\", \"mat-input-element\"],\n      hostVars: 8,\n      hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n            return ctx._blur();\n          })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n            return ctx._focus();\n          })(\"input\", function MatChipInput_input_HostBindingHandler() {\n            return ctx._onInput();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"disabled\", ctx.disabled && !ctx.disabledInteractive ? \"\" : null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipGrid && ctx._chipGrid.ngControl ? ctx._chipGrid.ngControl.invalid : null)(\"aria-required\", ctx._chipGrid && ctx._chipGrid.required || null)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"readonly\", ctx._getReadonlyAttribute())(\"required\", ctx._chipGrid && ctx._chipGrid.required || null);\n        }\n      },\n      inputs: {\n        chipGrid: [0, \"matChipInputFor\", \"chipGrid\"],\n        addOnBlur: [2, \"matChipInputAddOnBlur\", \"addOnBlur\", booleanAttribute],\n        separatorKeyCodes: [0, \"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n        placeholder: \"placeholder\",\n        id: \"id\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        readonly: [2, \"readonly\", \"readonly\", booleanAttribute],\n        disabledInteractive: [2, \"matChipInputDisabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        chipEnd: \"matChipInputTokenEnd\"\n      },\n      exportAs: [\"matChipInput\", \"matChipInputFor\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatChipInput;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst CHIP_DECLARATIONS = [MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon];\nlet MatChipsModule = /*#__PURE__*/(() => {\n  class MatChipsModule {\n    static ɵfac = function MatChipsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatChipsModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatChipsModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }],\n      imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n    });\n  }\n  return MatChipsModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };", "map": {"version": 3, "names": ["FocusMonitor", "_IdGenerator", "FocusKeyManager", "ENTER", "SPACE", "BACKSPACE", "DELETE", "TAB", "hasModifierKey", "UP_ARROW", "DOWN_ARROW", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "i0", "InjectionToken", "inject", "ElementRef", "booleanAttribute", "numberAttribute", "Directive", "Input", "ChangeDetectorRef", "HOST_TAG_NAME", "NgZone", "DOCUMENT", "EventEmitter", "Injector", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "Output", "ContentChild", "ViewChild", "afterNextRender", "QueryList", "forwardRef", "NgModule", "Subject", "merge", "_", "_StructuralStylesLoader", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "_animationsDisabled", "M", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "takeUntil", "startWith", "switchMap", "Directionality", "NG_VALUE_ACCESSOR", "NgControl", "Validators", "NgForm", "FormGroupDirective", "E", "ErrorStateMatcher", "_ErrorStateTracker", "k", "MatFormFieldControl", "h", "MAT_FORM_FIELD", "MatCommonModule", "MatRippleModule", "_c0", "_c1", "MatChip_Conditional_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "MatChip_Conditional_7_Template", "MatChipOption_Conditional_3_Template", "ɵɵnamespaceSVG", "ɵɵelement", "MatChipOption_Conditional_7_Template", "_c2", "_c3", "_c4", "MatChipRow_Conditional_0_Template", "MatChipRow_Conditional_2_Template", "MatChipRow_Conditional_4_Conditional_0_Template", "MatChipRow_Conditional_4_Conditional_1_Template", "MatChipRow_Conditional_4_Template", "ɵɵconditionalCreate", "ctx_r0", "ɵɵnextContext", "ɵɵconditional", "contentEditInput", "MatChipRow_Conditional_5_Template", "MatChipRow_Conditional_7_Template", "_c5", "_c6", "MAT_CHIPS_DEFAULT_OPTIONS", "providedIn", "factory", "separatorKeyCodes", "MAT_CHIP_AVATAR", "MAT_CHIP_TRAILING_ICON", "MAT_CHIP_REMOVE", "MAT_CHIP", "MatChipAction", "_elementRef", "_parentChip", "isInteractive", "_isPrimary", "disabled", "_disabled", "value", "tabIndex", "_allowFocusWhenDisabled", "_getDisabledAttribute", "_getTabindex", "toString", "constructor", "load", "nativeElement", "nodeName", "setAttribute", "focus", "_handleClick", "event", "preventDefault", "_handlePrimaryActionInteraction", "_handleKeydown", "keyCode", "_isEditing", "ɵfac", "MatChipAction_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatChipAction_HostBindings", "ɵɵlistener", "MatChipAction_click_HostBindingHandler", "$event", "MatChipAction_keydown_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "inputs", "ngDevMode", "MatChipAvatar", "MatChipAvatar_Factory", "features", "ɵɵProvidersFeature", "provide", "useExisting", "MatChipTrailingIcon", "ɵMatChipTrailingIcon_BaseFactory", "MatChipTrailingIcon_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature", "MatChipRemove", "stopPropagation", "remove", "ɵMatChipRemove_BaseFactory", "MatChipRemove_Factory", "MatChipRemove_HostBindings", "MatChip", "_changeDetectorRef", "_tagName", "_ngZone", "_focusMonitor", "_globalRippleOptions", "optional", "_document", "_onFocus", "_onBlur", "_isBasicChip", "role", "_hasFocusInternal", "_pendingFocus", "_actionChanges", "_allLeadingIcons", "_allTrailingIcons", "_allRemoveIcons", "_hasFocus", "id", "getId", "aria<PERSON><PERSON><PERSON>", "ariaDescription", "_ariaDescriptionId", "_chipListDisabled", "_textElement", "_value", "undefined", "textContent", "trim", "color", "removable", "highlighted", "disable<PERSON><PERSON><PERSON>", "removed", "destroyed", "basicChipAttrName", "leadingIcon", "trailingIcon", "removeIcon", "primaryAction", "_ripple<PERSON><PERSON>der", "_injector", "<PERSON><PERSON><PERSON><PERSON>", "_monitorFocus", "configureRipple", "className", "_isRippleDisabled", "ngOnInit", "hasAttribute", "toLowerCase", "ngAfterViewInit", "querySelector", "ngAfterContentInit", "changes", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngDoCheck", "setDisabled", "ngOnDestroy", "stopMonitoring", "destroyRipple", "unsubscribe", "emit", "chip", "complete", "_hasTrailingIcon", "repeat", "_getSourceAction", "target", "_getActions", "find", "action", "element", "contains", "result", "push", "monitor", "origin", "hasFocus", "next", "setTimeout", "run", "MatChip_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatChip_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatChip_Query", "ɵɵviewQuery", "MatChip_HostBindings", "MatChip_keydown_HostBindingHandler", "ɵɵdomProperty", "ɵɵclassMap", "outputs", "exportAs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatChip_Template", "ɵɵprojectionDef", "ɵɵadvance", "ɵɵproperty", "dependencies", "styles", "encapsulation", "changeDetection", "MatChipSelectionChange", "source", "selected", "isUserInput", "MatChipOption", "_defaultOptions", "chipListSelectable", "_chipListMultiple", "_chipListHideSingleSelectionIndicator", "hideSingleSelectionIndicator", "selectable", "_selectable", "_selected", "_setSelectedState", "ariaSelected", "selectionChange", "select", "deselect", "selectViaInteraction", "toggleSelected", "_hasLeadingGraphic", "isSelected", "emitEvent", "ɵMatChipOption_BaseFactory", "MatChipOption_Factory", "MatChipOption_HostBindings", "MatChipOption_Template", "ɵɵtext", "ɵɵtextInterpolate", "MatChipEditInput", "initialize", "initialValue", "getNativeElement", "setValue", "_moveCursorToEndOfInput", "getValue", "range", "createRange", "selectNodeContents", "collapse", "sel", "window", "getSelection", "removeAllRanges", "addRange", "MatChipEditInput_Factory", "MatChipRow", "_editStartPending", "editable", "edited", "defaultEditInput", "pipe", "_onEditFinish", "_handleFocus", "_startEditing", "_handleDoubleclick", "_getEditInput", "injector", "activeElement", "body", "MatChipRow_Factory", "MatChipRow_ContentQueries", "MatChipRow_Query", "MatChipRow_HostBindings", "MatChipRow_focus_HostBindingHandler", "MatChipRow_dblclick_HostBindingHandler", "MatChipRow_Template", "MatChipSet", "_dir", "_lastDestroyedFocusedChipIndex", "_keyManager", "_destroyed", "_defaultRole", "chipFocusChanges", "_getChipStream", "chipDestroyedChanges", "chipRemovedChanges", "_syncChipsState", "empty", "_chips", "length", "_explicitRole", "focused", "_hasFocusedChip", "_chipActions", "_setUpFocusManagement", "_trackChipSetChanges", "_trackDestroyedFocusedChip", "destroy", "some", "for<PERSON>ach", "_originatesFromChip", "onKeydown", "_isValidIndex", "index", "_allowFocusEscape", "previous", "mappingFunction", "map", "currentElement", "classList", "parentElement", "chips", "actions", "reset", "notifyOn<PERSON><PERSON>es", "withVerticalOrientation", "withHorizontalOrientation", "withHomeAndEnd", "skipPredicate", "_skipPredicate", "document", "updateActiveItem", "change", "direction", "Promise", "resolve", "then", "_redirectDestroyedChipFocus", "chipArray", "toArray", "chipIndex", "indexOf", "newIndex", "Math", "min", "chipToFocus", "setPreviousItemActive", "MatChipSet_Factory", "MatChipSet_ContentQueries", "MatChipSet_HostBindings", "MatChipSet_keydown_HostBindingHandler", "MatChipSet_Template", "MatChipListboxChange", "MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR", "MatChipListbox", "multi", "_onTouched", "_onChange", "multiple", "_multiple", "_syncListboxProperties", "selectedChips", "filter", "ariaOrientation", "compareWith", "o1", "o2", "required", "_hideSingleSelectionIndicator", "chipSelectionChanges", "chipBlurChanges", "_setSelectionByValue", "_blur", "_propagateChanges", "firstSelectedChip", "_getFirstSelectedChip", "setFirstItemActive", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "_clearSelection", "Array", "isArray", "currentValue", "_selectValue", "_markAsTouched", "_keydown", "valueToEmit", "skip", "correspondingChip", "ɵMatChipListbox_BaseFactory", "MatChipListbox_Factory", "MatChipListbox_ContentQueries", "MatChipListbox_HostBindings", "MatChipListbox_focus_HostBindingHandler", "MatChipListbox_blur_HostBindingHandler", "MatChipListbox_keydown_HostBindingHandler", "MatChipListbox_Template", "MatChipGridChange", "MatChipGrid", "ngControl", "self", "controlType", "_chipInput", "_errorStateTracker", "_ariaDescribedbyIds", "stateChanges", "placeholder", "_placeholder", "_required", "control", "hasValidator", "shouldLabelFloat", "errorStateMatcher", "matcher", "valueChange", "errorState", "parentForm", "parentFormGroup", "defaultErrorStateMatcher", "valueAccessor", "Error", "updateErrorState", "registerInput", "inputElement", "setDescribedByIds", "onContainerClick", "activeItem", "describedByIds", "ids", "last", "setActiveItem", "_focusLastChip", "eligibleActions", "currentIndex", "delta", "MatChipGrid_Factory", "MatChipGrid_ContentQueries", "MatChipGrid_HostBindings", "MatChipGrid_focus_HostBindingHandler", "MatChipGrid_blur_HostBindingHandler", "MatChipGrid_Template", "MatChipInput", "chipGrid", "_chipGrid", "addOnBlur", "chipEnd", "readonly", "disabledInteractive", "defaultOptions", "formField", "inputDisabledInteractive", "add", "ngOnChanges", "_emitChipEnd", "_focus", "_isSeparator<PERSON>ey", "input", "chipInput", "_onInput", "clear", "existingDescribedBy", "getAttribute", "split", "join", "removeAttribute", "Set", "has", "_getReadonlyAttribute", "MatChipInput_Factory", "MatChipInput_HostBindings", "MatChipInput_keydown_HostBindingHandler", "MatChipInput_blur_HostBindingHandler", "MatChipInput_focus_HostBindingHandler", "MatChipInput_input_HostBindingHandler", "invalid", "ɵɵNgOnChangesFeature", "CHIP_DECLARATIONS", "MatChipsModule", "MatChipsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "useValue", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/chips.mjs"], "sourcesContent": ["import { FocusMonitor, _Id<PERSON>enerator, FocusKeyManager } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, booleanAttribute, numberAttribute, Directive, Input, ChangeDetectorRef, HOST_TAG_NAME, NgZone, DOCUMENT, EventEmitter, Injector, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, Output, ContentChild, ViewChild, afterNextRender, QueryList, forwardRef, NgModule } from '@angular/core';\nimport { Subject, merge } from 'rxjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BYgV4oZC.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nimport { takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR, NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { k as MatFormFieldControl, h as MAT_FORM_FIELD } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\n\n/** Injection token to be used to override the default options for the chips module. */\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options', {\n    providedIn: 'root',\n    factory: () => ({\n        separatorKeyCodes: [ENTER],\n    }),\n});\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = new InjectionToken('MatChip');\n\n/**\n * Section within a chip.\n * @docs-private\n */\nclass MatChipAction {\n    _elementRef = inject(ElementRef);\n    _parentChip = inject(MAT_CHIP);\n    /** Whether the action is interactive. */\n    isInteractive = true;\n    /** Whether this is the primary action in the chip. */\n    _isPrimary = true;\n    /** Whether the action is disabled. */\n    get disabled() {\n        return this._disabled || this._parentChip?.disabled || false;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Tab index of the action. */\n    tabIndex = -1;\n    /**\n     * Private API to allow focusing this chip when it is disabled.\n     */\n    _allowFocusWhenDisabled = false;\n    /**\n     * Determine the value of the disabled attribute for this chip action.\n     */\n    _getDisabledAttribute() {\n        // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n        // string to indicate that disabled attribute should be included.\n        return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n    }\n    /**\n     * Determine the value of the tabindex attribute for this chip action.\n     */\n    _getTabindex() {\n        return (this.disabled && !this._allowFocusWhenDisabled) || !this.isInteractive\n            ? null\n            : this.tabIndex.toString();\n    }\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n            this._elementRef.nativeElement.setAttribute('type', 'button');\n        }\n    }\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    _handleClick(event) {\n        if (!this.disabled && this.isInteractive && this._isPrimary) {\n            event.preventDefault();\n            this._parentChip._handlePrimaryActionInteraction();\n        }\n    }\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) &&\n            !this.disabled &&\n            this.isInteractive &&\n            this._isPrimary &&\n            !this._parentChip._isEditing) {\n            event.preventDefault();\n            this._parentChip._handlePrimaryActionInteraction();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatChipAction, isStandalone: true, selector: \"[matChipAction]\", inputs: { isInteractive: \"isInteractive\", disabled: [\"disabled\", \"disabled\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? -1 : numberAttribute(value))], _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-evolution-chip__action--primary\": \"_isPrimary\", \"class.mdc-evolution-chip__action--presentational\": \"!isInteractive\", \"class.mdc-evolution-chip__action--trailing\": \"!_isPrimary\", \"attr.tabindex\": \"_getTabindex()\", \"attr.disabled\": \"_getDisabledAttribute()\", \"attr.aria-disabled\": \"disabled\" }, classAttribute: \"mdc-evolution-chip__action mat-mdc-chip-action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipAction]',\n                    host: {\n                        'class': 'mdc-evolution-chip__action mat-mdc-chip-action',\n                        '[class.mdc-evolution-chip__action--primary]': '_isPrimary',\n                        '[class.mdc-evolution-chip__action--presentational]': '!isInteractive',\n                        '[class.mdc-evolution-chip__action--trailing]': '!_isPrimary',\n                        '[attr.tabindex]': '_getTabindex()',\n                        '[attr.disabled]': '_getDisabledAttribute()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '(click)': '_handleClick($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { isInteractive: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? -1 : numberAttribute(value)),\n                    }]\n            }], _allowFocusWhenDisabled: [{\n                type: Input\n            }] } });\n\n/** Avatar image within a chip. */\nclass MatChipAvatar {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatChipAvatar, isStandalone: true, selector: \"mat-chip-avatar, [matChipAvatar]\", host: { attributes: { \"role\": \"img\" }, classAttribute: \"mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary\" }, providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-avatar, [matChipAvatar]',\n                    host: {\n                        'class': 'mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary',\n                        'role': 'img',\n                    },\n                    providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }],\n                }]\n        }] });\n/** Non-interactive trailing icon in a chip. */\nclass MatChipTrailingIcon extends MatChipAction {\n    /**\n     * MDC considers all trailing actions as a remove icon,\n     * but we support non-interactive trailing icons.\n     */\n    isInteractive = false;\n    _isPrimary = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipTrailingIcon, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatChipTrailingIcon, isStandalone: true, selector: \"mat-chip-trailing-icon, [matChipTrailingIcon]\", host: { attributes: { \"aria-hidden\": \"true\" }, classAttribute: \"mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing\" }, providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipTrailingIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n                    host: {\n                        'class': 'mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n                        'aria-hidden': 'true',\n                    },\n                    providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }],\n                }]\n        }] });\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nclass MatChipRemove extends MatChipAction {\n    _isPrimary = false;\n    _handleClick(event) {\n        if (!this.disabled) {\n            event.stopPropagation();\n            event.preventDefault();\n            this._parentChip.remove();\n        }\n    }\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n            event.stopPropagation();\n            event.preventDefault();\n            this._parentChip.remove();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipRemove, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatChipRemove, isStandalone: true, selector: \"[matChipRemove]\", host: { attributes: { \"role\": \"button\" }, properties: { \"attr.aria-hidden\": \"null\" }, classAttribute: \"mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing\" }, providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipRemove, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipRemove]',\n                    host: {\n                        'class': 'mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator ' +\n                            'mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n                        'role': 'button',\n                        '[attr.aria-hidden]': 'null',\n                    },\n                    providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }],\n                }]\n        }] });\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nclass MatChip {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _tagName = inject(HOST_TAG_NAME);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n        optional: true,\n    });\n    _document = inject(DOCUMENT);\n    /** Emits when the chip is focused. */\n    _onFocus = new Subject();\n    /** Emits when the chip is blurred. */\n    _onBlur = new Subject();\n    /** Whether this chip is a basic (unstyled) chip. */\n    _isBasicChip;\n    /** Role for the root of the chip. */\n    role = null;\n    /** Whether the chip has focus. */\n    _hasFocusInternal = false;\n    /** Whether moving focus into the chip is pending. */\n    _pendingFocus;\n    /** Subscription to changes in the chip's actions. */\n    _actionChanges;\n    /** Whether animations for the chip are enabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All avatars present in the chip. */\n    _allLeadingIcons;\n    /** All trailing icons present in the chip. */\n    _allTrailingIcons;\n    /** All remove icons present in the chip. */\n    _allRemoveIcons;\n    _hasFocus() {\n        return this._hasFocusInternal;\n    }\n    /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-');\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA label for the content of the chip. */\n    ariaLabel = null;\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA description for the content of the chip. */\n    ariaDescription = null;\n    /** Id of a span that contains this chip's aria description. */\n    _ariaDescriptionId = `${this.id}-aria-description`;\n    /** Whether the chip list is disabled. */\n    _chipListDisabled = false;\n    _textElement;\n    /**\n     * The value of the chip. Defaults to the content inside\n     * the `mat-mdc-chip-action-label` element.\n     */\n    get value() {\n        return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n    }\n    set value(value) {\n        this._value = value;\n    }\n    _value;\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the chip. This API is supported in M2 themes only, it has no\n     * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    removable = true;\n    /**\n     * Colors the chip for emphasis as if it were selected.\n     */\n    highlighted = false;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    /** Whether the chip is disabled. */\n    get disabled() {\n        return this._disabled || this._chipListDisabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Emitted when a chip is to be removed. */\n    removed = new EventEmitter();\n    /** Emitted when the chip is destroyed. */\n    destroyed = new EventEmitter();\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip';\n    /** The chip's leading icon. */\n    leadingIcon;\n    /** The chip's trailing icon. */\n    trailingIcon;\n    /** The chip's trailing remove icon. */\n    removeIcon;\n    /** Action receiving the primary set of user interactions. */\n    primaryAction;\n    /**\n     * Handles the lazy creation of the MatChip ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    _injector = inject(Injector);\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_StructuralStylesLoader);\n        styleLoader.load(_VisuallyHiddenLoader);\n        this._monitorFocus();\n        this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n            className: 'mat-mdc-chip-ripple',\n            disabled: this._isRippleDisabled(),\n        });\n    }\n    ngOnInit() {\n        // This check needs to happen in `ngOnInit` so the overridden value of\n        // `basicChipAttrName` coming from base classes can be picked up.\n        this._isBasicChip =\n            this._elementRef.nativeElement.hasAttribute(this.basicChipAttrName) ||\n                this._tagName.toLowerCase() === this.basicChipAttrName;\n    }\n    ngAfterViewInit() {\n        this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n        if (this._pendingFocus) {\n            this._pendingFocus = false;\n            this.focus();\n        }\n    }\n    ngAfterContentInit() {\n        // Since the styling depends on the presence of some\n        // actions, we have to mark for check on changes.\n        this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    ngDoCheck() {\n        this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n        this._actionChanges?.unsubscribe();\n        this.destroyed.emit({ chip: this });\n        this.destroyed.complete();\n    }\n    /**\n     * Allows for programmatic removal of the chip.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n        if (this.removable) {\n            this.removed.emit({ chip: this });\n        }\n    }\n    /** Whether or not the ripple should be disabled. */\n    _isRippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._animationsDisabled ||\n            this._isBasicChip ||\n            !!this._globalRippleOptions?.disabled);\n    }\n    /** Returns whether the chip has a trailing icon. */\n    _hasTrailingIcon() {\n        return !!(this.trailingIcon || this.removeIcon);\n    }\n    /** Handles keyboard events on the chip. */\n    _handleKeydown(event) {\n        // Ignore backspace events where the user is holding down the key\n        // so that we don't accidentally remove too many chips.\n        if ((event.keyCode === BACKSPACE && !event.repeat) || event.keyCode === DELETE) {\n            event.preventDefault();\n            this.remove();\n        }\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n        if (!this.disabled) {\n            // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n            // This can happen if the consumer tries to focus a chip immediately after it is added.\n            // Queue the method to be called again on init.\n            if (this.primaryAction) {\n                this.primaryAction.focus();\n            }\n            else {\n                this._pendingFocus = true;\n            }\n        }\n    }\n    /** Gets the action that contains a specific target node. */\n    _getSourceAction(target) {\n        return this._getActions().find(action => {\n            const element = action._elementRef.nativeElement;\n            return element === target || element.contains(target);\n        });\n    }\n    /** Gets all of the actions within the chip. */\n    _getActions() {\n        const result = [];\n        if (this.primaryAction) {\n            result.push(this.primaryAction);\n        }\n        if (this.removeIcon) {\n            result.push(this.removeIcon);\n        }\n        if (this.trailingIcon) {\n            result.push(this.trailingIcon);\n        }\n        return result;\n    }\n    /** Handles interactions with the primary action of the chip. */\n    _handlePrimaryActionInteraction() {\n        // Empty here, but is overwritten in child classes.\n    }\n    /** Starts the focus monitoring process on the chip. */\n    _monitorFocus() {\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n            const hasFocus = origin !== null;\n            if (hasFocus !== this._hasFocusInternal) {\n                this._hasFocusInternal = hasFocus;\n                if (hasFocus) {\n                    this._onFocus.next({ chip: this });\n                }\n                else {\n                    // When animations are enabled, Angular may end up removing the chip from the DOM a little\n                    // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n                    // that moves focus to the next item. To work around the issue, we defer marking the chip\n                    // as not focused until after the next render.\n                    this._changeDetectorRef.markForCheck();\n                    setTimeout(() => this._ngZone.run(() => this._onBlur.next({ chip: this })));\n                }\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChip, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatChip, isStandalone: true, selector: \"mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]\", inputs: { role: \"role\", id: \"id\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaDescription: [\"aria-description\", \"ariaDescription\"], value: \"value\", color: \"color\", removable: [\"removable\", \"removable\", booleanAttribute], highlighted: [\"highlighted\", \"highlighted\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { removed: \"removed\", destroyed: \"destroyed\" }, host: { listeners: { \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mdc-evolution-chip\": \"!_isBasicChip\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-graphic\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-basic-chip\": \"_isBasicChip\", \"class.mat-mdc-standard-chip\": \"!_isBasicChip\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"id\": \"id\", \"attr.role\": \"role\", \"attr.aria-label\": \"ariaLabel\" }, classAttribute: \"mat-mdc-chip\" }, providers: [{ provide: MAT_CHIP, useExisting: MatChip }], queries: [{ propertyName: \"leadingIcon\", first: true, predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"trailingIcon\", first: true, predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"removeIcon\", first: true, predicate: MAT_CHIP_REMOVE, descendants: true }, { propertyName: \"_allLeadingIcons\", predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"_allTrailingIcons\", predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"_allRemoveIcons\", predicate: MAT_CHIP_REMOVE, descendants: true }], viewQueries: [{ propertyName: \"primaryAction\", first: true, predicate: MatChipAction, descendants: true }], exportAs: [\"matChip\"], ngImport: i0, template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChip, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]', exportAs: 'matChip', host: {\n                        'class': 'mat-mdc-chip',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mdc-evolution-chip]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-basic-chip]': '_isBasicChip',\n                        '[class.mat-mdc-standard-chip]': '!_isBasicChip',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[id]': 'id',\n                        '[attr.role]': 'role',\n                        '[attr.aria-label]': 'ariaLabel',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MAT_CHIP, useExisting: MatChip }], imports: [MatChipAction], template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], _allLeadingIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_AVATAR, { descendants: true }]\n            }], _allTrailingIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_TRAILING_ICON, { descendants: true }]\n            }], _allRemoveIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_REMOVE, { descendants: true }]\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaDescription: [{\n                type: Input,\n                args: ['aria-description']\n            }], value: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], removable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], highlighted: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], removed: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], leadingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_AVATAR]\n            }], trailingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_TRAILING_ICON]\n            }], removeIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_REMOVE]\n            }], primaryAction: [{\n                type: ViewChild,\n                args: [MatChipAction]\n            }] } });\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n    source;\n    selected;\n    isUserInput;\n    constructor(\n    /** Reference to the chip that emitted the event. */\n    source, \n    /** Whether the chip that emitted the event is selected. */\n    selected, \n    /** Whether the selection change was a result of a user interaction. */\n    isUserInput = false) {\n        this.source = source;\n        this.selected = selected;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nclass MatChipOption extends MatChip {\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, { optional: true });\n    /** Whether the chip list is selectable. */\n    chipListSelectable = true;\n    /** Whether the chip list is in multi-selection mode. */\n    _chipListMultiple = false;\n    /** Whether the chip list hides single-selection indicator. */\n    _chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /**\n     * Whether or not the chip is selectable.\n     *\n     * When a chip is not selectable, changes to its selected state are always\n     * ignored. By default an option chip is selectable, and it becomes\n     * non-selectable if its parent chip list is not selectable.\n     */\n    get selectable() {\n        return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n        this._selectable = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    _selectable = true;\n    /** Whether the chip is selected. */\n    get selected() {\n        return this._selected;\n    }\n    set selected(value) {\n        this._setSelectedState(value, false, true);\n    }\n    _selected = false;\n    /**\n     * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n     * interaction patterns.\n     *\n     * From [WAI ARIA Listbox authoring practices guide](\n     * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n     *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n     *  set to true. All options that are selectable but not selected have either aria-selected or\n     *  aria-checked set to false.\"\n     *\n     * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n     * VoiceOver reading every option as \"selected\" (#25736).\n     */\n    get ariaSelected() {\n        return this.selectable ? this.selected.toString() : null;\n    }\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip-option';\n    /** Emitted when the chip is selected or deselected. */\n    selectionChange = new EventEmitter();\n    ngOnInit() {\n        super.ngOnInit();\n        this.role = 'presentation';\n    }\n    /** Selects the chip. */\n    select() {\n        this._setSelectedState(true, false, true);\n    }\n    /** Deselects the chip. */\n    deselect() {\n        this._setSelectedState(false, false, true);\n    }\n    /** Selects this chip and emits userInputSelection event */\n    selectViaInteraction() {\n        this._setSelectedState(true, true, true);\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n        this._setSelectedState(!this.selected, isUserInput, true);\n        return this.selected;\n    }\n    _handlePrimaryActionInteraction() {\n        if (!this.disabled) {\n            // Interacting with the primary action implies that the chip already has focus, however\n            // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n            // We work around it by explicitly focusing the primary action of the current chip.\n            this.focus();\n            if (this.selectable) {\n                this.toggleSelected(true);\n            }\n        }\n    }\n    _hasLeadingGraphic() {\n        if (this.leadingIcon) {\n            return true;\n        }\n        // The checkmark graphic communicates selected state for both single-select and multi-select.\n        // Include checkmark in single-select to fix a11y issue where selected state is communicated\n        // visually only using color (#25886).\n        return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n    }\n    _setSelectedState(isSelected, isUserInput, emitEvent) {\n        if (isSelected !== this.selected) {\n            this._selected = isSelected;\n            if (emitEvent) {\n                this.selectionChange.emit({\n                    source: this,\n                    isUserInput,\n                    selected: this.selected,\n                });\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipOption, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatChipOption, isStandalone: true, selector: \"mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]\", inputs: { selectable: [\"selectable\", \"selectable\", booleanAttribute], selected: [\"selected\", \"selected\", booleanAttribute] }, outputs: { selectionChange: \"selectionChange\" }, host: { properties: { \"class.mdc-evolution-chip\": \"!_isBasicChip\", \"class.mdc-evolution-chip--filter\": \"!_isBasicChip\", \"class.mdc-evolution-chip--selectable\": \"!_isBasicChip\", \"class.mat-mdc-chip-selected\": \"selected\", \"class.mat-mdc-chip-multiple\": \"_chipListMultiple\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--selected\": \"selected\", \"class.mdc-evolution-chip--selecting\": \"!_animationsDisabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-graphic\": \"_hasLeadingGraphic()\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-description\": \"null\", \"attr.role\": \"role\", \"id\": \"id\" }, classAttribute: \"mat-mdc-chip mat-mdc-chip-option\" }, providers: [\n            { provide: MatChip, useExisting: MatChipOption },\n            { provide: MAT_CHIP, useExisting: MatChipOption },\n        ], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]', host: {\n                        'class': 'mat-mdc-chip mat-mdc-chip-option',\n                        '[class.mdc-evolution-chip]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--filter]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--selectable]': '!_isBasicChip',\n                        '[class.mat-mdc-chip-selected]': 'selected',\n                        '[class.mat-mdc-chip-multiple]': '_chipListMultiple',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--selected]': 'selected',\n                        // This class enables the transition on the checkmark. Usually MDC adds it when selection\n                        // starts and removes it once the animation is finished. We don't need to go through all\n                        // the trouble, because we only care about the selection animation. MDC needs to do it,\n                        // because they also have an exit animation that we don't care about.\n                        '[class.mdc-evolution-chip--selecting]': '!_animationsDisabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': '_hasLeadingGraphic()',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-description]': 'null',\n                        '[attr.role]': 'role',\n                        '[id]': 'id',\n                    }, providers: [\n                        { provide: MatChip, useExisting: MatChipOption },\n                        { provide: MAT_CHIP, useExisting: MatChipOption },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatChipAction], template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], propDecorators: { selectable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selected: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectionChange: [{\n                type: Output\n            }] } });\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nclass MatChipEditInput {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    constructor() { }\n    initialize(initialValue) {\n        this.getNativeElement().focus();\n        this.setValue(initialValue);\n    }\n    getNativeElement() {\n        return this._elementRef.nativeElement;\n    }\n    setValue(value) {\n        this.getNativeElement().textContent = value;\n        this._moveCursorToEndOfInput();\n    }\n    getValue() {\n        return this.getNativeElement().textContent || '';\n    }\n    _moveCursorToEndOfInput() {\n        const range = this._document.createRange();\n        range.selectNodeContents(this.getNativeElement());\n        range.collapse(false);\n        const sel = window.getSelection();\n        sel.removeAllRanges();\n        sel.addRange(range);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipEditInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatChipEditInput, isStandalone: true, selector: \"span[matChipEditInput]\", host: { attributes: { \"role\": \"textbox\", \"tabindex\": \"-1\", \"contenteditable\": \"true\" }, classAttribute: \"mat-chip-edit-input\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipEditInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'span[matChipEditInput]',\n                    host: {\n                        'class': 'mat-chip-edit-input',\n                        'role': 'textbox',\n                        'tabindex': '-1',\n                        'contenteditable': 'true',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nclass MatChipRow extends MatChip {\n    basicChipAttrName = 'mat-basic-chip-row';\n    /**\n     * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n     * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n     * while the editing action is being initialized.\n     */\n    _editStartPending = false;\n    editable = false;\n    /** Emitted when the chip is edited. */\n    edited = new EventEmitter();\n    /** The default chip edit input that is used if none is projected into this chip row. */\n    defaultEditInput;\n    /** The projected chip edit input. */\n    contentEditInput;\n    _isEditing = false;\n    constructor() {\n        super();\n        this.role = 'row';\n        this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n            if (this._isEditing && !this._editStartPending) {\n                this._onEditFinish();\n            }\n        });\n    }\n    _hasTrailingIcon() {\n        // The trailing icon is hidden while editing.\n        return !this._isEditing && super._hasTrailingIcon();\n    }\n    /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n    _handleFocus() {\n        if (!this._isEditing && !this.disabled) {\n            this.focus();\n        }\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === ENTER && !this.disabled) {\n            if (this._isEditing) {\n                event.preventDefault();\n                this._onEditFinish();\n            }\n            else if (this.editable) {\n                this._startEditing(event);\n            }\n        }\n        else if (this._isEditing) {\n            // Stop the event from reaching the chip set in order to avoid navigating.\n            event.stopPropagation();\n        }\n        else {\n            super._handleKeydown(event);\n        }\n    }\n    _handleDoubleclick(event) {\n        if (!this.disabled && this.editable) {\n            this._startEditing(event);\n        }\n    }\n    _startEditing(event) {\n        if (!this.primaryAction ||\n            (this.removeIcon && this._getSourceAction(event.target) === this.removeIcon)) {\n            return;\n        }\n        // The value depends on the DOM so we need to extract it before we flip the flag.\n        const value = this.value;\n        this._isEditing = this._editStartPending = true;\n        // Defer initializing the input until after it has been added to the DOM.\n        afterNextRender(() => {\n            this._getEditInput().initialize(value);\n            this._editStartPending = false;\n        }, { injector: this._injector });\n    }\n    _onEditFinish() {\n        this._isEditing = this._editStartPending = false;\n        this.edited.emit({ chip: this, value: this._getEditInput().getValue() });\n        // If the edit input is still focused or focus was returned to the body after it was destroyed,\n        // return focus to the chip contents.\n        if (this._document.activeElement === this._getEditInput().getNativeElement() ||\n            this._document.activeElement === this._document.body) {\n            this.primaryAction.focus();\n        }\n    }\n    _isRippleDisabled() {\n        return super._isRippleDisabled() || this._isEditing;\n    }\n    /**\n     * Gets the projected chip edit input, or the default input if none is projected in. One of these\n     * two values is guaranteed to be defined.\n     */\n    _getEditInput() {\n        return this.contentEditInput || this.defaultEditInput;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatChipRow, isStandalone: true, selector: \"mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]\", inputs: { editable: \"editable\" }, outputs: { edited: \"edited\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"dblclick\": \"_handleDoubleclick($event)\" }, properties: { \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-chip-editing\": \"_isEditing\", \"class.mat-mdc-chip-editable\": \"editable\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-graphic\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"id\": \"id\", \"attr.tabindex\": \"disabled ? null : -1\", \"attr.aria-label\": \"null\", \"attr.aria-description\": \"null\", \"attr.role\": \"role\" }, classAttribute: \"mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip\" }, providers: [\n            { provide: MatChip, useExisting: MatChipRow },\n            { provide: MAT_CHIP, useExisting: MatChipRow },\n        ], queries: [{ propertyName: \"contentEditInput\", first: true, predicate: MatChipEditInput, descendants: true }], viewQueries: [{ propertyName: \"defaultEditInput\", first: true, predicate: MatChipEditInput, descendants: true }], usesInheritance: true, ngImport: i0, template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }, { kind: \"directive\", type: MatChipEditInput, selector: \"span[matChipEditInput]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipRow, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]', host: {\n                        'class': 'mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-editing]': '_isEditing',\n                        '[class.mat-mdc-chip-editable]': 'editable',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[id]': 'id',\n                        // Has to have a negative tabindex in order to capture\n                        // focus and redirect it to the primary action.\n                        '[attr.tabindex]': 'disabled ? null : -1',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-description]': 'null',\n                        '[attr.role]': 'role',\n                        '(focus)': '_handleFocus()',\n                        '(dblclick)': '_handleDoubleclick($event)',\n                    }, providers: [\n                        { provide: MatChip, useExisting: MatChipRow },\n                        { provide: MAT_CHIP, useExisting: MatChipRow },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatChipAction, MatChipEditInput], template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-outline-width, 1px);border-radius:var(--mat-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mat-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mat-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mat-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mat-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mat-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mat-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mat-chip-with-avatar-avatar-size, 24px);height:var(--mat-chip-with-avatar-avatar-size, 24px);font-size:var(--mat-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mat-chip-container-shape-radius, 8px);height:var(--mat-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mat-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mat-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mat-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mat-chip-with-icon-icon-size, 18px);height:var(--mat-chip-with-icon-icon-size, 18px);font-size:var(--mat-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mat-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mat-chip-with-icon-icon-color: var(--mat-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mat-chip-elevated-container-color: var(--mat-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mat-chip-label-text-color: var(--mat-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mat-chip-outline-width: var(--mat-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mat-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mat-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mat-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mat-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mat-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mat-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mat-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { editable: [{\n                type: Input\n            }], edited: [{\n                type: Output\n            }], defaultEditInput: [{\n                type: ViewChild,\n                args: [MatChipEditInput]\n            }], contentEditInput: [{\n                type: ContentChild,\n                args: [MatChipEditInput]\n            }] } });\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nclass MatChipSet {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, { optional: true });\n    /** Index of the last destroyed chip that had focus. */\n    _lastDestroyedFocusedChipIndex = null;\n    /** Used to manage focus within the chip list. */\n    _keyManager;\n    /** Subject that emits when the component has been destroyed. */\n    _destroyed = new Subject();\n    /** Role to use if it hasn't been overwritten by the user. */\n    _defaultRole = 'presentation';\n    /** Combined stream of all of the child chips' focus events. */\n    get chipFocusChanges() {\n        return this._getChipStream(chip => chip._onFocus);\n    }\n    /** Combined stream of all of the child chips' destroy events. */\n    get chipDestroyedChanges() {\n        return this._getChipStream(chip => chip.destroyed);\n    }\n    /** Combined stream of all of the child chips' remove events. */\n    get chipRemovedChanges() {\n        return this._getChipStream(chip => chip.removed);\n    }\n    /** Whether the chip set is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._syncChipsState();\n    }\n    _disabled = false;\n    /** Whether the chip list contains chips or not. */\n    get empty() {\n        return !this._chips || this._chips.length === 0;\n    }\n    /** The ARIA role applied to the chip set. */\n    get role() {\n        if (this._explicitRole) {\n            return this._explicitRole;\n        }\n        return this.empty ? null : this._defaultRole;\n    }\n    /** Tabindex of the chip set. */\n    tabIndex = 0;\n    set role(value) {\n        this._explicitRole = value;\n    }\n    _explicitRole = null;\n    /** Whether any of the chips inside of this chip-set has focus. */\n    get focused() {\n        return this._hasFocusedChip();\n    }\n    /** The chips that are part of this chip set. */\n    _chips;\n    /** Flat list of all the actions contained within the chips. */\n    _chipActions = new QueryList();\n    constructor() { }\n    ngAfterViewInit() {\n        this._setUpFocusManagement();\n        this._trackChipSetChanges();\n        this._trackDestroyedFocusedChip();\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._chipActions.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n        return this._chips && this._chips.some(chip => chip._hasFocus());\n    }\n    /** Syncs the chip-set's state with the individual chips. */\n    _syncChipsState() {\n        this._chips?.forEach(chip => {\n            chip._chipListDisabled = this._disabled;\n            chip._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n    focus() { }\n    /** Handles keyboard events on the chip set. */\n    _handleKeydown(event) {\n        if (this._originatesFromChip(event)) {\n            this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n        return index >= 0 && index < this._chips.length;\n    }\n    /**\n     * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the set from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        const previous = this._elementRef.nativeElement.tabIndex;\n        if (previous !== -1) {\n            // Set the tabindex directly on the element, instead of going through\n            // the data binding, because we aren't guaranteed that change detection\n            // will run quickly enough to allow focus to escape.\n            this._elementRef.nativeElement.tabIndex = -1;\n            // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n            // doesn't allow enough time for the focus to escape.\n            setTimeout(() => (this._elementRef.nativeElement.tabIndex = previous));\n        }\n    }\n    /**\n     * Gets a stream of events from all the chips within the set.\n     * The stream will automatically incorporate any newly-added chips.\n     */\n    _getChipStream(mappingFunction) {\n        return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n        let currentElement = event.target;\n        while (currentElement && currentElement !== this._elementRef.nativeElement) {\n            if (currentElement.classList.contains('mat-mdc-chip')) {\n                return true;\n            }\n            currentElement = currentElement.parentElement;\n        }\n        return false;\n    }\n    /** Sets up the chip set's focus management logic. */\n    _setUpFocusManagement() {\n        // Create a flat `QueryList` containing the actions of all of the chips.\n        // This allows us to navigate both within the chip and move to the next/previous\n        // one using the existing `ListKeyManager`.\n        this._chips.changes.pipe(startWith(this._chips)).subscribe((chips) => {\n            const actions = [];\n            chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n            this._chipActions.reset(actions);\n            this._chipActions.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._chipActions)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr')\n            .withHomeAndEnd()\n            .skipPredicate(action => this._skipPredicate(action));\n        // Keep the manager active index in sync so that navigation picks\n        // up from the current chip if the user clicks into the list directly.\n        this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({ chip }) => {\n            const action = chip._getSourceAction(document.activeElement);\n            if (action) {\n                this._keyManager.updateActiveItem(action);\n            }\n        });\n        this._dir?.change\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive and disabled actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n        // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n        // chips.\n        return !action.isInteractive || action.disabled;\n    }\n    /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n    _trackChipSetChanges() {\n        this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.disabled) {\n                // Since this happens after the content has been\n                // checked, we need to defer it to the next tick.\n                Promise.resolve().then(() => this._syncChipsState());\n            }\n            this._redirectDestroyedChipFocus();\n        });\n    }\n    /** Starts tracking the destroyed chips in order to capture the focused one. */\n    _trackDestroyedFocusedChip() {\n        this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe((event) => {\n            const chipArray = this._chips.toArray();\n            const chipIndex = chipArray.indexOf(event.chip);\n            // If the focused chip is destroyed, save its index so that we can move focus to the next\n            // chip. We only save the index here, rather than move the focus immediately, because we want\n            // to wait until the chip is removed from the chip list before focusing the next one. This\n            // allows us to keep focus on the same index if the chip gets swapped out.\n            if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n                this._lastDestroyedFocusedChipIndex = chipIndex;\n            }\n        });\n    }\n    /**\n     * Finds the next appropriate chip to move focus to,\n     * if the currently-focused chip is destroyed.\n     */\n    _redirectDestroyedChipFocus() {\n        if (this._lastDestroyedFocusedChipIndex == null) {\n            return;\n        }\n        if (this._chips.length) {\n            const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n            const chipToFocus = this._chips.toArray()[newIndex];\n            if (chipToFocus.disabled) {\n                // If we're down to one disabled chip, move focus back to the set.\n                if (this._chips.length === 1) {\n                    this.focus();\n                }\n                else {\n                    this._keyManager.setPreviousItemActive();\n                }\n            }\n            else {\n                chipToFocus.focus();\n            }\n        }\n        else {\n            this.focus();\n        }\n        this._lastDestroyedFocusedChipIndex = null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipSet, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatChipSet, isStandalone: true, selector: \"mat-chip-set\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], role: \"role\", tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, host: { listeners: { \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.role\": \"role\" }, classAttribute: \"mat-mdc-chip-set mdc-evolution-chip-set\" }, queries: [{ propertyName: \"_chips\", predicate: MatChip, descendants: true }], ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipSet, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-set', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mat-mdc-chip-set mdc-evolution-chip-set',\n                        '(keydown)': '_handleKeydown($event)',\n                        '[attr.role]': 'role',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], role: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChip, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n    source;\n    value;\n    constructor(\n    /** Chip listbox that emitted the event. */\n    source, \n    /** Value of the chip listbox when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatChipListbox),\n    multi: true,\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nclass MatChipListbox extends MatChipSet {\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => { };\n    // TODO: MDC uses `grid` here\n    _defaultRole = 'listbox';\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, { optional: true });\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = value;\n        this._syncListboxProperties();\n    }\n    _multiple = false;\n    /** The array of selected chips inside the chip listbox. */\n    get selected() {\n        const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n        return this.multiple ? selectedChips : selectedChips[0];\n    }\n    /** Orientation of the chip list. */\n    ariaOrientation = 'horizontal';\n    /**\n     * Whether or not this chip listbox is selectable.\n     *\n     * When a chip listbox is not selectable, the selected states for all\n     * the chips inside the chip listbox are always ignored.\n     */\n    get selectable() {\n        return this._selectable;\n    }\n    set selectable(value) {\n        this._selectable = value;\n        this._syncListboxProperties();\n    }\n    _selectable = true;\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    compareWith = (o1, o2) => o1 === o2;\n    /** Whether this chip listbox is required. */\n    required = false;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncListboxProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n        return this._getChipStream(chip => chip.selectionChange);\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n        return this._getChipStream(chip => chip._onBlur);\n    }\n    /** The value of the listbox, which is the combined value of the selected chips. */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        if (this._chips && this._chips.length) {\n            this._setSelectionByValue(value, false);\n        }\n        this._value = value;\n    }\n    _value;\n    /** Event emitted when the selected chip listbox value has been changed by the user. */\n    change = new EventEmitter();\n    _chips = undefined;\n    ngAfterContentInit() {\n        this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.value !== undefined) {\n                Promise.resolve().then(() => {\n                    this._setSelectionByValue(this.value, false);\n                });\n            }\n            // Update listbox selectable/multiple properties on chips\n            this._syncListboxProperties();\n        });\n        this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n        this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n            if (!this.multiple) {\n                this._chips.forEach(chip => {\n                    if (chip !== event.source) {\n                        chip._setSelectedState(false, false, false);\n                    }\n                });\n            }\n            if (event.isUserInput) {\n                this._propagateChanges();\n            }\n        });\n    }\n    /**\n     * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n     * are no selected chips.\n     */\n    focus() {\n        if (this.disabled) {\n            return;\n        }\n        const firstSelectedChip = this._getFirstSelectedChip();\n        if (firstSelectedChip && !firstSelectedChip.disabled) {\n            firstSelectedChip.focus();\n        }\n        else if (this._chips.length > 0) {\n            this._keyManager.setFirstItemActive();\n        }\n        else {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n        if (value != null) {\n            this.value = value;\n        }\n        else {\n            this.value = undefined;\n        }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    /** Selects all chips with value. */\n    _setSelectionByValue(value, isUserInput = true) {\n        this._clearSelection();\n        if (Array.isArray(value)) {\n            value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n        }\n        else {\n            this._selectValue(value, isUserInput);\n        }\n    }\n    /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n    _blur() {\n        if (!this.disabled) {\n            // Wait to see if focus moves to an individual chip.\n            setTimeout(() => {\n                if (!this.focused) {\n                    this._markAsTouched();\n                }\n            });\n        }\n    }\n    _keydown(event) {\n        if (event.keyCode === TAB) {\n            super._allowFocusEscape();\n        }\n    }\n    /** Marks the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n        let valueToEmit = null;\n        if (Array.isArray(this.selected)) {\n            valueToEmit = this.selected.map(chip => chip.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : undefined;\n        }\n        this._value = valueToEmit;\n        this.change.emit(new MatChipListboxChange(this, valueToEmit));\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Deselects every chip in the listbox.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n        this._chips.forEach(chip => {\n            if (chip !== skip) {\n                chip.deselect();\n            }\n        });\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput) {\n        const correspondingChip = this._chips.find(chip => {\n            return chip.value != null && this.compareWith(chip.value, value);\n        });\n        if (correspondingChip) {\n            isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n        }\n        return correspondingChip;\n    }\n    /** Syncs the chip-listbox selection state with the individual chips. */\n    _syncListboxProperties() {\n        if (this._chips) {\n            // Defer setting the value in order to avoid the \"Expression\n            // has changed after it was checked\" errors from Angular.\n            Promise.resolve().then(() => {\n                this._chips.forEach(chip => {\n                    chip._chipListMultiple = this.multiple;\n                    chip.chipListSelectable = this._selectable;\n                    chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n                    chip._changeDetectorRef.markForCheck();\n                });\n            });\n        }\n    }\n    /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n    _getFirstSelectedChip() {\n        if (Array.isArray(this.selected)) {\n            return this.selected.length ? this.selected[0] : undefined;\n        }\n        else {\n            return this.selected;\n        }\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n        // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n        // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n        // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n        // exceptions for compound widgets.\n        //\n        // From [Developing a Keyboard Interface](\n        // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n        //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n        //   Listbox...\"\n        return !action.isInteractive;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipListbox, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatChipListbox, isStandalone: true, selector: \"mat-chip-listbox\", inputs: { multiple: [\"multiple\", \"multiple\", booleanAttribute], ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"], selectable: [\"selectable\", \"selectable\", booleanAttribute], compareWith: \"compareWith\", required: [\"required\", \"required\", booleanAttribute], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], value: \"value\" }, outputs: { change: \"change\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.role\": \"role\", \"tabIndex\": \"(disabled || empty) ? -1 : tabIndex\", \"attr.aria-required\": \"role ? required : null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-multiselectable\": \"multiple\", \"attr.aria-orientation\": \"ariaOrientation\", \"class.mat-mdc-chip-list-disabled\": \"disabled\", \"class.mat-mdc-chip-list-required\": \"required\" }, classAttribute: \"mdc-evolution-chip-set mat-mdc-chip-listbox\" }, providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR], queries: [{ propertyName: \"_chips\", predicate: MatChipOption, descendants: true }], usesInheritance: true, ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipListbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-listbox', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mdc-evolution-chip-set mat-mdc-chip-listbox',\n                        '[attr.role]': 'role',\n                        '[tabIndex]': '(disabled || empty) ? -1 : tabIndex',\n                        '[attr.aria-required]': 'role ? required : null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '[attr.aria-orientation]': 'ariaOrientation',\n                        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-list-required]': 'required',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                        '(keydown)': '_keydown($event)',\n                    }, providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"] }]\n        }], propDecorators: { multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], ariaOrientation: [{\n                type: Input,\n                args: ['aria-orientation']\n            }], selectable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChipOption, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n    source;\n    value;\n    constructor(\n    /** Chip grid that emitted the event. */\n    source, \n    /** Value of the chip grid when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nclass MatChipGrid extends MatChipSet {\n    ngControl = inject(NgControl, { optional: true, self: true });\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-chip-grid';\n    /** The chip input to add more chips */\n    _chipInput;\n    _defaultRole = 'grid';\n    _errorStateTracker;\n    /**\n     * List of element ids to propagate to the chipInput's aria-describedby attribute.\n     */\n    _ariaDescribedbyIds = [];\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => { };\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._syncChipsState();\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._chipInput.id;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return ((!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0));\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n        return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n    get focused() {\n        return this._chipInput.focused || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    _required;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this._value = value;\n    }\n    _value = [];\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n        return this._getChipStream(chip => chip._onBlur);\n    }\n    /** Emits when the chip grid value has been changed by the user. */\n    change = new EventEmitter();\n    /**\n     * Emits whenever the raw value of the chip-grid changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    _chips = undefined;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /** Whether the chip grid is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    constructor() {\n        super();\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        if (this.ngControl) {\n            this.ngControl.valueAccessor = this;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    }\n    ngAfterContentInit() {\n        this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._blur();\n            this.stateChanges.next();\n        });\n        merge(this.chipFocusChanges, this._chips.changes)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.stateChanges.next());\n    }\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n        if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n        }\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n        }\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this.stateChanges.complete();\n    }\n    /** Associates an HTML input element with this chip grid. */\n    registerInput(inputElement) {\n        this._chipInput = inputElement;\n        this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n        if (!this.disabled && !this._originatesFromChip(event)) {\n            this.focus();\n        }\n    }\n    /**\n     * Focuses the first chip in this chip grid, or the associated input when there\n     * are no eligible chips.\n     */\n    focus() {\n        if (this.disabled || this._chipInput.focused) {\n            return;\n        }\n        if (!this._chips.length || this._chips.first.disabled) {\n            // Delay until the next tick, because this can cause a \"changed after checked\"\n            // error if the input does something on focus (e.g. opens an autocomplete).\n            Promise.resolve().then(() => this._chipInput.focus());\n        }\n        else {\n            const activeItem = this._keyManager.activeItem;\n            if (activeItem) {\n                activeItem.focus();\n            }\n            else {\n                this._keyManager.setFirstItemActive();\n            }\n        }\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get describedByIds() {\n        return this._chipInput?.describedByIds || [];\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        // We must keep this up to date to handle the case where ids are set\n        // before the chip input is registered.\n        this._ariaDescribedbyIds = ids;\n        this._chipInput?.setDescribedByIds(ids);\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n        // The user is responsible for creating the child chips, so we just store the value.\n        this._value = value;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this.stateChanges.next();\n    }\n    /** Refreshes the error state of the chip grid. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n    _blur() {\n        if (!this.disabled) {\n            // Check whether the focus moved to chip input.\n            // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n            // to chip input, do nothing.\n            // Timeout is needed to wait for the focus() event trigger on chip input.\n            setTimeout(() => {\n                if (!this.focused) {\n                    this._propagateChanges();\n                    this._markAsTouched();\n                }\n            });\n        }\n    }\n    /**\n     * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the grid from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        if (!this._chipInput.focused) {\n            super._allowFocusEscape();\n        }\n    }\n    /** Handles custom keyboard events. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const activeItem = this._keyManager.activeItem;\n        if (keyCode === TAB) {\n            if (this._chipInput.focused &&\n                hasModifierKey(event, 'shiftKey') &&\n                this._chips.length &&\n                !this._chips.last.disabled) {\n                event.preventDefault();\n                if (activeItem) {\n                    this._keyManager.setActiveItem(activeItem);\n                }\n                else {\n                    this._focusLastChip();\n                }\n            }\n            else {\n                // Use the super method here since it doesn't check for the input\n                // focused state. This allows focus to escape if there's only one\n                // disabled chip left in the list.\n                super._allowFocusEscape();\n            }\n        }\n        else if (!this._chipInput.focused) {\n            // The up and down arrows are supposed to navigate between the individual rows in the grid.\n            // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n            // flag as the active action and moving focus between them ourseles instead of delegating\n            // to the key manager. For more information, see #29359 and:\n            // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n            if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n                const eligibleActions = this._chipActions.filter(action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action));\n                const currentIndex = eligibleActions.indexOf(activeItem);\n                const delta = event.keyCode === UP_ARROW ? -1 : 1;\n                event.preventDefault();\n                if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n                    this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n                }\n            }\n            else {\n                super._handleKeydown(event);\n            }\n        }\n        this.stateChanges.next();\n    }\n    _focusLastChip() {\n        if (this._chips.length) {\n            this._chips.last.focus();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n        const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n        this._value = valueToEmit;\n        this.change.emit(new MatChipGridChange(this, valueToEmit));\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipGrid, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatChipGrid, isStandalone: true, selector: \"mat-chip-grid\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], value: \"value\", errorStateMatcher: \"errorStateMatcher\" }, outputs: { change: \"change\", valueChange: \"valueChange\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\" }, properties: { \"attr.role\": \"role\", \"attr.tabindex\": \"(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"class.mat-mdc-chip-list-disabled\": \"disabled\", \"class.mat-mdc-chip-list-invalid\": \"errorState\", \"class.mat-mdc-chip-list-required\": \"required\" }, classAttribute: \"mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipGrid }], queries: [{ propertyName: \"_chips\", predicate: MatChipRow, descendants: true }], usesInheritance: true, ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipGrid, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-grid', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set',\n                        '[attr.role]': 'role',\n                        '[attr.tabindex]': '(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-list-invalid]': 'errorState',\n                        '[class.mat-mdc-chip-list-required]': 'required',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                    }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipGrid }], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-moz-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input::-webkit-input-placeholder{opacity:1}.mat-mdc-form-field:not(.mat-form-field-hide-placeholder) input.mat-mdc-chip-input:-ms-input-placeholder{opacity:1}.mat-mdc-chip-set+input.mat-mdc-chip-input{margin-left:0;margin-right:0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChipRow, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nclass MatChipInput {\n    _elementRef = inject(ElementRef);\n    /** Whether the control is focused. */\n    focused = false;\n    /** Register input for chip list */\n    get chipGrid() {\n        return this._chipGrid;\n    }\n    set chipGrid(value) {\n        if (value) {\n            this._chipGrid = value;\n            this._chipGrid.registerInput(this);\n        }\n    }\n    _chipGrid;\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    addOnBlur = false;\n    /**\n     * The list of key codes that will trigger a chipEnd event.\n     *\n     * Defaults to `[ENTER]`.\n     */\n    separatorKeyCodes;\n    /** Emitted when a chip is to be added. */\n    chipEnd = new EventEmitter();\n    /** The input's placeholder text. */\n    placeholder = '';\n    /** Unique id for the input. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n    /** Whether the input is disabled. */\n    get disabled() {\n        return this._disabled || (this._chipGrid && this._chipGrid.disabled);\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Whether the input is readonly. */\n    readonly = false;\n    /** Whether the input should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** Whether the input is empty. */\n    get empty() {\n        return !this.inputElement.value;\n    }\n    /** The native input element to which this directive is attached. */\n    inputElement;\n    constructor() {\n        const defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS);\n        const formField = inject(MAT_FORM_FIELD, { optional: true });\n        this.inputElement = this._elementRef.nativeElement;\n        this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n        this.disabledInteractive = defaultOptions.inputDisabledInteractive ?? false;\n        if (formField) {\n            this.inputElement.classList.add('mat-mdc-form-field-input-control');\n        }\n    }\n    ngOnChanges() {\n        this._chipGrid.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.chipEnd.complete();\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n        if (this.empty && event.keyCode === BACKSPACE) {\n            // Ignore events where the user is holding down backspace\n            // so that we don't accidentally remove too many chips.\n            if (!event.repeat) {\n                this._chipGrid._focusLastChip();\n            }\n            event.preventDefault();\n        }\n        else {\n            this._emitChipEnd(event);\n        }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n        if (this.addOnBlur) {\n            this._emitChipEnd();\n        }\n        this.focused = false;\n        // Blur the chip list if it is not focused\n        if (!this._chipGrid.focused) {\n            this._chipGrid._blur();\n        }\n        this._chipGrid.stateChanges.next();\n    }\n    _focus() {\n        this.focused = true;\n        this._chipGrid.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n        if (!event || (this._isSeparatorKey(event) && !event.repeat)) {\n            this.chipEnd.emit({\n                input: this.inputElement,\n                value: this.inputElement.value,\n                chipInput: this,\n            });\n            event?.preventDefault();\n        }\n    }\n    _onInput() {\n        // Let chip list know whenever the value changes.\n        this._chipGrid.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus() {\n        this.inputElement.focus();\n    }\n    /** Clears the input */\n    clear() {\n        this.inputElement.value = '';\n    }\n    /**\n     * Implemented as part of MatChipTextControl.\n     * @docs-private\n     */\n    get describedByIds() {\n        const element = this._elementRef.nativeElement;\n        const existingDescribedBy = element.getAttribute('aria-describedby');\n        return existingDescribedBy?.split(' ') || [];\n    }\n    setDescribedByIds(ids) {\n        const element = this._elementRef.nativeElement;\n        // Set the value directly in the DOM since this binding\n        // is prone to \"changed after checked\" errors.\n        if (ids.length) {\n            element.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            element.removeAttribute('aria-describedby');\n        }\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n        return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n    /** Gets the value to set on the `readonly` attribute. */\n    _getReadonlyAttribute() {\n        return this.readonly || (this.disabled && this.disabledInteractive) ? 'true' : null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatChipInput, isStandalone: true, selector: \"input[matChipInputFor]\", inputs: { chipGrid: [\"matChipInputFor\", \"chipGrid\"], addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\", booleanAttribute], separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"], placeholder: \"placeholder\", id: \"id\", disabled: [\"disabled\", \"disabled\", booleanAttribute], readonly: [\"readonly\", \"readonly\", booleanAttribute], disabledInteractive: [\"matChipInputDisabledInteractive\", \"disabledInteractive\", booleanAttribute] }, outputs: { chipEnd: \"matChipInputTokenEnd\" }, host: { listeners: { \"keydown\": \"_keydown($event)\", \"blur\": \"_blur()\", \"focus\": \"_focus()\", \"input\": \"_onInput()\" }, properties: { \"id\": \"id\", \"attr.disabled\": \"disabled && !disabledInteractive ? \\\"\\\" : null\", \"attr.placeholder\": \"placeholder || null\", \"attr.aria-invalid\": \"_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null\", \"attr.aria-required\": \"_chipGrid && _chipGrid.required || null\", \"attr.aria-disabled\": \"disabled && disabledInteractive ? \\\"true\\\" : null\", \"attr.readonly\": \"_getReadonlyAttribute()\", \"attr.required\": \"_chipGrid && _chipGrid.required || null\" }, classAttribute: \"mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element\" }, exportAs: [\"matChipInput\", \"matChipInputFor\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matChipInputFor]',\n                    exportAs: 'matChipInput, matChipInputFor',\n                    host: {\n                        // TODO: eventually we should remove `mat-input-element` from here since it comes from the\n                        // non-MDC version of the input. It's currently being kept for backwards compatibility, because\n                        // the MDC chips were landed initially with it.\n                        'class': 'mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element',\n                        '(keydown)': '_keydown($event)',\n                        '(blur)': '_blur()',\n                        '(focus)': '_focus()',\n                        '(input)': '_onInput()',\n                        '[id]': 'id',\n                        '[attr.disabled]': 'disabled && !disabledInteractive ? \"\" : null',\n                        '[attr.placeholder]': 'placeholder || null',\n                        '[attr.aria-invalid]': '_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null',\n                        '[attr.aria-required]': '_chipGrid && _chipGrid.required || null',\n                        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n                        '[attr.readonly]': '_getReadonlyAttribute()',\n                        '[attr.required]': '_chipGrid && _chipGrid.required || null',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { chipGrid: [{\n                type: Input,\n                args: ['matChipInputFor']\n            }], addOnBlur: [{\n                type: Input,\n                args: [{ alias: 'matChipInputAddOnBlur', transform: booleanAttribute }]\n            }], separatorKeyCodes: [{\n                type: Input,\n                args: ['matChipInputSeparatorKeyCodes']\n            }], chipEnd: [{\n                type: Output,\n                args: ['matChipInputTokenEnd']\n            }], placeholder: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], readonly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ alias: 'matChipInputDisabledInteractive', transform: booleanAttribute }]\n            }] } });\n\nconst CHIP_DECLARATIONS = [\n    MatChip,\n    MatChipAvatar,\n    MatChipEditInput,\n    MatChipGrid,\n    MatChipInput,\n    MatChipListbox,\n    MatChipOption,\n    MatChipRemove,\n    MatChipRow,\n    MatChipSet,\n    MatChipTrailingIcon,\n];\nclass MatChipsModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipsModule, imports: [MatCommonModule, MatRippleModule, MatChipAction, MatChip,\n            MatChipAvatar,\n            MatChipEditInput,\n            MatChipGrid,\n            MatChipInput,\n            MatChipListbox,\n            MatChipOption,\n            MatChipRemove,\n            MatChipRow,\n            MatChipSet,\n            MatChipTrailingIcon], exports: [MatCommonModule, MatChip,\n            MatChipAvatar,\n            MatChipEditInput,\n            MatChipGrid,\n            MatChipInput,\n            MatChipListbox,\n            MatChipOption,\n            MatChipRemove,\n            MatChipRow,\n            MatChipSet,\n            MatChipTrailingIcon] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipsModule, providers: [\n            ErrorStateMatcher,\n            {\n                provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                useValue: {\n                    separatorKeyCodes: [ENTER],\n                },\n            },\n        ], imports: [MatCommonModule, MatRippleModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatChipsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, MatChipAction, CHIP_DECLARATIONS],\n                    exports: [MatCommonModule, CHIP_DECLARATIONS],\n                    providers: [\n                        ErrorStateMatcher,\n                        {\n                            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                            useValue: {\n                                separatorKeyCodes: [ENTER],\n                            },\n                        },\n                    ],\n                }]\n        }] });\n\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,EAAEC,eAAe,QAAQ,mBAAmB;AAC/E,SAASC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AAClH,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACpF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC9V,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,yBAAyB,QAAQ,uBAAuB;AACtE,SAASH,CAAC,IAAII,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAChE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AACrG,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAASjB,CAAC,IAAIkB,kBAAkB,QAAQ,4BAA4B;AACpE,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AACzF,SAASjB,CAAC,IAAIkB,eAAe,QAAQ,8BAA8B;AACnE,SAASlB,CAAC,IAAImB,eAAe,QAAQ,sBAAsB;AAC3D,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAC5B,OAAO,iBAAiB;AACxB,OAAO,gCAAgC;;AAEvC;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgG6FvD,EAAE,CAAAyD,cAAA,aA4Wm+E,CAAC;IA5Wt+EzD,EAAE,CAAA0D,YAAA,KA4WkjF,CAAC;IA5WrjF1D,EAAE,CAAA2D,YAAA,CA4WikF,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5WpkFvD,EAAE,CAAAyD,cAAA,aA4W+5F,CAAC;IA5Wl6FzD,EAAE,CAAA0D,YAAA,KA4WsgG,CAAC;IA5WzgG1D,EAAE,CAAA2D,YAAA,CA4WihG,CAAC;EAAA;AAAA;AAAA,SAAAE,qCAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5WphGvD,EAAE,CAAAyD,cAAA,aA+jBua,CAAC;IA/jB1azD,EAAE,CAAA0D,YAAA,KA+jBsf,CAAC;IA/jBzf1D,EAAE,CAAAyD,cAAA,aA+jB8iB,CAAC;IA/jBjjBzD,EAAE,CAAA8D,cAAA;IAAF9D,EAAE,CAAAyD,cAAA,YA+jBguB,CAAC;IA/jBnuBzD,EAAE,CAAA+D,SAAA,cA+jBm4B,CAAC;IA/jBt4B/D,EAAE,CAAA2D,YAAA,CA+jBq5B,CAAC,CAAgB,CAAC,CAAc,CAAC;EAAA;AAAA;AAAA,SAAAK,qCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/jBx7BvD,EAAE,CAAAyD,cAAA,aA+jBqxC,CAAC;IA/jBxxCzD,EAAE,CAAA0D,YAAA,KA+jB43C,CAAC;IA/jB/3C1D,EAAE,CAAA2D,YAAA,CA+jBu4C,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,kCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/jB14CvD,EAAE,CAAA+D,SAAA,aA8vBoQ,CAAC;EAAA;AAAA;AAAA,SAAAM,kCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vBvQvD,EAAE,CAAAyD,cAAA,aA8vB+kB,CAAC;IA9vBllBzD,EAAE,CAAA0D,YAAA,EA8vB4pB,CAAC;IA9vB/pB1D,EAAE,CAAA2D,YAAA,CA8vByqB,CAAC;EAAA;AAAA;AAAA,SAAAW,gDAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vB5qBvD,EAAE,CAAA0D,YAAA,KA8vBs3B,CAAC;EAAA;AAAA;AAAA,SAAAa,gDAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vBz3BvD,EAAE,CAAA+D,SAAA,aA8vB+6B,CAAC;EAAA;AAAA;AAAA,SAAAS,kCAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vBl7BvD,EAAE,CAAAyE,mBAAA,IAAAH,+CAAA,MA8vBqzB,CAAC,IAAAC,+CAAA,iBAAiF,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAmB,MAAA,GA9vB14B1E,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAA4E,aAAA,CAAAF,MAAA,CAAAG,gBAAA,QA8vBw7B,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vB37BvD,EAAE,CAAA0D,YAAA,KA8vBw+B,CAAC;EAAA;AAAA;AAAA,SAAAqB,kCAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9vB3+BvD,EAAE,CAAAyD,cAAA,aA8vB0vC,CAAC;IA9vB7vCzD,EAAE,CAAA0D,YAAA,KA8vBi2C,CAAC;IA9vBp2C1D,EAAE,CAAA2D,YAAA,CA8vB42C,CAAC;EAAA;AAAA;AAAA,MAAAqB,GAAA;AAAA,MAAAC,GAAA;AA71B58C,MAAMC,yBAAyB,gBAAG,IAAIjF,cAAc,CAAC,2BAA2B,EAAE;EAC9EkF,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,MAAO;IACZC,iBAAiB,EAAE,CAAC/F,KAAK;EAC7B,CAAC;AACL,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMgG,eAAe,gBAAG,IAAIrF,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMsF,sBAAsB,gBAAG,IAAItF,cAAc,CAAC,qBAAqB,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA,MAAMuF,eAAe,gBAAG,IAAIvF,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA;AACA;AACA,MAAMwF,QAAQ,gBAAG,IAAIxF,cAAc,CAAC,SAAS,CAAC;;AAE9C;AACA;AACA;AACA;AAHA,IAIMyF,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBC,WAAW,GAAGzF,MAAM,CAACC,UAAU,CAAC;IAChCyF,WAAW,GAAG1F,MAAM,CAACuF,QAAQ,CAAC;IAC9B;IACAI,aAAa,GAAG,IAAI;IACpB;IACAC,UAAU,GAAG,IAAI;IACjB;IACA,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACJ,WAAW,EAAEG,QAAQ,IAAI,KAAK;IAChE;IACA,IAAIA,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IAC1B;IACAD,SAAS,GAAG,KAAK;IACjB;IACAE,QAAQ,GAAG,CAAC,CAAC;IACb;AACJ;AACA;IACIC,uBAAuB,GAAG,KAAK;IAC/B;AACJ;AACA;IACIC,qBAAqBA,CAAA,EAAG;MACpB;MACA;MACA,OAAO,IAAI,CAACL,QAAQ,IAAI,CAAC,IAAI,CAACI,uBAAuB,GAAG,EAAE,GAAG,IAAI;IACrE;IACA;AACJ;AACA;IACIE,YAAYA,CAAA,EAAG;MACX,OAAQ,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAACI,uBAAuB,IAAK,CAAC,IAAI,CAACN,aAAa,GACxE,IAAI,GACJ,IAAI,CAACK,QAAQ,CAACI,QAAQ,CAAC,CAAC;IAClC;IACAC,WAAWA,CAAA,EAAG;MACVrG,MAAM,CAACJ,sBAAsB,CAAC,CAAC0G,IAAI,CAAC5E,uBAAuB,CAAC;MAC5D,IAAI,IAAI,CAAC+D,WAAW,CAACc,aAAa,CAACC,QAAQ,KAAK,QAAQ,EAAE;QACtD,IAAI,CAACf,WAAW,CAACc,aAAa,CAACE,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACjE;IACJ;IACAC,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACjB,WAAW,CAACc,aAAa,CAACG,KAAK,CAAC,CAAC;IAC1C;IACAC,YAAYA,CAACC,KAAK,EAAE;MAChB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,UAAU,EAAE;QACzDgB,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnB,WAAW,CAACoB,+BAA+B,CAAC,CAAC;MACtD;IACJ;IACAC,cAAcA,CAACH,KAAK,EAAE;MAClB,IAAI,CAACA,KAAK,CAACI,OAAO,KAAK5H,KAAK,IAAIwH,KAAK,CAACI,OAAO,KAAK3H,KAAK,KACnD,CAAC,IAAI,CAACwG,QAAQ,IACd,IAAI,CAACF,aAAa,IAClB,IAAI,CAACC,UAAU,IACf,CAAC,IAAI,CAACF,WAAW,CAACuB,UAAU,EAAE;QAC9BL,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnB,WAAW,CAACoB,+BAA+B,CAAC,CAAC;MACtD;IACJ;IACA,OAAOI,IAAI,YAAAC,sBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwF5B,aAAa;IAAA;IAChH,OAAO6B,IAAI,kBAD8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EACJ/B,aAAa;MAAAgC,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,2BAAAvE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADXvD,EAAE,CAAA+H,UAAA,mBAAAC,uCAAAC,MAAA;YAAA,OACJzE,GAAA,CAAAqD,YAAA,CAAAoB,MAAmB,CAAC;UAAA,CAAR,CAAC,qBAAAC,yCAAAD,MAAA;YAAA,OAAbzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;UAAA,CAAV,CAAC;QAAA;QAAA,IAAA1E,EAAA;UADXvD,EAAE,CAAAmI,WAAA,aACJ3E,GAAA,CAAA6C,YAAA,CAAa,CAAC,cAAd7C,GAAA,CAAA4C,qBAAA,CAAsB,CAAC,mBAAA5C,GAAA,CAAAuC,QAAA;UADrB/F,EAAE,CAAAoI,WAAA,wCAAA5E,GAAA,CAAAsC,UACQ,CAAC,gDAAAtC,GAAA,CAAAqC,aAAD,CAAC,0CAAArC,GAAA,CAAAsC,UAAD,CAAC;QAAA;MAAA;MAAAuC,MAAA;QAAAxC,aAAA;QAAAE,QAAA,8BAAgI3F,gBAAgB;QAAA8F,QAAA,8BAAuCD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG5F,eAAe,CAAC4F,KAAK,CAAE;QAAAE,uBAAA;MAAA;IAAA;EACtV;EAAC,OAhEKT,aAAa;AAAA;AAiEnB;EAAA,QAAA4C,SAAA,oBAAAA,SAAA;AAAA;;AA8BA;AAAA,IACMC,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB,OAAOnB,IAAI,YAAAoB,sBAAAlB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiB,aAAa;IAAA;IAChH,OAAOhB,IAAI,kBApC8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EAoCJc,aAAa;MAAAb,SAAA;MAAAC,SAAA,WAAkG,KAAK;MAAAc,QAAA,GApClHzI,EAAE,CAAA0I,kBAAA,CAoCmO,CAAC;QAAEC,OAAO,EAAErD,eAAe;QAAEsD,WAAW,EAAEL;MAAc,CAAC,CAAC;IAAA;EAC5X;EAAC,OAHKA,aAAa;AAAA;AAInB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAWA;AAAA,IACMO,mBAAmB;EAAzB,MAAMA,mBAAmB,SAASnD,aAAa,CAAC;IAC5C;AACJ;AACA;AACA;IACIG,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClB,OAAOsB,IAAI;MAAA,IAAA0B,gCAAA;MAAA,gBAAAC,4BAAAzB,iBAAA;QAAA,QAAAwB,gCAAA,KAAAA,gCAAA,GAzD8E9I,EAAE,CAAAgJ,qBAAA,CAyDQH,mBAAmB,IAAAvB,iBAAA,IAAnBuB,mBAAmB;MAAA;IAAA;IACtH,OAAOtB,IAAI,kBA1D8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EA0DJoB,mBAAmB;MAAAnB,SAAA;MAAAC,SAAA,kBAAsH,MAAM;MAAAc,QAAA,GA1D7IzI,EAAE,CAAA0I,kBAAA,CA0DsQ,CAAC;QAAEC,OAAO,EAAEpD,sBAAsB;QAAEqD,WAAW,EAAEC;MAAoB,CAAC,CAAC,GA1D/U7I,EAAE,CAAAiJ,0BAAA;IAAA;EA2D/F;EAAC,OATKJ,mBAAmB;AAAA;AAUzB;EAAA,QAAAP,SAAA,oBAAAA,SAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,IAeMY,aAAa;EAAnB,MAAMA,aAAa,SAASxD,aAAa,CAAC;IACtCI,UAAU,GAAG,KAAK;IAClBe,YAAYA,CAACC,KAAK,EAAE;MAChB,IAAI,CAAC,IAAI,CAACf,QAAQ,EAAE;QAChBe,KAAK,CAACqC,eAAe,CAAC,CAAC;QACvBrC,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnB,WAAW,CAACwD,MAAM,CAAC,CAAC;MAC7B;IACJ;IACAnC,cAAcA,CAACH,KAAK,EAAE;MAClB,IAAI,CAACA,KAAK,CAACI,OAAO,KAAK5H,KAAK,IAAIwH,KAAK,CAACI,OAAO,KAAK3H,KAAK,KAAK,CAAC,IAAI,CAACwG,QAAQ,EAAE;QACxEe,KAAK,CAACqC,eAAe,CAAC,CAAC;QACvBrC,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnB,WAAW,CAACwD,MAAM,CAAC,CAAC;MAC7B;IACJ;IACA,OAAOhC,IAAI;MAAA,IAAAiC,0BAAA;MAAA,gBAAAC,sBAAAhC,iBAAA;QAAA,QAAA+B,0BAAA,KAAAA,0BAAA,GAtG8ErJ,EAAE,CAAAgJ,qBAAA,CAsGQE,aAAa,IAAA5B,iBAAA,IAAb4B,aAAa;MAAA;IAAA;IAChH,OAAO3B,IAAI,kBAvG8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EAuGJyB,aAAa;MAAAxB,SAAA;MAAAC,SAAA,WAAiF,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAA0B,2BAAAhG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvGpGvD,EAAE,CAAAmI,WAAA,gBAuGJ,IAAI;QAAA;MAAA;MAAAM,QAAA,GAvGFzI,EAAE,CAAA0I,kBAAA,CAuGiT,CAAC;QAAEC,OAAO,EAAEnD,eAAe;QAAEoD,WAAW,EAAEM;MAAc,CAAC,CAAC,GAvG7WlJ,EAAE,CAAAiJ,0BAAA;IAAA;EAwG/F;EAAC,OAlBKC,aAAa;AAAA;AAmBnB;EAAA,QAAAZ,SAAA,oBAAAA,SAAA;AAAA;;AAcA;AACA;AACA;AACA;AACA;AAJA,IAKMkB,OAAO;EAAb,MAAMA,OAAO,CAAC;IACVC,kBAAkB,GAAGvJ,MAAM,CAACM,iBAAiB,CAAC;IAC9CmF,WAAW,GAAGzF,MAAM,CAACC,UAAU,CAAC;IAChCuJ,QAAQ,GAAGxJ,MAAM,CAACO,aAAa,CAAC;IAChCkJ,OAAO,GAAGzJ,MAAM,CAACQ,MAAM,CAAC;IACxBkJ,aAAa,GAAG1J,MAAM,CAACf,YAAY,CAAC;IACpC0K,oBAAoB,GAAG3J,MAAM,CAAC4B,yBAAyB,EAAE;MACrDgI,QAAQ,EAAE;IACd,CAAC,CAAC;IACFC,SAAS,GAAG7J,MAAM,CAACS,QAAQ,CAAC;IAC5B;IACAqJ,QAAQ,GAAG,IAAIvI,OAAO,CAAC,CAAC;IACxB;IACAwI,OAAO,GAAG,IAAIxI,OAAO,CAAC,CAAC;IACvB;IACAyI,YAAY;IACZ;IACAC,IAAI,GAAG,IAAI;IACX;IACAC,iBAAiB,GAAG,KAAK;IACzB;IACAC,aAAa;IACb;IACAC,cAAc;IACd;IACAvI,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C;IACAwI,gBAAgB;IAChB;IACAC,iBAAiB;IACjB;IACAC,eAAe;IACfC,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACN,iBAAiB;IACjC;IACA;IACAO,EAAE,GAAGzK,MAAM,CAACd,YAAY,CAAC,CAACwL,KAAK,CAAC,eAAe,CAAC;IAChD;IACA;IACA;IACA;IACAC,SAAS,GAAG,IAAI;IAChB;IACA;IACA;IACA;IACAC,eAAe,GAAG,IAAI;IACtB;IACAC,kBAAkB,GAAG,GAAG,IAAI,CAACJ,EAAE,mBAAmB;IAClD;IACAK,iBAAiB,GAAG,KAAK;IACzBC,YAAY;IACZ;AACJ;AACA;AACA;IACI,IAAIhF,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACiF,MAAM,KAAKC,SAAS,GAAG,IAAI,CAACD,MAAM,GAAG,IAAI,CAACD,YAAY,CAACG,WAAW,CAACC,IAAI,CAAC,CAAC;IACzF;IACA,IAAIpF,KAAKA,CAACA,KAAK,EAAE;MACb,IAAI,CAACiF,MAAM,GAAGjF,KAAK;IACvB;IACAiF,MAAM;IACN;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACII,KAAK;IACL;AACJ;AACA;IACIC,SAAS,GAAG,IAAI;IAChB;AACJ;AACA;IACIC,WAAW,GAAG,KAAK;IACnB;IACAC,aAAa,GAAG,KAAK;IACrB;IACA,IAAI1F,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACgF,iBAAiB;IACnD;IACA,IAAIjF,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IAC1B;IACAD,SAAS,GAAG,KAAK;IACjB;IACA0F,OAAO,GAAG,IAAI9K,YAAY,CAAC,CAAC;IAC5B;IACA+K,SAAS,GAAG,IAAI/K,YAAY,CAAC,CAAC;IAC9B;IACAgL,iBAAiB,GAAG,gBAAgB;IACpC;IACAC,WAAW;IACX;IACAC,YAAY;IACZ;IACAC,UAAU;IACV;IACAC,aAAa;IACb;AACJ;AACA;AACA;IACIC,aAAa,GAAG/L,MAAM,CAAC+B,eAAe,CAAC;IACvCiK,SAAS,GAAGhM,MAAM,CAACW,QAAQ,CAAC;IAC5B0F,WAAWA,CAAA,EAAG;MACV,MAAM4F,WAAW,GAAGjM,MAAM,CAACJ,sBAAsB,CAAC;MAClDqM,WAAW,CAAC3F,IAAI,CAAC5E,uBAAuB,CAAC;MACzCuK,WAAW,CAAC3F,IAAI,CAACzG,qBAAqB,CAAC;MACvC,IAAI,CAACqM,aAAa,CAAC,CAAC;MACpB,IAAI,CAACH,aAAa,EAAEI,eAAe,CAAC,IAAI,CAAC1G,WAAW,CAACc,aAAa,EAAE;QAChE6F,SAAS,EAAE,qBAAqB;QAChCvG,QAAQ,EAAE,IAAI,CAACwG,iBAAiB,CAAC;MACrC,CAAC,CAAC;IACN;IACAC,QAAQA,CAAA,EAAG;MACP;MACA;MACA,IAAI,CAACtC,YAAY,GACb,IAAI,CAACvE,WAAW,CAACc,aAAa,CAACgG,YAAY,CAAC,IAAI,CAACb,iBAAiB,CAAC,IAC/D,IAAI,CAAClC,QAAQ,CAACgD,WAAW,CAAC,CAAC,KAAK,IAAI,CAACd,iBAAiB;IAClE;IACAe,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACtF,WAAW,CAACc,aAAa,CAACmG,aAAa,CAAC,4BAA4B,CAAC;MAC9F,IAAI,IAAI,CAACvC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACzD,KAAK,CAAC,CAAC;MAChB;IACJ;IACAiG,kBAAkBA,CAAA,EAAG;MACjB;MACA;MACA,IAAI,CAACvC,cAAc,GAAG5I,KAAK,CAAC,IAAI,CAAC6I,gBAAgB,CAACuC,OAAO,EAAE,IAAI,CAACtC,iBAAiB,CAACsC,OAAO,EAAE,IAAI,CAACrC,eAAe,CAACqC,OAAO,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACtD,kBAAkB,CAACuD,YAAY,CAAC,CAAC,CAAC;IACpL;IACAC,SAASA,CAAA,EAAG;MACR,IAAI,CAAChB,aAAa,CAACiB,WAAW,CAAC,IAAI,CAACvH,WAAW,CAACc,aAAa,EAAE,IAAI,CAAC8F,iBAAiB,CAAC,CAAC,CAAC;IAC5F;IACAY,WAAWA,CAAA,EAAG;MACV,IAAI,CAACvD,aAAa,CAACwD,cAAc,CAAC,IAAI,CAACzH,WAAW,CAAC;MACnD,IAAI,CAACsG,aAAa,EAAEoB,aAAa,CAAC,IAAI,CAAC1H,WAAW,CAACc,aAAa,CAAC;MACjE,IAAI,CAAC6D,cAAc,EAAEgD,WAAW,CAAC,CAAC;MAClC,IAAI,CAAC3B,SAAS,CAAC4B,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;MACnC,IAAI,CAAC7B,SAAS,CAAC8B,QAAQ,CAAC,CAAC;IAC7B;IACA;AACJ;AACA;AACA;AACA;IACIrE,MAAMA,CAAA,EAAG;MACL,IAAI,IAAI,CAACmC,SAAS,EAAE;QAChB,IAAI,CAACG,OAAO,CAAC6B,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACrC;IACJ;IACA;IACAjB,iBAAiBA,CAAA,EAAG;MAChB,OAAQ,IAAI,CAACxG,QAAQ,IACjB,IAAI,CAAC0F,aAAa,IAClB,IAAI,CAAC1J,mBAAmB,IACxB,IAAI,CAACmI,YAAY,IACjB,CAAC,CAAC,IAAI,CAACL,oBAAoB,EAAE9D,QAAQ;IAC7C;IACA;IACA2H,gBAAgBA,CAAA,EAAG;MACf,OAAO,CAAC,EAAE,IAAI,CAAC5B,YAAY,IAAI,IAAI,CAACC,UAAU,CAAC;IACnD;IACA;IACA9E,cAAcA,CAACH,KAAK,EAAE;MAClB;MACA;MACA,IAAKA,KAAK,CAACI,OAAO,KAAK1H,SAAS,IAAI,CAACsH,KAAK,CAAC6G,MAAM,IAAK7G,KAAK,CAACI,OAAO,KAAKzH,MAAM,EAAE;QAC5EqH,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACqC,MAAM,CAAC,CAAC;MACjB;IACJ;IACA;IACAxC,KAAKA,CAAA,EAAG;MACJ,IAAI,CAAC,IAAI,CAACb,QAAQ,EAAE;QAChB;QACA;QACA;QACA,IAAI,IAAI,CAACiG,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAACpF,KAAK,CAAC,CAAC;QAC9B,CAAC,MACI;UACD,IAAI,CAACyD,aAAa,GAAG,IAAI;QAC7B;MACJ;IACJ;IACA;IACAuD,gBAAgBA,CAACC,MAAM,EAAE;MACrB,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI;QACrC,MAAMC,OAAO,GAAGD,MAAM,CAACrI,WAAW,CAACc,aAAa;QAChD,OAAOwH,OAAO,KAAKJ,MAAM,IAAII,OAAO,CAACC,QAAQ,CAACL,MAAM,CAAC;MACzD,CAAC,CAAC;IACN;IACA;IACAC,WAAWA,CAAA,EAAG;MACV,MAAMK,MAAM,GAAG,EAAE;MACjB,IAAI,IAAI,CAACnC,aAAa,EAAE;QACpBmC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpC,aAAa,CAAC;MACnC;MACA,IAAI,IAAI,CAACD,UAAU,EAAE;QACjBoC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,UAAU,CAAC;MAChC;MACA,IAAI,IAAI,CAACD,YAAY,EAAE;QACnBqC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtC,YAAY,CAAC;MAClC;MACA,OAAOqC,MAAM;IACjB;IACA;IACAnH,+BAA+BA,CAAA,EAAG;MAC9B;IAAA;IAEJ;IACAoF,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACxC,aAAa,CAACyE,OAAO,CAAC,IAAI,CAAC1I,WAAW,EAAE,IAAI,CAAC,CAACoH,SAAS,CAACuB,MAAM,IAAI;QACnE,MAAMC,QAAQ,GAAGD,MAAM,KAAK,IAAI;QAChC,IAAIC,QAAQ,KAAK,IAAI,CAACnE,iBAAiB,EAAE;UACrC,IAAI,CAACA,iBAAiB,GAAGmE,QAAQ;UACjC,IAAIA,QAAQ,EAAE;YACV,IAAI,CAACvE,QAAQ,CAACwE,IAAI,CAAC;cAAEhB,IAAI,EAAE;YAAK,CAAC,CAAC;UACtC,CAAC,MACI;YACD;YACA;YACA;YACA;YACA,IAAI,CAAC/D,kBAAkB,CAACuD,YAAY,CAAC,CAAC;YACtCyB,UAAU,CAAC,MAAM,IAAI,CAAC9E,OAAO,CAAC+E,GAAG,CAAC,MAAM,IAAI,CAACzE,OAAO,CAACuE,IAAI,CAAC;cAAEhB,IAAI,EAAE;YAAK,CAAC,CAAC,CAAC,CAAC;UAC/E;QACJ;MACJ,CAAC,CAAC;IACN;IACA,OAAOpG,IAAI,YAAAuH,gBAAArH,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkC,OAAO;IAAA;IAC1G,OAAOoF,IAAI,kBA5W8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA4WJ+B,OAAO;MAAA9B,SAAA;MAAAoH,cAAA,WAAAC,uBAAAxL,EAAA,EAAAC,GAAA,EAAAwL,QAAA;QAAA,IAAAzL,EAAA;UA5WLvD,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4WqkD1J,eAAe;UA5WtlDtF,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4WmqDzJ,sBAAsB;UA5W3rDvF,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4WswDxJ,eAAe;UA5WvxDxF,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4W21D1J,eAAe;UA5W52DtF,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4Wi7DzJ,sBAAsB;UA5Wz8DvF,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA4W4gExJ,eAAe;QAAA;QAAA,IAAAjC,EAAA;UAAA,IAAA2L,EAAA;UA5W7hElP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAqI,WAAA,GAAAqD,EAAA,CAAAG,KAAA;UAAFrP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAsI,YAAA,GAAAoD,EAAA,CAAAG,KAAA;UAAFrP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAuI,UAAA,GAAAmD,EAAA,CAAAG,KAAA;UAAFrP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAA+G,gBAAA,GAAA2E,EAAA;UAAFlP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAgH,iBAAA,GAAA0E,EAAA;UAAFlP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAiH,eAAA,GAAAyE,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAC,cAAAhM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAwP,WAAA,CA4W0nE9J,aAAa;QAAA;QAAA,IAAAnC,EAAA;UAAA,IAAA2L,EAAA;UA5WzoElP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAwI,aAAA,GAAAkD,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA1H,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA4H,qBAAAlM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAA+H,UAAA,qBAAA2H,mCAAAzH,MAAA;YAAA,OA4WJzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;UAAA,CAAhB,CAAC;QAAA;QAAA,IAAA1E,EAAA;UA5WLvD,EAAE,CAAA2P,aAAA,OAAAnM,GAAA,CAAAmH,EA4WE,CAAC;UA5WL3K,EAAE,CAAAmI,WAAA,SAAA3E,GAAA,CAAA2G,IAAA,gBAAA3G,GAAA,CAAAqH,SAAA;UAAF7K,EAAE,CAAA4P,UAAA,CA4WJ,MAAM,IAAApM,GAAA,CAAA8H,KAAA,IAAa,SAAS,CAAtB,CAAC;UA5WLtL,EAAE,CAAAoI,WAAA,wBAAA5E,GAAA,CAAA0G,YA4WE,CAAC,iCAAA1G,GAAA,CAAAuC,QAAD,CAAC,6CAAPvC,GAAA,CAAAkK,gBAAA,CAAiB,CAAX,CAAC,6CAAAlK,GAAA,CAAAqI,WAAD,CAAC,0CAAArI,GAAA,CAAAqI,WAAD,CAAC,oCAAArI,GAAA,CAAAqI,WAAD,CAAC,6BAAArI,GAAA,CAAAqI,WAAD,CAAC,6BAAArI,GAAA,CAAAgI,WAAD,CAAC,0BAAAhI,GAAA,CAAAuC,QAAD,CAAC,uBAAAvC,GAAA,CAAA0G,YAAD,CAAC,2BAAA1G,GAAA,CAAA0G,YAAD,CAAC,oCAAP1G,GAAA,CAAAkK,gBAAA,CAAiB,CAAX,CAAC,4BAAAlK,GAAA,CAAAzB,mBAAD,CAAC;QAAA;MAAA;MAAAsG,MAAA;QAAA8B,IAAA;QAAAQ,EAAA;QAAAE,SAAA;QAAAC,eAAA;QAAA7E,KAAA;QAAAqF,KAAA;QAAAC,SAAA,gCAAoSnL,gBAAgB;QAAAoL,WAAA,oCAA+CpL,gBAAgB;QAAAqL,aAAA,wCAAqDrL,gBAAgB;QAAA2F,QAAA,8BAAsC3F,gBAAgB;MAAA;MAAAyP,OAAA;QAAAnE,OAAA;QAAAC,SAAA;MAAA;MAAAmE,QAAA;MAAArH,QAAA,GA5WnfzI,EAAE,CAAA0I,kBAAA,CA4Wq9C,CAAC;QAAEC,OAAO,EAAElD,QAAQ;QAAEmD,WAAW,EAAEY;MAAQ,CAAC,CAAC;MAAAuG,kBAAA,EAAA1M,GAAA;MAAA2M,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iBAAA7M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5WpgDvD,EAAE,CAAAqQ,eAAA,CAAAjN,GAAA;UAAFpD,EAAE,CAAA+D,SAAA,aA4WgwE,CAAC;UA5WnwE/D,EAAE,CAAAyD,cAAA,aA4W+0E,CAAC,aAAiD,CAAC;UA5Wp4EzD,EAAE,CAAAyE,mBAAA,IAAAnB,8BAAA,iBA4W05E,CAAC;UA5W75EtD,EAAE,CAAAyD,cAAA,aA4WupF,CAAC;UA5W1pFzD,EAAE,CAAA0D,YAAA,EA4WwrF,CAAC;UA5W3rF1D,EAAE,CAAA+D,SAAA,aA4WgxF,CAAC;UA5WnxF/D,EAAE,CAAA2D,YAAA,CA4W6xF,CAAC,CAAU,CAAC,CAAQ,CAAC;UA5WpzF3D,EAAE,CAAAyE,mBAAA,IAAAb,8BAAA,iBA4W+0F,CAAC;QAAA;QAAA,IAAAL,EAAA;UA5Wl1FvD,EAAE,CAAAsQ,SAAA,EA4Wg4E,CAAC;UA5Wn4EtQ,EAAE,CAAAuQ,UAAA,uBA4Wg4E,CAAC;UA5Wn4EvQ,EAAE,CAAAsQ,SAAA,CA4WwkF,CAAC;UA5W3kFtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAqI,WAAA,SA4WwkF,CAAC;UA5W3kF7L,EAAE,CAAAsQ,SAAA,EA4WohG,CAAC;UA5WvhGtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAkK,gBAAA,WA4WohG,CAAC;QAAA;MAAA;MAAA8C,YAAA,GAAggiB9K,aAAa;MAAA+K,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACjooB;EAAC,OAjPKnH,OAAO;AAAA;AAkPb;EAAA,QAAAlB,SAAA,oBAAAA,SAAA;AAAA;;AA4EA;AACA,MAAMsI,sBAAsB,CAAC;EACzBC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXxK,WAAWA,CACX;EACAsK,MAAM,EACN;EACAC,QAAQ,EACR;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMC,aAAa;EAAnB,MAAMA,aAAa,SAASxH,OAAO,CAAC;IAChC;IACAyH,eAAe,GAAG/Q,MAAM,CAACgF,yBAAyB,EAAE;MAAE4E,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE;IACAoH,kBAAkB,GAAG,IAAI;IACzB;IACAC,iBAAiB,GAAG,KAAK;IACzB;IACAC,qCAAqC,GAAG,IAAI,CAACH,eAAe,EAAEI,4BAA4B,IAAI,KAAK;IACnG;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIC,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAACL,kBAAkB;IACtD;IACA,IAAII,UAAUA,CAACrL,KAAK,EAAE;MAClB,IAAI,CAACsL,WAAW,GAAGtL,KAAK;MACxB,IAAI,CAACwD,kBAAkB,CAACuD,YAAY,CAAC,CAAC;IAC1C;IACAuE,WAAW,GAAG,IAAI;IAClB;IACA,IAAIT,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACU,SAAS;IACzB;IACA,IAAIV,QAAQA,CAAC7K,KAAK,EAAE;MAChB,IAAI,CAACwL,iBAAiB,CAACxL,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IAC9C;IACAuL,SAAS,GAAG,KAAK;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIE,YAAYA,CAAA,EAAG;MACf,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACR,QAAQ,CAACxK,QAAQ,CAAC,CAAC,GAAG,IAAI;IAC5D;IACA;IACAsF,iBAAiB,GAAG,uBAAuB;IAC3C;IACA+F,eAAe,GAAG,IAAI/Q,YAAY,CAAC,CAAC;IACpC4L,QAAQA,CAAA,EAAG;MACP,KAAK,CAACA,QAAQ,CAAC,CAAC;MAChB,IAAI,CAACrC,IAAI,GAAG,cAAc;IAC9B;IACA;IACAyH,MAAMA,CAAA,EAAG;MACL,IAAI,CAACH,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC7C;IACA;IACAI,QAAQA,CAAA,EAAG;MACP,IAAI,CAACJ,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IAC9C;IACA;IACAK,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACL,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5C;IACA;IACAM,cAAcA,CAAChB,WAAW,GAAG,KAAK,EAAE;MAChC,IAAI,CAACU,iBAAiB,CAAC,CAAC,IAAI,CAACX,QAAQ,EAAEC,WAAW,EAAE,IAAI,CAAC;MACzD,OAAO,IAAI,CAACD,QAAQ;IACxB;IACA9J,+BAA+BA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAE;QAChB;QACA;QACA;QACA,IAAI,CAACa,KAAK,CAAC,CAAC;QACZ,IAAI,IAAI,CAAC0K,UAAU,EAAE;UACjB,IAAI,CAACS,cAAc,CAAC,IAAI,CAAC;QAC7B;MACJ;IACJ;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACnG,WAAW,EAAE;QAClB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,CAAC,IAAI,CAACuF,qCAAqC,IAAI,IAAI,CAACD,iBAAiB;IAChF;IACAM,iBAAiBA,CAACQ,UAAU,EAAElB,WAAW,EAAEmB,SAAS,EAAE;MAClD,IAAID,UAAU,KAAK,IAAI,CAACnB,QAAQ,EAAE;QAC9B,IAAI,CAACU,SAAS,GAAGS,UAAU;QAC3B,IAAIC,SAAS,EAAE;UACX,IAAI,CAACP,eAAe,CAACpE,IAAI,CAAC;YACtBsD,MAAM,EAAE,IAAI;YACZE,WAAW;YACXD,QAAQ,EAAE,IAAI,CAACA;UACnB,CAAC,CAAC;QACN;QACA,IAAI,CAACrH,kBAAkB,CAACuD,YAAY,CAAC,CAAC;MAC1C;IACJ;IACA,OAAO5F,IAAI;MAAA,IAAA+K,0BAAA;MAAA,gBAAAC,sBAAA9K,iBAAA;QAAA,QAAA6K,0BAAA,KAAAA,0BAAA,GA3jB8EnS,EAAE,CAAAgJ,qBAAA,CA2jBQgI,aAAa,IAAA1J,iBAAA,IAAb0J,aAAa;MAAA;IAAA;IAChH,OAAOpC,IAAI,kBA5jB8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA4jBJuJ,aAAa;MAAAtJ,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAwK,2BAAA9O,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5jBXvD,EAAE,CAAA2P,aAAA,OAAAnM,GAAA,CAAAmH,EA4jBQ,CAAC;UA5jBX3K,EAAE,CAAAmI,WAAA,aA4jBJ,IAAI,gBAAJ,IAAI,sBAAJ,IAAI,UAAA3E,GAAA,CAAA2G,IAAA;UA5jBFnK,EAAE,CAAAoI,WAAA,wBAAA5E,GAAA,CAAA0G,YA4jBQ,CAAC,gCAAA1G,GAAA,CAAA0G,YAAD,CAAC,oCAAA1G,GAAA,CAAA0G,YAAD,CAAC,0BAAA1G,GAAA,CAAAsN,QAAD,CAAC,0BAAAtN,GAAA,CAAA2N,iBAAD,CAAC,0BAAA3N,GAAA,CAAAuC,QAAD,CAAC,6BAAAvC,GAAA,CAAAqI,WAAD,CAAC,iCAAArI,GAAA,CAAAuC,QAAD,CAAC,iCAAAvC,GAAA,CAAAsN,QAAD,CAAC,mCAAAtN,GAAA,CAAAzB,mBAAD,CAAC,6CAAbyB,GAAA,CAAAkK,gBAAA,CAAiB,CAAL,CAAC,0CAAAlK,GAAA,CAAAqI,WAAD,CAAC,6CAAbrI,GAAA,CAAAwO,kBAAA,CAAmB,CAAP,CAAC,oCAAAxO,GAAA,CAAAqI,WAAD,CAAC,6BAAArI,GAAA,CAAAgI,WAAD,CAAC,oCAAbhI,GAAA,CAAAkK,gBAAA,CAAiB,CAAL,CAAC;QAAA;MAAA;MAAArF,MAAA;QAAAiJ,UAAA,kCAAyKlR,gBAAgB;QAAA0Q,QAAA,8BAAsC1Q,gBAAgB;MAAA;MAAAyP,OAAA;QAAA8B,eAAA;MAAA;MAAAlJ,QAAA,GA5jB1PzI,EAAE,CAAA0I,kBAAA,CA4jB82C,CACj8C;QAAEC,OAAO,EAAEa,OAAO;QAAEZ,WAAW,EAAEoI;MAAc,CAAC,EAChD;QAAErI,OAAO,EAAElD,QAAQ;QAAEmD,WAAW,EAAEoI;MAAc,CAAC,CACpD,GA/jBoFhR,EAAE,CAAAiJ,0BAAA;MAAA8G,kBAAA,EAAA1M,GAAA;MAAA2M,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmC,uBAAA/O,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAqQ,eAAA,CAAAjN,GAAA;UAAFpD,EAAE,CAAA+D,SAAA,aA+jBa,CAAC;UA/jBhB/D,EAAE,CAAAyD,cAAA,aA+jB4F,CAAC,eAA+N,CAAC;UA/jB/TzD,EAAE,CAAAyE,mBAAA,IAAAZ,oCAAA,iBA+jB8V,CAAC;UA/jBjW7D,EAAE,CAAAyD,cAAA,aA+jB2gC,CAAC;UA/jB9gCzD,EAAE,CAAA0D,YAAA,EA+jB4iC,CAAC;UA/jB/iC1D,EAAE,CAAA+D,SAAA,aA+jBooC,CAAC;UA/jBvoC/D,EAAE,CAAA2D,YAAA,CA+jBipC,CAAC,CAAY,CAAC,CAAQ,CAAC;UA/jB1qC3D,EAAE,CAAAyE,mBAAA,IAAAT,oCAAA,iBA+jBqsC,CAAC;UA/jBxsChE,EAAE,CAAAyD,cAAA,aA+jB88C,CAAC;UA/jBj9CzD,EAAE,CAAAuS,MAAA,EA+jBi+C,CAAC;UA/jBp+CvS,EAAE,CAAA2D,YAAA,CA+jBw+C,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA/jB3+CvD,EAAE,CAAAsQ,SAAA,EA+jBkK,CAAC;UA/jBrKtQ,EAAE,CAAAuQ,UAAA,gCA+jBkK,CAAC;UA/jBrKvQ,EAAE,CAAAmI,WAAA,kBAAA3E,GAAA,CAAAkO,YAAA,gBAAAlO,GAAA,CAAAqH,SAAA,sBAAArH,GAAA,CAAAuH,kBAAA;UAAF/K,EAAE,CAAAsQ,SAAA,CA+jB47B,CAAC;UA/jB/7BtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAwO,kBAAA,WA+jB47B,CAAC;UA/jB/7BhS,EAAE,CAAAsQ,SAAA,EA+jB04C,CAAC;UA/jB74CtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAkK,gBAAA,WA+jB04C,CAAC;UA/jB74C1N,EAAE,CAAAsQ,SAAA,CA+jB68C,CAAC;UA/jBh9CtQ,EAAE,CAAAuQ,UAAA,OAAA/M,GAAA,CAAAuH,kBA+jB68C,CAAC;UA/jBh9C/K,EAAE,CAAAsQ,SAAA,CA+jBi+C,CAAC;UA/jBp+CtQ,EAAE,CAAAwS,iBAAA,CAAAhP,GAAA,CAAAsH,eA+jBi+C,CAAC;QAAA;MAAA;MAAA0F,YAAA,GAAugiB9K,aAAa;MAAA+K,MAAA,GAAAxM,GAAA;MAAAyM,aAAA;MAAAC,eAAA;IAAA;EACrllB;EAAC,OA/GKK,aAAa;AAAA;AAgHnB;EAAA,QAAA1I,SAAA,oBAAAA,SAAA;AAAA;;AA2CA;AACA;AACA;AACA;AAHA,IAIMmK,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB9M,WAAW,GAAGzF,MAAM,CAACC,UAAU,CAAC;IAChC4J,SAAS,GAAG7J,MAAM,CAACS,QAAQ,CAAC;IAC5B4F,WAAWA,CAAA,EAAG,CAAE;IAChBmM,UAAUA,CAACC,YAAY,EAAE;MACrB,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAChM,KAAK,CAAC,CAAC;MAC/B,IAAI,CAACiM,QAAQ,CAACF,YAAY,CAAC;IAC/B;IACAC,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAACjN,WAAW,CAACc,aAAa;IACzC;IACAoM,QAAQA,CAAC5M,KAAK,EAAE;MACZ,IAAI,CAAC2M,gBAAgB,CAAC,CAAC,CAACxH,WAAW,GAAGnF,KAAK;MAC3C,IAAI,CAAC6M,uBAAuB,CAAC,CAAC;IAClC;IACAC,QAAQA,CAAA,EAAG;MACP,OAAO,IAAI,CAACH,gBAAgB,CAAC,CAAC,CAACxH,WAAW,IAAI,EAAE;IACpD;IACA0H,uBAAuBA,CAAA,EAAG;MACtB,MAAME,KAAK,GAAG,IAAI,CAACjJ,SAAS,CAACkJ,WAAW,CAAC,CAAC;MAC1CD,KAAK,CAACE,kBAAkB,CAAC,IAAI,CAACN,gBAAgB,CAAC,CAAC,CAAC;MACjDI,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC;MACrB,MAAMC,GAAG,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;MACjCF,GAAG,CAACG,eAAe,CAAC,CAAC;MACrBH,GAAG,CAACI,QAAQ,CAACR,KAAK,CAAC;IACvB;IACA,OAAO5L,IAAI,YAAAqM,yBAAAnM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmL,gBAAgB;IAAA;IACnH,OAAOlL,IAAI,kBA3oB8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EA2oBJgL,gBAAgB;MAAA/K,SAAA;MAAAC,SAAA,WAAwF,SAAS,cAAc,IAAI,qBAAqB,MAAM;IAAA;EACzP;EAAC,OA5BK8K,gBAAgB;AAAA;AA6BtB;EAAA,QAAAnK,SAAA,oBAAAA,SAAA;AAAA;;AAaA;AACA;AACA;AACA;AAHA,IAIMoL,UAAU;EAAhB,MAAMA,UAAU,SAASlK,OAAO,CAAC;IAC7BoC,iBAAiB,GAAG,oBAAoB;IACxC;AACJ;AACA;AACA;AACA;IACI+H,iBAAiB,GAAG,KAAK;IACzBC,QAAQ,GAAG,KAAK;IAChB;IACAC,MAAM,GAAG,IAAIjT,YAAY,CAAC,CAAC;IAC3B;IACAkT,gBAAgB;IAChB;IACAjP,gBAAgB;IAChBsC,UAAU,GAAG,KAAK;IAClBZ,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,IAAI,CAAC4D,IAAI,GAAG,KAAK;MACjB,IAAI,CAACF,OAAO,CAAC8J,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACyJ,SAAS,CAAC,CAAC,CAACoB,SAAS,CAAC,MAAM;QACzD,IAAI,IAAI,CAAC5F,UAAU,IAAI,CAAC,IAAI,CAACwM,iBAAiB,EAAE;UAC5C,IAAI,CAACK,aAAa,CAAC,CAAC;QACxB;MACJ,CAAC,CAAC;IACN;IACAtG,gBAAgBA,CAAA,EAAG;MACf;MACA,OAAO,CAAC,IAAI,CAACvG,UAAU,IAAI,KAAK,CAACuG,gBAAgB,CAAC,CAAC;IACvD;IACA;IACAuG,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAAC9M,UAAU,IAAI,CAAC,IAAI,CAACpB,QAAQ,EAAE;QACpC,IAAI,CAACa,KAAK,CAAC,CAAC;MAChB;IACJ;IACAK,cAAcA,CAACH,KAAK,EAAE;MAClB,IAAIA,KAAK,CAACI,OAAO,KAAK5H,KAAK,IAAI,CAAC,IAAI,CAACyG,QAAQ,EAAE;QAC3C,IAAI,IAAI,CAACoB,UAAU,EAAE;UACjBL,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAI,CAACiN,aAAa,CAAC,CAAC;QACxB,CAAC,MACI,IAAI,IAAI,CAACJ,QAAQ,EAAE;UACpB,IAAI,CAACM,aAAa,CAACpN,KAAK,CAAC;QAC7B;MACJ,CAAC,MACI,IAAI,IAAI,CAACK,UAAU,EAAE;QACtB;QACAL,KAAK,CAACqC,eAAe,CAAC,CAAC;MAC3B,CAAC,MACI;QACD,KAAK,CAAClC,cAAc,CAACH,KAAK,CAAC;MAC/B;IACJ;IACAqN,kBAAkBA,CAACrN,KAAK,EAAE;MACtB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,IAAI,CAAC6N,QAAQ,EAAE;QACjC,IAAI,CAACM,aAAa,CAACpN,KAAK,CAAC;MAC7B;IACJ;IACAoN,aAAaA,CAACpN,KAAK,EAAE;MACjB,IAAI,CAAC,IAAI,CAACkF,aAAa,IAClB,IAAI,CAACD,UAAU,IAAI,IAAI,CAAC6B,gBAAgB,CAAC9G,KAAK,CAAC+G,MAAM,CAAC,KAAK,IAAI,CAAC9B,UAAW,EAAE;QAC9E;MACJ;MACA;MACA,MAAM9F,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,IAAI,CAACkB,UAAU,GAAG,IAAI,CAACwM,iBAAiB,GAAG,IAAI;MAC/C;MACAtS,eAAe,CAAC,MAAM;QAClB,IAAI,CAAC+S,aAAa,CAAC,CAAC,CAAC1B,UAAU,CAACzM,KAAK,CAAC;QACtC,IAAI,CAAC0N,iBAAiB,GAAG,KAAK;MAClC,CAAC,EAAE;QAAEU,QAAQ,EAAE,IAAI,CAACnI;MAAU,CAAC,CAAC;IACpC;IACA8H,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC7M,UAAU,GAAG,IAAI,CAACwM,iBAAiB,GAAG,KAAK;MAChD,IAAI,CAACE,MAAM,CAACtG,IAAI,CAAC;QAAEC,IAAI,EAAE,IAAI;QAAEvH,KAAK,EAAE,IAAI,CAACmO,aAAa,CAAC,CAAC,CAACrB,QAAQ,CAAC;MAAE,CAAC,CAAC;MACxE;MACA;MACA,IAAI,IAAI,CAAChJ,SAAS,CAACuK,aAAa,KAAK,IAAI,CAACF,aAAa,CAAC,CAAC,CAACxB,gBAAgB,CAAC,CAAC,IACxE,IAAI,CAAC7I,SAAS,CAACuK,aAAa,KAAK,IAAI,CAACvK,SAAS,CAACwK,IAAI,EAAE;QACtD,IAAI,CAACvI,aAAa,CAACpF,KAAK,CAAC,CAAC;MAC9B;IACJ;IACA2F,iBAAiBA,CAAA,EAAG;MAChB,OAAO,KAAK,CAACA,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACpF,UAAU;IACvD;IACA;AACJ;AACA;AACA;IACIiN,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACvP,gBAAgB,IAAI,IAAI,CAACiP,gBAAgB;IACzD;IACA,OAAO1M,IAAI,YAAAoN,mBAAAlN,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoM,UAAU;IAAA;IAC7G,OAAO9E,IAAI,kBA3vB8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA2vBJiM,UAAU;MAAAhM,SAAA;MAAAoH,cAAA,WAAA2F,0BAAAlR,EAAA,EAAAC,GAAA,EAAAwL,QAAA;QAAA,IAAAzL,EAAA;UA3vBRvD,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA8vBdyD,gBAAgB;QAAA;QAAA,IAAAlP,EAAA;UAAA,IAAA2L,EAAA;UA9vBJlP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAqB,gBAAA,GAAAqK,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAoF,iBAAAnR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAwP,WAAA,CA8vBoGiD,gBAAgB;QAAA;QAAA,IAAAlP,EAAA;UAAA,IAAA2L,EAAA;UA9vBtHlP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAsQ,gBAAA,GAAA5E,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA1H,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA8M,wBAAApR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAA+H,UAAA,mBAAA6M,oCAAA;YAAA,OA2vBJpR,GAAA,CAAAyQ,YAAA,CAAa,CAAC;UAAA,CAAL,CAAC,sBAAAY,uCAAA5M,MAAA;YAAA,OAAVzE,GAAA,CAAA2Q,kBAAA,CAAAlM,MAAyB,CAAC;UAAA,CAAjB,CAAC;QAAA;QAAA,IAAA1E,EAAA;UA3vBRvD,EAAE,CAAA2P,aAAA,OAAAnM,GAAA,CAAAmH,EA2vBK,CAAC;UA3vBR3K,EAAE,CAAAmI,WAAA,aAAA3E,GAAA,CAAAuC,QAAA,GA2vBO,IAAI,IAAI,CAAC,gBAApB,IAAI,sBAAJ,IAAI,UAAAvC,GAAA,CAAA2G,IAAA;UA3vBFnK,EAAE,CAAAoI,WAAA,6BAAA5E,GAAA,CAAAqI,WA2vBK,CAAC,0BAAArI,GAAA,CAAAuC,QAAD,CAAC,yBAAAvC,GAAA,CAAA2D,UAAD,CAAC,0BAAA3D,GAAA,CAAAoQ,QAAD,CAAC,iCAAApQ,GAAA,CAAAuC,QAAD,CAAC,6CAAVvC,GAAA,CAAAkK,gBAAA,CAAiB,CAAR,CAAC,6CAAAlK,GAAA,CAAAqI,WAAD,CAAC,0CAAArI,GAAA,CAAAqI,WAAD,CAAC,oCAAArI,GAAA,CAAAqI,WAAD,CAAC,6BAAArI,GAAA,CAAAgI,WAAD,CAAC,oCAAVhI,GAAA,CAAAkK,gBAAA,CAAiB,CAAR,CAAC;QAAA;MAAA;MAAArF,MAAA;QAAAuL,QAAA;MAAA;MAAA/D,OAAA;QAAAgE,MAAA;MAAA;MAAApL,QAAA,GA3vBRzI,EAAE,CAAA0I,kBAAA,CA2vBwkC,CAC3pC;QAAEC,OAAO,EAAEa,OAAO;QAAEZ,WAAW,EAAE8K;MAAW,CAAC,EAC7C;QAAE/K,OAAO,EAAElD,QAAQ;QAAEmD,WAAW,EAAE8K;MAAW,CAAC,CACjD,GA9vBoF1T,EAAE,CAAAiJ,0BAAA;MAAA8G,kBAAA,EAAA5L,GAAA;MAAA6L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2E,oBAAAvR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAqQ,eAAA,CAAAnM,GAAA;UAAFlE,EAAE,CAAAyE,mBAAA,IAAAL,iCAAA,iBA8vB8M,CAAC;UA9vBjNpE,EAAE,CAAAyD,cAAA,aA8vBif,CAAC;UA9vBpfzD,EAAE,CAAAyE,mBAAA,IAAAJ,iCAAA,iBA8vBwgB,CAAC;UA9vB3gBrE,EAAE,CAAAyD,cAAA,aA8vB6vB,CAAC;UA9vBhwBzD,EAAE,CAAAyE,mBAAA,IAAAD,iCAAA,MA8vBqxB,CAAC,IAAAM,iCAAA,MAAiL,CAAC;UA9vB18B9E,EAAE,CAAA+D,SAAA,aA8vB4lC,CAAC;UA9vB/lC/D,EAAE,CAAA2D,YAAA,CA8vBumC,CAAC,CAAQ,CAAC;UA9vBnnC3D,EAAE,CAAAyE,mBAAA,IAAAM,iCAAA,iBA8vB8oC,CAAC;UA9vBjpC/E,EAAE,CAAAyD,cAAA,aA8vBm7C,CAAC;UA9vBt7CzD,EAAE,CAAAuS,MAAA,EA8vBs8C,CAAC;UA9vBz8CvS,EAAE,CAAA2D,YAAA,CA8vB68C,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA9vBh9CvD,EAAE,CAAA4E,aAAA,EAAApB,GAAA,CAAA2D,UAAA,SA8vBuQ,CAAC;UA9vB1QnH,EAAE,CAAAsQ,SAAA,CA8vBuZ,CAAC;UA9vB1ZtQ,EAAE,CAAAuQ,UAAA,aAAA/M,GAAA,CAAAuC,QA8vBuZ,CAAC;UA9vB1Z/F,EAAE,CAAAmI,WAAA,eAAA3E,GAAA,CAAAqH,SAAA,sBAAArH,GAAA,CAAAuH,kBAAA;UAAF/K,EAAE,CAAAsQ,SAAA,CA8vB8qB,CAAC;UA9vBjrBtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAqI,WAAA,SA8vB8qB,CAAC;UA9vBjrB7L,EAAE,CAAAsQ,SAAA,EA8vB++B,CAAC;UA9vBl/BtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAA2D,UAAA,QA8vB++B,CAAC;UA9vBl/BnH,EAAE,CAAAsQ,SAAA,EA8vB+2C,CAAC;UA9vBl3CtQ,EAAE,CAAA4E,aAAA,CAAApB,GAAA,CAAAkK,gBAAA,WA8vB+2C,CAAC;UA9vBl3C1N,EAAE,CAAAsQ,SAAA,CA8vBk7C,CAAC;UA9vBr7CtQ,EAAE,CAAAuQ,UAAA,OAAA/M,GAAA,CAAAuH,kBA8vBk7C,CAAC;UA9vBr7C/K,EAAE,CAAAsQ,SAAA,CA8vBs8C,CAAC;UA9vBz8CtQ,EAAE,CAAAwS,iBAAA,CAAAhP,GAAA,CAAAsH,eA8vBs8C,CAAC;QAAA;MAAA;MAAA0F,YAAA,GAAugiB9K,aAAa,EAA0I+M,gBAAgB;MAAAhC,MAAA,GAAAxM,GAAA;MAAAyM,aAAA;MAAAC,eAAA;IAAA;EACptlB;EAAC,OAjGK+C,UAAU;AAAA;AAkGhB;EAAA,QAAApL,SAAA,oBAAAA,SAAA;AAAA;;AAwCA;AACA;AACA;AACA;AACA;AAJA,IAKMyM,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACbpP,WAAW,GAAGzF,MAAM,CAACC,UAAU,CAAC;IAChCsJ,kBAAkB,GAAGvJ,MAAM,CAACM,iBAAiB,CAAC;IAC9CwU,IAAI,GAAG9U,MAAM,CAACmC,cAAc,EAAE;MAAEyH,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjD;IACAmL,8BAA8B,GAAG,IAAI;IACrC;IACAC,WAAW;IACX;IACAC,UAAU,GAAG,IAAI1T,OAAO,CAAC,CAAC;IAC1B;IACA2T,YAAY,GAAG,cAAc;IAC7B;IACA,IAAIC,gBAAgBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACC,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAACxD,QAAQ,CAAC;IACrD;IACA;IACA,IAAIuL,oBAAoBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACD,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAAC7B,SAAS,CAAC;IACtD;IACA;IACA,IAAI6J,kBAAkBA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACF,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAAC9B,OAAO,CAAC;IACpD;IACA;IACA,IAAI3F,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACwP,eAAe,CAAC,CAAC;IAC1B;IACAzP,SAAS,GAAG,KAAK;IACjB;IACA,IAAI0P,KAAKA,CAAA,EAAG;MACR,OAAO,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,KAAK,CAAC;IACnD;IACA;IACA,IAAIzL,IAAIA,CAAA,EAAG;MACP,IAAI,IAAI,CAAC0L,aAAa,EAAE;QACpB,OAAO,IAAI,CAACA,aAAa;MAC7B;MACA,OAAO,IAAI,CAACH,KAAK,GAAG,IAAI,GAAG,IAAI,CAACN,YAAY;IAChD;IACA;IACAlP,QAAQ,GAAG,CAAC;IACZ,IAAIiE,IAAIA,CAAClE,KAAK,EAAE;MACZ,IAAI,CAAC4P,aAAa,GAAG5P,KAAK;IAC9B;IACA4P,aAAa,GAAG,IAAI;IACpB;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC;IACA;IACAJ,MAAM;IACN;IACAK,YAAY,GAAG,IAAI1U,SAAS,CAAC,CAAC;IAC9BiF,WAAWA,CAAA,EAAG,CAAE;IAChBoG,eAAeA,CAAA,EAAG;MACd,IAAI,CAACsJ,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACrC;IACAhJ,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC+H,WAAW,EAAEkB,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACjB,UAAU,CAAC3G,IAAI,CAAC,CAAC;MACtB,IAAI,CAAC2G,UAAU,CAAC1H,QAAQ,CAAC,CAAC;IAC9B;IACA;IACAsI,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACU,IAAI,CAAC7I,IAAI,IAAIA,IAAI,CAAC9C,SAAS,CAAC,CAAC,CAAC;IACpE;IACA;IACA+K,eAAeA,CAAA,EAAG;MACd,IAAI,CAACE,MAAM,EAAEW,OAAO,CAAC9I,IAAI,IAAI;QACzBA,IAAI,CAACxC,iBAAiB,GAAG,IAAI,CAAChF,SAAS;QACvCwH,IAAI,CAAC/D,kBAAkB,CAACuD,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACApG,KAAKA,CAAA,EAAG,CAAE;IACV;IACAK,cAAcA,CAACH,KAAK,EAAE;MAClB,IAAI,IAAI,CAACyP,mBAAmB,CAACzP,KAAK,CAAC,EAAE;QACjC,IAAI,CAACoO,WAAW,CAACsB,SAAS,CAAC1P,KAAK,CAAC;MACrC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACI2P,aAAaA,CAACC,KAAK,EAAE;MACjB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACf,MAAM,CAACC,MAAM;IACnD;IACA;AACJ;AACA;AACA;AACA;IACIe,iBAAiBA,CAAA,EAAG;MAChB,MAAMC,QAAQ,GAAG,IAAI,CAACjR,WAAW,CAACc,aAAa,CAACP,QAAQ;MACxD,IAAI0Q,QAAQ,KAAK,CAAC,CAAC,EAAE;QACjB;QACA;QACA;QACA,IAAI,CAACjR,WAAW,CAACc,aAAa,CAACP,QAAQ,GAAG,CAAC,CAAC;QAC5C;QACA;QACAuI,UAAU,CAAC,MAAO,IAAI,CAAC9I,WAAW,CAACc,aAAa,CAACP,QAAQ,GAAG0Q,QAAS,CAAC;MAC1E;IACJ;IACA;AACJ;AACA;AACA;IACItB,cAAcA,CAACuB,eAAe,EAAE;MAC5B,OAAO,IAAI,CAAClB,MAAM,CAAC7I,OAAO,CAACiH,IAAI,CAAC5R,SAAS,CAAC,IAAI,CAAC,EAAEC,SAAS,CAAC,MAAMV,KAAK,CAAC,GAAG,IAAI,CAACiU,MAAM,CAACmB,GAAG,CAACD,eAAe,CAAC,CAAC,CAAC,CAAC;IACjH;IACA;IACAN,mBAAmBA,CAACzP,KAAK,EAAE;MACvB,IAAIiQ,cAAc,GAAGjQ,KAAK,CAAC+G,MAAM;MACjC,OAAOkJ,cAAc,IAAIA,cAAc,KAAK,IAAI,CAACpR,WAAW,CAACc,aAAa,EAAE;QACxE,IAAIsQ,cAAc,CAACC,SAAS,CAAC9I,QAAQ,CAAC,cAAc,CAAC,EAAE;UACnD,OAAO,IAAI;QACf;QACA6I,cAAc,GAAGA,cAAc,CAACE,aAAa;MACjD;MACA,OAAO,KAAK;IAChB;IACA;IACAhB,qBAAqBA,CAAA,EAAG;MACpB;MACA;MACA;MACA,IAAI,CAACN,MAAM,CAAC7I,OAAO,CAACiH,IAAI,CAAC5R,SAAS,CAAC,IAAI,CAACwT,MAAM,CAAC,CAAC,CAAC5I,SAAS,CAAEmK,KAAK,IAAK;QAClE,MAAMC,OAAO,GAAG,EAAE;QAClBD,KAAK,CAACZ,OAAO,CAAC9I,IAAI,IAAIA,IAAI,CAACM,WAAW,CAAC,CAAC,CAACwI,OAAO,CAACtI,MAAM,IAAImJ,OAAO,CAAC/I,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC;QACjF,IAAI,CAACgI,YAAY,CAACoB,KAAK,CAACD,OAAO,CAAC;QAChC,IAAI,CAACnB,YAAY,CAACqB,eAAe,CAAC,CAAC;MACvC,CAAC,CAAC;MACF,IAAI,CAACnC,WAAW,GAAG,IAAI7V,eAAe,CAAC,IAAI,CAAC2W,YAAY,CAAC,CACpDsB,uBAAuB,CAAC,CAAC,CACzBC,yBAAyB,CAAC,IAAI,CAACvC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC/O,KAAK,GAAG,KAAK,CAAC,CAC9DuR,cAAc,CAAC,CAAC,CAChBC,aAAa,CAACzJ,MAAM,IAAI,IAAI,CAAC0J,cAAc,CAAC1J,MAAM,CAAC,CAAC;MACzD;MACA;MACA,IAAI,CAACqH,gBAAgB,CAACtB,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAC,CAAC;QAAES;MAAK,CAAC,KAAK;QAC3E,MAAMQ,MAAM,GAAGR,IAAI,CAACI,gBAAgB,CAAC+J,QAAQ,CAACrD,aAAa,CAAC;QAC5D,IAAItG,MAAM,EAAE;UACR,IAAI,CAACkH,WAAW,CAAC0C,gBAAgB,CAAC5J,MAAM,CAAC;QAC7C;MACJ,CAAC,CAAC;MACF,IAAI,CAACgH,IAAI,EAAE6C,MAAM,CACZ9D,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAChCpI,SAAS,CAAC+K,SAAS,IAAI,IAAI,CAAC5C,WAAW,CAACqC,yBAAyB,CAACO,SAAS,CAAC,CAAC;IACtF;IACA;AACJ;AACA;AACA;IACIJ,cAAcA,CAAC1J,MAAM,EAAE;MACnB;MACA;MACA,OAAO,CAACA,MAAM,CAACnI,aAAa,IAAImI,MAAM,CAACjI,QAAQ;IACnD;IACA;IACAmQ,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACP,MAAM,CAAC7I,OAAO,CAACiH,IAAI,CAAC5R,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAC,MAAM;QAClF,IAAI,IAAI,CAAChH,QAAQ,EAAE;UACf;UACA;UACAgS,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACxC,eAAe,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,CAACyC,2BAA2B,CAAC,CAAC;MACtC,CAAC,CAAC;IACN;IACA;IACA/B,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAACZ,oBAAoB,CAACxB,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAEjG,KAAK,IAAK;QAC5E,MAAMqR,SAAS,GAAG,IAAI,CAACxC,MAAM,CAACyC,OAAO,CAAC,CAAC;QACvC,MAAMC,SAAS,GAAGF,SAAS,CAACG,OAAO,CAACxR,KAAK,CAAC0G,IAAI,CAAC;QAC/C;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACiJ,aAAa,CAAC4B,SAAS,CAAC,IAAIvR,KAAK,CAAC0G,IAAI,CAAC9C,SAAS,CAAC,CAAC,EAAE;UACzD,IAAI,CAACuK,8BAA8B,GAAGoD,SAAS;QACnD;MACJ,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIH,2BAA2BA,CAAA,EAAG;MAC1B,IAAI,IAAI,CAACjD,8BAA8B,IAAI,IAAI,EAAE;QAC7C;MACJ;MACA,IAAI,IAAI,CAACU,MAAM,CAACC,MAAM,EAAE;QACpB,MAAM2C,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACxD,8BAA8B,EAAE,IAAI,CAACU,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;QACtF,MAAM8C,WAAW,GAAG,IAAI,CAAC/C,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACG,QAAQ,CAAC;QACnD,IAAIG,WAAW,CAAC3S,QAAQ,EAAE;UACtB;UACA,IAAI,IAAI,CAAC4P,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAChP,KAAK,CAAC,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACsO,WAAW,CAACyD,qBAAqB,CAAC,CAAC;UAC5C;QACJ,CAAC,MACI;UACDD,WAAW,CAAC9R,KAAK,CAAC,CAAC;QACvB;MACJ,CAAC,MACI;QACD,IAAI,CAACA,KAAK,CAAC,CAAC;MAChB;MACA,IAAI,CAACqO,8BAA8B,GAAG,IAAI;IAC9C;IACA,OAAO7N,IAAI,YAAAwR,mBAAAtR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyN,UAAU;IAAA;IAC7G,OAAOnG,IAAI,kBA9gC8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA8gCJsN,UAAU;MAAArN,SAAA;MAAAoH,cAAA,WAAA+J,0BAAAtV,EAAA,EAAAC,GAAA,EAAAwL,QAAA;QAAA,IAAAzL,EAAA;UA9gCRvD,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA8gCgbxF,OAAO;QAAA;QAAA,IAAAjG,EAAA;UAAA,IAAA2L,EAAA;UA9gCzblP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAmS,MAAA,GAAAzG,EAAA;QAAA;MAAA;MAAAvH,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAiR,wBAAAvV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAA+H,UAAA,qBAAAgR,sCAAA9Q,MAAA;YAAA,OA8gCJzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;UAAA,CAAb,CAAC;QAAA;QAAA,IAAA1E,EAAA;UA9gCRvD,EAAE,CAAAmI,WAAA,SAAA3E,GAAA,CAAA2G,IAAA;QAAA;MAAA;MAAA9B,MAAA;QAAAtC,QAAA,8BA8gCmG3F,gBAAgB;QAAA+J,IAAA;QAAAjE,QAAA,8BAAqDD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG5F,eAAe,CAAC4F,KAAK,CAAE;MAAA;MAAA8J,kBAAA,EAAA/K,GAAA;MAAAgL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6I,oBAAAzV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9gChOvD,EAAE,CAAAqQ,eAAA;UAAFrQ,EAAE,CAAAyD,cAAA,YA+gC7B,CAAC;UA/gC0BzD,EAAE,CAAA0D,YAAA,EAghCjE,CAAC;UAhhC8D1D,EAAE,CAAA2D,YAAA,CAihCtF,CAAC;QAAA;MAAA;MAAA8M,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAEV;EAAC,OAtOKoE,UAAU;AAAA;AAuOhB;EAAA,QAAAzM,SAAA,oBAAAA,SAAA;AAAA;;AA8BA;AACA,MAAM2Q,oBAAoB,CAAC;EACvBpI,MAAM;EACN5K,KAAK;EACLM,WAAWA,CACX;EACAsK,MAAM,EACN;EACA5K,KAAK,EAAE;IACH,IAAI,CAAC4K,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC5K,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiT,uCAAuC,GAAG;EAC5CvQ,OAAO,EAAErG,iBAAiB;EAC1BsG,WAAW,eAAErH,UAAU,CAAC,MAAM4X,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AAHA,IAIMD,cAAc;EAApB,MAAMA,cAAc,SAASpE,UAAU,CAAC;IACpC;AACJ;AACA;AACA;IACIsE,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB;AACJ;AACA;AACA;IACIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;IACrB;IACAlE,YAAY,GAAG,SAAS;IACxB;IACAnE,eAAe,GAAG/Q,MAAM,CAACgF,yBAAyB,EAAE;MAAE4E,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE;IACA,IAAIyP,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACtT,KAAK,EAAE;MAChB,IAAI,CAACuT,SAAS,GAAGvT,KAAK;MACtB,IAAI,CAACwT,sBAAsB,CAAC,CAAC;IACjC;IACAD,SAAS,GAAG,KAAK;IACjB;IACA,IAAI1I,QAAQA,CAAA,EAAG;MACX,MAAM4I,aAAa,GAAG,IAAI,CAAC/D,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACuB,MAAM,CAACnM,IAAI,IAAIA,IAAI,CAACsD,QAAQ,CAAC;MACzE,OAAO,IAAI,CAACyI,QAAQ,GAAGG,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC;IAC3D;IACA;IACAE,eAAe,GAAG,YAAY;IAC9B;AACJ;AACA;AACA;AACA;AACA;IACI,IAAItI,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,WAAW;IAC3B;IACA,IAAID,UAAUA,CAACrL,KAAK,EAAE;MAClB,IAAI,CAACsL,WAAW,GAAGtL,KAAK;MACxB,IAAI,CAACwT,sBAAsB,CAAC,CAAC;IACjC;IACAlI,WAAW,GAAG,IAAI;IAClB;AACJ;AACA;AACA;AACA;IACIsI,WAAW,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACnC;IACAC,QAAQ,GAAG,KAAK;IAChB;IACA,IAAI3I,4BAA4BA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAAC4I,6BAA6B;IAC7C;IACA,IAAI5I,4BAA4BA,CAACpL,KAAK,EAAE;MACpC,IAAI,CAACgU,6BAA6B,GAAGhU,KAAK;MAC1C,IAAI,CAACwT,sBAAsB,CAAC,CAAC;IACjC;IACAQ,6BAA6B,GAAG,IAAI,CAAChJ,eAAe,EAAEI,4BAA4B,IAAI,KAAK;IAC3F;IACA,IAAI6I,oBAAoBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAAC5E,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAACmE,eAAe,CAAC;IAC5D;IACA;IACA,IAAIwI,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC7E,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAACvD,OAAO,CAAC;IACpD;IACA;IACA,IAAIhE,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACiF,MAAM;IACtB;IACA,IAAIjF,KAAKA,CAACA,KAAK,EAAE;MACb,IAAI,IAAI,CAAC0P,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnC,IAAI,CAACwE,oBAAoB,CAACnU,KAAK,EAAE,KAAK,CAAC;MAC3C;MACA,IAAI,CAACiF,MAAM,GAAGjF,KAAK;IACvB;IACAiF,MAAM;IACN;IACA2M,MAAM,GAAG,IAAIjX,YAAY,CAAC,CAAC;IAC3B+U,MAAM,GAAGxK,SAAS;IAClB0B,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC8I,MAAM,CAAC7I,OAAO,CAACiH,IAAI,CAAC5R,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAC,MAAM;QAClF,IAAI,IAAI,CAAC9G,KAAK,KAAKkF,SAAS,EAAE;UAC1B4M,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzB,IAAI,CAACmC,oBAAoB,CAAC,IAAI,CAACnU,KAAK,EAAE,KAAK,CAAC;UAChD,CAAC,CAAC;QACN;QACA;QACA,IAAI,CAACwT,sBAAsB,CAAC,CAAC;MACjC,CAAC,CAAC;MACF,IAAI,CAACU,eAAe,CAACpG,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAC,MAAM,IAAI,CAACsN,KAAK,CAAC,CAAC,CAAC;MACnF,IAAI,CAACH,oBAAoB,CAACnG,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAACjG,KAAK,IAAI;QAC1E,IAAI,CAAC,IAAI,CAACyS,QAAQ,EAAE;UAChB,IAAI,CAAC5D,MAAM,CAACW,OAAO,CAAC9I,IAAI,IAAI;YACxB,IAAIA,IAAI,KAAK1G,KAAK,CAAC+J,MAAM,EAAE;cACvBrD,IAAI,CAACiE,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC/C;UACJ,CAAC,CAAC;QACN;QACA,IAAI3K,KAAK,CAACiK,WAAW,EAAE;UACnB,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACI1T,KAAKA,CAAA,EAAG;MACJ,IAAI,IAAI,CAACb,QAAQ,EAAE;QACf;MACJ;MACA,MAAMwU,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACtD,IAAID,iBAAiB,IAAI,CAACA,iBAAiB,CAACxU,QAAQ,EAAE;QAClDwU,iBAAiB,CAAC3T,KAAK,CAAC,CAAC;MAC7B,CAAC,MACI,IAAI,IAAI,CAAC+O,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACV,WAAW,CAACuF,kBAAkB,CAAC,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAAC9U,WAAW,CAACc,aAAa,CAACG,KAAK,CAAC,CAAC;MAC1C;IACJ;IACA;AACJ;AACA;AACA;IACI8T,UAAUA,CAACzU,KAAK,EAAE;MACd,IAAIA,KAAK,IAAI,IAAI,EAAE;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK;MACtB,CAAC,MACI;QACD,IAAI,CAACA,KAAK,GAAGkF,SAAS;MAC1B;IACJ;IACA;AACJ;AACA;AACA;IACIwP,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACtB,SAAS,GAAGsB,EAAE;IACvB;IACA;AACJ;AACA;AACA;IACIC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAACvB,UAAU,GAAGuB,EAAE;IACxB;IACA;AACJ;AACA;AACA;IACIE,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAAChV,QAAQ,GAAGgV,UAAU;IAC9B;IACA;IACAX,oBAAoBA,CAACnU,KAAK,EAAE8K,WAAW,GAAG,IAAI,EAAE;MAC5C,IAAI,CAACiK,eAAe,CAAC,CAAC;MACtB,IAAIC,KAAK,CAACC,OAAO,CAACjV,KAAK,CAAC,EAAE;QACtBA,KAAK,CAACqQ,OAAO,CAAC6E,YAAY,IAAI,IAAI,CAACC,YAAY,CAACD,YAAY,EAAEpK,WAAW,CAAC,CAAC;MAC/E,CAAC,MACI;QACD,IAAI,CAACqK,YAAY,CAACnV,KAAK,EAAE8K,WAAW,CAAC;MACzC;IACJ;IACA;IACAsJ,KAAKA,CAAA,EAAG;MACJ,IAAI,CAAC,IAAI,CAACtU,QAAQ,EAAE;QAChB;QACA0I,UAAU,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAACqH,OAAO,EAAE;YACf,IAAI,CAACuF,cAAc,CAAC,CAAC;UACzB;QACJ,CAAC,CAAC;MACN;IACJ;IACAC,QAAQA,CAACxU,KAAK,EAAE;MACZ,IAAIA,KAAK,CAACI,OAAO,KAAKxH,GAAG,EAAE;QACvB,KAAK,CAACiX,iBAAiB,CAAC,CAAC;MAC7B;IACJ;IACA;IACA0E,cAAcA,CAAA,EAAG;MACb,IAAI,CAAChC,UAAU,CAAC,CAAC;MACjB,IAAI,CAAC5P,kBAAkB,CAACuD,YAAY,CAAC,CAAC;IAC1C;IACA;IACAsN,iBAAiBA,CAAA,EAAG;MAChB,IAAIiB,WAAW,GAAG,IAAI;MACtB,IAAIN,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAE;QAC9ByK,WAAW,GAAG,IAAI,CAACzK,QAAQ,CAACgG,GAAG,CAACtJ,IAAI,IAAIA,IAAI,CAACvH,KAAK,CAAC;MACvD,CAAC,MACI;QACDsV,WAAW,GAAG,IAAI,CAACzK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC7K,KAAK,GAAGkF,SAAS;MACjE;MACA,IAAI,CAACD,MAAM,GAAGqQ,WAAW;MACzB,IAAI,CAAC1D,MAAM,CAACtK,IAAI,CAAC,IAAI0L,oBAAoB,CAAC,IAAI,EAAEsC,WAAW,CAAC,CAAC;MAC7D,IAAI,CAACjC,SAAS,CAACiC,WAAW,CAAC;MAC3B,IAAI,CAAC9R,kBAAkB,CAACuD,YAAY,CAAC,CAAC;IAC1C;IACA;AACJ;AACA;AACA;IACIgO,eAAeA,CAACQ,IAAI,EAAE;MAClB,IAAI,CAAC7F,MAAM,CAACW,OAAO,CAAC9I,IAAI,IAAI;QACxB,IAAIA,IAAI,KAAKgO,IAAI,EAAE;UACfhO,IAAI,CAACqE,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;IACA;AACJ;AACA;AACA;IACIuJ,YAAYA,CAACnV,KAAK,EAAE8K,WAAW,EAAE;MAC7B,MAAM0K,iBAAiB,GAAG,IAAI,CAAC9F,MAAM,CAAC5H,IAAI,CAACP,IAAI,IAAI;QAC/C,OAAOA,IAAI,CAACvH,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC4T,WAAW,CAACrM,IAAI,CAACvH,KAAK,EAAEA,KAAK,CAAC;MACpE,CAAC,CAAC;MACF,IAAIwV,iBAAiB,EAAE;QACnB1K,WAAW,GAAG0K,iBAAiB,CAAC3J,oBAAoB,CAAC,CAAC,GAAG2J,iBAAiB,CAAC7J,MAAM,CAAC,CAAC;MACvF;MACA,OAAO6J,iBAAiB;IAC5B;IACA;IACAhC,sBAAsBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC9D,MAAM,EAAE;QACb;QACA;QACAoC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACtC,MAAM,CAACW,OAAO,CAAC9I,IAAI,IAAI;YACxBA,IAAI,CAAC2D,iBAAiB,GAAG,IAAI,CAACoI,QAAQ;YACtC/L,IAAI,CAAC0D,kBAAkB,GAAG,IAAI,CAACK,WAAW;YAC1C/D,IAAI,CAAC4D,qCAAqC,GAAG,IAAI,CAACC,4BAA4B;YAC9E7D,IAAI,CAAC/D,kBAAkB,CAACuD,YAAY,CAAC,CAAC;UAC1C,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ;IACA;IACAwN,qBAAqBA,CAAA,EAAG;MACpB,IAAIS,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAE;QAC9B,OAAO,IAAI,CAACA,QAAQ,CAAC8E,MAAM,GAAG,IAAI,CAAC9E,QAAQ,CAAC,CAAC,CAAC,GAAG3F,SAAS;MAC9D,CAAC,MACI;QACD,OAAO,IAAI,CAAC2F,QAAQ;MACxB;IACJ;IACA;AACJ;AACA;AACA;IACI4G,cAAcA,CAAC1J,MAAM,EAAE;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,CAACA,MAAM,CAACnI,aAAa;IAChC;IACA,OAAOuB,IAAI;MAAA,IAAAsU,2BAAA;MAAA,gBAAAC,uBAAArU,iBAAA;QAAA,QAAAoU,2BAAA,KAAAA,2BAAA,GA11C8E1b,EAAE,CAAAgJ,qBAAA,CA01CQmQ,cAAc,IAAA7R,iBAAA,IAAd6R,cAAc;MAAA;IAAA;IACjH,OAAOvK,IAAI,kBA31C8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA21CJ0R,cAAc;MAAAzR,SAAA;MAAAoH,cAAA,WAAA8M,8BAAArY,EAAA,EAAAC,GAAA,EAAAwL,QAAA;QAAA,IAAAzL,EAAA;UA31CZvD,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA21CslCgC,aAAa;QAAA;QAAA,IAAAzN,EAAA;UAAA,IAAA2L,EAAA;UA31CrmClP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAmS,MAAA,GAAAzG,EAAA;QAAA;MAAA;MAAAvH,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgU,4BAAAtY,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAA+H,UAAA,mBAAA+T,wCAAA;YAAA,OA21CJtY,GAAA,CAAAoD,KAAA,CAAM,CAAC;UAAA,CAAM,CAAC,kBAAAmV,uCAAA;YAAA,OAAdvY,GAAA,CAAA6W,KAAA,CAAM,CAAC;UAAA,CAAM,CAAC,qBAAA2B,0CAAA/T,MAAA;YAAA,OAAdzE,GAAA,CAAA8X,QAAA,CAAArT,MAAe,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAA1E,EAAA;UA31CZvD,EAAE,CAAA2P,aAAA,aAAAnM,GAAA,CAAAuC,QAAA,IAAAvC,GAAA,CAAAkS,KAAA,IA21CmB,CAAC,GAAAlS,GAAA,CAAA0C,QAAX,CAAC;UA31CZlG,EAAE,CAAAmI,WAAA,SAAA3E,GAAA,CAAA2G,IAAA,mBAAA3G,GAAA,CAAA2G,IAAA,GAAA3G,GAAA,CAAAwW,QAAA,GA21Cc,IAAI,mBAAtBxW,GAAA,CAAAuC,QAAA,CAAAO,QAAA,CAAkB,CAAC,0BAAA9C,GAAA,CAAA+V,QAAA,sBAAA/V,GAAA,CAAAoW,eAAA;UA31CjB5Z,EAAE,CAAAoI,WAAA,+BAAA5E,GAAA,CAAAuC,QA21CS,CAAC,+BAAAvC,GAAA,CAAAwW,QAAD,CAAC;QAAA;MAAA;MAAA3R,MAAA;QAAAkR,QAAA,8BAAiGnZ,gBAAgB;QAAAwZ,eAAA;QAAAtI,UAAA,kCAAsGlR,gBAAgB;QAAAyZ,WAAA;QAAAG,QAAA,8BAAkE5Z,gBAAgB;QAAAiR,4BAAA,sEAAkGjR,gBAAgB;QAAA6F,KAAA;MAAA;MAAA4J,OAAA;QAAAgI,MAAA;MAAA;MAAApP,QAAA,GA31CvbzI,EAAE,CAAA0I,kBAAA,CA21C4/B,CAACwQ,uCAAuC,CAAC,GA31CviClZ,EAAE,CAAAiJ,0BAAA;MAAA8G,kBAAA,EAAA/K,GAAA;MAAAgL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8L,wBAAA1Y,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAqQ,eAAA;UAAFrQ,EAAE,CAAAyD,cAAA,YA41C7B,CAAC;UA51C0BzD,EAAE,CAAA0D,YAAA,EA61CjE,CAAC;UA71C8D1D,EAAE,CAAA2D,YAAA,CA81CtF,CAAC;QAAA;MAAA;MAAA8M,MAAA,GAAAxL,GAAA;MAAAyL,aAAA;MAAAC,eAAA;IAAA;EAEV;EAAC,OAnRKwI,cAAc;AAAA;AAoRpB;EAAA,QAAA7Q,SAAA,oBAAAA,SAAA;AAAA;;AAkDA;AACA,MAAM4T,iBAAiB,CAAC;EACpBrL,MAAM;EACN5K,KAAK;EACLM,WAAWA,CACX;EACAsK,MAAM,EACN;EACA5K,KAAK,EAAE;IACH,IAAI,CAAC4K,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC5K,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AAHA,IAIMkW,WAAW;EAAjB,MAAMA,WAAW,SAASpH,UAAU,CAAC;IACjCqH,SAAS,GAAGlc,MAAM,CAACqC,SAAS,EAAE;MAAEuH,QAAQ,EAAE,IAAI;MAAEuS,IAAI,EAAE;IAAK,CAAC,CAAC;IAC7D;AACJ;AACA;AACA;IACIC,WAAW,GAAG,eAAe;IAC7B;IACAC,UAAU;IACVnH,YAAY,GAAG,MAAM;IACrBoH,kBAAkB;IAClB;AACJ;AACA;IACIC,mBAAmB,GAAG,EAAE;IACxB;AACJ;AACA;AACA;IACIpD,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB;AACJ;AACA;AACA;IACIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;IACrB;AACJ;AACA;AACA;IACI,IAAIvT,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACqW,SAAS,GAAG,CAAC,CAAC,IAAI,CAACA,SAAS,CAACrW,QAAQ,GAAG,IAAI,CAACC,SAAS;IACtE;IACA,IAAID,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACwP,eAAe,CAAC,CAAC;MACtB,IAAI,CAACiH,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACA;AACJ;AACA;AACA;IACI,IAAI7D,EAAEA,CAAA,EAAG;MACL,OAAO,IAAI,CAAC4R,UAAU,CAAC5R,EAAE;IAC7B;IACA;AACJ;AACA;AACA;IACI,IAAI+K,KAAKA,CAAA,EAAG;MACR,OAAQ,CAAC,CAAC,IAAI,CAAC6G,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC7G,KAAK,MAAM,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,KAAK,CAAC,CAAC;IACrG;IACA;AACJ;AACA;AACA;IACI,IAAI+G,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACI,WAAW,GAAG,IAAI,CAACC,YAAY;IAC5E;IACA,IAAID,WAAWA,CAAC1W,KAAK,EAAE;MACnB,IAAI,CAAC2W,YAAY,GAAG3W,KAAK;MACzB,IAAI,CAACyW,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACAoO,YAAY;IACZ;IACA,IAAI9G,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACyG,UAAU,CAACzG,OAAO,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;IAC5D;IACA;AACJ;AACA;AACA;IACI,IAAIiE,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC6C,SAAS,IAAI,IAAI,CAACT,SAAS,EAAEU,OAAO,EAAEC,YAAY,CAACva,UAAU,CAACwX,QAAQ,CAAC,IAAI,KAAK;IAChG;IACA,IAAIA,QAAQA,CAAC/T,KAAK,EAAE;MAChB,IAAI,CAAC4W,SAAS,GAAG5W,KAAK;MACtB,IAAI,CAACyW,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACAqO,SAAS;IACT;AACJ;AACA;AACA;IACI,IAAIG,gBAAgBA,CAAA,EAAG;MACnB,OAAO,CAAC,IAAI,CAACtH,KAAK,IAAI,IAAI,CAACI,OAAO;IACtC;IACA;AACJ;AACA;AACA;IACI,IAAI7P,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACiF,MAAM;IACtB;IACA,IAAIjF,KAAKA,CAACA,KAAK,EAAE;MACb,IAAI,CAACiF,MAAM,GAAGjF,KAAK;IACvB;IACAiF,MAAM,GAAG,EAAE;IACX;IACA,IAAI+R,iBAAiBA,CAAA,EAAG;MACpB,OAAO,IAAI,CAACT,kBAAkB,CAACU,OAAO;IAC1C;IACA,IAAID,iBAAiBA,CAAChX,KAAK,EAAE;MACzB,IAAI,CAACuW,kBAAkB,CAACU,OAAO,GAAGjX,KAAK;IAC3C;IACA;IACA,IAAIkU,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC7E,cAAc,CAAC9H,IAAI,IAAIA,IAAI,CAACvD,OAAO,CAAC;IACpD;IACA;IACA4N,MAAM,GAAG,IAAIjX,YAAY,CAAC,CAAC;IAC3B;AACJ;AACA;AACA;AACA;IACIuc,WAAW,GAAG,IAAIvc,YAAY,CAAC,CAAC;IAChC+U,MAAM,GAAGxK,SAAS;IAClB;AACJ;AACA;AACA;AACA;IACIuR,YAAY,GAAG,IAAIjb,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI2b,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACZ,kBAAkB,CAACY,UAAU;IAC7C;IACA,IAAIA,UAAUA,CAACnX,KAAK,EAAE;MAClB,IAAI,CAACuW,kBAAkB,CAACY,UAAU,GAAGnX,KAAK;IAC9C;IACAM,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAM8W,UAAU,GAAGnd,MAAM,CAACuC,MAAM,EAAE;QAAEqH,QAAQ,EAAE;MAAK,CAAC,CAAC;MACrD,MAAMwT,eAAe,GAAGpd,MAAM,CAACwC,kBAAkB,EAAE;QAAEoH,QAAQ,EAAE;MAAK,CAAC,CAAC;MACtE,MAAMyT,wBAAwB,GAAGrd,MAAM,CAAC0C,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACwZ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACoB,aAAa,GAAG,IAAI;MACvC;MACA,IAAI,CAAChB,kBAAkB,GAAG,IAAI3Z,kBAAkB,CAAC0a,wBAAwB,EAAE,IAAI,CAACnB,SAAS,EAAEkB,eAAe,EAAED,UAAU,EAAE,IAAI,CAACX,YAAY,CAAC;IAC9I;IACA7P,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACsN,eAAe,CAACpG,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAACpI,SAAS,CAAC,MAAM;QAClE,IAAI,CAACsN,KAAK,CAAC,CAAC;QACZ,IAAI,CAACqC,YAAY,CAAClO,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;MACF9M,KAAK,CAAC,IAAI,CAAC2T,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAC7I,OAAO,CAAC,CAC5CiH,IAAI,CAAC7R,SAAS,CAAC,IAAI,CAACiT,UAAU,CAAC,CAAC,CAChCpI,SAAS,CAAC,MAAM,IAAI,CAAC2P,YAAY,CAAClO,IAAI,CAAC,CAAC,CAAC;IAClD;IACA7B,eAAeA,CAAA,EAAG;MACd,KAAK,CAACA,eAAe,CAAC,CAAC;MACvB,IAAI,CAAC,IAAI,CAAC4P,UAAU,KAAK,OAAOjU,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrE,MAAMmV,KAAK,CAAC,iEAAiE,CAAC;MAClF;IACJ;IACAxQ,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACmP,SAAS,EAAE;QAChB;QACA;QACA;QACA,IAAI,CAACsB,gBAAgB,CAAC,CAAC;MAC3B;IACJ;IACAvQ,WAAWA,CAAA,EAAG;MACV,KAAK,CAACA,WAAW,CAAC,CAAC;MACnB,IAAI,CAACuP,YAAY,CAACjP,QAAQ,CAAC,CAAC;IAChC;IACA;IACAkQ,aAAaA,CAACC,YAAY,EAAE;MACxB,IAAI,CAACrB,UAAU,GAAGqB,YAAY;MAC9B,IAAI,CAACrB,UAAU,CAACsB,iBAAiB,CAAC,IAAI,CAACpB,mBAAmB,CAAC;IAC/D;IACA;AACJ;AACA;AACA;IACIqB,gBAAgBA,CAAChX,KAAK,EAAE;MACpB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,CAAC,IAAI,CAACwQ,mBAAmB,CAACzP,KAAK,CAAC,EAAE;QACpD,IAAI,CAACF,KAAK,CAAC,CAAC;MAChB;IACJ;IACA;AACJ;AACA;AACA;IACIA,KAAKA,CAAA,EAAG;MACJ,IAAI,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACwW,UAAU,CAACzG,OAAO,EAAE;QAC1C;MACJ;MACA,IAAI,CAAC,IAAI,CAACH,MAAM,CAACC,MAAM,IAAI,IAAI,CAACD,MAAM,CAACtG,KAAK,CAACtJ,QAAQ,EAAE;QACnD;QACA;QACAgS,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACsE,UAAU,CAAC3V,KAAK,CAAC,CAAC,CAAC;MACzD,CAAC,MACI;QACD,MAAMmX,UAAU,GAAG,IAAI,CAAC7I,WAAW,CAAC6I,UAAU;QAC9C,IAAIA,UAAU,EAAE;UACZA,UAAU,CAACnX,KAAK,CAAC,CAAC;QACtB,CAAC,MACI;UACD,IAAI,CAACsO,WAAW,CAACuF,kBAAkB,CAAC,CAAC;QACzC;MACJ;MACA,IAAI,CAACiC,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACA;AACJ;AACA;AACA;IACI,IAAIwP,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACzB,UAAU,EAAEyB,cAAc,IAAI,EAAE;IAChD;IACA;AACJ;AACA;AACA;IACIH,iBAAiBA,CAACI,GAAG,EAAE;MACnB;MACA;MACA,IAAI,CAACxB,mBAAmB,GAAGwB,GAAG;MAC9B,IAAI,CAAC1B,UAAU,EAAEsB,iBAAiB,CAACI,GAAG,CAAC;IAC3C;IACA;AACJ;AACA;AACA;IACIvD,UAAUA,CAACzU,KAAK,EAAE;MACd;MACA,IAAI,CAACiF,MAAM,GAAGjF,KAAK;IACvB;IACA;AACJ;AACA;AACA;IACI0U,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACtB,SAAS,GAAGsB,EAAE;IACvB;IACA;AACJ;AACA;AACA;IACIC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAACvB,UAAU,GAAGuB,EAAE;IACxB;IACA;AACJ;AACA;AACA;IACIE,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAAChV,QAAQ,GAAGgV,UAAU;MAC1B,IAAI,CAAC2B,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACA;IACAkP,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAAClB,kBAAkB,CAACkB,gBAAgB,CAAC,CAAC;IAC9C;IACA;IACArD,KAAKA,CAAA,EAAG;MACJ,IAAI,CAAC,IAAI,CAACtU,QAAQ,EAAE;QAChB;QACA;QACA;QACA;QACA0I,UAAU,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAACqH,OAAO,EAAE;YACf,IAAI,CAACwE,iBAAiB,CAAC,CAAC;YACxB,IAAI,CAACe,cAAc,CAAC,CAAC;UACzB;QACJ,CAAC,CAAC;MACN;IACJ;IACA;AACJ;AACA;AACA;AACA;IACI1E,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAAC4F,UAAU,CAACzG,OAAO,EAAE;QAC1B,KAAK,CAACa,iBAAiB,CAAC,CAAC;MAC7B;IACJ;IACA;IACA1P,cAAcA,CAACH,KAAK,EAAE;MAClB,MAAMI,OAAO,GAAGJ,KAAK,CAACI,OAAO;MAC7B,MAAM6W,UAAU,GAAG,IAAI,CAAC7I,WAAW,CAAC6I,UAAU;MAC9C,IAAI7W,OAAO,KAAKxH,GAAG,EAAE;QACjB,IAAI,IAAI,CAAC6c,UAAU,CAACzG,OAAO,IACvBnW,cAAc,CAACmH,KAAK,EAAE,UAAU,CAAC,IACjC,IAAI,CAAC6O,MAAM,CAACC,MAAM,IAClB,CAAC,IAAI,CAACD,MAAM,CAACuI,IAAI,CAACnY,QAAQ,EAAE;UAC5Be,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAIgX,UAAU,EAAE;YACZ,IAAI,CAAC7I,WAAW,CAACiJ,aAAa,CAACJ,UAAU,CAAC;UAC9C,CAAC,MACI;YACD,IAAI,CAACK,cAAc,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;UACA;UACA;UACA,KAAK,CAACzH,iBAAiB,CAAC,CAAC;QAC7B;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC4F,UAAU,CAACzG,OAAO,EAAE;QAC/B;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC5O,OAAO,KAAKtH,QAAQ,IAAIsH,OAAO,KAAKrH,UAAU,KAAKke,UAAU,EAAE;UAChE,MAAMM,eAAe,GAAG,IAAI,CAACrI,YAAY,CAAC2D,MAAM,CAAC3L,MAAM,IAAIA,MAAM,CAAClI,UAAU,KAAKiY,UAAU,CAACjY,UAAU,IAAI,CAAC,IAAI,CAAC4R,cAAc,CAAC1J,MAAM,CAAC,CAAC;UACvI,MAAMsQ,YAAY,GAAGD,eAAe,CAAC/F,OAAO,CAACyF,UAAU,CAAC;UACxD,MAAMQ,KAAK,GAAGzX,KAAK,CAACI,OAAO,KAAKtH,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;UACjDkH,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAIuX,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC7H,aAAa,CAAC6H,YAAY,GAAGC,KAAK,CAAC,EAAE;YAC/D,IAAI,CAACrJ,WAAW,CAACiJ,aAAa,CAACE,eAAe,CAACC,YAAY,GAAGC,KAAK,CAAC,CAAC;UACzE;QACJ,CAAC,MACI;UACD,KAAK,CAACtX,cAAc,CAACH,KAAK,CAAC;QAC/B;MACJ;MACA,IAAI,CAAC4V,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACA4P,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACzI,MAAM,CAACC,MAAM,EAAE;QACpB,IAAI,CAACD,MAAM,CAACuI,IAAI,CAACtX,KAAK,CAAC,CAAC;MAC5B;IACJ;IACA;IACA0T,iBAAiBA,CAAA,EAAG;MAChB,MAAMiB,WAAW,GAAG,IAAI,CAAC5F,MAAM,CAACC,MAAM,GAAG,IAAI,CAACD,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACtB,GAAG,CAACtJ,IAAI,IAAIA,IAAI,CAACvH,KAAK,CAAC,GAAG,EAAE;MAC3F,IAAI,CAACiF,MAAM,GAAGqQ,WAAW;MACzB,IAAI,CAAC1D,MAAM,CAACtK,IAAI,CAAC,IAAI2O,iBAAiB,CAAC,IAAI,EAAEX,WAAW,CAAC,CAAC;MAC1D,IAAI,CAAC4B,WAAW,CAAC5P,IAAI,CAACgO,WAAW,CAAC;MAClC,IAAI,CAACjC,SAAS,CAACiC,WAAW,CAAC;MAC3B,IAAI,CAAC9R,kBAAkB,CAACuD,YAAY,CAAC,CAAC;IAC1C;IACA;IACAqO,cAAcA,CAAA,EAAG;MACb,IAAI,CAAChC,UAAU,CAAC,CAAC;MACjB,IAAI,CAAC5P,kBAAkB,CAACuD,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC0P,YAAY,CAAClO,IAAI,CAAC,CAAC;IAC5B;IACA,OAAOpH,IAAI,YAAAoX,oBAAAlX,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6U,WAAW;IAAA;IAC9G,OAAOvN,IAAI,kBA/vD8E5O,EAAE,CAAA6O,iBAAA;MAAApH,IAAA,EA+vDJ0U,WAAW;MAAAzU,SAAA;MAAAoH,cAAA,WAAA2P,2BAAAlb,EAAA,EAAAC,GAAA,EAAAwL,QAAA;QAAA,IAAAzL,EAAA;UA/vDTvD,EAAE,CAAAiP,cAAA,CAAAD,QAAA,EA+vD45B0E,UAAU;QAAA;QAAA,IAAAnQ,EAAA;UAAA,IAAA2L,EAAA;UA/vDx6BlP,EAAE,CAAAmP,cAAA,CAAAD,EAAA,GAAFlP,EAAE,CAAAoP,WAAA,QAAA5L,GAAA,CAAAmS,MAAA,GAAAzG,EAAA;QAAA;MAAA;MAAAvH,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA6W,yBAAAnb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAA+H,UAAA,mBAAA4W,qCAAA;YAAA,OA+vDJnb,GAAA,CAAAoD,KAAA,CAAM,CAAC;UAAA,CAAG,CAAC,kBAAAgY,oCAAA;YAAA,OAAXpb,GAAA,CAAA6W,KAAA,CAAM,CAAC;UAAA,CAAG,CAAC;QAAA;QAAA,IAAA9W,EAAA;UA/vDTvD,EAAE,CAAAmI,WAAA,SAAA3E,GAAA,CAAA2G,IAAA,cAAA3G,GAAA,CAAAuC,QAAA,IAAAvC,GAAA,CAAAmS,MAAA,IAAAnS,GAAA,CAAAmS,MAAA,CAAAC,MAAA,KA+vDsC,CAAC,IAAM,CAAC,GAAApS,GAAA,CAAA0C,QAAA,mBAAlD1C,GAAA,CAAAuC,QAAA,CAAAO,QAAA,CAAkB,CAAC,kBAAA9C,GAAA,CAAA4Z,UAAA;UA/vDjBpd,EAAE,CAAAoI,WAAA,+BAAA5E,GAAA,CAAAuC,QA+vDM,CAAC,8BAAAvC,GAAA,CAAA4Z,UAAD,CAAC,+BAAA5Z,GAAA,CAAAwW,QAAD,CAAC;QAAA;MAAA;MAAA3R,MAAA;QAAAtC,QAAA,8BAA8F3F,gBAAgB;QAAAuc,WAAA;QAAA3C,QAAA,8BAAkE5Z,gBAAgB;QAAA6F,KAAA;QAAAgX,iBAAA;MAAA;MAAApN,OAAA;QAAAgI,MAAA;QAAAsF,WAAA;MAAA;MAAA1U,QAAA,GA/vDzMzI,EAAE,CAAA0I,kBAAA,CA+vD+yB,CAAC;QAAEC,OAAO,EAAE5F,mBAAmB;QAAE6F,WAAW,EAAEuT;MAAY,CAAC,CAAC,GA/vD72Bnc,EAAE,CAAAiJ,0BAAA;MAAA8G,kBAAA,EAAA/K,GAAA;MAAAgL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0O,qBAAAtb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvD,EAAE,CAAAqQ,eAAA;UAAFrQ,EAAE,CAAAyD,cAAA,YAgwD7B,CAAC;UAhwD0BzD,EAAE,CAAA0D,YAAA,EAiwDjE,CAAC;UAjwD8D1D,EAAE,CAAA2D,YAAA,CAkwDtF,CAAC;QAAA;MAAA;MAAA8M,MAAA,GAAAxL,GAAA;MAAAyL,aAAA;MAAAC,eAAA;IAAA;EAEV;EAAC,OAhWKwL,WAAW;AAAA;AAiWjB;EAAA,QAAA7T,SAAA,oBAAAA,SAAA;AAAA;;AA2CA;AACA;AACA;AACA;AAHA,IAIMwW,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfnZ,WAAW,GAAGzF,MAAM,CAACC,UAAU,CAAC;IAChC;IACA2V,OAAO,GAAG,KAAK;IACf;IACA,IAAIiJ,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAAC9Y,KAAK,EAAE;MAChB,IAAIA,KAAK,EAAE;QACP,IAAI,CAAC+Y,SAAS,GAAG/Y,KAAK;QACtB,IAAI,CAAC+Y,SAAS,CAACrB,aAAa,CAAC,IAAI,CAAC;MACtC;IACJ;IACAqB,SAAS;IACT;AACJ;AACA;IACIC,SAAS,GAAG,KAAK;IACjB;AACJ;AACA;AACA;AACA;IACI5Z,iBAAiB;IACjB;IACA6Z,OAAO,GAAG,IAAIte,YAAY,CAAC,CAAC;IAC5B;IACA+b,WAAW,GAAG,EAAE;IAChB;IACAhS,EAAE,GAAGzK,MAAM,CAACd,YAAY,CAAC,CAACwL,KAAK,CAAC,0BAA0B,CAAC;IAC3D;IACA,IAAI7E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACgZ,SAAS,IAAI,IAAI,CAACA,SAAS,CAACjZ,QAAS;IACxE;IACA,IAAIA,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IAC1B;IACAD,SAAS,GAAG,KAAK;IACjB;IACAmZ,QAAQ,GAAG,KAAK;IAChB;IACAC,mBAAmB;IACnB;IACA,IAAI1J,KAAKA,CAAA,EAAG;MACR,OAAO,CAAC,IAAI,CAACkI,YAAY,CAAC3X,KAAK;IACnC;IACA;IACA2X,YAAY;IACZrX,WAAWA,CAAA,EAAG;MACV,MAAM8Y,cAAc,GAAGnf,MAAM,CAACgF,yBAAyB,CAAC;MACxD,MAAMoa,SAAS,GAAGpf,MAAM,CAAC+C,cAAc,EAAE;QAAE6G,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC5D,IAAI,CAAC8T,YAAY,GAAG,IAAI,CAACjY,WAAW,CAACc,aAAa;MAClD,IAAI,CAACpB,iBAAiB,GAAGga,cAAc,CAACha,iBAAiB;MACzD,IAAI,CAAC+Z,mBAAmB,GAAGC,cAAc,CAACE,wBAAwB,IAAI,KAAK;MAC3E,IAAID,SAAS,EAAE;QACX,IAAI,CAAC1B,YAAY,CAAC5G,SAAS,CAACwI,GAAG,CAAC,kCAAkC,CAAC;MACvE;IACJ;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACT,SAAS,CAACtC,YAAY,CAAClO,IAAI,CAAC,CAAC;IACtC;IACArB,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC+R,OAAO,CAACzR,QAAQ,CAAC,CAAC;IAC3B;IACA;IACA6N,QAAQA,CAACxU,KAAK,EAAE;MACZ,IAAI,IAAI,CAAC4O,KAAK,IAAI5O,KAAK,CAACI,OAAO,KAAK1H,SAAS,EAAE;QAC3C;QACA;QACA,IAAI,CAACsH,KAAK,CAAC6G,MAAM,EAAE;UACf,IAAI,CAACqR,SAAS,CAACZ,cAAc,CAAC,CAAC;QACnC;QACAtX,KAAK,CAACC,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI;QACD,IAAI,CAAC2Y,YAAY,CAAC5Y,KAAK,CAAC;MAC5B;IACJ;IACA;IACAuT,KAAKA,CAAA,EAAG;MACJ,IAAI,IAAI,CAAC4E,SAAS,EAAE;QAChB,IAAI,CAACS,YAAY,CAAC,CAAC;MACvB;MACA,IAAI,CAAC5J,OAAO,GAAG,KAAK;MACpB;MACA,IAAI,CAAC,IAAI,CAACkJ,SAAS,CAAClJ,OAAO,EAAE;QACzB,IAAI,CAACkJ,SAAS,CAAC3E,KAAK,CAAC,CAAC;MAC1B;MACA,IAAI,CAAC2E,SAAS,CAACtC,YAAY,CAAClO,IAAI,CAAC,CAAC;IACtC;IACAmR,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC7J,OAAO,GAAG,IAAI;MACnB,IAAI,CAACkJ,SAAS,CAACtC,YAAY,CAAClO,IAAI,CAAC,CAAC;IACtC;IACA;IACAkR,YAAYA,CAAC5Y,KAAK,EAAE;MAChB,IAAI,CAACA,KAAK,IAAK,IAAI,CAAC8Y,eAAe,CAAC9Y,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC6G,MAAO,EAAE;QAC1D,IAAI,CAACuR,OAAO,CAAC3R,IAAI,CAAC;UACdsS,KAAK,EAAE,IAAI,CAACjC,YAAY;UACxB3X,KAAK,EAAE,IAAI,CAAC2X,YAAY,CAAC3X,KAAK;UAC9B6Z,SAAS,EAAE;QACf,CAAC,CAAC;QACFhZ,KAAK,EAAEC,cAAc,CAAC,CAAC;MAC3B;IACJ;IACAgZ,QAAQA,CAAA,EAAG;MACP;MACA,IAAI,CAACf,SAAS,CAACtC,YAAY,CAAClO,IAAI,CAAC,CAAC;IACtC;IACA;IACA5H,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACgX,YAAY,CAAChX,KAAK,CAAC,CAAC;IAC7B;IACA;IACAoZ,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACpC,YAAY,CAAC3X,KAAK,GAAG,EAAE;IAChC;IACA;AACJ;AACA;AACA;IACI,IAAI+X,cAAcA,CAAA,EAAG;MACjB,MAAM/P,OAAO,GAAG,IAAI,CAACtI,WAAW,CAACc,aAAa;MAC9C,MAAMwZ,mBAAmB,GAAGhS,OAAO,CAACiS,YAAY,CAAC,kBAAkB,CAAC;MACpE,OAAOD,mBAAmB,EAAEE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;IAChD;IACAtC,iBAAiBA,CAACI,GAAG,EAAE;MACnB,MAAMhQ,OAAO,GAAG,IAAI,CAACtI,WAAW,CAACc,aAAa;MAC9C;MACA;MACA,IAAIwX,GAAG,CAACrI,MAAM,EAAE;QACZ3H,OAAO,CAACtH,YAAY,CAAC,kBAAkB,EAAEsX,GAAG,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D,CAAC,MACI;QACDnS,OAAO,CAACoS,eAAe,CAAC,kBAAkB,CAAC;MAC/C;IACJ;IACA;IACAT,eAAeA,CAAC9Y,KAAK,EAAE;MACnB,OAAO,CAACnH,cAAc,CAACmH,KAAK,CAAC,IAAI,IAAIwZ,GAAG,CAAC,IAAI,CAACjb,iBAAiB,CAAC,CAACkb,GAAG,CAACzZ,KAAK,CAACI,OAAO,CAAC;IACvF;IACA;IACAsZ,qBAAqBA,CAAA,EAAG;MACpB,OAAO,IAAI,CAACrB,QAAQ,IAAK,IAAI,CAACpZ,QAAQ,IAAI,IAAI,CAACqZ,mBAAoB,GAAG,MAAM,GAAG,IAAI;IACvF;IACA,OAAOhY,IAAI,YAAAqZ,qBAAAnZ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwX,YAAY;IAAA;IAC/G,OAAOvX,IAAI,kBAv8D8EvH,EAAE,CAAAwH,iBAAA;MAAAC,IAAA,EAu8DJqX,YAAY;MAAApX,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA6Y,0BAAAnd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAv8DVvD,EAAE,CAAA+H,UAAA,qBAAA4Y,wCAAA1Y,MAAA;YAAA,OAu8DJzE,GAAA,CAAA8X,QAAA,CAAArT,MAAe,CAAC;UAAA,CAAL,CAAC,kBAAA2Y,qCAAA;YAAA,OAAZpd,GAAA,CAAA6W,KAAA,CAAM,CAAC;UAAA,CAAI,CAAC,mBAAAwG,sCAAA;YAAA,OAAZrd,GAAA,CAAAmc,MAAA,CAAO,CAAC;UAAA,CAAG,CAAC,mBAAAmB,sCAAA;YAAA,OAAZtd,GAAA,CAAAuc,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;QAAA;QAAA,IAAAxc,EAAA;UAv8DVvD,EAAE,CAAA2P,aAAA,OAAAnM,GAAA,CAAAmH,EAu8DO,CAAC;UAv8DV3K,EAAE,CAAAmI,WAAA,aAAA3E,GAAA,CAAAuC,QAAA,KAAAvC,GAAA,CAAA4b,mBAAA,GAu8D+B,EAAE,GAAG,IAAI,iBAAA5b,GAAA,CAAAmZ,WAAA,IAA7B,IAAI,kBAAAnZ,GAAA,CAAAwb,SAAA,IAAAxb,GAAA,CAAAwb,SAAA,CAAA5C,SAAA,GAAA5Y,GAAA,CAAAwb,SAAA,CAAA5C,SAAA,CAAA2E,OAAA,GAA8C,IAAI,mBAAAvd,GAAA,CAAAwb,SAAA,IAAAxb,GAAA,CAAAwb,SAAA,CAAAhF,QAAA,IAAlC,IAAI,mBAAAxW,GAAA,CAAAuC,QAAA,IAAAvC,GAAA,CAAA4b,mBAAA,GAAL,MAAM,GAAG,IAAI,cAA/C5b,GAAA,CAAAgd,qBAAA,CAAsB,CAAC,cAAAhd,GAAA,CAAAwb,SAAA,IAAAxb,GAAA,CAAAwb,SAAA,CAAAhF,QAAA,IAAY,IAAI;QAAA;MAAA;MAAA3R,MAAA;QAAA0W,QAAA;QAAAE,SAAA,4CAAsI7e,gBAAgB;QAAAiF,iBAAA;QAAAsX,WAAA;QAAAhS,EAAA;QAAA5E,QAAA,8BAAuJ3F,gBAAgB;QAAA+e,QAAA,8BAAsC/e,gBAAgB;QAAAgf,mBAAA,gEAAmFhf,gBAAgB;MAAA;MAAAyP,OAAA;QAAAqP,OAAA;MAAA;MAAApP,QAAA;MAAArH,QAAA,GAv8D3fzI,EAAE,CAAAghB,oBAAA;IAAA;EAw8D/F;EAAC,OApJKlC,YAAY;AAAA;AAqJlB;EAAA,QAAAxW,SAAA,oBAAAA,SAAA;AAAA;AAmDA,MAAM2Y,iBAAiB,GAAG,CACtBzX,OAAO,EACPjB,aAAa,EACbkK,gBAAgB,EAChB0J,WAAW,EACX2C,YAAY,EACZ3F,cAAc,EACdnI,aAAa,EACb9H,aAAa,EACbwK,UAAU,EACVqB,UAAU,EACVlM,mBAAmB,CACtB;AAAC,IACIqY,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB,OAAO9Z,IAAI,YAAA+Z,uBAAA7Z,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4Z,cAAc;IAAA;IACjH,OAAOE,IAAI,kBA3gE8EphB,EAAE,CAAAqhB,gBAAA;MAAA5Z,IAAA,EA2gESyZ;IAAc;IAqBlH,OAAOI,IAAI,kBAhiE8EthB,EAAE,CAAAuhB,gBAAA;MAAAC,SAAA,EAgiEoC,CACvH5e,iBAAiB,EACjB;QACI+F,OAAO,EAAEzD,yBAAyB;QAClCuc,QAAQ,EAAE;UACNpc,iBAAiB,EAAE,CAAC/F,KAAK;QAC7B;MACJ,CAAC,CACJ;MAAAoiB,OAAA,GAAYxe,eAAe,EAAEC,eAAe,EAAED,eAAe;IAAA;EACtE;EAAC,OAhCKge,cAAc;AAAA;AAiCpB;EAAA,QAAA5Y,SAAA,oBAAAA,SAAA;AAAA;AAiBA,SAAS7C,QAAQ,EAAEP,yBAAyB,EAAEI,eAAe,EAAE4T,uCAAuC,EAAE1T,eAAe,EAAED,sBAAsB,EAAEiE,OAAO,EAAEjB,aAAa,EAAEkK,gBAAgB,EAAE0J,WAAW,EAAED,iBAAiB,EAAE4C,YAAY,EAAE3F,cAAc,EAAEF,oBAAoB,EAAEjI,aAAa,EAAE9H,aAAa,EAAEwK,UAAU,EAAE9C,sBAAsB,EAAEmE,UAAU,EAAElM,mBAAmB,EAAEqY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}