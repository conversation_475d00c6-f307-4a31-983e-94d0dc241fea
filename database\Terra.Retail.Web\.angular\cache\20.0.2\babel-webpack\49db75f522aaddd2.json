{"ast": null, "code": "import { CommonModule } from '@angular/common';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nfunction SupplierManagementComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"mat-card\", 19)(3, \"mat-card-content\")(4, \"div\", 20)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"people\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"h3\");\n    i0.ɵɵtext(9, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 22);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 23);\n    i0.ɵɵtext(13, \"\\u0645\\u0648\\u0631\\u062F \\u0645\\u0633\\u062C\\u0644\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card\", 24)(15, \"mat-card-content\")(16, \"div\", 20)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 21)(20, \"h3\");\n    i0.ɵɵtext(21, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 22);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 23);\n    i0.ɵɵtext(25, \"\\u0645\\u0648\\u0631\\u062F \\u0646\\u0634\\u0637\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(26, \"mat-card\", 25)(27, \"mat-card-content\")(28, \"div\", 20)(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"cancel\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 21)(32, \"h3\");\n    i0.ɵɵtext(33, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u063A\\u064A\\u0631 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\", 22);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 23);\n    i0.ɵɵtext(37, \"\\u0645\\u0648\\u0631\\u062F \\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(38, \"mat-card\", 26)(39, \"mat-card-content\")(40, \"div\", 20)(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"account_balance_wallet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 21)(44, \"h3\");\n    i0.ɵɵtext(45, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"p\", 27);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\", 23);\n    i0.ɵɵtext(49, \"\\u0631\\u0635\\u064A\\u062F \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.totalSuppliers);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.activeSuppliers);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.inactiveSuppliers);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getBalanceClass(ctx_r0.stats.totalBalance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.formatCurrency(ctx_r0.stats.totalBalance), \" \");\n  }\n}\nfunction SupplierManagementComponent_mat_card_19_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function SupplierManagementComponent_mat_card_19_div_11_Template_div_click_0_listener($event) {\n      const action_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.navigateToAction(action_r5.route));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r5.title);\n  }\n}\nfunction SupplierManagementComponent_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 28);\n    i0.ɵɵlistener(\"click\", function SupplierManagementComponent_mat_card_19_Template_mat_card_click_0_listener() {\n      const section_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.navigateToSection(section_r3.route));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 29)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-title\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"div\", 30);\n    i0.ɵɵtemplate(11, SupplierManagementComponent_mat_card_19_div_11_Template, 5, 2, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 32)(14, \"span\");\n    i0.ɵɵtext(15, \"\\u062F\\u062E\\u0648\\u0644 \\u0627\\u0644\\u0642\\u0633\\u0645\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const section_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", section_r3.color + \"-card\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", section_r3.color + \"-icon\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", section_r3.actions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", section_r3.color);\n  }\n}\nfunction SupplierManagementComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"mat-spinner\", 35);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SupplierManagementComponent = /*#__PURE__*/(() => {\n  class SupplierManagementComponent {\n    router;\n    http;\n    // Component State\n    isLoading = false;\n    // Statistics\n    stats = {\n      totalSuppliers: 0,\n      activeSuppliers: 0,\n      inactiveSuppliers: 0,\n      totalBalance: 0,\n      positiveBalance: 0,\n      negativeBalance: 0\n    };\n    // Subscriptions\n    subscriptions = [];\n    // Management Sections\n    managementSections = [{\n      title: 'إدارة الموردين',\n      description: 'عرض وإدارة جميع الموردين',\n      icon: 'people',\n      route: '/suppliers',\n      color: 'primary',\n      actions: [{\n        title: 'عرض الموردين',\n        route: '/suppliers',\n        icon: 'list'\n      }, {\n        title: 'إضافة مورد',\n        route: '/suppliers/add',\n        icon: 'add'\n      }]\n    }, {\n      title: 'تقييم الموردين',\n      description: 'تقييم أداء الموردين وجودة الخدمة',\n      icon: 'star_rate',\n      route: '/suppliers/evaluation',\n      color: 'accent',\n      actions: [{\n        title: 'تقييم الموردين',\n        route: '/suppliers/evaluation',\n        icon: 'assessment'\n      }, {\n        title: 'تقارير التقييم',\n        route: '/suppliers/evaluation-reports',\n        icon: 'analytics'\n      }]\n    }, {\n      title: 'عقود الموردين',\n      description: 'إدارة العقود والاتفاقيات',\n      icon: 'description',\n      route: '/suppliers/contracts',\n      color: 'warn',\n      actions: [{\n        title: 'العقود النشطة',\n        route: '/suppliers/contracts/active',\n        icon: 'assignment'\n      }, {\n        title: 'إضافة عقد',\n        route: '/suppliers/contracts/add',\n        icon: 'add_box'\n      }]\n    }, {\n      title: 'المدفوعات للموردين',\n      description: 'إدارة المدفوعات والمستحقات',\n      icon: 'payment',\n      route: '/suppliers/payments',\n      color: 'primary',\n      actions: [{\n        title: 'المدفوعات',\n        route: '/suppliers/payments',\n        icon: 'account_balance'\n      }, {\n        title: 'إضافة دفعة',\n        route: '/suppliers/payments/add',\n        icon: 'add_card'\n      }]\n    }, {\n      title: 'طلبات الشراء',\n      description: 'إدارة طلبات الشراء من الموردين',\n      icon: 'shopping_cart',\n      route: '/suppliers/purchase-orders',\n      color: 'accent',\n      actions: [{\n        title: 'طلبات الشراء',\n        route: '/suppliers/purchase-orders',\n        icon: 'shopping_basket'\n      }, {\n        title: 'طلب جديد',\n        route: '/suppliers/purchase-orders/add',\n        icon: 'add_shopping_cart'\n      }]\n    }, {\n      title: 'تقارير الموردين',\n      description: 'تقارير شاملة عن أداء الموردين',\n      icon: 'analytics',\n      route: '/suppliers/reports',\n      color: 'warn',\n      actions: [{\n        title: 'تقارير الأداء',\n        route: '/suppliers/reports/performance',\n        icon: 'trending_up'\n      }, {\n        title: 'تقارير مالية',\n        route: '/suppliers/reports/financial',\n        icon: 'account_balance_wallet'\n      }]\n    }];\n    constructor(router, http) {\n      this.router = router;\n      this.http = http;\n    }\n    ngOnInit() {\n      this.loadStatistics();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Load supplier statistics\n     */\n    loadStatistics() {\n      this.isLoading = true;\n      const sub = this.http.get('http://localhost:5127/api/simple/suppliers').subscribe({\n        next: response => {\n          const suppliers = response.suppliers || [];\n          this.calculateStatistics(suppliers);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading statistics:', error);\n          this.stats = this.getMockStatistics();\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Calculate statistics from suppliers data\n     */\n    calculateStatistics(suppliers) {\n      this.stats.totalSuppliers = suppliers.length;\n      this.stats.activeSuppliers = suppliers.filter(s => s.IsActive !== false).length;\n      this.stats.inactiveSuppliers = this.stats.totalSuppliers - this.stats.activeSuppliers;\n      const balances = suppliers.map(s => s.CurrentBalance || 0);\n      this.stats.totalBalance = balances.reduce((sum, balance) => sum + balance, 0);\n      this.stats.positiveBalance = balances.filter(b => b > 0).reduce((sum, balance) => sum + balance, 0);\n      this.stats.negativeBalance = balances.filter(b => b < 0).reduce((sum, balance) => sum + balance, 0);\n    }\n    /**\n     * Navigate to section\n     */\n    navigateToSection(route) {\n      if (route.includes('/suppliers/')) {\n        // For now, redirect unimplemented routes to main suppliers page\n        if (route === '/suppliers' || route === '/suppliers/add') {\n          this.router.navigate([route]);\n        } else {\n          alert(`هذا القسم قيد التطوير: ${route}`);\n        }\n      } else {\n        this.router.navigate([route]);\n      }\n    }\n    /**\n     * Navigate to action\n     */\n    navigateToAction(route) {\n      this.navigateToSection(route);\n    }\n    /**\n     * Format currency\n     */\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('ar-EG', {\n        style: 'currency',\n        currency: 'EGP'\n      }).format(amount);\n    }\n    /**\n     * Get balance class for styling\n     */\n    getBalanceClass(balance) {\n      if (balance > 0) return 'positive';\n      if (balance < 0) return 'negative';\n      return 'zero';\n    }\n    /**\n     * Get mock statistics\n     */\n    getMockStatistics() {\n      return {\n        totalSuppliers: 10,\n        activeSuppliers: 8,\n        inactiveSuppliers: 2,\n        totalBalance: -50000,\n        positiveBalance: 200000,\n        negativeBalance: -250000\n      };\n    }\n    static ɵfac = function SupplierManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SupplierManagementComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierManagementComponent,\n      selectors: [[\"app-supplier-management\"]],\n      decls: 57,\n      vars: 3,\n      consts: [[1, \"supplier-management-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"statistics-section\", 4, \"ngIf\"], [1, \"management-sections\"], [1, \"section-title\"], [1, \"sections-grid\"], [\"class\", \"management-card\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-actions-section\"], [1, \"quick-actions-grid\"], [1, \"quick-action-card\", 3, \"click\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"statistics-section\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-card\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-card\", \"active-card\"], [1, \"stat-card\", \"inactive-card\"], [1, \"stat-card\", \"balance-card\"], [1, \"stat-number\", 3, \"ngClass\"], [1, \"management-card\", 3, \"click\", \"ngClass\"], [1, \"card-icon\", 3, \"ngClass\"], [1, \"actions-list\"], [\"class\", \"action-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-button\", \"\", 3, \"color\"], [1, \"action-item\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function SupplierManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"\\u0646\\u0638\\u0627\\u0645 \\u0634\\u0627\\u0645\\u0644 \\u0644\\u0625\\u062F\\u0627\\u0631\\u0629 \\u062C\\u0645\\u064A\\u0639 \\u0639\\u0645\\u0644\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function SupplierManagementComponent_Template_button_click_9_listener() {\n            return ctx.navigateToSection(\"/suppliers/add\");\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(14, SupplierManagementComponent_div_14_Template, 50, 5, \"div\", 8);\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"h2\", 10);\n          i0.ɵɵtext(17, \"\\u0623\\u0642\\u0633\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 11);\n          i0.ɵɵtemplate(19, SupplierManagementComponent_mat_card_19_Template, 18, 7, \"mat-card\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"h2\", 10);\n          i0.ɵɵtext(22, \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"mat-card\", 15);\n          i0.ɵɵlistener(\"click\", function SupplierManagementComponent_Template_mat_card_click_24_listener() {\n            return ctx.navigateToSection(\"/suppliers\");\n          });\n          i0.ɵɵelementStart(25, \"mat-card-content\")(26, \"mat-icon\");\n          i0.ɵɵtext(27, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"h3\");\n          i0.ɵɵtext(29, \"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"\\u0627\\u0633\\u062A\\u0639\\u0631\\u0627\\u0636 \\u0642\\u0627\\u0626\\u0645\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0628\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"mat-card\", 15);\n          i0.ɵɵlistener(\"click\", function SupplierManagementComponent_Template_mat_card_click_32_listener() {\n            return ctx.navigateToSection(\"/suppliers/add\");\n          });\n          i0.ɵɵelementStart(33, \"mat-card-content\")(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"h3\");\n          i0.ɵɵtext(37, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"mat-card\", 15);\n          i0.ɵɵlistener(\"click\", function SupplierManagementComponent_Template_mat_card_click_40_listener() {\n            return ctx.navigateToSection(\"/suppliers/reports\");\n          });\n          i0.ɵɵelementStart(41, \"mat-card-content\")(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"h3\");\n          i0.ɵɵtext(45, \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\");\n          i0.ɵɵtext(47, \"\\u0639\\u0631\\u0636 \\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0639\\u0646 \\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"mat-card\", 15);\n          i0.ɵɵlistener(\"click\", function SupplierManagementComponent_Template_mat_card_click_48_listener() {\n            return ctx.navigateToSection(\"/suppliers/payments\");\n          });\n          i0.ɵɵelementStart(49, \"mat-card-content\")(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"payment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"h3\");\n          i0.ɵɵtext(53, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p\");\n          i0.ɵɵtext(55, \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0633\\u062A\\u062D\\u0642\\u0627\\u062A\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(56, SupplierManagementComponent_div_56_Template, 4, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.managementSections);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, MatCardModule, i4.MatCard, i4.MatCardActions, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, MatButtonModule, i5.MatButton, MatIconModule, i6.MatIcon, MatTabsModule, MatProgressSpinnerModule, i7.MatProgressSpinner, MatGridListModule],\n      styles: [\"\\n\\n.supplier-management-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: var(--warning-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: var(--warning-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n\\n\\n\\n.statistics-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-3xl);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-lg) !important;\\n  padding: var(--spacing-xl) !important;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: var(--radius-full);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  color: white;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--gray-600);\\n  margin: 0 0 var(--spacing-xs) 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-xs) 0;\\n  color: var(--gray-900);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-number.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-600);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n  font-weight: 500;\\n}\\n.stat-card.total-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.stat-card.active-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.stat-card.inactive-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.stat-card.balance-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--info-500), var(--info-600));\\n}\\n\\n\\n\\n.management-sections[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-3xl);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0 0 var(--spacing-2xl) 0;\\n  text-align: center;\\n}\\n\\n.sections-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.management-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n  cursor: pointer;\\n  overflow: hidden;\\n}\\n.management-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-6px) !important;\\n  box-shadow: var(--shadow-2xl) !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-xl) !important;\\n  border-bottom: 1px solid var(--gray-200) !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-lg) !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: var(--radius-full);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .card-icon.primary-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .card-icon.accent-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .card-icon.warn-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--error-500), var(--error-600));\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  color: var(--gray-600) !important;\\n  margin: var(--spacing-xs) 0 0 0 !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n.management-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md);\\n  border-radius: var(--radius-lg);\\n  background: var(--gray-50);\\n  transition: all var(--transition-normal);\\n  cursor: pointer;\\n}\\n.management-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]:hover {\\n  background: var(--gray-100);\\n  transform: translateX(var(--spacing-sm));\\n}\\n.management-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600);\\n  font-size: 1.25rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n.management-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--gray-700);\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-actions[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  border-top: 1px solid var(--gray-200) !important;\\n  background: var(--gray-25) !important;\\n}\\n.management-card[_ngcontent-%COMP%]   .mat-mdc-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-sm) !important;\\n}\\n.management-card.primary-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--primary-500) !important;\\n}\\n.management-card.accent-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--warning-500) !important;\\n}\\n.management-card.warn-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--error-500) !important;\\n}\\n\\n\\n\\n.quick-actions-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.quick-actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-lg);\\n}\\n\\n.quick-action-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-lg) !important;\\n  box-shadow: var(--shadow-md) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  transition: all var(--transition-normal) !important;\\n  cursor: pointer;\\n  text-align: center;\\n}\\n.quick-action-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border-color: var(--primary-300) !important;\\n}\\n.quick-action-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) !important;\\n}\\n.quick-action-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: var(--primary-600);\\n  margin-bottom: var(--spacing-md);\\n}\\n.quick-action-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n  margin: 0 0 var(--spacing-sm) 0;\\n}\\n.quick-action-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  font-size: 1.125rem;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   .mat-mdc-progress-spinner[_ngcontent-%COMP%] {\\n  --mdc-circular-progress-active-indicator-color: var(--primary-500);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .sections-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .sections-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .quick-actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .quick-actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    text-align: center !important;\\n    gap: var(--spacing-md) !important;\\n  }\\n  .management-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    text-align: center !important;\\n    gap: var(--spacing-md) !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SupplierManagementComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatTabsModule", "MatProgressSpinnerModule", "MatGridListModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "stats", "totalSuppliers", "activeSuppliers", "inactiveSuppliers", "ɵɵproperty", "getBalanceClass", "totalBalance", "ɵɵtextInterpolate1", "formatCurrency", "ɵɵlistener", "SupplierManagementComponent_mat_card_19_div_11_Template_div_click_0_listener", "$event", "action_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "stopPropagation", "ɵɵresetView", "navigateToAction", "route", "icon", "title", "SupplierManagementComponent_mat_card_19_Template_mat_card_click_0_listener", "section_r3", "_r2", "navigateToSection", "ɵɵtemplate", "SupplierManagementComponent_mat_card_19_div_11_Template", "color", "description", "actions", "ɵɵelement", "SupplierManagementComponent", "router", "http", "isLoading", "positiveBalance", "negativeBalance", "subscriptions", "managementSections", "constructor", "ngOnInit", "loadStatistics", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "get", "subscribe", "next", "response", "suppliers", "calculateStatistics", "error", "console", "getMockStatistics", "push", "length", "filter", "s", "IsActive", "balances", "map", "CurrentBalance", "reduce", "sum", "balance", "b", "includes", "navigate", "alert", "amount", "Intl", "NumberFormat", "style", "currency", "format", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "SupplierManagementComponent_Template", "rf", "ctx", "SupplierManagementComponent_Template_button_click_9_listener", "SupplierManagementComponent_div_14_Template", "SupplierManagementComponent_mat_card_19_Template", "SupplierManagementComponent_Template_mat_card_click_24_listener", "SupplierManagementComponent_Template_mat_card_click_32_listener", "SupplierManagementComponent_Template_mat_card_click_40_listener", "SupplierManagementComponent_Template_mat_card_click_48_listener", "SupplierManagementComponent_div_56_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "MatCard", "MatCardActions", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i5", "MatButton", "i6", "MatIcon", "i7", "MatProgressSpinner", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-management\\supplier-management.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\supplier-management\\supplier-management.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatGridListModule } from '@angular/material/grid-list';\n\n@Component({\n  selector: 'app-supplier-management',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTabsModule,\n    MatProgressSpinnerModule,\n    MatGridListModule\n  ],\n  templateUrl: './supplier-management.component.html',\n  styleUrls: ['./supplier-management.component.scss']\n})\nexport class SupplierManagementComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  \n  // Statistics\n  stats = {\n    totalSuppliers: 0,\n    activeSuppliers: 0,\n    inactiveSuppliers: 0,\n    totalBalance: 0,\n    positiveBalance: 0,\n    negativeBalance: 0\n  };\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  // Management Sections\n  managementSections = [\n    {\n      title: 'إدارة الموردين',\n      description: 'عرض وإدارة جميع الموردين',\n      icon: 'people',\n      route: '/suppliers',\n      color: 'primary',\n      actions: [\n        { title: 'عرض الموردين', route: '/suppliers', icon: 'list' },\n        { title: 'إضافة مورد', route: '/suppliers/add', icon: 'add' }\n      ]\n    },\n    {\n      title: 'تقييم الموردين',\n      description: 'تقييم أداء الموردين وجودة الخدمة',\n      icon: 'star_rate',\n      route: '/suppliers/evaluation',\n      color: 'accent',\n      actions: [\n        { title: 'تقييم الموردين', route: '/suppliers/evaluation', icon: 'assessment' },\n        { title: 'تقارير التقييم', route: '/suppliers/evaluation-reports', icon: 'analytics' }\n      ]\n    },\n    {\n      title: 'عقود الموردين',\n      description: 'إدارة العقود والاتفاقيات',\n      icon: 'description',\n      route: '/suppliers/contracts',\n      color: 'warn',\n      actions: [\n        { title: 'العقود النشطة', route: '/suppliers/contracts/active', icon: 'assignment' },\n        { title: 'إضافة عقد', route: '/suppliers/contracts/add', icon: 'add_box' }\n      ]\n    },\n    {\n      title: 'المدفوعات للموردين',\n      description: 'إدارة المدفوعات والمستحقات',\n      icon: 'payment',\n      route: '/suppliers/payments',\n      color: 'primary',\n      actions: [\n        { title: 'المدفوعات', route: '/suppliers/payments', icon: 'account_balance' },\n        { title: 'إضافة دفعة', route: '/suppliers/payments/add', icon: 'add_card' }\n      ]\n    },\n    {\n      title: 'طلبات الشراء',\n      description: 'إدارة طلبات الشراء من الموردين',\n      icon: 'shopping_cart',\n      route: '/suppliers/purchase-orders',\n      color: 'accent',\n      actions: [\n        { title: 'طلبات الشراء', route: '/suppliers/purchase-orders', icon: 'shopping_basket' },\n        { title: 'طلب جديد', route: '/suppliers/purchase-orders/add', icon: 'add_shopping_cart' }\n      ]\n    },\n    {\n      title: 'تقارير الموردين',\n      description: 'تقارير شاملة عن أداء الموردين',\n      icon: 'analytics',\n      route: '/suppliers/reports',\n      color: 'warn',\n      actions: [\n        { title: 'تقارير الأداء', route: '/suppliers/reports/performance', icon: 'trending_up' },\n        { title: 'تقارير مالية', route: '/suppliers/reports/financial', icon: 'account_balance_wallet' }\n      ]\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    this.loadStatistics();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Load supplier statistics\n   */\n  private loadStatistics(): void {\n    this.isLoading = true;\n    \n    const sub = this.http.get<any>('http://localhost:5127/api/simple/suppliers').subscribe({\n      next: (response) => {\n        const suppliers = response.suppliers || [];\n        this.calculateStatistics(suppliers);\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error loading statistics:', error);\n        this.stats = this.getMockStatistics();\n        this.isLoading = false;\n      }\n    });\n    \n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Calculate statistics from suppliers data\n   */\n  private calculateStatistics(suppliers: any[]): void {\n    this.stats.totalSuppliers = suppliers.length;\n    this.stats.activeSuppliers = suppliers.filter(s => s.IsActive !== false).length;\n    this.stats.inactiveSuppliers = this.stats.totalSuppliers - this.stats.activeSuppliers;\n    \n    const balances = suppliers.map(s => s.CurrentBalance || 0);\n    this.stats.totalBalance = balances.reduce((sum, balance) => sum + balance, 0);\n    this.stats.positiveBalance = balances.filter(b => b > 0).reduce((sum, balance) => sum + balance, 0);\n    this.stats.negativeBalance = balances.filter(b => b < 0).reduce((sum, balance) => sum + balance, 0);\n  }\n\n  /**\n   * Navigate to section\n   */\n  navigateToSection(route: string): void {\n    if (route.includes('/suppliers/')) {\n      // For now, redirect unimplemented routes to main suppliers page\n      if (route === '/suppliers' || route === '/suppliers/add') {\n        this.router.navigate([route]);\n      } else {\n        alert(`هذا القسم قيد التطوير: ${route}`);\n      }\n    } else {\n      this.router.navigate([route]);\n    }\n  }\n\n  /**\n   * Navigate to action\n   */\n  navigateToAction(route: string): void {\n    this.navigateToSection(route);\n  }\n\n  /**\n   * Format currency\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('ar-EG', {\n      style: 'currency',\n      currency: 'EGP'\n    }).format(amount);\n  }\n\n  /**\n   * Get balance class for styling\n   */\n  getBalanceClass(balance: number): string {\n    if (balance > 0) return 'positive';\n    if (balance < 0) return 'negative';\n    return 'zero';\n  }\n\n  /**\n   * Get mock statistics\n   */\n  private getMockStatistics(): any {\n    return {\n      totalSuppliers: 10,\n      activeSuppliers: 8,\n      inactiveSuppliers: 2,\n      totalBalance: -50000,\n      positiveBalance: 200000,\n      negativeBalance: -250000\n    };\n  }\n}\n", "<!-- Terra Retail ERP - Supplier Management -->\n<div class=\"supplier-management-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-text\">\n        <h1 class=\"page-title\">إدارة الموردين</h1>\n        <p class=\"page-subtitle\">نظام شامل لإدارة جميع عمليات الموردين</p>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" (click)=\"navigateToSection('/suppliers/add')\">\n          <mat-icon>add</mat-icon>\n          <span>إضافة مورد جديد</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Statistics Cards -->\n  <div class=\"statistics-section\" *ngIf=\"!isLoading\">\n    <div class=\"stats-grid\">\n      \n      <!-- Total Suppliers -->\n      <mat-card class=\"stat-card total-card\">\n        <mat-card-content>\n          <div class=\"stat-icon\">\n            <mat-icon>people</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <h3>إجمالي الموردين</h3>\n            <p class=\"stat-number\">{{ stats.totalSuppliers }}</p>\n            <span class=\"stat-label\">مورد مسجل</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Active Suppliers -->\n      <mat-card class=\"stat-card active-card\">\n        <mat-card-content>\n          <div class=\"stat-icon\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <h3>الموردين النشطين</h3>\n            <p class=\"stat-number\">{{ stats.activeSuppliers }}</p>\n            <span class=\"stat-label\">مورد نشط</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Inactive Suppliers -->\n      <mat-card class=\"stat-card inactive-card\">\n        <mat-card-content>\n          <div class=\"stat-icon\">\n            <mat-icon>cancel</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <h3>الموردين غير النشطين</h3>\n            <p class=\"stat-number\">{{ stats.inactiveSuppliers }}</p>\n            <span class=\"stat-label\">مورد غير نشط</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Total Balance -->\n      <mat-card class=\"stat-card balance-card\">\n        <mat-card-content>\n          <div class=\"stat-icon\">\n            <mat-icon>account_balance_wallet</mat-icon>\n          </div>\n          <div class=\"stat-info\">\n            <h3>إجمالي الأرصدة</h3>\n            <p class=\"stat-number\" [ngClass]=\"getBalanceClass(stats.totalBalance)\">\n              {{ formatCurrency(stats.totalBalance) }}\n            </p>\n            <span class=\"stat-label\">رصيد إجمالي</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n    </div>\n  </div>\n\n  <!-- Management Sections -->\n  <div class=\"management-sections\">\n    <h2 class=\"section-title\">أقسام إدارة الموردين</h2>\n    \n    <div class=\"sections-grid\">\n      \n      <mat-card *ngFor=\"let section of managementSections\" \n                class=\"management-card\" \n                [ngClass]=\"section.color + '-card'\"\n                (click)=\"navigateToSection(section.route)\">\n        \n        <mat-card-header>\n          <div class=\"card-icon\" [ngClass]=\"section.color + '-icon'\">\n            <mat-icon>{{ section.icon }}</mat-icon>\n          </div>\n          <mat-card-title>{{ section.title }}</mat-card-title>\n          <mat-card-subtitle>{{ section.description }}</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <div class=\"actions-list\">\n            <div *ngFor=\"let action of section.actions\" \n                 class=\"action-item\"\n                 (click)=\"$event.stopPropagation(); navigateToAction(action.route)\">\n              <mat-icon>{{ action.icon }}</mat-icon>\n              <span>{{ action.title }}</span>\n            </div>\n          </div>\n        </mat-card-content>\n\n        <mat-card-actions>\n          <button mat-button [color]=\"section.color\">\n            <span>دخول القسم</span>\n            <mat-icon>arrow_forward</mat-icon>\n          </button>\n        </mat-card-actions>\n\n      </mat-card>\n\n    </div>\n  </div>\n\n  <!-- Quick Actions -->\n  <div class=\"quick-actions-section\">\n    <h2 class=\"section-title\">إجراءات سريعة</h2>\n    \n    <div class=\"quick-actions-grid\">\n      \n      <mat-card class=\"quick-action-card\" (click)=\"navigateToSection('/suppliers')\">\n        <mat-card-content>\n          <mat-icon>list</mat-icon>\n          <h3>عرض جميع الموردين</h3>\n          <p>استعراض قائمة شاملة بجميع الموردين</p>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"quick-action-card\" (click)=\"navigateToSection('/suppliers/add')\">\n        <mat-card-content>\n          <mat-icon>person_add</mat-icon>\n          <h3>إضافة مورد جديد</h3>\n          <p>تسجيل مورد جديد في النظام</p>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"quick-action-card\" (click)=\"navigateToSection('/suppliers/reports')\">\n        <mat-card-content>\n          <mat-icon>analytics</mat-icon>\n          <h3>تقارير الموردين</h3>\n          <p>عرض تقارير شاملة عن أداء الموردين</p>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"quick-action-card\" (click)=\"navigateToSection('/suppliers/payments')\">\n        <mat-card-content>\n          <mat-icon>payment</mat-icon>\n          <h3>إدارة المدفوعات</h3>\n          <p>متابعة المدفوعات والمستحقات</p>\n        </mat-card-content>\n      </mat-card>\n\n    </div>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل بيانات الموردين...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAK9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;;;;;;;;;;;ICenDC,EAPV,CAAAC,cAAA,cAAmD,cACzB,mBAGiB,uBACnB,cACO,eACX;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,yDAAS;IAGxCF,EAHwC,CAAAG,YAAA,EAAO,EACrC,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAwC,wBACpB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,mGAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,mDAAQ;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA0C,wBACtB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,sHAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,sEAAY;IAG3CF,EAH2C,CAAAG,YAAA,EAAO,EACxC,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAyC,wBACrB,eACO,gBACX;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;IAEJH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,uFAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,qEAAW;IAM9CF,EAN8C,CAAAG,YAAA,EAAO,EACvC,EACW,EACV,EAEP,EACF;;;;IAnD2BH,EAAA,CAAAI,SAAA,IAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,cAAA,CAA0B;IAc1BR,EAAA,CAAAI,SAAA,IAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAE,eAAA,CAA2B;IAc3BT,EAAA,CAAAI,SAAA,IAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAG,iBAAA,CAA6B;IAc7BV,EAAA,CAAAI,SAAA,IAA+C;IAA/CJ,EAAA,CAAAW,UAAA,YAAAL,MAAA,CAAAM,eAAA,CAAAN,MAAA,CAAAC,KAAA,CAAAM,YAAA,EAA+C;IACpEb,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAS,cAAA,CAAAT,MAAA,CAAAC,KAAA,CAAAM,YAAA,OACF;;;;;;IA8BAb,EAAA,CAAAC,cAAA,cAEwE;IAAnED,EAAA,CAAAgB,UAAA,mBAAAC,6EAAAC,MAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAASL,MAAA,CAAAM,eAAA,EAAwB;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAEnB,MAAA,CAAAoB,gBAAA,CAAAP,SAAA,CAAAQ,KAAA,CAA8B;IAAA,EAAC;IACrE3B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;;;;IAFMH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAc,SAAA,CAAAS,IAAA,CAAiB;IACrB5B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAc,SAAA,CAAAU,KAAA,CAAkB;;;;;;IAnBhC7B,EAAA,CAAAC,cAAA,mBAGqD;IAA3CD,EAAA,CAAAgB,UAAA,mBAAAc,2EAAA;MAAA,MAAAC,UAAA,GAAA/B,EAAA,CAAAoB,aAAA,CAAAY,GAAA,EAAAV,SAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASnB,MAAA,CAAA2B,iBAAA,CAAAF,UAAA,CAAAJ,KAAA,CAAgC;IAAA,EAAC;IAI9C3B,EAFJ,CAAAC,cAAA,sBAAiB,cAC4C,eAC/C;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACnC;IACNH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACpDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAC9CF,EAD8C,CAAAG,YAAA,EAAoB,EAChD;IAGhBH,EADF,CAAAC,cAAA,uBAAkB,eACU;IACxBD,EAAA,CAAAkC,UAAA,KAAAC,uDAAA,kBAEwE;IAK5EnC,EADE,CAAAG,YAAA,EAAM,EACW;IAIfH,EAFJ,CAAAC,cAAA,wBAAkB,kBAC2B,YACnC;IAAAD,EAAA,CAAAE,MAAA,+DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvBH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAI7BF,EAJ6B,CAAAG,YAAA,EAAW,EAC3B,EACQ,EAEV;;;;IA7BDH,EAAA,CAAAW,UAAA,YAAAoB,UAAA,CAAAK,KAAA,WAAmC;IAIlBpC,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAW,UAAA,YAAAoB,UAAA,CAAAK,KAAA,WAAmC;IAC9CpC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA0B,UAAA,CAAAH,IAAA,CAAkB;IAEd5B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA0B,UAAA,CAAAF,KAAA,CAAmB;IAChB7B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAA0B,UAAA,CAAAM,WAAA,CAAyB;IAKlBrC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAW,UAAA,YAAAoB,UAAA,CAAAO,OAAA,CAAkB;IAUzBtC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,UAAA,UAAAoB,UAAA,CAAAK,KAAA,CAAuB;;;;;IAqDlDpC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAuC,SAAA,sBAAyC;IACzCvC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uJAA6B;IAClCF,EADkC,CAAAG,YAAA,EAAI,EAChC;;;AD9IR,WAAaqC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAyF5BC,MAAA;IACAC,IAAA;IAxFV;IACAC,SAAS,GAAG,KAAK;IAEjB;IACApC,KAAK,GAAG;MACNC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBG,YAAY,EAAE,CAAC;MACf+B,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE;KAClB;IAED;IACQC,aAAa,GAAmB,EAAE;IAE1C;IACAC,kBAAkB,GAAG,CACnB;MACElB,KAAK,EAAE,gBAAgB;MACvBQ,WAAW,EAAE,0BAA0B;MACvCT,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,YAAY;MACnBS,KAAK,EAAE,SAAS;MAChBE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,cAAc;QAAEF,KAAK,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAM,CAAE,EAC5D;QAAEC,KAAK,EAAE,YAAY;QAAEF,KAAK,EAAE,gBAAgB;QAAEC,IAAI,EAAE;MAAK,CAAE;KAEhE,EACD;MACEC,KAAK,EAAE,gBAAgB;MACvBQ,WAAW,EAAE,kCAAkC;MAC/CT,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,uBAAuB;MAC9BS,KAAK,EAAE,QAAQ;MACfE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,gBAAgB;QAAEF,KAAK,EAAE,uBAAuB;QAAEC,IAAI,EAAE;MAAY,CAAE,EAC/E;QAAEC,KAAK,EAAE,gBAAgB;QAAEF,KAAK,EAAE,+BAA+B;QAAEC,IAAI,EAAE;MAAW,CAAE;KAEzF,EACD;MACEC,KAAK,EAAE,eAAe;MACtBQ,WAAW,EAAE,0BAA0B;MACvCT,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,sBAAsB;MAC7BS,KAAK,EAAE,MAAM;MACbE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,eAAe;QAAEF,KAAK,EAAE,6BAA6B;QAAEC,IAAI,EAAE;MAAY,CAAE,EACpF;QAAEC,KAAK,EAAE,WAAW;QAAEF,KAAK,EAAE,0BAA0B;QAAEC,IAAI,EAAE;MAAS,CAAE;KAE7E,EACD;MACEC,KAAK,EAAE,oBAAoB;MAC3BQ,WAAW,EAAE,4BAA4B;MACzCT,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE,qBAAqB;MAC5BS,KAAK,EAAE,SAAS;MAChBE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,WAAW;QAAEF,KAAK,EAAE,qBAAqB;QAAEC,IAAI,EAAE;MAAiB,CAAE,EAC7E;QAAEC,KAAK,EAAE,YAAY;QAAEF,KAAK,EAAE,yBAAyB;QAAEC,IAAI,EAAE;MAAU,CAAE;KAE9E,EACD;MACEC,KAAK,EAAE,cAAc;MACrBQ,WAAW,EAAE,gCAAgC;MAC7CT,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,4BAA4B;MACnCS,KAAK,EAAE,QAAQ;MACfE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,cAAc;QAAEF,KAAK,EAAE,4BAA4B;QAAEC,IAAI,EAAE;MAAiB,CAAE,EACvF;QAAEC,KAAK,EAAE,UAAU;QAAEF,KAAK,EAAE,gCAAgC;QAAEC,IAAI,EAAE;MAAmB,CAAE;KAE5F,EACD;MACEC,KAAK,EAAE,iBAAiB;MACxBQ,WAAW,EAAE,+BAA+B;MAC5CT,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,oBAAoB;MAC3BS,KAAK,EAAE,MAAM;MACbE,OAAO,EAAE,CACP;QAAET,KAAK,EAAE,eAAe;QAAEF,KAAK,EAAE,gCAAgC;QAAEC,IAAI,EAAE;MAAa,CAAE,EACxF;QAAEC,KAAK,EAAE,cAAc;QAAEF,KAAK,EAAE,8BAA8B;QAAEC,IAAI,EAAE;MAAwB,CAAE;KAEnG,CACF;IAEDoB,YACUP,MAAc,EACdC,IAAgB;MADhB,KAAAD,MAAM,GAANA,MAAM;MACN,KAAAC,IAAI,GAAJA,IAAI;IACX;IAEHO,QAAQA,CAAA;MACN,IAAI,CAACC,cAAc,EAAE;IACvB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQJ,cAAcA,CAAA;MACpB,IAAI,CAACP,SAAS,GAAG,IAAI;MAErB,MAAMU,GAAG,GAAG,IAAI,CAACX,IAAI,CAACa,GAAG,CAAM,4CAA4C,CAAC,CAACC,SAAS,CAAC;QACrFC,IAAI,EAAGC,QAAQ,IAAI;UACjB,MAAMC,SAAS,GAAGD,QAAQ,CAACC,SAAS,IAAI,EAAE;UAC1C,IAAI,CAACC,mBAAmB,CAACD,SAAS,CAAC;UACnC,IAAI,CAAChB,SAAS,GAAG,KAAK;QACxB,CAAC;QACDkB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAACtD,KAAK,GAAG,IAAI,CAACwD,iBAAiB,EAAE;UACrC,IAAI,CAACpB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MAEF,IAAI,CAACG,aAAa,CAACkB,IAAI,CAACX,GAAG,CAAC;IAC9B;IAEA;;;IAGQO,mBAAmBA,CAACD,SAAgB;MAC1C,IAAI,CAACpD,KAAK,CAACC,cAAc,GAAGmD,SAAS,CAACM,MAAM;MAC5C,IAAI,CAAC1D,KAAK,CAACE,eAAe,GAAGkD,SAAS,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,KAAK,CAAC,CAACH,MAAM;MAC/E,IAAI,CAAC1D,KAAK,CAACG,iBAAiB,GAAG,IAAI,CAACH,KAAK,CAACC,cAAc,GAAG,IAAI,CAACD,KAAK,CAACE,eAAe;MAErF,MAAM4D,QAAQ,GAAGV,SAAS,CAACW,GAAG,CAACH,CAAC,IAAIA,CAAC,CAACI,cAAc,IAAI,CAAC,CAAC;MAC1D,IAAI,CAAChE,KAAK,CAACM,YAAY,GAAGwD,QAAQ,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,EAAE,CAAC,CAAC;MAC7E,IAAI,CAACnE,KAAK,CAACqC,eAAe,GAAGyB,QAAQ,CAACH,MAAM,CAACS,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAACH,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,EAAE,CAAC,CAAC;MACnG,IAAI,CAACnE,KAAK,CAACsC,eAAe,GAAGwB,QAAQ,CAACH,MAAM,CAACS,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAACH,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,EAAE,CAAC,CAAC;IACrG;IAEA;;;IAGAzC,iBAAiBA,CAACN,KAAa;MAC7B,IAAIA,KAAK,CAACiD,QAAQ,CAAC,aAAa,CAAC,EAAE;QACjC;QACA,IAAIjD,KAAK,KAAK,YAAY,IAAIA,KAAK,KAAK,gBAAgB,EAAE;UACxD,IAAI,CAACc,MAAM,CAACoC,QAAQ,CAAC,CAAClD,KAAK,CAAC,CAAC;QAC/B,CAAC,MAAM;UACLmD,KAAK,CAAC,0BAA0BnD,KAAK,EAAE,CAAC;QAC1C;MACF,CAAC,MAAM;QACL,IAAI,CAACc,MAAM,CAACoC,QAAQ,CAAC,CAAClD,KAAK,CAAC,CAAC;MAC/B;IACF;IAEA;;;IAGAD,gBAAgBA,CAACC,KAAa;MAC5B,IAAI,CAACM,iBAAiB,CAACN,KAAK,CAAC;IAC/B;IAEA;;;IAGAZ,cAAcA,CAACgE,MAAc;MAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE;OACX,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;IACnB;IAEA;;;IAGAnE,eAAeA,CAAC8D,OAAe;MAC7B,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,OAAO,MAAM;IACf;IAEA;;;IAGQX,iBAAiBA,CAAA;MACvB,OAAO;QACLvD,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,CAAC;QAClBC,iBAAiB,EAAE,CAAC;QACpBG,YAAY,EAAE,CAAC,KAAK;QACpB+B,eAAe,EAAE,MAAM;QACvBC,eAAe,EAAE,CAAC;OACnB;IACH;;uCA/LWL,2BAA2B,EAAAxC,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;;YAA3BjD,2BAA2B;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBhChG,EANR,CAAAC,cAAA,aAA2C,aAGhB,aACK,aACD,YACA;UAAAD,EAAA,CAAAE,MAAA,sFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1CH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,4MAAqC;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAEJH,EADF,CAAAC,cAAA,aAA4B,gBAC8D;UAA9CD,EAAA,CAAAgB,UAAA,mBAAAkF,6DAAA;YAAA,OAASD,GAAA,CAAAhE,iBAAA,CAAkB,gBAAgB,CAAC;UAAA,EAAC;UACrFjC,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wFAAe;UAI7BF,EAJ6B,CAAAG,YAAA,EAAO,EACrB,EACL,EACF,EACF;UAGNH,EAAA,CAAAkC,UAAA,KAAAiE,2CAAA,kBAAmD;UAkEjDnG,EADF,CAAAC,cAAA,cAAiC,cACL;UAAAD,EAAA,CAAAE,MAAA,sHAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnDH,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAkC,UAAA,KAAAkE,gDAAA,wBAGqD;UA+BzDpG,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAAmC,cACP;UAAAD,EAAA,CAAAE,MAAA,iFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI1CH,EAFF,CAAAC,cAAA,eAAgC,oBAEgD;UAA1CD,EAAA,CAAAgB,UAAA,mBAAAqF,gEAAA;YAAA,OAASJ,GAAA,CAAAhE,iBAAA,CAAkB,YAAY,CAAC;UAAA,EAAC;UAEzEjC,EADF,CAAAC,cAAA,wBAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oGAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,gMAAkC;UAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACxB,EACV;UAEXH,EAAA,CAAAC,cAAA,oBAAkF;UAA9CD,EAAA,CAAAgB,UAAA,mBAAAsF,gEAAA;YAAA,OAASL,GAAA,CAAAhE,iBAAA,CAAkB,gBAAgB,CAAC;UAAA,EAAC;UAE7EjC,EADF,CAAAC,cAAA,wBAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,wFAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0IAAyB;UAEhCF,EAFgC,CAAAG,YAAA,EAAI,EACf,EACV;UAEXH,EAAA,CAAAC,cAAA,oBAAsF;UAAlDD,EAAA,CAAAgB,UAAA,mBAAAuF,gEAAA;YAAA,OAASN,GAAA,CAAAhE,iBAAA,CAAkB,oBAAoB,CAAC;UAAA,EAAC;UAEjFjC,EADF,CAAAC,cAAA,wBAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qLAAiC;UAExCF,EAFwC,CAAAG,YAAA,EAAI,EACvB,EACV;UAEXH,EAAA,CAAAC,cAAA,oBAAuF;UAAnDD,EAAA,CAAAgB,UAAA,mBAAAwF,gEAAA;YAAA,OAASP,GAAA,CAAAhE,iBAAA,CAAkB,qBAAqB,CAAC;UAAA,EAAC;UAElFjC,EADF,CAAAC,cAAA,wBAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,gKAA2B;UAKtCF,EALsC,CAAAG,YAAA,EAAI,EACjB,EACV,EAEP,EACF;UAGNH,EAAA,CAAAkC,UAAA,KAAAuE,2CAAA,kBAA+C;UAKjDzG,EAAA,CAAAG,YAAA,EAAM;;;UAzJ6BH,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAW,UAAA,UAAAsF,GAAA,CAAAtD,SAAA,CAAgB;UAsEf3C,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAW,UAAA,YAAAsF,GAAA,CAAAlD,kBAAA,CAAqB;UA8EzB/C,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAW,UAAA,SAAAsF,GAAA,CAAAtD,SAAA,CAAe;;;qBDtJ3ClD,YAAY,EAAAiH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZnH,aAAa,EAAAoH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EACbzH,eAAe,EAAA0H,EAAA,CAAAC,SAAA,EACf1H,aAAa,EAAA2H,EAAA,CAAAC,OAAA,EACb3H,aAAa,EACbC,wBAAwB,EAAA2H,EAAA,CAAAC,kBAAA,EACxB3H,iBAAiB;MAAA4H,MAAA;IAAA;;SAKRnF,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}