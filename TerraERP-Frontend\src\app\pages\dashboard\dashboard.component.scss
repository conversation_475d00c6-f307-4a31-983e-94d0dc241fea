/* Dashboard Styles */
.dashboard-container {
  padding: 20px;
  direction: rtl;
}

/* Page Header */
.page-header {
  margin-bottom: 30px;

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      color: #3498db;
    }
  }

  .page-subtitle {
    color: #7f8c8d;
    margin: 0;
    font-size: 16px;
  }
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      margin: 0 0 4px 0;
      color: #2c3e50;
    }

    .stat-label {
      font-size: 14px;
      color: #7f8c8d;
      margin: 0 0 8px 0;
    }

    .stat-change {
      font-size: 12px;
      font-weight: 500;

      &.positive {
        color: #27ae60;
      }

      &.negative {
        color: #e74c3c;
      }
    }
  }

  &.customers .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.suppliers .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  &.products .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.sales .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #3498db;
      }
    }

    .view-all-link {
      color: #3498db;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .card-content {
    padding: 24px;
  }
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
  }

  th {
    font-weight: 600;
    color: #2c3e50;
    background-color: #f8f9fa;
  }

  td {
    color: #495057;
  }
}

.badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.badge-completed {
    background-color: #d4edda;
    color: #155724;
  }

  &.badge-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  &.badge-cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }
}

/* Low Stock List */
.low-stock-list {
  .low-stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;

    &:last-child {
      border-bottom: none;
    }

    .product-info {
      .product-name {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
        margin: 0 0 4px 0;
      }

      .product-code {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0;
      }
    }

    .stock-info {
      font-size: 14px;
      font-weight: 500;

      .current-stock {
        color: #e74c3c;
      }

      .min-stock {
        color: #7f8c8d;
      }
    }
  }
}

/* Quick Actions */
.quick-actions {
  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .action-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #495057;
    transition: all 0.2s;

    &:hover {
      border-color: #3498db;
      color: #3498db;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    i {
      font-size: 24px;
      margin-bottom: 8px;
      display: block;
    }

    span {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
