<!-- Terra Retail ERP - Professional Layout -->
<div class="app-layout" [class.sidebar-collapsed]="sidebarCollapsed">
  
  <!-- Top Navigation Bar -->
  <header class="top-navbar">
    <div class="navbar-content">
      <!-- Left Section -->
      <div class="navbar-left">
        <button mat-icon-button (click)="toggleSidebar()" class="sidebar-toggle">
          <mat-icon>{{ sidebarCollapsed ? 'menu' : 'menu_open' }}</mat-icon>
        </button>
        
        <div class="logo-section">
          <img src="assets/images/logo.svg" alt="Terra Retail" class="logo" />
          <span class="logo-text" *ngIf="!sidebarCollapsed">Terra Retail ERP</span>
        </div>
      </div>

      <!-- Center Section - Search -->
      <div class="navbar-center">
        <div class="global-search">
          <mat-form-field appearance="outline" class="search-field">
            <mat-icon matPrefix>search</mat-icon>
            <input matInput placeholder="البحث في النظام..." [(ngModel)]="globalSearchTerm">
          </mat-form-field>
        </div>
      </div>

      <!-- Right Section -->
      <div class="navbar-right">
        <!-- Notifications -->
        <button mat-icon-button [matMenuTriggerFor]="notificationsMenu" class="notification-btn">
          <mat-icon matBadge="3" matBadgeColor="warn">notifications</mat-icon>
        </button>
        
        <!-- User Profile -->
        <button mat-button [matMenuTriggerFor]="userMenu" class="user-profile">
          <div class="user-avatar">
            <img src="assets/images/user-avatar.svg" alt="User" />
          </div>
          <div class="user-info" *ngIf="!sidebarCollapsed">
            <span class="user-name">أحمد محمد</span>
            <span class="user-role">مدير النظام</span>
          </div>
          <mat-icon>keyboard_arrow_down</mat-icon>
        </button>

        <!-- Settings -->
        <button mat-icon-button class="settings-btn">
          <mat-icon>settings</mat-icon>
        </button>
      </div>
    </div>
  </header>

  <!-- Sidebar Navigation -->
  <aside class="sidebar" [class.collapsed]="sidebarCollapsed">
    <nav class="sidebar-nav">
      
      <!-- Main Navigation -->
      <div class="nav-section">
        <h3 class="nav-section-title" *ngIf="!sidebarCollapsed">القائمة الرئيسية</h3>
        
        <a routerLink="/dashboard" routerLinkActive="active" class="nav-item">
          <mat-icon>dashboard</mat-icon>
          <span *ngIf="!sidebarCollapsed">لوحة التحكم</span>
        </a>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('sales')" 
                  [class.expanded]="expandedGroups.has('sales')">
            <mat-icon>point_of_sale</mat-icon>
            <span *ngIf="!sidebarCollapsed">المبيعات</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('sales') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('sales') && !sidebarCollapsed">
            <a routerLink="/pos" routerLinkActive="active" class="nav-subitem">
              <mat-icon>shopping_cart</mat-icon>
              <span>نقطة البيع</span>
            </a>
            <a routerLink="/sales" routerLinkActive="active" class="nav-subitem">
              <mat-icon>receipt_long</mat-icon>
              <span>فواتير المبيعات</span>
            </a>
            <a routerLink="/sales-returns" routerLinkActive="active" class="nav-subitem">
              <mat-icon>assignment_return</mat-icon>
              <span>مرتجعات المبيعات</span>
            </a>
          </div>
        </div>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('inventory')" 
                  [class.expanded]="expandedGroups.has('inventory')">
            <mat-icon>inventory_2</mat-icon>
            <span *ngIf="!sidebarCollapsed">المخزون</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('inventory') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('inventory') && !sidebarCollapsed">
            <a routerLink="/products" routerLinkActive="active" class="nav-subitem">
              <mat-icon>category</mat-icon>
              <span>المنتجات</span>
            </a>
            <a routerLink="/categories" routerLinkActive="active" class="nav-subitem">
              <mat-icon>label</mat-icon>
              <span>التصنيفات</span>
            </a>
            <a routerLink="/inventory-movements" routerLinkActive="active" class="nav-subitem">
              <mat-icon>swap_horiz</mat-icon>
              <span>حركات المخزون</span>
            </a>
          </div>
        </div>

        <a routerLink="/customers" routerLinkActive="active" class="nav-item">
          <mat-icon>people</mat-icon>
          <span *ngIf="!sidebarCollapsed">العملاء</span>
        </a>

        <a routerLink="/suppliers" routerLinkActive="active" class="nav-item">
          <mat-icon>local_shipping</mat-icon>
          <span *ngIf="!sidebarCollapsed">الموردين</span>
        </a>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('purchases')" 
                  [class.expanded]="expandedGroups.has('purchases')">
            <mat-icon>shopping_bag</mat-icon>
            <span *ngIf="!sidebarCollapsed">المشتريات</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('purchases') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('purchases') && !sidebarCollapsed">
            <a routerLink="/purchases" routerLinkActive="active" class="nav-subitem">
              <mat-icon>receipt</mat-icon>
              <span>فواتير المشتريات</span>
            </a>
            <a routerLink="/purchase-orders" routerLinkActive="active" class="nav-subitem">
              <mat-icon>list_alt</mat-icon>
              <span>أوامر الشراء</span>
            </a>
          </div>
        </div>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('finance')" 
                  [class.expanded]="expandedGroups.has('finance')">
            <mat-icon>account_balance</mat-icon>
            <span *ngIf="!sidebarCollapsed">المالية</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('finance') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('finance') && !sidebarCollapsed">
            <a routerLink="/accounts" routerLinkActive="active" class="nav-subitem">
              <mat-icon>account_balance_wallet</mat-icon>
              <span>الحسابات</span>
            </a>
            <a routerLink="/journal-entries" routerLinkActive="active" class="nav-subitem">
              <mat-icon>book</mat-icon>
              <span>القيود المحاسبية</span>
            </a>
            <a routerLink="/treasury" routerLinkActive="active" class="nav-subitem">
              <mat-icon>savings</mat-icon>
              <span>الخزينة</span>
            </a>
          </div>
        </div>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('hr')" 
                  [class.expanded]="expandedGroups.has('hr')">
            <mat-icon>badge</mat-icon>
            <span *ngIf="!sidebarCollapsed">الموارد البشرية</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('hr') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('hr') && !sidebarCollapsed">
            <a routerLink="/employees" routerLinkActive="active" class="nav-subitem">
              <mat-icon>person</mat-icon>
              <span>الموظفين</span>
            </a>
            <a routerLink="/payroll" routerLinkActive="active" class="nav-subitem">
              <mat-icon>payment</mat-icon>
              <span>كشوف المرتبات</span>
            </a>
            <a routerLink="/attendance" routerLinkActive="active" class="nav-subitem">
              <mat-icon>schedule</mat-icon>
              <span>الحضور والانصراف</span>
            </a>
          </div>
        </div>

        <div class="nav-group">
          <button class="nav-item expandable" (click)="toggleNavGroup('reports')" 
                  [class.expanded]="expandedGroups.has('reports')">
            <mat-icon>assessment</mat-icon>
            <span *ngIf="!sidebarCollapsed">التقارير</span>
            <mat-icon class="expand-icon" *ngIf="!sidebarCollapsed">
              {{ expandedGroups.has('reports') ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>
          
          <div class="nav-submenu" *ngIf="expandedGroups.has('reports') && !sidebarCollapsed">
            <a routerLink="/sales-reports" routerLinkActive="active" class="nav-subitem">
              <mat-icon>trending_up</mat-icon>
              <span>تقارير المبيعات</span>
            </a>
            <a routerLink="/inventory-reports" routerLinkActive="active" class="nav-subitem">
              <mat-icon>inventory</mat-icon>
              <span>تقارير المخزون</span>
            </a>
            <a routerLink="/financial-reports" routerLinkActive="active" class="nav-subitem">
              <mat-icon>pie_chart</mat-icon>
              <span>التقارير المالية</span>
            </a>
          </div>
        </div>

        <a routerLink="/settings" routerLinkActive="active" class="nav-item">
          <mat-icon>settings</mat-icon>
          <span *ngIf="!sidebarCollapsed">الإعدادات</span>
        </a>
      </div>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer" *ngIf="!sidebarCollapsed">
      <div class="system-info">
        <p class="version">الإصدار 2.0.1</p>
        <p class="copyright">© 2024 Terra Retail</p>
      </div>
    </div>
  </aside>

  <!-- Main Content Area -->
  <main class="main-content">
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </main>

  <!-- Notifications Menu -->
  <mat-menu #notificationsMenu="matMenu" class="notifications-menu">
    <div class="menu-header">
      <h3>الإشعارات</h3>
      <button mat-button color="primary">مشاهدة الكل</button>
    </div>
    <mat-divider></mat-divider>
    
    <button mat-menu-item class="notification-item">
      <mat-icon color="primary">shopping_cart</mat-icon>
      <div class="notification-content">
        <p class="notification-title">طلب جديد</p>
        <p class="notification-time">منذ 5 دقائق</p>
      </div>
    </button>
    
    <button mat-menu-item class="notification-item">
      <mat-icon color="warn">inventory</mat-icon>
      <div class="notification-content">
        <p class="notification-title">نفاد مخزون</p>
        <p class="notification-time">منذ 15 دقيقة</p>
      </div>
    </button>
  </mat-menu>

  <!-- User Menu -->
  <mat-menu #userMenu="matMenu" class="user-menu">
    <button mat-menu-item>
      <mat-icon>person</mat-icon>
      <span>الملف الشخصي</span>
    </button>
    <button mat-menu-item>
      <mat-icon>settings</mat-icon>
      <span>الإعدادات</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item>
      <mat-icon>logout</mat-icon>
      <span>تسجيل الخروج</span>
    </button>
  </mat-menu>

</div>
