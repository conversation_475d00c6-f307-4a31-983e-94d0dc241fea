using Microsoft.EntityFrameworkCore;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي لنظام Terra Retail
    /// </summary>
    public class TerraRetailDbContext : DbContext
    {
        public TerraRetailDbContext(DbContextOptions<TerraRetailDbContext> options) : base(options)
        {
        }

        #region DbSets - الجداول

        // الكيانات الأساسية
        public DbSet<Counter> Counters { get; set; }
        public DbSet<Branch> Branches { get; set; }
        public DbSet<Area> Areas { get; set; }
        public DbSet<PriceCategory> PriceCategories { get; set; }

        // العملاء
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerType> CustomerTypes { get; set; }
        public DbSet<CustomerTransaction> CustomerTransactions { get; set; }

        // المنتجات
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<ProductImage> ProductImages { get; set; }
        public DbSet<ProductCode> ProductCodes { get; set; }
        public DbSet<ProductBranchPrice> ProductBranchPrices { get; set; }
        public DbSet<ProductStock> ProductStocks { get; set; }
        public DbSet<ProductBatch> ProductBatches { get; set; }

        // المخزون
        public DbSet<StockMovement> StockMovements { get; set; }

        // المبيعات
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<SalePayment> SalePayments { get; set; }
        public DbSet<SaleReturn> SaleReturns { get; set; }
        public DbSet<SaleReturnItem> SaleReturnItems { get; set; }

        // المشتريات
        public DbSet<Purchase> Purchases { get; set; }
        public DbSet<PurchaseItem> PurchaseItems { get; set; }
        public DbSet<PurchasePayment> PurchasePayments { get; set; }
        public DbSet<PurchaseReturn> PurchaseReturns { get; set; }
        public DbSet<PurchaseReturnItem> PurchaseReturnItems { get; set; }

        // الموردين
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<ProductSupplier> ProductSuppliers { get; set; }
        public DbSet<SupplierContact> SupplierContacts { get; set; }
        public DbSet<SupplierEvaluation> SupplierEvaluations { get; set; }

        // الموظفين
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<EmployeeDocument> EmployeeDocuments { get; set; }
        public DbSet<Attendance> AttendanceRecords { get; set; }
        public DbSet<Shift> Shifts { get; set; }
        public DbSet<EmployeeShift> EmployeeShifts { get; set; }

        // الإجازات
        public DbSet<LeaveType> LeaveTypes { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeeLeaveDocument> EmployeeLeaveDocuments { get; set; }
        public DbSet<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; }

        // الرواتب
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<PayrollItem> PayrollItems { get; set; }

        // المستخدمين والصلاحيات
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserBranch> UserBranches { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }

        // طرق الدفع والخزينة
        public DbSet<PaymentMethod> PaymentMethods { get; set; }
        public DbSet<CashBox> CashBoxes { get; set; }
        public DbSet<CashTransaction> CashTransactions { get; set; }

        // المحاسبة
        public DbSet<Account> Accounts { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryItem> JournalEntryItems { get; set; }

        // المراجعة والتدقيق
        public DbSet<AuditLog> AuditLogs { get; set; }

        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تطبيق إعدادات الكيانات
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(TerraRetailDbContext).Assembly);

            // إعداد الفهارس
            ConfigureIndexes(modelBuilder);

            // إعداد القيود
            ConfigureConstraints(modelBuilder);

            // إعداد البيانات الأولية
            SeedData(modelBuilder);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس العملاء
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.CustomerCode)
                .IsUnique();

            modelBuilder.Entity<Customer>()
                .HasIndex(c => new { c.NameAr, c.BranchId });

            // فهارس المنتجات
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.ProductCode)
                .IsUnique();

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Barcode);

            modelBuilder.Entity<Product>()
                .HasIndex(p => new { p.NameAr, p.CategoryId });

            // فهارس المبيعات
            modelBuilder.Entity<Sale>()
                .HasIndex(s => s.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<Sale>()
                .HasIndex(s => new { s.InvoiceDate, s.BranchId });

            // فهارس المشتريات
            modelBuilder.Entity<Purchase>()
                .HasIndex(p => p.InvoiceNumber)
                .IsUnique();

            // فهارس المستخدمين
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            // فهارس الموظفين
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.IdentityNumber)
                .IsUnique();

            // فهارس الموردين
            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.SupplierCode)
                .IsUnique();

            // فهارس الحسابات
            modelBuilder.Entity<Account>()
                .HasIndex(a => a.AccountNumber)
                .IsUnique();

            // فهارس القيود
            modelBuilder.Entity<JournalEntry>()
                .HasIndex(j => j.EntryNumber)
                .IsUnique();

            // فهارس العدادات
            modelBuilder.Entity<Counter>()
                .HasIndex(c => new { c.CounterName, c.BranchId })
                .IsUnique();
        }

        private void ConfigureConstraints(ModelBuilder modelBuilder)
        {
            // قيود التحقق من صحة البيانات
            modelBuilder.Entity<Customer>()
                .HasCheckConstraint("CK_Customer_DiscountPercentage", "[DiscountPercentage] >= 0 AND [DiscountPercentage] <= 100");

            modelBuilder.Entity<Product>()
                .HasCheckConstraint("CK_Product_ProfitMargin", "[ProfitMargin] >= 0");

            modelBuilder.Entity<ProductBranchPrice>()
                .HasCheckConstraint("CK_ProductBranchPrice_Price", "[Price] >= 0");

            modelBuilder.Entity<Sale>()
                .HasCheckConstraint("CK_Sale_TotalAmount", "[TotalAmount] >= 0");

            modelBuilder.Entity<Purchase>()
                .HasCheckConstraint("CK_Purchase_TotalAmount", "[TotalAmount] >= 0");

            modelBuilder.Entity<Employee>()
                .HasCheckConstraint("CK_Employee_BasicSalary", "[BasicSalary] >= 0");

            modelBuilder.Entity<JournalEntryItem>()
                .HasCheckConstraint("CK_JournalEntryItem_DebitCredit", 
                    "([DebitAmount] > 0 AND [CreditAmount] = 0) OR ([DebitAmount] = 0 AND [CreditAmount] > 0)");
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // بيانات أولية للعدادات
            modelBuilder.Entity<Counter>().HasData(
                new Counter { Id = 1, CounterName = "CUSTOMER", Prefix = "CUS", CurrentValue = 0, NumberLength = 6, CreatedAt = DateTime.UtcNow },
                new Counter { Id = 2, CounterName = "PRODUCT", Prefix = "PRD", CurrentValue = 0, NumberLength = 6, CreatedAt = DateTime.UtcNow },
                new Counter { Id = 3, CounterName = "SALE", Prefix = "SAL", CurrentValue = 0, NumberLength = 8, CreatedAt = DateTime.UtcNow },
                new Counter { Id = 4, CounterName = "PURCHASE", Prefix = "PUR", CurrentValue = 0, NumberLength = 8, CreatedAt = DateTime.UtcNow },
                new Counter { Id = 5, CounterName = "SUPPLIER", Prefix = "SUP", CurrentValue = 0, NumberLength = 6, CreatedAt = DateTime.UtcNow },
                new Counter { Id = 6, CounterName = "EMPLOYEE", Prefix = "EMP", CurrentValue = 0, NumberLength = 6, CreatedAt = DateTime.UtcNow }
            );

            // بيانات أولية لأنواع العملاء
            modelBuilder.Entity<CustomerType>().HasData(
                new CustomerType { Id = 1, NameAr = "عميل عادي", NameEn = "Regular Customer", DefaultDiscountPercentage = 0, IsActive = true, CreatedAt = DateTime.UtcNow },
                new CustomerType { Id = 2, NameAr = "عميل جملة", NameEn = "Wholesale Customer", DefaultDiscountPercentage = 5, IsActive = true, CreatedAt = DateTime.UtcNow },
                new CustomerType { Id = 3, NameAr = "عميل VIP", NameEn = "VIP Customer", DefaultDiscountPercentage = 10, IsActive = true, CreatedAt = DateTime.UtcNow }
            );

            // بيانات أولية لفئات الأسعار
            modelBuilder.Entity<PriceCategory>().HasData(
                new PriceCategory { Id = 1, NameAr = "سعر التجزئة", NameEn = "Retail Price", Code = "RETAIL", IsDefault = true, IsActive = true, CreatedAt = DateTime.UtcNow },
                new PriceCategory { Id = 2, NameAr = "سعر الجملة", NameEn = "Wholesale Price", Code = "WHOLESALE", PriceAdjustmentPercentage = -10, IsActive = true, CreatedAt = DateTime.UtcNow },
                new PriceCategory { Id = 3, NameAr = "سعر VIP", NameEn = "VIP Price", Code = "VIP", PriceAdjustmentPercentage = -15, IsActive = true, CreatedAt = DateTime.UtcNow }
            );

            // بيانات أولية لوحدات القياس
            modelBuilder.Entity<Unit>().HasData(
                new Unit { Id = 1, NameAr = "قطعة", NameEn = "Piece", Symbol = "PC", UnitType = UnitType.Quantity, IsDefault = true, IsActive = true, CreatedAt = DateTime.UtcNow },
                new Unit { Id = 2, NameAr = "كيلوجرام", NameEn = "Kilogram", Symbol = "KG", UnitType = UnitType.Weight, IsActive = true, CreatedAt = DateTime.UtcNow },
                new Unit { Id = 3, NameAr = "متر", NameEn = "Meter", Symbol = "M", UnitType = UnitType.Length, IsActive = true, CreatedAt = DateTime.UtcNow },
                new Unit { Id = 4, NameAr = "لتر", NameEn = "Liter", Symbol = "L", UnitType = UnitType.Volume, IsActive = true, CreatedAt = DateTime.UtcNow }
            );

            // بيانات أولية لطرق الدفع
            modelBuilder.Entity<PaymentMethod>().HasData(
                new PaymentMethod { Id = 1, NameAr = "نقدي", NameEn = "Cash", Code = "CASH", PaymentType = PaymentMethodType.Cash, IsDefault = true, IsActive = true, CreatedAt = DateTime.UtcNow },
                new PaymentMethod { Id = 2, NameAr = "بطاقة ائتمان", NameEn = "Credit Card", Code = "CREDIT", PaymentType = PaymentMethodType.CreditCard, IsActive = true, CreatedAt = DateTime.UtcNow },
                new PaymentMethod { Id = 3, NameAr = "تحويل بنكي", NameEn = "Bank Transfer", Code = "TRANSFER", PaymentType = PaymentMethodType.BankTransfer, IsActive = true, CreatedAt = DateTime.UtcNow },
                new PaymentMethod { Id = 4, NameAr = "آجل", NameEn = "Credit", Code = "CREDIT_TERM", PaymentType = PaymentMethodType.Credit, IsActive = true, CreatedAt = DateTime.UtcNow }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                }
            }
        }
    }
}
