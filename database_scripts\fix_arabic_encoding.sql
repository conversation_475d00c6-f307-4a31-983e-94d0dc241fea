-- Terra Retail ERP - Fix Arabic Encoding
-- إصلاح مشكلة encoding النصوص العربية

USE [TerraRetailERP]
GO

-- تحديث البيانات الموجودة بدلاً من حذفها (لتجنب foreign key constraints)
-- سنقوم بتحديث النصوص العربية مباشرة

-- تحديث أنواع الموردين الموجودة
UPDATE SupplierTypes SET NameAr = N'مورد محلي', NameEn = 'Local Supplier', Description = N'مورد من داخل البلد' WHERE Id = 1
UPDATE SupplierTypes SET NameAr = N'مورد دولي', NameEn = 'International Supplier', Description = N'مورد من خارج البلد' WHERE Id = 2
UPDATE SupplierTypes SET NameAr = N'مورد خدمات', NameEn = 'Service Provider', Description = N'مقدم خدمات وليس منتجات' WHERE Id = 3

-- إضافة أنواع موردين جديدة إذا لم تكن موجودة
IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 4)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد مواد خام', 'Raw Materials Supplier', N'مورد المواد الخام والمكونات', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 5)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد تجزئة', 'Retail Supplier', N'مورد منتجات جاهزة للبيع', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 6)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد جملة', 'Wholesale Supplier', N'مورد بكميات كبيرة', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 7)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد معدات', 'Equipment Supplier', N'مورد المعدات والآلات', 1, GETDATE(), GETDATE())

IF NOT EXISTS (SELECT 1 FROM SupplierTypes WHERE Id = 8)
    INSERT INTO SupplierTypes (NameAr, NameEn, Description, IsActive, CreatedAt, UpdatedAt)
    VALUES (N'مورد تقنية', 'Technology Supplier', N'مورد الحلول التقنية والبرمجيات', 1, GETDATE(), GETDATE())

-- إعادة إدراج المحافظات المصرية بـ encoding صحيح
SET IDENTITY_INSERT Areas ON

INSERT INTO Areas (Id, NameAr, NameEn, Code, IsActive, CreatedAt, UpdatedAt)
VALUES 
-- محافظات القاهرة الكبرى
(1, N'القاهرة', 'Cairo', 'CAI', 1, GETDATE(), GETDATE()),
(2, N'الجيزة', 'Giza', 'GIZ', 1, GETDATE(), GETDATE()),
(3, N'القليوبية', 'Qalyubia', 'QLY', 1, GETDATE(), GETDATE()),

-- محافظات الإسكندرية والساحل الشمالي
(4, N'الإسكندرية', 'Alexandria', 'ALX', 1, GETDATE(), GETDATE()),
(5, N'البحيرة', 'Beheira', 'BHR', 1, GETDATE(), GETDATE()),
(6, N'مطروح', 'Matrouh', 'MTR', 1, GETDATE(), GETDATE()),

-- محافظات الدلتا
(7, N'كفر الشيخ', 'Kafr El Sheikh', 'KFS', 1, GETDATE(), GETDATE()),
(8, N'الغربية', 'Gharbia', 'GHR', 1, GETDATE(), GETDATE()),
(9, N'المنوفية', 'Monufia', 'MNF', 1, GETDATE(), GETDATE()),
(10, N'الدقهلية', 'Dakahlia', 'DKH', 1, GETDATE(), GETDATE()),
(11, N'دمياط', 'Damietta', 'DMT', 1, GETDATE(), GETDATE()),
(12, N'الشرقية', 'Sharqia', 'SHR', 1, GETDATE(), GETDATE()),
(13, N'الإسماعيلية', 'Ismailia', 'ISM', 1, GETDATE(), GETDATE()),
(14, N'بورسعيد', 'Port Said', 'PTS', 1, GETDATE(), GETDATE()),
(15, N'السويس', 'Suez', 'SUZ', 1, GETDATE(), GETDATE()),

-- محافظات الصعيد
(16, N'الفيوم', 'Fayoum', 'FYM', 1, GETDATE(), GETDATE()),
(17, N'بني سويف', 'Beni Suef', 'BNS', 1, GETDATE(), GETDATE()),
(18, N'المنيا', 'Minya', 'MNY', 1, GETDATE(), GETDATE()),
(19, N'أسيوط', 'Asyut', 'AST', 1, GETDATE(), GETDATE()),
(20, N'سوهاج', 'Sohag', 'SOH', 1, GETDATE(), GETDATE()),
(21, N'قنا', 'Qena', 'QNA', 1, GETDATE(), GETDATE()),
(22, N'الأقصر', 'Luxor', 'LXR', 1, GETDATE(), GETDATE()),
(23, N'أسوان', 'Aswan', 'ASW', 1, GETDATE(), GETDATE()),

-- محافظات سيناء والبحر الأحمر
(24, N'البحر الأحمر', 'Red Sea', 'RDS', 1, GETDATE(), GETDATE()),
(25, N'جنوب سيناء', 'South Sinai', 'SSI', 1, GETDATE(), GETDATE()),
(26, N'شمال سيناء', 'North Sinai', 'NSI', 1, GETDATE(), GETDATE()),

-- المحافظات الحدودية
(27, N'الوادي الجديد', 'New Valley', 'NVL', 1, GETDATE(), GETDATE())

SET IDENTITY_INSERT Areas OFF

-- تحديث بيانات الموردين الموجودة
UPDATE Suppliers 
SET 
    SupplierTypeId = CASE 
        WHEN Id <= 3 THEN 1  -- مورد محلي
        WHEN Id <= 6 THEN 2  -- مورد دولي  
        WHEN Id <= 8 THEN 5  -- مورد تجزئة
        ELSE 6               -- مورد جملة
    END,
    AreaId = CASE 
        WHEN Id = 1 THEN 1   -- القاهرة
        WHEN Id = 2 THEN 4   -- الإسكندرية
        WHEN Id = 3 THEN 2   -- الجيزة
        WHEN Id = 4 THEN 12  -- الشرقية
        WHEN Id = 5 THEN 10  -- الدقهلية
        WHEN Id = 6 THEN 8   -- الغربية
        WHEN Id = 7 THEN 18  -- المنيا
        WHEN Id = 8 THEN 19  -- أسيوط
        WHEN Id = 9 THEN 3   -- القليوبية
        ELSE 1               -- القاهرة (افتراضي)
    END
WHERE SupplierTypeId IS NULL OR AreaId IS NULL

-- التحقق من البيانات
SELECT 'أنواع الموردين' as DataType, COUNT(*) as Count FROM SupplierTypes WHERE IsActive = 1
UNION ALL
SELECT 'المحافظات' as DataType, COUNT(*) as Count FROM Areas WHERE IsActive = 1
UNION ALL
SELECT 'الموردين' as DataType, COUNT(*) as Count FROM Suppliers WHERE IsActive = 1

-- عرض أنواع الموردين
SELECT Id, NameAr, NameEn FROM SupplierTypes WHERE IsActive = 1 ORDER BY Id

-- عرض المحافظات
SELECT Id, NameAr, NameEn, Code FROM Areas WHERE IsActive = 1 ORDER BY Id

PRINT N'تم إصلاح encoding النصوص العربية بنجاح!'
