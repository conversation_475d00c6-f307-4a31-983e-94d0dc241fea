{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, DOCUMENT, EventEmitter, Input, Output, NgModule } from '@angular/core';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  _attachedHost;\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  /** The type of the component that will be instantiated for attachment. */\n  component;\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This is different from where the component *renders*, which is determined by the PortalOutlet.\n   * The origin is necessary when the host is outside of the Angular application context.\n   */\n  viewContainerRef;\n  /** Injector used for the instantiation of the component. */\n  injector;\n  /**\n   * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n   */\n  projectableNodes;\n  constructor(component, viewContainerRef, injector, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  templateRef;\n  viewContainerRef;\n  context;\n  injector;\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  /** DOM node hosting the portal's content. */\n  element;\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  /** The portal currently attached to the host. */\n  _attachedPortal;\n  /** A function that will permanently dispose this host. */\n  _disposeFn;\n  /** Whether this host has already been permanently disposed. */\n  _isDisposed = false;\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n  attachDomPortal = null;\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  outletElement;\n  _appRef;\n  _defaultInjector;\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement, _appRef, _defaultInjector) {\n    super();\n    this.outletElement = outletElement;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {\n        optional: true\n      }) || undefined;\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef;\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Attaches a DOM portal by transferring its content into the outlet.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    const element = portal.element;\n    if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('DOM portal content must be attached to a parent node.');\n    }\n    // Anchor used to save the element's previous position so\n    // that we can restore it when the portal is detached.\n    const anchorNode = this.outletElement.ownerDocument.createComment('dom-portal');\n    element.parentNode.insertBefore(anchorNode, element);\n    this.outletElement.appendChild(element);\n    this._attachedPortal = portal;\n    super.setDisposeFn(() => {\n      // We can't use `replaceWith` here because IE doesn't support it.\n      if (anchorNode.parentNode) {\n        anchorNode.parentNode.replaceChild(element, anchorNode);\n      }\n    });\n  };\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nlet CdkPortal = /*#__PURE__*/(() => {\n  class CdkPortal extends TemplatePortal {\n    constructor() {\n      const templateRef = inject(TemplateRef);\n      const viewContainerRef = inject(ViewContainerRef);\n      super(templateRef, viewContainerRef);\n    }\n    static ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortal)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortal,\n      selectors: [[\"\", \"cdkPortal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkPortal;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nlet TemplatePortalDirective = /*#__PURE__*/(() => {\n  class TemplatePortalDirective extends CdkPortal {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵTemplatePortalDirective_BaseFactory;\n      return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n        return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TemplatePortalDirective,\n      selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return TemplatePortalDirective;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nlet CdkPortalOutlet = /*#__PURE__*/(() => {\n  class CdkPortalOutlet extends BasePortalOutlet {\n    _moduleRef = inject(NgModuleRef, {\n      optional: true\n    });\n    _document = inject(DOCUMENT);\n    _viewContainerRef = inject(ViewContainerRef);\n    /** Whether the portal component is initialized. */\n    _isInitialized = false;\n    /** Reference to the currently-attached component/view ref. */\n    _attachedRef;\n    constructor() {\n      super();\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n      return this._attachedPortal;\n    }\n    set portal(portal) {\n      // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n      // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n      // and attach a portal programmatically in the parent component. When Angular does the first CD\n      // round, it will fire the setter with empty string, causing the user's content to be cleared.\n      if (this.hasAttached() && !portal && !this._isInitialized) {\n        return;\n      }\n      if (this.hasAttached()) {\n        super.detach();\n      }\n      if (portal) {\n        super.attach(portal);\n      }\n      this._attachedPortal = portal || null;\n    }\n    /** Emits when a portal is attached to the outlet. */\n    attached = new EventEmitter();\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n      return this._attachedRef;\n    }\n    ngOnInit() {\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      super.dispose();\n      this._attachedRef = this._attachedPortal = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n      portal.setAttachedHost(this);\n      // If the portal specifies an origin, use that as the logical location of the component\n      // in the application tree. Otherwise use the location of this PortalOutlet.\n      const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n      const ref = viewContainerRef.createComponent(portal.component, {\n        index: viewContainerRef.length,\n        injector: portal.injector || viewContainerRef.injector,\n        projectableNodes: portal.projectableNodes || undefined,\n        ngModuleRef: this._moduleRef || undefined\n      });\n      // If we're using a view container that's different from the injected one (e.g. when the portal\n      // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n      // inside of the alternate view container.\n      if (viewContainerRef !== this._viewContainerRef) {\n        this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n      }\n      super.setDisposeFn(() => ref.destroy());\n      this._attachedPortal = portal;\n      this._attachedRef = ref;\n      this.attached.emit(ref);\n      return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n      portal.setAttachedHost(this);\n      const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n        injector: portal.injector\n      });\n      super.setDisposeFn(() => this._viewContainerRef.clear());\n      this._attachedPortal = portal;\n      this._attachedRef = viewRef;\n      this.attached.emit(viewRef);\n      return viewRef;\n    }\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = portal => {\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n      this._getRootNode().appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n      const nativeElement = this._viewContainerRef.element.nativeElement;\n      // The directive could be set on a template which will result in a comment\n      // node being the root. Use the comment's parent node if that is the case.\n      return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n    }\n    static ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortalOutlet)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortalOutlet,\n      selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalOutlet\", \"portal\"]\n      },\n      outputs: {\n        attached: \"attached\"\n      },\n      exportAs: [\"cdkPortalOutlet\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkPortalOutlet;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nlet PortalHostDirective = /*#__PURE__*/(() => {\n  class PortalHostDirective extends CdkPortalOutlet {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵPortalHostDirective_BaseFactory;\n      return function PortalHostDirective_Factory(__ngFactoryType__) {\n        return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PortalHostDirective,\n      selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalHost\", \"portal\"]\n      },\n      exportAs: [\"cdkPortalHost\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return PortalHostDirective;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PortalModule = /*#__PURE__*/(() => {\n  class PortalModule {\n    static ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PortalModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PortalModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return PortalModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalOutlet, Portal, PortalHostDirective, PortalModule, TemplatePortal, TemplatePortalDirective };", "map": {"version": 3, "names": ["i0", "ElementRef", "NgModuleRef", "EnvironmentInjector", "createComponent", "Injector", "inject", "TemplateRef", "ViewContainerRef", "Directive", "DOCUMENT", "EventEmitter", "Input", "Output", "NgModule", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "_attachedHost", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "component", "viewContainerRef", "injector", "projectableNodes", "constructor", "TemplatePortal", "templateRef", "context", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_attachedPortal", "_disposeFn", "_isDisposed", "portal", "attachComponentPortal", "attachTemplatePortal", "attachDomPortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "DomPortalOutlet", "outletElement", "_appRef", "_defaultInjector", "componentRef", "ngModuleRef", "get", "optional", "index", "length", "destroy", "appRef", "elementInjector", "NULL", "environmentInjector", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "append<PERSON><PERSON><PERSON>", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "indexOf", "remove", "parentNode", "anchorNode", "ownerDocument", "createComment", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "CdkPortal", "ɵfac", "CdkPortal_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "features", "ɵɵInheritDefinitionFeature", "TemplatePortalDirective", "ɵTemplatePortalDirective_BaseFactory", "TemplatePortalDirective_Factory", "ɵɵgetInheritedFactory", "ɵɵProvidersFeature", "provide", "useExisting", "CdkPortalOutlet", "_moduleRef", "_document", "_viewContainerRef", "_isInitialized", "_attachedRef", "attached", "attachedRef", "ngOnInit", "ngOnDestroy", "ref", "_getRootNode", "emit", "clear", "nodeType", "ELEMENT_NODE", "CdkPortalOutlet_Factory", "inputs", "outputs", "PortalHostDirective", "ɵPortalHostDirective_BaseFactory", "PortalHostDirective_Factory", "PortalModule", "PortalModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/portal.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, DOCUMENT, EventEmitter, Input, Output, NgModule } from '@angular/core';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    _attachedHost;\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    /** The type of the component that will be instantiated for attachment. */\n    component;\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This is different from where the component *renders*, which is determined by the PortalOutlet.\n     * The origin is necessary when the host is outside of the Angular application context.\n     */\n    viewContainerRef;\n    /** Injector used for the instantiation of the component. */\n    injector;\n    /**\n     * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n     */\n    projectableNodes;\n    constructor(component, viewContainerRef, injector, projectableNodes) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.projectableNodes = projectableNodes;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    templateRef;\n    viewContainerRef;\n    context;\n    injector;\n    constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    templateRef, \n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    viewContainerRef, \n    /** Contextual data to be passed in to the embedded view. */\n    context, \n    /** The injector to use for the embedded view. */\n    injector) {\n        super();\n        this.templateRef = templateRef;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n        this.injector = injector;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    /** DOM node hosting the portal's content. */\n    element;\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    /** The portal currently attached to the host. */\n    _attachedPortal;\n    /** A function that will permanently dispose this host. */\n    _disposeFn;\n    /** Whether this host has already been permanently disposed. */\n    _isDisposed = false;\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    attachDomPortal = null;\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    outletElement;\n    _appRef;\n    _defaultInjector;\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, _appRef, _defaultInjector) {\n        super();\n        this.outletElement = outletElement;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            const injector = portal.injector || portal.viewContainerRef.injector;\n            const ngModuleRef = injector.get(NgModuleRef, null, { optional: true }) || undefined;\n            componentRef = portal.viewContainerRef.createComponent(portal.component, {\n                index: portal.viewContainerRef.length,\n                injector,\n                ngModuleRef,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            const appRef = this._appRef;\n            const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n            const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n            componentRef = createComponent(portal.component, {\n                elementInjector,\n                environmentInjector,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (appRef.viewCount > 0) {\n                    appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this.outletElement.ownerDocument.createComment('dom-portal');\n        element.parentNode.insertBefore(anchorNode, element);\n        this.outletElement.appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            // We can't use `replaceWith` here because IE doesn't support it.\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        super(templateRef, viewContainerRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkPortal, isStandalone: true, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: TemplatePortalDirective, isStandalone: true, selector: \"[cdk-portal], [portal]\", providers: [\n            {\n                provide: CdkPortal,\n                useExisting: TemplatePortalDirective,\n            },\n        ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    _moduleRef = inject(NgModuleRef, { optional: true });\n    _document = inject(DOCUMENT);\n    _viewContainerRef = inject(ViewContainerRef);\n    /** Whether the portal component is initialized. */\n    _isInitialized = false;\n    /** Reference to the currently-attached component/view ref. */\n    _attachedRef;\n    constructor() {\n        super();\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Emits when a portal is attached to the outlet. */\n    attached = new EventEmitter();\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedRef = this._attachedPortal = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const ref = viewContainerRef.createComponent(portal.component, {\n            index: viewContainerRef.length,\n            injector: portal.injector || viewContainerRef.injector,\n            projectableNodes: portal.projectableNodes || undefined,\n            ngModuleRef: this._moduleRef || undefined,\n        });\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this._document.createComment('dom-portal');\n        portal.setAttachedHost(this);\n        element.parentNode.insertBefore(anchorNode, element);\n        this._getRootNode().appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkPortalOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkPortalOutlet, isStandalone: true, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                }]\n        }], ctorParameters: () => [], propDecorators: { portal: [{\n                type: Input,\n                args: ['cdkPortalOutlet']\n            }], attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: PortalHostDirective, isStandalone: true, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n            {\n                provide: CdkPortalOutlet,\n                useExisting: PortalHostDirective,\n            },\n        ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: [{ name: 'portal', alias: 'cdkPortalHost' }],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                }]\n        }] });\nclass PortalModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalModule, imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\nexport { BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalOutlet, Portal, PortalHostDirective, PortalModule, TemplatePortal, TemplatePortalDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;;AAE1M;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,KAAK,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC,MAAMD,KAAK,CAAC,oCAAoC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASE,qCAAqCA,CAAA,EAAG;EAC7C,MAAMF,KAAK,CAAC,6CAA6C,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,MAAMH,KAAK,CAAC,+EAA+E,GACvF,wCAAwC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASI,0BAA0BA,CAAA,EAAG;EAClC,MAAMJ,KAAK,CAAC,sDAAsD,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAASK,0BAA0BA,CAAA,EAAG;EAClC,MAAML,KAAK,CAAC,8DAA8D,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA,MAAMM,MAAM,CAAC;EACTC,aAAa;EACb;EACAC,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAID,IAAI,IAAI,IAAI,EAAE;QACdL,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAIK,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QACpBV,+BAA+B,CAAC,CAAC;MACrC;IACJ;IACA,IAAI,CAACM,aAAa,GAAGE,IAAI;IACzB,OAAOA,IAAI,CAACD,MAAM,CAAC,IAAI,CAAC;EAC5B;EACA;EACAI,MAAMA,CAAA,EAAG;IACL,IAAIH,IAAI,GAAG,IAAI,CAACF,aAAa;IAC7B,IAAIE,IAAI,IAAI,IAAI,EAAE;MACd,IAAI,CAACF,aAAa,GAAG,IAAI;MACzBE,IAAI,CAACG,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,OAAOF,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpDL,0BAA0B,CAAC,CAAC;IAChC;EACJ;EACA;EACA,IAAIQ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,aAAa,IAAI,IAAI;EACrC;EACA;AACJ;AACA;AACA;EACIO,eAAeA,CAACL,IAAI,EAAE;IAClB,IAAI,CAACF,aAAa,GAAGE,IAAI;EAC7B;AACJ;AACA;AACA;AACA;AACA,MAAMM,eAAe,SAAST,MAAM,CAAC;EACjC;EACAU,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;EACAC,QAAQ;EACR;AACJ;AACA;EACIC,gBAAgB;EAChBC,WAAWA,CAACJ,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,gBAAgB,EAAE;IACjE,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;AACJ;AACA;AACA;AACA;AACA,MAAME,cAAc,SAASf,MAAM,CAAC;EAChCgB,WAAW;EACXL,gBAAgB;EAChBM,OAAO;EACPL,QAAQ;EACRE,WAAWA,CACX;EACAE,WAAW,EACX;EACAL,gBAAgB,EAChB;EACAM,OAAO,EACP;EACAL,QAAQ,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACL,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACM,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIM,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,WAAW,CAACG,UAAU;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIjB,MAAMA,CAACC,IAAI,EAAEc,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,KAAK,CAACf,MAAM,CAACC,IAAI,CAAC;EAC7B;EACAG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACW,OAAO,GAAGG,SAAS;IACxB,OAAO,KAAK,CAACd,MAAM,CAAC,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,SAAS,SAASrB,MAAM,CAAC;EAC3B;EACAsB,OAAO;EACPR,WAAWA,CAACQ,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO,YAAY3C,UAAU,GAAG2C,OAAO,CAACC,aAAa,GAAGD,OAAO;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EACnB;EACAC,eAAe;EACf;EACAC,UAAU;EACV;EACAC,WAAW,GAAG,KAAK;EACnB;EACAtB,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACoB,eAAe;EACjC;EACA;EACAvB,MAAMA,CAAC0B,MAAM,EAAE;IACX,IAAI,OAAOxB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACwB,MAAM,EAAE;QACTnC,oBAAoB,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACY,WAAW,CAAC,CAAC,EAAE;QACpBV,+BAA+B,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAACgC,WAAW,EAAE;QAClB/B,qCAAqC,CAAC,CAAC;MAC3C;IACJ;IACA,IAAIgC,MAAM,YAAYnB,eAAe,EAAE;MACnC,IAAI,CAACgB,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;IAC7C,CAAC,MACI,IAAIA,MAAM,YAAYb,cAAc,EAAE;MACvC,IAAI,CAACU,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACE,oBAAoB,CAACF,MAAM,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,IAAI,CAACG,eAAe,IAAIH,MAAM,YAAYP,SAAS,EAAE;MAC1D,IAAI,CAACI,eAAe,GAAGG,MAAM;MAC7B,OAAO,IAAI,CAACG,eAAe,CAACH,MAAM,CAAC;IACvC;IACA,IAAI,OAAOxB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CP,2BAA2B,CAAC,CAAC;IACjC;EACJ;EACA;EACAkC,eAAe,GAAG,IAAI;EACtB;EACAzB,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACmB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACjB,eAAe,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACiB,eAAe,GAAG,IAAI;IAC/B;IACA,IAAI,CAACO,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACL,WAAW,GAAG,IAAI;EAC3B;EACA;EACAO,YAAYA,CAACC,EAAE,EAAE;IACb,IAAI,CAACT,UAAU,GAAGS,EAAE;EACxB;EACAH,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACN,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC,CAAC;MACjB,IAAI,CAACA,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMU,eAAe,SAASZ,gBAAgB,CAAC;EAC3Ca,aAAa;EACbC,OAAO;EACPC,gBAAgB;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIzB,WAAWA,CACX;EACAuB,aAAa,EAAEC,OAAO,EAAEC,gBAAgB,EAAE;IACtC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;AACA;AACA;EACIV,qBAAqBA,CAACD,MAAM,EAAE;IAC1B,IAAIY,YAAY;IAChB;IACA;IACA;IACA;IACA,IAAIZ,MAAM,CAACjB,gBAAgB,EAAE;MACzB,MAAMC,QAAQ,GAAGgB,MAAM,CAAChB,QAAQ,IAAIgB,MAAM,CAACjB,gBAAgB,CAACC,QAAQ;MACpE,MAAM6B,WAAW,GAAG7B,QAAQ,CAAC8B,GAAG,CAAC9D,WAAW,EAAE,IAAI,EAAE;QAAE+D,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAIvB,SAAS;MACpFoB,YAAY,GAAGZ,MAAM,CAACjB,gBAAgB,CAAC7B,eAAe,CAAC8C,MAAM,CAAClB,SAAS,EAAE;QACrEkC,KAAK,EAAEhB,MAAM,CAACjB,gBAAgB,CAACkC,MAAM;QACrCjC,QAAQ;QACR6B,WAAW;QACX5B,gBAAgB,EAAEe,MAAM,CAACf,gBAAgB,IAAIO;MACjD,CAAC,CAAC;MACF,IAAI,CAACc,YAAY,CAAC,MAAMM,YAAY,CAACM,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC,OAAO1C,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC,IAAI,CAACkC,OAAO,EAAE;QAClE,MAAM5C,KAAK,CAAC,qEAAqE,CAAC;MACtF;MACA,MAAMqD,MAAM,GAAG,IAAI,CAACT,OAAO;MAC3B,MAAMU,eAAe,GAAGpB,MAAM,CAAChB,QAAQ,IAAI,IAAI,CAAC2B,gBAAgB,IAAIxD,QAAQ,CAACkE,IAAI;MACjF,MAAMC,mBAAmB,GAAGF,eAAe,CAACN,GAAG,CAAC7D,mBAAmB,EAAEkE,MAAM,CAACnC,QAAQ,CAAC;MACrF4B,YAAY,GAAG1D,eAAe,CAAC8C,MAAM,CAAClB,SAAS,EAAE;QAC7CsC,eAAe;QACfE,mBAAmB;QACnBrC,gBAAgB,EAAEe,MAAM,CAACf,gBAAgB,IAAIO;MACjD,CAAC,CAAC;MACF2B,MAAM,CAACI,UAAU,CAACX,YAAY,CAACY,QAAQ,CAAC;MACxC,IAAI,CAAClB,YAAY,CAAC,MAAM;QACpB;QACA;QACA,IAAIa,MAAM,CAACM,SAAS,GAAG,CAAC,EAAE;UACtBN,MAAM,CAACO,UAAU,CAACd,YAAY,CAACY,QAAQ,CAAC;QAC5C;QACAZ,YAAY,CAACM,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACT,aAAa,CAACkB,WAAW,CAAC,IAAI,CAACC,qBAAqB,CAAChB,YAAY,CAAC,CAAC;IACxE,IAAI,CAACf,eAAe,GAAGG,MAAM;IAC7B,OAAOY,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIV,oBAAoBA,CAACF,MAAM,EAAE;IACzB,IAAI6B,aAAa,GAAG7B,MAAM,CAACjB,gBAAgB;IAC3C,IAAI+C,OAAO,GAAGD,aAAa,CAACE,kBAAkB,CAAC/B,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;MAC/EL,QAAQ,EAAEgB,MAAM,CAAChB;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA8C,OAAO,CAACE,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI,IAAI,CAACzB,aAAa,CAACkB,WAAW,CAACO,QAAQ,CAAC,CAAC;IAC/E;IACA;IACA;IACAJ,OAAO,CAACK,aAAa,CAAC,CAAC;IACvB,IAAI,CAAC7B,YAAY,CAAC,MAAM;MACpB,IAAIU,KAAK,GAAGa,aAAa,CAACO,OAAO,CAACN,OAAO,CAAC;MAC1C,IAAId,KAAK,KAAK,CAAC,CAAC,EAAE;QACda,aAAa,CAACQ,MAAM,CAACrB,KAAK,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAACnB,eAAe,GAAGG,MAAM;IAC7B;IACA,OAAO8B,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3B,eAAe,GAAIH,MAAM,IAAK;IAC1B,MAAMN,OAAO,GAAGM,MAAM,CAACN,OAAO;IAC9B,IAAI,CAACA,OAAO,CAAC4C,UAAU,KAAK,OAAO9D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACxE,MAAMV,KAAK,CAAC,uDAAuD,CAAC;IACxE;IACA;IACA;IACA,MAAMyE,UAAU,GAAG,IAAI,CAAC9B,aAAa,CAAC+B,aAAa,CAACC,aAAa,CAAC,YAAY,CAAC;IAC/E/C,OAAO,CAAC4C,UAAU,CAACI,YAAY,CAACH,UAAU,EAAE7C,OAAO,CAAC;IACpD,IAAI,CAACe,aAAa,CAACkB,WAAW,CAACjC,OAAO,CAAC;IACvC,IAAI,CAACG,eAAe,GAAGG,MAAM;IAC7B,KAAK,CAACM,YAAY,CAAC,MAAM;MACrB;MACA,IAAIiC,UAAU,CAACD,UAAU,EAAE;QACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAACjD,OAAO,EAAE6C,UAAU,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACIlC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACI,aAAa,CAAC4B,MAAM,CAAC,CAAC;EAC/B;EACA;EACAT,qBAAqBA,CAAChB,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACY,QAAQ,CAACQ,SAAS,CAAC,CAAC,CAAC;EAC7C;AACJ;;AAEA;AACA;AACA;AACA;AAHA,IAIMY,SAAS;EAAf,MAAMA,SAAS,SAASzD,cAAc,CAAC;IACnCD,WAAWA,CAAA,EAAG;MACV,MAAME,WAAW,GAAGhC,MAAM,CAACC,WAAW,CAAC;MACvC,MAAM0B,gBAAgB,GAAG3B,MAAM,CAACE,gBAAgB,CAAC;MACjD,KAAK,CAAC8B,WAAW,EAAEL,gBAAgB,CAAC;IACxC;IACA,OAAO8D,IAAI,YAAAC,kBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,SAAS;IAAA;IAC5G,OAAOI,IAAI,kBAD8ElG,EAAE,CAAAmG,iBAAA;MAAAC,IAAA,EACJN,SAAS;MAAAO,SAAA;MAAAC,QAAA;MAAAC,QAAA,GADPvG,EAAE,CAAAwG,0BAAA;IAAA;EAE/F;EAAC,OARKV,SAAS;AAAA;AASf;EAAA,QAAApE,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AAHA,IAIM+E,uBAAuB;EAA7B,MAAMA,uBAAuB,SAASX,SAAS,CAAC;IAC5C,OAAOC,IAAI;MAAA,IAAAW,oCAAA;MAAA,gBAAAC,gCAAAV,iBAAA;QAAA,QAAAS,oCAAA,KAAAA,oCAAA,GAf8E1G,EAAE,CAAA4G,qBAAA,CAeQH,uBAAuB,IAAAR,iBAAA,IAAvBQ,uBAAuB;MAAA;IAAA;IAC1H,OAAOP,IAAI,kBAhB8ElG,EAAE,CAAAmG,iBAAA;MAAAC,IAAA,EAgBJK,uBAAuB;MAAAJ,SAAA;MAAAC,QAAA;MAAAC,QAAA,GAhBrBvG,EAAE,CAAA6G,kBAAA,CAgBwF,CAC3K;QACIC,OAAO,EAAEhB,SAAS;QAClBiB,WAAW,EAAEN;MACjB,CAAC,CACJ,GArBoFzG,EAAE,CAAAwG,0BAAA;IAAA;EAsB/F;EAAC,OARKC,uBAAuB;AAAA;AAS7B;EAAA,QAAA/E,SAAA,oBAAAA,SAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMsF,eAAe;EAArB,MAAMA,eAAe,SAASlE,gBAAgB,CAAC;IAC3CmE,UAAU,GAAG3G,MAAM,CAACJ,WAAW,EAAE;MAAE+D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACpDiD,SAAS,GAAG5G,MAAM,CAACI,QAAQ,CAAC;IAC5ByG,iBAAiB,GAAG7G,MAAM,CAACE,gBAAgB,CAAC;IAC5C;IACA4G,cAAc,GAAG,KAAK;IACtB;IACAC,YAAY;IACZjF,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;IACX;IACA;IACA,IAAIc,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACH,eAAe;IAC/B;IACA,IAAIG,MAAMA,CAACA,MAAM,EAAE;MACf;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACvB,WAAW,CAAC,CAAC,IAAI,CAACuB,MAAM,IAAI,CAAC,IAAI,CAACkE,cAAc,EAAE;QACvD;MACJ;MACA,IAAI,IAAI,CAACzF,WAAW,CAAC,CAAC,EAAE;QACpB,KAAK,CAACC,MAAM,CAAC,CAAC;MAClB;MACA,IAAIsB,MAAM,EAAE;QACR,KAAK,CAAC1B,MAAM,CAAC0B,MAAM,CAAC;MACxB;MACA,IAAI,CAACH,eAAe,GAAGG,MAAM,IAAI,IAAI;IACzC;IACA;IACAoE,QAAQ,GAAG,IAAI3G,YAAY,CAAC,CAAC;IAC7B;IACA,IAAI4G,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACF,YAAY;IAC5B;IACAG,QAAQA,CAAA,EAAG;MACP,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;IACAK,WAAWA,CAAA,EAAG;MACV,KAAK,CAAClE,OAAO,CAAC,CAAC;MACf,IAAI,CAAC8D,YAAY,GAAG,IAAI,CAACtE,eAAe,GAAG,IAAI;IACnD;IACA;AACJ;AACA;AACA;AACA;AACA;IACII,qBAAqBA,CAACD,MAAM,EAAE;MAC1BA,MAAM,CAACpB,eAAe,CAAC,IAAI,CAAC;MAC5B;MACA;MACA,MAAMG,gBAAgB,GAAGiB,MAAM,CAACjB,gBAAgB,IAAI,IAAI,GAAGiB,MAAM,CAACjB,gBAAgB,GAAG,IAAI,CAACkF,iBAAiB;MAC3G,MAAMO,GAAG,GAAGzF,gBAAgB,CAAC7B,eAAe,CAAC8C,MAAM,CAAClB,SAAS,EAAE;QAC3DkC,KAAK,EAAEjC,gBAAgB,CAACkC,MAAM;QAC9BjC,QAAQ,EAAEgB,MAAM,CAAChB,QAAQ,IAAID,gBAAgB,CAACC,QAAQ;QACtDC,gBAAgB,EAAEe,MAAM,CAACf,gBAAgB,IAAIO,SAAS;QACtDqB,WAAW,EAAE,IAAI,CAACkD,UAAU,IAAIvE;MACpC,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAIT,gBAAgB,KAAK,IAAI,CAACkF,iBAAiB,EAAE;QAC7C,IAAI,CAACQ,YAAY,CAAC,CAAC,CAAC9C,WAAW,CAAC6C,GAAG,CAAChD,QAAQ,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9D;MACA,KAAK,CAAC1B,YAAY,CAAC,MAAMkE,GAAG,CAACtD,OAAO,CAAC,CAAC,CAAC;MACvC,IAAI,CAACrB,eAAe,GAAGG,MAAM;MAC7B,IAAI,CAACmE,YAAY,GAAGK,GAAG;MACvB,IAAI,CAACJ,QAAQ,CAACM,IAAI,CAACF,GAAG,CAAC;MACvB,OAAOA,GAAG;IACd;IACA;AACJ;AACA;AACA;AACA;IACItE,oBAAoBA,CAACF,MAAM,EAAE;MACzBA,MAAM,CAACpB,eAAe,CAAC,IAAI,CAAC;MAC5B,MAAMkD,OAAO,GAAG,IAAI,CAACmC,iBAAiB,CAAClC,kBAAkB,CAAC/B,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;QAC1FL,QAAQ,EAAEgB,MAAM,CAAChB;MACrB,CAAC,CAAC;MACF,KAAK,CAACsB,YAAY,CAAC,MAAM,IAAI,CAAC2D,iBAAiB,CAACU,KAAK,CAAC,CAAC,CAAC;MACxD,IAAI,CAAC9E,eAAe,GAAGG,MAAM;MAC7B,IAAI,CAACmE,YAAY,GAAGrC,OAAO;MAC3B,IAAI,CAACsC,QAAQ,CAACM,IAAI,CAAC5C,OAAO,CAAC;MAC3B,OAAOA,OAAO;IAClB;IACA;AACJ;AACA;AACA;AACA;AACA;IACI3B,eAAe,GAAIH,MAAM,IAAK;MAC1B,MAAMN,OAAO,GAAGM,MAAM,CAACN,OAAO;MAC9B,IAAI,CAACA,OAAO,CAAC4C,UAAU,KAAK,OAAO9D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMV,KAAK,CAAC,uDAAuD,CAAC;MACxE;MACA;MACA;MACA,MAAMyE,UAAU,GAAG,IAAI,CAACyB,SAAS,CAACvB,aAAa,CAAC,YAAY,CAAC;MAC7DzC,MAAM,CAACpB,eAAe,CAAC,IAAI,CAAC;MAC5Bc,OAAO,CAAC4C,UAAU,CAACI,YAAY,CAACH,UAAU,EAAE7C,OAAO,CAAC;MACpD,IAAI,CAAC+E,YAAY,CAAC,CAAC,CAAC9C,WAAW,CAACjC,OAAO,CAAC;MACxC,IAAI,CAACG,eAAe,GAAGG,MAAM;MAC7B,KAAK,CAACM,YAAY,CAAC,MAAM;QACrB,IAAIiC,UAAU,CAACD,UAAU,EAAE;UACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAACjD,OAAO,EAAE6C,UAAU,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC;IACD;IACAkC,YAAYA,CAAA,EAAG;MACX,MAAM9E,aAAa,GAAG,IAAI,CAACsE,iBAAiB,CAACvE,OAAO,CAACC,aAAa;MAClE;MACA;MACA,OAAQA,aAAa,CAACiF,QAAQ,KAAKjF,aAAa,CAACkF,YAAY,GACvDlF,aAAa,GACbA,aAAa,CAAC2C,UAAU;IAClC;IACA,OAAOO,IAAI,YAAAiC,wBAAA/B,iBAAA;MAAA,YAAAA,iBAAA,IAAwFe,eAAe;IAAA;IAClH,OAAOd,IAAI,kBAtK8ElG,EAAE,CAAAmG,iBAAA;MAAAC,IAAA,EAsKJY,eAAe;MAAAX,SAAA;MAAA4B,MAAA;QAAA/E,MAAA;MAAA;MAAAgF,OAAA;QAAAZ,QAAA;MAAA;MAAAhB,QAAA;MAAAC,QAAA,GAtKbvG,EAAE,CAAAwG,0BAAA;IAAA;EAuK/F;EAAC,OA5HKQ,eAAe;AAAA;AA6HrB;EAAA,QAAAtF,SAAA,oBAAAA,SAAA;AAAA;AAYA;AACA;AACA;AACA;AAHA,IAIMyG,mBAAmB;EAAzB,MAAMA,mBAAmB,SAASnB,eAAe,CAAC;IAC9C,OAAOjB,IAAI;MAAA,IAAAqC,gCAAA;MAAA,gBAAAC,4BAAApC,iBAAA;QAAA,QAAAmC,gCAAA,KAAAA,gCAAA,GAzL8EpI,EAAE,CAAA4G,qBAAA,CAyLQuB,mBAAmB,IAAAlC,iBAAA,IAAnBkC,mBAAmB;MAAA;IAAA;IACtH,OAAOjC,IAAI,kBA1L8ElG,EAAE,CAAAmG,iBAAA;MAAAC,IAAA,EA0LJ+B,mBAAmB;MAAA9B,SAAA;MAAA4B,MAAA;QAAA/E,MAAA;MAAA;MAAAoD,QAAA;MAAAC,QAAA,GA1LjBvG,EAAE,CAAA6G,kBAAA,CA0L4I,CAC/N;QACIC,OAAO,EAAEE,eAAe;QACxBD,WAAW,EAAEoB;MACjB,CAAC,CACJ,GA/LoFnI,EAAE,CAAAwG,0BAAA;IAAA;EAgM/F;EAAC,OARK2B,mBAAmB;AAAA;AASzB;EAAA,QAAAzG,SAAA,oBAAAA,SAAA;AAAA;AAac,IACR4G,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACf,OAAOvC,IAAI,YAAAwC,qBAAAtC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqC,YAAY;IAAA;IAC/G,OAAOE,IAAI,kBAjN8ExI,EAAE,CAAAyI,gBAAA;MAAArC,IAAA,EAiNSkC;IAAY;IAChH,OAAOI,IAAI,kBAlN8E1I,EAAE,CAAA2I,gBAAA;EAmN/F;EAAC,OAJKL,YAAY;AAAA;AAKlB;EAAA,QAAA5G,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAASoB,gBAAgB,EAAEgD,SAAS,EAAEkB,eAAe,EAAEjF,eAAe,EAAEY,SAAS,EAAEe,eAAe,EAAEpC,MAAM,EAAE6G,mBAAmB,EAAEG,YAAY,EAAEjG,cAAc,EAAEoE,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}