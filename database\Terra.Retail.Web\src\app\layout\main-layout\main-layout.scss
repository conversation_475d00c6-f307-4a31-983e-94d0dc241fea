/* Terra Retail ERP - Main Layout */

.main-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
  direction: rtl;
}

/* Content Area */
.content-area {
  flex: 1;
  margin-right: 280px;
  display: flex;
  flex-direction: column;
  transition: margin-right 0.3s ease;

  &.sidebar-collapsed {
    margin-right: 70px;
  }
}

/* Top Header */
.top-header {
  background: white;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e0e6ed;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.menu-toggle {
  color: #64748b;

  &:hover {
    background: rgba(100, 116, 139, 0.1);
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;

  mat-icon {
    font-size: 1.2rem;
    color: #3498db;
  }

  span {
    font-weight: 600;
    color: #2c3e50;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-btn {
  color: #64748b;
  position: relative;

  &:hover {
    background: rgba(100, 116, 139, 0.1);
  }
}

.user-menu-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  color: #2c3e50;

  &:hover {
    background: rgba(52, 152, 219, 0.1);
  }

  span {
    font-weight: 500;
  }
}

/* Page Content */
.page-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: #f5f7fa;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* Notification Menu */
::ng-deep .notification-menu {
  width: 320px;
  max-height: 400px;

  .mat-mdc-menu-content {
    padding: 0;
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;

  h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
  }
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;

  &:hover {
    background: #f8fafc;
  }

  mat-icon {
    font-size: 1.5rem;
  }
}

.notification-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.notification-time {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.2rem;
}

/* User Menu */
::ng-deep .user-menu {
  .mat-mdc-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;

    mat-icon {
      font-size: 1.2rem;
    }

    span {
      font-weight: 500;
    }
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-area {
    margin-right: 0;

    &.sidebar-collapsed {
      margin-right: 0;
    }
  }

  .page-content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .top-header {
    padding: 0 1rem;
  }

  .breadcrumb span {
    display: none;
  }

  .user-menu-btn span {
    display: none;
  }

  .page-content {
    padding: 0.5rem;
  }
}

/* Animation */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.top-header {
  animation: slideInDown 0.3s ease;
}

/* Loading State */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Custom Scrollbar for Content */
.page-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Focus States */
.menu-toggle:focus,
.notification-btn:focus,
.user-menu-btn:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .top-header {
    border-bottom: 2px solid #000;
  }

  .breadcrumb {
    color: #000;
  }
}