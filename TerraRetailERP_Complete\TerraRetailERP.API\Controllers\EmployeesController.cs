using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("👨‍💼 Human Resources Management")]
    public class EmployeesController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public EmployeesController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Employee>>> GetEmployees(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] int? positionId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Employees
                    .Include(e => e.Area)
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Branch)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(e => e.NameAr.Contains(search) || 
                                           e.NameEn!.Contains(search) || 
                                           e.EmployeeCode.Contains(search) ||
                                           e.Phone1.Contains(search) ||
                                           e.Email.Contains(search) ||
                                           e.IdentityNumber.Contains(search));
                }

                if (departmentId.HasValue)
                    query = query.Where(e => e.DepartmentId == departmentId);

                if (positionId.HasValue)
                    query = query.Where(e => e.PositionId == positionId);

                if (branchId.HasValue)
                    query = query.Where(e => e.BranchId == branchId);

                if (isActive.HasValue)
                    query = query.Where(e => e.IsActive == isActive);

                var totalCount = await query.CountAsync();
                var employees = await query
                    .OrderBy(e => e.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(e => new
                    {
                        e.Id,
                        e.EmployeeCode,
                        e.NameAr,
                        e.NameEn,
                        e.IdentityNumber,
                        e.Phone1,
                        e.Email,
                        e.Gender,
                        e.BirthDate,
                        e.HireDate,
                        e.BasicSalary,
                        e.IsActive,
                        Area = new { e.Area.Id, e.Area.NameAr },
                        Department = e.Department != null ? new { e.Department.Id, e.Department.NameAr } : null,
                        Position = e.Position != null ? new { e.Position.Id, e.Position.NameAr } : null,
                        Branch = new { e.Branch.Id, e.Branch.NameAr },
                        e.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = employees,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Employee>> GetEmployee(int id)
        {
            try
            {
                var employee = await _context.Employees
                    .Include(e => e.Area)
                    .Include(e => e.Department)
                    .Include(e => e.Position)
                    .Include(e => e.Branch)
                    .Include(e => e.EmployeeShifts)
                        .ThenInclude(es => es.Shift)
                    .Include(e => e.EmployeeDocuments)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (employee == null)
                    return NotFound(new { message = "الموظف غير موجود" });

                return Ok(employee);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Employee>> CreateEmployee(CreateEmployeeRequest request)
        {
            try
            {
                // Generate employee code
                var employeeCode = await GenerateEmployeeCode(request.BranchId);

                var employee = new Employee
                {
                    EmployeeCode = employeeCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    IdentityNumber = request.IdentityNumber,
                    BirthDate = request.BirthDate,
                    Gender = request.Gender,
                    MaritalStatus = request.MaritalStatus,
                    Nationality = request.Nationality,
                    Phone1 = request.Phone1,
                    Phone2 = request.Phone2,
                    Email = request.Email,
                    Address = request.Address,
                    AreaId = request.AreaId,
                    DepartmentId = request.DepartmentId,
                    PositionId = request.PositionId,
                    BranchId = request.BranchId,
                    BiometricId = request.BiometricId,
                    BasicSalary = request.BasicSalary,
                    Allowances = request.Allowances,
                    SocialInsuranceNumber = request.SocialInsuranceNumber,
                    TaxNumber = request.TaxNumber,
                    BankAccountNumber = request.BankAccountNumber,
                    BankName = request.BankName,
                    EmergencyContactName = request.EmergencyContactName,
                    EmergencyContactPhone = request.EmergencyContactPhone,
                    EmergencyContactRelation = request.EmergencyContactRelation,
                    HireDate = request.HireDate,
                    Notes = request.Notes,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();

                // Create initial leave balances
                await CreateInitialLeaveBalances(employee.Id);

                // Load related data for response
                await _context.Entry(employee)
                    .Reference(e => e.Area)
                    .LoadAsync();
                await _context.Entry(employee)
                    .Reference(e => e.Department)
                    .LoadAsync();
                await _context.Entry(employee)
                    .Reference(e => e.Position)
                    .LoadAsync();
                await _context.Entry(employee)
                    .Reference(e => e.Branch)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetEmployee), new { id = employee.Id }, employee);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEmployee(int id, UpdateEmployeeRequest request)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee == null)
                    return NotFound(new { message = "الموظف غير موجود" });

                employee.NameAr = request.NameAr;
                employee.NameEn = request.NameEn;
                employee.IdentityNumber = request.IdentityNumber;
                employee.BirthDate = request.BirthDate;
                employee.Gender = request.Gender;
                employee.MaritalStatus = request.MaritalStatus;
                employee.Nationality = request.Nationality;
                employee.Phone1 = request.Phone1;
                employee.Phone2 = request.Phone2;
                employee.Email = request.Email;
                employee.Address = request.Address;
                employee.AreaId = request.AreaId;
                employee.DepartmentId = request.DepartmentId;
                employee.PositionId = request.PositionId;
                employee.BiometricId = request.BiometricId;
                employee.BasicSalary = request.BasicSalary;
                employee.Allowances = request.Allowances;
                employee.SocialInsuranceNumber = request.SocialInsuranceNumber;
                employee.TaxNumber = request.TaxNumber;
                employee.BankAccountNumber = request.BankAccountNumber;
                employee.BankName = request.BankName;
                employee.EmergencyContactName = request.EmergencyContactName;
                employee.EmergencyContactPhone = request.EmergencyContactPhone;
                employee.EmergencyContactRelation = request.EmergencyContactRelation;
                employee.TerminationDate = request.TerminationDate;
                employee.TerminationReason = request.TerminationReason;
                employee.Notes = request.Notes;
                employee.IsActive = request.IsActive;
                employee.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث بيانات الموظف بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee == null)
                    return NotFound(new { message = "الموظف غير موجود" });

                // Check if employee has attendance records
                var hasAttendance = await _context.AttendanceRecords.AnyAsync(ar => ar.EmployeeId == id);
                if (hasAttendance)
                {
                    return BadRequest(new { message = "لا يمكن حذف الموظف لوجود سجلات حضور مرتبطة به" });
                }

                // Check if employee has payroll records
                var hasPayroll = await _context.Payrolls.AnyAsync(p => p.EmployeeId == id);
                if (hasPayroll)
                {
                    return BadRequest(new { message = "لا يمكن حذف الموظف لوجود سجلات رواتب مرتبطة به" });
                }

                _context.Employees.Remove(employee);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف الموظف بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("departments")]
        public async Task<ActionResult<IEnumerable<Department>>> GetDepartments([FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.Departments
                    .Include(d => d.Manager)
                    .Include(d => d.Branch)
                    .Where(d => d.IsActive);

                if (branchId.HasValue)
                    query = query.Where(d => d.BranchId == branchId);

                var departments = await query
                    .OrderBy(d => d.NameAr)
                    .ToListAsync();

                return Ok(departments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("positions")]
        public async Task<ActionResult<IEnumerable<Position>>> GetPositions([FromQuery] int? departmentId = null)
        {
            try
            {
                var query = _context.Positions
                    .Include(p => p.Department)
                    .Where(p => p.IsActive);

                if (departmentId.HasValue)
                    query = query.Where(p => p.DepartmentId == departmentId);

                var positions = await query
                    .OrderBy(p => p.Level)
                    .ThenBy(p => p.NameAr)
                    .ToListAsync();

                return Ok(positions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}/attendance")]
        public async Task<ActionResult> GetEmployeeAttendance(int id, 
            [FromQuery] DateTime? fromDate = null, 
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddDays(-30);
                var endDate = toDate ?? DateTime.Today;

                var attendance = await _context.AttendanceRecords
                    .Include(ar => ar.Shift)
                    .Where(ar => ar.EmployeeId == id && ar.ShiftDate >= startDate && ar.ShiftDate <= endDate)
                    .OrderByDescending(ar => ar.ShiftDate)
                    .Select(ar => new
                    {
                        ar.Id,
                        ar.ShiftDate,
                        ar.ActualCheckInTime,
                        ar.ActualCheckOutTime,
                        ar.PlannedCheckInTime,
                        ar.PlannedCheckOutTime,
                        ar.WorkingMinutes,
                        ar.LateMinutes,
                        ar.EarlyLeaveMinutes,
                        ar.OvertimeMinutes,
                        ar.AttendanceStatus,
                        ar.IsComplete,
                        ar.IsManualEntry,
                        ar.Notes,
                        Shift = new { ar.Shift.ShiftName, ar.Shift.StartTime, ar.Shift.EndTime }
                    })
                    .ToListAsync();

                // Calculate summary
                var summary = new
                {
                    TotalDays = attendance.Count,
                    PresentDays = attendance.Count(a => a.AttendanceStatus == "Present"),
                    AbsentDays = attendance.Count(a => a.AttendanceStatus == "Absent"),
                    LateDays = attendance.Count(a => a.LateMinutes > 0),
                    TotalWorkingHours = attendance.Sum(a => a.WorkingMinutes) / 60.0,
                    TotalOvertimeHours = attendance.Sum(a => a.OvertimeMinutes) / 60.0,
                    AverageLateMinutes = attendance.Where(a => a.LateMinutes > 0).Average(a => (double?)a.LateMinutes) ?? 0
                };

                return Ok(new { data = attendance, summary });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}/leaves")]
        public async Task<ActionResult> GetEmployeeLeaves(int id, [FromQuery] int? year = null)
        {
            try
            {
                var targetYear = year ?? DateTime.Now.Year;

                var leaves = await _context.EmployeeLeaves
                    .Include(el => el.LeaveType)
                    .Include(el => el.ApprovedByUser)
                    .Where(el => el.EmployeeId == id && el.StartDate.Year == targetYear)
                    .OrderByDescending(el => el.StartDate)
                    .ToListAsync();

                var balances = await _context.EmployeeLeaveBalances
                    .Include(elb => elb.LeaveType)
                    .Where(elb => elb.EmployeeId == id && elb.Year == targetYear)
                    .ToListAsync();

                return Ok(new { leaves, balances });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateEmployeeCode(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.Employee && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.Employee,
                    Prefix = "EMP",
                    CurrentValue = 1,
                    NumberLength = 6,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task CreateInitialLeaveBalances(int employeeId)
        {
            var currentYear = DateTime.Now.Year;
            var leaveTypes = await _context.LeaveTypes.Where(lt => lt.IsActive).ToListAsync();

            foreach (var leaveType in leaveTypes)
            {
                var balance = new EmployeeLeaveBalance
                {
                    EmployeeId = employeeId,
                    LeaveTypeId = leaveType.Id,
                    Year = currentYear,
                    EntitledDays = leaveType.MaxDaysPerYear,
                    UsedDays = 0,
                    CarriedForwardDays = 0,
                    LastUpdated = DateTime.Now
                };

                _context.EmployeeLeaveBalances.Add(balance);
            }

            await _context.SaveChangesAsync();
        }
    }

    // DTOs
    public class CreateEmployeeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string IdentityNumber { get; set; } = string.Empty;
        public DateTime BirthDate { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string? MaritalStatus { get; set; }
        public string? Nationality { get; set; }
        public string Phone1 { get; set; } = string.Empty;
        public string? Phone2 { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int AreaId { get; set; }
        public int? DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public int BranchId { get; set; }
        public string? BiometricId { get; set; }
        public decimal BasicSalary { get; set; } = 0;
        public decimal Allowances { get; set; } = 0;
        public string? SocialInsuranceNumber { get; set; }
        public string? TaxNumber { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankName { get; set; }
        public string? EmergencyContactName { get; set; }
        public string? EmergencyContactPhone { get; set; }
        public string? EmergencyContactRelation { get; set; }
        public DateTime? HireDate { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateEmployeeRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string IdentityNumber { get; set; } = string.Empty;
        public DateTime BirthDate { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string? MaritalStatus { get; set; }
        public string? Nationality { get; set; }
        public string Phone1 { get; set; } = string.Empty;
        public string? Phone2 { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int AreaId { get; set; }
        public int? DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public string? BiometricId { get; set; }
        public decimal BasicSalary { get; set; } = 0;
        public decimal Allowances { get; set; } = 0;
        public string? SocialInsuranceNumber { get; set; }
        public string? TaxNumber { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankName { get; set; }
        public string? EmergencyContactName { get; set; }
        public string? EmergencyContactPhone { get; set; }
        public string? EmergencyContactRelation { get; set; }
        public DateTime? TerminationDate { get; set; }
        public string? TerminationReason { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
