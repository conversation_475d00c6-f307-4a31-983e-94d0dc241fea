@echo off
chcp 65001 >nul
echo ========================================
echo 🧪 اختبار نظام Terra Retail ERP
echo 🧪 Testing Terra Retail ERP System
echo ========================================
echo.

echo 🔍 التحقق من قاعدة البيانات...
echo 🔍 Checking database...
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "SELECT COUNT(*) as TablesCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"

if %ERRORLEVEL% EQU 0 (
    echo ✅ قاعدة البيانات متصلة
    echo ✅ Database connected
) else (
    echo ❌ مشكلة في قاعدة البيانات
    echo ❌ Database connection issue
)

echo.
echo 📊 عرض البيانات الأساسية...
echo 📊 Showing basic data...

echo.
echo === وحدات القياس ===
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "USE TerraRetailERP; SELECT TOP 5 Id, NameAr, NameEn, Symbol FROM Units"

echo.
echo === أنواع العملاء ===
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "USE TerraRetailERP; SELECT TOP 5 Id, NameAr, NameEn FROM CustomerTypes"

echo.
echo === فئات الأسعار ===
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "USE TerraRetailERP; SELECT TOP 5 Id, NameAr, NameEn, Code FROM PriceCategories"

echo.
echo ========================================
echo 🎉 اختبار النظام مكتمل!
echo 🎉 System test completed!
echo ========================================
echo.

pause
