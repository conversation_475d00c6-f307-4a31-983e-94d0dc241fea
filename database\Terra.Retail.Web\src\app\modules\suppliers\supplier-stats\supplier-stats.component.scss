/* Terra Retail ERP - Supplier Statistics Styles */

.supplier-stats-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--info-600) 0%, var(--primary-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .back-btn {
      background: rgba(255, 255, 255, 0.2) !important;
      color: white !important;
      width: 48px !important;
      height: 48px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
      }
    }

    .header-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0 0 var(--spacing-sm) 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .page-subtitle {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-md);

    button {
      padding: var(--spacing-md) var(--spacing-lg) !important;
      font-weight: 600 !important;

      &[mat-raised-button] {
        background: var(--warning-500) !important;
        color: white !important;

        &:hover {
          background: var(--warning-600) !important;
        }
      }

      &[mat-stroked-button] {
        border-color: rgba(255, 255, 255, 0.5) !important;
        color: white !important;

        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
          border-color: white !important;
        }
      }
    }
  }
}

/* ===== CONTENT ===== */
.content {
  padding: var(--spacing-2xl) 0;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--spacing-xl) 0;
  text-align: center;
}

/* ===== STATISTICS SECTION ===== */
.stats-section {
  margin-bottom: var(--spacing-3xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.stat-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-normal) !important;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-xl) !important;
  }

  .mat-mdc-card-content {
    display: flex !important;
    align-items: center !important;
    gap: var(--spacing-lg) !important;
    padding: var(--spacing-xl) !important;
  }

  .stat-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: white;
    }
  }

  .stat-info {
    flex: 1;

    h3 {
      font-size: 1rem;
      font-weight: 600;
      color: var(--gray-600);
      margin: 0 0 var(--spacing-xs) 0;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-xs) 0;
      color: var(--gray-900);

      &.positive {
        color: var(--success-600);
      }

      &.negative {
        color: var(--error-600);
      }

      &.zero {
        color: var(--gray-600);
      }
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--gray-500);
      font-weight: 500;
    }
  }

  &.total-card .stat-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  }

  &.active-card .stat-icon {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
  }

  &.inactive-card .stat-icon {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  }

  &.balance-card .stat-icon {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
  }
}

/* ===== FINANCIAL SECTION ===== */
.financial-section {
  margin-bottom: var(--spacing-3xl);
}

.financial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.financial-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;

      mat-icon {
        font-size: 1.5rem !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
    text-align: center;

    .amount {
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: var(--spacing-md);

      &.positive {
        color: var(--success-600);
      }

      &.negative {
        color: var(--error-600);
      }
    }

    .description {
      font-size: 1rem;
      color: var(--gray-600);
      margin: 0;
    }
  }

  &.positive-card {
    border-left: 4px solid var(--success-500) !important;

    .mat-mdc-card-title mat-icon {
      color: var(--success-600) !important;
    }
  }

  &.negative-card {
    border-left: 4px solid var(--error-500) !important;

    .mat-mdc-card-title mat-icon {
      color: var(--error-600) !important;
    }
  }
}

/* ===== CODE SECTION ===== */
.code-section {
  margin-bottom: var(--spacing-3xl);
}

.code-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}

.code-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;

  .mat-mdc-card-header {
    background: var(--gray-50) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: var(--gray-900) !important;
      margin: 0 !important;

      mat-icon {
        color: var(--primary-600) !important;
        font-size: 1.5rem !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
  }
}

.code-info, .capacity-info {
  .info-row, .capacity-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 600;
      color: var(--gray-700);
    }

    .value {
      color: var(--gray-900);
      font-weight: 500;

      &.highlight {
        background: var(--primary-100);
        color: var(--primary-700);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-weight: 700;
      }

      &.remaining {
        color: var(--success-600);
        font-weight: 700;
      }
    }
  }
}

.usage-info {
  text-align: center;

  .usage-percentage {
    margin-bottom: var(--spacing-lg);

    .percentage-value {
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--primary-600);
      display: block;
    }

    .percentage-label {
      font-size: 0.875rem;
      color: var(--gray-600);
    }
  }

  .mat-mdc-progress-bar {
    margin-bottom: var(--spacing-lg);
    height: 12px !important;
    border-radius: var(--radius-full) !important;

    &.low {
      --mdc-linear-progress-active-indicator-color: var(--success-500);
    }

    &.medium {
      --mdc-linear-progress-active-indicator-color: var(--warning-500);
    }

    &.high {
      --mdc-linear-progress-active-indicator-color: var(--error-500);
    }

    &.critical {
      --mdc-linear-progress-active-indicator-color: var(--error-700);
    }
  }

  .usage-status {
    .mat-mdc-chip {
      font-weight: 600 !important;

      &.low {
        background: var(--success-100) !important;
        color: var(--success-700) !important;
      }

      &.medium {
        background: var(--warning-100) !important;
        color: var(--warning-700) !important;
      }

      &.high {
        background: var(--error-100) !important;
        color: var(--error-700) !important;
      }

      &.critical {
        background: var(--error-200) !important;
        color: var(--error-800) !important;
      }
    }
  }
}

/* ===== RECOMMENDATIONS ===== */
.recommendations-section {
  margin-bottom: var(--spacing-2xl);
}

.recommendations-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--warning-200) !important;
  background: var(--warning-25) !important;

  .mat-mdc-card-header {
    background: var(--warning-50) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;

    .mat-mdc-card-title {
      display: flex !important;
      align-items: center !important;
      gap: var(--spacing-md) !important;
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: var(--warning-800) !important;
      margin: 0 !important;

      mat-icon {
        color: var(--warning-600) !important;
        font-size: 1.5rem !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
      }
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
  }
}

.recommendations-list {
  .recommendation-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: white;
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);

    &:last-child {
      margin-bottom: 0;
    }

    .warning-icon {
      color: var(--error-600);
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      flex-shrink: 0;
    }

    .info-icon {
      color: var(--info-600);
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      flex-shrink: 0;
    }

    .recommendation-text {
      flex: 1;

      h4 {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--gray-900);
        margin: 0 0 var(--spacing-xs) 0;
      }

      p {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin: 0;
        line-height: 1.5;
      }
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
    font-size: 1.125rem;
  }

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: var(--info-500);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .code-grid {
    grid-template-columns: 1fr 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-xl);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);

    .header-content {
      flex-direction: column;
      gap: var(--spacing-lg);
      text-align: center;
    }

    .header-left {
      flex-direction: column;
      gap: var(--spacing-md);

      .header-text {
        .page-title {
          font-size: 2rem;
        }
      }
    }

    .header-actions {
      width: 100%;
      justify-content: center;
    }
  }

  .stats-grid, .financial-grid, .code-grid {
    grid-template-columns: 1fr;
  }

  .content {
    padding: var(--spacing-xl) 0;
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-left {
      .header-text {
        .page-title {
          font-size: 1.75rem;
        }

        .page-subtitle {
          font-size: 1rem;
        }
      }
    }

    .header-actions {
      flex-direction: column;
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  .stat-card {
    .mat-mdc-card-content {
      flex-direction: column !important;
      text-align: center !important;
      gap: var(--spacing-md) !important;
    }
  }

  .financial-card {
    .mat-mdc-card-content {
      .amount {
        font-size: 2rem;
      }
    }
  }

  .usage-info {
    .usage-percentage {
      .percentage-value {
        font-size: 2rem;
      }
    }
  }
}
