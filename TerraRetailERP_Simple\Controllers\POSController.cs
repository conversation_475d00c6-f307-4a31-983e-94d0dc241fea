using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("🛒 Point of Sale (POS)")]
    public class POSController : ControllerBase
    {
        private readonly AppDbContext _context;

        public POSController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("products/search")]
        public async Task<ActionResult> SearchProducts(
            [FromQuery] string? search = null,
            [FromQuery] string? barcode = null,
            [FromQuery] int branchId = 1)
        {
            try
            {
                var query = _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Unit)
                    .Where(p => p.IsActive);

                if (!string.IsNullOrEmpty(barcode))
                {
                    query = query.Where(p => p.Barcode == barcode);
                }
                else if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.NameAr.Contains(search) || 
                                           p.NameEn!.Contains(search) || 
                                           p.ProductCode.Contains(search));
                }

                var products = await query
                    .Select(p => new
                    {
                        p.Id,
                        p.ProductCode,
                        p.NameAr,
                        p.NameEn,
                        p.Barcode,
                        p.BasePrice,
                        CategoryName = p.Category.NameAr,
                        UnitName = p.Unit.NameAr,
                        UnitSymbol = p.Unit.Symbol,
                        // Get stock for this branch
                        Stock = _context.ProductStocks
                            .Where(ps => ps.ProductId == p.Id && ps.BranchId == branchId)
                            .Select(ps => new
                            {
                                ps.AvailableQuantity,
                                ps.AverageCostPrice,
                                ps.IsAvailableForSale
                            })
                            .FirstOrDefault()
                    })
                    .Take(20)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم البحث عن المنتجات بنجاح",
                    data = products
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("customers/search")]
        public async Task<ActionResult> SearchCustomers([FromQuery] string? search = null)
        {
            try
            {
                var query = _context.Customers
                    .Where(c => c.IsActive);

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(c => c.NameAr.Contains(search) || 
                                           c.Phone1!.Contains(search) ||
                                           c.CustomerCode.Contains(search));
                }

                var customers = await query
                    .Select(c => new
                    {
                        c.Id,
                        c.CustomerCode,
                        c.NameAr,
                        c.Phone1,
                        c.CurrentBalance
                    })
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم البحث عن العملاء بنجاح",
                    data = customers
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("quick-sale")]
        public async Task<ActionResult> CreateQuickSale(POSQuickSaleRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate invoice number
                var lastSale = await _context.Sales
                    .OrderByDescending(s => s.Id)
                    .FirstOrDefaultAsync();

                var nextId = (lastSale?.Id ?? 0) + 1;
                var invoiceNumber = $"POS{nextId:D8}";

                // Calculate totals
                var subTotal = request.Items.Sum(i => i.Quantity * i.UnitPrice);
                var discountAmount = subTotal * (request.DiscountPercentage / 100);
                var taxableAmount = subTotal - discountAmount;
                var taxAmount = taxableAmount * (request.TaxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount;

                var sale = new Sale
                {
                    InvoiceNumber = invoiceNumber,
                    CustomerId = request.CustomerId,
                    CustomerName = request.CustomerName ?? "عميل نقدي",
                    BranchId = request.BranchId,
                    UserId = request.UserId,
                    InvoiceDate = DateTime.Now,
                    Status = 1, // Active
                    SaleType = request.SaleType,
                    SubTotal = subTotal,
                    DiscountPercentage = request.DiscountPercentage,
                    DiscountAmount = discountAmount,
                    TaxPercentage = request.TaxPercentage,
                    TaxAmount = taxAmount,
                    TotalAmount = totalAmount,
                    PaidAmount = totalAmount, // POS is always fully paid
                    RemainingAmount = 0,
                    Notes = "بيع نقطة بيع",
                    CreatedAt = DateTime.Now
                };

                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Add sale items and update stock
                int lineNumber = 1;
                foreach (var item in request.Items)
                {
                    // Check stock availability
                    var stock = await _context.ProductStocks
                        .FirstOrDefaultAsync(ps => ps.ProductId == item.ProductId && ps.BranchId == request.BranchId);

                    if (stock == null || stock.AvailableQuantity < item.Quantity)
                    {
                        var product = await _context.Products.FindAsync(item.ProductId);
                        throw new Exception($"الكمية المطلوبة غير متوفرة للمنتج {product?.NameAr}");
                    }

                    var lineTotal = item.Quantity * item.UnitPrice;
                    var lineDiscountAmount = lineTotal * (item.DiscountPercentage / 100);
                    var netLineTotal = lineTotal - lineDiscountAmount;
                    var lineTaxAmount = netLineTotal * (item.TaxPercentage / 100);
                    var finalTotal = netLineTotal + lineTaxAmount;

                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        LineNumber = lineNumber++,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        UnitCostPrice = stock.AverageCostPrice,
                        DiscountPercentage = item.DiscountPercentage,
                        DiscountAmount = lineDiscountAmount,
                        LineTotal = lineTotal,
                        TaxPercentage = item.TaxPercentage,
                        TaxAmount = lineTaxAmount,
                        FinalTotal = finalTotal,
                        CreatedAt = DateTime.Now
                    };

                    _context.SaleItems.Add(saleItem);

                    // Update stock
                    stock.AvailableQuantity -= item.Quantity;
                    stock.TotalOutQuantity += item.Quantity;
                    stock.LastMovementDate = DateTime.Now;
                    stock.UpdatedAt = DateTime.Now;
                    stock.StockValue = stock.AvailableQuantity * stock.AverageCostPrice;

                    // Create stock movement
                    var movement = new StockMovement
                    {
                        ProductId = item.ProductId,
                        BranchId = request.BranchId,
                        MovementNumber = invoiceNumber,
                        MovementType = 2, // Out
                        MovementReason = "POS Sale",
                        Quantity = item.Quantity,
                        UnitCost = stock.AverageCostPrice,
                        TotalCost = item.Quantity * stock.AverageCostPrice,
                        BalanceBefore = stock.AvailableQuantity + item.Quantity,
                        BalanceAfter = stock.AvailableQuantity,
                        MovementDate = DateTime.Now,
                        SourceType = "POS",
                        SourceId = sale.Id,
                        UserId = request.UserId,
                        CreatedAt = DateTime.Now
                    };

                    _context.StockMovements.Add(movement);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم إتمام البيع بنجاح",
                    data = new
                    {
                        sale.Id,
                        sale.InvoiceNumber,
                        sale.TotalAmount,
                        sale.InvoiceDate,
                        ItemsCount = request.Items.Count
                    }
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("daily-summary")]
        public async Task<ActionResult> GetDailySummary([FromQuery] int branchId = 1, [FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;

                var summary = await _context.Sales
                    .Where(s => s.BranchId == branchId && 
                               s.InvoiceDate.Date == targetDate && 
                               s.Status == 1)
                    .GroupBy(s => 1)
                    .Select(g => new
                    {
                        TotalSales = g.Count(),
                        TotalAmount = g.Sum(s => s.TotalAmount),
                        TotalDiscount = g.Sum(s => s.DiscountAmount),
                        TotalTax = g.Sum(s => s.TaxAmount),
                        CashSales = g.Where(s => s.SaleType == 1).Sum(s => s.TotalAmount),
                        CreditSales = g.Where(s => s.SaleType == 2).Sum(s => s.TotalAmount)
                    })
                    .FirstOrDefaultAsync();

                var topProducts = await _context.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.BranchId == branchId && 
                               si.Sale.InvoiceDate.Date == targetDate && 
                               si.Sale.Status == 1)
                    .GroupBy(si => new { si.ProductId, si.Product.NameAr })
                    .Select(g => new
                    {
                        ProductName = g.Key.NameAr,
                        Quantity = g.Sum(si => si.Quantity),
                        Amount = g.Sum(si => si.FinalTotal)
                    })
                    .OrderByDescending(p => p.Quantity)
                    .Take(5)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب ملخص اليوم بنجاح",
                    data = new
                    {
                        date = targetDate,
                        summary = summary ?? new { TotalSales = 0, TotalAmount = 0m, TotalDiscount = 0m, TotalTax = 0m, CashSales = 0m, CreditSales = 0m },
                        topProducts
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("payment-methods")]
        public async Task<ActionResult> GetPaymentMethods()
        {
            try
            {
                var paymentMethods = await _context.PaymentMethods
                    .Where(pm => pm.IsActive)
                    .OrderBy(pm => pm.DisplayOrder)
                    .Select(pm => new
                    {
                        pm.Id,
                        pm.NameAr,
                        pm.NameEn,
                        pm.Code,
                        pm.PaymentType,
                        pm.Icon,
                        pm.Color,
                        pm.IsDefault
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب طرق الدفع بنجاح",
                    data = paymentMethods
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs
    public class POSQuickSaleRequest
    {
        public int? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public int BranchId { get; set; } = 1;
        public int UserId { get; set; } = 1;
        public int SaleType { get; set; } = 1; // 1=Cash, 2=Credit
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
        public List<POSQuickSaleItemRequest> Items { get; set; } = new();
    }

    public class POSQuickSaleItemRequest
    {
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal TaxPercentage { get; set; } = 0;
    }
}
