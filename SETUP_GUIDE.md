# 🚀 دليل إعداد وتشغيل Terra Retail ERP
## Setup and Installation Guide

### 📋 المتطلبات الأساسية | Prerequisites

#### البرامج المطلوبة | Required Software
- **SQL Server 2019+** مع SQL Server Management Studio
- **.NET 8.0 SDK** - [تحميل من هنا](https://dotnet.microsoft.com/download)
- **Node.js 18+** - [تحميل من هنا](https://nodejs.org)
- **Angular CLI** - يتم تثبيته عبر npm
- **Visual Studio 2022** أو **VS Code** (اختياري)

#### إعدادات قاعدة البيانات | Database Settings
- **Server**: localhost
- **Username**: sa
- **Password**: @a123admin4
- **Database**: TerraRetailERP

---

## 🛠️ خطوات التثبيت | Installation Steps

### 1️⃣ إعداد قاعدة البيانات | Database Setup

```bash
# انتقل إلى مجلد قاعدة البيانات
cd database

# تشغيل سكريبت إنشاء قاعدة البيانات
run_database.bat

# إصلاح ترميز النصوص العربية (إذا لزم الأمر)
fix_encoding.bat
```

### 2️⃣ تشغيل Backend API

```bash
# انتقل إلى مجلد API
cd src/Terra.Retail.API

# استعادة الحزم
dotnet restore

# تشغيل التطبيق
dotnet run
```

أو استخدم الملف المختصر:
```bash
run_api_simple.bat
```

### 3️⃣ تشغيل Frontend (Angular) - قريباً

```bash
# انتقل إلى مجلد Angular (سيتم إنشاؤه لاحقاً)
cd src/Terra.Retail.Web

# تثبيت الحزم
npm install

# تشغيل التطبيق
ng serve
```

---

## 🌐 الوصول للتطبيق | Application Access

### API Documentation
- **URL**: http://localhost:5000
- **Swagger UI**: http://localhost:5000/swagger

### Angular Application (قريباً)
- **URL**: http://localhost:4200

---

## 🧪 اختبار النظام | System Testing

### اختبار قاعدة البيانات
```bash
test_simple.bat
```

### اختبار API Endpoints

#### 1. فحص حالة النظام
```bash
curl http://localhost:5000/health
```

#### 2. الحصول على إحصائيات النظام
```bash
curl http://localhost:5000/api/system/statistics
```

#### 3. الحصول على قائمة العملاء
```bash
curl http://localhost:5000/api/customers
```

#### 4. الحصول على قائمة المنتجات
```bash
curl http://localhost:5000/api/products
```

#### 5. الحصول على قائمة الفروع
```bash
curl http://localhost:5000/api/branches
```

---

## 📊 البيانات الأولية | Initial Data

النظام يأتي مع البيانات الأولية التالية:

### وحدات القياس | Units
- قطعة (PC)
- كيلوجرام (KG)
- جرام (G)
- متر (M)
- سنتيمتر (CM)
- لتر (L)
- مليلتر (ML)
- علبة (BOX)
- كرتون (CTN)
- دزينة (DOZ)

### أنواع العملاء | Customer Types
- عميل عادي
- عميل جملة
- عميل VIP
- عميل مؤسسي
- عميل حكومي

### فئات الأسعار | Price Categories
- سعر التجزئة (افتراضي)
- سعر الجملة (-10%)
- سعر VIP (-15%)
- سعر المؤسسات (-8%)
- سعر الموظفين (-20%)

### فئات المنتجات | Product Categories
- عام
- أغذية ومشروبات
- إلكترونيات
- ملابس وأزياء
- منزل وحديقة
- صحة وجمال
- رياضة وترفيه
- كتب وقرطاسية
- ألعاب وهدايا
- سيارات وقطع غيار

---

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشكلة الاتصال بقاعدة البيانات
1. تأكد من تشغيل SQL Server
2. تحقق من صحة اسم المستخدم وكلمة المرور
3. تأكد من وجود قاعدة البيانات TerraRetailERP

### مشكلة ترميز النصوص العربية
```bash
cd database
fix_encoding.bat
```

### مشكلة في تشغيل API
1. تأكد من تثبيت .NET 8.0 SDK
2. تحقق من عدم استخدام المنفذ 5000
3. تشغيل الأمر: `dotnet restore` في مجلد API

### مشكلة في الحزم
```bash
# تنظيف cache NuGet
dotnet nuget locals all --clear

# استعادة الحزم
dotnet restore
```

---

## 📁 هيكل المشروع | Project Structure

```
Terra.Retail.ERP/
├── src/
│   ├── Terra.Retail.API/          # Web API
│   ├── Terra.Retail.Core/         # Domain Models
│   ├── Terra.Retail.Infrastructure/ # Data Access
│   ├── Terra.Retail.Shared/       # Shared DTOs
│   └── Terra.Retail.Web/          # Angular Frontend (قريباً)
├── database/                      # Database Scripts
│   ├── create_database.sql        # إنشاء قاعدة البيانات
│   ├── fix_arabic_encoding.sql    # إصلاح الترميز
│   ├── run_database.bat          # تشغيل إنشاء قاعدة البيانات
│   └── fix_encoding.bat          # تشغيل إصلاح الترميز
├── docs/                         # Documentation
├── *.bat                         # ملفات التشغيل السريع
└── README.md                     # الوثائق الرئيسية
```

---

## 🎯 الخطوات التالية | Next Steps

1. **إنشاء Angular Frontend** - واجهة المستخدم
2. **إضافة نظام المصادقة** - JWT Authentication
3. **تطوير وحدة POS** - نقطة البيع
4. **إضافة التقارير** - Reports Module
5. **تطوير تطبيق الموبايل** - Mobile App

---

## 📞 الدعم والتواصل | Support & Contact

- **المطور**: فريق Terra Retail
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.terraretail.com

---

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**Terra Retail ERP** - نظام إدارة متكامل للمستقبل 🚀
