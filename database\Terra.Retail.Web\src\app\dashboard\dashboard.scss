/* Terra Retail ERP - Professional Dashboard */

.dashboard-container {
  min-height: 100vh;
  background: #f5f7fa;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  font-size: 2.5rem !important;
  color: rgba(255, 255, 255, 0.9);
}

.logo-text h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.logo-text p {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.8;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
}

.user-branch {
  font-size: 0.8rem;
  opacity: 0.8;
}

.user-avatar {
  font-size: 2.5rem !important;
  opacity: 0.9;
}

.user-menu-btn {
  color: white;
}

/* Main Content */
.dashboard-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.welcome-content h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.welcome-content p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.welcome-actions {
  display: flex;
  gap: 1rem;
}

.welcome-actions button {
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.sales-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.customers-card {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.products-card {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.orders-card {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-icon {
  font-size: 3rem !important;
  opacity: 0.8;
}

.stat-info h3 {
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
  opacity: 0.9;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.stat-change {
  font-weight: 600;

  &.positive {
    color: #27ae60;
  }

  &.negative {
    color: #e74c3c;
  }

  &.neutral {
    color: #f39c12;
  }
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 3rem;
}

.quick-actions h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 15px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  mat-card-content {
    text-align: center;
    padding: 2rem 1rem;
  }
}

.action-icon {
  font-size: 3rem !important;
  color: #667eea;
  margin-bottom: 1rem;
}

.action-card h4 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.action-card p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

/* System Status */
.system-status {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.system-status h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.status-value {
  font-weight: 600;
  color: #2c3e50;
}

.status-online {
  color: #27ae60;
}

.status-offline {
  color: #e74c3c;
}

/* Footer */
.dashboard-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 1.5rem;
  margin-top: 2rem;
}

.dashboard-footer p {
  margin: 0.25rem 0;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .welcome-section {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .welcome-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .status-grid {
    grid-template-columns: 1fr;
  }
}

/* Material Design Overrides */
::ng-deep {
  .success-snackbar {
    background: #27ae60 !important;
    color: white !important;
  }

  .error-snackbar {
    background: #e74c3c !important;
    color: white !important;
  }
}