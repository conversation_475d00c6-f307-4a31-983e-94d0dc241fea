@echo off
title Terra Retail ERP - System Launcher
color 0A

:MENU
cls
echo.
echo ========================================
echo    Terra Retail ERP - System Launcher
echo ========================================
echo.
echo Please select an option:
echo.
echo 1. Setup Database (First Time)
echo 2. Fix Arabic Encoding
echo 3. Start API Server
echo 4. Test System
echo 5. View System Info
echo 6. Open API Documentation
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto SETUP_DB
if "%choice%"=="2" goto FIX_ENCODING
if "%choice%"=="3" goto START_API
if "%choice%"=="4" goto TEST_SYSTEM
if "%choice%"=="5" goto SYSTEM_INFO
if "%choice%"=="6" goto OPEN_DOCS
if "%choice%"=="7" goto EXIT
goto MENU

:SETUP_DB
cls
echo Setting up database...
cd database
call run_database.bat
cd ..
pause
goto MENU

:FIX_ENCODING
cls
echo Fixing Arabic encoding...
cd database
sqlcmd -S localhost -U sa -P "@a123admin4" -i fix_arabic_encoding.sql
cd ..
pause
goto MENU

:START_API
cls
echo Starting API Server...
echo API will be available at: http://localhost:5000
echo Press Ctrl+C to stop the server
echo.
cd src\Terra.Retail.API
dotnet run --urls "http://localhost:5000"
cd ..\..
pause
goto MENU

:TEST_SYSTEM
cls
echo Testing system...
call test_simple.bat
goto MENU

:SYSTEM_INFO
cls
echo ========================================
echo    Terra Retail ERP - System Information
echo ========================================
echo.
echo System Name: Terra Retail ERP
echo Version: 1.0.0
echo Technology: ASP.NET Core 8.0 + Angular
echo Database: SQL Server
echo.
echo API URL: http://localhost:5000
echo Documentation: http://localhost:5000/swagger
echo.
echo Database Server: localhost
echo Database Name: TerraRetailERP
echo Username: sa
echo.
echo Project Structure:
echo - src/Terra.Retail.API/          (Web API)
echo - src/Terra.Retail.Core/         (Domain Models)
echo - src/Terra.Retail.Infrastructure/ (Data Access)
echo - src/Terra.Retail.Shared/       (Shared DTOs)
echo - database/                      (Database Scripts)
echo.
pause
goto MENU

:OPEN_DOCS
cls
echo Opening API Documentation...
start http://localhost:5000
pause
goto MENU

:EXIT
cls
echo Thank you for using Terra Retail ERP!
echo.
pause
exit
