using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الحسابات المالية
    /// </summary>
    public class Account : BaseEntity
    {
        /// <summary>
        /// رقم الحساب
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string AccountNumber { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? NameEn { get; set; }

        /// <summary>
        /// نوع الحساب
        /// </summary>
        public AccountType AccountType { get; set; }

        /// <summary>
        /// طبيعة الحساب
        /// </summary>
        public AccountNature AccountNature { get; set; }

        /// <summary>
        /// الحساب الأب
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب في الهرم
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// مسار الحساب الهرمي
        /// </summary>
        [MaxLength(200)]
        public string? Path { get; set; }

        /// <summary>
        /// وصف الحساب
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// إجمالي المدين
        /// </summary>
        public decimal TotalDebit { get; set; } = 0;

        /// <summary>
        /// إجمالي الدائن
        /// </summary>
        public decimal TotalCredit { get; set; } = 0;

        /// <summary>
        /// هل الحساب نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الحساب نهائي (لا يمكن إضافة حسابات فرعية)
        /// </summary>
        public bool IsFinal { get; set; } = false;

        /// <summary>
        /// هل الحساب مدمج في النظام
        /// </summary>
        public bool IsSystemAccount { get; set; } = false;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// هل يسمح بالرصيد السالب
        /// </summary>
        public bool AllowNegativeBalance { get; set; } = false;

        /// <summary>
        /// الحد الأقصى للرصيد
        /// </summary>
        public decimal? MaxBalance { get; set; }

        /// <summary>
        /// الحد الأدنى للرصيد
        /// </summary>
        public decimal? MinBalance { get; set; }

        /// <summary>
        /// تاريخ آخر حركة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        /// <summary>
        /// معرف مركز التكلفة
        /// </summary>
        public int? CostCenterId { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(1000)]
        public string? AdditionalNotes { get; set; }

        // Navigation Properties
        public virtual Account? ParentAccount { get; set; }
        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual CostCenter? CostCenter { get; set; }
        public virtual ICollection<JournalEntryItem> JournalEntryItems { get; set; } = new List<JournalEntryItem>();
        public virtual ICollection<PaymentMethod> PaymentMethods { get; set; } = new List<PaymentMethod>();
        public virtual ICollection<CashBox> CashBoxes { get; set; } = new List<CashBox>();
    }

    /// <summary>
    /// أنواع الحسابات
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// أصول
        /// </summary>
        Assets = 1,

        /// <summary>
        /// خصوم
        /// </summary>
        Liabilities = 2,

        /// <summary>
        /// حقوق الملكية
        /// </summary>
        Equity = 3,

        /// <summary>
        /// إيرادات
        /// </summary>
        Revenue = 4,

        /// <summary>
        /// مصروفات
        /// </summary>
        Expenses = 5
    }

    /// <summary>
    /// طبيعة الحساب
    /// </summary>
    public enum AccountNature
    {
        /// <summary>
        /// مدين
        /// </summary>
        Debit = 1,

        /// <summary>
        /// دائن
        /// </summary>
        Credit = 2
    }
}
