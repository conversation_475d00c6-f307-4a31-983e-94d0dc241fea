/* Customer Form Styles */
.customer-form-container {
  padding: 20px;
  direction: rtl;
  max-width: 1200px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 12px;

      i {
        color: #3498db;
      }
    }

    .page-subtitle {
      color: #7f8c8d;
      margin: 0;
      font-size: 16px;
    }
  }

  .header-actions {
    .btn {
      padding: 12px 24px;
      font-weight: 500;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

/* Form Container */
.customer-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Form Sections */
.form-sections {
  padding: 30px;
}

.form-section {
  margin-bottom: 40px;
  
  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 25px 0;
    padding: 15px 0;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      color: #3498db;
      font-size: 18px;
    }
  }
}

/* Form Rows and Groups */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;

    &.required::after {
      content: ' *';
      color: #e74c3c;
      font-weight: bold;
    }
  }

  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s;
    background-color: #fff;

    &:focus {
      border-color: #3498db;
      box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
      outline: none;
    }

    &:read-only {
      background-color: #f8f9fa;
      color: #6c757d;
    }

    &.is-invalid {
      border-color: #e74c3c;
      box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
    }

    &::placeholder {
      color: #adb5bd;
    }
  }

  textarea.form-control {
    resize: vertical;
    min-height: 80px;
  }

  .form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;

    i {
      color: #17a2b8;
    }
  }

  .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: #e74c3c;
  }
}

/* Input Groups */
.input-group {
  display: flex;
  width: 100%;

  .form-control {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .input-group-append {
    .input-group-text {
      padding: 12px 16px;
      background-color: #e9ecef;
      border: 1px solid #ddd;
      border-right: none;
      border-radius: 0 8px 8px 0;
      font-size: 14px;
      color: #495057;
    }
  }
}

/* Input with Add Button */
.input-with-add {
  display: flex;
  gap: 8px;
  align-items: stretch;

  .form-control {
    flex: 1;
  }

  .btn-add {
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    font-size: 14px;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }

    i {
      font-size: 12px;
    }
  }
}

/* Checkboxes */
.form-check {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;

  .form-check-input {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
  }

  .form-check-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin: 0;
  }
}

/* Form Actions */
.form-actions {
  padding: 25px 30px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 15px;
  justify-content: flex-start;

  .btn {
    padding: 12px 24px;
    font-weight: 500;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-primary {
      background-color: #3498db;
      color: white;

      &:hover:not(:disabled) {
        background-color: #2980b9;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      color: white;

      &:hover:not(:disabled) {
        background-color: #545b62;
      }
    }

    &.btn-outline-secondary {
      background-color: transparent;
      color: #6c757d;
      border: 1px solid #6c757d;

      &:hover:not(:disabled) {
        background-color: #6c757d;
        color: white;
      }
    }

    i.fa-spin {
      animation: spin 1s linear infinite;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .customer-form-container {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .form-sections {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form-actions {
    flex-direction: column;
    
    .btn {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .form-sections {
    padding: 15px;
  }

  .form-group .form-control {
    padding: 10px 12px;
  }

  .section-title {
    font-size: 18px;
  }
}

/* Animation for spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Special styling for required fields */
.form-group:has(.form-label.required) .form-control {
  border-left: 3px solid #3498db;
}

/* Required fields with red border initially */
.form-control.required-field {
  border: 2px solid #e74c3c !important;
  box-shadow: 0 0 0 0.1rem rgba(231, 76, 60, 0.15) !important;
}

/* Remove red border when field has value */
.form-control.required-field.has-value {
  border: 1px solid #ddd !important;
  box-shadow: none !important;
}

/* Focus state for required fields */
.form-control.required-field:focus {
  border-color: #3498db !important;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

/* Success state */
.form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Focus within form groups */
.form-group:focus-within .form-label {
  color: #3498db;
}

/* Hover effects */
.form-control:hover:not(:focus):not(:read-only) {
  border-color: #bdc3c7;
}

/* Select styling */
select.form-control {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-left: 40px;
}
