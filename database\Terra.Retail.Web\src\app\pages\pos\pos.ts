import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatMenuModule } from '@angular/material/menu';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

interface Product {
  id: number;
  nameAr: string;
  nameEn: string;
  productCode: string;
  price: number;
  stock: number;
  categoryId: number;
}

interface Category {
  id: number;
  nameAr: string;
  nameEn: string;
}

interface Customer {
  id: number;
  fullName: string;
  phoneNumber: string;
}

interface CartItem {
  product: Product;
  quantity: number;
}

interface PaymentMethod {
  id: string;
  nameAr: string;
  icon: string;
}

@Component({
  selector: 'app-pos',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HttpClientModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatMenuModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  templateUrl: './pos.html',
  styleUrls: ['./pos.scss']
})
export class Pos implements OnInit {
  currentUser: any = null;
  searchTerm: string = '';
  selectedCategory: number | null = null;
  selectedCustomer: number | null = null;
  selectedPaymentMethod: string = 'cash';
  paidAmount: number = 0;
  discount: number = 0;
  taxRate: number = 14; // 14% VAT in Egypt
  isProcessing: boolean = false;

  products: Product[] = [];
  filteredProducts: Product[] = [];
  categories: Category[] = [];
  customers: Customer[] = [];
  cartItems: CartItem[] = [];

  paymentMethods: PaymentMethod[] = [
    { id: 'cash', nameAr: 'نقدي', icon: 'money' },
    { id: 'card', nameAr: 'بطاقة', icon: 'credit_card' },
    { id: 'transfer', nameAr: 'تحويل', icon: 'account_balance' }
  ];

  constructor(
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadCurrentUser();
    this.loadProducts();
    this.loadCategories();
    this.loadCustomers();
  }

  loadCurrentUser() {
    const userData = localStorage.getItem('currentUser');
    if (userData) {
      this.currentUser = JSON.parse(userData);
    }
  }

  loadProducts() {
    this.http.get<any>('http://localhost:5000/api/simple/products').subscribe({
      next: (response) => {
        this.products = response.products || [];
        this.filteredProducts = [...this.products];
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.showMessage('خطأ في تحميل المنتجات', true);
      }
    });
  }

  loadCategories() {
    this.http.get<any>('http://localhost:5000/api/simple/categories').subscribe({
      next: (response) => {
        this.categories = response.categories || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  loadCustomers() {
    this.http.get<any>('http://localhost:5000/api/simple/customers').subscribe({
      next: (response) => {
        this.customers = response.customers || [];
      },
      error: (error) => {
        console.error('Error loading customers:', error);
      }
    });
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('ar-EG');
  }

  searchProducts() {
    this.filteredProducts = this.products.filter(product =>
      product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase())
    );

    if (this.selectedCategory) {
      this.filteredProducts = this.filteredProducts.filter(product =>
        product.categoryId === this.selectedCategory
      );
    }
  }

  selectCategory(categoryId: number) {
    this.selectedCategory = this.selectedCategory === categoryId ? null : categoryId;
    this.searchProducts();
  }

  addToCart(product: Product) {
    if (product.stock <= 0) {
      this.showMessage('هذا المنتج غير متوفر في المخزون', true);
      return;
    }

    const existingItem = this.cartItems.find(item => item.product.id === product.id);

    if (existingItem) {
      if (existingItem.quantity < product.stock) {
        existingItem.quantity++;
      } else {
        this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);
      }
    } else {
      this.cartItems.push({ product, quantity: 1 });
    }
  }

  removeFromCart(index: number) {
    this.cartItems.splice(index, 1);
  }

  increaseQuantity(index: number) {
    const item = this.cartItems[index];
    if (item.quantity < item.product.stock) {
      item.quantity++;
    } else {
      this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);
    }
  }

  decreaseQuantity(index: number) {
    const item = this.cartItems[index];
    if (item.quantity > 1) {
      item.quantity--;
    }
  }

  getItemTotal(item: CartItem): number {
    return item.product.price * item.quantity;
  }

  getSubtotal(): number {
    return this.cartItems.reduce((total, item) => total + this.getItemTotal(item), 0);
  }

  getTax(): number {
    return this.getSubtotal() * (this.taxRate / 100);
  }

  getTotal(): number {
    return this.getSubtotal() + this.getTax() - this.discount;
  }

  getChange(): number {
    return this.paidAmount - this.getTotal();
  }

  selectPaymentMethod(methodId: string) {
    this.selectedPaymentMethod = methodId;
    if (methodId !== 'cash') {
      this.paidAmount = this.getTotal();
    }
  }

  calculateChange() {
    // Change is calculated automatically in getChange()
  }

  canCompleteSale(): boolean {
    if (this.cartItems.length === 0) return false;
    if (this.selectedPaymentMethod === 'cash') {
      return this.paidAmount >= this.getTotal();
    }
    return true;
  }

  completeSale() {
    if (!this.canCompleteSale()) return;

    this.isProcessing = true;

    const saleData = {
      customerId: this.selectedCustomer,
      items: this.cartItems.map(item => ({
        productId: item.product.id,
        quantity: item.quantity,
        price: item.product.price
      })),
      subtotal: this.getSubtotal(),
      tax: this.getTax(),
      discount: this.discount,
      total: this.getTotal(),
      paymentMethod: this.selectedPaymentMethod,
      paidAmount: this.paidAmount
    };

    // محاكاة إتمام البيع
    setTimeout(() => {
      this.isProcessing = false;
      this.showMessage('تم إتمام البيع بنجاح');
      this.clearCart();
      this.printReceipt(saleData);
    }, 2000);
  }

  clearCart() {
    this.cartItems = [];
    this.selectedCustomer = null;
    this.paidAmount = 0;
    this.discount = 0;
    this.selectedPaymentMethod = 'cash';
  }

  newSale() {
    this.clearCart();
    this.showMessage('بيع جديد - تم مسح الفاتورة');
  }

  openDrawer() {
    this.showMessage('تم فتح درج النقدية');
  }

  scanBarcode() {
    this.showMessage('مسح الباركود - قيد التطوير');
  }

  addNewCustomer() {
    this.showMessage('إضافة عميل جديد - قيد التطوير');
  }

  viewSalesHistory() {
    this.showMessage('تاريخ المبيعات - قيد التطوير');
  }

  printLastReceipt() {
    this.showMessage('طباعة آخر فاتورة - قيد التطوير');
  }

  endOfDay() {
    this.showMessage('إقفال اليوم - قيد التطوير');
  }

  printReceipt(saleData: any) {
    console.log('Printing receipt:', saleData);
    this.showMessage('تم طباعة الفاتورة');
  }

  private showMessage(message: string, isError = false) {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top',
      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']
    });
  }
}
