using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("📊 Reports & Analytics")]
    public class ReportsController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public ReportsController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult> GetDashboard([FromQuery] int? branchId = null)
        {
            try
            {
                var today = DateTime.Today;
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var thisYear = new DateTime(today.Year, 1, 1);

                // Sales Summary
                var salesQuery = _context.Sales.AsQueryable();
                if (branchId.HasValue)
                    salesQuery = salesQuery.Where(s => s.BranchId == branchId);

                var salesSummary = new
                {
                    Today = await salesQuery
                        .Where(s => s.InvoiceDate.Date == today && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m },

                    ThisMonth = await salesQuery
                        .Where(s => s.InvoiceDate >= thisMonth && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m },

                    ThisYear = await salesQuery
                        .Where(s => s.InvoiceDate >= thisYear && s.Status == 1)
                        .GroupBy(s => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m }
                };

                // Purchase Summary
                var purchaseQuery = _context.PurchaseInvoices.AsQueryable();
                if (branchId.HasValue)
                    purchaseQuery = purchaseQuery.Where(p => p.BranchId == branchId);

                var purchaseSummary = new
                {
                    ThisMonth = await purchaseQuery
                        .Where(p => p.InvoiceDate >= thisMonth && p.Status != 3)
                        .GroupBy(p => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(p => p.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m },

                    ThisYear = await purchaseQuery
                        .Where(p => p.InvoiceDate >= thisYear && p.Status != 3)
                        .GroupBy(p => 1)
                        .Select(g => new { Count = g.Count(), Total = g.Sum(p => p.TotalAmount) })
                        .FirstOrDefaultAsync() ?? new { Count = 0, Total = 0m }
                };

                // Inventory Summary
                var inventoryQuery = _context.ProductStocks.AsQueryable();
                if (branchId.HasValue)
                    inventoryQuery = inventoryQuery.Where(ps => ps.BranchId == branchId);

                var inventorySummary = new
                {
                    TotalProducts = await inventoryQuery.CountAsync(),
                    TotalValue = await inventoryQuery.SumAsync(ps => ps.StockValue),
                    LowStockItems = await inventoryQuery
                        .Where(ps => ps.AvailableQuantity <= ps.BranchMinStock)
                        .CountAsync(),
                    OutOfStockItems = await inventoryQuery
                        .Where(ps => ps.AvailableQuantity <= 0)
                        .CountAsync()
                };

                // Customer & Supplier Summary
                var customerQuery = _context.Customers.AsQueryable();
                var supplierQuery = _context.Suppliers.Where(s => !s.IsDeleted).AsQueryable();

                if (branchId.HasValue)
                    customerQuery = customerQuery.Where(c => c.BranchId == branchId);

                var customerSummary = new
                {
                    TotalCustomers = await customerQuery.Where(c => c.IsActive).CountAsync(),
                    TotalReceivables = await customerQuery.SumAsync(c => c.CurrentBalance)
                };

                var supplierSummary = new
                {
                    TotalSuppliers = await supplierQuery.Where(s => s.IsActive).CountAsync(),
                    TotalPayables = await supplierQuery.SumAsync(s => s.CurrentBalance)
                };

                // Employee Summary (if HR module is enabled)
                var employeeQuery = _context.Employees.AsQueryable();
                if (branchId.HasValue)
                    employeeQuery = employeeQuery.Where(e => e.BranchId == branchId);

                var employeeSummary = new
                {
                    TotalEmployees = await employeeQuery.Where(e => e.IsActive).CountAsync(),
                    PresentToday = await _context.AttendanceRecords
                        .Where(ar => ar.ShiftDate.Date == today && ar.AttendanceStatus == "Present")
                        .CountAsync()
                };

                return Ok(new
                {
                    sales = salesSummary,
                    purchases = purchaseSummary,
                    inventory = inventorySummary,
                    customers = customerSummary,
                    suppliers = supplierSummary,
                    employees = employeeSummary,
                    generatedAt = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("sales-chart")]
        public async Task<ActionResult> GetSalesChart(
            [FromQuery] int? branchId = null,
            [FromQuery] string period = "month", // day, week, month, year
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var endDate = toDate ?? DateTime.Today;
                var startDate = fromDate ?? period switch
                {
                    "day" => endDate.AddDays(-30),
                    "week" => endDate.AddDays(-7 * 12), // 12 weeks
                    "month" => endDate.AddMonths(-12),
                    "year" => endDate.AddYears(-5),
                    _ => endDate.AddMonths(-12)
                };

                var query = _context.Sales
                    .Where(s => s.InvoiceDate >= startDate && s.InvoiceDate <= endDate && s.Status == 1);

                if (branchId.HasValue)
                    query = query.Where(s => s.BranchId == branchId);

                var data = period switch
                {
                    "day" => (await query
                        .GroupBy(s => s.InvoiceDate.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count(), Total = g.Sum(s => s.TotalAmount) })
                        .OrderBy(x => x.Date)
                        .ToListAsync()).Cast<object>().ToList(),

                    "week" => (await query
                        .GroupBy(s => new { Year = s.InvoiceDate.Year, Week = (s.InvoiceDate.DayOfYear - 1) / 7 + 1 })
                        .Select(g => new {
                            Date = new DateTime(g.Key.Year, 1, 1).AddDays((g.Key.Week - 1) * 7),
                            Count = g.Count(),
                            Total = g.Sum(s => s.TotalAmount)
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync()).Cast<object>().ToList(),

                    "month" => (await query
                        .GroupBy(s => new { s.InvoiceDate.Year, s.InvoiceDate.Month })
                        .Select(g => new {
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Count = g.Count(),
                            Total = g.Sum(s => s.TotalAmount)
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync()).Cast<object>().ToList(),

                    "year" => (await query
                        .GroupBy(s => s.InvoiceDate.Year)
                        .Select(g => new {
                            Date = new DateTime(g.Key, 1, 1),
                            Count = g.Count(),
                            Total = g.Sum(s => s.TotalAmount)
                        })
                        .OrderBy(x => x.Date)
                        .ToListAsync()).Cast<object>().ToList(),

                    _ => new List<object>()
                };

                return Ok(data);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("top-products")]
        public async Task<ActionResult> GetTopProducts(
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int limit = 10,
            [FromQuery] string sortBy = "quantity") // quantity, amount
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.SaleItems
                    .Include(si => si.Product)
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate >= startDate && 
                               si.Sale.InvoiceDate <= endDate && 
                               si.Sale.Status == 1);

                if (branchId.HasValue)
                    query = query.Where(si => si.Sale.BranchId == branchId);

                var products = await query
                    .GroupBy(si => new { si.ProductId, si.Product.NameAr, si.Product.ProductCode })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.NameAr,
                        ProductCode = g.Key.ProductCode,
                        TotalQuantity = g.Sum(si => si.Quantity),
                        TotalAmount = g.Sum(si => si.FinalTotal),
                        TransactionCount = g.Count(),
                        AveragePrice = g.Average(si => si.UnitPrice)
                    })
                    .ToListAsync();

                var sortedProducts = sortBy.ToLower() switch
                {
                    "amount" => products.OrderByDescending(p => p.TotalAmount),
                    "transactions" => products.OrderByDescending(p => p.TransactionCount),
                    _ => products.OrderByDescending(p => p.TotalQuantity)
                };

                return Ok(sortedProducts.Take(limit));
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("inventory-status")]
        public async Task<ActionResult> GetInventoryStatus([FromQuery] int? branchId = null)
        {
            try
            {
                var query = _context.ProductStocks
                    .Include(ps => ps.Product)
                    .Include(ps => ps.Branch)
                    .AsQueryable();

                if (branchId.HasValue)
                    query = query.Where(ps => ps.BranchId == branchId);

                var lowStockItems = await query
                    .Where(ps => ps.AvailableQuantity <= ps.BranchMinStock && ps.BranchMinStock > 0)
                    .Select(ps => new
                    {
                        ps.ProductId,
                        ProductName = ps.Product.NameAr,
                        ProductCode = ps.Product.ProductCode,
                        BranchName = ps.Branch.NameAr,
                        ps.AvailableQuantity,
                        ps.BranchMinStock,
                        ps.BranchMaxStock,
                        ps.StockValue,
                        Status = ps.AvailableQuantity <= 0 ? "Out of Stock" : "Low Stock"
                    })
                    .OrderBy(ps => ps.AvailableQuantity)
                    .ToListAsync();

                var overStockItems = await query
                    .Where(ps => ps.AvailableQuantity > ps.BranchMaxStock && ps.BranchMaxStock > 0)
                    .Select(ps => new
                    {
                        ps.ProductId,
                        ProductName = ps.Product.NameAr,
                        ProductCode = ps.Product.ProductCode,
                        BranchName = ps.Branch.NameAr,
                        ps.AvailableQuantity,
                        ps.BranchMinStock,
                        ps.BranchMaxStock,
                        ps.StockValue,
                        Status = "Over Stock"
                    })
                    .OrderByDescending(ps => ps.AvailableQuantity)
                    .ToListAsync();

                var summary = new
                {
                    TotalItems = await query.CountAsync(),
                    LowStockCount = lowStockItems.Count(i => i.Status == "Low Stock"),
                    OutOfStockCount = lowStockItems.Count(i => i.Status == "Out of Stock"),
                    OverStockCount = overStockItems.Count,
                    TotalStockValue = await query.SumAsync(ps => ps.StockValue)
                };

                return Ok(new
                {
                    summary,
                    lowStockItems = lowStockItems.Take(20),
                    overStockItems = overStockItems.Take(20)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("financial-summary")]
        public async Task<ActionResult> GetFinancialSummary(
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddMonths(-1);
                var endDate = toDate ?? DateTime.Today;

                // Sales Revenue
                var salesQuery = _context.Sales
                    .Where(s => s.InvoiceDate >= startDate && s.InvoiceDate <= endDate && s.Status == 1);

                if (branchId.HasValue)
                    salesQuery = salesQuery.Where(s => s.BranchId == branchId);

                var salesRevenue = await salesQuery.SumAsync(s => s.TotalAmount);
                var salesCost = await _context.SaleItems
                    .Include(si => si.Sale)
                    .Where(si => si.Sale.InvoiceDate >= startDate && 
                               si.Sale.InvoiceDate <= endDate && 
                               si.Sale.Status == 1 &&
                               (!branchId.HasValue || si.Sale.BranchId == branchId))
                    .SumAsync(si => si.Quantity * si.UnitCostPrice);

                // Purchase Expenses
                var purchaseQuery = _context.PurchaseInvoices
                    .Where(p => p.InvoiceDate >= startDate && p.InvoiceDate <= endDate && p.Status != 3);

                if (branchId.HasValue)
                    purchaseQuery = purchaseQuery.Where(p => p.BranchId == branchId);

                var purchaseExpenses = await purchaseQuery.SumAsync(p => p.TotalAmount);

                // Receivables & Payables
                var receivables = await _context.Customers
                    .Where(c => !branchId.HasValue || c.BranchId == branchId)
                    .SumAsync(c => c.CurrentBalance);

                var payables = await _context.Suppliers
                    .Where(s => !s.IsDeleted)
                    .SumAsync(s => s.CurrentBalance);

                // Cash Flow
                var receipts = await _context.Receipts
                    .Where(r => r.ReceiptDate >= startDate && r.ReceiptDate <= endDate && r.Status == 1 &&
                              (!branchId.HasValue || r.BranchId == branchId))
                    .SumAsync(r => r.Amount);

                var payments = await _context.Payments
                    .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate && p.Status == 1 &&
                              (!branchId.HasValue || p.BranchId == branchId))
                    .SumAsync(p => p.Amount);

                return Ok(new
                {
                    revenue = new
                    {
                        sales = salesRevenue,
                        cost = salesCost,
                        grossProfit = salesRevenue - salesCost,
                        grossProfitMargin = salesRevenue > 0 ? ((salesRevenue - salesCost) / salesRevenue) * 100 : 0
                    },
                    expenses = new
                    {
                        purchases = purchaseExpenses
                    },
                    balances = new
                    {
                        receivables,
                        payables,
                        netPosition = receivables - payables
                    },
                    cashFlow = new
                    {
                        receipts,
                        payments,
                        netCashFlow = receipts - payments
                    },
                    period = new
                    {
                        fromDate = startDate,
                        toDate = endDate
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("employee-attendance")]
        public async Task<ActionResult> GetEmployeeAttendanceReport(
            [FromQuery] int? branchId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var startDate = fromDate ?? DateTime.Today.AddDays(-30);
                var endDate = toDate ?? DateTime.Today;

                var query = _context.AttendanceRecords
                    .Include(ar => ar.Employee)
                        .ThenInclude(e => e.Department)
                    .Include(ar => ar.Employee)
                        .ThenInclude(e => e.Branch)
                    .Where(ar => ar.ShiftDate >= startDate && ar.ShiftDate <= endDate);

                if (branchId.HasValue)
                    query = query.Where(ar => ar.Employee.BranchId == branchId);

                if (departmentId.HasValue)
                    query = query.Where(ar => ar.Employee.DepartmentId == departmentId);

                var attendanceData = await query
                    .GroupBy(ar => new 
                    { 
                        ar.EmployeeId, 
                        ar.Employee.NameAr, 
                        ar.Employee.EmployeeCode,
                        DepartmentName = ar.Employee.Department != null ? ar.Employee.Department.NameAr : "غير محدد",
                        BranchName = ar.Employee.Branch.NameAr
                    })
                    .Select(g => new
                    {
                        g.Key.EmployeeId,
                        g.Key.NameAr,
                        g.Key.EmployeeCode,
                        g.Key.DepartmentName,
                        g.Key.BranchName,
                        TotalDays = g.Count(),
                        PresentDays = g.Count(ar => ar.AttendanceStatus == "Present"),
                        AbsentDays = g.Count(ar => ar.AttendanceStatus == "Absent"),
                        LateDays = g.Count(ar => ar.LateMinutes > 0),
                        TotalWorkingHours = g.Sum(ar => ar.WorkingMinutes) / 60.0,
                        TotalOvertimeHours = g.Sum(ar => ar.OvertimeMinutes) / 60.0,
                        AttendancePercentage = g.Count() > 0 ? (g.Count(ar => ar.AttendanceStatus == "Present") * 100.0 / g.Count()) : 0
                    })
                    .OrderByDescending(x => x.AttendancePercentage)
                    .ToListAsync();

                var summary = new
                {
                    TotalEmployees = attendanceData.Count,
                    AverageAttendance = attendanceData.Any() ? attendanceData.Average(x => x.AttendancePercentage) : 0,
                    TotalWorkingHours = attendanceData.Sum(x => x.TotalWorkingHours),
                    TotalOvertimeHours = attendanceData.Sum(x => x.TotalOvertimeHours)
                };

                return Ok(new { data = attendanceData, summary });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }
    }
}
