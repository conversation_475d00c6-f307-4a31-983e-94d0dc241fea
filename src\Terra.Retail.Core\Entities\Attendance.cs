using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الحضور والانصراف
    /// </summary>
    public class Attendance : BaseEntity
    {
        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// تاريخ الحضور
        /// </summary>
        public DateTime AttendanceDate { get; set; } = DateTime.Today;

        /// <summary>
        /// وقت الحضور
        /// </summary>
        public TimeSpan? CheckInTime { get; set; }

        /// <summary>
        /// وقت الانصراف
        /// </summary>
        public TimeSpan? CheckOutTime { get; set; }

        /// <summary>
        /// إجمالي ساعات العمل
        /// </summary>
        public TimeSpan? TotalWorkHours { get; set; }

        /// <summary>
        /// ساعات العمل العادية
        /// </summary>
        public TimeSpan? RegularHours { get; set; }

        /// <summary>
        /// ساعات الإضافي
        /// </summary>
        public TimeSpan? OvertimeHours { get; set; }

        /// <summary>
        /// ساعات التأخير
        /// </summary>
        public TimeSpan? LateHours { get; set; }

        /// <summary>
        /// ساعات الانصراف المبكر
        /// </summary>
        public TimeSpan? EarlyLeaveHours { get; set; }

        /// <summary>
        /// حالة الحضور
        /// </summary>
        public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;

        /// <summary>
        /// طريقة تسجيل الحضور
        /// </summary>
        public CheckInMethod CheckInMethod { get; set; } = CheckInMethod.Manual;

        /// <summary>
        /// طريقة تسجيل الانصراف
        /// </summary>
        public CheckOutMethod CheckOutMethod { get; set; } = CheckOutMethod.Manual;

        /// <summary>
        /// الموقع الجغرافي للحضور
        /// </summary>
        [MaxLength(200)]
        public string? CheckInLocation { get; set; }

        /// <summary>
        /// الموقع الجغرافي للانصراف
        /// </summary>
        [MaxLength(200)]
        public string? CheckOutLocation { get; set; }

        /// <summary>
        /// عنوان IP للحضور
        /// </summary>
        [MaxLength(45)]
        public string? CheckInIP { get; set; }

        /// <summary>
        /// عنوان IP للانصراف
        /// </summary>
        [MaxLength(45)]
        public string? CheckOutIP { get; set; }

        /// <summary>
        /// معرف الجهاز المستخدم للحضور
        /// </summary>
        [MaxLength(100)]
        public string? CheckInDeviceId { get; set; }

        /// <summary>
        /// معرف الجهاز المستخدم للانصراف
        /// </summary>
        [MaxLength(100)]
        public string? CheckOutDeviceId { get; set; }

        /// <summary>
        /// ملاحظات الحضور
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل الحضور مؤكد
        /// </summary>
        public bool IsApproved { get; set; } = true;

        /// <summary>
        /// معرف المستخدم الذي أكد الحضور
        /// </summary>
        public int? ApprovedById { get; set; }

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف الشيفت
        /// </summary>
        public int? ShiftId { get; set; }

        /// <summary>
        /// هل يوم عطلة
        /// </summary>
        public bool IsHoliday { get; set; } = false;

        /// <summary>
        /// نوع اليوم
        /// </summary>
        public DayType DayType { get; set; } = DayType.WorkDay;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual User? ApprovedBy { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual Shift? Shift { get; set; }
    }

    /// <summary>
    /// حالة الحضور
    /// </summary>
    public enum AttendanceStatus
    {
        /// <summary>
        /// حاضر
        /// </summary>
        Present = 1,

        /// <summary>
        /// غائب
        /// </summary>
        Absent = 2,

        /// <summary>
        /// متأخر
        /// </summary>
        Late = 3,

        /// <summary>
        /// انصراف مبكر
        /// </summary>
        EarlyLeave = 4,

        /// <summary>
        /// إجازة
        /// </summary>
        OnLeave = 5,

        /// <summary>
        /// عطلة
        /// </summary>
        Holiday = 6,

        /// <summary>
        /// مهمة خارجية
        /// </summary>
        OutsideWork = 7
    }

    /// <summary>
    /// طريقة تسجيل الحضور
    /// </summary>
    public enum CheckInMethod
    {
        /// <summary>
        /// يدوي
        /// </summary>
        Manual = 1,

        /// <summary>
        /// بصمة
        /// </summary>
        Biometric = 2,

        /// <summary>
        /// بطاقة
        /// </summary>
        Card = 3,

        /// <summary>
        /// تطبيق الجوال
        /// </summary>
        MobileApp = 4,

        /// <summary>
        /// موقع ويب
        /// </summary>
        WebPortal = 5
    }

    /// <summary>
    /// طريقة تسجيل الانصراف
    /// </summary>
    public enum CheckOutMethod
    {
        /// <summary>
        /// يدوي
        /// </summary>
        Manual = 1,

        /// <summary>
        /// بصمة
        /// </summary>
        Biometric = 2,

        /// <summary>
        /// بطاقة
        /// </summary>
        Card = 3,

        /// <summary>
        /// تطبيق الجوال
        /// </summary>
        MobileApp = 4,

        /// <summary>
        /// موقع ويب
        /// </summary>
        WebPortal = 5,

        /// <summary>
        /// تلقائي
        /// </summary>
        Automatic = 6
    }

    /// <summary>
    /// نوع اليوم
    /// </summary>
    public enum DayType
    {
        /// <summary>
        /// يوم عمل
        /// </summary>
        WorkDay = 1,

        /// <summary>
        /// عطلة أسبوعية
        /// </summary>
        WeeklyHoliday = 2,

        /// <summary>
        /// عطلة رسمية
        /// </summary>
        PublicHoliday = 3,

        /// <summary>
        /// إجازة سنوية
        /// </summary>
        AnnualLeave = 4,

        /// <summary>
        /// إجازة مرضية
        /// </summary>
        SickLeave = 5,

        /// <summary>
        /// إجازة طارئة
        /// </summary>
        EmergencyLeave = 6
    }
}
