using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("UserBranches")]
    public class UserBranch
    {
        [Key]
        public int Id { get; set; }

        public int UserId { get; set; }
        public int BranchId { get; set; }

        // Branch Permissions
        public bool IsDefault { get; set; } = false;
        public bool CanView { get; set; } = true;
        public bool CanCreate { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
        public bool CanApprove { get; set; } = false;
        public bool CanViewReports { get; set; } = false;
        public bool CanManageInventory { get; set; } = false;
        public bool CanManageFinance { get; set; } = false;
        public bool CanManageHR { get; set; } = false;

        // Time Restrictions
        public TimeSpan? AccessStartTime { get; set; }
        public TimeSpan? AccessEndTime { get; set; }

        [StringLength(20)]
        public string? AccessDays { get; set; } // "1234567" for Sun-Sat

        // Validity Period
        public DateTime EffectiveDate { get; set; } = DateTime.Today;
        public DateTime? ExpiryDate { get; set; }

        // Audit Fields
        public bool IsActive { get; set; } = true;
        public int? AssignedBy { get; set; }
        public DateTime AssignedAt { get; set; } = DateTime.Now;
        public int? LastModifiedBy { get; set; }
        public DateTime? LastModifiedAt { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        [ForeignKey("AssignedBy")]
        public virtual User? AssignedByUser { get; set; }

        [ForeignKey("LastModifiedBy")]
        public virtual User? LastModifiedByUser { get; set; }
    }
}
