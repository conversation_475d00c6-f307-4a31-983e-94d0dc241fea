{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = /*#__PURE__*/new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nlet MediaMatcher = /*#__PURE__*/(() => {\n  class MediaMatcher {\n    _platform = inject(Platform);\n    _nonce = inject(CSP_NONCE, {\n      optional: true\n    });\n    /** The internal matchMedia method to return back a MediaQueryList like object. */\n    _matchMedia;\n    constructor() {\n      this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n      // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n      // call it from a different scope.\n      window.matchMedia.bind(window) : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n      if (this._platform.WEBKIT || this._platform.BLINK) {\n        createEmptyStyleRule(query, this._nonce);\n      }\n      return this._matchMedia(query);\n    }\n    static ɵfac = function MediaMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MediaMatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MediaMatcher,\n      factory: MediaMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MediaMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nlet BreakpointObserver = /*#__PURE__*/(() => {\n  class BreakpointObserver {\n    _mediaMatcher = inject(MediaMatcher);\n    _zone = inject(NgZone);\n    /**  A map of all media queries currently being listened for. */\n    _queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n    _destroySubject = new Subject();\n    constructor() {}\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n      this._destroySubject.next();\n      this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n      const queries = splitQueries(coerceArray(value));\n      return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n      const queries = splitQueries(coerceArray(value));\n      const observables = queries.map(query => this._registerQuery(query).observable);\n      let stateObservable = combineLatest(observables);\n      // Emit the first state immediately, and then debounce the subsequent emissions.\n      stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n      return stateObservable.pipe(map(breakpointStates => {\n        const response = {\n          matches: false,\n          breakpoints: {}\n        };\n        breakpointStates.forEach(({\n          matches,\n          query\n        }) => {\n          response.matches = response.matches || matches;\n          response.breakpoints[query] = matches;\n        });\n        return response;\n      }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n      // Only set up a new MediaQueryList if it is not already being listened for.\n      if (this._queries.has(query)) {\n        return this._queries.get(query);\n      }\n      const mql = this._mediaMatcher.matchMedia(query);\n      // Create callback for match changes and add it is as a listener.\n      const queryObservable = new Observable(observer => {\n        // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n        // back into the zone because matchMedia is only included in Zone.js by loading the\n        // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n        // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n        // patches it.\n        const handler = e => this._zone.run(() => observer.next(e));\n        mql.addListener(handler);\n        return () => {\n          mql.removeListener(handler);\n        };\n      }).pipe(startWith(mql), map(({\n        matches\n      }) => ({\n        query,\n        matches\n      })), takeUntil(this._destroySubject));\n      // Add the MediaQueryList to the set of queries.\n      const output = {\n        observable: queryObservable,\n        mql\n      };\n      this._queries.set(query, output);\n      return output;\n    }\n    static ɵfac = function BreakpointObserver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BreakpointObserver)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BreakpointObserver,\n      factory: BreakpointObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return BreakpointObserver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\nexport { BreakpointObserver as B, MediaMatcher as M };", "map": {"version": 3, "names": ["i0", "inject", "CSP_NONCE", "Injectable", "NgZone", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "P", "Platform", "c", "coerce<PERSON><PERSON><PERSON>", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "_platform", "_nonce", "optional", "_matchMedia", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "ɵfac", "MediaMatcher_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "nonce", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "_mediaMatcher", "_zone", "_queries", "Map", "_destroySubject", "ngOnDestroy", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "get", "queryObservable", "observer", "handler", "run", "output", "set", "BreakpointObserver_Factory", "split", "reduce", "a1", "a2", "trim", "B", "M"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/breakpoints-observer-QutrMj4x.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    _platform = inject(Platform);\n    _nonce = inject(CSP_NONCE, { optional: true });\n    /** The internal matchMedia method to return back a MediaQueryList like object. */\n    _matchMedia;\n    constructor() {\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query, this._nonce);\n        }\n        return this._matchMedia(query);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MediaMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MediaMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            if (nonce) {\n                mediaQueryStyleNode.setAttribute('nonce', nonce);\n            }\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n    _mediaMatcher = inject(MediaMatcher);\n    _zone = inject(NgZone);\n    /**  A map of all media queries currently being listened for. */\n    _queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n    _destroySubject = new Subject();\n    constructor() { }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BreakpointObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\nexport { BreakpointObserver as B, MediaMatcher as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,WAAW,QAAQ,sBAAsB;;AAEvD;AACA,MAAMC,kCAAkC,gBAAG,IAAIC,GAAG,CAAC,CAAC;AACpD;AACA,IAAIC,mBAAmB;AACvB;AAAA,IACMC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfC,SAAS,GAAGtB,MAAM,CAACe,QAAQ,CAAC;IAC5BQ,MAAM,GAAGvB,MAAM,CAACC,SAAS,EAAE;MAAEuB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC9C;IACAC,WAAW;IACXC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACD,WAAW,GACZ,IAAI,CAACH,SAAS,CAACK,SAAS,IAAIC,MAAM,CAACC,UAAU;MACvC;MACE;MACAD,MAAM,CAACC,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC,GAChCG,cAAc;IAC5B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIF,UAAUA,CAACG,KAAK,EAAE;MACd,IAAI,IAAI,CAACV,SAAS,CAACW,MAAM,IAAI,IAAI,CAACX,SAAS,CAACY,KAAK,EAAE;QAC/CC,oBAAoB,CAACH,KAAK,EAAE,IAAI,CAACT,MAAM,CAAC;MAC5C;MACA,OAAO,IAAI,CAACE,WAAW,CAACO,KAAK,CAAC;IAClC;IACA,OAAOI,IAAI,YAAAC,qBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFjB,YAAY;IAAA;IAC/G,OAAOkB,KAAK,kBAD6ExC,EAAE,CAAAyC,kBAAA;MAAAC,KAAA,EACYpB,YAAY;MAAAqB,OAAA,EAAZrB,YAAY,CAAAe,IAAA;MAAAO,UAAA,EAAc;IAAM;EAC3I;EAAC,OA3BKtB,YAAY;AAAA;AA4BlB;EAAA,QAAAuB,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,oBAAoBA,CAACH,KAAK,EAAEa,KAAK,EAAE;EACxC,IAAI3B,kCAAkC,CAAC4B,GAAG,CAACd,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACZ,mBAAmB,EAAE;MACtBA,mBAAmB,GAAG2B,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACrD,IAAIH,KAAK,EAAE;QACPzB,mBAAmB,CAAC6B,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAAC;MACpD;MACAzB,mBAAmB,CAAC6B,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC/B,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAACgC,KAAK,EAAE;MAC3BhC,mBAAmB,CAACgC,KAAK,CAACC,UAAU,CAAC,UAAUrB,KAAK,YAAY,EAAE,CAAC,CAAC;MACpEd,kCAAkC,CAACoC,GAAG,CAACtB,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAOuB,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAASxB,cAAcA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACH0B,OAAO,EAAE1B,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxC2B,KAAK,EAAE3B,KAAK;IACZ4B,WAAW,EAAEA,CAAA,KAAM,CAAE,CAAC;IACtBC,cAAc,EAAEA,CAAA,KAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AAAA,IACMC,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrBC,aAAa,GAAG/D,MAAM,CAACqB,YAAY,CAAC;IACpC2C,KAAK,GAAGhE,MAAM,CAACG,MAAM,CAAC;IACtB;IACA8D,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpB;IACAC,eAAe,GAAG,IAAI/D,OAAO,CAAC,CAAC;IAC/BsB,WAAWA,CAAA,EAAG,CAAE;IAChB;IACA0C,WAAWA,CAAA,EAAG;MACV,IAAI,CAACD,eAAe,CAACE,IAAI,CAAC,CAAC;MAC3B,IAAI,CAACF,eAAe,CAACG,QAAQ,CAAC,CAAC;IACnC;IACA;AACJ;AACA;AACA;AACA;IACIC,SAASA,CAACC,KAAK,EAAE;MACb,MAAMC,OAAO,GAAGC,YAAY,CAACzD,WAAW,CAACuD,KAAK,CAAC,CAAC;MAChD,OAAOC,OAAO,CAACE,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACpB,OAAO,CAAC;IAClF;IACA;AACJ;AACA;AACA;AACA;AACA;IACIqB,OAAOA,CAACP,KAAK,EAAE;MACX,MAAMC,OAAO,GAAGC,YAAY,CAACzD,WAAW,CAACuD,KAAK,CAAC,CAAC;MAChD,MAAMQ,WAAW,GAAGP,OAAO,CAAC9D,GAAG,CAACqB,KAAK,IAAI,IAAI,CAAC6C,cAAc,CAAC7C,KAAK,CAAC,CAACiD,UAAU,CAAC;MAC/E,IAAIC,eAAe,GAAG7E,aAAa,CAAC2E,WAAW,CAAC;MAChD;MACAE,eAAe,GAAG5E,MAAM,CAAC4E,eAAe,CAACC,IAAI,CAAC3E,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE0E,eAAe,CAACC,IAAI,CAAC1E,IAAI,CAAC,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACvG,OAAOwE,eAAe,CAACC,IAAI,CAACxE,GAAG,CAACyE,gBAAgB,IAAI;QAChD,MAAMC,QAAQ,GAAG;UACb3B,OAAO,EAAE,KAAK;UACd4B,WAAW,EAAE,CAAC;QAClB,CAAC;QACDF,gBAAgB,CAACG,OAAO,CAAC,CAAC;UAAE7B,OAAO;UAAE1B;QAAM,CAAC,KAAK;UAC7CqD,QAAQ,CAAC3B,OAAO,GAAG2B,QAAQ,CAAC3B,OAAO,IAAIA,OAAO;UAC9C2B,QAAQ,CAACC,WAAW,CAACtD,KAAK,CAAC,GAAG0B,OAAO;QACzC,CAAC,CAAC;QACF,OAAO2B,QAAQ;MACnB,CAAC,CAAC,CAAC;IACP;IACA;IACAR,cAAcA,CAAC7C,KAAK,EAAE;MAClB;MACA,IAAI,IAAI,CAACiC,QAAQ,CAACnB,GAAG,CAACd,KAAK,CAAC,EAAE;QAC1B,OAAO,IAAI,CAACiC,QAAQ,CAACuB,GAAG,CAACxD,KAAK,CAAC;MACnC;MACA,MAAM8C,GAAG,GAAG,IAAI,CAACf,aAAa,CAAClC,UAAU,CAACG,KAAK,CAAC;MAChD;MACA,MAAMyD,eAAe,GAAG,IAAIlF,UAAU,CAAEmF,QAAQ,IAAK;QACjD;QACA;QACA;QACA;QACA;QACA,MAAMC,OAAO,GAAIpC,CAAC,IAAK,IAAI,CAACS,KAAK,CAAC4B,GAAG,CAAC,MAAMF,QAAQ,CAACrB,IAAI,CAACd,CAAC,CAAC,CAAC;QAC7DuB,GAAG,CAAClB,WAAW,CAAC+B,OAAO,CAAC;QACxB,OAAO,MAAM;UACTb,GAAG,CAACjB,cAAc,CAAC8B,OAAO,CAAC;QAC/B,CAAC;MACL,CAAC,CAAC,CAACR,IAAI,CAACvE,SAAS,CAACkE,GAAG,CAAC,EAAEnE,GAAG,CAAC,CAAC;QAAE+C;MAAQ,CAAC,MAAM;QAAE1B,KAAK;QAAE0B;MAAQ,CAAC,CAAC,CAAC,EAAE7C,SAAS,CAAC,IAAI,CAACsD,eAAe,CAAC,CAAC;MACpG;MACA,MAAM0B,MAAM,GAAG;QAAEZ,UAAU,EAAEQ,eAAe;QAAEX;MAAI,CAAC;MACnD,IAAI,CAACb,QAAQ,CAAC6B,GAAG,CAAC9D,KAAK,EAAE6D,MAAM,CAAC;MAChC,OAAOA,MAAM;IACjB;IACA,OAAOzD,IAAI,YAAA2D,2BAAAzD,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwB,kBAAkB;IAAA;IACrH,OAAOvB,KAAK,kBA3H6ExC,EAAE,CAAAyC,kBAAA;MAAAC,KAAA,EA2HYqB,kBAAkB;MAAApB,OAAA,EAAlBoB,kBAAkB,CAAA1B,IAAA;MAAAO,UAAA,EAAc;IAAM;EACjJ;EAAC,OAzEKmB,kBAAkB;AAAA;AA0ExB;EAAA,QAAAlB,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA,SAAS8B,YAAYA,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACT9D,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACgE,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAAC5F,MAAM,CAAC6F,EAAE,CAAC,CAAC,CACjCxF,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACoE,IAAI,CAAC,CAAC,CAAC;AACnC;AAEA,SAAStC,kBAAkB,IAAIuC,CAAC,EAAEhF,YAAY,IAAIiF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}