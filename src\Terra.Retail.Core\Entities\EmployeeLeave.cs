using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// إجازات الموظفين
    /// </summary>
    public class EmployeeLeave : BaseEntity
    {
        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// معرف نوع الإجازة
        /// </summary>
        public int LeaveTypeId { get; set; }

        /// <summary>
        /// رقم طلب الإجازة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string LeaveNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ بداية الإجازة
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الإجازة
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// عدد الأيام
        /// </summary>
        public int TotalDays { get; set; }

        /// <summary>
        /// عدد أيام العمل الفعلية
        /// </summary>
        public int WorkingDays { get; set; }

        /// <summary>
        /// سبب الإجازة
        /// </summary>
        [MaxLength(1000)]
        public string? Reason { get; set; }

        /// <summary>
        /// حالة الطلب
        /// </summary>
        public LeaveStatus Status { get; set; } = LeaveStatus.Pending;

        /// <summary>
        /// تاريخ تقديم الطلب
        /// </summary>
        public DateTime ApplicationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي قدم الطلب
        /// </summary>
        public int? AppliedById { get; set; }

        /// <summary>
        /// تاريخ الموافقة/الرفض
        /// </summary>
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي راجع الطلب
        /// </summary>
        public int? ReviewedById { get; set; }

        /// <summary>
        /// ملاحظات المراجع
        /// </summary>
        [MaxLength(1000)]
        public string? ReviewNotes { get; set; }

        /// <summary>
        /// تاريخ العودة الفعلي
        /// </summary>
        public DateTime? ActualReturnDate { get; set; }

        /// <summary>
        /// هل عاد الموظف من الإجازة
        /// </summary>
        public bool HasReturned { get; set; } = false;

        /// <summary>
        /// عدد الأيام الإضافية (إذا تأخر في العودة)
        /// </summary>
        public int? ExtraDays { get; set; }

        /// <summary>
        /// سبب التأخير في العودة
        /// </summary>
        [MaxLength(500)]
        public string? DelayReason { get; set; }

        /// <summary>
        /// هل الإجازة طارئة
        /// </summary>
        public bool IsEmergency { get; set; } = false;

        /// <summary>
        /// رقم الهاتف أثناء الإجازة
        /// </summary>
        [MaxLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// عنوان الإقامة أثناء الإجازة
        /// </summary>
        [MaxLength(500)]
        public string? ContactAddress { get; set; }

        /// <summary>
        /// معرف الموظف البديل
        /// </summary>
        public int? ReplacementEmployeeId { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [MaxLength(1000)]
        public string? AdditionalNotes { get; set; }

        /// <summary>
        /// هل تؤثر على الراتب
        /// </summary>
        public bool AffectsSalary { get; set; } = false;

        /// <summary>
        /// مبلغ الخصم من الراتب
        /// </summary>
        public decimal SalaryDeduction { get; set; } = 0;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
        public virtual User? AppliedBy { get; set; }
        public virtual User? ReviewedBy { get; set; }
        public virtual Employee? ReplacementEmployee { get; set; }
        public virtual ICollection<EmployeeLeaveDocument> Documents { get; set; } = new List<EmployeeLeaveDocument>();
    }

    /// <summary>
    /// مستندات إجازات الموظفين
    /// </summary>
    public class EmployeeLeaveDocument : BaseEntity
    {
        /// <summary>
        /// معرف الإجازة
        /// </summary>
        public int EmployeeLeaveId { get; set; }

        /// <summary>
        /// اسم المستند
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string DocumentName { get; set; } = string.Empty;

        /// <summary>
        /// مسار الملف
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// حجم الملف
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// نوع الملف
        /// </summary>
        [MaxLength(50)]
        public string? ContentType { get; set; }

        // Navigation Properties
        public virtual EmployeeLeave EmployeeLeave { get; set; } = null!;
    }

    /// <summary>
    /// رصيد إجازات الموظفين
    /// </summary>
    public class EmployeeLeaveBalance : BaseEntity
    {
        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// معرف نوع الإجازة
        /// </summary>
        public int LeaveTypeId { get; set; }

        /// <summary>
        /// السنة
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// الرصيد المخصص
        /// </summary>
        public decimal AllocatedDays { get; set; }

        /// <summary>
        /// الرصيد المستخدم
        /// </summary>
        public decimal UsedDays { get; set; }

        /// <summary>
        /// الرصيد المتبقي
        /// </summary>
        public decimal RemainingDays { get; set; }

        /// <summary>
        /// الرصيد المرحل من السنة السابقة
        /// </summary>
        public decimal CarriedForwardDays { get; set; } = 0;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual LeaveType LeaveType { get; set; } = null!;
    }

    /// <summary>
    /// حالة طلب الإجازة
    /// </summary>
    public enum LeaveStatus
    {
        /// <summary>
        /// معلق
        /// </summary>
        Pending = 1,

        /// <summary>
        /// موافق عليه
        /// </summary>
        Approved = 2,

        /// <summary>
        /// مرفوض
        /// </summary>
        Rejected = 3,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// قيد التنفيذ
        /// </summary>
        InProgress = 5,

        /// <summary>
        /// مكتمل
        /// </summary>
        Completed = 6
    }
}
