using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مرتجعات المشتريات
    /// </summary>
    public class PurchaseReturn : BaseEntity
    {
        /// <summary>
        /// رقم المرتجع
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ReturnNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ المرتجع
        /// </summary>
        public DateTime ReturnDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف فاتورة المشتريات الأصلية
        /// </summary>
        public int PurchaseId { get; set; }

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أرجع البضاعة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// سبب الإرجاع
        /// </summary>
        public PurchaseReturnReason ReturnReason { get; set; }

        /// <summary>
        /// وصف سبب الإرجاع
        /// </summary>
        [MaxLength(500)]
        public string? ReturnReasonDescription { get; set; }

        /// <summary>
        /// إجمالي قيمة المرتجع
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المسترد
        /// </summary>
        public decimal RefundedAmount { get; set; } = 0;

        /// <summary>
        /// حالة المرتجع
        /// </summary>
        public PurchaseReturnStatus Status { get; set; } = PurchaseReturnStatus.Pending;

        /// <summary>
        /// ملاحظات المرتجع
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل المرتجع مؤكد
        /// </summary>
        public bool IsConfirmed { get; set; } = false;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد المرتجع
        /// </summary>
        public int? ConfirmedById { get; set; }

        /// <summary>
        /// هل تم استرداد المبلغ
        /// </summary>
        public bool IsRefunded { get; set; } = false;

        /// <summary>
        /// تاريخ الاسترداد
        /// </summary>
        public DateTime? RefundedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بالاسترداد
        /// </summary>
        public int? RefundedById { get; set; }

        // Navigation Properties
        public virtual Purchase Purchase { get; set; } = null!;
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
        public virtual User? RefundedBy { get; set; }
        public virtual ICollection<PurchaseReturnItem> Items { get; set; } = new List<PurchaseReturnItem>();
    }

    /// <summary>
    /// أصناف مرتجعات المشتريات
    /// </summary>
    public class PurchaseReturnItem : BaseEntity
    {
        /// <summary>
        /// معرف المرتجع
        /// </summary>
        public int PurchaseReturnId { get; set; }

        /// <summary>
        /// معرف صنف الفاتورة الأصلي
        /// </summary>
        public int PurchaseItemId { get; set; }

        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// الكمية المرتجعة
        /// </summary>
        public decimal ReturnedQuantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// إجمالي قيمة الصنف المرتجع
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// سبب إرجاع الصنف
        /// </summary>
        [MaxLength(500)]
        public string? ItemReturnReason { get; set; }

        /// <summary>
        /// حالة الصنف المرتجع
        /// </summary>
        public ReturnedItemCondition ItemCondition { get; set; } = ReturnedItemCondition.Good;

        /// <summary>
        /// رقم التشغيل/الدفعة
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// الرقم التسلسلي
        /// </summary>
        [MaxLength(100)]
        public string? SerialNumber { get; set; }

        // Navigation Properties
        public virtual PurchaseReturn PurchaseReturn { get; set; } = null!;
        public virtual PurchaseItem PurchaseItem { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    /// <summary>
    /// أسباب إرجاع المشتريات
    /// </summary>
    public enum PurchaseReturnReason
    {
        /// <summary>
        /// عيب في المنتج
        /// </summary>
        ProductDefect = 1,

        /// <summary>
        /// منتج خاطئ
        /// </summary>
        WrongProduct = 2,

        /// <summary>
        /// منتهي الصلاحية
        /// </summary>
        Expired = 3,

        /// <summary>
        /// تالف
        /// </summary>
        Damaged = 4,

        /// <summary>
        /// غير مطابق للمواصفات
        /// </summary>
        NotAsSpecified = 5,

        /// <summary>
        /// كمية زائدة
        /// </summary>
        ExcessQuantity = 6,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 7
    }

    /// <summary>
    /// حالة مرتجع المشتريات
    /// </summary>
    public enum PurchaseReturnStatus
    {
        /// <summary>
        /// معلق
        /// </summary>
        Pending = 1,

        /// <summary>
        /// مؤكد
        /// </summary>
        Confirmed = 2,

        /// <summary>
        /// مسترد
        /// </summary>
        Refunded = 3,

        /// <summary>
        /// مرفوض
        /// </summary>
        Rejected = 4,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 5
    }
}
