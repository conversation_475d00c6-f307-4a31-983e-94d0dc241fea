{"ast": null, "code": "import { T as TileCoordinator } from './public-api-BoO5eSq-.mjs';\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]], [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]], \"*\"];\nconst _c2 = [\"[mat-grid-avatar], [matGridAvatar]\", \"[mat-line], [matLine]\", \"*\"];\nconst _c3 = \".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\";\nexport { ɵ as ɵTileCoordinator } from './public-api-BoO5eSq-.mjs';\nimport { s as setLines, M as MatLine, a as MatLineModule } from './line-Bz5f9Cyx.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/a11y';\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = /*#__PURE__*/new InjectionToken('MAT_GRID_LIST');\nlet MatGridTile = /*#__PURE__*/(() => {\n  class MatGridTile {\n    _element = inject(ElementRef);\n    _gridList = inject(MAT_GRID_LIST, {\n      optional: true\n    });\n    _rowspan = 1;\n    _colspan = 1;\n    constructor() {}\n    /** Amount of rows that the grid tile takes up. */\n    get rowspan() {\n      return this._rowspan;\n    }\n    set rowspan(value) {\n      this._rowspan = Math.round(coerceNumberProperty(value));\n    }\n    /** Amount of columns that the grid tile takes up. */\n    get colspan() {\n      return this._colspan;\n    }\n    set colspan(value) {\n      this._colspan = Math.round(coerceNumberProperty(value));\n    }\n    /**\n     * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n     * \"Changed after checked\" errors that would occur with HostBinding.\n     */\n    _setStyle(property, value) {\n      this._element.nativeElement.style[property] = value;\n    }\n    static ɵfac = function MatGridTile_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTile)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTile,\n      selectors: [[\"mat-grid-tile\"]],\n      hostAttrs: [1, \"mat-grid-tile\"],\n      hostVars: 2,\n      hostBindings: function MatGridTile_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"rowspan\", ctx.rowspan)(\"colspan\", ctx.colspan);\n        }\n      },\n      inputs: {\n        rowspan: \"rowspan\",\n        colspan: \"colspan\"\n      },\n      exportAs: [\"matGridTile\"],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"mat-grid-tile-content\"]],\n      template: function MatGridTile_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridTile;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatGridTileText = /*#__PURE__*/(() => {\n  class MatGridTileText {\n    _element = inject(ElementRef);\n    _lines;\n    constructor() {}\n    ngAfterContentInit() {\n      setLines(this._lines, this._element);\n    }\n    static ɵfac = function MatGridTileText_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileText)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTileText,\n      selectors: [[\"mat-grid-tile-header\"], [\"mat-grid-tile-footer\"]],\n      contentQueries: function MatGridTileText_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n        }\n      },\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-grid-list-text\"]],\n      template: function MatGridTileText_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridTileText;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridAvatarCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridAvatarCssMatStyler {\n    static ɵfac = function MatGridAvatarCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridAvatarCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridAvatarCssMatStyler,\n      selectors: [[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-grid-avatar\"]\n    });\n  }\n  return MatGridAvatarCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridTileHeaderCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridTileHeaderCssMatStyler {\n    static ɵfac = function MatGridTileHeaderCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileHeaderCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileHeaderCssMatStyler,\n      selectors: [[\"mat-grid-tile-header\"]],\n      hostAttrs: [1, \"mat-grid-tile-header\"]\n    });\n  }\n  return MatGridTileHeaderCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridTileFooterCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridTileFooterCssMatStyler {\n    static ɵfac = function MatGridTileFooterCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileFooterCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileFooterCssMatStyler,\n      selectors: [[\"mat-grid-tile-footer\"]],\n      hostAttrs: [1, \"mat-grid-tile-footer\"]\n    });\n  }\n  return MatGridTileFooterCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n  _gutterSize;\n  _rows = 0;\n  _rowspan = 0;\n  _cols;\n  _direction;\n  /**\n   * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n   * because these properties haven't been calculated by that point.\n   *\n   * @param gutterSize Size of the grid's gutter.\n   * @param tracker Instance of the TileCoordinator.\n   * @param cols Amount of columns in the grid.\n   * @param direction Layout direction of the grid.\n   */\n  init(gutterSize, tracker, cols, direction) {\n    this._gutterSize = normalizeUnits(gutterSize);\n    this._rows = tracker.rowCount;\n    this._rowspan = tracker.rowspan;\n    this._cols = cols;\n    this._direction = direction;\n  }\n  /**\n   * Computes the amount of space a single 1x1 tile would take up (width or height).\n   * Used as a basis for other calculations.\n   * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n   * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n   * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n   */\n  getBaseTileSize(sizePercent, gutterFraction) {\n    // Take the base size percent (as would be if evenly dividing the size between cells),\n    // and then subtracting the size of one gutter. However, since there are no gutters on the\n    // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n    // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n    // edge evenly among the cells).\n    return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n  }\n  /**\n   * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n   * @param offset Number of tiles that have already been rendered in the row/column.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @return Position of the tile as a CSS calc() expression.\n   */\n  getTilePosition(baseSize, offset) {\n    // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n    // row/column (offset).\n    return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n  }\n  /**\n   * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @param span The tile's rowspan or colspan.\n   * @return Size of the tile as a CSS calc() expression.\n   */\n  getTileSize(baseSize, span) {\n    return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n  }\n  /**\n   * Sets the style properties to be applied to a tile for the given row and column index.\n   * @param tile Tile to which to apply the styling.\n   * @param rowIndex Index of the tile's row.\n   * @param colIndex Index of the tile's column.\n   */\n  setStyle(tile, rowIndex, colIndex) {\n    // Percent of the available horizontal space that one column takes up.\n    let percentWidthPerTile = 100 / this._cols;\n    // Fraction of the vertical gutter size that each column takes up.\n    // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n    let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n    this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n  }\n  /** Sets the horizontal placement of the tile in the list. */\n  setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n    // Base horizontal size of a column.\n    let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n    // The width and horizontal position of each tile is always calculated the same way, but the\n    // height and vertical position depends on the rowMode.\n    let side = this._direction === 'rtl' ? 'right' : 'left';\n    tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n    tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n  }\n  /**\n   * Calculates the total size taken up by gutters across one axis of a list.\n   */\n  getGutterSpan() {\n    return `${this._gutterSize} * (${this._rowspan} - 1)`;\n  }\n  /**\n   * Calculates the total size taken up by tiles across one axis of a list.\n   * @param tileHeight Height of the tile.\n   */\n  getTileSpan(tileHeight) {\n    return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n  }\n  /**\n   * Calculates the computed height and returns the correct style property to set.\n   * This method can be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  getComputedHeight() {\n    return null;\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n  fixedRowHeight;\n  constructor(fixedRowHeight) {\n    super();\n    this.fixedRowHeight = fixedRowHeight;\n  }\n  init(gutterSize, tracker, cols, direction) {\n    super.init(gutterSize, tracker, cols, direction);\n    this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n    if (!cssCalcAllowedValue.test(this.fixedRowHeight) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n    }\n  }\n  setRowStyles(tile, rowIndex) {\n    tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['height', null]);\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n  /** Ratio width:height given by user to determine row height. */\n  rowHeightRatio;\n  baseTileHeight;\n  constructor(value) {\n    super();\n    this._parseRatio(value);\n  }\n  setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n    let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n    this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n    // Use padding-top and margin-top to maintain the given aspect ratio, as\n    // a percentage-based value for these properties is applied versus the *width* of the\n    // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n    tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n    tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['paddingBottom', calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['paddingBottom', null]);\n    list._tiles.forEach(tile => {\n      tile._setStyle('marginTop', null);\n      tile._setStyle('paddingTop', null);\n    });\n  }\n  _parseRatio(value) {\n    const ratioParts = value.split(':');\n    if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n    }\n    this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n  }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n  setRowStyles(tile, rowIndex) {\n    // Percent of the available vertical space that one row takes up.\n    let percentHeightPerTile = 100 / this._rowspan;\n    // Fraction of the horizontal gutter size that each column takes up.\n    let gutterHeightPerTile = (this._rows - 1) / this._rows;\n    // Base vertical size of a column.\n    let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n    tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n  }\n  reset(list) {\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n  return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n  return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nlet MatGridList = /*#__PURE__*/(() => {\n  class MatGridList {\n    _element = inject(ElementRef);\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    /** Number of columns being rendered. */\n    _cols;\n    /** Used for determining the position of each tile in the grid. */\n    _tileCoordinator;\n    /**\n     * Row height value passed in by user. This can be one of three types:\n     * - Number value (ex: \"100px\"):  sets a fixed row height to that value\n     * - Ratio value (ex: \"4:3\"): sets the row height based on width:height ratio\n     * - \"Fit\" mode (ex: \"fit\"): sets the row height to total height divided by number of rows\n     */\n    _rowHeight;\n    /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n    _gutter = '1px';\n    /** Sets position and size styles for a tile */\n    _tileStyler;\n    /** Query list of tiles that are being rendered. */\n    _tiles;\n    constructor() {}\n    /** Amount of columns in the grid list. */\n    get cols() {\n      return this._cols;\n    }\n    set cols(value) {\n      this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n    }\n    /** Size of the grid list's gutter in pixels. */\n    get gutterSize() {\n      return this._gutter;\n    }\n    set gutterSize(value) {\n      this._gutter = `${value == null ? '' : value}`;\n    }\n    /** Set internal representation of row height from the user-provided value. */\n    get rowHeight() {\n      return this._rowHeight;\n    }\n    set rowHeight(value) {\n      const newValue = `${value == null ? '' : value}`;\n      if (newValue !== this._rowHeight) {\n        this._rowHeight = newValue;\n        this._setTileStyler(this._rowHeight);\n      }\n    }\n    ngOnInit() {\n      this._checkCols();\n      this._checkRowHeight();\n    }\n    /**\n     * The layout calculation is fairly cheap if nothing changes, so there's little cost\n     * to run it frequently.\n     */\n    ngAfterContentChecked() {\n      this._layoutTiles();\n    }\n    /** Throw a friendly error if cols property is missing */\n    _checkCols() {\n      if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n      }\n    }\n    /** Default to equal width:height if rowHeight property is missing */\n    _checkRowHeight() {\n      if (!this._rowHeight) {\n        this._setTileStyler('1:1');\n      }\n    }\n    /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n    _setTileStyler(rowHeight) {\n      if (this._tileStyler) {\n        this._tileStyler.reset(this);\n      }\n      if (rowHeight === MAT_FIT_MODE) {\n        this._tileStyler = new FitTileStyler();\n      } else if (rowHeight && rowHeight.indexOf(':') > -1) {\n        this._tileStyler = new RatioTileStyler(rowHeight);\n      } else {\n        this._tileStyler = new FixedTileStyler(rowHeight);\n      }\n    }\n    /** Computes and applies the size and position for all children grid tiles. */\n    _layoutTiles() {\n      if (!this._tileCoordinator) {\n        this._tileCoordinator = new TileCoordinator();\n      }\n      const tracker = this._tileCoordinator;\n      const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n      const direction = this._dir ? this._dir.value : 'ltr';\n      this._tileCoordinator.update(this.cols, tiles);\n      this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n      tiles.forEach((tile, index) => {\n        const pos = tracker.positions[index];\n        this._tileStyler.setStyle(tile, pos.row, pos.col);\n      });\n      this._setListStyle(this._tileStyler.getComputedHeight());\n    }\n    /** Sets style on the main grid-list element, given the style name and value. */\n    _setListStyle(style) {\n      if (style) {\n        this._element.nativeElement.style[style[0]] = style[1];\n      }\n    }\n    static ɵfac = function MatGridList_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridList)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridList,\n      selectors: [[\"mat-grid-list\"]],\n      contentQueries: function MatGridList_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatGridTile, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tiles = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-grid-list\"],\n      hostVars: 1,\n      hostBindings: function MatGridList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"cols\", ctx.cols);\n        }\n      },\n      inputs: {\n        cols: \"cols\",\n        gutterSize: \"gutterSize\",\n        rowHeight: \"rowHeight\"\n      },\n      exportAs: [\"matGridList\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_GRID_LIST,\n        useExisting: MatGridList\n      }])],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      template: function MatGridList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c3],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatGridListModule = /*#__PURE__*/(() => {\n  class MatGridListModule {\n    static ɵfac = function MatGridListModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridListModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatGridListModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatLineModule, MatCommonModule, MatLineModule, MatCommonModule]\n    });\n  }\n  return MatGridListModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, MatLine };", "map": {"version": 3, "names": ["T", "TileCoordinator", "_c0", "_c1", "_c2", "_c3", "ɵ", "ɵTileCoordinator", "s", "setLines", "M", "MatLine", "a", "MatLineModule", "i0", "InjectionToken", "inject", "ElementRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "ContentChildren", "Directive", "NgModule", "coerceNumberProperty", "Directionality", "MatCommonModule", "MAT_GRID_LIST", "MatGridTile", "_element", "_gridList", "optional", "_rowspan", "_colspan", "constructor", "rowspan", "value", "Math", "round", "colspan", "_setStyle", "property", "nativeElement", "style", "ɵfac", "MatGridTile_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatGridTile_HostBindings", "rf", "ctx", "ɵɵattribute", "inputs", "exportAs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatGridTile_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "styles", "encapsulation", "changeDetection", "ngDevMode", "MatGridTileText", "_lines", "ngAfterContentInit", "MatGridTileText_Factory", "contentQueries", "MatGridTileText_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "MatGridTileText_Template", "MatGridAvatarCssMatStyler", "MatGridAvatarCssMatStyler_Factory", "ɵdir", "ɵɵdefineDirective", "MatGridTileHeaderCssMatStyler", "MatGridTileHeaderCssMatStyler_Factory", "MatGridTileFooterCssMatStyler", "MatGridTileFooterCssMatStyler_Factory", "cssCalcAllowedValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_gutterSize", "_rows", "_cols", "_direction", "init", "gutterSize", "tracker", "cols", "direction", "normalizeUnits", "rowCount", "getBaseTileSize", "sizePercent", "gutterFraction", "getTilePosition", "baseSize", "offset", "calc", "getTileSize", "span", "setStyle", "tile", "rowIndex", "colIndex", "percentWidthPerTile", "gutterWidthFractionPerTile", "setColStyles", "setRowStyles", "percentWidth", "gutterWidth", "baseTileWidth", "side", "getGutterSpan", "getTileSpan", "tileHeight", "getComputedHeight", "FixedTileStyler", "fixedRowHeight", "test", "Error", "reset", "list", "_setListStyle", "_tiles", "for<PERSON>ach", "RatioTileStyler", "rowHeightRatio", "baseTileHeight", "_parseRatio", "percentHeightPerTile", "ratioParts", "split", "length", "parseFloat", "FitTileStyler", "gutterHeightPerTile", "exp", "match", "MAT_FIT_MODE", "MatGridList", "_dir", "_tileCoordinator", "_rowHeight", "_gutter", "_tileStyler", "max", "rowHeight", "newValue", "_setTileStyler", "ngOnInit", "_checkCols", "_checkRowHeight", "ngAfterContentChecked", "_layoutTiles", "indexOf", "tiles", "filter", "update", "index", "pos", "positions", "row", "col", "MatGridList_Factory", "MatGridList_ContentQueries", "MatGridList_HostBindings", "features", "ɵɵProvidersFeature", "provide", "useExisting", "MatGridList_Template", "MatGridListModule", "MatGridListModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/grid-list.mjs"], "sourcesContent": ["import { T as TileCoordinator } from './public-api-BoO5eSq-.mjs';\nexport { ɵ as ɵTileCoordinator } from './public-api-BoO5eSq-.mjs';\nimport { s as setLines, M as MatLine, a as MatLineModule } from './line-Bz5f9Cyx.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/a11y';\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = new InjectionToken('MAT_GRID_LIST');\n\nclass MatGridTile {\n    _element = inject(ElementRef);\n    _gridList = inject(MAT_GRID_LIST, { optional: true });\n    _rowspan = 1;\n    _colspan = 1;\n    constructor() { }\n    /** Amount of rows that the grid tile takes up. */\n    get rowspan() {\n        return this._rowspan;\n    }\n    set rowspan(value) {\n        this._rowspan = Math.round(coerceNumberProperty(value));\n    }\n    /** Amount of columns that the grid tile takes up. */\n    get colspan() {\n        return this._colspan;\n    }\n    set colspan(value) {\n        this._colspan = Math.round(coerceNumberProperty(value));\n    }\n    /**\n     * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n     * \"Changed after checked\" errors that would occur with HostBinding.\n     */\n    _setStyle(property, value) {\n        this._element.nativeElement.style[property] = value;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTile, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridTile, isStandalone: true, selector: \"mat-grid-tile\", inputs: { rowspan: \"rowspan\", colspan: \"colspan\" }, host: { properties: { \"attr.rowspan\": \"rowspan\", \"attr.colspan\": \"colspan\" }, classAttribute: \"mat-grid-tile\" }, exportAs: [\"matGridTile\"], ngImport: i0, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTile, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile', exportAs: 'matGridTile', host: {\n                        'class': 'mat-grid-tile',\n                        // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n                        // the DOM. This is needed for the grid-tile harness.\n                        '[attr.rowspan]': 'rowspan',\n                        '[attr.colspan]': 'colspan',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { rowspan: [{\n                type: Input\n            }], colspan: [{\n                type: Input\n            }] } });\nclass MatGridTileText {\n    _element = inject(ElementRef);\n    _lines;\n    constructor() { }\n    ngAfterContentInit() {\n        setLines(this._lines, this._element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileText, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridTileText, isStandalone: true, selector: \"mat-grid-tile-header, mat-grid-tile-footer\", queries: [{ propertyName: \"_lines\", predicate: MatLine, descendants: true }], ngImport: i0, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileText, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile-header, mat-grid-tile-footer', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatLine, { descendants: true }]\n            }] } });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridAvatarCssMatStyler {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridAvatarCssMatStyler, isStandalone: true, selector: \"[mat-grid-avatar], [matGridAvatar]\", host: { classAttribute: \"mat-grid-avatar\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-grid-avatar], [matGridAvatar]',\n                    host: { 'class': 'mat-grid-avatar' },\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileHeaderCssMatStyler {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridTileHeaderCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-header\", host: { classAttribute: \"mat-grid-tile-header\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-header',\n                    host: { 'class': 'mat-grid-tile-header' },\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileFooterCssMatStyler {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridTileFooterCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-footer\", host: { classAttribute: \"mat-grid-tile-footer\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-footer',\n                    host: { 'class': 'mat-grid-tile-footer' },\n                }]\n        }] });\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n    _gutterSize;\n    _rows = 0;\n    _rowspan = 0;\n    _cols;\n    _direction;\n    /**\n     * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n     * because these properties haven't been calculated by that point.\n     *\n     * @param gutterSize Size of the grid's gutter.\n     * @param tracker Instance of the TileCoordinator.\n     * @param cols Amount of columns in the grid.\n     * @param direction Layout direction of the grid.\n     */\n    init(gutterSize, tracker, cols, direction) {\n        this._gutterSize = normalizeUnits(gutterSize);\n        this._rows = tracker.rowCount;\n        this._rowspan = tracker.rowspan;\n        this._cols = cols;\n        this._direction = direction;\n    }\n    /**\n     * Computes the amount of space a single 1x1 tile would take up (width or height).\n     * Used as a basis for other calculations.\n     * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n     * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n     * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n     */\n    getBaseTileSize(sizePercent, gutterFraction) {\n        // Take the base size percent (as would be if evenly dividing the size between cells),\n        // and then subtracting the size of one gutter. However, since there are no gutters on the\n        // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n        // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n        // edge evenly among the cells).\n        return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n    }\n    /**\n     * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n     * @param offset Number of tiles that have already been rendered in the row/column.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @return Position of the tile as a CSS calc() expression.\n     */\n    getTilePosition(baseSize, offset) {\n        // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n        // row/column (offset).\n        return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n    }\n    /**\n     * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @param span The tile's rowspan or colspan.\n     * @return Size of the tile as a CSS calc() expression.\n     */\n    getTileSize(baseSize, span) {\n        return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n    }\n    /**\n     * Sets the style properties to be applied to a tile for the given row and column index.\n     * @param tile Tile to which to apply the styling.\n     * @param rowIndex Index of the tile's row.\n     * @param colIndex Index of the tile's column.\n     */\n    setStyle(tile, rowIndex, colIndex) {\n        // Percent of the available horizontal space that one column takes up.\n        let percentWidthPerTile = 100 / this._cols;\n        // Fraction of the vertical gutter size that each column takes up.\n        // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n        let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n        this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n        this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    }\n    /** Sets the horizontal placement of the tile in the list. */\n    setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n        // Base horizontal size of a column.\n        let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n        // The width and horizontal position of each tile is always calculated the same way, but the\n        // height and vertical position depends on the rowMode.\n        let side = this._direction === 'rtl' ? 'right' : 'left';\n        tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n        tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n    }\n    /**\n     * Calculates the total size taken up by gutters across one axis of a list.\n     */\n    getGutterSpan() {\n        return `${this._gutterSize} * (${this._rowspan} - 1)`;\n    }\n    /**\n     * Calculates the total size taken up by tiles across one axis of a list.\n     * @param tileHeight Height of the tile.\n     */\n    getTileSpan(tileHeight) {\n        return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n    }\n    /**\n     * Calculates the computed height and returns the correct style property to set.\n     * This method can be implemented by each type of TileStyler.\n     * @docs-private\n     */\n    getComputedHeight() {\n        return null;\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n    fixedRowHeight;\n    constructor(fixedRowHeight) {\n        super();\n        this.fixedRowHeight = fixedRowHeight;\n    }\n    init(gutterSize, tracker, cols, direction) {\n        super.init(gutterSize, tracker, cols, direction);\n        this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n        if (!cssCalcAllowedValue.test(this.fixedRowHeight) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n        }\n    }\n    setRowStyles(tile, rowIndex) {\n        tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n    }\n    reset(list) {\n        list._setListStyle(['height', null]);\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n    /** Ratio width:height given by user to determine row height. */\n    rowHeightRatio;\n    baseTileHeight;\n    constructor(value) {\n        super();\n        this._parseRatio(value);\n    }\n    setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n        let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n        this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n        // Use padding-top and margin-top to maintain the given aspect ratio, as\n        // a percentage-based value for these properties is applied versus the *width* of the\n        // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n        tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n        tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return [\n            'paddingBottom',\n            calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`),\n        ];\n    }\n    reset(list) {\n        list._setListStyle(['paddingBottom', null]);\n        list._tiles.forEach(tile => {\n            tile._setStyle('marginTop', null);\n            tile._setStyle('paddingTop', null);\n        });\n    }\n    _parseRatio(value) {\n        const ratioParts = value.split(':');\n        if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n        }\n        this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n    }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n    setRowStyles(tile, rowIndex) {\n        // Percent of the available vertical space that one row takes up.\n        let percentHeightPerTile = 100 / this._rowspan;\n        // Fraction of the horizontal gutter size that each column takes up.\n        let gutterHeightPerTile = (this._rows - 1) / this._rows;\n        // Base vertical size of a column.\n        let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n        tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n    }\n    reset(list) {\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n    return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n    return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nclass MatGridList {\n    _element = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    /** Number of columns being rendered. */\n    _cols;\n    /** Used for determining the position of each tile in the grid. */\n    _tileCoordinator;\n    /**\n     * Row height value passed in by user. This can be one of three types:\n     * - Number value (ex: \"100px\"):  sets a fixed row height to that value\n     * - Ratio value (ex: \"4:3\"): sets the row height based on width:height ratio\n     * - \"Fit\" mode (ex: \"fit\"): sets the row height to total height divided by number of rows\n     */\n    _rowHeight;\n    /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n    _gutter = '1px';\n    /** Sets position and size styles for a tile */\n    _tileStyler;\n    /** Query list of tiles that are being rendered. */\n    _tiles;\n    constructor() { }\n    /** Amount of columns in the grid list. */\n    get cols() {\n        return this._cols;\n    }\n    set cols(value) {\n        this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n    }\n    /** Size of the grid list's gutter in pixels. */\n    get gutterSize() {\n        return this._gutter;\n    }\n    set gutterSize(value) {\n        this._gutter = `${value == null ? '' : value}`;\n    }\n    /** Set internal representation of row height from the user-provided value. */\n    get rowHeight() {\n        return this._rowHeight;\n    }\n    set rowHeight(value) {\n        const newValue = `${value == null ? '' : value}`;\n        if (newValue !== this._rowHeight) {\n            this._rowHeight = newValue;\n            this._setTileStyler(this._rowHeight);\n        }\n    }\n    ngOnInit() {\n        this._checkCols();\n        this._checkRowHeight();\n    }\n    /**\n     * The layout calculation is fairly cheap if nothing changes, so there's little cost\n     * to run it frequently.\n     */\n    ngAfterContentChecked() {\n        this._layoutTiles();\n    }\n    /** Throw a friendly error if cols property is missing */\n    _checkCols() {\n        if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n        }\n    }\n    /** Default to equal width:height if rowHeight property is missing */\n    _checkRowHeight() {\n        if (!this._rowHeight) {\n            this._setTileStyler('1:1');\n        }\n    }\n    /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n    _setTileStyler(rowHeight) {\n        if (this._tileStyler) {\n            this._tileStyler.reset(this);\n        }\n        if (rowHeight === MAT_FIT_MODE) {\n            this._tileStyler = new FitTileStyler();\n        }\n        else if (rowHeight && rowHeight.indexOf(':') > -1) {\n            this._tileStyler = new RatioTileStyler(rowHeight);\n        }\n        else {\n            this._tileStyler = new FixedTileStyler(rowHeight);\n        }\n    }\n    /** Computes and applies the size and position for all children grid tiles. */\n    _layoutTiles() {\n        if (!this._tileCoordinator) {\n            this._tileCoordinator = new TileCoordinator();\n        }\n        const tracker = this._tileCoordinator;\n        const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._tileCoordinator.update(this.cols, tiles);\n        this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n        tiles.forEach((tile, index) => {\n            const pos = tracker.positions[index];\n            this._tileStyler.setStyle(tile, pos.row, pos.col);\n        });\n        this._setListStyle(this._tileStyler.getComputedHeight());\n    }\n    /** Sets style on the main grid-list element, given the style name and value. */\n    _setListStyle(style) {\n        if (style) {\n            this._element.nativeElement.style[style[0]] = style[1];\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridList, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatGridList, isStandalone: true, selector: \"mat-grid-list\", inputs: { cols: \"cols\", gutterSize: \"gutterSize\", rowHeight: \"rowHeight\" }, host: { properties: { \"attr.cols\": \"cols\" }, classAttribute: \"mat-grid-list\" }, providers: [\n            {\n                provide: MAT_GRID_LIST,\n                useExisting: MatGridList,\n            },\n        ], queries: [{ propertyName: \"_tiles\", predicate: MatGridTile, descendants: true }], exportAs: [\"matGridList\"], ngImport: i0, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-list', exportAs: 'matGridList', host: {\n                        'class': 'mat-grid-list',\n                        // Ensures that the \"cols\" input value is reflected in the DOM. This is\n                        // needed for the grid-list harness.\n                        '[attr.cols]': 'cols',\n                    }, providers: [\n                        {\n                            provide: MAT_GRID_LIST,\n                            useExisting: MatGridList,\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _tiles: [{\n                type: ContentChildren,\n                args: [MatGridTile, { descendants: true }]\n            }], cols: [{\n                type: Input\n            }], gutterSize: [{\n                type: Input\n            }], rowHeight: [{\n                type: Input\n            }] } });\n\nclass MatGridListModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridListModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule,\n            MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler], exports: [MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatLineModule,\n            MatCommonModule,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule, MatLineModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatGridListModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                    exports: [\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                }]\n        }] });\n\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, MatLine };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,QAAQ,2BAA2B;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACjE,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,2BAA2B;AACjE,SAASC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AACrF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACtK,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAAShB,CAAC,IAAIiB,eAAe,QAAQ,8BAA8B;AACnE,OAAO,gBAAgB;AACvB,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA;AACA,MAAMC,aAAa,gBAAG,IAAIb,cAAc,CAAC,eAAe,CAAC;AAAC,IAEpDc,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACdC,QAAQ,GAAGd,MAAM,CAACC,UAAU,CAAC;IAC7Bc,SAAS,GAAGf,MAAM,CAACY,aAAa,EAAE;MAAEI,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrDC,QAAQ,GAAG,CAAC;IACZC,QAAQ,GAAG,CAAC;IACZC,WAAWA,CAAA,EAAG,CAAE;IAChB;IACA,IAAIC,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACH,QAAQ;IACxB;IACA,IAAIG,OAAOA,CAACC,KAAK,EAAE;MACf,IAAI,CAACJ,QAAQ,GAAGK,IAAI,CAACC,KAAK,CAACd,oBAAoB,CAACY,KAAK,CAAC,CAAC;IAC3D;IACA;IACA,IAAIG,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACN,QAAQ;IACxB;IACA,IAAIM,OAAOA,CAACH,KAAK,EAAE;MACf,IAAI,CAACH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACd,oBAAoB,CAACY,KAAK,CAAC,CAAC;IAC3D;IACA;AACJ;AACA;AACA;IACII,SAASA,CAACC,QAAQ,EAAEL,KAAK,EAAE;MACvB,IAAI,CAACP,QAAQ,CAACa,aAAa,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGL,KAAK;IACvD;IACA,OAAOQ,IAAI,YAAAC,oBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFlB,WAAW;IAAA;IAC9G,OAAOmB,IAAI,kBAD8ElC,EAAE,CAAAmC,iBAAA;MAAAC,IAAA,EACJrB,WAAW;MAAAsB,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADT1C,EAAE,CAAA4C,WAAA,YAAAD,GAAA,CAAArB,OAAA,aAAAqB,GAAA,CAAAjB,OAAA;QAAA;MAAA;MAAAmB,MAAA;QAAAvB,OAAA;QAAAI,OAAA;MAAA;MAAAoB,QAAA;MAAAC,kBAAA,EAAA3D,GAAA;MAAA4D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAqD,eAAA;UAAFrD,EAAE,CAAAsD,cAAA,YACqT,CAAC;UADxTtD,EAAE,CAAAuD,YAAA,EACkV,CAAC;UADrVvD,EAAE,CAAAwD,YAAA,CAC0V,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC1b;EAAC,OA7BK5C,WAAW;AAAA;AA8BjB;EAAA,QAAA6C,SAAA,oBAAAA,SAAA;AAAA;AAaoB,IACdC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB7C,QAAQ,GAAGd,MAAM,CAACC,UAAU,CAAC;IAC7B2D,MAAM;IACNzC,WAAWA,CAAA,EAAG,CAAE;IAChB0C,kBAAkBA,CAAA,EAAG;MACjBpE,QAAQ,CAAC,IAAI,CAACmE,MAAM,EAAE,IAAI,CAAC9C,QAAQ,CAAC;IACxC;IACA,OAAOe,IAAI,YAAAiC,wBAAA/B,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4B,eAAe;IAAA;IAClH,OAAO3B,IAAI,kBAzB8ElC,EAAE,CAAAmC,iBAAA;MAAAC,IAAA,EAyBJyB,eAAe;MAAAxB,SAAA;MAAA4B,cAAA,WAAAC,+BAAAxB,EAAA,EAAAC,GAAA,EAAAwB,QAAA;QAAA,IAAAzB,EAAA;UAzBb1C,EAAE,CAAAoE,cAAA,CAAAD,QAAA,EAyBwItE,OAAO;QAAA;QAAA,IAAA6C,EAAA;UAAA,IAAA2B,EAAA;UAzBjJrE,EAAE,CAAAsE,cAAA,CAAAD,EAAA,GAAFrE,EAAE,CAAAuE,WAAA,QAAA5B,GAAA,CAAAmB,MAAA,GAAAO,EAAA;QAAA;MAAA;MAAAtB,kBAAA,EAAAzD,GAAA;MAAA0D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqB,yBAAA9B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAqD,eAAA,CAAAhE,GAAA;UAAFW,EAAE,CAAAuD,YAAA,EAyBsQ,CAAC;UAzBzQvD,EAAE,CAAAsD,cAAA,YAyB0S,CAAC;UAzB7StD,EAAE,CAAAuD,YAAA,KAyBoW,CAAC;UAzBvWvD,EAAE,CAAAwD,YAAA,CAyB0W,CAAC;UAzB7WxD,EAAE,CAAAuD,YAAA,KAyBqY,CAAC;QAAA;MAAA;MAAAG,aAAA;MAAAC,eAAA;IAAA;EACre;EAAC,OATKE,eAAe;AAAA;AAUrB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AAHA,IAIMa,yBAAyB;EAA/B,MAAMA,yBAAyB,CAAC;IAC5B,OAAO1C,IAAI,YAAA2C,kCAAAzC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwC,yBAAyB;IAAA;IAC5H,OAAOE,IAAI,kBAxC8E3E,EAAE,CAAA4E,iBAAA;MAAAxC,IAAA,EAwCJqC,yBAAyB;MAAApC,SAAA;MAAAC,SAAA;IAAA;EACpH;EAAC,OAHKmC,yBAAyB;AAAA;AAI/B;EAAA,QAAAb,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AAHA,IAIMiB,6BAA6B;EAAnC,MAAMA,6BAA6B,CAAC;IAChC,OAAO9C,IAAI,YAAA+C,sCAAA7C,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4C,6BAA6B;IAAA;IAChI,OAAOF,IAAI,kBAvD8E3E,EAAE,CAAA4E,iBAAA;MAAAxC,IAAA,EAuDJyC,6BAA6B;MAAAxC,SAAA;MAAAC,SAAA;IAAA;EACxH;EAAC,OAHKuC,6BAA6B;AAAA;AAInC;EAAA,QAAAjB,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AAHA,IAIMmB,6BAA6B;EAAnC,MAAMA,6BAA6B,CAAC;IAChC,OAAOhD,IAAI,YAAAiD,sCAAA/C,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8C,6BAA6B;IAAA;IAChI,OAAOJ,IAAI,kBAtE8E3E,EAAE,CAAA4E,iBAAA;MAAAxC,IAAA,EAsEJ2C,6BAA6B;MAAA1C,SAAA;MAAAC,SAAA;IAAA;EACxH;EAAC,OAHKyC,6BAA6B;AAAA;AAInC;EAAA,QAAAnB,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;AACA;AACA,MAAMqB,mBAAmB,GAAG,+BAA+B;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAW;EACXC,KAAK,GAAG,CAAC;EACTjE,QAAQ,GAAG,CAAC;EACZkE,KAAK;EACLC,UAAU;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,IAAIA,CAACC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAE;IACvC,IAAI,CAACR,WAAW,GAAGS,cAAc,CAACJ,UAAU,CAAC;IAC7C,IAAI,CAACJ,KAAK,GAAGK,OAAO,CAACI,QAAQ;IAC7B,IAAI,CAAC1E,QAAQ,GAAGsE,OAAO,CAACnE,OAAO;IAC/B,IAAI,CAAC+D,KAAK,GAAGK,IAAI;IACjB,IAAI,CAACJ,UAAU,GAAGK,SAAS;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,eAAeA,CAACC,WAAW,EAAEC,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,OAAO,IAAID,WAAW,QAAQ,IAAI,CAACZ,WAAW,MAAMa,cAAc,IAAI;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IAC9B;IACA;IACA,OAAOA,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGC,IAAI,CAAC,IAAIF,QAAQ,MAAM,IAAI,CAACf,WAAW,OAAOgB,MAAM,EAAE,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAACH,QAAQ,EAAEI,IAAI,EAAE;IACxB,OAAO,IAAIJ,QAAQ,MAAMI,IAAI,QAAQA,IAAI,GAAG,CAAC,MAAM,IAAI,CAACnB,WAAW,GAAG;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoB,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B;IACA,IAAIC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAACtB,KAAK;IAC1C;IACA;IACA,IAAIuB,0BAA0B,GAAG,CAAC,IAAI,CAACvB,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IAC9D,IAAI,CAACwB,YAAY,CAACL,IAAI,EAAEE,QAAQ,EAAEC,mBAAmB,EAAEC,0BAA0B,CAAC;IAClF,IAAI,CAACE,YAAY,CAACN,IAAI,EAAEC,QAAQ,EAAEE,mBAAmB,EAAEC,0BAA0B,CAAC;EACtF;EACA;EACAC,YAAYA,CAACL,IAAI,EAAEE,QAAQ,EAAEK,YAAY,EAAEC,WAAW,EAAE;IACpD;IACA,IAAIC,aAAa,GAAG,IAAI,CAACnB,eAAe,CAACiB,YAAY,EAAEC,WAAW,CAAC;IACnE;IACA;IACA,IAAIE,IAAI,GAAG,IAAI,CAAC5B,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IACvDkB,IAAI,CAAC7E,SAAS,CAACuF,IAAI,EAAE,IAAI,CAACjB,eAAe,CAACgB,aAAa,EAAEP,QAAQ,CAAC,CAAC;IACnEF,IAAI,CAAC7E,SAAS,CAAC,OAAO,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAACY,aAAa,EAAET,IAAI,CAAC9E,OAAO,CAAC,CAAC,CAAC;EAChF;EACA;AACJ;AACA;EACIyF,aAAaA,CAAA,EAAG;IACZ,OAAO,GAAG,IAAI,CAAChC,WAAW,OAAO,IAAI,CAAChE,QAAQ,OAAO;EACzD;EACA;AACJ;AACA;AACA;EACIiG,WAAWA,CAACC,UAAU,EAAE;IACpB,OAAO,GAAG,IAAI,CAAClG,QAAQ,MAAM,IAAI,CAACkF,WAAW,CAACgB,UAAU,EAAE,CAAC,CAAC,EAAE;EAClE;EACA;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASrC,UAAU,CAAC;EACrCsC,cAAc;EACdnG,WAAWA,CAACmG,cAAc,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAjC,IAAIA,CAACC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAE;IACvC,KAAK,CAACJ,IAAI,CAACC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,CAAC;IAChD,IAAI,CAAC6B,cAAc,GAAG5B,cAAc,CAAC,IAAI,CAAC4B,cAAc,CAAC;IACzD,IAAI,CAACvC,mBAAmB,CAACwC,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,KAC7C,OAAO5D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM8D,KAAK,CAAC,kBAAkB,IAAI,CAACF,cAAc,qBAAqB,CAAC;IAC3E;EACJ;EACAV,YAAYA,CAACN,IAAI,EAAEC,QAAQ,EAAE;IACzBD,IAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,IAAI,CAACsE,eAAe,CAAC,IAAI,CAACuB,cAAc,EAAEf,QAAQ,CAAC,CAAC;IAC1ED,IAAI,CAAC7E,SAAS,CAAC,QAAQ,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACmB,cAAc,EAAEhB,IAAI,CAAClF,OAAO,CAAC,CAAC,CAAC;EACvF;EACAgG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,QAAQ,EAAElB,IAAI,CAAC,GAAG,IAAI,CAACgB,WAAW,CAAC,IAAI,CAACI,cAAc,CAAC,MAAM,IAAI,CAACL,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EACjG;EACAQ,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAID,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAACvB,IAAI,IAAI;QACxBA,IAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3B6E,IAAI,CAAC7E,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqG,eAAe,SAAS9C,UAAU,CAAC;EACrC;EACA+C,cAAc;EACdC,cAAc;EACd7G,WAAWA,CAACE,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAAC4G,WAAW,CAAC5G,KAAK,CAAC;EAC3B;EACAuF,YAAYA,CAACN,IAAI,EAAEC,QAAQ,EAAEM,YAAY,EAAEC,WAAW,EAAE;IACpD,IAAIoB,oBAAoB,GAAGrB,YAAY,GAAG,IAAI,CAACkB,cAAc;IAC7D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACpC,eAAe,CAACsC,oBAAoB,EAAEpB,WAAW,CAAC;IAC7E;IACA;IACA;IACAR,IAAI,CAAC7E,SAAS,CAAC,WAAW,EAAE,IAAI,CAACsE,eAAe,CAAC,IAAI,CAACiC,cAAc,EAAEzB,QAAQ,CAAC,CAAC;IAChFD,IAAI,CAAC7E,SAAS,CAAC,YAAY,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC6B,cAAc,EAAE1B,IAAI,CAAClF,OAAO,CAAC,CAAC,CAAC;EAC3F;EACAgG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CACH,eAAe,EACflB,IAAI,CAAC,GAAG,IAAI,CAACgB,WAAW,CAAC,IAAI,CAACc,cAAc,CAAC,MAAM,IAAI,CAACf,aAAa,CAAC,CAAC,EAAE,CAAC,CAC7E;EACL;EACAQ,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAC3CD,IAAI,CAACE,MAAM,CAACC,OAAO,CAACvB,IAAI,IAAI;MACxBA,IAAI,CAAC7E,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;MACjC6E,IAAI,CAAC7E,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACN;EACAwG,WAAWA,CAAC5G,KAAK,EAAE;IACf,MAAM8G,UAAU,GAAG9G,KAAK,CAAC+G,KAAK,CAAC,GAAG,CAAC;IACnC,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,KAAK,OAAO3E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC5E,MAAM8D,KAAK,CAAC,uDAAuDnG,KAAK,GAAG,CAAC;IAChF;IACA,IAAI,CAAC0G,cAAc,GAAGO,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,SAASvD,UAAU,CAAC;EACnC4B,YAAYA,CAACN,IAAI,EAAEC,QAAQ,EAAE;IACzB;IACA,IAAI2B,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAACjH,QAAQ;IAC9C;IACA,IAAIuH,mBAAmB,GAAG,CAAC,IAAI,CAACtD,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IACvD;IACA,IAAI8C,cAAc,GAAG,IAAI,CAACpC,eAAe,CAACsC,oBAAoB,EAAEM,mBAAmB,CAAC;IACpFlC,IAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,IAAI,CAACsE,eAAe,CAACiC,cAAc,EAAEzB,QAAQ,CAAC,CAAC;IACrED,IAAI,CAAC7E,SAAS,CAAC,QAAQ,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC6B,cAAc,EAAE1B,IAAI,CAAClF,OAAO,CAAC,CAAC,CAAC;EAClF;EACAqG,KAAKA,CAACC,IAAI,EAAE;IACR,IAAIA,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAACvB,IAAI,IAAI;QACxBA,IAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3B6E,IAAI,CAAC7E,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA,SAASyE,IAAIA,CAACuC,GAAG,EAAE;EACf,OAAO,QAAQA,GAAG,GAAG;AACzB;AACA;AACA,SAAS/C,cAAcA,CAACrE,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACqH,KAAK,CAAC,eAAe,CAAC,GAAGrH,KAAK,GAAG,GAAGA,KAAK,IAAI;AAC9D;;AAEA;AACA;AACA;AACA,MAAMsH,YAAY,GAAG,KAAK;AAAC,IACrBC,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACd9H,QAAQ,GAAGd,MAAM,CAACC,UAAU,CAAC;IAC7B4I,IAAI,GAAG7I,MAAM,CAACU,cAAc,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjD;IACAmE,KAAK;IACL;IACA2D,gBAAgB;IAChB;AACJ;AACA;AACA;AACA;AACA;IACIC,UAAU;IACV;IACAC,OAAO,GAAG,KAAK;IACf;IACAC,WAAW;IACX;IACArB,MAAM;IACNzG,WAAWA,CAAA,EAAG,CAAE;IAChB;IACA,IAAIqE,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACL,KAAK;IACrB;IACA,IAAIK,IAAIA,CAACnE,KAAK,EAAE;MACZ,IAAI,CAAC8D,KAAK,GAAG7D,IAAI,CAAC4H,GAAG,CAAC,CAAC,EAAE5H,IAAI,CAACC,KAAK,CAACd,oBAAoB,CAACY,KAAK,CAAC,CAAC,CAAC;IACrE;IACA;IACA,IAAIiE,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC0D,OAAO;IACvB;IACA,IAAI1D,UAAUA,CAACjE,KAAK,EAAE;MAClB,IAAI,CAAC2H,OAAO,GAAG,GAAG3H,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;IAClD;IACA;IACA,IAAI8H,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACJ,UAAU;IAC1B;IACA,IAAII,SAASA,CAAC9H,KAAK,EAAE;MACjB,MAAM+H,QAAQ,GAAG,GAAG/H,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;MAChD,IAAI+H,QAAQ,KAAK,IAAI,CAACL,UAAU,EAAE;QAC9B,IAAI,CAACA,UAAU,GAAGK,QAAQ;QAC1B,IAAI,CAACC,cAAc,CAAC,IAAI,CAACN,UAAU,CAAC;MACxC;IACJ;IACAO,QAAQA,CAAA,EAAG;MACP,IAAI,CAACC,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;IACA;AACJ;AACA;AACA;IACIC,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;IACA;IACAH,UAAUA,CAAA,EAAG;MACT,IAAI,CAAC,IAAI,CAAC/D,IAAI,KAAK,OAAO9B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/D,MAAM8D,KAAK,CAAC,iDAAiD,GAAG,mCAAmC,CAAC;MACxG;IACJ;IACA;IACAgC,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE;QAClB,IAAI,CAACM,cAAc,CAAC,KAAK,CAAC;MAC9B;IACJ;IACA;IACAA,cAAcA,CAACF,SAAS,EAAE;MACtB,IAAI,IAAI,CAACF,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACxB,KAAK,CAAC,IAAI,CAAC;MAChC;MACA,IAAI0B,SAAS,KAAKR,YAAY,EAAE;QAC5B,IAAI,CAACM,WAAW,GAAG,IAAIV,aAAa,CAAC,CAAC;MAC1C,CAAC,MACI,IAAIY,SAAS,IAAIA,SAAS,CAACQ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QAC/C,IAAI,CAACV,WAAW,GAAG,IAAInB,eAAe,CAACqB,SAAS,CAAC;MACrD,CAAC,MACI;QACD,IAAI,CAACF,WAAW,GAAG,IAAI5B,eAAe,CAAC8B,SAAS,CAAC;MACrD;IACJ;IACA;IACAO,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAACZ,gBAAgB,EAAE;QACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI7J,eAAe,CAAC,CAAC;MACjD;MACA,MAAMsG,OAAO,GAAG,IAAI,CAACuD,gBAAgB;MACrC,MAAMc,KAAK,GAAG,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACvD,IAAI,IAAI,CAACA,IAAI,CAACvF,SAAS,IAAIuF,IAAI,CAACvF,SAAS,KAAK,IAAI,CAAC;MACpF,MAAM0E,SAAS,GAAG,IAAI,CAACoD,IAAI,GAAG,IAAI,CAACA,IAAI,CAACxH,KAAK,GAAG,KAAK;MACrD,IAAI,CAACyH,gBAAgB,CAACgB,MAAM,CAAC,IAAI,CAACtE,IAAI,EAAEoE,KAAK,CAAC;MAC9C,IAAI,CAACX,WAAW,CAAC5D,IAAI,CAAC,IAAI,CAACC,UAAU,EAAEC,OAAO,EAAE,IAAI,CAACC,IAAI,EAAEC,SAAS,CAAC;MACrEmE,KAAK,CAAC/B,OAAO,CAAC,CAACvB,IAAI,EAAEyD,KAAK,KAAK;QAC3B,MAAMC,GAAG,GAAGzE,OAAO,CAAC0E,SAAS,CAACF,KAAK,CAAC;QACpC,IAAI,CAACd,WAAW,CAAC5C,QAAQ,CAACC,IAAI,EAAE0D,GAAG,CAACE,GAAG,EAAEF,GAAG,CAACG,GAAG,CAAC;MACrD,CAAC,CAAC;MACF,IAAI,CAACxC,aAAa,CAAC,IAAI,CAACsB,WAAW,CAAC7B,iBAAiB,CAAC,CAAC,CAAC;IAC5D;IACA;IACAO,aAAaA,CAAC/F,KAAK,EAAE;MACjB,IAAIA,KAAK,EAAE;QACP,IAAI,CAACd,QAAQ,CAACa,aAAa,CAACC,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;MAC1D;IACJ;IACA,OAAOC,IAAI,YAAAuI,oBAAArI,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6G,WAAW;IAAA;IAC9G,OAAO5G,IAAI,kBApa8ElC,EAAE,CAAAmC,iBAAA;MAAAC,IAAA,EAoaJ0G,WAAW;MAAAzG,SAAA;MAAA4B,cAAA,WAAAsG,2BAAA7H,EAAA,EAAAC,GAAA,EAAAwB,QAAA;QAAA,IAAAzB,EAAA;UApaT1C,EAAE,CAAAoE,cAAA,CAAAD,QAAA,EAyarCpD,WAAW;QAAA;QAAA,IAAA2B,EAAA;UAAA,IAAA2B,EAAA;UAzawBrE,EAAE,CAAAsE,cAAA,CAAAD,EAAA,GAAFrE,EAAE,CAAAuE,WAAA,QAAA5B,GAAA,CAAAmF,MAAA,GAAAzD,EAAA;QAAA;MAAA;MAAA/B,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgI,yBAAA9H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAA4C,WAAA,SAAAD,GAAA,CAAA+C,IAAA;QAAA;MAAA;MAAA7C,MAAA;QAAA6C,IAAA;QAAAF,UAAA;QAAA6D,SAAA;MAAA;MAAAvG,QAAA;MAAA2H,QAAA,GAAFzK,EAAE,CAAA0K,kBAAA,CAoa+N,CAClT;QACIC,OAAO,EAAE7J,aAAa;QACtB8J,WAAW,EAAE9B;MACjB,CAAC,CACJ;MAAA/F,kBAAA,EAAA3D,GAAA;MAAA4D,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA0H,qBAAAnI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzaoF1C,EAAE,CAAAqD,eAAA;UAAFrD,EAAE,CAAAsD,cAAA,SAyasD,CAAC;UAzazDtD,EAAE,CAAAuD,YAAA,EAyamF,CAAC;UAzatFvD,EAAE,CAAAwD,YAAA,CAya2F,CAAC;QAAA;MAAA;MAAAC,MAAA,GAAAlE,GAAA;MAAAmE,aAAA;MAAAC,eAAA;IAAA;EAC3L;EAAC,OAjHKmF,WAAW;AAAA;AAkHjB;EAAA,QAAAlF,SAAA,oBAAAA,SAAA;AAAA;AAsBoB,IAEdkH,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB,OAAO/I,IAAI,YAAAgJ,0BAAA9I,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6I,iBAAiB;IAAA;IACpH,OAAOE,IAAI,kBArc8EhL,EAAE,CAAAiL,gBAAA;MAAA7I,IAAA,EAqcS0I;IAAiB;IAerH,OAAOI,IAAI,kBApd8ElL,EAAE,CAAAmL,gBAAA;MAAAC,OAAA,GAodsCrL,aAAa,EACtIc,eAAe,EAAEd,aAAa,EAC9Bc,eAAe;IAAA;EAC3B;EAAC,OApBKiK,iBAAiB;AAAA;AAqBvB;EAAA,QAAAlH,SAAA,oBAAAA,SAAA;AAAA;AA0BA,SAASa,yBAAyB,EAAEqE,WAAW,EAAEgC,iBAAiB,EAAE/J,WAAW,EAAEgE,6BAA6B,EAAEF,6BAA6B,EAAEhB,eAAe,EAAEhE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}