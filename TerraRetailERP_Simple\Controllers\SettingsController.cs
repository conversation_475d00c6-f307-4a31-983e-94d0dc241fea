using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Tags("⚙️ System Settings")]
    public class SettingsController : ControllerBase
    {
        private readonly AppDbContext _context;

        public SettingsController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("branches")]
        public async Task<ActionResult<IEnumerable<Branch>>> GetBranches()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الفروع بنجاح",
                    data = branches,
                    count = branches.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("branches")]
        public async Task<ActionResult<Branch>> CreateBranch(CreateBranchRequest request)
        {
            try
            {
                var branch = new Branch
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Code = $"BR{DateTime.Now:yyyyMMddHHmmss}",
                    Address = request.Address,
                    Phone = request.Phone,
                    Email = request.Email,
                    IsActive = true,
                    IsMainBranch = false,
                    CreatedAt = DateTime.Now
                };

                _context.Branches.Add(branch);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetBranches), new { id = branch.Id }, new
                {
                    success = true,
                    message = "تم إضافة الفرع بنجاح",
                    data = branch
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("categories")]
        public async Task<ActionResult<Category>> CreateCategory(CreateCategoryRequest request)
        {
            try
            {
                var category = new Category
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    ParentId = request.ParentId,
                    DisplayOrder = request.DisplayOrder,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetCategories), new { id = category.Id }, new
                {
                    success = true,
                    message = "تم إضافة الفئة بنجاح",
                    data = category
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("categories")]
        public async Task<ActionResult<IEnumerable<Category>>> GetCategories()
        {
            try
            {
                var categories = await _context.Categories
                    .Include(c => c.Parent)
                    .Include(c => c.Children)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الفئات بنجاح",
                    data = categories,
                    count = categories.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("units")]
        public async Task<ActionResult<Unit>> CreateUnit(CreateUnitRequest request)
        {
            try
            {
                var unit = new Unit
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Symbol = request.Symbol,
                    DisplayOrder = request.DisplayOrder,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Units.Add(unit);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetUnits), new { id = unit.Id }, new
                {
                    success = true,
                    message = "تم إضافة الوحدة بنجاح",
                    data = unit
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("units")]
        public async Task<ActionResult<IEnumerable<Unit>>> GetUnits()
        {
            try
            {
                var units = await _context.Units
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.DisplayOrder)
                    .ThenBy(u => u.NameAr)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة الوحدات بنجاح",
                    data = units,
                    count = units.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("system-info")]
        public async Task<ActionResult> GetSystemInfo()
        {
            try
            {
                var totalUsers = await _context.Users.CountAsync();
                var activeUsers = await _context.Users.Where(u => u.IsActive).CountAsync();
                var totalBranches = await _context.Branches.CountAsync();
                var activeBranches = await _context.Branches.Where(b => b.IsActive).CountAsync();
                var totalProducts = await _context.Products.CountAsync();
                var activeProducts = await _context.Products.Where(p => p.IsActive).CountAsync();
                var totalCustomers = await _context.Customers.CountAsync();
                var activeCustomers = await _context.Customers.Where(c => c.IsActive).CountAsync();
                var totalSuppliers = await _context.Suppliers.CountAsync();
                var activeSuppliers = await _context.Suppliers.Where(s => s.IsActive).CountAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب معلومات النظام بنجاح",
                    data = new
                    {
                        systemName = "Terra Retail ERP",
                        version = "1.0.0",
                        statistics = new
                        {
                            users = new { total = totalUsers, active = activeUsers },
                            branches = new { total = totalBranches, active = activeBranches },
                            products = new { total = totalProducts, active = activeProducts },
                            customers = new { total = totalCustomers, active = activeCustomers },
                            suppliers = new { total = totalSuppliers, active = activeSuppliers }
                        },
                        serverTime = DateTime.Now,
                        uptime = DateTime.Now - System.Diagnostics.Process.GetCurrentProcess().StartTime
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpGet("users")]
        public async Task<ActionResult<IEnumerable<User>>> GetUsers()
        {
            try
            {
                var users = await _context.Users
                    .Where(u => u.IsActive)
                    .Select(u => new
                    {
                        u.Id,
                        u.Username,
                        u.Email,
                        u.FullName,
                        u.IsActive,
                        u.IsSystemAdmin,
                        u.CreatedAt,
                        u.LastLoginAt
                    })
                    .OrderBy(u => u.FullName)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة المستخدمين بنجاح",
                    data = users,
                    count = users.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }

        [HttpPost("users")]
        public async Task<ActionResult<User>> CreateUser(CreateUserRequest request)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username || u.Email == request.Email);

                if (existingUser != null)
                    return BadRequest(new 
                    { 
                        success = false,
                        message = "اسم المستخدم أو البريد الإلكتروني موجود مسبقاً" 
                    });

                var user = new User
                {
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                    FullName = request.FullName,
                    IsActive = true,
                    IsSystemAdmin = request.IsSystemAdmin,
                    CreatedAt = DateTime.Now
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetUsers), new { id = user.Id }, new
                {
                    success = true,
                    message = "تم إضافة المستخدم بنجاح",
                    data = new
                    {
                        user.Id,
                        user.Username,
                        user.Email,
                        user.FullName,
                        user.IsActive,
                        user.IsSystemAdmin,
                        user.CreatedAt
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false,
                    message = "حدث خطأ في النظام", 
                    error = ex.Message 
                });
            }
        }
    }

    // DTOs


    public class CreateCategoryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public int? ParentId { get; set; }
        public int DisplayOrder { get; set; } = 1;
    }

    public class CreateUnitRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string Symbol { get; set; } = string.Empty;
        public int DisplayOrder { get; set; } = 1;
    }

    public class CreateUserRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public bool IsSystemAdmin { get; set; } = false;
    }
}
