/* Terra Retail ERP Global Styles */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  background-color: #f8f9fa;
  color: #212529;
  line-height: 1.6;
}

/* Arabic Font Support */
.arabic-text {
  font-family: 'Cairo', 'Segoe UI', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

/* Button Styles */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;

  &:hover {
    text-decoration: none;
  }

  &.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;

    &:hover {
      background-color: #0056b3;
      border-color: #004085;
    }
  }

  &.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;

    &:hover {
      background-color: #1e7e34;
      border-color: #1c7430;
    }
  }

  &.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;

    &:hover {
      background-color: #c82333;
      border-color: #bd2130;
    }
  }

  &.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;

    &:hover {
      background-color: #e0a800;
      border-color: #d39e00;
    }
  }

  &.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;

    &:hover {
      background-color: #545b62;
      border-color: #4e555b;
    }
  }
}

/* Form Styles */
.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

.form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #212529;
}

/* Card Styles */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .card-header {
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
  }

  .card-body {
    flex: 1 1 auto;
    padding: 1rem;
  }

  .card-title {
    margin-bottom: 0.75rem;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

/* Table Styles */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  border-collapse: collapse;

  th,
  td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
    text-align: right;
  }

  thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 600;
  }

  tbody + tbody {
    border-top: 2px solid #dee2e6;
  }
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Utilities */
@media (max-width: 576px) {
  .d-sm-none { display: none !important; }
}

@media (max-width: 768px) {
  .d-md-none { display: none !important; }
}

@media (max-width: 992px) {
  .d-lg-none { display: none !important; }
}
