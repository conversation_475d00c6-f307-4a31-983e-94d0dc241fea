{"ast": null, "code": "import { ElementRef } from '@angular/core';\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };", "map": {"version": 3, "names": ["ElementRef", "coerceNumberProperty", "value", "fallback<PERSON><PERSON><PERSON>", "_isNumberValue", "Number", "arguments", "length", "isNaN", "parseFloat", "coerceElement", "elementOrRef", "nativeElement", "_", "a", "c"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/element-x4z00URv.mjs"], "sourcesContent": ["import { ElementRef } from '@angular/core';\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n    if (_isNumberValue(value)) {\n        return Number(value);\n    }\n    return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n    // and other non-number values as NaN, where Number just uses 0) but it considers the string\n    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n    return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n    return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,aAAa,GAAG,CAAC,EAAE;EACpD,IAAIC,cAAc,CAACF,KAAK,CAAC,EAAE;IACvB,OAAOG,MAAM,CAACH,KAAK,CAAC;EACxB;EACA,OAAOI,SAAS,CAACC,MAAM,KAAK,CAAC,GAAGJ,aAAa,GAAG,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACF,KAAK,EAAE;EAC3B;EACA;EACA;EACA,OAAO,CAACM,KAAK,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC,IAAI,CAACM,KAAK,CAACH,MAAM,CAACH,KAAK,CAAC,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACC,YAAY,EAAE;EACjC,OAAOA,YAAY,YAAYX,UAAU,GAAGW,YAAY,CAACC,aAAa,GAAGD,YAAY;AACzF;AAEA,SAASP,cAAc,IAAIS,CAAC,EAAEH,aAAa,IAAII,CAAC,EAAEb,oBAAoB,IAAIc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}