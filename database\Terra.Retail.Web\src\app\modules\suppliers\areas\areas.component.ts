import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';

// Interfaces
interface Area {
  Id: number;
  NameAr: string;
  NameEn: string;
  Code: string;
  IsActive: boolean;
  CreatedAt?: string;
  UpdatedAt?: string;
}

@Component({
  selector: 'app-areas',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCheckboxModule
  ],
  templateUrl: './areas.component.html',
  styleUrls: ['./areas.component.scss']
})
export class AreasComponent implements OnInit, OnDestroy {

  // Component State
  isLoading = false;
  showAddForm = false;
  editingArea: Area | null = null;
  
  // Data
  areas: Area[] = [];
  
  // Form
  areaForm: FormGroup;
  
  // Table Configuration
  displayedColumns: string[] = ['nameAr', 'nameEn', 'code', 'isActive', 'actions'];
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private router: Router,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.areaForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadAreas();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    return this.fb.group({
      nameAr: ['', [Validators.required, Validators.minLength(2)]],
      nameEn: ['', [Validators.required, Validators.minLength(2)]],
      code: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(10)]],
      isActive: [true]
    });
  }

  /**
   * Load areas
   */
  loadAreas(): void {
    this.isLoading = true;
    
    const sub = this.http.get<any>('http://localhost:5127/api/simple/areas-db').subscribe({
      next: (response) => {
        console.log('Areas response:', response);
        this.areas = response.areas || [];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading areas:', error);
        this.showError('خطأ في تحميل المحافظات');
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  /**
   * Show add form
   */
  showAddArea(): void {
    this.showAddForm = true;
    this.editingArea = null;
    this.areaForm.reset();
    this.areaForm.patchValue({ isActive: true });
  }

  /**
   * Edit area
   */
  editArea(area: Area): void {
    this.showAddForm = true;
    this.editingArea = area;
    this.areaForm.patchValue({
      nameAr: area.NameAr,
      nameEn: area.NameEn,
      code: area.Code,
      isActive: area.IsActive
    });
  }

  /**
   * Save area
   */
  saveArea(): void {
    if (this.areaForm.valid) {
      this.isLoading = true;
      
      const formValue = this.areaForm.value;
      const request = {
        nameAr: formValue.nameAr,
        nameEn: formValue.nameEn,
        code: formValue.code.toUpperCase(),
        isActive: formValue.isActive
      };

      const apiCall = this.editingArea
        ? this.http.put<any>(`http://localhost:5127/api/areas/${this.editingArea.Id}`, request)
        : this.http.post<any>('http://localhost:5127/api/areas', request);

      const sub = apiCall.subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccess(this.editingArea ? 'تم تحديث المحافظة بنجاح' : 'تم إضافة المحافظة بنجاح');
          this.showAddForm = false;
          this.loadAreas();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error saving area:', error);
          this.showError('خطأ في حفظ المحافظة');
        }
      });
      this.subscriptions.push(sub);
    } else {
      this.markFormGroupTouched();
      this.showError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  /**
   * Delete area
   */
  deleteArea(area: Area): void {
    if (confirm(`هل أنت متأكد من حذف المحافظة "${area.NameAr}"؟`)) {
      this.isLoading = true;

      const sub = this.http.delete(`http://localhost:5127/api/areas/${area.Id}`).subscribe({
        next: (response: any) => {
          this.isLoading = false;
          this.showSuccess('تم حذف المحافظة بنجاح');
          this.loadAreas();
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error deleting area:', error);
          this.showError('خطأ في حذف المحافظة');
        }
      });
      this.subscriptions.push(sub);
    }
  }

  /**
   * Cancel form
   */
  cancelForm(): void {
    this.showAddForm = false;
    this.editingArea = null;
    this.areaForm.reset();
  }

  /**
   * Go back to suppliers
   */
  goBack(): void {
    this.router.navigate(['/suppliers']);
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.areaForm.controls).forEach(key => {
      const control = this.areaForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Show success message
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * Show error message
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'إغلاق', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }
}
