TableName	ColumnName	DataType	MaxLength	IsNullable	ColumnOrder
AccountBalances	Id	int	4	0	1
AccountBalances	AccountId	int	4	0	2
AccountBalances	DebitBalance	decimal	9	1	3
AccountBalances	CreditBalance	decimal	9	1	4
AccountBalances	LastTransactionDate	datetime2	8	1	5
AccountBalances	UpdatedAt	datetime2	8	1	6
AccountBalances	UpdatedBy	nvarchar	200	1	7
Areas	Id	int	4	0	1
Areas	NameAr	nvarchar	100	0	2
Areas	NameEn	nvarchar	100	1	3
Areas	Code	nvarchar	20	1	4
Areas	ParentAreaId	int	4	1	5
Areas	Description	nvarchar	400	1	6
Areas	IsActive	bit	1	0	7
Areas	DisplayOrder	int	4	0	8
Areas	Longitude	decimal	9	1	9
Areas	Latitude	decimal	9	1	10
Areas	CoverageRadius	decimal	9	1	11
Areas	CreatedAt	datetime2	8	0	12
Areas	UpdatedAt	datetime2	8	1	13
Areas	IsDeleted	bit	1	1	14
Areas	CreatedBy	nvarchar	200	1	15
Areas	UpdatedBy	nvarchar	200	1	16
AttendanceRecords	Id	int	4	0	1
AttendanceRecords	EmployeeId	int	4	0	2
AttendanceRecords	ShiftId	int	4	0	3
AttendanceRecords	ShiftDate	date	3	0	4
AttendanceRecords	ActualCheckInTime	datetime	8	1	5
AttendanceRecords	ActualCheckOutTime	datetime	8	1	6
AttendanceRecords	WorkingMinutes	int	4	1	7
AttendanceRecords	LateMinutes	int	4	1	8
AttendanceRecords	OvertimeMinutes	int	4	1	9
AttendanceRecords	AttendanceStatus	nvarchar	40	0	10
AttendanceRecords	IsComplete	bit	1	1	11
AuditTrail	Id	bigint	8	0	1
AuditTrail	TableName	nvarchar	200	0	2
AuditTrail	RecordId	nvarchar	100	0	3
AuditTrail	Operation	nvarchar	40	0	4
AuditTrail	OldValues	nvarchar	-1	1	5
AuditTrail	NewValues	nvarchar	-1	1	6
AuditTrail	UserId	nvarchar	200	0	7
AuditTrail	UserName	nvarchar	400	1	8
AuditTrail	OperationDate	datetime2	8	1	9
AuditTrail	IPAddress	nvarchar	100	1	10
AuditTrail	UserAgent	nvarchar	1000	1	11
AuditTrail	Notes	nvarchar	1000	1	12
BankAccounts	Id	int	4	0	1
BankAccounts	AccountCode	nvarchar	40	0	2
BankAccounts	BankNameAr	nvarchar	400	0	3
BankAccounts	BankNameEn	nvarchar	400	1	4
BankAccounts	AccountNumber	nvarchar	100	0	5
BankAccounts	AccountNameAr	nvarchar	400	0	6
BankAccounts	AccountNameEn	nvarchar	400	1	7
BankAccounts	IBAN	nvarchar	100	1	8
BankAccounts	SwiftCode	nvarchar	40	1	9
BankAccounts	Currency	nvarchar	20	1	10
BankAccounts	OpeningBalance	decimal	9	1	11
BankAccounts	CurrentBalance	decimal	9	1	12
BankAccounts	IsActive	bit	1	1	13
BankAccounts	CreatedAt	datetime2	8	1	14
BankAccounts	CreatedBy	nvarchar	200	0	15
BankAccounts	UpdatedAt	datetime2	8	1	16
BankAccounts	UpdatedBy	nvarchar	200	1	17
BankAccounts	IsDeleted	bit	1	1	18
BankTransactions	Id	bigint	8	0	1
BankTransactions	BankAccountId	int	4	0	2
BankTransactions	TransactionNumber	nvarchar	40	0	3
BankTransactions	TransactionDate	date	3	0	4
BankTransactions	TransactionType	nvarchar	40	0	5
BankTransactions	Amount	decimal	9	0	6
BankTransactions	Description	nvarchar	1000	1	7
BankTransactions	ReferenceNumber	nvarchar	100	1	8
BankTransactions	JournalEntryId	bigint	8	1	9
BankTransactions	Status	nvarchar	40	1	10
BankTransactions	CreatedAt	datetime2	8	1	11
BankTransactions	CreatedBy	nvarchar	200	0	12
BankTransactions	IsDeleted	bit	1	1	13
BiometricDevices	Id	int	4	0	1
BiometricDevices	DeviceName	nvarchar	200	0	2
BiometricDevices	IPAddress	nvarchar	30	0	3
BiometricDevices	Port	int	4	1	4
BiometricDevices	DeviceType	nvarchar	100	0	5
BiometricDevices	BranchId	int	4	0	6
BiometricDevices	IsActive	bit	1	1	7
Branches	Id	int	4	0	1
Branches	NameAr	nvarchar	200	0	2
Branches	NameEn	nvarchar	200	1	3
Branches	Code	nvarchar	20	0	4
Branches	Address	nvarchar	1000	1	5
Branches	Phone	nvarchar	40	1	6
Branches	Email	nvarchar	200	1	7
Branches	ManagerName	nvarchar	200	1	8
Branches	IsActive	bit	1	0	9
Branches	IsMainBranch	bit	1	0	10
Branches	OpeningDate	datetime2	8	1	11
Branches	WorkingHours	nvarchar	200	1	12
Branches	Longitude	decimal	9	1	13
Branches	Latitude	decimal	9	1	14
Branches	Area	decimal	9	1	15
Branches	EmployeeCount	int	4	1	16
Branches	TaxNumber	nvarchar	100	1	17
Branches	CommercialRegister	nvarchar	100	1	18
Branches	CreatedAt	datetime2	8	0	19
Branches	UpdatedAt	datetime2	8	1	20
BranchTransferDetails	Id	bigint	8	0	1
BranchTransferDetails	BranchTransferId	bigint	8	0	2
BranchTransferDetails	ProductId	int	4	0	3
BranchTransferDetails	RequestedQuantity	decimal	9	0	4
BranchTransferDetails	ShippedQuantity	decimal	9	1	5
BranchTransferDetails	ReceivedQuantity	decimal	9	1	6
BranchTransferDetails	UnitCost	decimal	9	0	7
BranchTransferDetails	TotalCost	decimal	9	0	8
BranchTransferDetails	Notes	nvarchar	1000	1	9
BranchTransferDetails	CreatedAt	datetime2	8	1	10
BranchTransferDetails	CreatedBy	nvarchar	200	0	11
BranchTransferDetails	IsDeleted	bit	1	1	12
BranchTransfers	Id	bigint	8	0	1
BranchTransfers	TransferNumber	nvarchar	40	0	2
BranchTransfers	TransferDate	date	3	0	3
BranchTransfers	FromBranchId	int	4	0	4
BranchTransfers	ToBranchId	int	4	0	5
BranchTransfers	FromWarehouseId	int	4	0	6
BranchTransfers	ToWarehouseId	int	4	0	7
BranchTransfers	TotalItems	int	4	1	8
BranchTransfers	TotalQuantity	decimal	9	1	9
BranchTransfers	TotalValue	decimal	9	1	10
BranchTransfers	Status	nvarchar	40	1	11
BranchTransfers	Notes	nvarchar	1000	1	12
BranchTransfers	RequestedBy	nvarchar	200	1	13
BranchTransfers	ApprovedBy	nvarchar	200	1	14
BranchTransfers	ShippedBy	nvarchar	200	1	15
BranchTransfers	ReceivedBy	nvarchar	200	1	16
BranchTransfers	ShippedDate	datetime2	8	1	17
BranchTransfers	ReceivedDate	datetime2	8	1	18
BranchTransfers	CreatedAt	datetime2	8	1	19
BranchTransfers	CreatedBy	nvarchar	200	0	20
BranchTransfers	UpdatedAt	datetime2	8	1	21
BranchTransfers	UpdatedBy	nvarchar	200	1	22
BranchTransfers	IsDeleted	bit	1	1	23
Categories	Id	int	4	0	1
Categories	NameAr	nvarchar	200	0	2
Categories	NameEn	nvarchar	200	1	3
Categories	Code	nvarchar	40	1	4
Categories	ParentCategoryId	int	4	1	5
Categories	Level	int	4	0	6
Categories	Path	nvarchar	200	1	7
Categories	Description	nvarchar	1000	1	8
Categories	Image	nvarchar	1000	1	9
Categories	Icon	nvarchar	100	1	10
Categories	Color	nvarchar	14	1	11
Categories	DisplayOrder	int	4	0	12
Categories	IsActive	bit	1	0	13
Categories	IsFeatured	bit	1	0	14
Categories	ProductCount	int	4	0	15
Categories	Keywords	nvarchar	1000	1	16
Categories	SeoTitle	nvarchar	400	1	17
Categories	SeoDescription	nvarchar	1000	1	18
Categories	SeoKeywords	nvarchar	1000	1	19
Categories	CreatedAt	datetime2	8	0	20
Categories	UpdatedAt	datetime2	8	1	21
ChartOfAccounts	Id	int	4	0	1
ChartOfAccounts	AccountCode	nvarchar	40	0	2
ChartOfAccounts	AccountNameAr	nvarchar	400	0	3
ChartOfAccounts	AccountNameEn	nvarchar	400	0	4
ChartOfAccounts	ParentAccountId	int	4	1	5
ChartOfAccounts	AccountType	nvarchar	100	0	6
ChartOfAccounts	AccountCategory	nvarchar	100	0	7
ChartOfAccounts	Level	int	4	0	8
ChartOfAccounts	IsParent	bit	1	1	9
ChartOfAccounts	IsActive	bit	1	1	10
ChartOfAccounts	CreatedAt	datetime2	8	1	11
ChartOfAccounts	CreatedBy	nvarchar	200	1	12
ChartOfAccounts	UpdatedAt	datetime2	8	1	13
ChartOfAccounts	UpdatedBy	nvarchar	200	1	14
ChartOfAccounts	IsDeleted	bit	1	1	15
ChartOfAccounts	AccountCodeNumeric	nvarchar	40	1	16
Counters	Id	int	4	0	1
Counters	CounterName	nvarchar	100	0	2
Counters	Prefix	nvarchar	20	1	3
Counters	CurrentValue	bigint	8	0	4
Counters	NumberLength	int	4	0	5
Counters	Description	nvarchar	400	1	6
Counters	BranchId	int	4	1	7
Counters	IsActive	bit	1	0	8
Counters	CodeFormat	nvarchar	200	1	9
Counters	CreatedAt	datetime2	8	0	10
Counters	UpdatedAt	datetime2	8	1	11
Countries	Id	int	4	0	1
Countries	NameAr	nvarchar	200	0	2
Countries	NameEn	nvarchar	200	0	3
Countries	Code	nvarchar	20	0	4
Countries	PhoneCode	nvarchar	20	1	5
Countries	IsActive	bit	1	0	6
Countries	CreatedAt	datetime2	8	0	7
Countries	UpdatedAt	datetime2	8	0	8
Countries	IsDeleted	bit	1	1	9
Countries	CreatedBy	nvarchar	200	1	10
Countries	UpdatedBy	nvarchar	200	1	11
Coupons	Id	int	4	0	1
Coupons	CouponCode	nvarchar	40	0	2
Coupons	CouponNameAr	nvarchar	400	0	3
Coupons	CouponNameEn	nvarchar	400	1	4
Coupons	DiscountType	nvarchar	40	0	5
Coupons	DiscountValue	decimal	9	0	6
Coupons	MinimumAmount	decimal	9	1	7
Coupons	MaximumDiscount	decimal	9	1	8
Coupons	StartDate	date	3	0	9
Coupons	EndDate	date	3	0	10
Coupons	UsageLimit	int	4	1	11
Coupons	UsedCount	int	4	1	12
Coupons	IsActive	bit	1	1	13
Coupons	CreatedAt	datetime2	8	1	14
Coupons	CreatedBy	nvarchar	200	0	15
Coupons	UpdatedAt	datetime2	8	1	16
Coupons	UpdatedBy	nvarchar	200	1	17
Coupons	IsDeleted	bit	1	1	18
Customers	Id	int	4	0	1
Customers	CustomerCode	nvarchar	40	0	2
Customers	NameAr	nvarchar	200	0	3
Customers	NameEn	nvarchar	200	1	4
Customers	CustomerTypeId	int	4	0	5
Customers	Phone1	nvarchar	40	1	6
Customers	Phone2	nvarchar	40	1	7
Customers	Email	nvarchar	200	1	8
Customers	Address	nvarchar	1000	1	9
Customers	AreaId	int	4	1	10
Customers	BranchId	int	4	0	11
Customers	PriceCategoryId	int	4	1	12
Customers	DiscountPercentage	decimal	5	0	13
Customers	OpeningBalance	decimal	9	0	14
Customers	CurrentBalance	decimal	9	0	15
Customers	CreditLimit	decimal	9	0	16
Customers	IdentityNumber	nvarchar	100	1	17
Customers	TaxNumber	nvarchar	100	1	18
Customers	IsActive	bit	1	0	19
Customers	CreatedAt	datetime2	8	0	20
Customers	UpdatedAt	datetime2	8	1	21
Customers	ChartAccountId	int	4	1	22
CustomerTypes	Id	int	4	0	1
CustomerTypes	NameAr	nvarchar	100	0	2
CustomerTypes	NameEn	nvarchar	100	1	3
CustomerTypes	Description	nvarchar	400	1	4
CustomerTypes	DefaultDiscountPercentage	decimal	5	0	5
CustomerTypes	DefaultCreditLimit	decimal	9	0	6
CustomerTypes	IsActive	bit	1	0	7
CustomerTypes	Color	nvarchar	14	1	8
CustomerTypes	DisplayOrder	int	4	0	9
CustomerTypes	CreatedAt	datetime2	8	0	10
CustomerTypes	UpdatedAt	datetime2	8	1	11
Departments	Id	int	4	0	1
Departments	NameAr	nvarchar	200	0	2
Departments	NameEn	nvarchar	200	1	3
Departments	Code	nvarchar	40	0	4
Departments	Description	nvarchar	1000	1	5
Departments	ManagerId	int	4	1	6
Departments	BranchId	int	4	0	7
Departments	IsActive	bit	1	0	8
Departments	CreatedBy	int	4	0	9
Departments	CreatedAt	datetime	8	0	10
Departments	LastModifiedBy	int	4	1	11
Departments	LastModifiedAt	datetime	8	1	12
EmployeeDocuments	Id	int	4	0	1
EmployeeDocuments	EmployeeId	int	4	0	2
EmployeeDocuments	DocumentType	nvarchar	100	0	3
EmployeeDocuments	DocumentName	nvarchar	400	0	4
EmployeeDocuments	FileName	nvarchar	1000	1	5
EmployeeDocuments	FilePath	nvarchar	2000	1	6
EmployeeDocuments	ExpiryDate	date	3	1	7
EmployeeDocuments	IsRequired	bit	1	1	8
EmployeeDocuments	UploadedBy	int	4	0	9
EmployeeDocuments	UploadedAt	datetime	8	1	10
EmployeeLeaveBalances	Id	int	4	0	1
EmployeeLeaveBalances	EmployeeId	int	4	0	2
EmployeeLeaveBalances	LeaveTypeId	int	4	0	3
EmployeeLeaveBalances	Year	int	4	0	4
EmployeeLeaveBalances	EntitledDays	int	4	0	5
EmployeeLeaveBalances	UsedDays	int	4	1	6
EmployeeLeaveBalances	CarriedForwardDays	int	4	1	7
EmployeeLeaveBalances	LastUpdated	datetime	8	1	8
EmployeeLeaves	Id	int	4	0	1
EmployeeLeaves	EmployeeId	int	4	0	2
EmployeeLeaves	LeaveTypeId	int	4	0	3
EmployeeLeaves	StartDate	date	3	0	4
EmployeeLeaves	EndDate	date	3	0	5
EmployeeLeaves	TotalDays	int	4	0	6
EmployeeLeaves	Reason	nvarchar	1000	0	7
EmployeeLeaves	LeaveStatus	nvarchar	40	1	8
EmployeeLeaves	RequestDate	datetime	8	1	9
Employees	Id	int	4	0	1
Employees	EmployeeCode	nvarchar	40	0	2
Employees	NameAr	nvarchar	400	0	3
Employees	NameEn	nvarchar	400	1	4
Employees	Position	nvarchar	200	1	5
Employees	Department	nvarchar	200	1	6
Employees	Phone	nvarchar	40	1	7
Employees	Email	nvarchar	200	1	8
Employees	Address	nvarchar	1000	1	9
Employees	HireDate	date	3	1	10
Employees	Salary	decimal	9	1	11
Employees	IsActive	bit	1	1	12
Employees	CreatedAt	datetime2	8	1	13
Employees	CreatedBy	nvarchar	200	0	14
Employees	UpdatedAt	datetime2	8	1	15
Employees	UpdatedBy	nvarchar	200	1	16
Employees	IsDeleted	bit	1	1	17
Employees	DepartmentId	int	4	1	18
Employees	PositionId	int	4	1	19
Employees	BiometricId	nvarchar	40	1	20
Employees	BasicSalary	decimal	9	0	21
Employees	Allowances	decimal	9	0	22
Employees	SocialInsuranceNumber	nvarchar	100	1	23
Employees	TaxNumber	nvarchar	100	1	24
Employees	BankAccountNumber	nvarchar	100	1	25
Employees	EmergencyContactName	nvarchar	200	1	26
Employees	EmergencyContactPhone	nvarchar	40	1	27
Employees	TerminationDate	date	3	1	28
Employees	TerminationReason	nvarchar	1000	1	29
EmployeeShifts	Id	int	4	0	1
EmployeeShifts	EmployeeId	int	4	0	2
EmployeeShifts	ShiftId	int	4	0	3
EmployeeShifts	EffectiveDate	date	3	0	4
EmployeeShifts	EndDate	date	3	1	5
EmployeeShifts	IsTemporary	bit	1	1	6
EmployeeShifts	AssignedBy	int	4	0	7
EmployeeShifts	AssignedAt	datetime	8	1	8
EmployeeShifts	IsActive	bit	1	1	9
FinancialTransactions	Id	bigint	8	0	1
FinancialTransactions	TransactionNumber	nvarchar	40	0	2
FinancialTransactions	TransactionDate	date	3	0	3
FinancialTransactions	AccountId	int	4	0	4
FinancialTransactions	DebitAmount	decimal	9	1	5
FinancialTransactions	CreditAmount	decimal	9	1	6
FinancialTransactions	Balance	decimal	9	1	7
FinancialTransactions	Description	nvarchar	1000	1	8
FinancialTransactions	ReferenceType	nvarchar	100	1	9
FinancialTransactions	ReferenceNumber	nvarchar	100	1	10
FinancialTransactions	ReferenceId	bigint	8	1	11
FinancialTransactions	JournalEntryId	bigint	8	1	12
FinancialTransactions	SupplierId	int	4	1	13
FinancialTransactions	CustomerId	int	4	1	14
FinancialTransactions	BranchId	int	4	1	15
FinancialTransactions	Status	nvarchar	40	1	16
FinancialTransactions	CreatedAt	datetime2	8	1	17
FinancialTransactions	CreatedBy	nvarchar	200	0	18
FinancialTransactions	IsDeleted	bit	1	1	19
JournalEntries	Id	bigint	8	0	1
JournalEntries	JournalNumber	nvarchar	40	0	2
JournalEntries	EntryDate	date	3	0	3
JournalEntries	Description	nvarchar	1000	0	4
JournalEntries	ReferenceNumber	nvarchar	100	1	5
JournalEntries	TotalDebit	decimal	9	0	6
JournalEntries	TotalCredit	decimal	9	0	7
JournalEntries	Status	nvarchar	40	1	8
JournalEntries	EntryType	nvarchar	100	0	9
JournalEntries	SupplierId	int	4	1	10
JournalEntries	CustomerId	int	4	1	11
JournalEntries	CreatedAt	datetime2	8	1	12
JournalEntries	CreatedBy	nvarchar	200	0	13
JournalEntries	PostedAt	datetime2	8	1	14
JournalEntries	PostedBy	nvarchar	200	1	15
JournalEntries	UpdatedAt	datetime2	8	1	16
JournalEntries	UpdatedBy	nvarchar	200	1	17
JournalEntries	IsDeleted	bit	1	1	18
JournalEntries	PreliminaryPostedBy	int	4	1	19
JournalEntries	PreliminaryPostedAt	datetime	8	1	20
JournalEntries	FinalPostedBy	int	4	1	21
JournalEntries	FinalPostedAt	datetime	8	1	22
JournalEntryDetails	Id	bigint	8	0	1
JournalEntryDetails	JournalEntryId	bigint	8	0	2
JournalEntryDetails	AccountId	int	4	0	3
JournalEntryDetails	DebitAmount	decimal	9	1	4
JournalEntryDetails	CreditAmount	decimal	9	1	5
JournalEntryDetails	Description	nvarchar	1000	1	6
JournalEntryDetails	SupplierId	int	4	1	7
JournalEntryDetails	CustomerId	int	4	1	8
JournalEntryDetails	CreatedAt	datetime2	8	1	9
JournalEntryDetails	CreatedBy	nvarchar	200	0	10
JournalEntryDetails	IsDeleted	bit	1	1	11
LeaveTypes	Id	int	4	0	1
LeaveTypes	NameAr	nvarchar	200	0	2
LeaveTypes	MaxDaysPerYear	int	4	0	3
LeaveTypes	IsPaid	bit	1	1	4
LeaveTypes	RequireApproval	bit	1	1	5
LeaveTypes	IsActive	bit	1	1	6
PaymentMethods	Id	int	4	0	1
PaymentMethods	NameAr	nvarchar	100	0	2
PaymentMethods	NameEn	nvarchar	100	1	3
PaymentMethods	Code	nvarchar	20	1	4
PaymentMethods	PaymentType	int	4	0	5
PaymentMethods	Description	nvarchar	400	1	6
PaymentMethods	IsDefault	bit	1	0	7
PaymentMethods	IsActive	bit	1	0	8
PaymentMethods	DisplayOrder	int	4	0	9
PaymentMethods	Icon	nvarchar	100	1	10
PaymentMethods	Color	nvarchar	14	1	11
PaymentMethods	MinAmount	decimal	9	1	12
PaymentMethods	MaxAmount	decimal	9	1	13
PaymentMethods	CommissionPercentage	decimal	5	0	14
PaymentMethods	CommissionAmount	decimal	9	0	15
PaymentMethods	AccountId	int	4	1	16
PaymentMethods	Settings	nvarchar	2000	1	17
PaymentMethods	CreatedAt	datetime2	8	0	18
PaymentMethods	UpdatedAt	datetime2	8	1	19
Payments	Id	bigint	8	0	1
Payments	PaymentNumber	nvarchar	40	0	2
Payments	PaymentDate	date	3	0	3
Payments	SupplierId	int	4	0	4
Payments	Amount	decimal	9	0	5
Payments	PaymentMethod	nvarchar	100	0	6
Payments	BankAccountId	int	4	1	7
Payments	CheckNumber	nvarchar	100	1	8
Payments	CheckDate	date	3	1	9
Payments	Description	nvarchar	1000	1	10
Payments	JournalEntryId	bigint	8	1	11
Payments	Status	nvarchar	40	1	12
Payments	CreatedAt	datetime2	8	1	13
Payments	CreatedBy	nvarchar	200	0	14
Payments	PostedAt	datetime2	8	1	15
Payments	PostedBy	nvarchar	200	1	16
Payments	IsDeleted	bit	1	1	17
PayrollItems	Id	int	4	0	1
PayrollItems	PayrollId	int	4	0	2
PayrollItems	ItemType	nvarchar	40	0	3
PayrollItems	ItemCode	nvarchar	40	0	4
PayrollItems	ItemNameAr	nvarchar	200	0	5
PayrollItems	Amount	decimal	9	0	6
PayrollItems	Quantity	decimal	9	1	7
PayrollItems	Rate	decimal	9	1	8
PayrollItems	Description	nvarchar	400	1	9
Payrolls	Id	int	4	0	1
Payrolls	EmployeeId	int	4	0	2
Payrolls	Year	int	4	0	3
Payrolls	Month	int	4	0	4
Payrolls	BasicSalary	decimal	9	0	5
Payrolls	NetAmount	decimal	9	0	6
Payrolls	PayrollStatus	nvarchar	40	1	7
Payrolls	IsPaid	bit	1	1	8
Payrolls	CreatedAt	datetime	8	1	9
Payslips	Id	int	4	0	1
Payslips	PayrollId	int	4	0	2
Payslips	PayslipNumber	nvarchar	100	0	3
Payslips	GeneratedAt	datetime	8	1	4
Payslips	GeneratedBy	int	4	0	5
Payslips	IsEmailSent	bit	1	1	6
Payslips	IsPrinted	bit	1	1	7
Permissions	Id	int	4	0	1
Permissions	NameAr	nvarchar	200	0	2
Permissions	NameEn	nvarchar	200	1	3
Permissions	Code	nvarchar	200	0	4
Permissions	Description	nvarchar	1000	1	5
Permissions	Group	nvarchar	100	0	6
Permissions	ParentPermissionId	int	4	1	7
Permissions	PermissionType	int	4	0	8
Permissions	IsActive	bit	1	0	9
Permissions	DisplayOrder	int	4	0	10
Permissions	Icon	nvarchar	100	1	11
Permissions	Color	nvarchar	14	1	12
Permissions	Path	nvarchar	400	1	13
Permissions	HttpMethod	nvarchar	20	1	14
Permissions	CreatedAt	datetime2	8	0	15
Permissions	UpdatedAt	datetime2	8	1	16
Positions	Id	int	4	0	1
Positions	NameAr	nvarchar	200	0	2
Positions	NameEn	nvarchar	200	1	3
Positions	Code	nvarchar	40	0	4
Positions	Description	nvarchar	1000	1	5
Positions	DepartmentId	int	4	0	6
Positions	Level	int	4	0	7
Positions	MinSalary	decimal	9	1	8
Positions	MaxSalary	decimal	9	1	9
Positions	IsActive	bit	1	0	10
Positions	CreatedBy	int	4	0	11
Positions	CreatedAt	datetime	8	0	12
PriceCategories	Id	int	4	0	1
PriceCategories	NameAr	nvarchar	100	0	2
PriceCategories	NameEn	nvarchar	100	1	3
PriceCategories	Code	nvarchar	20	1	4
PriceCategories	Description	nvarchar	400	1	5
PriceCategories	PriceAdjustmentPercentage	decimal	5	0	6
PriceCategories	IsDefault	bit	1	0	7
PriceCategories	IsActive	bit	1	0	8
PriceCategories	DisplayOrder	int	4	0	9
PriceCategories	Color	nvarchar	14	1	10
PriceCategories	MinimumQuantity	decimal	9	1	11
PriceCategories	MaximumQuantity	decimal	9	1	12
PriceCategories	CreatedAt	datetime2	8	0	13
PriceCategories	UpdatedAt	datetime2	8	1	14
ProductAlternativeCodes	Id	int	4	0	1
ProductAlternativeCodes	ProductId	int	4	0	2
ProductAlternativeCodes	AlternativeCode	nvarchar	100	0	3
ProductAlternativeCodes	CodeType	nvarchar	40	1	4
ProductAlternativeCodes	Description	nvarchar	400	1	5
ProductAlternativeCodes	CreatedAt	datetime2	8	0	6
ProductBatches	Id	int	4	0	1
ProductBatches	ProductId	int	4	0	2
ProductBatches	BranchId	int	4	0	3
ProductBatches	BatchNumber	nvarchar	100	0	4
ProductBatches	Quantity	decimal	9	0	5
ProductBatches	RemainingQuantity	decimal	9	0	6
ProductBatches	CostPrice	decimal	9	0	7
ProductBatches	CreatedAt	datetime	8	1	8
ProductBranchPrices	Id	int	4	0	1
ProductBranchPrices	ProductId	int	4	0	2
ProductBranchPrices	BranchId	int	4	0	3
ProductBranchPrices	PriceCategoryId	int	4	0	4
ProductBranchPrices	Price	decimal	9	0	5
ProductBranchPrices	IsActive	bit	1	0	6
ProductBranchPrices	CreatedAt	datetime2	8	0	7
ProductBranchPrices	UpdatedAt	datetime2	8	1	8
ProductImages	Id	int	4	0	1
ProductImages	ProductId	int	4	0	2
ProductImages	ImagePath	nvarchar	1000	0	3
ProductImages	ImageName	nvarchar	400	1	4
ProductImages	IsMain	bit	1	0	5
ProductImages	DisplayOrder	int	4	0	6
ProductImages	CreatedAt	datetime2	8	0	7
Products	Id	int	4	0	1
Products	ProductCode	nvarchar	40	0	2
Products	NameAr	nvarchar	400	0	3
Products	NameEn	nvarchar	400	1	4
Products	Description	nvarchar	2000	1	5
Products	CategoryId	int	4	0	6
Products	UnitId	int	4	0	7
Products	Barcode	nvarchar	100	1	8
Products	CostPrice	decimal	9	0	9
Products	BasePrice	decimal	9	0	10
Products	ProfitMargin	decimal	5	0	11
Products	MinimumStock	decimal	9	1	12
Products	MaximumStock	decimal	9	1	13
Products	ReorderPoint	decimal	9	1	14
Products	IsActive	bit	1	0	15
Products	Weight	decimal	9	1	16
Products	Length	decimal	9	1	17
Products	Width	decimal	9	1	18
Products	Height	decimal	9	1	19
Products	CreatedAt	datetime2	8	0	20
Products	UpdatedAt	datetime2	8	1	21
ProductStocks	Id	int	4	0	1
ProductStocks	ProductId	int	4	0	2
ProductStocks	BranchId	int	4	0	3
ProductStocks	AvailableQuantity	decimal	9	0	4
ProductStocks	ReservedQuantity	decimal	9	0	5
ProductStocks	OnOrderQuantity	decimal	9	0	6
ProductStocks	OpeningQuantity	decimal	9	0	7
ProductStocks	TotalInQuantity	decimal	9	0	8
ProductStocks	TotalOutQuantity	decimal	9	0	9
ProductStocks	AverageCostPrice	decimal	9	0	10
ProductStocks	LastCostPrice	decimal	9	0	11
ProductStocks	StockValue	decimal	9	0	12
ProductStocks	BranchMinStock	decimal	9	1	13
ProductStocks	BranchMaxStock	decimal	9	1	14
ProductStocks	BranchReorderPoint	decimal	9	1	15
ProductStocks	StorageLocation	nvarchar	200	1	16
ProductStocks	ShelfNumber	nvarchar	40	1	17
ProductStocks	StockNotes	nvarchar	1000	1	18
ProductStocks	IsAvailableForSale	bit	1	0	19
ProductStocks	CreatedAt	datetime2	8	0	20
ProductStocks	UpdatedAt	datetime2	8	1	21
ProductSuppliers	Id	bigint	8	0	1
ProductSuppliers	ProductId	int	4	0	2
ProductSuppliers	SupplierId	int	4	0	3
ProductSuppliers	SupplierProductCode	nvarchar	100	1	4
ProductSuppliers	SupplierProductName	nvarchar	400	1	5
ProductSuppliers	PurchasePrice	decimal	9	0	6
ProductSuppliers	MinOrderQuantity	decimal	9	1	7
ProductSuppliers	LeadTimeDays	int	4	1	8
ProductSuppliers	IsPreferred	bit	1	1	9
ProductSuppliers	IsActive	bit	1	1	10
ProductSuppliers	CreatedAt	datetime2	8	1	11
ProductSuppliers	CreatedBy	nvarchar	200	0	12
ProductSuppliers	UpdatedAt	datetime2	8	1	13
ProductSuppliers	UpdatedBy	nvarchar	200	1	14
ProductSuppliers	IsDeleted	bit	1	1	15
Promotions	Id	int	4	0	1
Promotions	PromotionCode	nvarchar	40	0	2
Promotions	PromotionNameAr	nvarchar	400	0	3
Promotions	PromotionNameEn	nvarchar	400	1	4
Promotions	PromotionType	nvarchar	100	0	5
Promotions	BuyQuantity	int	4	1	6
Promotions	GetQuantity	int	4	1	7
Promotions	DiscountPercentage	decimal	5	1	8
Promotions	DiscountAmount	decimal	9	1	9
Promotions	StartDate	date	3	0	10
Promotions	EndDate	date	3	0	11
Promotions	IsActive	bit	1	1	12
Promotions	CreatedAt	datetime2	8	1	13
Promotions	CreatedBy	nvarchar	200	0	14
Promotions	UpdatedAt	datetime2	8	1	15
Promotions	UpdatedBy	nvarchar	200	1	16
Promotions	IsDeleted	bit	1	1	17
PurchaseInvoiceDetails	Id	bigint	8	0	1
PurchaseInvoiceDetails	PurchaseInvoiceId	bigint	8	0	2
PurchaseInvoiceDetails	ProductId	int	4	0	3
PurchaseInvoiceDetails	Quantity	decimal	9	0	4
PurchaseInvoiceDetails	UnitPrice	decimal	9	0	5
PurchaseInvoiceDetails	DiscountAmount	decimal	9	1	6
PurchaseInvoiceDetails	TotalPrice	decimal	9	0	7
PurchaseInvoiceDetails	CreatedAt	datetime2	8	1	8
PurchaseInvoiceDetails	CreatedBy	nvarchar	200	0	9
PurchaseInvoiceDetails	IsDeleted	bit	1	1	10
PurchaseInvoices	Id	bigint	8	0	1
PurchaseInvoices	InvoiceNumber	nvarchar	40	0	2
PurchaseInvoices	InvoiceDate	date	3	0	3
PurchaseInvoices	SupplierId	int	4	0	4
PurchaseInvoices	EmployeeId	int	4	1	5
PurchaseInvoices	SubTotal	decimal	9	0	6
PurchaseInvoices	DiscountAmount	decimal	9	1	7
PurchaseInvoices	DiscountPercentage	decimal	5	1	8
PurchaseInvoices	TaxAmount	decimal	9	1	9
PurchaseInvoices	TotalAmount	decimal	9	0	10
PurchaseInvoices	PaidAmount	decimal	9	1	11
PurchaseInvoices	RemainingAmount	decimal	9	1	12
PurchaseInvoices	PaymentStatus	nvarchar	40	1	13
PurchaseInvoices	Notes	nvarchar	1000	1	14
PurchaseInvoices	JournalEntryId	bigint	8	1	15
PurchaseInvoices	Status	nvarchar	40	1	16
PurchaseInvoices	CreatedAt	datetime2	8	1	17
PurchaseInvoices	CreatedBy	nvarchar	200	0	18
PurchaseInvoices	UpdatedAt	datetime2	8	1	19
PurchaseInvoices	UpdatedBy	nvarchar	200	1	20
PurchaseInvoices	IsDeleted	bit	1	1	21
Receipts	Id	bigint	8	0	1
Receipts	ReceiptNumber	nvarchar	40	0	2
Receipts	ReceiptDate	date	3	0	3
Receipts	CustomerId	int	4	1	4
Receipts	Amount	decimal	9	0	5
Receipts	PaymentMethod	nvarchar	100	0	6
Receipts	BankAccountId	int	4	1	7
Receipts	CheckNumber	nvarchar	100	1	8
Receipts	CheckDate	date	3	1	9
Receipts	Description	nvarchar	1000	1	10
Receipts	JournalEntryId	bigint	8	1	11
Receipts	Status	nvarchar	40	1	12
Receipts	CreatedAt	datetime2	8	1	13
Receipts	CreatedBy	nvarchar	200	0	14
Receipts	PostedAt	datetime2	8	1	15
Receipts	PostedBy	nvarchar	200	1	16
Receipts	IsDeleted	bit	1	1	17
RolePermissions	Id	int	4	0	1
RolePermissions	RoleId	int	4	0	2
RolePermissions	PermissionId	int	4	0	3
RolePermissions	IsGranted	bit	1	0	4
RolePermissions	GrantedById	int	4	1	5
RolePermissions	GrantedAt	datetime2	8	0	6
RolePermissions	Constraints	nvarchar	2000	1	7
RolePermissions	Notes	nvarchar	1000	1	8
Roles	Id	int	4	0	1
Roles	NameAr	nvarchar	100	0	2
Roles	NameEn	nvarchar	100	1	3
Roles	Code	nvarchar	100	0	4
Roles	Description	nvarchar	1000	1	5
Roles	Level	int	4	0	6
Roles	IsActive	bit	1	0	7
Roles	IsSystemRole	bit	1	0	8
Roles	Color	nvarchar	14	1	9
Roles	Icon	nvarchar	100	1	10
Roles	CreatedAt	datetime2	8	0	11
Roles	UpdatedAt	datetime2	8	1	12
SaleItems	Id	int	4	0	1
SaleItems	SaleId	int	4	0	2
SaleItems	ProductId	int	4	0	3
SaleItems	LineNumber	int	4	0	4
SaleItems	Quantity	decimal	9	0	5
SaleItems	UnitPrice	decimal	9	0	6
SaleItems	UnitCostPrice	decimal	9	0	7
SaleItems	DiscountPercentage	decimal	5	0	8
SaleItems	DiscountAmount	decimal	9	0	9
SaleItems	NetUnitPrice	decimal	9	0	10
SaleItems	LineTotal	decimal	9	0	11
SaleItems	NetLineTotal	decimal	9	0	12
SaleItems	TaxPercentage	decimal	5	0	13
SaleItems	TaxAmount	decimal	9	0	14
SaleItems	FinalTotal	decimal	9	0	15
SaleItems	PriceCategoryId	int	4	1	16
SaleItems	ItemNotes	nvarchar	1000	1	17
SaleItems	BatchNumber	nvarchar	100	1	18
SaleItems	SerialNumber	nvarchar	200	1	19
SaleItems	ActualWeight	decimal	9	1	20
SaleItems	ReturnedQuantity	decimal	9	0	21
SaleItems	DiscountReason	nvarchar	400	1	22
SaleItems	CreatedAt	datetime2	8	0	23
SalePayments	Id	int	4	0	1
SalePayments	SaleId	int	4	0	2
SalePayments	PaymentNumber	nvarchar	40	0	3
SalePayments	PaymentMethodId	int	4	0	4
SalePayments	Amount	decimal	9	0	5
SalePayments	ReceivedAmount	decimal	9	0	6
SalePayments	ChangeAmount	decimal	9	0	7
SalePayments	PaymentDate	datetime2	8	0	8
SalePayments	Status	int	4	0	9
SalePayments	ReferenceNumber	nvarchar	200	1	10
SalePayments	BankName	nvarchar	200	1	11
SalePayments	AccountNumber	nvarchar	100	1	12
SalePayments	Notes	nvarchar	1000	1	13
SalePayments	UserId	int	4	0	14
SalePayments	ConfirmedById	int	4	1	15
SalePayments	ConfirmedAt	datetime2	8	1	16
SalePayments	ReceiptNumber	nvarchar	40	1	17
SalePayments	ExternalTransactionId	nvarchar	200	1	18
SalePayments	TransactionFee	decimal	9	0	19
SalePayments	Currency	nvarchar	6	0	20
SalePayments	ExchangeRate	decimal	9	0	21
SalePayments	CashBoxId	int	4	1	22
SalePayments	CreatedAt	datetime2	8	0	23
Sales	Id	int	4	0	1
Sales	InvoiceNumber	nvarchar	40	0	2
Sales	CustomerId	int	4	1	3
Sales	CustomerName	nvarchar	200	1	4
Sales	BranchId	int	4	0	5
Sales	UserId	int	4	0	6
Sales	InvoiceDate	datetime2	8	0	7
Sales	DueDate	datetime2	8	1	8
Sales	Status	int	4	0	9
Sales	SaleType	int	4	0	10
Sales	SubTotal	decimal	9	0	11
Sales	DiscountPercentage	decimal	5	0	12
Sales	DiscountAmount	decimal	9	0	13
Sales	TaxPercentage	decimal	5	0	14
Sales	TaxAmount	decimal	9	0	15
Sales	TotalAmount	decimal	9	0	16
Sales	PaidAmount	decimal	9	0	17
Sales	RemainingAmount	decimal	9	0	18
Sales	Notes	nvarchar	2000	1	19
Sales	InternalNotes	nvarchar	2000	1	20
Sales	ExternalReference	nvarchar	100	1	21
Sales	CancelledById	int	4	1	22
Sales	CancelledAt	datetime2	8	1	23
Sales	CancellationReason	nvarchar	1000	1	24
Sales	TableNumber	nvarchar	40	1	25
Sales	CreatedAt	datetime2	8	0	26
Sales	UpdatedAt	datetime2	8	1	27
SalesInvoiceDetails	Id	bigint	8	0	1
SalesInvoiceDetails	SalesInvoiceId	bigint	8	0	2
SalesInvoiceDetails	ProductId	int	4	0	3
SalesInvoiceDetails	Quantity	decimal	9	0	4
SalesInvoiceDetails	UnitPrice	decimal	9	0	5
SalesInvoiceDetails	DiscountAmount	decimal	9	1	6
SalesInvoiceDetails	TotalPrice	decimal	9	0	7
SalesInvoiceDetails	CreatedAt	datetime2	8	1	8
SalesInvoiceDetails	CreatedBy	nvarchar	200	0	9
SalesInvoiceDetails	IsDeleted	bit	1	1	10
SalesInvoices	Id	bigint	8	0	1
SalesInvoices	InvoiceNumber	nvarchar	40	0	2
SalesInvoices	InvoiceDate	date	3	0	3
SalesInvoices	CustomerId	int	4	0	4
SalesInvoices	EmployeeId	int	4	1	5
SalesInvoices	SubTotal	decimal	9	0	6
SalesInvoices	DiscountAmount	decimal	9	1	7
SalesInvoices	DiscountPercentage	decimal	5	1	8
SalesInvoices	TaxAmount	decimal	9	1	9
SalesInvoices	TotalAmount	decimal	9	0	10
SalesInvoices	PaidAmount	decimal	9	1	11
SalesInvoices	RemainingAmount	decimal	9	1	12
SalesInvoices	PaymentStatus	nvarchar	40	1	13
SalesInvoices	Notes	nvarchar	1000	1	14
SalesInvoices	JournalEntryId	bigint	8	1	15
SalesInvoices	Status	nvarchar	40	1	16
SalesInvoices	CreatedAt	datetime2	8	1	17
SalesInvoices	CreatedBy	nvarchar	200	0	18
SalesInvoices	UpdatedAt	datetime2	8	1	19
SalesInvoices	UpdatedBy	nvarchar	200	1	20
SalesInvoices	IsDeleted	bit	1	1	21
ShiftBreaks	Id	int	4	0	1
ShiftBreaks	ShiftId	int	4	0	2
ShiftBreaks	BreakName	nvarchar	100	0	3
ShiftBreaks	StartTime	time	5	0	4
ShiftBreaks	EndTime	time	5	0	5
ShiftBreaks	DurationMinutes	int	4	0	6
ShiftBreaks	IsPaid	bit	1	1	7
ShiftBreaks	IsOptional	bit	1	1	8
Shifts	Id	int	4	0	1
Shifts	ShiftName	nvarchar	200	0	2
Shifts	ShiftCode	nvarchar	40	0	3
Shifts	StartTime	time	5	0	4
Shifts	EndTime	time	5	0	5
Shifts	IsOvernightShift	bit	1	1	6
Shifts	WorkingHours	decimal	5	0	7
Shifts	BranchId	int	4	0	8
Shifts	IsActive	bit	1	1	9
StockMovements	Id	int	4	0	1
StockMovements	MovementNumber	nvarchar	40	0	2
StockMovements	ProductId	int	4	0	3
StockMovements	BranchId	int	4	0	4
StockMovements	MovementType	int	4	0	5
StockMovements	Quantity	decimal	9	0	6
StockMovements	UnitPrice	decimal	9	0	7
StockMovements	TotalValue	decimal	9	0	8
StockMovements	BalanceBefore	decimal	9	0	9
StockMovements	BalanceAfter	decimal	9	0	10
StockMovements	MovementDate	datetime2	8	0	11
StockMovements	ReferenceType	nvarchar	100	1	12
StockMovements	ReferenceId	int	4	1	13
StockMovements	Reference	nvarchar	100	1	14
StockMovements	Description	nvarchar	1000	1	15
StockMovements	BatchNumber	nvarchar	100	1	16
StockMovements	FromBranchId	int	4	1	17
StockMovements	ToBranchId	int	4	1	18
StockMovements	UserId	int	4	1	19
StockMovements	ConfirmedById	int	4	1	20
StockMovements	ConfirmedAt	datetime2	8	1	21
StockMovements	CreatedAt	datetime2	8	0	22
Suppliers	Id	int	4	0	1
Suppliers	SupplierCode	nvarchar	40	0	2
Suppliers	NameAr	nvarchar	200	0	3
Suppliers	NameEn	nvarchar	200	1	4
Suppliers	SupplierTypeId	int	4	0	5
Suppliers	Phone1	nvarchar	40	1	6
Suppliers	Phone2	nvarchar	40	1	7
Suppliers	Email	nvarchar	200	1	8
Suppliers	Website	nvarchar	400	1	9
Suppliers	Address	nvarchar	1000	1	10
Suppliers	AreaId	int	4	1	11
Suppliers	ContactPersonName	nvarchar	200	1	12
Suppliers	ContactPersonPhone	nvarchar	40	1	13
Suppliers	ContactPersonEmail	nvarchar	200	1	14
Suppliers	PaymentTerms	int	4	0	15
Suppliers	CreditLimit	decimal	9	0	16
Suppliers	OpeningBalance	decimal	9	0	17
Suppliers	CurrentBalance	decimal	9	0	18
Suppliers	TaxNumber	nvarchar	100	1	19
Suppliers	CommercialRegister	nvarchar	100	1	20
Suppliers	BankName	nvarchar	200	1	21
Suppliers	BankAccountNumber	nvarchar	100	1	22
Suppliers	IBAN	nvarchar	100	1	23
Suppliers	IsActive	bit	1	0	24
Suppliers	Rating	int	4	1	25
Suppliers	Notes	nvarchar	2000	1	26
Suppliers	CreatedAt	datetime2	8	0	27
Suppliers	UpdatedAt	datetime2	8	1	28
Suppliers	DeliveryDays	int	4	1	29
Suppliers	CreatedBy	nvarchar	200	1	30
Suppliers	UpdatedBy	nvarchar	200	1	31
Suppliers	IsDeleted	bit	1	1	32
Suppliers	CountryId	int	4	1	33
Suppliers	ChartAccountId	int	4	1	34
SupplierTypes	Id	int	4	0	1
SupplierTypes	NameAr	nvarchar	100	0	2
SupplierTypes	NameEn	nvarchar	100	1	3
SupplierTypes	Description	nvarchar	400	1	4
SupplierTypes	DefaultPaymentTerms	int	4	0	5
SupplierTypes	IsActive	bit	1	0	6
SupplierTypes	Color	nvarchar	14	1	7
SupplierTypes	DisplayOrder	int	4	0	8
SupplierTypes	CreatedAt	datetime2	8	0	9
SupplierTypes	UpdatedAt	datetime2	8	1	10
SupplierTypes	IsDeleted	bit	1	1	11
Transactions	TransactionId	bigint	8	0	1
Transactions	TransactionDate	date	3	0	2
Transactions	VoucherId	nvarchar	40	1	3
Transactions	Type	int	4	0	4
Transactions	LineNumber	int	4	0	5
Transactions	MainAccountId	int	4	0	6
Transactions	SubAccountId	int	4	1	7
Transactions	Credit	decimal	9	1	8
Transactions	Debit	decimal	9	1	9
Transactions	BranchId	int	4	1	10
Transactions	YearId	int	4	1	11
Transactions	Status	nvarchar	40	1	12
Transactions	CollectionName	nvarchar	200	1	13
Transactions	Notes	nvarchar	1000	1	14
Transactions	AddUser	nvarchar	200	1	15
Transactions	UpdateUser	nvarchar	200	1	16
Transactions	DeleteUser	nvarchar	200	1	17
Transactions	AddDate	datetime2	8	1	18
Transactions	UpdateDate	datetime2	8	1	19
Transactions	DeleteDate	datetime2	8	1	20
Transactions	IsDeleted	bit	1	1	21
Transactions	ReferenceType	nvarchar	100	1	22
Transactions	ReferenceId	bigint	8	1	23
Transactions	CustomerId	int	4	1	24
Transactions	SupplierId	int	4	1	25
TransactionTypes	TransactionTypeId	int	4	0	1
TransactionTypes	NameAr	nvarchar	200	0	2
TransactionTypes	NameEn	nvarchar	200	1	3
TransactionTypes	IsActive	bit	1	1	4
TransactionTypes	CreatedAt	datetime2	8	1	5
TransactionTypes	CreatedBy	nvarchar	200	1	6
Units	Id	int	4	0	1
Units	NameAr	nvarchar	100	0	2
Units	NameEn	nvarchar	100	1	3
Units	Symbol	nvarchar	20	0	4
Units	UnitType	int	4	0	5
Units	BaseUnitId	int	4	1	6
Units	ConversionFactor	decimal	9	0	7
Units	Description	nvarchar	400	1	8
Units	IsActive	bit	1	0	9
Units	IsDefault	bit	1	0	10
Units	DisplayOrder	int	4	0	11
Units	DecimalPlaces	int	4	0	12
Units	CreatedAt	datetime2	8	0	13
Units	UpdatedAt	datetime2	8	1	14
UserBranches	Id	int	4	0	1
UserBranches	UserId	int	4	0	2
UserBranches	BranchId	int	4	0	3
UserBranches	HasAccess	bit	1	0	4
UserBranches	GrantedById	int	4	1	5
UserBranches	GrantedAt	datetime2	8	0	6
UserBranches	Notes	nvarchar	1000	1	7
UserBranches	IsDefault	bit	1	1	8
UserBranches	CanView	bit	1	1	9
UserBranches	CanCreate	bit	1	1	10
UserBranches	CanEdit	bit	1	1	11
UserBranches	CanDelete	bit	1	1	12
UserBranches	CanApprove	bit	1	1	13
UserBranches	CanViewReports	bit	1	1	14
UserBranches	CanManageInventory	bit	1	1	15
UserBranches	CanManageFinance	bit	1	1	16
UserBranches	CanManageHR	bit	1	1	17
UserRoles	Id	int	4	0	1
UserRoles	UserId	int	4	0	2
UserRoles	RoleId	int	4	0	3
UserRoles	IsActive	bit	1	0	4
UserRoles	GrantedById	int	4	1	5
UserRoles	GrantedAt	datetime2	8	0	6
UserRoles	ExpiryDate	datetime2	8	1	7
UserRoles	Notes	nvarchar	1000	1	8
Users	Id	int	4	0	1
Users	Username	nvarchar	100	0	2
Users	Email	nvarchar	200	0	3
Users	PasswordHash	nvarchar	510	0	4
Users	FullName	nvarchar	200	0	5
Users	PhoneNumber	nvarchar	40	1	6
Users	ProfileImage	nvarchar	1000	1	7
Users	DefaultBranchId	int	4	1	8
Users	EmployeeId	int	4	1	9
Users	IsActive	bit	1	0	10
Users	IsEmailConfirmed	bit	1	0	11
Users	EmailConfirmationToken	nvarchar	510	1	12
Users	PasswordResetToken	nvarchar	510	1	13
Users	PasswordResetExpiry	datetime2	8	1	14
Users	LastLoginAt	datetime2	8	1	15
Users	LastLoginIP	nvarchar	90	1	16
Users	LoginAttempts	int	4	0	17
Users	LockedUntil	datetime2	8	1	18
Users	PreferredLanguage	nvarchar	10	0	19
Users	TimeZone	nvarchar	100	0	20
Users	Settings	nvarchar	4000	1	21
Users	CreatedAt	datetime2	8	0	22
Users	UpdatedAt	datetime2	8	1	23
UserSessions	Id	int	4	0	1
UserSessions	UserId	int	4	0	2
UserSessions	SessionToken	nvarchar	1000	0	3
UserSessions	CurrentBranchId	int	4	0	4
UserSessions	LoginTime	datetime	8	1	5
UserSessions	LastActivity	datetime	8	1	6
UserSessions	IsActive	bit	1	1	7
Warehouses	Id	int	4	0	1
Warehouses	WarehouseCode	nvarchar	40	0	2
Warehouses	WarehouseNameAr	nvarchar	400	0	3
Warehouses	WarehouseNameEn	nvarchar	400	1	4
Warehouses	BranchId	int	4	0	5
Warehouses	Address	nvarchar	1000	1	6
Warehouses	Capacity	decimal	9	1	7
Warehouses	WarehouseKeeperId	int	4	1	8
Warehouses	IsActive	bit	1	1	9
Warehouses	CreatedAt	datetime2	8	1	10
Warehouses	CreatedBy	nvarchar	200	0	11
Warehouses	UpdatedAt	datetime2	8	1	12
Warehouses	UpdatedBy	nvarchar	200	1	13
Warehouses	IsDeleted	bit	1	1	14
WorkRegulations	Id	int	4	0	1
WorkRegulations	RegulationType	nvarchar	100	0	2
WorkRegulations	RegulationCode	nvarchar	40	0	3
WorkRegulations	TitleAr	nvarchar	400	0	4
WorkRegulations	DescriptionAr	nvarchar	-1	0	5
WorkRegulations	NumericValue	decimal	9	1	6
WorkRegulations	BranchId	int	4	1	7
WorkRegulations	EffectiveDate	date	3	0	8
WorkRegulations	IsActive	bit	1	1	9
WorkRegulations	CreatedBy	int	4	0	10
WorkRegulations	CreatedAt	datetime	8	1	11