using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// المناصب الوظيفية
    /// </summary>
    public class Position : BaseEntity
    {
        /// <summary>
        /// اسم المنصب بالعربية
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المنصب بالإنجليزية
        /// </summary>
        [MaxLength(100)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود المنصب
        /// </summary>
        [MaxLength(20)]
        public string? Code { get; set; }

        /// <summary>
        /// القسم التابع له المنصب
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// وصف المنصب
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// المسؤوليات
        /// </summary>
        [MaxLength(2000)]
        public string? Responsibilities { get; set; }

        /// <summary>
        /// المؤهلات المطلوبة
        /// </summary>
        [MaxLength(1000)]
        public string? RequiredQualifications { get; set; }

        /// <summary>
        /// سنوات الخبرة المطلوبة
        /// </summary>
        public int? RequiredExperienceYears { get; set; }

        /// <summary>
        /// الراتب الأدنى
        /// </summary>
        public decimal? MinSalary { get; set; }

        /// <summary>
        /// الراتب الأقصى
        /// </summary>
        public decimal? MaxSalary { get; set; }

        /// <summary>
        /// مستوى المنصب (1-10)
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// هل المنصب إداري
        /// </summary>
        public bool IsManagerial { get; set; } = false;

        /// <summary>
        /// هل المنصب نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// لون المنصب (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة المنصب
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// عدد الموظفين المستهدف في هذا المنصب
        /// </summary>
        public int? TargetEmployeeCount { get; set; }

        /// <summary>
        /// عدد الموظفين الحالي في المنصب
        /// </summary>
        public int CurrentEmployeeCount { get; set; } = 0;

        /// <summary>
        /// نوع الدوام
        /// </summary>
        public WorkType WorkType { get; set; } = WorkType.FullTime;

        /// <summary>
        /// ساعات العمل الأسبوعية
        /// </summary>
        public int? WeeklyWorkHours { get; set; }

        /// <summary>
        /// هل يتطلب سفر
        /// </summary>
        public bool RequiresTravel { get; set; } = false;

        /// <summary>
        /// هل يتطلب رخصة قيادة
        /// </summary>
        public bool RequiresDrivingLicense { get; set; } = false;

        /// <summary>
        /// المهارات المطلوبة
        /// </summary>
        [MaxLength(1000)]
        public string? RequiredSkills { get; set; }

        // Navigation Properties
        public virtual Department Department { get; set; } = null!;
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    /// <summary>
    /// نوع الدوام
    /// </summary>
    public enum WorkType
    {
        /// <summary>
        /// دوام كامل
        /// </summary>
        FullTime = 1,

        /// <summary>
        /// دوام جزئي
        /// </summary>
        PartTime = 2,

        /// <summary>
        /// دوام مرن
        /// </summary>
        Flexible = 3,

        /// <summary>
        /// عمل عن بعد
        /// </summary>
        Remote = 4,

        /// <summary>
        /// مختلط
        /// </summary>
        Hybrid = 5
    }
}
