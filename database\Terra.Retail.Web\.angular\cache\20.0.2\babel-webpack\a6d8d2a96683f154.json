{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/menu\";\nimport * as i11 from \"@angular/material/chips\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/tooltip\";\nfunction Pos_mat_chip_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip\", 39);\n    i0.ɵɵlistener(\"click\", function Pos_mat_chip_55_Template_mat_chip_click_0_listener() {\n      const category_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectCategory(category_r3.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r3.selectedCategory === category_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r3.nameAr, \" \");\n  }\n}\nfunction Pos_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function Pos_div_57_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.addToCart(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 41)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"inventory\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 42)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 44);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 45);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵclassProp(\"out-of-stock\", product_r6.stock <= 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(product_r6.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.productCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(11, 8, product_r6.price, \"EGP\", \"symbol\", \"1.0-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"low-stock\", product_r6.stock <= 10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646: \", product_r6.stock, \" \");\n  }\n}\nfunction Pos_mat_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", customer_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", customer_r7.fullName, \" \");\n  }\n}\nfunction Pos_div_76_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"div\", 52)(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function Pos_div_76_div_1_Template_button_click_8_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.decreaseQuantity(i_r9));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"remove\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"span\", 54);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function Pos_div_76_div_1_Template_button_click_13_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.increaseQuantity(i_r9));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"add\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 56);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function Pos_div_76_div_1_Template_button_click_19_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.removeFromCart(i_r9));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r10.product.nameAr);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.product.productCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", item_r10.quantity <= 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r10.quantity);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(18, 5, ctx_r3.getItemTotal(item_r10), \"EGP\", \"symbol\", \"1.0-2\"));\n  }\n}\nfunction Pos_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, Pos_div_76_div_1_Template, 22, 10, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.cartItems);\n  }\n}\nfunction Pos_ng_template_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0639\\u0646\\u0627\\u0635\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0648\\u0623\\u0636\\u0641\\u0647\\u0627 \\u0644\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Pos_div_79_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\");\n    i0.ɵɵtext(2, \"\\u0627\\u0644\\u062E\\u0635\\u0645:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"-\", i0.ɵɵpipeBind4(5, 1, ctx_r3.discount, \"EGP\", \"symbol\", \"1.0-2\"));\n  }\n}\nfunction Pos_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"span\");\n    i0.ɵɵtext(3, \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 60)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, Pos_div_79_div_13_Template, 6, 6, \"div\", 61);\n    i0.ɵɵelementStart(14, \"div\", 62)(15, \"span\");\n    i0.ɵɵtext(16, \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(6, 5, ctx_r3.getSubtotal(), \"EGP\", \"symbol\", \"1.0-2\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629 (\", ctx_r3.taxRate, \"%):\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(12, 10, ctx_r3.getTax(), \"EGP\", \"symbol\", \"1.0-2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.discount > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(19, 15, ctx_r3.getTotal(), \"EGP\", \"symbol\", \"1.0-2\"));\n  }\n}\nfunction Pos_div_80_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function Pos_div_80_button_2_Template_button_click_0_listener() {\n      const method_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.selectPaymentMethod(method_r13.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const method_r13 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"color\", ctx_r3.selectedPaymentMethod === method_r13.id ? \"primary\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r13.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", method_r13.nameAr, \" \");\n  }\n}\nfunction Pos_div_80_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0627\\u0644\\u0628\\u0627\\u0642\\u064A: \", i0.ɵɵpipeBind4(3, 1, ctx_r3.getChange(), \"EGP\", \"symbol\", \"1.0-2\"));\n  }\n}\nfunction Pos_div_80_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"mat-form-field\", 73)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 74);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function Pos_div_80_div_3_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.paidAmount, $event) || (ctx_r3.paidAmount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function Pos_div_80_div_3_Template_input_input_4_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.calculateChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 75);\n    i0.ɵɵtext(6, \"\\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, Pos_div_80_div_3_div_7_Template, 4, 6, \"div\", 76);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.paidAmount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getChange() !== 0);\n  }\n}\nfunction Pos_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtemplate(2, Pos_div_80_button_2_Template, 4, 3, \"button\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Pos_div_80_div_3_Template, 8, 2, \"div\", 67);\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function Pos_div_80_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.completeSale());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u0625\\u062A\\u0645\\u0627\\u0645 \\u0627\\u0644\\u0628\\u064A\\u0639 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function Pos_div_80_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearCart());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" \\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.paymentMethods);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedPaymentMethod === \"cash\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canCompleteSale());\n  }\n}\nfunction Pos_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵelement(1, \"mat-spinner\", 79);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u0645\\u0639\\u0627\\u0644\\u062C\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u064A\\u0629...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let Pos = /*#__PURE__*/(() => {\n  class Pos {\n    http;\n    snackBar;\n    currentUser = null;\n    searchTerm = '';\n    selectedCategory = null;\n    selectedCustomer = null;\n    selectedPaymentMethod = 'cash';\n    paidAmount = 0;\n    discount = 0;\n    taxRate = 14; // 14% VAT in Egypt\n    isProcessing = false;\n    products = [];\n    filteredProducts = [];\n    categories = [];\n    customers = [];\n    cartItems = [];\n    paymentMethods = [{\n      id: 'cash',\n      nameAr: 'نقدي',\n      icon: 'money'\n    }, {\n      id: 'card',\n      nameAr: 'بطاقة',\n      icon: 'credit_card'\n    }, {\n      id: 'transfer',\n      nameAr: 'تحويل',\n      icon: 'account_balance'\n    }];\n    constructor(http, snackBar) {\n      this.http = http;\n      this.snackBar = snackBar;\n    }\n    ngOnInit() {\n      this.loadCurrentUser();\n      this.loadProducts();\n      this.loadCategories();\n      this.loadCustomers();\n    }\n    loadCurrentUser() {\n      const userData = localStorage.getItem('currentUser');\n      if (userData) {\n        this.currentUser = JSON.parse(userData);\n      }\n    }\n    loadProducts() {\n      this.http.get('http://localhost:5000/api/simple/products').subscribe({\n        next: response => {\n          this.products = response.products || [];\n          this.filteredProducts = [...this.products];\n        },\n        error: error => {\n          console.error('Error loading products:', error);\n          this.showMessage('خطأ في تحميل المنتجات', true);\n        }\n      });\n    }\n    loadCategories() {\n      this.http.get('http://localhost:5000/api/simple/categories').subscribe({\n        next: response => {\n          this.categories = response.categories || [];\n        },\n        error: error => {\n          console.error('Error loading categories:', error);\n        }\n      });\n    }\n    loadCustomers() {\n      this.http.get('http://localhost:5000/api/simple/customers').subscribe({\n        next: response => {\n          this.customers = response.customers || [];\n        },\n        error: error => {\n          console.error('Error loading customers:', error);\n        }\n      });\n    }\n    getCurrentDate() {\n      return new Date().toLocaleDateString('ar-EG');\n    }\n    searchProducts() {\n      this.filteredProducts = this.products.filter(product => product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) || product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase()));\n      if (this.selectedCategory) {\n        this.filteredProducts = this.filteredProducts.filter(product => product.categoryId === this.selectedCategory);\n      }\n    }\n    selectCategory(categoryId) {\n      this.selectedCategory = this.selectedCategory === categoryId ? null : categoryId;\n      this.searchProducts();\n    }\n    addToCart(product) {\n      if (product.stock <= 0) {\n        this.showMessage('هذا المنتج غير متوفر في المخزون', true);\n        return;\n      }\n      const existingItem = this.cartItems.find(item => item.product.id === product.id);\n      if (existingItem) {\n        if (existingItem.quantity < product.stock) {\n          existingItem.quantity++;\n        } else {\n          this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);\n        }\n      } else {\n        this.cartItems.push({\n          product,\n          quantity: 1\n        });\n      }\n    }\n    removeFromCart(index) {\n      this.cartItems.splice(index, 1);\n    }\n    increaseQuantity(index) {\n      const item = this.cartItems[index];\n      if (item.quantity < item.product.stock) {\n        item.quantity++;\n      } else {\n        this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);\n      }\n    }\n    decreaseQuantity(index) {\n      const item = this.cartItems[index];\n      if (item.quantity > 1) {\n        item.quantity--;\n      }\n    }\n    getItemTotal(item) {\n      return item.product.price * item.quantity;\n    }\n    getSubtotal() {\n      return this.cartItems.reduce((total, item) => total + this.getItemTotal(item), 0);\n    }\n    getTax() {\n      return this.getSubtotal() * (this.taxRate / 100);\n    }\n    getTotal() {\n      return this.getSubtotal() + this.getTax() - this.discount;\n    }\n    getChange() {\n      return this.paidAmount - this.getTotal();\n    }\n    selectPaymentMethod(methodId) {\n      this.selectedPaymentMethod = methodId;\n      if (methodId !== 'cash') {\n        this.paidAmount = this.getTotal();\n      }\n    }\n    calculateChange() {\n      // Change is calculated automatically in getChange()\n    }\n    canCompleteSale() {\n      if (this.cartItems.length === 0) return false;\n      if (this.selectedPaymentMethod === 'cash') {\n        return this.paidAmount >= this.getTotal();\n      }\n      return true;\n    }\n    completeSale() {\n      if (!this.canCompleteSale()) return;\n      this.isProcessing = true;\n      const saleData = {\n        customerId: this.selectedCustomer,\n        items: this.cartItems.map(item => ({\n          productId: item.product.id,\n          quantity: item.quantity,\n          price: item.product.price\n        })),\n        subtotal: this.getSubtotal(),\n        tax: this.getTax(),\n        discount: this.discount,\n        total: this.getTotal(),\n        paymentMethod: this.selectedPaymentMethod,\n        paidAmount: this.paidAmount\n      };\n      // محاكاة إتمام البيع\n      setTimeout(() => {\n        this.isProcessing = false;\n        this.showMessage('تم إتمام البيع بنجاح');\n        this.clearCart();\n        this.printReceipt(saleData);\n      }, 2000);\n    }\n    clearCart() {\n      this.cartItems = [];\n      this.selectedCustomer = null;\n      this.paidAmount = 0;\n      this.discount = 0;\n      this.selectedPaymentMethod = 'cash';\n    }\n    newSale() {\n      this.clearCart();\n      this.showMessage('بيع جديد - تم مسح الفاتورة');\n    }\n    openDrawer() {\n      this.showMessage('تم فتح درج النقدية');\n    }\n    scanBarcode() {\n      this.showMessage('مسح الباركود - قيد التطوير');\n    }\n    addNewCustomer() {\n      this.showMessage('إضافة عميل جديد - قيد التطوير');\n    }\n    viewSalesHistory() {\n      this.showMessage('تاريخ المبيعات - قيد التطوير');\n    }\n    printLastReceipt() {\n      this.showMessage('طباعة آخر فاتورة - قيد التطوير');\n    }\n    endOfDay() {\n      this.showMessage('إقفال اليوم - قيد التطوير');\n    }\n    printReceipt(saleData) {\n      console.log('Printing receipt:', saleData);\n      this.showMessage('تم طباعة الفاتورة');\n    }\n    showMessage(message, isError = false) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        horizontalPosition: 'center',\n        verticalPosition: 'top',\n        panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\n      });\n    }\n    static ɵfac = function Pos_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Pos)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Pos,\n      selectors: [[\"app-pos\"]],\n      decls: 82,\n      vars: 15,\n      consts: [[\"posMenu\", \"matMenu\"], [\"searchInput\", \"\"], [\"emptyCart\", \"\"], [1, \"pos-container\"], [1, \"pos-header\"], [1, \"header-content\"], [1, \"title-section\"], [1, \"pos-icon\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"pos-main\"], [1, \"products-panel\"], [1, \"search-section\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0623\\u0648 \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matPrefix\", \"\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"matTooltip\", \"\\u0645\\u0633\\u062D \\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\", 3, \"click\"], [1, \"categories-section\"], [1, \"category-chips\"], [3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"out-of-stock\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"cart-panel\"], [1, \"customer-section\"], [\"appearance\", \"outline\", 1, \"customer-field\"], [3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"matTooltip\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F\", 3, \"click\"], [1, \"cart-section\"], [1, \"cart-header\"], [1, \"items-count\"], [\"class\", \"cart-items\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"totals-section\", 4, \"ngIf\"], [\"class\", \"payment-section\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [3, \"click\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [1, \"product-info\"], [1, \"product-code\"], [1, \"product-price\"], [1, \"product-stock\"], [3, \"value\"], [1, \"cart-items\"], [\"class\", \"cart-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"cart-item\"], [1, \"item-info\"], [1, \"item-controls\"], [1, \"quantity-controls\"], [\"mat-icon-button\", \"\", 3, \"click\", \"disabled\"], [1, \"quantity\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"item-price\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"empty-cart\"], [1, \"totals-section\"], [1, \"total-row\"], [\"class\", \"total-row discount\", 4, \"ngIf\"], [1, \"total-row\", \"final\"], [1, \"total-row\", \"discount\"], [1, \"payment-section\"], [1, \"payment-methods\"], [\"mat-raised-button\", \"\", \"class\", \"payment-btn\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"payment-amount\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"complete-btn\", 3, \"click\", \"disabled\"], [\"mat-stroked-button\", \"\", 1, \"clear-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"payment-btn\", 3, \"click\", \"color\"], [1, \"payment-amount\"], [\"appearance\", \"outline\", 1, \"amount-field\"], [\"matInput\", \"\", \"type\", \"number\", \"placeholder\", \"0.00\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [\"class\", \"change-amount\", 4, \"ngIf\"], [1, \"change-amount\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function Pos_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"h1\")(5, \"mat-icon\", 7);\n          i0.ɵɵtext(6, \"storefront\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" \\u0646\\u0642\\u0637\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0639 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.newSale());\n          });\n          i0.ɵɵelementStart(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"add_shopping_cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" \\u0628\\u064A\\u0639 \\u062C\\u062F\\u064A\\u062F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openDrawer());\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"account_balance_wallet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" \\u0641\\u062A\\u062D \\u0627\\u0644\\u062F\\u0631\\u062C \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 11)(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"more_vert\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"mat-menu\", null, 0)(24, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.viewSalesHistory());\n          });\n          i0.ɵɵelementStart(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"history\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\");\n          i0.ɵɵtext(28, \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_29_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.printLastReceipt());\n          });\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"print\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\");\n          i0.ɵɵtext(33, \"\\u0637\\u0628\\u0627\\u0639\\u0629 \\u0622\\u062E\\u0631 \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.endOfDay());\n          });\n          i0.ɵɵelementStart(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"event_note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"\\u0625\\u0642\\u0641\\u0627\\u0644 \\u0627\\u0644\\u064A\\u0648\\u0645\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(39, \"div\", 13)(40, \"div\", 14)(41, \"div\", 15)(42, \"mat-form-field\", 16)(43, \"mat-label\");\n          i0.ɵɵtext(44, \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"input\", 17, 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function Pos_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function Pos_Template_input_input_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.searchProducts());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-icon\", 18);\n          i0.ɵɵtext(48, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_49_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scanBarcode());\n          });\n          i0.ɵɵelementStart(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"qr_code_scanner\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(52, \"div\", 20)(53, \"div\", 21)(54, \"mat-chip-set\");\n          i0.ɵɵtemplate(55, Pos_mat_chip_55_Template, 2, 3, \"mat-chip\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 23);\n          i0.ɵɵtemplate(57, Pos_div_57_Template, 14, 13, \"div\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 25)(59, \"div\", 26)(60, \"mat-form-field\", 27)(61, \"mat-label\");\n          i0.ɵɵtext(62, \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"mat-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function Pos_Template_mat_select_ngModelChange_63_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCustomer, $event) || (ctx.selectedCustomer = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(64, \"mat-option\", 29);\n          i0.ɵɵtext(65, \"\\u0639\\u0645\\u064A\\u0644 \\u0646\\u0642\\u062F\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(66, Pos_mat_option_66_Template, 2, 2, \"mat-option\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function Pos_Template_button_click_67_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.addNewCustomer());\n          });\n          i0.ɵɵelementStart(68, \"mat-icon\");\n          i0.ɵɵtext(69, \"person_add\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(70, \"div\", 32)(71, \"div\", 33)(72, \"h3\");\n          i0.ɵɵtext(73, \"\\u0639\\u0646\\u0627\\u0635\\u0631 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\", 34);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(76, Pos_div_76_Template, 2, 1, \"div\", 35)(77, Pos_ng_template_77_Template, 7, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, Pos_div_79_Template, 20, 20, \"div\", 36)(80, Pos_div_80_Template, 13, 3, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(81, Pos_div_81_Template, 4, 0, \"div\", 38);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const posMenu_r15 = i0.ɵɵreference(23);\n          const emptyCart_r16 = i0.ɵɵreference(78);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate2(\"\", (ctx.currentUser == null ? null : ctx.currentUser.branch == null ? null : ctx.currentUser.branch.nameAr) || \"\\u0627\\u0644\\u0641\\u0631\\u0639 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\", \" - \", ctx.getCurrentDate());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isProcessing);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", posMenu_r15);\n          i0.ɵɵadvance(26);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredProducts);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCustomer);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"\", ctx.cartItems.length, \" \\u0639\\u0646\\u0635\\u0631\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0)(\"ngIfElse\", emptyCart_r16);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.CurrencyPipe, FormsModule, i4.DefaultValueAccessor, i4.NumberValueAccessor, i4.NgControlStatus, i4.NgModel, HttpClientModule, MatCardModule, MatButtonModule, i5.MatButton, i5.MatIconButton, MatIconModule, i6.MatIcon, MatInputModule, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatPrefix, i8.MatSuffix, MatSelectModule, i9.MatSelect, i9.MatOption, MatMenuModule, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, MatChipsModule, i11.MatChip, i11.MatChipSet, MatProgressSpinnerModule, i12.MatProgressSpinner, MatTooltipModule, i13.MatTooltip, MatSnackBarModule],\n      styles: [\".pos-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: #f5f5f5;\\n  position: relative;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 16px 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 4px 0;\\n  color: #1976d2;\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   .pos-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .title-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  overflow: hidden;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%] {\\n  flex: 2;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .categories-section[_ngcontent-%COMP%]   .category-chips[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .categories-section[_ngcontent-%COMP%]   .category-chips[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .categories-section[_ngcontent-%COMP%]   .category-chips[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%]:hover {\\n  background: #bbdefb;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .categories-section[_ngcontent-%COMP%]   .category-chips[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%]   mat-chip[selected][_ngcontent-%COMP%], .pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .categories-section[_ngcontent-%COMP%]   .category-chips[_ngcontent-%COMP%]   mat-chip-set[_ngcontent-%COMP%]   mat-chip.selected[_ngcontent-%COMP%] {\\n  background: #1976d2 !important;\\n  color: white !important;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  gap: 16px;\\n  overflow-y: auto;\\n  padding: 8px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  border-color: #1976d2;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card.out-of-stock[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card.out-of-stock[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  border-color: transparent;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 12px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  color: #1976d2;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n  line-height: 1.3;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-code[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #4caf50;\\n  margin-bottom: 8px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stock[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .products-panel[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stock.low-stock[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-weight: 600;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .customer-section[_ngcontent-%COMP%]   .customer-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-size: 1.2rem;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-header[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  max-height: 300px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  background: #fafafa;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-controls[_ngcontent-%COMP%]   .quantity-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border-radius: 20px;\\n  padding: 4px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-controls[_ngcontent-%COMP%]   .quantity-controls[_ngcontent-%COMP%]   .quantity[_ngcontent-%COMP%] {\\n  min-width: 30px;\\n  text-align: center;\\n  font-weight: 600;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-controls[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4caf50;\\n  min-width: 80px;\\n  text-align: right;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .empty-cart[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #666;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .empty-cart[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .cart-section[_ngcontent-%COMP%]   .empty-cart[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .totals-section[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .totals-section[_ngcontent-%COMP%]   .total-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 0.9rem;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .totals-section[_ngcontent-%COMP%]   .total-row.discount[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .totals-section[_ngcontent-%COMP%]   .total-row.final[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #1976d2;\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 8px;\\n  margin-top: 8px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .payment-methods[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .payment-methods[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 12px 8px;\\n  font-size: 0.8rem;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .payment-amount[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .payment-amount[_ngcontent-%COMP%]   .amount-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .payment-amount[_ngcontent-%COMP%]   .change-amount[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  padding: 8px;\\n  background: #e8f5e8;\\n  border-radius: 4px;\\n  text-align: center;\\n  font-weight: 600;\\n  color: #4caf50;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .complete-btn[_ngcontent-%COMP%] {\\n  flex: 2;\\n  padding: 12px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n.pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%]   .payment-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n.pos-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.pos-container[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  color: #333;\\n}\\n\\n@media (max-width: 1024px) {\\n  .pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .pos-container[_ngcontent-%COMP%]   .pos-main[_ngcontent-%COMP%]   .cart-panel[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return Pos;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "HttpClientModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatSelectModule", "MatMenuModule", "MatChipsModule", "MatProgressSpinnerModule", "MatTooltipModule", "MatSnackBarModule", "i0", "ɵɵelementStart", "ɵɵlistener", "Pos_mat_chip_55_Template_mat_chip_click_0_listener", "category_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "selectCategory", "id", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "selectedCate<PERSON><PERSON>", "ɵɵadvance", "ɵɵtextInterpolate1", "nameAr", "Pos_div_57_Template_div_click_0_listener", "product_r6", "_r5", "addToCart", "stock", "ɵɵtextInterpolate", "productCode", "ɵɵpipeBind4", "price", "ɵɵproperty", "customer_r7", "fullName", "Pos_div_76_div_1_Template_button_click_8_listener", "i_r9", "_r8", "index", "decreaseQuantity", "Pos_div_76_div_1_Template_button_click_13_listener", "increaseQuantity", "Pos_div_76_div_1_Template_button_click_19_listener", "removeFromCart", "item_r10", "product", "quantity", "getItemTotal", "ɵɵtemplate", "Pos_div_76_div_1_Template", "cartItems", "discount", "Pos_div_79_div_13_Template", "getSubtotal", "taxRate", "getTax", "getTotal", "Pos_div_80_button_2_Template_button_click_0_listener", "method_r13", "_r12", "selectPaymentMethod", "selectedPaymentMethod", "icon", "getChange", "ɵɵtwoWayListener", "Pos_div_80_div_3_Template_input_ngModelChange_4_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "paidAmount", "Pos_div_80_div_3_Template_input_input_4_listener", "calculateChange", "Pos_div_80_div_3_div_7_Template", "ɵɵtwoWayProperty", "Pos_div_80_button_2_Template", "Pos_div_80_div_3_Template", "Pos_div_80_Template_button_click_5_listener", "_r11", "completeSale", "Pos_div_80_Template_button_click_9_listener", "clearCart", "paymentMethods", "canCompleteSale", "ɵɵelement", "Pos", "http", "snackBar", "currentUser", "searchTerm", "selectedCustomer", "isProcessing", "products", "filteredProducts", "categories", "customers", "constructor", "ngOnInit", "loadCurrentUser", "loadProducts", "loadCategories", "loadCustomers", "userData", "localStorage", "getItem", "JSON", "parse", "get", "subscribe", "next", "response", "error", "console", "showMessage", "getCurrentDate", "Date", "toLocaleDateString", "searchProducts", "filter", "toLowerCase", "includes", "categoryId", "existingItem", "find", "item", "push", "splice", "reduce", "total", "methodId", "length", "saleData", "customerId", "items", "map", "productId", "subtotal", "tax", "paymentMethod", "setTimeout", "printReceipt", "newSale", "openDrawer", "scanBarcode", "addNewCustomer", "viewSalesHistory", "printLastReceipt", "endOfDay", "log", "message", "isError", "open", "duration", "horizontalPosition", "verticalPosition", "panelClass", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "Pos_Template", "rf", "ctx", "Pos_Template_button_click_11_listener", "_r1", "Pos_Template_button_click_15_listener", "Pos_Template_button_click_24_listener", "Pos_Template_button_click_29_listener", "Pos_Template_button_click_34_listener", "Pos_Template_input_ngModelChange_45_listener", "Pos_Template_input_input_45_listener", "Pos_Template_button_click_49_listener", "Pos_mat_chip_55_Template", "Pos_div_57_Template", "Pos_Template_mat_select_ngModelChange_63_listener", "Pos_mat_option_66_Template", "Pos_Template_button_click_67_listener", "Pos_div_76_Template", "Pos_ng_template_77_Template", "ɵɵtemplateRefExtractor", "Pos_div_79_Template", "Pos_div_80_Template", "Pos_div_81_Template", "ɵɵtextInterpolate2", "branch", "posMenu_r15", "emptyCart_r16", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i4", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i5", "MatButton", "MatIconButton", "i6", "MatIcon", "i7", "MatInput", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatPrefix", "MatSuffix", "i9", "MatSelect", "MatOption", "i10", "MatMenu", "MatMenuItem", "MatMenuTrigger", "i11", "MatChip", "MatChipSet", "i12", "MatProgressSpinner", "i13", "MatTooltip", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\pos\\pos.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\pages\\pos\\pos.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HttpClient, HttpClientModule } from '@angular/common/http';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\r\n\r\ninterface Product {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n  productCode: string;\r\n  price: number;\r\n  stock: number;\r\n  categoryId: number;\r\n}\r\n\r\ninterface Category {\r\n  id: number;\r\n  nameAr: string;\r\n  nameEn: string;\r\n}\r\n\r\ninterface Customer {\r\n  id: number;\r\n  fullName: string;\r\n  phoneNumber: string;\r\n}\r\n\r\ninterface CartItem {\r\n  product: Product;\r\n  quantity: number;\r\n}\r\n\r\ninterface PaymentMethod {\r\n  id: string;\r\n  nameAr: string;\r\n  icon: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-pos',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    HttpClientModule,\r\n    MatCardModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatMenuModule,\r\n    MatChipsModule,\r\n    MatProgressSpinnerModule,\r\n    MatTooltipModule,\r\n    MatSnackBarModule\r\n  ],\r\n  templateUrl: './pos.html',\r\n  styleUrls: ['./pos.scss']\r\n})\r\nexport class Pos implements OnInit {\r\n  currentUser: any = null;\r\n  searchTerm: string = '';\r\n  selectedCategory: number | null = null;\r\n  selectedCustomer: number | null = null;\r\n  selectedPaymentMethod: string = 'cash';\r\n  paidAmount: number = 0;\r\n  discount: number = 0;\r\n  taxRate: number = 14; // 14% VAT in Egypt\r\n  isProcessing: boolean = false;\r\n\r\n  products: Product[] = [];\r\n  filteredProducts: Product[] = [];\r\n  categories: Category[] = [];\r\n  customers: Customer[] = [];\r\n  cartItems: CartItem[] = [];\r\n\r\n  paymentMethods: PaymentMethod[] = [\r\n    { id: 'cash', nameAr: 'نقدي', icon: 'money' },\r\n    { id: 'card', nameAr: 'بطاقة', icon: 'credit_card' },\r\n    { id: 'transfer', nameAr: 'تحويل', icon: 'account_balance' }\r\n  ];\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadCurrentUser();\r\n    this.loadProducts();\r\n    this.loadCategories();\r\n    this.loadCustomers();\r\n  }\r\n\r\n  loadCurrentUser() {\r\n    const userData = localStorage.getItem('currentUser');\r\n    if (userData) {\r\n      this.currentUser = JSON.parse(userData);\r\n    }\r\n  }\r\n\r\n  loadProducts() {\r\n    this.http.get<any>('http://localhost:5000/api/simple/products').subscribe({\r\n      next: (response) => {\r\n        this.products = response.products || [];\r\n        this.filteredProducts = [...this.products];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading products:', error);\r\n        this.showMessage('خطأ في تحميل المنتجات', true);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadCategories() {\r\n    this.http.get<any>('http://localhost:5000/api/simple/categories').subscribe({\r\n      next: (response) => {\r\n        this.categories = response.categories || [];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading categories:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadCustomers() {\r\n    this.http.get<any>('http://localhost:5000/api/simple/customers').subscribe({\r\n      next: (response) => {\r\n        this.customers = response.customers || [];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading customers:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  getCurrentDate(): string {\r\n    return new Date().toLocaleDateString('ar-EG');\r\n  }\r\n\r\n  searchProducts() {\r\n    this.filteredProducts = this.products.filter(product =>\r\n      product.nameAr.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\r\n      product.productCode.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n    );\r\n\r\n    if (this.selectedCategory) {\r\n      this.filteredProducts = this.filteredProducts.filter(product =>\r\n        product.categoryId === this.selectedCategory\r\n      );\r\n    }\r\n  }\r\n\r\n  selectCategory(categoryId: number) {\r\n    this.selectedCategory = this.selectedCategory === categoryId ? null : categoryId;\r\n    this.searchProducts();\r\n  }\r\n\r\n  addToCart(product: Product) {\r\n    if (product.stock <= 0) {\r\n      this.showMessage('هذا المنتج غير متوفر في المخزون', true);\r\n      return;\r\n    }\r\n\r\n    const existingItem = this.cartItems.find(item => item.product.id === product.id);\r\n\r\n    if (existingItem) {\r\n      if (existingItem.quantity < product.stock) {\r\n        existingItem.quantity++;\r\n      } else {\r\n        this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);\r\n      }\r\n    } else {\r\n      this.cartItems.push({ product, quantity: 1 });\r\n    }\r\n  }\r\n\r\n  removeFromCart(index: number) {\r\n    this.cartItems.splice(index, 1);\r\n  }\r\n\r\n  increaseQuantity(index: number) {\r\n    const item = this.cartItems[index];\r\n    if (item.quantity < item.product.stock) {\r\n      item.quantity++;\r\n    } else {\r\n      this.showMessage('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', true);\r\n    }\r\n  }\r\n\r\n  decreaseQuantity(index: number) {\r\n    const item = this.cartItems[index];\r\n    if (item.quantity > 1) {\r\n      item.quantity--;\r\n    }\r\n  }\r\n\r\n  getItemTotal(item: CartItem): number {\r\n    return item.product.price * item.quantity;\r\n  }\r\n\r\n  getSubtotal(): number {\r\n    return this.cartItems.reduce((total, item) => total + this.getItemTotal(item), 0);\r\n  }\r\n\r\n  getTax(): number {\r\n    return this.getSubtotal() * (this.taxRate / 100);\r\n  }\r\n\r\n  getTotal(): number {\r\n    return this.getSubtotal() + this.getTax() - this.discount;\r\n  }\r\n\r\n  getChange(): number {\r\n    return this.paidAmount - this.getTotal();\r\n  }\r\n\r\n  selectPaymentMethod(methodId: string) {\r\n    this.selectedPaymentMethod = methodId;\r\n    if (methodId !== 'cash') {\r\n      this.paidAmount = this.getTotal();\r\n    }\r\n  }\r\n\r\n  calculateChange() {\r\n    // Change is calculated automatically in getChange()\r\n  }\r\n\r\n  canCompleteSale(): boolean {\r\n    if (this.cartItems.length === 0) return false;\r\n    if (this.selectedPaymentMethod === 'cash') {\r\n      return this.paidAmount >= this.getTotal();\r\n    }\r\n    return true;\r\n  }\r\n\r\n  completeSale() {\r\n    if (!this.canCompleteSale()) return;\r\n\r\n    this.isProcessing = true;\r\n\r\n    const saleData = {\r\n      customerId: this.selectedCustomer,\r\n      items: this.cartItems.map(item => ({\r\n        productId: item.product.id,\r\n        quantity: item.quantity,\r\n        price: item.product.price\r\n      })),\r\n      subtotal: this.getSubtotal(),\r\n      tax: this.getTax(),\r\n      discount: this.discount,\r\n      total: this.getTotal(),\r\n      paymentMethod: this.selectedPaymentMethod,\r\n      paidAmount: this.paidAmount\r\n    };\r\n\r\n    // محاكاة إتمام البيع\r\n    setTimeout(() => {\r\n      this.isProcessing = false;\r\n      this.showMessage('تم إتمام البيع بنجاح');\r\n      this.clearCart();\r\n      this.printReceipt(saleData);\r\n    }, 2000);\r\n  }\r\n\r\n  clearCart() {\r\n    this.cartItems = [];\r\n    this.selectedCustomer = null;\r\n    this.paidAmount = 0;\r\n    this.discount = 0;\r\n    this.selectedPaymentMethod = 'cash';\r\n  }\r\n\r\n  newSale() {\r\n    this.clearCart();\r\n    this.showMessage('بيع جديد - تم مسح الفاتورة');\r\n  }\r\n\r\n  openDrawer() {\r\n    this.showMessage('تم فتح درج النقدية');\r\n  }\r\n\r\n  scanBarcode() {\r\n    this.showMessage('مسح الباركود - قيد التطوير');\r\n  }\r\n\r\n  addNewCustomer() {\r\n    this.showMessage('إضافة عميل جديد - قيد التطوير');\r\n  }\r\n\r\n  viewSalesHistory() {\r\n    this.showMessage('تاريخ المبيعات - قيد التطوير');\r\n  }\r\n\r\n  printLastReceipt() {\r\n    this.showMessage('طباعة آخر فاتورة - قيد التطوير');\r\n  }\r\n\r\n  endOfDay() {\r\n    this.showMessage('إقفال اليوم - قيد التطوير');\r\n  }\r\n\r\n  printReceipt(saleData: any) {\r\n    console.log('Printing receipt:', saleData);\r\n    this.showMessage('تم طباعة الفاتورة');\r\n  }\r\n\r\n  private showMessage(message: string, isError = false) {\r\n    this.snackBar.open(message, 'إغلاق', {\r\n      duration: 3000,\r\n      horizontalPosition: 'center',\r\n      verticalPosition: 'top',\r\n      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']\r\n    });\r\n  }\r\n}\r\n", "<!-- نقطة البيع الاحترافية -->\r\n<div class=\"pos-container\">\r\n  <!-- Header -->\r\n  <div class=\"pos-header\">\r\n    <div class=\"header-content\">\r\n      <div class=\"title-section\">\r\n        <h1>\r\n          <mat-icon class=\"pos-icon\">storefront</mat-icon>\r\n          نقطة البيع\r\n        </h1>\r\n        <p>{{ currentUser?.branch?.nameAr || 'الفرع الرئيسي' }} - {{ getCurrentDate() }}</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button mat-raised-button color=\"primary\" (click)=\"newSale()\" [disabled]=\"isProcessing\">\r\n          <mat-icon>add_shopping_cart</mat-icon>\r\n          بيع جديد\r\n        </button>\r\n        <button mat-stroked-button (click)=\"openDrawer()\">\r\n          <mat-icon>account_balance_wallet</mat-icon>\r\n          فتح الدرج\r\n        </button>\r\n        <button mat-icon-button [matMenuTriggerFor]=\"posMenu\">\r\n          <mat-icon>more_vert</mat-icon>\r\n        </button>\r\n        <mat-menu #posMenu=\"matMenu\">\r\n          <button mat-menu-item (click)=\"viewSalesHistory()\">\r\n            <mat-icon>history</mat-icon>\r\n            <span>تاريخ المبيعات</span>\r\n          </button>\r\n          <button mat-menu-item (click)=\"printLastReceipt()\">\r\n            <mat-icon>print</mat-icon>\r\n            <span>طباعة آخر فاتورة</span>\r\n          </button>\r\n          <button mat-menu-item (click)=\"endOfDay()\">\r\n            <mat-icon>event_note</mat-icon>\r\n            <span>إقفال اليوم</span>\r\n          </button>\r\n        </mat-menu>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main POS Interface -->\r\n  <div class=\"pos-main\">\r\n    <!-- Left Panel - Products -->\r\n    <div class=\"products-panel\">\r\n      <!-- Search -->\r\n      <div class=\"search-section\">\r\n        <mat-form-field appearance=\"outline\" class=\"search-field\">\r\n          <mat-label>البحث في المنتجات</mat-label>\r\n          <input matInput\r\n                 [(ngModel)]=\"searchTerm\"\r\n                 (input)=\"searchProducts()\"\r\n                 placeholder=\"اسم المنتج أو الباركود\"\r\n                 #searchInput>\r\n          <mat-icon matPrefix>search</mat-icon>\r\n          <button mat-icon-button matSuffix (click)=\"scanBarcode()\" matTooltip=\"مسح الباركود\">\r\n            <mat-icon>qr_code_scanner</mat-icon>\r\n          </button>\r\n        </mat-form-field>\r\n      </div>\r\n\r\n      <!-- Categories -->\r\n      <div class=\"categories-section\">\r\n        <div class=\"category-chips\">\r\n          <mat-chip-set>\r\n            <mat-chip\r\n              *ngFor=\"let category of categories\"\r\n              [class.selected]=\"selectedCategory === category.id\"\r\n              (click)=\"selectCategory(category.id)\">\r\n              {{ category.nameAr }}\r\n            </mat-chip>\r\n          </mat-chip-set>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Products Grid -->\r\n      <div class=\"products-grid\">\r\n        <div\r\n          *ngFor=\"let product of filteredProducts\"\r\n          class=\"product-card\"\r\n          (click)=\"addToCart(product)\"\r\n          [class.out-of-stock]=\"product.stock <= 0\">\r\n          <div class=\"product-image\">\r\n            <mat-icon>inventory</mat-icon>\r\n          </div>\r\n          <div class=\"product-info\">\r\n            <h4>{{ product.nameAr }}</h4>\r\n            <p class=\"product-code\">{{ product.productCode }}</p>\r\n            <div class=\"product-price\">{{ product.price | currency:'EGP':'symbol':'1.0-2' }}</div>\r\n            <div class=\"product-stock\" [class.low-stock]=\"product.stock <= 10\">\r\n              المخزون: {{ product.stock }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel - Cart & Payment -->\r\n    <div class=\"cart-panel\">\r\n      <!-- Customer Selection -->\r\n      <div class=\"customer-section\">\r\n        <mat-form-field appearance=\"outline\" class=\"customer-field\">\r\n          <mat-label>العميل</mat-label>\r\n          <mat-select [(ngModel)]=\"selectedCustomer\">\r\n            <mat-option value=\"\">عميل نقدي</mat-option>\r\n            <mat-option *ngFor=\"let customer of customers\" [value]=\"customer.id\">\r\n              {{ customer.fullName }}\r\n            </mat-option>\r\n          </mat-select>\r\n          <button mat-icon-button matSuffix (click)=\"addNewCustomer()\" matTooltip=\"إضافة عميل جديد\">\r\n            <mat-icon>person_add</mat-icon>\r\n          </button>\r\n        </mat-form-field>\r\n      </div>\r\n\r\n      <!-- Cart Items -->\r\n      <div class=\"cart-section\">\r\n        <div class=\"cart-header\">\r\n          <h3>عناصر الفاتورة</h3>\r\n          <span class=\"items-count\">{{ cartItems.length }} عنصر</span>\r\n        </div>\r\n\r\n        <div class=\"cart-items\" *ngIf=\"cartItems.length > 0; else emptyCart\">\r\n          <div *ngFor=\"let item of cartItems; let i = index\" class=\"cart-item\">\r\n            <div class=\"item-info\">\r\n              <h4>{{ item.product.nameAr }}</h4>\r\n              <p>{{ item.product.productCode }}</p>\r\n            </div>\r\n            <div class=\"item-controls\">\r\n              <div class=\"quantity-controls\">\r\n                <button mat-icon-button (click)=\"decreaseQuantity(i)\" [disabled]=\"item.quantity <= 1\">\r\n                  <mat-icon>remove</mat-icon>\r\n                </button>\r\n                <span class=\"quantity\">{{ item.quantity }}</span>\r\n                <button mat-icon-button (click)=\"increaseQuantity(i)\">\r\n                  <mat-icon>add</mat-icon>\r\n                </button>\r\n              </div>\r\n              <div class=\"item-price\">{{ getItemTotal(item) | currency:'EGP':'symbol':'1.0-2' }}</div>\r\n              <button mat-icon-button color=\"warn\" (click)=\"removeFromCart(i)\">\r\n                <mat-icon>delete</mat-icon>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <ng-template #emptyCart>\r\n          <div class=\"empty-cart\">\r\n            <mat-icon>shopping_cart</mat-icon>\r\n            <p>لا توجد عناصر في الفاتورة</p>\r\n            <p>ابحث عن المنتجات وأضفها للفاتورة</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n\r\n      <!-- Totals -->\r\n      <div class=\"totals-section\" *ngIf=\"cartItems.length > 0\">\r\n        <div class=\"total-row\">\r\n          <span>المجموع الفرعي:</span>\r\n          <span>{{ getSubtotal() | currency:'EGP':'symbol':'1.0-2' }}</span>\r\n        </div>\r\n        <div class=\"total-row\">\r\n          <span>الضريبة ({{ taxRate }}%):</span>\r\n          <span>{{ getTax() | currency:'EGP':'symbol':'1.0-2' }}</span>\r\n        </div>\r\n        <div class=\"total-row discount\" *ngIf=\"discount > 0\">\r\n          <span>الخصم:</span>\r\n          <span>-{{ discount | currency:'EGP':'symbol':'1.0-2' }}</span>\r\n        </div>\r\n        <div class=\"total-row final\">\r\n          <span>الإجمالي:</span>\r\n          <span>{{ getTotal() | currency:'EGP':'symbol':'1.0-2' }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Payment Section -->\r\n      <div class=\"payment-section\" *ngIf=\"cartItems.length > 0\">\r\n        <div class=\"payment-methods\">\r\n          <button\r\n            mat-raised-button\r\n            *ngFor=\"let method of paymentMethods\"\r\n            [color]=\"selectedPaymentMethod === method.id ? 'primary' : ''\"\r\n            (click)=\"selectPaymentMethod(method.id)\"\r\n            class=\"payment-btn\">\r\n            <mat-icon>{{ method.icon }}</mat-icon>\r\n            {{ method.nameAr }}\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"payment-amount\" *ngIf=\"selectedPaymentMethod === 'cash'\">\r\n          <mat-form-field appearance=\"outline\" class=\"amount-field\">\r\n            <mat-label>المبلغ المدفوع</mat-label>\r\n            <input matInput\r\n                   type=\"number\"\r\n                   [(ngModel)]=\"paidAmount\"\r\n                   placeholder=\"0.00\"\r\n                   (input)=\"calculateChange()\">\r\n            <span matSuffix>جنيه</span>\r\n          </mat-form-field>\r\n          <div class=\"change-amount\" *ngIf=\"getChange() !== 0\">\r\n            <span>الباقي: {{ getChange() | currency:'EGP':'symbol':'1.0-2' }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Action Buttons -->\r\n        <div class=\"action-buttons\">\r\n          <button\r\n            mat-raised-button\r\n            color=\"primary\"\r\n            (click)=\"completeSale()\"\r\n            [disabled]=\"!canCompleteSale()\"\r\n            class=\"complete-btn\">\r\n            <mat-icon>check_circle</mat-icon>\r\n            إتمام البيع\r\n          </button>\r\n          <button\r\n            mat-stroked-button\r\n            (click)=\"clearCart()\"\r\n            class=\"clear-btn\">\r\n            <mat-icon>clear</mat-icon>\r\n            مسح الفاتورة\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading Overlay -->\r\n  <div *ngIf=\"isProcessing\" class=\"loading-overlay\">\r\n    <mat-spinner diameter=\"50\"></mat-spinner>\r\n    <p>جاري معالجة العملية...</p>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAqBC,gBAAgB,QAAQ,sBAAsB;AACnE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAAsBC,iBAAiB,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;;ICqDhEC,EAAA,CAAAC,cAAA,mBAGwC;IAAtCD,EAAA,CAAAE,UAAA,mBAAAC,mDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,WAAA,CAAAQ,EAAA,CAA2B;IAAA,EAAC;IACrCZ,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAW;;;;;IAHTd,EAAA,CAAAe,WAAA,aAAAP,MAAA,CAAAQ,gBAAA,KAAAZ,WAAA,CAAAQ,EAAA,CAAmD;IAEnDZ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAd,WAAA,CAAAe,MAAA,MACF;;;;;;IAOJnB,EAAA,CAAAC,cAAA,cAI4C;IAD1CD,EAAA,CAAAE,UAAA,mBAAAkB,yCAAA;MAAA,MAAAC,UAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAf,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAe,SAAA,CAAAF,UAAA,CAAkB;IAAA,EAAC;IAG1BrB,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAa,MAAA,gBAAS;IACrBb,EADqB,CAAAc,YAAA,EAAW,EAC1B;IAEJd,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAa,MAAA,GAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC7Bd,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAa,MAAA,GAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IACrDd,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAa,MAAA,IAAqD;;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACtFd,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;IAZJd,EAAA,CAAAe,WAAA,iBAAAM,UAAA,CAAAG,KAAA,MAAyC;IAKnCxB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAyB,iBAAA,CAAAJ,UAAA,CAAAF,MAAA,CAAoB;IACAnB,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAyB,iBAAA,CAAAJ,UAAA,CAAAK,WAAA,CAAyB;IACtB1B,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,QAAAN,UAAA,CAAAO,KAAA,4BAAqD;IACrD5B,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAe,WAAA,cAAAM,UAAA,CAAAG,KAAA,OAAuC;IAChExB,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,kDAAAG,UAAA,CAAAG,KAAA,MACF;;;;;IAcAxB,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAa;;;;IAFkCd,EAAA,CAAA6B,UAAA,UAAAC,WAAA,CAAAlB,EAAA,CAAqB;IAClEZ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAY,WAAA,CAAAC,QAAA,MACF;;;;;;IAkBE/B,EAFJ,CAAAC,cAAA,cAAqE,cAC5C,SACjB;IAAAD,EAAA,CAAAa,MAAA,GAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAClCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAA8B;IACnCb,EADmC,CAAAc,YAAA,EAAI,EACjC;IAGFd,EAFJ,CAAAC,cAAA,cAA2B,cACM,iBACyD;IAA9DD,EAAA,CAAAE,UAAA,mBAAA8B,kDAAA;MAAA,MAAAC,IAAA,GAAAjC,EAAA,CAAAK,aAAA,CAAA6B,GAAA,EAAAC,KAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4B,gBAAA,CAAAH,IAAA,CAAmB;IAAA,EAAC;IACnDjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAClBb,EADkB,CAAAc,YAAA,EAAW,EACpB;IACTd,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAa,MAAA,IAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACjDd,EAAA,CAAAC,cAAA,kBAAsD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAmC,mDAAA;MAAA,MAAAJ,IAAA,GAAAjC,EAAA,CAAAK,aAAA,CAAA6B,GAAA,EAAAC,KAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8B,gBAAA,CAAAL,IAAA,CAAmB;IAAA,EAAC;IACnDjC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAa,MAAA,WAAG;IAEjBb,EAFiB,CAAAc,YAAA,EAAW,EACjB,EACL;IACNd,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAa,MAAA,IAA0D;;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACxFd,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAqC,mDAAA;MAAA,MAAAN,IAAA,GAAAjC,EAAA,CAAAK,aAAA,CAAA6B,GAAA,EAAAC,KAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgC,cAAA,CAAAP,IAAA,CAAiB;IAAA,EAAC;IAC9DjC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAa,MAAA,cAAM;IAGtBb,EAHsB,CAAAc,YAAA,EAAW,EACpB,EACL,EACF;;;;;IAlBEd,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAyB,iBAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAvB,MAAA,CAAyB;IAC1BnB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAyB,iBAAA,CAAAgB,QAAA,CAAAC,OAAA,CAAAhB,WAAA,CAA8B;IAIuB1B,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAA6B,UAAA,aAAAY,QAAA,CAAAE,QAAA,MAA+B;IAG9D3C,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAyB,iBAAA,CAAAgB,QAAA,CAAAE,QAAA,CAAmB;IAKpB3C,EAAA,CAAAiB,SAAA,GAA0D;IAA1DjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,QAAAnB,MAAA,CAAAoC,YAAA,CAAAH,QAAA,6BAA0D;;;;;IAhBxFzC,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAA6C,UAAA,IAAAC,yBAAA,oBAAqE;IAqBvE9C,EAAA,CAAAc,YAAA,EAAM;;;;IArBkBd,EAAA,CAAAiB,SAAA,EAAc;IAAdjB,EAAA,CAAA6B,UAAA,YAAArB,MAAA,CAAAuC,SAAA,CAAc;;;;;IAyBlC/C,EADF,CAAAC,cAAA,cAAwB,eACZ;IAAAD,EAAA,CAAAa,MAAA,oBAAa;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,yIAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAChCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,mLAAgC;IACrCb,EADqC,CAAAc,YAAA,EAAI,EACnC;;;;;IAeNd,EADF,CAAAC,cAAA,cAAqD,WAC7C;IAAAD,EAAA,CAAAa,MAAA,sCAAM;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACnBd,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,GAAiD;;IACzDb,EADyD,CAAAc,YAAA,EAAO,EAC1D;;;;IADEd,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA2B,WAAA,OAAAnB,MAAA,CAAAwC,QAAA,4BAAiD;;;;;IATvDhD,EAFJ,CAAAC,cAAA,cAAyD,cAChC,WACf;IAAAD,EAAA,CAAAa,MAAA,uFAAe;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC5Bd,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,GAAqD;;IAC7Db,EAD6D,CAAAc,YAAA,EAAO,EAC9D;IAEJd,EADF,CAAAC,cAAA,cAAuB,WACf;IAAAD,EAAA,CAAAa,MAAA,GAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACtCd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAgD;;IACxDb,EADwD,CAAAc,YAAA,EAAO,EACzD;IACNd,EAAA,CAAA6C,UAAA,KAAAI,0BAAA,kBAAqD;IAKnDjD,EADF,CAAAC,cAAA,eAA6B,YACrB;IAAAD,EAAA,CAAAa,MAAA,yDAAS;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACtBd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAkD;;IAE5Db,EAF4D,CAAAc,YAAA,EAAO,EAC3D,EACF;;;;IAdId,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,OAAAnB,MAAA,CAAA0C,WAAA,8BAAqD;IAGrDlD,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAkB,kBAAA,iDAAAV,MAAA,CAAA2C,OAAA,QAAyB;IACzBnD,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,SAAAnB,MAAA,CAAA4C,MAAA,8BAAgD;IAEvBpD,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAwC,QAAA,KAAkB;IAM3ChD,EAAA,CAAAiB,SAAA,GAAkD;IAAlDjB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,SAAAnB,MAAA,CAAA6C,QAAA,8BAAkD;;;;;;IAOxDrD,EAAA,CAAAC,cAAA,iBAKsB;IADpBD,EAAA,CAAAE,UAAA,mBAAAoD,qDAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAK,aAAA,CAAAmD,IAAA,EAAAjD,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiD,mBAAA,CAAAF,UAAA,CAAA3C,EAAA,CAA8B;IAAA,EAAC;IAExCZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACtCd,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IALPd,EAAA,CAAA6B,UAAA,UAAArB,MAAA,CAAAkD,qBAAA,KAAAH,UAAA,CAAA3C,EAAA,kBAA8D;IAGpDZ,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAyB,iBAAA,CAAA8B,UAAA,CAAAI,IAAA,CAAiB;IAC3B3D,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAApC,MAAA,MACF;;;;;IAcEnB,EADF,CAAAC,cAAA,cAAqD,WAC7C;IAAAD,EAAA,CAAAa,MAAA,GAA2D;;IACnEb,EADmE,CAAAc,YAAA,EAAO,EACpE;;;;IADEd,EAAA,CAAAiB,SAAA,GAA2D;IAA3DjB,EAAA,CAAAkB,kBAAA,2CAAAlB,EAAA,CAAA2B,WAAA,OAAAnB,MAAA,CAAAoD,SAAA,8BAA2D;;;;;;IATjE5D,EAFJ,CAAAC,cAAA,cAAqE,yBACT,gBAC7C;IAAAD,EAAA,CAAAa,MAAA,sFAAc;IAAAb,EAAA,CAAAc,YAAA,EAAY;IACrCd,EAAA,CAAAC,cAAA,gBAImC;IAF5BD,EAAA,CAAA6D,gBAAA,2BAAAC,yDAAAC,MAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiE,kBAAA,CAAAzD,MAAA,CAAA0D,UAAA,EAAAH,MAAA,MAAAvD,MAAA,CAAA0D,UAAA,GAAAH,MAAA;MAAA,OAAA/D,EAAA,CAAAU,WAAA,CAAAqD,MAAA;IAAA,EAAwB;IAExB/D,EAAA,CAAAE,UAAA,mBAAAiE,iDAAA;MAAAnE,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4D,eAAA,EAAiB;IAAA,EAAC;IAJlCpE,EAAA,CAAAc,YAAA,EAImC;IACnCd,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IACtBb,EADsB,CAAAc,YAAA,EAAO,EACZ;IACjBd,EAAA,CAAA6C,UAAA,IAAAwB,+BAAA,kBAAqD;IAGvDrE,EAAA,CAAAc,YAAA,EAAM;;;;IARKd,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAsE,gBAAA,YAAA9D,MAAA,CAAA0D,UAAA,CAAwB;IAKLlE,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAoD,SAAA,SAAuB;;;;;;IAtBrD5D,EADF,CAAAC,cAAA,cAA0D,cAC3B;IAC3BD,EAAA,CAAA6C,UAAA,IAAA0B,4BAAA,qBAKsB;IAIxBvE,EAAA,CAAAc,YAAA,EAAM;IAENd,EAAA,CAAA6C,UAAA,IAAA2B,yBAAA,kBAAqE;IAiBnExE,EADF,CAAAC,cAAA,cAA4B,iBAMH;IAFrBD,EAAA,CAAAE,UAAA,mBAAAuE,4CAAA;MAAAzE,EAAA,CAAAK,aAAA,CAAAqE,IAAA;MAAA,MAAAlE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmE,YAAA,EAAc;IAAA,EAAC;IAGxB3E,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,mBAAY;IAAAb,EAAA,CAAAc,YAAA,EAAW;IACjCd,EAAA,CAAAa,MAAA,sEACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAGoB;IADlBD,EAAA,CAAAE,UAAA,mBAAA0E,4CAAA;MAAA5E,EAAA,CAAAK,aAAA,CAAAqE,IAAA;MAAA,MAAAlE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqE,SAAA,EAAW;IAAA,EAAC;IAErB7E,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAa,MAAA,aAAK;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAC1Bd,EAAA,CAAAa,MAAA,6EACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;;;;IA3CmBd,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA6B,UAAA,YAAArB,MAAA,CAAAsE,cAAA,CAAiB;IASX9E,EAAA,CAAAiB,SAAA,EAAsC;IAAtCjB,EAAA,CAAA6B,UAAA,SAAArB,MAAA,CAAAkD,qBAAA,YAAsC;IAqB/D1D,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAA6B,UAAA,cAAArB,MAAA,CAAAuE,eAAA,GAA+B;;;;;IAkBzC/E,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAgF,SAAA,sBAAyC;IACzChF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,kHAAsB;IAC3Bb,EAD2B,CAAAc,YAAA,EAAI,EACzB;;;ADnKR,WAAamE,GAAG;EAAV,MAAOA,GAAG;IAwBJC,IAAA;IACAC,QAAA;IAxBVC,WAAW,GAAQ,IAAI;IACvBC,UAAU,GAAW,EAAE;IACvBrE,gBAAgB,GAAkB,IAAI;IACtCsE,gBAAgB,GAAkB,IAAI;IACtC5B,qBAAqB,GAAW,MAAM;IACtCQ,UAAU,GAAW,CAAC;IACtBlB,QAAQ,GAAW,CAAC;IACpBG,OAAO,GAAW,EAAE,CAAC,CAAC;IACtBoC,YAAY,GAAY,KAAK;IAE7BC,QAAQ,GAAc,EAAE;IACxBC,gBAAgB,GAAc,EAAE;IAChCC,UAAU,GAAe,EAAE;IAC3BC,SAAS,GAAe,EAAE;IAC1B5C,SAAS,GAAe,EAAE;IAE1B+B,cAAc,GAAoB,CAChC;MAAElE,EAAE,EAAE,MAAM;MAAEO,MAAM,EAAE,MAAM;MAAEwC,IAAI,EAAE;IAAO,CAAE,EAC7C;MAAE/C,EAAE,EAAE,MAAM;MAAEO,MAAM,EAAE,OAAO;MAAEwC,IAAI,EAAE;IAAa,CAAE,EACpD;MAAE/C,EAAE,EAAE,UAAU;MAAEO,MAAM,EAAE,OAAO;MAAEwC,IAAI,EAAE;IAAiB,CAAE,CAC7D;IAEDiC,YACUV,IAAgB,EAChBC,QAAqB;MADrB,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,QAAQ,GAARA,QAAQ;IACf;IAEHU,QAAQA,CAAA;MACN,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,aAAa,EAAE;IACtB;IAEAH,eAAeA,CAAA;MACb,MAAMI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACpD,IAAIF,QAAQ,EAAE;QACZ,IAAI,CAACd,WAAW,GAAGiB,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;MACzC;IACF;IAEAH,YAAYA,CAAA;MACV,IAAI,CAACb,IAAI,CAACqB,GAAG,CAAM,2CAA2C,CAAC,CAACC,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,QAAQ,GAAGkB,QAAQ,CAAClB,QAAQ,IAAI,EAAE;UACvC,IAAI,CAACC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;QAC5C,CAAC;QACDmB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,IAAI,CAACE,WAAW,CAAC,uBAAuB,EAAE,IAAI,CAAC;QACjD;OACD,CAAC;IACJ;IAEAb,cAAcA,CAAA;MACZ,IAAI,CAACd,IAAI,CAACqB,GAAG,CAAM,6CAA6C,CAAC,CAACC,SAAS,CAAC;QAC1EC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAChB,UAAU,GAAGgB,QAAQ,CAAChB,UAAU,IAAI,EAAE;QAC7C,CAAC;QACDiB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;OACD,CAAC;IACJ;IAEAV,aAAaA,CAAA;MACX,IAAI,CAACf,IAAI,CAACqB,GAAG,CAAM,4CAA4C,CAAC,CAACC,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACf,SAAS,GAAGe,QAAQ,CAACf,SAAS,IAAI,EAAE;QAC3C,CAAC;QACDgB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;IACJ;IAEAG,cAAcA,CAAA;MACZ,OAAO,IAAIC,IAAI,EAAE,CAACC,kBAAkB,CAAC,OAAO,CAAC;IAC/C;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAACxB,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAAC0B,MAAM,CAACxE,OAAO,IAClDA,OAAO,CAACvB,MAAM,CAACgG,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/B,UAAU,CAAC8B,WAAW,EAAE,CAAC,IACpEzE,OAAO,CAAChB,WAAW,CAACyF,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/B,UAAU,CAAC8B,WAAW,EAAE,CAAC,CAC1E;MAED,IAAI,IAAI,CAACnG,gBAAgB,EAAE;QACzB,IAAI,CAACyE,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACyB,MAAM,CAACxE,OAAO,IAC1DA,OAAO,CAAC2E,UAAU,KAAK,IAAI,CAACrG,gBAAgB,CAC7C;MACH;IACF;IAEAL,cAAcA,CAAC0G,UAAkB;MAC/B,IAAI,CAACrG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,KAAKqG,UAAU,GAAG,IAAI,GAAGA,UAAU;MAChF,IAAI,CAACJ,cAAc,EAAE;IACvB;IAEA1F,SAASA,CAACmB,OAAgB;MACxB,IAAIA,OAAO,CAAClB,KAAK,IAAI,CAAC,EAAE;QACtB,IAAI,CAACqF,WAAW,CAAC,iCAAiC,EAAE,IAAI,CAAC;QACzD;MACF;MAEA,MAAMS,YAAY,GAAG,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9E,OAAO,CAAC9B,EAAE,KAAK8B,OAAO,CAAC9B,EAAE,CAAC;MAEhF,IAAI0G,YAAY,EAAE;QAChB,IAAIA,YAAY,CAAC3E,QAAQ,GAAGD,OAAO,CAAClB,KAAK,EAAE;UACzC8F,YAAY,CAAC3E,QAAQ,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACkE,WAAW,CAAC,+CAA+C,EAAE,IAAI,CAAC;QACzE;MACF,CAAC,MAAM;QACL,IAAI,CAAC9D,SAAS,CAAC0E,IAAI,CAAC;UAAE/E,OAAO;UAAEC,QAAQ,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF;IAEAH,cAAcA,CAACL,KAAa;MAC1B,IAAI,CAACY,SAAS,CAAC2E,MAAM,CAACvF,KAAK,EAAE,CAAC,CAAC;IACjC;IAEAG,gBAAgBA,CAACH,KAAa;MAC5B,MAAMqF,IAAI,GAAG,IAAI,CAACzE,SAAS,CAACZ,KAAK,CAAC;MAClC,IAAIqF,IAAI,CAAC7E,QAAQ,GAAG6E,IAAI,CAAC9E,OAAO,CAAClB,KAAK,EAAE;QACtCgG,IAAI,CAAC7E,QAAQ,EAAE;MACjB,CAAC,MAAM;QACL,IAAI,CAACkE,WAAW,CAAC,+CAA+C,EAAE,IAAI,CAAC;MACzE;IACF;IAEAzE,gBAAgBA,CAACD,KAAa;MAC5B,MAAMqF,IAAI,GAAG,IAAI,CAACzE,SAAS,CAACZ,KAAK,CAAC;MAClC,IAAIqF,IAAI,CAAC7E,QAAQ,GAAG,CAAC,EAAE;QACrB6E,IAAI,CAAC7E,QAAQ,EAAE;MACjB;IACF;IAEAC,YAAYA,CAAC4E,IAAc;MACzB,OAAOA,IAAI,CAAC9E,OAAO,CAACd,KAAK,GAAG4F,IAAI,CAAC7E,QAAQ;IAC3C;IAEAO,WAAWA,CAAA;MACT,OAAO,IAAI,CAACH,SAAS,CAAC4E,MAAM,CAAC,CAACC,KAAK,EAAEJ,IAAI,KAAKI,KAAK,GAAG,IAAI,CAAChF,YAAY,CAAC4E,IAAI,CAAC,EAAE,CAAC,CAAC;IACnF;IAEApE,MAAMA,CAAA;MACJ,OAAO,IAAI,CAACF,WAAW,EAAE,IAAI,IAAI,CAACC,OAAO,GAAG,GAAG,CAAC;IAClD;IAEAE,QAAQA,CAAA;MACN,OAAO,IAAI,CAACH,WAAW,EAAE,GAAG,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAACJ,QAAQ;IAC3D;IAEAY,SAASA,CAAA;MACP,OAAO,IAAI,CAACM,UAAU,GAAG,IAAI,CAACb,QAAQ,EAAE;IAC1C;IAEAI,mBAAmBA,CAACoE,QAAgB;MAClC,IAAI,CAACnE,qBAAqB,GAAGmE,QAAQ;MACrC,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACvB,IAAI,CAAC3D,UAAU,GAAG,IAAI,CAACb,QAAQ,EAAE;MACnC;IACF;IAEAe,eAAeA,CAAA;MACb;IAAA;IAGFW,eAAeA,CAAA;MACb,IAAI,IAAI,CAAChC,SAAS,CAAC+E,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7C,IAAI,IAAI,CAACpE,qBAAqB,KAAK,MAAM,EAAE;QACzC,OAAO,IAAI,CAACQ,UAAU,IAAI,IAAI,CAACb,QAAQ,EAAE;MAC3C;MACA,OAAO,IAAI;IACb;IAEAsB,YAAYA,CAAA;MACV,IAAI,CAAC,IAAI,CAACI,eAAe,EAAE,EAAE;MAE7B,IAAI,CAACQ,YAAY,GAAG,IAAI;MAExB,MAAMwC,QAAQ,GAAG;QACfC,UAAU,EAAE,IAAI,CAAC1C,gBAAgB;QACjC2C,KAAK,EAAE,IAAI,CAAClF,SAAS,CAACmF,GAAG,CAACV,IAAI,KAAK;UACjCW,SAAS,EAAEX,IAAI,CAAC9E,OAAO,CAAC9B,EAAE;UAC1B+B,QAAQ,EAAE6E,IAAI,CAAC7E,QAAQ;UACvBf,KAAK,EAAE4F,IAAI,CAAC9E,OAAO,CAACd;SACrB,CAAC,CAAC;QACHwG,QAAQ,EAAE,IAAI,CAAClF,WAAW,EAAE;QAC5BmF,GAAG,EAAE,IAAI,CAACjF,MAAM,EAAE;QAClBJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB4E,KAAK,EAAE,IAAI,CAACvE,QAAQ,EAAE;QACtBiF,aAAa,EAAE,IAAI,CAAC5E,qBAAqB;QACzCQ,UAAU,EAAE,IAAI,CAACA;OAClB;MAED;MACAqE,UAAU,CAAC,MAAK;QACd,IAAI,CAAChD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACsB,WAAW,CAAC,sBAAsB,CAAC;QACxC,IAAI,CAAChC,SAAS,EAAE;QAChB,IAAI,CAAC2D,YAAY,CAACT,QAAQ,CAAC;MAC7B,CAAC,EAAE,IAAI,CAAC;IACV;IAEAlD,SAASA,CAAA;MACP,IAAI,CAAC9B,SAAS,GAAG,EAAE;MACnB,IAAI,CAACuC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACpB,UAAU,GAAG,CAAC;MACnB,IAAI,CAAClB,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACU,qBAAqB,GAAG,MAAM;IACrC;IAEA+E,OAAOA,CAAA;MACL,IAAI,CAAC5D,SAAS,EAAE;MAChB,IAAI,CAACgC,WAAW,CAAC,4BAA4B,CAAC;IAChD;IAEA6B,UAAUA,CAAA;MACR,IAAI,CAAC7B,WAAW,CAAC,oBAAoB,CAAC;IACxC;IAEA8B,WAAWA,CAAA;MACT,IAAI,CAAC9B,WAAW,CAAC,4BAA4B,CAAC;IAChD;IAEA+B,cAAcA,CAAA;MACZ,IAAI,CAAC/B,WAAW,CAAC,+BAA+B,CAAC;IACnD;IAEAgC,gBAAgBA,CAAA;MACd,IAAI,CAAChC,WAAW,CAAC,8BAA8B,CAAC;IAClD;IAEAiC,gBAAgBA,CAAA;MACd,IAAI,CAACjC,WAAW,CAAC,gCAAgC,CAAC;IACpD;IAEAkC,QAAQA,CAAA;MACN,IAAI,CAAClC,WAAW,CAAC,2BAA2B,CAAC;IAC/C;IAEA2B,YAAYA,CAACT,QAAa;MACxBnB,OAAO,CAACoC,GAAG,CAAC,mBAAmB,EAAEjB,QAAQ,CAAC;MAC1C,IAAI,CAAClB,WAAW,CAAC,mBAAmB,CAAC;IACvC;IAEQA,WAAWA,CAACoC,OAAe,EAAEC,OAAO,GAAG,KAAK;MAClD,IAAI,CAAC/D,QAAQ,CAACgE,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;QACnCG,QAAQ,EAAE,IAAI;QACdC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE,KAAK;QACvBC,UAAU,EAAEL,OAAO,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB;OAC/D,CAAC;IACJ;;uCA/PWjE,GAAG,EAAAjF,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;;YAAH3E,GAAG;MAAA4E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,aAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC9DNnK,EANV,CAAAC,cAAA,aAA2B,aAED,aACM,aACC,SACrB,kBACyB;UAAAD,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAChDd,EAAA,CAAAa,MAAA,gEACF;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACLd,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAa,MAAA,GAA6E;UAClFb,EADkF,CAAAc,YAAA,EAAI,EAChF;UAEJd,EADF,CAAAC,cAAA,cAA4B,iBAC8D;UAA9CD,EAAA,CAAAE,UAAA,mBAAAmK,sCAAA;YAAArK,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAA3B,OAAA,EAAS;UAAA,EAAC;UAC3DzI,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACtCd,EAAA,CAAAa,MAAA,qDACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAAkD;UAAvBD,EAAA,CAAAE,UAAA,mBAAAqK,sCAAA;YAAAvK,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAA1B,UAAA,EAAY;UAAA,EAAC;UAC/C1I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,8BAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC3Cd,EAAA,CAAAa,MAAA,2DACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAEPd,EADF,CAAAC,cAAA,kBAAsD,gBAC1C;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UACrBb,EADqB,CAAAc,YAAA,EAAW,EACvB;UAEPd,EADF,CAAAC,cAAA,yBAA6B,kBACwB;UAA7BD,EAAA,CAAAE,UAAA,mBAAAsK,sCAAA;YAAAxK,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAAvB,gBAAA,EAAkB;UAAA,EAAC;UAChD7I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC5Bd,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,uFAAc;UACtBb,EADsB,CAAAc,YAAA,EAAO,EACpB;UACTd,EAAA,CAAAC,cAAA,kBAAmD;UAA7BD,EAAA,CAAAE,UAAA,mBAAAuK,sCAAA;YAAAzK,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAAtB,gBAAA,EAAkB;UAAA,EAAC;UAChD9I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC1Bd,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,8FAAgB;UACxBb,EADwB,CAAAc,YAAA,EAAO,EACtB;UACTd,EAAA,CAAAC,cAAA,kBAA2C;UAArBD,EAAA,CAAAE,UAAA,mBAAAwK,sCAAA;YAAA1K,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAArB,QAAA,EAAU;UAAA,EAAC;UACxC/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAC/Bd,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,qEAAW;UAK3Bb,EAL2B,CAAAc,YAAA,EAAO,EACjB,EACA,EACP,EACF,EACF;UASEd,EANR,CAAAC,cAAA,eAAsB,eAEQ,eAEE,0BACgC,iBAC7C;UAAAD,EAAA,CAAAa,MAAA,oGAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,oBAIoB;UAHbD,EAAA,CAAA6D,gBAAA,2BAAA8G,6CAAA5G,MAAA;YAAA/D,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAAtK,EAAA,CAAAiE,kBAAA,CAAAmG,GAAA,CAAA/E,UAAA,EAAAtB,MAAA,MAAAqG,GAAA,CAAA/E,UAAA,GAAAtB,MAAA;YAAA,OAAA/D,EAAA,CAAAU,WAAA,CAAAqD,MAAA;UAAA,EAAwB;UACxB/D,EAAA,CAAAE,UAAA,mBAAA0K,qCAAA;YAAA5K,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAAnD,cAAA,EAAgB;UAAA,EAAC;UAFjCjH,EAAA,CAAAc,YAAA,EAIoB;UACpBd,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACrCd,EAAA,CAAAC,cAAA,kBAAoF;UAAlDD,EAAA,CAAAE,UAAA,mBAAA2K,sCAAA;YAAA7K,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAAzB,WAAA,EAAa;UAAA,EAAC;UACvD3I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,uBAAe;UAG/Bb,EAH+B,CAAAc,YAAA,EAAW,EAC7B,EACM,EACb;UAKFd,EAFJ,CAAAC,cAAA,eAAgC,eACF,oBACZ;UACZD,EAAA,CAAA6C,UAAA,KAAAiI,wBAAA,uBAGwC;UAK9C9K,EAFI,CAAAc,YAAA,EAAe,EACX,EACF;UAGNd,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA6C,UAAA,KAAAkI,mBAAA,oBAI4C;UAchD/K,EADE,CAAAc,YAAA,EAAM,EACF;UAOAd,EAJN,CAAAC,cAAA,eAAwB,eAEQ,0BACgC,iBAC/C;UAAAD,EAAA,CAAAa,MAAA,4CAAM;UAAAb,EAAA,CAAAc,YAAA,EAAY;UAC7Bd,EAAA,CAAAC,cAAA,sBAA2C;UAA/BD,EAAA,CAAA6D,gBAAA,2BAAAmH,kDAAAjH,MAAA;YAAA/D,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAAtK,EAAA,CAAAiE,kBAAA,CAAAmG,GAAA,CAAA9E,gBAAA,EAAAvB,MAAA,MAAAqG,GAAA,CAAA9E,gBAAA,GAAAvB,MAAA;YAAA,OAAA/D,EAAA,CAAAU,WAAA,CAAAqD,MAAA;UAAA,EAA8B;UACxC/D,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAa,MAAA,yDAAS;UAAAb,EAAA,CAAAc,YAAA,EAAa;UAC3Cd,EAAA,CAAA6C,UAAA,KAAAoI,0BAAA,yBAAqE;UAGvEjL,EAAA,CAAAc,YAAA,EAAa;UACbd,EAAA,CAAAC,cAAA,kBAA0F;UAAxDD,EAAA,CAAAE,UAAA,mBAAAgL,sCAAA;YAAAlL,EAAA,CAAAK,aAAA,CAAAiK,GAAA;YAAA,OAAAtK,EAAA,CAAAU,WAAA,CAAS0J,GAAA,CAAAxB,cAAA,EAAgB;UAAA,EAAC;UAC1D5I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAa,MAAA,kBAAU;UAG1Bb,EAH0B,CAAAc,YAAA,EAAW,EACxB,EACM,EACb;UAKFd,EAFJ,CAAAC,cAAA,eAA0B,eACC,UACnB;UAAAD,EAAA,CAAAa,MAAA,uFAAc;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvBd,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAa,MAAA,IAA2B;UACvDb,EADuD,CAAAc,YAAA,EAAO,EACxD;UA0BNd,EAxBA,CAAA6C,UAAA,KAAAsI,mBAAA,kBAAqE,KAAAC,2BAAA,gCAAApL,EAAA,CAAAqL,sBAAA,CAwB7C;UAO1BrL,EAAA,CAAAc,YAAA,EAAM;UAuBNd,EApBA,CAAA6C,UAAA,KAAAyI,mBAAA,oBAAyD,KAAAC,mBAAA,mBAoBC;UAiD9DvL,EADE,CAAAc,YAAA,EAAM,EACF;UAGNd,EAAA,CAAA6C,UAAA,KAAA2I,mBAAA,kBAAkD;UAIpDxL,EAAA,CAAAc,YAAA,EAAM;;;;;UA/NKd,EAAA,CAAAiB,SAAA,GAA6E;UAA7EjB,EAAA,CAAAyL,kBAAA,MAAArB,GAAA,CAAAhF,WAAA,kBAAAgF,GAAA,CAAAhF,WAAA,CAAAsG,MAAA,kBAAAtB,GAAA,CAAAhF,WAAA,CAAAsG,MAAA,CAAAvK,MAAA,yFAAAiJ,GAAA,CAAAtD,cAAA,GAA6E;UAGlB9G,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAA6B,UAAA,aAAAuI,GAAA,CAAA7E,YAAA,CAAyB;UAQ/DvF,EAAA,CAAAiB,SAAA,GAA6B;UAA7BjB,EAAA,CAAA6B,UAAA,sBAAA8J,WAAA,CAA6B;UA8B5C3L,EAAA,CAAAiB,SAAA,IAAwB;UAAxBjB,EAAA,CAAAsE,gBAAA,YAAA8F,GAAA,CAAA/E,UAAA,CAAwB;UAgBNrF,EAAA,CAAAiB,SAAA,IAAa;UAAbjB,EAAA,CAAA6B,UAAA,YAAAuI,GAAA,CAAA1E,UAAA,CAAa;UAYlB1F,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAA6B,UAAA,YAAAuI,GAAA,CAAA3E,gBAAA,CAAmB;UAyB3BzF,EAAA,CAAAiB,SAAA,GAA8B;UAA9BjB,EAAA,CAAAsE,gBAAA,YAAA8F,GAAA,CAAA9E,gBAAA,CAA8B;UAEPtF,EAAA,CAAAiB,SAAA,GAAY;UAAZjB,EAAA,CAAA6B,UAAA,YAAAuI,GAAA,CAAAzE,SAAA,CAAY;UAcrB3F,EAAA,CAAAiB,SAAA,GAA2B;UAA3BjB,EAAA,CAAAkB,kBAAA,KAAAkJ,GAAA,CAAArH,SAAA,CAAA+E,MAAA,8BAA2B;UAG9B9H,EAAA,CAAAiB,SAAA,EAA4B;UAAAjB,EAA5B,CAAA6B,UAAA,SAAAuI,GAAA,CAAArH,SAAA,CAAA+E,MAAA,KAA4B,aAAA8D,aAAA,CAAc;UAkCxC5L,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAA6B,UAAA,SAAAuI,GAAA,CAAArH,SAAA,CAAA+E,MAAA,KAA0B;UAoBzB9H,EAAA,CAAAiB,SAAA,EAA0B;UAA1BjB,EAAA,CAAA6B,UAAA,SAAAuI,GAAA,CAAArH,SAAA,CAAA+E,MAAA,KAA0B;UAoDtD9H,EAAA,CAAAiB,SAAA,EAAkB;UAAlBjB,EAAA,CAAA6B,UAAA,SAAAuI,GAAA,CAAA7E,YAAA,CAAkB;;;qBDjLtBpG,YAAY,EAAA0M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,YAAA,EACZ5M,WAAW,EAAA6M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EACXhN,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EAAA+M,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfhN,aAAa,EAAAiN,EAAA,CAAAC,OAAA,EACbjN,cAAc,EAAAkN,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAH,EAAA,CAAAI,SAAA,EACdvN,eAAe,EAAAwN,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,SAAA,EACfzN,aAAa,EAAA0N,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAG,cAAA,EACb5N,cAAc,EAAA6N,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,UAAA,EACd9N,wBAAwB,EAAA+N,GAAA,CAAAC,kBAAA,EACxB/N,gBAAgB,EAAAgO,GAAA,CAAAC,UAAA,EAChBhO,iBAAiB;MAAAiO,MAAA;IAAA;;SAKR/I,GAAG;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}