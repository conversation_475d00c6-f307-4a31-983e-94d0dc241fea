using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// وحدات القياس
    /// </summary>
    public class Unit : BaseEntity
    {
        /// <summary>
        /// اسم الوحدة بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الوحدة بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// الرمز المختصر للوحدة
        /// </summary>
        [Required]
        [MaxLength(10)]
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// نوع الوحدة (طول، وزن، حجم، كمية)
        /// </summary>
        public UnitType UnitType { get; set; }

        /// <summary>
        /// الوحدة الأساسية (للتحويل)
        /// </summary>
        public int? BaseUnitId { get; set; }

        /// <summary>
        /// معامل التحويل إلى الوحدة الأساسية
        /// </summary>
        public decimal ConversionFactor { get; set; } = 1;

        /// <summary>
        /// وصف الوحدة
        /// </summary>
        [MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// هل الوحدة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الوحدة افتراضية لنوعها
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// عدد الخانات العشرية المسموحة
        /// </summary>
        public int DecimalPlaces { get; set; } = 2;

        // Navigation Properties
        public virtual Unit? BaseUnit { get; set; }
        public virtual ICollection<Unit> DerivedUnits { get; set; } = new List<Unit>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    /// <summary>
    /// أنواع وحدات القياس
    /// </summary>
    public enum UnitType
    {
        /// <summary>
        /// كمية (قطعة، عدد)
        /// </summary>
        Quantity = 1,

        /// <summary>
        /// وزن (كيلو، جرام)
        /// </summary>
        Weight = 2,

        /// <summary>
        /// طول (متر، سنتيمتر)
        /// </summary>
        Length = 3,

        /// <summary>
        /// مساحة (متر مربع)
        /// </summary>
        Area = 4,

        /// <summary>
        /// حجم (لتر، متر مكعب)
        /// </summary>
        Volume = 5,

        /// <summary>
        /// وقت (ساعة، يوم)
        /// </summary>
        Time = 6
    }
}
