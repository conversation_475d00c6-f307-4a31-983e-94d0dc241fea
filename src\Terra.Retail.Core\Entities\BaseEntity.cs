using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الكيان الأساسي لجميع الجداول
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int? CreatedById { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل
        /// </summary>
        public int? UpdatedById { get; set; }

        /// <summary>
        /// هل السجل محذوف (حذف ناعم)
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// تاريخ الحذف
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي حذف السجل
        /// </summary>
        public int? DeletedById { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// رقم الإصدار للتحكم في التزامن
        /// </summary>
        [Timestamp]
        public byte[]? RowVersion { get; set; }
    }
}
