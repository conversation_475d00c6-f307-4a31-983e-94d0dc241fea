using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Dapper;
using System.Data;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SimpleController : ControllerBase
    {
        private readonly string _connectionString = "Server=localhost;Database=TerraRetailERP;User Id=sa;Password=***********;TrustServerCertificate=true;";
        /// <summary>
        /// اختبار بسيط للـ API
        /// </summary>
        [HttpGet("test")]
        public ActionResult Test()
        {
            return Ok(new {
                message = "API يعمل بنجاح",
                timestamp = DateTime.Now,
                status = "success",
                arabic = "النص العربي يعمل بشكل صحيح"
            });
        }

        /// <summary>
        /// بيانات تجريبية للمحافظات المصرية
        /// </summary>
        [HttpGet("areas")]
        public ActionResult GetAreas()
        {
            var areas = new[]
            {
                new { id = 1, nameAr = "القاهرة", nameEn = "Cairo" },
                new { id = 2, nameAr = "الجيزة", nameEn = "Giza" },
                new { id = 3, nameAr = "الإسكندرية", nameEn = "Alexandria" },
                new { id = 4, nameAr = "الدقهلية", nameEn = "Dakahlia" },
                new { id = 5, nameAr = "الشرقية", nameEn = "Sharqia" }
            };

            return Ok(new {
                count = areas.Length,
                areas = areas,
                message = "تم تحميل المحافظات بنجاح"
            });
        }

        /// <summary>
        /// بيانات تجريبية للفروع المصرية
        /// </summary>
        [HttpGet("branches")]
        public ActionResult GetBranches()
        {
            var branches = new[]
            {
                new { id = 1, nameAr = "الفرع الرئيسي - القاهرة", nameEn = "Main Branch - Cairo", isMainBranch = true },
                new { id = 2, nameAr = "فرع الإسكندرية", nameEn = "Alexandria Branch", isMainBranch = false },
                new { id = 3, nameAr = "فرع الجيزة", nameEn = "Giza Branch", isMainBranch = false },
                new { id = 4, nameAr = "فرع المنصورة", nameEn = "Mansoura Branch", isMainBranch = false }
            };

            return Ok(new {
                count = branches.Length,
                branches = branches,
                message = "تم تحميل الفروع بنجاح"
            });
        }

        /// <summary>
        /// الحصول على العملاء من قاعدة البيانات
        /// </summary>
        [HttpGet("customers")]
        public async Task<ActionResult> GetCustomers()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT TOP 25
                        c.Id,
                        c.NameAr,
                        c.CustomerCode,
                        c.Phone,
                        c.Email,
                        c.Address,
                        ct.NameAr as CustomerTypeName,
                        a.NameAr as AreaName
                    FROM Customers c
                    LEFT JOIN CustomerTypes ct ON c.CustomerTypeId = ct.Id
                    LEFT JOIN Areas a ON c.AreaId = a.Id
                    WHERE c.IsActive = 1
                    ORDER BY c.Id";

                var customers = await connection.QueryAsync(query);

                if (!customers.Any())
                {
                    // إذا لم توجد بيانات في قاعدة البيانات، نرجع بيانات افتراضية
                    var fallbackCustomers = new[]
                    {
                        new {
                            id = 1,
                            nameAr = "أحمد محمد علي السيد",
                            customerCode = "C001",
                            phone = "01012345678",
                            email = "<EMAIL>",
                            address = "شارع التحرير، وسط البلد، القاهرة",
                            customerTypeName = "عميل عادي",
                            areaName = "القاهرة"
                        },
                        new {
                            id = 2,
                            nameAr = "فاطمة حسن إبراهيم محمد",
                            customerCode = "C002",
                            phone = "01123456789",
                            email = "<EMAIL>",
                            address = "مدينة نصر، القاهرة",
                            customerTypeName = "عميل جملة",
                            areaName = "القاهرة"
                        },
                        new {
                            id = 3,
                            nameAr = "محمود عبدالله أحمد حسن",
                            customerCode = "C003",
                            phone = "01234567890",
                            email = "<EMAIL>",
                            address = "مصر الجديدة، القاهرة",
                            customerTypeName = "عميل VIP",
                            areaName = "القاهرة"
                        }
                    };

                    return Ok(new {
                        count = fallbackCustomers.Length,
                        customers = fallbackCustomers,
                        message = "تم تحميل العملاء (بيانات افتراضية)",
                        source = "fallback"
                    });
                }

                return Ok(new {
                    count = customers.Count(),
                    customers = customers,
                    message = "تم تحميل العملاء من قاعدة البيانات",
                    source = "database"
                });
            }
            catch (Exception ex)
            {
                // في حالة خطأ في قاعدة البيانات، نرجع بيانات افتراضية
                var fallbackCustomers = new[]
                {
                    new {
                        id = 1,
                        nameAr = "أحمد محمد علي السيد",
                        customerCode = "C001",
                        phone = "01012345678",
                        email = "<EMAIL>",
                        address = "شارع التحرير، وسط البلد، القاهرة",
                        customerTypeName = "عميل عادي",
                        areaName = "القاهرة"
                    },
                    new {
                        id = 2,
                        nameAr = "فاطمة حسن إبراهيم محمد",
                        customerCode = "C002",
                        phone = "01123456789",
                        email = "<EMAIL>",
                        address = "مدينة نصر، القاهرة",
                        customerTypeName = "عميل جملة",
                        areaName = "القاهرة"
                    }
                };

                return Ok(new {
                    count = fallbackCustomers.Length,
                    customers = fallbackCustomers,
                    message = "تم تحميل العملاء (بيانات افتراضية - خطأ في قاعدة البيانات)",
                    source = "fallback",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// بيانات تجريبية لأنواع العملاء
        /// </summary>
        [HttpGet("customer-types")]
        public ActionResult GetCustomerTypes()
        {
            var customerTypes = new[]
            {
                new { id = 1, nameAr = "عميل عادي", nameEn = "Regular Customer" },
                new { id = 2, nameAr = "عميل جملة", nameEn = "Wholesale Customer" },
                new { id = 3, nameAr = "عميل VIP", nameEn = "VIP Customer" },
                new { id = 4, nameAr = "عميل تاجر", nameEn = "Merchant Customer" },
                new { id = 5, nameAr = "عميل مؤسسة", nameEn = "Corporate Customer" }
            };

            return Ok(new {
                count = customerTypes.Length,
                customerTypes = customerTypes,
                message = "تم تحميل أنواع العملاء بنجاح"
            });
        }

        /// <summary>
        /// إحصائيات شاملة للبيانات العربية
        /// </summary>
        [HttpGet("statistics")]
        public ActionResult GetStatistics()
        {
            var stats = new
            {
                areasCount = 26,
                branchesCount = 8,
                customersCount = 24,
                customerTypesCount = 5,
                productsCount = 5,
                message = "تم تحميل الإحصائيات بنجاح",
                timestamp = DateTime.Now
            };

            return Ok(stats);
        }

        /// <summary>
        /// الحصول على المنتجات من قاعدة البيانات
        /// </summary>
        [HttpGet("products")]
        public async Task<ActionResult> GetProducts()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT TOP 50
                        p.Id,
                        p.NameAr,
                        p.NameEn,
                        p.ProductCode,
                        p.CategoryId,
                        c.NameAr as CategoryName,
                        p.UnitId,
                        u.NameAr as UnitName,
                        p.Price,
                        p.Stock,
                        p.IsActive
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    LEFT JOIN Units u ON p.UnitId = u.Id
                    WHERE p.IsActive = 1
                    ORDER BY p.Id";

                var products = await connection.QueryAsync(query);

                if (!products.Any())
                {
                    // إذا لم توجد بيانات في قاعدة البيانات، نرجع بيانات افتراضية
                    var fallbackProducts = new[]
                    {
                        new {
                            id = 1,
                            nameAr = "أرز مصري أبيض",
                            nameEn = "Egyptian White Rice",
                            productCode = "P001",
                            categoryId = 2,
                            categoryName = "مواد غذائية ومشروبات",
                            unitId = 14,
                            unitName = "كيلو",
                            price = 30.00,
                            stock = 150,
                            isActive = true
                        },
                        new {
                            id = 2,
                            nameAr = "سكر أبيض مصري",
                            nameEn = "Egyptian White Sugar",
                            productCode = "P002",
                            categoryId = 2,
                            categoryName = "مواد غذائية ومشروبات",
                            unitId = 14,
                            unitName = "كيلو",
                            price = 22.00,
                            stock = 200,
                            isActive = true
                        },
                        new {
                            id = 3,
                            nameAr = "شاي أحمد تي",
                            nameEn = "Ahmad Tea",
                            productCode = "P003",
                            categoryId = 2,
                            categoryName = "مواد غذائية ومشروبات",
                            unitId = 13,
                            unitName = "حبة",
                            price = 55.00,
                            stock = 80,
                            isActive = true
                        },
                        new {
                            id = 4,
                            nameAr = "زيت عباد الشمس",
                            nameEn = "Sunflower Oil",
                            productCode = "P004",
                            categoryId = 2,
                            categoryName = "مواد غذائية ومشروبات",
                            unitId = 17,
                            unitName = "لتر",
                            price = 42.00,
                            stock = 120,
                            isActive = true
                        },
                        new {
                            id = 5,
                            nameAr = "مكرونة مصرية",
                            nameEn = "Egyptian Pasta",
                            productCode = "P005",
                            categoryId = 2,
                            categoryName = "مواد غذائية ومشروبات",
                            unitId = 13,
                            unitName = "حبة",
                            price = 12.00,
                            stock = 300,
                            isActive = true
                        }
                    };

                    return Ok(new {
                        count = fallbackProducts.Length,
                        products = fallbackProducts,
                        message = "تم تحميل المنتجات (بيانات افتراضية)",
                        source = "fallback"
                    });
                }

                return Ok(new {
                    count = products.Count(),
                    products = products,
                    message = "تم تحميل المنتجات من قاعدة البيانات",
                    source = "database"
                });
            }
            catch (Exception ex)
            {
                // في حالة خطأ في قاعدة البيانات، نرجع بيانات افتراضية
                var fallbackProducts = new[]
                {
                    new {
                        id = 1,
                        nameAr = "أرز مصري أبيض",
                        nameEn = "Egyptian White Rice",
                        productCode = "P001",
                        categoryId = 2,
                        categoryName = "مواد غذائية ومشروبات",
                        unitId = 14,
                        unitName = "كيلو",
                        price = 30.00,
                        stock = 150,
                        isActive = true
                    },
                    new {
                        id = 2,
                        nameAr = "سكر أبيض مصري",
                        nameEn = "Egyptian White Sugar",
                        productCode = "P002",
                        categoryId = 2,
                        categoryName = "مواد غذائية ومشروبات",
                        unitId = 14,
                        unitName = "كيلو",
                        price = 22.00,
                        stock = 200,
                        isActive = true
                    }
                };

                return Ok(new {
                    count = fallbackProducts.Length,
                    products = fallbackProducts,
                    message = "تم تحميل المنتجات (بيانات افتراضية - خطأ في قاعدة البيانات)",
                    source = "fallback",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// الحصول على فئات المنتجات
        /// </summary>
        [HttpGet("categories")]
        public ActionResult GetCategories()
        {
            var categories = new[]
            {
                new { id = 1, nameAr = "عام", nameEn = "General", code = "GEN" },
                new { id = 2, nameAr = "مواد غذائية ومشروبات", nameEn = "Food & Beverages", code = "FOOD" },
                new { id = 3, nameAr = "أجهزة كهربائية", nameEn = "Electronics", code = "ELEC" },
                new { id = 4, nameAr = "ملابس وأحذية", nameEn = "Clothing & Shoes", code = "CLOTH" },
                new { id = 5, nameAr = "أدوات منزلية", nameEn = "Home & Kitchen", code = "HOME" }
            };

            return Ok(new {
                count = categories.Length,
                categories = categories,
                message = "تم تحميل فئات المنتجات بنجاح"
            });
        }

        /// <summary>
        /// الحصول على وحدات القياس من قاعدة البيانات
        /// </summary>
        [HttpGet("units")]
        public async Task<ActionResult> GetUnits()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT
                        Id,
                        NameAr,
                        NameEn,
                        Symbol,
                        IsActive
                    FROM Units
                    WHERE IsActive = 1
                    ORDER BY NameAr";

                var units = await connection.QueryAsync(query);

                if (!units.Any())
                {
                    // إذا لم توجد بيانات، نضيف الوحدات الأساسية
                    await SeedUnitsData(connection);
                    units = await connection.QueryAsync(query);
                }

                return Ok(new {
                    count = units.Count(),
                    units = units,
                    message = "تم تحميل وحدات القياس بنجاح",
                    source = "database"
                });
            }
            catch (Exception ex)
            {
                var fallbackUnits = new[]
                {
                    new { id = 1, nameAr = "حبة", nameEn = "Piece", symbol = "حبة", isActive = true },
                    new { id = 2, nameAr = "كيلو", nameEn = "Kilogram", symbol = "كجم", isActive = true },
                    new { id = 3, nameAr = "جرام", nameEn = "Gram", symbol = "جم", isActive = true },
                    new { id = 4, nameAr = "متر", nameEn = "Meter", symbol = "م", isActive = true },
                    new { id = 5, nameAr = "لتر", nameEn = "Liter", symbol = "لتر", isActive = true },
                    new { id = 6, nameAr = "قطعة", nameEn = "Unit", symbol = "قطعة", isActive = true }
                };

                return Ok(new {
                    count = fallbackUnits.Length,
                    units = fallbackUnits,
                    message = "تم تحميل وحدات القياس (بيانات افتراضية)",
                    source = "fallback",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// إضافة الوحدات الأساسية لقاعدة البيانات
        /// </summary>
        private async Task SeedUnitsData(SqlConnection connection)
        {
            var insertQuery = @"
                INSERT INTO Units (NameAr, NameEn, Symbol, IsActive, CreatedDate)
                VALUES
                    ('حبة', 'Piece', 'حبة', 1, GETDATE()),
                    ('كيلو', 'Kilogram', 'كجم', 1, GETDATE()),
                    ('جرام', 'Gram', 'جم', 1, GETDATE()),
                    ('متر', 'Meter', 'م', 1, GETDATE()),
                    ('لتر', 'Liter', 'لتر', 1, GETDATE()),
                    ('قطعة', 'Unit', 'قطعة', 1, GETDATE()),
                    ('طن', 'Ton', 'طن', 1, GETDATE()),
                    ('سنتيمتر', 'Centimeter', 'سم', 1, GETDATE()),
                    ('مليلتر', 'Milliliter', 'مل', 1, GETDATE()),
                    ('عبوة', 'Package', 'عبوة', 1, GETDATE())";

            await connection.ExecuteAsync(insertQuery);
        }

        /// <summary>
        /// الحصول على طرق الدفع المصرية
        /// </summary>
        [HttpGet("payment-methods")]
        public ActionResult GetPaymentMethods()
        {
            var paymentMethods = new[]
            {
                new { id = 1, nameAr = "كاش", nameEn = "Cash", code = "CASH", paymentType = 1 },
                new { id = 2, nameAr = "فيزا", nameEn = "Visa Card", code = "VISA", paymentType = 2 },
                new { id = 3, nameAr = "ميزة", nameEn = "Meeza Card", code = "MEEZA", paymentType = 2 },
                new { id = 4, nameAr = "فودافون كاش", nameEn = "Vodafone Cash", code = "VFCASH", paymentType = 2 },
                new { id = 5, nameAr = "أورانج موني", nameEn = "Orange Money", code = "ORANGE", paymentType = 2 },
                new { id = 6, nameAr = "إتصالات فلوس", nameEn = "Etisalat Flous", code = "ETISALAT", paymentType = 2 },
                new { id = 7, nameAr = "فوري", nameEn = "Fawry", code = "FAWRY", paymentType = 2 },
                new { id = 8, nameAr = "تحويل بنكي", nameEn = "Bank Transfer", code = "TRANSFER", paymentType = 3 }
            };

            return Ok(new {
                count = paymentMethods.Length,
                paymentMethods = paymentMethods,
                message = "تم تحميل طرق الدفع بنجاح"
            });
        }

        /// <summary>
        /// لوحة التحكم - إحصائيات شاملة
        /// </summary>
        [HttpGet("dashboard")]
        public ActionResult GetDashboard()
        {
            var dashboard = new
            {
                totalSales = 125000.50m,
                totalCustomers = 24,
                totalProducts = 5,
                totalOrders = 156,
                todaySales = 8500.00m,
                monthSales = 45000.00m,
                topProducts = new[]
                {
                    new { name = "أرز مصري أبيض", sales = 15000.00m, quantity = 500 },
                    new { name = "سكر أبيض مصري", sales = 12000.00m, quantity = 545 },
                    new { name = "شاي أحمد تي", sales = 8500.00m, quantity = 155 }
                },
                recentOrders = new[]
                {
                    new { id = 1, customerName = "أحمد محمد علي", amount = 250.00m, date = DateTime.Now.AddHours(-2) },
                    new { id = 2, customerName = "فاطمة حسن إبراهيم", amount = 180.00m, date = DateTime.Now.AddHours(-4) },
                    new { id = 3, customerName = "محمود عبدالله أحمد", amount = 320.00m, date = DateTime.Now.AddHours(-6) }
                },
                lowStockProducts = new[]
                {
                    new { name = "شاي أحمد تي", currentStock = 80, minStock = 100 },
                    new { name = "زيت عباد الشمس", currentStock = 120, minStock = 150 }
                },
                salesChart = new
                {
                    labels = new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" },
                    data = new[] { 25000, 30000, 28000, 35000, 40000, 45000 }
                },
                message = "تم تحميل بيانات لوحة التحكم بنجاح"
            };

            return Ok(dashboard);
        }

        /// <summary>
        /// الحصول على المبيعات
        /// </summary>
        [HttpGet("sales")]
        public ActionResult GetSales()
        {
            var sales = new[]
            {
                new {
                    id = 1,
                    invoiceNumber = "INV-001",
                    customerName = "أحمد محمد علي",
                    totalAmount = 250.00m,
                    date = DateTime.Now.AddDays(-1),
                    status = "مدفوعة",
                    paymentMethod = "كاش"
                },
                new {
                    id = 2,
                    invoiceNumber = "INV-002",
                    customerName = "فاطمة حسن إبراهيم",
                    totalAmount = 180.00m,
                    date = DateTime.Now.AddDays(-2),
                    status = "مدفوعة",
                    paymentMethod = "فيزا"
                },
                new {
                    id = 3,
                    invoiceNumber = "INV-003",
                    customerName = "محمود عبدالله أحمد",
                    totalAmount = 320.00m,
                    date = DateTime.Now.AddDays(-3),
                    status = "معلقة",
                    paymentMethod = "آجل"
                }
            };

            return Ok(new {
                count = sales.Length,
                sales = sales,
                totalAmount = sales.Sum(s => s.totalAmount),
                message = "تم تحميل المبيعات بنجاح"
            });
        }

        /// <summary>
        /// توليد كود منتج جديد حسب النوع
        /// </summary>
        [HttpPost("generate-product-code")]
        public async Task<ActionResult> GenerateProductCode([FromBody] ProductCodeRequest request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                string newCode = "";

                switch (request.ProductType.ToLower())
                {
                    case "محلي":
                        newCode = await GenerateLocalProductCode(connection);
                        break;
                    case "دولي":
                        // للمنتجات الدولية، يتم إدخال الكود يدوياً
                        return Ok(new {
                            productCode = "",
                            message = "يرجى إدخال كود المنتج الدولي يدوياً",
                            requiresManualInput = true,
                            productType = "دولي"
                        });
                    case "موزون":
                        newCode = await GenerateWeightedProductCode(connection);
                        break;
                    default:
                        return BadRequest(new { message = "نوع المنتج غير صحيح. يجب أن يكون: محلي، دولي، أو موزون" });
                }

                return Ok(new {
                    productCode = newCode,
                    message = "تم توليد كود المنتج بنجاح",
                    productType = request.ProductType,
                    requiresManualInput = false
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في توليد كود المنتج", error = ex.Message });
            }
        }

        /// <summary>
        /// توليد كود للمنتجات المحلية
        /// </summary>
        private async Task<string> GenerateLocalProductCode(SqlConnection connection)
        {
            var query = @"
                SELECT TOP 1 ProductCode
                FROM Products
                WHERE ProductCode LIKE '2%' AND LEN(ProductCode) = 13
                ORDER BY ProductCode DESC";

            var lastCode = await connection.QueryFirstOrDefaultAsync<string>(query);

            if (string.IsNullOrEmpty(lastCode))
            {
                return "2000000000001";
            }

            if (long.TryParse(lastCode, out long numericCode))
            {
                return (numericCode + 1).ToString();
            }

            return "2000000000001";
        }

        /// <summary>
        /// توليد كود للمنتجات الموزونة
        /// </summary>
        private async Task<string> GenerateWeightedProductCode(SqlConnection connection)
        {
            var query = @"
                SELECT TOP 1 ProductCode
                FROM Products
                WHERE ProductCode LIKE 'W%'
                ORDER BY ProductCode DESC";

            var lastCode = await connection.QueryFirstOrDefaultAsync<string>(query);

            if (string.IsNullOrEmpty(lastCode))
            {
                return "W000001";
            }

            var numericPart = lastCode.Substring(1);
            if (int.TryParse(numericPart, out int number))
            {
                return $"W{(number + 1):D6}";
            }

            return "W000001";
        }

        /// <summary>
        /// التحقق من توفر كود المنتج
        /// </summary>
        [HttpPost("check-product-code")]
        public async Task<ActionResult> CheckProductCode([FromBody] CheckCodeRequest request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT COUNT(*) FROM Products WHERE ProductCode = @ProductCode";
                var count = await connection.QueryFirstOrDefaultAsync<int>(query, new { ProductCode = request.ProductCode });

                return Ok(new {
                    isAvailable = count == 0,
                    productCode = request.ProductCode,
                    message = count == 0 ? "الكود متاح للاستخدام" : "الكود مستخدم بالفعل"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في التحقق من الكود", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على الموردين من قاعدة البيانات
        /// </summary>
        [HttpGet("suppliers")]
        public async Task<ActionResult> GetSuppliers()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT
                        Id,
                        NameAr,
                        NameEn,
                        SupplierCode,
                        Phone1 as Phone,
                        Email,
                        Address,
                        IsActive,
                        CurrentBalance,
                        Rating,
                        ContactPersonName
                    FROM Suppliers
                    WHERE IsActive = 1
                    ORDER BY NameAr";

                var suppliers = await connection.QueryAsync(query);

                return Ok(new {
                    count = suppliers.Count(),
                    suppliers = suppliers,
                    message = "تم تحميل الموردين بنجاح",
                    source = "database"
                });
            }
            catch (Exception ex)
            {
                var fallbackSuppliers = new[]
                {
                    new {
                        id = 1,
                        nameAr = "شركة الأهرام للتجارة",
                        nameEn = "Al-Ahram Trading Company",
                        supplierCode = "SUP001",
                        phone = "0223456789",
                        email = "<EMAIL>",
                        address = "القاهرة، مصر",
                        isActive = true
                    }
                };

                return BadRequest(new {
                    message = "خطأ في تحميل الموردين",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get supplier by ID
        /// </summary>
        [HttpGet("suppliers/{id}")]
        public async Task<ActionResult> GetSupplier(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT
                        Id, SupplierCode, NameAr, NameEn, SupplierTypeId,
                        Phone1, Phone2, Email, Website, Address, AreaId,
                        ContactPersonName, ContactPersonPhone, ContactPersonEmail,
                        PaymentTerms, CreditLimit, OpeningBalance, CurrentBalance,
                        TaxNumber, CommercialRegister, BankName, BankAccountNumber,
                        Rating, Notes, IsActive, CreatedAt, UpdatedAt
                    FROM Suppliers
                    WHERE Id = @Id";

                var supplier = await connection.QueryFirstOrDefaultAsync(query, new { Id = id });

                if (supplier == null)
                {
                    return NotFound(new { message = "المورد غير موجود" });
                }

                return Ok(new {
                    supplier = supplier,
                    message = "تم تحميل بيانات المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل بيانات المورد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Create new supplier
        /// </summary>
        [HttpPost("suppliers")]
        public async Task<ActionResult> CreateSupplier([FromBody] CreateSupplierRequest request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Use the supplier code from the request (already generated from frontend)
                var supplierCode = request.SupplierCode;

                // If no code provided, generate one as fallback
                if (string.IsNullOrEmpty(supplierCode))
                {
                    var nextCodeParam = new SqlParameter("@NextCode", SqlDbType.NVarChar, 50)
                    {
                        Direction = ParameterDirection.Output
                    };

                    var command = new SqlCommand("GetNextCounterValue", connection)
                    {
                        CommandType = CommandType.StoredProcedure
                    };

                    command.Parameters.AddWithValue("@CounterName", "SupplierCode");
                    command.Parameters.Add(nextCodeParam);

                    await command.ExecuteNonQueryAsync();

                    supplierCode = nextCodeParam.Value?.ToString();
                }

                var insertQuery = @"
                    INSERT INTO Suppliers (
                        SupplierCode, NameAr, NameEn, SupplierTypeId,
                        Phone1, Phone2, Email, Website, Address, AreaId,
                        ContactPersonName, ContactPersonPhone, ContactPersonEmail,
                        PaymentTerms, DeliveryDays, CreditLimit, OpeningBalance, CurrentBalance,
                        TaxNumber, CommercialRegister, BankName, BankAccountNumber, IBAN,
                        Rating, Notes, IsActive, CreatedAt, CreatedBy
                    ) VALUES (
                        @SupplierCode, @NameAr, @NameEn, @SupplierTypeId,
                        @Phone1, @Phone2, @Email, @Website, @Address, @AreaId,
                        @ContactPersonName, @ContactPersonPhone, @ContactPersonEmail,
                        @PaymentTerms, @DeliveryDays, @CreditLimit, @OpeningBalance, @OpeningBalance,
                        @TaxNumber, @CommercialRegister, @BankName, @BankAccountNumber, @Iban,
                        @Rating, @Notes, @IsActive, GETUTCDATE(), @CreatedBy
                    );
                    SELECT SCOPE_IDENTITY();";

                var supplierId = await connection.QueryFirstAsync<int>(insertQuery, new
                {
                    SupplierCode = supplierCode,
                    request.NameAr,
                    request.NameEn,
                    SupplierTypeId = request.SupplierTypeId ?? 1,
                    request.Phone1,
                    request.Phone2,
                    request.Email,
                    request.Website,
                    request.Address,
                    request.AreaId,
                    request.ContactPersonName,
                    request.ContactPersonPhone,
                    request.ContactPersonEmail,
                    PaymentTerms = request.PaymentTerms ?? 30,
                    DeliveryDays = request.DeliveryDays ?? 7,
                    CreditLimit = request.CreditLimit ?? 0,
                    OpeningBalance = request.OpeningBalance ?? 0,
                    request.TaxNumber,
                    request.CommercialRegister,
                    request.BankName,
                    request.BankAccountNumber,
                    request.Iban,
                    request.Rating,
                    request.Notes,
                    IsActive = request.IsActive ?? true,
                    CreatedBy = "Admin" // TODO: Get from authentication context
                });

                return Ok(new {
                    id = supplierId,
                    supplierCode = supplierCode,
                    message = "تم إضافة المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في إضافة المورد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Update supplier
        /// </summary>
        [HttpPut("suppliers/{id}")]
        public async Task<ActionResult> UpdateSupplier(int id, [FromBody] UpdateSupplierRequest request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var updateQuery = @"
                    UPDATE Suppliers SET
                        NameAr = @NameAr,
                        NameEn = @NameEn,
                        SupplierTypeId = @SupplierTypeId,
                        Phone1 = @Phone1,
                        Phone2 = @Phone2,
                        Email = @Email,
                        Website = @Website,
                        Address = @Address,
                        AreaId = @AreaId,
                        ContactPersonName = @ContactPersonName,
                        ContactPersonPhone = @ContactPersonPhone,
                        ContactPersonEmail = @ContactPersonEmail,
                        PaymentTerms = @PaymentTerms,
                        DeliveryDays = @DeliveryDays,
                        CreditLimit = @CreditLimit,
                        TaxNumber = @TaxNumber,
                        CommercialRegister = @CommercialRegister,
                        BankName = @BankName,
                        BankAccountNumber = @BankAccountNumber,
                        Rating = @Rating,
                        Notes = @Notes,
                        IsActive = @IsActive,
                        UpdatedAt = GETUTCDATE(),
                        UpdatedBy = @UpdatedBy
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(updateQuery, new
                {
                    Id = id,
                    request.NameAr,
                    request.NameEn,
                    request.SupplierTypeId,
                    request.Phone1,
                    request.Phone2,
                    request.Email,
                    request.Website,
                    request.Address,
                    request.AreaId,
                    request.ContactPersonName,
                    request.ContactPersonPhone,
                    request.ContactPersonEmail,
                    request.PaymentTerms,
                    request.DeliveryDays,
                    request.CreditLimit,
                    request.TaxNumber,
                    request.CommercialRegister,
                    request.BankName,
                    request.BankAccountNumber,
                    request.Rating,
                    request.Notes,
                    request.IsActive,
                    UpdatedBy = "Admin" // TODO: Get from authentication context
                });

                if (rowsAffected == 0)
                {
                    return NotFound(new { message = "المورد غير موجود" });
                }

                return Ok(new {
                    message = "تم تحديث بيانات المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحديث بيانات المورد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Delete supplier
        /// </summary>
        [HttpDelete("suppliers/{id}")]
        public async Task<ActionResult> DeleteSupplier(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Soft delete - set IsActive to false
                var deleteQuery = @"
                    UPDATE Suppliers
                    SET IsActive = 0, UpdatedAt = GETUTCDATE()
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(deleteQuery, new { Id = id });

                if (rowsAffected == 0)
                {
                    return NotFound(new { message = "المورد غير موجود" });
                }

                return Ok(new {
                    message = "تم حذف المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في حذف المورد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get supplier statistics and limits
        /// </summary>
        [HttpGet("supplier-stats")]
        public ActionResult GetSupplierStats()
        {
            try
            {
                return Ok(new {
                    statistics = new {
                        totalSuppliers = 10,
                        activeSuppliers = 8,
                        inactiveSuppliers = 2,
                        totalBalance = -50000,
                        positiveBalance = 200000,
                        negativeBalance = -250000
                    },
                    codeInfo = new {
                        lastSupplierNumber = 10,
                        nextSupplierCode = "SUP011",
                        maxPossibleCodes = 999999,
                        remainingCodes = 999989,
                        codeFormat = "SUP001 إلى SUP999999",
                        canAddMore = true
                    },
                    message = "تم تحميل إحصائيات الموردين بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل إحصائيات الموردين",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get next supplier code
        /// </summary>
        [HttpGet("next-supplier-code")]
        public async Task<ActionResult> GetNextSupplierCode()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var nextCodeParam = new SqlParameter("@NextCode", SqlDbType.NVarChar, 50)
                {
                    Direction = ParameterDirection.Output
                };

                var command = new SqlCommand("GetNextCounterValue", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@CounterName", "SupplierCode");
                command.Parameters.Add(nextCodeParam);

                await command.ExecuteNonQueryAsync();

                var nextCode = nextCodeParam.Value?.ToString();

                return Ok(new {
                    nextCode = nextCode,
                    message = "تم الحصول على الكود التالي بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في الحصول على الكود التالي",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get supplier types
        /// </summary>
        [HttpGet("supplier-types")]
        public async Task<ActionResult> GetSupplierTypes()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var supplierTypes = await connection.QueryAsync(@"
                    SELECT Id, NameAr, NameEn, Description, IsActive
                    FROM SupplierTypes
                    WHERE IsActive = 1
                    ORDER BY NameAr");

                return Ok(new {
                    supplierTypes = supplierTypes,
                    message = "تم تحميل أنواع الموردين بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل أنواع الموردين",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get areas (governorates) from database
        /// </summary>
        [HttpGet("areas-db")]
        public async Task<ActionResult> GetAreasFromDatabase()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var areas = await connection.QueryAsync(@"
                    SELECT Id, NameAr, NameEn, Code, IsActive
                    FROM Areas
                    WHERE IsActive = 1
                    ORDER BY NameAr");

                return Ok(new {
                    areas = areas,
                    message = "تم تحميل المناطق بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل المناطق",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Add new area
        /// </summary>
        [HttpPost("areas")]
        public async Task<ActionResult> AddArea([FromBody] dynamic request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    INSERT INTO Areas (NameAr, NameEn, Code, IsActive, CreatedAt)
                    VALUES (@NameAr, @NameEn, @Code, @IsActive, GETDATE());
                    SELECT SCOPE_IDENTITY();";

                var newId = await connection.QuerySingleAsync<int>(query, new {
                    NameAr = (string)request.nameAr,
                    NameEn = (string)request.nameEn,
                    Code = (string)request.code,
                    IsActive = (bool)request.isActive
                });

                return Ok(new {
                    id = newId,
                    message = "تم إضافة المحافظة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في إضافة المحافظة",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get supplier statistics
        /// </summary>
        [HttpGet("supplier-stats")]
        public async Task<ActionResult> GetSupplierStatistics()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                // Get supplier counts
                var supplierStats = await connection.QuerySingleAsync(@"
                    SELECT
                        COUNT(*) as TotalSuppliers,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveSuppliers,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveSuppliers,
                        ISNULL(SUM(OpeningBalance), 0) as TotalBalance,
                        ISNULL(SUM(CASE WHEN OpeningBalance > 0 THEN OpeningBalance ELSE 0 END), 0) as PositiveBalance,
                        ISNULL(SUM(CASE WHEN OpeningBalance < 0 THEN OpeningBalance ELSE 0 END), 0) as NegativeBalance
                    FROM Suppliers");

                // Get code information
                var lastSupplierCode = await connection.QuerySingleOrDefaultAsync<string>(@"
                    SELECT TOP 1 SupplierCode
                    FROM Suppliers
                    ORDER BY Id DESC");

                var lastNumber = 0;
                if (!string.IsNullOrEmpty(lastSupplierCode))
                {
                    var numberPart = lastSupplierCode.Replace("SUP", "");
                    int.TryParse(numberPart, out lastNumber);
                }

                var codeInfo = new {
                    lastSupplierNumber = lastNumber,
                    nextSupplierCode = $"SUP{(lastNumber + 1):D3}",
                    maxPossibleCodes = 999999,
                    remainingCodes = 999999 - lastNumber,
                    codeFormat = "SUP001 إلى SUP999999",
                    canAddMore = lastNumber < 999999
                };

                return Ok(new {
                    statistics = supplierStats,
                    codeInfo = codeInfo,
                    message = "تم تحميل إحصائيات الموردين بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحميل إحصائيات الموردين",
                    error = ex.Message
                });
            }
        }

        // ===== COUNTRIES API FOR SIMPLE OPERATIONS =====

        [HttpGet("countries")]
        public async Task<ActionResult> GetCountriesSimple()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var countries = await connection.QueryAsync(@"
                    SELECT Id, NameAr, NameEn, Code, PhoneCode, IsActive
                    FROM Countries
                    WHERE IsActive = 1
                    ORDER BY NameAr");

                return Ok(new { countries = countries.ToList() });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطأ في تحميل البلدان", error = ex.Message });
            }
        }
    }

    public class ProductCodeRequest
    {
        public string ProductType { get; set; } = "";
    }

    public class CheckCodeRequest
    {
        public string ProductCode { get; set; } = "";
    }

    public class CreateSupplierRequest
    {
        public string? SupplierCode { get; set; }
        public string NameAr { get; set; } = "";
        public string? NameEn { get; set; }
        public int? SupplierTypeId { get; set; }
        public string Phone1 { get; set; } = "";
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public int? CountryId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? ContactPersonPhone { get; set; }
        public string? ContactPersonEmail { get; set; }
        public int? PaymentTerms { get; set; }
        public int? DeliveryDays { get; set; }
        public decimal? CreditLimit { get; set; }
        public decimal? OpeningBalance { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? Iban { get; set; }
        public int? Rating { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
    }

        /// <summary>
        /// أنواع العملاء من قاعدة البيانات - محدث
        /// </summary>
        [HttpGet("customer-types-db")]
        public async Task<ActionResult> GetCustomerTypesFromDb()
        {
            try
            {
                var customerTypes = await _context.CustomerTypes
                    .Where(ct => ct.IsActive)
                    .OrderBy(ct => ct.NameAr)
                    .Select(ct => new
                    {
                        id = ct.Id,
                        nameAr = ct.NameAr,
                        nameEn = ct.NameEn,
                        defaultDiscountPercentage = ct.DefaultDiscountPercentage,
                        defaultCreditLimit = ct.DefaultCreditLimit
                    })
                    .ToListAsync();

                return Ok(new {
                    count = customerTypes.Count,
                    customerTypes = customerTypes,
                    message = "تم تحميل أنواع العملاء من قاعدة البيانات بنجاح"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع أنواع العملاء من قاعدة البيانات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الفئات السعرية من قاعدة البيانات
        /// </summary>
        [HttpGet("price-categories-db")]
        public async Task<ActionResult> GetPriceCategoriesFromDb()
        {
            try
            {
                var priceCategories = await _context.PriceCategories
                    .Where(pc => pc.IsActive && !pc.IsDeleted)
                    .OrderBy(pc => pc.CategoryNameAr)
                    .Select(pc => new
                    {
                        id = pc.Id,
                        categoryCode = pc.CategoryCode,
                        categoryNameAr = pc.CategoryNameAr,
                        categoryNameEn = pc.CategoryNameEn,
                        discountPercentage = pc.DiscountPercentage,
                        isDefault = pc.IsDefault
                    })
                    .ToListAsync();

                return Ok(new {
                    count = priceCategories.Count,
                    priceCategories = priceCategories,
                    message = "تم تحميل الفئات السعرية من قاعدة البيانات بنجاح"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفئات السعرية من قاعدة البيانات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }

        /// <summary>
        /// الفروع من قاعدة البيانات
        /// </summary>
        [HttpGet("branches-db")]
        public async Task<ActionResult> GetBranchesFromDb()
        {
            try
            {
                var branches = await _context.Branches
                    .Where(b => b.IsActive)
                    .OrderBy(b => b.NameAr)
                    .Select(b => new
                    {
                        id = b.Id,
                        code = b.Code,
                        nameAr = b.NameAr,
                        nameEn = b.NameEn,
                        address = b.Address,
                        phone = b.Phone,
                        isMainBranch = b.IsMainBranch
                    })
                    .ToListAsync();

                return Ok(new {
                    count = branches.Count,
                    branches = branches,
                    message = "تم تحميل الفروع من قاعدة البيانات بنجاح"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استرجاع الفروع من قاعدة البيانات");
                return StatusCode(500, new { message = "حدث خطأ في استرجاع البيانات", error = ex.Message });
            }
        }
    }

    public class UpdateSupplierRequest
    {
        public string NameAr { get; set; } = "";
        public string? NameEn { get; set; }
        public int? SupplierTypeId { get; set; }
        public string Phone1 { get; set; } = "";
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? ContactPersonPhone { get; set; }
        public string? ContactPersonEmail { get; set; }
        public int? PaymentTerms { get; set; }
        public int? DeliveryDays { get; set; }
        public decimal? CreditLimit { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public int? Rating { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
    }
}
