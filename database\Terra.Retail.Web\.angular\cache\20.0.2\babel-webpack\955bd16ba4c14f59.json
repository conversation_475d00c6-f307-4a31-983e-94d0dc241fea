{"ast": null, "code": "import { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Possible states for a ripple element. */\nvar RippleState = /*#__PURE__*/function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n  return RippleState;\n}(RippleState || {});\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  _renderer;\n  element;\n  config;\n  _animationForciblyDisabledThroughCss;\n  /** Current state of the ripple. */\n  state = RippleState.HIDDEN;\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  _events = /*#__PURE__*/new Map();\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  _delegateEventHandler = event => {\n    const target = _getEventTarget(event);\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nlet _MatRippleStylesLoader = /*#__PURE__*/(() => {\n  class _MatRippleStylesLoader {\n    static ɵfac = function _MatRippleStylesLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatRippleStylesLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatRippleStylesLoader,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"mat-ripple-style-loader\", \"\"],\n      decls: 0,\n      vars: 0,\n      template: function _MatRippleStylesLoader_Template(rf, ctx) {},\n      styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _MatRippleStylesLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  _target;\n  _ngZone;\n  _platform;\n  /** Element where the ripples are being added to. */\n  _containerElement;\n  /** Element which triggers the ripple elements on mouse events. */\n  _triggerElement;\n  /** Whether the pointer is currently down or not. */\n  _isPointerDown = false;\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  _activeRipples = /*#__PURE__*/new Map();\n  /** Latest non-persistent ripple that was triggered. */\n  _mostRecentTransientRipple;\n  /** Time in milliseconds when the last touchstart event happened. */\n  _lastTouchStartEvent;\n  /** Whether pointer-up event listeners have been registered. */\n  _pointerUpEventsRegistered = false;\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  _containerRect;\n  static _eventManager = /*#__PURE__*/new RippleEventManager();\n  constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel,\n          fallbackTimer\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = /*#__PURE__*/new InjectionToken('mat-ripple-global-options');\nlet MatRipple = /*#__PURE__*/(() => {\n  class MatRipple {\n    _elementRef = inject(ElementRef);\n    _animationsDisabled = _animationsDisabled();\n    /** Custom color for all ripples. */\n    color;\n    /** Whether the ripples should be visible outside the component's bounds. */\n    unbounded;\n    /**\n     * Whether the ripple always originates from the center of the host element's bounds, rather\n     * than originating from the location of the click event.\n     */\n    centered;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    radius = 0;\n    /**\n     * Configuration for the ripple animation. Allows modifying the enter and exit animation\n     * duration of the ripples. The animation durations will be overwritten if the\n     * `NoopAnimationsModule` is being used.\n     */\n    animation;\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      if (value) {\n        this.fadeOutAllNonPersistent();\n      }\n      this._disabled = value;\n      this._setupTriggerEventsIfEnabled();\n    }\n    _disabled = false;\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n      return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n      this._trigger = trigger;\n      this._setupTriggerEventsIfEnabled();\n    }\n    _trigger;\n    /** Renderer for the ripple DOM manipulations. */\n    _rippleRenderer;\n    /** Options that are set globally for all ripples. */\n    _globalOptions;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    _isInitialized = false;\n    constructor() {\n      const ngZone = inject(NgZone);\n      const platform = inject(Platform);\n      const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n        optional: true\n      });\n      const injector = inject(Injector);\n      // Note: cannot use `inject()` here, because this class\n      // gets instantiated manually in the ripple loader.\n      this._globalOptions = globalOptions || {};\n      this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n    }\n    ngOnInit() {\n      this._isInitialized = true;\n      this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n      this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n      this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n      this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n      return {\n        centered: this.centered,\n        radius: this.radius,\n        color: this.color,\n        animation: {\n          ...this._globalOptions.animation,\n          ...(this._animationsDisabled ? {\n            enterDuration: 0,\n            exitDuration: 0\n          } : {}),\n          ...this.animation\n        },\n        terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n      };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n      return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n      if (!this.disabled && this._isInitialized) {\n        this._rippleRenderer.setupTriggerEvents(this.trigger);\n      }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n      if (typeof configOrX === 'number') {\n        return this._rippleRenderer.fadeInRipple(configOrX, y, {\n          ...this.rippleConfig,\n          ...config\n        });\n      } else {\n        return this._rippleRenderer.fadeInRipple(0, 0, {\n          ...this.rippleConfig,\n          ...configOrX\n        });\n      }\n    }\n    static ɵfac = function MatRipple_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRipple)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRipple,\n      selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n      hostAttrs: [1, \"mat-ripple\"],\n      hostVars: 2,\n      hostBindings: function MatRipple_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n        }\n      },\n      inputs: {\n        color: [0, \"matRippleColor\", \"color\"],\n        unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n        centered: [0, \"matRippleCentered\", \"centered\"],\n        radius: [0, \"matRippleRadius\", \"radius\"],\n        animation: [0, \"matRippleAnimation\", \"animation\"],\n        disabled: [0, \"matRippleDisabled\", \"disabled\"],\n        trigger: [0, \"matRippleTrigger\", \"trigger\"]\n      },\n      exportAs: [\"matRipple\"]\n    });\n  }\n  return MatRipple;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };", "map": {"version": 3, "names": ["normalizePassiveListenerOptions", "_getEventTarget", "Platform", "i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "InjectionToken", "inject", "ElementRef", "NgZone", "Injector", "Directive", "Input", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "coerceElement", "_CdkPrivateStyleLoader", "_", "_animationsDisabled", "RippleState", "RippleRef", "_renderer", "element", "config", "_animationForciblyDisabledThroughCss", "state", "HIDDEN", "constructor", "fadeOut", "fadeOutRipple", "passiveCapturingEventOptions$1", "passive", "capture", "RippleEventManager", "_events", "Map", "add<PERSON><PERSON><PERSON>", "ngZone", "name", "handler", "handlersForEvent", "get", "handlersForElement", "add", "set", "Set", "runOutsideAngular", "document", "addEventListener", "_delegate<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "delete", "size", "removeEventListener", "event", "target", "type", "for<PERSON>ach", "handlers", "contains", "handleEvent", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveCapturingEventOptions", "pointerDownEvents", "pointerUpEvents", "_MatRippleStylesLoader", "ɵfac", "_MatRippleStylesLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "decls", "vars", "template", "_MatRippleStylesLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_target", "_ngZone", "_platform", "_containerElement", "_triggerElement", "_isPointerDown", "_activeRipples", "_mostRecentTransientRipple", "_lastTouchStartEvent", "_pointerUpEventsRegistered", "_containerRect", "_eventManager", "elementOrElementRef", "injector", "<PERSON><PERSON><PERSON><PERSON>", "load", "fadeInRipple", "x", "y", "containerRect", "getBoundingClientRect", "animationConfig", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "ripple", "createElement", "classList", "style", "color", "backgroundColor", "transitionDuration", "append<PERSON><PERSON><PERSON>", "computedStyles", "window", "getComputedStyle", "userTransitionProperty", "transitionProperty", "userTransitionDuration", "animationForciblyDisabledThroughCss", "rippleRef", "transform", "FADING_IN", "persistent", "eventListeners", "onTransitionEnd", "fallbackTimer", "clearTimeout", "_finishRippleTransition", "onTransitionCancel", "_destroyRipple", "setTimeout", "FADING_OUT", "rippleEl", "opacity", "fadeOutAll", "_getActiveRipples", "fadeOutAllNonPersistent", "setupTriggerEvents", "_removeTriggerEvents", "_onMousedown", "_onTouchStart", "_onPointerUp", "_startFadeOutTransition", "isMostRecentTransientRipple", "VISIBLE", "remove", "isFakeMousedown", "isSyntheticEvent", "Date", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "i", "length", "isVisible", "terminateOnPointerUp", "Array", "from", "keys", "trigger", "rect", "distX", "Math", "max", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "_elementRef", "unbounded", "disabled", "_disabled", "value", "_setupTriggerEventsIfEnabled", "_trigger", "nativeElement", "_ripple<PERSON><PERSON>er", "_globalOptions", "_isInitialized", "platform", "globalOptions", "optional", "ngOnInit", "ngOnDestroy", "launch", "configOrX", "MatRipple_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "MatRipple_HostBindings", "ɵɵclassProp", "inputs", "exportAs", "M", "R", "a", "b", "c", "d"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/ripple-BYgV4oZC.mjs"], "sourcesContent": ["import { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n    RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n    RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n    RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n    RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    _renderer;\n    element;\n    config;\n    _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    state = RippleState.HIDDEN;\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config, \n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    _animationForciblyDisabledThroughCss = false) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n    _events = new Map();\n    /** Adds an event handler. */\n    addHandler(ngZone, name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (handlersForEvent) {\n            const handlersForElement = handlersForEvent.get(element);\n            if (handlersForElement) {\n                handlersForElement.add(handler);\n            }\n            else {\n                handlersForEvent.set(element, new Set([handler]));\n            }\n        }\n        else {\n            this._events.set(name, new Map([[element, new Set([handler])]]));\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n            });\n        }\n    }\n    /** Removes an event handler. */\n    removeHandler(name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (!handlersForEvent) {\n            return;\n        }\n        const handlersForElement = handlersForEvent.get(element);\n        if (!handlersForElement) {\n            return;\n        }\n        handlersForElement.delete(handler);\n        if (handlersForElement.size === 0) {\n            handlersForEvent.delete(element);\n        }\n        if (handlersForEvent.size === 0) {\n            this._events.delete(name);\n            document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n        }\n    }\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    _delegateEventHandler = (event) => {\n        const target = _getEventTarget(event);\n        if (target) {\n            this._events.get(event.type)?.forEach((handlers, element) => {\n                if (element === target || element.contains(target)) {\n                    handlers.forEach(handler => handler.handleEvent(event));\n                }\n            });\n        }\n    };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatRippleStylesLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _MatRippleStylesLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"mat-ripple-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatRippleStylesLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'mat-ripple-style-loader': '' }, styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"] }]\n        }] });\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    _target;\n    _ngZone;\n    _platform;\n    /** Element where the ripples are being added to. */\n    _containerElement;\n    /** Element which triggers the ripple elements on mouse events. */\n    _triggerElement;\n    /** Whether the pointer is currently down or not. */\n    _isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    _activeRipples = new Map();\n    /** Latest non-persistent ripple that was triggered. */\n    _mostRecentTransientRipple;\n    /** Time in milliseconds when the last touchstart event happened. */\n    _lastTouchStartEvent;\n    /** Whether pointer-up event listeners have been registered. */\n    _pointerUpEventsRegistered = false;\n    /**\n     * Cached dimensions of the ripple container. Set when the first\n     * ripple is shown and cleared once no more ripples are visible.\n     */\n    _containerRect;\n    static _eventManager = new RippleEventManager();\n    constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        // Only do anything if we're on the browser.\n        if (_platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n        if (injector) {\n            injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const enterDuration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${enterDuration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical to ensure that the `scale` animates properly.\n        // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n        // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n        const computedStyles = window.getComputedStyle(ripple);\n        const userTransitionProperty = computedStyles.transitionProperty;\n        const userTransitionDuration = computedStyles.transitionDuration;\n        // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n        // `transition: none` or `display: none`). This is technically unexpected since animations are\n        // controlled through the animation config, but this exists for backwards compatibility. This\n        // logic does not need to be super accurate since it covers some edge cases which can be easily\n        // avoided by users.\n        const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n            // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n            // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n            userTransitionDuration === '0s' ||\n            userTransitionDuration === '0s, 0s' ||\n            // If the container is 0x0, it's likely `display: none`.\n            (containerRect.width === 0 && containerRect.height === 0);\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n        // Start the enter animation by setting the transform/scale to 100%. The animation will\n        // execute as part of this statement because we forced a style recalculation before.\n        // Note: We use a 3d transform here in order to avoid an issue in Safari where\n        // the ripples aren't clipped when inside the shadow DOM (see #24028).\n        ripple.style.transform = 'scale3d(1, 1, 1)';\n        rippleRef.state = RippleState.FADING_IN;\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        let eventListeners = null;\n        // Do not register the `transition` event listener if fade-in and fade-out duration\n        // are set to zero. The events won't fire anyway and we can save resources here.\n        if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n            this._ngZone.runOutsideAngular(() => {\n                const onTransitionEnd = () => {\n                    // Clear the fallback timer since the transition fired correctly.\n                    if (eventListeners) {\n                        eventListeners.fallbackTimer = null;\n                    }\n                    clearTimeout(fallbackTimer);\n                    this._finishRippleTransition(rippleRef);\n                };\n                const onTransitionCancel = () => this._destroyRipple(rippleRef);\n                // In some cases where there's a higher load on the browser, it can choose not to dispatch\n                // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n                // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n                // because timers aren't precise. Note that another approach can be to transition the ripple\n                // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n                // `transitionstart`. We go with the timer because it's one less event listener and\n                // it's less likely to break existing tests.\n                const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n                ripple.addEventListener('transitionend', onTransitionEnd);\n                // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n                // directly as otherwise we would keep it part of the ripple container forever.\n                // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n                ripple.addEventListener('transitioncancel', onTransitionCancel);\n                eventListeners = { onTransitionEnd, onTransitionCancel, fallbackTimer };\n            });\n        }\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.set(rippleRef, eventListeners);\n        // In case there is no fade-in transition duration, we need to manually call the transition\n        // end listener because `transitionend` doesn't fire if there is no transition.\n        if (animationForciblyDisabledThroughCss || !enterDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        // For ripples already fading out or hidden, this should be a noop.\n        if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        // This starts the fade-out transition and will fire the transition end listener that\n        // removes the ripple element from the DOM.\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = RippleState.FADING_OUT;\n        // In case there is no fade-out transition duration, we need to manually call the\n        // transition end listener because `transitionend` doesn't fire if there is no transition.\n        if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._getActiveRipples().forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        // Use event delegation for the trigger events since they're\n        // set up during creation and are performance-sensitive.\n        pointerDownEvents.forEach(type => {\n            RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n        });\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            // The events for hiding the ripple are bound directly on the trigger, because:\n            // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n            // delegation will be diminished by having to look through all the data structures often.\n            // 2. They aren't as performance-sensitive, because they're bound only after the user\n            // has interacted with an element.\n            this._ngZone.runOutsideAngular(() => {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n                });\n            });\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Method that will be called if the fade-in or fade-in transition completed. */\n    _finishRippleTransition(rippleRef) {\n        if (rippleRef.state === RippleState.FADING_IN) {\n            this._startFadeOutTransition(rippleRef);\n        }\n        else if (rippleRef.state === RippleState.FADING_OUT) {\n            this._destroyRipple(rippleRef);\n        }\n    }\n    /**\n     * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n     * is not held down anymore.\n     */\n    _startFadeOutTransition(rippleRef) {\n        const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n        const { persistent } = rippleRef.config;\n        rippleRef.state = RippleState.VISIBLE;\n        // When the timer runs out while the user has kept their pointer down, we want to\n        // keep only the persistent ripples and the latest transient ripple. We do this,\n        // because we don't want stacked transient ripples to appear after their enter\n        // animation has finished.\n        if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n            rippleRef.fadeOut();\n        }\n    }\n    /** Destroys the given ripple by removing it from the DOM and updating its state. */\n    _destroyRipple(rippleRef) {\n        const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n        this._activeRipples.delete(rippleRef);\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // If the current ref is the most recent transient ripple, unset it\n        // avoid memory leaks.\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        rippleRef.state = RippleState.HIDDEN;\n        if (eventListeners !== null) {\n            rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n            rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n            if (eventListeners.fallbackTimer !== null) {\n                clearTimeout(eventListeners.fallbackTimer);\n            }\n        }\n        rippleRef.element.remove();\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            // According to the typings the touches should always be defined, but in some cases\n            // the browser appears to not assign them in tests which leads to flakes.\n            if (touches) {\n                for (let i = 0; i < touches.length; i++) {\n                    this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n                }\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._getActiveRipples().forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === RippleState.VISIBLE ||\n                (ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    _getActiveRipples() {\n        return Array.from(this._activeRipples.keys());\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        const trigger = this._triggerElement;\n        if (trigger) {\n            pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n                this._pointerUpEventsRegistered = false;\n            }\n        }\n    }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    _elementRef = inject(ElementRef);\n    _animationsDisabled = _animationsDisabled();\n    /** Custom color for all ripples. */\n    color;\n    /** Whether the ripples should be visible outside the component's bounds. */\n    unbounded;\n    /**\n     * Whether the ripple always originates from the center of the host element's bounds, rather\n     * than originating from the location of the click event.\n     */\n    centered;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    radius = 0;\n    /**\n     * Configuration for the ripple animation. Allows modifying the enter and exit animation\n     * duration of the ripples. The animation durations will be overwritten if the\n     * `NoopAnimationsModule` is being used.\n     */\n    animation;\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    _disabled = false;\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    _trigger;\n    /** Renderer for the ripple DOM manipulations. */\n    _rippleRenderer;\n    /** Options that are set globally for all ripples. */\n    _globalOptions;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    _isInitialized = false;\n    constructor() {\n        const ngZone = inject(NgZone);\n        const platform = inject(Platform);\n        const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, { optional: true });\n        const injector = inject(Injector);\n        // Note: cannot use `inject()` here, because this class\n        // gets instantiated manually in the ripple loader.\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationsDisabled ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRipple, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatRipple, isStandalone: true, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };\n"], "mappings": "AAAA,SAASA,+BAA+B,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,uBAAuB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAC7J,SAASC,+BAA+B,EAAEC,gCAAgC,QAAQ,mBAAmB;AACrG,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;;AAEnE;AACA,IAAIC,WAAW,gBACd,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACvDA,WAAW,CAACA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACnDA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzDA,WAAW,CAACA,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAAC,OAJ3CA,WAAW;AAKtB,CAAC,CAAEA,WAAW,IAAmB,CAAC,CAAE,CANrB;AAOf;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,oCAAoC;EACpC;EACAC,KAAK,GAAGN,WAAW,CAACO,MAAM;EAC1BC,WAAWA,CAACN,SAAS,EACrB;EACAC,OAAO,EACP;EACAC,MAAM,EACN;EACAC,oCAAoC,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,oCAAoC,GAAGA,oCAAoC;EACpF;EACA;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACP,SAAS,CAACQ,aAAa,CAAC,IAAI,CAAC;EACtC;AACJ;;AAEA;AACA,MAAMC,8BAA8B,gBAAG/B,+BAA+B,CAAC;EACnEgC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMC,kBAAkB,CAAC;EACrBC,OAAO,gBAAG,IAAIC,GAAG,CAAC,CAAC;EACnB;EACAC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEhB,OAAO,EAAEiB,OAAO,EAAE;IACvC,MAAMC,gBAAgB,GAAG,IAAI,CAACN,OAAO,CAACO,GAAG,CAACH,IAAI,CAAC;IAC/C,IAAIE,gBAAgB,EAAE;MAClB,MAAME,kBAAkB,GAAGF,gBAAgB,CAACC,GAAG,CAACnB,OAAO,CAAC;MACxD,IAAIoB,kBAAkB,EAAE;QACpBA,kBAAkB,CAACC,GAAG,CAACJ,OAAO,CAAC;MACnC,CAAC,MACI;QACDC,gBAAgB,CAACI,GAAG,CAACtB,OAAO,EAAE,IAAIuB,GAAG,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,IAAI,CAACL,OAAO,CAACU,GAAG,CAACN,IAAI,EAAE,IAAIH,GAAG,CAAC,CAAC,CAACb,OAAO,EAAE,IAAIuB,GAAG,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEF,MAAM,CAACS,iBAAiB,CAAC,MAAM;QAC3BC,QAAQ,CAACC,gBAAgB,CAACV,IAAI,EAAE,IAAI,CAACW,qBAAqB,EAAEnB,8BAA8B,CAAC;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;EACAoB,aAAaA,CAACZ,IAAI,EAAEhB,OAAO,EAAEiB,OAAO,EAAE;IAClC,MAAMC,gBAAgB,GAAG,IAAI,CAACN,OAAO,CAACO,GAAG,CAACH,IAAI,CAAC;IAC/C,IAAI,CAACE,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAME,kBAAkB,GAAGF,gBAAgB,CAACC,GAAG,CAACnB,OAAO,CAAC;IACxD,IAAI,CAACoB,kBAAkB,EAAE;MACrB;IACJ;IACAA,kBAAkB,CAACS,MAAM,CAACZ,OAAO,CAAC;IAClC,IAAIG,kBAAkB,CAACU,IAAI,KAAK,CAAC,EAAE;MAC/BZ,gBAAgB,CAACW,MAAM,CAAC7B,OAAO,CAAC;IACpC;IACA,IAAIkB,gBAAgB,CAACY,IAAI,KAAK,CAAC,EAAE;MAC7B,IAAI,CAAClB,OAAO,CAACiB,MAAM,CAACb,IAAI,CAAC;MACzBS,QAAQ,CAACM,mBAAmB,CAACf,IAAI,EAAE,IAAI,CAACW,qBAAqB,EAAEnB,8BAA8B,CAAC;IAClG;EACJ;EACA;EACAmB,qBAAqB,GAAIK,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAGvD,eAAe,CAACsD,KAAK,CAAC;IACrC,IAAIC,MAAM,EAAE;MACR,IAAI,CAACrB,OAAO,CAACO,GAAG,CAACa,KAAK,CAACE,IAAI,CAAC,EAAEC,OAAO,CAAC,CAACC,QAAQ,EAAEpC,OAAO,KAAK;QACzD,IAAIA,OAAO,KAAKiC,MAAM,IAAIjC,OAAO,CAACqC,QAAQ,CAACJ,MAAM,CAAC,EAAE;UAChDG,QAAQ,CAACD,OAAO,CAAClB,OAAO,IAAIA,OAAO,CAACqB,WAAW,CAACN,KAAK,CAAC,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMO,4BAA4B,GAAG;EACjCC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,GAAG;AACpC;AACA,MAAMC,4BAA4B,gBAAGlE,+BAA+B,CAAC;EACjEgC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMkC,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACrD;AACA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;AAAC,IACvEC,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzB,OAAOC,IAAI,YAAAC,+BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,sBAAsB;IAAA;IACzH,OAAOI,IAAI,kBAD8EtE,EAAE,CAAAuE,iBAAA;MAAAjB,IAAA,EACJY,sBAAsB;MAAAM,SAAA;MAAAC,SAAA,8BAAiG,EAAE;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACpN;EAAC,OAHKhB,sBAAsB;AAAA;AAI5B;EAAA,QAAAiB,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,OAAO;EACPC,OAAO;EACPC,SAAS;EACT;EACAC,iBAAiB;EACjB;EACAC,eAAe;EACf;EACAC,cAAc,GAAG,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIC,cAAc,gBAAG,IAAI1D,GAAG,CAAC,CAAC;EAC1B;EACA2D,0BAA0B;EAC1B;EACAC,oBAAoB;EACpB;EACAC,0BAA0B,GAAG,KAAK;EAClC;AACJ;AACA;AACA;EACIC,cAAc;EACd,OAAOC,aAAa,gBAAG,IAAIjE,kBAAkB,CAAC,CAAC;EAC/CN,WAAWA,CAAC4D,OAAO,EAAEC,OAAO,EAAEW,mBAAmB,EAAEV,SAAS,EAAEW,QAAQ,EAAE;IACpE,IAAI,CAACb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAIA,SAAS,CAACY,SAAS,EAAE;MACrB,IAAI,CAACX,iBAAiB,GAAG3E,aAAa,CAACoF,mBAAmB,CAAC;IAC/D;IACA,IAAIC,QAAQ,EAAE;MACVA,QAAQ,CAAC3D,GAAG,CAACzB,sBAAsB,CAAC,CAACsF,IAAI,CAAClC,sBAAsB,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACImC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAElF,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMmF,aAAa,GAAI,IAAI,CAACT,cAAc,GACtC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACP,iBAAiB,CAACiB,qBAAqB,CAAC,CAAE;IAC1E,MAAMC,eAAe,GAAG;MAAE,GAAG/C,4BAA4B;MAAE,GAAGtC,MAAM,CAACsF;IAAU,CAAC;IAChF,IAAItF,MAAM,CAACuF,QAAQ,EAAE;MACjBN,CAAC,GAAGE,aAAa,CAACK,IAAI,GAAGL,aAAa,CAACM,KAAK,GAAG,CAAC;MAChDP,CAAC,GAAGC,aAAa,CAACO,GAAG,GAAGP,aAAa,CAACQ,MAAM,GAAG,CAAC;IACpD;IACA,MAAMC,MAAM,GAAG5F,MAAM,CAAC4F,MAAM,IAAIC,wBAAwB,CAACZ,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC7E,MAAMW,OAAO,GAAGb,CAAC,GAAGE,aAAa,CAACK,IAAI;IACtC,MAAMO,OAAO,GAAGb,CAAC,GAAGC,aAAa,CAACO,GAAG;IACrC,MAAMnD,aAAa,GAAG8C,eAAe,CAAC9C,aAAa;IACnD,MAAMyD,MAAM,GAAGxE,QAAQ,CAACyE,aAAa,CAAC,KAAK,CAAC;IAC5CD,MAAM,CAACE,SAAS,CAAC9E,GAAG,CAAC,oBAAoB,CAAC;IAC1C4E,MAAM,CAACG,KAAK,CAACX,IAAI,GAAG,GAAGM,OAAO,GAAGF,MAAM,IAAI;IAC3CI,MAAM,CAACG,KAAK,CAACT,GAAG,GAAG,GAAGK,OAAO,GAAGH,MAAM,IAAI;IAC1CI,MAAM,CAACG,KAAK,CAACR,MAAM,GAAG,GAAGC,MAAM,GAAG,CAAC,IAAI;IACvCI,MAAM,CAACG,KAAK,CAACV,KAAK,GAAG,GAAGG,MAAM,GAAG,CAAC,IAAI;IACtC;IACA;IACA,IAAI5F,MAAM,CAACoG,KAAK,IAAI,IAAI,EAAE;MACtBJ,MAAM,CAACG,KAAK,CAACE,eAAe,GAAGrG,MAAM,CAACoG,KAAK;IAC/C;IACAJ,MAAM,CAACG,KAAK,CAACG,kBAAkB,GAAG,GAAG/D,aAAa,IAAI;IACtD,IAAI,CAAC4B,iBAAiB,CAACoC,WAAW,CAACP,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACA,MAAMQ,cAAc,GAAGC,MAAM,CAACC,gBAAgB,CAACV,MAAM,CAAC;IACtD,MAAMW,sBAAsB,GAAGH,cAAc,CAACI,kBAAkB;IAChE,MAAMC,sBAAsB,GAAGL,cAAc,CAACF,kBAAkB;IAChE;IACA;IACA;IACA;IACA;IACA,MAAMQ,mCAAmC,GAAGH,sBAAsB,KAAK,MAAM;IACzE;IACA;IACAE,sBAAsB,KAAK,IAAI,IAC/BA,sBAAsB,KAAK,QAAQ;IACnC;IACC1B,aAAa,CAACM,KAAK,KAAK,CAAC,IAAIN,aAAa,CAACQ,MAAM,KAAK,CAAE;IAC7D;IACA,MAAMoB,SAAS,GAAG,IAAIlH,SAAS,CAAC,IAAI,EAAEmG,MAAM,EAAEhG,MAAM,EAAE8G,mCAAmC,CAAC;IAC1F;IACA;IACA;IACA;IACAd,MAAM,CAACG,KAAK,CAACa,SAAS,GAAG,kBAAkB;IAC3CD,SAAS,CAAC7G,KAAK,GAAGN,WAAW,CAACqH,SAAS;IACvC,IAAI,CAACjH,MAAM,CAACkH,UAAU,EAAE;MACpB,IAAI,CAAC3C,0BAA0B,GAAGwC,SAAS;IAC/C;IACA,IAAII,cAAc,GAAG,IAAI;IACzB;IACA;IACA,IAAI,CAACL,mCAAmC,KAAKvE,aAAa,IAAI8C,eAAe,CAAC7C,YAAY,CAAC,EAAE;MACzF,IAAI,CAACyB,OAAO,CAAC1C,iBAAiB,CAAC,MAAM;QACjC,MAAM6F,eAAe,GAAGA,CAAA,KAAM;UAC1B;UACA,IAAID,cAAc,EAAE;YAChBA,cAAc,CAACE,aAAa,GAAG,IAAI;UACvC;UACAC,YAAY,CAACD,aAAa,CAAC;UAC3B,IAAI,CAACE,uBAAuB,CAACR,SAAS,CAAC;QAC3C,CAAC;QACD,MAAMS,kBAAkB,GAAGA,CAAA,KAAM,IAAI,CAACC,cAAc,CAACV,SAAS,CAAC;QAC/D;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMM,aAAa,GAAGK,UAAU,CAACF,kBAAkB,EAAEjF,aAAa,GAAG,GAAG,CAAC;QACzEyD,MAAM,CAACvE,gBAAgB,CAAC,eAAe,EAAE2F,eAAe,CAAC;QACzD;QACA;QACA;QACApB,MAAM,CAACvE,gBAAgB,CAAC,kBAAkB,EAAE+F,kBAAkB,CAAC;QAC/DL,cAAc,GAAG;UAAEC,eAAe;UAAEI,kBAAkB;UAAEH;QAAc,CAAC;MAC3E,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC/C,cAAc,CAACjD,GAAG,CAAC0F,SAAS,EAAEI,cAAc,CAAC;IAClD;IACA;IACA,IAAIL,mCAAmC,IAAI,CAACvE,aAAa,EAAE;MACvD,IAAI,CAACgF,uBAAuB,CAACR,SAAS,CAAC;IAC3C;IACA,OAAOA,SAAS;EACpB;EACA;EACAzG,aAAaA,CAACyG,SAAS,EAAE;IACrB;IACA,IAAIA,SAAS,CAAC7G,KAAK,KAAKN,WAAW,CAAC+H,UAAU,IAAIZ,SAAS,CAAC7G,KAAK,KAAKN,WAAW,CAACO,MAAM,EAAE;MACtF;IACJ;IACA,MAAMyH,QAAQ,GAAGb,SAAS,CAAChH,OAAO;IAClC,MAAMsF,eAAe,GAAG;MAAE,GAAG/C,4BAA4B;MAAE,GAAGyE,SAAS,CAAC/G,MAAM,CAACsF;IAAU,CAAC;IAC1F;IACA;IACAsC,QAAQ,CAACzB,KAAK,CAACG,kBAAkB,GAAG,GAAGjB,eAAe,CAAC7C,YAAY,IAAI;IACvEoF,QAAQ,CAACzB,KAAK,CAAC0B,OAAO,GAAG,GAAG;IAC5Bd,SAAS,CAAC7G,KAAK,GAAGN,WAAW,CAAC+H,UAAU;IACxC;IACA;IACA,IAAIZ,SAAS,CAAC9G,oCAAoC,IAAI,CAACoF,eAAe,CAAC7C,YAAY,EAAE;MACjF,IAAI,CAAC+E,uBAAuB,CAACR,SAAS,CAAC;IAC3C;EACJ;EACA;EACAe,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC7F,OAAO,CAAC8D,MAAM,IAAIA,MAAM,CAAC3F,OAAO,CAAC,CAAC,CAAC;EAChE;EACA;EACA2H,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACD,iBAAiB,CAAC,CAAC,CAAC7F,OAAO,CAAC8D,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,CAAChG,MAAM,CAACkH,UAAU,EAAE;QAC3BlB,MAAM,CAAC3F,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA;EACA4H,kBAAkBA,CAACrD,mBAAmB,EAAE;IACpC,MAAM7E,OAAO,GAAGP,aAAa,CAACoF,mBAAmB,CAAC;IAClD,IAAI,CAAC,IAAI,CAACV,SAAS,CAACY,SAAS,IAAI,CAAC/E,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACqE,eAAe,EAAE;MAC3E;IACJ;IACA;IACA,IAAI,CAAC8D,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC9D,eAAe,GAAGrE,OAAO;IAC9B;IACA;IACA4C,iBAAiB,CAACT,OAAO,CAACD,IAAI,IAAI;MAC9B8B,cAAc,CAACY,aAAa,CAAC9D,UAAU,CAAC,IAAI,CAACoD,OAAO,EAAEhC,IAAI,EAAElC,OAAO,EAAE,IAAI,CAAC;IAC9E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIsC,WAAWA,CAACN,KAAK,EAAE;IACf,IAAIA,KAAK,CAACE,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACkG,YAAY,CAACpG,KAAK,CAAC;IAC5B,CAAC,MACI,IAAIA,KAAK,CAACE,IAAI,KAAK,YAAY,EAAE;MAClC,IAAI,CAACmG,aAAa,CAACrG,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACsG,YAAY,CAAC,CAAC;IACvB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC5D,0BAA0B,EAAE;MAClC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACR,OAAO,CAAC1C,iBAAiB,CAAC,MAAM;QACjCqB,eAAe,CAACV,OAAO,CAACD,IAAI,IAAI;UAC5B,IAAI,CAACmC,eAAe,CAAC3C,gBAAgB,CAACQ,IAAI,EAAE,IAAI,EAAES,4BAA4B,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAAC+B,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACA;EACA8C,uBAAuBA,CAACR,SAAS,EAAE;IAC/B,IAAIA,SAAS,CAAC7G,KAAK,KAAKN,WAAW,CAACqH,SAAS,EAAE;MAC3C,IAAI,CAACqB,uBAAuB,CAACvB,SAAS,CAAC;IAC3C,CAAC,MACI,IAAIA,SAAS,CAAC7G,KAAK,KAAKN,WAAW,CAAC+H,UAAU,EAAE;MACjD,IAAI,CAACF,cAAc,CAACV,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACIuB,uBAAuBA,CAACvB,SAAS,EAAE;IAC/B,MAAMwB,2BAA2B,GAAGxB,SAAS,KAAK,IAAI,CAACxC,0BAA0B;IACjF,MAAM;MAAE2C;IAAW,CAAC,GAAGH,SAAS,CAAC/G,MAAM;IACvC+G,SAAS,CAAC7G,KAAK,GAAGN,WAAW,CAAC4I,OAAO;IACrC;IACA;IACA;IACA;IACA,IAAI,CAACtB,UAAU,KAAK,CAACqB,2BAA2B,IAAI,CAAC,IAAI,CAAClE,cAAc,CAAC,EAAE;MACvE0C,SAAS,CAAC1G,OAAO,CAAC,CAAC;IACvB;EACJ;EACA;EACAoH,cAAcA,CAACV,SAAS,EAAE;IACtB,MAAMI,cAAc,GAAG,IAAI,CAAC7C,cAAc,CAACpD,GAAG,CAAC6F,SAAS,CAAC,IAAI,IAAI;IACjE,IAAI,CAACzC,cAAc,CAAC1C,MAAM,CAACmF,SAAS,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAACzC,cAAc,CAACzC,IAAI,EAAE;MAC3B,IAAI,CAAC6C,cAAc,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAIqC,SAAS,KAAK,IAAI,CAACxC,0BAA0B,EAAE;MAC/C,IAAI,CAACA,0BAA0B,GAAG,IAAI;IAC1C;IACAwC,SAAS,CAAC7G,KAAK,GAAGN,WAAW,CAACO,MAAM;IACpC,IAAIgH,cAAc,KAAK,IAAI,EAAE;MACzBJ,SAAS,CAAChH,OAAO,CAAC+B,mBAAmB,CAAC,eAAe,EAAEqF,cAAc,CAACC,eAAe,CAAC;MACtFL,SAAS,CAAChH,OAAO,CAAC+B,mBAAmB,CAAC,kBAAkB,EAAEqF,cAAc,CAACK,kBAAkB,CAAC;MAC5F,IAAIL,cAAc,CAACE,aAAa,KAAK,IAAI,EAAE;QACvCC,YAAY,CAACH,cAAc,CAACE,aAAa,CAAC;MAC9C;IACJ;IACAN,SAAS,CAAChH,OAAO,CAAC0I,MAAM,CAAC,CAAC;EAC9B;EACA;EACAN,YAAYA,CAACpG,KAAK,EAAE;IAChB;IACA;IACA,MAAM2G,eAAe,GAAGpJ,+BAA+B,CAACyC,KAAK,CAAC;IAC9D,MAAM4G,gBAAgB,GAAG,IAAI,CAACnE,oBAAoB,IAC9CoE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrE,oBAAoB,GAAG/B,wBAAwB;IACrE,IAAI,CAAC,IAAI,CAACuB,OAAO,CAAC8E,cAAc,IAAI,CAACJ,eAAe,IAAI,CAACC,gBAAgB,EAAE;MACvE,IAAI,CAACtE,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACW,YAAY,CAACjD,KAAK,CAACgH,OAAO,EAAEhH,KAAK,CAACiH,OAAO,EAAE,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC;IAC9E;EACJ;EACA;EACAb,aAAaA,CAACrG,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACiC,OAAO,CAAC8E,cAAc,IAAI,CAACvJ,gCAAgC,CAACwC,KAAK,CAAC,EAAE;MAC1E;MACA;MACA;MACA,IAAI,CAACyC,oBAAoB,GAAGoE,IAAI,CAACC,GAAG,CAAC,CAAC;MACtC,IAAI,CAACxE,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,MAAM6E,OAAO,GAAGnH,KAAK,CAACoH,cAAc;MACpC;MACA;MACA,IAAID,OAAO,EAAE;QACT,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,IAAI,CAACpE,YAAY,CAACkE,OAAO,CAACE,CAAC,CAAC,CAACL,OAAO,EAAEG,OAAO,CAACE,CAAC,CAAC,CAACJ,OAAO,EAAE,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC;QACxF;MACJ;IACJ;EACJ;EACA;EACAZ,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAChE,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAAC0D,iBAAiB,CAAC,CAAC,CAAC7F,OAAO,CAAC8D,MAAM,IAAI;MACvC;MACA;MACA,MAAMsD,SAAS,GAAGtD,MAAM,CAAC9F,KAAK,KAAKN,WAAW,CAAC4I,OAAO,IACjDxC,MAAM,CAAChG,MAAM,CAACuJ,oBAAoB,IAAIvD,MAAM,CAAC9F,KAAK,KAAKN,WAAW,CAACqH,SAAU;MAClF,IAAI,CAACjB,MAAM,CAAChG,MAAM,CAACkH,UAAU,IAAIoC,SAAS,EAAE;QACxCtD,MAAM,CAAC3F,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA0H,iBAAiBA,CAAA,EAAG;IAChB,OAAOyB,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnF,cAAc,CAACoF,IAAI,CAAC,CAAC,CAAC;EACjD;EACA;EACAxB,oBAAoBA,CAAA,EAAG;IACnB,MAAMyB,OAAO,GAAG,IAAI,CAACvF,eAAe;IACpC,IAAIuF,OAAO,EAAE;MACThH,iBAAiB,CAACT,OAAO,CAACD,IAAI,IAAI8B,cAAc,CAACY,aAAa,CAAChD,aAAa,CAACM,IAAI,EAAE0H,OAAO,EAAE,IAAI,CAAC,CAAC;MAClG,IAAI,IAAI,CAAClF,0BAA0B,EAAE;QACjC7B,eAAe,CAACV,OAAO,CAACD,IAAI,IAAI0H,OAAO,CAAC7H,mBAAmB,CAACG,IAAI,EAAE,IAAI,EAAES,4BAA4B,CAAC,CAAC;QACtG,IAAI,CAAC+B,0BAA0B,GAAG,KAAK;MAC3C;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA,SAASoB,wBAAwBA,CAACZ,CAAC,EAAEC,CAAC,EAAE0E,IAAI,EAAE;EAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC/E,CAAC,GAAG2E,IAAI,CAACpE,IAAI,CAAC,EAAEsE,IAAI,CAACE,GAAG,CAAC/E,CAAC,GAAG2E,IAAI,CAACK,KAAK,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC9E,CAAC,GAAG0E,IAAI,CAAClE,GAAG,CAAC,EAAEoE,IAAI,CAACE,GAAG,CAAC9E,CAAC,GAAG0E,IAAI,CAACO,MAAM,CAAC,CAAC;EACzE,OAAOL,IAAI,CAACM,IAAI,CAACP,KAAK,GAAGA,KAAK,GAAGK,KAAK,GAAGA,KAAK,CAAC;AACnD;;AAEA;AACA,MAAMG,yBAAyB,gBAAG,IAAItL,cAAc,CAAC,2BAA2B,CAAC;AAAC,IAC5EuL,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZC,WAAW,GAAGvL,MAAM,CAACC,UAAU,CAAC;IAChCU,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C;IACAyG,KAAK;IACL;IACAoE,SAAS;IACT;AACJ;AACA;AACA;IACIjF,QAAQ;IACR;AACJ;AACA;AACA;AACA;IACIK,MAAM,GAAG,CAAC;IACV;AACJ;AACA;AACA;AACA;IACIN,SAAS;IACT;AACJ;AACA;AACA;IACI,IAAImF,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACE,KAAK,EAAE;MAChB,IAAIA,KAAK,EAAE;QACP,IAAI,CAAC3C,uBAAuB,CAAC,CAAC;MAClC;MACA,IAAI,CAAC0C,SAAS,GAAGC,KAAK;MACtB,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACvC;IACAF,SAAS,GAAG,KAAK;IACjB;AACJ;AACA;AACA;IACI,IAAIf,OAAOA,CAAA,EAAG;MACV,OAAO,IAAI,CAACkB,QAAQ,IAAI,IAAI,CAACN,WAAW,CAACO,aAAa;IAC1D;IACA,IAAInB,OAAOA,CAACA,OAAO,EAAE;MACjB,IAAI,CAACkB,QAAQ,GAAGlB,OAAO;MACvB,IAAI,CAACiB,4BAA4B,CAAC,CAAC;IACvC;IACAC,QAAQ;IACR;IACAE,eAAe;IACf;IACAC,cAAc;IACd;IACAC,cAAc,GAAG,KAAK;IACtB7K,WAAWA,CAAA,EAAG;MACV,MAAMU,MAAM,GAAG9B,MAAM,CAACE,MAAM,CAAC;MAC7B,MAAMgM,QAAQ,GAAGlM,MAAM,CAACN,QAAQ,CAAC;MACjC,MAAMyM,aAAa,GAAGnM,MAAM,CAACqL,yBAAyB,EAAE;QAAEe,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC3E,MAAMvG,QAAQ,GAAG7F,MAAM,CAACG,QAAQ,CAAC;MACjC;MACA;MACA,IAAI,CAAC6L,cAAc,GAAGG,aAAa,IAAI,CAAC,CAAC;MACzC,IAAI,CAACJ,eAAe,GAAG,IAAIhH,cAAc,CAAC,IAAI,EAAEjD,MAAM,EAAE,IAAI,CAACyJ,WAAW,EAAEW,QAAQ,EAAErG,QAAQ,CAAC;IACjG;IACAwG,QAAQA,CAAA,EAAG;MACP,IAAI,CAACJ,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACL,4BAA4B,CAAC,CAAC;IACvC;IACAU,WAAWA,CAAA,EAAG;MACV,IAAI,CAACP,eAAe,CAAC7C,oBAAoB,CAAC,CAAC;IAC/C;IACA;IACAJ,UAAUA,CAAA,EAAG;MACT,IAAI,CAACiD,eAAe,CAACjD,UAAU,CAAC,CAAC;IACrC;IACA;IACAE,uBAAuBA,CAAA,EAAG;MACtB,IAAI,CAAC+C,eAAe,CAAC/C,uBAAuB,CAAC,CAAC;IAClD;IACA;AACJ;AACA;AACA;IACI,IAAIiB,YAAYA,CAAA,EAAG;MACf,OAAO;QACH1D,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBK,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBQ,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBd,SAAS,EAAE;UACP,GAAG,IAAI,CAAC0F,cAAc,CAAC1F,SAAS;UAChC,IAAI,IAAI,CAAC3F,mBAAmB,GAAG;YAAE4C,aAAa,EAAE,CAAC;YAAEC,YAAY,EAAE;UAAE,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1E,GAAG,IAAI,CAAC8C;QACZ,CAAC;QACDiE,oBAAoB,EAAE,IAAI,CAACyB,cAAc,CAACzB;MAC9C,CAAC;IACL;IACA;AACJ;AACA;AACA;IACI,IAAIT,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC2B,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACO,cAAc,CAACP,QAAQ;IAC1D;IACA;IACAG,4BAA4BA,CAAA,EAAG;MAC3B,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACQ,cAAc,EAAE;QACvC,IAAI,CAACF,eAAe,CAAC9C,kBAAkB,CAAC,IAAI,CAAC0B,OAAO,CAAC;MACzD;IACJ;IACA;IACA4B,MAAMA,CAACC,SAAS,EAAEtG,CAAC,GAAG,CAAC,EAAElF,MAAM,EAAE;MAC7B,IAAI,OAAOwL,SAAS,KAAK,QAAQ,EAAE;QAC/B,OAAO,IAAI,CAACT,eAAe,CAAC/F,YAAY,CAACwG,SAAS,EAAEtG,CAAC,EAAE;UAAE,GAAG,IAAI,CAAC+D,YAAY;UAAE,GAAGjJ;QAAO,CAAC,CAAC;MAC/F,CAAC,MACI;QACD,OAAO,IAAI,CAAC+K,eAAe,CAAC/F,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;UAAE,GAAG,IAAI,CAACiE,YAAY;UAAE,GAAGuC;QAAU,CAAC,CAAC;MAC1F;IACJ;IACA,OAAO1I,IAAI,YAAA2I,kBAAAzI,iBAAA;MAAA,YAAAA,iBAAA,IAAwFsH,SAAS;IAAA;IAC5G,OAAOoB,IAAI,kBA7d8E/M,EAAE,CAAAgN,iBAAA;MAAA1J,IAAA,EA6dJqI,SAAS;MAAAnH,SAAA;MAAAC,SAAA;MAAAwI,QAAA;MAAAC,YAAA,WAAAC,uBAAArI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7dP9E,EAAE,CAAAoN,WAAA,yBAAArI,GAAA,CAAA8G,SA6dI,CAAC;QAAA;MAAA;MAAAwB,MAAA;QAAA5F,KAAA;QAAAoE,SAAA;QAAAjF,QAAA;QAAAK,MAAA;QAAAN,SAAA;QAAAmF,QAAA;QAAAd,OAAA;MAAA;MAAAsC,QAAA;IAAA;EACpG;EAAC,OA3HK3B,SAAS;AAAA;AA4Hf;EAAA,QAAAxG,SAAA,oBAAAA,SAAA;AAAA;AAiCA,SAASwG,SAAS,IAAI4B,CAAC,EAAEnI,cAAc,IAAIoI,CAAC,EAAE9B,yBAAyB,IAAI+B,CAAC,EAAExM,WAAW,IAAIyM,CAAC,EAAExM,SAAS,IAAIyM,CAAC,EAAEhK,4BAA4B,IAAIiK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}