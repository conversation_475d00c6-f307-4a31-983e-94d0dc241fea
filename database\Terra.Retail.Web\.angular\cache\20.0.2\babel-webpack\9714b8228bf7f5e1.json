{"ast": null, "code": "import { O as OverlayContainer } from './overlay-module-Bd2UplUU.mjs';\nexport { B as BlockScrollStrategy, b as CdkConnectedOverlay, C as CdkOverlayOrigin, p as CloseScrollStrategy, l as ConnectedOverlayPositionChange, j as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, i as OverlayConfig, w as OverlayKeyboardDispatcher, t as OverlayModule, u as OverlayOutsideClickDispatcher, e as OverlayPositionBuilder, d as OverlayRef, R as RepositionScrollStrategy, S as STANDARD_DROPDOWN_ADJACENT_POSITIONS, g as STANDARD_DROPDOWN_BELOW_POSITIONS, n as ScrollStrategyOptions, k as ScrollingVisibility, s as createBlockScrollStrategy, q as createCloseScrollStrategy, h as createFlexibleConnectedPositionStrategy, f as createGlobalPositionStrategy, r as createNoopScrollStrategy, c as createOverlayRef, o as createRepositionScrollStrategy, m as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-Bd2UplUU.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll, CdkScrollableModule as ɵɵCdkScrollableModule, CdkVirtualForOf as ɵɵCdkVirtualForOf, CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport, CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement, CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow } from './scrolling.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport '@angular/common';\nimport './platform-DNDzkVcI.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport 'rxjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './portal.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport 'rxjs/operators';\nimport './id-generator-LuoRZSid.mjs';\nimport './directionality-CChdj3az.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport './keycodes.mjs';\nimport './element-x4z00URv.mjs';\nimport './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nlet FullscreenOverlayContainer = /*#__PURE__*/(() => {\n  class FullscreenOverlayContainer extends OverlayContainer {\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _fullScreenEventName;\n    _cleanupFullScreenListener;\n    constructor() {\n      super();\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._cleanupFullScreenListener?.();\n    }\n    _createContainer() {\n      const eventName = this._getEventName();\n      super._createContainer();\n      this._adjustParentForFullscreenChange();\n      if (eventName) {\n        this._cleanupFullScreenListener?.();\n        this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n          this._adjustParentForFullscreenChange();\n        });\n      }\n    }\n    _adjustParentForFullscreenChange() {\n      if (this._containerElement) {\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n      }\n    }\n    _getEventName() {\n      if (!this._fullScreenEventName) {\n        const _document = this._document;\n        if (_document.fullscreenEnabled) {\n          this._fullScreenEventName = 'fullscreenchange';\n        } else if (_document.webkitFullscreenEnabled) {\n          this._fullScreenEventName = 'webkitfullscreenchange';\n        } else if (_document.mozFullScreenEnabled) {\n          this._fullScreenEventName = 'mozfullscreenchange';\n        } else if (_document.msFullscreenEnabled) {\n          this._fullScreenEventName = 'MSFullscreenChange';\n        }\n      }\n      return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n      const _document = this._document;\n      return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n    }\n    static ɵfac = function FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FullscreenOverlayContainer)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FullscreenOverlayContainer,\n      factory: FullscreenOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return FullscreenOverlayContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { FullscreenOverlayContainer, OverlayContainer };", "map": {"version": 3, "names": ["O", "OverlayContainer", "B", "BlockScrollStrategy", "b", "CdkConnectedOverlay", "C", "CdkOverlayOrigin", "p", "CloseScrollStrategy", "l", "ConnectedOverlayPositionChange", "j", "ConnectionPositionPair", "F", "FlexibleConnectedPositionStrategy", "G", "GlobalPositionStrategy", "N", "NoopScrollStrategy", "a", "Overlay", "i", "OverlayConfig", "w", "OverlayKeyboardDispatcher", "t", "OverlayModule", "u", "OverlayOutsideClickDispatcher", "e", "OverlayPositionBuilder", "d", "OverlayRef", "R", "RepositionScrollStrategy", "S", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "g", "STANDARD_DROPDOWN_BELOW_POSITIONS", "n", "ScrollStrategyOptions", "k", "ScrollingVisibility", "s", "createBlockScrollStrategy", "q", "createCloseScrollStrategy", "h", "createFlexibleConnectedPositionStrategy", "f", "createGlobalPositionStrategy", "r", "createNoopScrollStrategy", "c", "createOverlayRef", "o", "createRepositionScrollStrategy", "m", "validateHorizontalPosition", "v", "validateVerticalPosition", "i0", "inject", "RendererFactory2", "Injectable", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "CdkFixedSizeVirtualScroll", "ɵɵCdkFixedSizeVirtualScroll", "CdkScrollableModule", "ɵɵCdkScrollableModule", "CdkVirtualForOf", "ɵɵCdkVirtualForOf", "CdkVirtualScrollViewport", "ɵɵCdkVirtualScrollViewport", "CdkVirtualScrollableElement", "ɵɵCdkVirtualScrollableElement", "CdkVirtualScrollableWindow", "ɵɵCdkVirtualScrollableWindow", "<PERSON><PERSON>", "ɵɵDir", "FullscreenOverlayContainer", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_fullScreenEventName", "_cleanupFullScreenListener", "constructor", "ngOnDestroy", "_createContainer", "eventName", "_getEventName", "_adjustParentForFullscreenChange", "listen", "_containerElement", "fullscreenElement", "getFullscreenElement", "parent", "_document", "body", "append<PERSON><PERSON><PERSON>", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "ɵfac", "FullscreenOverlayContainer_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/overlay.mjs"], "sourcesContent": ["import { O as OverlayContainer } from './overlay-module-Bd2UplUU.mjs';\nexport { B as BlockScrollStrategy, b as CdkConnectedOverlay, C as CdkOverlayOrigin, p as CloseScrollStrategy, l as ConnectedOverlayPositionChange, j as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, i as OverlayConfig, w as OverlayKeyboardDispatcher, t as OverlayModule, u as OverlayOutsideClickDispatcher, e as OverlayPositionBuilder, d as OverlayRef, R as RepositionScrollStrategy, S as STANDARD_DROPDOWN_ADJACENT_POSITIONS, g as STANDARD_DROPDOWN_BELOW_POSITIONS, n as ScrollStrategyOptions, k as ScrollingVisibility, s as createBlockScrollStrategy, q as createCloseScrollStrategy, h as createFlexibleConnectedPositionStrategy, f as createGlobalPositionStrategy, r as createNoopScrollStrategy, c as createOverlayRef, o as createRepositionScrollStrategy, m as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-Bd2UplUU.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll, CdkScrollableModule as ɵɵCdkScrollableModule, CdkVirtualForOf as ɵɵCdkVirtualForOf, CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport, CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement, CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow } from './scrolling.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport '@angular/common';\nimport './platform-DNDzkVcI.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport 'rxjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './portal.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport 'rxjs/operators';\nimport './id-generator-LuoRZSid.mjs';\nimport './directionality-CChdj3az.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport './keycodes.mjs';\nimport './element-x4z00URv.mjs';\nimport './recycle-view-repeater-strategy-SfuyU210.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _fullScreenEventName;\n    _cleanupFullScreenListener;\n    constructor() {\n        super();\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._cleanupFullScreenListener?.();\n    }\n    _createContainer() {\n        const eventName = this._getEventName();\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        if (eventName) {\n            this._cleanupFullScreenListener?.();\n            this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n                this._adjustParentForFullscreenChange();\n            });\n        }\n    }\n    _adjustParentForFullscreenChange() {\n        if (this._containerElement) {\n            const fullscreenElement = this.getFullscreenElement();\n            const parent = fullscreenElement || this._document.body;\n            parent.appendChild(this._containerElement);\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FullscreenOverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { FullscreenOverlayContainer, OverlayContainer };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,gBAAgB,QAAQ,+BAA+B;AACrE,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,oCAAoC,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,uCAAuC,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,wBAAwB,QAAQ,+BAA+B;AAC/6B,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACpE,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,yBAAyB,IAAIC,2BAA2B,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,wBAAwB,IAAIC,0BAA0B,EAAEC,2BAA2B,IAAIC,6BAA6B,EAAEC,0BAA0B,IAAIC,4BAA4B,QAAQ,iBAAiB;AAChZ,SAASC,GAAG,IAAIC,KAAK,QAAQ,YAAY;AACzC,OAAO,iBAAiB;AACxB,OAAO,yBAAyB;AAChC,OAAO,2BAA2B;AAClC,OAAO,iCAAiC;AACxC,OAAO,6BAA6B;AACpC,OAAO,MAAM;AACb,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,cAAc;AACrB,OAAO,0BAA0B;AACjC,OAAO,gBAAgB;AACvB,OAAO,6BAA6B;AACpC,OAAO,+BAA+B;AACtC,OAAO,yBAAyB;AAChC,OAAO,gBAAgB;AACvB,OAAO,wBAAwB;AAC/B,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMC,0BAA0B;EAAhC,MAAMA,0BAA0B,SAASlF,gBAAgB,CAAC;IACtDmF,SAAS,GAAGrB,MAAM,CAACC,gBAAgB,CAAC,CAACqB,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAC/DC,oBAAoB;IACpBC,0BAA0B;IAC1BC,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;IACX;IACAC,WAAWA,CAAA,EAAG;MACV,KAAK,CAACA,WAAW,CAAC,CAAC;MACnB,IAAI,CAACF,0BAA0B,GAAG,CAAC;IACvC;IACAG,gBAAgBA,CAAA,EAAG;MACf,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACtC,KAAK,CAACF,gBAAgB,CAAC,CAAC;MACxB,IAAI,CAACG,gCAAgC,CAAC,CAAC;MACvC,IAAIF,SAAS,EAAE;QACX,IAAI,CAACJ,0BAA0B,GAAG,CAAC;QACnC,IAAI,CAACA,0BAA0B,GAAG,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,UAAU,EAAEH,SAAS,EAAE,MAAM;UACjF,IAAI,CAACE,gCAAgC,CAAC,CAAC;QAC3C,CAAC,CAAC;MACN;IACJ;IACAA,gCAAgCA,CAAA,EAAG;MAC/B,IAAI,IAAI,CAACE,iBAAiB,EAAE;QACxB,MAAMC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;QACrD,MAAMC,MAAM,GAAGF,iBAAiB,IAAI,IAAI,CAACG,SAAS,CAACC,IAAI;QACvDF,MAAM,CAACG,WAAW,CAAC,IAAI,CAACN,iBAAiB,CAAC;MAC9C;IACJ;IACAH,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAACN,oBAAoB,EAAE;QAC5B,MAAMa,SAAS,GAAG,IAAI,CAACA,SAAS;QAChC,IAAIA,SAAS,CAACG,iBAAiB,EAAE;UAC7B,IAAI,CAAChB,oBAAoB,GAAG,kBAAkB;QAClD,CAAC,MACI,IAAIa,SAAS,CAACI,uBAAuB,EAAE;UACxC,IAAI,CAACjB,oBAAoB,GAAG,wBAAwB;QACxD,CAAC,MACI,IAAIa,SAAS,CAACK,oBAAoB,EAAE;UACrC,IAAI,CAAClB,oBAAoB,GAAG,qBAAqB;QACrD,CAAC,MACI,IAAIa,SAAS,CAACM,mBAAmB,EAAE;UACpC,IAAI,CAACnB,oBAAoB,GAAG,oBAAoB;QACpD;MACJ;MACA,OAAO,IAAI,CAACA,oBAAoB;IACpC;IACA;AACJ;AACA;AACA;IACIW,oBAAoBA,CAAA,EAAG;MACnB,MAAME,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,OAAQA,SAAS,CAACH,iBAAiB,IAC/BG,SAAS,CAACO,uBAAuB,IACjCP,SAAS,CAACQ,oBAAoB,IAC9BR,SAAS,CAACS,mBAAmB,IAC7B,IAAI;IACZ;IACA,OAAOC,IAAI,YAAAC,mCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwF5B,0BAA0B;IAAA;IAC7H,OAAO6B,KAAK,kBAD6ElD,EAAE,CAAAmD,kBAAA;MAAAC,KAAA,EACY/B,0BAA0B;MAAAgC,OAAA,EAA1BhC,0BAA0B,CAAA0B,IAAA;MAAAO,UAAA,EAAc;IAAM;EACzJ;EAAC,OA7DKjC,0BAA0B;AAAA;AA8DhC;EAAA,QAAAkC,SAAA,oBAAAA,SAAA;AAAA;AAKA,SAASlC,0BAA0B,EAAElF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}