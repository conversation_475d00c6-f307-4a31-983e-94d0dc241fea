using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الأدوار
    /// </summary>
    public class Role : BaseEntity
    {
        /// <summary>
        /// اسم الدور بالعربية
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الدور بالإنجليزية
        /// </summary>
        [MaxLength(50)]
        public string? NameEn { get; set; }

        /// <summary>
        /// كود الدور (فريد)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// وصف الدور
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// هل الدور نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الدور افتراضي للمستخدمين الجدد
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// هل الدور مدمج في النظام (لا يمكن حذفه)
        /// </summary>
        public bool IsSystemRole { get; set; } = false;

        /// <summary>
        /// مستوى الدور (للترتيب الهرمي)
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// لون الدور (Hex)
        /// </summary>
        [MaxLength(7)]
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة الدور
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// الحد الأقصى لعدد المستخدمين في هذا الدور
        /// </summary>
        public int? MaxUsers { get; set; }

        /// <summary>
        /// عدد المستخدمين الحاليين في الدور
        /// </summary>
        public int CurrentUserCount { get; set; } = 0;

        // Navigation Properties
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
}
