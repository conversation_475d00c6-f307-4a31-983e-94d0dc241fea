{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nlet MatOptionModule = /*#__PURE__*/(() => {\n  class MatOptionModule {\n    static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatR<PERSON>pleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption]\n    });\n  }\n  return MatOptionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatOptionModule as M };", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatRippleModule", "MatCommonModule", "MatOption", "a", "MatOptgroup", "MatPseudoCheckboxModule", "MatOptionModule", "ɵfac", "MatOptionModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/index-DwiL-HGk.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\n\nclass MatOptionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup], exports: [MatOption, MatOptgroup] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n                    exports: [MatOption, MatOptgroup],\n                }]\n        }] });\n\nexport { MatOptionModule as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,eAAe,QAAQ,sBAAsB;AAC3D,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AACnE,SAASF,CAAC,IAAIG,SAAS,EAAEC,CAAC,IAAIC,WAAW,QAAQ,uBAAuB;AACxE,SAASL,CAAC,IAAIM,uBAAuB,QAAQ,uCAAuC;AAAC,IAE/EC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOC,IAAI,YAAAC,wBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,eAAe;IAAA;IAClH,OAAOI,IAAI,kBAD8Eb,EAAE,CAAAc,gBAAA;MAAAC,IAAA,EACSN;IAAe;IACnH,OAAOO,IAAI,kBAF8EhB,EAAE,CAAAiB,gBAAA;MAAAC,OAAA,GAEoCf,eAAe,EAAEC,eAAe,EAAEI,uBAAuB,EAAEH,SAAS;IAAA;EACvM;EAAC,OAJKI,eAAe;AAAA;AAKrB;EAAA,QAAAU,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAASV,eAAe,IAAIP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}