{"ast": null, "code": "export { P as Platform } from './platform-DNDzkVcI.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport '@angular/common';\nlet PlatformModule = /*#__PURE__*/(() => {\n  class PlatformModule {\n    static ɵfac = function PlatformModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PlatformModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return PlatformModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\nexport { PlatformModule, getSupportedInputTypes };", "map": {"version": 3, "names": ["P", "Platform", "i0", "NgModule", "n", "normalizePassiveListenerOptions", "s", "supportsPassiveEventListeners", "R", "RtlScrollAxisType", "g", "getRtlScrollAxisType", "supportsScrollBehavior", "_", "_getEventTarget", "c", "_getFocusedElementPierceShadowDom", "a", "_getShadowRoot", "b", "_supportsShadowDom", "_isTestEnvironment", "PlatformModule", "ɵfac", "PlatformModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "supportedInputTypes", "candidateInputTypes", "getSupportedInputTypes", "document", "Set", "featureTestInput", "createElement", "filter", "value", "setAttribute"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/platform.mjs"], "sourcesContent": ["export { P as Platform } from './platform-DNDzkVcI.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport '@angular/common';\n\nclass PlatformModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n    // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n    // first changing it to something else:\n    // The specified value \"\" does not conform to the required format.\n    // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n    'color',\n    'button',\n    'checkbox',\n    'date',\n    'datetime-local',\n    'email',\n    'file',\n    'hidden',\n    'image',\n    'month',\n    'number',\n    'password',\n    'radio',\n    'range',\n    'reset',\n    'search',\n    'submit',\n    'tel',\n    'text',\n    'time',\n    'url',\n    'week',\n];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n    // Result is cached.\n    if (supportedInputTypes) {\n        return supportedInputTypes;\n    }\n    // We can't check if an input type is not supported until we're on the browser, so say that\n    // everything is supported when not on the browser. We don't use `Platform` here since it's\n    // just a helper function and can't inject it.\n    if (typeof document !== 'object' || !document) {\n        supportedInputTypes = new Set(candidateInputTypes);\n        return supportedInputTypes;\n    }\n    let featureTestInput = document.createElement('input');\n    supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n        featureTestInput.setAttribute('type', value);\n        return featureTestInput.type === value;\n    }));\n    return supportedInputTypes;\n}\n\nexport { PlatformModule, getSupportedInputTypes };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,6BAA6B,QAAQ,kCAAkC;AAC3H,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEL,CAAC,IAAIM,sBAAsB,QAAQ,0BAA0B;AACzH,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,kBAAkB,QAAQ,2BAA2B;AACtJ,SAASP,CAAC,IAAIQ,kBAAkB,QAAQ,iCAAiC;AACzE,OAAO,iBAAiB;AAAC,IAEnBC,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB,OAAOC,IAAI,YAAAC,uBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,cAAc;IAAA;IACjH,OAAOI,IAAI,kBAD8ExB,EAAE,CAAAyB,gBAAA;MAAAC,IAAA,EACSN;IAAc;IAClH,OAAOO,IAAI,kBAF8E3B,EAAE,CAAA4B,gBAAA;EAG/F;EAAC,OAJKR,cAAc;AAAA;AAKpB;EAAA,QAAAS,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA,IAAIC,mBAAmB;AACvB;AACA,MAAMC,mBAAmB,GAAG;AACxB;AACA;AACA;AACA;AACA,OAAO,EACP,QAAQ,EACR,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,CACT;AACD;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B;EACA,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB;EAC9B;EACA;EACA;EACA;EACA,IAAI,OAAOG,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3CH,mBAAmB,GAAG,IAAII,GAAG,CAACH,mBAAmB,CAAC;IAClD,OAAOD,mBAAmB;EAC9B;EACA,IAAIK,gBAAgB,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACtDN,mBAAmB,GAAG,IAAII,GAAG,CAACH,mBAAmB,CAACM,MAAM,CAACC,KAAK,IAAI;IAC9DH,gBAAgB,CAACI,YAAY,CAAC,MAAM,EAAED,KAAK,CAAC;IAC5C,OAAOH,gBAAgB,CAACT,IAAI,KAAKY,KAAK;EAC1C,CAAC,CAAC,CAAC;EACH,OAAOR,mBAAmB;AAC9B;AAEA,SAASV,cAAc,EAAEY,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}