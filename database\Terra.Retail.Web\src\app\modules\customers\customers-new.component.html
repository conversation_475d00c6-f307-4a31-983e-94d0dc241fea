<!-- Terra Retail ERP - Professional Customers Module -->
<div class="customers-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">إدارة العملاء</h1>
        <p class="page-subtitle">إدارة شاملة لجميع عملاء المتجر</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" class="add-btn" (click)="openAddCustomer()">
          <mat-icon>person_add</mat-icon>
          <span>إضافة عميل جديد</span>
        </button>
        <button mat-stroked-button class="export-btn" (click)="exportCustomers()">
          <mat-icon>file_download</mat-icon>
          <span>تصدير</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <mat-card class="filters-card">
      <mat-card-content>
        <div class="filters-grid">
          
          <!-- Search -->
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>البحث</mat-label>
            <input matInput placeholder="ابحث بالاسم، الهاتف، أو الرقم..." 
                   [(ngModel)]="searchTerm" (input)="onSearch()">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <!-- Customer Type Filter -->
          <mat-form-field appearance="outline">
            <mat-label>نوع العميل</mat-label>
            <mat-select [(ngModel)]="selectedCustomerType" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الأنواع</mat-option>
              <mat-option *ngFor="let type of customerTypes" [value]="type.id">
                {{ type.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Governorate Filter -->
          <mat-form-field appearance="outline">
            <mat-label>المحافظة</mat-label>
            <mat-select [(ngModel)]="selectedGovernorate" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع المحافظات</mat-option>
              <mat-option *ngFor="let gov of governorates" [value]="gov.id">
                {{ gov.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Status Filter -->
          <mat-form-field appearance="outline">
            <mat-label>الحالة</mat-label>
            <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الحالات</mat-option>
              <mat-option value="active">نشط</mat-option>
              <mat-option value="inactive">غير نشط</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Clear Filters -->
          <button mat-stroked-button class="clear-filters-btn" (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            <span>مسح الفلاتر</span>
          </button>

        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-section">
    <div class="stats-grid">
      
      <div class="stat-card total-customers">
        <div class="stat-icon">
          <mat-icon>people</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalCustomers }}</div>
          <div class="stat-label">إجمالي العملاء</div>
        </div>
      </div>

      <div class="stat-card active-customers">
        <div class="stat-icon">
          <mat-icon>person</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.activeCustomers }}</div>
          <div class="stat-label">العملاء النشطين</div>
        </div>
      </div>

      <div class="stat-card new-customers">
        <div class="stat-icon">
          <mat-icon>person_add</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.newCustomersThisMonth }}</div>
          <div class="stat-label">عملاء جدد هذا الشهر</div>
        </div>
      </div>

      <div class="stat-card vip-customers">
        <div class="stat-icon">
          <mat-icon>star</mat-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.vipCustomers }}</div>
          <div class="stat-label">عملاء VIP</div>
        </div>
      </div>

    </div>
  </div>

  <!-- Customers Table -->
  <div class="table-section">
    <mat-card class="table-card">
      
      <!-- Table Header -->
      <div class="table-header">
        <div class="table-title">
          <h3>قائمة العملاء</h3>
          <span class="results-count">({{ filteredCustomers.length }} عميل)</span>
        </div>
        <div class="table-actions">
          <mat-form-field appearance="outline" class="page-size-field">
            <mat-label>عدد الصفوف</mat-label>
            <mat-select [(ngModel)]="pageSize" (selectionChange)="onPageSizeChange()">
              <mat-option value="10">10</mat-option>
              <mat-option value="25">25</mat-option>
              <mat-option value="50">50</mat-option>
              <mat-option value="100">100</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Table Content -->
      <div class="table-container">
        <table mat-table [dataSource]="paginatedCustomers" class="customers-table" matSort>

          <!-- Customer Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>كود العميل</th>
            <td mat-cell *matCellDef="let customer">
              <span class="customer-code">{{ customer.code }}</span>
            </td>
          </ng-container>

          <!-- Customer Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم العميل</th>
            <td mat-cell *matCellDef="let customer">
              <div class="customer-info">
                <div class="customer-name">{{ customer.name }}</div>
                <div class="customer-type">{{ customer.customerTypeName }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Contact Info Column -->
          <ng-container matColumnDef="contact">
            <th mat-header-cell *matHeaderCellDef>معلومات الاتصال</th>
            <td mat-cell *matCellDef="let customer">
              <div class="contact-info">
                <div class="phone" *ngIf="customer.phone">
                  <mat-icon>phone</mat-icon>
                  <span>{{ customer.phone }}</span>
                </div>
                <div class="email" *ngIf="customer.email">
                  <mat-icon>email</mat-icon>
                  <span>{{ customer.email }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Address Column -->
          <ng-container matColumnDef="address">
            <th mat-header-cell *matHeaderCellDef>العنوان</th>
            <td mat-cell *matCellDef="let customer">
              <div class="address-info">
                <div class="governorate">{{ customer.governorateName }}</div>
                <div class="address" *ngIf="customer.address">{{ customer.address }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Balance Column -->
          <ng-container matColumnDef="balance">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد</th>
            <td mat-cell *matCellDef="let customer">
              <div class="balance" [class]="getBalanceClass(customer.balance)">
                {{ customer.balance | number:'1.2-2' }} جنيه
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>الحالة</th>
            <td mat-cell *matCellDef="let customer">
              <span class="status-badge" [class]="customer.isActive ? 'active' : 'inactive'">
                {{ customer.isActive ? 'نشط' : 'غير نشط' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let customer">
              <div class="actions-buttons">
                <button mat-icon-button color="primary" 
                        matTooltip="عرض التفاصيل" 
                        (click)="viewCustomer(customer)">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button color="accent" 
                        matTooltip="تعديل" 
                        (click)="editCustomer(customer)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" 
                        matTooltip="حذف" 
                        (click)="deleteCustomer(customer)">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
              class="table-row" (click)="viewCustomer(row)"></tr>

        </table>
      </div>

      <!-- Pagination -->
      <mat-paginator 
        [length]="filteredCustomers.length"
        [pageSize]="pageSize"
        [pageSizeOptions]="[10, 25, 50, 100]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>

    </mat-card>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل البيانات...</p>
  </div>

</div>
