<!-- Terra Retail ERP - Supplier Management -->
<div class="supplier-management-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-text">
        <h1 class="page-title">إدارة الموردين</h1>
        <p class="page-subtitle">نظام شامل لإدارة جميع عمليات الموردين</p>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="navigateToSection('/suppliers/add')">
          <mat-icon>add</mat-icon>
          <span>إضافة مورد جديد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="statistics-section" *ngIf="!isLoading">
    <div class="stats-grid">
      
      <!-- Total Suppliers -->
      <mat-card class="stat-card total-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>people</mat-icon>
          </div>
          <div class="stat-info">
            <h3>إجمالي الموردين</h3>
            <p class="stat-number">{{ stats.totalSuppliers }}</p>
            <span class="stat-label">مورد مسجل</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Active Suppliers -->
      <mat-card class="stat-card active-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-info">
            <h3>الموردين النشطين</h3>
            <p class="stat-number">{{ stats.activeSuppliers }}</p>
            <span class="stat-label">مورد نشط</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Inactive Suppliers -->
      <mat-card class="stat-card inactive-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>cancel</mat-icon>
          </div>
          <div class="stat-info">
            <h3>الموردين غير النشطين</h3>
            <p class="stat-number">{{ stats.inactiveSuppliers }}</p>
            <span class="stat-label">مورد غير نشط</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Total Balance -->
      <mat-card class="stat-card balance-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>account_balance_wallet</mat-icon>
          </div>
          <div class="stat-info">
            <h3>إجمالي الأرصدة</h3>
            <p class="stat-number" [ngClass]="getBalanceClass(stats.totalBalance)">
              {{ formatCurrency(stats.totalBalance) }}
            </p>
            <span class="stat-label">رصيد إجمالي</span>
          </div>
        </mat-card-content>
      </mat-card>

    </div>
  </div>

  <!-- Management Sections -->
  <div class="management-sections">
    <h2 class="section-title">أقسام إدارة الموردين</h2>
    
    <div class="sections-grid">
      
      <mat-card *ngFor="let section of managementSections" 
                class="management-card" 
                [ngClass]="section.color + '-card'"
                (click)="navigateToSection(section.route)">
        
        <mat-card-header>
          <div class="card-icon" [ngClass]="section.color + '-icon'">
            <mat-icon>{{ section.icon }}</mat-icon>
          </div>
          <mat-card-title>{{ section.title }}</mat-card-title>
          <mat-card-subtitle>{{ section.description }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="actions-list">
            <div *ngFor="let action of section.actions" 
                 class="action-item"
                 (click)="$event.stopPropagation(); navigateToAction(action.route)">
              <mat-icon>{{ action.icon }}</mat-icon>
              <span>{{ action.title }}</span>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button [color]="section.color">
            <span>دخول القسم</span>
            <mat-icon>arrow_forward</mat-icon>
          </button>
        </mat-card-actions>

      </mat-card>

    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions-section">
    <h2 class="section-title">إجراءات سريعة</h2>
    
    <div class="quick-actions-grid">
      
      <mat-card class="quick-action-card" (click)="navigateToSection('/suppliers')">
        <mat-card-content>
          <mat-icon>list</mat-icon>
          <h3>عرض جميع الموردين</h3>
          <p>استعراض قائمة شاملة بجميع الموردين</p>
        </mat-card-content>
      </mat-card>

      <mat-card class="quick-action-card" (click)="navigateToSection('/suppliers/add')">
        <mat-card-content>
          <mat-icon>person_add</mat-icon>
          <h3>إضافة مورد جديد</h3>
          <p>تسجيل مورد جديد في النظام</p>
        </mat-card-content>
      </mat-card>

      <mat-card class="quick-action-card" (click)="navigateToSection('/suppliers/reports')">
        <mat-card-content>
          <mat-icon>analytics</mat-icon>
          <h3>تقارير الموردين</h3>
          <p>عرض تقارير شاملة عن أداء الموردين</p>
        </mat-card-content>
      </mat-card>

      <mat-card class="quick-action-card" (click)="navigateToSection('/suppliers/payments')">
        <mat-card-content>
          <mat-icon>payment</mat-icon>
          <h3>إدارة المدفوعات</h3>
          <p>متابعة المدفوعات والمستحقات</p>
        </mat-card-content>
      </mat-card>

    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل بيانات الموردين...</p>
  </div>

</div>
