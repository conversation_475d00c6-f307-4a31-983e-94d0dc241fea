{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, TemplateRef, InjectionToken, signal, EventEmitter, computed, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChild, ContentChildren, ViewChild, Input, Output, ChangeDetectorRef, QueryList, numberAttribute, NgModule } from '@angular/core';\nimport { ControlContainer } from '@angular/forms';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { F as FocusKeyManager } from './focus-key-manager-CPmlyB_c.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { S as SPACE, c as ENTER } from './keycodes-CpHkExLC.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { BidiModule } from './bidi.mjs';\nimport './list-key-manager-C7tp3RbG.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nconst _c0 = [\"*\"];\nfunction CdkStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nlet CdkStepHeader = /*#__PURE__*/(() => {\n  class CdkStepHeader {\n    _elementRef = inject(ElementRef);\n    constructor() {}\n    /** Focuses the step header. */\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    static ɵfac = function CdkStepHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepHeader)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepHeader,\n      selectors: [[\"\", \"cdkStepHeader\", \"\"]],\n      hostAttrs: [\"role\", \"tab\"]\n    });\n  }\n  return CdkStepHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepLabel = /*#__PURE__*/(() => {\n  class CdkStepLabel {\n    template = inject(TemplateRef);\n    constructor() {}\n    static ɵfac = function CdkStepLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepLabel,\n      selectors: [[\"\", \"cdkStepLabel\", \"\"]]\n    });\n  }\n  return CdkStepLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {\n  /** Index of the step now selected. */\n  selectedIndex;\n  /** Index of the step previously selected. */\n  previouslySelectedIndex;\n  /** The step instance now selected. */\n  selectedStep;\n  /** The step instance previously selected. */\n  previouslySelectedStep;\n}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error'\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = /*#__PURE__*/new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nlet CdkStep = /*#__PURE__*/(() => {\n  class CdkStep {\n    _stepperOptions;\n    _stepper = inject(CdkStepper);\n    _displayDefaultIndicatorType;\n    /** Template for step label if it exists. */\n    stepLabel;\n    /** Forms that have been projected into the step. */\n    _childForms;\n    /** Template for step content. */\n    content;\n    /** The top level abstract control of the step. */\n    stepControl;\n    /** Whether user has attempted to move away from the step. */\n    get interacted() {\n      return this._interacted();\n    }\n    set interacted(value) {\n      this._interacted.set(value);\n    }\n    _interacted = signal(false);\n    /** Emits when the user has attempted to move away from the step. */\n    interactedStream = new EventEmitter();\n    /** Plain text label of the step. */\n    label;\n    /** Error message to display when there's an error. */\n    errorMessage;\n    /** Aria label for the tab. */\n    ariaLabel;\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    ariaLabelledby;\n    /** State of the step. */\n    get state() {\n      return this._state();\n    }\n    set state(value) {\n      this._state.set(value);\n    }\n    _state = signal(undefined);\n    /** Whether the user can return to this step once it has been marked as completed. */\n    get editable() {\n      return this._editable();\n    }\n    set editable(value) {\n      this._editable.set(value);\n    }\n    _editable = signal(true);\n    /** Whether the completion of step is optional. */\n    optional = false;\n    /** Whether step is marked as completed. */\n    get completed() {\n      const override = this._completedOverride();\n      const interacted = this._interacted();\n      if (override != null) {\n        return override;\n      }\n      return interacted && (!this.stepControl || this.stepControl.valid);\n    }\n    set completed(value) {\n      this._completedOverride.set(value);\n    }\n    _completedOverride = signal(null);\n    /** Current index of the step within the stepper. */\n    index = signal(-1);\n    /** Whether the step is selected. */\n    isSelected = computed(() => this._stepper.selectedIndex === this.index());\n    /** Type of indicator that should be shown for the step. */\n    indicatorType = computed(() => {\n      const selected = this.isSelected();\n      const completed = this.completed;\n      const defaultState = this._state() ?? STEP_STATE.NUMBER;\n      const editable = this._editable();\n      if (this._showError() && this.hasError && !selected) {\n        return STEP_STATE.ERROR;\n      }\n      if (this._displayDefaultIndicatorType) {\n        if (!completed || selected) {\n          return STEP_STATE.NUMBER;\n        }\n        return editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n      } else {\n        if (completed && !selected) {\n          return STEP_STATE.DONE;\n        } else if (completed && selected) {\n          return defaultState;\n        }\n        return editable && selected ? STEP_STATE.EDIT : defaultState;\n      }\n    });\n    /** Whether the user can navigate to the step. */\n    isNavigable = computed(() => {\n      const isSelected = this.isSelected();\n      const isCompleted = this.completed;\n      return isCompleted || isSelected || !this._stepper.linear;\n    });\n    /** Whether step has an error. */\n    get hasError() {\n      const customError = this._customError();\n      return customError == null ? this._getDefaultError() : customError;\n    }\n    set hasError(value) {\n      this._customError.set(value);\n    }\n    _customError = signal(null);\n    _getDefaultError() {\n      return this.interacted && !!this.stepControl?.invalid;\n    }\n    constructor() {\n      const stepperOptions = inject(STEPPER_GLOBAL_OPTIONS, {\n        optional: true\n      });\n      this._stepperOptions = stepperOptions ? stepperOptions : {};\n      this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n      this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n      this._interacted.set(false);\n      if (this._completedOverride() != null) {\n        this._completedOverride.set(false);\n      }\n      if (this._customError() != null) {\n        this._customError.set(false);\n      }\n      if (this.stepControl) {\n        // Reset the forms since the default error state matchers will show errors on submit and we\n        // want the form to be back to its initial state (see #29781). Submitted state is on the\n        // individual directives, rather than the control, so we need to reset them ourselves.\n        this._childForms?.forEach(form => form.resetForm?.());\n        this.stepControl.reset();\n      }\n    }\n    ngOnChanges() {\n      // Since basically all inputs of the MatStep get proxied through the view down to the\n      // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n      this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n      if (!this._interacted()) {\n        this._interacted.set(true);\n        this.interactedStream.emit(this);\n      }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n      // We want to show the error state either if the user opted into/out of it using the\n      // global options, or if they've explicitly set it through the `hasError` input.\n      return this._stepperOptions.showError ?? this._customError() != null;\n    }\n    static ɵfac = function CdkStep_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStep)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkStep,\n      selectors: [[\"cdk-step\"]],\n      contentQueries: function CdkStep_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStepLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex,\n          // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n          // provides themselves as such, but we don't want to have a concrete reference to both of\n          // the directives. The type is marked as `Partial` in case we run into a class that provides\n          // itself as `ControlContainer` but doesn't have the same interface as the directives.\n          ControlContainer, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._childForms = _t);\n        }\n      },\n      viewQuery: function CdkStep_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        stepControl: \"stepControl\",\n        label: \"label\",\n        errorMessage: \"errorMessage\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        state: \"state\",\n        editable: [2, \"editable\", \"editable\", booleanAttribute],\n        optional: [2, \"optional\", \"optional\", booleanAttribute],\n        completed: [2, \"completed\", \"completed\", booleanAttribute],\n        hasError: [2, \"hasError\", \"hasError\", booleanAttribute]\n      },\n      outputs: {\n        interactedStream: \"interacted\"\n      },\n      exportAs: [\"cdkStep\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function CdkStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, CdkStep_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return CdkStep;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepper = /*#__PURE__*/(() => {\n  class CdkStepper {\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Used for managing keyboard focus. */\n    _keyManager;\n    /** Full list of steps inside the stepper, including inside nested steppers. */\n    _steps;\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n    steps = new QueryList();\n    /** The list of step headers of the steps in the stepper. */\n    _stepHeader;\n    /** List of step headers sorted based on their DOM order. */\n    _sortedHeaders = new QueryList();\n    /** Whether the validity of previous steps should be checked or not. */\n    linear = false;\n    /** The index of the selected step. */\n    get selectedIndex() {\n      return this._selectedIndex();\n    }\n    set selectedIndex(index) {\n      if (this._steps) {\n        // Ensure that the index can't be out of bounds.\n        if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n        }\n        if (this.selectedIndex !== index) {\n          this.selected?._markAsInteracted();\n          if (!this._anyControlsInvalidOrPending(index) && (index >= this.selectedIndex || this.steps.toArray()[index].editable)) {\n            this._updateSelectedItemIndex(index);\n          }\n        }\n      } else {\n        this._selectedIndex.set(index);\n      }\n    }\n    _selectedIndex = signal(0);\n    /** The step that is selected. */\n    get selected() {\n      return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n      this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Event emitted when the selected step has changed. */\n    selectionChange = new EventEmitter();\n    /** Output to support two-way binding on `[(selectedIndex)]` */\n    selectedIndexChange = new EventEmitter();\n    /** Used to track unique ID for each stepper component. */\n    _groupId = inject(_IdGenerator).getId('cdk-stepper-');\n    /** Orientation of the stepper. */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(value) {\n      // This is a protected method so that `MatStepper` can hook into it.\n      this._orientation = value;\n      if (this._keyManager) {\n        this._keyManager.withVerticalOrientation(value === 'vertical');\n      }\n    }\n    _orientation = 'horizontal';\n    constructor() {}\n    ngAfterContentInit() {\n      this._steps.changes.pipe(startWith(this._steps), takeUntil(this._destroyed)).subscribe(steps => {\n        this.steps.reset(steps.filter(step => step._stepper === this));\n        this.steps.forEach((step, index) => step.index.set(index));\n        this.steps.notifyOnChanges();\n      });\n    }\n    ngAfterViewInit() {\n      // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n      // Material stepper, they won't appear in the `QueryList` in the same order as they're\n      // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n      // them manually to ensure that they're correct. Alternatively, we can change the Material\n      // template to inline the headers in the `ngFor`, but that'll result in a lot of\n      // code duplication. See #23539.\n      this._stepHeader.changes.pipe(startWith(this._stepHeader), takeUntil(this._destroyed)).subscribe(headers => {\n        this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n          const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n          // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n          // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n          // tslint:disable-next-line:no-bitwise\n          return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        }));\n        this._sortedHeaders.notifyOnChanges();\n      });\n      // Note that while the step headers are content children by default, any components that\n      // extend this one might have them as view children. We initialize the keyboard handling in\n      // AfterViewInit so we're guaranteed for both view and content children to be defined.\n      this._keyManager = new FocusKeyManager(this._sortedHeaders).withWrap().withHomeAndEnd().withVerticalOrientation(this._orientation === 'vertical');\n      // The selected index may have changed between when the component was created and when the\n      // key manager was initialized. Use `updateActiveItem` so it's correct, but it doesn't steal\n      // away focus from the user.\n      this._keyManager.updateActiveItem(this.selectedIndex);\n      (this._dir ? this._dir.change : of()).pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed)).subscribe(direction => this._keyManager?.withHorizontalOrientation(direction));\n      this._keyManager.updateActiveItem(this.selectedIndex);\n      // No need to `takeUntil` here, because we're the ones destroying `steps`.\n      this.steps.changes.subscribe(() => {\n        if (!this.selected) {\n          this._selectedIndex.set(Math.max(this.selectedIndex - 1, 0));\n        }\n      });\n      // The logic which asserts that the selected index is within bounds doesn't run before the\n      // steps are initialized, because we don't how many steps there are yet so we may have an\n      // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n      if (!this._isValidIndex(this.selectedIndex)) {\n        this._selectedIndex.set(0);\n      }\n      // For linear step and selected index is greater than zero,\n      // set all the previous steps to interacted so that we can navigate to previous steps.\n      if (this.linear && this.selectedIndex > 0) {\n        const visitedSteps = this.steps.toArray().slice(0, this._selectedIndex());\n        for (const step of visitedSteps) {\n          step._markAsInteracted();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this.steps.destroy();\n      this._sortedHeaders.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n      this.selectedIndex = Math.min(this._selectedIndex() + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n      this.selectedIndex = Math.max(this._selectedIndex() - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n      this._updateSelectedItemIndex(0);\n      this.steps.forEach(step => step.reset());\n      this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n      return `${this._groupId}-label-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n      return `${this._groupId}-content-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n      const position = index - this._selectedIndex();\n      if (position < 0) {\n        return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n      } else if (position > 0) {\n        return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n      }\n      return 'current';\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n      return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex();\n    }\n    _updateSelectedItemIndex(newIndex) {\n      const stepsArray = this.steps.toArray();\n      const selectedIndex = this._selectedIndex();\n      this.selectionChange.emit({\n        selectedIndex: newIndex,\n        previouslySelectedIndex: selectedIndex,\n        selectedStep: stepsArray[newIndex],\n        previouslySelectedStep: stepsArray[selectedIndex]\n      });\n      // If focus is inside the stepper, move it to the next header, otherwise it may become\n      // lost when the active step content is hidden. We can't be more granular with the check\n      // (e.g. checking whether focus is inside the active step), because we don't have a\n      // reference to the elements that are rendering out the content.\n      if (this._keyManager) {\n        this._containsFocus() ? this._keyManager.setActiveItem(newIndex) : this._keyManager.updateActiveItem(newIndex);\n      }\n      this._selectedIndex.set(newIndex);\n      this.selectedIndexChange.emit(newIndex);\n      this._stateChanged();\n    }\n    _onKeydown(event) {\n      const hasModifier = hasModifierKey(event);\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      if (manager?.activeItemIndex != null && !hasModifier && (keyCode === SPACE || keyCode === ENTER)) {\n        this.selectedIndex = manager.activeItemIndex;\n        event.preventDefault();\n      } else {\n        manager?.setFocusOrigin('keyboard').onKeydown(event);\n      }\n    }\n    _anyControlsInvalidOrPending(index) {\n      if (this.linear && index >= 0) {\n        return this.steps.toArray().slice(0, index).some(step => {\n          const control = step.stepControl;\n          const isIncomplete = control ? control.invalid || control.pending || !step.interacted : !step.completed;\n          return isIncomplete && !step.optional && !step._completedOverride();\n        });\n      }\n      return false;\n    }\n    _layoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n      const stepperElement = this._elementRef.nativeElement;\n      const focusedElement = _getFocusedElementPierceShadowDom();\n      return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n      return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static ɵfac = function CdkStepper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepper)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepper,\n      selectors: [[\"\", \"cdkStepper\", \"\"]],\n      contentQueries: function CdkStepper_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStep, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkStepHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n        }\n      },\n      inputs: {\n        linear: [2, \"linear\", \"linear\", booleanAttribute],\n        selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n        selected: \"selected\",\n        orientation: \"orientation\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        selectedIndexChange: \"selectedIndexChange\"\n      },\n      exportAs: [\"cdkStepper\"]\n    });\n  }\n  return CdkStepper;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nlet CdkStepperNext = /*#__PURE__*/(() => {\n  class CdkStepperNext {\n    _stepper = inject(CdkStepper);\n    /** Type of the next button. Defaults to \"submit\" if not specified. */\n    type = 'submit';\n    constructor() {}\n    static ɵfac = function CdkStepperNext_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperNext)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperNext,\n      selectors: [[\"button\", \"cdkStepperNext\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperNext_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperNext_click_HostBindingHandler() {\n            return ctx._stepper.next();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      }\n    });\n  }\n  return CdkStepperNext;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nlet CdkStepperPrevious = /*#__PURE__*/(() => {\n  class CdkStepperPrevious {\n    _stepper = inject(CdkStepper);\n    /** Type of the previous button. Defaults to \"button\" if not specified. */\n    type = 'button';\n    constructor() {}\n    static ɵfac = function CdkStepperPrevious_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperPrevious)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperPrevious,\n      selectors: [[\"button\", \"cdkStepperPrevious\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperPrevious_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperPrevious_click_HostBindingHandler() {\n            return ctx._stepper.previous();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      }\n    });\n  }\n  return CdkStepperPrevious;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepperModule = /*#__PURE__*/(() => {\n  class CdkStepperModule {\n    static ɵfac = function CdkStepperModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkStepperModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule]\n    });\n  }\n  return CdkStepperModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "Directive", "TemplateRef", "InjectionToken", "signal", "EventEmitter", "computed", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChild", "ContentChildren", "ViewChild", "Input", "Output", "ChangeDetectorRef", "QueryList", "numberAttribute", "NgModule", "ControlContainer", "Subject", "of", "startWith", "takeUntil", "D", "Directionality", "_", "_IdGenerator", "F", "FocusKeyManager", "hasModifierKey", "S", "SPACE", "c", "ENTER", "_getFocusedElementPierceShadowDom", "BidiModule", "_c0", "CdkStep_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "CdkStepHeader", "_elementRef", "constructor", "focus", "nativeElement", "ɵfac", "CdkStepHeader_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "CdkStepLabel", "template", "CdkStepLabel_Factory", "StepperSelectionEvent", "selectedIndex", "previouslySelectedIndex", "selectedStep", "previouslySelectedStep", "STEP_STATE", "NUMBER", "EDIT", "DONE", "ERROR", "STEPPER_GLOBAL_OPTIONS", "CdkStep", "_stepperOptions", "_stepper", "CdkStepper", "_displayDefaultIndicatorType", "<PERSON><PERSON><PERSON><PERSON>", "_childForms", "content", "stepControl", "interacted", "_interacted", "value", "set", "interactedStream", "label", "errorMessage", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "_state", "undefined", "editable", "_editable", "optional", "completed", "override", "_completedOverride", "valid", "index", "isSelected", "indicatorType", "selected", "defaultState", "_showError", "<PERSON><PERSON><PERSON><PERSON>", "isNavigable", "isCompleted", "linear", "customError", "_customError", "_getDefaultError", "invalid", "stepperOptions", "displayDefaultIndicatorType", "select", "reset", "for<PERSON>ach", "form", "resetForm", "ngOnChanges", "_stateChanged", "_markAsInteracted", "emit", "showError", "CdkStep_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "CdkStep_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "CdkStep_Query", "ɵɵviewQuery", "inputs", "outputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "CdkStep_Template", "ɵɵprojectionDef", "ɵɵtemplate", "encapsulation", "changeDetection", "_dir", "_changeDetectorRef", "_destroyed", "_keyManager", "_steps", "steps", "_step<PERSON><PERSON>er", "_sortedHeaders", "_selectedIndex", "_isValidIndex", "Error", "_anyControlsInvalidOrPending", "toArray", "_updateSelectedItemIndex", "step", "indexOf", "selectionChange", "selectedIndexChange", "_groupId", "getId", "orientation", "_orientation", "withVerticalOrientation", "ngAfterContentInit", "changes", "pipe", "subscribe", "filter", "notifyOn<PERSON><PERSON>es", "ngAfterViewInit", "headers", "sort", "a", "b", "documentPosition", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "withWrap", "withHomeAndEnd", "updateActiveItem", "change", "_layoutDirection", "direction", "withHorizontalOrientation", "Math", "max", "visitedSteps", "slice", "ngOnDestroy", "destroy", "next", "complete", "min", "length", "previous", "_getStepLabelId", "i", "_getStepContentId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAnimationDirection", "position", "_getFocusIndex", "activeItemIndex", "newIndex", "stepsArray", "_containsFocus", "setActiveItem", "_onKeydown", "event", "hasModifier", "keyCode", "manager", "preventDefault", "setFocusOrigin", "onKeydown", "some", "control", "isIncomplete", "pending", "stepper<PERSON><PERSON>", "focusedElement", "contains", "CdkStepper_Factory", "CdkStepper_ContentQueries", "CdkStepperNext", "CdkStepperNext_Factory", "hostVars", "hostBindings", "CdkStepperNext_HostBindings", "ɵɵlistener", "CdkStepperNext_click_HostBindingHandler", "ɵɵdomProperty", "CdkStepperPrevious", "CdkStepperPrevious_Factory", "CdkStepperPrevious_HostBindings", "CdkStepperPrevious_click_HostBindingHandler", "CdkStepperModule", "CdkStepperModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/stepper.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, TemplateRef, InjectionToken, signal, EventEmitter, computed, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChild, ContentChildren, ViewChild, Input, Output, ChangeDetectorRef, QueryList, numberAttribute, NgModule } from '@angular/core';\nimport { ControlContainer } from '@angular/forms';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { F as FocusKeyManager } from './focus-key-manager-CPmlyB_c.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { S as SPACE, c as ENTER } from './keycodes-CpHkExLC.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { BidiModule } from './bidi.mjs';\nimport './list-key-manager-C7tp3RbG.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\n\nclass CdkStepHeader {\n    _elementRef = inject(ElementRef);\n    constructor() { }\n    /** Focuses the step header. */\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepHeader, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkStepHeader, isStandalone: true, selector: \"[cdkStepHeader]\", host: { attributes: { \"role\": \"tab\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepHeader, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepHeader]',\n                    host: {\n                        'role': 'tab',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\nclass CdkStepLabel {\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkStepLabel, isStandalone: true, selector: \"[cdkStepLabel]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepLabel]',\n                }]\n        }], ctorParameters: () => [] });\n\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {\n    /** Index of the step now selected. */\n    selectedIndex;\n    /** Index of the step previously selected. */\n    previouslySelectedIndex;\n    /** The step instance now selected. */\n    selectedStep;\n    /** The step instance previously selected. */\n    previouslySelectedStep;\n}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n    NUMBER: 'number',\n    EDIT: 'edit',\n    DONE: 'done',\n    ERROR: 'error',\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nclass CdkStep {\n    _stepperOptions;\n    _stepper = inject(CdkStepper);\n    _displayDefaultIndicatorType;\n    /** Template for step label if it exists. */\n    stepLabel;\n    /** Forms that have been projected into the step. */\n    _childForms;\n    /** Template for step content. */\n    content;\n    /** The top level abstract control of the step. */\n    stepControl;\n    /** Whether user has attempted to move away from the step. */\n    get interacted() {\n        return this._interacted();\n    }\n    set interacted(value) {\n        this._interacted.set(value);\n    }\n    _interacted = signal(false);\n    /** Emits when the user has attempted to move away from the step. */\n    interactedStream = new EventEmitter();\n    /** Plain text label of the step. */\n    label;\n    /** Error message to display when there's an error. */\n    errorMessage;\n    /** Aria label for the tab. */\n    ariaLabel;\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    ariaLabelledby;\n    /** State of the step. */\n    get state() {\n        return this._state();\n    }\n    set state(value) {\n        this._state.set(value);\n    }\n    _state = signal(undefined);\n    /** Whether the user can return to this step once it has been marked as completed. */\n    get editable() {\n        return this._editable();\n    }\n    set editable(value) {\n        this._editable.set(value);\n    }\n    _editable = signal(true);\n    /** Whether the completion of step is optional. */\n    optional = false;\n    /** Whether step is marked as completed. */\n    get completed() {\n        const override = this._completedOverride();\n        const interacted = this._interacted();\n        if (override != null) {\n            return override;\n        }\n        return interacted && (!this.stepControl || this.stepControl.valid);\n    }\n    set completed(value) {\n        this._completedOverride.set(value);\n    }\n    _completedOverride = signal(null);\n    /** Current index of the step within the stepper. */\n    index = signal(-1);\n    /** Whether the step is selected. */\n    isSelected = computed(() => this._stepper.selectedIndex === this.index());\n    /** Type of indicator that should be shown for the step. */\n    indicatorType = computed(() => {\n        const selected = this.isSelected();\n        const completed = this.completed;\n        const defaultState = this._state() ?? STEP_STATE.NUMBER;\n        const editable = this._editable();\n        if (this._showError() && this.hasError && !selected) {\n            return STEP_STATE.ERROR;\n        }\n        if (this._displayDefaultIndicatorType) {\n            if (!completed || selected) {\n                return STEP_STATE.NUMBER;\n            }\n            return editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n        }\n        else {\n            if (completed && !selected) {\n                return STEP_STATE.DONE;\n            }\n            else if (completed && selected) {\n                return defaultState;\n            }\n            return editable && selected ? STEP_STATE.EDIT : defaultState;\n        }\n    });\n    /** Whether the user can navigate to the step. */\n    isNavigable = computed(() => {\n        const isSelected = this.isSelected();\n        const isCompleted = this.completed;\n        return isCompleted || isSelected || !this._stepper.linear;\n    });\n    /** Whether step has an error. */\n    get hasError() {\n        const customError = this._customError();\n        return customError == null ? this._getDefaultError() : customError;\n    }\n    set hasError(value) {\n        this._customError.set(value);\n    }\n    _customError = signal(null);\n    _getDefaultError() {\n        return this.interacted && !!this.stepControl?.invalid;\n    }\n    constructor() {\n        const stepperOptions = inject(STEPPER_GLOBAL_OPTIONS, { optional: true });\n        this._stepperOptions = stepperOptions ? stepperOptions : {};\n        this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n        this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n        this._interacted.set(false);\n        if (this._completedOverride() != null) {\n            this._completedOverride.set(false);\n        }\n        if (this._customError() != null) {\n            this._customError.set(false);\n        }\n        if (this.stepControl) {\n            // Reset the forms since the default error state matchers will show errors on submit and we\n            // want the form to be back to its initial state (see #29781). Submitted state is on the\n            // individual directives, rather than the control, so we need to reset them ourselves.\n            this._childForms?.forEach(form => form.resetForm?.());\n            this.stepControl.reset();\n        }\n    }\n    ngOnChanges() {\n        // Since basically all inputs of the MatStep get proxied through the view down to the\n        // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n        this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n        if (!this._interacted()) {\n            this._interacted.set(true);\n            this.interactedStream.emit(this);\n        }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n        // We want to show the error state either if the user opted into/out of it using the\n        // global options, or if they've explicitly set it through the `hasError` input.\n        return this._stepperOptions.showError ?? this._customError() != null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStep, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkStep, isStandalone: true, selector: \"cdk-step\", inputs: { stepControl: \"stepControl\", label: \"label\", errorMessage: \"errorMessage\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], state: \"state\", editable: [\"editable\", \"editable\", booleanAttribute], optional: [\"optional\", \"optional\", booleanAttribute], completed: [\"completed\", \"completed\", booleanAttribute], hasError: [\"hasError\", \"hasError\", booleanAttribute] }, outputs: { interactedStream: \"interacted\" }, queries: [{ propertyName: \"stepLabel\", first: true, predicate: CdkStepLabel, descendants: true }, { propertyName: \"_childForms\", predicate: \n                // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n                // provides themselves as such, but we don't want to have a concrete reference to both of\n                // the directives. The type is marked as `Partial` in case we run into a class that provides\n                // itself as `ControlContainer` but doesn't have the same interface as the directives.\n                ControlContainer, descendants: true }], viewQueries: [{ propertyName: \"content\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"cdkStep\"], usesOnChanges: true, ngImport: i0, template: '<ng-template><ng-content/></ng-template>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStep, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-step',\n                    exportAs: 'cdkStep',\n                    template: '<ng-template><ng-content/></ng-template>',\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: () => [], propDecorators: { stepLabel: [{\n                type: ContentChild,\n                args: [CdkStepLabel]\n            }], _childForms: [{\n                type: ContentChildren,\n                args: [\n                    // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n                    // provides themselves as such, but we don't want to have a concrete reference to both of\n                    // the directives. The type is marked as `Partial` in case we run into a class that provides\n                    // itself as `ControlContainer` but doesn't have the same interface as the directives.\n                    ControlContainer,\n                    {\n                        descendants: true,\n                    }]\n            }], content: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], stepControl: [{\n                type: Input\n            }], interactedStream: [{\n                type: Output,\n                args: ['interacted']\n            }], label: [{\n                type: Input\n            }], errorMessage: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], state: [{\n                type: Input\n            }], editable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], optional: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], completed: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasError: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass CdkStepper {\n    _dir = inject(Directionality, { optional: true });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Used for managing keyboard focus. */\n    _keyManager;\n    /** Full list of steps inside the stepper, including inside nested steppers. */\n    _steps;\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n    steps = new QueryList();\n    /** The list of step headers of the steps in the stepper. */\n    _stepHeader;\n    /** List of step headers sorted based on their DOM order. */\n    _sortedHeaders = new QueryList();\n    /** Whether the validity of previous steps should be checked or not. */\n    linear = false;\n    /** The index of the selected step. */\n    get selectedIndex() {\n        return this._selectedIndex();\n    }\n    set selectedIndex(index) {\n        if (this._steps) {\n            // Ensure that the index can't be out of bounds.\n            if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n            }\n            if (this.selectedIndex !== index) {\n                this.selected?._markAsInteracted();\n                if (!this._anyControlsInvalidOrPending(index) &&\n                    (index >= this.selectedIndex || this.steps.toArray()[index].editable)) {\n                    this._updateSelectedItemIndex(index);\n                }\n            }\n        }\n        else {\n            this._selectedIndex.set(index);\n        }\n    }\n    _selectedIndex = signal(0);\n    /** The step that is selected. */\n    get selected() {\n        return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n        this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Event emitted when the selected step has changed. */\n    selectionChange = new EventEmitter();\n    /** Output to support two-way binding on `[(selectedIndex)]` */\n    selectedIndexChange = new EventEmitter();\n    /** Used to track unique ID for each stepper component. */\n    _groupId = inject(_IdGenerator).getId('cdk-stepper-');\n    /** Orientation of the stepper. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(value) {\n        // This is a protected method so that `MatStepper` can hook into it.\n        this._orientation = value;\n        if (this._keyManager) {\n            this._keyManager.withVerticalOrientation(value === 'vertical');\n        }\n    }\n    _orientation = 'horizontal';\n    constructor() { }\n    ngAfterContentInit() {\n        this._steps.changes\n            .pipe(startWith(this._steps), takeUntil(this._destroyed))\n            .subscribe((steps) => {\n            this.steps.reset(steps.filter(step => step._stepper === this));\n            this.steps.forEach((step, index) => step.index.set(index));\n            this.steps.notifyOnChanges();\n        });\n    }\n    ngAfterViewInit() {\n        // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n        // Material stepper, they won't appear in the `QueryList` in the same order as they're\n        // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n        // them manually to ensure that they're correct. Alternatively, we can change the Material\n        // template to inline the headers in the `ngFor`, but that'll result in a lot of\n        // code duplication. See #23539.\n        this._stepHeader.changes\n            .pipe(startWith(this._stepHeader), takeUntil(this._destroyed))\n            .subscribe((headers) => {\n            this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n                const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n                // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n                // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n                // tslint:disable-next-line:no-bitwise\n                return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n            }));\n            this._sortedHeaders.notifyOnChanges();\n        });\n        // Note that while the step headers are content children by default, any components that\n        // extend this one might have them as view children. We initialize the keyboard handling in\n        // AfterViewInit so we're guaranteed for both view and content children to be defined.\n        this._keyManager = new FocusKeyManager(this._sortedHeaders)\n            .withWrap()\n            .withHomeAndEnd()\n            .withVerticalOrientation(this._orientation === 'vertical');\n        // The selected index may have changed between when the component was created and when the\n        // key manager was initialized. Use `updateActiveItem` so it's correct, but it doesn't steal\n        // away focus from the user.\n        this._keyManager.updateActiveItem(this.selectedIndex);\n        (this._dir ? this._dir.change : of())\n            .pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed))\n            .subscribe(direction => this._keyManager?.withHorizontalOrientation(direction));\n        this._keyManager.updateActiveItem(this.selectedIndex);\n        // No need to `takeUntil` here, because we're the ones destroying `steps`.\n        this.steps.changes.subscribe(() => {\n            if (!this.selected) {\n                this._selectedIndex.set(Math.max(this.selectedIndex - 1, 0));\n            }\n        });\n        // The logic which asserts that the selected index is within bounds doesn't run before the\n        // steps are initialized, because we don't how many steps there are yet so we may have an\n        // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n        if (!this._isValidIndex(this.selectedIndex)) {\n            this._selectedIndex.set(0);\n        }\n        // For linear step and selected index is greater than zero,\n        // set all the previous steps to interacted so that we can navigate to previous steps.\n        if (this.linear && this.selectedIndex > 0) {\n            const visitedSteps = this.steps.toArray().slice(0, this._selectedIndex());\n            for (const step of visitedSteps) {\n                step._markAsInteracted();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this.steps.destroy();\n        this._sortedHeaders.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n        this.selectedIndex = Math.min(this._selectedIndex() + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n        this.selectedIndex = Math.max(this._selectedIndex() - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n        this._updateSelectedItemIndex(0);\n        this.steps.forEach(step => step.reset());\n        this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n        return `${this._groupId}-label-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n        return `${this._groupId}-content-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n        const position = index - this._selectedIndex();\n        if (position < 0) {\n            return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n        }\n        else if (position > 0) {\n            return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n        }\n        return 'current';\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex();\n    }\n    _updateSelectedItemIndex(newIndex) {\n        const stepsArray = this.steps.toArray();\n        const selectedIndex = this._selectedIndex();\n        this.selectionChange.emit({\n            selectedIndex: newIndex,\n            previouslySelectedIndex: selectedIndex,\n            selectedStep: stepsArray[newIndex],\n            previouslySelectedStep: stepsArray[selectedIndex],\n        });\n        // If focus is inside the stepper, move it to the next header, otherwise it may become\n        // lost when the active step content is hidden. We can't be more granular with the check\n        // (e.g. checking whether focus is inside the active step), because we don't have a\n        // reference to the elements that are rendering out the content.\n        if (this._keyManager) {\n            this._containsFocus()\n                ? this._keyManager.setActiveItem(newIndex)\n                : this._keyManager.updateActiveItem(newIndex);\n        }\n        this._selectedIndex.set(newIndex);\n        this.selectedIndexChange.emit(newIndex);\n        this._stateChanged();\n    }\n    _onKeydown(event) {\n        const hasModifier = hasModifierKey(event);\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        if (manager?.activeItemIndex != null &&\n            !hasModifier &&\n            (keyCode === SPACE || keyCode === ENTER)) {\n            this.selectedIndex = manager.activeItemIndex;\n            event.preventDefault();\n        }\n        else {\n            manager?.setFocusOrigin('keyboard').onKeydown(event);\n        }\n    }\n    _anyControlsInvalidOrPending(index) {\n        if (this.linear && index >= 0) {\n            return this.steps\n                .toArray()\n                .slice(0, index)\n                .some(step => {\n                const control = step.stepControl;\n                const isIncomplete = control\n                    ? control.invalid || control.pending || !step.interacted\n                    : !step.completed;\n                return isIncomplete && !step.optional && !step._completedOverride();\n            });\n        }\n        return false;\n    }\n    _layoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n        const stepperElement = this._elementRef.nativeElement;\n        const focusedElement = _getFocusedElementPierceShadowDom();\n        return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n        return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepper, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkStepper, isStandalone: true, selector: \"[cdkStepper]\", inputs: { linear: [\"linear\", \"linear\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], selected: \"selected\", orientation: \"orientation\" }, outputs: { selectionChange: \"selectionChange\", selectedIndexChange: \"selectedIndexChange\" }, queries: [{ propertyName: \"_steps\", predicate: CdkStep, descendants: true }, { propertyName: \"_stepHeader\", predicate: CdkStepHeader, descendants: true }], exportAs: [\"cdkStepper\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepper]',\n                    exportAs: 'cdkStepper',\n                }]\n        }], ctorParameters: () => [], propDecorators: { _steps: [{\n                type: ContentChildren,\n                args: [CdkStep, { descendants: true }]\n            }], _stepHeader: [{\n                type: ContentChildren,\n                args: [CdkStepHeader, { descendants: true }]\n            }], linear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selected: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], selectedIndexChange: [{\n                type: Output\n            }], orientation: [{\n                type: Input\n            }] } });\n\n/** Button that moves to the next step in a stepper workflow. */\nclass CdkStepperNext {\n    _stepper = inject(CdkStepper);\n    /** Type of the next button. Defaults to \"submit\" if not specified. */\n    type = 'submit';\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperNext, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkStepperNext, isStandalone: true, selector: \"button[cdkStepperNext]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.next()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperNext, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperNext]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.next()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { type: [{\n                type: Input\n            }] } });\n/** Button that moves to the previous step in a stepper workflow. */\nclass CdkStepperPrevious {\n    _stepper = inject(CdkStepper);\n    /** Type of the previous button. Defaults to \"button\" if not specified. */\n    type = 'button';\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperPrevious, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkStepperPrevious, isStandalone: true, selector: \"button[cdkStepperPrevious]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.previous()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperPrevious, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperPrevious]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.previous()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { type: [{\n                type: Input\n            }] } });\n\nclass CdkStepperModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule,\n            CdkStep,\n            CdkStepper,\n            CdkStepHeader,\n            CdkStepLabel,\n            CdkStepperNext,\n            CdkStepperPrevious], exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkStepperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkStep,\n                        CdkStepper,\n                        CdkStepHeader,\n                        CdkStepLabel,\n                        CdkStepperNext,\n                        CdkStepperPrevious,\n                    ],\n                    exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n                }]\n        }] });\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACrT,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACrD,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,eAAe,QAAQ,kCAAkC;AACvE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,KAAK,QAAQ,yBAAyB;AAChE,SAASD,CAAC,IAAIE,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,UAAU,QAAQ,YAAY;AACvC,OAAO,iCAAiC;AACxC,OAAO,0BAA0B;AAAC,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAS2D1C,EAAE,CAAA4C,YAAA,EA8MyK,CAAC;EAAA;AAAA;AAAA,IArNnQC,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBC,WAAW,GAAG7C,MAAM,CAACC,UAAU,CAAC;IAChC6C,WAAWA,CAAA,EAAG,CAAE;IAChB;IACAC,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACF,WAAW,CAACG,aAAa,CAACD,KAAK,CAAC,CAAC;IAC1C;IACA,OAAOE,IAAI,YAAAC,sBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFP,aAAa;IAAA;IAChH,OAAOQ,IAAI,kBAD8ErD,EAAE,CAAAsD,iBAAA;MAAAC,IAAA,EACJV,aAAa;MAAAW,SAAA;MAAAC,SAAA,WAAiF,KAAK;IAAA;EAC9L;EAAC,OATKZ,aAAa;AAAA;AAUnB;EAAA,QAAAa,SAAA,oBAAAA,SAAA;AAAA;AAQwC,IAElCC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfC,QAAQ,GAAG3D,MAAM,CAACG,WAAW,CAAC;IAC9B2C,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOG,IAAI,YAAAW,qBAAAT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,YAAY;IAAA;IAC/G,OAAON,IAAI,kBAjB8ErD,EAAE,CAAAsD,iBAAA;MAAAC,IAAA,EAiBJI,YAAY;MAAAH,SAAA;IAAA;EACvG;EAAC,OALKG,YAAY;AAAA;AAMlB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AAOA;AACA,MAAMI,qBAAqB,CAAC;EACxB;EACAC,aAAa;EACb;EACAC,uBAAuB;EACvB;EACAC,YAAY;EACZ;EACAC,sBAAsB;AAC1B;AACA;AACA,MAAMC,UAAU,GAAG;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,sBAAsB,gBAAG,IAAInE,cAAc,CAAC,wBAAwB,CAAC;AAAC,IACtEoE,OAAO;EAAb,MAAMA,OAAO,CAAC;IACVC,eAAe;IACfC,QAAQ,GAAG1E,MAAM,CAAC2E,UAAU,CAAC;IAC7BC,4BAA4B;IAC5B;IACAC,SAAS;IACT;IACAC,WAAW;IACX;IACAC,OAAO;IACP;IACAC,WAAW;IACX;IACA,IAAIC,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAID,UAAUA,CAACE,KAAK,EAAE;MAClB,IAAI,CAACD,WAAW,CAACE,GAAG,CAACD,KAAK,CAAC;IAC/B;IACAD,WAAW,GAAG7E,MAAM,CAAC,KAAK,CAAC;IAC3B;IACAgF,gBAAgB,GAAG,IAAI/E,YAAY,CAAC,CAAC;IACrC;IACAgF,KAAK;IACL;IACAC,YAAY;IACZ;IACAC,SAAS;IACT;AACJ;AACA;AACA;IACIC,cAAc;IACd;IACA,IAAIC,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC;IACxB;IACA,IAAID,KAAKA,CAACP,KAAK,EAAE;MACb,IAAI,CAACQ,MAAM,CAACP,GAAG,CAACD,KAAK,CAAC;IAC1B;IACAQ,MAAM,GAAGtF,MAAM,CAACuF,SAAS,CAAC;IAC1B;IACA,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;IAC3B;IACA,IAAID,QAAQA,CAACV,KAAK,EAAE;MAChB,IAAI,CAACW,SAAS,CAACV,GAAG,CAACD,KAAK,CAAC;IAC7B;IACAW,SAAS,GAAGzF,MAAM,CAAC,IAAI,CAAC;IACxB;IACA0F,QAAQ,GAAG,KAAK;IAChB;IACA,IAAIC,SAASA,CAAA,EAAG;MACZ,MAAMC,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC1C,MAAMjB,UAAU,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACrC,IAAIe,QAAQ,IAAI,IAAI,EAAE;QAClB,OAAOA,QAAQ;MACnB;MACA,OAAOhB,UAAU,KAAK,CAAC,IAAI,CAACD,WAAW,IAAI,IAAI,CAACA,WAAW,CAACmB,KAAK,CAAC;IACtE;IACA,IAAIH,SAASA,CAACb,KAAK,EAAE;MACjB,IAAI,CAACe,kBAAkB,CAACd,GAAG,CAACD,KAAK,CAAC;IACtC;IACAe,kBAAkB,GAAG7F,MAAM,CAAC,IAAI,CAAC;IACjC;IACA+F,KAAK,GAAG/F,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB;IACAgG,UAAU,GAAG9F,QAAQ,CAAC,MAAM,IAAI,CAACmE,QAAQ,CAACZ,aAAa,KAAK,IAAI,CAACsC,KAAK,CAAC,CAAC,CAAC;IACzE;IACAE,aAAa,GAAG/F,QAAQ,CAAC,MAAM;MAC3B,MAAMgG,QAAQ,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MAClC,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,MAAMQ,YAAY,GAAG,IAAI,CAACb,MAAM,CAAC,CAAC,IAAIzB,UAAU,CAACC,MAAM;MACvD,MAAM0B,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MACjC,IAAI,IAAI,CAACW,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,IAAI,CAACH,QAAQ,EAAE;QACjD,OAAOrC,UAAU,CAACI,KAAK;MAC3B;MACA,IAAI,IAAI,CAACM,4BAA4B,EAAE;QACnC,IAAI,CAACoB,SAAS,IAAIO,QAAQ,EAAE;UACxB,OAAOrC,UAAU,CAACC,MAAM;QAC5B;QACA,OAAO0B,QAAQ,GAAG3B,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,IAAI;MACvD,CAAC,MACI;QACD,IAAI2B,SAAS,IAAI,CAACO,QAAQ,EAAE;UACxB,OAAOrC,UAAU,CAACG,IAAI;QAC1B,CAAC,MACI,IAAI2B,SAAS,IAAIO,QAAQ,EAAE;UAC5B,OAAOC,YAAY;QACvB;QACA,OAAOX,QAAQ,IAAIU,QAAQ,GAAGrC,UAAU,CAACE,IAAI,GAAGoC,YAAY;MAChE;IACJ,CAAC,CAAC;IACF;IACAG,WAAW,GAAGpG,QAAQ,CAAC,MAAM;MACzB,MAAM8F,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;MACpC,MAAMO,WAAW,GAAG,IAAI,CAACZ,SAAS;MAClC,OAAOY,WAAW,IAAIP,UAAU,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAACmC,MAAM;IAC7D,CAAC,CAAC;IACF;IACA,IAAIH,QAAQA,CAAA,EAAG;MACX,MAAMI,WAAW,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACvC,OAAOD,WAAW,IAAI,IAAI,GAAG,IAAI,CAACE,gBAAgB,CAAC,CAAC,GAAGF,WAAW;IACtE;IACA,IAAIJ,QAAQA,CAACvB,KAAK,EAAE;MAChB,IAAI,CAAC4B,YAAY,CAAC3B,GAAG,CAACD,KAAK,CAAC;IAChC;IACA4B,YAAY,GAAG1G,MAAM,CAAC,IAAI,CAAC;IAC3B2G,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAAC/B,UAAU,IAAI,CAAC,CAAC,IAAI,CAACD,WAAW,EAAEiC,OAAO;IACzD;IACAnE,WAAWA,CAAA,EAAG;MACV,MAAMoE,cAAc,GAAGlH,MAAM,CAACuE,sBAAsB,EAAE;QAAEwB,QAAQ,EAAE;MAAK,CAAC,CAAC;MACzE,IAAI,CAACtB,eAAe,GAAGyC,cAAc,GAAGA,cAAc,GAAG,CAAC,CAAC;MAC3D,IAAI,CAACtC,4BAA4B,GAAG,IAAI,CAACH,eAAe,CAAC0C,2BAA2B,KAAK,KAAK;IAClG;IACA;IACAC,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC1C,QAAQ,CAAC6B,QAAQ,GAAG,IAAI;IACjC;IACA;IACAc,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACnC,WAAW,CAACE,GAAG,CAAC,KAAK,CAAC;MAC3B,IAAI,IAAI,CAACc,kBAAkB,CAAC,CAAC,IAAI,IAAI,EAAE;QACnC,IAAI,CAACA,kBAAkB,CAACd,GAAG,CAAC,KAAK,CAAC;MACtC;MACA,IAAI,IAAI,CAAC2B,YAAY,CAAC,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACA,YAAY,CAAC3B,GAAG,CAAC,KAAK,CAAC;MAChC;MACA,IAAI,IAAI,CAACJ,WAAW,EAAE;QAClB;QACA;QACA;QACA,IAAI,CAACF,WAAW,EAAEwC,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;QACrD,IAAI,CAACxC,WAAW,CAACqC,KAAK,CAAC,CAAC;MAC5B;IACJ;IACAI,WAAWA,CAAA,EAAG;MACV;MACA;MACA,IAAI,CAAC/C,QAAQ,CAACgD,aAAa,CAAC,CAAC;IACjC;IACAC,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAACzC,WAAW,CAAC,CAAC,EAAE;QACrB,IAAI,CAACA,WAAW,CAACE,GAAG,CAAC,IAAI,CAAC;QAC1B,IAAI,CAACC,gBAAgB,CAACuC,IAAI,CAAC,IAAI,CAAC;MACpC;IACJ;IACA;IACAnB,UAAUA,CAAA,EAAG;MACT;MACA;MACA,OAAO,IAAI,CAAChC,eAAe,CAACoD,SAAS,IAAI,IAAI,CAACd,YAAY,CAAC,CAAC,IAAI,IAAI;IACxE;IACA,OAAO9D,IAAI,YAAA6E,gBAAA3E,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqB,OAAO;IAAA;IAC1G,OAAOuD,IAAI,kBAzM8EhI,EAAE,CAAAiI,iBAAA;MAAA1E,IAAA,EAyMJkB,OAAO;MAAAjB,SAAA;MAAA0E,cAAA,WAAAC,uBAAAzF,EAAA,EAAAC,GAAA,EAAAyF,QAAA;QAAA,IAAA1F,EAAA;UAzML1C,EAAE,CAAAqI,cAAA,CAAAD,QAAA,EAyM2jBzE,YAAY;UAzMzkB3D,EAAE,CAAAqI,cAAA,CAAAD,QAAA;UA0M/E;UACA;UACA;UACA;UACA9G,gBAAgB;QAAA;QAAA,IAAAoB,EAAA;UAAA,IAAA4F,EAAA;UA9M6DtI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAA7F,GAAA,CAAAmC,SAAA,GAAAwD,EAAA,CAAAG,KAAA;UAAFzI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAA7F,GAAA,CAAAoC,WAAA,GAAAuD,EAAA;QAAA;MAAA;MAAAI,SAAA,WAAAC,cAAAjG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAA4I,WAAA,CA8M0BxI,WAAW;QAAA;QAAA,IAAAsC,EAAA;UAAA,IAAA4F,EAAA;UA9MvCtI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAA7F,GAAA,CAAAqC,OAAA,GAAAsD,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,MAAA;QAAA5D,WAAA;QAAAM,KAAA;QAAAC,YAAA;QAAAC,SAAA;QAAAC,cAAA;QAAAC,KAAA;QAAAG,QAAA,8BAyMqRrF,gBAAgB;QAAAuF,QAAA,8BAAsCvF,gBAAgB;QAAAwF,SAAA,gCAAyCxF,gBAAgB;QAAAkG,QAAA,8BAAsClG,gBAAgB;MAAA;MAAAqI,OAAA;QAAAxD,gBAAA;MAAA;MAAAyD,QAAA;MAAAC,QAAA,GAzM5chJ,EAAE,CAAAiJ,oBAAA;MAAAC,kBAAA,EAAA1G,GAAA;MAAA2G,KAAA;MAAAC,IAAA;MAAAxF,QAAA,WAAAyF,iBAAA3G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1C,EAAE,CAAAsJ,eAAA;UAAFtJ,EAAE,CAAAuJ,UAAA,IAAA9G,8BAAA,qBA8M4J,CAAC;QAAA;MAAA;MAAA+G,aAAA;MAAAC,eAAA;IAAA;EAC5P;EAAC,OAjKKhF,OAAO;AAAA;AAkKb;EAAA,QAAAf,SAAA,oBAAAA,SAAA;AAAA;AAuDoB,IACdkB,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACb8E,IAAI,GAAGzJ,MAAM,CAAC2B,cAAc,EAAE;MAAEoE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjD2D,kBAAkB,GAAG1J,MAAM,CAACiB,iBAAiB,CAAC;IAC9C4B,WAAW,GAAG7C,MAAM,CAACC,UAAU,CAAC;IAChC;IACA0J,UAAU,GAAG,IAAIrI,OAAO,CAAC,CAAC;IAC1B;IACAsI,WAAW;IACX;IACAC,MAAM;IACN;IACAC,KAAK,GAAG,IAAI5I,SAAS,CAAC,CAAC;IACvB;IACA6I,WAAW;IACX;IACAC,cAAc,GAAG,IAAI9I,SAAS,CAAC,CAAC;IAChC;IACA2F,MAAM,GAAG,KAAK;IACd;IACA,IAAI/C,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACmG,cAAc,CAAC,CAAC;IAChC;IACA,IAAInG,aAAaA,CAACsC,KAAK,EAAE;MACrB,IAAI,IAAI,CAACyD,MAAM,EAAE;QACb;QACA,IAAI,CAAC,IAAI,CAACK,aAAa,CAAC9D,KAAK,CAAC,KAAK,OAAO3C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;UAC/E,MAAM0G,KAAK,CAAC,mEAAmE,CAAC;QACpF;QACA,IAAI,IAAI,CAACrG,aAAa,KAAKsC,KAAK,EAAE;UAC9B,IAAI,CAACG,QAAQ,EAAEoB,iBAAiB,CAAC,CAAC;UAClC,IAAI,CAAC,IAAI,CAACyC,4BAA4B,CAAChE,KAAK,CAAC,KACxCA,KAAK,IAAI,IAAI,CAACtC,aAAa,IAAI,IAAI,CAACgG,KAAK,CAACO,OAAO,CAAC,CAAC,CAACjE,KAAK,CAAC,CAACP,QAAQ,CAAC,EAAE;YACvE,IAAI,CAACyE,wBAAwB,CAAClE,KAAK,CAAC;UACxC;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAAC6D,cAAc,CAAC7E,GAAG,CAACgB,KAAK,CAAC;MAClC;IACJ;IACA6D,cAAc,GAAG5J,MAAM,CAAC,CAAC,CAAC;IAC1B;IACA,IAAIkG,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACuD,KAAK,GAAG,IAAI,CAACA,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,IAAI,CAACvG,aAAa,CAAC,GAAG8B,SAAS;IAC5E;IACA,IAAIW,QAAQA,CAACgE,IAAI,EAAE;MACf,IAAI,CAACzG,aAAa,GAAGyG,IAAI,IAAI,IAAI,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK,CAACO,OAAO,CAAC,CAAC,CAACG,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;IACrF;IACA;IACAE,eAAe,GAAG,IAAInK,YAAY,CAAC,CAAC;IACpC;IACAoK,mBAAmB,GAAG,IAAIpK,YAAY,CAAC,CAAC;IACxC;IACAqK,QAAQ,GAAG3K,MAAM,CAAC6B,YAAY,CAAC,CAAC+I,KAAK,CAAC,cAAc,CAAC;IACrD;IACA,IAAIC,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,YAAY;IAC5B;IACA,IAAID,WAAWA,CAAC1F,KAAK,EAAE;MACnB;MACA,IAAI,CAAC2F,YAAY,GAAG3F,KAAK;MACzB,IAAI,IAAI,CAACyE,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACmB,uBAAuB,CAAC5F,KAAK,KAAK,UAAU,CAAC;MAClE;IACJ;IACA2F,YAAY,GAAG,YAAY;IAC3BhI,WAAWA,CAAA,EAAG,CAAE;IAChBkI,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACnB,MAAM,CAACoB,OAAO,CACdC,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAACqI,MAAM,CAAC,EAAEpI,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CACxDwB,SAAS,CAAErB,KAAK,IAAK;QACtB,IAAI,CAACA,KAAK,CAACzC,KAAK,CAACyC,KAAK,CAACsB,MAAM,CAACb,IAAI,IAAIA,IAAI,CAAC7F,QAAQ,KAAK,IAAI,CAAC,CAAC;QAC9D,IAAI,CAACoF,KAAK,CAACxC,OAAO,CAAC,CAACiD,IAAI,EAAEnE,KAAK,KAAKmE,IAAI,CAACnE,KAAK,CAAChB,GAAG,CAACgB,KAAK,CAAC,CAAC;QAC1D,IAAI,CAAC0D,KAAK,CAACuB,eAAe,CAAC,CAAC;MAChC,CAAC,CAAC;IACN;IACAC,eAAeA,CAAA,EAAG;MACd;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACvB,WAAW,CAACkB,OAAO,CACnBC,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAACuI,WAAW,CAAC,EAAEtI,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CAC7DwB,SAAS,CAAEI,OAAO,IAAK;QACxB,IAAI,CAACvB,cAAc,CAAC3C,KAAK,CAACkE,OAAO,CAAClB,OAAO,CAAC,CAAC,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACvD,MAAMC,gBAAgB,GAAGF,CAAC,CAAC5I,WAAW,CAACG,aAAa,CAAC4I,uBAAuB,CAACF,CAAC,CAAC7I,WAAW,CAACG,aAAa,CAAC;UACzG;UACA;UACA;UACA,OAAO2I,gBAAgB,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC9B,cAAc,CAACqB,eAAe,CAAC,CAAC;MACzC,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAI,CAACzB,WAAW,GAAG,IAAI7H,eAAe,CAAC,IAAI,CAACiI,cAAc,CAAC,CACtD+B,QAAQ,CAAC,CAAC,CACVC,cAAc,CAAC,CAAC,CAChBjB,uBAAuB,CAAC,IAAI,CAACD,YAAY,KAAK,UAAU,CAAC;MAC9D;MACA;MACA;MACA,IAAI,CAAClB,WAAW,CAACqC,gBAAgB,CAAC,IAAI,CAACnI,aAAa,CAAC;MACrD,CAAC,IAAI,CAAC2F,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyC,MAAM,GAAG3K,EAAE,CAAC,CAAC,EAC/B2J,IAAI,CAAC1J,SAAS,CAAC,IAAI,CAAC2K,gBAAgB,CAAC,CAAC,CAAC,EAAE1K,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CACpEwB,SAAS,CAACiB,SAAS,IAAI,IAAI,CAACxC,WAAW,EAAEyC,yBAAyB,CAACD,SAAS,CAAC,CAAC;MACnF,IAAI,CAACxC,WAAW,CAACqC,gBAAgB,CAAC,IAAI,CAACnI,aAAa,CAAC;MACrD;MACA,IAAI,CAACgG,KAAK,CAACmB,OAAO,CAACE,SAAS,CAAC,MAAM;QAC/B,IAAI,CAAC,IAAI,CAAC5E,QAAQ,EAAE;UAChB,IAAI,CAAC0D,cAAc,CAAC7E,GAAG,CAACkH,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzI,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE;MACJ,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACoG,aAAa,CAAC,IAAI,CAACpG,aAAa,CAAC,EAAE;QACzC,IAAI,CAACmG,cAAc,CAAC7E,GAAG,CAAC,CAAC,CAAC;MAC9B;MACA;MACA;MACA,IAAI,IAAI,CAACyB,MAAM,IAAI,IAAI,CAAC/C,aAAa,GAAG,CAAC,EAAE;QACvC,MAAM0I,YAAY,GAAG,IAAI,CAAC1C,KAAK,CAACO,OAAO,CAAC,CAAC,CAACoC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACxC,cAAc,CAAC,CAAC,CAAC;QACzE,KAAK,MAAMM,IAAI,IAAIiC,YAAY,EAAE;UAC7BjC,IAAI,CAAC5C,iBAAiB,CAAC,CAAC;QAC5B;MACJ;IACJ;IACA+E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC9C,WAAW,EAAE+C,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC7C,KAAK,CAAC6C,OAAO,CAAC,CAAC;MACpB,IAAI,CAAC3C,cAAc,CAAC2C,OAAO,CAAC,CAAC;MAC7B,IAAI,CAAChD,UAAU,CAACiD,IAAI,CAAC,CAAC;MACtB,IAAI,CAACjD,UAAU,CAACkD,QAAQ,CAAC,CAAC;IAC9B;IACA;IACAD,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC9I,aAAa,GAAGwI,IAAI,CAACQ,GAAG,CAAC,IAAI,CAAC7C,cAAc,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACH,KAAK,CAACiD,MAAM,GAAG,CAAC,CAAC;IACnF;IACA;IACAC,QAAQA,CAAA,EAAG;MACP,IAAI,CAAClJ,aAAa,GAAGwI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACtC,cAAc,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/D;IACA;IACA5C,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACiD,wBAAwB,CAAC,CAAC,CAAC;MAChC,IAAI,CAACR,KAAK,CAACxC,OAAO,CAACiD,IAAI,IAAIA,IAAI,CAAClD,KAAK,CAAC,CAAC,CAAC;MACxC,IAAI,CAACK,aAAa,CAAC,CAAC;IACxB;IACA;IACAuF,eAAeA,CAACC,CAAC,EAAE;MACf,OAAO,GAAG,IAAI,CAACvC,QAAQ,UAAUuC,CAAC,EAAE;IACxC;IACA;IACAC,iBAAiBA,CAACD,CAAC,EAAE;MACjB,OAAO,GAAG,IAAI,CAACvC,QAAQ,YAAYuC,CAAC,EAAE;IAC1C;IACA;IACAxF,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACgC,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;IAC1C;IACA;IACAC,sBAAsBA,CAACjH,KAAK,EAAE;MAC1B,MAAMkH,QAAQ,GAAGlH,KAAK,GAAG,IAAI,CAAC6D,cAAc,CAAC,CAAC;MAC9C,IAAIqD,QAAQ,GAAG,CAAC,EAAE;QACd,OAAO,IAAI,CAACnB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,UAAU;MAClE,CAAC,MACI,IAAImB,QAAQ,GAAG,CAAC,EAAE;QACnB,OAAO,IAAI,CAACnB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,GAAG,MAAM;MAClE;MACA,OAAO,SAAS;IACpB;IACA;IACAoB,cAAcA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC3D,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC4D,eAAe,GAAG,IAAI,CAACvD,cAAc,CAAC,CAAC;IACtF;IACAK,wBAAwBA,CAACmD,QAAQ,EAAE;MAC/B,MAAMC,UAAU,GAAG,IAAI,CAAC5D,KAAK,CAACO,OAAO,CAAC,CAAC;MACvC,MAAMvG,aAAa,GAAG,IAAI,CAACmG,cAAc,CAAC,CAAC;MAC3C,IAAI,CAACQ,eAAe,CAAC7C,IAAI,CAAC;QACtB9D,aAAa,EAAE2J,QAAQ;QACvB1J,uBAAuB,EAAED,aAAa;QACtCE,YAAY,EAAE0J,UAAU,CAACD,QAAQ,CAAC;QAClCxJ,sBAAsB,EAAEyJ,UAAU,CAAC5J,aAAa;MACpD,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC8F,WAAW,EAAE;QAClB,IAAI,CAAC+D,cAAc,CAAC,CAAC,GACf,IAAI,CAAC/D,WAAW,CAACgE,aAAa,CAACH,QAAQ,CAAC,GACxC,IAAI,CAAC7D,WAAW,CAACqC,gBAAgB,CAACwB,QAAQ,CAAC;MACrD;MACA,IAAI,CAACxD,cAAc,CAAC7E,GAAG,CAACqI,QAAQ,CAAC;MACjC,IAAI,CAAC/C,mBAAmB,CAAC9C,IAAI,CAAC6F,QAAQ,CAAC;MACvC,IAAI,CAAC/F,aAAa,CAAC,CAAC;IACxB;IACAmG,UAAUA,CAACC,KAAK,EAAE;MACd,MAAMC,WAAW,GAAG/L,cAAc,CAAC8L,KAAK,CAAC;MACzC,MAAME,OAAO,GAAGF,KAAK,CAACE,OAAO;MAC7B,MAAMC,OAAO,GAAG,IAAI,CAACrE,WAAW;MAChC,IAAIqE,OAAO,EAAET,eAAe,IAAI,IAAI,IAChC,CAACO,WAAW,KACXC,OAAO,KAAK9L,KAAK,IAAI8L,OAAO,KAAK5L,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAC0B,aAAa,GAAGmK,OAAO,CAACT,eAAe;QAC5CM,KAAK,CAACI,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI;QACDD,OAAO,EAAEE,cAAc,CAAC,UAAU,CAAC,CAACC,SAAS,CAACN,KAAK,CAAC;MACxD;IACJ;IACA1D,4BAA4BA,CAAChE,KAAK,EAAE;MAChC,IAAI,IAAI,CAACS,MAAM,IAAIT,KAAK,IAAI,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC0D,KAAK,CACZO,OAAO,CAAC,CAAC,CACToC,KAAK,CAAC,CAAC,EAAErG,KAAK,CAAC,CACfiI,IAAI,CAAC9D,IAAI,IAAI;UACd,MAAM+D,OAAO,GAAG/D,IAAI,CAACvF,WAAW;UAChC,MAAMuJ,YAAY,GAAGD,OAAO,GACtBA,OAAO,CAACrH,OAAO,IAAIqH,OAAO,CAACE,OAAO,IAAI,CAACjE,IAAI,CAACtF,UAAU,GACtD,CAACsF,IAAI,CAACvE,SAAS;UACrB,OAAOuI,YAAY,IAAI,CAAChE,IAAI,CAACxE,QAAQ,IAAI,CAACwE,IAAI,CAACrE,kBAAkB,CAAC,CAAC;QACvE,CAAC,CAAC;MACN;MACA,OAAO,KAAK;IAChB;IACAiG,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAAC1C,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtE,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;IACjE;IACA;IACAwI,cAAcA,CAAA,EAAG;MACb,MAAMc,cAAc,GAAG,IAAI,CAAC5L,WAAW,CAACG,aAAa;MACrD,MAAM0L,cAAc,GAAGrM,iCAAiC,CAAC,CAAC;MAC1D,OAAOoM,cAAc,KAAKC,cAAc,IAAID,cAAc,CAACE,QAAQ,CAACD,cAAc,CAAC;IACvF;IACA;IACAxE,aAAaA,CAAC9D,KAAK,EAAE;MACjB,OAAOA,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC0D,KAAK,IAAI1D,KAAK,GAAG,IAAI,CAAC0D,KAAK,CAACiD,MAAM,CAAC;IACnE;IACA,OAAO9J,IAAI,YAAA2L,mBAAAzL,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwB,UAAU;IAAA;IAC7G,OAAOvB,IAAI,kBA5f8ErD,EAAE,CAAAsD,iBAAA;MAAAC,IAAA,EA4fJqB,UAAU;MAAApB,SAAA;MAAA0E,cAAA,WAAA4G,0BAAApM,EAAA,EAAAC,GAAA,EAAAyF,QAAA;QAAA,IAAA1F,EAAA;UA5fR1C,EAAE,CAAAqI,cAAA,CAAAD,QAAA,EA4foX3D,OAAO;UA5f7XzE,EAAE,CAAAqI,cAAA,CAAAD,QAAA,EA4f4bvF,aAAa;QAAA;QAAA,IAAAH,EAAA;UAAA,IAAA4F,EAAA;UA5f3ctI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAA7F,GAAA,CAAAmH,MAAA,GAAAxB,EAAA;UAAFtI,EAAE,CAAAuI,cAAA,CAAAD,EAAA,GAAFtI,EAAE,CAAAwI,WAAA,QAAA7F,GAAA,CAAAqH,WAAA,GAAA1B,EAAA;QAAA;MAAA;MAAAO,MAAA;QAAA/B,MAAA,0BA4f6FrG,gBAAgB;QAAAsD,aAAA,wCAAqD3C,eAAe;QAAAoF,QAAA;QAAAsE,WAAA;MAAA;MAAAhC,OAAA;QAAA4B,eAAA;QAAAC,mBAAA;MAAA;MAAA5B,QAAA;IAAA;EAChR;EAAC,OArPKnE,UAAU;AAAA;AAsPhB;EAAA,QAAAlB,SAAA,oBAAAA,SAAA;AAAA;;AA4BA;AAAA,IACMqL,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBpK,QAAQ,GAAG1E,MAAM,CAAC2E,UAAU,CAAC;IAC7B;IACArB,IAAI,GAAG,QAAQ;IACfR,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOG,IAAI,YAAA8L,uBAAA5L,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2L,cAAc;IAAA;IACjH,OAAO1L,IAAI,kBAjiB8ErD,EAAE,CAAAsD,iBAAA;MAAAC,IAAA,EAiiBJwL,cAAc;MAAAvL,SAAA;MAAAyL,QAAA;MAAAC,YAAA,WAAAC,4BAAAzM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjiBZ1C,EAAE,CAAAoP,UAAA,mBAAAC,wCAAA;YAAA,OAiiBJ1M,GAAA,CAAAgC,QAAA,CAAAkI,IAAA,CAAc,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAAnK,EAAA;UAjiBZ1C,EAAE,CAAAsP,aAAA,SAAA3M,GAAA,CAAAY,IAiiBS,CAAC;QAAA;MAAA;MAAAsF,MAAA;QAAAtF,IAAA;MAAA;IAAA;EACzG;EAAC,OAPKwL,cAAc;AAAA;AAQpB;EAAA,QAAArL,SAAA,oBAAAA,SAAA;AAAA;AAYA;AAAA,IACM6L,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB5K,QAAQ,GAAG1E,MAAM,CAAC2E,UAAU,CAAC;IAC7B;IACArB,IAAI,GAAG,QAAQ;IACfR,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOG,IAAI,YAAAsM,2BAAApM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmM,kBAAkB;IAAA;IACrH,OAAOlM,IAAI,kBAtjB8ErD,EAAE,CAAAsD,iBAAA;MAAAC,IAAA,EAsjBJgM,kBAAkB;MAAA/L,SAAA;MAAAyL,QAAA;MAAAC,YAAA,WAAAO,gCAAA/M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtjBhB1C,EAAE,CAAAoP,UAAA,mBAAAM,4CAAA;YAAA,OAsjBJ/M,GAAA,CAAAgC,QAAA,CAAAsI,QAAA,CAAkB,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAAvK,EAAA;UAtjBhB1C,EAAE,CAAAsP,aAAA,SAAA3M,GAAA,CAAAY,IAsjBa,CAAC;QAAA;MAAA;MAAAsF,MAAA;QAAAtF,IAAA;MAAA;IAAA;EAC7G;EAAC,OAPKgM,kBAAkB;AAAA;AAQxB;EAAA,QAAA7L,SAAA,oBAAAA,SAAA;AAAA;AAWoB,IAEdiM,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB,OAAOzM,IAAI,YAAA0M,yBAAAxM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuM,gBAAgB;IAAA;IACnH,OAAOE,IAAI,kBAvkB8E7P,EAAE,CAAA8P,gBAAA;MAAAvM,IAAA,EAukBSoM;IAAgB;IAOpH,OAAOI,IAAI,kBA9kB8E/P,EAAE,CAAAgQ,gBAAA;MAAAC,OAAA,GA8kBqC1N,UAAU;IAAA;EAC9I;EAAC,OAVKoN,gBAAgB;AAAA;AAWtB;EAAA,QAAAjM,SAAA,oBAAAA,SAAA;AAAA;AAgBA,SAASe,OAAO,EAAE5B,aAAa,EAAEc,YAAY,EAAEiB,UAAU,EAAE+K,gBAAgB,EAAEZ,cAAc,EAAEQ,kBAAkB,EAAE/K,sBAAsB,EAAEL,UAAU,EAAEL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}