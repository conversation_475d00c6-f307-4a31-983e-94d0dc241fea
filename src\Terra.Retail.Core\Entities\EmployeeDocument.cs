using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// مستندات الموظفين
    /// </summary>
    public class EmployeeDocument : BaseEntity
    {
        /// <summary>
        /// معرف الموظف
        /// </summary>
        public int EmployeeId { get; set; }

        /// <summary>
        /// نوع المستند
        /// </summary>
        public DocumentType DocumentType { get; set; }

        /// <summary>
        /// اسم المستند
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string DocumentName { get; set; } = string.Empty;

        /// <summary>
        /// مسار الملف
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// اسم الملف الأصلي
        /// </summary>
        [MaxLength(255)]
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// حجم الملف (بالبايت)
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// نوع الملف (MIME Type)
        /// </summary>
        [MaxLength(50)]
        public string? ContentType { get; set; }

        /// <summary>
        /// تاريخ انتهاء صلاحية المستند
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// تاريخ إصدار المستند
        /// </summary>
        public DateTime? IssueDate { get; set; }

        /// <summary>
        /// جهة الإصدار
        /// </summary>
        [MaxLength(200)]
        public string? IssuingAuthority { get; set; }

        /// <summary>
        /// رقم المستند
        /// </summary>
        [MaxLength(100)]
        public string? DocumentNumber { get; set; }

        /// <summary>
        /// هل المستند مطلوب
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// هل المستند مؤكد
        /// </summary>
        public bool IsVerified { get; set; } = false;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? VerifiedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد المستند
        /// </summary>
        public int? VerifiedById { get; set; }

        /// <summary>
        /// ملاحظات المستند
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل المستند نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual Employee Employee { get; set; } = null!;
        public virtual User? VerifiedBy { get; set; }
    }

    /// <summary>
    /// أنواع المستندات
    /// </summary>
    public enum DocumentType
    {
        /// <summary>
        /// صورة الهوية
        /// </summary>
        IdentityCard = 1,

        /// <summary>
        /// السيرة الذاتية
        /// </summary>
        Resume = 2,

        /// <summary>
        /// الشهادات العلمية
        /// </summary>
        Certificates = 3,

        /// <summary>
        /// عقد العمل
        /// </summary>
        EmploymentContract = 4,

        /// <summary>
        /// شهادة الخبرة
        /// </summary>
        ExperienceCertificate = 5,

        /// <summary>
        /// التقرير الطبي
        /// </summary>
        MedicalReport = 6,

        /// <summary>
        /// صورة شخصية
        /// </summary>
        PersonalPhoto = 7,

        /// <summary>
        /// رخصة القيادة
        /// </summary>
        DrivingLicense = 8,

        /// <summary>
        /// جواز السفر
        /// </summary>
        Passport = 9,

        /// <summary>
        /// شهادة التأمين
        /// </summary>
        InsuranceCertificate = 10,

        /// <summary>
        /// مستندات أخرى
        /// </summary>
        Other = 11
    }
}
