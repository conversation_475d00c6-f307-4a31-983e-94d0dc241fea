<!-- Terra Retail ERP - Supplier Types -->
<div class="supplier-types-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <button mat-icon-button class="back-btn" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">أنواع الموردين</h1>
          <p class="page-subtitle">إدارة أنواع الموردين في النظام</p>
        </div>
      </div>
      <div class="header-actions">
        <button mat-raised-button color="primary" class="add-btn" (click)="showAddSupplierType()">
          <mat-icon>add</mat-icon>
          <span>إضافة نوع مورد</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content">
    
    <!-- Add/Edit Form -->
    <mat-card class="form-card" *ngIf="showAddForm">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>{{ editingType ? 'edit' : 'add' }}</mat-icon>
          <span>{{ editingType ? 'تعديل نوع المورد' : 'إضافة نوع مورد جديد' }}</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="supplierTypeForm" class="supplier-type-form">
          <div class="form-grid">
            
            <!-- Arabic Name -->
            <mat-form-field appearance="outline">
              <mat-label>الاسم بالعربية *</mat-label>
              <input matInput formControlName="nameAr" placeholder="أدخل اسم نوع المورد بالعربية" required>
              <mat-icon matSuffix>business</mat-icon>
              <mat-error *ngIf="supplierTypeForm.get('nameAr')?.hasError('required')">
                الاسم بالعربية مطلوب
              </mat-error>
              <mat-error *ngIf="supplierTypeForm.get('nameAr')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- English Name -->
            <mat-form-field appearance="outline">
              <mat-label>الاسم بالإنجليزية *</mat-label>
              <input matInput formControlName="nameEn" placeholder="Enter supplier type name in English" required>
              <mat-icon matSuffix>business</mat-icon>
              <mat-error *ngIf="supplierTypeForm.get('nameEn')?.hasError('required')">
                الاسم بالإنجليزية مطلوب
              </mat-error>
              <mat-error *ngIf="supplierTypeForm.get('nameEn')?.hasError('minlength')">
                الاسم يجب أن يكون حرفين على الأقل
              </mat-error>
            </mat-form-field>

            <!-- Description -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>الوصف</mat-label>
              <textarea matInput formControlName="description" rows="3" 
                        placeholder="أدخل وصف نوع المورد (اختياري)"></textarea>
              <mat-icon matSuffix>description</mat-icon>
            </mat-form-field>

            <!-- Is Active -->
            <div class="checkbox-field">
              <mat-checkbox formControlName="isActive">
                نوع مورد نشط
              </mat-checkbox>
            </div>

          </div>
        </form>
      </mat-card-content>
      <mat-card-actions align="end">
        <button mat-button (click)="cancelForm()">
          <mat-icon>cancel</mat-icon>
          <span>إلغاء</span>
        </button>
        <button mat-raised-button color="primary" (click)="saveSupplierType()" [disabled]="isLoading">
          <mat-icon>{{ editingType ? 'save' : 'add' }}</mat-icon>
          <span>{{ editingType ? 'تحديث' : 'إضافة' }}</span>
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Supplier Types Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>list</mat-icon>
          <span>قائمة أنواع الموردين</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        
        <!-- Loading Spinner -->
        <div class="loading-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>جاري تحميل البيانات...</p>
        </div>

        <!-- Table -->
        <div class="table-container" *ngIf="!isLoading">
          <table mat-table [dataSource]="supplierTypes" class="supplier-types-table">

            <!-- Arabic Name Column -->
            <ng-container matColumnDef="nameAr">
              <th mat-header-cell *matHeaderCellDef>الاسم بالعربية</th>
              <td mat-cell *matCellDef="let type">{{ type.NameAr }}</td>
            </ng-container>

            <!-- English Name Column -->
            <ng-container matColumnDef="nameEn">
              <th mat-header-cell *matHeaderCellDef>الاسم بالإنجليزية</th>
              <td mat-cell *matCellDef="let type">{{ type.NameEn }}</td>
            </ng-container>

            <!-- Description Column -->
            <ng-container matColumnDef="description">
              <th mat-header-cell *matHeaderCellDef>الوصف</th>
              <td mat-cell *matCellDef="let type">{{ type.Description || '-' }}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="isActive">
              <th mat-header-cell *matHeaderCellDef>الحالة</th>
              <td mat-cell *matCellDef="let type">
                <span class="status-badge" [ngClass]="type.IsActive ? 'active' : 'inactive'">
                  {{ type.IsActive ? 'نشط' : 'غير نشط' }}
                </span>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
              <td mat-cell *matCellDef="let type">
                <div class="action-buttons">
                  <button mat-icon-button color="primary" 
                          matTooltip="تعديل"
                          (click)="editSupplierType(type)">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" 
                          matTooltip="حذف"
                          (click)="deleteSupplierType(type)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          </table>

          <!-- No Data Message -->
          <div class="no-data" *ngIf="supplierTypes.length === 0">
            <mat-icon>inbox</mat-icon>
            <h3>لا توجد أنواع موردين</h3>
            <p>لم يتم إضافة أي أنواع موردين بعد</p>
            <button mat-raised-button color="primary" (click)="showAddSupplierType()">
              <mat-icon>add</mat-icon>
              <span>إضافة نوع مورد جديد</span>
            </button>
          </div>

        </div>
      </mat-card-content>
    </mat-card>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري المعالجة...</p>
  </div>

</div>
