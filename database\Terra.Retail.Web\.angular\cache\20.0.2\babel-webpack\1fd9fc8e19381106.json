{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, inject, ChangeDetectorRef, ElementRef, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { FocusMonitor, AriaDescriber } from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 3);\n    i0.ɵɵelement(2, \"path\", 4);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nlet MatSort = /*#__PURE__*/(() => {\n  class MatSort {\n    _defaultOptions;\n    _initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    _stateChanges = new Subject();\n    /** The id of the most recently sorted MatSortable. */\n    active;\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    start = 'asc';\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n      return this._direction;\n    }\n    set direction(direction) {\n      if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortInvalidDirectionError(direction);\n      }\n      this._direction = direction;\n    }\n    _direction = '';\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    disableClear;\n    /** Whether the sortable is disabled. */\n    disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor(_defaultOptions) {\n      this._defaultOptions = _defaultOptions;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!sortable.id) {\n          throw getSortHeaderMissingIdError();\n        }\n        if (this.sortables.has(sortable.id)) {\n          throw getSortDuplicateSortableIdError(sortable.id);\n        }\n      }\n      this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n      this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n      if (this.active != sortable.id) {\n        this.active = sortable.id;\n        this.direction = sortable.start ? sortable.start : this.start;\n      } else {\n        this.direction = this.getNextSortDirection(sortable);\n      }\n      this.sortChange.emit({\n        active: this.active,\n        direction: this.direction\n      });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n      if (!sortable) {\n        return '';\n      }\n      // Get the sort direction cycle with the potential sortable overrides.\n      const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n      let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n      // Get and return the next direction in the cycle\n      let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n      if (nextDirectionIndex >= sortDirectionCycle.length) {\n        nextDirectionIndex = 0;\n      }\n      return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n      this._initializedStream.next();\n    }\n    ngOnChanges() {\n      this._stateChanges.next();\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n      this._initializedStream.complete();\n    }\n    static ɵfac = function MatSort_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSort,\n      selectors: [[\"\", \"matSort\", \"\"]],\n      hostAttrs: [1, \"mat-sort\"],\n      inputs: {\n        active: [0, \"matSortActive\", \"active\"],\n        start: [0, \"matSortStart\", \"start\"],\n        direction: [0, \"matSortDirection\", \"direction\"],\n        disableClear: [2, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n        disabled: [2, \"matSortDisabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        sortChange: \"matSortChange\"\n      },\n      exportAs: [\"matSort\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatSort;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nlet MatSortHeaderIntl = /*#__PURE__*/(() => {\n  class MatSortHeaderIntl {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    changes = new Subject();\n    static ɵfac = function MatSortHeaderIntl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortHeaderIntl)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSortHeaderIntl,\n      factory: MatSortHeaderIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatSortHeaderIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nlet MatSortHeader = /*#__PURE__*/(() => {\n  class MatSortHeader {\n    _intl = inject(MatSortHeaderIntl);\n    _sort = inject(MatSort, {\n      optional: true\n    });\n    _columnDef = inject('MAT_SORT_HEADER_COLUMN_DEF', {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber, {\n      optional: true\n    });\n    _renderChanges;\n    _animationsDisabled = _animationsDisabled();\n    /**\n     * Indicates which state was just cleared from the sort header.\n     * Will be reset on the next interaction. Used for coordinating animations.\n     */\n    _recentlyCleared = signal(null);\n    /**\n     * The element with role=\"button\" inside this component's view. We need this\n     * in order to apply a description with AriaDescriber.\n     */\n    _sortButton;\n    /**\n     * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n     * the column's name.\n     */\n    id;\n    /** Sets the position of the arrow that displays when sorted. */\n    arrowPosition = 'after';\n    /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n    start;\n    /** whether the sort header is disabled. */\n    disabled = false;\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n      return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n      this._updateSortActionDescription(value);\n    }\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    _sortActionDescription = 'Sort';\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    disableClear;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const defaultOptions = inject(MAT_SORT_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      // Note that we use a string token for the `_columnDef`, because the value is provided both by\n      // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n      // and we want to avoid having the sort header depending on the CDK table because\n      // of this single reference.\n      if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortHeaderNotContainedWithinSortError();\n      }\n      if (defaultOptions?.arrowPosition) {\n        this.arrowPosition = defaultOptions?.arrowPosition;\n      }\n    }\n    ngOnInit() {\n      if (!this.id && this._columnDef) {\n        this.id = this._columnDef.name;\n      }\n      this._sort.register(this);\n      this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() => this._changeDetectorRef.markForCheck());\n      this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n      this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n      // We use the focus monitor because we also want to style\n      // things differently based on the focus origin.\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(() => this._recentlyCleared.set(null));\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._sort.deregister(this);\n      this._renderChanges?.unsubscribe();\n      if (this._sortButton) {\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n      if (!this._isDisabled()) {\n        const wasSorted = this._isSorted();\n        const prevDirection = this._sort.direction;\n        this._sort.sort(this);\n        this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n      }\n    }\n    _handleKeydown(event) {\n      if (event.keyCode === SPACE || event.keyCode === ENTER) {\n        event.preventDefault();\n        this._toggleOnInteraction();\n      }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n      return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n    }\n    _isDisabled() {\n      return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n      if (!this._isSorted()) {\n        return 'none';\n      }\n      return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n      return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n      // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n      // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n      // for every *cell* in the table, creating a lot of unnecessary noise.\n      // If _sortButton is undefined, the component hasn't been initialized yet so there's\n      // nothing to update in the DOM.\n      if (this._sortButton) {\n        // removeDescription will no-op if there is no existing message.\n        // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        this._ariaDescriber?.describe(this._sortButton, newDescription);\n      }\n      this._sortActionDescription = newDescription;\n    }\n    static ɵfac = function MatSortHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortHeader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSortHeader,\n      selectors: [[\"\", \"mat-sort-header\", \"\"]],\n      hostAttrs: [1, \"mat-sort-header\"],\n      hostVars: 3,\n      hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n            return ctx._toggleOnInteraction();\n          })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n            return ctx._recentlyCleared.set(null);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n          i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n        }\n      },\n      inputs: {\n        id: [0, \"mat-sort-header\", \"id\"],\n        arrowPosition: \"arrowPosition\",\n        start: \"start\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        sortActionDescription: \"sortActionDescription\",\n        disableClear: [2, \"disableClear\", \"disableClear\", booleanAttribute]\n      },\n      exportAs: [\"matSortHeader\"],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 17,\n      consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [\"viewBox\", \"0 -960 960 960\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\"]],\n      template: function MatSortHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(3, MatSortHeader_Conditional_3_Template, 3, 0, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\")(\"mat-sort-header-descending\", ctx._sort.direction === \"desc\")(\"mat-sort-header-ascending\", ctx._sort.direction === \"asc\")(\"mat-sort-header-recently-cleared-ascending\", ctx._recentlyCleared() === \"asc\")(\"mat-sort-header-recently-cleared-descending\", ctx._recentlyCleared() === \"desc\")(\"mat-sort-header-animations-disabled\", ctx._animationsDisabled);\n          i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx._renderArrow() ? 3 : -1);\n        }\n      },\n      styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSortHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSortModule = /*#__PURE__*/(() => {\n  class MatSortModule {\n    static ɵfac = function MatSortModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSortModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n      imports: [MatCommonModule]\n    });\n  }\n  return MatSortModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matSortAnimations = {\n  // Represents:\n  // trigger('indicator', [\n  //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n  //   // 10px is the height of the sort indicator, minus the width of the pointers\n  //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that moves the sort indicator. */\n  indicator: {\n    type: 7,\n    name: 'indicator',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(10px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('leftPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: {\n    type: 7,\n    name: 'leftPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('rightPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: {\n    type: 7,\n    name: 'rightPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowOpacity', [\n  //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n  //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n  //   state(\n  //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n  //     style({opacity: 0}),\n  //   ),\n  //   // Transition between all states except for immediate transitions\n  //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n  //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: {\n    type: 7,\n    name: 'arrowOpacity',\n    definitions: [{\n      type: 0,\n      name: 'desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 1\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0.54\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => asc, * => desc, * => active, * => hint, * => void',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '0ms'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* <=> *',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowPosition', [\n  //   // Hidden Above => Hint Center\n  //   transition(\n  //     '* => desc-to-hint, * => desc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Below\n  //   transition(\n  //     '* => hint-to-desc, * => active-to-desc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n  //     ),\n  //   ),\n  //   // Hidden Below => Hint Center\n  //   transition(\n  //     '* => asc-to-hint, * => asc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Above\n  //   transition(\n  //     '* => hint-to-asc, * => active-to-asc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n  //     ),\n  //   ),\n  //   state(\n  //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n  //     style({transform: 'translateY(0)'}),\n  //   ),\n  //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n  //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n  // ])\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: {\n    type: 7,\n    name: 'arrowPosition',\n    definitions: [{\n      type: 1,\n      expr: '* => desc-to-hint, * => desc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-desc, * => active-to-desc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => asc-to-hint, * => asc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-asc, * => active-to-asc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(-25%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-asc, active-to-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(25%)'\n        },\n        offset: null\n      }\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('allowChildren', [\n  //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n  // ])\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: {\n    type: 7,\n    name: 'allowChildren',\n    definitions: [{\n      type: 1,\n      expr: '* <=> *',\n      animation: [{\n        type: 11,\n        selector: '@*',\n        animation: {\n          type: 9,\n          options: null\n        },\n        options: {\n          optional: true\n        }\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Inject", "Input", "Output", "Injectable", "SkipSelf", "inject", "ChangeDetectorRef", "ElementRef", "signal", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "FocusMonitor", "AriaDescriber", "SPACE", "ENTER", "ReplaySubject", "Subject", "merge", "_CdkPrivateStyleLoader", "_", "_animationsDisabled", "_StructuralStylesLoader", "M", "MatCommonModule", "_c0", "_c1", "MatSortHeader_Conditional_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "getSortDuplicateSortableIdError", "id", "Error", "getSortHeaderNotContainedWithinSortError", "getSortHeaderMissingIdError", "getSortInvalidDirectionError", "direction", "MAT_SORT_DEFAULT_OPTIONS", "MatSort", "_defaultOptions", "_initializedStream", "sortables", "Map", "_stateChanges", "active", "start", "_direction", "ngDevMode", "disableClear", "disabled", "sortChange", "initialized", "constructor", "register", "sortable", "has", "set", "deregister", "delete", "sort", "getNextSortDirection", "emit", "sortDirectionCycle", "getSortDirectionCycle", "nextDirectionIndex", "indexOf", "length", "ngOnInit", "next", "ngOnChanges", "ngOnDestroy", "complete", "ɵfac", "MatSort_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "outputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "sortOrder", "reverse", "push", "MatSortHeaderIntl", "changes", "MatSortHeaderIntl_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "MAT_SORT_HEADER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_SORT_HEADER_INTL_PROVIDER", "provide", "deps", "useFactory", "Mat<PERSON>ort<PERSON><PERSON>er", "_intl", "_sort", "optional", "_columnDef", "_changeDetectorRef", "_focusMonitor", "_elementRef", "_ariaDescriber", "_renderChanges", "_recentlyCleared", "_sortButton", "arrowPosition", "sortActionDescription", "_sortActionDescription", "value", "_updateSortActionDescription", "load", "defaultOptions", "name", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "querySelector", "ngAfterViewInit", "monitor", "stopMonitoring", "unsubscribe", "removeDescription", "_toggleOnInteraction", "_isDisabled", "wasSorted", "_isSorted", "prevDirection", "_handleKeydown", "event", "keyCode", "preventDefault", "_getAriaSortAttribute", "_renderArrow", "newDescription", "describe", "MatSortHeader_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "MatSortHeader_HostBindings", "ɵɵlistener", "MatSortHeader_click_HostBindingHandler", "MatSortHeader_keydown_HostBindingHandler", "$event", "MatSortHeader_mouseleave_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSortHeader_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵconditionalCreate", "ɵɵadvance", "ɵɵconditional", "styles", "encapsulation", "changeDetection", "MatSortModule", "MatSortModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "matSortAnimations", "indicator", "definitions", "transform", "offset", "expr", "animation", "timings", "options", "leftPointer", "rightPointer", "arrowOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selector"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, inject, ChangeDetectorRef, ElementRef, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { FocusMonitor, AriaDescriber } from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n    return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n    return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n    _defaultOptions;\n    _initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    _stateChanges = new Subject();\n    /** The id of the most recently sorted MatSortable. */\n    active;\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    start = 'asc';\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n        return this._direction;\n    }\n    set direction(direction) {\n        if (direction &&\n            direction !== 'asc' &&\n            direction !== 'desc' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortInvalidDirectionError(direction);\n        }\n        this._direction = direction;\n    }\n    _direction = '';\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    disableClear;\n    /** Whether the sortable is disabled. */\n    disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor(_defaultOptions) {\n        this._defaultOptions = _defaultOptions;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!sortable.id) {\n                throw getSortHeaderMissingIdError();\n            }\n            if (this.sortables.has(sortable.id)) {\n                throw getSortDuplicateSortableIdError(sortable.id);\n            }\n        }\n        this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n        this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n        if (this.active != sortable.id) {\n            this.active = sortable.id;\n            this.direction = sortable.start ? sortable.start : this.start;\n        }\n        else {\n            this.direction = this.getNextSortDirection(sortable);\n        }\n        this.sortChange.emit({ active: this.active, direction: this.direction });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n        if (!sortable) {\n            return '';\n        }\n        // Get the sort direction cycle with the potential sortable overrides.\n        const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n        let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n        // Get and return the next direction in the cycle\n        let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n        if (nextDirectionIndex >= sortDirectionCycle.length) {\n            nextDirectionIndex = 0;\n        }\n        return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n        this._initializedStream.next();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._initializedStream.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSort, deps: [{ token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatSort, isStandalone: true, selector: \"[matSort]\", inputs: { active: [\"matSortActive\", \"active\"], start: [\"matSortStart\", \"start\"], direction: [\"matSortDirection\", \"direction\"], disableClear: [\"matSortDisableClear\", \"disableClear\", booleanAttribute], disabled: [\"matSortDisabled\", \"disabled\", booleanAttribute] }, outputs: { sortChange: \"matSortChange\" }, host: { classAttribute: \"mat-sort\" }, exportAs: [\"matSort\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSort, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSort]',\n                    exportAs: 'matSort',\n                    host: {\n                        'class': 'mat-sort',\n                    },\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }], propDecorators: { active: [{\n                type: Input,\n                args: ['matSortActive']\n            }], start: [{\n                type: Input,\n                args: ['matSortStart']\n            }], direction: [{\n                type: Input,\n                args: ['matSortDirection']\n            }], disableClear: [{\n                type: Input,\n                args: [{ alias: 'matSortDisableClear', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matSortDisabled', transform: booleanAttribute }]\n            }], sortChange: [{\n                type: Output,\n                args: ['matSortChange']\n            }] } });\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n    let sortOrder = ['asc', 'desc'];\n    if (start == 'desc') {\n        sortOrder.reverse();\n    }\n    if (!disableClear) {\n        sortOrder.push('');\n    }\n    return sortOrder;\n}\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    changes = new Subject();\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortHeaderIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortHeaderIntl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortHeaderIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatSortHeaderIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n    // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n    provide: MatSortHeaderIntl,\n    deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n    useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n    _intl = inject(MatSortHeaderIntl);\n    _sort = inject(MatSort, { optional: true });\n    _columnDef = inject('MAT_SORT_HEADER_COLUMN_DEF', {\n        optional: true,\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber, { optional: true });\n    _renderChanges;\n    _animationsDisabled = _animationsDisabled();\n    /**\n     * Indicates which state was just cleared from the sort header.\n     * Will be reset on the next interaction. Used for coordinating animations.\n     */\n    _recentlyCleared = signal(null);\n    /**\n     * The element with role=\"button\" inside this component's view. We need this\n     * in order to apply a description with AriaDescriber.\n     */\n    _sortButton;\n    /**\n     * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n     * the column's name.\n     */\n    id;\n    /** Sets the position of the arrow that displays when sorted. */\n    arrowPosition = 'after';\n    /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n    start;\n    /** whether the sort header is disabled. */\n    disabled = false;\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n        return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n        this._updateSortActionDescription(value);\n    }\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    _sortActionDescription = 'Sort';\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    disableClear;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const defaultOptions = inject(MAT_SORT_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        // Note that we use a string token for the `_columnDef`, because the value is provided both by\n        // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n        // and we want to avoid having the sort header depending on the CDK table because\n        // of this single reference.\n        if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortHeaderNotContainedWithinSortError();\n        }\n        if (defaultOptions?.arrowPosition) {\n            this.arrowPosition = defaultOptions?.arrowPosition;\n        }\n    }\n    ngOnInit() {\n        if (!this.id && this._columnDef) {\n            this.id = this._columnDef.name;\n        }\n        this._sort.register(this);\n        this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() => this._changeDetectorRef.markForCheck());\n        this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n        this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n        // We use the focus monitor because we also want to style\n        // things differently based on the focus origin.\n        this._focusMonitor\n            .monitor(this._elementRef, true)\n            .subscribe(() => this._recentlyCleared.set(null));\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._sort.deregister(this);\n        this._renderChanges?.unsubscribe();\n        if (this._sortButton) {\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n        if (!this._isDisabled()) {\n            const wasSorted = this._isSorted();\n            const prevDirection = this._sort.direction;\n            this._sort.sort(this);\n            this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n        }\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            event.preventDefault();\n            this._toggleOnInteraction();\n        }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n        return (this._sort.active == this.id &&\n            (this._sort.direction === 'asc' || this._sort.direction === 'desc'));\n    }\n    _isDisabled() {\n        return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n        if (!this._isSorted()) {\n            return 'none';\n        }\n        return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n        return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n        // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n        // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n        // for every *cell* in the table, creating a lot of unnecessary noise.\n        // If _sortButton is undefined, the component hasn't been initialized yet so there's\n        // nothing to update in the DOM.\n        if (this._sortButton) {\n            // removeDescription will no-op if there is no existing message.\n            // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n            this._ariaDescriber?.describe(this._sortButton, newDescription);\n        }\n        this._sortActionDescription = newDescription;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatSortHeader, isStandalone: true, selector: \"[mat-sort-header]\", inputs: { id: [\"mat-sort-header\", \"id\"], arrowPosition: \"arrowPosition\", start: \"start\", disabled: [\"disabled\", \"disabled\", booleanAttribute], sortActionDescription: \"sortActionDescription\", disableClear: [\"disableClear\", \"disableClear\", booleanAttribute] }, host: { listeners: { \"click\": \"_toggleOnInteraction()\", \"keydown\": \"_handleKeydown($event)\", \"mouseleave\": \"_recentlyCleared.set(null)\" }, properties: { \"attr.aria-sort\": \"_getAriaSortAttribute()\", \"class.mat-sort-header-disabled\": \"_isDisabled()\" }, classAttribute: \"mat-sort-header\" }, exportAs: [\"matSortHeader\"], ngImport: i0, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [class.mat-sort-header-descending]=\\\"this._sort.direction === 'desc'\\\"\\n     [class.mat-sort-header-ascending]=\\\"this._sort.direction === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-ascending]=\\\"_recentlyCleared() === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-descending]=\\\"_recentlyCleared() === 'desc'\\\"\\n     [class.mat-sort-header-animations-disabled]=\\\"_animationsDisabled\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\">\\n      <svg viewBox=\\\"0 -960 960 960\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\\\"/>\\n      </svg>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortHeader, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-sort-header]', exportAs: 'matSortHeader', host: {\n                        'class': 'mat-sort-header',\n                        '(click)': '_toggleOnInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(mouseleave)': '_recentlyCleared.set(null)',\n                        '[attr.aria-sort]': '_getAriaSortAttribute()',\n                        '[class.mat-sort-header-disabled]': '_isDisabled()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [class.mat-sort-header-descending]=\\\"this._sort.direction === 'desc'\\\"\\n     [class.mat-sort-header-ascending]=\\\"this._sort.direction === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-ascending]=\\\"_recentlyCleared() === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-descending]=\\\"_recentlyCleared() === 'desc'\\\"\\n     [class.mat-sort-header-animations-disabled]=\\\"_animationsDisabled\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\">\\n      <svg viewBox=\\\"0 -960 960 960\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\\\"/>\\n      </svg>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { id: [{\n                type: Input,\n                args: ['mat-sort-header']\n            }], arrowPosition: [{\n                type: Input\n            }], start: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], sortActionDescription: [{\n                type: Input\n            }], disableClear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatSortModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortModule, imports: [MatCommonModule, MatSort, MatSortHeader], exports: [MatSort, MatSortHeader] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortModule, providers: [MAT_SORT_HEADER_INTL_PROVIDER], imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatSortModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatSort, MatSortHeader],\n                    exports: [MatSort, MatSortHeader],\n                    providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matSortAnimations = {\n    // Represents:\n    // trigger('indicator', [\n    //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n    //   // 10px is the height of the sort indicator, minus the width of the pointers\n    //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that moves the sort indicator. */\n    indicator: {\n        type: 7,\n        name: 'indicator',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'translateY(0px)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'translateY(10px)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('leftPointer', [\n    //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n    //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n    leftPointer: {\n        type: 7,\n        name: 'leftPointer',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'rotate(-45deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'rotate(45deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('rightPointer', [\n    //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n    //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n    rightPointer: {\n        type: 7,\n        name: 'rightPointer',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'rotate(45deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'rotate(-45deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('arrowOpacity', [\n    //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n    //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n    //   state(\n    //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n    //     style({opacity: 0}),\n    //   ),\n    //   // Transition between all states except for immediate transitions\n    //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n    //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that controls the arrow opacity. */\n    arrowOpacity: {\n        type: 7,\n        name: 'arrowOpacity',\n        definitions: [\n            {\n                type: 0,\n                name: 'desc-to-active, asc-to-active, active',\n                styles: { type: 6, styles: { 'opacity': 1 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'desc-to-hint, asc-to-hint, hint',\n                styles: { type: 6, styles: { 'opacity': 0.54 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n                styles: { type: 6, styles: { 'opacity': 0 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => asc, * => desc, * => active, * => hint, * => void',\n                animation: { type: 4, styles: null, timings: '0ms' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* <=> *',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('arrowPosition', [\n    //   // Hidden Above => Hint Center\n    //   transition(\n    //     '* => desc-to-hint, * => desc-to-active',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n    //     ),\n    //   ),\n    //   // Hint Center => Hidden Below\n    //   transition(\n    //     '* => hint-to-desc, * => active-to-desc',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n    //     ),\n    //   ),\n    //   // Hidden Below => Hint Center\n    //   transition(\n    //     '* => asc-to-hint, * => asc-to-active',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n    //     ),\n    //   ),\n    //   // Hint Center => Hidden Above\n    //   transition(\n    //     '* => hint-to-asc, * => active-to-asc',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n    //     ),\n    //   ),\n    //   state(\n    //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n    //     style({transform: 'translateY(0)'}),\n    //   ),\n    //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n    //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n    // ])\n    /**\n     * Animation for the translation of the arrow as a whole. States are separated into two\n     * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n     * peek, and active. The other states define a specific animation (source-to-destination)\n     * and are determined as a function of their prev user-perceived state and what the next state\n     * should be.\n     */\n    arrowPosition: {\n        type: 7,\n        name: 'arrowPosition',\n        definitions: [\n            {\n                type: 1,\n                expr: '* => desc-to-hint, * => desc-to-active',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => hint-to-desc, * => active-to-desc',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => asc-to-hint, * => asc-to-active',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => hint-to-asc, * => active-to-asc',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 0,\n                name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n                styles: { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-desc, active-to-desc, desc',\n                styles: { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-asc, active-to-asc, asc',\n                styles: { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('allowChildren', [\n    //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n    // ])\n    /** Necessary trigger that calls animate on children animations. */\n    allowChildren: {\n        type: 7,\n        name: 'allowChildren',\n        definitions: [\n            {\n                type: 1,\n                expr: '* <=> *',\n                animation: [\n                    {\n                        type: 11,\n                        selector: '@*',\n                        animation: { type: 9, options: null },\n                        options: { optional: true },\n                    },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,eAAe;AAChQ,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,aAAa,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACpD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASD,CAAC,IAAIE,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;;AAE1B;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwH6FnC,EAAE,CAAAqC,cAAA,YA6OkmF,CAAC;IA7OrmFrC,EAAE,CAAAsC,cAAA;IAAFtC,EAAE,CAAAqC,cAAA,YA6OmrF,CAAC;IA7OtrFrC,EAAE,CAAAuC,SAAA,aA6O8wF,CAAC;IA7OjxFvC,EAAE,CAAAwC,YAAA,CA6O4xF,CAAC,CAAW,CAAC;EAAA;AAAA;AApWx4F,SAASC,+BAA+BA,CAACC,EAAE,EAAE;EACzC,OAAOC,KAAK,CAAC,kDAAkDD,EAAE,IAAI,CAAC;AAC1E;AACA;AACA,SAASE,wCAAwCA,CAAA,EAAG;EAChD,OAAOD,KAAK,CAAC,kFAAkF,CAAC;AACpG;AACA;AACA,SAASE,2BAA2BA,CAAA,EAAG;EACnC,OAAOF,KAAK,CAAC,kDAAkD,CAAC;AACpE;AACA;AACA,SAASG,4BAA4BA,CAACC,SAAS,EAAE;EAC7C,OAAOJ,KAAK,CAAC,GAAGI,SAAS,mDAAmD,CAAC;AACjF;;AAEA;AACA,MAAMC,wBAAwB,gBAAG,IAAI/C,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AAAA,IACMgD,OAAO;EAAb,MAAMA,OAAO,CAAC;IACVC,eAAe;IACfC,kBAAkB,GAAG,IAAI5B,aAAa,CAAC,CAAC,CAAC;IACzC;IACA6B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrB;IACAC,aAAa,GAAG,IAAI9B,OAAO,CAAC,CAAC;IAC7B;IACA+B,MAAM;IACN;AACJ;AACA;AACA;IACIC,KAAK,GAAG,KAAK;IACb;IACA,IAAIT,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACU,UAAU;IAC1B;IACA,IAAIV,SAASA,CAACA,SAAS,EAAE;MACrB,IAAIA,SAAS,IACTA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,MAAM,KACnB,OAAOW,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMZ,4BAA4B,CAACC,SAAS,CAAC;MACjD;MACA,IAAI,CAACU,UAAU,GAAGV,SAAS;IAC/B;IACAU,UAAU,GAAG,EAAE;IACf;AACJ;AACA;AACA;IACIE,YAAY;IACZ;IACAC,QAAQ,GAAG,KAAK;IAChB;IACAC,UAAU,GAAG,IAAI3D,YAAY,CAAC,CAAC;IAC/B;IACA4D,WAAW,GAAG,IAAI,CAACX,kBAAkB;IACrCY,WAAWA,CAACb,eAAe,EAAE;MACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;IACA;AACJ;AACA;AACA;IACIc,QAAQA,CAACC,QAAQ,EAAE;MACf,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACO,QAAQ,CAACvB,EAAE,EAAE;UACd,MAAMG,2BAA2B,CAAC,CAAC;QACvC;QACA,IAAI,IAAI,CAACO,SAAS,CAACc,GAAG,CAACD,QAAQ,CAACvB,EAAE,CAAC,EAAE;UACjC,MAAMD,+BAA+B,CAACwB,QAAQ,CAACvB,EAAE,CAAC;QACtD;MACJ;MACA,IAAI,CAACU,SAAS,CAACe,GAAG,CAACF,QAAQ,CAACvB,EAAE,EAAEuB,QAAQ,CAAC;IAC7C;IACA;AACJ;AACA;AACA;IACIG,UAAUA,CAACH,QAAQ,EAAE;MACjB,IAAI,CAACb,SAAS,CAACiB,MAAM,CAACJ,QAAQ,CAACvB,EAAE,CAAC;IACtC;IACA;IACA4B,IAAIA,CAACL,QAAQ,EAAE;MACX,IAAI,IAAI,CAACV,MAAM,IAAIU,QAAQ,CAACvB,EAAE,EAAE;QAC5B,IAAI,CAACa,MAAM,GAAGU,QAAQ,CAACvB,EAAE;QACzB,IAAI,CAACK,SAAS,GAAGkB,QAAQ,CAACT,KAAK,GAAGS,QAAQ,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK;MACjE,CAAC,MACI;QACD,IAAI,CAACT,SAAS,GAAG,IAAI,CAACwB,oBAAoB,CAACN,QAAQ,CAAC;MACxD;MACA,IAAI,CAACJ,UAAU,CAACW,IAAI,CAAC;QAAEjB,MAAM,EAAE,IAAI,CAACA,MAAM;QAAER,SAAS,EAAE,IAAI,CAACA;MAAU,CAAC,CAAC;IAC5E;IACA;IACAwB,oBAAoBA,CAACN,QAAQ,EAAE;MAC3B,IAAI,CAACA,QAAQ,EAAE;QACX,OAAO,EAAE;MACb;MACA;MACA,MAAMN,YAAY,GAAGM,QAAQ,EAAEN,YAAY,IAAI,IAAI,CAACA,YAAY,IAAI,CAAC,CAAC,IAAI,CAACT,eAAe,EAAES,YAAY;MACxG,IAAIc,kBAAkB,GAAGC,qBAAqB,CAACT,QAAQ,CAACT,KAAK,IAAI,IAAI,CAACA,KAAK,EAAEG,YAAY,CAAC;MAC1F;MACA,IAAIgB,kBAAkB,GAAGF,kBAAkB,CAACG,OAAO,CAAC,IAAI,CAAC7B,SAAS,CAAC,GAAG,CAAC;MACvE,IAAI4B,kBAAkB,IAAIF,kBAAkB,CAACI,MAAM,EAAE;QACjDF,kBAAkB,GAAG,CAAC;MAC1B;MACA,OAAOF,kBAAkB,CAACE,kBAAkB,CAAC;IACjD;IACAG,QAAQA,CAAA,EAAG;MACP,IAAI,CAAC3B,kBAAkB,CAAC4B,IAAI,CAAC,CAAC;IAClC;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC1B,aAAa,CAACyB,IAAI,CAAC,CAAC;IAC7B;IACAE,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC3B,aAAa,CAAC4B,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC/B,kBAAkB,CAAC+B,QAAQ,CAAC,CAAC;IACtC;IACA,OAAOC,IAAI,YAAAC,gBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFpC,OAAO,EAAjBjD,EAAE,CAAAsF,iBAAA,CAAiCtC,wBAAwB;IAAA;IACpJ,OAAOuC,IAAI,kBAD8EvF,EAAE,CAAAwF,iBAAA;MAAAC,IAAA,EACJxC,OAAO;MAAAyC,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAArC,MAAA;QAAAC,KAAA;QAAAT,SAAA;QAAAY,YAAA,6CAAkOxD,gBAAgB;QAAAyD,QAAA,qCAA6CzD,gBAAgB;MAAA;MAAA0F,OAAA;QAAAhC,UAAA;MAAA;MAAAiC,QAAA;MAAAC,QAAA,GADpT/F,EAAE,CAAAgG,oBAAA;IAAA;EAE/F;EAAC,OAtGK/C,OAAO;AAAA;AAuGb;EAAA,QAAAS,SAAA,oBAAAA,SAAA;AAAA;AAiCA;AACA,SAASgB,qBAAqBA,CAAClB,KAAK,EAAEG,YAAY,EAAE;EAChD,IAAIsC,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EAC/B,IAAIzC,KAAK,IAAI,MAAM,EAAE;IACjByC,SAAS,CAACC,OAAO,CAAC,CAAC;EACvB;EACA,IAAI,CAACvC,YAAY,EAAE;IACfsC,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,OAAOF,SAAS;AACpB;;AAEA;AACA;AACA;AACA;AAHA,IAIMG,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB;AACJ;AACA;AACA;IACIC,OAAO,GAAG,IAAI7E,OAAO,CAAC,CAAC;IACvB,OAAO2D,IAAI,YAAAmB,0BAAAjB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFe,iBAAiB;IAAA;IACpH,OAAOG,KAAK,kBA3D6EvG,EAAE,CAAAwG,kBAAA;MAAAC,KAAA,EA2DYL,iBAAiB;MAAAM,OAAA,EAAjBN,iBAAiB,CAAAjB,IAAA;MAAAwB,UAAA,EAAc;IAAM;EAChJ;EAAC,OARKP,iBAAiB;AAAA;AASvB;EAAA,QAAA1C,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA,SAASkD,qCAAqCA,CAACC,UAAU,EAAE;EACvD,OAAOA,UAAU,IAAI,IAAIT,iBAAiB,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,6BAA6B,GAAG;EAClC;EACAC,OAAO,EAAEX,iBAAiB;EAC1BY,IAAI,EAAE,CAAC,cAAC,IAAI3G,QAAQ,CAAC,CAAC,eAAE,IAAIK,QAAQ,CAAC,CAAC,EAAE0F,iBAAiB,CAAC,CAAC;EAC3Da,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,IASMM,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBC,KAAK,GAAGxG,MAAM,CAACyF,iBAAiB,CAAC;IACjCgB,KAAK,GAAGzG,MAAM,CAACsC,OAAO,EAAE;MAAEoE,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3CC,UAAU,GAAG3G,MAAM,CAAC,4BAA4B,EAAE;MAC9C0G,QAAQ,EAAE;IACd,CAAC,CAAC;IACFE,kBAAkB,GAAG5G,MAAM,CAACC,iBAAiB,CAAC;IAC9C4G,aAAa,GAAG7G,MAAM,CAACQ,YAAY,CAAC;IACpCsG,WAAW,GAAG9G,MAAM,CAACE,UAAU,CAAC;IAChC6G,cAAc,GAAG/G,MAAM,CAACS,aAAa,EAAE;MAAEiG,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC1DM,cAAc;IACd/F,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C;AACJ;AACA;AACA;IACIgG,gBAAgB,GAAG9G,MAAM,CAAC,IAAI,CAAC;IAC/B;AACJ;AACA;AACA;IACI+G,WAAW;IACX;AACJ;AACA;AACA;IACInF,EAAE;IACF;IACAoF,aAAa,GAAG,OAAO;IACvB;IACAtE,KAAK;IACL;IACAI,QAAQ,GAAG,KAAK;IAChB;AACJ;AACA;AACA;IACI,IAAImE,qBAAqBA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACC,sBAAsB;IACtC;IACA,IAAID,qBAAqBA,CAACE,KAAK,EAAE;MAC7B,IAAI,CAACC,4BAA4B,CAACD,KAAK,CAAC;IAC5C;IACA;IACA;IACA;IACAD,sBAAsB,GAAG,MAAM;IAC/B;IACArE,YAAY;IACZI,WAAWA,CAAA,EAAG;MACVpD,MAAM,CAACe,sBAAsB,CAAC,CAACyG,IAAI,CAACtG,uBAAuB,CAAC;MAC5D,MAAMuG,cAAc,GAAGzH,MAAM,CAACqC,wBAAwB,EAAE;QACpDqE,QAAQ,EAAE;MACd,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACD,KAAK,KAAK,OAAO1D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAChE,MAAMd,wCAAwC,CAAC,CAAC;MACpD;MACA,IAAIwF,cAAc,EAAEN,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAGM,cAAc,EAAEN,aAAa;MACtD;IACJ;IACAhD,QAAQA,CAAA,EAAG;MACP,IAAI,CAAC,IAAI,CAACpC,EAAE,IAAI,IAAI,CAAC4E,UAAU,EAAE;QAC7B,IAAI,CAAC5E,EAAE,GAAG,IAAI,CAAC4E,UAAU,CAACe,IAAI;MAClC;MACA,IAAI,CAACjB,KAAK,CAACpD,QAAQ,CAAC,IAAI,CAAC;MACzB,IAAI,CAAC2D,cAAc,GAAGlG,KAAK,CAAC,IAAI,CAAC2F,KAAK,CAAC9D,aAAa,EAAE,IAAI,CAAC8D,KAAK,CAACvD,UAAU,CAAC,CAACyE,SAAS,CAAC,MAAM,IAAI,CAACf,kBAAkB,CAACgB,YAAY,CAAC,CAAC,CAAC;MACpI,IAAI,CAACV,WAAW,GAAG,IAAI,CAACJ,WAAW,CAACe,aAAa,CAACC,aAAa,CAAC,4BAA4B,CAAC;MAC7F,IAAI,CAACP,4BAA4B,CAAC,IAAI,CAACF,sBAAsB,CAAC;IAClE;IACAU,eAAeA,CAAA,EAAG;MACd;MACA;MACA,IAAI,CAAClB,aAAa,CACbmB,OAAO,CAAC,IAAI,CAAClB,WAAW,EAAE,IAAI,CAAC,CAC/Ba,SAAS,CAAC,MAAM,IAAI,CAACV,gBAAgB,CAACzD,GAAG,CAAC,IAAI,CAAC,CAAC;IACzD;IACAc,WAAWA,CAAA,EAAG;MACV,IAAI,CAACuC,aAAa,CAACoB,cAAc,CAAC,IAAI,CAACnB,WAAW,CAAC;MACnD,IAAI,CAACL,KAAK,CAAChD,UAAU,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACuD,cAAc,EAAEkB,WAAW,CAAC,CAAC;MAClC,IAAI,IAAI,CAAChB,WAAW,EAAE;QAClB,IAAI,CAACH,cAAc,EAAEoB,iBAAiB,CAAC,IAAI,CAACjB,WAAW,EAAE,IAAI,CAACG,sBAAsB,CAAC;MACzF;IACJ;IACA;IACAe,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;QACrB,MAAMC,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;QAClC,MAAMC,aAAa,GAAG,IAAI,CAAC/B,KAAK,CAACrE,SAAS;QAC1C,IAAI,CAACqE,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,CAACsD,gBAAgB,CAACzD,GAAG,CAAC8E,SAAS,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC,GAAGC,aAAa,GAAG,IAAI,CAAC;MACpF;IACJ;IACAC,cAAcA,CAACC,KAAK,EAAE;MAClB,IAAIA,KAAK,CAACC,OAAO,KAAKjI,KAAK,IAAIgI,KAAK,CAACC,OAAO,KAAKhI,KAAK,EAAE;QACpD+H,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACR,oBAAoB,CAAC,CAAC;MAC/B;IACJ;IACA;IACAG,SAASA,CAAA,EAAG;MACR,OAAQ,IAAI,CAAC9B,KAAK,CAAC7D,MAAM,IAAI,IAAI,CAACb,EAAE,KAC/B,IAAI,CAAC0E,KAAK,CAACrE,SAAS,KAAK,KAAK,IAAI,IAAI,CAACqE,KAAK,CAACrE,SAAS,KAAK,MAAM,CAAC;IAC3E;IACAiG,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAAC5B,KAAK,CAACxD,QAAQ,IAAI,IAAI,CAACA,QAAQ;IAC/C;IACA;AACJ;AACA;AACA;AACA;AACA;IACI4F,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC,EAAE;QACnB,OAAO,MAAM;MACjB;MACA,OAAO,IAAI,CAAC9B,KAAK,CAACrE,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;IACrE;IACA;IACA0G,YAAYA,CAAA,EAAG;MACX,OAAO,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC;IAClD;IACAhB,4BAA4BA,CAACwB,cAAc,EAAE;MACzC;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC7B,WAAW,EAAE;QAClB;QACA;QACA,IAAI,CAACH,cAAc,EAAEoB,iBAAiB,CAAC,IAAI,CAACjB,WAAW,EAAE,IAAI,CAACG,sBAAsB,CAAC;QACrF,IAAI,CAACN,cAAc,EAAEiC,QAAQ,CAAC,IAAI,CAAC9B,WAAW,EAAE6B,cAAc,CAAC;MACnE;MACA,IAAI,CAAC1B,sBAAsB,GAAG0B,cAAc;IAChD;IACA,OAAOvE,IAAI,YAAAyE,sBAAAvE,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6B,aAAa;IAAA;IAChH,OAAO2C,IAAI,kBA7O8E7J,EAAE,CAAA8J,iBAAA;MAAArE,IAAA,EA6OJyB,aAAa;MAAAxB,SAAA;MAAAC,SAAA;MAAAoE,QAAA;MAAAC,YAAA,WAAAC,2BAAA9H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7OXnC,EAAE,CAAAkK,UAAA,mBAAAC,uCAAA;YAAA,OA6OJ/H,GAAA,CAAA2G,oBAAA,CAAqB,CAAC;UAAA,CAAV,CAAC,qBAAAqB,yCAAAC,MAAA;YAAA,OAAbjI,GAAA,CAAAgH,cAAA,CAAAiB,MAAqB,CAAC;UAAA,CAAV,CAAC,wBAAAC,4CAAA;YAAA,OAAblI,GAAA,CAAAwF,gBAAA,CAAAzD,GAAA,CAAqB,IAAI,CAAC;UAAA,CAAd,CAAC;QAAA;QAAA,IAAAhC,EAAA;UA7OXnC,EAAE,CAAAuK,WAAA,cA6OJnI,GAAA,CAAAoH,qBAAA,CAAsB,CAAC;UA7OrBxJ,EAAE,CAAAwK,WAAA,6BA6OJpI,GAAA,CAAA4G,WAAA,CAAY,EAAC;QAAA;MAAA;MAAApD,MAAA;QAAAlD,EAAA;QAAAoF,aAAA;QAAAtE,KAAA;QAAAI,QAAA,8BAAiLzD,gBAAgB;QAAA4H,qBAAA;QAAApE,YAAA,sCAAkGxD,gBAAgB;MAAA;MAAA2F,QAAA;MAAA2E,KAAA,EAAAzI,GAAA;MAAA0I,kBAAA,EAAAzI,GAAA;MAAA0I,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAA5I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7O9TnC,EAAE,CAAAgL,eAAA;UAAFhL,EAAE,CAAAqC,cAAA,YA6Os/D,CAAC,YAAub,CAAC;UA7Oj7ErC,EAAE,CAAAiL,YAAA,EA6O68E,CAAC;UA7Oh9EjL,EAAE,CAAAwC,YAAA,CA6Ou9E,CAAC;UA7O19ExC,EAAE,CAAAkL,mBAAA,IAAAhJ,oCAAA,gBA6OujF,CAAC;UA7O1jFlC,EAAE,CAAAwC,YAAA,CA6OqzF,CAAC;QAAA;QAAA,IAAAL,EAAA;UA7OxzFnC,EAAE,CAAAwK,WAAA,2BAAApI,GAAA,CAAA8G,SAAA,EA6Ou6C,CAAC,oCAAA9G,GAAA,CAAA0F,aAAA,aAA4E,CAAC,+BAAA1F,GAAA,CAAAgF,KAAA,CAAArE,SAAA,WAA4E,CAAC,8BAAAX,GAAA,CAAAgF,KAAA,CAAArE,SAAA,UAA0E,CAAC,+CAAAX,GAAA,CAAAwF,gBAAA,YAAyF,CAAC,gDAAAxF,GAAA,CAAAwF,gBAAA,aAA2F,CAAC,wCAAAxF,GAAA,CAAAR,mBAAyE,CAAC;UA7O/4D5B,EAAE,CAAAuK,WAAA,aAAAnI,GAAA,CAAA4G,WAAA,uBAAA5G,GAAA,CAAA4G,WAAA;UAAFhJ,EAAE,CAAAmL,SAAA,EA6O6yF,CAAC;UA7OhzFnL,EAAE,CAAAoL,aAAA,CAAAhJ,GAAA,CAAAqH,YAAA,WA6O6yF,CAAC;QAAA;MAAA;MAAA4B,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC74F;EAAC,OAhJKrE,aAAa;AAAA;AAiJnB;EAAA,QAAAxD,SAAA,oBAAAA,SAAA;AAAA;AAyBoB,IAEd8H,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB,OAAOrG,IAAI,YAAAsG,sBAAApG,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmG,aAAa;IAAA;IAChH,OAAOE,IAAI,kBA5Q8E1L,EAAE,CAAA2L,gBAAA;MAAAlG,IAAA,EA4QS+F;IAAa;IACjH,OAAOI,IAAI,kBA7Q8E5L,EAAE,CAAA6L,gBAAA;MAAAC,SAAA,EA6QmC,CAAChF,6BAA6B,CAAC;MAAAiF,OAAA,GAAYhK,eAAe;IAAA;EAC5L;EAAC,OAJKyJ,aAAa;AAAA;AAKnB;EAAA,QAAA9H,SAAA,oBAAAA,SAAA;AAAA;;AASA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsI,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,SAAS,EAAE;IACPxG,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,WAAW;IACjB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,iBAAiB;MACvBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAkB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC9E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,mBAAmB;MACzBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAmB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC/E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAE7G,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE,IAAI;QAAEkB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,WAAW,EAAE;IACThH,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,aAAa;IACnB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,iBAAiB;MACvBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAiB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,mBAAmB;MACzBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAgB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAE7G,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE,IAAI;QAAEkB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAE,YAAY,EAAE;IACVjH,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,cAAc;IACpB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,iBAAiB;MACvBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAgB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,mBAAmB;MACzBgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAiB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAE7G,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE,IAAI;QAAEkB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,YAAY,EAAE;IACVlH,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,cAAc;IACpB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,uCAAuC;MAC7CgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAE,SAAS,EAAE;QAAE,CAAC;QAAEe,MAAM,EAAE;MAAK;IAC9D,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,iCAAiC;MACvCgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAE,SAAS,EAAE;QAAK,CAAC;QAAEe,MAAM,EAAE;MAAK;IACjE,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,2EAA2E;MACjFgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAE,SAAS,EAAE;QAAE,CAAC;QAAEe,MAAM,EAAE;MAAK;IAC9D,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,wDAAwD;MAC9DC,SAAS,EAAE;QAAE7G,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE,IAAI;QAAEkB,OAAO,EAAE;MAAM,CAAC;MACpDC,OAAO,EAAE;IACb,CAAC,EACD;MACI/G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE;QAAE7G,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE,IAAI;QAAEkB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1E,aAAa,EAAE;IACXrC,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,eAAe;IACrB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,wCAAwC;MAC9CC,SAAS,EAAE;QACP7G,IAAI,EAAE,CAAC;QACP4F,MAAM,EAAE;UACJ5F,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAmB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,EACpE;YAAE3G,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAgB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC;QAEzE,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI/G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,wCAAwC;MAC9CC,SAAS,EAAE;QACP7G,IAAI,EAAE,CAAC;QACP4F,MAAM,EAAE;UACJ5F,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAgB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,EACjE;YAAE3G,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAkB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC;QAE3E,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI/G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,sCAAsC;MAC5CC,SAAS,EAAE;QACP7G,IAAI,EAAE,CAAC;QACP4F,MAAM,EAAE;UACJ5F,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAkB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,EACnE;YAAE3G,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAgB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC;QAEzE,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI/G,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,sCAAsC;MAC5CC,SAAS,EAAE;QACP7G,IAAI,EAAE,CAAC;QACP4F,MAAM,EAAE;UACJ5F,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAgB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,EACjE;YAAE3G,IAAI,EAAE,CAAC;YAAE4F,MAAM,EAAE;cAAEc,SAAS,EAAE;YAAmB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC;QAE5E,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI/G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,wEAAwE;MAC9EgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAgB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,oCAAoC;MAC1CgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAmB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC/E,CAAC,EACD;MACI3G,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAE,iCAAiC;MACvCgD,MAAM,EAAE;QAAE5F,IAAI,EAAE,CAAC;QAAE4F,MAAM,EAAE;UAAEc,SAAS,EAAE;QAAkB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC9E,CAAC,CACJ;IACDI,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACAI,aAAa,EAAE;IACXnH,IAAI,EAAE,CAAC;IACP4C,IAAI,EAAE,eAAe;IACrB6D,WAAW,EAAE,CACT;MACIzG,IAAI,EAAE,CAAC;MACP4G,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,CACP;QACI7G,IAAI,EAAE,EAAE;QACRoH,QAAQ,EAAE,IAAI;QACdP,SAAS,EAAE;UAAE7G,IAAI,EAAE,CAAC;UAAE+G,OAAO,EAAE;QAAK,CAAC;QACrCA,OAAO,EAAE;UAAEnF,QAAQ,EAAE;QAAK;MAC9B,CAAC,CACJ;MACDmF,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASxJ,wBAAwB,EAAE8D,6BAA6B,EAAEF,qCAAqC,EAAE3D,OAAO,EAAEiE,aAAa,EAAEd,iBAAiB,EAAEoF,aAAa,EAAEQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}