using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Employees")]
    public class Employee
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string EmployeeCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(100)]
        public string IdentityNumber { get; set; } = string.Empty;

        public DateTime BirthDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Gender { get; set; } = string.Empty;

        [StringLength(40)]
        public string? MaritalStatus { get; set; }

        [StringLength(100)]
        public string? Nationality { get; set; }

        [Required]
        [StringLength(40)]
        public string Phone1 { get; set; } = string.Empty;

        [StringLength(40)]
        public string? Phone2 { get; set; }

        [Required]
        [StringLength(200)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Address { get; set; } = string.Empty;

        public int AreaId { get; set; }

        public int? DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public int BranchId { get; set; }

        [StringLength(40)]
        public string? BiometricId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Allowances { get; set; } = 0;

        [StringLength(100)]
        public string? SocialInsuranceNumber { get; set; }

        [StringLength(100)]
        public string? TaxNumber { get; set; }

        [StringLength(100)]
        public string? BankAccountNumber { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(200)]
        public string? EmergencyContactName { get; set; }

        [StringLength(40)]
        public string? EmergencyContactPhone { get; set; }

        [StringLength(200)]
        public string? EmergencyContactRelation { get; set; }

        public DateTime? HireDate { get; set; }
        public DateTime? TerminationDate { get; set; }

        [StringLength(1000)]
        public string? TerminationReason { get; set; }

        public bool IsActive { get; set; } = true;

        [StringLength(1000)]
        public string? ProfileImage { get; set; }

        [StringLength(2000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("AreaId")]
        public virtual Area Area { get; set; } = null!;

        [ForeignKey("DepartmentId")]
        public virtual Department? Department { get; set; }

        [ForeignKey("PositionId")]
        public virtual Position? Position { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<EmployeeShift> EmployeeShifts { get; set; } = new List<EmployeeShift>();
        public virtual ICollection<AttendanceRecord> AttendanceRecords { get; set; } = new List<AttendanceRecord>();
        public virtual ICollection<EmployeeLeave> EmployeeLeaves { get; set; } = new List<EmployeeLeave>();
        public virtual ICollection<EmployeeLeaveBalance> EmployeeLeaveBalances { get; set; } = new List<EmployeeLeaveBalance>();
        public virtual ICollection<Payroll> Payrolls { get; set; } = new List<Payroll>();
        public virtual ICollection<EmployeeDocument> EmployeeDocuments { get; set; } = new List<EmployeeDocument>();
    }

    [Table("Departments")]
    public class Department
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(40)]
        public string Code { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public int? ManagerId { get; set; }
        public int BranchId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("ManagerId")]
        public virtual Employee? Manager { get; set; }

        [ForeignKey("BranchId")]
        public virtual Branch Branch { get; set; } = null!;

        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public virtual ICollection<Position> Positions { get; set; } = new List<Position>();
    }

    [Table("Positions")]
    public class Position
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        [Required]
        [StringLength(40)]
        public string Code { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public int DepartmentId { get; set; }
        public int Level { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinSalary { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxSalary { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; } = null!;

        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    }

    [Table("EmployeeDocuments")]
    public class EmployeeDocument
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }

        [Required]
        [StringLength(100)]
        public string DocumentType { get; set; } = string.Empty;

        [Required]
        [StringLength(400)]
        public string DocumentName { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? FileName { get; set; }

        [StringLength(2000)]
        public string? FilePath { get; set; }

        public long? FileSize { get; set; }

        [StringLength(200)]
        public string? MimeType { get; set; }

        public DateTime? ExpiryDate { get; set; }
        public bool IsRequired { get; set; } = false;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int UploadedBy { get; set; }
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("UploadedBy")]
        public virtual User UploadedByUser { get; set; } = null!;
    }
}
