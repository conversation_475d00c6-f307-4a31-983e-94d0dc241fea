{"ast": null, "code": "import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nlet MatFormFieldModule = /*#__PURE__*/(() => {\n  class MatFormFieldModule {\n    static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatFormFieldModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, ObserversModule, MatFormField, MatCommonModule]\n    });\n  }\n  return MatFormFieldModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatFormFieldModule as M };", "map": {"version": 3, "names": ["ObserversModule", "i0", "NgModule", "j", "MatFormField", "M", "<PERSON><PERSON><PERSON><PERSON>", "b", "<PERSON><PERSON><PERSON><PERSON>", "c", "MatHint", "e", "MatPrefix", "g", "MatSuffix", "MatCommonModule", "MatFormFieldModule", "ɵfac", "MatFormFieldModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/module-DzZHEh7B.mjs"], "sourcesContent": ["import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON>ield, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\nclass MatFormFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule,\n            MatFormField,\n            MatLabel,\n            Mat<PERSON>rror,\n            MatHint,\n            MatPrefix,\n            MatSuffix], exports: [MatFormField, MatLabel, Mat<PERSON>int, <PERSON><PERSON>rror, MatPrefix, MatSuffix, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule,\n            MatFormField, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatFormFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        ObserversModule,\n                        MatFormField,\n                        MatLabel,\n                        MatError,\n                        MatHint,\n                        MatPrefix,\n                        MatSuffix,\n                    ],\n                    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n                }]\n        }] });\n\nexport { MatFormFieldModule as M };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAAST,CAAC,IAAIU,eAAe,QAAQ,8BAA8B;AAAC,IAE9DC,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAOC,IAAI,YAAAC,2BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,kBAAkB;IAAA;IACrH,OAAOI,IAAI,kBAD8EnB,EAAE,CAAAoB,gBAAA;MAAAC,IAAA,EACSN;IAAkB;IAQtH,OAAOO,IAAI,kBAT8EtB,EAAE,CAAAuB,gBAAA;MAAAC,OAAA,GASuCV,eAAe,EACzIf,eAAe,EACfI,YAAY,EAAEW,eAAe;IAAA;EACzC;EAAC,OAbKC,kBAAkB;AAAA;AAcxB;EAAA,QAAAU,SAAA,oBAAAA,SAAA;AAAA;AAiBA,SAASV,kBAAkB,IAAIX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}