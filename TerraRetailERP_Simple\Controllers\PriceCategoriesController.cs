using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP_Simple.Data;
using TerraRetailERP_Simple.Models;

namespace TerraRetailERP_Simple.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PriceCategoriesController : ControllerBase
    {
        private readonly AppDbContext _context;

        public PriceCategoriesController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetPriceCategories()
        {
            try
            {
                var priceCategories = await _context.PriceCategories
                    .Where(pc => pc.IsActive)
                    .OrderBy(pc => pc.DisplayOrder)
                    .ThenBy(pc => pc.NameAr)
                    .Select(pc => new
                    {
                        id = pc.Id,
                        nameAr = pc.NameAr,
                        nameEn = pc.NameEn,
                        description = pc.Description,
                        discountPercentage = pc.DiscountPercentage,
                        markupPercentage = pc.MarkupPercentage,
                        displayOrder = pc.DisplayOrder,
                        color = pc.Color,
                        isActive = pc.IsActive
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم جلب قائمة فئات الأسعار بنجاح",
                    data = priceCategories,
                    count = priceCategories.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب قائمة فئات الأسعار",
                    error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetPriceCategory(int id)
        {
            try
            {
                var priceCategory = await _context.PriceCategories
                    .Where(pc => pc.Id == id && pc.IsActive)
                    .Select(pc => new
                    {
                        id = pc.Id,
                        nameAr = pc.NameAr,
                        nameEn = pc.NameEn,
                        description = pc.Description,
                        discountPercentage = pc.DiscountPercentage,
                        markupPercentage = pc.MarkupPercentage,
                        displayOrder = pc.DisplayOrder,
                        color = pc.Color,
                        isActive = pc.IsActive,
                        createdAt = pc.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (priceCategory == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "فئة السعر غير موجودة"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "تم جلب بيانات فئة السعر بنجاح",
                    data = priceCategory
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب بيانات فئة السعر",
                    error = ex.Message
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult<object>> CreatePriceCategory(CreatePriceCategoryRequest request)
        {
            try
            {
                var priceCategory = new PriceCategory
                {
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    Description = request.Description,
                    DiscountPercentage = request.DiscountPercentage,
                    MarkupPercentage = request.MarkupPercentage,
                    DisplayOrder = request.DisplayOrder,
                    Color = request.Color,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.PriceCategories.Add(priceCategory);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetPriceCategory), new { id = priceCategory.Id }, new
                {
                    success = true,
                    message = "تم إضافة فئة السعر بنجاح",
                    data = new
                    {
                        id = priceCategory.Id,
                        nameAr = priceCategory.NameAr,
                        nameEn = priceCategory.NameEn,
                        description = priceCategory.Description,
                        discountPercentage = priceCategory.DiscountPercentage,
                        markupPercentage = priceCategory.MarkupPercentage,
                        displayOrder = priceCategory.DisplayOrder,
                        color = priceCategory.Color,
                        isActive = priceCategory.IsActive
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء إضافة فئة السعر",
                    error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<object>> UpdatePriceCategory(int id, UpdatePriceCategoryRequest request)
        {
            try
            {
                var priceCategory = await _context.PriceCategories.FindAsync(id);
                if (priceCategory == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "فئة السعر غير موجودة"
                    });
                }

                priceCategory.NameAr = request.NameAr;
                priceCategory.NameEn = request.NameEn;
                priceCategory.Description = request.Description;
                priceCategory.DiscountPercentage = request.DiscountPercentage;
                priceCategory.MarkupPercentage = request.MarkupPercentage;
                priceCategory.DisplayOrder = request.DisplayOrder;
                priceCategory.Color = request.Color;
                priceCategory.IsActive = request.IsActive;
                priceCategory.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث بيانات فئة السعر بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء تحديث فئة السعر",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeletePriceCategory(int id)
        {
            try
            {
                var priceCategory = await _context.PriceCategories.FindAsync(id);
                if (priceCategory == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "فئة السعر غير موجودة"
                    });
                }

                priceCategory.IsActive = false;
                priceCategory.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف فئة السعر بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء حذف فئة السعر",
                    error = ex.Message
                });
            }
        }
    }

    // DTOs
    public class CreatePriceCategoryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal MarkupPercentage { get; set; } = 0;
        public int DisplayOrder { get; set; } = 1;
        public string? Color { get; set; }
    }

    public class UpdatePriceCategoryRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public string? Description { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal MarkupPercentage { get; set; } = 0;
        public int DisplayOrder { get; set; } = 1;
        public string? Color { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
