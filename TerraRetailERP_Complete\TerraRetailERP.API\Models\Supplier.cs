using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Suppliers")]
    public class Supplier
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(40)]
        public string SupplierCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameEn { get; set; }

        public int SupplierTypeId { get; set; }

        [StringLength(40)]
        public string? Phone1 { get; set; }

        [StringLength(40)]
        public string? Phone2 { get; set; }

        [StringLength(200)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(400)]
        public string? Website { get; set; }

        [StringLength(1000)]
        public string? Address { get; set; }

        public int? AreaId { get; set; }
        public int? CountryId { get; set; }

        [StringLength(200)]
        public string? ContactPersonName { get; set; }

        [StringLength(40)]
        public string? ContactPersonPhone { get; set; }

        [StringLength(200)]
        public string? ContactPersonEmail { get; set; }

        public int PaymentTerms { get; set; } = 30; // Days

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(100)]
        public string? TaxNumber { get; set; }

        [StringLength(100)]
        public string? CommercialRegister { get; set; }

        [StringLength(200)]
        public string? BankName { get; set; }

        [StringLength(100)]
        public string? BankAccountNumber { get; set; }

        [StringLength(100)]
        public string? IBAN { get; set; }

        public bool IsActive { get; set; } = true;

        public int? Rating { get; set; } // 1-5 stars

        [StringLength(2000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        public int? DeliveryDays { get; set; }

        [StringLength(200)]
        public string? CreatedBy { get; set; }

        [StringLength(200)]
        public string? UpdatedBy { get; set; }

        public bool IsDeleted { get; set; } = false;

        public int? ChartAccountId { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierTypeId")]
        public virtual SupplierType SupplierType { get; set; } = null!;

        [ForeignKey("AreaId")]
        public virtual Area? Area { get; set; }

        [ForeignKey("CountryId")]
        public virtual Country? Country { get; set; }

        [ForeignKey("ChartAccountId")]
        public virtual ChartOfAccount? ChartAccount { get; set; }

        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<ProductSupplier> ProductSuppliers { get; set; } = new List<ProductSupplier>();
        public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
        public virtual ICollection<JournalEntry> JournalEntries { get; set; } = new List<JournalEntry>();
    }

    [Table("SupplierTypes")]
    public class SupplierType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string NameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameEn { get; set; }

        [StringLength(400)]
        public string? Description { get; set; }

        public int DefaultPaymentTerms { get; set; } = 30;

        public bool IsActive { get; set; } = true;

        [StringLength(14)]
        public string? Color { get; set; }

        public int DisplayOrder { get; set; } = 1;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        public bool IsDeleted { get; set; } = false;

        // Navigation Properties
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }
}
