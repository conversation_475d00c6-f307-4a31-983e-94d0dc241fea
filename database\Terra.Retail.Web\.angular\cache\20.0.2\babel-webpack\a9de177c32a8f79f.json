{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { environment } from '../../../../environments/environment';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/tooltip\";\nfunction AddSupplierComponent_div_25_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u0623\\u0648\\u0644 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u062A\\u0643\\u0648\\u0646 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_li_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-card\", 61)(2, \"mat-card-content\")(3, \"div\", 62)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"\\u064A\\u0631\\u062C\\u0649 \\u062A\\u0635\\u062D\\u064A\\u062D \\u0627\\u0644\\u0623\\u062E\\u0637\\u0627\\u0621 \\u0627\\u0644\\u062A\\u0627\\u0644\\u064A\\u0629:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ul\", 63);\n    i0.ɵɵtemplate(9, AddSupplierComponent_div_25_li_9_Template, 2, 0, \"li\", 26)(10, AddSupplierComponent_div_25_li_10_Template, 2, 0, \"li\", 26)(11, AddSupplierComponent_div_25_li_11_Template, 2, 0, \"li\", 26)(12, AddSupplierComponent_div_25_li_12_Template, 2, 0, \"li\", 26)(13, AddSupplierComponent_div_25_li_13_Template, 2, 0, \"li\", 26)(14, AddSupplierComponent_div_25_li_14_Template, 2, 0, \"li\", 26)(15, AddSupplierComponent_div_25_li_15_Template, 2, 0, \"li\", 26)(16, AddSupplierComponent_div_25_li_16_Template, 2, 0, \"li\", 26)(17, AddSupplierComponent_div_25_li_17_Template, 2, 0, \"li\", 26);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.supplierForm.get(\"supplierCode\")) == null ? null : tmp_1_0.hasError(\"required\")) && ((tmp_1_0 = ctx_r0.supplierForm.get(\"supplierCode\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.supplierForm.get(\"nameAr\")) == null ? null : tmp_2_0.hasError(\"required\")) && ((tmp_2_0 = ctx_r0.supplierForm.get(\"nameAr\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.supplierForm.get(\"nameAr\")) == null ? null : tmp_3_0.hasError(\"minlength\")) && ((tmp_3_0 = ctx_r0.supplierForm.get(\"nameAr\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.supplierForm.get(\"supplierTypeId\")) == null ? null : tmp_4_0.hasError(\"required\")) && ((tmp_4_0 = ctx_r0.supplierForm.get(\"supplierTypeId\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.supplierForm.get(\"phone1\")) == null ? null : tmp_5_0.hasError(\"required\")) && ((tmp_5_0 = ctx_r0.supplierForm.get(\"phone1\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.supplierForm.get(\"phone1\")) == null ? null : tmp_6_0.hasError(\"pattern\")) && ((tmp_6_0 = ctx_r0.supplierForm.get(\"phone1\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_7_0.hasError(\"required\")) && ((tmp_7_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_8_0.hasError(\"min\")) && ((tmp_8_0 = ctx_r0.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.supplierForm.get(\"email\")) == null ? null : tmp_9_0.hasError(\"email\")) && ((tmp_9_0 = ctx_r0.supplierForm.get(\"email\")) == null ? null : tmp_9_0.touched));\n  }\n}\nfunction AddSupplierComponent_mat_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r2.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.NameAr, \" \");\n  }\n}\nfunction AddSupplierComponent_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 \\u062D\\u0631\\u0641\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0645\\u0637\\u0644\\u0648\\u0628 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u062A\\u0643\\u0648\\u0646 \\u064A\\u0648\\u0645 \\u0648\\u0627\\u062D\\u062F \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0623\\u0642\\u0644 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_error_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddSupplierComponent_mat_option_141_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r3.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", country_r3.NameAr, \" \");\n  }\n}\nfunction AddSupplierComponent_mat_option_154_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", area_r4.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r4.NameAr, \" \");\n  }\n}\nfunction AddSupplierComponent_div_276_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"mat-spinner\", 66);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062D\\u0641\\u0638 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AddSupplierComponent = /*#__PURE__*/(() => {\n  class AddSupplierComponent {\n    fb;\n    router;\n    http;\n    snackBar;\n    // Component State\n    isLoading = false;\n    supplierForm;\n    // API URL\n    apiUrl = `${environment.apiUrl}/simple`;\n    // Data\n    supplierTypes = [];\n    areas = [];\n    countries = [];\n    // Subscriptions\n    subscriptions = [];\n    constructor(fb, router, http, snackBar) {\n      this.fb = fb;\n      this.router = router;\n      this.http = http;\n      this.snackBar = snackBar;\n      this.supplierForm = this.createForm();\n    }\n    ngOnInit() {\n      this.loadInitialData();\n      this.generateSupplierCode(); // توليد الكود عند فتح الصفحة\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    // Arabic names to English mapping for common names\n    arabicNamesMap = {\n      'محمد': 'Mohamed',\n      'أحمد': 'Ahmed',\n      'علي': 'Ali',\n      'حسن': 'Hassan',\n      'حسين': 'Hussein',\n      'عبد الله': 'Abdullah',\n      'عبدالله': 'Abdullah',\n      'عبد الرحمن': 'Abdulrahman',\n      'عبدالرحمن': 'Abdulrahman',\n      'عبد العزيز': 'Abdulaziz',\n      'عبدالعزيز': 'Abdulaziz',\n      'خالد': 'Khaled',\n      'سعد': 'Saad',\n      'فهد': 'Fahd',\n      'عمر': 'Omar',\n      'يوسف': 'Youssef',\n      'إبراهيم': 'Ibrahim',\n      'عثمان': 'Othman',\n      'صالح': 'Saleh',\n      'ناصر': 'Nasser',\n      'الصعيدي': 'Alsaydy',\n      'للادوات': 'Lladwat',\n      'المنزلية': 'Almnzlyh',\n      'للتجارة': 'Lltgara',\n      'والتوزيع': 'Waltwze',\n      'شركة': 'Company',\n      'مؤسسة': 'Foundation',\n      'متجر': 'Store',\n      'محل': 'Shop'\n    };\n    // Arabic to English character mapping for fallback\n    arabicToEnglishMap = {\n      'ا': 'a',\n      'أ': 'a',\n      'إ': 'i',\n      'آ': 'aa',\n      'ب': 'b',\n      'ت': 't',\n      'ث': 'th',\n      'ج': 'g',\n      'ح': 'h',\n      'خ': 'kh',\n      'د': 'd',\n      'ذ': 'th',\n      'ر': 'r',\n      'ز': 'z',\n      'س': 's',\n      'ش': 'sh',\n      'ص': 's',\n      'ض': 'd',\n      'ط': 't',\n      'ظ': 'z',\n      'ع': 'a',\n      'غ': 'gh',\n      'ف': 'f',\n      'ق': 'q',\n      'ك': 'k',\n      'ل': 'l',\n      'م': 'm',\n      'ن': 'n',\n      'ه': 'h',\n      'و': 'w',\n      'ي': 'y',\n      'ى': 'a',\n      'ة': 'h',\n      'ء': 'a',\n      'ئ': 'e',\n      'ؤ': 'o',\n      ' ': ' ',\n      '-': '-',\n      '_': '_'\n    };\n    onArabicNameChange(event) {\n      const arabicText = event.target.value;\n      const englishText = this.translateArabicToEnglish(arabicText);\n      this.supplierForm.patchValue({\n        nameEn: englishText\n      });\n    }\n    translateArabicToEnglish(arabicText) {\n      if (!arabicText) return '';\n      let englishText = arabicText.trim();\n      // First, try to translate using the names mapping\n      const words = englishText.split(/\\s+/);\n      const translatedWords = [];\n      for (let word of words) {\n        const cleanWord = word.trim();\n        if (this.arabicNamesMap[cleanWord]) {\n          translatedWords.push(this.arabicNamesMap[cleanWord]);\n        } else {\n          // Fallback to character-by-character translation\n          let translatedWord = '';\n          for (let char of cleanWord) {\n            translatedWord += this.arabicToEnglishMap[char] || char;\n          }\n          translatedWords.push(translatedWord);\n        }\n      }\n      englishText = translatedWords.join(' ');\n      // Clean up and capitalize\n      englishText = englishText.replace(/\\s+/g, ' ').trim();\n      return englishText.replace(/\\b\\w/g, l => l.toUpperCase());\n    }\n    /**\n     * Create reactive form\n     */\n    createForm() {\n      return this.fb.group({\n        supplierCode: ['', [Validators.required]],\n        // سيتم توليده تلقائياً عند فتح الصفحة\n        nameAr: ['', [Validators.required, Validators.minLength(2)]],\n        nameEn: ['', [Validators.required]],\n        supplierTypeId: [null, [Validators.required]],\n        phone1: ['', [Validators.required, Validators.pattern(/^[+]?[0-9\\s\\-\\(\\)]+$/)]],\n        phone2: [''],\n        email: ['', [Validators.email]],\n        website: [''],\n        address: [''],\n        areaId: [''],\n        countryId: [''],\n        contactPersonName: [''],\n        contactPersonPhone: [''],\n        contactPersonEmail: ['', [Validators.email]],\n        paymentTerms: [30, [Validators.min(0)]],\n        deliveryDays: [7, [Validators.required, Validators.min(1)]],\n        // مدة التوريد بالأيام\n        creditLimit: [0, [Validators.min(0)]],\n        openingBalance: [0],\n        taxNumber: [''],\n        commercialRegister: [''],\n        bankName: [''],\n        bankAccountNumber: [''],\n        iban: [''],\n        rating: [''],\n        notes: [''],\n        isActive: [true]\n      });\n    }\n    /**\n     * Load initial data\n     */\n    loadInitialData() {\n      Promise.all([this.loadSupplierTypes(), this.loadAreas(), this.loadCountries()]).catch(error => {\n        console.error('Error loading initial data:', error);\n        this.showError('خطأ في تحميل البيانات الأولية');\n      });\n    }\n    /**\n     * Load supplier types\n     */\n    loadSupplierTypes() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get(`${this.apiUrl}/supplier-types`).subscribe({\n          next: response => {\n            console.log('Supplier types response:', response);\n            this.supplierTypes = response.supplierTypes || [];\n            console.log('Loaded supplier types:', this.supplierTypes);\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading supplier types:', error);\n            this.showError('خطأ في تحميل أنواع الموردين');\n            reject(error);\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load areas\n     */\n    loadAreas() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get(`${this.apiUrl}/areas-db`).subscribe({\n          next: response => {\n            console.log('Areas response:', response);\n            this.areas = response.areas || [];\n            console.log('Loaded areas:', this.areas);\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading areas:', error);\n            this.showError('خطأ في تحميل المحافظات');\n            reject(error);\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load countries\n     */\n    loadCountries() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get(`${this.apiUrl}/countries`).subscribe({\n          next: response => {\n            console.log('Countries response:', response);\n            this.countries = response.countries || [];\n            console.log('Loaded countries:', this.countries);\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading countries:', error);\n            this.showError('خطأ في تحميل البلدان');\n            reject(error);\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Generate supplier code\n     */\n    generateSupplierCode() {\n      this.isLoading = true;\n      const sub = this.http.get(`${this.apiUrl}/next-supplier-code`).subscribe({\n        next: response => {\n          console.log('Next supplier code response:', response);\n          this.supplierForm.patchValue({\n            supplierCode: response.nextCode\n          });\n          console.log('Set supplier code:', response.nextCode);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error generating supplier code:', error);\n          this.showError('خطأ في توليد كود المورد');\n          this.isLoading = false;\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    /**\n     * Save supplier\n     */\n    saveSupplier() {\n      if (this.supplierForm.valid) {\n        this.isLoading = true;\n        const formValue = this.supplierForm.value;\n        const request = {\n          supplierCode: formValue.supplierCode,\n          nameAr: formValue.nameAr,\n          nameEn: formValue.nameEn || null,\n          supplierTypeId: formValue.supplierTypeId,\n          phone1: formValue.phone1,\n          phone2: formValue.phone2 || null,\n          email: formValue.email || null,\n          website: formValue.website || null,\n          address: formValue.address || null,\n          areaId: formValue.areaId || null,\n          countryId: formValue.countryId || null,\n          contactPersonName: formValue.contactPersonName || null,\n          contactPersonPhone: formValue.contactPersonPhone || null,\n          contactPersonEmail: formValue.contactPersonEmail || null,\n          paymentTerms: formValue.paymentTerms,\n          deliveryDays: formValue.deliveryDays,\n          creditLimit: formValue.creditLimit,\n          openingBalance: formValue.openingBalance,\n          taxNumber: formValue.taxNumber || null,\n          commercialRegister: formValue.commercialRegister || null,\n          bankName: formValue.bankName || null,\n          bankAccountNumber: formValue.bankAccountNumber || null,\n          iban: formValue.iban || null,\n          rating: formValue.rating || null,\n          notes: formValue.notes || null,\n          isActive: formValue.isActive\n        };\n        const sub = this.http.post(`${this.apiUrl}/suppliers`, request).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.showSuccess('تم إضافة المورد بنجاح');\n            this.router.navigate(['/suppliers']);\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error saving supplier:', error);\n            this.showError('خطأ في حفظ بيانات المورد');\n          }\n        });\n        this.subscriptions.push(sub);\n      } else {\n        this.markFormGroupTouched();\n        this.showError('يرجى تصحيح الأخطاء في النموذج');\n      }\n    }\n    /**\n     * Mark all form fields as touched\n     */\n    markFormGroupTouched() {\n      Object.keys(this.supplierForm.controls).forEach(key => {\n        const control = this.supplierForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    /**\n     * Go back to suppliers list\n     */\n    goBack() {\n      this.router.navigate(['/suppliers']);\n    }\n    /**\n     * Cancel and go back\n     */\n    cancel() {\n      this.goBack();\n    }\n    /**\n     * Open supplier types page\n     */\n    openAddSupplierTypeDialog() {\n      this.router.navigate(['/suppliers/types']);\n    }\n    /**\n     * Open areas page\n     */\n    openAddAreaDialog() {\n      this.router.navigate(['/suppliers/areas']);\n    }\n    /**\n     * Open countries page\n     */\n    openAddCountryDialog() {\n      this.router.navigate(['/suppliers/countries']);\n    }\n    /**\n     * Show success message\n     */\n    showSuccess(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 3000,\n        panelClass: ['success-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    /**\n     * Show error message\n     */\n    showError(message) {\n      this.snackBar.open(message, 'إغلاق', {\n        duration: 5000,\n        panelClass: ['error-snackbar'],\n        horizontalPosition: 'center',\n        verticalPosition: 'top'\n      });\n    }\n    static ɵfac = function AddSupplierComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddSupplierComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddSupplierComponent,\n      selectors: [[\"app-add-supplier\"]],\n      decls: 277,\n      vars: 17,\n      consts: [[1, \"add-supplier-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [\"mat-icon-button\", \"\", 1, \"back-btn\", 3, \"click\"], [1, \"header-text\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-stroked-button\", \"\", 1, \"cancel-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-btn\", 3, \"click\", \"disabled\"], [1, \"form-content\"], [1, \"supplier-form\", 3, \"formGroup\"], [\"class\", \"validation-summary\", 4, \"ngIf\"], [1, \"form-card\", \"required-section\"], [1, \"required-title\"], [1, \"form-grid\"], [1, \"required-field\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"supplierCode\", \"placeholder\", \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0624\\u0647 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B\", \"readonly\", \"\"], [\"matSuffix\", \"\"], [1, \"field-with-button\", \"required-field\"], [\"appearance\", \"outline\", 1, \"flex-field\"], [\"formControlName\", \"supplierTypeId\", \"required\", \"\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltip\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0646\\u0648\\u0639 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\", 1, \"add-button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"nameAr\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\", \"required\", \"\", 3, \"input\"], [\"matInput\", \"\", \"formControlName\", \"nameEn\", \"placeholder\", \"Enter supplier name in English\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"phone1\", \"placeholder\", \"+201234567890\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"deliveryDays\", \"type\", \"number\", \"placeholder\", \"7\", \"required\", \"\"], [1, \"form-card\"], [\"matInput\", \"\", \"formControlName\", \"phone2\", \"placeholder\", \"+201234567890\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"formControlName\", \"website\", \"placeholder\", \"www.supplier.com\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\"], [1, \"field-with-button\"], [\"formControlName\", \"countryId\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltip\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062D\\u0627\\u0641\\u0638\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\", 1, \"add-button\", 3, \"click\"], [\"formControlName\", \"areaId\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltip\", \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u0637\\u0642\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\", 1, \"add-button\", 3, \"click\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"rows\", \"2\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A \\u0644\\u0644\\u0645\\u0648\\u0631\\u062F\"], [\"matInput\", \"\", \"formControlName\", \"paymentTerms\", \"type\", \"number\", \"placeholder\", \"30\"], [\"matInput\", \"\", \"formControlName\", \"creditLimit\", \"type\", \"number\", \"placeholder\", \"0\"], [\"matInput\", \"\", \"formControlName\", \"openingBalance\", \"type\", \"number\", \"placeholder\", \"0\"], [\"matInput\", \"\", \"formControlName\", \"taxNumber\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"], [\"matInput\", \"\", \"formControlName\", \"commercialRegister\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\"], [\"matInput\", \"\", \"formControlName\", \"bankName\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643\"], [\"matInput\", \"\", \"formControlName\", \"bankAccountNumber\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"], [\"matInput\", \"\", \"formControlName\", \"iban\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0622\\u064A\\u0628\\u0627\\u0646\"], [\"matInput\", \"\", \"formControlName\", \"rating\", \"type\", \"number\", \"min\", \"1\", \"max\", \"5\", \"placeholder\", \"\\u0645\\u0646 1 \\u0625\\u0644\\u0649 5\"], [1, \"checkbox-field\"], [\"formControlName\", \"isActive\"], [\"matInput\", \"\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0623\\u064A \\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629 \\u0639\\u0646 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"], [1, \"action-buttons\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"save-btn\", 3, \"click\", \"disabled\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"validation-summary\"], [1, \"error-card\"], [1, \"error-header\"], [1, \"error-list\"], [3, \"value\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function AddSupplierComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\", 6);\n          i0.ɵɵtext(9, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 7);\n          i0.ɵɵtext(11, \"\\u0625\\u062F\\u062E\\u0627\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F \\u0644\\u0644\\u0646\\u0638\\u0627\\u0645\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_13_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"span\");\n          i0.ɵɵtext(17, \"\\u0625\\u0644\\u063A\\u0627\\u0621\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_18_listener() {\n            return ctx.saveSupplier();\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(23, \"div\", 11)(24, \"form\", 12);\n          i0.ɵɵtemplate(25, AddSupplierComponent_div_25_Template, 18, 9, \"div\", 13);\n          i0.ɵɵelementStart(26, \"mat-card\", 14)(27, \"mat-card-header\")(28, \"mat-card-title\", 15)(29, \"mat-icon\");\n          i0.ɵɵtext(30, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\");\n          i0.ɵɵtext(32, \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"mat-card-content\")(34, \"div\", 16)(35, \"div\", 17)(36, \"mat-form-field\", 18)(37, \"mat-label\");\n          i0.ɵɵtext(38, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"input\", 19);\n          i0.ɵɵelementStart(40, \"mat-icon\", 20);\n          i0.ɵɵtext(41, \"tag\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 21)(43, \"mat-form-field\", 22)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-select\", 23)(47, \"mat-option\", 24);\n          i0.ɵɵtext(48, \"\\u0627\\u062E\\u062A\\u0631 \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, AddSupplierComponent_mat_option_49_Template, 2, 2, \"mat-option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"mat-icon\", 20);\n          i0.ɵɵtext(51, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, AddSupplierComponent_mat_error_52_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_53_listener() {\n            return ctx.openAddSupplierTypeDialog();\n          });\n          i0.ɵɵelementStart(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 17)(57, \"mat-form-field\", 18)(58, \"mat-label\");\n          i0.ɵɵtext(59, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"input\", 28);\n          i0.ɵɵlistener(\"input\", function AddSupplierComponent_Template_input_input_60_listener($event) {\n            return ctx.onArabicNameChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-icon\", 20);\n          i0.ɵɵtext(62, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(63, AddSupplierComponent_mat_error_63_Template, 2, 0, \"mat-error\", 26)(64, AddSupplierComponent_mat_error_64_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 17)(66, \"mat-form-field\", 18)(67, \"mat-label\");\n          i0.ɵɵtext(68, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"input\", 29);\n          i0.ɵɵelementStart(70, \"mat-icon\", 20);\n          i0.ɵɵtext(71, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(72, AddSupplierComponent_mat_error_72_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 17)(74, \"mat-form-field\", 18)(75, \"mat-label\");\n          i0.ɵɵtext(76, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u0623\\u0648\\u0644 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(77, \"input\", 30);\n          i0.ɵɵelementStart(78, \"mat-icon\", 20);\n          i0.ɵɵtext(79, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(80, AddSupplierComponent_mat_error_80_Template, 2, 0, \"mat-error\", 26)(81, AddSupplierComponent_mat_error_81_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 17)(83, \"mat-form-field\", 18)(84, \"mat-label\");\n          i0.ɵɵtext(85, \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F (\\u0628\\u0627\\u0644\\u0623\\u064A\\u0627\\u0645) *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(86, \"input\", 31);\n          i0.ɵɵelementStart(87, \"mat-icon\", 20);\n          i0.ɵɵtext(88, \"local_shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(89, AddSupplierComponent_mat_error_89_Template, 2, 0, \"mat-error\", 26)(90, AddSupplierComponent_mat_error_90_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(91, \"mat-card\", 32)(92, \"mat-card-header\")(93, \"mat-card-title\")(94, \"mat-icon\");\n          i0.ɵɵtext(95, \"contact_phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"span\");\n          i0.ɵɵtext(97, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644 \\u0648\\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(98, \"mat-card-content\")(99, \"div\", 16)(100, \"mat-form-field\", 18)(101, \"mat-label\");\n          i0.ɵɵtext(102, \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(103, \"input\", 33);\n          i0.ɵɵelementStart(104, \"mat-icon\", 20);\n          i0.ɵɵtext(105, \"phone\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(106, \"mat-form-field\", 18)(107, \"mat-label\");\n          i0.ɵɵtext(108, \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(109, \"input\", 34);\n          i0.ɵɵelementStart(110, \"mat-icon\", 20);\n          i0.ɵɵtext(111, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(112, AddSupplierComponent_mat_error_112_Template, 2, 0, \"mat-error\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"mat-form-field\", 18)(114, \"mat-label\");\n          i0.ɵɵtext(115, \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(116, \"input\", 35);\n          i0.ɵɵelementStart(117, \"mat-icon\", 20);\n          i0.ɵɵtext(118, \"language\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"mat-form-field\", 18)(120, \"mat-label\");\n          i0.ɵɵtext(121, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(122, \"input\", 36);\n          i0.ɵɵelementStart(123, \"mat-icon\", 20);\n          i0.ɵɵtext(124, \"person\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(125, \"mat-card\", 32)(126, \"mat-card-header\")(127, \"mat-card-title\")(128, \"mat-icon\");\n          i0.ɵɵtext(129, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"span\");\n          i0.ɵɵtext(131, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0648\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(132, \"mat-card-content\")(133, \"div\", 16)(134, \"div\", 37)(135, \"mat-form-field\", 22)(136, \"mat-label\");\n          i0.ɵɵtext(137, \"\\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"mat-select\", 38)(139, \"mat-option\", 24);\n          i0.ɵɵtext(140, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0641\\u0638\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(141, AddSupplierComponent_mat_option_141_Template, 2, 2, \"mat-option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"mat-icon\", 20);\n          i0.ɵɵtext(143, \"public\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(144, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_144_listener() {\n            return ctx.openAddCountryDialog();\n          });\n          i0.ɵɵelementStart(145, \"mat-icon\");\n          i0.ɵɵtext(146, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(147, \"div\", 37)(148, \"mat-form-field\", 22)(149, \"mat-label\");\n          i0.ɵɵtext(150, \"\\u0627\\u0644\\u0645\\u0646\\u0637\\u0642\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(151, \"mat-select\", 40)(152, \"mat-option\", 24);\n          i0.ɵɵtext(153, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u0646\\u0637\\u0642\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(154, AddSupplierComponent_mat_option_154_Template, 2, 2, \"mat-option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"mat-icon\", 20);\n          i0.ɵɵtext(156, \"location_city\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(157, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_157_listener() {\n            return ctx.openAddAreaDialog();\n          });\n          i0.ɵɵelementStart(158, \"mat-icon\");\n          i0.ɵɵtext(159, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(160, \"mat-form-field\", 42)(161, \"mat-label\");\n          i0.ɵɵtext(162, \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(163, \"textarea\", 43);\n          i0.ɵɵelementStart(164, \"mat-icon\", 20);\n          i0.ɵɵtext(165, \"home\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(166, \"mat-card\", 32)(167, \"mat-card-header\")(168, \"mat-card-title\")(169, \"mat-icon\");\n          i0.ɵɵtext(170, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"span\");\n          i0.ɵɵtext(172, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(173, \"mat-card-content\")(174, \"div\", 16)(175, \"mat-form-field\", 18)(176, \"mat-label\");\n          i0.ɵɵtext(177, \"\\u0634\\u0631\\u0648\\u0637 \\u0627\\u0644\\u062F\\u0641\\u0639 (\\u0628\\u0627\\u0644\\u0623\\u064A\\u0627\\u0645)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(178, \"input\", 44);\n          i0.ɵɵelementStart(179, \"mat-icon\", 20);\n          i0.ɵɵtext(180, \"schedule\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(181, \"mat-form-field\", 18)(182, \"mat-label\");\n          i0.ɵɵtext(183, \"\\u062D\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(184, \"input\", 45);\n          i0.ɵɵelementStart(185, \"mat-icon\", 20);\n          i0.ɵɵtext(186, \"credit_card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(187, \"mat-form-field\", 18)(188, \"mat-label\");\n          i0.ɵɵtext(189, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u0627\\u0641\\u062A\\u062A\\u0627\\u062D\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(190, \"input\", 46);\n          i0.ɵɵelementStart(191, \"mat-icon\", 20);\n          i0.ɵɵtext(192, \"account_balance_wallet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(193, \"mat-card\", 32)(194, \"mat-card-header\")(195, \"mat-card-title\")(196, \"mat-icon\");\n          i0.ɵɵtext(197, \"gavel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(198, \"span\");\n          i0.ɵɵtext(199, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0642\\u0627\\u0646\\u0648\\u0646\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(200, \"mat-card-content\")(201, \"div\", 16)(202, \"mat-form-field\", 18)(203, \"mat-label\");\n          i0.ɵɵtext(204, \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(205, \"input\", 47);\n          i0.ɵɵelementStart(206, \"mat-icon\", 20);\n          i0.ɵɵtext(207, \"receipt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(208, \"mat-form-field\", 18)(209, \"mat-label\");\n          i0.ɵɵtext(210, \"\\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(211, \"input\", 48);\n          i0.ɵɵelementStart(212, \"mat-icon\", 20);\n          i0.ɵɵtext(213, \"business_center\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(214, \"mat-card\", 32)(215, \"mat-card-header\")(216, \"mat-card-title\")(217, \"mat-icon\");\n          i0.ɵɵtext(218, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(219, \"span\");\n          i0.ɵɵtext(220, \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0641\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(221, \"mat-card-content\")(222, \"div\", 16)(223, \"mat-form-field\", 18)(224, \"mat-label\");\n          i0.ɵɵtext(225, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(226, \"input\", 49);\n          i0.ɵɵelementStart(227, \"mat-icon\", 20);\n          i0.ɵɵtext(228, \"account_balance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(229, \"mat-form-field\", 18)(230, \"mat-label\");\n          i0.ɵɵtext(231, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0641\\u064A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(232, \"input\", 50);\n          i0.ɵɵelementStart(233, \"mat-icon\", 20);\n          i0.ɵɵtext(234, \"credit_card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(235, \"mat-form-field\", 18)(236, \"mat-label\");\n          i0.ɵɵtext(237, \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0622\\u064A\\u0628\\u0627\\u0646 (IBAN)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(238, \"input\", 51);\n          i0.ɵɵelementStart(239, \"mat-icon\", 20);\n          i0.ɵɵtext(240, \"account_balance\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(241, \"mat-card\", 32)(242, \"mat-card-header\")(243, \"mat-card-title\")(244, \"mat-icon\");\n          i0.ɵɵtext(245, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(246, \"span\");\n          i0.ɵɵtext(247, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(248, \"mat-card-content\")(249, \"div\", 16)(250, \"mat-form-field\", 18)(251, \"mat-label\");\n          i0.ɵɵtext(252, \"\\u0627\\u0644\\u062A\\u0642\\u064A\\u064A\\u0645\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(253, \"input\", 52);\n          i0.ɵɵelementStart(254, \"mat-icon\", 20);\n          i0.ɵɵtext(255, \"star\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(256, \"div\", 53)(257, \"mat-checkbox\", 54);\n          i0.ɵɵtext(258, \" \\u0645\\u0648\\u0631\\u062F \\u0646\\u0634\\u0637 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(259, \"mat-form-field\", 42)(260, \"mat-label\");\n          i0.ɵɵtext(261, \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(262, \"textarea\", 55);\n          i0.ɵɵelementStart(263, \"mat-icon\", 20);\n          i0.ɵɵtext(264, \"note\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(265, \"div\", 56)(266, \"button\", 57);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_266_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(267, \"mat-icon\");\n          i0.ɵɵtext(268, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(269, \"span\");\n          i0.ɵɵtext(270, \"\\u0627\\u0644\\u0639\\u0648\\u062F\\u0629\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(271, \"button\", 58);\n          i0.ɵɵlistener(\"click\", function AddSupplierComponent_Template_button_click_271_listener() {\n            return ctx.saveSupplier();\n          });\n          i0.ɵɵelementStart(272, \"mat-icon\");\n          i0.ɵɵtext(273, \"save\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(274, \"span\");\n          i0.ɵɵtext(275, \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(276, AddSupplierComponent_div_276_Template, 4, 0, \"div\", 59);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"disabled\", !ctx.supplierForm.valid || ctx.isLoading);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.supplierForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.supplierForm.valid && ctx.supplierForm.touched);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.supplierTypes);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.supplierForm.get(\"supplierTypeId\")) == null ? null : tmp_4_0.hasError(\"required\")) && ((tmp_4_0 = ctx.supplierForm.get(\"supplierTypeId\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.supplierForm.get(\"nameAr\")) == null ? null : tmp_5_0.hasError(\"required\")) && ((tmp_5_0 = ctx.supplierForm.get(\"nameAr\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.supplierForm.get(\"nameAr\")) == null ? null : tmp_6_0.hasError(\"minlength\")) && ((tmp_6_0 = ctx.supplierForm.get(\"nameAr\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.supplierForm.get(\"nameEn\")) == null ? null : tmp_7_0.hasError(\"required\")) && ((tmp_7_0 = ctx.supplierForm.get(\"nameEn\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.supplierForm.get(\"phone1\")) == null ? null : tmp_8_0.hasError(\"required\")) && ((tmp_8_0 = ctx.supplierForm.get(\"phone1\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.supplierForm.get(\"phone1\")) == null ? null : tmp_9_0.hasError(\"pattern\")) && ((tmp_9_0 = ctx.supplierForm.get(\"phone1\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_10_0.hasError(\"required\")) && ((tmp_10_0 = ctx.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_11_0.hasError(\"min\")) && ((tmp_11_0 = ctx.supplierForm.get(\"deliveryDays\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = ctx.supplierForm.get(\"email\")) == null ? null : tmp_12_0.hasError(\"email\"));\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.areas);\n          i0.ɵɵadvance(117);\n          i0.ɵɵproperty(\"disabled\", !ctx.supplierForm.valid || ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, MatButtonModule, i7.MatButton, i7.MatIconButton, MatIconModule, i8.MatIcon, MatFormFieldModule, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, MatInputModule, i10.MatInput, MatSelectModule, i11.MatSelect, i11.MatOption, MatCheckboxModule, i12.MatCheckbox, MatProgressSpinnerModule, i13.MatProgressSpinner, MatSnackBarModule, MatTooltipModule, i14.MatTooltip],\n      styles: [\"\\n\\n.add-supplier-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n  position: relative;\\n}\\n.add-supplier-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);\\n  color: white;\\n  padding: 40px 40px 60px;\\n  margin: -40px -40px 40px;\\n  border-radius: 0 0 30px 30px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2) !important;\\n  color: white !important;\\n  width: 48px !important;\\n  height: 48px !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  align-items: center;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n  background: var(--success-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: var(--success-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:disabled {\\n  background: var(--gray-400) !important;\\n  color: var(--gray-600) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-color: white !important;\\n}\\n\\n\\n\\n.form-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) 0;\\n}\\n\\n.supplier-form[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-2xl);\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  overflow: hidden !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n  padding: var(--spacing-xl) var(--spacing-2xl) !important;\\n  border-bottom: 1px solid var(--gray-200) !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: var(--spacing-md) !important;\\n  font-size: 1.25rem !important;\\n  font-weight: 700 !important;\\n  color: var(--gray-900) !important;\\n  margin: 0 !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-600) !important;\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n.form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) !important;\\n}\\n\\n\\n\\n.form-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: var(--spacing-2xl);\\n  align-items: start;\\n  padding: var(--spacing-md);\\n}\\n.form-grid[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n\\n\\n\\n.mat-mdc-form-field[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: white !important;\\n  border-radius: var(--radius-lg) !important;\\n  transition: all var(--transition-normal) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-sm) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 3px var(--primary-100) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n  font-weight: 500 !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-input-element[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n}\\n.mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-icon-suffix[_ngcontent-%COMP%] {\\n  color: var(--gray-500) !important;\\n}\\n.mat-mdc-form-field.mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-select-trigger[_ngcontent-%COMP%] {\\n  background: white !important;\\n}\\n.mat-mdc-form-field.mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-select-arrow[_ngcontent-%COMP%] {\\n  color: var(--primary-600) !important;\\n}\\n.mat-mdc-form-field.mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%] {\\n  font-weight: 500 !important;\\n}\\n\\n\\n\\n.checkbox-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 56px;\\n  padding: var(--spacing-md);\\n  border: 2px solid var(--gray-200);\\n  border-radius: var(--radius-lg);\\n  background: white;\\n  transition: all var(--transition-normal);\\n}\\n.checkbox-field[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n.checkbox-field[_ngcontent-%COMP%]   .mat-mdc-checkbox[_ngcontent-%COMP%]   .mdc-checkbox[_ngcontent-%COMP%]   .mdc-checkbox__native-control[_ngcontent-%COMP%]:enabled:checked    ~ .mdc-checkbox__background[_ngcontent-%COMP%] {\\n  background-color: var(--primary-500) !important;\\n  border-color: var(--primary-500) !important;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   .mat-mdc-checkbox[_ngcontent-%COMP%]   .mdc-form-field[_ngcontent-%COMP%] {\\n  font-family: var(--font-family-primary) !important;\\n  font-weight: 500 !important;\\n  color: var(--gray-800) !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n  font-size: 1.125rem;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   .mat-mdc-progress-spinner[_ngcontent-%COMP%] {\\n  --mdc-circular-progress-active-indicator-color: var(--primary-500);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .form-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) 0;\\n  }\\n  .supplier-form[_ngcontent-%COMP%] {\\n    gap: var(--spacing-xl);\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .mat-mdc-card-title[_ngcontent-%COMP%] {\\n    font-size: 1.125rem !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) var(--spacing-lg) !important;\\n  }\\n  .form-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) !important;\\n  }\\n  .form-grid[_ngcontent-%COMP%] {\\n    gap: var(--spacing-md);\\n  }\\n}\\n\\n\\n.field-with-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  width: 100%;\\n}\\n.field-with-button[_ngcontent-%COMP%]   .flex-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.field-with-button[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  width: 48px !important;\\n  height: 48px !important;\\n  color: white !important;\\n  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;\\n  border: 2px solid #2196f3 !important;\\n  border-radius: 8px !important;\\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important;\\n}\\n.field-with-button[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;\\n  border-color: #1976d2 !important;\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4) !important;\\n}\\n.field-with-button[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) !important;\\n}\\n.field-with-button[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px !important;\\n  width: 24px !important;\\n  height: 24px !important;\\n  font-weight: bold !important;\\n}\\n\\n\\n\\n.required-section[_ngcontent-%COMP%] {\\n  border: 3px solid #f44336 !important;\\n  border-radius: 12px !important;\\n  background: rgba(244, 67, 54, 0.02) !important;\\n  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.1) !important;\\n}\\n.required-section[_ngcontent-%COMP%]   .required-title[_ngcontent-%COMP%] {\\n  color: #d32f2f !important;\\n  font-weight: 700 !important;\\n  font-size: 1.3rem !important;\\n}\\n.required-section[_ngcontent-%COMP%]   .required-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f !important;\\n  font-size: 1.6rem !important;\\n  width: 1.6rem !important;\\n  height: 1.6rem !important;\\n}\\n.required-section[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background: rgba(244, 67, 54, 0.05) !important;\\n  border-bottom: 2px solid rgba(244, 67, 54, 0.2) !important;\\n}\\n\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  color: #d32f2f !important;\\n  font-weight: 600 !important;\\n}\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__leading[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__notch[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__trailing[_ngcontent-%COMP%] {\\n  border-color: #f44336 !important;\\n  border-width: 2px !important;\\n}\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__leading[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__notch[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]:hover   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__trailing[_ngcontent-%COMP%] {\\n  border-color: #d32f2f !important;\\n}\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__leading[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__notch[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper.mdc-text-field--focused[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__trailing[_ngcontent-%COMP%] {\\n  border-color: #d32f2f !important;\\n  border-width: 2px !important;\\n}\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-required-marker[_ngcontent-%COMP%] {\\n  color: #d32f2f !important;\\n  font-weight: bold !important;\\n}\\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__leading[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__notch[_ngcontent-%COMP%], \\n.required-field[_ngcontent-%COMP%]   .mat-mdc-form-field-type-mat-select[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]   .mdc-notched-outline[_ngcontent-%COMP%]   .mdc-notched-outline__trailing[_ngcontent-%COMP%] {\\n  border-color: #f44336 !important;\\n  border-width: 2px !important;\\n}\\n\\n\\n\\n.validation-summary[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  border: 2px solid #f44336 !important;\\n  border-radius: 8px;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n  color: #d32f2f !important;\\n  font-weight: 600;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f !important;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-list[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-right: 1.5rem;\\n  color: #d32f2f !important;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.validation-summary[_ngcontent-%COMP%]   .error-card[_ngcontent-%COMP%]   .error-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: var(--spacing-xl);\\n  padding: var(--spacing-2xl) 0;\\n  margin-top: var(--spacing-xl);\\n  border-top: 2px solid var(--gray-200);\\n}\\n.action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%) !important;\\n  color: white !important;\\n  padding: var(--spacing-lg) var(--spacing-2xl) !important;\\n  font-weight: 700 !important;\\n  font-size: 1.1rem !important;\\n  border-radius: 8px !important;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;\\n  border: 2px solid #4caf50 !important;\\n  min-width: 160px !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:disabled {\\n  background: var(--gray-400) !important;\\n  color: var(--gray-600) !important;\\n  border-color: var(--gray-400) !important;\\n  box-shadow: none !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  background: white !important;\\n  color: #f44336 !important;\\n  border: 2px solid #f44336 !important;\\n  padding: var(--spacing-lg) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  font-size: 1.1rem !important;\\n  border-radius: 8px !important;\\n  min-width: 120px !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(244, 67, 54, 0.05) !important;\\n  border-color: #d32f2f !important;\\n  color: #d32f2f !important;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: var(--spacing-md);\\n  }\\n  .action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], \\n   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 300px;\\n  }\\n}\\n\\n\\n  .mat-mdc-select-panel {\\n  background: white !important;\\n  border-radius: 8px !important;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  max-height: 300px !important;\\n}\\n  .mat-mdc-select-panel .mat-mdc-option {\\n  font-family: var(--font-family-primary) !important;\\n  font-weight: 500 !important;\\n  padding: 12px 16px !important;\\n  border-bottom: 1px solid var(--gray-100) !important;\\n}\\n  .mat-mdc-select-panel .mat-mdc-option:last-child {\\n  border-bottom: none !important;\\n}\\n  .mat-mdc-select-panel .mat-mdc-option:hover {\\n  background: var(--primary-50) !important;\\n  color: var(--primary-700) !important;\\n}\\n  .mat-mdc-select-panel .mat-mdc-option.mat-mdc-option-active {\\n  background: var(--primary-100) !important;\\n  color: var(--primary-800) !important;\\n}\\n  .mat-mdc-select-panel .mat-mdc-option.mdc-list-item--selected {\\n  background: var(--primary-500) !important;\\n  color: white !important;\\n  font-weight: 600 !important;\\n}\\n\\n\\n\\n  .success-snackbar {\\n  background: #4caf50 !important;\\n  color: white !important;\\n  font-weight: 600 !important;\\n}\\n  .success-snackbar .mat-mdc-snack-bar-action {\\n  color: white !important;\\n}\\n\\n  .error-snackbar {\\n  background: #f44336 !important;\\n  color: white !important;\\n  font-weight: 600 !important;\\n}\\n  .error-snackbar .mat-mdc-snack-bar-action {\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AddSupplierComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "environment", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTooltipModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddSupplierComponent_div_25_li_9_Template", "AddSupplierComponent_div_25_li_10_Template", "AddSupplierComponent_div_25_li_11_Template", "AddSupplierComponent_div_25_li_12_Template", "AddSupplierComponent_div_25_li_13_Template", "AddSupplierComponent_div_25_li_14_Template", "AddSupplierComponent_div_25_li_15_Template", "AddSupplierComponent_div_25_li_16_Template", "AddSupplierComponent_div_25_li_17_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "ctx_r0", "supplierForm", "get", "<PERSON><PERSON><PERSON><PERSON>", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "type_r2", "Id", "ɵɵtextInterpolate1", "NameAr", "country_r3", "area_r4", "ɵɵelement", "AddSupplierComponent", "fb", "router", "http", "snackBar", "isLoading", "apiUrl", "supplierTypes", "areas", "countries", "subscriptions", "constructor", "createForm", "ngOnInit", "loadInitialData", "generateSupplierCode", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "arabicNamesMap", "arabicToEnglishMap", "onArabicNameChange", "event", "arabicText", "target", "value", "englishText", "translateArabicToEnglish", "patchValue", "nameEn", "trim", "words", "split", "<PERSON><PERSON><PERSON><PERSON>", "word", "cleanWord", "push", "translated<PERSON><PERSON>", "char", "join", "replace", "l", "toUpperCase", "group", "supplierCode", "required", "nameAr", "<PERSON><PERSON><PERSON><PERSON>", "supplierTypeId", "phone1", "pattern", "phone2", "email", "website", "address", "areaId", "countryId", "contactPersonName", "contactPersonPhone", "contactPersonEmail", "paymentTerms", "min", "deliveryDays", "creditLimit", "openingBalance", "taxNumber", "commercialRegister", "bankName", "bankAccountNumber", "iban", "rating", "notes", "isActive", "Promise", "all", "loadSupplierTypes", "loadAreas", "loadCountries", "catch", "error", "console", "showError", "resolve", "reject", "subscribe", "next", "response", "log", "nextCode", "saveSupplier", "valid", "formValue", "request", "post", "showSuccess", "navigate", "markFormGroupTouched", "Object", "keys", "controls", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "goBack", "cancel", "openAddSupplierTypeDialog", "openAddAreaDialog", "openAddCountryDialog", "message", "open", "duration", "panelClass", "horizontalPosition", "verticalPosition", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "HttpClient", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "AddSupplierComponent_Template", "rf", "ctx", "ɵɵlistener", "AddSupplierComponent_Template_button_click_4_listener", "AddSupplierComponent_Template_button_click_13_listener", "AddSupplierComponent_Template_button_click_18_listener", "AddSupplierComponent_div_25_Template", "AddSupplierComponent_mat_option_49_Template", "AddSupplierComponent_mat_error_52_Template", "AddSupplierComponent_Template_button_click_53_listener", "AddSupplierComponent_Template_input_input_60_listener", "$event", "AddSupplierComponent_mat_error_63_Template", "AddSupplierComponent_mat_error_64_Template", "AddSupplierComponent_mat_error_72_Template", "AddSupplierComponent_mat_error_80_Template", "AddSupplierComponent_mat_error_81_Template", "AddSupplierComponent_mat_error_89_Template", "AddSupplierComponent_mat_error_90_Template", "AddSupplierComponent_mat_error_112_Template", "AddSupplierComponent_mat_option_141_Template", "AddSupplierComponent_Template_button_click_144_listener", "AddSupplierComponent_mat_option_154_Template", "AddSupplierComponent_Template_button_click_157_listener", "AddSupplierComponent_Template_button_click_266_listener", "AddSupplierComponent_Template_button_click_271_listener", "AddSupplierComponent_div_276_Template", "tmp_10_0", "tmp_11_0", "tmp_12_0", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "MaxValidator", "FormGroupDirective", "FormControlName", "i6", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i7", "MatButton", "MatIconButton", "i8", "MatIcon", "i9", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i10", "MatInput", "i11", "MatSelect", "MatOption", "i12", "MatCheckbox", "i13", "MatProgressSpinner", "i14", "MatTooltip", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\add-supplier\\add-supplier.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\add-supplier\\add-supplier.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from '../../../../environments/environment';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Interfaces\ninterface SupplierType {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n}\n\ninterface Area {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n  Code: string;\n}\n\ninterface Country {\n  Id: number;\n  NameAr: string;\n  NameEn: string;\n  Code: string;\n  PhoneCode: string;\n}\n\ninterface CreateSupplierRequest {\n  supplierCode: string;\n  nameAr: string;\n  nameEn?: string;\n  supplierTypeId: number;\n  phone1: string;\n  phone2?: string;\n  email?: string;\n  website?: string;\n  address?: string;\n  areaId?: number;\n  countryId?: number;\n  contactPersonName?: string;\n  contactPersonPhone?: string;\n  contactPersonEmail?: string;\n  paymentTerms: number;\n  deliveryDays: number;\n  creditLimit: number;\n  openingBalance: number;\n  taxNumber?: string;\n  commercialRegister?: string;\n  bankName?: string;\n  bankAccountNumber?: string;\n  iban?: string;\n  rating?: number;\n  notes?: string;\n  isActive: boolean;\n}\n\n@Component({\n  selector: 'app-add-supplier',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatTooltipModule\n  ],\n  templateUrl: './add-supplier.component.html',\n  styleUrls: ['./add-supplier.component.scss']\n})\nexport class AddSupplierComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = false;\n  supplierForm: FormGroup;\n\n  // API URL\n  private apiUrl = `${environment.apiUrl}/simple`;\n\n  // Data\n  supplierTypes: SupplierType[] = [];\n  areas: Area[] = [];\n  countries: Country[] = [];\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private fb: FormBuilder,\n    private router: Router,\n    private http: HttpClient,\n    private snackBar: MatSnackBar\n  ) {\n    this.supplierForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    this.loadInitialData();\n    this.generateSupplierCode(); // توليد الكود عند فتح الصفحة\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  // Arabic names to English mapping for common names\n  private arabicNamesMap: { [key: string]: string } = {\n    'محمد': 'Mohamed',\n    'أحمد': 'Ahmed',\n    'علي': 'Ali',\n    'حسن': 'Hassan',\n    'حسين': 'Hussein',\n    'عبد الله': 'Abdullah',\n    'عبدالله': 'Abdullah',\n    'عبد الرحمن': 'Abdulrahman',\n    'عبدالرحمن': 'Abdulrahman',\n    'عبد العزيز': 'Abdulaziz',\n    'عبدالعزيز': 'Abdulaziz',\n    'خالد': 'Khaled',\n    'سعد': 'Saad',\n    'فهد': 'Fahd',\n    'عمر': 'Omar',\n    'يوسف': 'Youssef',\n    'إبراهيم': 'Ibrahim',\n    'عثمان': 'Othman',\n    'صالح': 'Saleh',\n    'ناصر': 'Nasser',\n    'الصعيدي': 'Alsaydy',\n    'للادوات': 'Lladwat',\n    'المنزلية': 'Almnzlyh',\n    'للتجارة': 'Lltgara',\n    'والتوزيع': 'Waltwze',\n    'شركة': 'Company',\n    'مؤسسة': 'Foundation',\n    'متجر': 'Store',\n    'محل': 'Shop'\n  };\n\n  // Arabic to English character mapping for fallback\n  private arabicToEnglishMap: { [key: string]: string } = {\n    'ا': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'aa',\n    'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'g',\n    'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'th',\n    'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh',\n    'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z',\n    'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',\n    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',\n    'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a',\n    'ة': 'h', 'ء': 'a', 'ئ': 'e', 'ؤ': 'o',\n    ' ': ' ', '-': '-', '_': '_'\n  };\n\n  onArabicNameChange(event: any): void {\n    const arabicText = event.target.value;\n    const englishText = this.translateArabicToEnglish(arabicText);\n    this.supplierForm.patchValue({ nameEn: englishText });\n  }\n\n  private translateArabicToEnglish(arabicText: string): string {\n    if (!arabicText) return '';\n\n    let englishText = arabicText.trim();\n\n    // First, try to translate using the names mapping\n    const words = englishText.split(/\\s+/);\n    const translatedWords: string[] = [];\n\n    for (let word of words) {\n      const cleanWord = word.trim();\n      if (this.arabicNamesMap[cleanWord]) {\n        translatedWords.push(this.arabicNamesMap[cleanWord]);\n      } else {\n        // Fallback to character-by-character translation\n        let translatedWord = '';\n        for (let char of cleanWord) {\n          translatedWord += this.arabicToEnglishMap[char] || char;\n        }\n        translatedWords.push(translatedWord);\n      }\n    }\n\n    englishText = translatedWords.join(' ');\n\n    // Clean up and capitalize\n    englishText = englishText.replace(/\\s+/g, ' ').trim();\n    return englishText.replace(/\\b\\w/g, l => l.toUpperCase());\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      supplierCode: ['', [Validators.required]], // سيتم توليده تلقائياً عند فتح الصفحة\n      nameAr: ['', [Validators.required, Validators.minLength(2)]],\n      nameEn: ['', [Validators.required]],\n      supplierTypeId: [null, [Validators.required]],\n      phone1: ['', [Validators.required, Validators.pattern(/^[+]?[0-9\\s\\-\\(\\)]+$/)]],\n      phone2: [''],\n      email: ['', [Validators.email]],\n      website: [''],\n      address: [''],\n      areaId: [''],\n      countryId: [''],\n      contactPersonName: [''],\n      contactPersonPhone: [''],\n      contactPersonEmail: ['', [Validators.email]],\n      paymentTerms: [30, [Validators.min(0)]],\n      deliveryDays: [7, [Validators.required, Validators.min(1)]], // مدة التوريد بالأيام\n      creditLimit: [0, [Validators.min(0)]],\n      openingBalance: [0],\n      taxNumber: [''],\n      commercialRegister: [''],\n      bankName: [''],\n      bankAccountNumber: [''],\n      iban: [''],\n      rating: [''],\n      notes: [''],\n      isActive: [true]\n    });\n  }\n\n  /**\n   * Load initial data\n   */\n  private loadInitialData(): void {\n    Promise.all([\n      this.loadSupplierTypes(),\n      this.loadAreas(),\n      this.loadCountries()\n    ]).catch(error => {\n      console.error('Error loading initial data:', error);\n      this.showError('خطأ في تحميل البيانات الأولية');\n    });\n  }\n\n  /**\n   * Load supplier types\n   */\n  private loadSupplierTypes(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>(`${this.apiUrl}/supplier-types`).subscribe({\n        next: (response) => {\n          console.log('Supplier types response:', response);\n          this.supplierTypes = response.supplierTypes || [];\n          console.log('Loaded supplier types:', this.supplierTypes);\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading supplier types:', error);\n          this.showError('خطأ في تحميل أنواع الموردين');\n          reject(error);\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load areas\n   */\n  private loadAreas(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>(`${this.apiUrl}/areas-db`).subscribe({\n        next: (response) => {\n          console.log('Areas response:', response);\n          this.areas = response.areas || [];\n          console.log('Loaded areas:', this.areas);\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading areas:', error);\n          this.showError('خطأ في تحميل المحافظات');\n          reject(error);\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load countries\n   */\n  private loadCountries(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>(`${this.apiUrl}/countries`).subscribe({\n        next: (response) => {\n          console.log('Countries response:', response);\n          this.countries = response.countries || [];\n          console.log('Loaded countries:', this.countries);\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading countries:', error);\n          this.showError('خطأ في تحميل البلدان');\n          reject(error);\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Generate supplier code\n   */\n  generateSupplierCode(): void {\n    this.isLoading = true;\n    const sub = this.http.get<any>(`${this.apiUrl}/next-supplier-code`).subscribe({\n      next: (response) => {\n        console.log('Next supplier code response:', response);\n        this.supplierForm.patchValue({\n          supplierCode: response.nextCode\n        });\n        console.log('Set supplier code:', response.nextCode);\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('Error generating supplier code:', error);\n        this.showError('خطأ في توليد كود المورد');\n        this.isLoading = false;\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n\n  /**\n   * Save supplier\n   */\n  saveSupplier(): void {\n    if (this.supplierForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.supplierForm.value;\n      const request: CreateSupplierRequest = {\n        supplierCode: formValue.supplierCode,\n        nameAr: formValue.nameAr,\n        nameEn: formValue.nameEn || null,\n        supplierTypeId: formValue.supplierTypeId,\n        phone1: formValue.phone1,\n        phone2: formValue.phone2 || null,\n        email: formValue.email || null,\n        website: formValue.website || null,\n        address: formValue.address || null,\n        areaId: formValue.areaId || null,\n        countryId: formValue.countryId || null,\n        contactPersonName: formValue.contactPersonName || null,\n        contactPersonPhone: formValue.contactPersonPhone || null,\n        contactPersonEmail: formValue.contactPersonEmail || null,\n        paymentTerms: formValue.paymentTerms,\n        deliveryDays: formValue.deliveryDays,\n        creditLimit: formValue.creditLimit,\n        openingBalance: formValue.openingBalance,\n        taxNumber: formValue.taxNumber || null,\n        commercialRegister: formValue.commercialRegister || null,\n        bankName: formValue.bankName || null,\n        bankAccountNumber: formValue.bankAccountNumber || null,\n        iban: formValue.iban || null,\n        rating: formValue.rating || null,\n        notes: formValue.notes || null,\n        isActive: formValue.isActive\n      };\n\n      const sub = this.http.post<any>(`${this.apiUrl}/suppliers`, request).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          this.showSuccess('تم إضافة المورد بنجاح');\n          this.router.navigate(['/suppliers']);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error saving supplier:', error);\n          this.showError('خطأ في حفظ بيانات المورد');\n        }\n      });\n      this.subscriptions.push(sub);\n    } else {\n      this.markFormGroupTouched();\n      this.showError('يرجى تصحيح الأخطاء في النموذج');\n    }\n  }\n\n  /**\n   * Mark all form fields as touched\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.supplierForm.controls).forEach(key => {\n      const control = this.supplierForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Go back to suppliers list\n   */\n  goBack(): void {\n    this.router.navigate(['/suppliers']);\n  }\n\n  /**\n   * Cancel and go back\n   */\n  cancel(): void {\n    this.goBack();\n  }\n\n  /**\n   * Open supplier types page\n   */\n  openAddSupplierTypeDialog(): void {\n    this.router.navigate(['/suppliers/types']);\n  }\n\n  /**\n   * Open areas page\n   */\n  openAddAreaDialog(): void {\n    this.router.navigate(['/suppliers/areas']);\n  }\n\n  /**\n   * Open countries page\n   */\n  openAddCountryDialog(): void {\n    this.router.navigate(['/suppliers/countries']);\n  }\n\n  /**\n   * Show success message\n   */\n  private showSuccess(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 3000,\n      panelClass: ['success-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Show error message\n   */\n  private showError(message: string): void {\n    this.snackBar.open(message, 'إغلاق', {\n      duration: 5000,\n      panelClass: ['error-snackbar'],\n      horizontalPosition: 'center',\n      verticalPosition: 'top'\n    });\n  }\n\n\n}\n", "<!-- Terra Retail ERP - Add Supplier Form -->\n<div class=\"add-supplier-container\">\n\n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <button mat-icon-button class=\"back-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n        </button>\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">إضافة مورد جديد</h1>\n          <p class=\"page-subtitle\">إدخال بيانات مورد جديد للنظام</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-stroked-button class=\"cancel-btn\" (click)=\"cancel()\">\n          <mat-icon>close</mat-icon>\n          <span>إلغاء</span>\n        </button>\n        <button mat-raised-button color=\"primary\" class=\"save-btn\"\n                [disabled]=\"!supplierForm.valid || isLoading\"\n                (click)=\"saveSupplier()\">\n          <mat-icon>save</mat-icon>\n          <span>حفظ المورد</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Form Content -->\n  <div class=\"form-content\">\n    <form [formGroup]=\"supplierForm\" class=\"supplier-form\">\n\n      <!-- Validation Summary -->\n      <div class=\"validation-summary\" *ngIf=\"!supplierForm.valid && supplierForm.touched\">\n        <mat-card class=\"error-card\">\n          <mat-card-content>\n            <div class=\"error-header\">\n              <mat-icon>error</mat-icon>\n              <span>يرجى تصحيح الأخطاء التالية:</span>\n            </div>\n            <ul class=\"error-list\">\n              <li *ngIf=\"supplierForm.get('supplierCode')?.hasError('required') && supplierForm.get('supplierCode')?.touched\">\n                كود المورد مطلوب\n              </li>\n              <li *ngIf=\"supplierForm.get('nameAr')?.hasError('required') && supplierForm.get('nameAr')?.touched\">\n                اسم المورد بالعربية مطلوب\n              </li>\n              <li *ngIf=\"supplierForm.get('nameAr')?.hasError('minlength') && supplierForm.get('nameAr')?.touched\">\n                اسم المورد يجب أن يكون حرفين على الأقل\n              </li>\n              <li *ngIf=\"supplierForm.get('supplierTypeId')?.hasError('required') && supplierForm.get('supplierTypeId')?.touched\">\n                نوع المورد مطلوب\n              </li>\n              <li *ngIf=\"supplierForm.get('phone1')?.hasError('required') && supplierForm.get('phone1')?.touched\">\n                الهاتف الأول مطلوب\n              </li>\n              <li *ngIf=\"supplierForm.get('phone1')?.hasError('pattern') && supplierForm.get('phone1')?.touched\">\n                رقم الهاتف غير صحيح\n              </li>\n              <li *ngIf=\"supplierForm.get('deliveryDays')?.hasError('required') && supplierForm.get('deliveryDays')?.touched\">\n                مدة التوريد مطلوبة\n              </li>\n              <li *ngIf=\"supplierForm.get('deliveryDays')?.hasError('min') && supplierForm.get('deliveryDays')?.touched\">\n                مدة التوريد يجب أن تكون يوم واحد على الأقل\n              </li>\n              <li *ngIf=\"supplierForm.get('email')?.hasError('email') && supplierForm.get('email')?.touched\">\n                البريد الإلكتروني غير صحيح\n              </li>\n            </ul>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- البيانات الأساسية المطلوبة -->\n      <mat-card class=\"form-card required-section\">\n        <mat-card-header>\n          <mat-card-title class=\"required-title\">\n            <mat-icon>business</mat-icon>\n            <span>البيانات الأساسية المطلوبة</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Supplier Code -->\n            <div class=\"required-field\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>كود المورد *</mat-label>\n                <input matInput formControlName=\"supplierCode\" placeholder=\"سيتم إنشاؤه تلقائياً\" readonly>\n                <mat-icon matSuffix>tag</mat-icon>\n              </mat-form-field>\n            </div>\n\n            <!-- Supplier Type -->\n            <div class=\"field-with-button required-field\">\n              <mat-form-field appearance=\"outline\" class=\"flex-field\">\n                <mat-label>نوع المورد *</mat-label>\n                <mat-select formControlName=\"supplierTypeId\" required>\n                  <mat-option value=\"\">اختر نوع المورد</mat-option>\n                  <mat-option *ngFor=\"let type of supplierTypes\" [value]=\"type.Id\">\n                    {{ type.NameAr }}\n                  </mat-option>\n                </mat-select>\n                <mat-icon matSuffix>category</mat-icon>\n                <mat-error *ngIf=\"supplierForm.get('supplierTypeId')?.hasError('required') && supplierForm.get('supplierTypeId')?.touched\">\n                  نوع المورد مطلوب\n                </mat-error>\n              </mat-form-field>\n              <button mat-icon-button type=\"button\" class=\"add-button\"\n                      matTooltip=\"إضافة نوع مورد جديد\"\n                      (click)=\"openAddSupplierTypeDialog()\">\n                <mat-icon>add</mat-icon>\n              </button>\n            </div>\n\n            <!-- Arabic Name -->\n            <div class=\"required-field\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>اسم المورد بالعربية *</mat-label>\n                <input matInput formControlName=\"nameAr\" placeholder=\"أدخل اسم المورد بالعربية\" required\n                       (input)=\"onArabicNameChange($event)\">\n                <mat-icon matSuffix>business</mat-icon>\n                <mat-error *ngIf=\"supplierForm.get('nameAr')?.hasError('required') && supplierForm.get('nameAr')?.touched\">\n                  اسم المورد مطلوب\n                </mat-error>\n                <mat-error *ngIf=\"supplierForm.get('nameAr')?.hasError('minlength') && supplierForm.get('nameAr')?.touched\">\n                  اسم المورد يجب أن يكون حرفين على الأقل\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- English Name -->\n            <div class=\"required-field\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>اسم المورد بالإنجليزية *</mat-label>\n                <input matInput formControlName=\"nameEn\" placeholder=\"Enter supplier name in English\" required>\n                <mat-icon matSuffix>business</mat-icon>\n                <mat-error *ngIf=\"supplierForm.get('nameEn')?.hasError('required') && supplierForm.get('nameEn')?.touched\">\n                  اسم المورد بالإنجليزية مطلوب\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Phone 1 -->\n            <div class=\"required-field\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>الهاتف الأول *</mat-label>\n                <input matInput formControlName=\"phone1\" placeholder=\"+201234567890\" required>\n                <mat-icon matSuffix>phone</mat-icon>\n                <mat-error *ngIf=\"supplierForm.get('phone1')?.hasError('required') && supplierForm.get('phone1')?.touched\">\n                  رقم الهاتف مطلوب\n                </mat-error>\n                <mat-error *ngIf=\"supplierForm.get('phone1')?.hasError('pattern') && supplierForm.get('phone1')?.touched\">\n                  رقم الهاتف غير صحيح\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <!-- Delivery Days -->\n            <div class=\"required-field\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>مدة التوريد (بالأيام) *</mat-label>\n                <input matInput formControlName=\"deliveryDays\" type=\"number\" placeholder=\"7\" required>\n                <mat-icon matSuffix>local_shipping</mat-icon>\n                <mat-error *ngIf=\"supplierForm.get('deliveryDays')?.hasError('required') && supplierForm.get('deliveryDays')?.touched\">\n                  مدة التوريد مطلوبة\n                </mat-error>\n                <mat-error *ngIf=\"supplierForm.get('deliveryDays')?.hasError('min') && supplierForm.get('deliveryDays')?.touched\">\n                  مدة التوريد يجب أن تكون يوم واحد على الأقل\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- معلومات الاتصال والتواصل -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>contact_phone</mat-icon>\n            <span>معلومات الاتصال والتواصل</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Phone 2 -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الهاتف الثاني</mat-label>\n              <input matInput formControlName=\"phone2\" placeholder=\"+201234567890\">\n              <mat-icon matSuffix>phone</mat-icon>\n            </mat-form-field>\n\n            <!-- Email -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>البريد الإلكتروني</mat-label>\n              <input matInput formControlName=\"email\" type=\"email\" placeholder=\"<EMAIL>\">\n              <mat-icon matSuffix>email</mat-icon>\n              <mat-error *ngIf=\"supplierForm.get('email')?.hasError('email')\">\n                البريد الإلكتروني غير صحيح\n              </mat-error>\n            </mat-form-field>\n\n            <!-- Website -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الموقع الإلكتروني</mat-label>\n              <input matInput formControlName=\"website\" placeholder=\"www.supplier.com\">\n              <mat-icon matSuffix>language</mat-icon>\n            </mat-form-field>\n\n            <!-- Contact Person Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم الشخص المسؤول</mat-label>\n              <input matInput formControlName=\"contactPersonName\" placeholder=\"أدخل اسم الشخص المسؤول\">\n              <mat-icon matSuffix>person</mat-icon>\n            </mat-form-field>\n\n\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- معلومات الموقع والعنوان -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>location_on</mat-icon>\n            <span>معلومات الموقع والعنوان</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Country -->\n            <div class=\"field-with-button\">\n              <mat-form-field appearance=\"outline\" class=\"flex-field\">\n                <mat-label>المحافظة</mat-label>\n                <mat-select formControlName=\"countryId\">\n                  <mat-option value=\"\">اختر المحافظة</mat-option>\n                  <mat-option *ngFor=\"let country of countries\" [value]=\"country.Id\">\n                    {{ country.NameAr }}\n                  </mat-option>\n                </mat-select>\n                <mat-icon matSuffix>public</mat-icon>\n              </mat-form-field>\n              <button mat-icon-button type=\"button\" class=\"add-button\"\n                      matTooltip=\"إضافة محافظة جديدة\"\n                      (click)=\"openAddCountryDialog()\">\n                <mat-icon>add</mat-icon>\n              </button>\n            </div>\n\n            <!-- Area -->\n            <div class=\"field-with-button\">\n              <mat-form-field appearance=\"outline\" class=\"flex-field\">\n                <mat-label>المنطقة</mat-label>\n                <mat-select formControlName=\"areaId\">\n                  <mat-option value=\"\">اختر المنطقة</mat-option>\n                  <mat-option *ngFor=\"let area of areas\" [value]=\"area.Id\">\n                    {{ area.NameAr }}\n                  </mat-option>\n                </mat-select>\n                <mat-icon matSuffix>location_city</mat-icon>\n              </mat-form-field>\n              <button mat-icon-button type=\"button\" class=\"add-button\"\n                      matTooltip=\"إضافة منطقة جديدة\"\n                      (click)=\"openAddAreaDialog()\">\n                <mat-icon>add</mat-icon>\n              </button>\n            </div>\n\n            <!-- Address -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>العنوان التفصيلي</mat-label>\n              <textarea matInput formControlName=\"address\" rows=\"2\"\n                        placeholder=\"أدخل العنوان التفصيلي للمورد\"></textarea>\n              <mat-icon matSuffix>home</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- المعلومات المالية -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>account_balance</mat-icon>\n            <span>المعلومات المالية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Payment Terms -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>شروط الدفع (بالأيام)</mat-label>\n              <input matInput formControlName=\"paymentTerms\" type=\"number\" placeholder=\"30\">\n              <mat-icon matSuffix>schedule</mat-icon>\n            </mat-form-field>\n\n            <!-- Credit Limit -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>حد الائتمان</mat-label>\n              <input matInput formControlName=\"creditLimit\" type=\"number\" placeholder=\"0\">\n              <mat-icon matSuffix>credit_card</mat-icon>\n            </mat-form-field>\n\n            <!-- Opening Balance -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الرصيد الافتتاحي</mat-label>\n              <input matInput formControlName=\"openingBalance\" type=\"number\" placeholder=\"0\">\n              <mat-icon matSuffix>account_balance_wallet</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- المعلومات القانونية -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>gavel</mat-icon>\n            <span>المعلومات القانونية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Tax Number -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>الرقم الضريبي</mat-label>\n              <input matInput formControlName=\"taxNumber\" placeholder=\"أدخل الرقم الضريبي\">\n              <mat-icon matSuffix>receipt</mat-icon>\n            </mat-form-field>\n\n            <!-- Commercial Register -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>السجل التجاري</mat-label>\n              <input matInput formControlName=\"commercialRegister\" placeholder=\"أدخل رقم السجل التجاري\">\n              <mat-icon matSuffix>business_center</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- المعلومات المصرفية -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>account_balance</mat-icon>\n            <span>المعلومات المصرفية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Bank Name -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>اسم البنك</mat-label>\n              <input matInput formControlName=\"bankName\" placeholder=\"أدخل اسم البنك\">\n              <mat-icon matSuffix>account_balance</mat-icon>\n            </mat-form-field>\n\n            <!-- Bank Account Number -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>رقم الحساب المصرفي</mat-label>\n              <input matInput formControlName=\"bankAccountNumber\" placeholder=\"أدخل رقم الحساب\">\n              <mat-icon matSuffix>credit_card</mat-icon>\n            </mat-form-field>\n\n            <!-- IBAN -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>رقم الآيبان (IBAN)</mat-label>\n              <input matInput formControlName=\"iban\" placeholder=\"أدخل رقم الآيبان\">\n              <mat-icon matSuffix>account_balance</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- معلومات إضافية -->\n      <mat-card class=\"form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>info</mat-icon>\n            <span>معلومات إضافية</span>\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"form-grid\">\n\n            <!-- Rating -->\n            <mat-form-field appearance=\"outline\">\n              <mat-label>التقييم</mat-label>\n              <input matInput formControlName=\"rating\" type=\"number\" min=\"1\" max=\"5\" placeholder=\"من 1 إلى 5\">\n              <mat-icon matSuffix>star</mat-icon>\n            </mat-form-field>\n\n            <!-- Is Active -->\n            <div class=\"checkbox-field\">\n              <mat-checkbox formControlName=\"isActive\">\n                مورد نشط\n              </mat-checkbox>\n            </div>\n\n            <!-- Notes -->\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>ملاحظات</mat-label>\n              <textarea matInput formControlName=\"notes\" rows=\"3\"\n                        placeholder=\"أدخل أي ملاحظات إضافية عن المورد\"></textarea>\n              <mat-icon matSuffix>note</mat-icon>\n            </mat-form-field>\n\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Action Buttons -->\n      <div class=\"action-buttons\">\n        <button mat-button type=\"button\" class=\"cancel-btn\" (click)=\"goBack()\">\n          <mat-icon>arrow_back</mat-icon>\n          <span>العودة</span>\n        </button>\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"save-btn\"\n                [disabled]=\"!supplierForm.valid || isLoading\" (click)=\"saveSupplier()\">\n          <mat-icon>save</mat-icon>\n          <span>حفظ المورد</span>\n        </button>\n      </div>\n\n    </form>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري حفظ بيانات المورد...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AAIxF,SAASC,WAAW,QAAQ,sCAAsC;AAElE;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;ICyB9CC,EAAA,CAAAC,cAAA,SAAgH;IAC9GD,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAoG;IAClGD,EAAA,CAAAE,MAAA,gJACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAqG;IACnGD,EAAA,CAAAE,MAAA,0MACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAoH;IAClHD,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAoG;IAClGD,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAmG;IACjGD,EAAA,CAAAE,MAAA,4GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAgH;IAC9GD,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAA2G;IACzGD,EAAA,CAAAE,MAAA,6NACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAA+F;IAC7FD,EAAA,CAAAE,MAAA,sJACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IA9BLH,EAJR,CAAAC,cAAA,cAAoF,mBACrD,uBACT,cACU,eACd;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,qJAA2B;IACnCF,EADmC,CAAAG,YAAA,EAAO,EACpC;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAyBrBD,EAxBA,CAAAI,UAAA,IAAAC,yCAAA,iBAAgH,KAAAC,0CAAA,iBAGZ,KAAAC,0CAAA,iBAGC,KAAAC,0CAAA,iBAGe,KAAAC,0CAAA,iBAGhB,KAAAC,0CAAA,iBAGD,KAAAC,0CAAA,iBAGa,KAAAC,0CAAA,iBAGL,KAAAC,0CAAA,iBAGZ;IAMvGb,EAHM,CAAAG,YAAA,EAAK,EACY,EACV,EACP;;;;;;;;;;;;;IA9BOH,EAAA,CAAAc,SAAA,GAAyG;IAAzGd,EAAA,CAAAe,UAAA,WAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAH,OAAA,CAAAI,QAAA,mBAAAJ,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAH,OAAA,CAAAK,OAAA,EAAyG;IAGzGrB,EAAA,CAAAc,SAAA,EAA6F;IAA7Fd,EAAA,CAAAe,UAAA,WAAAO,OAAA,GAAAL,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAG,OAAA,CAAAF,QAAA,mBAAAE,OAAA,GAAAL,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAG,OAAA,CAAAD,OAAA,EAA6F;IAG7FrB,EAAA,CAAAc,SAAA,EAA8F;IAA9Fd,EAAA,CAAAe,UAAA,WAAAQ,OAAA,GAAAN,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAI,OAAA,CAAAH,QAAA,oBAAAG,OAAA,GAAAN,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAI,OAAA,CAAAF,OAAA,EAA8F;IAG9FrB,EAAA,CAAAc,SAAA,EAA6G;IAA7Gd,EAAA,CAAAe,UAAA,WAAAS,OAAA,GAAAP,MAAA,CAAAC,YAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAJ,QAAA,mBAAAI,OAAA,GAAAP,MAAA,CAAAC,YAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAH,OAAA,EAA6G;IAG7GrB,EAAA,CAAAc,SAAA,EAA6F;IAA7Fd,EAAA,CAAAe,UAAA,WAAAU,OAAA,GAAAR,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAM,OAAA,CAAAL,QAAA,mBAAAK,OAAA,GAAAR,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAM,OAAA,CAAAJ,OAAA,EAA6F;IAG7FrB,EAAA,CAAAc,SAAA,EAA4F;IAA5Fd,EAAA,CAAAe,UAAA,WAAAW,OAAA,GAAAT,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAO,OAAA,CAAAN,QAAA,kBAAAM,OAAA,GAAAT,MAAA,CAAAC,YAAA,CAAAC,GAAA,6BAAAO,OAAA,CAAAL,OAAA,EAA4F;IAG5FrB,EAAA,CAAAc,SAAA,EAAyG;IAAzGd,EAAA,CAAAe,UAAA,WAAAY,OAAA,GAAAV,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAQ,OAAA,CAAAP,QAAA,mBAAAO,OAAA,GAAAV,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAQ,OAAA,CAAAN,OAAA,EAAyG;IAGzGrB,EAAA,CAAAc,SAAA,EAAoG;IAApGd,EAAA,CAAAe,UAAA,WAAAa,OAAA,GAAAX,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAS,OAAA,CAAAR,QAAA,cAAAQ,OAAA,GAAAX,MAAA,CAAAC,YAAA,CAAAC,GAAA,mCAAAS,OAAA,CAAAP,OAAA,EAAoG;IAGpGrB,EAAA,CAAAc,SAAA,EAAwF;IAAxFd,EAAA,CAAAe,UAAA,WAAAc,OAAA,GAAAZ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAU,OAAA,CAAAT,QAAA,gBAAAS,OAAA,GAAAZ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAU,OAAA,CAAAR,OAAA,EAAwF;;;;;IAkCzFrB,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAe,UAAA,UAAAe,OAAA,CAAAC,EAAA,CAAiB;IAC9D/B,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAgC,kBAAA,MAAAF,OAAA,CAAAG,MAAA,MACF;;;;;IAGFjC,EAAA,CAAAC,cAAA,gBAA2H;IACzHD,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBZH,EAAA,CAAAC,cAAA,gBAA2G;IACzGD,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA4G;IAC1GD,EAAA,CAAAE,MAAA,0MACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAA2G;IACzGD,EAAA,CAAAE,MAAA,kKACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAA2G;IACzGD,EAAA,CAAAE,MAAA,+FACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAE,MAAA,4GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAAuH;IACrHD,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAkH;IAChHD,EAAA,CAAAE,MAAA,6NACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA+BdH,EAAA,CAAAC,cAAA,gBAAgE;IAC9DD,EAAA,CAAAE,MAAA,sJACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAwCRH,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAAe,UAAA,UAAAmB,UAAA,CAAAH,EAAA,CAAoB;IAChE/B,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAgC,kBAAA,MAAAE,UAAA,CAAAD,MAAA,MACF;;;;;IAiBAjC,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAe,UAAA,UAAAoB,OAAA,CAAAJ,EAAA,CAAiB;IACtD/B,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAgC,kBAAA,MAAAG,OAAA,CAAAF,MAAA,MACF;;;;;IAkLhBjC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAoC,SAAA,sBAAyC;IACzCpC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+HAAyB;IAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;;;ADnWR,WAAakC,oBAAoB;EAA3B,MAAOA,oBAAoB;IAkBrBC,EAAA;IACAC,MAAA;IACAC,IAAA;IACAC,QAAA;IAnBV;IACAC,SAAS,GAAG,KAAK;IACjBxB,YAAY;IAEZ;IACQyB,MAAM,GAAG,GAAGtD,WAAW,CAACsD,MAAM,SAAS;IAE/C;IACAC,aAAa,GAAmB,EAAE;IAClCC,KAAK,GAAW,EAAE;IAClBC,SAAS,GAAc,EAAE;IAEzB;IACQC,aAAa,GAAmB,EAAE;IAE1CC,YACUV,EAAe,EACfC,MAAc,EACdC,IAAgB,EAChBC,QAAqB;MAHrB,KAAAH,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,QAAQ,GAARA,QAAQ;MAEhB,IAAI,CAACvB,YAAY,GAAG,IAAI,CAAC+B,UAAU,EAAE;IACvC;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACC,oBAAoB,EAAE,CAAC,CAAC;IAC/B;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;IACQC,cAAc,GAA8B;MAClD,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE,OAAO;MACf,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,QAAQ;MACf,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,UAAU;MACrB,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,aAAa;MAC1B,YAAY,EAAE,WAAW;MACzB,WAAW,EAAE,WAAW;MACxB,MAAM,EAAE,QAAQ;MAChB,KAAK,EAAE,MAAM;MACb,KAAK,EAAE,MAAM;MACb,KAAK,EAAE,MAAM;MACb,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE,SAAS;MACpB,OAAO,EAAE,QAAQ;MACjB,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,QAAQ;MAChB,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,SAAS;MACrB,MAAM,EAAE,SAAS;MACjB,OAAO,EAAE,YAAY;MACrB,MAAM,EAAE,OAAO;MACf,KAAK,EAAE;KACR;IAED;IACQC,kBAAkB,GAA8B;MACtD,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MACvC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MAAE,GAAG,EAAE,GAAG;MACvC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MACxC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MACvC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MACtC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,IAAI;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MACvC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MACtC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MACtC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MACtC,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE,GAAG;MAAE,GAAG,EAAE;KAC1B;IAEDC,kBAAkBA,CAACC,KAAU;MAC3B,MAAMC,UAAU,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;MACrC,MAAMC,WAAW,GAAG,IAAI,CAACC,wBAAwB,CAACJ,UAAU,CAAC;MAC7D,IAAI,CAAC3C,YAAY,CAACgD,UAAU,CAAC;QAAEC,MAAM,EAAEH;MAAW,CAAE,CAAC;IACvD;IAEQC,wBAAwBA,CAACJ,UAAkB;MACjD,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAE1B,IAAIG,WAAW,GAAGH,UAAU,CAACO,IAAI,EAAE;MAEnC;MACA,MAAMC,KAAK,GAAGL,WAAW,CAACM,KAAK,CAAC,KAAK,CAAC;MACtC,MAAMC,eAAe,GAAa,EAAE;MAEpC,KAAK,IAAIC,IAAI,IAAIH,KAAK,EAAE;QACtB,MAAMI,SAAS,GAAGD,IAAI,CAACJ,IAAI,EAAE;QAC7B,IAAI,IAAI,CAACX,cAAc,CAACgB,SAAS,CAAC,EAAE;UAClCF,eAAe,CAACG,IAAI,CAAC,IAAI,CAACjB,cAAc,CAACgB,SAAS,CAAC,CAAC;QACtD,CAAC,MAAM;UACL;UACA,IAAIE,cAAc,GAAG,EAAE;UACvB,KAAK,IAAIC,IAAI,IAAIH,SAAS,EAAE;YAC1BE,cAAc,IAAI,IAAI,CAACjB,kBAAkB,CAACkB,IAAI,CAAC,IAAIA,IAAI;UACzD;UACAL,eAAe,CAACG,IAAI,CAACC,cAAc,CAAC;QACtC;MACF;MAEAX,WAAW,GAAGO,eAAe,CAACM,IAAI,CAAC,GAAG,CAAC;MAEvC;MACAb,WAAW,GAAGA,WAAW,CAACc,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACV,IAAI,EAAE;MACrD,OAAOJ,WAAW,CAACc,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,EAAE,CAAC;IAC3D;IAEA;;;IAGQ/B,UAAUA,CAAA;MAChB,OAAO,IAAI,CAACX,EAAE,CAAC2C,KAAK,CAAC;QACnBC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAAC+F,QAAQ,CAAC,CAAC;QAAE;QAC3CC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAChG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACiG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DlB,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAAC+F,QAAQ,CAAC,CAAC;QACnCG,cAAc,EAAE,CAAC,IAAI,EAAE,CAAClG,UAAU,CAAC+F,QAAQ,CAAC,CAAC;QAC7CI,MAAM,EAAE,CAAC,EAAE,EAAE,CAACnG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACoG,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAC/EC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACsG,KAAK,CAAC,CAAC;QAC/BC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC,EAAE,CAAC;QACfC,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACvBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QACxBC,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC7G,UAAU,CAACsG,KAAK,CAAC,CAAC;QAC5CQ,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC9G,UAAU,CAAC+G,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvCC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAChH,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAAC+G,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAAE;QAC7DE,WAAW,EAAE,CAAC,CAAC,EAAE,CAACjH,UAAU,CAAC+G,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrCG,cAAc,EAAE,CAAC,CAAC,CAAC;QACnBC,SAAS,EAAE,CAAC,EAAE,CAAC;QACfC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QACxBC,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdC,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACvBC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC,IAAI;OAChB,CAAC;IACJ;IAEA;;;IAGQ3D,eAAeA,CAAA;MACrB4D,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,iBAAiB,EAAE,EACxB,IAAI,CAACC,SAAS,EAAE,EAChB,IAAI,CAACC,aAAa,EAAE,CACrB,CAAC,CAACC,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACE,SAAS,CAAC,+BAA+B,CAAC;MACjD,CAAC,CAAC;IACJ;IAEA;;;IAGQN,iBAAiBA,CAAA;MACvB,OAAO,IAAIF,OAAO,CAAC,CAACS,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMlE,GAAG,GAAG,IAAI,CAACf,IAAI,CAACrB,GAAG,CAAM,GAAG,IAAI,CAACwB,MAAM,iBAAiB,CAAC,CAAC+E,SAAS,CAAC;UACxEC,IAAI,EAAGC,QAAQ,IAAI;YACjBN,OAAO,CAACO,GAAG,CAAC,0BAA0B,EAAED,QAAQ,CAAC;YACjD,IAAI,CAAChF,aAAa,GAAGgF,QAAQ,CAAChF,aAAa,IAAI,EAAE;YACjD0E,OAAO,CAACO,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACjF,aAAa,CAAC;YACzD4E,OAAO,EAAE;UACX,CAAC;UACDH,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,IAAI,CAACE,SAAS,CAAC,6BAA6B,CAAC;YAC7CE,MAAM,CAACJ,KAAK,CAAC;UACf;SACD,CAAC;QACF,IAAI,CAACtE,aAAa,CAAC2B,IAAI,CAACnB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQ2D,SAASA,CAAA;MACf,OAAO,IAAIH,OAAO,CAAC,CAACS,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMlE,GAAG,GAAG,IAAI,CAACf,IAAI,CAACrB,GAAG,CAAM,GAAG,IAAI,CAACwB,MAAM,WAAW,CAAC,CAAC+E,SAAS,CAAC;UAClEC,IAAI,EAAGC,QAAQ,IAAI;YACjBN,OAAO,CAACO,GAAG,CAAC,iBAAiB,EAAED,QAAQ,CAAC;YACxC,IAAI,CAAC/E,KAAK,GAAG+E,QAAQ,CAAC/E,KAAK,IAAI,EAAE;YACjCyE,OAAO,CAACO,GAAG,CAAC,eAAe,EAAE,IAAI,CAAChF,KAAK,CAAC;YACxC2E,OAAO,EAAE;UACX,CAAC;UACDH,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C,IAAI,CAACE,SAAS,CAAC,wBAAwB,CAAC;YACxCE,MAAM,CAACJ,KAAK,CAAC;UACf;SACD,CAAC;QACF,IAAI,CAACtE,aAAa,CAAC2B,IAAI,CAACnB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQ4D,aAAaA,CAAA;MACnB,OAAO,IAAIJ,OAAO,CAAC,CAACS,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMlE,GAAG,GAAG,IAAI,CAACf,IAAI,CAACrB,GAAG,CAAM,GAAG,IAAI,CAACwB,MAAM,YAAY,CAAC,CAAC+E,SAAS,CAAC;UACnEC,IAAI,EAAGC,QAAQ,IAAI;YACjBN,OAAO,CAACO,GAAG,CAAC,qBAAqB,EAAED,QAAQ,CAAC;YAC5C,IAAI,CAAC9E,SAAS,GAAG8E,QAAQ,CAAC9E,SAAS,IAAI,EAAE;YACzCwE,OAAO,CAACO,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC/E,SAAS,CAAC;YAChD0E,OAAO,EAAE;UACX,CAAC;UACDH,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChD,IAAI,CAACE,SAAS,CAAC,sBAAsB,CAAC;YACtCE,MAAM,CAACJ,KAAK,CAAC;UACf;SACD,CAAC;QACF,IAAI,CAACtE,aAAa,CAAC2B,IAAI,CAACnB,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGAH,oBAAoBA,CAAA;MAClB,IAAI,CAACV,SAAS,GAAG,IAAI;MACrB,MAAMa,GAAG,GAAG,IAAI,CAACf,IAAI,CAACrB,GAAG,CAAM,GAAG,IAAI,CAACwB,MAAM,qBAAqB,CAAC,CAAC+E,SAAS,CAAC;QAC5EC,IAAI,EAAGC,QAAQ,IAAI;UACjBN,OAAO,CAACO,GAAG,CAAC,8BAA8B,EAAED,QAAQ,CAAC;UACrD,IAAI,CAAC1G,YAAY,CAACgD,UAAU,CAAC;YAC3BgB,YAAY,EAAE0C,QAAQ,CAACE;WACxB,CAAC;UACFR,OAAO,CAACO,GAAG,CAAC,oBAAoB,EAAED,QAAQ,CAACE,QAAQ,CAAC;UACpD,IAAI,CAACpF,SAAS,GAAG,KAAK;QACxB,CAAC;QACD2E,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACE,SAAS,CAAC,yBAAyB,CAAC;UACzC,IAAI,CAAC7E,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MACF,IAAI,CAACK,aAAa,CAAC2B,IAAI,CAACnB,GAAG,CAAC;IAC9B;IAEA;;;IAGAwE,YAAYA,CAAA;MACV,IAAI,IAAI,CAAC7G,YAAY,CAAC8G,KAAK,EAAE;QAC3B,IAAI,CAACtF,SAAS,GAAG,IAAI;QAErB,MAAMuF,SAAS,GAAG,IAAI,CAAC/G,YAAY,CAAC6C,KAAK;QACzC,MAAMmE,OAAO,GAA0B;UACrChD,YAAY,EAAE+C,SAAS,CAAC/C,YAAY;UACpCE,MAAM,EAAE6C,SAAS,CAAC7C,MAAM;UACxBjB,MAAM,EAAE8D,SAAS,CAAC9D,MAAM,IAAI,IAAI;UAChCmB,cAAc,EAAE2C,SAAS,CAAC3C,cAAc;UACxCC,MAAM,EAAE0C,SAAS,CAAC1C,MAAM;UACxBE,MAAM,EAAEwC,SAAS,CAACxC,MAAM,IAAI,IAAI;UAChCC,KAAK,EAAEuC,SAAS,CAACvC,KAAK,IAAI,IAAI;UAC9BC,OAAO,EAAEsC,SAAS,CAACtC,OAAO,IAAI,IAAI;UAClCC,OAAO,EAAEqC,SAAS,CAACrC,OAAO,IAAI,IAAI;UAClCC,MAAM,EAAEoC,SAAS,CAACpC,MAAM,IAAI,IAAI;UAChCC,SAAS,EAAEmC,SAAS,CAACnC,SAAS,IAAI,IAAI;UACtCC,iBAAiB,EAAEkC,SAAS,CAAClC,iBAAiB,IAAI,IAAI;UACtDC,kBAAkB,EAAEiC,SAAS,CAACjC,kBAAkB,IAAI,IAAI;UACxDC,kBAAkB,EAAEgC,SAAS,CAAChC,kBAAkB,IAAI,IAAI;UACxDC,YAAY,EAAE+B,SAAS,CAAC/B,YAAY;UACpCE,YAAY,EAAE6B,SAAS,CAAC7B,YAAY;UACpCC,WAAW,EAAE4B,SAAS,CAAC5B,WAAW;UAClCC,cAAc,EAAE2B,SAAS,CAAC3B,cAAc;UACxCC,SAAS,EAAE0B,SAAS,CAAC1B,SAAS,IAAI,IAAI;UACtCC,kBAAkB,EAAEyB,SAAS,CAACzB,kBAAkB,IAAI,IAAI;UACxDC,QAAQ,EAAEwB,SAAS,CAACxB,QAAQ,IAAI,IAAI;UACpCC,iBAAiB,EAAEuB,SAAS,CAACvB,iBAAiB,IAAI,IAAI;UACtDC,IAAI,EAAEsB,SAAS,CAACtB,IAAI,IAAI,IAAI;UAC5BC,MAAM,EAAEqB,SAAS,CAACrB,MAAM,IAAI,IAAI;UAChCC,KAAK,EAAEoB,SAAS,CAACpB,KAAK,IAAI,IAAI;UAC9BC,QAAQ,EAAEmB,SAAS,CAACnB;SACrB;QAED,MAAMvD,GAAG,GAAG,IAAI,CAACf,IAAI,CAAC2F,IAAI,CAAM,GAAG,IAAI,CAACxF,MAAM,YAAY,EAAEuF,OAAO,CAAC,CAACR,SAAS,CAAC;UAC7EC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAClF,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC0F,WAAW,CAAC,uBAAuB,CAAC;YACzC,IAAI,CAAC7F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;UACtC,CAAC;UACDhB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAC3E,SAAS,GAAG,KAAK;YACtB4E,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;YAC9C,IAAI,CAACE,SAAS,CAAC,0BAA0B,CAAC;UAC5C;SACD,CAAC;QACF,IAAI,CAACxE,aAAa,CAAC2B,IAAI,CAACnB,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC+E,oBAAoB,EAAE;QAC3B,IAAI,CAACf,SAAS,CAAC,+BAA+B,CAAC;MACjD;IACF;IAEA;;;IAGQe,oBAAoBA,CAAA;MAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtH,YAAY,CAACuH,QAAQ,CAAC,CAACnF,OAAO,CAACoF,GAAG,IAAG;QACpD,MAAMC,OAAO,GAAG,IAAI,CAACzH,YAAY,CAACC,GAAG,CAACuH,GAAG,CAAC;QAC1CC,OAAO,EAAEC,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA;;;IAGAC,MAAMA,CAAA;MACJ,IAAI,CAACtG,MAAM,CAAC8F,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA;;;IAGAS,MAAMA,CAAA;MACJ,IAAI,CAACD,MAAM,EAAE;IACf;IAEA;;;IAGAE,yBAAyBA,CAAA;MACvB,IAAI,CAACxG,MAAM,CAAC8F,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;;;IAGAW,iBAAiBA,CAAA;MACf,IAAI,CAACzG,MAAM,CAAC8F,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;;;IAGAY,oBAAoBA,CAAA;MAClB,IAAI,CAAC1G,MAAM,CAAC8F,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAChD;IAEA;;;IAGQD,WAAWA,CAACc,OAAe;MACjC,IAAI,CAACzG,QAAQ,CAAC0G,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,kBAAkB,CAAC;QAChCC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;;;IAGQhC,SAASA,CAAC2B,OAAe;MAC/B,IAAI,CAACzG,QAAQ,CAAC0G,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QACnCE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;OACnB,CAAC;IACJ;;uCA1XWlH,oBAAoB,EAAArC,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA5J,EAAA,CAAAwJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA9J,EAAA,CAAAwJ,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;;YAApB3H,oBAAoB;MAAA4H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpFzBvK,EANR,CAAAC,cAAA,aAAoC,aAGT,aACK,aACD,gBACqC;UAAnBD,EAAA,CAAAyK,UAAA,mBAAAC,sDAAA;YAAA,OAASF,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzD7I,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAAyB,YACA;UAAAD,EAAA,CAAAE,MAAA,uFAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,kKAA6B;UAE1DF,EAF0D,CAAAG,YAAA,EAAI,EACtD,EACF;UAEJH,EADF,CAAAC,cAAA,cAA4B,iBACuC;UAAnBD,EAAA,CAAAyK,UAAA,mBAAAE,uDAAA;YAAA,OAASH,GAAA,CAAA1B,MAAA,EAAQ;UAAA,EAAC;UAC9D9I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UACbF,EADa,CAAAG,YAAA,EAAO,EACX;UACTH,EAAA,CAAAC,cAAA,kBAEiC;UAAzBD,EAAA,CAAAyK,UAAA,mBAAAG,uDAAA;YAAA,OAASJ,GAAA,CAAAzC,YAAA,EAAc;UAAA,EAAC;UAC9B/H,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAIxBF,EAJwB,CAAAG,YAAA,EAAO,EAChB,EACL,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA0B,gBAC+B;UAGrDD,EAAA,CAAAI,UAAA,KAAAyK,oCAAA,mBAAoF;UA4C9E7K,EAHN,CAAAC,cAAA,oBAA6C,uBAC1B,0BACwB,gBAC3B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0JAA0B;UAEpCF,EAFoC,CAAAG,YAAA,EAAO,EACxB,EACD;UAOVH,EANR,CAAAC,cAAA,wBAAkB,eACO,eAGO,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,iEAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAoC,SAAA,iBAA2F;UAC3FpC,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAE3BF,EAF2B,CAAAG,YAAA,EAAW,EACnB,EACb;UAKFH,EAFJ,CAAAC,cAAA,eAA8C,0BACY,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iEAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEjCH,EADF,CAAAC,cAAA,sBAAsD,sBAC/B;UAAAD,EAAA,CAAAE,MAAA,wFAAe;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACjDH,EAAA,CAAAI,UAAA,KAAA0K,2CAAA,yBAAiE;UAGnE9K,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAAI,UAAA,KAAA2K,0CAAA,wBAA2H;UAG7H/K,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,kBAE8C;UAAtCD,EAAA,CAAAyK,UAAA,mBAAAO,uDAAA;YAAA,OAASR,GAAA,CAAAzB,yBAAA,EAA2B;UAAA,EAAC;UAC3C/I,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAEjBF,EAFiB,CAAAG,YAAA,EAAW,EACjB,EACL;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,kHAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAC,cAAA,iBAC4C;UAArCD,EAAA,CAAAyK,UAAA,mBAAAQ,sDAAAC,MAAA;YAAA,OAASV,GAAA,CAAA7G,kBAAA,CAAAuH,MAAA,CAA0B;UAAA,EAAC;UAD3ClL,EAAA,CAAAG,YAAA,EAC4C;UAC5CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIvCH,EAHA,CAAAI,UAAA,KAAA+K,0CAAA,wBAA2G,KAAAC,0CAAA,wBAGC;UAIhHpL,EADE,CAAAG,YAAA,EAAiB,EACb;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,oIAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/CH,EAAA,CAAAoC,SAAA,iBAA+F;UAC/FpC,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAAI,UAAA,KAAAiL,0CAAA,wBAA2G;UAI/GrL,EADE,CAAAG,YAAA,EAAiB,EACb;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,6EAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAoC,SAAA,iBAA8E;UAC9EpC,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIpCH,EAHA,CAAAI,UAAA,KAAAkL,0CAAA,wBAA2G,KAAAC,0CAAA,wBAGD;UAI9GvL,EADE,CAAAG,YAAA,EAAiB,EACb;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,0BACW,iBACxB;UAAAD,EAAA,CAAAE,MAAA,oHAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9CH,EAAA,CAAAoC,SAAA,iBAAsF;UACtFpC,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAI7CH,EAHA,CAAAI,UAAA,KAAAoL,0CAAA,wBAAuH,KAAAC,0CAAA,wBAGL;UAQ5HzL,EALQ,CAAAG,YAAA,EAAiB,EACb,EAEF,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,gBACJ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,8IAAwB;UAElCF,EAFkC,CAAAG,YAAA,EAAO,EACtB,EACD;UAMZH,EALN,CAAAC,cAAA,wBAAkB,eACO,2BAGgB,kBACxB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAoC,SAAA,kBAAqE;UACrEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAC3BF,EAD2B,CAAAG,YAAA,EAAW,EACrB;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,0GAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAoC,SAAA,kBAAwF;UACxFpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAI,UAAA,MAAAsL,2CAAA,wBAAgE;UAGlE1L,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,0GAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAoC,SAAA,kBAAyE;UACzEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,qGAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAoC,SAAA,kBAAyF;UACzFpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAOlCF,EAPkC,CAAAG,YAAA,EAAW,EACtB,EAIb,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,yIAAuB;UAEjCF,EAFiC,CAAAG,YAAA,EAAO,EACrB,EACD;UAOVH,EANR,CAAAC,cAAA,yBAAkB,gBACO,gBAGU,2BAC2B,kBAC3C;UAAAD,EAAA,CAAAE,MAAA,yDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE7BH,EADF,CAAAC,cAAA,uBAAwC,uBACjB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC/CH,EAAA,CAAAI,UAAA,MAAAuL,4CAAA,yBAAmE;UAGrE3L,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UACjBH,EAAA,CAAAC,cAAA,mBAEyC;UAAjCD,EAAA,CAAAyK,UAAA,mBAAAmB,wDAAA;YAAA,OAASpB,GAAA,CAAAvB,oBAAA,EAAsB;UAAA,EAAC;UACtCjJ,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAEjBF,EAFiB,CAAAG,YAAA,EAAW,EACjB,EACL;UAKFH,EAFJ,CAAAC,cAAA,gBAA+B,2BAC2B,kBAC3C;UAAAD,EAAA,CAAAE,MAAA,mDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE5BH,EADF,CAAAC,cAAA,uBAAqC,uBACd;UAAAD,EAAA,CAAAE,MAAA,4EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAI,UAAA,MAAAyL,4CAAA,yBAAyD;UAG3D7L,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnCF,EADmC,CAAAG,YAAA,EAAW,EAC7B;UACjBH,EAAA,CAAAC,cAAA,mBAEsC;UAA9BD,EAAA,CAAAyK,UAAA,mBAAAqB,wDAAA;YAAA,OAAStB,GAAA,CAAAxB,iBAAA,EAAmB;UAAA,EAAC;UACnChJ,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAEjBF,EAFiB,CAAAG,YAAA,EAAW,EACjB,EACL;UAIJH,EADF,CAAAC,cAAA,2BAAwD,kBAC3C;UAAAD,EAAA,CAAAE,MAAA,oGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAoC,SAAA,qBACgE;UAChEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAKhCF,EALgC,CAAAG,YAAA,EAAW,EACpB,EAEb,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,0GAAiB;UAE3BF,EAF2B,CAAAG,YAAA,EAAO,EACf,EACD;UAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;UAAAD,EAAA,CAAAE,MAAA,6GAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3CH,EAAA,CAAAoC,SAAA,kBAA8E;UAC9EpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACxB;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,sEAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAoC,SAAA,kBAA4E;UAC5EpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACjCF,EADiC,CAAAG,YAAA,EAAW,EAC3B;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,oGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAoC,SAAA,kBAA+E;UAC/EpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,+BAAsB;UAKlDF,EALkD,CAAAG,YAAA,EAAW,EACtC,EAEb,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sHAAmB;UAE7BF,EAF6B,CAAAG,YAAA,EAAO,EACjB,EACD;UAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAoC,SAAA,kBAA6E;UAC7EpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACvB;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAoC,SAAA,kBAA0F;UAC1FpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAK3CF,EAL2C,CAAAG,YAAA,EAAW,EAC/B,EAEb,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gHAAkB;UAE5BF,EAF4B,CAAAG,YAAA,EAAO,EAChB,EACD;UAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;UAAAD,EAAA,CAAAE,MAAA,0DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAoC,SAAA,kBAAwE;UACxEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UACrCF,EADqC,CAAAG,YAAA,EAAW,EAC/B;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,2GAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAoC,SAAA,kBAAkF;UAClFpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACjCF,EADiC,CAAAG,YAAA,EAAW,EAC3B;UAIfH,EADF,CAAAC,cAAA,2BAAqC,kBACxB;UAAAD,EAAA,CAAAE,MAAA,6EAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAoC,SAAA,kBAAsE;UACtEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAK3CF,EAL2C,CAAAG,YAAA,EAAW,EAC/B,EAEb,EACW,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAA4B,wBACT,uBACC,iBACJ;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,wFAAc;UAExBF,EAFwB,CAAAG,YAAA,EAAO,EACZ,EACD;UAMZH,EALN,CAAAC,cAAA,yBAAkB,gBACO,2BAGgB,kBACxB;UAAAD,EAAA,CAAAE,MAAA,mDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAoC,SAAA,kBAAgG;UAChGpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAC1BF,EAD0B,CAAAG,YAAA,EAAW,EACpB;UAIfH,EADF,CAAAC,cAAA,gBAA4B,yBACe;UACvCD,EAAA,CAAAE,MAAA,sDACF;UACFF,EADE,CAAAG,YAAA,EAAe,EACX;UAIJH,EADF,CAAAC,cAAA,2BAAwD,kBAC3C;UAAAD,EAAA,CAAAE,MAAA,mDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAoC,SAAA,qBACoE;UACpEpC,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAKhCF,EALgC,CAAAG,YAAA,EAAW,EACpB,EAEb,EACW,EACV;UAITH,EADF,CAAAC,cAAA,gBAA4B,mBAC6C;UAAnBD,EAAA,CAAAyK,UAAA,mBAAAsB,wDAAA;YAAA,OAASvB,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACpE7I,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,6CAAM;UACdF,EADc,CAAAG,YAAA,EAAO,EACZ;UACTH,EAAA,CAAAC,cAAA,mBAC+E;UAAzBD,EAAA,CAAAyK,UAAA,mBAAAuB,wDAAA;YAAA,OAASxB,GAAA,CAAAzC,YAAA,EAAc;UAAA,EAAC;UAC5E/H,EAAA,CAAAC,cAAA,iBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAKxBF,EALwB,CAAAG,YAAA,EAAO,EAChB,EACL,EAED,EACH;UAGNH,EAAA,CAAAI,UAAA,MAAA6L,qCAAA,kBAA+C;UAKjDjM,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;UA3aUH,EAAA,CAAAc,SAAA,IAA6C;UAA7Cd,EAAA,CAAAe,UAAA,cAAAyJ,GAAA,CAAAtJ,YAAA,CAAA8G,KAAA,IAAAwC,GAAA,CAAA9H,SAAA,CAA6C;UAWnD1C,EAAA,CAAAc,SAAA,GAA0B;UAA1Bd,EAAA,CAAAe,UAAA,cAAAyJ,GAAA,CAAAtJ,YAAA,CAA0B;UAGGlB,EAAA,CAAAc,SAAA,EAAiD;UAAjDd,EAAA,CAAAe,UAAA,UAAAyJ,GAAA,CAAAtJ,YAAA,CAAA8G,KAAA,IAAAwC,GAAA,CAAAtJ,YAAA,CAAAG,OAAA,CAAiD;UAkEzCrB,EAAA,CAAAc,SAAA,IAAgB;UAAhBd,EAAA,CAAAe,UAAA,YAAAyJ,GAAA,CAAA5H,aAAA,CAAgB;UAKnC5C,EAAA,CAAAc,SAAA,GAA6G;UAA7Gd,EAAA,CAAAe,UAAA,WAAAS,OAAA,GAAAgJ,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAJ,QAAA,mBAAAI,OAAA,GAAAgJ,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,qCAAAK,OAAA,CAAAH,OAAA,EAA6G;UAkB7GrB,EAAA,CAAAc,SAAA,IAA6F;UAA7Fd,EAAA,CAAAe,UAAA,WAAAU,OAAA,GAAA+I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAM,OAAA,CAAAL,QAAA,mBAAAK,OAAA,GAAA+I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAM,OAAA,CAAAJ,OAAA,EAA6F;UAG7FrB,EAAA,CAAAc,SAAA,EAA8F;UAA9Fd,EAAA,CAAAe,UAAA,WAAAW,OAAA,GAAA8I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAO,OAAA,CAAAN,QAAA,oBAAAM,OAAA,GAAA8I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAO,OAAA,CAAAL,OAAA,EAA8F;UAY9FrB,EAAA,CAAAc,SAAA,GAA6F;UAA7Fd,EAAA,CAAAe,UAAA,WAAAY,OAAA,GAAA6I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAQ,OAAA,CAAAP,QAAA,mBAAAO,OAAA,GAAA6I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAQ,OAAA,CAAAN,OAAA,EAA6F;UAY7FrB,EAAA,CAAAc,SAAA,GAA6F;UAA7Fd,EAAA,CAAAe,UAAA,WAAAa,OAAA,GAAA4I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAS,OAAA,CAAAR,QAAA,mBAAAQ,OAAA,GAAA4I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAS,OAAA,CAAAP,OAAA,EAA6F;UAG7FrB,EAAA,CAAAc,SAAA,EAA4F;UAA5Fd,EAAA,CAAAe,UAAA,WAAAc,OAAA,GAAA2I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAU,OAAA,CAAAT,QAAA,kBAAAS,OAAA,GAAA2I,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,6BAAAU,OAAA,CAAAR,OAAA,EAA4F;UAY5FrB,EAAA,CAAAc,SAAA,GAAyG;UAAzGd,EAAA,CAAAe,UAAA,WAAAmL,QAAA,GAAA1B,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,mCAAA+K,QAAA,CAAA9K,QAAA,mBAAA8K,QAAA,GAAA1B,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,mCAAA+K,QAAA,CAAA7K,OAAA,EAAyG;UAGzGrB,EAAA,CAAAc,SAAA,EAAoG;UAApGd,EAAA,CAAAe,UAAA,WAAAoL,QAAA,GAAA3B,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,mCAAAgL,QAAA,CAAA/K,QAAA,cAAA+K,QAAA,GAAA3B,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,mCAAAgL,QAAA,CAAA9K,OAAA,EAAoG;UAiCtGrB,EAAA,CAAAc,SAAA,IAAkD;UAAlDd,EAAA,CAAAe,UAAA,UAAAqL,QAAA,GAAA5B,GAAA,CAAAtJ,YAAA,CAAAC,GAAA,4BAAAiL,QAAA,CAAAhL,QAAA,UAAkD;UA0C1BpB,EAAA,CAAAc,SAAA,IAAY;UAAZd,EAAA,CAAAe,UAAA,YAAAyJ,GAAA,CAAA1H,SAAA,CAAY;UAmBf9C,EAAA,CAAAc,SAAA,IAAQ;UAARd,EAAA,CAAAe,UAAA,YAAAyJ,GAAA,CAAA3H,KAAA,CAAQ;UA0KvC7C,EAAA,CAAAc,SAAA,KAA6C;UAA7Cd,EAAA,CAAAe,UAAA,cAAAyJ,GAAA,CAAAtJ,YAAA,CAAA8G,KAAA,IAAAwC,GAAA,CAAA9H,SAAA,CAA6C;UAU7B1C,EAAA,CAAAc,SAAA,GAAe;UAAfd,EAAA,CAAAe,UAAA,SAAAyJ,GAAA,CAAA9H,SAAA,CAAe;;;qBDhX3CxD,YAAY,EAAAmN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpN,mBAAmB,EAAAsK,EAAA,CAAA+C,aAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,eAAA,EAAAlD,EAAA,CAAAmD,oBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAApD,EAAA,CAAAqD,YAAA,EAAArD,EAAA,CAAAsD,YAAA,EAAAtD,EAAA,CAAAuD,kBAAA,EAAAvD,EAAA,CAAAwD,eAAA,EACnB3N,aAAa,EAAA4N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb/N,eAAe,EAAAgO,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfjO,aAAa,EAAAkO,EAAA,CAAAC,OAAA,EACblO,kBAAkB,EAAAmO,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBtO,cAAc,EAAAuO,GAAA,CAAAC,QAAA,EACdvO,eAAe,EAAAwO,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACfzO,iBAAiB,EAAA0O,GAAA,CAAAC,WAAA,EACjB1O,wBAAwB,EAAA2O,GAAA,CAAAC,kBAAA,EACxB3O,iBAAiB,EACjBC,gBAAgB,EAAA2O,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;;SAKPvM,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}