using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Terra.Retail.Core.Entities;

namespace Terra.Retail.Infrastructure.Data.Configurations
{
    public class SaleConfiguration : IEntityTypeConfiguration<Sale>
    {
        public void Configure(EntityTypeBuilder<Sale> builder)
        {
            builder.ToTable("Sales");

            builder.HasKey(s => s.Id);

            builder.Property(s => s.InvoiceNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(s => s.CustomerName)
                .HasMaxLength(100);

            builder.Property(s => s.SubTotal)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.DiscountPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.DiscountAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.TaxPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.TaxAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.TotalAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.PaidAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.RemainingAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(s => s.Notes)
                .HasMaxLength(1000);

            builder.Property(s => s.InternalNotes)
                .HasMaxLength(1000);

            builder.Property(s => s.ExternalReference)
                .HasMaxLength(50);

            builder.Property(s => s.CancellationReason)
                .HasMaxLength(500);

            builder.Property(s => s.TableNumber)
                .HasMaxLength(20);

            // Relationships
            builder.HasOne(s => s.Customer)
                .WithMany(c => c.Sales)
                .HasForeignKey(s => s.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(s => s.Branch)
                .WithMany(b => b.Sales)
                .HasForeignKey(s => s.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(s => s.User)
                .WithMany(u => u.Sales)
                .HasForeignKey(s => s.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(s => s.CancelledBy)
                .WithMany()
                .HasForeignKey(s => s.CancelledById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(s => s.InvoiceNumber)
                .IsUnique()
                .HasDatabaseName("IX_Sales_InvoiceNumber");

            builder.HasIndex(s => new { s.InvoiceDate, s.BranchId })
                .HasDatabaseName("IX_Sales_InvoiceDate_BranchId");

            builder.HasIndex(s => new { s.CustomerId, s.InvoiceDate })
                .HasDatabaseName("IX_Sales_CustomerId_InvoiceDate");

            builder.HasIndex(s => s.Status)
                .HasDatabaseName("IX_Sales_Status");
        }
    }

    public class SaleItemConfiguration : IEntityTypeConfiguration<SaleItem>
    {
        public void Configure(EntityTypeBuilder<SaleItem> builder)
        {
            builder.ToTable("SaleItems");

            builder.HasKey(si => si.Id);

            builder.Property(si => si.Quantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(si => si.UnitPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(si => si.UnitCostPrice)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(si => si.DiscountPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(si => si.DiscountAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(si => si.NetUnitPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(si => si.LineTotal)
                .HasColumnType("decimal(18,2)");

            builder.Property(si => si.NetLineTotal)
                .HasColumnType("decimal(18,2)");

            builder.Property(si => si.TaxPercentage)
                .HasColumnType("decimal(5,2)")
                .HasDefaultValue(0);

            builder.Property(si => si.TaxAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(si => si.FinalTotal)
                .HasColumnType("decimal(18,2)");

            builder.Property(si => si.ItemNotes)
                .HasMaxLength(500);

            builder.Property(si => si.BatchNumber)
                .HasMaxLength(50);

            builder.Property(si => si.SerialNumber)
                .HasMaxLength(100);

            builder.Property(si => si.ActualWeight)
                .HasColumnType("decimal(18,3)");

            builder.Property(si => si.ReturnedQuantity)
                .HasColumnType("decimal(18,3)")
                .HasDefaultValue(0);

            builder.Property(si => si.DiscountReason)
                .HasMaxLength(200);

            // Relationships
            builder.HasOne(si => si.Sale)
                .WithMany(s => s.Items)
                .HasForeignKey(si => si.SaleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(si => si.Product)
                .WithMany(p => p.SaleItems)
                .HasForeignKey(si => si.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(si => si.PriceCategory)
                .WithMany()
                .HasForeignKey(si => si.PriceCategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(si => new { si.SaleId, si.LineNumber })
                .IsUnique()
                .HasDatabaseName("IX_SaleItems_SaleId_LineNumber");

            builder.HasIndex(si => si.ProductId)
                .HasDatabaseName("IX_SaleItems_ProductId");
        }
    }

    public class SalePaymentConfiguration : IEntityTypeConfiguration<SalePayment>
    {
        public void Configure(EntityTypeBuilder<SalePayment> builder)
        {
            builder.ToTable("SalePayments");

            builder.HasKey(sp => sp.Id);

            builder.Property(sp => sp.PaymentNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(sp => sp.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(sp => sp.ReceivedAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(sp => sp.ChangeAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(sp => sp.ReferenceNumber)
                .HasMaxLength(100);

            builder.Property(sp => sp.BankName)
                .HasMaxLength(100);

            builder.Property(sp => sp.AccountNumber)
                .HasMaxLength(50);

            builder.Property(sp => sp.Notes)
                .HasMaxLength(500);

            builder.Property(sp => sp.ReceiptNumber)
                .HasMaxLength(20);

            builder.Property(sp => sp.ExternalTransactionId)
                .HasMaxLength(100);

            builder.Property(sp => sp.TransactionFee)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(sp => sp.Currency)
                .HasMaxLength(3)
                .HasDefaultValue("SAR");

            builder.Property(sp => sp.ExchangeRate)
                .HasColumnType("decimal(18,6)")
                .HasDefaultValue(1);

            // Relationships
            builder.HasOne(sp => sp.Sale)
                .WithMany(s => s.Payments)
                .HasForeignKey(sp => sp.SaleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(sp => sp.PaymentMethod)
                .WithMany(pm => pm.SalePayments)
                .HasForeignKey(sp => sp.PaymentMethodId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sp => sp.User)
                .WithMany()
                .HasForeignKey(sp => sp.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sp => sp.ConfirmedBy)
                .WithMany()
                .HasForeignKey(sp => sp.ConfirmedById)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(sp => sp.CashBox)
                .WithMany(cb => cb.SalePayments)
                .HasForeignKey(sp => sp.CashBoxId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(sp => sp.PaymentNumber)
                .IsUnique()
                .HasDatabaseName("IX_SalePayments_PaymentNumber");

            builder.HasIndex(sp => new { sp.SaleId, sp.PaymentDate })
                .HasDatabaseName("IX_SalePayments_SaleId_PaymentDate");

            builder.HasIndex(sp => sp.Status)
                .HasDatabaseName("IX_SalePayments_Status");
        }
    }

    public class SaleReturnConfiguration : IEntityTypeConfiguration<SaleReturn>
    {
        public void Configure(EntityTypeBuilder<SaleReturn> builder)
        {
            builder.ToTable("SaleReturns");

            builder.HasKey(sr => sr.Id);

            builder.Property(sr => sr.ReturnNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(sr => sr.ReturnReasonDescription)
                .HasMaxLength(500);

            builder.Property(sr => sr.TotalAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(sr => sr.RefundedAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(sr => sr.Notes)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(sr => sr.Sale)
                .WithMany(s => s.Returns)
                .HasForeignKey(sr => sr.SaleId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sr => sr.Customer)
                .WithMany()
                .HasForeignKey(sr => sr.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(sr => sr.Branch)
                .WithMany()
                .HasForeignKey(sr => sr.BranchId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sr => sr.User)
                .WithMany()
                .HasForeignKey(sr => sr.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sr => sr.ConfirmedBy)
                .WithMany()
                .HasForeignKey(sr => sr.ConfirmedById)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(sr => sr.RefundedBy)
                .WithMany()
                .HasForeignKey(sr => sr.RefundedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(sr => sr.ReturnNumber)
                .IsUnique()
                .HasDatabaseName("IX_SaleReturns_ReturnNumber");

            builder.HasIndex(sr => new { sr.SaleId, sr.ReturnDate })
                .HasDatabaseName("IX_SaleReturns_SaleId_ReturnDate");

            builder.HasIndex(sr => sr.Status)
                .HasDatabaseName("IX_SaleReturns_Status");
        }
    }

    public class SaleReturnItemConfiguration : IEntityTypeConfiguration<SaleReturnItem>
    {
        public void Configure(EntityTypeBuilder<SaleReturnItem> builder)
        {
            builder.ToTable("SaleReturnItems");

            builder.HasKey(sri => sri.Id);

            builder.Property(sri => sri.ReturnedQuantity)
                .HasColumnType("decimal(18,3)");

            builder.Property(sri => sri.UnitPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(sri => sri.TotalAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(sri => sri.ItemReturnReason)
                .HasMaxLength(500);

            builder.Property(sri => sri.BatchNumber)
                .HasMaxLength(50);

            builder.Property(sri => sri.SerialNumber)
                .HasMaxLength(100);

            // Relationships
            builder.HasOne(sri => sri.SaleReturn)
                .WithMany(sr => sr.Items)
                .HasForeignKey(sri => sri.SaleReturnId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(sri => sri.SaleItem)
                .WithMany(si => si.ReturnItems)
                .HasForeignKey(sri => sri.SaleItemId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(sri => sri.Product)
                .WithMany()
                .HasForeignKey(sri => sri.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(sri => new { sri.SaleReturnId, sri.SaleItemId })
                .HasDatabaseName("IX_SaleReturnItems_SaleReturnId_SaleItemId");
        }
    }
}
