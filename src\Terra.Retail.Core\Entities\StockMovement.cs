using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// حركات المخزون
    /// </summary>
    public class StockMovement : BaseEntity
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// رقم الحركة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string MovementNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الحركة
        /// </summary>
        public DateTime MovementDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// نوع الحركة
        /// </summary>
        public StockMovementType MovementType { get; set; }

        /// <summary>
        /// الكمية (موجبة للداخل، سالبة للخارج)
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; } = 0;

        /// <summary>
        /// إجمالي القيمة
        /// </summary>
        public decimal TotalValue { get; set; } = 0;

        /// <summary>
        /// الرصيد قبل الحركة
        /// </summary>
        public decimal BalanceBefore { get; set; }

        /// <summary>
        /// الرصيد بعد الحركة
        /// </summary>
        public decimal BalanceAfter { get; set; }

        /// <summary>
        /// مرجع الحركة (رقم الفاتورة مثلاً)
        /// </summary>
        [MaxLength(50)]
        public string? Reference { get; set; }

        /// <summary>
        /// نوع المرجع
        /// </summary>
        public StockReferenceType? ReferenceType { get; set; }

        /// <summary>
        /// معرف المرجع في الجدول المرتبط
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// وصف الحركة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أدخل الحركة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// رقم التشغيل/الدفعة
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية للدفعة
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الفرع المحول منه (في حالة التحويل)
        /// </summary>
        public int? FromBranchId { get; set; }

        /// <summary>
        /// الفرع المحول إليه (في حالة التحويل)
        /// </summary>
        public int? ToBranchId { get; set; }

        /// <summary>
        /// هل الحركة مؤكدة
        /// </summary>
        public bool IsConfirmed { get; set; } = true;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد الحركة
        /// </summary>
        public int? ConfirmedById { get; set; }

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual Branch Branch { get; set; } = null!;
        public virtual Branch? FromBranch { get; set; }
        public virtual Branch? ToBranch { get; set; }
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
    }

    /// <summary>
    /// أنواع حركات المخزون
    /// </summary>
    public enum StockMovementType
    {
        /// <summary>
        /// رصيد افتتاحي
        /// </summary>
        OpeningBalance = 1,

        /// <summary>
        /// شراء
        /// </summary>
        Purchase = 2,

        /// <summary>
        /// بيع
        /// </summary>
        Sale = 3,

        /// <summary>
        /// مرتجع مبيعات
        /// </summary>
        SalesReturn = 4,

        /// <summary>
        /// مرتجع مشتريات
        /// </summary>
        PurchaseReturn = 5,

        /// <summary>
        /// تحويل داخل
        /// </summary>
        TransferIn = 6,

        /// <summary>
        /// تحويل خارج
        /// </summary>
        TransferOut = 7,

        /// <summary>
        /// تسوية جرد موجب
        /// </summary>
        StockAdjustmentPositive = 8,

        /// <summary>
        /// تسوية جرد سالب
        /// </summary>
        StockAdjustmentNegative = 9,

        /// <summary>
        /// تالف
        /// </summary>
        Damaged = 10,

        /// <summary>
        /// مفقود
        /// </summary>
        Lost = 11,

        /// <summary>
        /// منتهي الصلاحية
        /// </summary>
        Expired = 12,

        /// <summary>
        /// إنتاج
        /// </summary>
        Production = 13,

        /// <summary>
        /// استهلاك إنتاج
        /// </summary>
        ProductionConsumption = 14
    }

    /// <summary>
    /// أنواع مراجع حركات المخزون
    /// </summary>
    public enum StockReferenceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        SalesInvoice = 1,

        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        PurchaseInvoice = 2,

        /// <summary>
        /// مرتجع مبيعات
        /// </summary>
        SalesReturn = 3,

        /// <summary>
        /// مرتجع مشتريات
        /// </summary>
        PurchaseReturn = 4,

        /// <summary>
        /// أمر تحويل
        /// </summary>
        TransferOrder = 5,

        /// <summary>
        /// جرد مخزون
        /// </summary>
        StockTaking = 6,

        /// <summary>
        /// تسوية مخزون
        /// </summary>
        StockAdjustment = 7,

        /// <summary>
        /// أمر إنتاج
        /// </summary>
        ProductionOrder = 8
    }
}
