/* Terra Retail ERP - Professional Dashboard Styles */

.dashboard-container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* ===== PAGE HEADER ===== */
.page-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
  color: white;
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);
  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 var(--spacing-sm) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
  }

  .date-time {
    text-align: left;
    
    .current-date {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: var(--spacing-xs);
    }

    .current-time {
      font-size: 2rem;
      font-weight: 700;
      font-family: 'Courier New', monospace;
    }
  }
}

/* ===== STATS GRID ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
  padding: 0 var(--spacing-sm);
  box-sizing: border-box;
}

.stat-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  min-height: 120px;
  box-sizing: border-box;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
  }

  &.loading {
    opacity: 0.7;
    pointer-events: none;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: shimmer 1.5s infinite;
    }
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: white;
    }
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
  }

  .stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
  }

  .stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 600;

    &.positive {
      color: var(--success-600);
    }

    &.negative {
      color: var(--error-600);
    }

    &.neutral {
      color: var(--gray-500);
    }

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }

  .stat-chart {
    width: 80px;
    height: 40px;
    flex-shrink: 0;
  }

  .mini-chart {
    width: 100%;
    height: 100%;
    border-radius: var(--radius-md);
    opacity: 0.8;
  }

  // Card-specific styles
  &.sales-card {
    .stat-icon {
      background: linear-gradient(135deg, var(--success-500), var(--success-600));
    }
    .sales-chart {
      background: linear-gradient(135deg, var(--success-100), var(--success-200));
    }
  }

  &.orders-card {
    .stat-icon {
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    }
    .orders-chart {
      background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    }
  }

  &.customers-card {
    .stat-icon {
      background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
    }
    .customers-chart {
      background: linear-gradient(135deg, var(--secondary-100), var(--secondary-200));
    }
  }

  &.products-card {
    .stat-icon {
      background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    }
    .products-chart {
      background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
    }
  }
}

/* ===== CONTENT GRID ===== */
.content-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-2xl);
  grid-template-areas:
    "sales sales sales sales orders orders orders products products products products products"
    "status status status actions actions actions notifications notifications notifications notifications notifications notifications";
  padding: 0 var(--spacing-sm);
  box-sizing: border-box;
}

/* ===== CARDS ===== */
.chart-card,
.data-card,
.status-card,
.actions-card,
.notifications-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-2xl);
    border-bottom: 1px solid var(--gray-100);
    background: var(--gray-50);
    box-sizing: border-box;

    h3 {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
    }

    .card-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .card-content {
    padding: var(--spacing-2xl);
    box-sizing: border-box;
  }
}

.sales-overview {
  grid-area: sales;

  .chart-container {
    height: 300px;
    position: relative;
  }
}

.recent-orders {
  grid-area: orders;

  .orders-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);

    &:last-child {
      border-bottom: none;
    }

    .order-info {
      flex: 1;

      .order-number {
        font-weight: 600;
        color: var(--primary-600);
        margin-bottom: var(--spacing-xs);
      }

      .order-customer {
        color: var(--gray-700);
        margin-bottom: var(--spacing-xs);
      }

      .order-time {
        font-size: 0.875rem;
        color: var(--gray-500);
      }
    }

    .order-amount {
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 var(--spacing-lg);
    }

    .order-status {
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--radius-md);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;

      &.pending {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.completed {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.cancelled {
        background: var(--error-100);
        color: var(--error-700);
      }
    }
  }
}

.top-products {
  grid-area: products;

  .products-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .product-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);

    &:last-child {
      border-bottom: none;
    }

    .product-rank {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: var(--primary-100);
      color: var(--primary-700);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      font-size: 0.875rem;
    }

    .product-image {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-md);
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .product-info {
      flex: 1;

      .product-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--spacing-xs);
      }

      .product-category {
        font-size: 0.875rem;
        color: var(--gray-500);
      }
    }

    .product-sales {
      text-align: left;

      .sales-count {
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: var(--spacing-xs);
      }

      .sales-amount {
        font-size: 0.875rem;
        color: var(--success-600);
        font-weight: 600;
      }
    }
  }
}

.system-status {
  grid-area: status;

  .status-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .status-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;

        &.connected {
          color: var(--success-600);
        }

        &.disconnected {
          color: var(--error-600);
        }

        &.warning {
          color: var(--warning-600);
        }
      }
    }

    .status-info {
      flex: 1;

      .status-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--spacing-xs);
      }

      .status-value {
        font-size: 0.875rem;
        color: var(--gray-600);
      }
    }
  }

  .status-indicator {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;

    &.healthy {
      background: var(--success-100);
      color: var(--success-700);
    }

    &.warning {
      background: var(--warning-100);
      color: var(--warning-700);
    }

    &.error {
      background: var(--error-100);
      color: var(--error-700);
    }
  }
}

.quick-actions {
  grid-area: actions;

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .action-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: var(--spacing-sm) !important;
    padding: var(--spacing-lg) !important;
    height: auto !important;
    min-height: 80px !important;
    border-radius: var(--radius-lg) !important;

    mat-icon {
      font-size: 1.5rem !important;
      width: 1.5rem !important;
      height: 1.5rem !important;
    }

    span {
      font-size: 0.875rem !important;
      text-align: center !important;
    }
  }
}

.notifications-card {
  grid-area: notifications;

  .notifications-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);

    &:last-child {
      border-bottom: none;
    }

    .notification-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
        color: white;
      }

      &.info {
        background: var(--primary-500);
      }

      &.warning {
        background: var(--warning-500);
      }

      &.error {
        background: var(--error-500);
      }

      &.success {
        background: var(--success-500);
      }
    }

    .notification-content {
      flex: 1;

      .notification-title {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--spacing-xs);
      }

      .notification-message {
        color: var(--gray-700);
        margin-bottom: var(--spacing-xs);
        font-size: 0.875rem;
      }

      .notification-time {
        font-size: 0.75rem;
        color: var(--gray-500);
      }
    }

    .notification-action {
      color: var(--gray-400) !important;
    }
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: var(--spacing-lg);
    color: var(--gray-600);
    font-weight: 500;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
  .content-grid {
    grid-template-areas:
      "sales sales sales sales sales sales orders orders orders orders orders orders"
      "products products products products products products status status status status status status"
      "actions actions actions actions actions actions notifications notifications notifications notifications notifications notifications";
  }
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
  }

  .content-grid {
    grid-template-areas:
      "sales sales sales sales sales sales sales sales sales sales sales sales"
      "orders orders orders orders orders orders products products products products products products"
      "status status status status actions actions actions actions notifications notifications notifications notifications";
  }
}

@media (max-width: 992px) {
  .content-grid {
    grid-template-columns: 1fr;
    grid-template-areas:
      "sales"
      "orders"
      "products"
      "status"
      "actions"
      "notifications";
    gap: var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0;
  }

  .page-header {
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);
    padding: var(--spacing-xl);

    .header-content {
      flex-direction: column;
      text-align: center;
      gap: var(--spacing-lg);
    }

    .page-title {
      font-size: 2rem;
    }

    .date-time {
      text-align: center;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    padding: 0;
  }

  .stat-card {
    padding: var(--spacing-xl);
    min-height: 100px;
  }

  .content-grid {
    gap: var(--spacing-lg);
    padding: 0;
  }

  .card-header {
    padding: var(--spacing-lg) var(--spacing-xl) !important;
  }

  .card-content {
    padding: var(--spacing-xl) !important;
  }

  .quick-actions .actions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-lg);
    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-lg);
  }

  .stats-grid {
    gap: var(--spacing-md);
  }

  .stat-card {
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
    min-height: 80px;
  }

  .content-grid {
    gap: var(--spacing-md);
  }

  .card-header {
    padding: var(--spacing-md) var(--spacing-lg) !important;
  }

  .card-content {
    padding: var(--spacing-lg) !important;
  }
}


