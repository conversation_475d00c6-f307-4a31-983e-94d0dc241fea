{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, DOCUMENT, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nconst _c1 = [\"bodyWrapper\"];\nconst _c2 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c3 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanel_ng_template_7_Template(rf, ctx) {}\nconst _c4 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c5 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 2);\n    i0.ɵɵelement(2, \"path\", 3);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst MAT_ACCORDION = /*#__PURE__*/new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = /*#__PURE__*/new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nlet MatExpansionPanelContent = /*#__PURE__*/(() => {\n  class MatExpansionPanelContent {\n    _template = inject(TemplateRef);\n    _expansionPanel = inject(MAT_EXPANSION_PANEL, {\n      optional: true\n    });\n    constructor() {}\n    static ɵfac = function MatExpansionPanelContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelContent,\n      selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n    });\n  }\n  return MatExpansionPanelContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nlet MatExpansionPanel = /*#__PURE__*/(() => {\n  class MatExpansionPanel extends CdkAccordionItem {\n    _viewContainerRef = inject(ViewContainerRef);\n    _animationsDisabled = _animationsDisabled();\n    _document = inject(DOCUMENT);\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n      return this._hideToggle || this.accordion && this.accordion.hideToggle;\n    }\n    set hideToggle(value) {\n      this._hideToggle = value;\n    }\n    _hideToggle = false;\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n      return this._togglePosition || this.accordion && this.accordion.togglePosition;\n    }\n    set togglePosition(value) {\n      this._togglePosition = value;\n    }\n    _togglePosition;\n    /** An event emitted after the body's expansion animation happens. */\n    afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    _inputChanges = new Subject();\n    /** Optionally defined accordion the expansion panel belongs to. */\n    accordion = inject(MAT_ACCORDION, {\n      optional: true,\n      skipSelf: true\n    });\n    /** Content that will be rendered lazily. */\n    _lazyContent;\n    /** Element containing the panel's user-provided content. */\n    _body;\n    /** Element wrapping the panel body. */\n    _bodyWrapper;\n    /** Portal holding the user's content. */\n    _portal;\n    /** ID for the associated header element. Used for a11y labelling. */\n    _headerId = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n    constructor() {\n      super();\n      const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n      if (defaultOptions) {\n        this.hideToggle = defaultOptions.hideToggle;\n      }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n      if (this.accordion) {\n        return this.expanded && this.accordion.displayMode === 'default';\n      }\n      return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n      return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n      this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n      this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n      this.expanded = true;\n    }\n    ngAfterContentInit() {\n      if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n        // Render the content as soon as the panel becomes open.\n        this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        });\n      }\n      this._setupAnimationEvents();\n    }\n    ngOnChanges(changes) {\n      this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._cleanupTransitionEnd?.();\n      this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n      if (this._body) {\n        const focusedElement = this._document.activeElement;\n        const bodyElement = this._body.nativeElement;\n        return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n      }\n      return false;\n    }\n    _transitionEndListener = ({\n      target,\n      propertyName\n    }) => {\n      if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n        this._ngZone.run(() => {\n          if (this.expanded) {\n            this.afterExpand.emit();\n          } else {\n            this.afterCollapse.emit();\n          }\n        });\n      }\n    };\n    _setupAnimationEvents() {\n      // This method is defined separately, because we need to\n      // disable this logic in some internal components.\n      this._ngZone.runOutsideAngular(() => {\n        if (this._animationsDisabled) {\n          this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n          this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n        } else {\n          setTimeout(() => {\n            const element = this._elementRef.nativeElement;\n            this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n            element.classList.add('mat-expansion-panel-animations-enabled');\n          }, 200);\n        }\n      });\n    }\n    static ɵfac = function MatExpansionPanel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanel)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanel,\n      selectors: [[\"mat-expansion-panel\"]],\n      contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n        }\n      },\n      viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._bodyWrapper = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-expansion-panel\"],\n      hostVars: 4,\n      hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        togglePosition: \"togglePosition\"\n      },\n      outputs: {\n        afterExpand: \"afterExpand\",\n        afterCollapse: \"afterCollapse\"\n      },\n      exportAs: [\"matExpansionPanel\"],\n      features: [i0.ɵɵProvidersFeature([\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 4,\n      consts: [[\"bodyWrapper\", \"\"], [\"body\", \"\"], [1, \"mat-expansion-panel-content-wrapper\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n      template: function MatExpansionPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 2, 0)(3, \"div\", 3, 1)(5, \"div\", 4);\n          i0.ɵɵprojection(6, 1);\n          i0.ɵɵtemplate(7, MatExpansionPanel_ng_template_7_Template, 0, 0, \"ng-template\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(8, 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"inert\", ctx.expanded ? null : \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatExpansionPanel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nlet MatExpansionPanelActionRow = /*#__PURE__*/(() => {\n  class MatExpansionPanelActionRow {\n    static ɵfac = function MatExpansionPanelActionRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelActionRow)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelActionRow,\n      selectors: [[\"mat-action-row\"]],\n      hostAttrs: [1, \"mat-action-row\"]\n    });\n  }\n  return MatExpansionPanelActionRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nlet MatExpansionPanelHeader = /*#__PURE__*/(() => {\n  class MatExpansionPanelHeader {\n    panel = inject(MatExpansionPanel, {\n      host: true\n    });\n    _element = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parentChangeSubscription = Subscription.EMPTY;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const panel = this.panel;\n      const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n      this.tabIndex = parseInt(tabIndex || '') || 0;\n      // Since the toggle state depends on an @Input on the panel, we\n      // need to subscribe and trigger change detection manually.\n      this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n        return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n      }))).subscribe(() => this._changeDetectorRef.markForCheck());\n      // Avoids focus being lost if the panel contained the focused element and was closed.\n      panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n      if (defaultOptions) {\n        this.expandedHeight = defaultOptions.expandedHeight;\n        this.collapsedHeight = defaultOptions.collapsedHeight;\n      }\n    }\n    /** Height of the header while the panel is expanded. */\n    expandedHeight;\n    /** Height of the header while the panel is collapsed. */\n    collapsedHeight;\n    /** Tab index of the header. */\n    tabIndex = 0;\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n      return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n      if (!this.disabled) {\n        this.panel.toggle();\n      }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n      return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n      return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n      return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n      return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n      return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n      const isExpanded = this._isExpanded();\n      if (isExpanded && this.expandedHeight) {\n        return this.expandedHeight;\n      } else if (!isExpanded && this.collapsedHeight) {\n        return this.collapsedHeight;\n      }\n      return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n      switch (event.keyCode) {\n        // Toggle for space and enter keys.\n        case SPACE:\n        case ENTER:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n            this._toggle();\n          }\n          break;\n        default:\n          if (this.panel.accordion) {\n            this.panel.accordion._handleHeaderKeydown(event);\n          }\n          return;\n      }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._element).subscribe(origin => {\n        if (origin && this.panel.accordion) {\n          this.panel.accordion._handleHeaderFocus(this);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._parentChangeSubscription.unsubscribe();\n      this._focusMonitor.stopMonitoring(this._element);\n    }\n    static ɵfac = function MatExpansionPanelHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelHeader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanelHeader,\n      selectors: [[\"mat-expansion-panel-header\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n      hostVars: 13,\n      hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n            return ctx._toggle();\n          })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n          i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n          i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\");\n        }\n      },\n      inputs: {\n        expandedHeight: \"expandedHeight\",\n        collapsedHeight: \"collapsedHeight\",\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      ngContentSelectors: _c5,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n      template: function MatExpansionPanelHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c4);\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(4, MatExpansionPanelHeader_Conditional_4_Template, 3, 0, \"span\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx._showToggle() ? 4 : -1);\n        }\n      },\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatExpansionPanelHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nlet MatExpansionPanelDescription = /*#__PURE__*/(() => {\n  class MatExpansionPanelDescription {\n    static ɵfac = function MatExpansionPanelDescription_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelDescription)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelDescription,\n      selectors: [[\"mat-panel-description\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n    });\n  }\n  return MatExpansionPanelDescription;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nlet MatExpansionPanelTitle = /*#__PURE__*/(() => {\n  class MatExpansionPanelTitle {\n    static ɵfac = function MatExpansionPanelTitle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelTitle)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelTitle,\n      selectors: [[\"mat-panel-title\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n    });\n  }\n  return MatExpansionPanelTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nlet MatAccordion = /*#__PURE__*/(() => {\n  class MatAccordion extends CdkAccordion {\n    _keyManager;\n    /** Headers belonging to this accordion. */\n    _ownHeaders = new QueryList();\n    /** All headers inside the accordion. Includes headers inside nested accordions. */\n    _headers;\n    /** Whether the expansion indicator should be hidden. */\n    hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    displayMode = 'default';\n    /** The position of the expansion indicator. */\n    togglePosition = 'after';\n    ngAfterContentInit() {\n      this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n        this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n        this._ownHeaders.notifyOnChanges();\n      });\n      this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n      this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n      this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._keyManager?.destroy();\n      this._ownHeaders.destroy();\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatAccordion_BaseFactory;\n      return function MatAccordion_Factory(__ngFactoryType__) {\n        return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(__ngFactoryType__ || MatAccordion);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAccordion,\n      selectors: [[\"mat-accordion\"]],\n      contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-accordion\"],\n      hostVars: 2,\n      hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        displayMode: \"displayMode\",\n        togglePosition: \"togglePosition\"\n      },\n      exportAs: [\"matAccordion\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatAccordion;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatExpansionModule = /*#__PURE__*/(() => {\n  class MatExpansionModule {\n    static ɵfac = function MatExpansionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatExpansionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n    });\n  }\n  return MatExpansionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n  // Represents:\n  // trigger('indicatorRotate', [\n  //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n  //   state('expanded', style({transform: 'rotate(180deg)'})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: {\n    type: 7,\n    name: 'indicatorRotate',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(0deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(180deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('bodyExpansion', [\n  //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n  //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  //   // that have a `visibility` of their own (see #27436).\n  //   state('expanded', style({height: '*', visibility: ''})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: {\n    type: 7,\n    name: 'bodyExpansion',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '0px',\n          'visibility': 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '*',\n          'visibility': ''\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "TemplateRef", "Directive", "ViewContainerRef", "DOCUMENT", "NgZone", "ElementRef", "Renderer2", "EventEmitter", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "ContentChild", "ViewChild", "ChangeDetectorRef", "HostAttributeToken", "numberAttribute", "QueryList", "ContentChildren", "NgModule", "CdkAccordionItem", "CdkAccordion", "CdkAccordionModule", "TemplatePortal", "CdkPortalOutlet", "PortalModule", "_IdGenerator", "FocusMonitor", "FocusKeyManager", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "Subject", "Subscription", "EMPTY", "merge", "UniqueSelectionDispatcher", "_", "_animationsDisabled", "_CdkPrivateStyleLoader", "_StructuralStylesLoader", "M", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "MatExpansionPanel_ng_template_7_Template", "rf", "ctx", "_c4", "_c5", "MatExpansionPanelHeader_Conditional_4_Template", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "MAT_ACCORDION", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "_template", "_expansionPanel", "optional", "constructor", "ɵfac", "MatExpansionPanelContent_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "_viewContainerRef", "_document", "_ngZone", "_elementRef", "_renderer", "_cleanupTransitionEnd", "hideToggle", "_hideToggle", "accordion", "value", "togglePosition", "_togglePosition", "afterExpand", "afterCollapse", "_inputChanges", "skipSelf", "_lazyContent", "_body", "_bodyWrapper", "_portal", "_headerId", "getId", "defaultOptions", "_expansionDispatcher", "_hasSpacing", "expanded", "displayMode", "_getExpandedState", "toggle", "close", "open", "ngAfterContentInit", "opened", "pipe", "subscribe", "_setupAnimationEvents", "ngOnChanges", "changes", "next", "ngOnDestroy", "complete", "_containsFocus", "focusedElement", "activeElement", "bodyElement", "nativeElement", "contains", "_transitionEndListener", "target", "propertyName", "run", "emit", "runOutsideAngular", "closed", "setTimeout", "element", "listen", "classList", "add", "MatExpansionPanel_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatExpansionPanel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatExpansionPanel_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatExpansionPanel_HostBindings", "ɵɵclassProp", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useValue", "undefined", "useExisting", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatExpansionPanel_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵtemplate", "ɵɵadvance", "ɵɵattribute", "ɵɵproperty", "id", "dependencies", "styles", "encapsulation", "changeDetection", "MatExpansionPanelActionRow", "MatExpansionPanelActionRow_Factory", "MatExpansionPanelHeader", "panel", "host", "_element", "_focusMonitor", "_changeDetectorRef", "_parentChangeSubscription", "load", "tabIndex", "accordionHideToggleChange", "_stateChanges", "parseInt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusVia", "expandedHeight", "collapsedHeight", "disabled", "_toggle", "_isExpanded", "_getPanelId", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "event", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "unsubscribe", "stopMonitoring", "MatExpansionPanelHeader_Factory", "MatExpansionPanelHeader_HostBindings", "ɵɵlistener", "MatExpansionPanelHeader_click_HostBindingHandler", "MatExpansionPanelHeader_keydown_HostBindingHandler", "$event", "ɵɵstyleProp", "MatExpansionPanelHeader_Template", "ɵɵconditionalCreate", "ɵɵconditional", "MatExpansionPanelDescription", "MatExpansionPanelDescription_Factory", "MatExpansionPanelTitle", "MatExpansionPanelTitle_Factory", "Mat<PERSON><PERSON>rdi<PERSON>", "_keyManager", "_ownHeaders", "_headers", "headers", "reset", "header", "notifyOn<PERSON><PERSON>es", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "destroy", "ɵMatAccordion_BaseFactory", "MatAccordion_Factory", "ɵɵgetInheritedFactory", "MatAccordion_ContentQueries", "MatAccordion_HostBindings", "multi", "MatExpansionModule", "MatExpansionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "name", "definitions", "transform", "offset", "expr", "animation", "timings", "bodyExpansion"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/expansion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, DOCUMENT, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    _template = inject(TemplateRef);\n    _expansionPanel = inject(MAT_EXPANSION_PANEL, { optional: true });\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatExpansionPanelContent, isStandalone: true, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    _viewContainerRef = inject(ViewContainerRef);\n    _animationsDisabled = _animationsDisabled();\n    _document = inject(DOCUMENT);\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = value;\n    }\n    _hideToggle = false;\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    _togglePosition;\n    /** An event emitted after the body's expansion animation happens. */\n    afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    _inputChanges = new Subject();\n    /** Optionally defined accordion the expansion panel belongs to. */\n    accordion = inject(MAT_ACCORDION, { optional: true, skipSelf: true });\n    /** Content that will be rendered lazily. */\n    _lazyContent;\n    /** Element containing the panel's user-provided content. */\n    _body;\n    /** Element wrapping the panel body. */\n    _bodyWrapper;\n    /** Portal holding the user's content. */\n    _portal;\n    /** ID for the associated header element. Used for a11y labelling. */\n    _headerId = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n    constructor() {\n        super();\n        const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, { optional: true });\n        this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n        this._setupAnimationEvents();\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._cleanupTransitionEnd?.();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n    _transitionEndListener = ({ target, propertyName }) => {\n        if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n            this._ngZone.run(() => {\n                if (this.expanded) {\n                    this.afterExpand.emit();\n                }\n                else {\n                    this.afterCollapse.emit();\n                }\n            });\n        }\n    };\n    _setupAnimationEvents() {\n        // This method is defined separately, because we need to\n        // disable this logic in some internal components.\n        this._ngZone.runOutsideAngular(() => {\n            if (this._animationsDisabled) {\n                this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n                this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n            }\n            else {\n                setTimeout(() => {\n                    const element = this._elementRef.nativeElement;\n                    this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n                    element.classList.add('mat-expansion-panel-animations-enabled');\n                }, 200);\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatExpansionPanel, isStandalone: true, selector: \"mat-expansion-panel\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], togglePosition: \"togglePosition\" }, outputs: { afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n            // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n            // to the same accordion.\n            { provide: MAT_ACCORDION, useValue: undefined },\n            { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n        ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }, { propertyName: \"_bodyWrapper\", first: true, predicate: [\"bodyWrapper\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, imports: [CdkPortalOutlet], template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }], _bodyWrapper: [{\n                type: ViewChild,\n                args: ['bodyWrapper']\n            }] } });\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatExpansionPanelActionRow, isStandalone: true, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                }]\n        }] });\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n    panel = inject(MatExpansionPanel, { host: true });\n    _element = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parentChangeSubscription = Subscription.EMPTY;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const panel = this.panel;\n        const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, { optional: true });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /** Height of the header while the panel is expanded. */\n    expandedHeight;\n    /** Height of the header while the panel is collapsed. */\n    collapsedHeight;\n    /** Tab index of the header. */\n    tabIndex = 0;\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatExpansionPanelHeader, isStandalone: true, selector: \"mat-expansion-panel-header\", inputs: { expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\", tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatExpansionPanelDescription, isStandalone: true, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatExpansionPanelTitle, isStandalone: true, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                }]\n        }] });\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    _keyManager;\n    /** Headers belonging to this accordion. */\n    _ownHeaders = new QueryList();\n    /** All headers inside the accordion. Includes headers inside nested accordions. */\n    _headers;\n    /** Whether the expansion indicator should be hidden. */\n    hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    displayMode = 'default';\n    /** The position of the expansion indicator. */\n    togglePosition = 'after';\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._keyManager?.destroy();\n        this._ownHeaders.destroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatAccordion, isStandalone: true, selector: \"mat-accordion\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n            {\n                provide: MAT_ACCORDION,\n                useExisting: MatAccordion,\n            },\n        ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\nclass MatExpansionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule,\n            MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent], exports: [MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CdkAccordionModule,\n                        PortalModule,\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n    // Represents:\n    // trigger('indicatorRotate', [\n    //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n    //   state('expanded', style({transform: 'rotate(180deg)'})),\n    //   transition(\n    //     'expanded <=> collapsed, void => collapsed',\n    //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    //   ),\n    // ])\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: {\n        type: 7,\n        name: 'indicatorRotate',\n        definitions: [\n            {\n                type: 0,\n                name: 'collapsed, void',\n                styles: { type: 6, styles: { transform: 'rotate(0deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'expanded',\n                styles: { type: 6, styles: { transform: 'rotate(180deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'expanded <=> collapsed, void => collapsed',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('bodyExpansion', [\n    //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n    //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n    //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n    //   // that have a `visibility` of their own (see #27436).\n    //   state('expanded', style({height: '*', visibility: ''})),\n    //   transition(\n    //     'expanded <=> collapsed, void => collapsed',\n    //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    //   ),\n    // ])\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: {\n        type: 7,\n        name: 'bodyExpansion',\n        definitions: [\n            {\n                type: 0,\n                name: 'collapsed, void',\n                styles: { type: 6, styles: { 'height': '0px', 'visibility': 'hidden' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'expanded',\n                styles: { type: 6, styles: { 'height': '*', 'visibility': '' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'expanded <=> collapsed, void => collapsed',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACtW,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3F,SAASC,cAAc,EAAEC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,QAAQ,mBAAmB;AAC/E,SAASC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AACxD,SAASC,KAAK,EAAEC,cAAc,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC1D,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASF,CAAC,IAAIG,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+CAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoB6FxD,EAAE,CAAA6D,cAAA,aA6U8rC,CAAC;IA7UjsC7D,EAAE,CAAA8D,cAAA;IAAF9D,EAAE,CAAA6D,cAAA,YA6U80C,CAAC;IA7Uj1C7D,EAAE,CAAA+D,SAAA,aA6Uy5C,CAAC;IA7U55C/D,EAAE,CAAAgE,YAAA,CA6Uq6C,CAAC,CAAU,CAAC;EAAA;AAAA;AA7VhhD,MAAMC,aAAa,gBAAG,IAAIhE,cAAc,CAAC,eAAe,CAAC;;AAEzD;AACA;AACA;AACA;AACA,MAAMiE,mBAAmB,gBAAG,IAAIjE,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AAHA,IAIMkE,wBAAwB;EAA9B,MAAMA,wBAAwB,CAAC;IAC3BC,SAAS,GAAGlE,MAAM,CAACC,WAAW,CAAC;IAC/BkE,eAAe,GAAGnE,MAAM,CAACgE,mBAAmB,EAAE;MAAEI,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjEC,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOC,IAAI,YAAAC,iCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFP,wBAAwB;IAAA;IAC3H,OAAOQ,IAAI,kBAD8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EACJV,wBAAwB;MAAAW,SAAA;IAAA;EACnH;EAAC,OANKX,wBAAwB;AAAA;AAO9B;EAAA,QAAAY,SAAA,oBAAAA,SAAA;AAAA;;AAOA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,gBAAG,IAAI/E,cAAc,CAAC,qCAAqC,CAAC;AACrG;AACA;AACA;AACA;AAHA,IAIMgF,iBAAiB;EAAvB,MAAMA,iBAAiB,SAASxD,gBAAgB,CAAC;IAC7CyD,iBAAiB,GAAGhF,MAAM,CAACG,gBAAgB,CAAC;IAC5CyC,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3CqC,SAAS,GAAGjF,MAAM,CAACI,QAAQ,CAAC;IAC5B8E,OAAO,GAAGlF,MAAM,CAACK,MAAM,CAAC;IACxB8E,WAAW,GAAGnF,MAAM,CAACM,UAAU,CAAC;IAChC8E,SAAS,GAAGpF,MAAM,CAACO,SAAS,CAAC;IAC7B8E,qBAAqB;IACrB;IACA,IAAIC,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,WAAW,IAAK,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACF,UAAW;IAC5E;IACA,IAAIA,UAAUA,CAACG,KAAK,EAAE;MAClB,IAAI,CAACF,WAAW,GAAGE,KAAK;IAC5B;IACAF,WAAW,GAAG,KAAK;IACnB;IACA,IAAIG,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACC,eAAe,IAAK,IAAI,CAACH,SAAS,IAAI,IAAI,CAACA,SAAS,CAACE,cAAe;IACpF;IACA,IAAIA,cAAcA,CAACD,KAAK,EAAE;MACtB,IAAI,CAACE,eAAe,GAAGF,KAAK;IAChC;IACAE,eAAe;IACf;IACAC,WAAW,GAAG,IAAIpF,YAAY,CAAC,CAAC;IAChC;IACAqF,aAAa,GAAG,IAAIrF,YAAY,CAAC,CAAC;IAClC;IACAsF,aAAa,GAAG,IAAIxD,OAAO,CAAC,CAAC;IAC7B;IACAkD,SAAS,GAAGxF,MAAM,CAAC+D,aAAa,EAAE;MAAEK,QAAQ,EAAE,IAAI;MAAE2B,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrE;IACAC,YAAY;IACZ;IACAC,KAAK;IACL;IACAC,YAAY;IACZ;IACAC,OAAO;IACP;IACAC,SAAS,GAAGpG,MAAM,CAAC6B,YAAY,CAAC,CAACwE,KAAK,CAAC,6BAA6B,CAAC;IACrEhC,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,CAAC;MACP,MAAMiC,cAAc,GAAGtG,MAAM,CAAC8E,mCAAmC,EAAE;QAAEV,QAAQ,EAAE;MAAK,CAAC,CAAC;MACtF,IAAI,CAACmC,oBAAoB,GAAGvG,MAAM,CAAC0C,yBAAyB,CAAC;MAC7D,IAAI4D,cAAc,EAAE;QAChB,IAAI,CAAChB,UAAU,GAAGgB,cAAc,CAAChB,UAAU;MAC/C;IACJ;IACA;IACAkB,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAAChB,SAAS,EAAE;QAChB,OAAO,IAAI,CAACiB,QAAQ,IAAI,IAAI,CAACjB,SAAS,CAACkB,WAAW,KAAK,SAAS;MACpE;MACA,OAAO,KAAK;IAChB;IACA;IACAC,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACF,QAAQ,GAAG,UAAU,GAAG,WAAW;IACnD;IACA;IACAG,MAAMA,CAAA,EAAG;MACL,IAAI,CAACH,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAClC;IACA;IACAI,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACJ,QAAQ,GAAG,KAAK;IACzB;IACA;IACAK,IAAIA,CAAA,EAAG;MACH,IAAI,CAACL,QAAQ,GAAG,IAAI;IACxB;IACAM,kBAAkBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACf,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC7B,eAAe,KAAK,IAAI,EAAE;QACjE;QACA,IAAI,CAAC6C,MAAM,CACNC,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC,EAAEC,MAAM,CAAC,MAAM,IAAI,CAACwE,QAAQ,IAAI,CAAC,IAAI,CAACN,OAAO,CAAC,EAAEjE,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5EgF,SAAS,CAAC,MAAM;UACjB,IAAI,CAACf,OAAO,GAAG,IAAIzE,cAAc,CAAC,IAAI,CAACsE,YAAY,CAAC9B,SAAS,EAAE,IAAI,CAACc,iBAAiB,CAAC;QAC1F,CAAC,CAAC;MACN;MACA,IAAI,CAACmC,qBAAqB,CAAC,CAAC;IAChC;IACAC,WAAWA,CAACC,OAAO,EAAE;MACjB,IAAI,CAACvB,aAAa,CAACwB,IAAI,CAACD,OAAO,CAAC;IACpC;IACAE,WAAWA,CAAA,EAAG;MACV,KAAK,CAACA,WAAW,CAAC,CAAC;MACnB,IAAI,CAAClC,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAACS,aAAa,CAAC0B,QAAQ,CAAC,CAAC;IACjC;IACA;IACAC,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACxB,KAAK,EAAE;QACZ,MAAMyB,cAAc,GAAG,IAAI,CAACzC,SAAS,CAAC0C,aAAa;QACnD,MAAMC,WAAW,GAAG,IAAI,CAAC3B,KAAK,CAAC4B,aAAa;QAC5C,OAAOH,cAAc,KAAKE,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAACJ,cAAc,CAAC;MACjF;MACA,OAAO,KAAK;IAChB;IACAK,sBAAsB,GAAGA,CAAC;MAAEC,MAAM;MAAEC;IAAa,CAAC,KAAK;MACnD,IAAID,MAAM,KAAK,IAAI,CAAC9B,YAAY,EAAE2B,aAAa,IAAII,YAAY,KAAK,oBAAoB,EAAE;QACtF,IAAI,CAAC/C,OAAO,CAACgD,GAAG,CAAC,MAAM;UACnB,IAAI,IAAI,CAACzB,QAAQ,EAAE;YACf,IAAI,CAACb,WAAW,CAACuC,IAAI,CAAC,CAAC;UAC3B,CAAC,MACI;YACD,IAAI,CAACtC,aAAa,CAACsC,IAAI,CAAC,CAAC;UAC7B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACDhB,qBAAqBA,CAAA,EAAG;MACpB;MACA;MACA,IAAI,CAACjC,OAAO,CAACkD,iBAAiB,CAAC,MAAM;QACjC,IAAI,IAAI,CAACxF,mBAAmB,EAAE;UAC1B,IAAI,CAACoE,MAAM,CAACE,SAAS,CAAC,MAAM,IAAI,CAAChC,OAAO,CAACgD,GAAG,CAAC,MAAM,IAAI,CAACtC,WAAW,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC5E,IAAI,CAACE,MAAM,CAACnB,SAAS,CAAC,MAAM,IAAI,CAAChC,OAAO,CAACgD,GAAG,CAAC,MAAM,IAAI,CAACrC,aAAa,CAACsC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,MACI;UACDG,UAAU,CAAC,MAAM;YACb,MAAMC,OAAO,GAAG,IAAI,CAACpD,WAAW,CAAC0C,aAAa;YAC9C,IAAI,CAACxC,qBAAqB,GAAG,IAAI,CAACD,SAAS,CAACoD,MAAM,CAACD,OAAO,EAAE,eAAe,EAAE,IAAI,CAACR,sBAAsB,CAAC;YACzGQ,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACnE,CAAC,EAAE,GAAG,CAAC;QACX;MACJ,CAAC,CAAC;IACN;IACA,OAAOpE,IAAI,YAAAqE,0BAAAnE,iBAAA;MAAA,YAAAA,iBAAA,IAAwFO,iBAAiB;IAAA;IACpH,OAAO6D,IAAI,kBAtJ8E9I,EAAE,CAAA+I,iBAAA;MAAAlE,IAAA,EAsJJI,iBAAiB;MAAAH,SAAA;MAAAkE,cAAA,WAAAC,iCAAAzF,EAAA,EAAAC,GAAA,EAAAyF,QAAA;QAAA,IAAA1F,EAAA;UAtJfxD,EAAE,CAAAmJ,cAAA,CAAAD,QAAA,EA2JlB/E,wBAAwB;QAAA;QAAA,IAAAX,EAAA;UAAA,IAAA4F,EAAA;UA3JRpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA7F,GAAA,CAAAyC,YAAA,GAAAkD,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,wBAAAjG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxD,EAAE,CAAA0J,WAAA,CAAAvG,GAAA;UAAFnD,EAAE,CAAA0J,WAAA,CAAAtG,GAAA;QAAA;QAAA,IAAAI,EAAA;UAAA,IAAA4F,EAAA;UAAFpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA7F,GAAA,CAAA0C,KAAA,GAAAiD,EAAA,CAAAG,KAAA;UAAFvJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA7F,GAAA,CAAA2C,YAAA,GAAAgD,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,+BAAAtG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxD,EAAE,CAAA+J,WAAA,iBAAAtG,GAAA,CAAAkD,QAsJY,CAAC,gCAAjBlD,GAAA,CAAAiD,WAAA,CAAY,CAAI,CAAC;QAAA;MAAA;MAAAsD,MAAA;QAAAxE,UAAA,kCAA0G7E,gBAAgB;QAAAiF,cAAA;MAAA;MAAAqE,OAAA;QAAAnE,WAAA;QAAAC,aAAA;MAAA;MAAAmE,QAAA;MAAAC,QAAA,GAtJzInK,EAAE,CAAAoK,kBAAA,CAsJ2Z;MAC9e;MACA;MACA;QAAEC,OAAO,EAAEpG,aAAa;QAAEqG,QAAQ,EAAEC;MAAU,CAAC,EAC/C;QAAEF,OAAO,EAAEnG,mBAAmB;QAAEsG,WAAW,EAAEvF;MAAkB,CAAC,CACnE,GA3JoFjF,EAAE,CAAAyK,0BAAA,EAAFzK,EAAE,CAAA0K,oBAAA;MAAAC,kBAAA,EAAArH,GAAA;MAAAsH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAxH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxD,EAAE,CAAAiL,eAAA,CAAA5H,GAAA;UAAFrD,EAAE,CAAAkL,YAAA,EA2J+X,CAAC;UA3JlYlL,EAAE,CAAA6D,cAAA,eA2Jue,CAAC,eAAqJ,CAAC,YAA6C,CAAC;UA3J9qB7D,EAAE,CAAAkL,YAAA,KA2J4sB,CAAC;UA3J/sBlL,EAAE,CAAAmL,UAAA,IAAA5H,wCAAA,wBA2J+vB,CAAC;UA3JlwBvD,EAAE,CAAAgE,YAAA,CA2JyxB,CAAC;UA3J5xBhE,EAAE,CAAAkL,YAAA,KA2Jk1B,CAAC;UA3Jr1BlL,EAAE,CAAAgE,YAAA,CA2J41B,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAR,EAAA;UA3Jv2BxD,EAAE,CAAAoL,SAAA,CA2Jyd,CAAC;UA3J5dpL,EAAE,CAAAqL,WAAA,UAAA5H,GAAA,CAAAkD,QAAA;UAAF3G,EAAE,CAAAoL,SAAA,EA2J8mB,CAAC;UA3JjnBpL,EAAE,CAAAsL,UAAA,OAAA7H,GAAA,CAAA8H,EA2J8mB,CAAC;UA3JjnBvL,EAAE,CAAAqL,WAAA,oBAAA5H,GAAA,CAAA6C,SAAA;UAAFtG,EAAE,CAAAoL,SAAA,EA2J8vB,CAAC;UA3JjwBpL,EAAE,CAAAsL,UAAA,oBAAA7H,GAAA,CAAA4C,OA2J8vB,CAAC;QAAA;MAAA;MAAAmF,YAAA,GAA87G3J,eAAe;MAAA4J,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAC3yI;EAAC,OAzIK1G,iBAAiB;AAAA;AA0IvB;EAAA,QAAAF,SAAA,oBAAAA,SAAA;AAAA;AA+BA;AACA;AACA;AAFA,IAGM6G,0BAA0B;EAAhC,MAAMA,0BAA0B,CAAC;IAC7B,OAAOpH,IAAI,YAAAqH,mCAAAnH,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkH,0BAA0B;IAAA;IAC7H,OAAOjH,IAAI,kBAjM8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAiMJ+G,0BAA0B;MAAA9G,SAAA;MAAA6E,SAAA;IAAA;EACrH;EAAC,OAHKiC,0BAA0B;AAAA;AAIhC;EAAA,QAAA7G,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA;AACA;AAFA,IAGM+G,uBAAuB;EAA7B,MAAMA,uBAAuB,CAAC;IAC1BC,KAAK,GAAG7L,MAAM,CAAC+E,iBAAiB,EAAE;MAAE+G,IAAI,EAAE;IAAK,CAAC,CAAC;IACjDC,QAAQ,GAAG/L,MAAM,CAACM,UAAU,CAAC;IAC7B0L,aAAa,GAAGhM,MAAM,CAAC8B,YAAY,CAAC;IACpCmK,kBAAkB,GAAGjM,MAAM,CAACiB,iBAAiB,CAAC;IAC9CiL,yBAAyB,GAAG3J,YAAY,CAACC,KAAK;IAC9C6B,WAAWA,CAAA,EAAG;MACVrE,MAAM,CAAC6C,sBAAsB,CAAC,CAACsJ,IAAI,CAACrJ,uBAAuB,CAAC;MAC5D,MAAM+I,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMvF,cAAc,GAAGtG,MAAM,CAAC8E,mCAAmC,EAAE;QAAEV,QAAQ,EAAE;MAAK,CAAC,CAAC;MACtF,MAAMgI,QAAQ,GAAGpM,MAAM,CAAC,IAAIkB,kBAAkB,CAAC,UAAU,CAAC,EAAE;QAAEkD,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC/E,MAAMiI,yBAAyB,GAAGR,KAAK,CAACrG,SAAS,GAC3CqG,KAAK,CAACrG,SAAS,CAAC8G,aAAa,CAACrF,IAAI,CAAChF,MAAM,CAACoF,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC7G7E,KAAK;MACX,IAAI,CAAC4J,QAAQ,GAAGG,QAAQ,CAACH,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;MAC7C;MACA;MACA,IAAI,CAACF,yBAAyB,GAAGzJ,KAAK,CAACoJ,KAAK,CAAC7E,MAAM,EAAE6E,KAAK,CAACxD,MAAM,EAAEgE,yBAAyB,EAAER,KAAK,CAAC/F,aAAa,CAACmB,IAAI,CAAChF,MAAM,CAACoF,OAAO,IAAI;QACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC;MACxF,CAAC,CAAC,CAAC,CAAC,CAACH,SAAS,CAAC,MAAM,IAAI,CAAC+E,kBAAkB,CAACO,YAAY,CAAC,CAAC,CAAC;MAC5D;MACAX,KAAK,CAACxD,MAAM,CACPpB,IAAI,CAAChF,MAAM,CAAC,MAAM4J,KAAK,CAACpE,cAAc,CAAC,CAAC,CAAC,CAAC,CAC1CP,SAAS,CAAC,MAAM,IAAI,CAAC8E,aAAa,CAACS,QAAQ,CAAC,IAAI,CAACV,QAAQ,EAAE,SAAS,CAAC,CAAC;MAC3E,IAAIzF,cAAc,EAAE;QAChB,IAAI,CAACoG,cAAc,GAAGpG,cAAc,CAACoG,cAAc;QACnD,IAAI,CAACC,eAAe,GAAGrG,cAAc,CAACqG,eAAe;MACzD;IACJ;IACA;IACAD,cAAc;IACd;IACAC,eAAe;IACf;IACAP,QAAQ,GAAG,CAAC;IACZ;AACJ;AACA;AACA;IACI,IAAIQ,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACf,KAAK,CAACe,QAAQ;IAC9B;IACA;IACAC,OAAOA,CAAA,EAAG;MACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;QAChB,IAAI,CAACf,KAAK,CAACjF,MAAM,CAAC,CAAC;MACvB;IACJ;IACA;IACAkG,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAACjB,KAAK,CAACpF,QAAQ;IAC9B;IACA;IACAE,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACkF,KAAK,CAAClF,iBAAiB,CAAC,CAAC;IACzC;IACA;IACAoG,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAAClB,KAAK,CAACR,EAAE;IACxB;IACA;IACA2B,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACnB,KAAK,CAACnG,cAAc;IACpC;IACA;IACAuH,WAAWA,CAAA,EAAG;MACV,OAAO,CAAC,IAAI,CAACpB,KAAK,CAACvG,UAAU,IAAI,CAAC,IAAI,CAACuG,KAAK,CAACe,QAAQ;IACzD;IACA;AACJ;AACA;AACA;IACIM,gBAAgBA,CAAA,EAAG;MACf,MAAMC,UAAU,GAAG,IAAI,CAACL,WAAW,CAAC,CAAC;MACrC,IAAIK,UAAU,IAAI,IAAI,CAACT,cAAc,EAAE;QACnC,OAAO,IAAI,CAACA,cAAc;MAC9B,CAAC,MACI,IAAI,CAACS,UAAU,IAAI,IAAI,CAACR,eAAe,EAAE;QAC1C,OAAO,IAAI,CAACA,eAAe;MAC/B;MACA,OAAO,IAAI;IACf;IACA;IACAS,QAAQA,CAACC,KAAK,EAAE;MACZ,QAAQA,KAAK,CAACC,OAAO;QACjB;QACA,KAAKjL,KAAK;QACV,KAAKF,KAAK;UACN,IAAI,CAACC,cAAc,CAACiL,KAAK,CAAC,EAAE;YACxBA,KAAK,CAACE,cAAc,CAAC,CAAC;YACtB,IAAI,CAACV,OAAO,CAAC,CAAC;UAClB;UACA;QACJ;UACI,IAAI,IAAI,CAAChB,KAAK,CAACrG,SAAS,EAAE;YACtB,IAAI,CAACqG,KAAK,CAACrG,SAAS,CAACgI,oBAAoB,CAACH,KAAK,CAAC;UACpD;UACA;MACR;IACJ;IACA;AACJ;AACA;AACA;AACA;IACII,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;MACnB,IAAID,MAAM,EAAE;QACR,IAAI,CAAC1B,aAAa,CAACS,QAAQ,CAAC,IAAI,CAACV,QAAQ,EAAE2B,MAAM,EAAEC,OAAO,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAAC5B,QAAQ,CAAClE,aAAa,CAAC4F,KAAK,CAACE,OAAO,CAAC;MAC9C;IACJ;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC5B,aAAa,CAAC6B,OAAO,CAAC,IAAI,CAAC9B,QAAQ,CAAC,CAAC7E,SAAS,CAACwG,MAAM,IAAI;QAC1D,IAAIA,MAAM,IAAI,IAAI,CAAC7B,KAAK,CAACrG,SAAS,EAAE;UAChC,IAAI,CAACqG,KAAK,CAACrG,SAAS,CAACsI,kBAAkB,CAAC,IAAI,CAAC;QACjD;MACJ,CAAC,CAAC;IACN;IACAvG,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2E,yBAAyB,CAAC6B,WAAW,CAAC,CAAC;MAC5C,IAAI,CAAC/B,aAAa,CAACgC,cAAc,CAAC,IAAI,CAACjC,QAAQ,CAAC;IACpD;IACA,OAAOzH,IAAI,YAAA2J,gCAAAzJ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoH,uBAAuB;IAAA;IAC1H,OAAOhD,IAAI,kBA7U8E9I,EAAE,CAAA+I,iBAAA;MAAAlE,IAAA,EA6UJiH,uBAAuB;MAAAhH,SAAA;MAAA6E,SAAA,WAA2Q,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAAuE,qCAAA5K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7UxSxD,EAAE,CAAAqO,UAAA,mBAAAC,iDAAA;YAAA,OA6UJ7K,GAAA,CAAAsJ,OAAA,CAAQ,CAAC;UAAA,CAAa,CAAC,qBAAAwB,mDAAAC,MAAA;YAAA,OAAvB/K,GAAA,CAAA6J,QAAA,CAAAkB,MAAe,CAAC;UAAA,CAAM,CAAC;QAAA;QAAA,IAAAhL,EAAA;UA7UrBxD,EAAE,CAAAqL,WAAA,OAAA5H,GAAA,CAAAsI,KAAA,CAAAzF,SAAA,cAAA7C,GAAA,CAAAqJ,QAAA,IA6UQ,CAAC,GAAArJ,GAAA,CAAA6I,QAAA,mBAAb7I,GAAA,CAAAwJ,WAAA,CAAY,CAAC,mBAAbxJ,GAAA,CAAAuJ,WAAA,CAAY,CAAC,mBAAAvJ,GAAA,CAAAsI,KAAA,CAAAe,QAAA;UA7UX9M,EAAE,CAAAyO,WAAA,WA6UJhL,GAAA,CAAA2J,gBAAA,CAAiB,CAAK,CAAC;UA7UrBpN,EAAE,CAAA+J,WAAA,iBA6UJtG,GAAA,CAAAuJ,WAAA,CAAY,CAAU,CAAC,yCAAvBvJ,GAAA,CAAAyJ,kBAAA,CAAmB,CAAC,KAAK,OAAH,CAAC,0CAAvBzJ,GAAA,CAAAyJ,kBAAA,CAAmB,CAAC,KAAK,QAAH,CAAC;QAAA;MAAA;MAAAlD,MAAA;QAAA4C,cAAA;QAAAC,eAAA;QAAAP,QAAA,8BAAkL3G,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGtE,eAAe,CAACsE,KAAK,CAAE;MAAA;MAAAgF,kBAAA,EAAAhH,GAAA;MAAAiH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2D,iCAAAlL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7U7PxD,EAAE,CAAAiL,eAAA,CAAAvH,GAAA;UAAF1D,EAAE,CAAA6D,cAAA,aA6U69B,CAAC;UA7Uh+B7D,EAAE,CAAAkL,YAAA,EA6UqhC,CAAC;UA7UxhClL,EAAE,CAAAkL,YAAA,KA6UmlC,CAAC;UA7UtlClL,EAAE,CAAAkL,YAAA,KA6UgnC,CAAC;UA7UnnClL,EAAE,CAAAgE,YAAA,CA6UynC,CAAC;UA7U5nChE,EAAE,CAAA2O,mBAAA,IAAA/K,8CAAA,iBA6UkpC,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA7UrpCxD,EAAE,CAAA+J,WAAA,6BAAAtG,GAAA,CAAA0J,WAAA,EA6U49B,CAAC;UA7U/9BnN,EAAE,CAAAoL,SAAA,EA6Um7C,CAAC;UA7Ut7CpL,EAAE,CAAA4O,aAAA,CAAAnL,GAAA,CAAA0J,WAAA,WA6Um7C,CAAC;QAAA;MAAA;MAAA1B,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACnhD;EAAC,OA9HKG,uBAAuB;AAAA;AA+H7B;EAAA,QAAA/G,SAAA,oBAAAA,SAAA;AAAA;AA2BA;AACA;AACA;AAFA,IAGM8J,4BAA4B;EAAlC,MAAMA,4BAA4B,CAAC;IAC/B,OAAOrK,IAAI,YAAAsK,qCAAApK,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmK,4BAA4B;IAAA;IAC/H,OAAOlK,IAAI,kBA/W8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EA+WJgK,4BAA4B;MAAA/J,SAAA;MAAA6E,SAAA;IAAA;EACvH;EAAC,OAHKkF,4BAA4B;AAAA;AAIlC;EAAA,QAAA9J,SAAA,oBAAAA,SAAA;AAAA;AASA;AACA;AACA;AAFA,IAGMgK,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzB,OAAOvK,IAAI,YAAAwK,+BAAAtK,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqK,sBAAsB;IAAA;IACzH,OAAOpK,IAAI,kBA/X8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EA+XJkK,sBAAsB;MAAAjK,SAAA;MAAA6E,SAAA;IAAA;EACjH;EAAC,OAHKoF,sBAAsB;AAAA;AAI5B;EAAA,QAAAhK,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA;AACA;AAFA,IAGMkK,YAAY;EAAlB,MAAMA,YAAY,SAASvN,YAAY,CAAC;IACpCwN,WAAW;IACX;IACAC,WAAW,GAAG,IAAI7N,SAAS,CAAC,CAAC;IAC7B;IACA8N,QAAQ;IACR;IACA5J,UAAU,GAAG,KAAK;IAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIoB,WAAW,GAAG,SAAS;IACvB;IACAhB,cAAc,GAAG,OAAO;IACxBqB,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACmI,QAAQ,CAAC7H,OAAO,CAChBJ,IAAI,CAACjF,SAAS,CAAC,IAAI,CAACkN,QAAQ,CAAC,CAAC,CAC9BhI,SAAS,CAAEiI,OAAO,IAAK;QACxB,IAAI,CAACF,WAAW,CAACG,KAAK,CAACD,OAAO,CAAClN,MAAM,CAACoN,MAAM,IAAIA,MAAM,CAACxD,KAAK,CAACrG,SAAS,KAAK,IAAI,CAAC,CAAC;QACjF,IAAI,CAACyJ,WAAW,CAACK,eAAe,CAAC,CAAC;MACtC,CAAC,CAAC;MACF,IAAI,CAACN,WAAW,GAAG,IAAIjN,eAAe,CAAC,IAAI,CAACkN,WAAW,CAAC,CAACM,QAAQ,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;IACxF;IACA;IACAhC,oBAAoBA,CAACH,KAAK,EAAE;MACxB,IAAI,CAAC2B,WAAW,CAACS,SAAS,CAACpC,KAAK,CAAC;IACrC;IACAS,kBAAkBA,CAACuB,MAAM,EAAE;MACvB,IAAI,CAACL,WAAW,CAACU,gBAAgB,CAACL,MAAM,CAAC;IAC7C;IACA9H,WAAWA,CAAA,EAAG;MACV,KAAK,CAACA,WAAW,CAAC,CAAC;MACnB,IAAI,CAACyH,WAAW,EAAEW,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACV,WAAW,CAACU,OAAO,CAAC,CAAC;IAC9B;IACA,OAAOrL,IAAI;MAAA,IAAAsL,yBAAA;MAAA,gBAAAC,qBAAArL,iBAAA;QAAA,QAAAoL,yBAAA,KAAAA,yBAAA,GAtb8E9P,EAAE,CAAAgQ,qBAAA,CAsbQf,YAAY,IAAAvK,iBAAA,IAAZuK,YAAY;MAAA;IAAA;IAC/G,OAAOtK,IAAI,kBAvb8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAubJoK,YAAY;MAAAnK,SAAA;MAAAkE,cAAA,WAAAiH,4BAAAzM,EAAA,EAAAC,GAAA,EAAAyF,QAAA;QAAA,IAAA1F,EAAA;UAvbVxD,EAAE,CAAAmJ,cAAA,CAAAD,QAAA,EA4bnC4C,uBAAuB;QAAA;QAAA,IAAAtI,EAAA;UAAA,IAAA4F,EAAA;UA5bUpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA7F,GAAA,CAAA2L,QAAA,GAAAhG,EAAA;QAAA;MAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAqG,0BAAA1M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxD,EAAE,CAAA+J,WAAA,wBAAAtG,GAAA,CAAA0M,KAubO,CAAC;QAAA;MAAA;MAAAnG,MAAA;QAAAxE,UAAA,kCAAoG7E,gBAAgB;QAAAiG,WAAA;QAAAhB,cAAA;MAAA;MAAAsE,QAAA;MAAAC,QAAA,GAvb9HnK,EAAE,CAAAoK,kBAAA,CAubgT,CACnY;QACIC,OAAO,EAAEpG,aAAa;QACtBuG,WAAW,EAAEyE;MACjB,CAAC,CACJ,GA5boFjP,EAAE,CAAAyK,0BAAA;IAAA;EA6b/F;EAAC,OA/CKwE,YAAY;AAAA;AAgDlB;EAAA,QAAAlK,SAAA,oBAAAA,SAAA;AAAA;AA4BoB,IAEdqL,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB,OAAO5L,IAAI,YAAA6L,2BAAA3L,iBAAA;MAAA,YAAAA,iBAAA,IAAwF0L,kBAAkB;IAAA;IACrH,OAAOE,IAAI,kBA9d8EtQ,EAAE,CAAAuQ,gBAAA;MAAA1L,IAAA,EA8dSuL;IAAkB;IAgBtH,OAAOI,IAAI,kBA9e8ExQ,EAAE,CAAAyQ,gBAAA;MAAAC,OAAA,GA8euCxN,eAAe,EACzIvB,kBAAkB,EAClBG,YAAY;IAAA;EACxB;EAAC,OArBKsO,kBAAkB;AAAA;AAsBxB;EAAA,QAAArL,SAAA,oBAAAA,SAAA;AAAA;;AA2BA;AACA;AACA;AACA;AACA;AACA,MAAM4L,gCAAgC,GAAG,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,eAAe,EAAE;IACbhM,IAAI,EAAE,CAAC;IACPiM,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,CACT;MACIlM,IAAI,EAAE,CAAC;MACPiM,IAAI,EAAE,iBAAiB;MACvBrF,MAAM,EAAE;QAAE5G,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE;UAAEuF,SAAS,EAAE;QAAe,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC3E,CAAC,EACD;MACIpM,IAAI,EAAE,CAAC;MACPiM,IAAI,EAAE,UAAU;MAChBrF,MAAM,EAAE;QAAE5G,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE;UAAEuF,SAAS,EAAE;QAAiB,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACIpM,IAAI,EAAE,CAAC;MACPqM,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE;QAAEtM,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE,IAAI;QAAE2F,OAAO,EAAE;MAAoC,CAAC;MAClFvD,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAwD,aAAa,EAAE;IACXxM,IAAI,EAAE,CAAC;IACPiM,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,CACT;MACIlM,IAAI,EAAE,CAAC;MACPiM,IAAI,EAAE,iBAAiB;MACvBrF,MAAM,EAAE;QAAE5G,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE;UAAE,QAAQ,EAAE,KAAK;UAAE,YAAY,EAAE;QAAS,CAAC;QAAEwF,MAAM,EAAE;MAAK;IACzF,CAAC,EACD;MACIpM,IAAI,EAAE,CAAC;MACPiM,IAAI,EAAE,UAAU;MAChBrF,MAAM,EAAE;QAAE5G,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE;UAAE,QAAQ,EAAE,GAAG;UAAE,YAAY,EAAE;QAAG,CAAC;QAAEwF,MAAM,EAAE;MAAK;IACjF,CAAC,EACD;MACIpM,IAAI,EAAE,CAAC;MACPqM,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE;QAAEtM,IAAI,EAAE,CAAC;QAAE4G,MAAM,EAAE,IAAI;QAAE2F,OAAO,EAAE;MAAoC,CAAC;MAClFvD,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAS8C,gCAAgC,EAAE1M,aAAa,EAAEC,mBAAmB,EAAEc,mCAAmC,EAAEiK,YAAY,EAAEmB,kBAAkB,EAAEnL,iBAAiB,EAAE2G,0BAA0B,EAAEzH,wBAAwB,EAAE0K,4BAA4B,EAAE/C,uBAAuB,EAAEiD,sBAAsB,EAAE6B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}