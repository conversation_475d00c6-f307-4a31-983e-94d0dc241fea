import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatStepperModule } from '@angular/material/stepper';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { trigger, transition, style, animate } from '@angular/animations';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatRadioModule } from '@angular/material/radio';

interface Category {
  id: number;
  nameAr: string;
  nameEn: string;
  code: string;
}

interface Unit {
  id: number;
  nameAr: string;
  nameEn: string;
  symbol: string;
}

interface Supplier {
  id: number;
  nameAr: string;
  nameEn: string;
  supplierCode: string;
  phone: string;
  email: string;
}

@Component({
  selector: 'app-add-product',
  standalone: true,
  // animations: [
  //   trigger('slideAnimation', [
  //     transition(':enter', [
  //       style({ opacity: 0, transform: 'translateX(20px)' }),
  //       animate('300ms ease-in', style({ opacity: 1, transform: 'translateX(0)' }))
  //     ])
  //   ])
  // ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatStepperModule,
    MatSnackBarModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatRadioModule,
    MatFormFieldModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './add-product-new.html',
  styleUrls: ['./add-product-professional.scss']
})
export class AddProduct implements OnInit {
  productForm: FormGroup;
  categories: Category[] = [];
  units: Unit[] = [];
  suppliers: Supplier[] = [];
  selectedSuppliers: Supplier[] = [];
  
  isLoading: boolean = false;
  isGeneratingCode: boolean = false;
  currentStep: number = 1;
  profitMargin: number | null = null;
  
  productTypes = [
    { value: 'محلي', label: 'محلي', description: 'منتج محلي - كود تلقائي يبدأ من 2000000000001' },
    { value: 'دولي', label: 'دولي', description: 'منتج دولي - إدخال الكود يدوياً' },
    { value: 'موزون', label: 'موزون', description: 'منتج موزون - كود تلقائي للميزان' }
  ];

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.productForm = this.fb.group({
      // معلومات أساسية
      productType: ['محلي', Validators.required],
      productCode: ['', Validators.required],
      nameAr: ['', Validators.required],
      nameEn: [''],
      description: [''],
      
      // التصنيف والوحدة
      categoryId: ['', Validators.required],
      unitId: ['', Validators.required],
      
      // الأسعار والمخزون
      purchasePrice: [0, [Validators.required, Validators.min(0)]],
      salePrice: [0, [Validators.required, Validators.min(0)]],
      minStock: [0, [Validators.min(0)]],
      maxStock: [0, [Validators.min(0)]],
      currentStock: [0, [Validators.min(0)]],
      
      // المورد الرئيسي
      mainSupplierId: ['', Validators.required],
      
      // إعدادات
      isActive: [true],
      isWeighted: [false],
      hasExpiry: [false],
      trackSerial: [false]
    });
  }

  ngOnInit() {
    this.loadCategories();
    this.loadUnits();
    this.loadSuppliers();
    
    // مراقبة تغيير نوع المنتج
    this.productForm.get('productType')?.valueChanges.subscribe(type => {
      this.onProductTypeChange(type);
    });
  }

  loadCategories() {
    this.http.get<any>('http://localhost:5000/api/simple/categories').subscribe({
      next: (response) => {
        this.categories = response.categories || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.showMessage('خطأ في تحميل الفئات');
      }
    });
  }

  loadUnits() {
    this.http.get<any>('http://localhost:5000/api/simple/units').subscribe({
      next: (response) => {
        this.units = response.units || [];
      },
      error: (error) => {
        console.error('Error loading units:', error);
        this.showMessage('خطأ في تحميل الوحدات');
      }
    });
  }

  loadSuppliers() {
    this.http.get<any>('http://localhost:5000/api/simple/suppliers').subscribe({
      next: (response) => {
        this.suppliers = response.suppliers || [];
      },
      error: (error) => {
        console.error('Error loading suppliers:', error);
        this.showMessage('خطأ في تحميل الموردين');
      }
    });
  }

  onProductTypeChange(type: string) {
    if (type === 'موزون') {
      this.productForm.get('isWeighted')?.setValue(true);
      this.productForm.get('isWeighted')?.disable();
    } else {
      this.productForm.get('isWeighted')?.setValue(false);
      this.productForm.get('isWeighted')?.enable();
    }

    // تحديد إمكانية تعديل الكود
    if (type === 'دولي') {
      this.productForm.get('productCode')?.enable();
    } else {
      this.productForm.get('productCode')?.disable();
    }

    // مسح الكود الحالي عند تغيير النوع
    this.productForm.get('productCode')?.setValue('');
  }

  generateProductCode() {
    const productType = this.productForm.get('productType')?.value;
    
    if (!productType) {
      this.showMessage('يرجى اختيار نوع المنتج أولاً');
      return;
    }

    if (productType === 'دولي') {
      this.showMessage('للمنتجات الدولية، يرجى إدخال الكود يدوياً');
      return;
    }

    this.isGeneratingCode = true;
    
    this.http.post<any>('http://localhost:5000/api/simple/generate-product-code', {
      productType: productType
    }).subscribe({
      next: (response) => {
        if (response.requiresManualInput) {
          this.showMessage(response.message);
        } else {
          this.productForm.patchValue({ productCode: response.productCode });
          this.showMessage(`تم توليد الكود: ${response.productCode}`);
        }
        this.isGeneratingCode = false;
      },
      error: (error) => {
        console.error('Error generating code:', error);
        this.showMessage('خطأ في توليد الكود');
        this.isGeneratingCode = false;
      }
    });
  }

  checkProductCode() {
    const productCode = this.productForm.get('productCode')?.value;
    
    if (!productCode) {
      return;
    }

    this.http.post<any>('http://localhost:5000/api/simple/check-product-code', {
      productCode: productCode
    }).subscribe({
      next: (response) => {
        if (!response.isAvailable) {
          this.showMessage('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر', true);
          this.productForm.get('productCode')?.setErrors({ 'duplicate': true });
        } else {
          this.productForm.get('productCode')?.setErrors(null);
        }
      },
      error: (error) => {
        console.error('Error checking code:', error);
      }
    });
  }

  addSupplier(supplier: Supplier) {
    if (!this.selectedSuppliers.find(s => s.id === supplier.id)) {
      this.selectedSuppliers.push(supplier);
    }
  }

  removeSupplier(supplier: Supplier) {
    this.selectedSuppliers = this.selectedSuppliers.filter(s => s.id !== supplier.id);
  }

  onSubmit() {
    if (this.productForm.valid) {
      this.isLoading = true;
      
      const formData = {
        ...this.productForm.value,
        suppliers: this.selectedSuppliers.map(s => s.id)
      };

      // هنا سيتم إرسال البيانات لـ API
      console.log('Product Data:', formData);
      
      // محاكاة حفظ البيانات
      setTimeout(() => {
        this.isLoading = false;
        this.showMessage('تم إضافة المنتج بنجاح');
        this.router.navigate(['/products']);
      }, 2000);
    } else {
      this.showMessage('يرجى ملء جميع الحقول المطلوبة', true);
    }
  }

  cancel() {
    this.router.navigate(['/products']);
  }

  private showMessage(message: string, isError = false) {
    this.snackBar.open(message, 'إغلاق', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'top',
      panelClass: isError ? ['error-snackbar'] : ['success-snackbar']
    });
  }

  // Navigation Methods for Professional UI
  nextStep(): void {
    if (this.currentStep < 4) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  goBack(): void {
    // Navigate back to products page
    this.router.navigate(['/products']);
  }

  // Calculate profit margin
  calculateProfitMargin(): void {
    const purchasePrice = this.productForm.get('purchasePrice')?.value;
    const salePrice = this.productForm.get('salePrice')?.value;

    if (purchasePrice && salePrice && purchasePrice > 0) {
      this.profitMargin = Math.round(((salePrice - purchasePrice) / purchasePrice) * 100);
    } else {
      this.profitMargin = null;
    }
  }

  // Generate product code
  generateCode(): void {
    this.generateProductCode();
  }

  // Save product
  saveProduct(): void {
    if (this.productForm.valid) {
      this.onSubmit();
    } else {
      this.snackBar.open('يرجى ملء جميع الحقول المطلوبة', 'إغلاق', {
        duration: 3000,
        horizontalPosition: 'center',
        verticalPosition: 'top'
      });
    }
  }
}
