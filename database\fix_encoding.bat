@echo off
chcp 65001 >nul
echo ========================================
echo إصلاح ترميز النصوص العربية
echo Fix Arabic Text Encoding
echo ========================================
echo.

echo 🔧 تشغيل سكريبت إصلاح الترميز...
echo Running encoding fix script...
echo.

sqlcmd -S localhost -U sa -P @a123admin4 -i fix_arabic_encoding.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ تم إصلاح الترميز بنجاح!
    echo ✅ Encoding fixed successfully!
    echo ========================================
    echo.
    echo 📋 النصوص العربية ستظهر الآن بشكل صحيح
    echo 📋 Arabic texts will now display correctly
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ حدث خطأ في إصلاح الترميز
    echo ❌ Error occurred while fixing encoding
    echo ========================================
    echo.
    echo تأكد من:
    echo Please check:
    echo 1. SQL Server is running
    echo 2. Database exists
    echo 3. Connection credentials are correct
    echo.
)

pause
