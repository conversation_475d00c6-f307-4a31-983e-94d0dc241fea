{"ast": null, "code": "import { _ as _resolveDirectionality, D as Directionality } from './directionality-CChdj3az.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CChdj3az.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, Directive, Output, Input, NgModule } from '@angular/core';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nlet Dir = /*#__PURE__*/(() => {\n  class Dir {\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n      return this.valueSignal();\n    }\n    set dir(value) {\n      const previousValue = this.valueSignal();\n      // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n      // whereas the browser does it based on the content of the element. Since doing so based\n      // on the content can be expensive, for now we're doing the simpler matching.\n      this.valueSignal.set(_resolveDirectionality(value));\n      this._rawDir = value;\n      if (previousValue !== this.valueSignal() && this._isInitialized) {\n        this.change.emit(this.valueSignal());\n      }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n      return this.dir;\n    }\n    valueSignal = signal('ltr');\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      this.change.complete();\n    }\n    static ɵfac = function Dir_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dir)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Dir,\n      selectors: [[\"\", \"dir\", \"\"]],\n      hostVars: 1,\n      hostBindings: function Dir_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"dir\", ctx._rawDir);\n        }\n      },\n      inputs: {\n        dir: \"dir\"\n      },\n      outputs: {\n        change: \"dirChange\"\n      },\n      exportAs: [\"dir\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: Directionality,\n        useExisting: Dir\n      }])]\n    });\n  }\n  return Dir;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BidiModule = /*#__PURE__*/(() => {\n  class BidiModule {\n    static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BidiModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BidiModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return BidiModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { BidiModule, Dir, Directionality };", "map": {"version": 3, "names": ["_", "_resolveDirectionality", "D", "Directionality", "a", "DIR_DOCUMENT", "i0", "EventEmitter", "signal", "Directive", "Output", "Input", "NgModule", "<PERSON><PERSON>", "_isInitialized", "_rawDir", "change", "dir", "valueSignal", "value", "previousValue", "set", "emit", "ngAfterContentInit", "ngOnDestroy", "complete", "ɵfac", "Dir_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "Dir_<PERSON><PERSON><PERSON><PERSON>", "rf", "ctx", "ɵɵattribute", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "BidiModule", "BidiModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/cdk/fesm2022/bidi.mjs"], "sourcesContent": ["import { _ as _resolveDirectionality, D as Directionality } from './directionality-CChdj3az.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CChdj3az.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, Directive, Output, Input, NgModule } from '@angular/core';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n        return this.valueSignal();\n    }\n    set dir(value) {\n        const previousValue = this.valueSignal();\n        // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n        // whereas the browser does it based on the content of the element. Since doing so based\n        // on the content can be expensive, for now we're doing the simpler matching.\n        this.valueSignal.set(_resolveDirectionality(value));\n        this._rawDir = value;\n        if (previousValue !== this.valueSignal() && this._isInitialized) {\n            this.change.emit(this.valueSignal());\n        }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n        return this.dir;\n    }\n    valueSignal = signal('ltr');\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dir, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: Dir, isStandalone: true, selector: \"[dir]\", inputs: { dir: \"dir\" }, outputs: { change: \"dirChange\" }, host: { properties: { \"attr.dir\": \"_rawDir\" } }, providers: [{ provide: Directionality, useExisting: Dir }], exportAs: [\"dir\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dir, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[dir]',\n                    providers: [{ provide: Directionality, useExisting: Dir }],\n                    host: { '[attr.dir]': '_rawDir' },\n                    exportAs: 'dir',\n                }]\n        }], propDecorators: { change: [{\n                type: Output,\n                args: ['dirChange']\n            }], dir: [{\n                type: Input\n            }] } });\n\nclass BidiModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BidiModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: BidiModule, imports: [Dir], exports: [Dir] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BidiModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BidiModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Dir],\n                    exports: [Dir],\n                }]\n        }] });\n\nexport { BidiModule, Dir, Directionality };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AAChG,SAASC,CAAC,IAAIC,YAAY,QAAQ,+BAA+B;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;;AAExF;AACA;AACA;AACA;AACA;AACA;AALA,IAMMC,GAAG;EAAT,MAAMA,GAAG,CAAC;IACN;IACAC,cAAc,GAAG,KAAK;IACtB;IACAC,OAAO;IACP;IACAC,MAAM,GAAG,IAAIT,YAAY,CAAC,CAAC;IAC3B;IACA,IAAIU,GAAGA,CAAA,EAAG;MACN,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAID,GAAGA,CAACE,KAAK,EAAE;MACX,MAAMC,aAAa,GAAG,IAAI,CAACF,WAAW,CAAC,CAAC;MACxC;MACA;MACA;MACA,IAAI,CAACA,WAAW,CAACG,GAAG,CAACpB,sBAAsB,CAACkB,KAAK,CAAC,CAAC;MACnD,IAAI,CAACJ,OAAO,GAAGI,KAAK;MACpB,IAAIC,aAAa,KAAK,IAAI,CAACF,WAAW,CAAC,CAAC,IAAI,IAAI,CAACJ,cAAc,EAAE;QAC7D,IAAI,CAACE,MAAM,CAACM,IAAI,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC,CAAC;MACxC;IACJ;IACA;IACA,IAAIC,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACF,GAAG;IACnB;IACAC,WAAW,GAAGV,MAAM,CAAC,KAAK,CAAC;IAC3B;IACAe,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACT,cAAc,GAAG,IAAI;IAC9B;IACAU,WAAWA,CAAA,EAAG;MACV,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC;IAC1B;IACA,OAAOC,IAAI,YAAAC,YAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFf,GAAG;IAAA;IACtG,OAAOgB,IAAI,kBAD8EvB,EAAE,CAAAwB,iBAAA;MAAAC,IAAA,EACJlB,GAAG;MAAAmB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADD9B,EAAE,CAAAgC,WAAA,QAAAD,GAAA,CAAAtB,OAAA;QAAA;MAAA;MAAAwB,MAAA;QAAAtB,GAAA;MAAA;MAAAuB,OAAA;QAAAxB,MAAA;MAAA;MAAAyB,QAAA;MAAAC,QAAA,GAAFpC,EAAE,CAAAqC,kBAAA,CAC8J,CAAC;QAAEC,OAAO,EAAEzC,cAAc;QAAE0C,WAAW,EAAEhC;MAAI,CAAC,CAAC;IAAA;EAC5S;EAAC,OApCKA,GAAG;AAAA;AAqCT;EAAA,QAAAiC,SAAA,oBAAAA,SAAA;AAAA;AAaoB,IAEdC,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACb,OAAOrB,IAAI,YAAAsB,mBAAApB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmB,UAAU;IAAA;IAC7G,OAAOE,IAAI,kBApB8E3C,EAAE,CAAA4C,gBAAA;MAAAnB,IAAA,EAoBSgB;IAAU;IAC9G,OAAOI,IAAI,kBArB8E7C,EAAE,CAAA8C,gBAAA;EAsB/F;EAAC,OAJKL,UAAU;AAAA;AAKhB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAQA,SAASC,UAAU,EAAElC,GAAG,EAAEV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}