#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
import sys

def get_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=localhost;"
            "DATABASE=TerraRetailERP;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "Encrypt=no;"
        )
        return pyodbc.connect(connection_string)
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def insert_areas(cursor):
    """إدراج المحافظات المصرية"""
    print("🏙️ إدراج المحافظات المصرية...")
    
    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM Areas")
    
    areas = [
        (1, 'القاهرة', 'Cairo', 'CAI', True),
        (2, 'الجيزة', 'Giza', 'GIZ', True),
        (3, 'الإسكندرية', 'Alexandria', 'ALX', True),
        (4, 'الدقهلية', 'Dakahlia', 'DAK', True),
        (5, 'الشرقية', 'Sharqia', 'SHR', True),
        (6, 'القليوبية', 'Qalyubia', 'QLY', True),
        (7, 'كفر الشيخ', 'Kafr El Sheikh', 'KFS', True),
        (8, 'الغربية', 'Gharbia', 'GHR', True),
        (9, 'المنوفية', 'Monufia', 'MNF', True),
        (10, 'البحيرة', 'Beheira', 'BHR', True),
        (11, 'الإسماعيلية', 'Ismailia', 'ISM', True),
        (12, 'بورسعيد', 'Port Said', 'PTS', True),
        (13, 'السويس', 'Suez', 'SUZ', True),
        (14, 'المنيا', 'Minya', 'MNY', True),
        (15, 'بني سويف', 'Beni Suef', 'BNS', True),
        (16, 'الفيوم', 'Fayoum', 'FYM', True),
        (17, 'أسيوط', 'Asyut', 'ASY', True),
        (18, 'سوهاج', 'Sohag', 'SOH', True),
        (19, 'قنا', 'Qena', 'QNA', True),
        (20, 'الأقصر', 'Luxor', 'LXR', True),
        (21, 'أسوان', 'Aswan', 'ASW', True),
        (22, 'البحر الأحمر', 'Red Sea', 'RSS', True),
        (23, 'الوادي الجديد', 'New Valley', 'NVL', True),
        (24, 'مطروح', 'Matrouh', 'MTR', True),
        (25, 'شمال سيناء', 'North Sinai', 'NSI', True),
        (26, 'جنوب سيناء', 'South Sinai', 'SSI', True),
        (27, 'دمياط', 'Damietta', 'DMT', True)
    ]
    
    for area in areas:
        cursor.execute("""
            INSERT INTO Areas (Id, NameAr, NameEn, Code, IsActive)
            VALUES (?, ?, ?, ?, ?)
        """, area)
    
    print(f"✅ تم إدراج {len(areas)} محافظة بنجاح")

def insert_countries(cursor):
    """إدراج البلدان العربية والعالمية"""
    print("🌍 إدراج البلدان...")
    
    # حذف البيانات الموجودة
    cursor.execute("DELETE FROM Countries")
    
    countries = [
        (1, 'مصر', 'Egypt', 'EG', '+20', True),
        (2, 'السعودية', 'Saudi Arabia', 'SA', '+966', True),
        (3, 'الإمارات العربية المتحدة', 'United Arab Emirates', 'AE', '+971', True),
        (4, 'الكويت', 'Kuwait', 'KW', '+965', True),
        (5, 'قطر', 'Qatar', 'QA', '+974', True),
        (6, 'البحرين', 'Bahrain', 'BH', '+973', True),
        (7, 'عمان', 'Oman', 'OM', '+968', True),
        (8, 'الأردن', 'Jordan', 'JO', '+962', True),
        (9, 'لبنان', 'Lebanon', 'LB', '+961', True),
        (10, 'سوريا', 'Syria', 'SY', '+963', True),
        (11, 'العراق', 'Iraq', 'IQ', '+964', True),
        (12, 'فلسطين', 'Palestine', 'PS', '+970', True),
        (13, 'المغرب', 'Morocco', 'MA', '+212', True),
        (14, 'الجزائر', 'Algeria', 'DZ', '+213', True),
        (15, 'تونس', 'Tunisia', 'TN', '+216', True),
        (16, 'ليبيا', 'Libya', 'LY', '+218', True),
        (17, 'السودان', 'Sudan', 'SD', '+249', True),
        (18, 'اليمن', 'Yemen', 'YE', '+967', True),
        (19, 'الولايات المتحدة', 'United States', 'US', '+1', True),
        (20, 'المملكة المتحدة', 'United Kingdom', 'GB', '+44', True),
        (21, 'ألمانيا', 'Germany', 'DE', '+49', True),
        (22, 'فرنسا', 'France', 'FR', '+33', True),
        (23, 'إيطاليا', 'Italy', 'IT', '+39', True),
        (24, 'إسبانيا', 'Spain', 'ES', '+34', True),
        (25, 'تركيا', 'Turkey', 'TR', '+90', True),
        (26, 'الصين', 'China', 'CN', '+86', True),
        (27, 'اليابان', 'Japan', 'JP', '+81', True),
        (28, 'الهند', 'India', 'IN', '+91', True),
        (29, 'روسيا', 'Russia', 'RU', '+7', True),
        (30, 'كندا', 'Canada', 'CA', '+1', True)
    ]
    
    for country in countries:
        cursor.execute("""
            INSERT INTO Countries (Id, NameAr, NameEn, Code, PhoneCode, IsActive)
            VALUES (?, ?, ?, ?, ?, ?)
        """, country)
    
    print(f"✅ تم إدراج {len(countries)} بلد بنجاح")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إدراج بيانات المحافظات والبلدان...")
    
    connection = get_connection()
    if not connection:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        sys.exit(1)
    
    try:
        cursor = connection.cursor()
        
        # إدراج المحافظات
        insert_areas(cursor)
        
        # إدراج البلدان
        insert_countries(cursor)
        
        # حفظ التغييرات
        connection.commit()
        print("✅ تم حفظ جميع البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ أثناء إدراج البيانات: {e}")
        connection.rollback()
        sys.exit(1)
    
    finally:
        connection.close()
        print("🔒 تم إغلاق الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    main()
