{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@angular/material/paginator\";\nimport * as i13 from \"@angular/material/sort\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/tooltip\";\nconst _c0 = () => [10, 25, 50, 100];\nfunction SuppliersNewComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r1.name, \" \");\n  }\n}\nfunction SuppliersNewComponent_mat_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", country_r2.name, \" \");\n  }\n}\nfunction SuppliersNewComponent_th_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"span\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(supplier_r3.code);\n  }\n}\nfunction SuppliersNewComponent_th_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 67)(2, \"div\", 68);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const supplier_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(supplier_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(supplier_r4.supplierTypeName);\n  }\n}\nfunction SuppliersNewComponent_th_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 70);\n    i0.ɵɵtext(1, \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_134_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(supplier_r5.phone);\n  }\n}\nfunction SuppliersNewComponent_td_134_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(supplier_r5.email);\n  }\n}\nfunction SuppliersNewComponent_td_134_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(supplier_r5.website);\n  }\n}\nfunction SuppliersNewComponent_td_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 71);\n    i0.ɵɵtemplate(2, SuppliersNewComponent_td_134_div_2_Template, 5, 1, \"div\", 72)(3, SuppliersNewComponent_td_134_div_3_Template, 5, 1, \"div\", 73)(4, SuppliersNewComponent_td_134_div_4_Template, 5, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", supplier_r5.phone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", supplier_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", supplier_r5.website);\n  }\n}\nfunction SuppliersNewComponent_th_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 70);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_137_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const supplier_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(supplier_r6.city);\n  }\n}\nfunction SuppliersNewComponent_td_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 78)(2, \"div\", 79)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"flag\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, SuppliersNewComponent_td_137_div_7_Template, 2, 1, \"div\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r6 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(supplier_r6.countryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", supplier_r6.city);\n  }\n}\nfunction SuppliersNewComponent_th_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 82)(2, \"span\", 83);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 84);\n    i0.ɵɵtext(5, \"\\u0645\\u0646\\u062A\\u062C\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const supplier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(supplier_r7.productsCount);\n  }\n}\nfunction SuppliersNewComponent_th_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_143_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 85);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r8 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r8.getBalanceClass(supplier_r8.balance));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 3, supplier_r8.balance, \"1.2-2\"), \" \\u062C\\u0646\\u064A\\u0647 \");\n  }\n}\nfunction SuppliersNewComponent_th_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062A\\u0642\\u064A\\u064A\\u0645\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_146_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const star_r10 = ctx.$implicit;\n    i0.ɵɵclassMap(star_r10 ? \"filled\" : \"empty\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", star_r10 ? \"star\" : \"star_border\", \" \");\n  }\n}\nfunction SuppliersNewComponent_td_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 86)(2, \"div\", 87);\n    i0.ɵɵtemplate(3, SuppliersNewComponent_td_146_mat_icon_3_Template, 2, 3, \"mat-icon\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 89);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const supplier_r11 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.getStarsArray(supplier_r11.rating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", supplier_r11.rating, \"/5)\");\n  }\n}\nfunction SuppliersNewComponent_th_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 70);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"span\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const supplier_r12 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r8.getStatusClass(supplier_r12.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStatusText(supplier_r12.status), \" \");\n  }\n}\nfunction SuppliersNewComponent_th_151_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 70);\n    i0.ɵɵtext(1, \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_td_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"div\", 91)(2, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SuppliersNewComponent_td_152_Template_button_click_2_listener() {\n      const supplier_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.viewSupplier(supplier_r14));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function SuppliersNewComponent_td_152_Template_button_click_5_listener() {\n      const supplier_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.editSupplier(supplier_r14));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SuppliersNewComponent_td_152_Template_button_click_8_listener() {\n      const supplier_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.deleteSupplier(supplier_r14));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function SuppliersNewComponent_td_152_Template_button_click_11_listener() {\n      const supplier_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.viewSupplierProducts(supplier_r14));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"inventory\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SuppliersNewComponent_tr_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 96);\n  }\n}\nfunction SuppliersNewComponent_tr_154_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 97);\n    i0.ɵɵlistener(\"click\", function SuppliersNewComponent_tr_154_Template_tr_click_0_listener() {\n      const row_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.viewSupplier(row_r16));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuppliersNewComponent_div_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"mat-spinner\", 99);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SuppliersNewComponent = /*#__PURE__*/(() => {\n  class SuppliersNewComponent {\n    http;\n    router;\n    // Component State\n    isLoading = true;\n    // Data\n    suppliers = [];\n    filteredSuppliers = [];\n    paginatedSuppliers = [];\n    supplierTypes = [];\n    countries = [];\n    // Statistics\n    statistics = {\n      totalSuppliers: 0,\n      activeSuppliers: 0,\n      localSuppliers: 0,\n      internationalSuppliers: 0\n    };\n    // Filters\n    searchTerm = '';\n    selectedSupplierType = '';\n    selectedCountry = '';\n    selectedStatus = '';\n    // Table Configuration\n    displayedColumns = ['code', 'name', 'contact', 'location', 'products', 'balance', 'rating', 'status', 'actions'];\n    pageSize = 25;\n    currentPage = 0;\n    // Subscriptions\n    subscriptions = [];\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.loadInitialData();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    /**\n     * Load initial data\n     */\n    loadInitialData() {\n      this.isLoading = true;\n      // Load all data in parallel\n      Promise.all([this.loadSuppliers(), this.loadSupplierTypes(), this.loadCountries()]).then(() => {\n        this.calculateStatistics();\n        this.applyFilters();\n        this.isLoading = false;\n      }).catch(error => {\n        console.error('Error loading data:', error);\n        this.isLoading = false;\n      });\n    }\n    /**\n     * Load suppliers from API\n     */\n    loadSuppliers() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/suppliers').subscribe({\n          next: response => {\n            console.log('API Response:', response);\n            // Map API response to match our interface\n            this.suppliers = (response.suppliers || []).map(supplier => ({\n              id: supplier.Id || supplier.id,\n              code: supplier.SupplierCode || supplier.supplierCode || supplier.code,\n              name: supplier.NameAr || supplier.nameAr || supplier.name,\n              nameEn: supplier.NameEn || supplier.nameEn,\n              phone: supplier.Phone || supplier.phone1 || supplier.Phone1,\n              email: supplier.Email || supplier.email,\n              website: supplier.Website || supplier.website,\n              address: supplier.Address || supplier.address,\n              city: this.extractCityFromAddress(supplier.Address || supplier.address),\n              supplierTypeId: supplier.SupplierTypeId || supplier.supplierTypeId || 1,\n              supplierTypeName: this.getSupplierTypeById(supplier.SupplierTypeId || supplier.supplierTypeId),\n              countryId: 1,\n              countryName: 'مصر',\n              balance: supplier.CurrentBalance || supplier.currentBalance || 0,\n              rating: supplier.Rating || supplier.rating || 0,\n              productsCount: Math.floor(Math.random() * 50) + 1,\n              status: supplier.IsActive !== false ? 'active' : 'inactive',\n              isActive: supplier.IsActive !== false,\n              createdAt: new Date(supplier.CreatedAt || supplier.createdAt || Date.now())\n            }));\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading suppliers:', error);\n            // Use mock data if API fails\n            this.suppliers = this.getMockSuppliers();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load supplier types from API\n     */\n    loadSupplierTypes() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/supplier-types').subscribe({\n          next: response => {\n            this.supplierTypes = response.supplierTypes || [];\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading supplier types:', error);\n            // Use mock data if API fails\n            this.supplierTypes = this.getMockSupplierTypes();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Load countries from API\n     */\n    loadCountries() {\n      return new Promise((resolve, reject) => {\n        const sub = this.http.get('http://localhost:5127/api/simple/countries').subscribe({\n          next: response => {\n            this.countries = response.countries || [];\n            resolve();\n          },\n          error: error => {\n            console.error('Error loading countries:', error);\n            // Use mock data if API fails\n            this.countries = this.getMockCountries();\n            resolve();\n          }\n        });\n        this.subscriptions.push(sub);\n      });\n    }\n    /**\n     * Calculate statistics\n     */\n    calculateStatistics() {\n      this.statistics.totalSuppliers = this.suppliers.length;\n      this.statistics.activeSuppliers = this.suppliers.filter(s => s.status === 'active').length;\n      this.statistics.localSuppliers = this.suppliers.filter(s => s.countryName === 'مصر').length;\n      this.statistics.internationalSuppliers = this.suppliers.filter(s => s.countryName !== 'مصر').length;\n    }\n    /**\n     * Apply filters\n     */\n    applyFilters() {\n      let filtered = [...this.suppliers];\n      // Search filter\n      if (this.searchTerm.trim()) {\n        const term = this.searchTerm.toLowerCase().trim();\n        filtered = filtered.filter(supplier => supplier.name.toLowerCase().includes(term) || supplier.code.toLowerCase().includes(term) || supplier.phone && supplier.phone.includes(term) || supplier.email && supplier.email.toLowerCase().includes(term));\n      }\n      // Supplier type filter\n      if (this.selectedSupplierType) {\n        filtered = filtered.filter(supplier => supplier.supplierTypeId.toString() === this.selectedSupplierType);\n      }\n      // Country filter\n      if (this.selectedCountry) {\n        filtered = filtered.filter(supplier => supplier.countryId.toString() === this.selectedCountry);\n      }\n      // Status filter\n      if (this.selectedStatus) {\n        filtered = filtered.filter(supplier => supplier.status === this.selectedStatus);\n      }\n      this.filteredSuppliers = filtered;\n      this.currentPage = 0;\n      this.updatePaginatedData();\n    }\n    /**\n     * Update paginated data\n     */\n    updatePaginatedData() {\n      const startIndex = this.currentPage * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      this.paginatedSuppliers = this.filteredSuppliers.slice(startIndex, endIndex);\n    }\n    /**\n     * Handle search\n     */\n    onSearch() {\n      this.applyFilters();\n    }\n    /**\n     * Handle filter change\n     */\n    onFilterChange() {\n      this.applyFilters();\n    }\n    /**\n     * Clear all filters\n     */\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedSupplierType = '';\n      this.selectedCountry = '';\n      this.selectedStatus = '';\n      this.applyFilters();\n    }\n    /**\n     * Handle page change\n     */\n    onPageChange(event) {\n      this.currentPage = event.pageIndex;\n      this.pageSize = event.pageSize;\n      this.updatePaginatedData();\n    }\n    /**\n     * Handle page size change\n     */\n    onPageSizeChange() {\n      this.currentPage = 0;\n      this.updatePaginatedData();\n    }\n    /**\n     * Get balance class for styling\n     */\n    getBalanceClass(balance) {\n      if (balance > 0) return 'positive';\n      if (balance < 0) return 'negative';\n      return 'zero';\n    }\n    /**\n     * Get status class for styling\n     */\n    getStatusClass(status) {\n      return status;\n    }\n    /**\n     * Get status text\n     */\n    getStatusText(status) {\n      switch (status) {\n        case 'active':\n          return 'نشط';\n        case 'inactive':\n          return 'غير نشط';\n        case 'blocked':\n          return 'محظور';\n        default:\n          return status;\n      }\n    }\n    /**\n     * Get stars array for rating display\n     */\n    getStarsArray(rating) {\n      const stars = [];\n      for (let i = 1; i <= 5; i++) {\n        stars.push(i <= rating);\n      }\n      return stars;\n    }\n    /**\n     * Open add supplier page\n     */\n    openAddSupplier() {\n      this.router.navigate(['/suppliers/add']);\n    }\n    /**\n     * View supplier details\n     */\n    viewSupplier(supplier) {\n      this.router.navigate(['/suppliers/details', supplier.id]);\n    }\n    /**\n     * Edit supplier\n     */\n    editSupplier(supplier) {\n      this.router.navigate(['/suppliers/edit', supplier.id]);\n    }\n    /**\n     * Delete supplier\n     */\n    deleteSupplier(supplier) {\n      if (confirm(`هل أنت متأكد من حذف المورد \"${supplier.name}\"؟`)) {\n        this.isLoading = true;\n        const sub = this.http.delete(`http://localhost:5127/api/simple/suppliers/${supplier.id}`).subscribe({\n          next: response => {\n            this.isLoading = false;\n            console.log('Supplier deleted successfully:', response);\n            // Remove supplier from local array\n            this.suppliers = this.suppliers.filter(s => s.id !== supplier.id);\n            this.applyFilters();\n            // Show success message\n            alert('تم حذف المورد بنجاح');\n          },\n          error: error => {\n            this.isLoading = false;\n            console.error('Error deleting supplier:', error);\n            alert('خطأ في حذف المورد');\n          }\n        });\n        this.subscriptions.push(sub);\n      }\n    }\n    /**\n     * View supplier products\n     */\n    viewSupplierProducts(supplier) {\n      console.log('View supplier products:', supplier);\n      // TODO: Implement view supplier products functionality\n      alert(`عرض منتجات المورد: ${supplier.name}`);\n    }\n    /**\n     * Export suppliers\n     */\n    exportSuppliers() {\n      console.log('Export suppliers');\n      // Implement export functionality\n    }\n    /**\n     * Import suppliers\n     */\n    importSuppliers() {\n      console.log('Import suppliers');\n      // Implement import functionality\n    }\n    /**\n     * Get mock suppliers data\n     */\n    getMockSuppliers() {\n      return [{\n        id: 1,\n        code: 'SUP001',\n        name: 'شركة النيل للتجارة',\n        phone: '+201234567890',\n        email: '<EMAIL>',\n        website: 'www.nile-trade.com',\n        address: 'شارع التحرير، وسط البلد',\n        city: 'القاهرة',\n        supplierTypeId: 1,\n        supplierTypeName: 'مورد محلي',\n        countryId: 1,\n        countryName: 'مصر',\n        balance: -25000,\n        rating: 4,\n        productsCount: 150,\n        status: 'active',\n        isActive: true,\n        createdAt: new Date('2023-01-15')\n      }, {\n        id: 2,\n        code: 'SUP002',\n        name: 'Global Electronics Ltd',\n        phone: '+8613800138000',\n        email: '<EMAIL>',\n        website: 'www.global-electronics.com',\n        address: 'Shenzhen Technology Park',\n        city: 'Shenzhen',\n        supplierTypeId: 2,\n        supplierTypeName: 'مورد دولي',\n        countryId: 2,\n        countryName: 'الصين',\n        balance: 50000,\n        rating: 5,\n        productsCount: 300,\n        status: 'active',\n        isActive: true,\n        createdAt: new Date('2023-03-20')\n      }, {\n        id: 3,\n        code: 'SUP003',\n        name: 'مصنع الإسكندرية للمنسوجات',\n        phone: '+201098765432',\n        email: '<EMAIL>',\n        website: 'www.alex-textiles.com',\n        address: 'المنطقة الصناعية، برج العرب',\n        city: 'الإسكندرية',\n        supplierTypeId: 3,\n        supplierTypeName: 'مصنع',\n        countryId: 1,\n        countryName: 'مصر',\n        balance: 0,\n        rating: 3,\n        productsCount: 75,\n        status: 'inactive',\n        isActive: false,\n        createdAt: new Date('2023-06-10')\n      }];\n    }\n    /**\n     * Get mock supplier types\n     */\n    getMockSupplierTypes() {\n      return [{\n        id: 1,\n        name: 'مورد محلي'\n      }, {\n        id: 2,\n        name: 'مورد دولي'\n      }, {\n        id: 3,\n        name: 'مصنع'\n      }, {\n        id: 4,\n        name: 'موزع'\n      }, {\n        id: 5,\n        name: 'وكيل حصري'\n      }];\n    }\n    /**\n     * Get mock countries\n     */\n    getMockCountries() {\n      return [{\n        id: 1,\n        name: 'مصر',\n        code: 'EG'\n      }, {\n        id: 2,\n        name: 'الصين',\n        code: 'CN'\n      }, {\n        id: 3,\n        name: 'تركيا',\n        code: 'TR'\n      }, {\n        id: 4,\n        name: 'الإمارات',\n        code: 'AE'\n      }, {\n        id: 5,\n        name: 'السعودية',\n        code: 'SA'\n      }, {\n        id: 6,\n        name: 'ألمانيا',\n        code: 'DE'\n      }, {\n        id: 7,\n        name: 'إيطاليا',\n        code: 'IT'\n      }];\n    }\n    /**\n     * Extract city from address\n     */\n    extractCityFromAddress(address) {\n      if (!address) return '';\n      const cities = ['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية', 'القليوبية', 'البحيرة', 'الغربية'];\n      for (const city of cities) {\n        if (address.includes(city)) {\n          return city;\n        }\n      }\n      return 'غير محدد';\n    }\n    /**\n     * Get supplier type by ID\n     */\n    getSupplierTypeById(typeId) {\n      const types = {\n        1: 'محلي',\n        2: 'دولي',\n        3: 'حكومي'\n      };\n      return types[typeId] || 'محلي';\n    }\n    static ɵfac = function SuppliersNewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SuppliersNewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuppliersNewComponent,\n      selectors: [[\"app-suppliers-new\"]],\n      decls: 157,\n      vars: 20,\n      consts: [[1, \"suppliers-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"export-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"import-btn\", 3, \"click\"], [1, \"filters-section\"], [1, \"filters-card\"], [1, \"filters-grid\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"\\u0627\\u0628\\u062D\\u062B \\u0628\\u0627\\u0644\\u0627\\u0633\\u0645\\u060C \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\\u060C \\u0623\\u0648 \\u0627\\u0644\\u0631\\u0642\\u0645...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\"], [3, \"ngModelChange\", \"selectionChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"active\"], [\"value\", \"inactive\"], [\"value\", \"blocked\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"stats-section\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total-suppliers\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-card\", \"active-suppliers\"], [1, \"stat-card\", \"local-suppliers\"], [1, \"stat-card\", \"international-suppliers\"], [1, \"table-section\"], [1, \"table-card\"], [1, \"table-header\"], [1, \"table-title\"], [1, \"results-count\"], [1, \"table-actions\"], [\"appearance\", \"outline\", 1, \"page-size-field\"], [\"value\", \"10\"], [\"value\", \"25\"], [\"value\", \"50\"], [\"value\", \"100\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"suppliers-table\", 3, \"dataSource\"], [\"matColumnDef\", \"code\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"name\"], [\"matColumnDef\", \"contact\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"location\"], [\"matColumnDef\", \"products\"], [\"matColumnDef\", \"balance\"], [\"matColumnDef\", \"rating\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"table-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [3, \"value\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"supplier-code\"], [1, \"supplier-info\"], [1, \"supplier-name\"], [1, \"supplier-type\"], [\"mat-header-cell\", \"\"], [1, \"contact-info\"], [\"class\", \"phone\", 4, \"ngIf\"], [\"class\", \"email\", 4, \"ngIf\"], [\"class\", \"website\", 4, \"ngIf\"], [1, \"phone\"], [1, \"email\"], [1, \"website\"], [1, \"location-info\"], [1, \"country\"], [\"class\", \"city\", 4, \"ngIf\"], [1, \"city\"], [1, \"products-count\"], [1, \"count\"], [1, \"label\"], [1, \"balance\"], [1, \"rating\"], [1, \"stars\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-value\"], [1, \"status-badge\"], [1, \"actions-buttons\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"\\u062A\\u0639\\u062F\\u064A\\u0644\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"\\u062D\\u0630\\u0641\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"table-row\", 3, \"click\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"]],\n      template: function SuppliersNewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0644\\u062C\\u0645\\u064A\\u0639 \\u0645\\u0648\\u0631\\u062F\\u064A \\u0627\\u0644\\u0645\\u062A\\u062C\\u0631 \\u0648\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function SuppliersNewComponent_Template_button_click_9_listener() {\n            return ctx.openAddSupplier();\n          });\n          i0.ɵɵelementStart(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0648\\u0631\\u062F \\u062C\\u062F\\u064A\\u062F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function SuppliersNewComponent_Template_button_click_14_listener() {\n            return ctx.exportSuppliers();\n          });\n          i0.ɵɵelementStart(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"file_download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"\\u062A\\u0635\\u062F\\u064A\\u0631\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function SuppliersNewComponent_Template_button_click_19_listener() {\n            return ctx.importSuppliers();\n          });\n          i0.ɵɵelementStart(20, \"mat-icon\");\n          i0.ɵɵtext(21, \"file_upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"mat-card\", 11)(26, \"mat-card-content\")(27, \"div\", 12)(28, \"mat-form-field\", 13)(29, \"mat-label\");\n          i0.ɵɵtext(30, \"\\u0627\\u0644\\u0628\\u062D\\u062B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SuppliersNewComponent_Template_input_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function SuppliersNewComponent_Template_input_input_31_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-icon\", 15);\n          i0.ɵɵtext(33, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 16)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SuppliersNewComponent_Template_mat_select_ngModelChange_37_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedSupplierType, $event) || (ctx.selectedSupplierType = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function SuppliersNewComponent_Template_mat_select_selectionChange_37_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(38, \"mat-option\", 18);\n          i0.ɵɵtext(39, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0646\\u0648\\u0627\\u0639\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, SuppliersNewComponent_mat_option_40_Template, 2, 2, \"mat-option\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 16)(42, \"mat-label\");\n          i0.ɵɵtext(43, \"\\u0627\\u0644\\u0628\\u0644\\u062F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SuppliersNewComponent_Template_mat_select_ngModelChange_44_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCountry, $event) || (ctx.selectedCountry = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function SuppliersNewComponent_Template_mat_select_selectionChange_44_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(45, \"mat-option\", 18);\n          i0.ɵɵtext(46, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u0644\\u062F\\u0627\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, SuppliersNewComponent_mat_option_47_Template, 2, 2, \"mat-option\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"mat-form-field\", 16)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SuppliersNewComponent_Template_mat_select_ngModelChange_51_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function SuppliersNewComponent_Template_mat_select_selectionChange_51_listener() {\n            return ctx.onFilterChange();\n          });\n          i0.ɵɵelementStart(52, \"mat-option\", 18);\n          i0.ɵɵtext(53, \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0627\\u0644\\u0627\\u062A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-option\", 20);\n          i0.ɵɵtext(55, \"\\u0646\\u0634\\u0637\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"mat-option\", 21);\n          i0.ɵɵtext(57, \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"mat-option\", 22);\n          i0.ɵɵtext(59, \"\\u0645\\u062D\\u0638\\u0648\\u0631\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SuppliersNewComponent_Template_button_click_60_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵelementStart(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\");\n          i0.ɵɵtext(64, \"\\u0645\\u0633\\u062D \\u0627\\u0644\\u0641\\u0644\\u0627\\u062A\\u0631\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(65, \"div\", 24)(66, \"div\", 25)(67, \"div\", 26)(68, \"div\", 27)(69, \"mat-icon\");\n          i0.ɵɵtext(70, \"business\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 28)(72, \"div\", 29);\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 30);\n          i0.ɵɵtext(75, \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 31)(77, \"div\", 27)(78, \"mat-icon\");\n          i0.ɵɵtext(79, \"verified\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 28)(81, \"div\", 29);\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 30);\n          i0.ɵɵtext(84, \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(85, \"div\", 32)(86, \"div\", 27)(87, \"mat-icon\");\n          i0.ɵɵtext(88, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 28)(90, \"div\", 29);\n          i0.ɵɵtext(91);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"div\", 30);\n          i0.ɵɵtext(93, \"\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0645\\u062D\\u0644\\u064A\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 33)(95, \"div\", 27)(96, \"mat-icon\");\n          i0.ɵɵtext(97, \"public\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 28)(99, \"div\", 29);\n          i0.ɵɵtext(100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 30);\n          i0.ɵɵtext(102, \"\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u062F\\u0648\\u0644\\u064A\\u064A\\u0646\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(103, \"div\", 34)(104, \"mat-card\", 35)(105, \"div\", 36)(106, \"div\", 37)(107, \"h3\");\n          i0.ɵɵtext(108, \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"span\", 38);\n          i0.ɵɵtext(110);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(111, \"div\", 39)(112, \"mat-form-field\", 40)(113, \"mat-label\");\n          i0.ɵɵtext(114, \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"mat-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SuppliersNewComponent_Template_mat_select_ngModelChange_115_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function SuppliersNewComponent_Template_mat_select_selectionChange_115_listener() {\n            return ctx.onPageSizeChange();\n          });\n          i0.ɵɵelementStart(116, \"mat-option\", 41);\n          i0.ɵɵtext(117, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"mat-option\", 42);\n          i0.ɵɵtext(119, \"25\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"mat-option\", 43);\n          i0.ɵɵtext(121, \"50\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"mat-option\", 44);\n          i0.ɵɵtext(123, \"100\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(124, \"div\", 45)(125, \"table\", 46);\n          i0.ɵɵelementContainerStart(126, 47);\n          i0.ɵɵtemplate(127, SuppliersNewComponent_th_127_Template, 2, 0, \"th\", 48)(128, SuppliersNewComponent_td_128_Template, 3, 1, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(129, 50);\n          i0.ɵɵtemplate(130, SuppliersNewComponent_th_130_Template, 2, 0, \"th\", 48)(131, SuppliersNewComponent_td_131_Template, 6, 2, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(132, 51);\n          i0.ɵɵtemplate(133, SuppliersNewComponent_th_133_Template, 2, 0, \"th\", 52)(134, SuppliersNewComponent_td_134_Template, 5, 3, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(135, 53);\n          i0.ɵɵtemplate(136, SuppliersNewComponent_th_136_Template, 2, 0, \"th\", 52)(137, SuppliersNewComponent_td_137_Template, 8, 2, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(138, 54);\n          i0.ɵɵtemplate(139, SuppliersNewComponent_th_139_Template, 2, 0, \"th\", 48)(140, SuppliersNewComponent_td_140_Template, 6, 1, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(141, 55);\n          i0.ɵɵtemplate(142, SuppliersNewComponent_th_142_Template, 2, 0, \"th\", 48)(143, SuppliersNewComponent_td_143_Template, 4, 6, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(144, 56);\n          i0.ɵɵtemplate(145, SuppliersNewComponent_th_145_Template, 2, 0, \"th\", 48)(146, SuppliersNewComponent_td_146_Template, 6, 2, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(147, 57);\n          i0.ɵɵtemplate(148, SuppliersNewComponent_th_148_Template, 2, 0, \"th\", 52)(149, SuppliersNewComponent_td_149_Template, 3, 3, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(150, 58);\n          i0.ɵɵtemplate(151, SuppliersNewComponent_th_151_Template, 2, 0, \"th\", 52)(152, SuppliersNewComponent_td_152_Template, 14, 0, \"td\", 49);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(153, SuppliersNewComponent_tr_153_Template, 1, 0, \"tr\", 59)(154, SuppliersNewComponent_tr_154_Template, 1, 0, \"tr\", 60);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(155, \"mat-paginator\", 61);\n          i0.ɵɵlistener(\"page\", function SuppliersNewComponent_Template_mat_paginator_page_155_listener($event) {\n            return ctx.onPageChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(156, SuppliersNewComponent_div_156_Template, 4, 0, \"div\", 62);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedSupplierType);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.supplierTypes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCountry);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedStatus);\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate(ctx.statistics.totalSuppliers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.activeSuppliers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.localSuppliers);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.statistics.internationalSuppliers);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"(\", ctx.filteredSuppliers.length, \" \\u0645\\u0648\\u0631\\u062F)\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.pageSize);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"dataSource\", ctx.paginatedSuppliers);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.filteredSuppliers.length)(\"pageSize\", ctx.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(19, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, MatCardModule, i5.MatCard, i5.MatCardContent, MatButtonModule, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatFormFieldModule, i8.MatFormField, i8.MatLabel, i8.MatSuffix, MatInputModule, i9.MatInput, MatSelectModule, i10.MatSelect, i10.MatOption, MatTableModule, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, MatPaginatorModule, i12.MatPaginator, MatSortModule, i13.MatSort, i13.MatSortHeader, MatProgressSpinnerModule, i14.MatProgressSpinner, MatTooltipModule, i15.MatTooltip],\n      styles: [\"\\n\\n.suppliers-container[_ngcontent-%COMP%] {\\n  padding: 0;\\n  background: transparent;\\n  min-height: 100vh;\\n  width: 100%;\\n  max-width: 100%;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-600) 0%, var(--primary-600) 100%);\\n  color: white;\\n  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-3xl);\\n  margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-2xl);\\n  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  position: relative;\\n  overflow: hidden;\\n  box-sizing: border-box;\\n}\\n.page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grid\\\" width=\\\"10\\\" height=\\\"10\\\" patternUnits=\\\"userSpaceOnUse\\\"><path d=\\\"M 10 0 L 0 0 0 10\\\" fill=\\\"none\\\" stroke=\\\"rgba(255,255,255,0.1)\\\" stroke-width=\\\"0.5\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grid)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 800;\\n  margin: 0 0 var(--spacing-sm) 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  align-items: center;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%] {\\n  background: var(--success-500) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-xl) !important;\\n  font-weight: 600 !important;\\n  box-shadow: var(--shadow-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--success-600) !important;\\n  transform: translateY(-2px) !important;\\n  box-shadow: var(--shadow-xl) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%], .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .import-btn[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.5) !important;\\n  color: white !important;\\n  padding: var(--spacing-md) var(--spacing-lg) !important;\\n}\\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]:hover, .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .import-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-color: white !important;\\n}\\n\\n\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n}\\n.filters-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: var(--spacing-2xl) !important;\\n}\\n\\n.filters-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr auto;\\n  gap: var(--spacing-xl);\\n  align-items: end;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  padding: 0 var(--spacing-lg) !important;\\n  color: var(--gray-600) !important;\\n  border-color: var(--gray-300) !important;\\n}\\n.filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--gray-50) !important;\\n  color: var(--gray-800) !important;\\n}\\n\\n\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-xl);\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  padding: var(--spacing-xl);\\n  box-shadow: var(--shadow-lg);\\n  border: 1px solid var(--gray-200);\\n  position: relative;\\n  overflow: hidden;\\n  transition: all var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-lg);\\n  min-height: 100px;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--shadow-2xl);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: var(--radius-xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: white;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 800;\\n  color: var(--gray-900);\\n  line-height: 1;\\n  margin-bottom: var(--spacing-xs);\\n}\\n.stat-card[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n.stat-card.total-suppliers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));\\n}\\n.stat-card.active-suppliers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\\n}\\n.stat-card.local-suppliers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\\n}\\n.stat-card.international-suppliers[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\\n}\\n\\n\\n\\n.table-section[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-2xl);\\n}\\n\\n.table-card[_ngcontent-%COMP%] {\\n  border-radius: var(--radius-xl) !important;\\n  box-shadow: var(--shadow-lg) !important;\\n  border: 1px solid var(--gray-200) !important;\\n  overflow: hidden !important;\\n}\\n.table-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-xl) var(--spacing-2xl);\\n  border-bottom: 1px solid var(--gray-200);\\n  background: var(--gray-50);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: var(--gray-900);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-title[_ngcontent-%COMP%]   .results-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-500);\\n  background: var(--gray-200);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-md);\\n}\\n.table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%]   .page-size-field[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-height: 600px;\\n}\\n\\n.suppliers-table[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  background: white !important;\\n}\\n.suppliers-table[_ngcontent-%COMP%]   .mat-mdc-header-row[_ngcontent-%COMP%] {\\n  background: var(--gray-50) !important;\\n}\\n.suppliers-table[_ngcontent-%COMP%]   .mat-mdc-header-row[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 700 !important;\\n  color: var(--gray-800) !important;\\n  border-bottom: 2px solid var(--gray-200) !important;\\n  padding: var(--spacing-lg) !important;\\n  font-family: var(--font-family-primary) !important;\\n  font-size: 0.875rem !important;\\n}\\n.suppliers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast) !important;\\n  cursor: pointer !important;\\n}\\n.suppliers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]:hover {\\n  background: var(--secondary-50) !important;\\n}\\n.suppliers-table[_ngcontent-%COMP%]   .mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg) !important;\\n  border-bottom: 1px solid var(--gray-100) !important;\\n  font-family: var(--font-family-primary) !important;\\n  vertical-align: middle !important;\\n}\\n\\n\\n\\n.supplier-code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 600;\\n  color: var(--secondary-600);\\n  background: var(--secondary-50);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-sm);\\n  font-size: 0.875rem;\\n}\\n\\n.supplier-info[_ngcontent-%COMP%]   .supplier-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--gray-900);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.supplier-info[_ngcontent-%COMP%]   .supplier-type[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  background: var(--gray-100);\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--radius-sm);\\n  display: inline-block;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-xs);\\n}\\n.contact-info[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .website[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  font-size: 0.875rem;\\n}\\n.contact-info[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .contact-info[_ngcontent-%COMP%]   .website[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: var(--gray-500);\\n}\\n\\n.location-info[_ngcontent-%COMP%]   .country[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  font-weight: 600;\\n  color: var(--gray-800);\\n  margin-bottom: var(--spacing-xs);\\n}\\n.location-info[_ngcontent-%COMP%]   .country[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: var(--warning-500);\\n}\\n.location-info[_ngcontent-%COMP%]   .city[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--gray-600);\\n}\\n\\n.products-count[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.products-count[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: var(--primary-600);\\n  display: block;\\n}\\n.products-count[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n}\\n\\n.balance[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 0.875rem;\\n}\\n.balance.positive[_ngcontent-%COMP%] {\\n  color: var(--success-600);\\n}\\n.balance.negative[_ngcontent-%COMP%] {\\n  color: var(--error-600);\\n}\\n.balance.zero[_ngcontent-%COMP%] {\\n  color: var(--gray-500);\\n}\\n\\n.rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n}\\n.rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   mat-icon.filled[_ngcontent-%COMP%] {\\n  color: var(--warning-500);\\n}\\n.rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   mat-icon.empty[_ngcontent-%COMP%] {\\n  color: var(--gray-300);\\n}\\n.rating[_ngcontent-%COMP%]   .rating-value[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xs) var(--spacing-md);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.status-badge.active[_ngcontent-%COMP%] {\\n  background: var(--success-100);\\n  color: var(--success-700);\\n}\\n.status-badge.inactive[_ngcontent-%COMP%] {\\n  background: var(--gray-100);\\n  color: var(--gray-700);\\n}\\n.status-badge.blocked[_ngcontent-%COMP%] {\\n  background: var(--error-100);\\n  color: var(--error-700);\\n}\\n\\n.actions-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n}\\n.actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px !important;\\n  height: 36px !important;\\n  min-width: 36px !important;\\n}\\n.actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.125rem !important;\\n  width: 1.125rem !important;\\n  height: 1.125rem !important;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n.loading-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-lg);\\n  color: var(--gray-600);\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .filters-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr 1fr;\\n  }\\n  .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n    grid-column: span 3;\\n    justify-self: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl);\\n    margin: calc(-1 * var(--spacing-2xl)) calc(-1 * var(--spacing-2xl)) var(--spacing-xl);\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-lg);\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n  }\\n  .filters-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: var(--spacing-lg);\\n  }\\n  .table-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-md);\\n    align-items: stretch;\\n  }\\n  .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%] {\\n    align-self: center;\\n  }\\n  .suppliers-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%], \\n   .suppliers-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) !important;\\n    font-size: 0.875rem !important;\\n  }\\n  .actions-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: var(--spacing-xs);\\n  }\\n  .actions-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 32px !important;\\n    height: 32px !important;\\n    min-width: 32px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SuppliersNewComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatProgressSpinnerModule", "MatTooltipModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "country_r2", "ɵɵtextInterpolate", "supplier_r3", "code", "supplier_r4", "supplierTypeName", "supplier_r5", "phone", "email", "website", "ɵɵtemplate", "SuppliersNewComponent_td_134_div_2_Template", "SuppliersNewComponent_td_134_div_3_Template", "SuppliersNewComponent_td_134_div_4_Template", "supplier_r6", "city", "SuppliersNewComponent_td_137_div_7_Template", "countryName", "supplier_r7", "productsCount", "ɵɵclassMap", "ctx_r8", "getBalanceClass", "supplier_r8", "balance", "ɵɵpipeBind2", "star_r10", "SuppliersNewComponent_td_146_mat_icon_3_Template", "getStarsArray", "supplier_r11", "rating", "getStatusClass", "supplier_r12", "status", "getStatusText", "ɵɵlistener", "SuppliersNewComponent_td_152_Template_button_click_2_listener", "supplier_r14", "ɵɵrestoreView", "_r13", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewSupplier", "SuppliersNewComponent_td_152_Template_button_click_5_listener", "editSupplier", "SuppliersNewComponent_td_152_Template_button_click_8_listener", "deleteSupplier", "SuppliersNewComponent_td_152_Template_button_click_11_listener", "viewSupplierProducts", "ɵɵelement", "SuppliersNewComponent_tr_154_Template_tr_click_0_listener", "row_r16", "_r15", "SuppliersNewComponent", "http", "router", "isLoading", "suppliers", "filteredSuppliers", "paginatedSuppliers", "supplierTypes", "countries", "statistics", "totalSuppliers", "activeSuppliers", "localSuppliers", "internationalSuppliers", "searchTerm", "selectedSupplierType", "selectedCountry", "selectedStatus", "displayedColumns", "pageSize", "currentPage", "subscriptions", "constructor", "ngOnInit", "loadInitialData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "Promise", "all", "loadSuppliers", "loadSupplierTypes", "loadCountries", "then", "calculateStatistics", "applyFilters", "catch", "error", "console", "resolve", "reject", "get", "subscribe", "next", "response", "log", "map", "supplier", "Id", "SupplierCode", "supplierCode", "NameAr", "nameAr", "nameEn", "NameEn", "Phone", "phone1", "Phone1", "Email", "Website", "address", "Address", "extractCityFromAddress", "supplierTypeId", "SupplierTypeId", "getSupplierTypeById", "countryId", "CurrentBalance", "currentBalance", "Rating", "Math", "floor", "random", "IsActive", "isActive", "createdAt", "Date", "CreatedAt", "now", "getMockSuppliers", "push", "getMockSupplierTypes", "getMockCountries", "length", "filter", "s", "filtered", "trim", "term", "toLowerCase", "includes", "toString", "updatePaginatedData", "startIndex", "endIndex", "slice", "onSearch", "onFilterChange", "clearFilters", "onPageChange", "event", "pageIndex", "onPageSizeChange", "stars", "i", "openAddSupplier", "navigate", "confirm", "delete", "alert", "exportSuppliers", "importSuppliers", "cities", "typeId", "types", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "SuppliersNewComponent_Template", "rf", "ctx", "SuppliersNewComponent_Template_button_click_9_listener", "SuppliersNewComponent_Template_button_click_14_listener", "SuppliersNewComponent_Template_button_click_19_listener", "ɵɵtwoWayListener", "SuppliersNewComponent_Template_input_ngModelChange_31_listener", "$event", "ɵɵtwoWayBindingSet", "SuppliersNewComponent_Template_input_input_31_listener", "SuppliersNewComponent_Template_mat_select_ngModelChange_37_listener", "SuppliersNewComponent_Template_mat_select_selectionChange_37_listener", "SuppliersNewComponent_mat_option_40_Template", "SuppliersNewComponent_Template_mat_select_ngModelChange_44_listener", "SuppliersNewComponent_Template_mat_select_selectionChange_44_listener", "SuppliersNewComponent_mat_option_47_Template", "SuppliersNewComponent_Template_mat_select_ngModelChange_51_listener", "SuppliersNewComponent_Template_mat_select_selectionChange_51_listener", "SuppliersNewComponent_Template_button_click_60_listener", "SuppliersNewComponent_Template_mat_select_ngModelChange_115_listener", "SuppliersNewComponent_Template_mat_select_selectionChange_115_listener", "ɵɵelementContainerStart", "SuppliersNewComponent_th_127_Template", "SuppliersNewComponent_td_128_Template", "SuppliersNewComponent_th_130_Template", "SuppliersNewComponent_td_131_Template", "SuppliersNewComponent_th_133_Template", "SuppliersNewComponent_td_134_Template", "SuppliersNewComponent_th_136_Template", "SuppliersNewComponent_td_137_Template", "SuppliersNewComponent_th_139_Template", "SuppliersNewComponent_td_140_Template", "SuppliersNewComponent_th_142_Template", "SuppliersNewComponent_td_143_Template", "SuppliersNewComponent_th_145_Template", "SuppliersNewComponent_td_146_Template", "SuppliersNewComponent_th_148_Template", "SuppliersNewComponent_td_149_Template", "SuppliersNewComponent_th_151_Template", "SuppliersNewComponent_td_152_Template", "SuppliersNewComponent_tr_153_Template", "SuppliersNewComponent_tr_154_Template", "SuppliersNewComponent_Template_mat_paginator_page_155_listener", "SuppliersNewComponent_div_156_Template", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "i6", "MatButton", "MatIconButton", "i7", "MatIcon", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i9", "MatInput", "i10", "MatSelect", "MatOption", "i11", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i12", "MatPaginator", "i13", "MatSort", "Mat<PERSON>ort<PERSON><PERSON>er", "i14", "MatProgressSpinner", "i15", "MatTooltip", "styles"], "sources": ["X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\suppliers-new.component.ts", "X:\\barmaga\\Angler\\Erp 2\\database\\Terra.Retail.Web\\src\\app\\modules\\suppliers\\suppliers-new.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\n\n// Angular Material Imports\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule, PageEvent } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Interfaces\ninterface Supplier {\n  id: number;\n  code: string;\n  name: string;\n  phone?: string;\n  email?: string;\n  website?: string;\n  address?: string;\n  city?: string;\n  supplierTypeId: number;\n  supplierTypeName: string;\n  countryId: number;\n  countryName: string;\n  balance: number;\n  rating: number;\n  productsCount: number;\n  status: 'active' | 'inactive' | 'blocked';\n  isActive: boolean;\n  createdAt: Date;\n}\n\ninterface SupplierType {\n  id: number;\n  name: string;\n}\n\ninterface Country {\n  id: number;\n  name: string;\n  code: string;\n}\n\ninterface Statistics {\n  totalSuppliers: number;\n  activeSuppliers: number;\n  localSuppliers: number;\n  internationalSuppliers: number;\n}\n\n@Component({\n  selector: 'app-suppliers-new',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule\n  ],\n  templateUrl: './suppliers-new.component.html',\n  styleUrls: ['./suppliers-new.component.scss']\n})\nexport class SuppliersNewComponent implements OnInit, OnDestroy {\n\n  // Component State\n  isLoading = true;\n  \n  // Data\n  suppliers: Supplier[] = [];\n  filteredSuppliers: Supplier[] = [];\n  paginatedSuppliers: Supplier[] = [];\n  supplierTypes: SupplierType[] = [];\n  countries: Country[] = [];\n  \n  // Statistics\n  statistics: Statistics = {\n    totalSuppliers: 0,\n    activeSuppliers: 0,\n    localSuppliers: 0,\n    internationalSuppliers: 0\n  };\n\n  // Filters\n  searchTerm = '';\n  selectedSupplierType = '';\n  selectedCountry = '';\n  selectedStatus = '';\n\n  // Table Configuration\n  displayedColumns: string[] = ['code', 'name', 'contact', 'location', 'products', 'balance', 'rating', 'status', 'actions'];\n  pageSize = 25;\n  currentPage = 0;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadInitialData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Load initial data\n   */\n  private loadInitialData(): void {\n    this.isLoading = true;\n    \n    // Load all data in parallel\n    Promise.all([\n      this.loadSuppliers(),\n      this.loadSupplierTypes(),\n      this.loadCountries()\n    ]).then(() => {\n      this.calculateStatistics();\n      this.applyFilters();\n      this.isLoading = false;\n    }).catch(error => {\n      console.error('Error loading data:', error);\n      this.isLoading = false;\n    });\n  }\n\n  /**\n   * Load suppliers from API\n   */\n  private loadSuppliers(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/suppliers').subscribe({\n        next: (response) => {\n          console.log('API Response:', response);\n          // Map API response to match our interface\n          this.suppliers = (response.suppliers || []).map((supplier: any) => ({\n            id: supplier.Id || supplier.id,\n            code: supplier.SupplierCode || supplier.supplierCode || supplier.code,\n            name: supplier.NameAr || supplier.nameAr || supplier.name,\n            nameEn: supplier.NameEn || supplier.nameEn,\n            phone: supplier.Phone || supplier.phone1 || supplier.Phone1,\n            email: supplier.Email || supplier.email,\n            website: supplier.Website || supplier.website,\n            address: supplier.Address || supplier.address,\n            city: this.extractCityFromAddress(supplier.Address || supplier.address),\n            supplierTypeId: supplier.SupplierTypeId || supplier.supplierTypeId || 1,\n            supplierTypeName: this.getSupplierTypeById(supplier.SupplierTypeId || supplier.supplierTypeId),\n            countryId: 1,\n            countryName: 'مصر',\n            balance: supplier.CurrentBalance || supplier.currentBalance || 0,\n            rating: supplier.Rating || supplier.rating || 0,\n            productsCount: Math.floor(Math.random() * 50) + 1,\n            status: supplier.IsActive !== false ? 'active' : 'inactive',\n            isActive: supplier.IsActive !== false,\n            createdAt: new Date(supplier.CreatedAt || supplier.createdAt || Date.now())\n          }));\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading suppliers:', error);\n          // Use mock data if API fails\n          this.suppliers = this.getMockSuppliers();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load supplier types from API\n   */\n  private loadSupplierTypes(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/supplier-types').subscribe({\n        next: (response) => {\n          this.supplierTypes = response.supplierTypes || [];\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading supplier types:', error);\n          // Use mock data if API fails\n          this.supplierTypes = this.getMockSupplierTypes();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Load countries from API\n   */\n  private loadCountries(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const sub = this.http.get<any>('http://localhost:5127/api/simple/countries').subscribe({\n        next: (response) => {\n          this.countries = response.countries || [];\n          resolve();\n        },\n        error: (error) => {\n          console.error('Error loading countries:', error);\n          // Use mock data if API fails\n          this.countries = this.getMockCountries();\n          resolve();\n        }\n      });\n      this.subscriptions.push(sub);\n    });\n  }\n\n  /**\n   * Calculate statistics\n   */\n  private calculateStatistics(): void {\n    this.statistics.totalSuppliers = this.suppliers.length;\n    this.statistics.activeSuppliers = this.suppliers.filter(s => s.status === 'active').length;\n    this.statistics.localSuppliers = this.suppliers.filter(s => s.countryName === 'مصر').length;\n    this.statistics.internationalSuppliers = this.suppliers.filter(s => s.countryName !== 'مصر').length;\n  }\n\n  /**\n   * Apply filters\n   */\n  applyFilters(): void {\n    let filtered = [...this.suppliers];\n\n    // Search filter\n    if (this.searchTerm.trim()) {\n      const term = this.searchTerm.toLowerCase().trim();\n      filtered = filtered.filter(supplier => \n        supplier.name.toLowerCase().includes(term) ||\n        supplier.code.toLowerCase().includes(term) ||\n        (supplier.phone && supplier.phone.includes(term)) ||\n        (supplier.email && supplier.email.toLowerCase().includes(term))\n      );\n    }\n\n    // Supplier type filter\n    if (this.selectedSupplierType) {\n      filtered = filtered.filter(supplier => \n        supplier.supplierTypeId.toString() === this.selectedSupplierType\n      );\n    }\n\n    // Country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(supplier => \n        supplier.countryId.toString() === this.selectedCountry\n      );\n    }\n\n    // Status filter\n    if (this.selectedStatus) {\n      filtered = filtered.filter(supplier => supplier.status === this.selectedStatus);\n    }\n\n    this.filteredSuppliers = filtered;\n    this.currentPage = 0;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Update paginated data\n   */\n  private updatePaginatedData(): void {\n    const startIndex = this.currentPage * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedSuppliers = this.filteredSuppliers.slice(startIndex, endIndex);\n  }\n\n  /**\n   * Handle search\n   */\n  onSearch(): void {\n    this.applyFilters();\n  }\n\n  /**\n   * Handle filter change\n   */\n  onFilterChange(): void {\n    this.applyFilters();\n  }\n\n  /**\n   * Clear all filters\n   */\n  clearFilters(): void {\n    this.searchTerm = '';\n    this.selectedSupplierType = '';\n    this.selectedCountry = '';\n    this.selectedStatus = '';\n    this.applyFilters();\n  }\n\n  /**\n   * Handle page change\n   */\n  onPageChange(event: PageEvent): void {\n    this.currentPage = event.pageIndex;\n    this.pageSize = event.pageSize;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Handle page size change\n   */\n  onPageSizeChange(): void {\n    this.currentPage = 0;\n    this.updatePaginatedData();\n  }\n\n  /**\n   * Get balance class for styling\n   */\n  getBalanceClass(balance: number): string {\n    if (balance > 0) return 'positive';\n    if (balance < 0) return 'negative';\n    return 'zero';\n  }\n\n  /**\n   * Get status class for styling\n   */\n  getStatusClass(status: string): string {\n    return status;\n  }\n\n  /**\n   * Get status text\n   */\n  getStatusText(status: string): string {\n    switch (status) {\n      case 'active': return 'نشط';\n      case 'inactive': return 'غير نشط';\n      case 'blocked': return 'محظور';\n      default: return status;\n    }\n  }\n\n  /**\n   * Get stars array for rating display\n   */\n  getStarsArray(rating: number): boolean[] {\n    const stars = [];\n    for (let i = 1; i <= 5; i++) {\n      stars.push(i <= rating);\n    }\n    return stars;\n  }\n\n  /**\n   * Open add supplier page\n   */\n  openAddSupplier(): void {\n    this.router.navigate(['/suppliers/add']);\n  }\n\n  /**\n   * View supplier details\n   */\n  viewSupplier(supplier: Supplier): void {\n    this.router.navigate(['/suppliers/details', supplier.id]);\n  }\n\n  /**\n   * Edit supplier\n   */\n  editSupplier(supplier: Supplier): void {\n    this.router.navigate(['/suppliers/edit', supplier.id]);\n  }\n\n  /**\n   * Delete supplier\n   */\n  deleteSupplier(supplier: Supplier): void {\n    if (confirm(`هل أنت متأكد من حذف المورد \"${supplier.name}\"؟`)) {\n      this.isLoading = true;\n\n      const sub = this.http.delete(`http://localhost:5127/api/simple/suppliers/${supplier.id}`).subscribe({\n        next: (response: any) => {\n          this.isLoading = false;\n          console.log('Supplier deleted successfully:', response);\n\n          // Remove supplier from local array\n          this.suppliers = this.suppliers.filter(s => s.id !== supplier.id);\n          this.applyFilters();\n\n          // Show success message\n          alert('تم حذف المورد بنجاح');\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Error deleting supplier:', error);\n          alert('خطأ في حذف المورد');\n        }\n      });\n\n      this.subscriptions.push(sub);\n    }\n  }\n\n  /**\n   * View supplier products\n   */\n  viewSupplierProducts(supplier: Supplier): void {\n    console.log('View supplier products:', supplier);\n    // TODO: Implement view supplier products functionality\n    alert(`عرض منتجات المورد: ${supplier.name}`);\n  }\n\n  /**\n   * Export suppliers\n   */\n  exportSuppliers(): void {\n    console.log('Export suppliers');\n    // Implement export functionality\n  }\n\n  /**\n   * Import suppliers\n   */\n  importSuppliers(): void {\n    console.log('Import suppliers');\n    // Implement import functionality\n  }\n\n  /**\n   * Get mock suppliers data\n   */\n  private getMockSuppliers(): Supplier[] {\n    return [\n      {\n        id: 1,\n        code: 'SUP001',\n        name: 'شركة النيل للتجارة',\n        phone: '+201234567890',\n        email: '<EMAIL>',\n        website: 'www.nile-trade.com',\n        address: 'شارع التحرير، وسط البلد',\n        city: 'القاهرة',\n        supplierTypeId: 1,\n        supplierTypeName: 'مورد محلي',\n        countryId: 1,\n        countryName: 'مصر',\n        balance: -25000,\n        rating: 4,\n        productsCount: 150,\n        status: 'active',\n        isActive: true,\n        createdAt: new Date('2023-01-15')\n      },\n      {\n        id: 2,\n        code: 'SUP002',\n        name: 'Global Electronics Ltd',\n        phone: '+8613800138000',\n        email: '<EMAIL>',\n        website: 'www.global-electronics.com',\n        address: 'Shenzhen Technology Park',\n        city: 'Shenzhen',\n        supplierTypeId: 2,\n        supplierTypeName: 'مورد دولي',\n        countryId: 2,\n        countryName: 'الصين',\n        balance: 50000,\n        rating: 5,\n        productsCount: 300,\n        status: 'active',\n        isActive: true,\n        createdAt: new Date('2023-03-20')\n      },\n      {\n        id: 3,\n        code: 'SUP003',\n        name: 'مصنع الإسكندرية للمنسوجات',\n        phone: '+201098765432',\n        email: '<EMAIL>',\n        website: 'www.alex-textiles.com',\n        address: 'المنطقة الصناعية، برج العرب',\n        city: 'الإسكندرية',\n        supplierTypeId: 3,\n        supplierTypeName: 'مصنع',\n        countryId: 1,\n        countryName: 'مصر',\n        balance: 0,\n        rating: 3,\n        productsCount: 75,\n        status: 'inactive',\n        isActive: false,\n        createdAt: new Date('2023-06-10')\n      }\n    ];\n  }\n\n  /**\n   * Get mock supplier types\n   */\n  private getMockSupplierTypes(): SupplierType[] {\n    return [\n      { id: 1, name: 'مورد محلي' },\n      { id: 2, name: 'مورد دولي' },\n      { id: 3, name: 'مصنع' },\n      { id: 4, name: 'موزع' },\n      { id: 5, name: 'وكيل حصري' }\n    ];\n  }\n\n  /**\n   * Get mock countries\n   */\n  private getMockCountries(): Country[] {\n    return [\n      { id: 1, name: 'مصر', code: 'EG' },\n      { id: 2, name: 'الصين', code: 'CN' },\n      { id: 3, name: 'تركيا', code: 'TR' },\n      { id: 4, name: 'الإمارات', code: 'AE' },\n      { id: 5, name: 'السعودية', code: 'SA' },\n      { id: 6, name: 'ألمانيا', code: 'DE' },\n      { id: 7, name: 'إيطاليا', code: 'IT' }\n    ];\n  }\n\n  /**\n   * Extract city from address\n   */\n  private extractCityFromAddress(address: string): string {\n    if (!address) return '';\n\n    const cities = ['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية', 'القليوبية', 'البحيرة', 'الغربية'];\n    for (const city of cities) {\n      if (address.includes(city)) {\n        return city;\n      }\n    }\n    return 'غير محدد';\n  }\n\n  /**\n   * Get supplier type by ID\n   */\n  private getSupplierTypeById(typeId: number): string {\n    const types: { [key: number]: string } = {\n      1: 'محلي',\n      2: 'دولي',\n      3: 'حكومي'\n    };\n    return types[typeId] || 'محلي';\n  }\n}\n", "<!-- Terra Retail ERP - Professional Suppliers Module -->\n<div class=\"suppliers-container\">\n  \n  <!-- Page Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <h1 class=\"page-title\">إدارة الموردين</h1>\n        <p class=\"page-subtitle\">إدارة شاملة لجميع موردي المتجر والشركات</p>\n      </div>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\" class=\"add-btn\" (click)=\"openAddSupplier()\">\n          <mat-icon>business</mat-icon>\n          <span>إضافة مورد جديد</span>\n        </button>\n        <button mat-stroked-button class=\"export-btn\" (click)=\"exportSuppliers()\">\n          <mat-icon>file_download</mat-icon>\n          <span>تصدير</span>\n        </button>\n        <button mat-stroked-button class=\"import-btn\" (click)=\"importSuppliers()\">\n          <mat-icon>file_upload</mat-icon>\n          <span>استيراد</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Filters Section -->\n  <div class=\"filters-section\">\n    <mat-card class=\"filters-card\">\n      <mat-card-content>\n        <div class=\"filters-grid\">\n          \n          <!-- Search -->\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>البحث</mat-label>\n            <input matInput placeholder=\"ابحث بالاسم، الهاتف، أو الرقم...\" \n                   [(ngModel)]=\"searchTerm\" (input)=\"onSearch()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <!-- Supplier Type Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>نوع المورد</mat-label>\n            <mat-select [(ngModel)]=\"selectedSupplierType\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الأنواع</mat-option>\n              <mat-option *ngFor=\"let type of supplierTypes\" [value]=\"type.id\">\n                {{ type.name }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Country Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>البلد</mat-label>\n            <mat-select [(ngModel)]=\"selectedCountry\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع البلدان</mat-option>\n              <mat-option *ngFor=\"let country of countries\" [value]=\"country.id\">\n                {{ country.name }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Status Filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>الحالة</mat-label>\n            <mat-select [(ngModel)]=\"selectedStatus\" (selectionChange)=\"onFilterChange()\">\n              <mat-option value=\"\">جميع الحالات</mat-option>\n              <mat-option value=\"active\">نشط</mat-option>\n              <mat-option value=\"inactive\">غير نشط</mat-option>\n              <mat-option value=\"blocked\">محظور</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Clear Filters -->\n          <button mat-stroked-button class=\"clear-filters-btn\" (click)=\"clearFilters()\">\n            <mat-icon>clear</mat-icon>\n            <span>مسح الفلاتر</span>\n          </button>\n\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Statistics Cards -->\n  <div class=\"stats-section\">\n    <div class=\"stats-grid\">\n      \n      <div class=\"stat-card total-suppliers\">\n        <div class=\"stat-icon\">\n          <mat-icon>business</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.totalSuppliers }}</div>\n          <div class=\"stat-label\">إجمالي الموردين</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card active-suppliers\">\n        <div class=\"stat-icon\">\n          <mat-icon>verified</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.activeSuppliers }}</div>\n          <div class=\"stat-label\">الموردين النشطين</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card local-suppliers\">\n        <div class=\"stat-icon\">\n          <mat-icon>location_on</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.localSuppliers }}</div>\n          <div class=\"stat-label\">موردين محليين</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card international-suppliers\">\n        <div class=\"stat-icon\">\n          <mat-icon>public</mat-icon>\n        </div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ statistics.internationalSuppliers }}</div>\n          <div class=\"stat-label\">موردين دوليين</div>\n        </div>\n      </div>\n\n    </div>\n  </div>\n\n  <!-- Suppliers Table -->\n  <div class=\"table-section\">\n    <mat-card class=\"table-card\">\n      \n      <!-- Table Header -->\n      <div class=\"table-header\">\n        <div class=\"table-title\">\n          <h3>قائمة الموردين</h3>\n          <span class=\"results-count\">({{ filteredSuppliers.length }} مورد)</span>\n        </div>\n        <div class=\"table-actions\">\n          <mat-form-field appearance=\"outline\" class=\"page-size-field\">\n            <mat-label>عدد الصفوف</mat-label>\n            <mat-select [(ngModel)]=\"pageSize\" (selectionChange)=\"onPageSizeChange()\">\n              <mat-option value=\"10\">10</mat-option>\n              <mat-option value=\"25\">25</mat-option>\n              <mat-option value=\"50\">50</mat-option>\n              <mat-option value=\"100\">100</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Table Content -->\n      <div class=\"table-container\">\n        <table mat-table [dataSource]=\"paginatedSuppliers\" class=\"suppliers-table\" matSort>\n\n          <!-- Supplier Code Column -->\n          <ng-container matColumnDef=\"code\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>كود المورد</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <span class=\"supplier-code\">{{ supplier.code }}</span>\n            </td>\n          </ng-container>\n\n          <!-- Supplier Name Column -->\n          <ng-container matColumnDef=\"name\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>اسم المورد</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"supplier-info\">\n                <div class=\"supplier-name\">{{ supplier.name }}</div>\n                <div class=\"supplier-type\">{{ supplier.supplierTypeName }}</div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Contact Info Column -->\n          <ng-container matColumnDef=\"contact\">\n            <th mat-header-cell *matHeaderCellDef>معلومات الاتصال</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"contact-info\">\n                <div class=\"phone\" *ngIf=\"supplier.phone\">\n                  <mat-icon>phone</mat-icon>\n                  <span>{{ supplier.phone }}</span>\n                </div>\n                <div class=\"email\" *ngIf=\"supplier.email\">\n                  <mat-icon>email</mat-icon>\n                  <span>{{ supplier.email }}</span>\n                </div>\n                <div class=\"website\" *ngIf=\"supplier.website\">\n                  <mat-icon>language</mat-icon>\n                  <span>{{ supplier.website }}</span>\n                </div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Location Column -->\n          <ng-container matColumnDef=\"location\">\n            <th mat-header-cell *matHeaderCellDef>الموقع</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"location-info\">\n                <div class=\"country\">\n                  <mat-icon>flag</mat-icon>\n                  <span>{{ supplier.countryName }}</span>\n                </div>\n                <div class=\"city\" *ngIf=\"supplier.city\">{{ supplier.city }}</div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Products Count Column -->\n          <ng-container matColumnDef=\"products\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>المنتجات</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"products-count\">\n                <span class=\"count\">{{ supplier.productsCount }}</span>\n                <span class=\"label\">منتج</span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Balance Column -->\n          <ng-container matColumnDef=\"balance\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>الرصيد</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"balance\" [class]=\"getBalanceClass(supplier.balance)\">\n                {{ supplier.balance | number:'1.2-2' }} جنيه\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Rating Column -->\n          <ng-container matColumnDef=\"rating\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header>التقييم</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"rating\">\n                <div class=\"stars\">\n                  <mat-icon *ngFor=\"let star of getStarsArray(supplier.rating)\" \n                           [class]=\"star ? 'filled' : 'empty'\">\n                    {{ star ? 'star' : 'star_border' }}\n                  </mat-icon>\n                </div>\n                <span class=\"rating-value\">({{ supplier.rating }}/5)</span>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Status Column -->\n          <ng-container matColumnDef=\"status\">\n            <th mat-header-cell *matHeaderCellDef>الحالة</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <span class=\"status-badge\" [class]=\"getStatusClass(supplier.status)\">\n                {{ getStatusText(supplier.status) }}\n              </span>\n            </td>\n          </ng-container>\n\n          <!-- Actions Column -->\n          <ng-container matColumnDef=\"actions\">\n            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>\n            <td mat-cell *matCellDef=\"let supplier\">\n              <div class=\"actions-buttons\">\n                <button mat-icon-button color=\"primary\" \n                        matTooltip=\"عرض التفاصيل\" \n                        (click)=\"viewSupplier(supplier)\">\n                  <mat-icon>visibility</mat-icon>\n                </button>\n                <button mat-icon-button color=\"accent\" \n                        matTooltip=\"تعديل\" \n                        (click)=\"editSupplier(supplier)\">\n                  <mat-icon>edit</mat-icon>\n                </button>\n                <button mat-icon-button color=\"warn\" \n                        matTooltip=\"حذف\" \n                        (click)=\"deleteSupplier(supplier)\">\n                  <mat-icon>delete</mat-icon>\n                </button>\n                <button mat-icon-button \n                        matTooltip=\"المنتجات\" \n                        (click)=\"viewSupplierProducts(supplier)\">\n                  <mat-icon>inventory</mat-icon>\n                </button>\n              </div>\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n          <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" \n              class=\"table-row\" (click)=\"viewSupplier(row)\"></tr>\n\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <mat-paginator \n        [length]=\"filteredSuppliers.length\"\n        [pageSize]=\"pageSize\"\n        [pageSizeOptions]=\"[10, 25, 50, 100]\"\n        (page)=\"onPageChange($event)\"\n        showFirstLastButtons>\n      </mat-paginator>\n\n    </mat-card>\n  </div>\n\n  <!-- Loading Overlay -->\n  <div class=\"loading-overlay\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>جاري تحميل البيانات...</p>\n  </div>\n\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAK5C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAmB,6BAA6B;AAC3E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;;;;;;;;;;;;;;;;;;;;IC4B9CC,EAAA,CAAAC,cAAA,qBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,IAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAAI,UAAA,UAAAM,UAAA,CAAAJ,EAAA,CAAoB;IAChEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,UAAA,CAAAD,IAAA,MACF;;;;;IAsGFT,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEnEH,EADF,CAAAC,cAAA,aAAwC,eACV;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;;;;IADyBH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAC,WAAA,CAAAC,IAAA,CAAmB;;;;;IAMjDb,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,8DAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAGjEH,EAFJ,CAAAC,cAAA,aAAwC,cACX,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC5D,EACH;;;;IAH0BH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAG,WAAA,CAAAL,IAAA,CAAmB;IACnBT,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAW,iBAAA,CAAAG,WAAA,CAAAC,gBAAA,CAA+B;;;;;IAO9Df,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,4FAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAIpDH,EADF,CAAAC,cAAA,cAA0C,eAC9B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;IADEH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAK,WAAA,CAAAC,KAAA,CAAoB;;;;;IAG1BjB,EADF,CAAAC,cAAA,cAA0C,eAC9B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;IADEH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAK,WAAA,CAAAE,KAAA,CAAoB;;;;;IAG1BlB,EADF,CAAAC,cAAA,cAA8C,eAClC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;;;;IADEH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAK,WAAA,CAAAG,OAAA,CAAsB;;;;;IAXhCnB,EADF,CAAAC,cAAA,aAAwC,cACZ;IASxBD,EARA,CAAAoB,UAAA,IAAAC,2CAAA,kBAA0C,IAAAC,2CAAA,kBAIA,IAAAC,2CAAA,kBAII;IAKlDvB,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAbmBH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAY,WAAA,CAAAC,KAAA,CAAoB;IAIpBjB,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAY,WAAA,CAAAE,KAAA,CAAoB;IAIlBlB,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAY,WAAA,CAAAG,OAAA,CAAsB;;;;;IAUhDnB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAO7CH,EAAA,CAAAC,cAAA,cAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAzBH,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAa,WAAA,CAAAC,IAAA,CAAmB;;;;;IAHzDzB,EAHN,CAAAC,cAAA,aAAwC,cACX,cACJ,eACT;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;IACNH,EAAA,CAAAoB,UAAA,IAAAM,2CAAA,kBAAwC;IAE5C1B,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAJOH,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAW,iBAAA,CAAAa,WAAA,CAAAG,WAAA,CAA0B;IAEf3B,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAoB,WAAA,CAAAC,IAAA,CAAmB;;;;;IAO1CzB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAG/DH,EAFJ,CAAAC,cAAA,aAAwC,cACV,eACN;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAE5BF,EAF4B,CAAAG,YAAA,EAAO,EAC3B,EACH;;;;IAHmBH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAW,iBAAA,CAAAiB,WAAA,CAAAC,aAAA,CAA4B;;;;;IAQpD7B,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/DH,EADF,CAAAC,cAAA,aAAwC,cAC2B;IAC/DD,EAAA,CAAAE,MAAA,GACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;;;;;IAHkBH,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAA8B,UAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAC,WAAA,CAAAC,OAAA,EAA2C;IAC9DlC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAmC,WAAA,OAAAF,WAAA,CAAAC,OAAA,yCACF;;;;;IAMFlC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAI5DH,EAAA,CAAAC,cAAA,eAC6C;IAC3CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAFFH,EAAA,CAAA8B,UAAA,CAAAM,QAAA,sBAAmC;IAC1CpC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA4B,QAAA,+BACF;;;;;IAJFpC,EAFJ,CAAAC,cAAA,aAAwC,cAClB,cACC;IACjBD,EAAA,CAAAoB,UAAA,IAAAiB,gDAAA,uBAC6C;IAG/CrC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAExDF,EAFwD,CAAAG,YAAA,EAAO,EACvD,EACH;;;;;IAP4BH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,YAAA2B,MAAA,CAAAO,aAAA,CAAAC,YAAA,CAAAC,MAAA,EAAiC;IAKnCxC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,kBAAA,MAAA+B,YAAA,CAAAC,MAAA,QAAyB;;;;;IAOxDxC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAwC,eAC+B;IACnED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;;IAHwBH,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA8B,UAAA,CAAAC,MAAA,CAAAU,cAAA,CAAAC,YAAA,CAAAC,MAAA,EAAyC;IAClE3C,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAuB,MAAA,CAAAa,aAAA,CAAAF,YAAA,CAAAC,MAAA,OACF;;;;;IAMF3C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAGhDH,EAFJ,CAAAC,cAAA,aAAwC,cACT,iBAGc;IAAjCD,EAAA,CAAA6C,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,YAAA,GAAA/C,EAAA,CAAAgD,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAA/B,EAAA,CAAAmD,aAAA;MAAA,OAAAnD,EAAA,CAAAoD,WAAA,CAASrB,MAAA,CAAAsB,YAAA,CAAAN,YAAA,CAAsB;IAAA,EAAC;IACtC/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAEyC;IAAjCD,EAAA,CAAA6C,UAAA,mBAAAS,8DAAA;MAAA,MAAAP,YAAA,GAAA/C,EAAA,CAAAgD,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAA/B,EAAA,CAAAmD,aAAA;MAAA,OAAAnD,EAAA,CAAAoD,WAAA,CAASrB,MAAA,CAAAwB,YAAA,CAAAR,YAAA,CAAsB;IAAA,EAAC;IACtC/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;IACTH,EAAA,CAAAC,cAAA,iBAE2C;IAAnCD,EAAA,CAAA6C,UAAA,mBAAAW,8DAAA;MAAA,MAAAT,YAAA,GAAA/C,EAAA,CAAAgD,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAA/B,EAAA,CAAAmD,aAAA;MAAA,OAAAnD,EAAA,CAAAoD,WAAA,CAASrB,MAAA,CAAA0B,cAAA,CAAAV,YAAA,CAAwB;IAAA,EAAC;IACxC/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACpB;IACTH,EAAA,CAAAC,cAAA,kBAEiD;IAAzCD,EAAA,CAAA6C,UAAA,mBAAAa,+DAAA;MAAA,MAAAX,YAAA,GAAA/C,EAAA,CAAAgD,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAA/B,EAAA,CAAAmD,aAAA;MAAA,OAAAnD,EAAA,CAAAoD,WAAA,CAASrB,MAAA,CAAA4B,oBAAA,CAAAZ,YAAA,CAA8B;IAAA,EAAC;IAC9C/C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAGzBF,EAHyB,CAAAG,YAAA,EAAW,EACvB,EACL,EACH;;;;;IAGPH,EAAA,CAAA4D,SAAA,aAA4D;;;;;;IAC5D5D,EAAA,CAAAC,cAAA,aACkD;IAA5BD,EAAA,CAAA6C,UAAA,mBAAAgB,0DAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAgD,aAAA,CAAAe,IAAA,EAAAb,SAAA;MAAA,MAAAnB,MAAA,GAAA/B,EAAA,CAAAmD,aAAA;MAAA,OAAAnD,EAAA,CAAAoD,WAAA,CAASrB,MAAA,CAAAsB,YAAA,CAAAS,OAAA,CAAiB;IAAA,EAAC;IAAC9D,EAAA,CAAAG,YAAA,EAAK;;;;;IAkB/DH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA4D,SAAA,sBAAyC;IACzC5D,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kHAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;ADvOR,WAAa6D,qBAAqB;EAA5B,MAAOA,qBAAqB;IAmCtBC,IAAA;IACAC,MAAA;IAlCV;IACAC,SAAS,GAAG,IAAI;IAEhB;IACAC,SAAS,GAAe,EAAE;IAC1BC,iBAAiB,GAAe,EAAE;IAClCC,kBAAkB,GAAe,EAAE;IACnCC,aAAa,GAAmB,EAAE;IAClCC,SAAS,GAAc,EAAE;IAEzB;IACAC,UAAU,GAAe;MACvBC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,sBAAsB,EAAE;KACzB;IAED;IACAC,UAAU,GAAG,EAAE;IACfC,oBAAoB,GAAG,EAAE;IACzBC,eAAe,GAAG,EAAE;IACpBC,cAAc,GAAG,EAAE;IAEnB;IACAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC1HC,QAAQ,GAAG,EAAE;IACbC,WAAW,GAAG,CAAC;IAEf;IACQC,aAAa,GAAmB,EAAE;IAE1CC,YACUrB,IAAgB,EAChBC,MAAc;MADd,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,MAAM,GAANA,MAAM;IACb;IAEHqB,QAAQA,CAAA;MACN,IAAI,CAACC,eAAe,EAAE;IACxB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD;IAEA;;;IAGQJ,eAAeA,CAAA;MACrB,IAAI,CAACrB,SAAS,GAAG,IAAI;MAErB;MACA0B,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,aAAa,EAAE,EACpB,IAAI,CAACC,iBAAiB,EAAE,EACxB,IAAI,CAACC,aAAa,EAAE,CACrB,CAAC,CAACC,IAAI,CAAC,MAAK;QACX,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACjC,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC,CAACkC,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACnC,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;IAEA;;;IAGQ4B,aAAaA,CAAA;MACnB,OAAO,IAAIF,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAAC1B,IAAI,CAACyC,GAAG,CAAM,4CAA4C,CAAC,CAACC,SAAS,CAAC;UACrFC,IAAI,EAAGC,QAAQ,IAAI;YACjBN,OAAO,CAACO,GAAG,CAAC,eAAe,EAAED,QAAQ,CAAC;YACtC;YACA,IAAI,CAACzC,SAAS,GAAG,CAACyC,QAAQ,CAACzC,SAAS,IAAI,EAAE,EAAE2C,GAAG,CAAEC,QAAa,KAAM;cAClE1G,EAAE,EAAE0G,QAAQ,CAACC,EAAE,IAAID,QAAQ,CAAC1G,EAAE;cAC9BO,IAAI,EAAEmG,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACnG,IAAI;cACrEJ,IAAI,EAAEuG,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACK,MAAM,IAAIL,QAAQ,CAACvG,IAAI;cACzD6G,MAAM,EAAEN,QAAQ,CAACO,MAAM,IAAIP,QAAQ,CAACM,MAAM;cAC1CrG,KAAK,EAAE+F,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAACS,MAAM,IAAIT,QAAQ,CAACU,MAAM;cAC3DxG,KAAK,EAAE8F,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAAC9F,KAAK;cACvCC,OAAO,EAAE6F,QAAQ,CAACY,OAAO,IAAIZ,QAAQ,CAAC7F,OAAO;cAC7C0G,OAAO,EAAEb,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAACa,OAAO;cAC7CpG,IAAI,EAAE,IAAI,CAACsG,sBAAsB,CAACf,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAACa,OAAO,CAAC;cACvEG,cAAc,EAAEhB,QAAQ,CAACiB,cAAc,IAAIjB,QAAQ,CAACgB,cAAc,IAAI,CAAC;cACvEjH,gBAAgB,EAAE,IAAI,CAACmH,mBAAmB,CAAClB,QAAQ,CAACiB,cAAc,IAAIjB,QAAQ,CAACgB,cAAc,CAAC;cAC9FG,SAAS,EAAE,CAAC;cACZxG,WAAW,EAAE,KAAK;cAClBO,OAAO,EAAE8E,QAAQ,CAACoB,cAAc,IAAIpB,QAAQ,CAACqB,cAAc,IAAI,CAAC;cAChE7F,MAAM,EAAEwE,QAAQ,CAACsB,MAAM,IAAItB,QAAQ,CAACxE,MAAM,IAAI,CAAC;cAC/CX,aAAa,EAAE0G,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;cACjD9F,MAAM,EAAEqE,QAAQ,CAAC0B,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,UAAU;cAC3DC,QAAQ,EAAE3B,QAAQ,CAAC0B,QAAQ,KAAK,KAAK;cACrCE,SAAS,EAAE,IAAIC,IAAI,CAAC7B,QAAQ,CAAC8B,SAAS,IAAI9B,QAAQ,CAAC4B,SAAS,IAAIC,IAAI,CAACE,GAAG,EAAE;aAC3E,CAAC,CAAC;YACHvC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChD;YACA,IAAI,CAAClC,SAAS,GAAG,IAAI,CAAC4E,gBAAgB,EAAE;YACxCxC,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC4D,IAAI,CAACtD,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQK,iBAAiBA,CAAA;MACvB,OAAO,IAAIH,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAAC1B,IAAI,CAACyC,GAAG,CAAM,iDAAiD,CAAC,CAACC,SAAS,CAAC;UAC1FC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACtC,aAAa,GAAGsC,QAAQ,CAACtC,aAAa,IAAI,EAAE;YACjDiC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD;YACA,IAAI,CAAC/B,aAAa,GAAG,IAAI,CAAC2E,oBAAoB,EAAE;YAChD1C,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC4D,IAAI,CAACtD,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQM,aAAaA,CAAA;MACnB,OAAO,IAAIJ,OAAO,CAAC,CAACW,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMd,GAAG,GAAG,IAAI,CAAC1B,IAAI,CAACyC,GAAG,CAAM,4CAA4C,CAAC,CAACC,SAAS,CAAC;UACrFC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACrC,SAAS,GAAGqC,QAAQ,CAACrC,SAAS,IAAI,EAAE;YACzCgC,OAAO,EAAE;UACX,CAAC;UACDF,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChD;YACA,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAAC2E,gBAAgB,EAAE;YACxC3C,OAAO,EAAE;UACX;SACD,CAAC;QACF,IAAI,CAACnB,aAAa,CAAC4D,IAAI,CAACtD,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ;IAEA;;;IAGQQ,mBAAmBA,CAAA;MACzB,IAAI,CAAC1B,UAAU,CAACC,cAAc,GAAG,IAAI,CAACN,SAAS,CAACgF,MAAM;MACtD,IAAI,CAAC3E,UAAU,CAACE,eAAe,GAAG,IAAI,CAACP,SAAS,CAACiF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3G,MAAM,KAAK,QAAQ,CAAC,CAACyG,MAAM;MAC1F,IAAI,CAAC3E,UAAU,CAACG,cAAc,GAAG,IAAI,CAACR,SAAS,CAACiF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3H,WAAW,KAAK,KAAK,CAAC,CAACyH,MAAM;MAC3F,IAAI,CAAC3E,UAAU,CAACI,sBAAsB,GAAG,IAAI,CAACT,SAAS,CAACiF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3H,WAAW,KAAK,KAAK,CAAC,CAACyH,MAAM;IACrG;IAEA;;;IAGAhD,YAAYA,CAAA;MACV,IAAImD,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACnF,SAAS,CAAC;MAElC;MACA,IAAI,IAAI,CAACU,UAAU,CAAC0E,IAAI,EAAE,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC3E,UAAU,CAAC4E,WAAW,EAAE,CAACF,IAAI,EAAE;QACjDD,QAAQ,GAAGA,QAAQ,CAACF,MAAM,CAACrC,QAAQ,IACjCA,QAAQ,CAACvG,IAAI,CAACiJ,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IAC1CzC,QAAQ,CAACnG,IAAI,CAAC6I,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IACzCzC,QAAQ,CAAC/F,KAAK,IAAI+F,QAAQ,CAAC/F,KAAK,CAAC0I,QAAQ,CAACF,IAAI,CAAE,IAChDzC,QAAQ,CAAC9F,KAAK,IAAI8F,QAAQ,CAAC9F,KAAK,CAACwI,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAE,CAChE;MACH;MAEA;MACA,IAAI,IAAI,CAAC1E,oBAAoB,EAAE;QAC7BwE,QAAQ,GAAGA,QAAQ,CAACF,MAAM,CAACrC,QAAQ,IACjCA,QAAQ,CAACgB,cAAc,CAAC4B,QAAQ,EAAE,KAAK,IAAI,CAAC7E,oBAAoB,CACjE;MACH;MAEA;MACA,IAAI,IAAI,CAACC,eAAe,EAAE;QACxBuE,QAAQ,GAAGA,QAAQ,CAACF,MAAM,CAACrC,QAAQ,IACjCA,QAAQ,CAACmB,SAAS,CAACyB,QAAQ,EAAE,KAAK,IAAI,CAAC5E,eAAe,CACvD;MACH;MAEA;MACA,IAAI,IAAI,CAACC,cAAc,EAAE;QACvBsE,QAAQ,GAAGA,QAAQ,CAACF,MAAM,CAACrC,QAAQ,IAAIA,QAAQ,CAACrE,MAAM,KAAK,IAAI,CAACsC,cAAc,CAAC;MACjF;MAEA,IAAI,CAACZ,iBAAiB,GAAGkF,QAAQ;MACjC,IAAI,CAACnE,WAAW,GAAG,CAAC;MACpB,IAAI,CAACyE,mBAAmB,EAAE;IAC5B;IAEA;;;IAGQA,mBAAmBA,CAAA;MACzB,MAAMC,UAAU,GAAG,IAAI,CAAC1E,WAAW,GAAG,IAAI,CAACD,QAAQ;MACnD,MAAM4E,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC3E,QAAQ;MAC3C,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACD,iBAAiB,CAAC2F,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IAC9E;IAEA;;;IAGAE,QAAQA,CAAA;MACN,IAAI,CAAC7D,YAAY,EAAE;IACrB;IAEA;;;IAGA8D,cAAcA,CAAA;MACZ,IAAI,CAAC9D,YAAY,EAAE;IACrB;IAEA;;;IAGA+D,YAAYA,CAAA;MACV,IAAI,CAACrF,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACC,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACmB,YAAY,EAAE;IACrB;IAEA;;;IAGAgE,YAAYA,CAACC,KAAgB;MAC3B,IAAI,CAACjF,WAAW,GAAGiF,KAAK,CAACC,SAAS;MAClC,IAAI,CAACnF,QAAQ,GAAGkF,KAAK,CAAClF,QAAQ;MAC9B,IAAI,CAAC0E,mBAAmB,EAAE;IAC5B;IAEA;;;IAGAU,gBAAgBA,CAAA;MACd,IAAI,CAACnF,WAAW,GAAG,CAAC;MACpB,IAAI,CAACyE,mBAAmB,EAAE;IAC5B;IAEA;;;IAGA7H,eAAeA,CAACE,OAAe;MAC7B,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;MAClC,OAAO,MAAM;IACf;IAEA;;;IAGAO,cAAcA,CAACE,MAAc;MAC3B,OAAOA,MAAM;IACf;IAEA;;;IAGAC,aAAaA,CAACD,MAAc;MAC1B,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,KAAK;QAC3B,KAAK,UAAU;UAAE,OAAO,SAAS;QACjC,KAAK,SAAS;UAAE,OAAO,OAAO;QAC9B;UAAS,OAAOA,MAAM;MACxB;IACF;IAEA;;;IAGAL,aAAaA,CAACE,MAAc;MAC1B,MAAMgI,KAAK,GAAG,EAAE;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BD,KAAK,CAACvB,IAAI,CAACwB,CAAC,IAAIjI,MAAM,CAAC;MACzB;MACA,OAAOgI,KAAK;IACd;IAEA;;;IAGAE,eAAeA,CAAA;MACb,IAAI,CAACxG,MAAM,CAACyG,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC1C;IAEA;;;IAGAtH,YAAYA,CAAC2D,QAAkB;MAC7B,IAAI,CAAC9C,MAAM,CAACyG,QAAQ,CAAC,CAAC,oBAAoB,EAAE3D,QAAQ,CAAC1G,EAAE,CAAC,CAAC;IAC3D;IAEA;;;IAGAiD,YAAYA,CAACyD,QAAkB;MAC7B,IAAI,CAAC9C,MAAM,CAACyG,QAAQ,CAAC,CAAC,iBAAiB,EAAE3D,QAAQ,CAAC1G,EAAE,CAAC,CAAC;IACxD;IAEA;;;IAGAmD,cAAcA,CAACuD,QAAkB;MAC/B,IAAI4D,OAAO,CAAC,+BAA+B5D,QAAQ,CAACvG,IAAI,IAAI,CAAC,EAAE;QAC7D,IAAI,CAAC0D,SAAS,GAAG,IAAI;QAErB,MAAMwB,GAAG,GAAG,IAAI,CAAC1B,IAAI,CAAC4G,MAAM,CAAC,8CAA8C7D,QAAQ,CAAC1G,EAAE,EAAE,CAAC,CAACqG,SAAS,CAAC;UAClGC,IAAI,EAAGC,QAAa,IAAI;YACtB,IAAI,CAAC1C,SAAS,GAAG,KAAK;YACtBoC,OAAO,CAACO,GAAG,CAAC,gCAAgC,EAAED,QAAQ,CAAC;YAEvD;YACA,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChJ,EAAE,KAAK0G,QAAQ,CAAC1G,EAAE,CAAC;YACjE,IAAI,CAAC8F,YAAY,EAAE;YAEnB;YACA0E,KAAK,CAAC,qBAAqB,CAAC;UAC9B,CAAC;UACDxE,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACnC,SAAS,GAAG,KAAK;YACtBoC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;YAChDwE,KAAK,CAAC,mBAAmB,CAAC;UAC5B;SACD,CAAC;QAEF,IAAI,CAACzF,aAAa,CAAC4D,IAAI,CAACtD,GAAG,CAAC;MAC9B;IACF;IAEA;;;IAGAhC,oBAAoBA,CAACqD,QAAkB;MACrCT,OAAO,CAACO,GAAG,CAAC,yBAAyB,EAAEE,QAAQ,CAAC;MAChD;MACA8D,KAAK,CAAC,sBAAsB9D,QAAQ,CAACvG,IAAI,EAAE,CAAC;IAC9C;IAEA;;;IAGAsK,eAAeA,CAAA;MACbxE,OAAO,CAACO,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEA;;;IAGAkE,eAAeA,CAAA;MACbzE,OAAO,CAACO,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;IAEA;;;IAGQkC,gBAAgBA,CAAA;MACtB,OAAO,CACL;QACE1I,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,QAAQ;QACdJ,IAAI,EAAE,oBAAoB;QAC1BQ,KAAK,EAAE,eAAe;QACtBC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE,oBAAoB;QAC7B0G,OAAO,EAAE,yBAAyB;QAClCpG,IAAI,EAAE,SAAS;QACfuG,cAAc,EAAE,CAAC;QACjBjH,gBAAgB,EAAE,WAAW;QAC7BoH,SAAS,EAAE,CAAC;QACZxG,WAAW,EAAE,KAAK;QAClBO,OAAO,EAAE,CAAC,KAAK;QACfM,MAAM,EAAE,CAAC;QACTX,aAAa,EAAE,GAAG;QAClBc,MAAM,EAAE,QAAQ;QAChBgG,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;OACjC,EACD;QACEvI,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,QAAQ;QACdJ,IAAI,EAAE,wBAAwB;QAC9BQ,KAAK,EAAE,gBAAgB;QACvBC,KAAK,EAAE,8BAA8B;QACrCC,OAAO,EAAE,4BAA4B;QACrC0G,OAAO,EAAE,0BAA0B;QACnCpG,IAAI,EAAE,UAAU;QAChBuG,cAAc,EAAE,CAAC;QACjBjH,gBAAgB,EAAE,WAAW;QAC7BoH,SAAS,EAAE,CAAC;QACZxG,WAAW,EAAE,OAAO;QACpBO,OAAO,EAAE,KAAK;QACdM,MAAM,EAAE,CAAC;QACTX,aAAa,EAAE,GAAG;QAClBc,MAAM,EAAE,QAAQ;QAChBgG,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;OACjC,EACD;QACEvI,EAAE,EAAE,CAAC;QACLO,IAAI,EAAE,QAAQ;QACdJ,IAAI,EAAE,2BAA2B;QACjCQ,KAAK,EAAE,eAAe;QACtBC,KAAK,EAAE,0BAA0B;QACjCC,OAAO,EAAE,uBAAuB;QAChC0G,OAAO,EAAE,6BAA6B;QACtCpG,IAAI,EAAE,YAAY;QAClBuG,cAAc,EAAE,CAAC;QACjBjH,gBAAgB,EAAE,MAAM;QACxBoH,SAAS,EAAE,CAAC;QACZxG,WAAW,EAAE,KAAK;QAClBO,OAAO,EAAE,CAAC;QACVM,MAAM,EAAE,CAAC;QACTX,aAAa,EAAE,EAAE;QACjBc,MAAM,EAAE,UAAU;QAClBgG,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY;OACjC,CACF;IACH;IAEA;;;IAGQK,oBAAoBA,CAAA;MAC1B,OAAO,CACL;QAAE5I,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAW,CAAE,EAC5B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAW,CAAE,EAC5B;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAM,CAAE,EACvB;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAM,CAAE,EACvB;QAAEH,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE;MAAW,CAAE,CAC7B;IACH;IAEA;;;IAGQ0I,gBAAgBA,CAAA;MACtB,OAAO,CACL;QAAE7I,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,KAAK;QAAEI,IAAI,EAAE;MAAI,CAAE,EAClC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,OAAO;QAAEI,IAAI,EAAE;MAAI,CAAE,EACpC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,OAAO;QAAEI,IAAI,EAAE;MAAI,CAAE,EACpC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,UAAU;QAAEI,IAAI,EAAE;MAAI,CAAE,EACvC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,UAAU;QAAEI,IAAI,EAAE;MAAI,CAAE,EACvC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,SAAS;QAAEI,IAAI,EAAE;MAAI,CAAE,EACtC;QAAEP,EAAE,EAAE,CAAC;QAAEG,IAAI,EAAE,SAAS;QAAEI,IAAI,EAAE;MAAI,CAAE,CACvC;IACH;IAEA;;;IAGQkH,sBAAsBA,CAACF,OAAe;MAC5C,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MAEvB,MAAMoD,MAAM,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;MAC5G,KAAK,MAAMxJ,IAAI,IAAIwJ,MAAM,EAAE;QACzB,IAAIpD,OAAO,CAAC8B,QAAQ,CAAClI,IAAI,CAAC,EAAE;UAC1B,OAAOA,IAAI;QACb;MACF;MACA,OAAO,UAAU;IACnB;IAEA;;;IAGQyG,mBAAmBA,CAACgD,MAAc;MACxC,MAAMC,KAAK,GAA8B;QACvC,CAAC,EAAE,MAAM;QACT,CAAC,EAAE,MAAM;QACT,CAAC,EAAE;OACJ;MACD,OAAOA,KAAK,CAACD,MAAM,CAAC,IAAI,MAAM;IAChC;;uCA3eWlH,qBAAqB,EAAAhE,EAAA,CAAAoL,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtL,EAAA,CAAAoL,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;;YAArBxH,qBAAqB;MAAAyH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1E1B/L,EANR,CAAAC,cAAA,aAAiC,aAGN,aACK,aACD,YACA;UAAAD,EAAA,CAAAE,MAAA,sFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1CH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,wNAAuC;UAClEF,EADkE,CAAAG,YAAA,EAAI,EAChE;UAEJH,EADF,CAAAC,cAAA,aAA4B,gBAC4D;UAA5BD,EAAA,CAAA6C,UAAA,mBAAAoJ,uDAAA;YAAA,OAASD,GAAA,CAAAtB,eAAA,EAAiB;UAAA,EAAC;UACnF1K,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wFAAe;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACrB;UACTH,EAAA,CAAAC,cAAA,iBAA0E;UAA5BD,EAAA,CAAA6C,UAAA,mBAAAqJ,wDAAA;YAAA,OAASF,GAAA,CAAAjB,eAAA,EAAiB;UAAA,EAAC;UACvE/K,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UACbF,EADa,CAAAG,YAAA,EAAO,EACX;UACTH,EAAA,CAAAC,cAAA,iBAA0E;UAA5BD,EAAA,CAAA6C,UAAA,mBAAAsJ,wDAAA;YAAA,OAASH,GAAA,CAAAhB,eAAA,EAAiB;UAAA,EAAC;UACvEhL,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAIrBF,EAJqB,CAAAG,YAAA,EAAO,EACb,EACL,EACF,EACF;UAUIH,EAPV,CAAAC,cAAA,eAA6B,oBACI,wBACX,eACU,0BAGkC,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,iBACqD;UAA9CD,EAAA,CAAAoM,gBAAA,2BAAAC,+DAAAC,MAAA;YAAAtM,EAAA,CAAAuM,kBAAA,CAAAP,GAAA,CAAAlH,UAAA,EAAAwH,MAAA,MAAAN,GAAA,CAAAlH,UAAA,GAAAwH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACtM,EAAA,CAAA6C,UAAA,mBAAA2J,uDAAA;YAAA,OAASR,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UADpDjK,EAAA,CAAAG,YAAA,EACqD;UACrDH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,sBAAoF;UAAxED,EAAA,CAAAoM,gBAAA,2BAAAK,oEAAAH,MAAA;YAAAtM,EAAA,CAAAuM,kBAAA,CAAAP,GAAA,CAAAjH,oBAAA,EAAAuH,MAAA,MAAAN,GAAA,CAAAjH,oBAAA,GAAAuH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAACtM,EAAA,CAAA6C,UAAA,6BAAA6J,sEAAA;YAAA,OAAmBV,GAAA,CAAA9B,cAAA,EAAgB;UAAA,EAAC;UACjFlK,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAoB,UAAA,KAAAuL,4CAAA,yBAAiE;UAIrE3M,EADE,CAAAG,YAAA,EAAa,EACE;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,sBAA+E;UAAnED,EAAA,CAAAoM,gBAAA,2BAAAQ,oEAAAN,MAAA;YAAAtM,EAAA,CAAAuM,kBAAA,CAAAP,GAAA,CAAAhH,eAAA,EAAAsH,MAAA,MAAAN,GAAA,CAAAhH,eAAA,GAAAsH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAACtM,EAAA,CAAA6C,UAAA,6BAAAgK,sEAAA;YAAA,OAAmBb,GAAA,CAAA9B,cAAA,EAAgB;UAAA,EAAC;UAC5ElK,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAoB,UAAA,KAAA0L,4CAAA,yBAAmE;UAIvE9M,EADE,CAAAG,YAAA,EAAa,EACE;UAIfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAC,cAAA,sBAA8E;UAAlED,EAAA,CAAAoM,gBAAA,2BAAAW,oEAAAT,MAAA;YAAAtM,EAAA,CAAAuM,kBAAA,CAAAP,GAAA,CAAA/G,cAAA,EAAAqH,MAAA,MAAAN,GAAA,CAAA/G,cAAA,GAAAqH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAACtM,EAAA,CAAA6C,UAAA,6BAAAmK,sEAAA;YAAA,OAAmBhB,GAAA,CAAA9B,cAAA,EAAgB;UAAA,EAAC;UAC3ElK,EAAA,CAAAC,cAAA,sBAAqB;UAAAD,EAAA,CAAAE,MAAA,2EAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAC,cAAA,sBAA2B;UAAAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC3CH,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACjDH,EAAA,CAAAC,cAAA,sBAA4B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAErCF,EAFqC,CAAAG,YAAA,EAAa,EACnC,EACE;UAGjBH,EAAA,CAAAC,cAAA,kBAA8E;UAAzBD,EAAA,CAAA6C,UAAA,mBAAAoK,wDAAA;YAAA,OAASjB,GAAA,CAAA7B,YAAA,EAAc;UAAA,EAAC;UAC3EnK,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qEAAW;UAM3BF,EAN2B,CAAAG,YAAA,EAAO,EACjB,EAEL,EACW,EACV,EACP;UAQEH,EALR,CAAAC,cAAA,eAA2B,eACD,eAEiB,eACd,gBACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,6FAAe;UAE3CF,EAF2C,CAAAG,YAAA,EAAM,EACzC,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACf,gBACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC9DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mGAAgB;UAE5CF,EAF4C,CAAAG,YAAA,EAAM,EAC1C,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAuC,eACd,gBACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,iFAAa;UAEzCF,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA+C,eACtB,gBACX;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;UAEJH,EADF,CAAAC,cAAA,eAA0B,eACA;UAAAD,EAAA,CAAAE,MAAA,KAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrEH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,kFAAa;UAK7CF,EAL6C,CAAAG,YAAA,EAAM,EACvC,EACF,EAEF,EACF;UASEH,EANR,CAAAC,cAAA,gBAA2B,qBACI,gBAGD,gBACC,WACnB;UAAAD,EAAA,CAAAE,MAAA,wFAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,iBAA4B;UAAAD,EAAA,CAAAE,MAAA,KAAqC;UACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;UAGFH,EAFJ,CAAAC,cAAA,gBAA2B,2BACoC,kBAChD;UAAAD,EAAA,CAAAE,MAAA,gEAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,uBAA0E;UAA9DD,EAAA,CAAAoM,gBAAA,2BAAAc,qEAAAZ,MAAA;YAAAtM,EAAA,CAAAuM,kBAAA,CAAAP,GAAA,CAAA7G,QAAA,EAAAmH,MAAA,MAAAN,GAAA,CAAA7G,QAAA,GAAAmH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAACtM,EAAA,CAAA6C,UAAA,6BAAAsK,uEAAA;YAAA,OAAmBnB,GAAA,CAAAzB,gBAAA,EAAkB;UAAA,EAAC;UACvEvK,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAuB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtCH,EAAA,CAAAC,cAAA,uBAAwB;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAInCF,EAJmC,CAAAG,YAAA,EAAa,EAC7B,EACE,EACb,EACF;UAIJH,EADF,CAAAC,cAAA,gBAA6B,kBACwD;UAGjFD,EAAA,CAAAoN,uBAAA,SAAkC;UAEhCpN,EADA,CAAAoB,UAAA,MAAAiM,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAM1CtN,EAAA,CAAAoN,uBAAA,SAAkC;UAEhCpN,EADA,CAAAoB,UAAA,MAAAmM,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAS1CxN,EAAA,CAAAoN,uBAAA,SAAqC;UAEnCpN,EADA,CAAAoB,UAAA,MAAAqM,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAmB1C1N,EAAA,CAAAoN,uBAAA,SAAsC;UAEpCpN,EADA,CAAAoB,UAAA,MAAAuM,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAY1C5N,EAAA,CAAAoN,uBAAA,SAAsC;UAEpCpN,EADA,CAAAoB,UAAA,MAAAyM,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAS1C9N,EAAA,CAAAoN,uBAAA,SAAqC;UAEnCpN,EADA,CAAAoB,UAAA,MAAA2M,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAQ1ChO,EAAA,CAAAoN,uBAAA,SAAoC;UAElCpN,EADA,CAAAoB,UAAA,MAAA6M,qCAAA,iBAAsD,MAAAC,qCAAA,iBACd;;UAc1ClO,EAAA,CAAAoN,uBAAA,SAAoC;UAElCpN,EADA,CAAAoB,UAAA,MAAA+M,qCAAA,iBAAsC,MAAAC,qCAAA,iBACE;;UAQ1CpO,EAAA,CAAAoN,uBAAA,SAAqC;UAEnCpN,EADA,CAAAoB,UAAA,MAAAiN,qCAAA,iBAAsC,MAAAC,qCAAA,kBACE;;UA2B1CtO,EADA,CAAAoB,UAAA,MAAAmN,qCAAA,iBAAuD,MAAAC,qCAAA,iBAEL;UAGtDxO,EADE,CAAAG,YAAA,EAAQ,EACJ;UAGNH,EAAA,CAAAC,cAAA,0BAKuB;UADrBD,EAAA,CAAA6C,UAAA,kBAAA4L,+DAAAnC,MAAA;YAAA,OAAQN,GAAA,CAAA5B,YAAA,CAAAkC,MAAA,CAAoB;UAAA,EAAC;UAKnCtM,EAHI,CAAAG,YAAA,EAAgB,EAEP,EACP;UAGNH,EAAA,CAAAoB,UAAA,MAAAsN,sCAAA,kBAA+C;UAKjD1O,EAAA,CAAAG,YAAA,EAAM;;;UArRaH,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAA2O,gBAAA,YAAA3C,GAAA,CAAAlH,UAAA,CAAwB;UAOnB9E,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAA2O,gBAAA,YAAA3C,GAAA,CAAAjH,oBAAA,CAAkC;UAEf/E,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA4L,GAAA,CAAAzH,aAAA,CAAgB;UASnCvE,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAA2O,gBAAA,YAAA3C,GAAA,CAAAhH,eAAA,CAA6B;UAEPhF,EAAA,CAAAO,SAAA,GAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA4L,GAAA,CAAAxH,SAAA,CAAY;UASlCxE,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAA2O,gBAAA,YAAA3C,GAAA,CAAA/G,cAAA,CAA4B;UA4BlBjF,EAAA,CAAAO,SAAA,IAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAqL,GAAA,CAAAvH,UAAA,CAAAC,cAAA,CAA+B;UAU/B1E,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAW,iBAAA,CAAAqL,GAAA,CAAAvH,UAAA,CAAAE,eAAA,CAAgC;UAUhC3E,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAqL,GAAA,CAAAvH,UAAA,CAAAG,cAAA,CAA+B;UAU/B5E,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAW,iBAAA,CAAAqL,GAAA,CAAAvH,UAAA,CAAAI,sBAAA,CAAuC;UAgBnC7E,EAAA,CAAAO,SAAA,IAAqC;UAArCP,EAAA,CAAAQ,kBAAA,MAAAwL,GAAA,CAAA3H,iBAAA,CAAA+E,MAAA,+BAAqC;UAKnDpJ,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAA2O,gBAAA,YAAA3C,GAAA,CAAA7G,QAAA,CAAsB;UAYrBnF,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,eAAA4L,GAAA,CAAA1H,kBAAA,CAAiC;UAoI5BtE,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAI,UAAA,oBAAA4L,GAAA,CAAA9G,gBAAA,CAAiC;UACpBlF,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,qBAAA4L,GAAA,CAAA9G,gBAAA,CAA0B;UAQ7DlF,EAAA,CAAAO,SAAA,EAAmC;UAEnCP,EAFA,CAAAI,UAAA,WAAA4L,GAAA,CAAA3H,iBAAA,CAAA+E,MAAA,CAAmC,aAAA4C,GAAA,CAAA7G,QAAA,CACd,oBAAAnF,EAAA,CAAA4O,eAAA,KAAAC,GAAA,EACgB;UASb7O,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA4L,GAAA,CAAA7H,SAAA,CAAe;;;qBDrP3ChF,YAAY,EAAA2P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZ7P,WAAW,EAAA8P,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXhQ,aAAa,EAAAiQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EACblQ,eAAe,EAAAmQ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfpQ,aAAa,EAAAqQ,EAAA,CAAAC,OAAA,EACbrQ,kBAAkB,EAAAsQ,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAClBxQ,cAAc,EAAAyQ,EAAA,CAAAC,QAAA,EACdzQ,eAAe,EAAA0Q,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,SAAA,EACf3Q,cAAc,EAAA4Q,GAAA,CAAAC,QAAA,EAAAD,GAAA,CAAAE,gBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,UAAA,EAAAL,GAAA,CAAAM,SAAA,EAAAN,GAAA,CAAAO,aAAA,EAAAP,GAAA,CAAAQ,OAAA,EAAAR,GAAA,CAAAS,YAAA,EAAAT,GAAA,CAAAU,MAAA,EACdrR,kBAAkB,EAAAsR,GAAA,CAAAC,YAAA,EAClBtR,aAAa,EAAAuR,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,aAAA,EACbxR,wBAAwB,EAAAyR,GAAA,CAAAC,kBAAA,EACxBzR,gBAAgB,EAAA0R,GAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;;SAKP3N,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}