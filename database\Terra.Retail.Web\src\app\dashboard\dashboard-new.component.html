<!-- Terra Retail ERP - Professional Dashboard -->
<div class="dashboard-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">لوحة التحكم</h1>
        <p class="page-subtitle">مرحباً بك في نظام Terra Retail ERP</p>
      </div>
      <div class="header-right">
        <div class="date-time">
          <div class="current-date">{{ currentDate | date:'EEEE, dd MMMM yyyy' }}</div>
          <div class="current-time">{{ currentTime | date:'HH:mm' }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Stats Cards -->
  <div class="stats-grid">
    <div class="stat-card sales-card" [class.loading]="isLoading">
      <div class="stat-icon">
        <mat-icon>trending_up</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats.todaySales | number:'1.0-0' }} جنيه</div>
        <div class="stat-label">مبيعات اليوم</div>
        <div class="stat-change positive">
          <mat-icon>arrow_upward</mat-icon>
          <span>+12.5%</span>
        </div>
      </div>
      <div class="stat-chart">
        <!-- Mini chart placeholder -->
        <div class="mini-chart sales-chart"></div>
      </div>
    </div>

    <div class="stat-card orders-card" [class.loading]="isLoading">
      <div class="stat-icon">
        <mat-icon>shopping_cart</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats.todayOrders }}</div>
        <div class="stat-label">طلبات اليوم</div>
        <div class="stat-change positive">
          <mat-icon>arrow_upward</mat-icon>
          <span>+8.2%</span>
        </div>
      </div>
      <div class="stat-chart">
        <div class="mini-chart orders-chart"></div>
      </div>
    </div>

    <div class="stat-card customers-card" [class.loading]="isLoading">
      <div class="stat-icon">
        <mat-icon>people</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats.customers }}</div>
        <div class="stat-label">إجمالي العملاء</div>
        <div class="stat-change positive">
          <mat-icon>arrow_upward</mat-icon>
          <span>+5.1%</span>
        </div>
      </div>
      <div class="stat-chart">
        <div class="mini-chart customers-chart"></div>
      </div>
    </div>

    <div class="stat-card products-card" [class.loading]="isLoading">
      <div class="stat-icon">
        <mat-icon>inventory_2</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats.products }}</div>
        <div class="stat-label">المنتجات</div>
        <div class="stat-change neutral">
          <mat-icon>remove</mat-icon>
          <span>0%</span>
        </div>
      </div>
      <div class="stat-chart">
        <div class="mini-chart products-chart"></div>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="content-grid">
    
    <!-- Sales Chart -->
    <div class="chart-card sales-overview">
      <div class="card-header">
        <h3>نظرة عامة على المبيعات</h3>
        <div class="card-actions">
          <button mat-button color="primary">عرض التفاصيل</button>
        </div>
      </div>
      <div class="card-content">
        <div class="chart-container">
          <canvas #salesChart></canvas>
        </div>
      </div>
    </div>

    <!-- Recent Orders -->
    <div class="data-card recent-orders">
      <div class="card-header">
        <h3>الطلبات الأخيرة</h3>
        <div class="card-actions">
          <button mat-icon-button>
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>
      <div class="card-content">
        <div class="orders-list">
          <div class="order-item" *ngFor="let order of recentOrders">
            <div class="order-info">
              <div class="order-number">#{{ order.id }}</div>
              <div class="order-customer">{{ order.customerName }}</div>
              <div class="order-time">{{ order.createdAt | date:'short' }}</div>
            </div>
            <div class="order-amount">{{ order.total | number:'1.0-0' }} جنيه</div>
            <div class="order-status" [class]="order.status">
              <span>{{ getOrderStatusText(order.status) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Products -->
    <div class="data-card top-products">
      <div class="card-header">
        <h3>أفضل المنتجات مبيعاً</h3>
        <div class="card-actions">
          <button mat-button color="primary">عرض الكل</button>
        </div>
      </div>
      <div class="card-content">
        <div class="products-list">
          <div class="product-item" *ngFor="let product of topProducts; let i = index">
            <div class="product-rank">{{ i + 1 }}</div>
            <div class="product-image">
              <img [src]="product.image || 'assets/images/product-placeholder.svg'" [alt]="product.name">
            </div>
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-category">{{ product.category }}</div>
            </div>
            <div class="product-sales">
              <div class="sales-count">{{ product.salesCount }} مبيعة</div>
              <div class="sales-amount">{{ product.salesAmount | number:'1.0-0' }} جنيه</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Status -->
    <div class="status-card system-status">
      <div class="card-header">
        <h3>حالة النظام</h3>
        <div class="card-actions">
          <div class="status-indicator" [class]="systemStatus.overall">
            <span>{{ getSystemStatusText(systemStatus.overall) }}</span>
          </div>
        </div>
      </div>
      <div class="card-content">
        <div class="status-items">
          <div class="status-item">
            <div class="status-icon">
              <mat-icon [class]="systemStatus.api">cloud</mat-icon>
            </div>
            <div class="status-info">
              <div class="status-name">خدمة API</div>
              <div class="status-value">{{ getStatusText(systemStatus.api) }}</div>
            </div>
          </div>

          <div class="status-item">
            <div class="status-icon">
              <mat-icon [class]="systemStatus.database">storage</mat-icon>
            </div>
            <div class="status-info">
              <div class="status-name">قاعدة البيانات</div>
              <div class="status-value">{{ getStatusText(systemStatus.database) }}</div>
            </div>
          </div>

          <div class="status-item">
            <div class="status-icon">
              <mat-icon [class]="systemStatus.cache">memory</mat-icon>
            </div>
            <div class="status-info">
              <div class="status-name">ذاكرة التخزين المؤقت</div>
              <div class="status-value">{{ getStatusText(systemStatus.cache) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-card quick-actions">
      <div class="card-header">
        <h3>إجراءات سريعة</h3>
      </div>
      <div class="card-content">
        <div class="actions-grid">
          <button mat-raised-button color="primary" class="action-btn" (click)="openPOS()">
            <mat-icon>point_of_sale</mat-icon>
            <span>فتح نقطة البيع</span>
          </button>

          <button mat-raised-button color="accent" class="action-btn" routerLink="/customers/add">
            <mat-icon>person_add</mat-icon>
            <span>إضافة عميل</span>
          </button>

          <button mat-raised-button class="action-btn" routerLink="/products/add">
            <mat-icon>add_box</mat-icon>
            <span>إضافة منتج</span>
          </button>

          <button mat-raised-button class="action-btn" routerLink="/reports">
            <mat-icon>assessment</mat-icon>
            <span>عرض التقارير</span>
          </button>

          <button mat-raised-button class="action-btn" routerLink="/inventory">
            <mat-icon>inventory</mat-icon>
            <span>إدارة المخزون</span>
          </button>

          <button mat-raised-button class="action-btn" routerLink="/settings">
            <mat-icon>settings</mat-icon>
            <span>الإعدادات</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <div class="notifications-card">
      <div class="card-header">
        <h3>الإشعارات</h3>
        <div class="card-actions">
          <button mat-button color="primary">عرض الكل</button>
        </div>
      </div>
      <div class="card-content">
        <div class="notifications-list">
          <div class="notification-item" *ngFor="let notification of notifications">
            <div class="notification-icon" [class]="notification.type">
              <mat-icon>{{ getNotificationIcon(notification.type) }}</mat-icon>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ notification.createdAt | date:'short' }}</div>
            </div>
            <button mat-icon-button class="notification-action">
              <mat-icon>more_vert</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>جاري تحميل البيانات...</p>
  </div>

</div>
