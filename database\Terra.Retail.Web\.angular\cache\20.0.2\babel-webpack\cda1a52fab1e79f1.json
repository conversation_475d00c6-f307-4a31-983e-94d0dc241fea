{"ast": null, "code": "import { __asyncGenerator, __await } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n  return __asyncGenerator(this, arguments, function* readableStreamLikeToAsyncGenerator_1() {\n    const reader = readableStream.getReader();\n    try {\n      while (true) {\n        const {\n          value,\n          done\n        } = yield __await(reader.read());\n        if (done) {\n          return yield __await(void 0);\n        }\n        yield yield __await(value);\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  });\n}\nexport function isReadableStreamLike(obj) {\n  return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}", "map": {"version": 3, "names": ["__asyncGenerator", "__await", "isFunction", "readableStreamLikeToAsyncGenerator", "readableStream", "arguments", "readableStreamLikeToAsyncGenerator_1", "reader", "<PERSON><PERSON><PERSON><PERSON>", "value", "done", "read", "releaseLock", "isReadableStreamLike", "obj"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/rxjs/dist/esm/internal/util/isReadableStreamLike.js"], "sourcesContent": ["import { __asyncGenerator, __await } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n    return __asyncGenerator(this, arguments, function* readableStreamLikeToAsyncGenerator_1() {\n        const reader = readableStream.getReader();\n        try {\n            while (true) {\n                const { value, done } = yield __await(reader.read());\n                if (done) {\n                    return yield __await(void 0);\n                }\n                yield yield __await(value);\n            }\n        }\n        finally {\n            reader.releaseLock();\n        }\n    });\n}\nexport function isReadableStreamLike(obj) {\n    return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,OAAO,QAAQ,OAAO;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,kCAAkCA,CAACC,cAAc,EAAE;EAC/D,OAAOJ,gBAAgB,CAAC,IAAI,EAAEK,SAAS,EAAE,UAAUC,oCAAoCA,CAAA,EAAG;IACtF,MAAMC,MAAM,GAAGH,cAAc,CAACI,SAAS,CAAC,CAAC;IACzC,IAAI;MACA,OAAO,IAAI,EAAE;QACT,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMT,OAAO,CAACM,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC;QACpD,IAAID,IAAI,EAAE;UACN,OAAO,MAAMT,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC;QACA,MAAM,MAAMA,OAAO,CAACQ,KAAK,CAAC;MAC9B;IACJ,CAAC,SACO;MACJF,MAAM,CAACK,WAAW,CAAC,CAAC;IACxB;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtC,OAAOZ,UAAU,CAACY,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACN,SAAS,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}