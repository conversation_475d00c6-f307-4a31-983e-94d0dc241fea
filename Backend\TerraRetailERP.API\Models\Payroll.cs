using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Payrolls")]
    public class Payroll
    {
        [Key]
        public int Id { get; set; }

        public int EmployeeId { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime PayrollDate { get; set; }

        // Earnings
        [Column(TypeName = "decimal(10,2)")]
        public decimal BasicSalary { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal Allowances { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal OvertimeAmount { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Bonuses { get; set; } = 0;

        // Deductions
        [Column(TypeName = "decimal(10,2)")]
        public decimal SocialInsurance { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal IncomeTax { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Penalties { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal Advances { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal OtherDeductions { get; set; } = 0;

        // Computed Properties
        public decimal GrossAmount => BasicSalary + Allowances + OvertimeAmount + Bonuses;
        public decimal TotalDeductions => SocialInsurance + IncomeTax + Penalties + Advances + OtherDeductions;
        public decimal NetAmount => GrossAmount - TotalDeductions;

        [StringLength(20)]
        public string PayrollStatus { get; set; } = "Calculated"; // Calculated, Approved, Paid

        public bool IsPaid { get; set; } = false;
        public DateTime? PaidDate { get; set; }
        public int? PaidBy { get; set; }

        [StringLength(20)]
        public string? PaymentMethod { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;

        [ForeignKey("PaidBy")]
        public virtual User? PaidByUser { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User Creator { get; set; } = null!;

        public virtual ICollection<PayrollItem> PayrollItems { get; set; } = new List<PayrollItem>();
        public virtual ICollection<Payslip> Payslips { get; set; } = new List<Payslip>();
    }

    [Table("PayrollItems")]
    public class PayrollItem
    {
        [Key]
        public int Id { get; set; }

        public int PayrollId { get; set; }

        [Required]
        [StringLength(20)]
        public string ItemType { get; set; } = string.Empty; // Earning, Deduction

        [Required]
        [StringLength(20)]
        public string ItemCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ItemNameAr { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ItemNameEn { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? Quantity { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? Rate { get; set; }

        [StringLength(200)]
        public string? Description { get; set; }

        // Navigation Properties
        [ForeignKey("PayrollId")]
        public virtual Payroll Payroll { get; set; } = null!;
    }

    [Table("Payslips")]
    public class Payslip
    {
        [Key]
        public int Id { get; set; }

        public int PayrollId { get; set; }

        [Required]
        [StringLength(50)]
        public string PayslipNumber { get; set; } = string.Empty;

        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public int GeneratedBy { get; set; }
        public bool IsEmailSent { get; set; } = false;
        public DateTime? EmailSentAt { get; set; }
        public bool IsPrinted { get; set; } = false;
        public DateTime? PrintedAt { get; set; }
        public int? PrintedBy { get; set; }

        // Navigation Properties
        [ForeignKey("PayrollId")]
        public virtual Payroll Payroll { get; set; } = null!;

        [ForeignKey("GeneratedBy")]
        public virtual User GeneratedByUser { get; set; } = null!;

        [ForeignKey("PrintedBy")]
        public virtual User? PrintedByUser { get; set; }
    }
}
