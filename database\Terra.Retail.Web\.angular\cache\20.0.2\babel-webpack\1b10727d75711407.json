{"ast": null, "code": "import { c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-BzhYL_xC.mjs';\nconst _c0 = [\"panel\"];\nconst _c1 = [\"*\"];\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 0);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-mdc-autocomplete-visible\", ctx_r1.showPanel)(\"mat-mdc-autocomplete-hidden\", !ctx_r1.showPanel)(\"mat-autocomplete-panel-animations-enabled\", !ctx_r1._animationsDisabled)(\"mat-primary\", ctx_r1._color === \"primary\")(\"mat-accent\", ctx_r1._color === \"accent\")(\"mat-warn\", ctx_r1._color === \"warn\");\n    i0.ɵɵproperty(\"id\", ctx_r1.id);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\nexport { a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, EventEmitter, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ContentChildren, Input, Output, Directive, forwardRef, Injector, EnvironmentInjector, ViewContainerRef, NgZone, Renderer2, afterNextRender, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { _IdGenerator, ActiveDescendantKeyManager, removeAriaReferencedId, addAriaReferencedId } from '@angular/cdk/a11y';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget } from '@angular/cdk/platform';\nimport { Subscription, Subject, merge, of, defer, Observable } from 'rxjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, TAB, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { filter, map, startWith, switchMap, tap, delay, take } from 'rxjs/operators';\nimport { h as MAT_FORM_FIELD } from './form-field-C9DZXojn.mjs';\nimport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\nimport './structural-styles-CObeNzjn.mjs';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\nimport './index-BFRo2fUq.mjs';\nimport './pseudo-checkbox-module-4F8Up4PL.mjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n  source;\n  option;\n  constructor(/** Reference to the autocomplete panel that emitted the event. */\n  source, /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n}\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false,\n    hasBackdrop: false\n  };\n}\n/** Autocomplete component. */\nlet MatAutocomplete = /*#__PURE__*/(() => {\n  class MatAutocomplete {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS);\n    _animationsDisabled = _animationsDisabled();\n    _activeOptionChanges = Subscription.EMPTY;\n    /** Manages active item in option list based on key events. */\n    _keyManager;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n    showPanel = false;\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n      return this._isOpen && this.showPanel;\n    }\n    _isOpen = false;\n    /** Latest trigger that opened the autocomplete. */\n    _latestOpeningTrigger;\n    /** @docs-private Sets the theme color of the panel. */\n    _setColor(value) {\n      this._color = value;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** @docs-private theme color of the panel */\n    _color;\n    // The @ViewChild query for TemplateRef here needs to be static because some code paths\n    // lead to the overlay being created before change detection has finished for this component.\n    // Notably, another component may trigger `focus` on the autocomplete-trigger.\n    /** @docs-private */\n    template;\n    /** Element for the panel containing the autocomplete options. */\n    panel;\n    /** Reference to all options within the autocomplete. */\n    options;\n    /** Reference to all option groups within the autocomplete. */\n    optionGroups;\n    /** Aria label of the autocomplete. */\n    ariaLabel;\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Function that maps an option's control value to its display value in the trigger. */\n    displayWith = null;\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    autoActiveFirstOption;\n    /** Whether the active option should be selected as the user is navigating. */\n    autoSelectActiveOption;\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    requireSelection;\n    /**\n     * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n     * match the width of its host.\n     */\n    panelWidth;\n    /** Whether ripples are disabled within the autocomplete panel. */\n    disableRipple;\n    /** Event that is emitted whenever an option from the list is selected. */\n    optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n    opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n    closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n    optionActivated = new EventEmitter();\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n      this._classList = value;\n      this._elementRef.nativeElement.className = '';\n    }\n    _classList;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = value;\n      this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator;\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n      if (this.options) {\n        for (const option of this.options) {\n          option._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n    id = inject(_IdGenerator).getId('mat-autocomplete-');\n    /**\n     * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n     * @docs-private\n     */\n    inertGroups;\n    constructor() {\n      const platform = inject(Platform);\n      // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n      // Safari using VoiceOver. We should occasionally check back to see whether the bug\n      // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n      // option altogether.\n      this.inertGroups = platform?.SAFARI || false;\n      this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n      this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n      this.requireSelection = !!this._defaults.requireSelection;\n      this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n    }\n    ngAfterContentInit() {\n      this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap().skipPredicate(this._skipPredicate);\n      this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n        if (this.isOpen) {\n          this.optionActivated.emit({\n            source: this,\n            option: this.options.toArray()[index] || null\n          });\n        }\n      });\n      // Set the initial visibility state.\n      this._setVisibility();\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n      if (this.panel) {\n        this.panel.nativeElement.scrollTop = scrollTop;\n      }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n      return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n      this.showPanel = !!this.options?.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n      const event = new MatAutocompleteSelectedEvent(this, option);\n      this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n      if (this.ariaLabel) {\n        return null;\n      }\n      const labelExpression = labelId ? labelId + ' ' : '';\n      return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate() {\n      return false;\n    }\n    static ɵfac = function MatAutocomplete_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatAutocomplete)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatAutocomplete,\n      selectors: [[\"mat-autocomplete\"]],\n      contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        }\n      },\n      viewQuery: function MatAutocomplete_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-autocomplete\"],\n      inputs: {\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        displayWith: \"displayWith\",\n        autoActiveFirstOption: [2, \"autoActiveFirstOption\", \"autoActiveFirstOption\", booleanAttribute],\n        autoSelectActiveOption: [2, \"autoSelectActiveOption\", \"autoSelectActiveOption\", booleanAttribute],\n        requireSelection: [2, \"requireSelection\", \"requireSelection\", booleanAttribute],\n        panelWidth: \"panelWidth\",\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        classList: [0, \"class\", \"classList\"],\n        hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute]\n      },\n      outputs: {\n        optionSelected: \"optionSelected\",\n        opened: \"opened\",\n        closed: \"closed\",\n        optionActivated: \"optionActivated\"\n      },\n      exportAs: [\"matAutocomplete\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }])],\n      ngContentSelectors: _c1,\n      decls: 1,\n      vars: 0,\n      consts: [[\"panel\", \"\"], [\"role\", \"listbox\", 1, \"mat-mdc-autocomplete-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", 3, \"id\"]],\n      template: function MatAutocomplete_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 17, \"ng-template\");\n        }\n      },\n      styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatAutocomplete;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nlet MatAutocompleteOrigin = /*#__PURE__*/(() => {\n  class MatAutocompleteOrigin {\n    elementRef = inject(ElementRef);\n    constructor() {}\n    static ɵfac = function MatAutocompleteOrigin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatAutocompleteOrigin)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAutocompleteOrigin,\n      selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n      exportAs: [\"matAutocompleteOrigin\"]\n    });\n  }\n  return MatAutocompleteOrigin;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-autocomplete-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nlet MatAutocompleteTrigger = /*#__PURE__*/(() => {\n  class MatAutocompleteTrigger {\n    _environmentInjector = inject(EnvironmentInjector);\n    _element = inject(ElementRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _zone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _formField = inject(MAT_FORM_FIELD, {\n      optional: true,\n      host: true\n    });\n    _viewportRuler = inject(ViewportRuler);\n    _scrollStrategy = inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY);\n    _renderer = inject(Renderer2);\n    _animationsDisabled = _animationsDisabled();\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _overlayRef;\n    _portal;\n    _componentDestroyed = false;\n    _initialized = new Subject();\n    _keydownSubscription;\n    _outsideClickSubscription;\n    _cleanupWindowBlur;\n    /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n    _previousValue;\n    /** Value of the input element when the panel was attached (even if there are no options). */\n    _valueOnAttach;\n    /** Value on the previous keydown event. */\n    _valueOnLastKeydown;\n    /** Strategy that is used to position the panel. */\n    _positionStrategy;\n    /** Whether or not the label state is being overridden. */\n    _manuallyFloatingLabel = false;\n    /** The subscription for closing actions (some are bound to document). */\n    _closingActionsSubscription;\n    /** Subscription to viewport size changes. */\n    _viewportSubscription = Subscription.EMPTY;\n    /** Implements BreakpointObserver to be used to detect handset landscape */\n    _breakpointObserver = inject(BreakpointObserver);\n    _handsetLandscapeSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n    _canOpenOnNextFocus = true;\n    /** Value inside the input before we auto-selected an option. */\n    _valueBeforeAutoSelection;\n    /**\n     * Current option that we have auto-selected as the user is navigating,\n     * but which hasn't been propagated to the model value yet.\n     */\n    _pendingAutoselectedOption;\n    /** Stream of keyboard events that can close the panel. */\n    _closeKeyEventStream = new Subject();\n    /** Classes to apply to the panel. Exposed as a public property for internal usage. */\n    _overlayPanelClass = coerceArray(this._defaults?.overlayPanelClass || []);\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n    _windowBlurHandler = () => {\n      // If the user blurred the window while the autocomplete is focused, it means that it'll be\n      // refocused when they come back. In this case we want to skip the first focus event, if the\n      // pane was closed, in order to avoid reopening it unintentionally.\n      this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n    };\n    /** `View -> model callback called when value changes` */\n    _onChange = () => {};\n    /** `View -> model callback called when autocomplete has been touched` */\n    _onTouched = () => {};\n    /** The autocomplete panel to be attached to this trigger. */\n    autocomplete;\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n    position = 'auto';\n    /**\n     * Reference relative to which to position the autocomplete panel.\n     * Defaults to the autocomplete trigger element.\n     */\n    connectedTo;\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n    autocompleteAttribute = 'off';\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    autocompleteDisabled;\n    constructor() {}\n    /** Class to apply to the panel when it's above the input. */\n    _aboveClass = 'mat-mdc-autocomplete-panel-above';\n    ngAfterViewInit() {\n      this._initialized.next();\n      this._initialized.complete();\n      this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n    }\n    ngOnChanges(changes) {\n      if (changes['position'] && this._positionStrategy) {\n        this._setStrategyPositions(this._positionStrategy);\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._cleanupWindowBlur?.();\n      this._handsetLandscapeSubscription.unsubscribe();\n      this._viewportSubscription.unsubscribe();\n      this._componentDestroyed = true;\n      this._destroyPanel();\n      this._closeKeyEventStream.complete();\n      this._clearFromModal();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n      return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    _overlayAttached = false;\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n      this._openPanelInternal();\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n      this._resetLabel();\n      if (!this._overlayAttached) {\n        return;\n      }\n      if (this.panelOpen) {\n        // Only emit if the panel was visible.\n        // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n        // `_subscribeToClosingActions()` are also outside of the Angular zone.\n        // We should manually run in Angular zone to update UI after panel closing.\n        this._zone.run(() => {\n          this.autocomplete.closed.emit();\n        });\n      }\n      // Only reset if this trigger is the latest one that opened the\n      // autocomplete since another may have taken it over.\n      if (this.autocomplete._latestOpeningTrigger === this) {\n        this.autocomplete._isOpen = false;\n        this.autocomplete._latestOpeningTrigger = null;\n      }\n      this._overlayAttached = false;\n      this._pendingAutoselectedOption = null;\n      if (this._overlayRef && this._overlayRef.hasAttached()) {\n        this._overlayRef.detach();\n        this._closingActionsSubscription.unsubscribe();\n      }\n      this._updatePanelState();\n      // Note that in some cases this can end up being called after the component is destroyed.\n      // Add a check to ensure that we don't try to run change detection on a destroyed view.\n      if (!this._componentDestroyed) {\n        // We need to trigger change detection manually, because\n        // `fromEvent` doesn't seem to do it at the proper time.\n        // This ensures that the label is reset when the\n        // user clicks outside.\n        this._changeDetectorRef.detectChanges();\n      }\n      // Remove aria-owns attribute when the autocomplete is no longer visible.\n      if (this._trackedModal) {\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n      }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n      if (this._overlayAttached) {\n        this._overlayRef.updatePosition();\n      }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n      return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe(\n      // Normalize the output so we return a consistent type.\n      map(event => event instanceof MatOptionSelectionChange ? event : null));\n    }\n    /** Stream of changes to the selection state of the autocomplete options. */\n    optionSelections = defer(() => {\n      const options = this.autocomplete ? this.autocomplete.options : null;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n      // Return a stream that we'll replace with the real one once everything is in place.\n      return this._initialized.pipe(switchMap(() => this.optionSelections));\n    });\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n      if (this.autocomplete && this.autocomplete._keyManager) {\n        return this.autocomplete._keyManager.activeItem;\n      }\n      return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n      return new Observable(observer => {\n        const listener = event => {\n          // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n          // fall back to check the first element in the path of the click event.\n          const clickTarget = _getEventTarget(event);\n          const formField = this._formField ? this._formField.getConnectedOverlayOrigin().nativeElement : null;\n          const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n          if (this._overlayAttached && clickTarget !== this._element.nativeElement &&\n          // Normally focus moves inside `mousedown` so this condition will almost always be\n          // true. Its main purpose is to handle the case where the input is focused from an\n          // outside click which propagates up to the `body` listener within the same sequence\n          // and causes the panel to close immediately (see #3106).\n          !this._hasFocus() && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget)) {\n            observer.next(event);\n          }\n        };\n        const cleanups = [this._renderer.listen('document', 'click', listener), this._renderer.listen('document', 'auxclick', listener), this._renderer.listen('document', 'touchend', listener)];\n        return () => {\n          cleanups.forEach(current => current());\n        };\n      });\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n      Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n      this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(e) {\n      const event = e;\n      const keyCode = event.keyCode;\n      const hasModifier = hasModifierKey(event);\n      // Prevent the default action on all escape key presses. This is here primarily to bring IE\n      // in line with other browsers. By default, pressing escape on IE will cause it to revert\n      // the input value to the one that it had on focus, however it won't dispatch any events\n      // which means that the model value will be out of sync with the view.\n      if (keyCode === ESCAPE && !hasModifier) {\n        event.preventDefault();\n      }\n      this._valueOnLastKeydown = this._element.nativeElement.value;\n      if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n        this.activeOption._selectViaInteraction();\n        this._resetActiveItem();\n        event.preventDefault();\n      } else if (this.autocomplete) {\n        const prevActiveItem = this.autocomplete._keyManager.activeItem;\n        const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n        if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n          this.autocomplete._keyManager.onKeydown(event);\n        } else if (isArrowKey && this._canOpen()) {\n          this._openPanelInternal(this._valueOnLastKeydown);\n        }\n        if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n          this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n          if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n            if (!this._pendingAutoselectedOption) {\n              this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n            }\n            this._pendingAutoselectedOption = this.activeOption;\n            this._assignOptionValue(this.activeOption.value);\n          }\n        }\n      }\n    }\n    _handleInput(event) {\n      let target = event.target;\n      let value = target.value;\n      // Based on `NumberValueAccessor` from forms.\n      if (target.type === 'number') {\n        value = value == '' ? null : parseFloat(value);\n      }\n      // If the input has a placeholder, IE will fire the `input` event on page load,\n      // focus and blur, in addition to when the user actually changed the value. To\n      // filter out all of the extra events, we save the value on focus and between\n      // `input` events, and we check whether it changed.\n      // See: https://connect.microsoft.com/IE/feedback/details/885747/\n      if (this._previousValue !== value) {\n        this._previousValue = value;\n        this._pendingAutoselectedOption = null;\n        // If selection is required we don't write to the CVA while the user is typing.\n        // At the end of the selection either the user will have picked something\n        // or we'll reset the value back to null.\n        if (!this.autocomplete || !this.autocomplete.requireSelection) {\n          this._onChange(value);\n        }\n        if (!value) {\n          this._clearPreviousSelectedOption(null, false);\n        } else if (this.panelOpen && !this.autocomplete.requireSelection) {\n          // Note that we don't reset this when `requireSelection` is enabled,\n          // because the option will be reset when the panel is closed.\n          const selectedOption = this.autocomplete.options?.find(option => option.selected);\n          if (selectedOption) {\n            const display = this._getDisplayValue(selectedOption.value);\n            if (value !== display) {\n              selectedOption.deselect(false);\n            }\n          }\n        }\n        if (this._canOpen() && this._hasFocus()) {\n          // When the `input` event fires, the input's value will have already changed. This means\n          // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n          // behind. This can be a problem when the user selects a value, changes a character while\n          // the input still has focus and then clicks away (see #28432). To work around it, we\n          // capture the value in `keydown` so we can use it here.\n          const valueOnAttach = this._valueOnLastKeydown ?? this._element.nativeElement.value;\n          this._valueOnLastKeydown = null;\n          this._openPanelInternal(valueOnAttach);\n        }\n      }\n    }\n    _handleFocus() {\n      if (!this._canOpenOnNextFocus) {\n        this._canOpenOnNextFocus = true;\n      } else if (this._canOpen()) {\n        this._previousValue = this._element.nativeElement.value;\n        this._attachOverlay(this._previousValue);\n        this._floatLabel(true);\n      }\n    }\n    _handleClick() {\n      if (this._canOpen() && !this.panelOpen) {\n        this._openPanelInternal();\n      }\n    }\n    /** Whether the input currently has focus. */\n    _hasFocus() {\n      return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n      if (this._formField && this._formField.floatLabel === 'auto') {\n        if (shouldAnimate) {\n          this._formField._animateAndLockLabel();\n        } else {\n          this._formField.floatLabel = 'always';\n        }\n        this._manuallyFloatingLabel = true;\n      }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n      if (this._manuallyFloatingLabel) {\n        if (this._formField) {\n          this._formField.floatLabel = 'auto';\n        }\n        this._manuallyFloatingLabel = false;\n      }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n      const initialRender = new Observable(subscriber => {\n        afterNextRender(() => {\n          subscriber.next();\n        }, {\n          injector: this._environmentInjector\n        });\n      });\n      const optionChanges = this.autocomplete.options?.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()),\n      // Defer emitting to the stream until the next tick, because changing\n      // bindings in here will cause \"changed after checked\" errors.\n      delay(0)) ?? of();\n      // When the options are initially rendered, and when the option list changes...\n      return merge(initialRender, optionChanges).pipe(\n      // create a new stream of panelClosingActions, replacing any previous streams\n      // that were created, and flatten it so our stream only emits closing events...\n      switchMap(() => this._zone.run(() => {\n        // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n        // the Angular zone. This will lead to change detection being called outside of the Angular\n        // zone and the `autocomplete.opened` will also emit outside of the Angular.\n        const wasOpen = this.panelOpen;\n        this._resetActiveItem();\n        this._updatePanelState();\n        this._changeDetectorRef.detectChanges();\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition();\n        }\n        if (wasOpen !== this.panelOpen) {\n          // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n          // `closed` event, because we may not have emitted it. This can happen\n          // - if the users opens the panel and there are no options, but the\n          //   options come in slightly later or as a result of the value changing,\n          // - if the panel is closed after the user entered a string that did not match any\n          //   of the available options,\n          // - if a valid string is entered after an invalid one.\n          if (this.panelOpen) {\n            this._emitOpened();\n          } else {\n            this.autocomplete.closed.emit();\n          }\n        }\n        return this.panelClosingActions;\n      })),\n      // when the first closing event occurs...\n      take(1))\n      // set the value, close the panel, and complete.\n      .subscribe(event => this._setValueAndClose(event));\n    }\n    /**\n     * Emits the opened event once it's known that the panel will be shown and stores\n     * the state of the trigger right before the opening sequence was finished.\n     */\n    _emitOpened() {\n      this.autocomplete.opened.emit();\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n      if (this._overlayRef) {\n        this.closePanel();\n        this._overlayRef.dispose();\n        this._overlayRef = null;\n      }\n    }\n    /** Given a value, returns the string that should be shown within the input. */\n    _getDisplayValue(value) {\n      const autocomplete = this.autocomplete;\n      return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n    }\n    _assignOptionValue(value) {\n      const toDisplay = this._getDisplayValue(value);\n      if (value == null) {\n        this._clearPreviousSelectedOption(null, false);\n      }\n      // Simply falling back to an empty string if the display value is falsy does not work properly.\n      // The display value can also be the number zero and shouldn't fall back to an empty string.\n      this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n      // If it's used within a `MatFormField`, we should set it through the property so it can go\n      // through change detection.\n      if (this._formField) {\n        this._formField._control.value = value;\n      } else {\n        this._element.nativeElement.value = value;\n      }\n      this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n      const panel = this.autocomplete;\n      const toSelect = event ? event.source : this._pendingAutoselectedOption;\n      if (toSelect) {\n        this._clearPreviousSelectedOption(toSelect);\n        this._assignOptionValue(toSelect.value);\n        // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n        // gets reset while the panel is still animating which looks glitchy. It'll likely break\n        // some tests to change it at this point.\n        this._onChange(toSelect.value);\n        panel._emitSelectEvent(toSelect);\n        this._element.nativeElement.focus();\n      } else if (panel.requireSelection && this._element.nativeElement.value !== this._valueOnAttach) {\n        this._clearPreviousSelectedOption(null);\n        this._assignOptionValue(null);\n        this._onChange(null);\n      }\n      this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip, emitEvent) {\n      // Null checks are necessary here, because the autocomplete\n      // or its options may not have been assigned yet.\n      this.autocomplete?.options?.forEach(option => {\n        if (option !== skip && option.selected) {\n          option.deselect(emitEvent);\n        }\n      });\n    }\n    _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n      this._attachOverlay(valueOnAttach);\n      this._floatLabel();\n      // Add aria-owns attribute when the autocomplete becomes visible.\n      if (this._trackedModal) {\n        const panelId = this.autocomplete.id;\n        addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n    }\n    _attachOverlay(valueOnAttach) {\n      if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatAutocompleteMissingPanelError();\n      }\n      let overlayRef = this._overlayRef;\n      if (!overlayRef) {\n        this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n          id: this._formField?.getLabelId()\n        });\n        overlayRef = createOverlayRef(this._injector, this._getOverlayConfig());\n        this._overlayRef = overlayRef;\n        this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n          if (this.panelOpen && overlayRef) {\n            overlayRef.updateSize({\n              width: this._getPanelWidth()\n            });\n          }\n        });\n        // Subscribe to the breakpoint events stream to detect when screen is in\n        // handsetLandscape.\n        this._handsetLandscapeSubscription = this._breakpointObserver.observe(Breakpoints.HandsetLandscape).subscribe(result => {\n          const isHandsetLandscape = result.matches;\n          // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n          // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n          if (isHandsetLandscape) {\n            this._positionStrategy.withFlexibleDimensions(true).withGrowAfterOpen(true).withViewportMargin(8);\n          } else {\n            this._positionStrategy.withFlexibleDimensions(false).withGrowAfterOpen(false).withViewportMargin(0);\n          }\n        });\n      } else {\n        // Update the trigger, panel width and direction, in case anything has changed.\n        this._positionStrategy.setOrigin(this._getConnectedElement());\n        overlayRef.updateSize({\n          width: this._getPanelWidth()\n        });\n      }\n      if (overlayRef && !overlayRef.hasAttached()) {\n        overlayRef.attach(this._portal);\n        this._valueOnAttach = valueOnAttach;\n        this._valueOnLastKeydown = null;\n        this._closingActionsSubscription = this._subscribeToClosingActions();\n      }\n      const wasOpen = this.panelOpen;\n      this.autocomplete._isOpen = this._overlayAttached = true;\n      this.autocomplete._latestOpeningTrigger = this;\n      this.autocomplete._setColor(this._formField?.color);\n      this._updatePanelState();\n      this._applyModalPanelOwnership();\n      // We need to do an extra `panelOpen` check in here, because the\n      // autocomplete won't be shown if there are no options.\n      if (this.panelOpen && wasOpen !== this.panelOpen) {\n        this._emitOpened();\n      }\n    }\n    /** Handles keyboard events coming from the overlay panel. */\n    _handlePanelKeydown = event => {\n      // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n      // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n      if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n        // If the user had typed something in before we autoselected an option, and they decided\n        // to cancel the selection, restore the input value to the one they had typed in.\n        if (this._pendingAutoselectedOption) {\n          this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n          this._pendingAutoselectedOption = null;\n        }\n        this._closeKeyEventStream.next();\n        this._resetActiveItem();\n        // We need to stop propagation, otherwise the event will eventually\n        // reach the input itself and cause the overlay to be reopened.\n        event.stopPropagation();\n        event.preventDefault();\n      }\n    };\n    /** Updates the panel's visibility state and any trigger state tied to id. */\n    _updatePanelState() {\n      this.autocomplete._setVisibility();\n      // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n      // because the act of subscribing will prevent events from reaching other overlays and\n      // we don't want to block the events if there are no options.\n      if (this.panelOpen) {\n        const overlayRef = this._overlayRef;\n        if (!this._keydownSubscription) {\n          // Use the `keydownEvents` in order to take advantage of\n          // the overlay event targeting provided by the CDK overlay.\n          this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n        }\n        if (!this._outsideClickSubscription) {\n          // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n          // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n          // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n          this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n        }\n      } else {\n        this._keydownSubscription?.unsubscribe();\n        this._outsideClickSubscription?.unsubscribe();\n        this._keydownSubscription = this._outsideClickSubscription = null;\n      }\n    }\n    _getOverlayConfig() {\n      return new OverlayConfig({\n        positionStrategy: this._getOverlayPosition(),\n        scrollStrategy: this._scrollStrategy(),\n        width: this._getPanelWidth(),\n        direction: this._dir ?? undefined,\n        hasBackdrop: this._defaults?.hasBackdrop,\n        backdropClass: this._defaults?.backdropClass,\n        panelClass: this._overlayPanelClass,\n        disableAnimations: this._animationsDisabled\n      });\n    }\n    _getOverlayPosition() {\n      // Set default Overlay Position\n      const strategy = createFlexibleConnectedPositionStrategy(this._injector, this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n      this._setStrategyPositions(strategy);\n      this._positionStrategy = strategy;\n      return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n      // Note that we provide horizontal fallback positions, even though by default the dropdown\n      // width matches the input, because consumers can override the width. See #18854.\n      const belowPositions = [{\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top'\n      }, {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top'\n      }];\n      // The overlay edge connected to the trigger should have squared corners, while\n      // the opposite end has rounded corners. We apply a CSS class to swap the\n      // border-radius based on the overlay position.\n      const panelClass = this._aboveClass;\n      const abovePositions = [{\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n        panelClass\n      }, {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n        panelClass\n      }];\n      let positions;\n      if (this.position === 'above') {\n        positions = abovePositions;\n      } else if (this.position === 'below') {\n        positions = belowPositions;\n      } else {\n        positions = [...belowPositions, ...abovePositions];\n      }\n      positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n      if (this.connectedTo) {\n        return this.connectedTo.elementRef;\n      }\n      return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n      return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n      return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n     * option.\n     *\n     * If the consumer opted-in to automatically activatating the first option, activate the first\n     * *enabled* option.\n     */\n    _resetActiveItem() {\n      const autocomplete = this.autocomplete;\n      if (autocomplete.autoActiveFirstOption) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < autocomplete.options.length; index++) {\n          const option = autocomplete.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        autocomplete._keyManager.setActiveItem(-1);\n      }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n      const element = this._element.nativeElement;\n      return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n      // Given that we are not actually focusing active options, we must manually adjust scroll\n      // to reveal options below the fold. First, we find the offset of the option from the top\n      // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n      // the panel height + the option height, so the active option will be just visible at the\n      // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n      // will become the offset. If that offset is visible within the panel already, the scrollTop is\n      // not adjusted.\n      const autocomplete = this.autocomplete;\n      const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        autocomplete._setScrollTop(0);\n      } else if (autocomplete.panel) {\n        const option = autocomplete.options.toArray()[index];\n        if (option) {\n          const element = option._getHostElement();\n          const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n          autocomplete._setScrollTop(newScrollPosition);\n        }\n      }\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      if (!modal) {\n        // Most commonly, the autocomplete trigger is not inside a modal.\n        return;\n      }\n      const panelId = this.autocomplete.id;\n      if (this._trackedModal) {\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n      addAriaReferencedId(modal, 'aria-owns', panelId);\n      this._trackedModal = modal;\n    }\n    /** Clears the references to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n      if (this._trackedModal) {\n        const panelId = this.autocomplete.id;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n      }\n    }\n    static ɵfac = function MatAutocompleteTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatAutocompleteTrigger)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAutocompleteTrigger,\n      selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-autocomplete-trigger\"],\n      hostVars: 7,\n      hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n            return ctx._handleFocus();\n          })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n            return ctx._onTouched();\n          })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n            return ctx._handleInput($event);\n          })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n            return ctx._handleClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-controls\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n        }\n      },\n      inputs: {\n        autocomplete: [0, \"matAutocomplete\", \"autocomplete\"],\n        position: [0, \"matAutocompletePosition\", \"position\"],\n        connectedTo: [0, \"matAutocompleteConnectedTo\", \"connectedTo\"],\n        autocompleteAttribute: [0, \"autocomplete\", \"autocompleteAttribute\"],\n        autocompleteDisabled: [2, \"matAutocompleteDisabled\", \"autocompleteDisabled\", booleanAttribute]\n      },\n      exportAs: [\"matAutocompleteTrigger\"],\n      features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatAutocompleteTrigger;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatAutocompleteModule = /*#__PURE__*/(() => {\n  class MatAutocompleteModule {\n    static ɵfac = function MatAutocompleteModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatAutocompleteModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatAutocompleteModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n    });\n  }\n  return MatAutocompleteModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption, getMatAutocompleteMissingPanelError };", "map": {"version": 3, "names": ["c", "MAT_OPTION_PARENT_COMPONENT", "M", "MatOption", "d", "MAT_OPTGROUP", "e", "MatOptionSelectionChange", "_", "_countGroupLabelsBeforeOption", "b", "_getOptionScrollPosition", "_c0", "_c1", "MatAutocomplete_ng_template_0_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "formFieldId_r1", "id", "ctx_r1", "ɵɵnextContext", "ɵɵclassMap", "_classList", "ɵɵclassProp", "showPanel", "_animationsDisabled", "_color", "ɵɵproperty", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "a", "MatOptgroup", "InjectionToken", "inject", "ChangeDetectorRef", "ElementRef", "EventEmitter", "booleanAttribute", "TemplateRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "ContentChildren", "Input", "Output", "Directive", "forwardRef", "Injector", "EnvironmentInjector", "ViewContainerRef", "NgZone", "Renderer2", "afterNextRender", "NgModule", "ViewportRuler", "CdkScrollableModule", "createRepositionScrollStrategy", "createOverlayRef", "OverlayConfig", "createFlexibleConnectedPositionStrategy", "OverlayModule", "_IdGenerator", "ActiveDescendantKeyManager", "removeAriaReferencedId", "addAriaReferencedId", "Platform", "_getFocusedElementPierceShadowDom", "_getEventTarget", "Subscription", "Subject", "merge", "of", "defer", "Observable", "Directionality", "hasModifierKey", "ESCAPE", "ENTER", "TAB", "UP_ARROW", "DOWN_ARROW", "BreakpointObserver", "Breakpoints", "TemplatePortal", "coerce<PERSON><PERSON><PERSON>", "NG_VALUE_ACCESSOR", "filter", "map", "startWith", "switchMap", "tap", "delay", "take", "h", "MAT_FORM_FIELD", "MatOptionModule", "MatCommonModule", "MatAutocompleteSelectedEvent", "source", "option", "constructor", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY", "autoActiveFirstOption", "autoSelectActiveOption", "hideSingleSelectionIndicator", "requireSelection", "hasBackdrop", "MatAutocomplete", "_changeDetectorRef", "_elementRef", "_defaults", "_activeOptionChanges", "EMPTY", "_keyManager", "isOpen", "_isOpen", "_latestOpeningTrigger", "_setColor", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template", "panel", "options", "optionGroups", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayWith", "panelWidth", "disable<PERSON><PERSON><PERSON>", "optionSelected", "opened", "closed", "optionActivated", "classList", "nativeElement", "className", "_hideSingleSelectionIndicator", "_syncParentProperties", "getId", "inertGroups", "platform", "SAFARI", "ngAfterContentInit", "withWrap", "skipPredicate", "_skipPredicate", "change", "subscribe", "index", "emit", "toArray", "_setVisibility", "ngOnDestroy", "destroy", "unsubscribe", "_setScrollTop", "scrollTop", "_getScrollTop", "length", "_emitSelectEvent", "event", "labelId", "labelExpression", "ɵfac", "MatAutocomplete_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "MatAutocomplete_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "MatAutocomplete_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "decls", "vars", "consts", "MatAutocomplete_Template", "ɵɵprojectionDef", "ɵɵtemplate", "styles", "encapsulation", "changeDetection", "ngDevMode", "MatAutocompleteOrigin", "elementRef", "MatAutocompleteOrigin_Factory", "ɵdir", "ɵɵdefineDirective", "MAT_AUTOCOMPLETE_VALUE_ACCESSOR", "MatAutocompleteTrigger", "multi", "getMatAutocompleteMissingPanelError", "Error", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY", "injector", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY", "_overlay", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "_environmentInjector", "_element", "_injector", "_viewContainerRef", "_zone", "_dir", "optional", "_formField", "host", "_viewportRuler", "_scrollStrategy", "_renderer", "_overlayRef", "_portal", "_componentDestroyed", "_initialized", "_keydownSubscription", "_outsideClickSubscription", "_cleanupWindowBlur", "_previousValue", "_valueOnAttach", "_valueOnLastKeydown", "_positionStrategy", "_manuallyFloatingLabel", "_closingActionsSubscription", "_viewportSubscription", "_breakpointObserver", "_handsetLandscapeSubscription", "_canOpenOnNextFocus", "_valueBeforeAutoSelection", "_pendingAutoselectedOption", "_closeKeyEventStream", "_overlayPanelClass", "overlayPanelClass", "_windowBlurHandler", "panelOpen", "_hasFocus", "_onChange", "_onTouched", "autocomplete", "position", "connectedTo", "autocompleteAttribute", "autocompleteDisabled", "_aboveClass", "ngAfterViewInit", "next", "complete", "listen", "ngOnChanges", "changes", "_setStrategyPositions", "updatePosition", "_destroyPanel", "_clearFromModal", "_overlayAttached", "openPanel", "_openPanelInternal", "closePanel", "_resetLabel", "run", "has<PERSON>tta<PERSON>", "detach", "_updatePanelState", "detectChanges", "_trackedModal", "panelClosingActions", "optionSelections", "tabOut", "pipe", "_getOutsideClickStream", "detachments", "onSelectionChange", "activeOption", "activeItem", "observer", "listener", "clickTarget", "formField", "getConnectedOverlayOrigin", "customOrigin", "contains", "overlayElement", "cleanups", "for<PERSON>ach", "current", "writeValue", "Promise", "resolve", "then", "_assignOptionValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "_handleKeydown", "keyCode", "hasModifier", "preventDefault", "_selectViaInteraction", "_resetActiveItem", "prevActiveItem", "isArrowKey", "onKeydown", "_canOpen", "_scrollToOption", "activeItemIndex", "_handleInput", "target", "parseFloat", "_clearPreviousSelectedOption", "selectedOption", "find", "selected", "display", "_getDisplayValue", "deselect", "valueOnAttach", "_handleFocus", "_attachOverlay", "_floatLabel", "_handleClick", "shouldAnimate", "floatLabel", "_animateAndLockLabel", "_subscribeToClosingActions", "initialRender", "subscriber", "optionChanges", "reapplyLastPosition", "was<PERSON><PERSON>", "_emitOpened", "_setValueAndClose", "dispose", "toDisplay", "_updateNativeInputValue", "_control", "toSelect", "focus", "skip", "emitEvent", "panelId", "overlayRef", "getLabelId", "_getOverlayConfig", "updateSize", "width", "_get<PERSON><PERSON><PERSON><PERSON>idth", "observe", "HandsetLandscape", "result", "isHandsetLandscape", "matches", "withFlexibleDimensions", "withGrowAfterOpen", "withViewportMargin", "<PERSON><PERSON><PERSON><PERSON>", "_getConnectedElement", "attach", "color", "_applyModalPanelOwnership", "_handlePanelKeydown", "stopPropagation", "keydownEvents", "outsidePointerEvents", "positionStrategy", "_getOverlayPosition", "scrollStrategy", "direction", "undefined", "backdropClass", "panelClass", "disableAnimations", "strategy", "with<PERSON><PERSON>", "belowPositions", "originX", "originY", "overlayX", "overlayY", "abovePositions", "positions", "withPositions", "_getHostWidth", "getBoundingClientRect", "firstEnabledOptionIndex", "get", "setActiveItem", "element", "readOnly", "labelCount", "_getHostElement", "newScrollPosition", "offsetTop", "offsetHeight", "modal", "closest", "MatAutocompleteTrigger_Factory", "hostVars", "hostBindings", "MatAutocompleteTrigger_HostBindings", "ɵɵlistener", "MatAutocompleteTrigger_focusin_HostBindingHandler", "MatAutocompleteTrigger_blur_HostBindingHandler", "MatAutocompleteTrigger_input_HostBindingHandler", "$event", "MatAutocompleteTrigger_keydown_HostBindingHandler", "MatAutocompleteTrigger_click_HostBindingHandler", "toString", "ɵɵNgOnChangesFeature", "MatAutocompleteModule", "MatAutocompleteModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/autocomplete.mjs"], "sourcesContent": ["import { c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-BzhYL_xC.mjs';\nexport { a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, EventEmitter, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ContentChildren, Input, Output, Directive, forwardRef, Injector, EnvironmentInjector, ViewContainerRef, NgZone, Renderer2, afterNextRender, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { _IdGenerator, ActiveDescendantKeyManager, removeAriaReferencedId, addAriaReferencedId } from '@angular/cdk/a11y';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget } from '@angular/cdk/platform';\nimport { Subscription, Subject, merge, of, defer, Observable } from 'rxjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, TAB, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { filter, map, startWith, switchMap, tap, delay, take } from 'rxjs/operators';\nimport { h as MAT_FORM_FIELD } from './form-field-C9DZXojn.mjs';\nimport { M as MatOptionModule } from './index-DwiL-HGk.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\nimport './structural-styles-CObeNzjn.mjs';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\nimport './index-BFRo2fUq.mjs';\nimport './pseudo-checkbox-module-4F8Up4PL.mjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n    source;\n    option;\n    constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    source, \n    /** Option that was selected. */\n    option) {\n        this.source = source;\n        this.option = option;\n    }\n}\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        autoActiveFirstOption: false,\n        autoSelectActiveOption: false,\n        hideSingleSelectionIndicator: false,\n        requireSelection: false,\n        hasBackdrop: false,\n    };\n}\n/** Autocomplete component. */\nclass MatAutocomplete {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS);\n    _animationsDisabled = _animationsDisabled();\n    _activeOptionChanges = Subscription.EMPTY;\n    /** Manages active item in option list based on key events. */\n    _keyManager;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n    showPanel = false;\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n        return this._isOpen && this.showPanel;\n    }\n    _isOpen = false;\n    /** Latest trigger that opened the autocomplete. */\n    _latestOpeningTrigger;\n    /** @docs-private Sets the theme color of the panel. */\n    _setColor(value) {\n        this._color = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** @docs-private theme color of the panel */\n    _color;\n    // The @ViewChild query for TemplateRef here needs to be static because some code paths\n    // lead to the overlay being created before change detection has finished for this component.\n    // Notably, another component may trigger `focus` on the autocomplete-trigger.\n    /** @docs-private */\n    template;\n    /** Element for the panel containing the autocomplete options. */\n    panel;\n    /** Reference to all options within the autocomplete. */\n    options;\n    /** Reference to all option groups within the autocomplete. */\n    optionGroups;\n    /** Aria label of the autocomplete. */\n    ariaLabel;\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Function that maps an option's control value to its display value in the trigger. */\n    displayWith = null;\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    autoActiveFirstOption;\n    /** Whether the active option should be selected as the user is navigating. */\n    autoSelectActiveOption;\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    requireSelection;\n    /**\n     * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n     * match the width of its host.\n     */\n    panelWidth;\n    /** Whether ripples are disabled within the autocomplete panel. */\n    disableRipple;\n    /** Event that is emitted whenever an option from the list is selected. */\n    optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n    opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n    closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n    optionActivated = new EventEmitter();\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n        this._classList = value;\n        this._elementRef.nativeElement.className = '';\n    }\n    _classList;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator;\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n    id = inject(_IdGenerator).getId('mat-autocomplete-');\n    /**\n     * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n     * @docs-private\n     */\n    inertGroups;\n    constructor() {\n        const platform = inject(Platform);\n        // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n        // Safari using VoiceOver. We should occasionally check back to see whether the bug\n        // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n        // option altogether.\n        this.inertGroups = platform?.SAFARI || false;\n        this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n        this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n        this.requireSelection = !!this._defaults.requireSelection;\n        this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n    }\n    ngAfterContentInit() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withWrap()\n            .skipPredicate(this._skipPredicate);\n        this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n            if (this.isOpen) {\n                this.optionActivated.emit({ source: this, option: this.options.toArray()[index] || null });\n            }\n        });\n        // Set the initial visibility state.\n        this._setVisibility();\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n        if (this.panel) {\n            this.panel.nativeElement.scrollTop = scrollTop;\n        }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n        return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n        this.showPanel = !!this.options?.length;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n        const event = new MatAutocompleteSelectedEvent(this, option);\n        this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate() {\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocomplete, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatAutocomplete, isStandalone: true, selector: \"mat-autocomplete\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], displayWith: \"displayWith\", autoActiveFirstOption: [\"autoActiveFirstOption\", \"autoActiveFirstOption\", booleanAttribute], autoSelectActiveOption: [\"autoSelectActiveOption\", \"autoSelectActiveOption\", booleanAttribute], requireSelection: [\"requireSelection\", \"requireSelection\", booleanAttribute], panelWidth: \"panelWidth\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], classList: [\"class\", \"classList\"], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute] }, outputs: { optionSelected: \"optionSelected\", opened: \"opened\", closed: \"closed\", optionActivated: \"optionActivated\" }, host: { classAttribute: \"mat-mdc-autocomplete\" }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], queries: [{ propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true, static: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }], exportAs: [\"matAutocomplete\"], ngImport: i0, template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocomplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-autocomplete', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'matAutocomplete', host: {\n                        'class': 'mat-mdc-autocomplete',\n                    }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], displayWith: [{\n                type: Input\n            }], autoActiveFirstOption: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoSelectActiveOption: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], requireSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], panelWidth: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], optionSelected: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], optionActivated: [{\n                type: Output\n            }], classList: [{\n                type: Input,\n                args: ['class']\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin {\n    elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteOrigin, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatAutocompleteOrigin, isStandalone: true, selector: \"[matAutocompleteOrigin]\", exportAs: [\"matAutocompleteOrigin\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matAutocompleteOrigin]',\n                    exportAs: 'matAutocompleteOrigin',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatAutocompleteTrigger),\n    multi: true,\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n    return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' +\n        'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n        \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createRepositionScrollStrategy(injector);\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(_overlay) {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n    deps: [],\n    useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass MatAutocompleteTrigger {\n    _environmentInjector = inject(EnvironmentInjector);\n    _element = inject(ElementRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _zone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, { optional: true });\n    _formField = inject(MAT_FORM_FIELD, { optional: true, host: true });\n    _viewportRuler = inject(ViewportRuler);\n    _scrollStrategy = inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY);\n    _renderer = inject(Renderer2);\n    _animationsDisabled = _animationsDisabled();\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, { optional: true });\n    _overlayRef;\n    _portal;\n    _componentDestroyed = false;\n    _initialized = new Subject();\n    _keydownSubscription;\n    _outsideClickSubscription;\n    _cleanupWindowBlur;\n    /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n    _previousValue;\n    /** Value of the input element when the panel was attached (even if there are no options). */\n    _valueOnAttach;\n    /** Value on the previous keydown event. */\n    _valueOnLastKeydown;\n    /** Strategy that is used to position the panel. */\n    _positionStrategy;\n    /** Whether or not the label state is being overridden. */\n    _manuallyFloatingLabel = false;\n    /** The subscription for closing actions (some are bound to document). */\n    _closingActionsSubscription;\n    /** Subscription to viewport size changes. */\n    _viewportSubscription = Subscription.EMPTY;\n    /** Implements BreakpointObserver to be used to detect handset landscape */\n    _breakpointObserver = inject(BreakpointObserver);\n    _handsetLandscapeSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n    _canOpenOnNextFocus = true;\n    /** Value inside the input before we auto-selected an option. */\n    _valueBeforeAutoSelection;\n    /**\n     * Current option that we have auto-selected as the user is navigating,\n     * but which hasn't been propagated to the model value yet.\n     */\n    _pendingAutoselectedOption;\n    /** Stream of keyboard events that can close the panel. */\n    _closeKeyEventStream = new Subject();\n    /** Classes to apply to the panel. Exposed as a public property for internal usage. */\n    _overlayPanelClass = coerceArray(this._defaults?.overlayPanelClass || []);\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n    _windowBlurHandler = () => {\n        // If the user blurred the window while the autocomplete is focused, it means that it'll be\n        // refocused when they come back. In this case we want to skip the first focus event, if the\n        // pane was closed, in order to avoid reopening it unintentionally.\n        this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n    };\n    /** `View -> model callback called when value changes` */\n    _onChange = () => { };\n    /** `View -> model callback called when autocomplete has been touched` */\n    _onTouched = () => { };\n    /** The autocomplete panel to be attached to this trigger. */\n    autocomplete;\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n    position = 'auto';\n    /**\n     * Reference relative to which to position the autocomplete panel.\n     * Defaults to the autocomplete trigger element.\n     */\n    connectedTo;\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n    autocompleteAttribute = 'off';\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    autocompleteDisabled;\n    constructor() { }\n    /** Class to apply to the panel when it's above the input. */\n    _aboveClass = 'mat-mdc-autocomplete-panel-above';\n    ngAfterViewInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n    }\n    ngOnChanges(changes) {\n        if (changes['position'] && this._positionStrategy) {\n            this._setStrategyPositions(this._positionStrategy);\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._cleanupWindowBlur?.();\n        this._handsetLandscapeSubscription.unsubscribe();\n        this._viewportSubscription.unsubscribe();\n        this._componentDestroyed = true;\n        this._destroyPanel();\n        this._closeKeyEventStream.complete();\n        this._clearFromModal();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n        return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    _overlayAttached = false;\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n        this._openPanelInternal();\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n        this._resetLabel();\n        if (!this._overlayAttached) {\n            return;\n        }\n        if (this.panelOpen) {\n            // Only emit if the panel was visible.\n            // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n            // `_subscribeToClosingActions()` are also outside of the Angular zone.\n            // We should manually run in Angular zone to update UI after panel closing.\n            this._zone.run(() => {\n                this.autocomplete.closed.emit();\n            });\n        }\n        // Only reset if this trigger is the latest one that opened the\n        // autocomplete since another may have taken it over.\n        if (this.autocomplete._latestOpeningTrigger === this) {\n            this.autocomplete._isOpen = false;\n            this.autocomplete._latestOpeningTrigger = null;\n        }\n        this._overlayAttached = false;\n        this._pendingAutoselectedOption = null;\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n            this._closingActionsSubscription.unsubscribe();\n        }\n        this._updatePanelState();\n        // Note that in some cases this can end up being called after the component is destroyed.\n        // Add a check to ensure that we don't try to run change detection on a destroyed view.\n        if (!this._componentDestroyed) {\n            // We need to trigger change detection manually, because\n            // `fromEvent` doesn't seem to do it at the proper time.\n            // This ensures that the label is reset when the\n            // user clicks outside.\n            this._changeDetectorRef.detectChanges();\n        }\n        // Remove aria-owns attribute when the autocomplete is no longer visible.\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n        }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n        if (this._overlayAttached) {\n            this._overlayRef.updatePosition();\n        }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n        return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef\n            ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n            : of()).pipe(\n        // Normalize the output so we return a consistent type.\n        map(event => (event instanceof MatOptionSelectionChange ? event : null)));\n    }\n    /** Stream of changes to the selection state of the autocomplete options. */\n    optionSelections = defer(() => {\n        const options = this.autocomplete ? this.autocomplete.options : null;\n        if (options) {\n            return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n        // Return a stream that we'll replace with the real one once everything is in place.\n        return this._initialized.pipe(switchMap(() => this.optionSelections));\n    });\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n        if (this.autocomplete && this.autocomplete._keyManager) {\n            return this.autocomplete._keyManager.activeItem;\n        }\n        return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n        return new Observable(observer => {\n            const listener = (event) => {\n                // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n                // fall back to check the first element in the path of the click event.\n                const clickTarget = _getEventTarget(event);\n                const formField = this._formField\n                    ? this._formField.getConnectedOverlayOrigin().nativeElement\n                    : null;\n                const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n                if (this._overlayAttached &&\n                    clickTarget !== this._element.nativeElement &&\n                    // Normally focus moves inside `mousedown` so this condition will almost always be\n                    // true. Its main purpose is to handle the case where the input is focused from an\n                    // outside click which propagates up to the `body` listener within the same sequence\n                    // and causes the panel to close immediately (see #3106).\n                    !this._hasFocus() &&\n                    (!formField || !formField.contains(clickTarget)) &&\n                    (!customOrigin || !customOrigin.contains(clickTarget)) &&\n                    !!this._overlayRef &&\n                    !this._overlayRef.overlayElement.contains(clickTarget)) {\n                    observer.next(event);\n                }\n            };\n            const cleanups = [\n                this._renderer.listen('document', 'click', listener),\n                this._renderer.listen('document', 'auxclick', listener),\n                this._renderer.listen('document', 'touchend', listener),\n            ];\n            return () => {\n                cleanups.forEach(current => current());\n            };\n        });\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(e) {\n        const event = e;\n        const keyCode = event.keyCode;\n        const hasModifier = hasModifierKey(event);\n        // Prevent the default action on all escape key presses. This is here primarily to bring IE\n        // in line with other browsers. By default, pressing escape on IE will cause it to revert\n        // the input value to the one that it had on focus, however it won't dispatch any events\n        // which means that the model value will be out of sync with the view.\n        if (keyCode === ESCAPE && !hasModifier) {\n            event.preventDefault();\n        }\n        this._valueOnLastKeydown = this._element.nativeElement.value;\n        if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n            this.activeOption._selectViaInteraction();\n            this._resetActiveItem();\n            event.preventDefault();\n        }\n        else if (this.autocomplete) {\n            const prevActiveItem = this.autocomplete._keyManager.activeItem;\n            const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n            if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n                this.autocomplete._keyManager.onKeydown(event);\n            }\n            else if (isArrowKey && this._canOpen()) {\n                this._openPanelInternal(this._valueOnLastKeydown);\n            }\n            if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n                this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n                if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n                    if (!this._pendingAutoselectedOption) {\n                        this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n                    }\n                    this._pendingAutoselectedOption = this.activeOption;\n                    this._assignOptionValue(this.activeOption.value);\n                }\n            }\n        }\n    }\n    _handleInput(event) {\n        let target = event.target;\n        let value = target.value;\n        // Based on `NumberValueAccessor` from forms.\n        if (target.type === 'number') {\n            value = value == '' ? null : parseFloat(value);\n        }\n        // If the input has a placeholder, IE will fire the `input` event on page load,\n        // focus and blur, in addition to when the user actually changed the value. To\n        // filter out all of the extra events, we save the value on focus and between\n        // `input` events, and we check whether it changed.\n        // See: https://connect.microsoft.com/IE/feedback/details/885747/\n        if (this._previousValue !== value) {\n            this._previousValue = value;\n            this._pendingAutoselectedOption = null;\n            // If selection is required we don't write to the CVA while the user is typing.\n            // At the end of the selection either the user will have picked something\n            // or we'll reset the value back to null.\n            if (!this.autocomplete || !this.autocomplete.requireSelection) {\n                this._onChange(value);\n            }\n            if (!value) {\n                this._clearPreviousSelectedOption(null, false);\n            }\n            else if (this.panelOpen && !this.autocomplete.requireSelection) {\n                // Note that we don't reset this when `requireSelection` is enabled,\n                // because the option will be reset when the panel is closed.\n                const selectedOption = this.autocomplete.options?.find(option => option.selected);\n                if (selectedOption) {\n                    const display = this._getDisplayValue(selectedOption.value);\n                    if (value !== display) {\n                        selectedOption.deselect(false);\n                    }\n                }\n            }\n            if (this._canOpen() && this._hasFocus()) {\n                // When the `input` event fires, the input's value will have already changed. This means\n                // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n                // behind. This can be a problem when the user selects a value, changes a character while\n                // the input still has focus and then clicks away (see #28432). To work around it, we\n                // capture the value in `keydown` so we can use it here.\n                const valueOnAttach = this._valueOnLastKeydown ?? this._element.nativeElement.value;\n                this._valueOnLastKeydown = null;\n                this._openPanelInternal(valueOnAttach);\n            }\n        }\n    }\n    _handleFocus() {\n        if (!this._canOpenOnNextFocus) {\n            this._canOpenOnNextFocus = true;\n        }\n        else if (this._canOpen()) {\n            this._previousValue = this._element.nativeElement.value;\n            this._attachOverlay(this._previousValue);\n            this._floatLabel(true);\n        }\n    }\n    _handleClick() {\n        if (this._canOpen() && !this.panelOpen) {\n            this._openPanelInternal();\n        }\n    }\n    /** Whether the input currently has focus. */\n    _hasFocus() {\n        return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n        if (this._formField && this._formField.floatLabel === 'auto') {\n            if (shouldAnimate) {\n                this._formField._animateAndLockLabel();\n            }\n            else {\n                this._formField.floatLabel = 'always';\n            }\n            this._manuallyFloatingLabel = true;\n        }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n        if (this._manuallyFloatingLabel) {\n            if (this._formField) {\n                this._formField.floatLabel = 'auto';\n            }\n            this._manuallyFloatingLabel = false;\n        }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n        const initialRender = new Observable(subscriber => {\n            afterNextRender(() => {\n                subscriber.next();\n            }, { injector: this._environmentInjector });\n        });\n        const optionChanges = this.autocomplete.options?.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), \n        // Defer emitting to the stream until the next tick, because changing\n        // bindings in here will cause \"changed after checked\" errors.\n        delay(0)) ?? of();\n        // When the options are initially rendered, and when the option list changes...\n        return (merge(initialRender, optionChanges)\n            .pipe(\n        // create a new stream of panelClosingActions, replacing any previous streams\n        // that were created, and flatten it so our stream only emits closing events...\n        switchMap(() => this._zone.run(() => {\n            // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n            // the Angular zone. This will lead to change detection being called outside of the Angular\n            // zone and the `autocomplete.opened` will also emit outside of the Angular.\n            const wasOpen = this.panelOpen;\n            this._resetActiveItem();\n            this._updatePanelState();\n            this._changeDetectorRef.detectChanges();\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n            if (wasOpen !== this.panelOpen) {\n                // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n                // `closed` event, because we may not have emitted it. This can happen\n                // - if the users opens the panel and there are no options, but the\n                //   options come in slightly later or as a result of the value changing,\n                // - if the panel is closed after the user entered a string that did not match any\n                //   of the available options,\n                // - if a valid string is entered after an invalid one.\n                if (this.panelOpen) {\n                    this._emitOpened();\n                }\n                else {\n                    this.autocomplete.closed.emit();\n                }\n            }\n            return this.panelClosingActions;\n        })), \n        // when the first closing event occurs...\n        take(1))\n            // set the value, close the panel, and complete.\n            .subscribe(event => this._setValueAndClose(event)));\n    }\n    /**\n     * Emits the opened event once it's known that the panel will be shown and stores\n     * the state of the trigger right before the opening sequence was finished.\n     */\n    _emitOpened() {\n        this.autocomplete.opened.emit();\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n        if (this._overlayRef) {\n            this.closePanel();\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    /** Given a value, returns the string that should be shown within the input. */\n    _getDisplayValue(value) {\n        const autocomplete = this.autocomplete;\n        return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n    }\n    _assignOptionValue(value) {\n        const toDisplay = this._getDisplayValue(value);\n        if (value == null) {\n            this._clearPreviousSelectedOption(null, false);\n        }\n        // Simply falling back to an empty string if the display value is falsy does not work properly.\n        // The display value can also be the number zero and shouldn't fall back to an empty string.\n        this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n        // If it's used within a `MatFormField`, we should set it through the property so it can go\n        // through change detection.\n        if (this._formField) {\n            this._formField._control.value = value;\n        }\n        else {\n            this._element.nativeElement.value = value;\n        }\n        this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n        const panel = this.autocomplete;\n        const toSelect = event ? event.source : this._pendingAutoselectedOption;\n        if (toSelect) {\n            this._clearPreviousSelectedOption(toSelect);\n            this._assignOptionValue(toSelect.value);\n            // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n            // gets reset while the panel is still animating which looks glitchy. It'll likely break\n            // some tests to change it at this point.\n            this._onChange(toSelect.value);\n            panel._emitSelectEvent(toSelect);\n            this._element.nativeElement.focus();\n        }\n        else if (panel.requireSelection &&\n            this._element.nativeElement.value !== this._valueOnAttach) {\n            this._clearPreviousSelectedOption(null);\n            this._assignOptionValue(null);\n            this._onChange(null);\n        }\n        this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip, emitEvent) {\n        // Null checks are necessary here, because the autocomplete\n        // or its options may not have been assigned yet.\n        this.autocomplete?.options?.forEach(option => {\n            if (option !== skip && option.selected) {\n                option.deselect(emitEvent);\n            }\n        });\n    }\n    _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n        this._attachOverlay(valueOnAttach);\n        this._floatLabel();\n        // Add aria-owns attribute when the autocomplete becomes visible.\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n    }\n    _attachOverlay(valueOnAttach) {\n        if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatAutocompleteMissingPanelError();\n        }\n        let overlayRef = this._overlayRef;\n        if (!overlayRef) {\n            this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n                id: this._formField?.getLabelId(),\n            });\n            overlayRef = createOverlayRef(this._injector, this._getOverlayConfig());\n            this._overlayRef = overlayRef;\n            this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n                if (this.panelOpen && overlayRef) {\n                    overlayRef.updateSize({ width: this._getPanelWidth() });\n                }\n            });\n            // Subscribe to the breakpoint events stream to detect when screen is in\n            // handsetLandscape.\n            this._handsetLandscapeSubscription = this._breakpointObserver\n                .observe(Breakpoints.HandsetLandscape)\n                .subscribe(result => {\n                const isHandsetLandscape = result.matches;\n                // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n                // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n                if (isHandsetLandscape) {\n                    this._positionStrategy\n                        .withFlexibleDimensions(true)\n                        .withGrowAfterOpen(true)\n                        .withViewportMargin(8);\n                }\n                else {\n                    this._positionStrategy\n                        .withFlexibleDimensions(false)\n                        .withGrowAfterOpen(false)\n                        .withViewportMargin(0);\n                }\n            });\n        }\n        else {\n            // Update the trigger, panel width and direction, in case anything has changed.\n            this._positionStrategy.setOrigin(this._getConnectedElement());\n            overlayRef.updateSize({ width: this._getPanelWidth() });\n        }\n        if (overlayRef && !overlayRef.hasAttached()) {\n            overlayRef.attach(this._portal);\n            this._valueOnAttach = valueOnAttach;\n            this._valueOnLastKeydown = null;\n            this._closingActionsSubscription = this._subscribeToClosingActions();\n        }\n        const wasOpen = this.panelOpen;\n        this.autocomplete._isOpen = this._overlayAttached = true;\n        this.autocomplete._latestOpeningTrigger = this;\n        this.autocomplete._setColor(this._formField?.color);\n        this._updatePanelState();\n        this._applyModalPanelOwnership();\n        // We need to do an extra `panelOpen` check in here, because the\n        // autocomplete won't be shown if there are no options.\n        if (this.panelOpen && wasOpen !== this.panelOpen) {\n            this._emitOpened();\n        }\n    }\n    /** Handles keyboard events coming from the overlay panel. */\n    _handlePanelKeydown = (event) => {\n        // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n        // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n        if ((event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n            (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))) {\n            // If the user had typed something in before we autoselected an option, and they decided\n            // to cancel the selection, restore the input value to the one they had typed in.\n            if (this._pendingAutoselectedOption) {\n                this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n                this._pendingAutoselectedOption = null;\n            }\n            this._closeKeyEventStream.next();\n            this._resetActiveItem();\n            // We need to stop propagation, otherwise the event will eventually\n            // reach the input itself and cause the overlay to be reopened.\n            event.stopPropagation();\n            event.preventDefault();\n        }\n    };\n    /** Updates the panel's visibility state and any trigger state tied to id. */\n    _updatePanelState() {\n        this.autocomplete._setVisibility();\n        // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n        // because the act of subscribing will prevent events from reaching other overlays and\n        // we don't want to block the events if there are no options.\n        if (this.panelOpen) {\n            const overlayRef = this._overlayRef;\n            if (!this._keydownSubscription) {\n                // Use the `keydownEvents` in order to take advantage of\n                // the overlay event targeting provided by the CDK overlay.\n                this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n            }\n            if (!this._outsideClickSubscription) {\n                // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n                // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n                // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n                this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n            }\n        }\n        else {\n            this._keydownSubscription?.unsubscribe();\n            this._outsideClickSubscription?.unsubscribe();\n            this._keydownSubscription = this._outsideClickSubscription = null;\n        }\n    }\n    _getOverlayConfig() {\n        return new OverlayConfig({\n            positionStrategy: this._getOverlayPosition(),\n            scrollStrategy: this._scrollStrategy(),\n            width: this._getPanelWidth(),\n            direction: this._dir ?? undefined,\n            hasBackdrop: this._defaults?.hasBackdrop,\n            backdropClass: this._defaults?.backdropClass,\n            panelClass: this._overlayPanelClass,\n            disableAnimations: this._animationsDisabled,\n        });\n    }\n    _getOverlayPosition() {\n        // Set default Overlay Position\n        const strategy = createFlexibleConnectedPositionStrategy(this._injector, this._getConnectedElement())\n            .withFlexibleDimensions(false)\n            .withPush(false);\n        this._setStrategyPositions(strategy);\n        this._positionStrategy = strategy;\n        return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n        // Note that we provide horizontal fallback positions, even though by default the dropdown\n        // width matches the input, because consumers can override the width. See #18854.\n        const belowPositions = [\n            { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n            { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n        ];\n        // The overlay edge connected to the trigger should have squared corners, while\n        // the opposite end has rounded corners. We apply a CSS class to swap the\n        // border-radius based on the overlay position.\n        const panelClass = this._aboveClass;\n        const abovePositions = [\n            { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass },\n            { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass },\n        ];\n        let positions;\n        if (this.position === 'above') {\n            positions = abovePositions;\n        }\n        else if (this.position === 'below') {\n            positions = belowPositions;\n        }\n        else {\n            positions = [...belowPositions, ...abovePositions];\n        }\n        positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n        if (this.connectedTo) {\n            return this.connectedTo.elementRef;\n        }\n        return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n        return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n        return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n     * option.\n     *\n     * If the consumer opted-in to automatically activatating the first option, activate the first\n     * *enabled* option.\n     */\n    _resetActiveItem() {\n        const autocomplete = this.autocomplete;\n        if (autocomplete.autoActiveFirstOption) {\n            // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n            // because it activates the first option that passes the skip predicate, rather than the\n            // first *enabled* option.\n            let firstEnabledOptionIndex = -1;\n            for (let index = 0; index < autocomplete.options.length; index++) {\n                const option = autocomplete.options.get(index);\n                if (!option.disabled) {\n                    firstEnabledOptionIndex = index;\n                    break;\n                }\n            }\n            autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n        }\n        else {\n            autocomplete._keyManager.setActiveItem(-1);\n        }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n        const element = this._element.nativeElement;\n        return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n        // Given that we are not actually focusing active options, we must manually adjust scroll\n        // to reveal options below the fold. First, we find the offset of the option from the top\n        // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n        // the panel height + the option height, so the active option will be just visible at the\n        // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n        // will become the offset. If that offset is visible within the panel already, the scrollTop is\n        // not adjusted.\n        const autocomplete = this.autocomplete;\n        const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            autocomplete._setScrollTop(0);\n        }\n        else if (autocomplete.panel) {\n            const option = autocomplete.options.toArray()[index];\n            if (option) {\n                const element = option._getHostElement();\n                const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n                autocomplete._setScrollTop(newScrollPosition);\n            }\n        }\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = this.autocomplete.id;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the references to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n            this._trackedModal = null;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatAutocompleteTrigger, isStandalone: true, selector: \"input[matAutocomplete], textarea[matAutocomplete]\", inputs: { autocomplete: [\"matAutocomplete\", \"autocomplete\"], position: [\"matAutocompletePosition\", \"position\"], connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"], autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"], autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\", booleanAttribute] }, host: { listeners: { \"focusin\": \"_handleFocus()\", \"blur\": \"_onTouched()\", \"input\": \"_handleInput($event)\", \"keydown\": \"_handleKeydown($event)\", \"click\": \"_handleClick()\" }, properties: { \"attr.autocomplete\": \"autocompleteAttribute\", \"attr.role\": \"autocompleteDisabled ? null : \\\"combobox\\\"\", \"attr.aria-autocomplete\": \"autocompleteDisabled ? null : \\\"list\\\"\", \"attr.aria-activedescendant\": \"(panelOpen && activeOption) ? activeOption.id : null\", \"attr.aria-expanded\": \"autocompleteDisabled ? null : panelOpen.toString()\", \"attr.aria-controls\": \"(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id\", \"attr.aria-haspopup\": \"autocompleteDisabled ? null : \\\"listbox\\\"\" }, classAttribute: \"mat-mdc-autocomplete-trigger\" }, providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR], exportAs: [\"matAutocompleteTrigger\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n                    host: {\n                        'class': 'mat-mdc-autocomplete-trigger',\n                        '[attr.autocomplete]': 'autocompleteAttribute',\n                        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n                        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n                        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n                        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n                        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n                        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n                        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n                        // a little earlier. This avoids issues where IE delays the focusing of the input.\n                        '(focusin)': '_handleFocus()',\n                        '(blur)': '_onTouched()',\n                        '(input)': '_handleInput($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(click)': '_handleClick()',\n                    },\n                    exportAs: 'matAutocompleteTrigger',\n                    providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n                }]\n        }], ctorParameters: () => [], propDecorators: { autocomplete: [{\n                type: Input,\n                args: ['matAutocomplete']\n            }], position: [{\n                type: Input,\n                args: ['matAutocompletePosition']\n            }], connectedTo: [{\n                type: Input,\n                args: ['matAutocompleteConnectedTo']\n            }], autocompleteAttribute: [{\n                type: Input,\n                args: ['autocomplete']\n            }], autocompleteDisabled: [{\n                type: Input,\n                args: [{ alias: 'matAutocompleteDisabled', transform: booleanAttribute }]\n            }] } });\n\nclass MatAutocompleteModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteModule, imports: [OverlayModule,\n            MatOptionModule,\n            MatCommonModule,\n            MatAutocomplete,\n            MatAutocompleteTrigger,\n            MatAutocompleteOrigin], exports: [CdkScrollableModule,\n            MatAutocomplete,\n            MatOptionModule,\n            MatCommonModule,\n            MatAutocompleteTrigger,\n            MatAutocompleteOrigin] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteModule, providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [OverlayModule,\n            MatOptionModule,\n            MatCommonModule, CdkScrollableModule,\n            MatOptionModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatAutocompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatAutocomplete,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatAutocomplete,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                    ],\n                    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption, getMatAutocompleteMissingPanelError };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,wBAAwB,QAAQ,uBAAuB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAiPjHE,EAAE,CAAAC,cAAA,eAC2+D,CAAC;IAD9+DD,EAAE,CAAAE,YAAA,EAC0gE,CAAC;IAD7gEF,EAAE,CAAAG,YAAA,CACohE,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,cAAA,GAAAL,GAAA,CAAAM,EAAA;IAAA,MAAAC,MAAA,GADvhEN,EAAE,CAAAO,aAAA;IAAFP,EAAE,CAAAQ,UAAA,CAAAF,MAAA,CAAAG,UAC8hD,CAAC;IADjiDT,EAAE,CAAAU,WAAA,iCAAAJ,MAAA,CAAAK,SACslD,CAAC,iCAAAL,MAAA,CAAAK,SAAuD,CAAC,+CAAAL,MAAA,CAAAM,mBAA+E,CAAC,gBAAAN,MAAA,CAAAO,MAAA,cAAiD,CAAC,eAAAP,MAAA,CAAAO,MAAA,aAA+C,CAAC,aAAAP,MAAA,CAAAO,MAAA,WAA2C,CAAC;IAD/2Db,EAAE,CAAAc,UAAA,OAAAR,MAAA,CAAAD,EACkgD,CAAC;IADrgDL,EAAE,CAAAe,WAAA,eAAAT,MAAA,CAAAU,SAAA,6BAAAV,MAAA,CAAAW,uBAAA,CAAAb,cAAA;EAAA;AAAA;AAhP/F,SAASc,CAAC,IAAIC,WAAW,QAAQ,uBAAuB;AACxD,OAAO,KAAKnB,EAAE,MAAM,eAAe;AACnC,SAASoB,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC1V,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,SAASC,8BAA8B,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,uCAAuC,EAAEC,aAAa,QAAQ,sBAAsB;AAC9J,SAASC,YAAY,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,mBAAmB,QAAQ,mBAAmB;AACzH,SAASC,QAAQ,EAAEC,iCAAiC,EAAEC,eAAe,QAAQ,uBAAuB;AACpG,SAASC,YAAY,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAC1E,SAASvE,CAAC,IAAIqB,mBAAmB,QAAQ,0BAA0B;AACnE,SAASmD,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AAChG,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AACpF,SAASC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AAC/D,SAASlG,CAAC,IAAImG,eAAe,QAAQ,sBAAsB;AAC3D,SAASnG,CAAC,IAAIoG,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,gCAAgC;AACvC,OAAO,kCAAkC;AACzC,OAAO,iBAAiB;AACxB,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,uCAAuC;;AAE9C;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,MAAM;EACNC,MAAM;EACNC,WAAWA,CACX;EACAF,MAAM,EACN;EACAC,MAAM,EAAE;IACJ,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA,MAAME,gCAAgC,gBAAG,IAAItE,cAAc,CAAC,kCAAkC,EAAE;EAC5FuE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,wCAAwCA,CAAA,EAAG;EAChD,OAAO;IACHC,qBAAqB,EAAE,KAAK;IAC5BC,sBAAsB,EAAE,KAAK;IAC7BC,4BAA4B,EAAE,KAAK;IACnCC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACjB,CAAC;AACL;AACA;AAAA,IACMC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBC,kBAAkB,GAAG/E,MAAM,CAACC,iBAAiB,CAAC;IAC9C+E,WAAW,GAAGhF,MAAM,CAACE,UAAU,CAAC;IAChC+E,SAAS,GAAGjF,MAAM,CAACqE,gCAAgC,CAAC;IACpD9E,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C2F,oBAAoB,GAAG9C,YAAY,CAAC+C,KAAK;IACzC;IACAC,WAAW;IACX;IACA9F,SAAS,GAAG,KAAK;IACjB;IACA,IAAI+F,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAAChG,SAAS;IACzC;IACAgG,OAAO,GAAG,KAAK;IACf;IACAC,qBAAqB;IACrB;IACAC,SAASA,CAACC,KAAK,EAAE;MACb,IAAI,CAACjG,MAAM,GAAGiG,KAAK;MACnB,IAAI,CAACV,kBAAkB,CAACW,YAAY,CAAC,CAAC;IAC1C;IACA;IACAlG,MAAM;IACN;IACA;IACA;IACA;IACAmG,QAAQ;IACR;IACAC,KAAK;IACL;IACAC,OAAO;IACP;IACAC,YAAY;IACZ;IACAnG,SAAS;IACT;IACAoG,cAAc;IACd;IACAC,WAAW,GAAG,IAAI;IAClB;AACJ;AACA;AACA;IACIvB,qBAAqB;IACrB;IACAC,sBAAsB;IACtB;AACJ;AACA;AACA;AACA;AACA;IACIE,gBAAgB;IAChB;AACJ;AACA;AACA;IACIqB,UAAU;IACV;IACAC,aAAa;IACb;IACAC,cAAc,GAAG,IAAIhG,YAAY,CAAC,CAAC;IACnC;IACAiG,MAAM,GAAG,IAAIjG,YAAY,CAAC,CAAC;IAC3B;IACAkG,MAAM,GAAG,IAAIlG,YAAY,CAAC,CAAC;IAC3B;IACAmG,eAAe,GAAG,IAAInG,YAAY,CAAC,CAAC;IACpC;AACJ;AACA;AACA;IACI,IAAIoG,SAASA,CAACd,KAAK,EAAE;MACjB,IAAI,CAACrG,UAAU,GAAGqG,KAAK;MACvB,IAAI,CAACT,WAAW,CAACwB,aAAa,CAACC,SAAS,GAAG,EAAE;IACjD;IACArH,UAAU;IACV;IACA,IAAIuF,4BAA4BA,CAAA,EAAG;MAC/B,OAAO,IAAI,CAAC+B,6BAA6B;IAC7C;IACA,IAAI/B,4BAA4BA,CAACc,KAAK,EAAE;MACpC,IAAI,CAACiB,6BAA6B,GAAGjB,KAAK;MAC1C,IAAI,CAACkB,qBAAqB,CAAC,CAAC;IAChC;IACAD,6BAA6B;IAC7B;IACAC,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACd,OAAO,EAAE;QACd,KAAK,MAAM1B,MAAM,IAAI,IAAI,CAAC0B,OAAO,EAAE;UAC/B1B,MAAM,CAACY,kBAAkB,CAACW,YAAY,CAAC,CAAC;QAC5C;MACJ;IACJ;IACA;IACA1G,EAAE,GAAGgB,MAAM,CAAC6B,YAAY,CAAC,CAAC+E,KAAK,CAAC,mBAAmB,CAAC;IACpD;AACJ;AACA;AACA;IACIC,WAAW;IACXzC,WAAWA,CAAA,EAAG;MACV,MAAM0C,QAAQ,GAAG9G,MAAM,CAACiC,QAAQ,CAAC;MACjC;MACA;MACA;MACA;MACA,IAAI,CAAC4E,WAAW,GAAGC,QAAQ,EAAEC,MAAM,IAAI,KAAK;MAC5C,IAAI,CAACtC,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAACQ,SAAS,CAACR,qBAAqB;MACnE,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAACO,SAAS,CAACP,sBAAsB;MACrE,IAAI,CAACE,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAACK,SAAS,CAACL,gBAAgB;MACzD,IAAI,CAAC8B,6BAA6B,GAAG,IAAI,CAACzB,SAAS,CAACN,4BAA4B,IAAI,KAAK;IAC7F;IACAqC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC5B,WAAW,GAAG,IAAItD,0BAA0B,CAAC,IAAI,CAAC+D,OAAO,CAAC,CAC1DoB,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,IAAI,CAACC,cAAc,CAAC;MACvC,IAAI,CAACjC,oBAAoB,GAAG,IAAI,CAACE,WAAW,CAACgC,MAAM,CAACC,SAAS,CAACC,KAAK,IAAI;QACnE,IAAI,IAAI,CAACjC,MAAM,EAAE;UACb,IAAI,CAACiB,eAAe,CAACiB,IAAI,CAAC;YAAErD,MAAM,EAAE,IAAI;YAAEC,MAAM,EAAE,IAAI,CAAC0B,OAAO,CAAC2B,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC,IAAI;UAAK,CAAC,CAAC;QAC9F;MACJ,CAAC,CAAC;MACF;MACA,IAAI,CAACG,cAAc,CAAC,CAAC;IACzB;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACtC,WAAW,EAAEuC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACzC,oBAAoB,CAAC0C,WAAW,CAAC,CAAC;IAC3C;IACA;AACJ;AACA;AACA;IACIC,aAAaA,CAACC,SAAS,EAAE;MACrB,IAAI,IAAI,CAAClC,KAAK,EAAE;QACZ,IAAI,CAACA,KAAK,CAACY,aAAa,CAACsB,SAAS,GAAGA,SAAS;MAClD;IACJ;IACA;IACAC,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACnC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACY,aAAa,CAACsB,SAAS,GAAG,CAAC;IAC9D;IACA;IACAL,cAAcA,CAAA,EAAG;MACb,IAAI,CAACnI,SAAS,GAAG,CAAC,CAAC,IAAI,CAACuG,OAAO,EAAEmC,MAAM;MACvC,IAAI,CAACjD,kBAAkB,CAACW,YAAY,CAAC,CAAC;IAC1C;IACA;IACAuC,gBAAgBA,CAAC9D,MAAM,EAAE;MACrB,MAAM+D,KAAK,GAAG,IAAIjE,4BAA4B,CAAC,IAAI,EAAEE,MAAM,CAAC;MAC5D,IAAI,CAACgC,cAAc,CAACoB,IAAI,CAACW,KAAK,CAAC;IACnC;IACA;IACAtI,uBAAuBA,CAACuI,OAAO,EAAE;MAC7B,IAAI,IAAI,CAACxI,SAAS,EAAE;QAChB,OAAO,IAAI;MACf;MACA,MAAMyI,eAAe,GAAGD,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;MACpD,OAAO,IAAI,CAACpC,cAAc,GAAGqC,eAAe,GAAG,IAAI,CAACrC,cAAc,GAAGoC,OAAO;IAChF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAhB,cAAcA,CAAA,EAAG;MACb,OAAO,KAAK;IAChB;IACA,OAAOkB,IAAI,YAAAC,wBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFzD,eAAe;IAAA;IAClH,OAAO0D,IAAI,kBAD8E7J,EAAE,CAAA8J,iBAAA;MAAAC,IAAA,EACJ5D,eAAe;MAAA6D,SAAA;MAAAC,cAAA,WAAAC,+BAAApK,EAAA,EAAAC,GAAA,EAAAoK,QAAA;QAAA,IAAArK,EAAA;UADbE,EAAE,CAAAoK,cAAA,CAAAD,QAAA,EACm/BjL,SAAS;UAD9/Bc,EAAE,CAAAoK,cAAA,CAAAD,QAAA,EAC8jC/K,YAAY;QAAA;QAAA,IAAAU,EAAA;UAAA,IAAAuK,EAAA;UAD5kCrK,EAAE,CAAAsK,cAAA,CAAAD,EAAA,GAAFrK,EAAE,CAAAuK,WAAA,QAAAxK,GAAA,CAAAmH,OAAA,GAAAmD,EAAA;UAAFrK,EAAE,CAAAsK,cAAA,CAAAD,EAAA,GAAFrK,EAAE,CAAAuK,WAAA,QAAAxK,GAAA,CAAAoH,YAAA,GAAAkD,EAAA;QAAA;MAAA;MAAAG,SAAA,WAAAC,sBAAA3K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFE,EAAE,CAAA0K,WAAA,CACoqChJ,WAAW;UADjrC1B,EAAE,CAAA0K,WAAA,CAAA/K,GAAA;QAAA;QAAA,IAAAG,EAAA;UAAA,IAAAuK,EAAA;UAAFrK,EAAE,CAAAsK,cAAA,CAAAD,EAAA,GAAFrK,EAAE,CAAAuK,WAAA,QAAAxK,GAAA,CAAAiH,QAAA,GAAAqD,EAAA,CAAAM,KAAA;UAAF3K,EAAE,CAAAsK,cAAA,CAAAD,EAAA,GAAFrK,EAAE,CAAAuK,WAAA,QAAAxK,GAAA,CAAAkH,KAAA,GAAAoD,EAAA,CAAAM,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,MAAA;QAAA7J,SAAA;QAAAoG,cAAA;QAAAC,WAAA;QAAAvB,qBAAA,wDAC8QrE,gBAAgB;QAAAsE,sBAAA,0DAAgFtE,gBAAgB;QAAAwE,gBAAA,8CAA8DxE,gBAAgB;QAAA6F,UAAA;QAAAC,aAAA,wCAA+E9F,gBAAgB;QAAAmG,SAAA;QAAA5B,4BAAA,sEAAqIvE,gBAAgB;MAAA;MAAAqJ,OAAA;QAAAtD,cAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,eAAA;MAAA;MAAAoD,QAAA;MAAAC,QAAA,GADlsBhL,EAAE,CAAAiL,kBAAA,CACy3B,CAAC;QAAEC,OAAO,EAAElM,2BAA2B;QAAEmM,WAAW,EAAEhF;MAAgB,CAAC,CAAC;MAAAiF,kBAAA,EAAAxL,GAAA;MAAAyL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAvE,QAAA,WAAAwE,yBAAA1L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADn8BE,EAAE,CAAAyL,eAAA;UAAFzL,EAAE,CAAA0L,UAAA,IAAA7L,sCAAA,sBACi4C,CAAC;QAAA;MAAA;MAAA8L,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACj+C;EAAC,OArLK1F,eAAe;AAAA;AAsLrB;EAAA,QAAA2F,SAAA,oBAAAA,SAAA;AAAA;;AAuDA;AACA;AACA;AACA;AAHA,IAIMC,qBAAqB;EAA3B,MAAMA,qBAAqB,CAAC;IACxBC,UAAU,GAAG3K,MAAM,CAACE,UAAU,CAAC;IAC/BkE,WAAWA,CAAA,EAAG,CAAE;IAChB,OAAOiE,IAAI,YAAAuC,8BAAArC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFmC,qBAAqB;IAAA;IACxH,OAAOG,IAAI,kBAlE8ElM,EAAE,CAAAmM,iBAAA;MAAApC,IAAA,EAkEJgC,qBAAqB;MAAA/B,SAAA;MAAAe,QAAA;IAAA;EAChH;EAAC,OALKgB,qBAAqB;AAAA;AAM3B;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;AACA;AACA,MAAMM,+BAA+B,GAAG;EACpClB,OAAO,EAAExG,iBAAiB;EAC1ByG,WAAW,eAAEhJ,UAAU,CAAC,MAAMkK,sBAAsB,CAAC;EACrDC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAA,EAAG;EAC3C,OAAOC,KAAK,CAAC,kEAAkE,GAC3E,4EAA4E,GAC5E,iEAAiE,CAAC;AAC1E;AACA;AACA,MAAMC,gCAAgC,gBAAG,IAAIrL,cAAc,CAAC,kCAAkC,EAAE;EAC5FuE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM8G,QAAQ,GAAGrL,MAAM,CAACe,QAAQ,CAAC;IACjC,OAAO,MAAMS,8BAA8B,CAAC6J,QAAQ,CAAC;EACzD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,wCAAwCA,CAACC,QAAQ,EAAE;EACxD,MAAMF,QAAQ,GAAGrL,MAAM,CAACe,QAAQ,CAAC;EACjC,OAAO,MAAMS,8BAA8B,CAAC6J,QAAQ,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iDAAiD,GAAG;EACtD3B,OAAO,EAAEuB,gCAAgC;EACzCK,IAAI,EAAE,EAAE;EACRC,UAAU,EAAEJ;AAChB,CAAC;AACD;AAAA,IACMN,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzBW,oBAAoB,GAAG3L,MAAM,CAACgB,mBAAmB,CAAC;IAClD4K,QAAQ,GAAG5L,MAAM,CAACE,UAAU,CAAC;IAC7B2L,SAAS,GAAG7L,MAAM,CAACe,QAAQ,CAAC;IAC5B+K,iBAAiB,GAAG9L,MAAM,CAACiB,gBAAgB,CAAC;IAC5C8K,KAAK,GAAG/L,MAAM,CAACkB,MAAM,CAAC;IACtB6D,kBAAkB,GAAG/E,MAAM,CAACC,iBAAiB,CAAC;IAC9C+L,IAAI,GAAGhM,MAAM,CAAC0C,cAAc,EAAE;MAAEuJ,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjDC,UAAU,GAAGlM,MAAM,CAAC8D,cAAc,EAAE;MAAEmI,QAAQ,EAAE,IAAI;MAAEE,IAAI,EAAE;IAAK,CAAC,CAAC;IACnEC,cAAc,GAAGpM,MAAM,CAACsB,aAAa,CAAC;IACtC+K,eAAe,GAAGrM,MAAM,CAACoL,gCAAgC,CAAC;IAC1DkB,SAAS,GAAGtM,MAAM,CAACmB,SAAS,CAAC;IAC7B5B,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;IAC3C0F,SAAS,GAAGjF,MAAM,CAACqE,gCAAgC,EAAE;MAAE4H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxEM,WAAW;IACXC,OAAO;IACPC,mBAAmB,GAAG,KAAK;IAC3BC,YAAY,GAAG,IAAIrK,OAAO,CAAC,CAAC;IAC5BsK,oBAAoB;IACpBC,yBAAyB;IACzBC,kBAAkB;IAClB;IACAC,cAAc;IACd;IACAC,cAAc;IACd;IACAC,mBAAmB;IACnB;IACAC,iBAAiB;IACjB;IACAC,sBAAsB,GAAG,KAAK;IAC9B;IACAC,2BAA2B;IAC3B;IACAC,qBAAqB,GAAGhL,YAAY,CAAC+C,KAAK;IAC1C;IACAkI,mBAAmB,GAAGrN,MAAM,CAACiD,kBAAkB,CAAC;IAChDqK,6BAA6B,GAAGlL,YAAY,CAAC+C,KAAK;IAClD;AACJ;AACA;AACA;AACA;IACIoI,mBAAmB,GAAG,IAAI;IAC1B;IACAC,yBAAyB;IACzB;AACJ;AACA;AACA;IACIC,0BAA0B;IAC1B;IACAC,oBAAoB,GAAG,IAAIrL,OAAO,CAAC,CAAC;IACpC;IACAsL,kBAAkB,GAAGvK,WAAW,CAAC,IAAI,CAAC6B,SAAS,EAAE2I,iBAAiB,IAAI,EAAE,CAAC;IACzE;AACJ;AACA;AACA;IACIC,kBAAkB,GAAGA,CAAA,KAAM;MACvB;MACA;MACA;MACA,IAAI,CAACN,mBAAmB,GAAG,IAAI,CAACO,SAAS,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC;IAClE,CAAC;IACD;IACAC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;IACrB;IACAC,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB;IACAC,YAAY;IACZ;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,QAAQ,GAAG,MAAM;IACjB;AACJ;AACA;AACA;IACIC,WAAW;IACX;AACJ;AACA;AACA;IACIC,qBAAqB,GAAG,KAAK;IAC7B;AACJ;AACA;AACA;IACIC,oBAAoB;IACpBlK,WAAWA,CAAA,EAAG,CAAE;IAChB;IACAmK,WAAW,GAAG,kCAAkC;IAChDC,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC9B,YAAY,CAAC+B,IAAI,CAAC,CAAC;MACxB,IAAI,CAAC/B,YAAY,CAACgC,QAAQ,CAAC,CAAC;MAC5B,IAAI,CAAC7B,kBAAkB,GAAG,IAAI,CAACP,SAAS,CAACqC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAACd,kBAAkB,CAAC;IAC9F;IACAe,WAAWA,CAACC,OAAO,EAAE;MACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC5B,iBAAiB,EAAE;QAC/C,IAAI,CAAC6B,qBAAqB,CAAC,IAAI,CAAC7B,iBAAiB,CAAC;QAClD,IAAI,IAAI,CAACa,SAAS,EAAE;UAChB,IAAI,CAACvB,WAAW,CAACwC,cAAc,CAAC,CAAC;QACrC;MACJ;IACJ;IACArH,WAAWA,CAAA,EAAG;MACV,IAAI,CAACmF,kBAAkB,GAAG,CAAC;MAC3B,IAAI,CAACS,6BAA6B,CAAC1F,WAAW,CAAC,CAAC;MAChD,IAAI,CAACwF,qBAAqB,CAACxF,WAAW,CAAC,CAAC;MACxC,IAAI,CAAC6E,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACuC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACtB,oBAAoB,CAACgB,QAAQ,CAAC,CAAC;MACpC,IAAI,CAACO,eAAe,CAAC,CAAC;IAC1B;IACA;IACA,IAAInB,SAASA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACoB,gBAAgB,IAAI,IAAI,CAAChB,YAAY,CAAC5O,SAAS;IAC/D;IACA4P,gBAAgB,GAAG,KAAK;IACxB;IACAC,SAASA,CAAA,EAAG;MACR,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACAC,UAAUA,CAAA,EAAG;MACT,IAAI,CAACC,WAAW,CAAC,CAAC;MAClB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;QACxB;MACJ;MACA,IAAI,IAAI,CAACpB,SAAS,EAAE;QAChB;QACA;QACA;QACA;QACA,IAAI,CAAC/B,KAAK,CAACwD,GAAG,CAAC,MAAM;UACjB,IAAI,CAACrB,YAAY,CAAC7H,MAAM,CAACkB,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;MACN;MACA;MACA;MACA,IAAI,IAAI,CAAC2G,YAAY,CAAC3I,qBAAqB,KAAK,IAAI,EAAE;QAClD,IAAI,CAAC2I,YAAY,CAAC5I,OAAO,GAAG,KAAK;QACjC,IAAI,CAAC4I,YAAY,CAAC3I,qBAAqB,GAAG,IAAI;MAClD;MACA,IAAI,CAAC2J,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACzB,0BAA0B,GAAG,IAAI;MACtC,IAAI,IAAI,CAAClB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACiD,WAAW,CAAC,CAAC,EAAE;QACpD,IAAI,CAACjD,WAAW,CAACkD,MAAM,CAAC,CAAC;QACzB,IAAI,CAACtC,2BAA2B,CAACvF,WAAW,CAAC,CAAC;MAClD;MACA,IAAI,CAAC8H,iBAAiB,CAAC,CAAC;MACxB;MACA;MACA,IAAI,CAAC,IAAI,CAACjD,mBAAmB,EAAE;QAC3B;QACA;QACA;QACA;QACA,IAAI,CAAC1H,kBAAkB,CAAC4K,aAAa,CAAC,CAAC;MAC3C;MACA;MACA,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB7N,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC1B,YAAY,CAAClP,EAAE,CAAC;MACjF;IACJ;IACA;AACJ;AACA;AACA;IACI+P,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACG,gBAAgB,EAAE;QACvB,IAAI,CAAC3C,WAAW,CAACwC,cAAc,CAAC,CAAC;MACrC;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAIc,mBAAmBA,CAAA,EAAG;MACtB,OAAOvN,KAAK,CAAC,IAAI,CAACwN,gBAAgB,EAAE,IAAI,CAAC5B,YAAY,CAAC9I,WAAW,CAAC2K,MAAM,CAACC,IAAI,CAAC1M,MAAM,CAAC,MAAM,IAAI,CAAC4L,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACxB,oBAAoB,EAAE,IAAI,CAACuC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC1D,WAAW,GACxL,IAAI,CAACA,WAAW,CAAC2D,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC1M,MAAM,CAAC,MAAM,IAAI,CAAC4L,gBAAgB,CAAC,CAAC,GACxE3M,EAAE,CAAC,CAAC,CAAC,CAACyN,IAAI;MAChB;MACAzM,GAAG,CAAC2E,KAAK,IAAKA,KAAK,YAAYjK,wBAAwB,GAAGiK,KAAK,GAAG,IAAK,CAAC,CAAC;IAC7E;IACA;IACA4H,gBAAgB,GAAGtN,KAAK,CAAC,MAAM;MAC3B,MAAMqD,OAAO,GAAG,IAAI,CAACqI,YAAY,GAAG,IAAI,CAACA,YAAY,CAACrI,OAAO,GAAG,IAAI;MACpE,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACgJ,OAAO,CAACmB,IAAI,CAACxM,SAAS,CAACqC,OAAO,CAAC,EAAEpC,SAAS,CAAC,MAAMnB,KAAK,CAAC,GAAGuD,OAAO,CAACtC,GAAG,CAACY,MAAM,IAAIA,MAAM,CAACgM,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA;MACA;MACA,OAAO,IAAI,CAACzD,YAAY,CAACsD,IAAI,CAACvM,SAAS,CAAC,MAAM,IAAI,CAACqM,gBAAgB,CAAC,CAAC;IACzE,CAAC,CAAC;IACF;IACA,IAAIM,YAAYA,CAAA,EAAG;MACf,IAAI,IAAI,CAAClC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC9I,WAAW,EAAE;QACpD,OAAO,IAAI,CAAC8I,YAAY,CAAC9I,WAAW,CAACiL,UAAU;MACnD;MACA,OAAO,IAAI;IACf;IACA;IACAJ,sBAAsBA,CAAA,EAAG;MACrB,OAAO,IAAIxN,UAAU,CAAC6N,QAAQ,IAAI;QAC9B,MAAMC,QAAQ,GAAIrI,KAAK,IAAK;UACxB;UACA;UACA,MAAMsI,WAAW,GAAGrO,eAAe,CAAC+F,KAAK,CAAC;UAC1C,MAAMuI,SAAS,GAAG,IAAI,CAACvE,UAAU,GAC3B,IAAI,CAACA,UAAU,CAACwE,yBAAyB,CAAC,CAAC,CAAClK,aAAa,GACzD,IAAI;UACV,MAAMmK,YAAY,GAAG,IAAI,CAACvC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACzD,UAAU,CAACnE,aAAa,GAAG,IAAI;UACxF,IAAI,IAAI,CAAC0I,gBAAgB,IACrBsB,WAAW,KAAK,IAAI,CAAC5E,QAAQ,CAACpF,aAAa;UAC3C;UACA;UACA;UACA;UACA,CAAC,IAAI,CAACuH,SAAS,CAAC,CAAC,KAChB,CAAC0C,SAAS,IAAI,CAACA,SAAS,CAACG,QAAQ,CAACJ,WAAW,CAAC,CAAC,KAC/C,CAACG,YAAY,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,WAAW,CAAC,CAAC,IACtD,CAAC,CAAC,IAAI,CAACjE,WAAW,IAClB,CAAC,IAAI,CAACA,WAAW,CAACsE,cAAc,CAACD,QAAQ,CAACJ,WAAW,CAAC,EAAE;YACxDF,QAAQ,CAAC7B,IAAI,CAACvG,KAAK,CAAC;UACxB;QACJ,CAAC;QACD,MAAM4I,QAAQ,GAAG,CACb,IAAI,CAACxE,SAAS,CAACqC,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE4B,QAAQ,CAAC,EACpD,IAAI,CAACjE,SAAS,CAACqC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE4B,QAAQ,CAAC,EACvD,IAAI,CAACjE,SAAS,CAACqC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE4B,QAAQ,CAAC,CAC1D;QACD,OAAO,MAAM;UACTO,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;QAC1C,CAAC;MACL,CAAC,CAAC;IACN;IACA;IACAC,UAAUA,CAACxL,KAAK,EAAE;MACdyL,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAAC5L,KAAK,CAAC,CAAC;IACpE;IACA;IACA6L,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACvD,SAAS,GAAGuD,EAAE;IACvB;IACA;IACAC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAACtD,UAAU,GAAGsD,EAAE;IACxB;IACA;IACAE,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAAC9F,QAAQ,CAACpF,aAAa,CAACmL,QAAQ,GAAGD,UAAU;IACrD;IACAE,cAAcA,CAAC5T,CAAC,EAAE;MACd,MAAMkK,KAAK,GAAGlK,CAAC;MACf,MAAM6T,OAAO,GAAG3J,KAAK,CAAC2J,OAAO;MAC7B,MAAMC,WAAW,GAAGnP,cAAc,CAACuF,KAAK,CAAC;MACzC;MACA;MACA;MACA;MACA,IAAI2J,OAAO,KAAKjP,MAAM,IAAI,CAACkP,WAAW,EAAE;QACpC5J,KAAK,CAAC6J,cAAc,CAAC,CAAC;MAC1B;MACA,IAAI,CAAC/E,mBAAmB,GAAG,IAAI,CAACpB,QAAQ,CAACpF,aAAa,CAACf,KAAK;MAC5D,IAAI,IAAI,CAAC2K,YAAY,IAAIyB,OAAO,KAAKhP,KAAK,IAAI,IAAI,CAACiL,SAAS,IAAI,CAACgE,WAAW,EAAE;QAC1E,IAAI,CAAC1B,YAAY,CAAC4B,qBAAqB,CAAC,CAAC;QACzC,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACvB/J,KAAK,CAAC6J,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAAC7D,YAAY,EAAE;QACxB,MAAMgE,cAAc,GAAG,IAAI,CAAChE,YAAY,CAAC9I,WAAW,CAACiL,UAAU;QAC/D,MAAM8B,UAAU,GAAGN,OAAO,KAAK9O,QAAQ,IAAI8O,OAAO,KAAK7O,UAAU;QACjE,IAAI6O,OAAO,KAAK/O,GAAG,IAAKqP,UAAU,IAAI,CAACL,WAAW,IAAI,IAAI,CAAChE,SAAU,EAAE;UACnE,IAAI,CAACI,YAAY,CAAC9I,WAAW,CAACgN,SAAS,CAAClK,KAAK,CAAC;QAClD,CAAC,MACI,IAAIiK,UAAU,IAAI,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE;UACpC,IAAI,CAACjD,kBAAkB,CAAC,IAAI,CAACpC,mBAAmB,CAAC;QACrD;QACA,IAAImF,UAAU,IAAI,IAAI,CAACjE,YAAY,CAAC9I,WAAW,CAACiL,UAAU,KAAK6B,cAAc,EAAE;UAC3E,IAAI,CAACI,eAAe,CAAC,IAAI,CAACpE,YAAY,CAAC9I,WAAW,CAACmN,eAAe,IAAI,CAAC,CAAC;UACxE,IAAI,IAAI,CAACrE,YAAY,CAACxJ,sBAAsB,IAAI,IAAI,CAAC0L,YAAY,EAAE;YAC/D,IAAI,CAAC,IAAI,CAAC3C,0BAA0B,EAAE;cAClC,IAAI,CAACD,yBAAyB,GAAG,IAAI,CAACR,mBAAmB;YAC7D;YACA,IAAI,CAACS,0BAA0B,GAAG,IAAI,CAAC2C,YAAY;YACnD,IAAI,CAACiB,kBAAkB,CAAC,IAAI,CAACjB,YAAY,CAAC3K,KAAK,CAAC;UACpD;QACJ;MACJ;IACJ;IACA+M,YAAYA,CAACtK,KAAK,EAAE;MAChB,IAAIuK,MAAM,GAAGvK,KAAK,CAACuK,MAAM;MACzB,IAAIhN,KAAK,GAAGgN,MAAM,CAAChN,KAAK;MACxB;MACA,IAAIgN,MAAM,CAAC/J,IAAI,KAAK,QAAQ,EAAE;QAC1BjD,KAAK,GAAGA,KAAK,IAAI,EAAE,GAAG,IAAI,GAAGiN,UAAU,CAACjN,KAAK,CAAC;MAClD;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACqH,cAAc,KAAKrH,KAAK,EAAE;QAC/B,IAAI,CAACqH,cAAc,GAAGrH,KAAK;QAC3B,IAAI,CAACgI,0BAA0B,GAAG,IAAI;QACtC;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAACS,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACtJ,gBAAgB,EAAE;UAC3D,IAAI,CAACoJ,SAAS,CAACvI,KAAK,CAAC;QACzB;QACA,IAAI,CAACA,KAAK,EAAE;UACR,IAAI,CAACkN,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;QAClD,CAAC,MACI,IAAI,IAAI,CAAC7E,SAAS,IAAI,CAAC,IAAI,CAACI,YAAY,CAACtJ,gBAAgB,EAAE;UAC5D;UACA;UACA,MAAMgO,cAAc,GAAG,IAAI,CAAC1E,YAAY,CAACrI,OAAO,EAAEgN,IAAI,CAAC1O,MAAM,IAAIA,MAAM,CAAC2O,QAAQ,CAAC;UACjF,IAAIF,cAAc,EAAE;YAChB,MAAMG,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAACJ,cAAc,CAACnN,KAAK,CAAC;YAC3D,IAAIA,KAAK,KAAKsN,OAAO,EAAE;cACnBH,cAAc,CAACK,QAAQ,CAAC,KAAK,CAAC;YAClC;UACJ;QACJ;QACA,IAAI,IAAI,CAACZ,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACtE,SAAS,CAAC,CAAC,EAAE;UACrC;UACA;UACA;UACA;UACA;UACA,MAAMmF,aAAa,GAAG,IAAI,CAAClG,mBAAmB,IAAI,IAAI,CAACpB,QAAQ,CAACpF,aAAa,CAACf,KAAK;UACnF,IAAI,CAACuH,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACoC,kBAAkB,CAAC8D,aAAa,CAAC;QAC1C;MACJ;IACJ;IACAC,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAAC5F,mBAAmB,EAAE;QAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI;MACnC,CAAC,MACI,IAAI,IAAI,CAAC8E,QAAQ,CAAC,CAAC,EAAE;QACtB,IAAI,CAACvF,cAAc,GAAG,IAAI,CAAClB,QAAQ,CAACpF,aAAa,CAACf,KAAK;QACvD,IAAI,CAAC2N,cAAc,CAAC,IAAI,CAACtG,cAAc,CAAC;QACxC,IAAI,CAACuG,WAAW,CAAC,IAAI,CAAC;MAC1B;IACJ;IACAC,YAAYA,CAAA,EAAG;MACX,IAAI,IAAI,CAACjB,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAACvE,SAAS,EAAE;QACpC,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MAC7B;IACJ;IACA;IACArB,SAASA,CAAA,EAAG;MACR,OAAO7L,iCAAiC,CAAC,CAAC,KAAK,IAAI,CAAC0J,QAAQ,CAACpF,aAAa;IAC9E;IACA;AACJ;AACA;AACA;AACA;AACA;IACI6M,WAAWA,CAACE,aAAa,GAAG,KAAK,EAAE;MAC/B,IAAI,IAAI,CAACrH,UAAU,IAAI,IAAI,CAACA,UAAU,CAACsH,UAAU,KAAK,MAAM,EAAE;QAC1D,IAAID,aAAa,EAAE;UACf,IAAI,CAACrH,UAAU,CAACuH,oBAAoB,CAAC,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAACvH,UAAU,CAACsH,UAAU,GAAG,QAAQ;QACzC;QACA,IAAI,CAACtG,sBAAsB,GAAG,IAAI;MACtC;IACJ;IACA;IACAoC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACpC,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAAChB,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,CAACsH,UAAU,GAAG,MAAM;QACvC;QACA,IAAI,CAACtG,sBAAsB,GAAG,KAAK;MACvC;IACJ;IACA;AACJ;AACA;AACA;IACIwG,0BAA0BA,CAAA,EAAG;MACzB,MAAMC,aAAa,GAAG,IAAIlR,UAAU,CAACmR,UAAU,IAAI;QAC/CxS,eAAe,CAAC,MAAM;UAClBwS,UAAU,CAACnF,IAAI,CAAC,CAAC;QACrB,CAAC,EAAE;UAAEpD,QAAQ,EAAE,IAAI,CAACM;QAAqB,CAAC,CAAC;MAC/C,CAAC,CAAC;MACF,MAAMkI,aAAa,GAAG,IAAI,CAAC3F,YAAY,CAACrI,OAAO,EAAEgJ,OAAO,CAACmB,IAAI,CAACtM,GAAG,CAAC,MAAM,IAAI,CAACuJ,iBAAiB,CAAC6G,mBAAmB,CAAC,CAAC,CAAC;MACrH;MACA;MACAnQ,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIpB,EAAE,CAAC,CAAC;MACjB;MACA,OAAQD,KAAK,CAACqR,aAAa,EAAEE,aAAa,CAAC,CACtC7D,IAAI;MACT;MACA;MACAvM,SAAS,CAAC,MAAM,IAAI,CAACsI,KAAK,CAACwD,GAAG,CAAC,MAAM;QACjC;QACA;QACA;QACA,MAAMwE,OAAO,GAAG,IAAI,CAACjG,SAAS;QAC9B,IAAI,CAACmE,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACvC,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAAC3K,kBAAkB,CAAC4K,aAAa,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC7B,SAAS,EAAE;UAChB,IAAI,CAACvB,WAAW,CAACwC,cAAc,CAAC,CAAC;QACrC;QACA,IAAIgF,OAAO,KAAK,IAAI,CAACjG,SAAS,EAAE;UAC5B;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACA,SAAS,EAAE;YAChB,IAAI,CAACkG,WAAW,CAAC,CAAC;UACtB,CAAC,MACI;YACD,IAAI,CAAC9F,YAAY,CAAC7H,MAAM,CAACkB,IAAI,CAAC,CAAC;UACnC;QACJ;QACA,OAAO,IAAI,CAACsI,mBAAmB;MACnC,CAAC,CAAC,CAAC;MACH;MACAjM,IAAI,CAAC,CAAC,CAAC;MACH;MAAA,CACCyD,SAAS,CAACa,KAAK,IAAI,IAAI,CAAC+L,iBAAiB,CAAC/L,KAAK,CAAC,CAAC;IAC1D;IACA;AACJ;AACA;AACA;IACI8L,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC9F,YAAY,CAAC9H,MAAM,CAACmB,IAAI,CAAC,CAAC;IACnC;IACA;IACAyH,aAAaA,CAAA,EAAG;MACZ,IAAI,IAAI,CAACzC,WAAW,EAAE;QAClB,IAAI,CAAC8C,UAAU,CAAC,CAAC;QACjB,IAAI,CAAC9C,WAAW,CAAC2H,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC3H,WAAW,GAAG,IAAI;MAC3B;IACJ;IACA;IACAyG,gBAAgBA,CAACvN,KAAK,EAAE;MACpB,MAAMyI,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,OAAOA,YAAY,IAAIA,YAAY,CAAClI,WAAW,GAAGkI,YAAY,CAAClI,WAAW,CAACP,KAAK,CAAC,GAAGA,KAAK;IAC7F;IACA4L,kBAAkBA,CAAC5L,KAAK,EAAE;MACtB,MAAM0O,SAAS,GAAG,IAAI,CAACnB,gBAAgB,CAACvN,KAAK,CAAC;MAC9C,IAAIA,KAAK,IAAI,IAAI,EAAE;QACf,IAAI,CAACkN,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;MAClD;MACA;MACA;MACA,IAAI,CAACyB,uBAAuB,CAACD,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE,CAAC;IACpE;IACAC,uBAAuBA,CAAC3O,KAAK,EAAE;MAC3B;MACA;MACA,IAAI,IAAI,CAACyG,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACmI,QAAQ,CAAC5O,KAAK,GAAGA,KAAK;MAC1C,CAAC,MACI;QACD,IAAI,CAACmG,QAAQ,CAACpF,aAAa,CAACf,KAAK,GAAGA,KAAK;MAC7C;MACA,IAAI,CAACqH,cAAc,GAAGrH,KAAK;IAC/B;IACA;AACJ;AACA;AACA;AACA;IACIwO,iBAAiBA,CAAC/L,KAAK,EAAE;MACrB,MAAMtC,KAAK,GAAG,IAAI,CAACsI,YAAY;MAC/B,MAAMoG,QAAQ,GAAGpM,KAAK,GAAGA,KAAK,CAAChE,MAAM,GAAG,IAAI,CAACuJ,0BAA0B;MACvE,IAAI6G,QAAQ,EAAE;QACV,IAAI,CAAC3B,4BAA4B,CAAC2B,QAAQ,CAAC;QAC3C,IAAI,CAACjD,kBAAkB,CAACiD,QAAQ,CAAC7O,KAAK,CAAC;QACvC;QACA;QACA;QACA,IAAI,CAACuI,SAAS,CAACsG,QAAQ,CAAC7O,KAAK,CAAC;QAC9BG,KAAK,CAACqC,gBAAgB,CAACqM,QAAQ,CAAC;QAChC,IAAI,CAAC1I,QAAQ,CAACpF,aAAa,CAAC+N,KAAK,CAAC,CAAC;MACvC,CAAC,MACI,IAAI3O,KAAK,CAAChB,gBAAgB,IAC3B,IAAI,CAACgH,QAAQ,CAACpF,aAAa,CAACf,KAAK,KAAK,IAAI,CAACsH,cAAc,EAAE;QAC3D,IAAI,CAAC4F,4BAA4B,CAAC,IAAI,CAAC;QACvC,IAAI,CAACtB,kBAAkB,CAAC,IAAI,CAAC;QAC7B,IAAI,CAACrD,SAAS,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAACqB,UAAU,CAAC,CAAC;IACrB;IACA;AACJ;AACA;IACIsD,4BAA4BA,CAAC6B,IAAI,EAAEC,SAAS,EAAE;MAC1C;MACA;MACA,IAAI,CAACvG,YAAY,EAAErI,OAAO,EAAEkL,OAAO,CAAC5M,MAAM,IAAI;QAC1C,IAAIA,MAAM,KAAKqQ,IAAI,IAAIrQ,MAAM,CAAC2O,QAAQ,EAAE;UACpC3O,MAAM,CAAC8O,QAAQ,CAACwB,SAAS,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN;IACArF,kBAAkBA,CAAC8D,aAAa,GAAG,IAAI,CAACtH,QAAQ,CAACpF,aAAa,CAACf,KAAK,EAAE;MAClE,IAAI,CAAC2N,cAAc,CAACF,aAAa,CAAC;MAClC,IAAI,CAACG,WAAW,CAAC,CAAC;MAClB;MACA,IAAI,IAAI,CAACzD,aAAa,EAAE;QACpB,MAAM8E,OAAO,GAAG,IAAI,CAACxG,YAAY,CAAClP,EAAE;QACpCgD,mBAAmB,CAAC,IAAI,CAAC4N,aAAa,EAAE,WAAW,EAAE8E,OAAO,CAAC;MACjE;IACJ;IACAtB,cAAcA,CAACF,aAAa,EAAE;MAC1B,IAAI,CAAC,IAAI,CAAChF,YAAY,KAAK,OAAOzD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACvE,MAAMS,mCAAmC,CAAC,CAAC;MAC/C;MACA,IAAIyJ,UAAU,GAAG,IAAI,CAACpI,WAAW;MACjC,IAAI,CAACoI,UAAU,EAAE;QACb,IAAI,CAACnI,OAAO,GAAG,IAAIrJ,cAAc,CAAC,IAAI,CAAC+K,YAAY,CAACvI,QAAQ,EAAE,IAAI,CAACmG,iBAAiB,EAAE;UAClF9M,EAAE,EAAE,IAAI,CAACkN,UAAU,EAAE0I,UAAU,CAAC;QACpC,CAAC,CAAC;QACFD,UAAU,GAAGlT,gBAAgB,CAAC,IAAI,CAACoK,SAAS,EAAE,IAAI,CAACgJ,iBAAiB,CAAC,CAAC,CAAC;QACvE,IAAI,CAACtI,WAAW,GAAGoI,UAAU;QAC7B,IAAI,CAACvH,qBAAqB,GAAG,IAAI,CAAChB,cAAc,CAAChF,MAAM,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;UACtE,IAAI,IAAI,CAACyG,SAAS,IAAI6G,UAAU,EAAE;YAC9BA,UAAU,CAACG,UAAU,CAAC;cAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;YAAE,CAAC,CAAC;UAC3D;QACJ,CAAC,CAAC;QACF;QACA;QACA,IAAI,CAAC1H,6BAA6B,GAAG,IAAI,CAACD,mBAAmB,CACxD4H,OAAO,CAAC/R,WAAW,CAACgS,gBAAgB,CAAC,CACrC7N,SAAS,CAAC8N,MAAM,IAAI;UACrB,MAAMC,kBAAkB,GAAGD,MAAM,CAACE,OAAO;UACzC;UACA;UACA,IAAID,kBAAkB,EAAE;YACpB,IAAI,CAACnI,iBAAiB,CACjBqI,sBAAsB,CAAC,IAAI,CAAC,CAC5BC,iBAAiB,CAAC,IAAI,CAAC,CACvBC,kBAAkB,CAAC,CAAC,CAAC;UAC9B,CAAC,MACI;YACD,IAAI,CAACvI,iBAAiB,CACjBqI,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,iBAAiB,CAAC,KAAK,CAAC,CACxBC,kBAAkB,CAAC,CAAC,CAAC;UAC9B;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD;QACA,IAAI,CAACvI,iBAAiB,CAACwI,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;QAC7Df,UAAU,CAACG,UAAU,CAAC;UAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;QAAE,CAAC,CAAC;MAC3D;MACA,IAAIL,UAAU,IAAI,CAACA,UAAU,CAACnF,WAAW,CAAC,CAAC,EAAE;QACzCmF,UAAU,CAACgB,MAAM,CAAC,IAAI,CAACnJ,OAAO,CAAC;QAC/B,IAAI,CAACO,cAAc,GAAGmG,aAAa;QACnC,IAAI,CAAClG,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACG,2BAA2B,GAAG,IAAI,CAACuG,0BAA0B,CAAC,CAAC;MACxE;MACA,MAAMK,OAAO,GAAG,IAAI,CAACjG,SAAS;MAC9B,IAAI,CAACI,YAAY,CAAC5I,OAAO,GAAG,IAAI,CAAC4J,gBAAgB,GAAG,IAAI;MACxD,IAAI,CAAChB,YAAY,CAAC3I,qBAAqB,GAAG,IAAI;MAC9C,IAAI,CAAC2I,YAAY,CAAC1I,SAAS,CAAC,IAAI,CAAC0G,UAAU,EAAE0J,KAAK,CAAC;MACnD,IAAI,CAAClG,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACmG,yBAAyB,CAAC,CAAC;MAChC;MACA;MACA,IAAI,IAAI,CAAC/H,SAAS,IAAIiG,OAAO,KAAK,IAAI,CAACjG,SAAS,EAAE;QAC9C,IAAI,CAACkG,WAAW,CAAC,CAAC;MACtB;IACJ;IACA;IACA8B,mBAAmB,GAAI5N,KAAK,IAAK;MAC7B;MACA;MACA,IAAKA,KAAK,CAAC2J,OAAO,KAAKjP,MAAM,IAAI,CAACD,cAAc,CAACuF,KAAK,CAAC,IAClDA,KAAK,CAAC2J,OAAO,KAAK9O,QAAQ,IAAIJ,cAAc,CAACuF,KAAK,EAAE,QAAQ,CAAE,EAAE;QACjE;QACA;QACA,IAAI,IAAI,CAACuF,0BAA0B,EAAE;UACjC,IAAI,CAAC2G,uBAAuB,CAAC,IAAI,CAAC5G,yBAAyB,IAAI,EAAE,CAAC;UAClE,IAAI,CAACC,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAACC,oBAAoB,CAACe,IAAI,CAAC,CAAC;QAChC,IAAI,CAACwD,gBAAgB,CAAC,CAAC;QACvB;QACA;QACA/J,KAAK,CAAC6N,eAAe,CAAC,CAAC;QACvB7N,KAAK,CAAC6J,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACArC,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACxB,YAAY,CAACzG,cAAc,CAAC,CAAC;MAClC;MACA;MACA;MACA,IAAI,IAAI,CAACqG,SAAS,EAAE;QAChB,MAAM6G,UAAU,GAAG,IAAI,CAACpI,WAAW;QACnC,IAAI,CAAC,IAAI,CAACI,oBAAoB,EAAE;UAC5B;UACA;UACA,IAAI,CAACA,oBAAoB,GAAGgI,UAAU,CAACqB,aAAa,CAAC,CAAC,CAAC3O,SAAS,CAAC,IAAI,CAACyO,mBAAmB,CAAC;QAC9F;QACA,IAAI,CAAC,IAAI,CAAClJ,yBAAyB,EAAE;UACjC;UACA;UACA;UACA,IAAI,CAACA,yBAAyB,GAAG+H,UAAU,CAACsB,oBAAoB,CAAC,CAAC,CAAC5O,SAAS,CAAC,CAAC;QAClF;MACJ,CAAC,MACI;QACD,IAAI,CAACsF,oBAAoB,EAAE/E,WAAW,CAAC,CAAC;QACxC,IAAI,CAACgF,yBAAyB,EAAEhF,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC+E,oBAAoB,GAAG,IAAI,CAACC,yBAAyB,GAAG,IAAI;MACrE;IACJ;IACAiI,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAInT,aAAa,CAAC;QACrBwU,gBAAgB,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC5CC,cAAc,EAAE,IAAI,CAAC/J,eAAe,CAAC,CAAC;QACtC0I,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;QAC5BqB,SAAS,EAAE,IAAI,CAACrK,IAAI,IAAIsK,SAAS;QACjCzR,WAAW,EAAE,IAAI,CAACI,SAAS,EAAEJ,WAAW;QACxC0R,aAAa,EAAE,IAAI,CAACtR,SAAS,EAAEsR,aAAa;QAC5CC,UAAU,EAAE,IAAI,CAAC7I,kBAAkB;QACnC8I,iBAAiB,EAAE,IAAI,CAAClX;MAC5B,CAAC,CAAC;IACN;IACA4W,mBAAmBA,CAAA,EAAG;MAClB;MACA,MAAMO,QAAQ,GAAG/U,uCAAuC,CAAC,IAAI,CAACkK,SAAS,EAAE,IAAI,CAAC6J,oBAAoB,CAAC,CAAC,CAAC,CAChGJ,sBAAsB,CAAC,KAAK,CAAC,CAC7BqB,QAAQ,CAAC,KAAK,CAAC;MACpB,IAAI,CAAC7H,qBAAqB,CAAC4H,QAAQ,CAAC;MACpC,IAAI,CAACzJ,iBAAiB,GAAGyJ,QAAQ;MACjC,OAAOA,QAAQ;IACnB;IACA;IACA5H,qBAAqBA,CAACoH,gBAAgB,EAAE;MACpC;MACA;MACA,MAAMU,cAAc,GAAG,CACnB;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAM,CAAC,EAC3E;QAAEH,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE,QAAQ;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAC1E;MACD;MACA;MACA;MACA,MAAMR,UAAU,GAAG,IAAI,CAACjI,WAAW;MACnC,MAAM0I,cAAc,GAAG,CACnB;QAAEJ,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE,QAAQ;QAAER;MAAW,CAAC,EACvF;QAAEK,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE,QAAQ;QAAER;MAAW,CAAC,CACtF;MACD,IAAIU,SAAS;MACb,IAAI,IAAI,CAAC/I,QAAQ,KAAK,OAAO,EAAE;QAC3B+I,SAAS,GAAGD,cAAc;MAC9B,CAAC,MACI,IAAI,IAAI,CAAC9I,QAAQ,KAAK,OAAO,EAAE;QAChC+I,SAAS,GAAGN,cAAc;MAC9B,CAAC,MACI;QACDM,SAAS,GAAG,CAAC,GAAGN,cAAc,EAAE,GAAGK,cAAc,CAAC;MACtD;MACAf,gBAAgB,CAACiB,aAAa,CAACD,SAAS,CAAC;IAC7C;IACAxB,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACtH,WAAW,EAAE;QAClB,OAAO,IAAI,CAACA,WAAW,CAACzD,UAAU;MACtC;MACA,OAAO,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACwE,yBAAyB,CAAC,CAAC,GAAG,IAAI,CAAC9E,QAAQ;IACxF;IACAoJ,cAAcA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC9G,YAAY,CAACjI,UAAU,IAAI,IAAI,CAACmR,aAAa,CAAC,CAAC;IAC/D;IACA;IACAA,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC1B,oBAAoB,CAAC,CAAC,CAAClP,aAAa,CAAC6Q,qBAAqB,CAAC,CAAC,CAACtC,KAAK;IAClF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI9C,gBAAgBA,CAAA,EAAG;MACf,MAAM/D,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,IAAIA,YAAY,CAACzJ,qBAAqB,EAAE;QACpC;QACA;QACA;QACA,IAAI6S,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIhQ,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG4G,YAAY,CAACrI,OAAO,CAACmC,MAAM,EAAEV,KAAK,EAAE,EAAE;UAC9D,MAAMnD,MAAM,GAAG+J,YAAY,CAACrI,OAAO,CAAC0R,GAAG,CAACjQ,KAAK,CAAC;UAC9C,IAAI,CAACnD,MAAM,CAACwN,QAAQ,EAAE;YAClB2F,uBAAuB,GAAGhQ,KAAK;YAC/B;UACJ;QACJ;QACA4G,YAAY,CAAC9I,WAAW,CAACoS,aAAa,CAACF,uBAAuB,CAAC;MACnE,CAAC,MACI;QACDpJ,YAAY,CAAC9I,WAAW,CAACoS,aAAa,CAAC,CAAC,CAAC,CAAC;MAC9C;IACJ;IACA;IACAnF,QAAQA,CAAA,EAAG;MACP,MAAMoF,OAAO,GAAG,IAAI,CAAC7L,QAAQ,CAACpF,aAAa;MAC3C,OAAO,CAACiR,OAAO,CAACC,QAAQ,IAAI,CAACD,OAAO,CAAC9F,QAAQ,IAAI,CAAC,IAAI,CAACrD,oBAAoB;IAC/E;IACA;IACAgE,eAAeA,CAAChL,KAAK,EAAE;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM4G,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMyJ,UAAU,GAAGxZ,6BAA6B,CAACmJ,KAAK,EAAE4G,YAAY,CAACrI,OAAO,EAAEqI,YAAY,CAACpI,YAAY,CAAC;MACxG,IAAIwB,KAAK,KAAK,CAAC,IAAIqQ,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAzJ,YAAY,CAACrG,aAAa,CAAC,CAAC,CAAC;MACjC,CAAC,MACI,IAAIqG,YAAY,CAACtI,KAAK,EAAE;QACzB,MAAMzB,MAAM,GAAG+J,YAAY,CAACrI,OAAO,CAAC2B,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC;QACpD,IAAInD,MAAM,EAAE;UACR,MAAMsT,OAAO,GAAGtT,MAAM,CAACyT,eAAe,CAAC,CAAC;UACxC,MAAMC,iBAAiB,GAAGxZ,wBAAwB,CAACoZ,OAAO,CAACK,SAAS,EAAEL,OAAO,CAACM,YAAY,EAAE7J,YAAY,CAACnG,aAAa,CAAC,CAAC,EAAEmG,YAAY,CAACtI,KAAK,CAACY,aAAa,CAACuR,YAAY,CAAC;UACxK7J,YAAY,CAACrG,aAAa,CAACgQ,iBAAiB,CAAC;QACjD;MACJ;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIjI,aAAa,GAAG,IAAI;IACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIiG,yBAAyBA,CAAA,EAAG;MACxB;MACA;MACA;MACA;MACA;MACA;MACA,MAAMmC,KAAK,GAAG,IAAI,CAACpM,QAAQ,CAACpF,aAAa,CAACyR,OAAO,CAAC,mDAAmD,CAAC;MACtG,IAAI,CAACD,KAAK,EAAE;QACR;QACA;MACJ;MACA,MAAMtD,OAAO,GAAG,IAAI,CAACxG,YAAY,CAAClP,EAAE;MACpC,IAAI,IAAI,CAAC4Q,aAAa,EAAE;QACpB7N,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAE8E,OAAO,CAAC;MACpE;MACA1S,mBAAmB,CAACgW,KAAK,EAAE,WAAW,EAAEtD,OAAO,CAAC;MAChD,IAAI,CAAC9E,aAAa,GAAGoI,KAAK;IAC9B;IACA;IACA/I,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACW,aAAa,EAAE;QACpB,MAAM8E,OAAO,GAAG,IAAI,CAACxG,YAAY,CAAClP,EAAE;QACpC+C,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAE8E,OAAO,CAAC;QAChE,IAAI,CAAC9E,aAAa,GAAG,IAAI;MAC7B;IACJ;IACA,OAAOvH,IAAI,YAAA6P,+BAAA3P,iBAAA;MAAA,YAAAA,iBAAA,IAAwFyC,sBAAsB;IAAA;IACzH,OAAOH,IAAI,kBAj6B8ElM,EAAE,CAAAmM,iBAAA;MAAApC,IAAA,EAi6BJsC,sBAAsB;MAAArC,SAAA;MAAAY,SAAA;MAAA4O,QAAA;MAAAC,YAAA,WAAAC,oCAAA5Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAj6BpBE,EAAE,CAAA2Z,UAAA,qBAAAC,kDAAA;YAAA,OAi6BJ7Z,GAAA,CAAAyU,YAAA,CAAa,CAAC;UAAA,CAAO,CAAC,kBAAAqF,+CAAA;YAAA,OAAtB9Z,GAAA,CAAAuP,UAAA,CAAW,CAAC;UAAA,CAAS,CAAC,mBAAAwK,gDAAAC,MAAA;YAAA,OAAtBha,GAAA,CAAA8T,YAAA,CAAAkG,MAAmB,CAAC;UAAA,CAAC,CAAC,qBAAAC,kDAAAD,MAAA;YAAA,OAAtBha,GAAA,CAAAkT,cAAA,CAAA8G,MAAqB,CAAC;UAAA,CAAD,CAAC,mBAAAE,gDAAA;YAAA,OAAtBla,GAAA,CAAA4U,YAAA,CAAa,CAAC;UAAA,CAAO,CAAC;QAAA;QAAA,IAAA7U,EAAA;UAj6BpBE,EAAE,CAAAe,WAAA,iBAAAhB,GAAA,CAAA2P,qBAAA,UAAA3P,GAAA,CAAA4P,oBAAA,GAi6BmB,IAAI,GAAG,UAAU,uBAAA5P,GAAA,CAAA4P,oBAAA,GAAjB,IAAI,GAAG,MAAM,2BAAA5P,GAAA,CAAAoP,SAAA,IAAApP,GAAA,CAAA0R,YAAA,GAAA1R,GAAA,CAAA0R,YAAA,CAAApR,EAAA,GAAY,IAAI,mBAAAN,GAAA,CAAA4P,oBAAA,GAA7B,IAAI,GAAG5P,GAAA,CAAAoP,SAAA,CAAA+K,QAAA,CAAmB,CAAC,mBAAAna,GAAA,CAAA4P,oBAAA,KAAA5P,GAAA,CAAAoP,SAAA,GAAX,IAAI,GAAApP,GAAA,CAAAwP,YAAA,kBAAAxP,GAAA,CAAAwP,YAAA,CAAAlP,EAAA,mBAAAN,GAAA,CAAA4P,oBAAA,GAApB,IAAI,GAAG,SAAS;QAAA;MAAA;MAAA9E,MAAA;QAAA0E,YAAA;QAAAC,QAAA;QAAAC,WAAA;QAAAC,qBAAA;QAAAC,oBAAA,yDAA4XlO,gBAAgB;MAAA;MAAAsJ,QAAA;MAAAC,QAAA,GAj6BjbhL,EAAE,CAAAiL,kBAAA,CAi6BwpC,CAACmB,+BAA+B,CAAC,GAj6B3rCpM,EAAE,CAAAma,oBAAA;IAAA;EAk6B/F;EAAC,OAxyBK9N,sBAAsB;AAAA;AAyyB5B;EAAA,QAAAP,SAAA,oBAAAA,SAAA;AAAA;AAuCoB,IAEdsO,qBAAqB;EAA3B,MAAMA,qBAAqB,CAAC;IACxB,OAAO1Q,IAAI,YAAA2Q,8BAAAzQ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwQ,qBAAqB;IAAA;IACxH,OAAOE,IAAI,kBA98B8Eta,EAAE,CAAAua,gBAAA;MAAAxQ,IAAA,EA88BSqQ;IAAqB;IAWzH,OAAOI,IAAI,kBAz9B8Exa,EAAE,CAAAya,gBAAA;MAAAC,SAAA,EAy9B2C,CAAC7N,iDAAiD,CAAC;MAAA8N,OAAA,GAAY1X,aAAa,EAC1MmC,eAAe,EACfC,eAAe,EAAEzC,mBAAmB,EACpCwC,eAAe,EACfC,eAAe;IAAA;EAC3B;EAAC,OAlBK+U,qBAAqB;AAAA;AAmB3B;EAAA,QAAAtO,SAAA,oBAAAA,SAAA;AAAA;AAuBA,SAASpG,gCAAgC,EAAEG,wCAAwC,EAAE4G,gCAAgC,EAAEE,wCAAwC,EAAEE,iDAAiD,EAAET,+BAA+B,EAAEjG,eAAe,EAAEiU,qBAAqB,EAAErO,qBAAqB,EAAEzG,4BAA4B,EAAE+G,sBAAsB,EAAEnN,SAAS,EAAEqN,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}