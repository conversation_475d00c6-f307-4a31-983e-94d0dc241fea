# 🚀 Terra Retail ERP System

## نظام تخطيط موارد المؤسسات للتجارة التجزئة

نظام ERP شامل ومتطور للتجارة التجزئة يدعم اللغة العربية بالكامل مع واجهة احترافية وميزات متقدمة.

---

## 📋 **المميزات الرئيسية**

### 🔐 **إدارة المستخدمين والصلاحيات**
- نظام مصادقة متقدم باستخدام JWT
- إدارة الأدوار والصلاحيات
- نظام صلاحيات الفروع المتقدم
- تتبع جلسات المستخدمين
- سجل مراجعة شامل

### 👥 **إدارة العملاء والموردين**
- إدارة شاملة للعملاء مع أنواع مختلفة
- إدارة الموردين مع تقييمات ومعلومات مفصلة
- نظام أرصدة وحدود ائتمان
- تتبع المعاملات والمدفوعات

### 📦 **إدارة المنتجات والمخزون**
- إدارة شاملة للمنتجات مع تصنيفات هرمية
- نظام باركود متقدم
- إدارة المخزون متعددة الفروع
- تتبع الدفعات وتواريخ الانتهاء
- تحويلات بين الفروع
- جرد دوري وتسويات

### 💰 **إدارة المبيعات**
- نقاط بيع متقدمة
- فواتير مبيعات مع خصومات وضرائب
- طرق دفع متعددة
- إدارة المرتجعات
- تقارير مبيعات تفصيلية

### 🏭 **إدارة المشتريات**
- فواتير مشتريات شاملة
- إدارة طلبات الشراء
- تتبع التسليم والاستلام
- إدارة مدفوعات الموردين

### 👨‍💼 **إدارة الموارد البشرية**
- إدارة شاملة للموظفين
- نظام الحضور والانصراف
- إدارة الورديات
- إدارة الإجازات والأرصدة
- نظام الرواتب والمكافآت
- تقارير الموارد البشرية

### 💼 **النظام المالي والمحاسبي**
- دليل حسابات شجري
- قيود يومية بثلاث مراحل (مسودة، مرحل مبدئي، مرحل نهائي)
- إدارة الخزينة والبنوك
- سندات قبض وصرف
- تقارير مالية شاملة

### 📊 **التقارير والتحليلات**
- لوحة تحكم تفاعلية
- تقارير مبيعات ومشتريات
- تقارير مخزون ومالية
- تقارير موارد بشرية
- رسوم بيانية وإحصائيات

---

## 🛠 **التقنيات المستخدمة**

### Backend (API)
- **ASP.NET Core 8.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM للتعامل مع قاعدة البيانات
- **SQL Server** - قاعدة البيانات
- **JWT Authentication** - نظام المصادقة
- **Swagger/OpenAPI** - توثيق API
- **BCrypt** - تشفير كلمات المرور

### Frontend (سيتم إنشاؤه لاحقاً)
- **Angular 17+** - إطار العمل للواجهة
- **Angular Material** - مكتبة المكونات
- **TypeScript** - لغة البرمجة
- **RxJS** - إدارة البيانات التفاعلية

---

## 📁 **هيكل المشروع**

```
TerraRetailERP_Complete/
├── TerraRetailERP.API/
│   ├── Controllers/           # وحدات التحكم
│   │   ├── AuthController.cs
│   │   ├── CustomersController.cs
│   │   ├── SuppliersController.cs
│   │   ├── ProductsController.cs
│   │   ├── SalesController.cs
│   │   ├── EmployeesController.cs
│   │   ├── ReportsController.cs
│   │   └── SettingsController.cs
│   ├── Models/               # نماذج البيانات
│   │   ├── User.cs
│   │   ├── Branch.cs
│   │   ├── Customer.cs
│   │   ├── Supplier.cs
│   │   ├── Product.cs
│   │   ├── Sales.cs
│   │   ├── Employee.cs
│   │   ├── Financial.cs
│   │   └── Common.cs
│   ├── Data/                 # سياق قاعدة البيانات
│   │   └── TerraRetailDbContext.cs
│   ├── appsettings.json      # إعدادات التطبيق
│   └── Program.cs            # نقطة البداية
└── README.md                 # هذا الملف
```

---

## ⚙️ **متطلبات التشغيل**

### البرامج المطلوبة
- **.NET 8.0 SDK** أو أحدث
- **SQL Server 2019** أو أحدث (أو SQL Server Express)
- **Visual Studio 2022** أو **VS Code** (اختياري)

### إعدادات قاعدة البيانات
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TerraRetailERP;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"
  }
}
```

---

## 🚀 **طريقة التشغيل**

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd TerraRetailERP_Complete
```

### 2. تشغيل API
```bash
cd TerraRetailERP.API
dotnet restore
dotnet run
```

### 3. الوصول للتطبيق
- **Swagger UI**: https://localhost:7000
- **API Base URL**: https://localhost:7000/api

---

## 📊 **API Endpoints**

### 🔐 Authentication
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج
- `POST /api/auth/refresh-token` - تجديد الرمز المميز
- `GET /api/auth/profile` - معلومات المستخدم

### 👥 Customer Management
- `GET /api/customers` - قائمة العملاء
- `GET /api/customers/{id}` - تفاصيل عميل
- `POST /api/customers` - إضافة عميل جديد
- `PUT /api/customers/{id}` - تحديث بيانات عميل
- `DELETE /api/customers/{id}` - حذف عميل
- `GET /api/customers/balance/{id}` - رصيد العميل

### 🏭 Supplier Management
- `GET /api/suppliers` - قائمة الموردين
- `GET /api/suppliers/{id}` - تفاصيل مورد
- `POST /api/suppliers` - إضافة مورد جديد
- `PUT /api/suppliers/{id}` - تحديث بيانات مورد
- `DELETE /api/suppliers/{id}` - حذف مورد
- `GET /api/suppliers/balance/{id}` - رصيد المورد

### 📦 Product Management
- `GET /api/products` - قائمة المنتجات
- `GET /api/products/{id}` - تفاصيل منتج
- `POST /api/products` - إضافة منتج جديد
- `PUT /api/products/{id}` - تحديث بيانات منتج
- `DELETE /api/products/{id}` - حذف منتج
- `GET /api/products/categories` - فئات المنتجات
- `GET /api/products/units` - وحدات القياس

### 💰 Sales Management
- `GET /api/sales` - قائمة المبيعات
- `GET /api/sales/{id}` - تفاصيل فاتورة مبيعات
- `POST /api/sales` - إنشاء فاتورة مبيعات جديدة
- `PUT /api/sales/{id}/cancel` - إلغاء فاتورة مبيعات
- `GET /api/sales/dashboard` - لوحة تحكم المبيعات

### 👨‍💼 HR Management
- `GET /api/employees` - قائمة الموظفين
- `GET /api/employees/{id}` - تفاصيل موظف
- `POST /api/employees` - إضافة موظف جديد
- `PUT /api/employees/{id}` - تحديث بيانات موظف
- `DELETE /api/employees/{id}` - حذف موظف
- `GET /api/employees/{id}/attendance` - سجل حضور موظف

### 📊 Reports & Analytics
- `GET /api/reports/dashboard` - لوحة التحكم الرئيسية
- `GET /api/reports/sales-chart` - رسم بياني للمبيعات
- `GET /api/reports/top-products` - أفضل المنتجات مبيعاً
- `GET /api/reports/inventory-status` - حالة المخزون
- `GET /api/reports/financial-summary` - الملخص المالي

### ⚙️ Settings
- `GET /api/settings/branches` - قائمة الفروع
- `GET /api/settings/countries` - قائمة البلدان
- `GET /api/settings/areas` - قائمة المناطق
- `GET /api/settings/payment-methods` - طرق الدفع
- `GET /api/settings/system-info` - معلومات النظام

---

## 🗄️ **قاعدة البيانات**

### الجداول الرئيسية
- **Users** - المستخدمين
- **Branches** - الفروع
- **Customers** - العملاء
- **Suppliers** - الموردين
- **Products** - المنتجات
- **Sales** - المبيعات
- **Employees** - الموظفين
- **ChartOfAccounts** - دليل الحسابات
- **JournalEntries** - القيود اليومية

### المميزات المتقدمة
- **عدادات تلقائية آمنة** لمنع التكرار
- **نظام صلاحيات فروع متقدم**
- **تتبع شامل للتغييرات** (Audit Trail)
- **دعم العملات المتعددة**
- **نظام مخزون متعدد الفروع**

---

## 🔒 **الأمان**

- تشفير كلمات المرور باستخدام BCrypt
- مصادقة JWT مع انتهاء صلاحية
- تتبع جلسات المستخدمين
- سجل مراجعة شامل لجميع العمليات
- حماية من SQL Injection
- التحقق من صحة البيانات

---

## 📝 **الخطوات التالية**

1. ✅ **إنشاء API شامل** - مكتمل
2. 🔄 **إنشاء واجهة Angular** - قيد التطوير
3. 🔄 **إضافة المزيد من التقارير**
4. 🔄 **تحسين الأداء**
5. 🔄 **إضافة اختبارات الوحدة**

---

## 👨‍💻 **المطور**

تم تطوير هذا النظام بواسطة فريق Terra Retail ERP

**الدعم الفني**: <EMAIL>

---

## 📄 **الترخيص**

هذا المشروع محمي بحقوق الطبع والنشر. جميع الحقوق محفوظة.

---

**🎉 مبروك! تم إنشاء نظام Terra Retail ERP بنجاح!**

النظام جاهز للاستخدام ويحتوي على جميع الوحدات الأساسية لإدارة التجارة التجزئة بشكل احترافي ومتطور.
