using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// الموردين
    /// </summary>
    public class Supplier : BaseEntity
    {
        /// <summary>
        /// كود المورد (فريد)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string SupplierCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم المورد بالعربية
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المورد بالإنجليزية
        /// </summary>
        [MaxLength(200)]
        public string? NameEn { get; set; }

        /// <summary>
        /// نوع المورد
        /// </summary>
        public SupplierType SupplierType { get; set; } = SupplierType.Company;

        /// <summary>
        /// رقم السجل التجاري
        /// </summary>
        [MaxLength(50)]
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// الرقم الضريبي
        /// </summary>
        [MaxLength(50)]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [MaxLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// المدينة
        /// </summary>
        [MaxLength(100)]
        public string? City { get; set; }

        /// <summary>
        /// الدولة
        /// </summary>
        [MaxLength(100)]
        public string? Country { get; set; }

        /// <summary>
        /// الرمز البريدي
        /// </summary>
        [MaxLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// رقم الهاتف الرئيسي
        /// </summary>
        [MaxLength(20)]
        public string? Phone1 { get; set; }

        /// <summary>
        /// رقم الهاتف الإضافي
        /// </summary>
        [MaxLength(20)]
        public string? Phone2 { get; set; }

        /// <summary>
        /// رقم الفاكس
        /// </summary>
        [MaxLength(20)]
        public string? Fax { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [MaxLength(100)]
        public string? Email { get; set; }

        /// <summary>
        /// الموقع الإلكتروني
        /// </summary>
        [MaxLength(200)]
        public string? Website { get; set; }

        /// <summary>
        /// اسم جهة الاتصال
        /// </summary>
        [MaxLength(100)]
        public string? ContactPersonName { get; set; }

        /// <summary>
        /// منصب جهة الاتصال
        /// </summary>
        [MaxLength(100)]
        public string? ContactPersonTitle { get; set; }

        /// <summary>
        /// هاتف جهة الاتصال
        /// </summary>
        [MaxLength(20)]
        public string? ContactPersonPhone { get; set; }

        /// <summary>
        /// إيميل جهة الاتصال
        /// </summary>
        [MaxLength(100)]
        public string? ContactPersonEmail { get; set; }

        /// <summary>
        /// شروط الدفع (بالأيام)
        /// </summary>
        public int PaymentTerms { get; set; } = 0;

        /// <summary>
        /// الحد الائتماني
        /// </summary>
        public decimal CreditLimit { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// تقييم المورد (1-5)
        /// </summary>
        public decimal Rating { get; set; } = 0;

        /// <summary>
        /// هل المورد نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل المورد معتمد
        /// </summary>
        public bool IsApproved { get; set; } = true;

        /// <summary>
        /// تاريخ آخر معاملة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        /// <summary>
        /// العملة المفضلة
        /// </summary>
        [MaxLength(3)]
        public string PreferredCurrency { get; set; } = "SAR";

        /// <summary>
        /// طريقة الدفع المفضلة
        /// </summary>
        public int? PreferredPaymentMethodId { get; set; }

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [MaxLength(1000)]
        public string? GeneralNotes { get; set; }

        /// <summary>
        /// ملاحظات داخلية
        /// </summary>
        [MaxLength(1000)]
        public string? InternalNotes { get; set; }

        /// <summary>
        /// رقم الحساب البنكي
        /// </summary>
        [MaxLength(50)]
        public string? BankAccountNumber { get; set; }

        /// <summary>
        /// اسم البنك
        /// </summary>
        [MaxLength(100)]
        public string? BankName { get; set; }

        /// <summary>
        /// رقم IBAN
        /// </summary>
        [MaxLength(50)]
        public string? IBAN { get; set; }

        /// <summary>
        /// رمز SWIFT
        /// </summary>
        [MaxLength(20)]
        public string? SwiftCode { get; set; }

        // Navigation Properties
        public virtual PaymentMethod? PreferredPaymentMethod { get; set; }
        public virtual ICollection<ProductSupplier> ProductSuppliers { get; set; } = new List<ProductSupplier>();
        public virtual ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
        public virtual ICollection<ProductCode> ProductCodes { get; set; } = new List<ProductCode>();
        public virtual ICollection<SupplierContact> Contacts { get; set; } = new List<SupplierContact>();
        public virtual ICollection<SupplierEvaluation> Evaluations { get; set; } = new List<SupplierEvaluation>();
    }

    /// <summary>
    /// نوع المورد
    /// </summary>
    public enum SupplierType
    {
        /// <summary>
        /// شركة
        /// </summary>
        Company = 1,

        /// <summary>
        /// فرد
        /// </summary>
        Individual = 2,

        /// <summary>
        /// مؤسسة
        /// </summary>
        Institution = 3,

        /// <summary>
        /// وكيل
        /// </summary>
        Agent = 4,

        /// <summary>
        /// موزع
        /// </summary>
        Distributor = 5,

        /// <summary>
        /// مصنع
        /// </summary>
        Manufacturer = 6
    }
}
