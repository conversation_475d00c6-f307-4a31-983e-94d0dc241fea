{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nlet _StructuralStylesLoader = /*#__PURE__*/(() => {\n  class _StructuralStylesLoader {\n    static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _StructuralStylesLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _StructuralStylesLoader,\n      selectors: [[\"structural-styles\"]],\n      decls: 0,\n      vars: 0,\n      template: function _StructuralStylesLoader_Template(rf, ctx) {},\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _StructuralStylesLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _StructuralStylesLoader as _ };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "_StructuralStylesLoader", "ɵfac", "_StructuralStylesLoader_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "template", "_StructuralStylesLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "_"], "sources": ["X:/barmaga/Angler/Erp 2/database/Terra.Retail.Web/node_modules/@angular/material/fesm2022/structural-styles-CObeNzjn.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _StructuralStylesLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _StructuralStylesLoader, isStandalone: true, selector: \"structural-styles\", ngImport: i0, template: '', isInline: true, styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _StructuralStylesLoader, decorators: [{\n            type: Component,\n            args: [{ selector: 'structural-styles', encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"] }]\n        }] });\n\nexport { _StructuralStylesLoader as _ };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;;AAErF;AACA;AACA;AACA;AAHA,IAIMC,uBAAuB;EAA7B,MAAMA,uBAAuB,CAAC;IAC1B,OAAOC,IAAI,YAAAC,gCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFH,uBAAuB;IAAA;IAC1H,OAAOI,IAAI,kBAD8ER,EAAE,CAAAS,iBAAA;MAAAC,IAAA,EACJN,uBAAuB;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EAClH;EAAC,OAHKhB,uBAAuB;AAAA;AAI7B;EAAA,QAAAiB,SAAA,oBAAAA,SAAA;AAAA;AAKA,SAASjB,uBAAuB,IAAIkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}