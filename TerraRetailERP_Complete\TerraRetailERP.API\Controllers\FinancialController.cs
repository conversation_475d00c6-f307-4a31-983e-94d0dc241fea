using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("💰 Financial Management")]
    public class FinancialController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public FinancialController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet("chart-of-accounts")]
        public async Task<ActionResult<IEnumerable<ChartOfAccount>>> GetChartOfAccounts(
            [FromQuery] int? parentId = null,
            [FromQuery] int? accountType = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.ChartOfAccounts
                    .Include(coa => coa.Parent)
                    .Include(coa => coa.Children)
                    .AsQueryable();

                if (parentId.HasValue)
                    query = query.Where(coa => coa.ParentId == parentId);

                if (accountType.HasValue)
                    query = query.Where(coa => coa.AccountType == accountType);

                if (isActive.HasValue)
                    query = query.Where(coa => coa.IsActive == isActive);

                var accounts = await query
                    .OrderBy(coa => coa.AccountCode)
                    .ToListAsync();

                return Ok(accounts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("chart-of-accounts")]
        public async Task<ActionResult<ChartOfAccount>> CreateAccount(CreateAccountRequest request)
        {
            try
            {
                // Generate account code
                var accountCode = await GenerateAccountCode(request.ParentId, request.AccountType);

                var account = new ChartOfAccount
                {
                    AccountCode = accountCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    AccountType = request.AccountType,
                    ParentId = request.ParentId,
                    Level = await CalculateAccountLevel(request.ParentId),
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = request.AllowPosting,
                    Description = request.Description,
                    CreatedAt = DateTime.Now
                };

                _context.ChartOfAccounts.Add(account);

                // Update parent to be a parent account
                if (request.ParentId.HasValue)
                {
                    var parent = await _context.ChartOfAccounts.FindAsync(request.ParentId);
                    if (parent != null)
                    {
                        parent.IsParent = true;
                        parent.AllowPosting = false; // Parent accounts cannot have direct postings
                    }
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetChartOfAccounts), new { id = account.Id }, account);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("journal-entries")]
        public async Task<ActionResult<IEnumerable<JournalEntry>>> GetJournalEntries(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? status = null,
            [FromQuery] int? transactionType = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.JournalEntries
                    .Include(je => je.User)
                    .Include(je => je.JournalEntryDetails)
                        .ThenInclude(jed => jed.Account)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(je => je.EntryNumber.Contains(search) || 
                                            je.Description.Contains(search) ||
                                            je.Reference!.Contains(search));
                }

                if (status.HasValue)
                    query = query.Where(je => je.Status == status);

                if (transactionType.HasValue)
                    query = query.Where(je => je.TransactionType == transactionType);

                if (fromDate.HasValue)
                    query = query.Where(je => je.EntryDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(je => je.EntryDate <= toDate);

                var totalCount = await query.CountAsync();
                var entries = await query
                    .OrderByDescending(je => je.EntryDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(je => new
                    {
                        je.Id,
                        je.EntryNumber,
                        je.EntryDate,
                        je.Description,
                        je.Reference,
                        je.TransactionType,
                        TransactionTypeName = je.TransactionType == 1 ? "قيد عادي" :
                                            je.TransactionType == 2 ? "قيد افتتاحي" :
                                            je.TransactionType == 3 ? "قيد تسوية" : "قيد إقفال",
                        je.Status,
                        StatusName = je.Status == 1 ? "مسودة" :
                                   je.Status == 2 ? "مرحل مبدئي" : "مرحل نهائي",
                        je.TotalDebit,
                        je.TotalCredit,
                        UserName = je.User.FullName,
                        DetailsCount = je.JournalEntryDetails.Count,
                        je.CreatedAt,
                        je.PostedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = entries,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("journal-entries")]
        public async Task<ActionResult<JournalEntry>> CreateJournalEntry(CreateJournalEntryRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                // Validate that debits equal credits
                var totalDebit = request.Details.Sum(d => d.DebitAmount);
                var totalCredit = request.Details.Sum(d => d.CreditAmount);

                if (Math.Abs(totalDebit - totalCredit) > 0.01m)
                {
                    return BadRequest(new { message = "إجمالي المدين يجب أن يساوي إجمالي الدائن" });
                }

                // Generate entry number
                var entryNumber = await GenerateJournalEntryNumber();

                var journalEntry = new JournalEntry
                {
                    EntryNumber = entryNumber,
                    EntryDate = request.EntryDate ?? DateTime.Now,
                    Description = request.Description,
                    Reference = request.Reference,
                    TransactionType = request.TransactionType,
                    Status = 1, // Draft
                    TotalDebit = totalDebit,
                    TotalCredit = totalCredit,
                    UserId = userId.Value,
                    CreatedAt = DateTime.Now
                };

                _context.JournalEntries.Add(journalEntry);
                await _context.SaveChangesAsync();

                // Add journal entry details
                int lineNumber = 1;
                foreach (var detail in request.Details)
                {
                    var journalDetail = new JournalEntryDetail
                    {
                        JournalEntryId = journalEntry.Id,
                        AccountId = detail.AccountId,
                        LineNumber = lineNumber++,
                        Description = detail.Description,
                        DebitAmount = detail.DebitAmount,
                        CreditAmount = detail.CreditAmount,
                        CostCenterId = detail.CostCenterId,
                        Reference = detail.Reference,
                        CreatedAt = DateTime.Now
                    };

                    _context.JournalEntryDetails.Add(journalDetail);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetJournalEntries), new { id = journalEntry.Id }, journalEntry);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("journal-entries/{id}/post")]
        public async Task<IActionResult> PostJournalEntry(int id, [FromBody] PostJournalEntryRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                var journalEntry = await _context.JournalEntries
                    .Include(je => je.JournalEntryDetails)
                        .ThenInclude(jed => jed.Account)
                    .FirstOrDefaultAsync(je => je.Id == id);

                if (journalEntry == null)
                    return NotFound(new { message = "القيد غير موجود" });

                if (journalEntry.Status == 3)
                    return BadRequest(new { message = "القيد مرحل نهائياً ولا يمكن تعديله" });

                // Update account balances
                foreach (var detail in journalEntry.JournalEntryDetails)
                {
                    if (!detail.Account.AllowPosting)
                        continue;

                    var accountBalance = await _context.AccountBalances
                        .FirstOrDefaultAsync(ab => ab.AccountId == detail.AccountId);

                    if (accountBalance == null)
                    {
                        accountBalance = new AccountBalance
                        {
                            AccountId = detail.AccountId,
                            DebitBalance = 0,
                            CreditBalance = 0,
                            NetBalance = 0,
                            LastTransactionDate = journalEntry.EntryDate,
                            CreatedAt = DateTime.Now
                        };
                        _context.AccountBalances.Add(accountBalance);
                    }

                    // Update balances based on account type
                    if (detail.Account.AccountType == 1 || detail.Account.AccountType == 5) // Assets & Expenses
                    {
                        accountBalance.DebitBalance += detail.DebitAmount;
                        accountBalance.CreditBalance += detail.CreditAmount;
                        accountBalance.NetBalance = accountBalance.DebitBalance - accountBalance.CreditBalance;
                    }
                    else // Liabilities, Equity & Revenue
                    {
                        accountBalance.DebitBalance += detail.DebitAmount;
                        accountBalance.CreditBalance += detail.CreditAmount;
                        accountBalance.NetBalance = accountBalance.CreditBalance - accountBalance.DebitBalance;
                    }

                    accountBalance.LastTransactionDate = journalEntry.EntryDate;
                    accountBalance.UpdatedAt = DateTime.Now;
                }

                journalEntry.Status = request.FinalPost ? 3 : 2; // Final or Preliminary
                journalEntry.PostedAt = DateTime.Now;
                journalEntry.PostedBy = userId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                var statusMessage = request.FinalPost ? "تم ترحيل القيد نهائياً" : "تم ترحيل القيد مبدئياً";
                return Ok(new { message = statusMessage });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("receipts")]
        public async Task<ActionResult> GetReceipts(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? customerId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Receipts
                    .Include(r => r.Customer)
                    .Include(r => r.Branch)
                    .Include(r => r.User)
                    .Include(r => r.PaymentMethod)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(r => r.ReceiptNumber.Contains(search) || 
                                           r.Customer!.NameAr.Contains(search) ||
                                           r.ReferenceNumber!.Contains(search));
                }

                if (customerId.HasValue)
                    query = query.Where(r => r.CustomerId == customerId);

                if (branchId.HasValue)
                    query = query.Where(r => r.BranchId == branchId);

                if (fromDate.HasValue)
                    query = query.Where(r => r.ReceiptDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(r => r.ReceiptDate <= toDate);

                var totalCount = await query.CountAsync();
                var receipts = await query
                    .OrderByDescending(r => r.ReceiptDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(r => new
                    {
                        r.Id,
                        r.ReceiptNumber,
                        r.ReceiptDate,
                        r.CustomerId,
                        CustomerName = r.Customer != null ? r.Customer.NameAr : "نقدي",
                        r.Amount,
                        r.Description,
                        r.ReferenceNumber,
                        PaymentMethodName = r.PaymentMethod.NameAr,
                        BranchName = r.Branch.NameAr,
                        UserName = r.User.FullName,
                        r.Status,
                        StatusName = r.Status == 1 ? "مكتمل" : "ملغي",
                        r.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = receipts,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost("receipts")]
        public async Task<ActionResult> CreateReceipt(CreateReceiptRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized();

                // Generate receipt number
                var receiptNumber = await GenerateReceiptNumber(request.BranchId);

                var receipt = new Receipt
                {
                    ReceiptNumber = receiptNumber,
                    ReceiptDate = request.ReceiptDate ?? DateTime.Now,
                    CustomerId = request.CustomerId,
                    BranchId = request.BranchId,
                    PaymentMethodId = request.PaymentMethodId,
                    Amount = request.Amount,
                    Description = request.Description,
                    ReferenceNumber = request.ReferenceNumber,
                    BankName = request.BankName,
                    AccountNumber = request.AccountNumber,
                    Status = 1, // Completed
                    UserId = userId.Value,
                    Currency = request.Currency ?? "EGP",
                    ExchangeRate = request.ExchangeRate ?? 1,
                    CreatedAt = DateTime.Now
                };

                _context.Receipts.Add(receipt);

                // Update customer balance if applicable
                if (request.CustomerId.HasValue)
                {
                    var customer = await _context.Customers.FindAsync(request.CustomerId);
                    if (customer != null)
                    {
                        customer.CurrentBalance -= request.Amount;
                        customer.UpdatedAt = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return CreatedAtAction(nameof(GetReceipts), new { id = receipt.Id }, new
                {
                    message = "تم إنشاء سند القبض بنجاح",
                    receiptId = receipt.Id,
                    receiptNumber = receipt.ReceiptNumber
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("payments")]
        public async Task<ActionResult> GetPayments(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? supplierId = null,
            [FromQuery] int? branchId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Payments
                    .Include(p => p.Supplier)
                    .Include(p => p.Branch)
                    .Include(p => p.User)
                    .Include(p => p.PaymentMethod)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.PaymentNumber.Contains(search) || 
                                           p.Supplier!.NameAr.Contains(search) ||
                                           p.ReferenceNumber!.Contains(search));
                }

                if (supplierId.HasValue)
                    query = query.Where(p => p.SupplierId == supplierId);

                if (branchId.HasValue)
                    query = query.Where(p => p.BranchId == branchId);

                if (fromDate.HasValue)
                    query = query.Where(p => p.PaymentDate >= fromDate);

                if (toDate.HasValue)
                    query = query.Where(p => p.PaymentDate <= toDate);

                var totalCount = await query.CountAsync();
                var payments = await query
                    .OrderByDescending(p => p.PaymentDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new
                    {
                        p.Id,
                        p.PaymentNumber,
                        p.PaymentDate,
                        p.SupplierId,
                        SupplierName = p.Supplier != null ? p.Supplier.NameAr : "نقدي",
                        p.Amount,
                        p.Description,
                        p.ReferenceNumber,
                        PaymentMethodName = p.PaymentMethod.NameAr,
                        BranchName = p.Branch.NameAr,
                        UserName = p.User.FullName,
                        p.Status,
                        StatusName = p.Status == 1 ? "مكتمل" : "ملغي",
                        p.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = payments,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateAccountCode(int? parentId, int accountType)
        {
            if (parentId.HasValue)
            {
                var parent = await _context.ChartOfAccounts.FindAsync(parentId);
                if (parent != null)
                {
                    var childCount = await _context.ChartOfAccounts
                        .CountAsync(coa => coa.ParentId == parentId);
                    return $"{parent.AccountCode}{(childCount + 1):D2}";
                }
            }

            // Root level account
            var rootCount = await _context.ChartOfAccounts
                .CountAsync(coa => coa.ParentId == null && coa.AccountType == accountType);
            return $"{accountType}{(rootCount + 1):D3}";
        }

        private async Task<int> CalculateAccountLevel(int? parentId)
        {
            if (!parentId.HasValue)
                return 1;

            var parent = await _context.ChartOfAccounts.FindAsync(parentId);
            return parent != null ? parent.Level + 1 : 1;
        }

        private async Task<string> GenerateJournalEntryNumber()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == "JOURNAL_ENTRY");

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = "JOURNAL_ENTRY",
                    Prefix = "JE",
                    CurrentValue = 1,
                    NumberLength = 8,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private async Task<string> GenerateReceiptNumber(int branchId)
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.Receipt && c.BranchId == branchId);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.Receipt,
                    Prefix = "REC",
                    CurrentValue = 1,
                    NumberLength = 8,
                    BranchId = branchId,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : null;
        }
    }

    // DTOs
    public class CreateAccountRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int AccountType { get; set; }
        public int? ParentId { get; set; }
        public bool AllowPosting { get; set; } = true;
        public string? Description { get; set; }
    }

    public class CreateJournalEntryRequest
    {
        public DateTime? EntryDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public int TransactionType { get; set; } = 1;
        public List<CreateJournalEntryDetailRequest> Details { get; set; } = new();
    }

    public class CreateJournalEntryDetailRequest
    {
        public int AccountId { get; set; }
        public string? Description { get; set; }
        public decimal DebitAmount { get; set; } = 0;
        public decimal CreditAmount { get; set; } = 0;
        public int? CostCenterId { get; set; }
        public string? Reference { get; set; }
    }

    public class PostJournalEntryRequest
    {
        public bool FinalPost { get; set; } = false;
    }

    public class CreateReceiptRequest
    {
        public DateTime? ReceiptDate { get; set; }
        public int? CustomerId { get; set; }
        public int BranchId { get; set; }
        public int PaymentMethodId { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? ReferenceNumber { get; set; }
        public string? BankName { get; set; }
        public string? AccountNumber { get; set; }
        public string? Currency { get; set; }
        public decimal? ExchangeRate { get; set; }
    }
}
