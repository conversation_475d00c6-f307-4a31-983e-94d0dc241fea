using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// معاملات العملاء المالية
    /// </summary>
    public class CustomerTransaction : BaseEntity
    {
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// رقم المعاملة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string TransactionNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ المعاملة
        /// </summary>
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// نوع المعاملة (دائن/مدين)
        /// </summary>
        public TransactionType TransactionType { get; set; }

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// الرصيد قبل المعاملة
        /// </summary>
        public decimal BalanceBefore { get; set; }

        /// <summary>
        /// الرصيد بعد المعاملة
        /// </summary>
        public decimal BalanceAfter { get; set; }

        /// <summary>
        /// وصف المعاملة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// مرجع المعاملة (رقم الفاتورة مثلاً)
        /// </summary>
        [MaxLength(50)]
        public string? Reference { get; set; }

        /// <summary>
        /// نوع المرجع (فاتورة مبيعات، سند قبض، إلخ)
        /// </summary>
        public ReferenceType? ReferenceType { get; set; }

        /// <summary>
        /// معرف المرجع في الجدول المرتبط
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public int? PaymentMethodId { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أدخل المعاملة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// هل المعاملة مؤكدة
        /// </summary>
        public bool IsConfirmed { get; set; } = true;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد المعاملة
        /// </summary>
        public int? ConfirmedById { get; set; }

        // Navigation Properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual PaymentMethod? PaymentMethod { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
    }

    /// <summary>
    /// نوع المعاملة
    /// </summary>
    public enum TransactionType
    {
        /// <summary>
        /// دائن (إيداع)
        /// </summary>
        Credit = 1,

        /// <summary>
        /// مدين (سحب)
        /// </summary>
        Debit = 2
    }

    /// <summary>
    /// نوع المرجع
    /// </summary>
    public enum ReferenceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        SalesInvoice = 1,

        /// <summary>
        /// سند قبض
        /// </summary>
        Receipt = 2,

        /// <summary>
        /// سند دفع
        /// </summary>
        Payment = 3,

        /// <summary>
        /// مرتجع مبيعات
        /// </summary>
        SalesReturn = 4,

        /// <summary>
        /// تسوية رصيد
        /// </summary>
        BalanceAdjustment = 5,

        /// <summary>
        /// رصيد افتتاحي
        /// </summary>
        OpeningBalance = 6
    }
}
