@echo off
title Terra Retail ERP - Egyptian Professional System
color 0A
chcp 65001 >nul

echo.
echo ========================================
echo    🏪 Terra Retail ERP - Egyptian Edition
echo    نظام إدارة احترافي للمتاجر المصرية
echo ========================================
echo.

echo 🔧 Preparing Egyptian professional system...
echo تحضير النظام الاحترافي المصري...
echo.

echo 📊 Checking Egyptian database...
echo فحص قاعدة البيانات المصرية...
sqlcmd -S localhost -U sa -P "@a123admin4" -Q "USE TerraRetailERP; SELECT COUNT(*) as EgyptianBranches FROM Branches WHERE NameAr LIKE N'%%القاهرة%%' OR NameAr LIKE N'%%الإسكندرية%%'" -h -1 >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ Egyptian database ready
    echo ✅ قاعدة البيانات المصرية جاهزة
) else (
    echo ❌ Database setup required
    echo ❌ يتطلب إعداد قاعدة البيانات
    echo.
    echo Running Egyptian database setup...
    echo تشغيل إعداد قاعدة البيانات المصرية...
    cd database
    sqlcmd -S localhost -U sa -P "@a123admin4" -i convert_to_egyptian.sql
    cd ..
)

echo.
echo 🚀 Starting Egyptian API Server...
echo تشغيل خادم API المصري...
start "Terra Retail Egyptian API" /min cmd /c "cd src\Terra.Retail.API && echo Starting Egyptian API Server... && dotnet run --urls http://localhost:5000"

echo ⏳ Waiting for API to initialize...
echo انتظار تهيئة API...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 Starting Egyptian Angular Application...
echo تشغيل تطبيق Angular المصري...
start "Terra Retail Egyptian Web" cmd /c "cd src\Terra.Retail.Web && echo Starting Egyptian Angular App... && ng serve --port 4200 --open"

echo.
echo ⏳ Waiting for Angular to compile...
echo انتظار تجميع Angular...
timeout /t 20 /nobreak >nul

echo.
echo ========================================
echo ✅ Egyptian Professional System Started!
echo ✅ تم تشغيل النظام الاحترافي المصري!
echo ========================================
echo.
echo 📍 Access URLs | روابط الوصول:
echo.
echo 🔗 Login Page:    http://localhost:4200/login
echo 🔗 Dashboard:     http://localhost:4200/dashboard
echo 🔗 Customers:     http://localhost:4200/customers
echo 🔗 Products:      http://localhost:4200/products
echo 🔗 POS:           http://localhost:4200/pos
echo 🔗 API Server:    http://localhost:5000  
echo 📖 API Docs:      http://localhost:5000/swagger
echo 🏥 Health Check:  http://localhost:5000/health
echo.
echo 👤 Demo Login Credentials | بيانات الدخول التجريبية:
echo Username: admin
echo Password: admin123
echo.
echo 🏢 Egyptian Branches | الفروع المصرية:
echo - الفرع الرئيسي - القاهرة (Main Branch - Cairo)
echo - فرع الإسكندرية (Alexandria Branch)
echo - فرع الجيزة (Giza Branch)
echo - فرع المنصورة (Mansoura Branch)
echo - فرع أسيوط (Assiut Branch)
echo - فرع طنطا (Tanta Branch)
echo - فرع الزقازيق (Zagazig Branch)
echo - فرع أسوان (Aswan Branch)
echo.
echo 🇪🇬 Egyptian Features | الميزات المصرية:
echo ✅ Egyptian Governorates (26 محافظة مصرية)
echo ✅ Egyptian Payment Methods (فودافون كاش، أورانج موني، فوري)
echo ✅ Egyptian Customer Types (عميل عادي، جملة، تاجر)
echo ✅ Professional Sidebar Navigation
echo ✅ Beautiful Customer Management Page
echo ✅ Material Design UI Components
echo ✅ Multi-language Support (Arabic/English)
echo ✅ Role-based Authentication System
echo.

echo 🌐 Opening Egyptian Login Page...
echo فتح صفحة الدخول المصرية...
timeout /t 5 /nobreak >nul
start http://localhost:4200/login

echo.
echo ========================================
echo 🎉 Terra Retail ERP Egyptian Edition Ready!
echo 🎉 Terra Retail ERP النسخة المصرية جاهزة!
echo ========================================
echo.
echo Press any key to view system status...
echo اضغط أي مفتاح لعرض حالة النظام...
pause >nul

echo.
echo 📊 Egyptian System Status | حالة النظام المصري:
echo.

echo Testing API Health...
echo اختبار صحة API...
curl -s http://localhost:5000/health >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Egyptian API Server is running
    echo ✅ خادم API المصري يعمل
) else (
    echo ❌ API not responding
    echo ❌ API لا يستجيب
)

echo.
echo Testing Egyptian Angular App...
echo اختبار تطبيق Angular المصري...
curl -s http://localhost:4200 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Egyptian Angular App is running
    echo ✅ تطبيق Angular المصري يعمل
) else (
    echo ⏳ Egyptian Angular App still loading...
    echo ⏳ تطبيق Angular المصري لا يزال يحمل...
)

echo.
echo Testing Customer Management...
echo اختبار إدارة العملاء...
curl -s http://localhost:4200/customers >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Customer Management Page is accessible
    echo ✅ صفحة إدارة العملاء متاحة
) else (
    echo ⏳ Customer Page still loading...
    echo ⏳ صفحة العملاء لا تزال تحمل...
)

echo.
echo 🎯 Egyptian Professional System is ready for business!
echo 🎯 النظام الاحترافي المصري جاهز للعمل!
echo.
echo 📋 Next Steps | الخطوات التالية:
echo 1. Login with admin/admin123
echo 2. Select Egyptian branch (اختر فرع مصري)
echo 3. Explore the professional dashboard
echo 4. Check the beautiful customer management
echo 5. Start managing your Egyptian business!
echo.
echo 🇪🇬 Welcome to Terra Retail ERP Egyptian Edition!
echo 🇪🇬 أهلاً بك في Terra Retail ERP النسخة المصرية!
echo.

pause
