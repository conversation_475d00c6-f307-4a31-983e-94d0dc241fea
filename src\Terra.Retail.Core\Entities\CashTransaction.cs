using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// حركات الخزينة
    /// </summary>
    public class CashTransaction : BaseEntity
    {
        /// <summary>
        /// معرف الخزينة
        /// </summary>
        public int CashBoxId { get; set; }

        /// <summary>
        /// رقم الحركة
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string TransactionNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الحركة
        /// </summary>
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// نوع الحركة
        /// </summary>
        public CashTransactionType TransactionType { get; set; }

        /// <summary>
        /// المبلغ (موجب للداخل، سالب للخارج)
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// الرصيد قبل الحركة
        /// </summary>
        public decimal BalanceBefore { get; set; }

        /// <summary>
        /// الرصيد بعد الحركة
        /// </summary>
        public decimal BalanceAfter { get; set; }

        /// <summary>
        /// وصف الحركة
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// مرجع الحركة
        /// </summary>
        [MaxLength(50)]
        public string? Reference { get; set; }

        /// <summary>
        /// نوع المرجع
        /// </summary>
        public CashReferenceType? ReferenceType { get; set; }

        /// <summary>
        /// معرف المرجع في الجدول المرتبط
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public int? PaymentMethodId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أدخل الحركة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// الطرف الآخر (اسم العميل أو المورد)
        /// </summary>
        [MaxLength(200)]
        public string? CounterParty { get; set; }

        /// <summary>
        /// رقم الإيصال
        /// </summary>
        [MaxLength(20)]
        public string? ReceiptNumber { get; set; }

        /// <summary>
        /// هل الحركة مؤكدة
        /// </summary>
        public bool IsConfirmed { get; set; } = true;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أكد الحركة
        /// </summary>
        public int? ConfirmedById { get; set; }

        /// <summary>
        /// العملة
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "SAR";

        /// <summary>
        /// سعر الصرف
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// المبلغ بالعملة الأساسية
        /// </summary>
        public decimal BaseAmount { get; set; }

        // Navigation Properties
        public virtual CashBox CashBox { get; set; } = null!;
        public virtual PaymentMethod? PaymentMethod { get; set; }
        public virtual User User { get; set; } = null!;
        public virtual User? ConfirmedBy { get; set; }
    }

    /// <summary>
    /// أنواع حركات الخزينة
    /// </summary>
    public enum CashTransactionType
    {
        /// <summary>
        /// رصيد افتتاحي
        /// </summary>
        OpeningBalance = 1,

        /// <summary>
        /// إيداع
        /// </summary>
        Deposit = 2,

        /// <summary>
        /// سحب
        /// </summary>
        Withdrawal = 3,

        /// <summary>
        /// تحويل داخل
        /// </summary>
        TransferIn = 4,

        /// <summary>
        /// تحويل خارج
        /// </summary>
        TransferOut = 5,

        /// <summary>
        /// مقبوضات مبيعات
        /// </summary>
        SalesReceipt = 6,

        /// <summary>
        /// مدفوعات مشتريات
        /// </summary>
        PurchasePayment = 7,

        /// <summary>
        /// مصروفات
        /// </summary>
        Expense = 8,

        /// <summary>
        /// إيرادات أخرى
        /// </summary>
        OtherIncome = 9,

        /// <summary>
        /// تسوية
        /// </summary>
        Adjustment = 10
    }

    /// <summary>
    /// أنواع مراجع حركات الخزينة
    /// </summary>
    public enum CashReferenceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        SalesInvoice = 1,

        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        PurchaseInvoice = 2,

        /// <summary>
        /// سند قبض
        /// </summary>
        Receipt = 3,

        /// <summary>
        /// سند دفع
        /// </summary>
        Payment = 4,

        /// <summary>
        /// تحويل خزينة
        /// </summary>
        CashTransfer = 5,

        /// <summary>
        /// مصروف
        /// </summary>
        Expense = 6,

        /// <summary>
        /// إيراد آخر
        /// </summary>
        OtherIncome = 7
    }
}
