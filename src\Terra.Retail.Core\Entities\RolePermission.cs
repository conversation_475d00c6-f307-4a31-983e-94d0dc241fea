using System.ComponentModel.DataAnnotations;

namespace Terra.Retail.Core.Entities
{
    /// <summary>
    /// ربط الأدوار بالصلاحيات
    /// </summary>
    public class RolePermission : BaseEntity
    {
        /// <summary>
        /// معرف الدور
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// معرف الصلاحية
        /// </summary>
        public int PermissionId { get; set; }

        /// <summary>
        /// هل الصلاحية ممنوحة
        /// </summary>
        public bool IsGranted { get; set; } = true;

        /// <summary>
        /// تاريخ منح الصلاحية
        /// </summary>
        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// معرف المستخدم الذي منح الصلاحية
        /// </summary>
        public int? GrantedById { get; set; }

        /// <summary>
        /// قيود إضافية على الصلاحية (JSON)
        /// </summary>
        [MaxLength(1000)]
        public string? Constraints { get; set; }

        /// <summary>
        /// ملاحظات حول منح الصلاحية
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Role Role { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual User? GrantedBy { get; set; }
    }
}
