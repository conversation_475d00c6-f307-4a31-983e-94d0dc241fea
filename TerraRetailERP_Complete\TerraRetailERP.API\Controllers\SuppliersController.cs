using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TerraRetailERP.API.Data;
using TerraRetailERP.API.Models;

namespace TerraRetailERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Tags("🏭 Supplier Management")]
    public class SuppliersController : ControllerBase
    {
        private readonly TerraRetailDbContext _context;

        public SuppliersController(TerraRetailDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Supplier>>> GetSuppliers(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? search = null,
            [FromQuery] int? supplierTypeId = null,
            [FromQuery] int? countryId = null,
            [FromQuery] bool? isActive = null)
        {
            try
            {
                var query = _context.Suppliers
                    .Include(s => s.SupplierType)
                    .Include(s => s.Area)
                    .Include(s => s.Country)
                    .Where(s => !s.IsDeleted)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(s => s.NameAr.Contains(search) || 
                                           s.NameEn!.Contains(search) || 
                                           s.SupplierCode.Contains(search) ||
                                           s.Phone1!.Contains(search) ||
                                           s.Email!.Contains(search));
                }

                if (supplierTypeId.HasValue)
                    query = query.Where(s => s.SupplierTypeId == supplierTypeId);

                if (countryId.HasValue)
                    query = query.Where(s => s.CountryId == countryId);

                if (isActive.HasValue)
                    query = query.Where(s => s.IsActive == isActive);

                var totalCount = await query.CountAsync();
                var suppliers = await query
                    .OrderBy(s => s.NameAr)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return Ok(new
                {
                    data = suppliers,
                    totalCount,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Supplier>> GetSupplier(int id)
        {
            try
            {
                var supplier = await _context.Suppliers
                    .Include(s => s.SupplierType)
                    .Include(s => s.Area)
                    .Include(s => s.Country)
                    .Include(s => s.ChartAccount)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (supplier == null)
                    return NotFound(new { message = "المورد غير موجود" });

                return Ok(supplier);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<Supplier>> CreateSupplier(CreateSupplierRequest request)
        {
            try
            {
                // Generate supplier code
                var supplierCode = await GenerateSupplierCode();

                var supplier = new Supplier
                {
                    SupplierCode = supplierCode,
                    NameAr = request.NameAr,
                    NameEn = request.NameEn,
                    SupplierTypeId = request.SupplierTypeId,
                    Phone1 = request.Phone1,
                    Phone2 = request.Phone2,
                    Email = request.Email,
                    Website = request.Website,
                    Address = request.Address,
                    AreaId = request.AreaId,
                    CountryId = request.CountryId,
                    ContactPersonName = request.ContactPersonName,
                    ContactPersonPhone = request.ContactPersonPhone,
                    ContactPersonEmail = request.ContactPersonEmail,
                    PaymentTerms = request.PaymentTerms,
                    CreditLimit = request.CreditLimit,
                    OpeningBalance = request.OpeningBalance,
                    CurrentBalance = request.OpeningBalance,
                    TaxNumber = request.TaxNumber,
                    CommercialRegister = request.CommercialRegister,
                    BankName = request.BankName,
                    BankAccountNumber = request.BankAccountNumber,
                    IBAN = request.IBAN,
                    DeliveryDays = request.DeliveryDays,
                    Notes = request.Notes,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System", // TODO: Get from current user
                    IsDeleted = false
                };

                _context.Suppliers.Add(supplier);
                await _context.SaveChangesAsync();

                // Load related data for response
                await _context.Entry(supplier)
                    .Reference(s => s.SupplierType)
                    .LoadAsync();
                await _context.Entry(supplier)
                    .Reference(s => s.Area)
                    .LoadAsync();
                await _context.Entry(supplier)
                    .Reference(s => s.Country)
                    .LoadAsync();

                return CreatedAtAction(nameof(GetSupplier), new { id = supplier.Id }, supplier);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSupplier(int id, UpdateSupplierRequest request)
        {
            try
            {
                var supplier = await _context.Suppliers.FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
                if (supplier == null)
                    return NotFound(new { message = "المورد غير موجود" });

                supplier.NameAr = request.NameAr;
                supplier.NameEn = request.NameEn;
                supplier.SupplierTypeId = request.SupplierTypeId;
                supplier.Phone1 = request.Phone1;
                supplier.Phone2 = request.Phone2;
                supplier.Email = request.Email;
                supplier.Website = request.Website;
                supplier.Address = request.Address;
                supplier.AreaId = request.AreaId;
                supplier.CountryId = request.CountryId;
                supplier.ContactPersonName = request.ContactPersonName;
                supplier.ContactPersonPhone = request.ContactPersonPhone;
                supplier.ContactPersonEmail = request.ContactPersonEmail;
                supplier.PaymentTerms = request.PaymentTerms;
                supplier.CreditLimit = request.CreditLimit;
                supplier.TaxNumber = request.TaxNumber;
                supplier.CommercialRegister = request.CommercialRegister;
                supplier.BankName = request.BankName;
                supplier.BankAccountNumber = request.BankAccountNumber;
                supplier.IBAN = request.IBAN;
                supplier.DeliveryDays = request.DeliveryDays;
                supplier.Notes = request.Notes;
                supplier.Rating = request.Rating;
                supplier.IsActive = request.IsActive;
                supplier.UpdatedAt = DateTime.Now;
                supplier.UpdatedBy = "System"; // TODO: Get from current user

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث بيانات المورد بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSupplier(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
                if (supplier == null)
                    return NotFound(new { message = "المورد غير موجود" });

                // Check if supplier has transactions
                var hasTransactions = await _context.PurchaseInvoices.AnyAsync(p => p.SupplierId == id);
                if (hasTransactions)
                {
                    return BadRequest(new { message = "لا يمكن حذف المورد لوجود معاملات مرتبطة به" });
                }

                // Soft delete
                supplier.IsDeleted = true;
                supplier.UpdatedAt = DateTime.Now;
                supplier.UpdatedBy = "System"; // TODO: Get from current user

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المورد بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("types")]
        public async Task<ActionResult<IEnumerable<SupplierType>>> GetSupplierTypes()
        {
            try
            {
                var types = await _context.SupplierTypes
                    .Where(st => st.IsActive && !st.IsDeleted)
                    .OrderBy(st => st.DisplayOrder)
                    .ToListAsync();

                return Ok(types);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("balance/{id}")]
        public async Task<ActionResult> GetSupplierBalance(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
                if (supplier == null)
                    return NotFound(new { message = "المورد غير موجود" });

                // Calculate current balance from transactions
                var purchasesTotal = await _context.PurchaseInvoices
                    .Where(p => p.SupplierId == id && p.Status != 3) // Not cancelled
                    .SumAsync(p => p.RemainingAmount);

                var paymentsTotal = await _context.Payments
                    .Where(p => p.SupplierId == id && p.Status == 1)
                    .SumAsync(p => p.Amount);

                var currentBalance = supplier.OpeningBalance + purchasesTotal - paymentsTotal;

                return Ok(new
                {
                    supplierId = id,
                    supplierName = supplier.NameAr,
                    openingBalance = supplier.OpeningBalance,
                    purchasesTotal,
                    paymentsTotal,
                    currentBalance,
                    creditLimit = supplier.CreditLimit,
                    availableCredit = supplier.CreditLimit - currentBalance,
                    paymentTerms = supplier.PaymentTerms,
                    rating = supplier.Rating
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        [HttpGet("{id}/products")]
        public async Task<ActionResult> GetSupplierProducts(int id)
        {
            try
            {
                var products = await _context.ProductSuppliers
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Category)
                    .Include(ps => ps.Product)
                        .ThenInclude(p => p.Unit)
                    .Where(ps => ps.SupplierId == id && ps.IsActive && !ps.IsDeleted)
                    .Select(ps => new
                    {
                        ps.Id,
                        ps.ProductId,
                        ProductCode = ps.Product.ProductCode,
                        ProductNameAr = ps.Product.NameAr,
                        ProductNameEn = ps.Product.NameEn,
                        CategoryName = ps.Product.Category.NameAr,
                        UnitName = ps.Product.Unit.NameAr,
                        ps.SupplierProductCode,
                        ps.SupplierProductName,
                        ps.PurchasePrice,
                        ps.MinOrderQuantity,
                        ps.LeadTimeDays,
                        ps.IsPreferred
                    })
                    .ToListAsync();

                return Ok(products);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ في النظام", error = ex.Message });
            }
        }

        private async Task<string> GenerateSupplierCode()
        {
            var counter = await _context.Counters
                .FirstOrDefaultAsync(c => c.CounterName == CounterTypes.Supplier);

            if (counter == null)
            {
                counter = new Counter
                {
                    CounterName = CounterTypes.Supplier,
                    Prefix = "SUP",
                    CurrentValue = 1,
                    NumberLength = 6,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                _context.Counters.Add(counter);
            }
            else
            {
                counter.CurrentValue++;
                counter.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            return $"{counter.Prefix}{counter.CurrentValue.ToString().PadLeft(counter.NumberLength, '0')}";
        }
    }

    // DTOs
    public class CreateSupplierRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int SupplierTypeId { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public int? CountryId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? ContactPersonPhone { get; set; }
        public string? ContactPersonEmail { get; set; }
        public int PaymentTerms { get; set; } = 30;
        public decimal CreditLimit { get; set; } = 0;
        public decimal OpeningBalance { get; set; } = 0;
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? IBAN { get; set; }
        public int? DeliveryDays { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateSupplierRequest
    {
        public string NameAr { get; set; } = string.Empty;
        public string? NameEn { get; set; }
        public int SupplierTypeId { get; set; }
        public string? Phone1 { get; set; }
        public string? Phone2 { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? Address { get; set; }
        public int? AreaId { get; set; }
        public int? CountryId { get; set; }
        public string? ContactPersonName { get; set; }
        public string? ContactPersonPhone { get; set; }
        public string? ContactPersonEmail { get; set; }
        public int PaymentTerms { get; set; } = 30;
        public decimal CreditLimit { get; set; } = 0;
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? IBAN { get; set; }
        public int? DeliveryDays { get; set; }
        public string? Notes { get; set; }
        public int? Rating { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
