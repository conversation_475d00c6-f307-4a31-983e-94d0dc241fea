<!-- صفحة إضافة/تعديل العميل -->
<div class="customer-form-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-user-plus" *ngIf="!editMode"></i>
        <i class="fas fa-user-edit" *ngIf="editMode"></i>
        {{ editMode ? 'تعديل العميل' : 'إضافة عميل جديد' }}
      </h1>
      <p class="page-subtitle">{{ editMode ? 'تعديل بيانات العميل' : 'إدخال بيانات العميل الجديد' }}</p>
    </div>
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="goBack()">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
      </button>
    </div>
  </div>

  <!-- Customer Form -->
  <form [formGroup]="customerForm" (ngSubmit)="onSubmit()" class="customer-form">
    <div class="form-sections">
      
      <!-- البيانات الأساسية -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-user"></i>
          البيانات الأساسية
        </h3>
        
        <div class="form-row">
          <!-- كود العميل -->
          <div class="form-group">
            <label class="form-label required">كود العميل</label>
            <input
              type="text"
              class="form-control"
              formControlName="customerCode"
              [readonly]="true"
              placeholder="سيتم إنشاؤه تلقائياً عند الحفظ"
            >
            <small class="form-text">سيتم إنشاء الكود تلقائياً عند حفظ العميل</small>
          </div>

          <!-- نوع العميل -->
          <div class="form-group">
            <label class="form-label required">نوع العميل</label>
            <div class="input-with-add">
              <select [class]="getFieldClasses('customerTypeId')" formControlName="customerTypeId">
                <option value="">اختر نوع العميل</option>
                <option *ngFor="let type of customerTypes" [value]="type.id">{{ type.nameAr }}</option>
              </select>
              <button type="button" class="btn btn-outline-primary btn-add" (click)="openAddCustomerTypeModal()" title="إضافة نوع عميل جديد">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="invalid-feedback" *ngIf="isFieldInvalid('customerTypeId')">
              يرجى اختيار نوع العميل
            </div>
          </div>
        </div>

        <div class="form-row">
          <!-- الاسم بالعربية -->
          <div class="form-group">
            <label class="form-label required">الاسم بالعربية</label>
            <input
              type="text"
              [class]="getFieldClasses('nameAr')"
              formControlName="nameAr"
              placeholder="أدخل الاسم بالعربية"
              (input)="onArabicNameChange($event)"
            >
            <div class="invalid-feedback" *ngIf="isFieldInvalid('nameAr')">
              الاسم بالعربية مطلوب
            </div>
          </div>

          <!-- الاسم بالإنجليزية -->
          <div class="form-group">
            <label class="form-label required">الاسم بالإنجليزية</label>
            <input
              type="text"
              [class]="getFieldClasses('nameEn')"
              formControlName="nameEn"
              placeholder="سيتم إنشاؤه تلقائياً من الاسم العربي"
            >
            <div class="invalid-feedback" *ngIf="isFieldInvalid('nameEn')">
              الاسم بالإنجليزية مطلوب
            </div>
            <small class="form-text">سيتم تحويل الاسم العربي تلقائياً</small>
          </div>
        </div>
      </div>

      <!-- بيانات الاتصال -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-phone"></i>
          بيانات الاتصال
        </h3>
        
        <div class="form-row">
          <!-- رقم الهاتف الأساسي -->
          <div class="form-group">
            <label class="form-label required">رقم الهاتف الأساسي</label>
            <input 
              type="tel" 
              class="form-control" 
              formControlName="phone1"
              placeholder="01xxxxxxxxx"
              [class.is-invalid]="isFieldInvalid('phone1')"
            >
            <div class="invalid-feedback" *ngIf="isFieldInvalid('phone1')">
              رقم الهاتف الأساسي مطلوب
            </div>
          </div>

          <!-- رقم الهاتف الثانوي -->
          <div class="form-group">
            <label class="form-label">رقم الهاتف الثانوي</label>
            <input 
              type="tel" 
              class="form-control" 
              formControlName="phone2"
              placeholder="01xxxxxxxxx (اختياري)"
            >
          </div>
        </div>

        <div class="form-row">
          <!-- البريد الإلكتروني -->
          <div class="form-group">
            <label class="form-label">البريد الإلكتروني</label>
            <input 
              type="email" 
              class="form-control" 
              formControlName="email"
              placeholder="<EMAIL>"
              [class.is-invalid]="isFieldInvalid('email')"
            >
            <div class="invalid-feedback" *ngIf="isFieldInvalid('email')">
              يرجى إدخال بريد إلكتروني صحيح
            </div>
          </div>

          <!-- عرفتنا عن طريق -->
          <div class="form-group">
            <label class="form-label required">عرفتنا عن طريق</label>
            <div class="input-with-add">
              <select class="form-control" formControlName="referralSource">
                <option value="">اختر المصدر</option>
                <option value="social_media">وسائل التواصل الاجتماعي</option>
                <option value="friend">صديق أو معارف</option>
                <option value="advertisement">إعلان</option>
                <option value="website">الموقع الإلكتروني</option>
                <option value="exhibition">معرض</option>
                <option value="other">أخرى</option>
              </select>
              <button type="button" class="btn btn-outline-primary btn-add" (click)="openAddReferralSourceModal()" title="إضافة مصدر جديد">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="invalid-feedback" *ngIf="isFieldInvalid('referralSource')">
              يرجى اختيار مصدر التعرف
            </div>
          </div>
        </div>

        <!-- العنوان -->
        <div class="form-group">
          <label class="form-label">العنوان</label>
          <textarea
            class="form-control"
            formControlName="address"
            rows="3"
            placeholder="أدخل العنوان التفصيلي"
          ></textarea>
        </div>

        <div class="form-row">
          <!-- البلد -->
          <div class="form-group">
            <label class="form-label">البلد</label>
            <div class="input-with-add">
              <select class="form-control" formControlName="countryId" (change)="onCountryChange()">
                <option value="">اختر البلد</option>
                <option *ngFor="let country of countries" [value]="country.id">{{ country.nameAr }}</option>
              </select>
              <button type="button" class="btn btn-outline-primary btn-add" (click)="openAddCountryModal()" title="إضافة بلد جديد">
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </div>

          <!-- المنطقة/المدينة -->
          <div class="form-group">
            <label class="form-label">المنطقة/المدينة</label>
            <div class="input-with-add">
              <select class="form-control" formControlName="cityId" [disabled]="!selectedCountryId">
                <option value="">اختر المنطقة</option>
                <option *ngFor="let city of filteredCities" [value]="city.id">{{ city.nameAr }}</option>
              </select>
              <button type="button" class="btn btn-outline-primary btn-add" (click)="openAddCityModal()" title="إضافة منطقة جديدة" [disabled]="!selectedCountryId">
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- الإعدادات المالية -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-money-bill-wave"></i>
          الإعدادات المالية
        </h3>
        
        <div class="form-row">
          <!-- فئة السعر -->
          <div class="form-group">
            <label class="form-label required">فئة السعر</label>
            <select class="form-control" formControlName="priceCategoryId">
              <option value="">اختر فئة السعر</option>
              <option *ngFor="let category of priceCategories" [value]="category.id">
                {{ category.nameAr }} (خصم {{ category.discountPercentage }}%)
              </option>
            </select>
            <div class="invalid-feedback" *ngIf="isFieldInvalid('priceCategoryId')">
              يرجى اختيار فئة السعر
            </div>
            <small class="form-text">
              <i class="fas fa-info-circle"></i>
              سيتم تعيين فئة السعر الافتراضية للفرع تلقائياً
            </small>
          </div>

          <!-- حد الائتمان -->
          <div class="form-group">
            <label class="form-label">حد الائتمان</label>
            <input 
              type="number" 
              class="form-control" 
              formControlName="creditLimit"
              placeholder="0.00"
              min="0"
              step="0.01"
            >
            <small class="form-text">الحد الأقصى للمديونية المسموح بها</small>
          </div>
        </div>

        <!-- الرصيد الافتتاحي -->
        <div class="form-group">
          <label class="form-label">الرصيد الافتتاحي</label>
          <div class="input-group">
            <input 
              type="number" 
              class="form-control" 
              formControlName="openingBalance"
              placeholder="0.00"
              step="0.01"
            >
            <div class="input-group-append">
              <span class="input-group-text">جنيه</span>
            </div>
          </div>
          <small class="form-text">
            <i class="fas fa-info-circle"></i>
            سيتم إنشاء قيد يومية تلقائياً في النظام المحاسبي عند إدخال رصيد افتتاحي
          </small>
        </div>
      </div>

      <!-- إعدادات إضافية -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-cogs"></i>
          إعدادات إضافية
        </h3>
        
        <div class="form-row">
          <!-- الفرع -->
          <div class="form-group">
            <label class="form-label">الفرع المسؤول</label>
            <select class="form-control" formControlName="branchId">
              <option value="">اختر الفرع</option>
              <option *ngFor="let branch of branches" [value]="branch.id">{{ branch.nameAr }}</option>
            </select>
          </div>

          <!-- الحالة -->
          <div class="form-group">
            <div class="form-check">
              <input 
                type="checkbox" 
                class="form-check-input" 
                formControlName="isActive"
                id="isActive"
              >
              <label class="form-check-label" for="isActive">
                العميل نشط
              </label>
            </div>
          </div>
        </div>

        <!-- ملاحظات -->
        <div class="form-group">
          <label class="form-label">ملاحظات</label>
          <textarea 
            class="form-control" 
            formControlName="notes"
            rows="3"
            placeholder="أي ملاحظات إضافية عن العميل"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button type="submit" class="btn btn-primary" [disabled]="customerForm.invalid || isSubmitting">
        <i class="fas fa-save" *ngIf="!isSubmitting"></i>
        <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
        {{ isSubmitting ? 'جاري الحفظ...' : (editMode ? 'تحديث البيانات' : 'حفظ العميل') }}
      </button>
      
      <button type="button" class="btn btn-secondary" (click)="resetForm()" [disabled]="isSubmitting">
        <i class="fas fa-undo"></i>
        إعادة تعيين
      </button>
      
      <button type="button" class="btn btn-outline-secondary" (click)="goBack()" [disabled]="isSubmitting">
        <i class="fas fa-times"></i>
        إلغاء
      </button>
    </div>
  </form>
</div>
