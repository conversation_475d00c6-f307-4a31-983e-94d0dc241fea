using Microsoft.AspNetCore.Mvc;
using System.Data.SqlClient;
using Dapper;

namespace Terra.Retail.API.Controllers
{
    [ApiController]
    [Route("api/simple")]
    public class CountriesController : ControllerBase
    {
        private readonly string _connectionString;

        public CountriesController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? "";
        }

        /// <summary>
        /// Get countries from database
        /// </summary>
        [HttpGet("countries-db")]
        public async Task<ActionResult> GetCountriesFromDatabase()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var countries = await connection.QueryAsync(@"
                    SELECT Id, NameAr, NameEn, Code, PhoneCode, IsActive
                    FROM Countries
                    WHERE IsActive = 1
                    ORDER BY NameAr");

                return Ok(new { countries = countries.ToList() });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطأ في تحميل البلدان", error = ex.Message });
            }
        }

        /// <summary>
        /// Add new country
        /// </summary>
        [HttpPost("countries")]
        public async Task<ActionResult> AddCountry([FromBody] dynamic request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    INSERT INTO Countries (NameAr, NameEn, Code, PhoneCode, IsActive, CreatedAt)
                    VALUES (@NameAr, @NameEn, @Code, @PhoneCode, @IsActive, GETDATE());
                    SELECT SCOPE_IDENTITY();";

                var newId = await connection.QuerySingleAsync<int>(query, new {
                    NameAr = (string)request.nameAr,
                    NameEn = (string)request.nameEn,
                    Code = (string)request.code,
                    PhoneCode = (string)request.phoneCode,
                    IsActive = (bool)request.isActive
                });

                return Ok(new {
                    id = newId,
                    message = "تم إضافة البلد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في إضافة البلد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Update country
        /// </summary>
        [HttpPut("countries/{id}")]
        public async Task<ActionResult> UpdateCountry(int id, [FromBody] dynamic request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    UPDATE Countries 
                    SET NameAr = @NameAr, NameEn = @NameEn, Code = @Code, 
                        PhoneCode = @PhoneCode, IsActive = @IsActive, UpdatedAt = GETDATE()
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(query, new {
                    Id = id,
                    NameAr = (string)request.nameAr,
                    NameEn = (string)request.nameEn,
                    Code = (string)request.code,
                    PhoneCode = (string)request.phoneCode,
                    IsActive = (bool)request.isActive
                });

                if (rowsAffected > 0)
                {
                    return Ok(new { message = "تم تحديث البلد بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "البلد غير موجود" });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في تحديث البلد",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Delete country
        /// </summary>
        [HttpDelete("countries/{id}")]
        public async Task<ActionResult> DeleteCountry(int id)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    UPDATE Countries 
                    SET IsActive = 0, UpdatedAt = GETDATE()
                    WHERE Id = @Id";

                var rowsAffected = await connection.ExecuteAsync(query, new { Id = id });

                if (rowsAffected > 0)
                {
                    return Ok(new { message = "تم حذف البلد بنجاح" });
                }
                else
                {
                    return NotFound(new { message = "البلد غير موجود" });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new {
                    message = "خطأ في حذف البلد",
                    error = ex.Message
                });
            }
        }
    }
}
