using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TerraRetailERP.API.Models
{
    [Table("Users")]
    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(510)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(40)]
        public string? PhoneNumber { get; set; }

        [StringLength(1000)]
        public string? ProfileImage { get; set; }

        public int? DefaultBranchId { get; set; }
        public int? EmployeeId { get; set; }

        public bool IsActive { get; set; } = true;
        public bool IsEmailConfirmed { get; set; } = false;

        [StringLength(510)]
        public string? EmailConfirmationToken { get; set; }

        [StringLength(510)]
        public string? PasswordResetToken { get; set; }

        public DateTime? PasswordResetExpiry { get; set; }
        public DateTime? LastLoginAt { get; set; }

        [StringLength(90)]
        public string? LastLoginIP { get; set; }

        public int LoginAttempts { get; set; } = 0;
        public DateTime? LockedUntil { get; set; }

        [Required]
        [StringLength(10)]
        public string PreferredLanguage { get; set; } = "ar";

        [Required]
        [StringLength(100)]
        public string TimeZone { get; set; } = "Africa/Cairo";

        [StringLength(4000)]
        public string? Settings { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("DefaultBranchId")]
        public virtual Branch? DefaultBranch { get; set; }

        [ForeignKey("EmployeeId")]
        public virtual Employee? Employee { get; set; }

        public virtual ICollection<UserBranch> UserBranches { get; set; } = new List<UserBranch>();
        public virtual ICollection<UserSession> UserSessions { get; set; } = new List<UserSession>();
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<AuditTrail> AuditTrails { get; set; } = new List<AuditTrail>();
    }
}
