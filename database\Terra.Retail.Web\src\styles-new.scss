/* Terra Retail ERP - Professional Theme */

/* ===== IMPORTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* ===== CSS VARIABLES - PROFESSIONAL THEME ===== */
:root {
  /* Primary Colors - Modern Blue Gradient */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors - Elegant Purple */
  --secondary-50: #faf5ff;
  --secondary-100: #f3e8ff;
  --secondary-200: #e9d5ff;
  --secondary-300: #d8b4fe;
  --secondary-400: #c084fc;
  --secondary-500: #a855f7;
  --secondary-600: #9333ea;
  --secondary-700: #7c3aed;
  --secondary-800: #6b21a8;
  --secondary-900: #581c87;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Spacing - Enhanced for better layout */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  --spacing-3xl: 4rem;      /* 64px */
  --spacing-4xl: 5rem;      /* 80px */
  --spacing-5xl: 6rem;      /* 96px */

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-family-primary: 'Tajawal', 'IBM Plex Sans Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'IBM Plex Sans Arabic', 'Tajawal', sans-serif;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== GLOBAL RESET ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Prevent layout issues */
img {
  max-width: 100%;
  height: auto;
}

/* Ensure proper spacing for all elements */
.mat-mdc-card,
.mat-mdc-form-field,
.mat-mdc-button,
div,
section,
article {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
  min-height: 100vh;
  direction: rtl;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html[dir="rtl"] {
  direction: rtl;
}

* {
  direction: inherit;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--gray-700);
}

/* ===== MATERIAL DESIGN OVERRIDES ===== */
.mat-mdc-form-field {
  width: 100% !important;
  margin-bottom: var(--spacing-lg) !important;
  font-family: var(--font-family-primary) !important;

  .mat-mdc-form-field-wrapper {
    width: 100% !important;
    padding-bottom: 0 !important;
  }

  .mat-mdc-form-field-flex {
    width: 100% !important;
    min-height: 56px !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-infix {
    width: 100% !important;
    padding: var(--spacing-md) var(--spacing-lg) !important;
    border: none !important;
    min-height: auto !important;
  }

  .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }

  .mat-mdc-text-field-wrapper {
    width: 100% !important;
    min-height: 56px !important;
    border: 2px solid var(--gray-200) !important;
    border-radius: var(--radius-lg) !important;
    background: white !important;
    transition: all var(--transition-normal) !important;

    &:hover {
      border-color: var(--primary-300) !important;
      box-shadow: var(--shadow-sm) !important;
    }

    &.mdc-text-field--focused {
      border-color: var(--primary-500) !important;
      box-shadow: 0 0 0 4px var(--primary-100) !important;
    }
  }

  .mat-mdc-select-trigger {
    width: 100% !important;
    min-height: 56px !important;
    display: flex !important;
    align-items: center !important;
    padding: 0 var(--spacing-md) !important;
  }

  .mat-mdc-select-value {
    width: 100% !important;
    font-family: var(--font-family-primary) !important;
  }

  .mat-mdc-select-arrow {
    color: var(--gray-500) !important;
  }

  input, textarea {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    font-family: var(--font-family-primary) !important;
    font-size: 1rem !important;
    color: var(--gray-800) !important;
    
    &::placeholder {
      color: var(--gray-400) !important;
    }
  }

  .mat-mdc-form-field-label {
    font-family: var(--font-family-primary) !important;
    color: var(--gray-600) !important;
  }
}

/* ===== BUTTONS ===== */
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-unelevated-button {
  min-height: 48px !important;
  padding: 0 var(--spacing-xl) !important;
  border-radius: var(--radius-lg) !important;
  font-family: var(--font-family-primary) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: all var(--transition-normal) !important;
  text-transform: none !important;
  
  .mat-icon {
    margin-left: var(--spacing-sm) !important;
    margin-right: 0 !important;
  }
}

.mat-mdc-raised-button {
  box-shadow: var(--shadow-md) !important;
  
  &:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-1px) !important;
  }
}

/* ===== CARDS ===== */
.mat-mdc-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--gray-200) !important;
  background: white !important;
  transition: all var(--transition-normal) !important;
  
  &:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-2px) !important;
  }
  
  .mat-mdc-card-content {
    padding: var(--spacing-xl) !important;
  }
}

/* ===== TABLES ===== */
.mat-mdc-table {
  width: 100% !important;
  background: white !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-sm) !important;
  
  .mat-mdc-header-row {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%) !important;
    
    .mat-mdc-header-cell {
      font-weight: 700 !important;
      color: var(--primary-800) !important;
      border-bottom: 2px solid var(--primary-200) !important;
      padding: var(--spacing-lg) !important;
      font-family: var(--font-family-primary) !important;
    }
  }
  
  .mat-mdc-row {
    transition: all var(--transition-fast) !important;
    
    &:hover {
      background: var(--primary-50) !important;
    }
    
    .mat-mdc-cell {
      padding: var(--spacing-lg) !important;
      border-bottom: 1px solid var(--gray-100) !important;
      font-family: var(--font-family-primary) !important;
    }
  }
}

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
  border-radius: var(--radius-md);
  
  &:hover {
    background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* ===== UTILITY CLASSES ===== */
.text-primary { color: var(--primary-600) !important; }
.text-secondary { color: var(--secondary-600) !important; }
.text-success { color: var(--success-600) !important; }
.text-warning { color: var(--warning-600) !important; }
.text-error { color: var(--error-600) !important; }

.bg-primary { background-color: var(--primary-500) !important; }
.bg-secondary { background-color: var(--secondary-500) !important; }
.bg-success { background-color: var(--success-500) !important; }
.bg-warning { background-color: var(--warning-500) !important; }
.bg-error { background-color: var(--error-500) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

/* ===== SPACING UTILITIES ===== */
.p-0 { padding: 0 !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.p-2xl { padding: var(--spacing-2xl) !important; }

.m-0 { margin: 0 !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }
.m-2xl { margin: var(--spacing-2xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }
.mb-2xl { margin-bottom: var(--spacing-2xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }
.mt-2xl { margin-top: var(--spacing-2xl) !important; }

/* ===== LAYOUT UTILITIES ===== */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.items-center { align-items: center !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.gap-xs { gap: var(--spacing-xs) !important; }
.gap-sm { gap: var(--spacing-sm) !important; }
.gap-md { gap: var(--spacing-md) !important; }
.gap-lg { gap: var(--spacing-lg) !important; }
.gap-xl { gap: var(--spacing-xl) !important; }

/* ===== OVERFLOW UTILITIES ===== */
.overflow-hidden { overflow: hidden !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-y-auto { overflow-y: auto !important; }

/* ===== SIMPLE DROPDOWN FIX - WHITE BACKGROUND ONLY ===== */
.mat-mdc-select-panel {
  background: white !important;
}

.mat-mdc-menu-panel {
  background: white !important;
}
