import { Routes } from '@angular/router';
import { Login } from './auth/login/login';
import { DashboardNewComponent } from './dashboard/dashboard-new.component';
import { CustomersNewComponent } from './modules/customers/customers-new.component';
import { CustomersComponent } from './modules/customers/customers.component';
import { SuppliersNewComponent } from './modules/suppliers/suppliers-new.component';
import { AddSupplierComponent } from './modules/suppliers/add-supplier/add-supplier.component';
import { EditSupplierComponent } from './modules/suppliers/edit-supplier/edit-supplier.component';
import { SupplierDetailsComponent } from './modules/suppliers/supplier-details/supplier-details.component';
import { SupplierManagementComponent } from './modules/suppliers/supplier-management/supplier-management.component';
import { SupplierStatsComponent } from './modules/suppliers/supplier-stats/supplier-stats.component';
import { SupplierTypesComponent } from './modules/suppliers/supplier-types/supplier-types.component';
import { AreasComponent } from './modules/suppliers/areas/areas.component';
import { CountriesComponent } from './modules/suppliers/countries/countries.component';
import { Products } from './pages/products/products';
import { AddProduct } from './pages/add-product/add-product';
import { Pos } from './pages/pos/pos';

export const routes: Routes = [
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { path: 'login', component: Login },
  { path: 'dashboard', component: DashboardNewComponent },
  { path: 'pos', component: Pos },
  { path: 'customers', component: CustomersComponent },
  { path: 'customers-old', component: CustomersNewComponent },
  { path: 'suppliers', component: SuppliersNewComponent },
  { path: 'suppliers/add', component: AddSupplierComponent },
  { path: 'suppliers/edit/:id', component: EditSupplierComponent },
  { path: 'suppliers/details/:id', component: SupplierDetailsComponent },
  { path: 'suppliers/management', component: SupplierManagementComponent },
  { path: 'suppliers/stats', component: SupplierStatsComponent },
  { path: 'suppliers/types', component: SupplierTypesComponent },
  { path: 'suppliers/areas', component: AreasComponent },
  { path: 'suppliers/countries', component: CountriesComponent },
  { path: 'products', component: Products },
  { path: 'add-product', component: AddProduct },
  { path: 'sales', component: DashboardNewComponent }, // Temporary
  { path: 'inventory', component: DashboardNewComponent }, // Temporary
  { path: 'suppliers', component: DashboardNewComponent }, // Temporary
  { path: 'purchases', component: DashboardNewComponent }, // Temporary
  { path: 'reports', component: DashboardNewComponent }, // Temporary
  { path: 'settings', component: DashboardNewComponent }, // Temporary
  { path: '**', redirectTo: '/dashboard' }
];
