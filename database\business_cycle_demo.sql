-- 🔄 دورة برنامج كاملة لسنتر مفروشات مصري
-- تشمل: مشتريات، مبيعات، مقبوضات، مدفوعات، قيود محاسبية

-- 📊 إنشاء دليل الحسابات الأساسي
INSERT INTO ChartOfAccounts (AccountCode, NameAr, NameEn, AccountType, ParentAccountId, IsActive, CreatedAt) VALUES
('1000', N'الأصول', 'Assets', 1, NULL, 1, GETDATE()),
('1100', N'النقدية والبنوك', 'Cash and Banks', 1, 1, 1, GETDATE()),
('1200', N'العملاء', 'Customers', 1, 1, 1, GETDATE()),
('1300', N'المخزون', 'Inventory', 1, 1, 1, GETDATE()),
('2000', N'الخصوم', 'Liabilities', 2, NULL, 1, GETDATE()),
('2100', N'الموردين', 'Suppliers', 2, 5, 1, GETDATE()),
('3000', N'حقوق الملكية', 'Equity', 3, NULL, 1, GETDATE()),
('4000', N'الإيرادات', 'Revenue', 4, NULL, 1, GETDATE()),
('4100', N'مبيعات', 'Sales', 4, 8, 1, GETDATE()),
('5000', N'المصروفات', 'Expenses', 5, NULL, 1, GETDATE()),
('5100', N'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 5, 10, 1, GETDATE());

-- 💰 إنشاء قيد افتتاحي - رأس المال
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, Status, BranchId, UserId, TotalDebit, TotalCredit, CreatedAt) VALUES
('JE0000001', GETDATE(), N'قيد افتتاحي - رأس المال', 58, 2, 1, 1, 500000.00, 500000.00, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, Description, DebitAmount, CreditAmount, CreatedAt) VALUES
(1, 2, 1, N'نقدية في الصندوق', 500000.00, 0.00, GETDATE()),
(1, 7, 2, N'رأس المال', 0.00, 500000.00, GETDATE());

-- 🛒 إنشاء فاتورة شراء من مورد الأثاث
INSERT INTO Purchases (PurchaseNumber, PurchaseDate, SupplierId, BranchId, UserId, SubTotal, TaxAmount, DiscountAmount, TotalAmount, Status, Notes, CreatedAt) VALUES
('PUR0000001', GETDATE(), 4, 1, 1, 15000.00, 2250.00, 0.00, 17250.00, 2, N'شراء أثاث غرف نوم', GETDATE());

INSERT INTO PurchaseItems (PurchaseId, ProductId, Quantity, UnitPrice, TotalPrice, CreatedAt) VALUES
(1, 2, 3, 2500.00, 7500.00, GETDATE()),  -- 3 أسرة
(1, 3, 2, 3000.00, 6000.00, GETDATE()),  -- 2 دولاب
(1, 4, 1, 1500.00, 1500.00, GETDATE());  -- 1 تسريحة

-- 📝 قيد محاسبي للمشتريات
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, Status, BranchId, UserId, TotalDebit, TotalCredit, CreatedAt) VALUES
('JE0000002', GETDATE(), N'مشتريات أثاث من شركة الأثاث المصري', 10, 2, 1, 1, 17250.00, 17250.00, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, Description, DebitAmount, CreditAmount, CreatedAt) VALUES
(2, 4, 1, N'مخزون أثاث', 15000.00, 0.00, GETDATE()),
(2, 4, 2, N'ضريبة القيمة المضافة', 2250.00, 0.00, GETDATE()),
(2, 6, 3, N'مورد - شركة الأثاث المصري', 0.00, 17250.00, GETDATE());

-- 💸 دفع جزئي للمورد
INSERT INTO CashPayments (PaymentNumber, PaymentDate, PayeeType, PayeeId, Amount, PaymentMethodId, BranchId, UserId, Description, Status, CreatedAt) VALUES
('CP0000001', GETDATE(), 'Supplier', 4, 10000.00, 1, 1, 1, N'دفعة على حساب مشتريات الأثاث', 2, GETDATE());

-- 📝 قيد محاسبي للدفع
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, Status, BranchId, UserId, TotalDebit, TotalCredit, CreatedAt) VALUES
('JE0000003', GETDATE(), N'دفع نقدي لشركة الأثاث المصري', 20, 2, 1, 1, 10000.00, 10000.00, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, Description, DebitAmount, CreditAmount, CreatedAt) VALUES
(3, 6, 1, N'مورد - شركة الأثاث المصري', 10000.00, 0.00, GETDATE()),
(3, 2, 2, N'نقدية', 0.00, 10000.00, GETDATE());

-- 🛍️ إنشاء فاتورة مبيعات لعميل مميز
INSERT INTO Sales (SaleNumber, SaleDate, CustomerId, BranchId, UserId, SubTotal, TaxAmount, DiscountAmount, TotalAmount, Status, Notes, CreatedAt) VALUES
('SAL0000001', GETDATE(), 4, 1, 2, 15500.00, 2325.00, 1550.00, 16275.00, 2, N'بيع أثاث لعميل مميز مع خصم 10%', GETDATE());

INSERT INTO SaleItems (SaleId, ProductId, Quantity, UnitPrice, DiscountPercentage, TotalPrice, CreatedAt) VALUES
(1, 2, 1, 3500.00, 10.00, 3150.00, GETDATE()),  -- سرير
(1, 3, 1, 4200.00, 10.00, 3780.00, GETDATE()),  -- دولاب
(1, 5, 1, 12000.00, 10.00, 10800.00, GETDATE()); -- طقم صالون

-- 📝 قيد محاسبي للمبيعات
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, Status, BranchId, UserId, TotalDebit, TotalCredit, CreatedAt) VALUES
('JE0000004', GETDATE(), N'مبيعات أثاث لمحمد سعد الدين', 30, 2, 1, 2, 16275.00, 16275.00, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, Description, DebitAmount, CreditAmount, CreatedAt) VALUES
(4, 3, 1, N'عميل - محمد سعد الدين', 16275.00, 0.00, GETDATE()),
(4, 9, 2, N'مبيعات أثاث', 0.00, 15500.00, GETDATE()),
(4, 9, 3, N'ضريبة القيمة المضافة', 0.00, 2325.00, GETDATE()),
(4, 9, 4, N'خصم مسموح', 1550.00, 0.00, GETDATE());

-- 💰 تحصيل من العميل
INSERT INTO CashReceipts (ReceiptNumber, ReceiptDate, PayerType, PayerId, Amount, PaymentMethodId, BranchId, UserId, Description, Status, CreatedAt) VALUES
('CR0000001', GETDATE(), 'Customer', 4, 16275.00, 1, 1, 3, N'تحصيل كامل من محمد سعد الدين', 2, GETDATE());

-- 📝 قيد محاسبي للتحصيل
INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TransactionType, Status, BranchId, UserId, TotalDebit, TotalCredit, CreatedAt) VALUES
('JE0000005', GETDATE(), N'تحصيل نقدي من محمد سعد الدين', 40, 2, 1, 3, 16275.00, 16275.00, GETDATE());

INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, Description, DebitAmount, CreditAmount, CreatedAt) VALUES
(5, 2, 1, N'نقدية', 16275.00, 0.00, GETDATE()),
(5, 3, 2, N'عميل - محمد سعد الدين', 0.00, 16275.00, GETDATE());

PRINT N'تم إنشاء دورة برنامج كاملة بنجاح! 🎉';
PRINT N'تشمل: مشتريات، مبيعات، مقبوضات، مدفوعات، قيود محاسبية';
